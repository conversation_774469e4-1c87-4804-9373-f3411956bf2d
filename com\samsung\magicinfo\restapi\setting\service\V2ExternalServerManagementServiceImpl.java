package com.samsung.magicinfo.restapi.setting.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.framework.common.Menu;
import com.samsung.magicinfo.framework.common.MenuEntity;
import com.samsung.magicinfo.framework.common.MessageSourceManager;
import com.samsung.magicinfo.framework.common.MessageSourceManagerImpl;
import com.samsung.magicinfo.framework.setup.dao.DatalinkServerDao;
import com.samsung.magicinfo.framework.setup.entity.DatalinkServerEntity;
import com.samsung.magicinfo.framework.setup.entity.DatalinkServerTableEntity;
import com.samsung.magicinfo.framework.setup.entity.DatalinkTableItem;
import com.samsung.magicinfo.framework.setup.entity.DatalinkTableItemList;
import com.samsung.magicinfo.framework.setup.entity.RmServerEntity;
import com.samsung.magicinfo.framework.setup.manager.DatalinkServerImpl;
import com.samsung.magicinfo.framework.setup.manager.RmServerImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.restapi.exception.BaseRestException;
import com.samsung.magicinfo.restapi.setting.controller.V2ExternalServerManagementController;
import com.samsung.magicinfo.restapi.setting.model.RemoteServerListFilter;
import com.samsung.magicinfo.restapi.setting.model.ServerListResource;
import com.samsung.magicinfo.restapi.setting.model.V2AddDatalinkServerFilter;
import com.samsung.magicinfo.restapi.setting.model.V2DatalinkEditByPassFilter;
import com.samsung.magicinfo.restapi.setting.model.V2DatalinkEditByPassResource;
import com.samsung.magicinfo.restapi.setting.model.V2DatalinkServerCheckServerNameResource;
import com.samsung.magicinfo.restapi.setting.model.V2DatalinkServerDetailSaveFilter;
import com.samsung.magicinfo.restapi.setting.model.V2DatalinkServerEntity;
import com.samsung.magicinfo.restapi.setting.model.V2DatalinkServerTablesUpdate;
import com.samsung.magicinfo.restapi.setting.model.V2GetDataLinkListFilter;
import com.samsung.magicinfo.restapi.setting.model.V2GetDataLinkListResource;
import com.samsung.magicinfo.restapi.setting.model.V2GetDataLinkViewListResource;
import com.samsung.magicinfo.restapi.setting.model.V2RmServerEntity;
import com.samsung.magicinfo.restapi.setting.model.V2SplayerRemoteServerAddFilter;
import com.samsung.magicinfo.restapi.setting.model.V2SplayerRemoteServerSaveFilter;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import io.netty.handler.timeout.ReadTimeoutException;
import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.ConnectException;
import java.net.InetAddress;
import java.net.Socket;
import java.net.SocketException;
import java.net.URL;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.KeyManager;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509ExtendedTrustManager;
import javax.net.ssl.X509TrustManager;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.Unmarshaller;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.stream.XMLStreamReader;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXParseException;

@Service("V2ExternalServerManagementService")
@Transactional
public class V2ExternalServerManagementServiceImpl implements V2ExternalServerManagementService {
   protected Logger logger = LoggingManagerV2.getLogger(V2ExternalServerManagementController.class);
   ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();

   public V2ExternalServerManagementServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority','Server Setup Manage Authority')")
   public V2GetDataLinkListResource getDataLinkList(V2GetDataLinkListFilter filter, HttpServletRequest request, HttpServletResponse response) throws Exception {
      int startIndex = 1;
      int results = 10;
      if (!StrUtils.nvl(filter.getStartIndex()).equals("")) {
         startIndex = Integer.parseInt(filter.getStartIndex());
      }

      String search_text = StrUtils.nvl(filter.getSearch_text()).equals("") ? "" : filter.getSearch_text();
      if (!StrUtils.nvl(filter.getResult()).equals("")) {
         results = Integer.parseInt(filter.getResult());
      }

      String sort = filter.getSort();
      String dir = filter.getDir();
      String orderCol = filter.getOrderCol();
      String orderDir = filter.getOrderDir();
      byte var12 = -1;
      switch(orderCol.hashCode()) {
      case 49:
         if (orderCol.equals("1")) {
            var12 = 0;
         }
         break;
      case 50:
         if (orderCol.equals("2")) {
            var12 = 1;
         }
         break;
      case 51:
         if (orderCol.equals("3")) {
            var12 = 2;
         }
         break;
      case 52:
         if (orderCol.equals("4")) {
            var12 = 3;
         }
         break;
      case 53:
         if (orderCol.equals("5")) {
            var12 = 4;
         }
         break;
      case 54:
         if (orderCol.equals("6")) {
            var12 = 5;
         }
         break;
      case 55:
         if (orderCol.equals("7")) {
            var12 = 6;
         }
      }

      switch(var12) {
      case 0:
         sort = "server_name";
         break;
      case 1:
         sort = "ip_address";
         break;
      case 2:
         sort = "port";
         break;
      case 3:
         sort = "ftp_port";
         break;
      case 4:
         sort = "period";
         break;
      case 5:
         sort = "use_ssl";
         break;
      case 6:
         sort = "bypass";
      }

      response.setContentType("application/json;charset=UTF-8");
      DatalinkServerImpl dlsDao = DatalinkServerImpl.getInstance();
      V2GetDataLinkListResource resource = new V2GetDataLinkListResource();
      request.setAttribute("page", String.valueOf(startIndex / results + 1));
      ListManager listMgr = new ListManager(request, dlsDao, "commonlist");
      listMgr.addSearchInfo("sortColumn", sort);
      listMgr.addSearchInfo("sortOrder", orderDir);
      listMgr.addSearchInfo("searchText", search_text);
      listMgr.setLstSize(Integer.valueOf(results));
      listMgr.setSection("getDatalinkServerList");
      PageManager pageMgr = null;
      List searchList = listMgr.dbexecute();
      pageMgr = listMgr.getPageManager();
      resource.setRecordsReturned(searchList.size());
      resource.setTotalRecords(pageMgr.getTotalRowCount());
      resource.setRecordsTotal(pageMgr.getTotalRowCount());
      resource.setStartIndex(startIndex);
      if (sort != null && !sort.equals("")) {
         resource.setSort(sort);
         resource.setDir(orderDir);
      }

      resource.setResults(pageMgr.getInfo().getPageSize());
      List dataList = new ArrayList();

      for(int i = 0; i < searchList.size(); ++i) {
         LinkedHashMap data = new LinkedHashMap();
         DatalinkServerEntity dlsEntity = (DatalinkServerEntity)searchList.get(i);
         data.put("checkbox", "");
         data.put("serverName", dlsEntity.getServer_name());
         data.put("ip", dlsEntity.getIp_address());
         data.put("port", dlsEntity.getPort());
         data.put("ftpPort", dlsEntity.getFtp_port());
         if (dlsEntity.getPrivate_mode() != null && dlsEntity.getPrivate_mode()) {
            data.put("privateMode", dlsEntity.getPrivate_mode());
            data.put("privateIp", dlsEntity.getPrivate_ip_address());
            data.put("privateWebPort", dlsEntity.getPrivate_web_port());
         } else {
            data.put("privateMode", "false");
            data.put("privateIp", "");
            data.put("privateWebPort", "");
         }

         data.put("serverIp", InetAddress.getLocalHost().getHostAddress());
         data.put("period", dlsEntity.getPeriod());
         data.put("useSsl", dlsEntity.getUse_ssl());
         data.put("bypass", dlsEntity.getBypass());
         data.put("link", "link");
         dataList.add(data);
      }

      resource.setData(dataList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority','Server Setup Manage Authority')")
   public V2GetDataLinkViewListResource getDataLinkViewList(String serverName) throws SQLException {
      V2GetDataLinkViewListResource resource = new V2GetDataLinkViewListResource();
      serverName = StrUtils.nvl(serverName);
      DatalinkServerImpl dlsDao = DatalinkServerImpl.getInstance();
      DatalinkServerEntity dlsEntity = dlsDao.getDatalinkServerInfo(serverName);
      resource.setRecordsReturned(1);
      List maplist = new ArrayList();
      LinkedHashMap records = new LinkedHashMap();
      records.put("serverName", dlsEntity.getServer_name());
      records.put("ip", dlsEntity.getIp_address());
      records.put("port", dlsEntity.getPort());
      records.put("ftpPort", dlsEntity.getFtp_port());
      if (dlsEntity.getPrivate_mode() != null && dlsEntity.getPrivate_mode()) {
         records.put("privateMode", dlsEntity.getPrivate_mode());
         records.put("privateIp", dlsEntity.getPrivate_ip_address());
         records.put("privateWebPort", dlsEntity.getPrivate_web_port());
      } else {
         records.put("privateMode", "false");
         records.put("privateIp", "");
         records.put("privateWebPort", "");
      }

      records.put("period", dlsEntity.getPeriod());
      records.put("useSsl", dlsEntity.getUse_ssl());
      records.put("bypass", dlsEntity.getUse_ssl());
      records.put("link", "link");
      maplist.add(records);
      resource.setRecords(maplist);
      return resource;
   }

   public HashMap datalinkServerList(HttpServletRequest request, HttpServletResponse response) {
      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("settingsTab");
      Menu add = new Menu();
      add.setCmd("dataLink");
      add.setUrl(request.getContextPath() + "/setting/externalServerList.htm");
      add.setName("COM_BUTTON_ADD");
      resultMenu.putMenu("add", add);
      Menu delete = new Menu();
      delete.setCmd("dataLink");
      delete.setClassName("hasMultiChk");
      delete.setUrl(request.getContextPath() + "/setting/externalServerList.htm");
      delete.setName("COM_BUTTON_DELETE");
      resultMenu.putMenu("delete", delete);
      Menu assignTable = new Menu();
      assignTable.setCmd("datalink");
      assignTable.setUrl(request.getContextPath() + "/setting/externalServerList.htm");
      assignTable.setName("MIS_SID_ASSIGN_TABLE");
      resultMenu.putMenu("assignTable", assignTable);
      Menu tableWidth = new Menu();
      tableWidth.setTable("37px");
      tableWidth.setTable("10%");
      tableWidth.setTable("10%");
      tableWidth.setTable("10%");
      tableWidth.setTable("10%");
      tableWidth.setTable("10%");
      tableWidth.setTable("10%");
      tableWidth.setTable("10%");
      tableWidth.setTable("");
      resultMenu.putMenu("tableWidth", tableWidth);
      Menu tableHeader = new Menu();
      tableHeader.setTable("check");
      tableHeader.setTable("TABLE_SERVER_NAME_P");
      tableHeader.setTable("TABLE_IP_ADDR_P");
      tableHeader.setTable("TABLE_WEB_PORT_P");
      tableHeader.setTable("COM_SETUP_NEW_STRING26_P");
      tableHeader.setTable("COM_TEXT_PERIOD2_P");
      tableHeader.setTable("SSL");
      tableHeader.setTable("MIS_SID_20_BYPASS");
      tableHeader.setTable("COM_TV_SID_SERVER_NETWORK_SETTING");
      resultMenu.putMenu("tableHeader", tableHeader);
      resultMenu.setStatus("success");
      HashMap resultList = new HashMap();
      resultList.put("list", resultMenu);
      return resultList;
   }

   public HashMap datalinkServerView(String param) throws SQLException {
      String pageSize = "100";
      String search_text = "";
      String start_date = "";
      String end_date = "";
      Map infoMap = null;
      Integer errThresholdCnt = null;
      MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
      Locale locale = SecurityUtils.getLocale();
      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("settingsTab");
      infoMap = this.serverSetupDao.getServerInfoByOrgId(0L);
      errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
      DatalinkServerImpl dlsDao = DatalinkServerImpl.getInstance();
      DatalinkServerEntity dlsEntity = dlsDao.getDatalinkServerInfo(param);
      Menu serverName = new Menu();
      serverName.setName("TABLE_SERVER_NAME_P");
      serverName.setTableData(dlsEntity.getServer_name());
      serverName.setMode("span");
      resultMenu.putMenu("serverName", serverName);
      Menu ip = new Menu();
      ip.setName("TABLE_IP_ADDR_P");
      ip.setTableData(dlsEntity.getIp_address());
      ip.setMode("ip");
      resultMenu.putMenu("ip", ip);
      Menu webPort = new Menu();
      webPort.setName("TABLE_WEB_PORT_P");
      webPort.setTableData(dlsEntity.getPort().toString());
      webPort.setMode("input");
      resultMenu.putMenu("webPort", webPort);
      Menu privateMode = new Menu();
      privateMode.setName("MIS_SID_PRIVATE_MODE");
      privateMode.setTableData(dlsEntity.getPrivate_mode().toString());
      privateMode.setMode("privateMode");
      resultMenu.putMenu("privateMode", privateMode);
      Menu privateIp = new Menu();
      privateIp.setName("MIS_SID_PRIVATE_IIP_ADDRESS");
      if (dlsEntity.getPrivate_ip_address() != null) {
         privateIp.setTableData(dlsEntity.getPrivate_ip_address());
      }

      resultMenu.putMenu("privateIp", privateIp);
      Menu privatePort = new Menu();
      privatePort.setName("TABLE_WEB_PORT_P");
      if (dlsEntity.getPrivate_web_port() != null) {
         privatePort.setTableData(dlsEntity.getPrivate_web_port().toString());
      }

      resultMenu.putMenu("privatePort", privatePort);
      Menu ftpPort = new Menu();
      ftpPort.setName("COM_SETUP_NEW_STRING26_P");
      ftpPort.setTableData(dlsEntity.getFtp_port().toString());
      ftpPort.setMode("input");
      resultMenu.putMenu("ftpPort", ftpPort);
      Menu period = new Menu();
      period.setName("TEXT_PERIOD_P");
      period.setTableData(dlsEntity.getPeriod().toString());
      period.setMode("inputNumber");
      resultMenu.putMenu("period", period);
      Menu ssl = new Menu();
      ssl.setName("SSL");
      ssl.setTableData(dlsEntity.getUse_ssl().toString());
      ssl.setMode("checkbox");
      resultMenu.putMenu("ssl", ssl);
      Menu saveBtn = new Menu();
      saveBtn.setName("COM_BUTTON_SAVE");
      resultMenu.putMenu("saveBtn", saveBtn);
      Menu cancelBtn = new Menu();
      cancelBtn.setName("MSG_CANCELED");
      resultMenu.putMenu("cancelBtn", cancelBtn);
      resultMenu.setStatus("success");
      HashMap result = new HashMap();
      result.put("list", resultMenu);
      return result;
   }

   public V2DatalinkServerEntity datalinkServerSave(String serverName, V2DatalinkServerDetailSaveFilter filter) throws SQLException, Exception {
      Integer errThresholdCnt = null;
      Map infoMap = null;
      infoMap = this.serverSetupDao.getServerInfoByOrgId(0L);
      errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
      String ip = filter.getIp();
      String webPort = filter.getWebPort();
      String ftpPort = filter.getFtpPort();
      String period = filter.getPeriod();
      String useSsl = filter.getSsl();
      String usePrivate = filter.getUsePrivate();
      String privateHostName = filter.getPrivateHostName();
      String privateWebPort = filter.getPrivatePort();
      String privateSsl = filter.getPrivateSsl();
      String oldIP = null;
      DatalinkServerImpl dlsDao = DatalinkServerImpl.getInstance();
      DatalinkServerEntity dlsEntity = dlsDao.getDatalinkServerInfo(serverName);
      V2DatalinkServerEntity newDlsEntity = new V2DatalinkServerEntity();
      oldIP = dlsEntity.getIp_address();
      if (ip != null && !ip.equals("")) {
         newDlsEntity.setIpAddress(ip);
      }

      if (webPort != null && !webPort.equals("")) {
         newDlsEntity.setPort(Long.parseLong(webPort));
      }

      if (ftpPort != null && !ftpPort.equals("")) {
         newDlsEntity.setFtpPort(Long.parseLong(ftpPort));
      }

      if (period != null && !period.equals("")) {
         Long intPeriod = Long.parseLong(period);
         if (intPeriod < 0L) {
            throw new BaseRestException("INVALID_PARAM", "MESSAGE_DEVICE_INPUT_SERVER_INFORMATION_P");
         }

         newDlsEntity.setPeriod(intPeriod);
      }

      if (useSsl != null && !useSsl.equals("")) {
         newDlsEntity.setUseSsl(Boolean.valueOf(useSsl));
      }

      if (usePrivate != null && !usePrivate.equals("") && usePrivate.equals("true")) {
         newDlsEntity.setPrivateMode(true);
         if (privateHostName != null && !privateHostName.equals("")) {
            newDlsEntity.setPrivateIpAddress(privateHostName);
         }

         if (privateWebPort != null && !privateWebPort.equals("")) {
            newDlsEntity.setPrivateWebPort(Long.parseLong(privateWebPort));
         }
      } else {
         newDlsEntity.setPrivateMode(false);
         newDlsEntity.setPrivateIpAddress("");
         newDlsEntity.setPrivateWebPort(0L);
      }

      if (dlsDao.updateDatalinkServer(dlsEntity) == 0) {
         throw new Exception("MESSAGE_DEVICE_INPUT_SERVER_INFORMATION_P");
      } else {
         this.serverSetupDao.deleteExternalServerForMonitoring("DATALINK", oldIP);
         this.serverSetupDao.addExternalServerForMonitoring("DATALINK", dlsEntity.getIp_address(), errThresholdCnt + 1);
         this.serverSetupDao.setLastErrTime("DATALINK", dlsEntity.getIp_address());
         return newDlsEntity;
      }
   }

   public V2DatalinkServerCheckServerNameResource datalinkServerCheckServerName(String serverName) throws Exception {
      V2DatalinkServerCheckServerNameResource resource = new V2DatalinkServerCheckServerNameResource();
      Map infoMap = null;
      Integer errThresholdCnt = null;
      new MenuEntity();
      infoMap = this.serverSetupDao.getServerInfoByOrgId(0L);
      errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
      DatalinkServerImpl dlsDao = DatalinkServerImpl.getInstance();
      DatalinkServerEntity checkingExistEntity = dlsDao.getDatalinkServerInfo(serverName);
      if (checkingExistEntity != null) {
         throw new Exception("MIS_SID_20_SERVER_NAME_ALREADY_IN_USE_ENTER_A_DIFFERENT_SERVER_NAME");
      } else {
         resource.setMessage("MIS_TEXT_SETUP_CHECK_SERVERNAME_P");
         return resource;
      }
   }

   public V2DatalinkServerEntity addDatalinkServer(V2AddDatalinkServerFilter filter) throws Exception {
      Integer errThresholdCnt = null;
      Map infoMap = null;
      infoMap = this.serverSetupDao.getServerInfoByOrgId(0L);
      errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
      String serverName = filter.getServerName();
      String ip = filter.getRemoteHostName();
      String webPort = filter.getWebPort();
      String usePrivate = filter.getUsePrivate();
      String privateIp = filter.getPrivateHostName();
      String privateWebPort = filter.getPrivateWebPort();
      String ftpPort = filter.getFtpPort();
      String period = filter.getPeriod();
      String useSsl = StrUtils.nvl(filter.getUseSsl()).equals("") ? "false" : filter.getUseSsl();
      DatalinkServerEntity dlsEntity = new DatalinkServerEntity();
      V2DatalinkServerEntity newDlsEntity = new V2DatalinkServerEntity();
      if (serverName != null && !serverName.equals("")) {
         if (ip != null && !ip.equals("")) {
            if (webPort != null && !webPort.equals("") && ftpPort != null && !ftpPort.equals("") && period != null && !period.equals("") && Integer.parseInt(period) >= 0) {
               DatalinkServerImpl dlsDao = DatalinkServerImpl.getInstance();
               newDlsEntity.setServerName(serverName);
               newDlsEntity.setIpAddress(ip);
               newDlsEntity.setPort(Long.parseLong(webPort.toString()));
               newDlsEntity.setPrivateMode(Boolean.valueOf(usePrivate));
               if (Boolean.valueOf(usePrivate)) {
                  newDlsEntity.setPrivateIpAddress(privateIp);
                  newDlsEntity.setPrivateWebPort(Long.valueOf(privateWebPort));
               }

               newDlsEntity.setFtpPort(Long.parseLong(ftpPort.toString()));
               newDlsEntity.setPeriod(Long.parseLong(period));
               newDlsEntity.setUseSsl(Boolean.valueOf(useSsl.toString()));
               newDlsEntity.setBypass(false);
               dlsDao.addDatalinkServer(dlsEntity);
               this.serverSetupDao.addExternalServerForMonitoring("DATALINK", dlsEntity.getIp_address(), errThresholdCnt + 1);
               this.serverSetupDao.setLastErrTime("DATALINK", dlsEntity.getIp_address());
               return newDlsEntity;
            } else {
               throw new BaseRestException("INVALID_PARAM", "MESSAGE_DEVICE_INPUT_SERVER_INFORMATION_P");
            }
         } else {
            throw new BaseRestException("INVALID_PARAM", "MIS_TEXT_IP_ADDRESS_INCORRECT_P");
         }
      } else {
         throw new BaseRestException("INVALID_PARAM", "MIS_MESSAGE_SETUP_ENTER_SERVERNAME_P");
      }
   }

   public List datalinkServerDelete(String serverName) throws Exception {
      List records = new ArrayList();
      V2DatalinkServerEntity newDlsEntity = new V2DatalinkServerEntity();
      Map infoMap = null;
      Integer errThresholdCnt = null;
      new MenuEntity();
      infoMap = this.serverSetupDao.getServerInfoByOrgId(0L);
      errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
      DatalinkServerImpl dlsDao = DatalinkServerImpl.getInstance();
      String serverNameList = StrUtils.nvl(serverName);
      new DatalinkServerEntity();
      String[] arrServerName = serverNameList.split(",");
      int result = 0;

      for(int i = 0; i < arrServerName.length; ++i) {
         DatalinkServerEntity dlsEntity = dlsDao.getDatalinkServerInfo(arrServerName[i]);
         result += dlsDao.deleteDatalinkServer(dlsEntity);
         this.serverSetupDao.deleteExternalServerForMonitoring("DATALINK", dlsEntity.getIp_address());
         ConvertUtil cUtil = new ConvertUtil();
         newDlsEntity = (V2DatalinkServerEntity)ConvertUtil.convertObject(dlsEntity, newDlsEntity);
         records.add(newDlsEntity);
      }

      if (result > 0) {
         dlsDao.setDeviceDatalinkServerTriggering();
      }

      return records;
   }

   public ServerListResource splayerRemoteServerList(RemoteServerListFilter filter, HttpServletRequest request) throws Exception {
      int intStartIndex = true;
      int intResults = true;
      String results = StrUtils.nvl(filter.getPageSize()).equals("") ? "10" : filter.getPageSize();
      String startIndex = StrUtils.nvl(filter.getStartIndex()).equals("") ? "0" : filter.getStartIndex();
      int intResults = Integer.parseInt(results);
      int intStartIndex = Integer.parseInt(startIndex);
      String sort = filter.getSort();
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map infoMap = serverSetupDao.getServerInfoByOrgId(0L);
      Boolean rmMonEnable = (Boolean)infoMap.get("EXT_SERVER_RM_MON_ENABLE");
      RmServerImpl rmsDao = RmServerImpl.getInstance();
      request.setAttribute("page", String.valueOf(intStartIndex / intResults + 1));
      ListManager listMgr = new ListManager(request, rmsDao, "commonlist");
      listMgr.setLstSize(Integer.valueOf(results));
      listMgr.setSection("getRmServerList");
      PageManager pageMgr = null;
      List searchList = listMgr.dbexecute();
      pageMgr = listMgr.getPageManager();
      ServerListResource resource = new ServerListResource();
      resource.setRecordsReturned(searchList.size());
      resource.setTotalRecords(pageMgr.getTotalRowCount());
      resource.setRecordsTotal(pageMgr.getTotalRowCount());
      resource.setStartIndex(Integer.valueOf(startIndex));
      if (sort != null && !sort.equals("")) {
         resource.setSort(sort);
         resource.setDir(filter.getDir());
      }

      resource.setResults(pageMgr.getInfo().getPageSize());
      List dataList = new ArrayList();

      for(int i = 0; i < searchList.size(); ++i) {
         LinkedHashMap data = new LinkedHashMap();
         RmServerEntity rmEntity = (RmServerEntity)searchList.get(i);
         String rmserver_status = null;
         if (rmMonEnable != null && rmMonEnable) {
            rmserver_status = this.getRMStatusByJob(rmEntity);
         } else {
            rmserver_status = this.getRMStatusByRealTime(rmEntity);
         }

         data.put("checkbox", "");
         data.put("server_name", rmEntity.getServer_name());
         data.put("public_ip", rmEntity.getIp_address());
         if (rmEntity.getPrivate_ip_address() != null) {
            data.put("private_ip", rmEntity.getPrivate_ip_address());
         } else {
            data.put("private_ip", "");
         }

         if (rmEntity.getPrivate_port() != null) {
            data.put("private_port", rmEntity.getPrivate_port());
         } else {
            data.put("private_port", "");
         }

         data.put("private_mode", rmEntity.getPrivate_mode());
         data.put("port", rmEntity.getPort());
         data.put("use_ssl", rmEntity.getUse_ssl());
         data.put("link", rmserver_status);
         dataList.add(data);
      }

      resource.setData(dataList);
      return resource;
   }

   private String getRMStatusByJob(RmServerEntity rmEntity) throws Exception {
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Integer errCnt = 0;
      Map infoMap = serverSetupDao.getServerInfoByOrgId(0L);
      Integer errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
      errCnt = serverSetupDao.getExternalServerErrCount("RM", rmEntity.getIp_address());
      if (errThresholdCnt != null && errCnt != null) {
         return errThresholdCnt <= errCnt ? "OFF" : "ON";
      } else {
         return "ERROR";
      }
   }

   private String getRMStatusByRealTime(RmServerEntity rmEntity) throws Exception {
      DocumentBuilderFactory factory = DocumentUtils.getDocumentBuilderFactoryInstance();
      DocumentBuilder builder = factory.newDocumentBuilder();
      HttpClient httpclient = new DefaultHttpClient();
      String rmserver_status = null;
      String return_code = null;
      String rmServer = null;
      if (rmEntity.getPrivate_mode()) {
         if (rmEntity.getPrivate_ssl()) {
            rmServer = "https://" + rmEntity.getPrivate_ip_address() + ":" + rmEntity.getPrivate_port() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
         } else {
            rmServer = "http://" + rmEntity.getPrivate_ip_address() + ":" + rmEntity.getPrivate_port() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
         }
      } else if (rmEntity.getUse_ssl()) {
         rmServer = "https://" + rmEntity.getIp_address() + ":" + rmEntity.getPort() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
      } else {
         rmServer = "http://" + rmEntity.getIp_address() + ":" + rmEntity.getPort() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
      }

      HttpPost httpget;
      BasicHttpParams httpParams;
      HttpResponse Rmserver_response;
      HttpEntity entity;
      String line;
      if (!rmEntity.getPrivate_mode() && rmEntity.getUse_ssl() || rmEntity.getPrivate_mode() && rmEntity.getPrivate_ssl()) {
         URL url = new URL(rmServer);
         SecurityUtils.trustAllCertificates();
         HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
         conn.setConnectTimeout(30000);
         httpget = null;
         httpParams = null;
         Rmserver_response = null;

         try {
            conn.connect();
            conn.setInstanceFollowRedirects(true);
            InputStream in = conn.getInputStream();
            InputStreamReader isr = new InputStreamReader(in);
            BufferedReader reader = new BufferedReader(isr);
            entity = null;

            String line;
            for(line = new String(); (line = reader.readLine()) != null; line = line + line) {
            }

            reader.close();
            Document doc = builder.parse(new InputSource(new StringReader(line)));
            doc.getDocumentElement().normalize();
            NodeList headNodeList = doc.getElementsByTagName("response");
            Element subItem = (Element)headNodeList.item(0);
            return_code = subItem.getAttribute("code");
            if (return_code.equals("0")) {
               rmserver_status = "ON";
            } else {
               rmserver_status = "OFF";
            }

            isr.close();
            in.close();
         } catch (Exception var29) {
            rmserver_status = "OFF";
            this.logger.error("SSL time out! e : " + var29.getMessage());
         }
      } else {
         BufferedReader rd = null;
         InputStreamReader isr = null;

         try {
            new URL(rmServer);
            httpget = new HttpPost(rmServer);
            httpParams = new BasicHttpParams();
            HttpConnectionParams.setConnectionTimeout(httpParams, 30000);
            httpclient = new DefaultHttpClient(httpParams);
            Rmserver_response = httpclient.execute(httpget);
            entity = Rmserver_response.getEntity();
            if (entity != null) {
               isr = new InputStreamReader(Rmserver_response.getEntity().getContent());
               rd = new BufferedReader(isr);
               line = null;

               String resultXml;
               for(resultXml = new String(); (line = rd.readLine()) != null; resultXml = resultXml + line) {
               }

               Document doc = builder.parse(new InputSource(new StringReader(resultXml)));
               doc.getDocumentElement().normalize();
               NodeList headNodeList = doc.getElementsByTagName("response");
               Element subItem = (Element)headNodeList.item(0);
               return_code = subItem.getAttribute("code");
               if (return_code.equals("0")) {
                  rmserver_status = "ON";
               } else {
                  rmserver_status = "OFF";
               }

               rd.close();
            }

            httpget.abort();
            httpclient.getConnectionManager().shutdown();
         } catch (ClientProtocolException var30) {
            this.logger.error("[MagicInfo_RMServerAjaxController] ClientProtocolException");
            rmserver_status = "OFF";
         } catch (IllegalStateException var31) {
            this.logger.error("[MagicInfo_RMServerAjaxController] IllegalStateException");
            rmserver_status = "OFF";
         } catch (ConnectException var32) {
            this.logger.error("[MagicInfo_RMServerAjaxController] ConnectException");
            rmserver_status = "OFF";
         } catch (ConnectTimeoutException var33) {
            this.logger.error("[MagicInfo_RMServerAjaxController] ConnectTimeoutException");
            rmserver_status = "OFF";
         } catch (SocketException var34) {
            this.logger.error("[MagicInfo_RMServerAjaxController] SocketException");
            rmserver_status = "OFF";
         } catch (SAXParseException var35) {
            this.logger.error("[MagicInfo_RMServerAjaxController] SAXParseException");
            rmserver_status = "OFF";
         } catch (Exception var36) {
            this.logger.error("[MagicInfo_RMServerAjaxController] Exception");
            rmserver_status = "OFF";
         } finally {
            if (rd != null) {
               rd.close();
            }

            if (isr != null) {
               isr.close();
            }

            httpclient.getConnectionManager().shutdown();
         }
      }

      return rmserver_status;
   }

   public V2RmServerEntity splayerRemoteServerView(String serverNameValue) throws Exception {
      Integer errThresholdCnt = null;
      MenuEntity resultMenu = new MenuEntity();
      Map infoMap = null;
      resultMenu.setMenuName("settingsTab");
      infoMap = this.serverSetupDao.getServerInfoByOrgId(0L);
      errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
      RmServerImpl rmDao = RmServerImpl.getInstance();
      RmServerEntity rmInfo = rmDao.getRmServerInfo(serverNameValue);
      Menu serverName = new Menu();
      serverName.setName("TABLE_SERVER_NAME_P");
      serverName.setTableData(rmInfo.getServer_name());
      serverName.setMode("span");
      resultMenu.putMenu("serverName", serverName);
      Menu ip = new Menu();
      ip.setName("TABLE_IP_ADDR_P");
      ip.setTableData(rmInfo.getIp_address());
      ip.setMode("ip");
      resultMenu.putMenu("ip", ip);
      Menu webPort = new Menu();
      webPort.setName("TABLE_WEB_PORT_P");
      webPort.setTableData(rmInfo.getPort().toString());
      webPort.setMode("input");
      resultMenu.putMenu("webPort", webPort);
      Menu ssl = new Menu();
      ssl.setName("SSL");
      ssl.setTableData(rmInfo.getUse_ssl().toString());
      ssl.setMode("checkbox");
      resultMenu.putMenu("ssl", ssl);
      Menu privateMode = new Menu();
      privateMode.setName("MIS_SID_PRIVATE_MODE");
      privateMode.setTableData(rmInfo.getPrivate_mode().toString());
      privateMode.setMode("privateMode");
      resultMenu.putMenu("privateMode", privateMode);
      Menu privateIp = new Menu();
      privateIp.setName("MIS_SID_PRIVATE_IIP_ADDRESS");
      if (rmInfo.getPrivate_ip_address() != null) {
         privateIp.setTableData(rmInfo.getPrivate_ip_address());
      }

      resultMenu.putMenu("privateIp", privateIp);
      Menu privatePort = new Menu();
      privatePort.setName("TABLE_WEB_PORT_P");
      if (rmInfo.getPrivate_port() != null) {
         privatePort.setTableData(rmInfo.getPrivate_port().toString());
      }

      resultMenu.putMenu("privatePort", privatePort);
      Menu privateSsl = new Menu();
      privateSsl.setName("PRIVATE SSL");
      if (rmInfo.getPrivate_ssl() != null) {
         privateSsl.setTableData(rmInfo.getPrivate_ssl().toString());
      }

      resultMenu.putMenu("privateSsl", privateSsl);
      Menu saveBtn = new Menu();
      saveBtn.setName("COM_BUTTON_SAVE");
      resultMenu.putMenu("saveBtn", saveBtn);
      Menu cancelBtn = new Menu();
      cancelBtn.setName("MSG_CANCELED");
      resultMenu.putMenu("cancelBtn", cancelBtn);
      V2RmServerEntity newRmInfo = new V2RmServerEntity();
      ConvertUtil cUtil = new ConvertUtil();
      newRmInfo = (V2RmServerEntity)ConvertUtil.convertObject(rmInfo, newRmInfo);
      return newRmInfo;
   }

   public V2RmServerEntity splayerRemoteServerSave(String serverName, V2SplayerRemoteServerSaveFilter filter) throws Exception {
      Integer errThresholdCnt = null;
      Map infoMap = null;
      infoMap = this.serverSetupDao.getServerInfoByOrgId(0L);
      errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
      String oldIP = null;
      RmServerImpl rmDao = RmServerImpl.getInstance();
      RmServerEntity rmInfo = rmDao.getRmServerInfo(serverName);
      oldIP = rmInfo.getIp_address();
      String ip = filter.getIp();
      String webPort = filter.getWebPort();
      String useSsl = filter.getUseSsl();
      String usePrivate = filter.getUsePrivate();
      String privateHostName = filter.getPrivateHostName();
      String privateWebPort = filter.getPrivateWebPort();
      String privateSsl = filter.getPrivateSsl();
      if (ip != null && !ip.equals("")) {
         rmInfo.setIp_address(ip);
      }

      if (webPort != null && !webPort.equals("")) {
         rmInfo.setPort(Long.parseLong(webPort));
      }

      if (useSsl != null && !useSsl.equals("")) {
         rmInfo.setUse_ssl(Boolean.valueOf(useSsl));
      }

      if (usePrivate != null && !usePrivate.equals("") && usePrivate.equals("true")) {
         rmInfo.setPrivate_mode(true);
         if (privateHostName != null && !privateHostName.equals("")) {
            rmInfo.setPrivate_ip_address(privateHostName);
         }

         if (privateWebPort != null && !privateWebPort.equals("")) {
            rmInfo.setPrivate_port(Long.parseLong(privateWebPort));
         }

         if (privateSsl != null && !privateSsl.equals("") && privateSsl.equals("true")) {
            rmInfo.setPrivate_ssl(true);
         } else {
            rmInfo.setPrivate_ssl(false);
         }
      } else {
         rmInfo.setPrivate_mode(false);
         rmInfo.setPrivate_ip_address("");
         rmInfo.setPrivate_port(0L);
      }

      if (rmDao.updateRmServer(rmInfo) == 0) {
         throw new Exception("MESSAGE_DEVICE_INPUT_SERVER_INFORMATION_P");
      } else {
         this.serverSetupDao.deleteExternalServerForMonitoring("RM", oldIP);
         this.serverSetupDao.addExternalServerForMonitoring("RM", rmInfo.getIp_address(), errThresholdCnt + 1);
         this.serverSetupDao.setLastErrTime("RM", rmInfo.getIp_address());
         V2RmServerEntity newRmInfo = new V2RmServerEntity();
         ConvertUtil cUtil = new ConvertUtil();
         newRmInfo = (V2RmServerEntity)ConvertUtil.convertObject(rmInfo, newRmInfo);
         return newRmInfo;
      }
   }

   public HashMap splayerRemoteServerCheckServerName(String serverName) throws Exception {
      RmServerImpl rmDao = RmServerImpl.getInstance();
      RmServerEntity rmInfo = rmDao.getRmServerInfo(serverName);
      if (rmInfo != null) {
         throw new Exception("MIS_SID_20_SERVER_NAME_ALREADY_IN_USE_ENTER_A_DIFFERENT_SERVER_NAME");
      } else {
         HashMap resultMap = new HashMap();
         resultMap.put("serverName", serverName);
         return resultMap;
      }
   }

   public V2RmServerEntity splayerRemoteServerAdd(V2SplayerRemoteServerAddFilter filter) throws Exception {
      V2RmServerEntity newRmInfo = new V2RmServerEntity();
      Map infoMap = null;
      Integer errThresholdCnt = null;
      infoMap = this.serverSetupDao.getServerInfoByOrgId(0L);
      errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
      String serverName = filter.getServerName();
      String ip = filter.getIp();
      String webPort = filter.getWebPort();
      String useSsl = StrUtils.nvl(filter.getUseSsl()).equals("") ? "false" : filter.getUseSsl();
      String usePrivate = filter.getUsePrivate();
      String privateHostName = filter.getPrivateHostName();
      String privateWebPort = filter.getPrivateWebPort();
      String privateWebSsl = filter.getPrivateSsl();
      if (serverName != null && !serverName.equals("")) {
         if (ip != null && !ip.equals("")) {
            RmServerImpl rmDao = RmServerImpl.getInstance();
            RmServerEntity rmInfo = new RmServerEntity();
            rmInfo.setServer_name(serverName);
            rmInfo.setIp_address(ip);
            rmInfo.setPort(Long.parseLong(webPort.toString()));
            if (useSsl != null && !useSsl.equals("")) {
               rmInfo.setUse_ssl(Boolean.valueOf(useSsl.toString()));
               rmInfo.setPrivate_mode(Boolean.FALSE);
               if (usePrivate != null && !usePrivate.equals("") && usePrivate.equals("true")) {
                  rmInfo.setPrivate_mode(true);
                  if (privateHostName != null && !privateHostName.equals("")) {
                     rmInfo.setPrivate_ip_address(privateHostName);
                  }

                  if (privateWebPort != null && !privateWebPort.equals("")) {
                     rmInfo.setPrivate_port(Long.parseLong(privateWebPort));
                  }

                  if (privateWebSsl != null && !privateWebSsl.equals("") && privateWebSsl.equals("true")) {
                     rmInfo.setPrivate_ssl(true);
                  } else {
                     rmInfo.setPrivate_ssl(false);
                  }
               } else {
                  rmInfo.setPrivate_mode(false);
               }

               rmDao.addRmServer(rmInfo);
               this.serverSetupDao.addExternalServerForMonitoring("RM", rmInfo.getIp_address(), errThresholdCnt + 1);
               this.serverSetupDao.setLastErrTime("RM", rmInfo.getIp_address());
               ConvertUtil cUtil = new ConvertUtil();
               newRmInfo = (V2RmServerEntity)ConvertUtil.convertObject(rmInfo, newRmInfo);
               return newRmInfo;
            } else {
               throw new BaseRestException("INVALID_PARAM", "MESSAGE_DEVICE_INPUT_SERVER_INFORMATION_P");
            }
         } else {
            throw new BaseRestException("INVALID_PARAM", "MIS_TEXT_IP_ADDRESS_INCORRECT_P");
         }
      } else {
         throw new BaseRestException("INVALID_PARAM", "MIS_MESSAGE_SETUP_ENTER_SERVERNAME_P");
      }
   }

   public List splayerRemoteServerDelete(String serverName) throws Exception {
      List records = new ArrayList();
      Map infoMap = null;
      Integer errThresholdCnt = null;
      new MenuEntity();
      infoMap = this.serverSetupDao.getServerInfoByOrgId(0L);
      errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
      RmServerImpl rmDao = RmServerImpl.getInstance();
      String serverNameList = StrUtils.nvl(serverName);
      RmServerEntity rmInfo = new RmServerEntity();
      V2RmServerEntity newRmInfo = new V2RmServerEntity();
      String[] arrServerName = serverNameList.split(",");
      int result = 0;

      for(int i = 0; i < arrServerName.length; ++i) {
         rmInfo.setServer_name(arrServerName[i]);
         rmInfo = rmDao.getRmServerInfo(arrServerName[i]);
         result += rmDao.deleteRmServer(rmInfo);
         this.serverSetupDao.deleteExternalServerForMonitoring("RM", rmInfo.getIp_address());
         ConvertUtil cUtil = new ConvertUtil();
         newRmInfo = (V2RmServerEntity)ConvertUtil.convertObject(rmInfo, newRmInfo);
         records.add(newRmInfo);
      }

      if (result > 0) {
         rmDao.setDeviceRmServerTriggering();
      }

      return records;
   }

   public void datalinkServerModeList(HttpServletRequest request) throws SQLException {
      MenuEntity resultMenu = new MenuEntity();
      Menu add = new Menu();
      add.setCmd("dataLink");
      add.setUrl(request.getContextPath() + "/setting/externalServerList.htm");
      add.setName("COM_BUTTON_ADD");
      resultMenu.putMenu("add", add);
      Menu delete = new Menu();
      delete.setCmd("dataLink");
      delete.setClassName("hasMultiChk");
      delete.setUrl(request.getContextPath() + "/setting/externalServerList.htm");
      delete.setName("COM_BUTTON_DELETE");
      resultMenu.putMenu("delete", delete);
      Menu assignTable = new Menu();
      assignTable.setCmd("datalink");
      assignTable.setUrl(request.getContextPath() + "/setting/externalServerList.htm");
      assignTable.setName("MIS_SID_ASSIGN_TABLE");
      resultMenu.putMenu("assignTable", assignTable);
      Menu tableWidth = new Menu();
      tableWidth.setTable("37px");
      tableWidth.setTable("10%");
      tableWidth.setTable("10%");
      tableWidth.setTable("10%");
      tableWidth.setTable("10%");
      tableWidth.setTable("10%");
      tableWidth.setTable("10%");
      tableWidth.setTable("10%");
      tableWidth.setTable("");
      resultMenu.putMenu("tableWidth", tableWidth);
      Menu tableHeader = new Menu();
      tableHeader.setTable("check");
      tableHeader.setTable("TABLE_SERVER_NAME_P");
      tableHeader.setTable("TABLE_IP_ADDR_P");
      tableHeader.setTable("TABLE_WEB_PORT_P");
      tableHeader.setTable("COM_SETUP_NEW_STRING26_P");
      tableHeader.setTable("COM_TEXT_PERIOD2_P");
      tableHeader.setTable("SSL");
      tableHeader.setTable("MIS_SID_20_BYPASS");
      tableHeader.setTable("COM_TV_SID_SERVER_NETWORK_SETTING");
      resultMenu.putMenu("tableHeader", tableHeader);
      resultMenu.setStatus("success");
   }

   public V2DatalinkEditByPassResource datalinkEditByPass(String serverName, V2DatalinkEditByPassFilter filter) throws Exception {
      V2DatalinkEditByPassResource resource = new V2DatalinkEditByPassResource();
      DatalinkServerEntity dlsEntity = new DatalinkServerEntity();
      String bypass = filter.getBypass();
      DatalinkServerImpl dlsDao = DatalinkServerImpl.getInstance();
      if (!serverName.equals("") && !bypass.equals("")) {
         DatalinkServerEntity dlsInfo = dlsDao.getDatalinkServerInfo(serverName);
         dlsEntity.setIp_address(dlsInfo.getIp_address());
         dlsEntity.setServer_name(serverName);
         dlsEntity.setBypass(Boolean.parseBoolean(bypass));
         dlsDao.updateDatalinkServer(dlsEntity);
         resource.setServerName(serverName);
         resource.setBypass(Boolean.parseBoolean(bypass));
         return resource;
      } else {
         throw new Exception("ALERT_FAIL");
      }
   }

   public V2DatalinkServerTablesUpdate updateDatalinkServerTables(String serverName) {
      DatalinkServerDao dataLinkServerDao = new DatalinkServerDao();
      List failedList = new ArrayList();
      List successfulList = new ArrayList();
      new ArrayList();
      V2DatalinkServerTablesUpdate resource = new V2DatalinkServerTablesUpdate();
      DatalinkServerEntity datalinkServer = null;
      String[] serverNames = serverName.split(",");
      if (serverName.length() == 0) {
         resource.setFailedList(failedList);
         return resource;
      } else {
         try {
            datalinkServer = dataLinkServerDao.getDatalinkServerInfo(serverNames[0]);
         } catch (SQLException var12) {
            this.logger.error("Error in updateDatalinkServerTables " + var12.getMessage());
            failedList.add(datalinkServer.getServer_name());
         }

         try {
            dataLinkServerDao.deleteDatalinkInfoServerTableByServer(datalinkServer.getServer_name());
         } catch (SQLException var11) {
            this.logger.error("Error in updateDatalinkServerTables " + var11.getMessage());
            failedList.add(datalinkServer.getServer_name());
         }

         try {
            this.updateDatalinkServerTable(datalinkServer);
            successfulList.add(datalinkServer.getServer_name());
            resource.setSuccessfulList(successfulList);
         } catch (Exception var10) {
            this.logger.error("Error in updateDatalinkServerTables " + var10.getMessage());
            failedList.add(datalinkServer.getServer_name());
         }

         resource.setFailedList(failedList);
         return resource;
      }
   }

   public String updateDatalinkServerTable(DatalinkServerEntity datalinkServer) throws Exception {
      DatalinkServerImpl dlsDao = DatalinkServerImpl.getInstance();
      String getDatalinkTableAPI = "getDataTableList.do";
      String https = datalinkServer.getUse_ssl() ? "https://" : "http://";
      String url = null;
      String body = null;
      Boolean usePrivate = datalinkServer.getPrivate_mode();
      int itemlistSize = false;
      if (usePrivate) {
         url = https + datalinkServer.getPrivate_ip_address() + ":" + datalinkServer.getPrivate_web_port() + "/DataLink/html/" + getDatalinkTableAPI;
      } else {
         url = https + datalinkServer.getIp_address() + ":" + datalinkServer.getPort() + "/DataLink/html/" + getDatalinkTableAPI;
      }

      DatalinkServerTableEntity dlkTableEntity = new DatalinkServerTableEntity();
      RestTemplate restTemplate = this.getRestTemplate();
      if (datalinkServer.getUse_ssl()) {
         body = this.updateDatalinkHttps(url);
      } else {
         body = this.updateDatalinkHttp(restTemplate, url);
      }

      if (body == "fail_to_connect") {
         return "fail_to_connect";
      } else if (body != null && !body.isEmpty() && body != "") {
         JAXBContext jaxbContext = JAXBContext.newInstance(new Class[]{DatalinkTableItemList.class});
         Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
         XMLStreamReader reader = DocumentUtils.getXMLInputFactoryInstance().createXMLStreamReader(new StringReader(body));
         JAXBElement datalinkTableItemList = jaxbUnmarshaller.unmarshal(reader, DatalinkTableItemList.class);
         if (datalinkTableItemList != null && datalinkTableItemList.getValue() != null && ((DatalinkTableItemList)datalinkTableItemList.getValue()).getItem() != null) {
            int itemlistSize = ((DatalinkTableItemList)datalinkTableItemList.getValue()).getItem().size();
            if (itemlistSize <= 0) {
               return "empty";
            } else {
               for(int i = 0; i < itemlistSize; ++i) {
                  String serviceName = ((DatalinkTableItem)((DatalinkTableItemList)datalinkTableItemList.getValue()).getItem().get(i)).getSvrcName();
                  String dynaName = ((DatalinkTableItem)((DatalinkTableItemList)datalinkTableItemList.getValue()).getItem().get(i)).getDynaName();
                  String tableName = ((DatalinkTableItem)((DatalinkTableItemList)datalinkTableItemList.getValue()).getItem().get(i)).getName();
                  Boolean isDataView = serviceName == null;
                  if (dynaName != null && tableName != null) {
                     dlkTableEntity.setIs_dataView(isDataView);
                     dlkTableEntity.setServer_name(datalinkServer.getServer_name());
                     if (serviceName == null) {
                        serviceName = tableName;
                     }

                     dlkTableEntity.setService_name(serviceName);
                     dlkTableEntity.setTable_name(tableName);
                     dlkTableEntity.setDyna_name(dynaName);
                     dlsDao.addDatalinkTableInfo(dlkTableEntity);
                  }
               }

               return "success";
            }
         } else {
            return "empty";
         }
      } else {
         return "empty";
      }
   }

   private String updateDatalinkHttp(RestTemplate restTemplate, String url) throws ResourceAccessException, ReadTimeoutException {
      HttpHeaders headers = new HttpHeaders();
      headers.set("Accept", "text/plain;charset=utf-8");
      org.springframework.http.HttpEntity entity = new org.springframework.http.HttpEntity(headers);
      ResponseEntity responseData = restTemplate.exchange(url, HttpMethod.GET, entity, String.class, new Object[0]);
      return (String)responseData.getBody();
   }

   private String updateDatalinkHttps(String datalinkUrl) throws Exception {
      new String();
      HttpHeaders headers = new HttpHeaders();
      headers.set("Accept", "text/plain;charset=utf-8");
      org.springframework.http.HttpEntity entity = new org.springframework.http.HttpEntity(headers);
      URL url = new URL(datalinkUrl);
      HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
      conn.setHostnameVerifier(new HostnameVerifier() {
         public boolean verify(String hostname, SSLSession session) {
            return true;
         }
      });
      SSLContext context = SSLContext.getInstance("TLS");
      TrustManager[] trustAllCerts = new TrustManager[]{new X509ExtendedTrustManager() {
         public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
         }

         public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
            FileInputStream fis = null;

            try {
               String keyStoreIdentityPath = "";
               String keyStoreIdentityPassword = "";
               keyStoreIdentityPath = CommonConfig.get("keystore.identity.path");
               keyStoreIdentityPassword = CommonConfig.get("keystore.identity.password");
               KeyStore trustStore = KeyStore.getInstance("JKS");
               fis = new FileInputStream(keyStoreIdentityPath);
               trustStore.load(fis, keyStoreIdentityPassword.toCharArray());
               fis.close();
               TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
               tmf.init(trustStore);
               TrustManager[] tms = tmf.getTrustManagers();
               ((X509TrustManager)tms[0]).checkServerTrusted(chain, authType);
            } catch (KeyStoreException var23) {
            } catch (NoSuchAlgorithmException var24) {
            } catch (IOException var25) {
            } catch (ConfigException var26) {
               V2ExternalServerManagementServiceImpl.this.logger.error("", var26);
            } finally {
               try {
                  fis.close();
               } catch (IOException var22) {
               }

            }

         }

         public X509Certificate[] getAcceptedIssuers() {
            return null;
         }

         public void checkClientTrusted(X509Certificate[] x509Certificates, String s, Socket socket) throws CertificateException {
         }

         public void checkServerTrusted(X509Certificate[] x509Certificates, String s, Socket socket) throws CertificateException {
         }

         public void checkClientTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) throws CertificateException {
         }

         public void checkServerTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) throws CertificateException {
         }
      }};
      context.init((KeyManager[])null, trustAllCerts, (SecureRandom)null);
      SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(context, NoopHostnameVerifier.INSTANCE);
      Registry registry = RegistryBuilder.create().register("http", new PlainConnectionSocketFactory()).register("https", csf).build();
      PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager(registry);
      cm.setMaxTotal(100);
      CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(csf).setConnectionManager(cm).build();
      HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
      requestFactory.setHttpClient(httpClient);
      RestTemplate restTemplate = new RestTemplate(requestFactory);
      ResponseEntity response = restTemplate.exchange(datalinkUrl, HttpMethod.GET, entity, String.class, new Object[0]);
      String body = (String)response.getBody();
      return body;
   }

   private RestTemplate getRestTemplate() {
      HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
      factory.setConnectTimeout(1000);
      factory.setReadTimeout(1000);
      RestTemplate restTemplate = new RestTemplate(factory);
      return restTemplate;
   }
}
