package com.samsung.magicinfo.service.user;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.LDAPUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceGroupDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceMenuManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceMenuManagerImpl;
import com.samsung.magicinfo.framework.role.manager.RoleInfo;
import com.samsung.magicinfo.framework.role.manager.RoleInfoImpl;
import com.samsung.magicinfo.framework.setup.dao.LdapServerDao;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.entity.UserLdapSync;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserLdapSyncInfo;
import com.samsung.magicinfo.framework.user.manager.UserLdapSyncInfoImpl;
import com.samsung.magicinfo.protocol.util.BeanUtils;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManager;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManagerImpl;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.ftpserver.ftplet.UserManager;
import org.apache.ftpserver.usermanager.DbUserManagerFactory;
import org.apache.ftpserver.usermanager.impl.BaseUser;
import org.apache.logging.log4j.Logger;

public class LDAPService {
   private static UserInfo userInfo = UserInfoImpl.getInstance();
   private static LdapServerDao ldapServerDao = new LdapServerDao();
   protected static Logger logger = LoggingManagerV2.getLogger(LDAPService.class);
   private static List userLdapSyncList = new LinkedList();

   public LDAPService() {
      super();
   }

   public static ArrayList getSearchLDAPUserList(Long orgId, String selectDir, String filter) {
      Map ldapMap = ldapServerDao.getLdapServerInfo(orgId);
      String url = ldapMap.get("LDAP_SERVER").toString();
      String dn = ldapMap.get("LDAP_ROOT_DN").toString();
      String id = ldapMap.get("LDAP_MANAGER_DN").toString();
      String password = ldapMap.get("LDAP_MANAGER_PASSWORD").toString();
      String encVersion = ldapMap.containsKey("LDAP_ENC_VERSION") ? ldapMap.get("LDAP_ENC_VERSION").toString() : null;
      if (url.indexOf("ldap://") != 0 && url.indexOf("ldaps://") != 0) {
         url = "ldap://" + url;
      }

      password = LDAPUtils.getDec_password(id, password, encVersion);
      int result = LDAPUtils.checkLDAPServer(url, dn, id, password);
      Hashtable env = LDAPUtils.setInfo(url, dn, id, password);
      ArrayList userSearchList = null;
      if (result == 0) {
         userSearchList = LDAPUtils.getSearchUserList(env, dn, selectDir, filter, 2);
      }

      return userSearchList;
   }

   public static ArrayList getSearchLDAPUserList(Long orgId, String selectDir, String filter, int searchScope) throws Exception {
      Map ldapMap = ldapServerDao.getLdapServerInfo(orgId);
      String url = ldapMap.get("LDAP_SERVER").toString();
      String dn = ldapMap.get("LDAP_ROOT_DN").toString();
      String id = ldapMap.get("LDAP_MANAGER_DN").toString();
      String password = ldapMap.get("LDAP_MANAGER_PASSWORD").toString();
      String encVersion = ldapMap.containsKey("LDAP_ENC_VERSION") ? ldapMap.get("LDAP_ENC_VERSION").toString() : null;
      if (url.indexOf("ldap://") != 0 && url.indexOf("ldaps://") != 0) {
         url = "ldap://" + url;
      }

      password = LDAPUtils.getDec_password(id, password, encVersion);
      int result = LDAPUtils.checkLDAPServer(url, dn, id, password);
      if (result != 0) {
         throw new Exception("LDAP Login Failed. Error Code : " + result);
      } else {
         Hashtable env = LDAPUtils.setInfo(url, dn, id, password);
         ArrayList userSearchList = null;
         if (result == 0) {
            userSearchList = LDAPUtils.getSearchUserList(env, dn, selectDir, filter, searchScope);
         }

         return userSearchList;
      }
   }

   public static ArrayList getLDAPUserList(Long orgId) {
      ServerSetupInfo serverSetupInfo = ServerSetupInfoImpl.getInstance();
      Map ldapMap = ldapServerDao.getLdapServerInfo(orgId);
      String url = ldapMap.get("LDAP_SERVER").toString();
      String dn = ldapMap.get("LDAP_ROOT_DN").toString();
      String id = ldapMap.get("LDAP_MANAGER_DN").toString();
      String password = ldapMap.get("LDAP_MANAGER_PASSWORD").toString();
      String encVersion = ldapMap.containsKey("LDAP_ENC_VERSION") ? ldapMap.get("LDAP_ENC_VERSION").toString() : null;
      if (url.indexOf("ldap://") != 0 && url.indexOf("ldaps://") != 0) {
         url = "ldap://" + url;
      }

      password = LDAPUtils.getDec_password(id, password, encVersion);
      int result = LDAPUtils.checkLDAPServer(url, dn, id, password);
      Hashtable env = LDAPUtils.setInfo(url, dn, id, password);
      ArrayList resultList = null;
      if (result == 0) {
         resultList = LDAPUtils.getInfoList(env, dn);
      }

      return resultList;
   }

   public static int getLdapLoginStatus(Long org_id, String full_id, String ldap_id, String password) throws SQLException {
      Map ldapMap = ldapServerDao.getLdapServerInfo(org_id);
      String url = ldapMap.get("LDAP_SERVER").toString();
      String dn = ldapMap.get("LDAP_ROOT_DN").toString();
      if (url.indexOf("ldap://") != 0 && url.indexOf("ldaps://") != 0) {
         url = "ldap://" + url;
      }

      int result = LDAPUtils.checkLDAPServer(url, dn, full_id, password);
      return result;
   }

   public static boolean getLdapServerStatus(Long orgId) {
      LdapServerDao ldapServerDao = new LdapServerDao();
      boolean result = ldapServerDao.getLdapServerStatus(orgId);
      return result;
   }

   public static LdapCheckResult ldapCheck(String userId, String selectId, String ldapInfo, String password) {
      try {
         boolean canLoginStatus = true;
         boolean ldapLoginStatus = false;
         int ldapLoginResult = -1;
         List ldapUserInfo = userInfo.getLdapUserInfo(userId);
         userInfo.getOrganNameByUserId(userId);
         Long orgId = userInfo.getRootGroupIdByUserId(userId);
         if (ldapUserInfo != null && ldapUserInfo.size() > 1) {
            canLoginStatus = false;
            ldapLoginStatus = true;
         } else if (!selectId.equals("SEL_USER") && ldapUserInfo != null && ldapUserInfo.size() != 0) {
            Map ldapUserMap = (Map)ldapUserInfo.get(0);
            if (ldapUserMap.get("LDAP_INFO") != null && !ldapUserMap.get("LDAP_INFO").equals("")) {
               ldapInfo = StringEscapeUtils.unescapeHtml(ldapUserMap.get("LDAP_INFO").toString());
               selectId = "SEL_USER";
               userId = ldapUserMap.get("USER_ID").toString();
            } else {
               canLoginStatus = true;
            }
         }

         if (selectId.equals("SEL_USER")) {
            if (ldapInfo != null && !ldapInfo.equals("")) {
               logger.error("[LDAP LOGIN STATUS] LDAP Info : " + ldapInfo);
               ldapLoginResult = getLdapLoginStatus(orgId, ldapInfo, userId, password);
               password = "LDAP";
               if (ldapLoginResult == 0) {
                  canLoginStatus = true;
               } else {
                  canLoginStatus = false;
                  ldapLoginStatus = false;
               }
            } else {
               canLoginStatus = true;
            }
         }

         return new LdapCheckResult(userId, password, canLoginStatus, ldapLoginStatus, ldapLoginResult);
      } catch (SQLException var10) {
         throw new RuntimeException("An unexpected error when getting LDAP login status", var10);
      }
   }

   public static String getLDAPPassword(String userId, String password) {
      try {
         User ldapUser = userInfo.getUserByUserId(userId);
         Long orgId = userInfo.getRootGroupIdByUserId(userId);
         if (ldapUser != null && ldapUser.getLdap_info() != null && !ldapUser.getLdap_info().equals("")) {
            int result = getLdapLoginStatus(orgId, ldapUser.getLdap_info().toString(), ldapUser.getLdap_user_id().toString(), password);
            if (result == 0) {
               return "LDAP";
            }
         }

         return null;
      } catch (SQLException var5) {
         throw new RuntimeException("Error when getting LDAP user password", var5);
      }
   }

   public static LDAPUtils.LdapTreeNode getTreeOrganUnit(Long orgId) {
      Map ldapMap = ldapServerDao.getLdapServerInfo(orgId);
      String url = ldapMap.get("LDAP_SERVER").toString();
      String dn = ldapMap.get("LDAP_ROOT_DN").toString();
      String id = ldapMap.get("LDAP_MANAGER_DN").toString();
      String password = ldapMap.get("LDAP_MANAGER_PASSWORD").toString();
      String encVersion = ldapMap.containsKey("LDAP_ENC_VERSION") ? ldapMap.get("LDAP_ENC_VERSION").toString() : null;
      if (url.indexOf("ldap://") != 0 && url.indexOf("ldaps://") != 0) {
         url = "ldap://" + url;
      }

      password = LDAPUtils.getDec_password(id, password, encVersion);
      LDAPUtils.checkLDAPServer(url, dn, id, password);
      Hashtable env = LDAPUtils.setInfo(url, dn, id, password);
      LDAPUtils.LdapTreeNode rootNode = LDAPUtils.getOrganUnitTree(env, dn);
      return rootNode;
   }

   private static void sync() {
      logger.info("[MagicInfo_UserLdapSync] sync() start.");

      while(true) {
         synchronized(userLdapSyncList) {
            if (userLdapSyncList.size() == 0) {
               break;
            }

            Long id = (Long)userLdapSyncList.get(0);

            try {
               startUserLdapSync(id);
               userLdapSyncList.remove(id);
            } catch (Exception var4) {
               userLdapSyncList.clear();
               logger.error(var4);
               return;
            }
         }
      }

      logger.info("[MagicInfo_UserLdapSync] sync() end.");
   }

   public static void startUserLdapSync(Long[] arrId) {
      boolean runSync = false;
      if (userLdapSyncList.size() == 0) {
         runSync = true;
      }

      Long[] var2 = arrId;
      int var3 = arrId.length;

      for(int var4 = 0; var4 < var3; ++var4) {
         Long id = var2[var4];
         if (id != null) {
            if (userLdapSyncList.contains(id)) {
               logger.error("[MagicInfo_UserLdapSync] already syncing. ID = " + id);
            } else {
               userLdapSyncList.add(id);
            }
         }
      }

      if (runSync) {
         Thread thread = new Thread(new Runnable() {
            public void run() {
               LDAPService.sync();
            }
         });
         thread.start();
      }

   }

   private static void startUserLdapSync(long id) {
      try {
         RoleInfo roleInfo = RoleInfoImpl.getInstance();
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         UserLdapSyncInfo userLdapSyncInfo = UserLdapSyncInfoImpl.getInstance();
         UserLdapSync userLdapSync = userLdapSyncInfo.getUserLdapSyncById(id);
         DeviceGroupDao device_dao = new DeviceGroupDao();
         long orgId = userGroupInfo.getOrganizationGroupIdByGroupId(userLdapSync.getGroup_id());
         List ldapUserList = null;
         ldapUserList = getSearchLDAPUserList(orgId, userLdapSync.getLdap_group(), "*", 1);
         Map ldapIdUserMap = new HashMap();
         Iterator var11 = ldapUserList.iterator();

         while(var11.hasNext()) {
            Map map = (Map)var11.next();
            String tmpId = map.get("id").toString();
            String userId = tmpId.substring(tmpId.indexOf("=") + 1).trim();
            if (areNameCharactersValid(userId)) {
               ldapIdUserMap.put(userId, map);
            }
         }

         Map searchCondition = new HashMap();
         searchCondition.put("group_id", userLdapSync.getGroup_id() + "");
         List userList = userInfo.getAllUserList((Map)searchCondition);
         Iterator itr = userList.iterator();

         while(itr.hasNext()) {
            User user = (User)itr.next();
            if (!StringUtils.isBlank(user.getLdap_info())) {
               String userId = user.getUser_id();
               String ldapId = user.getLdap_user_id();
               if (ldapIdUserMap.containsKey(ldapId)) {
                  Map ldapUserInfo = (Map)ldapIdUserMap.get(ldapId);
                  ldapIdUserMap.remove(ldapId);
                  updateLdapUserInfo(roleInfo, userGroupInfo, userLdapSync, device_dao, orgId, user, userId, ldapUserInfo);
               } else {
                  try {
                     User deleteUser = userInfo.getUserInfo(userId);
                     ContentInfo contentInfo = ContentInfoImpl.getInstance();
                     contentInfo.transferContentToAdmin(deleteUser);
                     contentInfo.deleteGroupByCreatorId(userId);
                     PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
                     playlistInfo.transferPlaylistToAdmin(deleteUser);
                     playlistInfo.deleteGroupByCreatorId(userId);
                     userInfo.deleteUser(userId);
                     logger.error("[MagicInfo_UserLdapSync] Remove User : " + userId);
                  } catch (Exception var21) {
                     logger.error(var21.getMessage());
                  }
               }
            }
         }

         Iterator var28 = ldapIdUserMap.entrySet().iterator();

         while(var28.hasNext()) {
            Entry item = (Entry)var28.next();
            Map map = (Map)item.getValue();

            try {
               if (isExistLdapUserInfo(map)) {
                  String tmpId = map.get("id").toString();
                  String userId = tmpId.substring(tmpId.indexOf("=") + 1).trim();
                  User selectedUser = userInfo.getAllByUserId(userId);
                  updateLdapUserInfo(roleInfo, userGroupInfo, userLdapSync, device_dao, orgId, selectedUser, userId, map);
               } else {
                  addLdapUser(orgId, map, userLdapSync, device_dao);
               }
            } catch (Exception var20) {
               logger.error("[MagicInfo_UserLdapSync] Error : " + var20.getMessage());
            }
         }

         userLdapSyncInfo.updateLastSyncTime(id);
      } catch (SQLException var22) {
         logger.error("", var22);
      } catch (Exception var23) {
         logger.error("[MagicInfo_UserLdapSync] " + var23.getMessage(), var23);
      }

   }

   private static boolean isExistLdapUserInfo(Map map) throws SQLException {
      String fullId = map.get("full_id").toString().trim();
      String tmpId = map.get("id").toString();
      String userId = tmpId.substring(tmpId.indexOf("=") + 1).trim();
      boolean ret = true;
      List ldapUserInfo = userInfo.getLdapUserInfo(userId);
      if (ldapUserInfo.isEmpty()) {
         ret = false;
      }

      return ret;
   }

   private static void updateLdapUserInfo(RoleInfo roleInfo, UserGroupInfo userGroupInfo, UserLdapSync userLdapSync, DeviceGroupDao device_dao, long orgId, User user, String userId, Map ldapUserInfo) throws SQLException {
      String userName = null;
      PIIDataManager piiDataManager = (PIIDataManagerImpl)BeanUtils.getBean("PIIDataManager");
      if (ldapUserInfo.get("username") != null) {
         userName = ldapUserInfo.get("username").toString();
      } else if (ldapUserInfo.get("displayname") != null) {
         userName = ldapUserInfo.get("displayname").toString();
      }

      String mail = "";
      if (ldapUserInfo.get("mail") != null) {
         mail = ldapUserInfo.get("mail").toString();
      }

      String phone = "";
      if (ldapUserInfo.get("phone") != null) {
         phone = ldapUserInfo.get("phone").toString();
      }

      String mobile = "";
      if (ldapUserInfo.get("mobile") != null) {
         mobile = ldapUserInfo.get("mobile").toString();
      }

      boolean userInfoChanged = false;
      if (userName != null && !user.getUser_name().equals(userName)) {
         userInfoChanged = true;
         user.setUser_name(piiDataManager.encryptData(userName, "name"));
      }

      if (!user.getEmail().equals(mail)) {
         userInfoChanged = true;
         user.setEmail(piiDataManager.encryptData(mail, "email"));
      }

      if (!user.getPhone_num().equals(phone)) {
         userInfoChanged = true;
         user.setPhone_num(piiDataManager.encryptData(phone, "phone"));
      }

      if (!user.getMobile_num().equals(mobile)) {
         userInfoChanged = true;
         user.setMobile_num(piiDataManager.encryptData(mobile, "phone"));
      }

      if (ldapUserInfo.get("full_id") != null) {
         user.setLdap_info(ldapUserInfo.get("full_id").toString().trim());
      }

      Long roleIdl;
      if (!userLdapSync.getGroup_id().equals(user.getGroup_id())) {
         try {
            ContentInfo contentInfo = ContentInfoImpl.getInstance();
            contentInfo.transferContentToAdmin(user);
            contentInfo.deleteGroupByCreatorId(user.getUser_id());
            PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
            playlistInfo.transferPlaylistToAdmin(user);
            playlistInfo.deleteGroupByCreatorId(user.getUser_id());
            roleIdl = userLdapSync.getGroup_id();
            user.setGroup_id(roleIdl);
            String groupName = userGroupInfo.getGroupNameByGroupId(user.getGroup_id());
            user.setGroup_name(groupName);
            UserGroup userGroup = userGroupInfo.getGroupById(roleIdl);
            user.setRoot_group_id(userGroup.getRoot_group_id());
            String organization = userGroupInfo.getGroupNameByGroupId(orgId);
            user.setOrganization(organization);
            userGroupInfo.setUserGroupMapGroupUserIdByUserId(userId, roleIdl);
            contentInfo.addDefaultGroup(user.getUser_id(), orgId);
            playlistInfo.addDefaultGroup(user.getUser_id(), orgId);
            userInfoChanged = true;
         } catch (Exception var25) {
            logger.error(var25.getMessage());
         }
      }

      String roleName = user.getRole_name();
      String settingRoleName = roleInfo.getNameByRoleId(userLdapSync.getRole_id());
      if (!roleName.equals(settingRoleName)) {
         userInfoChanged = true;
         user.setRole_name(settingRoleName);
      }

      if (userInfoChanged) {
         roleIdl = roleInfo.getRoleIdByRoleName(settingRoleName);
         roleInfo.setRoleIdMapRoleUserByUserId(roleIdl, userId);
         userInfo.setUser(user);
      }

      if (!roleName.equals(settingRoleName)) {
         if (roleName.equals("Content Manager")) {
            userInfo.setContentApprover(userId, "N");
         } else if (roleName.equals("Device Manager")) {
            device_dao.addPermissionsDeviceGroup(userId, "");
         }
      }

      boolean settingContentApprover;
      if (userLdapSync.getContent_approver() != null) {
         settingContentApprover = userLdapSync.getContent_approver().equalsIgnoreCase("Y");
      } else {
         settingContentApprover = false;
      }

      boolean userContentApprover;
      if (user.getContent_approver() != null) {
         userContentApprover = user.getContent_approver().equalsIgnoreCase("Y");
      } else {
         userContentApprover = false;
      }

      if (settingContentApprover != userContentApprover) {
         userInfo.setContentApprover(userId, settingContentApprover ? "Y" : "N");
      }

      String[] settingPermissionDeviceGroups;
      int i;
      int i;
      if (StringUtils.isNotBlank(userLdapSync.getPermission_device_group())) {
         String[] userPermissionDeviceGroups = userLdapSync.getPermission_device_group().split(",");
         List list = device_dao.getPermissionsDeviceGroup(userId);
         if (userPermissionDeviceGroups.length == list.size()) {
            settingPermissionDeviceGroups = new String[list.size()];

            for(i = 0; i < list.size(); ++i) {
               settingPermissionDeviceGroups[i] = ((DeviceGroup)list.get(i)).getGroup_id() + "";
            }

            Arrays.sort(userPermissionDeviceGroups);
            Arrays.sort(settingPermissionDeviceGroups);
            boolean same = true;

            for(i = 0; i < userPermissionDeviceGroups.length; ++i) {
               if (!userPermissionDeviceGroups[i].equals(settingPermissionDeviceGroups[i])) {
                  same = false;
                  break;
               }
            }

            if (!same) {
               device_dao.addPermissionsDeviceGroup(userId, userLdapSync.getPermission_device_group());
            }
         } else {
            device_dao.addPermissionsDeviceGroup(userId, userLdapSync.getPermission_device_group());
         }
      }

      String notificationSetting = userLdapSync.getNotification_setting();
      if (StringUtils.isNotBlank(notificationSetting)) {
         String[] notificationSettingList = notificationSetting.split(",");
         userInfo.deleteEmailNotificationByOrdIdAndUserId(orgId, userId);
         settingPermissionDeviceGroups = notificationSettingList;
         i = notificationSettingList.length;

         for(i = 0; i < i; ++i) {
            String noti = settingPermissionDeviceGroups[i];
            userInfo.setEmailNotificationOption(orgId, userId, noti, true);
         }
      } else {
         userInfo.deleteEmailNotificationByOrdIdAndUserId(orgId, userId);
      }

   }

   private static void addLdapUser(Long orgId, Map map, UserLdapSync userLdapSync, DeviceGroupDao device_dao) throws SQLException {
      String userName = null;
      String tmpId = map.get("id").toString();
      String userId = tmpId.substring(tmpId.indexOf("=") + 1).trim();
      String fullId = map.get("full_id").toString().trim();
      if (map.get("username") != null) {
         userName = map.get("username").toString();
      } else if (map.get("displayname") != null) {
         userName = map.get("displayname").toString();
      }

      if (userName == null) {
         userName = userId;
      }

      String mail = "";
      if (map.get("mail") != null) {
         mail = map.get("mail").toString();
      }

      String phone = "";
      if (map.get("phone") != null) {
         phone = map.get("phone").toString();
      }

      String mobile = "";
      if (map.get("mobile") != null) {
         mobile = map.get("mobile").toString();
      }

      String syncUserId = saveUser(userId, fullId, userName, mail, phone, mobile, userLdapSync);
      if (userLdapSync.getContent_approver().equalsIgnoreCase("Y")) {
         userInfo.setContentApprover(syncUserId, "Y");
      }

      if (StringUtils.isNotBlank(userLdapSync.getPermission_device_group())) {
         User user = userInfo.getUserInfo(syncUserId);
         if (DeviceUtils.isDeviceGroupAuth(user)) {
            device_dao = new DeviceGroupDao();
            device_dao.addPermissionsDeviceGroup(syncUserId, userLdapSync.getPermission_device_group());
         } else {
            logger.error("[MagicInfo_UserLdapSync] Error (No Device Manager) : " + user.getUser_id());
         }
      }

      String notificationSetting = userLdapSync.getNotification_setting();
      if (StringUtils.isNotBlank(notificationSetting)) {
         String[] notificationSettingList = notificationSetting.split(",");

         for(int i = 0; i < notificationSettingList.length; ++i) {
            userInfo.setEmailNotificationOption(orgId, syncUserId, notificationSettingList[i], true);
         }
      }

      logger.info("[MagicInfo_UserLdapSync] Add User : " + map.get("id") + ", " + map.get("displayname") + ", " + map.get("mail"));
   }

   private static String getUniqueId(String id) throws SQLException {
      for(int count = 0; count < 1000; ++count) {
         User user = userInfo.getUserByUserId(id);
         if (user == null) {
            return id;
         }

         String tmpId = user.getUser_id();
         String[] arr = tmpId.split("_");
         if (arr.length == 1) {
            id = tmpId + "_0";
         } else if (NumberUtils.isParsable(arr[arr.length - 1])) {
            int n = Integer.parseInt(arr[arr.length - 1]) + 1;
            id = tmpId.substring(0, tmpId.lastIndexOf("_") + 1) + n;
         } else {
            id = tmpId + "_0";
         }
      }

      return "";
   }

   private static String saveUser(String id, String fullId, String name, String email, String phone, String mobile, UserLdapSync userLdapSync) {
      try {
         String ldapId = id;
         String password = "LDAP";
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         RoleInfo roleInfo = RoleInfoImpl.getInstance();
         PIIDataManager piiDataManager = (PIIDataManagerImpl)BeanUtils.getBean("PIIDataManager");
         long orgId = userGroupInfo.getOrganizationGroupIdByGroupId(userLdapSync.getGroup_id());
         String organization = userGroupInfo.getGroupNameByGroupId(orgId);
         id = getUniqueId(id);
         User user = new User();
         user.setUser_id(id);
         user.setLdap_user_id(ldapId);
         user.setUser_name(piiDataManager.encryptData(name, "name"));
         user.setPassword(password);
         user.setEmail(piiDataManager.encryptData(email, "email"));
         user.setPhone_num(piiDataManager.encryptData(phone, "phone"));
         user.setMobile_num(piiDataManager.encryptData(mobile, "phone"));
         user.setRole_name(roleInfo.getNameByRoleId(userLdapSync.getRole_id()));
         user.setGroup_name(userGroupInfo.getGroupNameByGroupId(userLdapSync.getGroup_id()));
         user.setOrganization(organization);
         user.setTeam("");
         user.setJob_position("");
         user.setIs_approved("Y");
         user.setIs_deleted("N");
         user.setRoot_group_id(userGroupInfo.getOrgGroupIdByName(organization));
         user.setLdap_info(fullId);
         userInfo.addUser(user, true);
         BaseUser ftpUser = new BaseUser();
         ftpUser.setName(id);
         ftpUser.setPassword(password);
         UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
         userMgr.save(ftpUser);
         ContentInfo contentInfo = ContentInfoImpl.getInstance();
         contentInfo.addDefaultGroup(id);
         PlaylistInfo playlintInfo = PlaylistInfoImpl.getInstance();
         playlintInfo.addDefaultGroup(id);
         DeviceMenuManager menuDao = DeviceMenuManagerImpl.getInstance();
         menuDao.addDeviceMapMenuUser(id);
         return id;
      } catch (Exception var21) {
         logger.error(var21.getMessage());
         return "";
      }
   }

   public static void V2StartUserLdapSync(Long[] arrId) {
      boolean runSync = false;
      if (userLdapSyncList.size() == 0) {
         runSync = true;
      }

      Long[] var2 = arrId;
      int var3 = arrId.length;

      for(int var4 = 0; var4 < var3; ++var4) {
         Long id = var2[var4];
         if (id != null) {
            if (userLdapSyncList.contains(id)) {
               logger.error("[MagicInfo_UserLdapSync] already syncing. ID = " + id);
            } else {
               userLdapSyncList.add(id);
            }
         }
      }

      if (runSync) {
         Thread thread = new Thread(new Runnable() {
            public void run() {
               LDAPService.V2Sync();
            }
         });
         thread.start();
      }

   }

   private static void V2Sync() {
      logger.info("[MagicInfo_UserLdapSync] sync() start.");

      while(true) {
         synchronized(userLdapSyncList) {
            if (userLdapSyncList.size() == 0) {
               break;
            }

            Long id = (Long)userLdapSyncList.get(0);

            try {
               V2StartUserLdapSync(id);
               userLdapSyncList.remove(id);
            } catch (Exception var4) {
               userLdapSyncList.clear();
               logger.error(var4);
               return;
            }
         }
      }

      logger.info("[MagicInfo_UserLdapSync] sync() end.");
   }

   public static boolean areNameCharactersValid(String name) {
      if (!StrUtils.isEmpty(name) && name.length() >= 3 && name.length() <= 64) {
         String pattern = "^[a-zA-Z0-9.]+$";
         return name.matches(pattern) && !name.equals("admin");
      } else {
         return false;
      }
   }

   private static void V2StartUserLdapSync(long id) {
      try {
         RoleInfo roleInfo = RoleInfoImpl.getInstance();
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         UserLdapSyncInfo userLdapSyncInfo = UserLdapSyncInfoImpl.getInstance();
         UserLdapSync userLdapSync = userLdapSyncInfo.getUserLdapSyncById(id);
         DeviceGroupDao device_dao = new DeviceGroupDao();
         long orgId = userGroupInfo.getOrganizationGroupIdByGroupId(userLdapSync.getGroup_id());
         List ldapUserList = null;
         ldapUserList = getSearchLDAPUserList(orgId, userLdapSync.getLdap_group(), "*", 1);
         Map ldapIdUserMap = new HashMap();
         Iterator var11 = ldapUserList.iterator();

         while(var11.hasNext()) {
            Map map = (Map)var11.next();
            String tmpId = map.get("id").toString();
            String userId = tmpId.substring(tmpId.indexOf("=") + 1).trim();
            if (areNameCharactersValid(userId)) {
               ldapIdUserMap.put(userId, map);
            }
         }

         Map searchCondition = new HashMap();
         searchCondition.put("group_id", userLdapSync.getGroup_id() + "");
         List userList = userInfo.getAllUserList((Map)searchCondition);
         Iterator itr = userList.iterator();

         while(itr.hasNext()) {
            User user = (User)itr.next();
            if (!StringUtils.isBlank(user.getLdap_info())) {
               String userId = user.getUser_id();
               String ldapId = user.getLdap_user_id();
               if (ldapIdUserMap.containsKey(ldapId)) {
                  Map ldapUserInfo = (Map)ldapIdUserMap.get(ldapId);
                  ldapIdUserMap.remove(ldapId);
                  updateLdapUserInfo(roleInfo, userGroupInfo, userLdapSync, device_dao, orgId, user, userId, ldapUserInfo);
               } else {
                  try {
                     User deleteUser = userInfo.getUserInfo(userId);
                     ContentInfo contentInfo = ContentInfoImpl.getInstance();
                     contentInfo.transferContentToAdmin(deleteUser);
                     contentInfo.deleteGroupByCreatorId(userId);
                     PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
                     playlistInfo.transferPlaylistToAdmin(deleteUser);
                     playlistInfo.deleteGroupByCreatorId(userId);
                     userInfo.deleteUser(userId);
                     logger.error("[MagicInfo_UserLdapSync] Remove User : " + userId);
                  } catch (Exception var23) {
                     logger.error(var23.getMessage());
                  }
               }
            }
         }

         Iterator var30 = ldapIdUserMap.entrySet().iterator();

         while(var30.hasNext()) {
            Entry item = (Entry)var30.next();
            Map map = (Map)item.getValue();

            try {
               if (isExistLdapUserInfo(map)) {
                  String tmpId = map.get("id").toString();
                  String userId = tmpId.substring(tmpId.indexOf("=") + 1).trim();
                  User selectedUser = userInfo.getAllByUserId(userId);
                  if (StrUtils.nvl(selectedUser.getLdap_info()).equals("")) {
                     ContentInfo contentInfo = ContentInfoImpl.getInstance();
                     contentInfo.transferContentToAdmin(selectedUser);
                     contentInfo.deleteGroupByCreatorId(userId);
                     PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
                     playlistInfo.transferPlaylistToAdmin(selectedUser);
                     playlistInfo.deleteGroupByCreatorId(userId);
                     userInfo.deleteUser(userId);
                     addLdapUser(orgId, map, userLdapSync, device_dao);
                  } else {
                     updateLdapUserInfo(roleInfo, userGroupInfo, userLdapSync, device_dao, orgId, selectedUser, userId, map);
                  }
               } else {
                  addLdapUser(orgId, map, userLdapSync, device_dao);
               }
            } catch (Exception var22) {
               logger.error("[MagicInfo_UserLdapSync] Error : " + var22.getMessage());
            }
         }

         userLdapSyncInfo.updateLastSyncTime(id);
      } catch (SQLException var24) {
         logger.error("", var24);
      } catch (Exception var25) {
         logger.error("[MagicInfo_UserLdapSync] " + var25.getMessage(), var25);
      }

   }
}
