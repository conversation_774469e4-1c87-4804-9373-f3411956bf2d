package com.samsung.magicinfo.webauthor.openapi.service.impl;

import com.samsung.magicinfo.webauthor.openapi.service.OpenAPIClientService;
import com.samsung.magicinfo.webauthor.util.Common;
import com.samsung.magicinfo.webauthor2.exception.service.ConnectionException;
import java.util.HashMap;
import java.util.Map;
import javax.xml.transform.Source;
import javax.xml.transform.dom.DOMSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.xml.xpath.XPathOperations;

@Service
public class OpenAPIClientServiceImpl implements OpenAPIClientService {
  @Autowired
  private RestTemplate restTemplate;
  
  @Autowired
  private XPathOperations xpathTemplate;
  
  private static final String INSPIRE_TOKEN_API = "/openapi/auth?cmd=inspireToken&token={token}";
  
  private static final String GET_AUTH_TOKEN_API = "/openapi/auth?cmd=getAuthToken&id={userId}&pw={password}";
  
  public boolean inspireToken(String token) {
    Map<String, String> vars = new HashMap<>();
    vars.put("token", token);
    String xmlString = (String)this.restTemplate.postForObject(Common.MIP_WEB_URL_LOCAL + "/openapi/auth?cmd=inspireToken&token={token}", "", String.class, vars);
    return (!xmlString.isEmpty() && xmlString.contains("code=\"0\""));
  }
  
  public String getAuthenticationToken(String userId, String password) throws ConnectionException {
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", userId);
    vars.put("password", password);
    Source xmlResponse = (Source)this.restTemplate.postForObject(Common.MIP_WEB_URL_LOCAL + "/openapi/auth?cmd=getAuthToken&id={userId}&pw={password}", "", DOMSource.class, vars);
    String errorResult = this.xpathTemplate.evaluateAsString("/response[@code!='0']/errorMessage", xmlResponse);
    if (errorResult != null && !errorResult.isEmpty())
      throw new ConnectionException(errorResult); 
    return this.xpathTemplate.evaluateAsString("/response[@code='0']/responseClass/text()", xmlResponse);
  }
}
