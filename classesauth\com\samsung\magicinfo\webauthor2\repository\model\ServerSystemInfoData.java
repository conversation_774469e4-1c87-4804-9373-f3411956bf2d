package com.samsung.magicinfo.webauthor2.repository.model;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlElement;

public class ServerSystemInfoData implements Serializable {
  @XmlElement(name = "tomcatVersion")
  private String tomcatVersion = "";
  
  @XmlElement(name = "osName")
  private String osName = "";
  
  @XmlElement(name = "osVersion")
  private String osVersion = "";
  
  @XmlElement(name = "osArchitecture")
  private String osArchitecture = "";
  
  @XmlElement(name = "jvmVersion")
  private String jvmVersion = "";
  
  @XmlElement(name = "jvmVendor")
  private String jvmVendor = "";
  
  @XmlElement(name = "hwUniqueKey")
  private String hwUniqueKey = "";
  
  public String getTomcatVersion() {
    return this.tomcatVersion;
  }
  
  public String getOsName() {
    return this.osName;
  }
  
  public String getOsVersion() {
    return this.osVersion;
  }
  
  public String getOsArchitecture() {
    return this.osArchitecture;
  }
  
  public String getJvmVersion() {
    return this.jvmVersion;
  }
  
  public String getJvmVendor() {
    return this.jvmVendor;
  }
  
  public String getHwUniqueKey() {
    return this.hwUniqueKey;
  }
}
