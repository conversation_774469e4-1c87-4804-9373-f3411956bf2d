package com.samsung.magicinfo.webauthor2.service;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.service.IllegalMISLoginArgsException;
import com.samsung.magicinfo.webauthor2.exception.service.InspireTokenFailedException;
import com.samsung.magicinfo.webauthor2.exception.service.UnsupportedMISVersionException;
import com.samsung.magicinfo.webauthor2.properties.MagicInfoProperties;
import com.samsung.magicinfo.webauthor2.service.MISLoginService;
import com.samsung.magicinfo.webauthor2.service.ServerInfoService;
import com.samsung.magicinfo.webauthor2.service.SupportedFormatService;
import com.samsung.magicinfo.webauthor2.service.TokenService;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.io.UnsupportedEncodingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriUtils;

@Service
public class MISLoginServiceImpl implements MISLoginService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.MISLoginServiceImpl.class);
  
  private SupportedFormatService supportedFormatService;
  
  private TokenService tokenService;
  
  private UserData userData;
  
  private ServerInfoService serverInfoService;
  
  private MagicInfoProperties magicInfoProperties;
  
  @Autowired
  public MISLoginServiceImpl(SupportedFormatService service, UserData userData, TokenService tokenService, ServerInfoService serverInfoService, MagicInfoProperties magicInfoProperties) {
    this.supportedFormatService = service;
    this.userData = userData;
    this.tokenService = tokenService;
    this.serverInfoService = serverInfoService;
    this.magicInfoProperties = magicInfoProperties;
  }
  
  public String loginToMIS(String username, String password, String token, String language, String contentId) {
    String newToken;
    this.userData.setStartContentId(Strings.isNullOrEmpty(contentId) ? "" : contentId);
    if (!isSupportedMISVersion())
      throw new UnsupportedMISVersionException(); 
    if (!Strings.isNullOrEmpty(language))
      this.userData.setLanguage(language); 
    if (!Strings.isNullOrEmpty(username)) {
      if (!Strings.isNullOrEmpty(token)) {
        newToken = loginToMIS(username, token);
      } else if (!Strings.isNullOrEmpty(password)) {
        newToken = loginToMISByLoginAndPass(username, password);
      } else {
        newToken = refreshMISSessionByActualUserData();
      } 
    } else {
      newToken = refreshMISSessionByActualUserData();
    } 
    return newToken;
  }
  
  public String refreshMISSessionByActualUserData() {
    if (Strings.isNullOrEmpty(this.userData.getUserId()) || Strings.isNullOrEmpty(this.userData.getToken()))
      throw new IllegalMISLoginArgsException(); 
    return loginToMIS(this.userData.getUserId(), this.userData.getToken());
  }
  
  public void inspireToken() {
    if (Strings.isNullOrEmpty(this.userData.getUserId()) || Strings.isNullOrEmpty(this.userData.getToken()))
      throw new IllegalMISLoginArgsException(); 
    if (!this.tokenService.inspireToken(this.userData.getToken()))
      throw new InspireTokenFailedException(); 
  }
  
  private String loginToMIS(String username, String token) {
    try {
      token = UriUtils.encodeQuery(token, "UTF-8");
    } catch (UnsupportedEncodingException e) {
      throw new InspireTokenFailedException();
    } 
    if (this.userData.isAnonymous()) {
      this.userData.setCredentials(username, token);
    } else if (!this.userData.getUserId().equals(username)) {
      logger.debug("User changed from {} to {}", this.userData.getUserId(), username);
      this.userData.invalidate();
      this.userData.setCredentials(username, token);
    } else {
      this.userData.setCredentials(token);
    } 
    if (!this.tokenService.inspireToken(this.userData.getToken()))
      throw new InspireTokenFailedException(username); 
    this.supportedFormatService.copySupportedFormatsFromMagicInfoServer();
    return this.userData.getToken();
  }
  
  private String loginToMISByLoginAndPass(String username, String password) {
    try {
      String token = this.tokenService.getAuthenticationToken(username, password);
      return loginToMIS(username, token);
    } catch (Exception e) {
      throw new InspireTokenFailedException(username, password);
    } 
  }
  
  private boolean isSupportedMISVersion() {
    String openApiVersionString;
    int openApiMajorVersion, supportedOpenApiVersion = this.magicInfoProperties.getWebAuthorMajorVersion();
    try {
      openApiVersionString = this.serverInfoService.getOpenApiVersion().split("\\.")[0];
      openApiMajorVersion = Integer.parseInt(openApiVersionString) / 10000;
    } catch (Exception e) {
      return false;
    } 
    if (Strings.isNullOrEmpty(openApiVersionString) || openApiMajorVersion < supportedOpenApiVersion)
      return false; 
    return true;
  }
}
