package com.samsung.magicinfo.webauthor2.util;

import java.io.Serializable;
import org.springframework.stereotype.Component;
import org.springframework.web.context.annotation.SessionScope;

@Component
@SessionScope
public class ServerInfo implements Serializable {
  private static final long serialVersionUID = 8790519221238869297L;
  
  private static final String DEFAULT_URL = "http://localhost:7001/MagicInfo";
  
  private String serverUrl = "http://localhost:7001/MagicInfo";
  
  public String getServerUrl() {
    return this.serverUrl;
  }
  
  public void setServerUrl(String serverUrl) {
    this.serverUrl = serverUrl;
  }
}
