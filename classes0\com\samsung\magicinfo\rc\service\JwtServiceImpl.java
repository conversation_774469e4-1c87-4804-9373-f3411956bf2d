package com.samsung.magicinfo.rc.service;

import com.samsung.magicinfo.rc.common.exception.RestExceptionCode;
import com.samsung.magicinfo.rc.common.exception.RestServiceException;
import com.samsung.magicinfo.rc.common.security.AuthenticationToken;
import com.samsung.magicinfo.rc.common.security.DeviceWithToken;
import com.samsung.magicinfo.rc.model.api.UserSession;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.impl.crypto.MacProvider;
import java.security.Key;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

@Service
public class JwtServiceImpl {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.service.JwtServiceImpl.class);
  
  @Autowired
  CacheManager cacheManager;
  
  private static final Key secret = MacProvider.generateKey(SignatureAlgorithm.HS512);
  
  public Map createClaims(UserSession userSession, String permission) {
    String sessionId = userSession.getId();
    List<DeviceWithToken> devices = createDevices(userSession);
    Map<String, Object> claims = new HashMap<>();
    claims.put("sub", sessionId);
    claims.put("devices", devices);
    claims.put("from", userSession.getFrom());
    claims.put("accessToken", userSession.getAccessToken());
    claims.put("created", new Date(System.currentTimeMillis()));
    claims.put("expired", new Date(System.currentTimeMillis() + (userSession.getSessionExpiry() * 1000)));
    claims.put("locale", userSession.getLocale());
    claims.put("permission", permission);
    return claims;
  }
  
  @Cacheable(cacheNames = {"tokens"}, key = "#sessionId")
  public String generateJwt(String sessionId, Map claims) {
    String jwt = null;
    try {
      jwt = Jwts.builder().setClaims(claims).setExpiration((Date)claims.get("expired")).signWith(secret).compact();
    } catch (Exception e) {
      log.error("", e);
    } 
    return jwt;
  }
  
  @CachePut(cacheNames = {"tokens"}, key = "#sessionId")
  public String updateJwt(String sessionId, Map claims) {
    try {
      String jwt = Jwts.builder().setClaims(claims).setExpiration((Date)claims.get("expired")).signWith(secret).compact();
      log.info("[RC[JWT][" + sessionId + "] old jwt " + this.cacheManager.getCache("tokens").get(sessionId).get());
      log.info("[RC][JWT][" + sessionId + "] update token " + jwt);
      this.cacheManager.getCache("tokens").put(sessionId, jwt);
      return jwt;
    } catch (Exception e) {
      log.error("", e);
      return null;
    } 
  }
  
  public String updateJwtWithDevices(String jwt, List<DeviceWithToken> devices) {
    Claims claims = getClaimsFromJwt(jwt);
    claims.put("created", new Date(((Long)claims.get("expired")).longValue()));
    claims.put("expired", new Date(((Long)claims.get("expired")).longValue()));
    claims.put("devices", devices);
    return updateJwt((String)claims.get("sub"), (Map)claims);
  }
  
  public Claims getClaimsFromJwt(String jwt) {
    Claims claims = null;
    try {
      claims = (Claims)Jwts.parserBuilder().setSigningKey(secret).build().parseClaimsJws(jwt).getBody();
    } catch (Exception e) {
      e.printStackTrace();
      log.error("[RC] fail to parse from jwt");
      throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_TOKEN_EXPIRED);
    } 
    return claims;
  }
  
  public String getTokenFromJwt(String deviceId, String jwt) {
    return getDeviceTokenByDeviceIdFromClaims(deviceId, getClaimsFromJwt(jwt));
  }
  
  public <T> T getClaimsFromJwt(String token, Function<Claims, T> claimsResolver) {
    Claims claims = getClaimsFromJwt(token);
    return claimsResolver.apply(claims);
  }
  
  public boolean validateJwtWithDeviceId(String deviceId, String jwt) {
    Claims claims = getClaimsFromJwt(jwt);
    List<Map> deviceWithTokens = getDeviceIdFromClaims(claims);
    for (Map deviceWithToken : deviceWithTokens) {
      if (deviceId.equals(deviceWithToken.get("deviceId")))
        return true; 
    } 
    return false;
  }
  
  public boolean isExpiredJwt(String jwt) {
    Claims claims = getClaimsFromJwt(jwt);
    Instant instant = Instant.ofEpochMilli(((Long)claims.get("expired")).longValue());
    return LocalDateTime.now().isBefore(LocalDateTime.ofInstant(instant, ZoneId.systemDefault()));
  }
  
  private LocalDateTime getExpirationTimeFromJwt(String jwt) {
    Date expirationDate = getClaimsFromJwt(jwt, Claims::getExpiration);
    Instant instant = Instant.ofEpochMilli(expirationDate.getTime());
    return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
  }
  
  public List<Map> getDeviceIdFromClaims(Claims claims) {
    return (List<Map>)claims.get("devices");
  }
  
  public String getDeviceTokenByDeviceIdFromClaims(String deviceId, Claims claims) {
    List<Map> deviceWithTokens = getDeviceIdFromClaims(claims);
    for (Map deviceWithToken : deviceWithTokens) {
      if (deviceId.equals(deviceWithToken.get("deviceId")))
        return (String)deviceWithToken.get("token"); 
    } 
    return null;
  }
  
  public String getTokenFromSecurityContext(String deviceId) {
    try {
      AuthenticationToken authenticationToken = (AuthenticationToken)SecurityContextHolder.getContext().getAuthentication();
      String jwt = authenticationToken.getJwt();
      return getTokenFromJwt(deviceId, jwt);
    } catch (Exception e) {
      log.error("", e);
      log.error("[RC] getTokenFromSecurityContext " + e.getStackTrace());
      throw new RuntimeException();
    } 
  }
  
  private List<DeviceWithToken> createDevices(UserSession userSession) {
    List<String> deviceIds = userSession.getDeviceIds();
    String token = userSession.getToken();
    List<DeviceWithToken> devices = new ArrayList<>();
    for (String deviceId : deviceIds)
      devices.add(DeviceWithToken.builder().deviceId(deviceId).token(token).build()); 
    return devices;
  }
}
