package com.samsung.magicinfo.webauthor2.model;

public enum DeviceType {
  iPLAYER, S10PLAYER, S9PLAYER, S8PLAYER, S7PLAYER, S6PLAYER, S5PLAYER, S4PLAYER, S3PLAYER, S2PLAYER, SPLAYER, WPLAYER, ALL;
  
  public String getPlayerType() {
    switch (null.$SwitchMap$com$samsung$magicinfo$webauthor2$model$DeviceType[ordinal()]) {
      case 1:
        return iPLAYER.name();
      case 2:
        return WPLAYER.name();
    } 
    return SPLAYER.name();
  }
  
  public static String getCompatiblePlayerType(String playerType) {
    if (playerType == null)
      return ""; 
    switch (playerType.toLowerCase()) {
      case "iplayer":
        return iPLAYER.name();
      case "splayer":
        return SPLAYER.name();
      case "ledbox":
        return SPLAYER.name();
      case "signage":
        return SPLAYER.name();
      case "rsplayer":
        return SPLAYER.name();
      case "riplayer":
        return iPLAYER.name();
      case "sig_child":
        return SPLAYER.name();
    } 
    return playerType;
  }
  
  public String getPlayerVersion() {
    switch (null.$SwitchMap$com$samsung$magicinfo$webauthor2$model$DeviceType[ordinal()]) {
      case 1:
        return "1.0";
      case 3:
        return "10.0";
      case 4:
        return "9.0";
      case 5:
        return "8.0";
      case 6:
        return "7.0";
      case 7:
        return "6.0";
      case 8:
        return "5.0";
      case 9:
        return "4.0";
      case 10:
        return "3.0";
      case 11:
        return "2.0";
      case 12:
        return "1.0";
      case 2:
        return "1.0";
    } 
    return "1.0";
  }
}
