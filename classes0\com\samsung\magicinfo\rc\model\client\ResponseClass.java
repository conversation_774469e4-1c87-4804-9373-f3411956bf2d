package com.samsung.magicinfo.rc.model.client;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlCData;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

public class ResponseClass {
  @JacksonXmlProperty(isAttribute = true)
  String className;
  
  @JacksonXmlProperty(localName = "resultValue")
  String resultValue;
  
  @JacksonXmlProperty(localName = "resultText")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JacksonXmlCData
  private String resultText;
  
  public ResponseClass() {}
  
  public ResponseClass(String resultValue) {
    this.resultValue = resultValue;
  }
  
  public String getClassName() {
    return this.className;
  }
  
  public void setClassName(String className) {
    this.className = className;
  }
  
  public String getResultValue() {
    return this.resultValue;
  }
  
  public void setResultValue(String resultValue) {
    this.resultValue = resultValue;
  }
  
  public String getResultText() {
    return this.resultText;
  }
  
  public void setResultText(String resultText) {
    this.resultText = resultText;
  }
}
