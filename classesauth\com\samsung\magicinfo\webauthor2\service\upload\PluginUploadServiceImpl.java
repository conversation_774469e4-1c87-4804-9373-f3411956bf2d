package com.samsung.magicinfo.webauthor2.service.upload;

import com.google.common.base.Optional;
import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.ContentSaveElements;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.FileItemsDescriptor;
import com.samsung.magicinfo.webauthor2.model.ImageDimension;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.repository.OpenAPIContentRepository2;
import com.samsung.magicinfo.webauthor2.service.CSDFileService;
import com.samsung.magicinfo.webauthor2.service.CidMappingService;
import com.samsung.magicinfo.webauthor2.service.upload.PluginUploadService;
import com.samsung.magicinfo.webauthor2.service.upload.UploadHelperService;
import com.samsung.magicinfo.webauthor2.util.CleanPreviewFolder;
import com.samsung.magicinfo.webauthor2.util.FileHashUtil;
import com.samsung.magicinfo.webauthor2.util.ImageDimensionUtil;
import com.samsung.magicinfo.webauthor2.util.MultipartFilenameValidator;
import com.samsung.magicinfo.webauthor2.util.PlayTimeUtil;
import com.samsung.magicinfo.webauthor2.util.SupportedFormatUtils;
import com.samsung.magicinfo.webauthor2.util.UserData;
import com.samsung.magicinfo.webauthor2.xml.transferfile.response.TransferFileResponseType;
import com.samsung.magicinfo.webauthor2.xml.transferfile.response.TransferFilesResponseType;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class PluginUploadServiceImpl implements PluginUploadService {
  private CidMappingService cidMappingService;
  
  private CSDFileService csdFileService;
  
  private ServletContext servletContext;
  
  private MultipartFilenameValidator multipartFilenameValidator;
  
  private UploadHelperService uploadHelperService;
  
  private ContentSaveElements contentSaveElements;
  
  private UserData userData;
  
  private OpenAPIContentRepository2 contentRepository;
  
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.upload.PluginUploadServiceImpl.class);
  
  private static final String DEFAULT_FILE_ID = "00000000-0000-0000-0000-000000000000";
  
  @Autowired
  public PluginUploadServiceImpl(CidMappingService cidMappingService, CSDFileService csdFileService, ServletContext servletContext, MultipartFilenameValidator multipartFilenameValidator, UploadHelperService uploadHelperService, ContentSaveElements contentSaveElements, UserData userData, OpenAPIContentRepository2 contentRepository) {
    this.cidMappingService = cidMappingService;
    this.csdFileService = csdFileService;
    this.servletContext = servletContext;
    this.multipartFilenameValidator = multipartFilenameValidator;
    this.uploadHelperService = uploadHelperService;
    this.contentSaveElements = contentSaveElements;
    this.userData = userData;
    this.contentRepository = contentRepository;
  }
  
  public String initializeUploadProcess(Optional<MultipartFile> thumbnail, String contentName, String startupPage, String cid) throws IOException, FileItemValidationException {
    String contentId = this.cidMappingService.cidMapping(cid);
    MediaSource msThumbnail = new MediaSource();
    if (thumbnail.isPresent()) {
      msThumbnail = storeFileItem(contentId, "", (MultipartFile)thumbnail.get());
    } else {
      msThumbnail = getDefaultThumbnailMediaSource();
    } 
    msThumbnail.setFileType("thumbnail");
    msThumbnail.setFileId("00000000-0000-0000-0000-000000000000");
    this.contentSaveElements.setProjectName(contentName + ".LFD");
    this.contentSaveElements.setProjectThumbnailMediaSource(msThumbnail);
    this.contentSaveElements.setPathTothumbnail(msThumbnail.getPath().toString());
    this.contentSaveElements.setPlayerType(DeviceType.WPLAYER);
    this.contentSaveElements.setMediaSources(new ArrayList());
    this.contentSaveElements.setPathTothumbnail(msThumbnail.getPath().toString());
    MediaSource lfdMS = new MediaSource();
    lfdMS.setContentId(contentId);
    lfdMS.setTitle(contentName);
    lfdMS.setStartupPage(startupPage);
    lfdMS.setMediaType(MediaType.PLUGIN_EFFECT);
    List<MediaSource> mediaSources = new ArrayList<>();
    mediaSources.add(lfdMS);
    this.contentSaveElements.setMediaSources(mediaSources);
    return contentId;
  }
  
  public MediaSource storeSupportFileItem(String cid, String path, MultipartFile file) throws IOException, FileItemValidationException {
    if (!CleanPreviewFolder.isPathValid(path))
      throw new FileItemValidationException("InvalidFilePathError"); 
    if (!CleanPreviewFolder.isCidValid(cid))
      throw new FileItemValidationException("InvalidContentIdError"); 
    MediaSource mediaSource = storeFileItem(cid, path, file);
    mediaSource.setData(pathStringFormatForCSD(path));
    mediaSource.setFileType("content");
    mediaSource.setSupportFileItem(true);
    this.contentSaveElements.getMediaSources().add(mediaSource);
    return mediaSource;
  }
  
  private MediaSource getDefaultThumbnailMediaSource() {
    MediaSource thumbnail = this.contentSaveElements.getProjectThumbnailMediaSource();
    if (!Strings.isNullOrEmpty(thumbnail.getFileName()))
      return thumbnail; 
    String name = "HTML_THUMBNAIL.PNG";
    Path thumbnailFilePath = Paths.get(this.servletContext.getRealPath("images"), new String[] { name });
    File thumbnailFile = new File(thumbnailFilePath.toString());
    MediaSource msThumbnail = new MediaSource();
    msThumbnail.setData("");
    msThumbnail.setFileName(name);
    String extension = FilenameUtils.getExtension(name);
    msThumbnail.setMediaType(SupportedFormatUtils.getMediaTypeForExtension(extension));
    msThumbnail.setFileType("thumbnail");
    msThumbnail.setFileHash(FileHashUtil.getHash(thumbnailFile));
    msThumbnail.setMediaSize(thumbnailFile.length());
    fillImageDimensions(msThumbnail, thumbnailFile);
    msThumbnail.setPath(thumbnailFilePath.toString());
    msThumbnail.setFileId(UUID.randomUUID().toString().toUpperCase());
    return msThumbnail;
  }
  
  private MediaSource storeFileItem(String cid, String path, MultipartFile file) throws IOException, FileItemValidationException {
    this.multipartFilenameValidator.validateSupportFileItem(file);
    Path fileUploaded = getFileFromMultipartFile(cid, path, file);
    MediaSource mediaSource = this.uploadHelperService.getDetailsFromFile(fileUploaded);
    return mediaSource;
  }
  
  public List<MediaSource> getUpdatedMediaSources(FileItemsDescriptor fileItemsDescriptor) {
    this.contentSaveElements.setXml(fileItemsDescriptor.getXml());
    this.contentSaveElements.setHeight(fileItemsDescriptor.getHeight());
    this.contentSaveElements.setWidth(fileItemsDescriptor.getWidth());
    this.contentSaveElements.setPlayTime(PlayTimeUtil.covertPlayTimeFromSeconds(fileItemsDescriptor.getPlayTime()));
    fillLfdInfo(this.contentSaveElements.getMediaSources().get(0));
    List<MediaSource> sourcesWithThumbnail = this.contentSaveElements.getMediaSources();
    sourcesWithThumbnail.add(this.contentSaveElements.getProjectThumbnailMediaSource());
    String csdXml = this.csdFileService.generateCSD(sourcesWithThumbnail, this.contentSaveElements.getProjectContentId(), DeviceType.WPLAYER);
    TransferFilesResponseType csdResponse = this.csdFileService.postProjectCsdToMips(csdXml, this.contentSaveElements.getProjectContentId(), null);
    logger.debug("CSD : " + csdXml);
    logger.debug("CSD RESPONSE, CID : " + csdResponse.getContentId());
    for (TransferFileResponseType fs : csdResponse.getTransferFiles())
      logger.debug("CSD FILE : IsNew - " + fs.isNew() + ", Idx - " + fs.getReqIndex() + ", FileID - " + fs.getFileId()); 
    List<MediaSource> updatedMediaSources = this.csdFileService.updateMediaSources(this.contentSaveElements.getMediaSources(), csdResponse);
    this.contentSaveElements.setMediaSources(updatedMediaSources);
    return updatedMediaSources;
  }
  
  public String deletePlugin(String contentId) {
    return this.contentRepository.deleteContent(contentId);
  }
  
  private Path getFileFromMultipartFile(String cid, String path, MultipartFile multipartFile) throws IOException {
    if (Strings.isNullOrEmpty(path) || path.equals("/"))
      path = ""; 
    if (path.length() > 1 && !path.startsWith("/"))
      path = "/" + path; 
    Path workingDirectory = createDirectoryStructure(cid + path);
    Path filePath = Paths.get(workingDirectory.toString(), new String[] { multipartFile.getOriginalFilename() });
    File file = filePath.toFile();
    FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), file);
    return filePath;
  }
  
  private String pathStringFormatForCSD(String path) {
    if (Strings.isNullOrEmpty(path) || path.equals("/"))
      return ".\\"; 
    if (path.length() > 1 && !path.startsWith("/"))
      path = "/" + path; 
    if (path.length() > 1 && !path.endsWith("/"))
      path = path + "/"; 
    return "." + path.replace("/", "\\");
  }
  
  private Path createDirectoryStructure(String relativePath) {
    try {
      String previewPath = this.servletContext.getRealPath("preview");
      Path workingDirectory = Paths.get(previewPath, new String[] { relativePath });
      if (Files.notExists(workingDirectory, new java.nio.file.LinkOption[0]))
        Files.createDirectories(workingDirectory, (FileAttribute<?>[])new FileAttribute[0]); 
      return workingDirectory;
    } catch (IOException e) {
      throw new UploaderException(500, "Error during file structure initialization");
    } 
  }
  
  public void fillLfdInfo(MediaSource mediaSource) {
    try {
      String xml = this.contentSaveElements.getXml();
      Path xmlPath = writeXmlToFile(this.contentSaveElements.getProjectName(), xml);
      mediaSource.setPath(xmlPath.toString());
      fillHash(mediaSource, xmlPath);
      fillLength(mediaSource, xmlPath);
      mediaSource.setFileName(this.contentSaveElements.getProjectName());
      mediaSource.setMediaWidth(this.contentSaveElements.getWidth());
      mediaSource.setMediaHeight(this.contentSaveElements.getHeight());
      mediaSource.setMediaDuration(PlayTimeUtil.convertPlayTime(this.contentSaveElements.getPlayTime()).doubleValue());
      mediaSource.setFileType("plugin_effect");
      mediaSource.setMediaType(MediaType.PLUGIN_EFFECT);
      if (Strings.isNullOrEmpty(mediaSource.getFileId()))
        mediaSource.setFileId("00000000-0000-0000-0000-000000000000"); 
    } catch (IOException e) {
      logger.error("Error during setting xml media source properties: id {}", mediaSource.getContentId());
    } 
  }
  
  private Path writeXmlToFile(String projectName, String xml) throws IOException {
    String insertContents = this.servletContext.getRealPath("insertContents");
    String userWorkspaceDirectory = this.userData.getWorkspaceFolderName();
    Path xmlPath = Paths.get(insertContents, new String[] { userWorkspaceDirectory, projectName });
    if (Files.exists(xmlPath, new java.nio.file.LinkOption[0]))
      FileUtils.deleteQuietly(xmlPath.toFile()); 
    FileUtils.writeStringToFile(xmlPath.toFile(), xml, StandardCharsets.UTF_8);
    return xmlPath;
  }
  
  private void fillHash(MediaSource mediaSource, Path filePath) {
    String hash = FileHashUtil.getHash(filePath.toFile());
    mediaSource.setFileHash(hash);
  }
  
  private void fillLength(MediaSource mediaSource, Path filePath) {
    try {
      mediaSource.setMediaSize(Files.size(filePath));
    } catch (IOException e) {
      mediaSource.setMediaSize(0L);
    } 
  }
  
  private void fillImageDimensions(MediaSource mediaSource, File thumbnailFile) {
    if (mediaSource.getMediaType() == null && mediaSource.getMediaType() == MediaType.IMAGE)
      try {
        ImageDimension imageDimensions = ImageDimensionUtil.getImageDimensions(thumbnailFile.toPath());
        mediaSource.setMediaWidth(imageDimensions.getWidth());
        mediaSource.setMediaHeight(imageDimensions.getHeight());
      } catch (IOException e) {
        logger.error("Error on setting image dimensions.");
      }  
  }
}
