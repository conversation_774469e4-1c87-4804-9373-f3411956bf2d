package com.samsung.common.export;

import com.lowagie.text.Chunk;
import com.lowagie.text.Document;
import com.lowagie.text.Font;
import com.lowagie.text.Paragraph;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import com.samsung.common.logger.LoggingManagerV2;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;
import org.springframework.web.servlet.view.document.AbstractPdfView;

public class PdfMultiBuilder extends AbstractPdfView {
   public PdfMultiBuilder() {
      super();
   }

   public void buildPdfDocument(Map map, Document doc, PdfWriter pdfWriter, HttpServletRequest request, HttpServletResponse response) {
      Logger logger = LoggingManagerV2.getLogger(PdfBuilder.class);
      BaseFont baseFont = null;
      Font koreanFont = null;

      try {
         baseFont = BaseFont.createFont("HYGoThic-Medium", "UniKS-UCS2-H", false);
         koreanFont = new Font(baseFont, 10.0F, 0);
         String msg = request.getParameter("msg");
         if (msg == null || msg.trim().length() <= 0) {
            msg = "";
         }

         ByteArrayOutputStream baos = new ByteArrayOutputStream();
         PdfWriter.getInstance(doc, baos);
         doc.open();
         String[] sheet_name = (String[])((String[])map.get("sheetName"));
         List columnNamesList = (List)map.get("columnNames");
         List fieldNamesList = (List)map.get("fieldNames");
         List dataList = (List)map.get("dataList");
         int sheetSize = sheet_name.length;
         doc.add(new Paragraph(msg));
         doc.add(Chunk.NEWLINE);

         for(int s = 0; s < sheetSize; ++s) {
            doc.add(Chunk.NEWLINE);
            doc.add(Chunk.NEWLINE);
            doc.add(new Paragraph(new Paragraph(sheet_name[s].toString(), koreanFont)));
            doc.add(Chunk.NEWLINE);
            String[] field_name = (String[])fieldNamesList.get(s);
            int size = field_name.length;
            float[] t = new float[size];

            for(int i = 0; i < size; ++i) {
               t[i] = 3.0F;
            }

            PdfPTable table = new PdfPTable(t);
            table.setWidthPercentage(100.0F);

            for(int i = 0; i < size; ++i) {
               String cell_value = field_name[i].toString();
               Paragraph cell_string = new Paragraph(cell_value, koreanFont);
               table.addCell(cell_string);
            }

            Object[] data = (Object[])dataList.get(s);
            String[] columnNames = (String[])columnNamesList.get(s);
            int column_size = columnNames.length;
            int length = data.length;

            for(int i = 0; i < length; ++i) {
               for(int j = 0; j < column_size; ++j) {
                  String methodName = "get" + columnNames[j].substring(0, 1).toUpperCase();
                  methodName = methodName + (columnNames[j].length() > 1 ? columnNames[j].substring(1) : "");
                  String var28 = null;

                  try {
                     Object value = data[i].getClass().getMethod(methodName).invoke(data[i]);
                     value = value == null ? "" : value;
                     String cell_value = value.toString();
                     Paragraph cell_string = new Paragraph(cell_value, koreanFont);
                     table.addCell(cell_string);
                  } catch (Exception var31) {
                     var28 = "";
                  }
               }
            }

            doc.add(table);
         }

         doc.close();
         response.setHeader("Expires", "0");
         response.setHeader("Cache-Control", "must-revalidate, post-check=0, pre-check=0");
         response.setHeader("Pragma", "public");
         response.setHeader("Content-Type", "application/pdf");
         response.setHeader("Content-Disposition", "attachment; filename=\"" + map.get("fileName").toString() + "\"");
         response.setHeader("Content-Description", "JSP Generated Data");
         response.setHeader("Content-Transfer-Encoding", "binary;");
         response.setContentLength(baos.size());
         ServletOutputStream out = response.getOutputStream();
         baos.writeTo(out);
         out.flush();
      } catch (Exception var32) {
         logger.error("Error in " + this.getClass().getName() + "\n" + var32);
      }

   }
}
