package com.samsung.common.cache;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

public class MapItemMerger implements MutatorOperation {
   public MapItemMerger() {
      super();
   }

   public Map mutate(Map currentItem, Entry newItem) {
      if (newItem != null) {
         currentItem.put(newItem.getKey(), newItem.getValue());
      }

      return currentItem;
   }

   public Map initialValue(Entry item) {
      HashMap initialValue = new HashMap();
      if (item != null) {
         initialValue.put(item.getKey(), item.getValue());
      }

      return initialValue;
   }
}
