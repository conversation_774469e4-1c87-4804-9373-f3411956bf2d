package com.samsung.magicinfo.webauthor2.util;

import com.samsung.magicinfo.webauthor2.model.ImageDimension;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Iterator;
import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import org.springframework.util.Assert;

public final class ImageDimensionUtil {
  public static ImageDimension getImageDimensions(Path imagePath) throws IOException {
    Assert.isTrue(Files.exists(imagePath, new java.nio.file.LinkOption[0]), "No file at path: " + imagePath);
    int width = 0;
    int height = 0;
    try (ImageInputStream in = ImageIO.createImageInputStream(imagePath.toFile())) {
      Iterator<ImageReader> readers = ImageIO.getImageReaders(in);
      if (readers.hasNext()) {
        ImageReader reader = readers.next();
        try {
          reader.setInput(in);
          width = reader.getWidth(reader.getMinIndex());
          height = reader.getHeight(reader.getMinIndex());
        } finally {
          reader.dispose();
        } 
      } 
    } 
    return new ImageDimension(width, height);
  }
}
