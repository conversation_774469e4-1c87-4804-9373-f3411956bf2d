package com.samsung.magicinfo.restapi.user.utils;

import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.util.BeanUtils;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManager;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManagerImpl;
import java.util.LinkedHashMap;
import org.apache.commons.lang3.StringUtils;

public class UserUtils {
   public UserUtils() {
      super();
   }

   public static LinkedHashMap makeAdminInfo(long orgId) {
      try {
         PIIDataManager piiDataManager = (PIIDataManagerImpl)BeanUtils.getBean("PIIDataManager");
         UserInfo userInfo = UserInfoImpl.getInstance();
         User admin = userInfo.getAdminOfOrganizationByRootGroupId(orgId);
         LinkedHashMap info = new LinkedHashMap();
         info.put("organization", admin.getOrganization());
         info.put("name", piiDataManager.decryptData(admin.getUser_name()));
         info.put("email", piiDataManager.decryptData(admin.getEmail()));
         return info;
      } catch (Exception var6) {
         return null;
      }
   }

   public static String getUnRecognizedData(String data) {
      if (data != null && data.length() != 0) {
         int len = data.length();
         switch(len) {
         case 1:
            return "*";
         case 2:
            return data.substring(0, 1) + "*";
         case 3:
            return data.substring(0, 1) + "*" + data.substring(len - 1);
         default:
            return data.substring(0, 2) + StringUtils.repeat('*', len - 3) + data.substring(len - 1);
         }
      } else {
         return "";
      }
   }
}
