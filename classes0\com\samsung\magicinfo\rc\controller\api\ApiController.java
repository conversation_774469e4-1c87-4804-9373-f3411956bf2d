package com.samsung.magicinfo.rc.controller.api;

import com.samsung.magicinfo.rc.common.Device;
import com.samsung.magicinfo.rc.common.aspects.PermissionDevice;
import com.samsung.magicinfo.rc.common.exception.RestExceptionCode;
import com.samsung.magicinfo.rc.common.exception.RestServiceException;
import com.samsung.magicinfo.rc.common.http.RCResponseBody;
import com.samsung.magicinfo.rc.model.api.DeviceAddResource;
import com.samsung.magicinfo.rc.model.api.DeviceControl;
import com.samsung.magicinfo.rc.service.ApiServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import java.io.ByteArrayInputStream;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "api for remote control", description = "", tags = {""})
@RestController
public class ApiController {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.controller.api.ApiController.class);
  
  @Autowired
  ApiServiceImpl apiService;
  
  @ApiOperation(value = "Get information for devices by session", notes = "Get information")
  @RequestMapping(value = {"/api/information/{sessionId}"}, method = {RequestMethod.GET})
  public ResponseEntity<RCResponseBody<Map>> getInformation(@PathVariable String sessionId) {
    this;
    log.info("[RC][REST][getInformation]");
    Map rtn = this.apiService.getUserInformation(sessionId);
    RCResponseBody responseBody = RCResponseBody.builder().data(rtn).build();
    return new ResponseEntity(responseBody, HttpStatus.OK);
  }
  
  @ApiOperation(value = "Get information for a deviceIdWithToken by deviceId", notes = "Get information for a deviceIdWithToken by deviceId", authorizations = {@Authorization("Authorization")})
  @RequestMapping(value = {"/api/devices/{deviceId}"}, method = {RequestMethod.GET})
  @PermissionDevice
  public ResponseEntity<RCResponseBody<Device>> getDeviceById(@PathVariable String deviceId) {
    Device device = this.apiService.getDeviceFromJwtById(deviceId);
    RCResponseBody responseBody = RCResponseBody.builder().data(device).build();
    return new ResponseEntity(responseBody, HttpStatus.OK);
  }
  
  @ApiOperation(value = "close session for a deviceIdWithToken", notes = "close session for a deviceIdWithToken")
  @RequestMapping(value = {"/api/devices/stop"}, method = {RequestMethod.POST})
  public ResponseEntity<RCResponseBody> stop(@RequestBody DeviceControl deviceControl) {
    this;
    log.info("[RC][REST][stopDevice]");
    this.apiService.stopDevices(deviceControl);
    RCResponseBody responseBody = RCResponseBody.builder().status("success").build();
    return new ResponseEntity(responseBody, HttpStatus.OK);
  }
  
  @ApiOperation(value = "remote control devices", notes = "remote control devices")
  @RequestMapping(value = {"/api/devices/control"}, method = {RequestMethod.PUT})
  public ResponseEntity<RCResponseBody> control(@RequestBody DeviceControl deviceControl) {
    this;
    log.info("[RC][REST][deviceControl]");
    this.apiService.deviceControl(deviceControl);
    RCResponseBody responseBody = RCResponseBody.builder().status("success").build();
    return new ResponseEntity(responseBody, HttpStatus.OK);
  }
  
  @ResponseBody
  @GetMapping({"/api/devices/{deviceId}/resource"})
  public ResponseEntity<InputStreamResource> getResource(@PathVariable("deviceId") String deviceId) {
    byte[] imageFile = this.apiService.getScreenCaptureByDeviceId(deviceId);
    if (imageFile != null) {
      ByteArrayInputStream iStream = new ByteArrayInputStream(imageFile);
      return ResponseEntity.ok()
        .contentLength(imageFile.length)
        .contentType(MediaType.parseMediaType("image/jpg"))
        .body(new InputStreamResource(iStream));
    } 
    return ResponseEntity.noContent().build();
  }
  
  @ApiOperation(value = "add control devices", notes = "add control devices")
  @RequestMapping(value = {"/api/devices"}, method = {RequestMethod.POST})
  public ResponseEntity<RCResponseBody<DeviceAddResource>> addDevices(@RequestBody DeviceControl deviceControl) {
    this;
    log.info("[RC][REST][addDevices]");
    DeviceAddResource devicesResource = this.apiService.addDevices(deviceControl.getDeviceIds());
    RCResponseBody responseBody = RCResponseBody.builder().data(devicesResource).build();
    return new ResponseEntity(responseBody, HttpStatus.OK);
  }
  
  @ApiOperation(value = "remove control devices", notes = "remove control devices")
  @RequestMapping(value = {"/api/devices/{deviceId}"}, method = {RequestMethod.DELETE})
  public ResponseEntity<RCResponseBody<String>> removeDevice(@PathVariable("deviceId") String deviceId) {
    String newJwt = this.apiService.stopDevice(deviceId);
    if (newJwt == null)
      throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CONNECT_FAIL); 
    RCResponseBody responseBody = RCResponseBody.builder().data(newJwt).status("success").build();
    return new ResponseEntity(responseBody, HttpStatus.OK);
  }
}
