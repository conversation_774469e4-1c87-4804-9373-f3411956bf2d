package com.samsung.magicinfo.framework.user.manager;

import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.auth.security.otp.UserAuthDevice;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.mu.manager.OrganizationGroupInfo;
import com.samsung.magicinfo.framework.mu.manager.OrganizationGroupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.user.dao.UserDao;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;

public class UserInfoImpl implements UserInfo {
   private Logger logger = LoggingManagerV2.getLogger(this.getClass());
   private UserDao dao = null;
   private static UserInfoImpl instance;

   private UserInfoImpl() {
      super();
      this.dao = new UserDao();
   }

   private UserInfoImpl(SqlSession session) {
      super();
      this.dao = new UserDao(session);
   }

   public static UserInfoImpl getInstance() {
      if (instance == null) {
         Class var0 = UserInfoImpl.class;
         synchronized(UserInfoImpl.class) {
            if (instance == null) {
               instance = new UserInfoImpl();
               return instance;
            }
         }
      }

      return instance;
   }

   public static UserInfoImpl getInstance(SqlSession session) {
      return new UserInfoImpl(session);
   }

   public User getAllByUserId(String userId) throws SQLException {
      return this.dao.getAllByUserId(userId);
   }

   public int getCountByUserId(String userId) throws SQLException {
      return this.dao.getCountByUserId(userId);
   }

   public int getCountByUserIdIsDeleted(String userId) throws SQLException {
      return this.dao.getCountByUserIdIsDeleted(userId);
   }

   public List getAllUser(Map map, int startPos, int pageSize) throws SQLException {
      return this.dao.getAllUser(map, startPos, pageSize);
   }

   public int getCountGroupedUser(Map map) throws SQLException {
      return this.dao.getCountGroupedUser(map);
   }

   public List getGroupedUser(Map map, int startPos, int pageSize) throws SQLException {
      return this.dao.getGroupedUser(map, startPos, pageSize);
   }

   public int getCountAllUser(Map map) throws SQLException {
      return this.dao.getCountAllUser(map);
   }

   public Boolean addUser(User user, boolean isNew) throws SQLException {
      SecurityUtils.encryptUserPasswordIfNecessary(user);
      return this.dao.addUser(user, isNew);
   }

   public Boolean addUser(User user, long userGroupId, boolean isNew) throws SQLException {
      SecurityUtils.encryptUserPasswordIfNecessary(user);
      return this.dao.addUser(user, userGroupId, isNew);
   }

   public Boolean setUser(User user) throws SQLException {
      if (user == null) {
         return false;
      } else {
         User userInfo = this.getUserByUserId(user.getUser_id());
         if (userInfo == null) {
            return false;
         } else {
            SecurityUtils.encryptUserPasswordIfNecessary(user);
            if (!user.getPassword().equals(userInfo.getPassword())) {
               try {
                  UserPasswordManager userPassword = UserPasswordManagerImpl.getInstance();
                  userPassword.addUserPasswordHistory(user.getUser_id(), userInfo.getPassword());
               } catch (SQLException var6) {
                  this.logger.error("", var6);
               }

               Timestamp timestamp = new Timestamp(System.currentTimeMillis());
               user.setPassword_change_date(timestamp);
            }

            new User();

            User updateUser;
            try {
               updateUser = (User)user.clone();
               if ("Y".equals(updateUser.getIs_mu())) {
                  updateUser.setOrganization("ROOT");
                  updateUser.setRoot_group_id(Long.valueOf("0"));
               }
            } catch (Exception var5) {
               updateUser = user;
            }

            return this.dao.setUser(updateUser);
         }
      }
   }

   public Boolean setLoginDateByUserId(String userId) throws SQLException {
      return this.dao.setLoginDateByUserId(userId);
   }

   public Boolean setIsDeletedByUserId(String reason, String userId, boolean isSelf) throws SQLException {
      return this.dao.setIsDeletedByUserId(reason, userId, isSelf);
   }

   public String getNameByUserId(String userId) throws SQLException {
      return this.dao.getNameByUserId(userId);
   }

   public PagedListInfo getPagedList(int startPos, int pageSize, Map condition, String section) throws Exception {
      List resList = null;
      int totCount = 0;
      if (section.equals("getUserList")) {
         totCount = this.getCountAllUser(condition);
         resList = this.getAllUser(condition, startPos, pageSize);
      } else if (section.equals("getGroupedUserList")) {
         totCount = this.getCountGroupedUser(condition);
         resList = this.getGroupedUser(condition, startPos, pageSize);
      } else if (section.equals("getNonApprovedUserList")) {
         totCount = this.getCountAllNonApprovedUser(condition);
         resList = this.getAllNonApprovedUser(condition, startPos, pageSize);
      } else if (section.equals("getWithdrawalUserList")) {
         totCount = this.getCountAllWithdrawalUser(condition);
         resList = this.getAllWithdrawalUser(condition, startPos, pageSize);
      }

      return new PagedListInfo(resList, totCount);
   }

   public Boolean setIsApprovedByUserId(String isApproved, String userId) throws SQLException {
      return this.dao.setIsApprovedByUserId(isApproved, userId);
   }

   public Long getRootGroupIdByUserId(String userId) throws SQLException {
      return this.dao.getRootGroupIdByUserId(userId);
   }

   public List getAllNonApprovedUser(Map map, int startPos, int pageSize) throws SQLException {
      return this.dao.getAllNonApprovedUser(map, startPos, pageSize);
   }

   public int getCountAllNonApprovedUser(Map map) throws SQLException {
      return this.dao.getCountAllNonApprovedUser(map);
   }

   public List getAllWithdrawalUser(Map map, int startPos, int pageSize) throws SQLException {
      return this.dao.getAllWithdrawalUser(map, startPos, pageSize);
   }

   public int getCountAllWithdrawalUser(Map map) throws SQLException {
      return this.dao.getCountAllWithdrawalUser(map);
   }

   public List getLdapUserInfo(String ldapUserId) throws SQLException {
      return this.dao.getLdapUserInfo(ldapUserId);
   }

   public User getUserByUserId(String userId) throws SQLException {
      return this.dao.getUserByUserId(userId);
   }

   public int deleteUser(String userId) throws SQLException {
      try {
         UserPasswordManager userPassword = UserPasswordManagerImpl.getInstance();
         userPassword.deleteUserPasswordHistoryByUser(userId);
         this.deleteUserDeviceByUserId(userId);
      } catch (SQLException var3) {
         this.logger.error("", var3);
      }

      return this.dao.deleteUser(userId);
   }

   public int deleteLDAPUser(Long orgId) throws SQLException {
      return this.dao.deleteLDAPUser(orgId);
   }

   public Boolean setApprovalByUser(User user) throws SQLException {
      return this.dao.setApprovalByUser(user);
   }

   public String getIsApprovedByUserId(String userId) throws SQLException {
      return this.dao.getIsApprovedByUserId(userId);
   }

   public String getIsDeletedByUserId(String userId) throws SQLException {
      return this.dao.getIsDeletedByUserId(userId);
   }

   public boolean setOrganizationByUserIdList(String organization, List userIdList) throws SQLException, ConfigException {
      return this.dao.setOrganizationByUserIdList(organization, userIdList);
   }

   public List getAllApprovalUserByRootGroupId(Long rootGroupId, boolean isAll) throws SQLException {
      return this.dao.getAllApprovalUserByRootGroupId(rootGroupId, isAll);
   }

   public List getAllUserByRootGroupId(Long rootGroupId) throws SQLException {
      return this.dao.getAllUserByRootGroupId(rootGroupId);
   }

   public List getAllUserListByRootGroupId(Long organization_id) throws SQLException {
      return this.dao.getAllUserListByRootGroupId(organization_id);
   }

   public boolean setRejectUser(String rejectReason, String userId) throws SQLException, ConfigException {
      return this.dao.setRejectUser(rejectReason, userId);
   }

   public Map getIsRejectByUserId(String userId) throws SQLException {
      return this.dao.getIsRejectByUserId(userId);
   }

   public Map getDeleteInfoByUserId(String userId) throws SQLException {
      return this.dao.getDeleteInfoByUserId(userId);
   }

   public List getAllUserList(Map map) throws SQLException {
      return this.dao.getAllUserList(map);
   }

   public List getAllUserList(Long organization_id) throws SQLException {
      return this.dao.getAllUserList(organization_id);
   }

   public List getAllUserListToMigrate() throws SQLException {
      return this.dao.getAllUserListToMigrate();
   }

   public boolean migrateUser(User user) throws SQLException {
      return this.dao.migrateUser(user);
   }

   public int deleteMappingInfoByUserID(String userId) throws SQLException {
      return this.dao.deleteMappingInfoByUserID(userId);
   }

   public String getOrganNameByUserId(String userId) throws SQLException {
      return this.dao.getOrganNameByUserId(userId);
   }

   public int getCountByUserIdForCheck(String userId) throws SQLException {
      return this.dao.getCountByUserIdForCheck(userId);
   }

   public int getCountByLDAPUserFullIdForCheck(String ldapFullId) throws SQLException {
      return this.dao.getCountByLDAPUserFullIdForCheck(ldapFullId);
   }

   public int getCountByLDAPUserIdForCheck(String ldapUserId) throws SQLException {
      return this.dao.getCountByLDAPUserIdForCheck(ldapUserId);
   }

   public String getMailList(String userIdList) throws SQLException {
      return this.dao.getMailList(userIdList);
   }

   public String getSMSList(String userIdList) throws SQLException {
      return this.dao.getSMSList(userIdList);
   }

   public List getAllUserListBySearch(Long search_id) throws SQLException {
      return this.dao.getAllUserListBySearch(search_id);
   }

   public String getImeiByUserId(String userId) throws SQLException {
      return this.dao.getImeiByUserId(userId);
   }

   public String getOsTypeByUserId(String userId) throws SQLException {
      return this.dao.getOsTypeByUserId(userId);
   }

   public String getIsResetPwdByUserId(String userId) throws SQLException {
      return this.dao.getIsResetPwdByUserId(userId);
   }

   public User getUserInfo(String userID) throws SQLException {
      return this.dao.getUserInfo(userID);
   }

   public boolean setEmailSendingOptions(String get_fault, String get_alarm, String user_id) throws SQLException {
      return this.dao.setEmailSendingOptions(get_fault, get_alarm, user_id);
   }

   public boolean setEmailSendingDisconnectAlarm(String user_id, String disconnect_alarm) throws SQLException {
      return this.dao.setEmailSendingDisconnectAlarm(user_id, disconnect_alarm);
   }

   public String[] getAllUserListByEmailSendingOptions(Long organization_id, String get_fault, String get_alarm) throws SQLException {
      return this.dao.getAllUserListByEmailSendingOptions(organization_id, get_fault, get_alarm);
   }

   public String getSendToList(Long organization_id, String get_fault, String get_alarm) throws SQLException {
      String[] useridList = this.dao.getAllUserListByEmailSendingOptions(organization_id, get_fault, get_alarm);
      StringBuffer sendTo = new StringBuffer();

      for(int i = 0; i < useridList.length; ++i) {
         sendTo.append(useridList[i]);
         if (i != useridList.length - 1) {
            sendTo.append(",");
         }
      }

      return sendTo.toString();
   }

   public String getShortcut(String userId) throws SQLException {
      return this.dao.getShortcut(userId);
   }

   public boolean setShortcut(String userId, String option) throws SQLException {
      return this.dao.setShortcut(userId, option);
   }

   public String getOrganGroupName(Long groupID) throws SQLException {
      String groupName = null;
      Group organ = this.dao.getOrganGroupInfo(groupID);
      if (organ != null) {
         groupName = organ.getGroup_name();
      }

      return groupName;
   }

   public List getNewAndModifiedUserByDate(String startDate, String endDate) throws SQLException {
      return this.dao.getNewAndModifiedUserByDate(startDate, endDate);
   }

   public List getOrganization() throws SQLException {
      return this.dao.getOrganization();
   }

   public List getDisconnectAlarmUserListByOrgId(Long orgId) throws SQLException {
      return this.dao.getDisconnectAlarmUserListByOrgId(orgId);
   }

   public List getContentManagerUserListByOrgId(Long orgId) throws SQLException {
      return this.dao.getContentManagerUserListByOrgId(orgId);
   }

   public boolean setContentApprover(String userId, String contentApprover) throws SQLException {
      return this.dao.setContentApprover(userId, contentApprover);
   }

   public boolean setAllContentApprover(String contentApprover) throws SQLException {
      return this.dao.setAllContentApprover(contentApprover);
   }

   public List getContentApproverListByGroupId(Long rootGroupId) throws SQLException {
      return this.dao.getContentApproverListByGroupId(rootGroupId);
   }

   public List getContentApprover() throws SQLException {
      return this.dao.getContentApprover();
   }

   public boolean setLocale(String userId, String locale) throws SQLException {
      return this.dao.setLocale(userId, locale);
   }

   public List getEmailNotificationUserListByOrgId(Long orgId, boolean includeRoot) throws SQLException {
      return this.dao.getEmailNotificationUserListByOrgId(orgId, includeRoot);
   }

   public boolean setEmailNotificationOption(Long orgId, String userId, String type, boolean value) throws SQLException {
      if (userId != null && !"".equals(userId)) {
         return type != null && !type.equals("") && userId != null && !userId.equals("") ? this.dao.setEmailNotificationOption(orgId, userId, type, value) : false;
      } else {
         return false;
      }
   }

   public int deleteEmailNotificationByOrdIdAndUserId(Long orgId, String userId) throws SQLException {
      return this.dao.deleteEmailNotificationByOrdIdAndUserId(orgId, userId);
   }

   public List getAlarmUserListByOrgIdAndType(Long orgId, String type) throws SQLException {
      return this.dao.getAlarmUserListByOrgIdAndType(orgId, type);
   }

   public String getOrganNameByRootGroupId(long rootGroupId) throws SQLException {
      return this.dao.getOrganNameByRootGroupId(rootGroupId);
   }

   public Integer deleteDashboardUserInfoByUserId(String userId) throws SQLException {
      return this.dao.deleteDashboardUserInfoByUserId(userId);
   }

   public boolean chkOrganizationByUserId(String organizationFromUser, String id) throws SQLException {
      String organization = this.getOrganNameByUserId(id);
      return organization != null && organization.equals(organizationFromUser);
   }

   public Boolean setIsFirstLoginByUserId(String userId) throws SQLException {
      return this.dao.setIsFirstLoginByUserId(userId);
   }

   public boolean setResetPwToken(String encryptionToken, long passwordResetExpirationInSec, String userId) {
      return this.dao.setResetPwToken(encryptionToken, passwordResetExpirationInSec, userId);
   }

   public boolean checkResetValidByTokenAndId(String userId, String encryptionToken) throws SQLException {
      return this.dao.checkResetValidByTokenAndId(userId, encryptionToken);
   }

   public List getAllUserWithEncryptionToken() throws SQLException {
      return this.dao.getAllUserWithEncryptionToken();
   }

   public List getAllRootUserList() throws SQLException {
      return this.dao.getAllRootUserList();
   }

   public long getCurMngOrgId(String userId) throws SQLException {
      long curOrgId = this.dao.getCurMngOrgId(userId);
      OrganizationGroupInfo organizationGroupInfo = OrganizationGroupInfoImpl.getInstance();
      List orgList = organizationGroupInfo.getMngOrgIdListByUserId(userId);
      if (!orgList.contains(curOrgId)) {
         this.dao.setCurMngOrgId(userId, (Long)orgList.get(0));
         curOrgId = (Long)orgList.get(0);
      }

      return curOrgId;
   }

   public boolean setCurMngOrgId(String userId, long mngOrgId) throws SQLException {
      return this.dao.setCurMngOrgId(userId, mngOrgId);
   }

   public List getMUInfoByMngOrgId(long mngOrg) throws SQLException {
      return this.dao.getMUInfoByMngOrgId(mngOrg);
   }

   public boolean setMUByUserId(String userId, String flag) throws SQLException {
      return this.dao.setMUByUserId(userId, flag);
   }

   public User getAdminOfOrganizationByRootGroupId(long rootGroupId) throws SQLException {
      return this.dao.getAdminOfOrganizationByRootGroupId(rootGroupId);
   }

   public User getAdminOfOrganizationNotInDeleteUsers(long rootGroupId, String[] deleteUsers) throws SQLException {
      return this.dao.getAdminOfOrganizationNotInDeleteUsers(rootGroupId, deleteUsers);
   }

   public int setIsResetPasswordBatch() throws SQLException {
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map serverInfoMap = serverSetupDao.getServerInfoByOrgId(0L);
      long month = (Long)serverInfoMap.get("PASSWORD_CHANGE_PERIOD");
      if (month <= 0L) {
         return 0;
      } else {
         DateTime now = new DateTime();
         Timestamp date = Timestamp.valueOf(now.plusMonths(-1 * (int)month).toString("yyyy-MM-dd") + " 00:00:00");
         return this.dao.setIsResetPasswordBatch(date);
      }
   }

   public List getfilterExport(Map map) throws SQLException {
      return this.dao.getfilterExport(map);
   }

   public int addAuthDeviceInfo(UserAuthDevice userAuthDevice) throws SQLException {
      return this.dao.addAuthDeviceInfo(userAuthDevice);
   }

   public long getAuthDeviceId() throws SQLException {
      return this.dao.getAuthDeviceId();
   }

   public List getUserAuthDevice(String userId) throws SQLException {
      return this.dao.getUserAuthDevice(userId);
   }

   public List getUserStoredDevice(String userId) throws SQLException {
      return this.dao.getUserStoredDevice(userId);
   }

   public int setUserSecretKey(String userId, String secretKey) throws SQLException {
      return this.dao.setUserSecretKey(userId, secretKey);
   }

   public int deleteUserDevice(int authDeviceId) throws SQLException {
      return this.dao.deleteUserDevice(authDeviceId);
   }

   public int deleteUserDeviceByUserId(String userId) throws SQLException {
      return this.dao.deleteUserDeviceByUserId(userId);
   }

   public int deleteExpiredUserDevice() throws SQLException {
      return this.dao.deleteExpiredUserDevice();
   }

   public int updateUserDeviceExpiredDate(int day) throws SQLException {
      return this.dao.updateUserDeviceExpiredDate(day);
   }

   public int updateUserDeviceByUserId(String userId) throws SQLException {
      return this.dao.updateUserDeviceByUserId(userId);
   }

   public int deleteAllMFAData() throws SQLException {
      int result1 = this.dao.deleteAllMfaDevice();
      int result3 = this.dao.setUserSecretKey((String)null, (String)null);
      return result1 + result3;
   }

   public String unapprovedUser(String userId) throws SQLException {
      return this.dao.unapprovedUser(userId);
   }
}
