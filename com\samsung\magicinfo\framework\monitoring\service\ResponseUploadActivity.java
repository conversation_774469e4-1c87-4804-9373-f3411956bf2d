package com.samsung.magicinfo.framework.monitoring.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfo;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfoImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManager;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManagerImpl;
import com.samsung.magicinfo.framework.monitoring.entity.CurrentPlayingEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ScreenCaptureEntity;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.ScreenCaptureManager;
import com.samsung.magicinfo.framework.monitoring.manager.ScreenCaptureManagerImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.exception.ServiceException;
import com.samsung.magicinfo.protocol.rmql.RMQL;
import com.samsung.magicinfo.protocol.rmql.RMQLDriver;
import com.samsung.magicinfo.protocol.rmql.ResultSet;
import com.samsung.magicinfo.protocol.servicemanager.ServiceOpActivity;
import com.samsung.magicinfo.protocol.util.RMQLDriverUtil;
import com.samsung.magicinfo.protocol.util.RMQLInstanceCreator;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.OpenOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import org.apache.logging.log4j.Logger;

public class ResponseUploadActivity extends ServiceOpActivity {
   Logger logger = LoggingManagerV2.getLogger(ResponseUploadActivity.class);
   boolean onStats = true;

   public ResponseUploadActivity() {
      super();

      try {
         if (StrUtils.nvl(CommonConfig.get("pop.enable")).equalsIgnoreCase("false")) {
            this.onStats = false;
         } else {
            this.onStats = true;
         }
      } catch (ConfigException var2) {
         this.onStats = true;
      }

   }

   public Object process(HashMap params) throws ServiceException {
      ResultSet rs = (ResultSet)params.get("resultset");
      String moDownload = null;
      Device device = null;
      boolean onS3Storage = false;

      String device_id;
      try {
         moDownload = rs.getAttribute("MO_DOWNLOAD");
         File file = (File)rs.getObjectAttribute("DOWNLOADABLE_FILE");
         device_id = rs.getAttribute("DEVICE_ID");
         String content_type = rs.getAttribute("CONTENT-TYPE");
         String contentName = rs.getAttribute("DWN_CONTENT_NAME_ATTR");
         String s3Path = "";
         String path = CommonConfig.get("UPLOAD_HOME");
         if (!path.endsWith("\\") && !path.endsWith("/")) {
            path = path + File.separator;
         }

         if (!onS3Storage) {
            if (content_type.equals("CONTENT")) {
               path = path + CommonConfig.get("CAPTURE_DIR");
            } else if (content_type.equals("PLAYHISTORY")) {
               if (contentName.startsWith("FACE")) {
                  path = path + CommonConfig.get("FACE_LOG_DIR");
               } else {
                  path = path + CommonConfig.get("POP_LOG_DIR");
               }
            }
         } else if (onS3Storage) {
            if (content_type.equals("CONTENT")) {
               s3Path = s3Path + CommonConfig.get("s3.CAPTURE_DIR") + device_id + "/";
            } else if (content_type.equals("PLAYHISTORY")) {
               s3Path = s3Path + CommonConfig.get("s3.POP_DIR") + device_id + "/";
            }
         }

         Path destinationPath = Paths.get(SecurityUtils.directoryTraversalChecker(path + File.separator + contentName, (String)null));
         Path sourcePath = Paths.get(file.getPath());

         try {
            Files.write(destinationPath, Files.readAllBytes(sourcePath), new OpenOption[0]);
         } catch (Exception var27) {
            this.logger.error("[MagicInfo_ScreenCaptureUpload] NIO write Exception! contentName : " + contentName + " e : " + var27.getMessage());
         }

         try {
            file.delete();
         } catch (Exception var26) {
            this.logger.error("[MagicInfo_ScreenCaptureUpload] NIO delete Exception! contentName : " + contentName + " e : " + var26.getMessage());
         }

         DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
         device = deviceDao.getDevice(device_id);
         RMQL rmql = null;
         RMQLDriver driver = null;
         if (content_type.equals("CONTENT")) {
            ScreenCaptureManager screenDao = ScreenCaptureManagerImpl.getInstance();
            ScreenCaptureEntity sc = new ScreenCaptureEntity(device_id, contentName);
            screenDao.updateScreenCapture(sc);
            rmql = RMQLInstanceCreator.getInstance(device, "DOWNLOAD", (Long)null);
            rmql.addParam("RESULT", "SUCCESS");
            rmql.addParam("MO_DOWNLOAD", moDownload);
            driver = RMQLDriverUtil.getWSRMQLDriver();

            try {
               if (DeviceUtils.isSupportNOC()) {
                  DeviceNocInfo nocDao = DeviceNocInfoImpl.getInstance();
                  MonitoringManager mgr = MonitoringManagerImpl.getInstance();
                  boolean nocGroup = nocDao.isNocSupportGroupByDeviceId(device_id);
                  CurrentPlayingEntity curEntity = mgr.getPlayingContent(device_id);
                  if (nocGroup && curEntity != null && curEntity.getInputSource() != 1000) {
                     DeviceNocManager nocService = DeviceNocManagerImpl.getInstance();
                     nocService.thingworxUpdateScreenCapture(device_id);
                  }
               }
            } catch (Exception var25) {
               this.logger.error("[DeviceUtils][thingworx] failed to call api for creating cabinets. :" + device_id);
            }
         } else if (content_type.equals("PLAYHISTORY")) {
            rmql = RMQLInstanceCreator.getInstance(device, "DOWNLOAD", (Long)null);
            rmql.addParam("RESULT", "SUCCESS");
            rmql.addParam("MO_DOWNLOAD", moDownload);
            driver = RMQLDriverUtil.getWSRMQLDriver();
         }

         if (driver != null) {
            return driver.createAppBOForResponse(rmql);
         } else {
            throw new NullPointerException("driver is null");
         }
      } catch (Exception var28) {
         this.logger.error("[MagicInfo_ScreenCapture] ", var28);

         try {
            if (this.onStats) {
               device_id = rs.getAttribute("DEVICE_ID");
               DeviceInfo deviceDAO = DeviceInfoImpl.getInstance();
               device = deviceDAO.getDevice(device_id);
            }

            RMQL rmql = RMQLInstanceCreator.getInstance(device, "DOWNLOAD", (Long)null);
            rmql.addParam("RESULT", "FAIL");
            rmql.addParam("MO_DOWNLOAD", moDownload);
            RMQLDriver driver = RMQLDriverUtil.getWSRMQLDriver();
            return driver.createAppBOForResponse(rmql);
         } catch (Exception var24) {
            throw new ServiceException(var24);
         }
      }
   }
}
