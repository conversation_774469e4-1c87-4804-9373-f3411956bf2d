package com.samsung.magicinfo.restapi.schedule.service;

import com.google.gson.Gson;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.export.PdfBuilder;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.common.ListEntity;
import com.samsung.magicinfo.framework.common.MenuEntity;
import com.samsung.magicinfo.framework.common.MessageSourceManager;
import com.samsung.magicinfo.framework.common.MessageSourceManagerImpl;
import com.samsung.magicinfo.framework.content.dao.ContentDao;
import com.samsung.magicinfo.framework.content.dao.PlaylistDao;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.NotificationData;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTag;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.monitoring.entity.CurrentPlayingEntity;
import com.samsung.magicinfo.framework.monitoring.manager.DownloadStatusInfo;
import com.samsung.magicinfo.framework.monitoring.manager.DownloadStatusInfoImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistInterface;
import com.samsung.magicinfo.framework.scheduler.entity.AdScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.AdSlotEntity;
import com.samsung.magicinfo.framework.scheduler.entity.AdThumbnailEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ChannelEntity;
import com.samsung.magicinfo.framework.scheduler.entity.CommonProgramEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.FrameEntity;
import com.samsung.magicinfo.framework.scheduler.entity.FrameTemplateEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramGroup;
import com.samsung.magicinfo.framework.scheduler.entity.ScheduleAdminEntity;
import com.samsung.magicinfo.framework.scheduler.entity.SelectConditionScheduleAdmin;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleManager;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleManagerImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleUtility;
import com.samsung.magicinfo.framework.scheduler.manager.common.ProgramGroupImpl;
import com.samsung.magicinfo.framework.scheduler.manager.common.ProgramGroupInterface;
import com.samsung.magicinfo.framework.scheduler.manager.common.ScheduleInterface;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.util.MailUtil;
import com.samsung.magicinfo.restapi.common.model.V2CommonDeleteFail;
import com.samsung.magicinfo.restapi.common.model.V2CommonDeleteResult;
import com.samsung.magicinfo.restapi.common.model.V2CommonGroupIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonResultResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTagResource;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.schedule.model.ContentScheduleFrameResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2DeviceGroupInfo;
import com.samsung.magicinfo.restapi.schedule.model.TTV2DisplayResolution;
import com.samsung.magicinfo.restapi.schedule.model.TTV2FrameTemplateResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2LayoutDivisionFrameInfo;
import com.samsung.magicinfo.restapi.schedule.model.TTV2LayoutDivisionLineInfo;
import com.samsung.magicinfo.restapi.schedule.model.TTV2ProgramResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2ProgramSummaryInfo;
import com.samsung.magicinfo.restapi.schedule.model.TTV2PublishingDeviceStatus;
import com.samsung.magicinfo.restapi.schedule.model.TTV2PublishingProgress;
import com.samsung.magicinfo.restapi.schedule.model.V2ContentScheduleAllListResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ContentScheduleBlockResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ContentScheduleChannelResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ContentScheduleDetailResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ContentScheduleDiskCheckInfo;
import com.samsung.magicinfo.restapi.schedule.model.V2ContentScheduleDownloadStatusResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ContentScheduleFrameResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ContentScheduleItemResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ContentScheduleRangeResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ContentScheduleResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ContentScheduleTemplateResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ScheduleAddChannelResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ScheduleCommonDeletionResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ScheduleDeleteChannelResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ScheduleDeleteParam;
import com.samsung.magicinfo.restapi.schedule.model.V2ScheduleDeviceGroup;
import com.samsung.magicinfo.restapi.schedule.model.V2ScheduleEditChannelFilter;
import com.samsung.magicinfo.restapi.schedule.model.V2ScheduleEditChannelResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ScheduleFilter;
import com.samsung.magicinfo.restapi.schedule.model.V2ScheduleLoadChannelResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ScheduleRestoreResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ScheduleSelectChannelResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ScheduleUpdateFrameResource;
import com.samsung.magicinfo.restapi.schedule.model.V2SchedulegetFrameResource;
import com.samsung.magicinfo.restapi.schedule.model.V2scheduleDelResource;
import com.samsung.magicinfo.restapi.schedule.utils.V2ContentScheduleHelper;
import com.samsung.magicinfo.restapi.schedule.utils.V2ContentScheduleValidator;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.service.statistics.DeviceStatisticsDownloadService;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.ModelAndView;

@Service("V2ScheduleContentService")
@Transactional
public class V2ScheduleContentServiceImpl implements V2ScheduleContentService {
   protected final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
   private DeviceStatisticsDownloadService downloadService = null;

   public V2ScheduleContentServiceImpl() {
      super();
   }

   public void setDownloadService(DeviceStatisticsDownloadService downloadService) {
      this.downloadService = downloadService;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody addContent(String programId, String contentId, String slotId, String startDate, String startTime, String endDate, String endTime, HttpServletRequest request) throws Exception {
      ResponseBody responsebody = new ResponseBody();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      String sessionId = request.getSession().getId();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      LinkedHashMap result = new LinkedHashMap();
      PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
      programId = StrUtils.nvl(programId).equals("") ? "" : programId;
      String CACHEDKEY = sessionId + programId;
      contentId = StrUtils.nvl(contentId).equals("") ? "" : contentId;
      slotId = StrUtils.nvl(slotId).equals("") ? "" : slotId;
      startDate = StrUtils.nvl(startDate).equals("") ? "" : startDate;
      startTime = StrUtils.nvl(startTime).equals("") ? "" : startTime;
      endDate = StrUtils.nvl(endDate).equals("") ? "" : endDate;
      endTime = StrUtils.nvl(endTime).equals("") ? "" : endTime;
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      List frameList = ((ChannelEntity)program.getChannelList().get(0)).getFrameList();
      if (frameList != null) {
         List slotList = ((FrameEntity)frameList.get(0)).getSlotList();

         for(int i = 0; i < slotList.size(); ++i) {
            if (((AdSlotEntity)slotList.get(i)).getSlot_id().equals(slotId)) {
               List scheduleList = ((AdSlotEntity)slotList.get(i)).getScheduleList();
               if (scheduleList == null) {
                  scheduleList = new ArrayList();
               }

               SimpleDateFormat transFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
               Iterator var23 = ((List)scheduleList).iterator();

               AdScheduleEntity scheudle;
               while(var23.hasNext()) {
                  scheudle = (AdScheduleEntity)var23.next();
                  Date oldStart = transFormat.parse(scheudle.getStart_date() + " " + scheudle.getStart_time());
                  Date oldEnd = transFormat.parse(scheudle.getStop_date() + " " + scheudle.getStop_time());
                  Date newStart = transFormat.parse(startDate + " " + startTime);
                  Date newStop = transFormat.parse(endDate + " " + endTime);
                  if (isOverlapping(oldStart, oldEnd, newStart, newStop)) {
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CHECK_TIME_VALUE);
                  }
               }

               String scheduleId = UUID.randomUUID().toString();
               scheudle = new AdScheduleEntity();
               scheudle.setProgram_id(programId);
               scheudle.setContent_type("PLAYLIST");
               PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
               Playlist playlist = pInfo.getPlaylistActiveVerInfo(contentId);
               if (playlist != null) {
                  List contentList = pInfo.getContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
                  if (contentList != null) {
                     List thumbnails = new ArrayList();
                     Iterator var29 = contentList.iterator();

                     while(var29.hasNext()) {
                        Content content = (Content)var29.next();
                        AdThumbnailEntity thumbnail = new AdThumbnailEntity();
                        thumbnail.setFileId(content.getThumb_file_id());
                        thumbnail.setFileName(content.getThumb_file_name());
                        thumbnails.add(thumbnail);
                     }

                     scheudle.setThumbnailList(thumbnails);
                  }

                  scheudle.setContent_name(playlist.getPlaylist_name());
               }

               scheudle.setContent_id(contentId);
               scheudle.setSchedule_id(scheduleId);
               scheudle.setSlot_id(slotId);
               scheudle.setStart_date(startDate);
               scheudle.setStart_time(startTime);
               scheudle.setStop_date(endDate);
               scheudle.setStop_time(endTime);
               scheudle.setUser_id(userId);
               ((List)scheduleList).add(scheudle);
               ((AdSlotEntity)slotList.get(i)).setScheduleList((List)scheduleList);
               scheduleMgr.updateSchedule(program, CACHEDKEY);
               result.put("status", "success");
            }
         }
      }

      new Gson();
      responsebody.setItems(result);
      return responsebody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody loadAd(String programId, HttpServletRequest request) throws Exception {
      ResponseBody responsebody = new ResponseBody();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
      Locale locale = SecurityUtils.getLocale();
      String sessionId = request.getSession().getId();
      String CACHEDKEY = sessionId + programId;
      UserContainer userContainer = SecurityUtils.getUserContainer();
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      LinkedHashMap result = new LinkedHashMap();
      ArrayList buttons = new ArrayList();
      LinkedHashMap saveBtn = new LinkedHashMap();
      saveBtn.put("name", messageMgr.getMessageSource("COM_BUTTON_SAVE", locale));
      saveBtn.put("id", "scheduleSave");
      buttons.add(saveBtn);
      LinkedHashMap saveAsBtn = new LinkedHashMap();
      saveAsBtn.put("name", messageMgr.getMessageSource("BUTTON_SAVE_AS_P", locale));
      saveAsBtn.put("id", "scheduleSaveAs");
      buttons.add(saveAsBtn);
      LinkedHashMap cancelBtn = new LinkedHashMap();
      cancelBtn.put("name", messageMgr.getMessageSource("BUTTON_CANCEL_P", locale));
      cancelBtn.put("id", "scheduleCancel");
      buttons.add(cancelBtn);
      result.put("menu", buttons);
      LinkedHashMap data = new LinkedHashMap();
      if (program.getProgram_group_name().equals("") && program.getProgram_group_id() == 0L) {
         String userOrganization = userContainer.getUser().getOrganization();
         ProgramGroupInterface programGroupDao = ProgramGroupImpl.getInstance();
         if (userOrganization.equals("ROOT")) {
            List prgoramGroupList = programGroupDao.getChildGroupList(0, false);
            Long groupId = ((ProgramGroup)prgoramGroupList.get(0)).getGroup_id();
            String orgranizationName = ((ProgramGroup)prgoramGroupList.get(0)).getGroup_name();
            prgoramGroupList = programGroupDao.getChildGroupList(CommonUtils.safeLongToInt(groupId), false);
            data.put("programGroupId", String.valueOf(((ProgramGroup)prgoramGroupList.get(0)).getGroup_id()));
            data.put("programGroupName", orgranizationName + " - " + ((ProgramGroup)prgoramGroupList.get(0)).getGroup_name());
         } else {
            int groupId = programGroupDao.getProgramGroupForOrg(userOrganization);
            List prgoramGroupList = programGroupDao.getChildGroupList(groupId, false);
            data.put("programGroupId", String.valueOf(((ProgramGroup)prgoramGroupList.get(0)).getGroup_id()));
            data.put("programGroupName", ((ProgramGroup)prgoramGroupList.get(0)).getGroup_name());
         }
      } else {
         data.put("programGroupId", program.getProgram_group_id());
         data.put("programGroupName", program.getProgram_group_name());
      }

      data.put("programName", program.getProgram_name());
      data.put("scheudleDesText", program.getDescription());
      result.put("data", data);
      responsebody.setStatus("Success");
      new Gson();
      responsebody.setItems(result);
      return responsebody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody getEvents(String programId, HttpServletRequest request) throws Exception {
      ResponseBody responsebody = new ResponseBody();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      String sessionId = request.getSession().getId();
      String CACHEDKEY = sessionId + programId;
      PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      LinkedHashMap result = new LinkedHashMap();
      List events = new ArrayList();
      List slotList = ((FrameEntity)((ChannelEntity)program.getChannelList().get(0)).getFrameList().get(0)).getSlotList();
      int index = 0;
      Iterator var13 = slotList.iterator();

      while(true) {
         AdSlotEntity slot;
         List scheduleList;
         do {
            if (!var13.hasNext()) {
               result.put("events", events);
               responsebody.setStatus("Success");
               new Gson();
               responsebody.setItems(result);
               return responsebody;
            }

            slot = (AdSlotEntity)var13.next();
            scheduleList = slot.getScheduleList();
         } while(scheduleList == null);

         for(Iterator var16 = scheduleList.iterator(); var16.hasNext(); ++index) {
            AdScheduleEntity scheudle = (AdScheduleEntity)var16.next();
            LinkedHashMap slotMap = new LinkedHashMap();
            slotMap.put("index", slot.getSlot_index() - 1);
            slotMap.put("scheduleId", scheudle.getSchedule_id());
            slotMap.put("slotId", slot.getSlot_id());
            slotMap.put("start", scheudle.getStart_date() + " " + scheudle.getStart_time());
            slotMap.put("end", scheudle.getStop_date() + " " + scheudle.getStop_time());
            String contentId = scheudle.getContent_id();
            String color = this.getColor(index);
            slotMap.put("color", color);
            if (scheudle.getContent_name() != null) {
               slotMap.put("text", scheudle.getContent_name());
            }

            if (scheudle.getThumbnailList() != null) {
               LinkedHashMap thumbnailMap = new LinkedHashMap();
               List thumbnails = scheudle.getThumbnailList();
               Iterator var23 = thumbnails.iterator();

               while(var23.hasNext()) {
                  AdThumbnailEntity thumbnail = (AdThumbnailEntity)var23.next();
                  thumbnailMap.put("fileName", thumbnail.getFileName());
                  thumbnailMap.put("fileId", thumbnail.getFileId());
               }

               slotMap.put("thumbnails", thumbnails);
            }

            slotMap.put("contentId", contentId);
            slotMap.put("data", "");
            events.add(slotMap);
         }
      }
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody updateSlotOption(String programId, String duration, String slotIds, String slotNames, String addSlots, String removeSlots, HttpServletRequest request) throws Exception {
      ResponseBody responsebody = new ResponseBody();
      String sessionId = request.getSession().getId();
      String CACHEDKEY = sessionId + programId;
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
      Locale locale = SecurityUtils.getLocale();
      duration = StrUtils.nvl(duration).equals("") ? "" : duration;
      slotIds = StrUtils.nvl(slotIds).equals("") ? "" : slotIds;
      slotNames = StrUtils.nvl(slotNames).equals("") ? "" : slotNames;
      addSlots = StrUtils.nvl(addSlots).equals("") ? "" : addSlots;
      removeSlots = StrUtils.nvl(removeSlots).equals("") ? "" : removeSlots;
      String[] slotIdList = null;
      if (slotIds != null && !slotIds.equals("")) {
         slotIdList = slotIds.split(",");
      }

      String[] slotNameList = null;
      if (slotNames != null && !slotNames.equals("")) {
         slotNameList = slotNames.split(",");
      }

      String[] addSlotList = null;
      if (addSlots != null && !addSlots.equals("")) {
         addSlotList = addSlots.split(",");
      }

      String[] removeSlotList = null;
      if (removeSlots != null && !removeSlots.equals("")) {
         removeSlotList = removeSlots.split(",");
      }

      LinkedHashMap result = new LinkedHashMap();
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      program.setAd_duration(Long.valueOf(duration));
      List adSlotList = ((FrameEntity)((ChannelEntity)program.getChannelList().get(0)).getFrameList().get(0)).getSlotList();
      int i;
      int i;
      String removeSlotId;
      if (removeSlotList != null && removeSlotList.length > 0) {
         for(int i = 0; i < adSlotList.size(); ++i) {
            String[] var22 = removeSlotList;
            i = removeSlotList.length;

            for(i = 0; i < i; ++i) {
               removeSlotId = var22[i];
               if (((AdSlotEntity)adSlotList.get(i)).getSlot_id().equals(removeSlotId)) {
                  adSlotList.remove(i);
               }
            }
         }
      }

      if (addSlotList != null && addSlotList.length > 0) {
         String frameId = ((FrameEntity)((ChannelEntity)program.getChannelList().get(0)).getFrameList().get(0)).getFrame_id();
         String slotText = messageMgr.getMessageSource("MIS_SID_20_MIX_SLOT", locale);
         slotText = slotText.replace(" <<A>>", "");

         for(i = 0; i < addSlotList.length; ++i) {
            AdSlotEntity slot = new AdSlotEntity();
            removeSlotId = addSlotList[i];
            slot.setSlot_id(removeSlotId);
            slot.setProgram_id(programId);
            slot.setFrame_id(frameId);
            slot.setSlot_index(i + 999);
            slot.setSlot_name(slotText);
            slot.setDuration(Integer.valueOf(duration));
            adSlotList.add(slot);
         }
      }

      Iterator var27 = adSlotList.iterator();

      while(var27.hasNext()) {
         AdSlotEntity adSlot = (AdSlotEntity)var27.next();
         String slotId = adSlot.getSlot_id();

         for(i = 0; i < slotIdList.length; ++i) {
            if (slotId.equals(slotIdList[i])) {
               adSlot.setSlot_index(i + 1);
               adSlot.setSlot_name(slotNameList[i]);
               adSlot.setDuration(Integer.valueOf(duration));
            }
         }
      }

      Collections.sort(adSlotList, new Comparator() {
         public int compare(AdSlotEntity o1, AdSlotEntity o2) {
            if (o1.getSlot_index() > o2.getSlot_index()) {
               return 1;
            } else {
               return o1.getSlot_index() < o2.getSlot_index() ? -1 : 0;
            }
         }
      });
      ((FrameEntity)((ChannelEntity)program.getChannelList().get(0)).getFrameList().get(0)).setSlotList(adSlotList);
      scheduleMgr.updateSchedule(program, CACHEDKEY);
      responsebody.setStatus("Success");
      new Gson();
      responsebody.setItems(result);
      return responsebody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody getSlotOptions(String programId, HttpServletRequest request) throws Exception {
      ResponseBody responsebody = new ResponseBody();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      String sessionId = request.getSession().getId();
      String CACHEDKEY = sessionId + programId;
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      LinkedHashMap result = new LinkedHashMap();
      List slots = new ArrayList();
      List slotList = ((FrameEntity)((ChannelEntity)program.getChannelList().get(0)).getFrameList().get(0)).getSlotList();
      long duration = program.getAd_duration();
      if (slotList != null) {
         Iterator var13 = slotList.iterator();

         while(var13.hasNext()) {
            AdSlotEntity slot = (AdSlotEntity)var13.next();
            LinkedHashMap slotMap = new LinkedHashMap();
            slotMap.put("slotName", slot.getSlot_name());
            slotMap.put("slotId", slot.getSlot_id());
            slotMap.put("index", slot.getSlot_index());
            slots.add(slotMap);
         }
      }

      result.put("slots", slots);
      result.put("duration", duration);
      responsebody.setStatus("Success");
      new Gson();
      responsebody.setItems(result);
      return responsebody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody deleteContent(String programId, String scheduleId, String slotId, HttpServletRequest request) throws SQLException, Exception {
      ResponseBody responsebody = new ResponseBody();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      String sessionId = request.getSession().getId();
      String CACHEDKEY = sessionId + programId;
      LinkedHashMap result = new LinkedHashMap();
      scheduleId = StrUtils.nvl(scheduleId).equals("") ? "" : scheduleId;
      slotId = StrUtils.nvl(slotId).equals("") ? "" : slotId;
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      if (program != null) {
         List slotList = ((FrameEntity)((ChannelEntity)program.getChannelList().get(0)).getFrameList().get(0)).getSlotList();
         if (slotList != null) {
            for(int i = 0; i < slotList.size(); ++i) {
               if (((AdSlotEntity)slotList.get(i)).getSlot_id().equals(slotId)) {
                  List scheduleList = ((AdSlotEntity)slotList.get(i)).getScheduleList();
                  if (scheduleList != null) {
                     for(int j = 0; j < scheduleList.size(); ++j) {
                        if (((AdScheduleEntity)scheduleList.get(j)).getSchedule_id().equals(scheduleId)) {
                           scheduleList.remove(j);
                           scheduleMgr.updateSchedule(program, CACHEDKEY);
                           responsebody.setStatus("Success");
                        }
                     }
                  }
               }
            }
         }
      }

      new Gson();
      responsebody.setItems(result);
      return responsebody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody updateContent(String programId, String scheduleId, String contentId, String slotId, String startDate, String startTime, String endDate, String endTime, String oriSlotId, String changeSlot, HttpServletRequest request) throws Exception {
      ResponseBody responsebody = new ResponseBody();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      String sessionId = request.getSession().getId();
      String CACHEDKEY = sessionId + programId;
      LinkedHashMap result = new LinkedHashMap();
      scheduleId = StrUtils.nvl(scheduleId).equals("") ? "" : scheduleId;
      contentId = StrUtils.nvl(contentId).equals("") ? "" : contentId;
      slotId = StrUtils.nvl(slotId).equals("") ? "" : slotId;
      startDate = StrUtils.nvl(startDate).equals("") ? "" : startDate;
      startTime = StrUtils.nvl(startTime).equals("") ? "" : startTime;
      endDate = StrUtils.nvl(endDate).equals("") ? "" : endDate;
      endTime = StrUtils.nvl(endTime).equals("") ? "" : endTime;
      oriSlotId = StrUtils.nvl(oriSlotId).equals("") ? "" : oriSlotId;
      changeSlot = StrUtils.nvl(changeSlot).equals("") ? "false" : changeSlot;
      PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      List frameList = ((ChannelEntity)program.getChannelList().get(0)).getFrameList();
      if (frameList != null) {
         List slotList = ((FrameEntity)frameList.get(0)).getSlotList();

         for(int i = 0; i < slotList.size(); ++i) {
            List scheduleList;
            Date date;
            if (changeSlot.equals("true")) {
               if (((AdSlotEntity)slotList.get(i)).getSlot_id().equals(oriSlotId)) {
                  scheduleList = ((AdSlotEntity)slotList.get(i)).getScheduleList();
                  if (scheduleList != null) {
                     for(int j = 0; j < scheduleList.size(); ++j) {
                        if (((AdScheduleEntity)scheduleList.get(j)).getSchedule_id().equals(scheduleId)) {
                           AdScheduleEntity schedule = (AdScheduleEntity)scheduleList.get(j);
                           List newSlotList = ((FrameEntity)frameList.get(0)).getSlotList();

                           for(int k = 0; k < newSlotList.size(); ++k) {
                              if (((AdSlotEntity)newSlotList.get(k)).getSlot_id().equals(slotId)) {
                                 List newScheduleList = ((AdSlotEntity)newSlotList.get(k)).getScheduleList();
                                 if (newScheduleList == null) {
                                    newScheduleList = new ArrayList();
                                 }

                                 SimpleDateFormat transFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                 Iterator var50 = ((List)newScheduleList).iterator();

                                 while(var50.hasNext()) {
                                    AdScheduleEntity adSchedule = (AdScheduleEntity)var50.next();
                                    if (!adSchedule.getSchedule_id().equals(scheduleId)) {
                                       Date oldStart = transFormat.parse(adSchedule.getStart_date() + " " + adSchedule.getStart_time());
                                       Date oldEnd = transFormat.parse(adSchedule.getStop_date() + " " + adSchedule.getStop_time());
                                       Date newStart = transFormat.parse(startDate + " " + startTime);
                                       Date newStop = transFormat.parse(endDate + " " + endTime);
                                       if (isOverlapping(oldStart, oldEnd, newStart, newStop)) {
                                          throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CHECK_TIME_VALUE);
                                       }
                                    }
                                 }

                                 scheduleList.remove(j);
                                 date = new Date();
                                 Timestamp timestamp = new Timestamp(date.getTime());
                                 schedule.setModify_date(timestamp);
                                 schedule.setContent_id(contentId);
                                 schedule.setSlot_id(slotId);
                                 schedule.setStart_date(startDate);
                                 schedule.setStart_time(startTime);
                                 schedule.setStop_date(endDate);
                                 schedule.setStop_time(endTime);
                                 schedule.setUser_id(userId);
                                 ((List)newScheduleList).add(schedule);
                                 ((AdSlotEntity)newSlotList.get(k)).setScheduleList((List)newScheduleList);
                                 scheduleMgr.updateSchedule(program, CACHEDKEY);
                                 responsebody.setStatus("Success");
                                 break;
                              }
                           }
                        }
                     }
                  }
               }
            } else if (((AdSlotEntity)slotList.get(i)).getSlot_id().equals(slotId)) {
               scheduleList = ((AdSlotEntity)slotList.get(i)).getScheduleList();
               SimpleDateFormat transFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
               Iterator var26 = scheduleList.iterator();

               Date date;
               while(var26.hasNext()) {
                  AdScheduleEntity adSchedule = (AdScheduleEntity)var26.next();
                  if (!adSchedule.getSchedule_id().equals(scheduleId)) {
                     Date oldStart = transFormat.parse(adSchedule.getStart_date() + " " + adSchedule.getStart_time());
                     Date oldEnd = transFormat.parse(adSchedule.getStop_date() + " " + adSchedule.getStop_time());
                     date = transFormat.parse(startDate + " " + startTime);
                     date = transFormat.parse(endDate + " " + endTime);
                     if (isOverlapping(oldStart, oldEnd, date, date)) {
                        throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CHECK_TIME_VALUE);
                     }
                  }
               }

               if (scheduleList != null) {
                  for(int j = 0; j < scheduleList.size(); ++j) {
                     if (((AdScheduleEntity)scheduleList.get(j)).getSchedule_id().equals(scheduleId)) {
                        String playlistName = playlistInfo.getPlaylistName(contentId);
                        if (playlistName != null) {
                           ((AdScheduleEntity)scheduleList.get(j)).setContent_name(playlistName);
                        }

                        ContentFile contentFile = playlistInfo.getThumbFileInfo(contentId);
                        ContentInfo cInfo = ContentInfoImpl.getInstance();
                        if (contentFile != null) {
                           ((AdScheduleEntity)scheduleList.get(j)).setFileId(contentFile.getFile_id());
                           ((AdScheduleEntity)scheduleList.get(j)).setFileName(contentFile.getFile_name());
                           List contentList = playlistInfo.getActiveVerContentList(contentId);
                           List thumbnailList = new ArrayList();
                           Content content = null;

                           for(int k = 0; k < contentList.size(); ++k) {
                              content = cInfo.getContentAndFileActiveVerInfo(((PlaylistContent)contentList.get(k)).getContent_id());
                              AdThumbnailEntity thumbnail = new AdThumbnailEntity();
                              thumbnail.setFileId(content.getThumb_file_id());
                              thumbnail.setFileName(content.getThumb_file_name());
                              thumbnailList.add(thumbnail);
                           }

                           ((AdScheduleEntity)scheduleList.get(j)).setThumbnailList(thumbnailList);
                        }

                        ((AdScheduleEntity)scheduleList.get(j)).setContent_id(contentId);
                        ((AdScheduleEntity)scheduleList.get(j)).setSlot_id(slotId);
                        ((AdScheduleEntity)scheduleList.get(j)).setStart_date(startDate);
                        ((AdScheduleEntity)scheduleList.get(j)).setStart_time(startTime);
                        ((AdScheduleEntity)scheduleList.get(j)).setStop_date(endDate);
                        ((AdScheduleEntity)scheduleList.get(j)).setStop_time(endTime);
                        ((AdScheduleEntity)scheduleList.get(j)).setUser_id(userId);
                        date = new Date();
                        Timestamp timestamp = new Timestamp(date.getTime());
                        ((AdScheduleEntity)scheduleList.get(j)).setModify_date(timestamp);
                        scheduleMgr.updateSchedule(program, CACHEDKEY);
                        responsebody.setStatus("Success");
                        break;
                     }
                  }
               }
            }
         }
      }

      responsebody.setItems(result);
      return responsebody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody addSlot() {
      ResponseBody responsebody = new ResponseBody();
      LinkedHashMap result = new LinkedHashMap();
      String slotId = UUID.randomUUID().toString();
      responsebody.setStatus("Success");
      result.put("slotId", slotId);
      responsebody.setItems(result);
      return responsebody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody removeSlot(String programId, int slotIndex, String slotId, HttpServletRequest request) throws Exception {
      ResponseBody responsebody = new ResponseBody();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      String sessionId = request.getSession().getId();
      String CACHEDKEY = sessionId + programId;
      LinkedHashMap result = new LinkedHashMap();
      slotId = StrUtils.nvl(slotId).equals("") ? "" : slotId;
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      List frameList = ((ChannelEntity)program.getChannelList().get(0)).getFrameList();
      if (frameList != null) {
         List slotList = ((FrameEntity)frameList.get(0)).getSlotList();

         int reIndex;
         for(reIndex = 0; reIndex < slotList.size(); ++reIndex) {
            if (((AdSlotEntity)slotList.get(reIndex)).getSlot_id().equals(slotId)) {
               slotList.remove(reIndex);
               ((FrameEntity)frameList.get(0)).setSlotList(slotList);
            }
         }

         Collections.sort(slotList, new Comparator() {
            public int compare(AdSlotEntity o1, AdSlotEntity o2) {
               if (o1.getSlot_index() > o2.getSlot_index()) {
                  return 1;
               } else {
                  return o1.getSlot_index() < o2.getSlot_index() ? -1 : 0;
               }
            }
         });
         reIndex = 1;

         for(Iterator var14 = slotList.iterator(); var14.hasNext(); ++reIndex) {
            AdSlotEntity slot = (AdSlotEntity)var14.next();
            slot.setSlot_index(reIndex);
         }

         scheduleMgr.updateSchedule(program, CACHEDKEY);
         responsebody.setStatus("Success");
      }

      if (frameList != null) {
         List slots = new ArrayList();
         List slotList = ((FrameEntity)frameList.get(0)).getSlotList();
         int index = 1;

         for(Iterator var21 = slotList.iterator(); var21.hasNext(); ++index) {
            AdSlotEntity slot = (AdSlotEntity)var21.next();
            LinkedHashMap slotMap = new LinkedHashMap();
            slotMap.put("slotId", slot.getSlot_id());
            slotMap.put("slotName", slot.getSlot_name());
            slotMap.put("index", index);
            slots.add(slotMap);
         }

         result.put("slots", slots);
      }

      responsebody.setItems(result);
      return responsebody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody getSlot(String programId, HttpServletRequest request) throws Exception {
      ResponseBody responsebody = new ResponseBody();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      String sessionId = request.getSession().getId();
      String CACHEDKEY = sessionId + programId;
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      LinkedHashMap result = new LinkedHashMap();
      List slots = new ArrayList();
      List slotList = ((FrameEntity)((ChannelEntity)program.getChannelList().get(0)).getFrameList().get(0)).getSlotList();
      if (slotList != null) {
         Iterator var11 = slotList.iterator();

         while(var11.hasNext()) {
            AdSlotEntity slot = (AdSlotEntity)var11.next();
            LinkedHashMap slotMap = new LinkedHashMap();
            slotMap.put("slotName", slot.getSlot_name());
            slotMap.put("slotId", slot.getSlot_id());
            slotMap.put("index", slot.getSlot_index());
            slotMap.put("duration", slot.getDuration());
            slots.add(slotMap);
         }
      }

      result.put("slots", slots);
      responsebody.setStatus("Success");
      new Gson();
      responsebody.setItems(result);
      return responsebody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody putEdit(String programId, String programName, String programGroup, String deviceGroupIds, String description, String deployReserve, String deployReserveTime, String deployReserveRepeatType, String deployReserveStartDate, String deployReserveEndDate, String weekly, String monthly, String selectId, HttpServletRequest request) throws Exception {
      ResponseBody responsebody = new ResponseBody();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      String sessionId = request.getSession().getId();
      String CACHEDKEY = sessionId + programId;
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      LinkedHashMap result = new LinkedHashMap();
      programName = StrUtils.nvl(programName.equals("") ? "" : programName);
      programGroup = StrUtils.nvl(programGroup.equals("") ? "" : programGroup);
      deviceGroupIds = StrUtils.nvl(deviceGroupIds.equals("") ? "" : deviceGroupIds);
      description = StrUtils.nvl(description.equals("") ? "" : description);
      deployReserve = StrUtils.nvl(deployReserve.equals("") ? "" : deployReserve);
      deployReserveTime = StrUtils.nvl(deployReserveTime.equals("") ? "" : deployReserveTime);
      selectId = StrUtils.nvl(selectId.equals("") ? "" : selectId);
      if (programName.equals("")) {
         programName = null;
      }

      Map deployReserveMap = new HashMap();
      if (deployReserve != null && deployReserve.equals("true")) {
         deployReserveMap.put("deployReserveTime", deployReserveTime);
         deployReserveMap.put("deployReserveRepeatType", deployReserveRepeatType);
         deployReserveMap.put("deployReserveStartDate", deployReserveStartDate);
         deployReserveMap.put("deployReserveEndDate", deployReserveEndDate);
         deployReserveMap.put("weekly", weekly);
         deployReserveMap.put("monthly", monthly);
      }

      if (selectId.equals("adscheduleSaveAsBtn")) {
         String newProgramId = UUID.randomUUID().toString();
         this.logger.error("[GetAdScheduleController] save as new programId : " + newProgramId);
         ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
         program.setProgram_id(newProgramId);
         if (program.getChannelList() != null) {
            List channelList = program.getChannelList();

            for(int i = 0; i < channelList.size(); ++i) {
               ((ChannelEntity)channelList.get(i)).setProgram_id(newProgramId);
               if (((ChannelEntity)channelList.get(i)).getFrameList() != null) {
                  for(int j = 0; j < ((ChannelEntity)channelList.get(i)).getFrameList().size(); ++j) {
                     String newFrameId = UUID.randomUUID().toString();
                     ((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).setProgram_id(newProgramId);
                     ((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).setFrame_id(newFrameId);
                     if (((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList() != null) {
                        for(int k = 0; k < ((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().size(); ++k) {
                           String newSlotId = UUID.randomUUID().toString();
                           ((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).setProgram_id(newProgramId);
                           ((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).setFrame_id(newFrameId);
                           ((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).setSlot_id(newSlotId);
                           if (((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).getScheduleList() != null) {
                              for(int l = 0; l < ((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).getScheduleList().size(); ++l) {
                                 String newScheduleId = UUID.randomUUID().toString();
                                 ((AdScheduleEntity)((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).getScheduleList().get(l)).setSchedule_id(newScheduleId);
                                 ((AdScheduleEntity)((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).getScheduleList().get(l)).setProgram_id(newProgramId);
                                 ((AdScheduleEntity)((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).getScheduleList().get(l)).setSlot_id(newSlotId);
                                 ((AdScheduleEntity)((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).getScheduleList().get(l)).setUser_id(userId);
                              }
                           }
                        }
                     }
                  }
               }
            }
         }

         if (!scheduleMgr.saveAdProgram(programId, programName, programGroup, deviceGroupIds, description, deployReserve, deployReserveMap, sessionId, request.getRemoteAddr())) {
            this.logger.error("[GetScheduleController] Do not save program! ");
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SCHEDULE_FILE_CREATE);
         }

         scheduleMgr.removeSchedule(CACHEDKEY);
         responsebody.setStatus("Success");
      } else {
         ScheduleInterface scheduleInfo = DAOFactory.getScheduleInfoImpl("PREMIUM");
         scheduleInfo.checkDeviceMapping(programId, deviceGroupIds);
         if (!scheduleMgr.updateADProgram(programId, programName, programGroup, deviceGroupIds, description, deployReserve, deployReserveMap, sessionId, request.getRemoteAddr())) {
            this.logger.error("[GetScheduleController] Do not save program! ");
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
         }

         scheduleMgr.removeSchedule(CACHEDKEY);
         responsebody.setStatus("Success");
      }

      responsebody.setItems(result);
      return responsebody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody postDeploy(String programId, String programName, String programGroup, String deviceGroupIds, String description, String deployReserve, String deployReserveTime, String deployReserveRepeatType, String deployReserveStartDate, String deployReserveEndDate, String weekly, String monthly, HttpServletRequest request) throws Exception {
      ResponseBody responsebody = new ResponseBody();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      String sessionId = request.getSession().getId();
      String CACHEDKEY = sessionId + programId;
      LinkedHashMap result = new LinkedHashMap();
      programName = StrUtils.nvl(programName.equals("") ? "" : programName);
      programGroup = StrUtils.nvl(programGroup.equals("") ? "" : programGroup);
      deviceGroupIds = StrUtils.nvl(deviceGroupIds.equals("") ? "" : deviceGroupIds);
      description = StrUtils.nvl(description.equals("") ? "" : description);
      deployReserve = StrUtils.nvl(deployReserve.equals("") ? "" : deployReserve);
      deployReserveTime = StrUtils.nvl(deployReserveTime.equals("") ? "" : deployReserveTime);
      Map deployReserveMap = new HashMap();
      if (deployReserve != null && deployReserve.equals("true")) {
         deployReserveMap.put("deployReserveTime", deployReserveTime);
         deployReserveMap.put("deployReserveRepeatType", deployReserveRepeatType);
         deployReserveMap.put("deployReserveStartDate", deployReserveStartDate);
         deployReserveMap.put("deployReserveEndDate", deployReserveEndDate);
         deployReserveMap.put("weekly", weekly);
         deployReserveMap.put("monthly", monthly);
      }

      if (scheduleMgr.saveAdProgram(programId, programName, programGroup, deviceGroupIds, description, deployReserve, deployReserveMap, sessionId, request.getRemoteAddr())) {
         scheduleMgr.removeSchedule(CACHEDKEY);
         responsebody.setStatus("Success");
         responsebody.setItems(result);
         return responsebody;
      } else {
         this.logger.error("[GetScheduleController] Do not save program! ");
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SCHEDULE_FILE_CREATE);
      }
   }

   private LinkedHashMap addScheduleItem(ProgramEntity program, V2ContentScheduleResource params) {
      LinkedHashMap result = new LinkedHashMap();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      List itemList = params.getItemList();
      if (itemList != null && itemList.size() > 0) {
         Iterator var6 = itemList.iterator();

         while(var6.hasNext()) {
            V2ContentScheduleItemResource contentScheduleItemResource = (V2ContentScheduleItemResource)var6.next();
            String contentName = contentScheduleItemResource.getContentName();
            int channelNo = contentScheduleItemResource.getChannelNo();
            String frameId = contentScheduleItemResource.getFrameId();
            String startDate = contentScheduleItemResource.getStartDate();
            String stopDate = contentScheduleItemResource.getStopDate();
            String startTime = contentScheduleItemResource.getStartTime();
            String duration = contentScheduleItemResource.getDuration();
            String contentId = contentScheduleItemResource.getContentId();
            String contentType = contentScheduleItemResource.getContentType();
            String repeatType = contentScheduleItemResource.getRepeatType();
            String weekdays = contentScheduleItemResource.getWeekdays();
            String monthday = contentScheduleItemResource.getMonthday();
            String scheduleType = contentScheduleItemResource.getScheduleType();
            String inputSource = contentScheduleItemResource.getInputSource();
            String isSync = contentScheduleItemResource.getIsSync();
            String cifsSlideTime = contentScheduleItemResource.getCifsSlideTime();
            String playerMode = contentScheduleItemResource.getPlayerMode();
            int priority = contentScheduleItemResource.getPriority();
            String safetyLock = contentScheduleItemResource.getSafetyLock();
            boolean check = false;
            long currentIndex = 0L;

            try {
               ContentsScheduleEntity content = new ContentsScheduleEntity();
               this.scheduleMgr.contentScheduleInit(program.getProgram_id(), content);
               content.setContent_name(contentName);
               content.setStart_date(startDate);
               content.setStop_date(stopDate);
               content.setStart_time(startTime);
               content.setDuration(Integer.valueOf(duration));
               content.setContent_id(contentId);
               content.setPlayer_mode(playerMode);
               currentIndex = program.getCurrentIndex();
               String mediaType = contentType;
               ++currentIndex;
               if (contentType != null && contentType.equals("HW_IS")) {
                  String deviceType = program.getDevice_type();
                  if (ScheduleUtility.isSupportInputSourceRepeat(deviceType, program.getDevice_type_version())) {
                     content.setContent_type("HW_IS");
                     content.setSchedule_type("00");
                  } else if (deviceType != null && (deviceType.equals("SPLAYER") || deviceType.equals("LPLAYER"))) {
                     content.setContent_id("");
                     if (inputSource.equals("1000")) {
                        content.setSchedule_type("01");
                     } else {
                        content.setSchedule_type("03");
                     }

                     content.setHw_input_source(inputSource);
                     content.setContent_type("");
                     content.setPriority(0L);
                     content.setHw_AtvDtv("-1");
                     content.setHw_AirCable("-1");
                     content.setHw_MajorCH("0");
                     content.setHw_MinorCH("0");
                     content.setHw_Volume("-1");
                     content.setHw_sch_ch("-1");
                  }
               } else {
                  PlaylistDao playlistDao = new PlaylistDao();
                  if (contentType != null && contentType.equals("PLAYLIST")) {
                     ContentFile thumbContent = playlistDao.getThumbFileInfo(contentId);
                     content.setFile_size(playlistDao.getPlaylistActiveVerInfo(contentId).getTotal_size());
                     if (thumbContent != null && thumbContent.getFile_id() != null && thumbContent.getFile_name() != null) {
                        content.setFile_id(thumbContent.getFile_id());
                        content.setFile_name(thumbContent.getFile_name());
                     } else {
                        Playlist playlistInfo = playlistDao.getPlaylistActiveVerInfo(contentId);
                        if (playlistInfo != null) {
                           ContentInfo contentDao = ContentInfoImpl.getInstance();
                           Content contentInfo = contentDao.getThumbInfoOfActiveVersion(playlistInfo.getContent_id());
                           content.setFile_id(contentInfo.getThumb_file_id());
                           content.setFile_name(contentInfo.getThumb_file_name());
                        }
                     }
                  } else {
                     ContentDao contentDao = new ContentDao();
                     Map thumbContent = contentDao.getThumbFileInfoOfActiveVersion(contentId);
                     content.setFile_size(contentDao.getContentActiveVerInfo(contentId).getTotal_size());
                     content.setFile_id((String)thumbContent.get("file_id"));
                     content.setFile_name((String)thumbContent.get("file_name"));
                  }

                  if (contentType != null && contentType.equals("CIFS")) {
                     int slideTime = 0;

                     try {
                        if (cifsSlideTime != null && !cifsSlideTime.equals("")) {
                           slideTime = Integer.valueOf(cifsSlideTime);
                        }
                     } catch (Exception var39) {
                        slideTime = 0;
                     }

                     content.setSlide_transition_time(slideTime);
                  } else {
                     content.setSlide_transition_time(0);
                  }

                  content.setSchedule_type("00");
                  content.setContent_type(contentType);
               }

               if (isSync != null && isSync.equals("true")) {
                  content.setIsSync(true);
               }

               content.setRepeat_type(repeatType);
               content.setWeekdays(weekdays);
               content.setMonthdays(monthday);
               content.setPriority((long)priority);
               content.setSafetyLock(safetyLock);
               content.setChannel_no(channelNo);
               Date date = new Date();
               Timestamp timestamp = new Timestamp(date.getTime());
               content.setModify_date(timestamp);
               content.setCreate_date(timestamp);
               content.setUser_id(userContainer.getUser().getUser_id());
               if (program != null) {
                  List channelList = program.getChannelList();
                  if (channelList == null) {
                     throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"channel list in the program information"});
                  }

                  for(int i = 0; i < channelList.size(); ++i) {
                     if (((ChannelEntity)channelList.get(i)).getChannel_no() == Integer.valueOf(channelNo)) {
                        List frameList = ((ChannelEntity)channelList.get(i)).getFrameList();
                        if (frameList == null) {
                           throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"frame list in the program information"});
                        }

                        for(int j = 0; j < frameList.size(); ++j) {
                           if (((FrameEntity)frameList.get(j)).getFrame_id().equals(frameId) || frameId.equals("new")) {
                              List scheduleList = ((FrameEntity)frameList.get(j)).getScheduleList();
                              if (program.getDevice_type().equals("SPLAYER") && mediaType != null && (mediaType.equals("LFD") || mediaType.equals("LFT")) && !((FrameEntity)frameList.get(j)).getLine_data().equals("ZeroFrameOnly")) {
                                 throw new RestServiceException(RestExceptionCode.BAD_REQUEST_LFD_CONTENT_NOT_PLAY_MULTI_FRAME_SCHEDULE);
                              }

                              if (scheduleList == null) {
                                 scheduleList = new ArrayList();
                              }

                              if (!content.getSchedule_type().equals("03")) {
                                 content.setFrame_index(((FrameEntity)frameList.get(j)).getFrame_index());
                              }

                              ((List)scheduleList).add(content);
                              ((FrameEntity)frameList.get(j)).setScheduleList((List)scheduleList);
                              ((ChannelEntity)channelList.get(i)).setFrameList(frameList);
                              program.setChannelList(channelList);
                              program.setCurrentIndex(currentIndex);
                              this.scheduleMgr.updateSchedule(program, program.getProgram_id());
                              result.put("scheduleId", content.getSchedule_id());
                              result.put("status", "success");
                              check = true;
                           }
                        }
                     }
                  }
               }
            } catch (Exception var40) {
               this.logger.error("", var40);
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_UNEXPECTED);
            }
         }
      }

      return result;
   }

   public static boolean isOverlapping(Date start1, Date end1, Date start2, Date end2) {
      return start1.before(end2) && start2.before(end1);
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Add Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public TTV2ProgramResource createContentSchedule(TTV2ProgramResource resource) throws Exception {
      RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, resource.getProgramGroupId());
      V2ContentScheduleValidator.getInstance(resource).validate();
      TTV2ProgramResource newScheduleProgramResource = (new V2ContentScheduleHelper(resource)).initialize().fit().addEvents().checkDisk().deploy();
      if (newScheduleProgramResource == null) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONTENT_SCHEDULE_DEPLOY);
      } else {
         return newScheduleProgramResource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public List getDeviceTags(V2CommonGroupIds deviceGroupIds) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      List deviceGroupIdList = deviceGroupIds.getIds();
      Long[] deviceGroupIdArr = new Long[deviceGroupIdList.size()];

      for(int i = 0; i < deviceGroupIdList.size(); ++i) {
         deviceGroupIdArr[i] = (Long)deviceGroupIdList.get(i);
      }

      List deviceTags = deviceDao.getDeviceAndTagListByGroupIds(deviceGroupIdArr);
      List deviceTagResources = new ArrayList();
      if (deviceTags != null) {
         Iterator var7 = deviceTags.iterator();

         while(var7.hasNext()) {
            DeviceTag deviceTag = (DeviceTag)var7.next();
            V2DeviceTagResource resource = new V2DeviceTagResource();
            resource.setDeviceId(deviceTag.getDevice_id());
            resource.setDeviceName(deviceTag.getDevice_name());
            resource.setTagId(String.valueOf(deviceTag.getTag_id()));
            resource.setTagName(deviceTag.getTag_name());
            resource.setTagType(deviceTag.getTag_type() == null ? "" : String.valueOf(deviceTag.getTag_type()));
            resource.setTagConditionId(deviceTag.getTag_condition_id() == null ? "" : String.valueOf(deviceTag.getTag_condition_id()));
            resource.setTagConditionName(deviceTag.getTag_condition());
            resource.setTagDescription(deviceTag.getTag_desc());
            deviceTagResources.add(resource);
         }
      }

      return deviceTagResources;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public TTV2ProgramResource getContentSchedule(String programId) throws Exception {
      if (!V2ContentScheduleValidator.isValidActionAsScheduleEditor(programId, RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE)) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE);
      }

      return (new V2ContentScheduleHelper()).loadProgramResource(programId);
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Add Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public TTV2ProgramResource updateContentSchedule(String programId, TTV2ProgramResource resource) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      if (!V2ContentScheduleValidator.isValidActionAsScheduleEditor(programId, RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE)) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE);
      }

      V2ContentScheduleValidator.getInstance(resource).validate();
      TTV2ProgramResource modifiedScheduleProgramResource = (new V2ContentScheduleHelper(programId, resource)).load().fit().addEvents().checkDisk().update();
      if (modifiedScheduleProgramResource == null) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONTENT_SCHEDULE_UPDATE);
      } else {
         return modifiedScheduleProgramResource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public TTV2ProgramResource simpleUpdateContentSchedule(String programId, TTV2ProgramSummaryInfo summary) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      if (!V2ContentScheduleValidator.isValidActionAsScheduleEditor(programId, RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE)) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE);
      }

      ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
      ProgramEntity programEntity = schInfo.getProgram(programId);
      if (programEntity == null) {
         this.logger.error("The content-schedule does not exist : " + programId);
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"programId"});
      } else {
         programEntity.setProgram_name(summary.getProgramName());
         programEntity.setProgram_group_id(summary.getProgramGroupId());
         programEntity.setDescription(summary.getDescription());
         if (schInfo.updateProgramView(programEntity)) {
            try {
               ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
               ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
               List notiDataList = new ArrayList();
               MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
               ProgramEntity program = scheduleInfo.getProgram(programId);
               String userId = SecurityUtils.getLoginUserId();
               String orgName = programGroupInfo.getOrgNameByGroupId(program.getProgram_group_id());
               NotificationData notiData = new NotificationData();
               notiData.setName(program.getProgram_name());
               notiData.setOrgName(orgName);
               notiData.setUserName(userId);
               notiDataList.add(notiData);
               if (notiDataList.size() > 0) {
                  MailUtil.sendContentScheduleEventMail(notiDataList, "01");
               }

               List deviceIds = scheduleInfo.getDevicesMappedInProgram(programId);
               Iterator var14 = deviceIds.iterator();

               while(var14.hasNext()) {
                  String deviceId = (String)var14.next();
                  CurrentPlayingEntity curr = monMgr.getPlayingContent(deviceId);
                  if (curr != null) {
                     curr.setProgramName(program.getProgram_name());
                     curr.setProgramId(program.getProgram_id());
                     monMgr.setPlayingContent(deviceId, curr);
                  }
               }
            } catch (Exception var17) {
               this.logger.error(var17);
            }

            return (new V2ContentScheduleHelper()).loadProgramResource(programId);
         } else {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONTENT_SCHEDULE_UPDATE);
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public TTV2ProgramResource saveAsNewContentSchedule(String programId, TTV2ProgramResource resource) throws Exception {
      RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, resource.getProgramGroupId());
      if (!V2ContentScheduleValidator.isValidActionAsScheduleEditor(programId, RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE)) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE);
      }

      V2ContentScheduleValidator.getInstance(resource).validate();
      TTV2ProgramResource modifiedScheduleProgramResource = (new V2ContentScheduleHelper(programId, resource)).load().fit().addEvents().saveAsNew().checkDisk().deploy();
      if (modifiedScheduleProgramResource == null) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONTENT_SCHEDULE_UPDATE);
      } else {
         return modifiedScheduleProgramResource;
      }
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody editContentSchedule(String programId, V2ContentScheduleResource params) throws ConfigException, SQLException, Exception {
      ResponseBody responseBody = new ResponseBody();
      String sessionId = "";
      ProgramEntity program = this.scheduleMgr.getScheudle(programId);
      if (program == null) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage("[MagicInfo_Schedule] Do not get program! ");
         return responseBody;
      } else {
         this.delScheduleItem(program, params);
         this.addScheduleItem(program, params);
         String deviceGroupIds = ConvertUtil.convertListToStringWithSeparator(params.getDeviceGroupIds(), ",");
         if (this.scheduleMgr.updateProgram(programId, params.getScheduleName(), params.getScheduleGroupId(), deviceGroupIds, params.getDescription(), params.getDeployReserve(), params.getDeployReserveResource(), params.getContentSyncOn(), sessionId, params.getBackgroundMusic(), params.getContentMute(), "mobile", params.getResume())) {
            responseBody.setStatus("Success");
            return responseBody;
         } else {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
         }
      }
   }

   private LinkedHashMap delScheduleItem(ProgramEntity program, V2ContentScheduleResource params) throws Exception {
      LinkedHashMap result = new LinkedHashMap();
      if (program == null) {
         this.logger.info("[MagicInfo_Schedule] program is null");
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"program information"});
      } else {
         List channelList = program.getChannelList();
         if (channelList == null) {
            this.logger.info("[MagicInfo_Schedule] channelList null : programId = " + program.getProgram_id());
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"channel list in the program information"});
         } else {
            for(int i = 0; i < channelList.size(); ++i) {
               List frameList = ((ChannelEntity)channelList.get(i)).getFrameList();
               if (frameList == null) {
                  this.logger.info("[MagicInfo_Schedule] frameList null : programId = " + program.getProgram_id());
                  throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"frame list in the program information"});
               }

               for(int j = 0; j < frameList.size(); ++j) {
                  List scheduleList = ((FrameEntity)frameList.get(j)).getScheduleList();
                  if (scheduleList == null) {
                     throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"content schedule list in the program information"});
                  }

                  ((FrameEntity)((ChannelEntity)program.getChannelList().get(i)).getFrameList().get(j)).setScheduleList(new ArrayList());
                  this.scheduleMgr.updateSchedule(program, program.getProgram_id());
               }
            }

            return result;
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public TTV2ProgramResource updateDevGroups(String programId, V2CommonIds deviceGroupIds) throws Exception {
      boolean flag = false;
      List groupIds = deviceGroupIds.getIds();

      for(int i = 0; i < groupIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.parseLong((String)groupIds.get(i)));
         } catch (Exception var25) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE);
      }

      if (!StrUtils.nvl(programId).equals("")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      }

      if (!V2ContentScheduleValidator.isValidActionAsScheduleEditor(programId, RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE)) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE);
      }

      boolean bRegLicLfd = false;
      boolean bool_reg_lic_soc = false;
      boolean bool_reg_lic_android = false;
      boolean bool_reg_lic_sinage = false;
      boolean bool_reg_lic_lite = false;
      boolean SlmLicenseCheck = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (bool_reg_lic_soc || bRegLicLfd || bool_reg_lic_android || bool_reg_lic_sinage || bool_reg_lic_lite) {
         bRegLicLfd = true;
         SlmLicenseCheck = true;
      }

      AbilityUtils ability = new AbilityUtils();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      ScheduleInterface infoDao = DAOFactory.getScheduleInfoImpl("PREMIUM");
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      boolean scheduleWrite = ability.checkAuthority("Content Schedule Add") && SlmLicenseCheck;
      boolean scheduleManage = ability.checkAuthority("Content Schedule Write") && SlmLicenseCheck;
      String groupId = "";
      if (groupIds != null && groupIds.size() > 0) {
         groupId = ConvertUtil.convertListToStringWithSeparator(groupIds, ",");
      }

      String strGroupId = this.checkDeviceGroupAuth(programId, groupId, userContainer.getUser().getUser_id(), userContainer.getUser().getRole_name(), "PREMIUM");
      if (!scheduleManage && (!scheduleWrite || !scheduleMgr.checkProgramPermission(userId, programId))) {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_NOT_HAVE, new String[]{"add & write"});
      } else {
         CommonProgramEntity program = infoDao.getProgram(programId);
         if (program != null) {
            Map m = infoDao.getDeviceGroupIdsAndName(programId);
            program.setDevice_group_ids((String)m.get("device_group_ids"));
            boolean updated = infoDao.updateDeviceGroup(strGroupId, programId, userContainer.getUser().getUser_id(), "");

            try {
               infoDao.reserveSchedule(program);
            } catch (Exception var24) {
               this.logger.error(var24);
            }

            if (!updated) {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
            } else {
               return (new V2ContentScheduleHelper()).loadProgramResource(programId);
            }
         } else {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"program information"});
         }
      }
   }

   private String checkDeviceGroupAuth(String strProgId, String strGroupId, String userId, String roleName, String productType) throws SQLException {
      UserInfo uInfo = UserInfoImpl.getInstance();
      long orgId = uInfo.getRootGroupIdByUserId(userId);
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      boolean permissions_func = serverSetupDao.checkPermissionsDeviceByOrgId(orgId);
      if (permissions_func && !roleName.equals("Administrator") && !roleName.equals("Server Administrator")) {
         String result = strGroupId;
         DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
         List deviceList = deviceGroupDao.getScheduleMappingDeviceGroupAuth(strProgId, userId, false);
         if (deviceList != null && deviceList.size() > 0) {
            if (strGroupId.length() > 0) {
               result = strGroupId + ",";
            }

            result = result + ((DeviceGroup)deviceList.get(0)).getGroup_id();

            for(int i = 1; i < deviceList.size(); ++i) {
               result = result + "," + ((DeviceGroup)deviceList.get(i)).getGroup_id();
            }
         }

         return result;
      } else {
         return strGroupId;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public V2PageResource listContentScheduleAll(V2ScheduleFilter filter) throws Exception {
      if (!StrUtils.nvl(filter.getGroupId()).equals("")) {
         RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, Long.parseLong(filter.getGroupId()));
      }

      UserContainer userContainer = SecurityUtils.getUserContainer();
      String productType = "PREMIUM";
      ScheduleInterface infoDao = DAOFactory.getScheduleInfoImpl(productType);
      String contentExits = "Exists";
      String contentDExists = "Does Not Exist";
      String creatorMapped = "Mapped";
      String creatorNotMapped = "Not Mapped";
      String strDefault = "Default";
      AbilityUtils abilityUtils = new AbilityUtils();
      String sort_name = "modify_date";
      String order_dir = null;
      int results = true;
      int startIndex2 = true;
      String searchText = null;
      String strGroup = "";
      String strFrameCount = null;
      String strRootGroup = userContainer.getUser().getOrganization();
      String groupId = null;
      String startModifiedDate = "";
      String endModifiedDate = "";
      String searchId = "-1";
      String strContentIds = "";
      List contentIds = filter.getContentIds();
      if (contentIds != null && contentIds.size() > 0) {
         strContentIds = ConvertUtil.convertListToStringWithSeparator(contentIds, ",");
         searchId = "-2";
      }

      String strDeviceGroupIds = "";
      List deviceGroupIds = filter.getDevGroupIds();
      if (deviceGroupIds != null && deviceGroupIds.size() > 0) {
         strDeviceGroupIds = ConvertUtil.convertListToStringWithSeparator(deviceGroupIds, ",");
         searchId = "-2";
      }

      String strScheduleTypes = "";
      List scheduleTypes = filter.getScheduleTypes();
      if (scheduleTypes != null) {
         if (scheduleTypes.size() <= 0) {
            List dataList = new ArrayList();
            V2PageResource resource = V2PageResource.createPageResource(dataList, 0);
            resource.setStartIndex(filter.getStartIndex());
            resource.setPageSize(filter.getPageSize());
            return resource;
         }

         strScheduleTypes = ConvertUtil.convertListToStringWithSeparator(scheduleTypes, ",");
      }

      if (!StrUtils.nvl(filter.getStartModifiedDate()).equals("")) {
         startModifiedDate = filter.getStartModifiedDate();
         searchId = "-2";
      }

      if (!StrUtils.nvl(filter.getEndModifiedDate()).equals("")) {
         endModifiedDate = filter.getEndModifiedDate();
         searchId = "-2";
      }

      int startIndex2 = filter.getStartIndex();
      int results = filter.getPageSize();
      if (!StrUtils.nvl(filter.getSearchText()).equals("")) {
         searchText = filter.getSearchText();
         searchId = "-2";
         searchText = searchText.replaceAll("]", "^]");
         searchText = searchText.replaceAll("%", "^%");
      }

      if (!StrUtils.nvl(filter.getGroupType()).equals("")) {
         strGroup = filter.getGroupType();
      }

      if (!StrUtils.nvl(filter.getGroupId()).equals("")) {
         groupId = filter.getGroupId();
      }

      if (strGroup != null && strGroup.equals("TRASH")) {
         sort_name = "modify_date";
      }

      if (!StrUtils.nvl(filter.getSortColumn()).equals("")) {
         sort_name = filter.getSortColumn();
      }

      if (!StrUtils.nvl(filter.getSortOrder()).equals("")) {
         order_dir = filter.getSortOrder();
      }

      SelectConditionScheduleAdmin conditionList = new SelectConditionScheduleAdmin();
      ListManager listMgr = new ListManager(infoDao, "commonlist");
      conditionList.setSearch_id(searchId);
      conditionList.setSort_name(sort_name);
      conditionList.setOrder_dir(order_dir);
      conditionList.setNameLike(searchText);
      conditionList.setGroupType(strGroup);
      conditionList.setFrameCount((String)strFrameCount);
      conditionList.setUserRootGroup(strRootGroup);
      conditionList.setDevice_type(filter.getDeviceType());
      conditionList.setStart_modified_date(startModifiedDate);
      conditionList.setEnd_modified_date(endModifiedDate);
      if (strContentIds != null && !strContentIds.equals("")) {
         conditionList.setSelect_content_ids(strContentIds);
      }

      if (strDeviceGroupIds != null && !strDeviceGroupIds.equals("")) {
         conditionList.setSelect_devgroup_ids(strDeviceGroupIds);
      }

      if (strScheduleTypes != null && !strScheduleTypes.equals("")) {
         conditionList.setProgram_type(strScheduleTypes);
      }

      if (groupId != null) {
         conditionList.setGroupType(groupId);
      }

      listMgr.addSearchInfo("condition", conditionList);
      listMgr.setLstSize(Integer.valueOf(results));
      if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         if ("TRASH".equals(strGroup) || !userContainer.checkAuthority("Content Schedule Write") && !userContainer.checkAuthority("Content Schedule Read")) {
            listMgr.setSection("getScheduleByUser");
            listMgr.addSearchInfo("UserGroupId", userContainer.getUser().getGroup_id());
         } else {
            listMgr.setSection("getScheduleAdminInfo");
         }
      }

      List frameList = infoDao.getDistinctFrames(conditionList);
      String strFrameVal = null;
      if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         strFrameVal = this.getFrameVal(frameList);
      }

      PageManager pageMgr = null;
      List infoList = null;
      if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         infoList = listMgr.V2dbexecuteWithTypeFilter(startIndex2, results);
      } else {
         infoList = listMgr.V2dbexecute(startIndex2, results);
      }

      pageMgr = listMgr.getPageManager();
      new ListEntity();
      int size = infoList.size();
      List dataList = new ArrayList();
      DeviceGroupInfo deviceGroupDao = null;
      if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      }

      for(int i = 0; i < size; ++i) {
         CommonProgramEntity info = (CommonProgramEntity)infoList.get(i);
         V2ContentScheduleAllListResource data = new V2ContentScheduleAllListResource();
         data.setCheckbox("");
         String contentMapping = "";
         String defaultMapping = "";
         String deviceType = "iPLAYER";
         Float deviceTypeVersion = 1.0F;
         if (info.getContent_id().equalsIgnoreCase("Y")) {
            contentMapping = contentExits;
         } else if (info.getContent_id().equalsIgnoreCase("N")) {
            contentMapping = contentDExists;
         }

         if (info.getCreator_group_attached() != null && info.getCreator_group_attached().equalsIgnoreCase("N")) {
            defaultMapping = creatorNotMapped;
         } else if (info.getCreator_group_attached() != null && info.getCreator_group_attached().equalsIgnoreCase("Y")) {
            defaultMapping = creatorMapped;
         }

         if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
            if (info.getDevice_type() != null && !info.getDevice_type().equalsIgnoreCase("")) {
               deviceType = info.getDevice_type();
               deviceTypeVersion = info.getDevice_type_version();
            }

            data.setDeviceType(deviceType);
            data.setDeviceTypeVersion(deviceTypeVersion);
         }

         data.setProgramName(info.getProgram_name());
         data.setProgramId(info.getProgram_id());
         data.setScheduleTypes(info.getProgram_type());
         DownloadStatusInfo downloadInfo = DownloadStatusInfoImpl.getInstacne();
         Timestamp modifyTime = null;
         if (info.getModify_date() != null) {
            modifyTime = info.getModify_date();
         }

         data.setModifyDate(modifyTime);
         data.setIsSync(info.getUse_sync_play());
         data.setIsVwl(info.getUse_multi_vwl());
         data.setIsAdSchedule(info.getUse_ad_schedule());
         if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
            List deviceGroupList = null;
            if (deviceGroupDao != null) {
               deviceGroupList = deviceGroupDao.getScheduleMappingDeviceGroupAuth(info.getProgram_id(), (String)null, true);
            }

            if (info.getDevice_group_name() != null && !info.getDevice_group_name().equals("")) {
               if (deviceGroupList != null) {
                  List deviceGroups = new ArrayList();
                  Iterator var65 = deviceGroupList.iterator();

                  while(var65.hasNext()) {
                     DeviceGroup deviceGroup = (DeviceGroup)var65.next();
                     TTV2DeviceGroupInfo groupInfo = new TTV2DeviceGroupInfo();
                     groupInfo.setGroupId(deviceGroup.getGroup_id());
                     groupInfo.setGroupName(deviceGroup.getGroup_name());
                     deviceGroups.add(groupInfo);
                  }

                  data.setDeviceGroups(deviceGroups);
               }
            } else if (abilityUtils.checkAuthority("Device Read")) {
               String deviceGroupIdList = info.getDevice_group_id();
               String deviceGroupNameList = info.getDevice_group_name();
               if (deviceGroupIdList != null && !deviceGroupIdList.isEmpty() && deviceGroupNameList != null && !deviceGroupNameList.isEmpty()) {
                  String[] tempDeviceGroupIds = deviceGroupIdList.split(",");
                  String[] tempDeviceGroupNames = deviceGroupNameList.split(",");
                  List deviceGroups = new ArrayList();

                  for(int j = 0; j < tempDeviceGroupIds.length; ++j) {
                     TTV2DeviceGroupInfo groupInfo = new TTV2DeviceGroupInfo();
                     groupInfo.setGroupId(Long.parseLong(tempDeviceGroupIds[j]));
                     groupInfo.setGroupName(tempDeviceGroupNames[j]);
                     deviceGroups.add(groupInfo);
                  }

                  data.setDeviceGroups(deviceGroups);
               }
            } else {
               data.setDeviceGroups((List)null);
            }

            DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
            boolean onVWLMenu = deviceGroupInfo.isVWLLayoutGroup(info.getDevice_group_id());
            if (onVWLMenu) {
               data.setOnVWLMenu(true);
            } else {
               data.setOnVWLMenu(false);
            }

            int dCnt = deviceGroupDao.getCntDeviceInProgram(info.getProgram_id());
            if (info.getDevice_group_id() != null && !info.getDevice_group_id().equals("")) {
               Object[] rtn = downloadInfo.getDownloadStatusListByProgramId(info.getProgram_id(), dCnt);
               if (rtn != null && rtn[1] != null) {
                  Map resultMap = (Map)rtn[1];
                  if (resultMap.get("deviceCount") != null) {
                     data.setPublishTotalCount((Integer)resultMap.get("deviceCount"));
                  }

                  if (resultMap.get("completeCount") != null) {
                     data.setPublishCurrentCount((Integer)resultMap.get("completeCount"));
                  }
               }

               switch((Integer)rtn[0]) {
               case 0:
                  data.setPublishStatus("published");
                  break;
               case 1:
                  data.setPublishStatus("publishing");
                  break;
               case 2:
                  data.setPublishStatus("waiting");
                  break;
               default:
                  data.setPublishStatus("");
               }
            }

            data.setDeviceCount(dCnt);
         }

         if ("default".equalsIgnoreCase(strGroup)) {
            data.setGroupName(StrUtils.nvl(strDefault));
            data.setGroupId(StrUtils.nvl(strDefault));
         } else {
            data.setGroupName(StrUtils.nvl(info.getGroup_name()));
            data.setGroupId(String.valueOf(info.getGroup_id()));
         }

         if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
            data.setChannelCount(info.getChannelCount());
            data.setContentId(contentMapping);
            data.setCreatorMapped(defaultMapping);
            data.setFramevals(strFrameVal);
         }

         data.setContentExist(contentMapping);
         ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
         ProgramEntity programEntity = scheduleInfo.getProgram(info.getProgram_id());
         data.setDeployTime(programEntity != null ? programEntity.getDeploy_time() : "");
         data.setUseSyncPlay(info.getUse_sync_play());
         Timestamp lastDeployTime = null;
         if (info.getLastdeploy_date() != null) {
            lastDeployTime = info.getLastdeploy_date();
         }

         data.setLastdeployDate(lastDeployTime);
         dataList.add(data);
      }

      V2PageResource resource = V2PageResource.createPageResource(dataList, pageMgr);
      resource.setStartIndex(startIndex2);
      resource.setPageSize(results);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   private String getFrameVal(List frameList) {
      StringBuffer strFrameBuffer = new StringBuffer();
      int frameCount = false;
      int frameCount = frameList.size();

      for(int i = 0; i < frameCount; ++i) {
         if (i == 0) {
            strFrameBuffer.append(String.valueOf(((ScheduleAdminEntity)frameList.get(i)).getFrameCount()));
         } else {
            strFrameBuffer.append(":").append(String.valueOf(((ScheduleAdminEntity)frameList.get(i)).getFrameCount()));
         }
      }

      return strFrameBuffer.toString();
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public ModelAndView contentScheduleExport(V2ScheduleFilter filter, String exportType, HttpServletResponse response, String localeData) throws Exception {
      if (!StrUtils.nvl(filter.getGroupId()).equals("")) {
         RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, Long.parseLong(filter.getGroupId()));
      }

      UserContainer userContainer = SecurityUtils.getUserContainer();
      String productType = "PREMIUM";
      ScheduleInterface infoDao = DAOFactory.getScheduleInfoImpl(productType);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      if (StrUtils.nvl(localeData).equals("")) {
         String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
         if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
            localeData = userLocale;
         } else {
            localeData = "en";
         }
      }

      Locale locale = new Locale(localeData);
      String sort_name = null;
      String order_dir = null;
      int results = true;
      int startIndex2 = true;
      String searchText = null;
      String strGroup = "";
      String strFrameCount = null;
      String strRootGroup = userContainer.getUser().getOrganization();
      String groupId = null;
      String startModifiedDate = "";
      String endModifiedDate = "";
      String searchId = "-1";
      String strContentIds = "";
      List contentIds = filter.getContentIds();
      if (contentIds != null && contentIds.size() > 0) {
         strContentIds = ConvertUtil.convertListToStringWithSeparator(contentIds, ",");
         searchId = "-2";
      }

      String strDeviceGroupIds = "";
      List deviceGroupIds = filter.getDevGroupIds();
      if (deviceGroupIds != null && deviceGroupIds.size() > 0) {
         strDeviceGroupIds = ConvertUtil.convertListToStringWithSeparator(deviceGroupIds, ",");
         searchId = "-2";
      }

      String strScheduleTypes = "";
      List scheduleTypes = filter.getScheduleTypes();
      String strFrameVal;
      if (scheduleTypes != null) {
         if (scheduleTypes.size() <= 0) {
            Map dataMap = new HashMap();
            String fileExtension = exportType.equalsIgnoreCase("PDF") ? "pdf" : "xls";
            String fileName = "ScheduleList." + fileExtension;
            strFrameVal = "Schedule";
            dataMap.put("fileName", fileName);
            dataMap.put("sheetName", strFrameVal);
            if (exportType.equalsIgnoreCase("PDF")) {
               PdfBuilder pdfView = new PdfBuilder();
               return new ModelAndView(pdfView, dataMap);
            }

            this.downloadService = new DeviceStatisticsDownloadService();
            this.downloadService.downloadExcelFile(dataMap, response);
            return null;
         }

         strScheduleTypes = ConvertUtil.convertListToStringWithSeparator(scheduleTypes, ",");
         searchId = "-2";
      }

      if (!StrUtils.nvl(filter.getStartModifiedDate()).equals("")) {
         startModifiedDate = filter.getStartModifiedDate();
         searchId = "-2";
      }

      if (!StrUtils.nvl(filter.getEndModifiedDate()).equals("")) {
         endModifiedDate = filter.getEndModifiedDate();
         searchId = "-2";
      }

      int startIndex2 = filter.getStartIndex();
      int results = filter.getPageSize();
      if (!StrUtils.nvl(filter.getSearchText()).equals("")) {
         searchText = filter.getSearchText();
         searchId = "-2";
         searchText = searchText.replaceAll("\\[", "^[");
         searchText = searchText.replaceAll("]", "^]");
         searchText = searchText.replaceAll("%", "^%");
      }

      if (!StrUtils.nvl(filter.getGroupType()).equals("")) {
         strGroup = filter.getGroupType();
      }

      if (!StrUtils.nvl(filter.getGroupId()).equals("")) {
         groupId = filter.getGroupId();
      }

      if (strGroup != null && strGroup.equals("TRASH")) {
         sort_name = "modify_date";
      }

      if (!StrUtils.nvl(filter.getSortColumn()).equals("")) {
         sort_name = filter.getSortColumn();
      }

      if (!StrUtils.nvl(filter.getSortOrder()).equals("")) {
         order_dir = filter.getSortOrder();
      }

      SelectConditionScheduleAdmin conditionList = new SelectConditionScheduleAdmin();
      ListManager listMgr = new ListManager(infoDao, "commonlist");
      conditionList.setSearch_id(searchId);
      conditionList.setSort_name(sort_name);
      conditionList.setOrder_dir(order_dir);
      conditionList.setNameLike(searchText);
      conditionList.setGroupType(strGroup);
      conditionList.setFrameCount((String)strFrameCount);
      conditionList.setUserRootGroup(strRootGroup);
      conditionList.setDevice_type(filter.getDeviceType());
      conditionList.setStart_modified_date(startModifiedDate);
      conditionList.setEnd_modified_date(endModifiedDate);
      if (strContentIds != null && !strContentIds.equals("")) {
         conditionList.setSelect_content_ids(strContentIds);
      }

      if (strDeviceGroupIds != null && !strDeviceGroupIds.equals("")) {
         conditionList.setSelect_devgroup_ids(strDeviceGroupIds);
      }

      if (strScheduleTypes != null && !strScheduleTypes.equals("")) {
         conditionList.setProgram_type(strScheduleTypes);
      }

      if (groupId != null) {
         conditionList.setGroupType(groupId);
      }

      listMgr.addSearchInfo("condition", conditionList);
      if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         if (strGroup.equals("TRASH") || !userContainer.checkAuthority("Content Schedule Write") && !userContainer.checkAuthority("Content Schedule Read")) {
            listMgr.setSection("getScheduleByUser");
            listMgr.addSearchInfo("UserGroupId", userContainer.getUser().getGroup_id());
         } else {
            listMgr.setSection("getScheduleAdminInfo");
         }
      }

      List frameList = infoDao.getDistinctFrames(conditionList);
      strFrameVal = null;
      if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         this.getFrameVal(frameList);
      }

      List infoList = null;
      if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         infoList = listMgr.V2dbexecuteWithTypeFilter(startIndex2, results);
      } else {
         infoList = listMgr.V2dbexecute(startIndex2, results);
      }

      exportType = StrUtils.nvl(exportType).equals("") ? "EXCEL" : exportType;
      Map dataMap = new HashMap();
      String fileExtension = exportType.equalsIgnoreCase("PDF") ? "pdf" : "xls";
      String fileName = "ScheduleList." + fileExtension;
      String sheetName = "Schedule";
      String msgScheduleName;
      String msgDeviceGroup;
      String msgLastDeployDate;
      String msgCreatorMapped;
      String[] fieldNames;
      if ("default".equalsIgnoreCase(filter.getGroupType())) {
         msgScheduleName = rms.getMessage("TEXT_SCHEDULE_NAME_P", (Object[])null, new Locale(locale.getLanguage()));
         msgDeviceGroup = rms.getMessage("TEXT_DEVICE_GROUP_P", (Object[])null, new Locale(locale.getLanguage()));
         msgLastDeployDate = rms.getMessage("TEXT_LASTDEPLOY_DATE_P", (Object[])null, new Locale(locale.getLanguage()));
         msgCreatorMapped = rms.getMessage("text.default_map", (Object[])null, new Locale(locale.getLanguage()));
         String[] columnNames = new String[]{"program_name", "device_group_name", "lastdeploy_date", "creator_group_attached"};
         fieldNames = new String[]{msgScheduleName, msgDeviceGroup, msgLastDeployDate, msgCreatorMapped};
         dataMap.put("columnNames", columnNames);
         dataMap.put("fieldNames", fieldNames);
      } else {
         msgScheduleName = rms.getMessage("TEXT_SCHEDULE_NAME_P", (Object[])null, new Locale(locale.getLanguage()));
         msgDeviceGroup = rms.getMessage("TEXT_DEVICE_GROUP_P", (Object[])null, new Locale(locale.getLanguage()));
         msgLastDeployDate = rms.getMessage("TEXT_SCHEDULE_GROUP_P", (Object[])null, new Locale(locale.getLanguage()));
         msgCreatorMapped = rms.getMessage("MIS_TEXT_CHANNEL_COUNT_P", (Object[])null, new Locale(locale.getLanguage()));
         String msgLastModifyDate = rms.getMessage("TEXT_LASTDEPLOY_DATE_P", (Object[])null, new Locale(locale.getLanguage()));
         fieldNames = new String[]{"program_name", "group_name", "channelCount", "device_group_name", "modify_date"};
         String[] fieldNames = new String[]{msgScheduleName, msgLastDeployDate, msgCreatorMapped, msgDeviceGroup, msgLastModifyDate};
         dataMap.put("columnNames", fieldNames);
         dataMap.put("fieldNames", fieldNames);
      }

      int dataListSize = infoList.size();
      Object[] dataList = new Object[dataListSize];

      for(int index = 0; index < dataListSize; ++index) {
         dataList[index] = infoList.get(index);
      }

      dataMap.put("fileName", fileName);
      dataMap.put("sheetName", sheetName);
      dataMap.put("dataList", dataList);
      if (exportType.equalsIgnoreCase("PDF")) {
         PdfBuilder pdfView = new PdfBuilder();
         return new ModelAndView(pdfView, dataMap);
      } else {
         this.downloadService = new DeviceStatisticsDownloadService();
         this.downloadService.downloadExcelFile(dataMap, response);
         return null;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public V2ScheduleSelectChannelResource scheduleSelectChannel(String programId, String channelNo, String sessionId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      String CACHEDKEY = sessionId + programId;
      boolean bRegLicLfd = false;
      boolean bool_reg_lic_soc = false;
      boolean bool_reg_lic_android = false;
      boolean bool_reg_lic_sinage = false;
      boolean bool_reg_lic_lite = false;
      boolean SlmLicenseCheck = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (bool_reg_lic_soc || bRegLicLfd || bool_reg_lic_android || bool_reg_lic_sinage || bool_reg_lic_lite) {
         bRegLicLfd = true;
         SlmLicenseCheck = true;
      }

      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("scheduleTab");
      int no = Integer.valueOf(channelNo);
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      V2ScheduleSelectChannelResource channel = new V2ScheduleSelectChannelResource();
      if (program == null) {
         this.logger.info("[MagicInfo_Schedule] program null : " + programId);
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"program"});
      } else {
         if (program.getChannelList() != null) {
            Iterator var16 = program.getChannelList().iterator();

            while(var16.hasNext()) {
               ChannelEntity channelEntity = (ChannelEntity)var16.next();
               if (channelEntity.getChannel_no() == no) {
                  channel.setChannelNo(channelEntity.getChannel_no());
                  channel.setChannelName(channelEntity.getChannel_name());
                  channel.setChannelDes(channelEntity.getChannel_description());
                  if (channelEntity.getFrameList() != null) {
                     channel.setFrameId(((FrameEntity)channelEntity.getFrameList().get(0)).getFrame_id());
                     channel.setFrameName(((FrameEntity)channelEntity.getFrameList().get(0)).getFrame_name());
                     channel.setFrameIndex(((FrameEntity)channelEntity.getFrameList().get(0)).getFrame_index());
                     channel.setIsMainFrame(((FrameEntity)channelEntity.getFrameList().get(0)).getIs_main_frame());
                     channel.setX(((FrameEntity)channelEntity.getFrameList().get(0)).getX());
                     channel.setY(((FrameEntity)channelEntity.getFrameList().get(0)).getY());
                     channel.setWidth(((FrameEntity)channelEntity.getFrameList().get(0)).getWidth());
                     channel.setHeight(((FrameEntity)channelEntity.getFrameList().get(0)).getHeight());
                     channel.setLineData(((FrameEntity)channelEntity.getFrameList().get(0)).getLine_data());
                     if (((FrameEntity)channelEntity.getFrameList().get(0)).getAuthorityList() != null) {
                        channel.setAuthority(((FrameEntity)channelEntity.getFrameList().get(0)).getAuthorityList().toString());
                     }
                  }
               }
            }
         }

         return channel;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public V2ScheduleAddChannelResource scheduleAddChannel(String programId, String sessionId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      String CACHEDKEY = sessionId + programId;
      V2ScheduleAddChannelResource resource = new V2ScheduleAddChannelResource();
      boolean bRegLicLfd = false;
      boolean bool_reg_lic_soc = false;
      boolean bool_reg_lic_android = false;
      boolean bool_reg_lic_sinage = false;
      boolean bool_reg_lic_lite = false;
      boolean SlmLicenseCheck = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (bool_reg_lic_soc || bRegLicLfd || bool_reg_lic_android || bool_reg_lic_sinage || bool_reg_lic_lite) {
         bRegLicLfd = true;
         SlmLicenseCheck = true;
      }

      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("scheduleTab");
      new LinkedHashMap();
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      List list = program.getChannelList();
      if (list != null && list.size() > 0) {
         int channelNum = 1;
         List channelList = new ArrayList();
         Iterator var18 = list.iterator();

         while(var18.hasNext()) {
            ChannelEntity channel = (ChannelEntity)var18.next();
            channelList.add(channel.getChannel_no());
         }

         for(int i = 1; i < 100; ++i) {
            if (!channelList.contains(i)) {
               channelNum = i;
               break;
            }
         }

         program.getChannelList().add(scheduleMgr.addChannel(programId, channelNum, "New Channel"));
         scheduleMgr.setScheudle(CACHEDKEY, program);
         resource.setChannelNum(channelNum);
      }

      resource.setProgramId(programId);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public V2ScheduleDeleteChannelResource scheduleDelChannel(String programId, int removeChannel, String sessionId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      String CACHEDKEY = sessionId + programId;
      boolean bRegLicLfd = false;
      boolean bool_reg_lic_soc = false;
      boolean bool_reg_lic_android = false;
      boolean bool_reg_lic_sinage = false;
      boolean bool_reg_lic_lite = false;
      boolean SlmLicenseCheck = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (bool_reg_lic_soc || bRegLicLfd || bool_reg_lic_android || bool_reg_lic_sinage || bool_reg_lic_lite) {
         bRegLicLfd = true;
         SlmLicenseCheck = true;
      }

      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("scheduleTab");
      V2ScheduleDeleteChannelResource result = new V2ScheduleDeleteChannelResource();
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      List channels = program.getChannelList();
      if (channels != null && channels.size() > 0) {
         Iterator nextChannel = channels.iterator();

         while(nextChannel.hasNext()) {
            ChannelEntity channel = (ChannelEntity)nextChannel.next();
            int channelNo = channel.getChannel_no();
            if (channelNo == Integer.valueOf(removeChannel)) {
               nextChannel.remove();
               break;
            }
         }

         scheduleMgr.setScheudle(CACHEDKEY, program);
      }

      result.setProgramId(programId);
      result.setRemoveChannel(removeChannel);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public V2ScheduleEditChannelResource scheduleEditChannel(String programId, String channelList, V2ScheduleEditChannelFilter filter, String sessionId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      String CACHEDKEY = sessionId + programId;
      V2ScheduleEditChannelResource resource = new V2ScheduleEditChannelResource();
      boolean bRegLicLfd = false;
      boolean bool_reg_lic_soc = false;
      boolean bool_reg_lic_android = false;
      boolean bool_reg_lic_sinage = false;
      boolean bool_reg_lic_lite = false;
      boolean SlmLicenseCheck = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (bool_reg_lic_soc || bRegLicLfd || bool_reg_lic_android || bool_reg_lic_sinage || bool_reg_lic_lite) {
         bRegLicLfd = true;
         SlmLicenseCheck = true;
      }

      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("scheduleTab");
      String channelNo = StrUtils.nvl(channelList.equals("") ? "" : channelList);
      String[] ChannelArray = null;
      if (!channelNo.equals("")) {
         ChannelArray = channelNo.split(",");
      }

      String[] removeChanneArray = null;
      if (filter.getRemoveChannelList() != null) {
         removeChanneArray = new String[filter.getRemoveChannelList().size()];

         for(int i = 0; i < filter.getRemoveChannelList().size(); ++i) {
            if (!((String)filter.getRemoveChannelList().get(i)).equals("")) {
               removeChanneArray[i] = (String)filter.getRemoveChannelList().get(i);
            }
         }
      }

      String[] newChannelArray = null;
      if (filter.getNewChannelList() != null) {
         newChannelArray = new String[filter.getNewChannelList().size()];

         for(int i = 0; i < filter.getNewChannelList().size(); ++i) {
            if (filter.getNewChannelList().get(i) != "") {
               newChannelArray[i] = (String)filter.getNewChannelList().get(i);
            }
         }
      }

      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      List channels = program.getChannelList();
      Iterator nextChannel = channels.iterator();

      while(true) {
         int i;
         int index;
         do {
            do {
               if (!nextChannel.hasNext()) {
                  if (ChannelArray != null && ChannelArray.length > 0) {
                     for(int i = 0; i < ChannelArray.length; ++i) {
                        String[] channelInfo = ChannelArray[i].split(":", 3);
                        if (channelInfo.length == 3) {
                           for(index = 0; index < channels.size(); ++index) {
                              if (!((ChannelEntity)channels.get(index)).getFlag() && Integer.valueOf(channelInfo[0]) == ((ChannelEntity)channels.get(index)).getChannel_no()) {
                                 if (Integer.valueOf(channelInfo[0]) == Integer.valueOf(channelInfo[1])) {
                                    ((ChannelEntity)program.getChannelList().get(index)).setChannel_name(channelInfo[2]);
                                 } else {
                                    ((ChannelEntity)channels.get(index)).setFlag(true);
                                    ((ChannelEntity)program.getChannelList().get(index)).setChannel_name(channelInfo[2]);
                                    ((ChannelEntity)program.getChannelList().get(index)).setChannel_no(Integer.valueOf(channelInfo[1]));
                                    if (((ChannelEntity)program.getChannelList().get(index)).getFrameList() != null && ((ChannelEntity)program.getChannelList().get(index)).getFrameList().size() > 0) {
                                       for(int frameIndex = 0; frameIndex < ((ChannelEntity)program.getChannelList().get(index)).getFrameList().size(); ++frameIndex) {
                                          ((FrameEntity)((ChannelEntity)program.getChannelList().get(index)).getFrameList().get(frameIndex)).setChannel_no(Integer.valueOf(channelInfo[1]));
                                          if (((FrameEntity)((ChannelEntity)program.getChannelList().get(index)).getFrameList().get(frameIndex)).getScheduleList() != null && ((FrameEntity)((ChannelEntity)program.getChannelList().get(index)).getFrameList().get(frameIndex)).getScheduleList().size() > 0) {
                                             for(int scheduleIndex = 0; scheduleIndex < ((FrameEntity)((ChannelEntity)program.getChannelList().get(index)).getFrameList().get(frameIndex)).getScheduleList().size(); ++scheduleIndex) {
                                                ((ContentsScheduleEntity)((FrameEntity)((ChannelEntity)program.getChannelList().get(index)).getFrameList().get(frameIndex)).getScheduleList().get(scheduleIndex)).setChannel_no(Integer.valueOf(channelInfo[1]));
                                             }
                                          }
                                       }
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }

                  Iterator var30 = channels.iterator();

                  while(var30.hasNext()) {
                     ChannelEntity channel = (ChannelEntity)var30.next();
                     channel.setFlag(false);
                  }

                  scheduleMgr.updateSchedule(program, CACHEDKEY);
                  ArrayList channelMap = new ArrayList();

                  for(i = 0; i < program.getChannelList().size(); ++i) {
                     ChannelEntity channelEntity = (ChannelEntity)program.getChannelList().get(i);
                     if (i == 0) {
                        resource.setDefaultChannelNo(channelEntity.getChannel_no());
                        resource.setDefaultChannelName(channelEntity.getChannel_name());
                        resource.setDefaultFrameId(((FrameEntity)channelEntity.getFrameList().get(0)).getFrame_id());
                     }

                     LinkedHashMap channel = new LinkedHashMap();
                     channel.put("channelNo", channelEntity.getChannel_no());
                     channel.put("channelName", channelEntity.getChannel_name());
                     channel.put("channelDes", channelEntity.getChannel_description());
                     channelMap.add(channel);
                  }

                  resource.setChannelMap(channelMap);
                  return resource;
               }

               ChannelEntity channel = (ChannelEntity)nextChannel.next();
               i = channel.getChannel_no();
            } while(removeChanneArray == null);
         } while(removeChanneArray.length <= 0);

         for(index = 0; index < removeChanneArray.length; ++index) {
            if (i == Integer.valueOf(removeChanneArray[index])) {
               nextChannel.remove();
            }
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public V2ScheduleLoadChannelResource scheduleLoadChannel(String programId, String sessionId, String elementId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      String CACHEDKEY = sessionId + programId;
      boolean bRegLicLfd = false;
      boolean bool_reg_lic_soc = false;
      boolean bool_reg_lic_android = false;
      boolean bool_reg_lic_sinage = false;
      boolean bool_reg_lic_lite = false;
      boolean SlmLicenseCheck = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (bool_reg_lic_soc || bRegLicLfd || bool_reg_lic_android || bool_reg_lic_sinage || bool_reg_lic_lite) {
         bRegLicLfd = true;
         SlmLicenseCheck = true;
      }

      MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("scheduleTab");
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      V2ScheduleLoadChannelResource result = new V2ScheduleLoadChannelResource();
      LinkedHashMap programMap = new LinkedHashMap();
      if (program == null) {
         this.logger.info("[MagicInfo_Schedule] program null : " + programId);
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"program"});
      } else {
         ArrayList channelList = new ArrayList();
         programMap.put("aspectRatio", program.getAspect_ratio());
         programMap.put("resolution", program.getResolution());
         programMap.put("programName", program.getProgram_name());
         LinkedHashMap channel;
         if (program.getChannelList() != null) {
            for(int i = 0; i < program.getChannelList().size(); ++i) {
               channel = new LinkedHashMap();
               ChannelEntity channelEntity = (ChannelEntity)program.getChannelList().get(i);
               channel.put("channelNo", channelEntity.getChannel_no());
               channel.put("channelName", channelEntity.getChannel_name());
               channel.put("channelDes", channelEntity.getChannel_description());
               channelList.add(channel);
               if (channelEntity.getFrameList() != null) {
                  ArrayList frameList = new ArrayList();

                  for(int j = 0; j < channelEntity.getFrameList().size(); ++j) {
                     LinkedHashMap frameMap = new LinkedHashMap();
                     FrameEntity frame = (FrameEntity)channelEntity.getFrameList().get(j);
                     frameMap.put("frameId", frame.getFrame_id());
                     frameMap.put("frameName", frame.getFrame_name());
                     frameMap.put("frameIndex", frame.getFrame_index());
                     frameMap.put("isMainFrame", frame.getIs_main_frame());
                     frameMap.put("x", frame.getX());
                     frameMap.put("y", frame.getY());
                     frameMap.put("width", frame.getWidth());
                     frameMap.put("height", frame.getHeight());
                     frameMap.put("lineData", frame.getLine_data());
                     if (frame.getAuthorityList() != null) {
                        frameMap.put("authority", frame.getAuthorityList().toString());
                     }

                     frameList.add(frameMap);
                  }

                  channel.put("frame", frameList);
               }
            }
         }

         ArrayList buttons = new ArrayList();
         channel = new LinkedHashMap();
         channel.put("name", "Save");
         channel.put("id", "scheduleSave");
         buttons.add(channel);
         LinkedHashMap cancelBtn;
         if (elementId.equals("editScheduleCreate") && program.getProgram_type() != null && !program.getProgram_type().equals("VWL") && !program.getProgram_type().equals("SYNC")) {
            cancelBtn = new LinkedHashMap();
            cancelBtn.put("name", "Save As");
            cancelBtn.put("id", "scheduleSaveAs");
            buttons.add(cancelBtn);
         }

         cancelBtn = new LinkedHashMap();
         cancelBtn.put("name", "Cancel");
         cancelBtn.put("id", "scheduleCancel");
         buttons.add(cancelBtn);
         result.setProgram(programMap);
         result.setMenu(buttons);
         result.setChannel(channelList);
         return result;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public List scheduleGetEvent(String programId, String sessionId, String channelNo, String frameId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      String CACHEDKEY = sessionId + programId;
      boolean bRegLicLfd = false;
      boolean bool_reg_lic_soc = false;
      boolean bool_reg_lic_android = false;
      boolean bool_reg_lic_sinage = false;
      boolean bool_reg_lic_lite = false;
      boolean SlmLicenseCheck = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (bool_reg_lic_soc || bRegLicLfd || bool_reg_lic_android || bool_reg_lic_sinage || bool_reg_lic_lite) {
         bRegLicLfd = true;
         SlmLicenseCheck = true;
      }

      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("scheduleTab");
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
      PlaylistDao playlistDao = new PlaylistDao();
      ArrayList events = new ArrayList();

      try {
         long maxIndex = 0L;
         if (program == null) {
            this.logger.info("[MagicInfo_Schedule] program null : " + programId);
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"program"});
         }

         String deviceType = program.getDevice_type();
         List channelList = program.getChannelList();
         if (channelList == null) {
            this.logger.info("[MagicInfo_Schedule] channelList null : " + programId);
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"channel list in the program"});
         }

         for(int i = 0; i < channelList.size(); ++i) {
            if (((ChannelEntity)channelList.get(i)).getChannel_no() == Integer.valueOf(channelNo)) {
               List frameList = ((ChannelEntity)channelList.get(i)).getFrameList();
               if (frameList == null) {
                  this.logger.info("[MagicInfo_Schedule] frameList null : " + programId);
                  throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"frame list in the program"});
               }

               List frameLists = new ArrayList();

               for(int j = 0; j < frameList.size(); ++j) {
                  new V2ContentScheduleFrameResource();
                  FrameEntity frame = (FrameEntity)frameList.get(j);
                  LinkedHashMap frameMap = new LinkedHashMap();
                  frameMap.put("frameId", frame.getFrame_id());
                  frameMap.put("frameName", frame.getFrame_name());
                  frameMap.put("frameIndex", frame.getFrame_index());
                  frameMap.put("isMainFrame", frame.getIs_main_frame());
                  frameMap.put("x", frame.getX());
                  frameMap.put("y", frame.getY());
                  frameMap.put("width", frame.getWidth());
                  frameMap.put("height", frame.getHeight());
                  frameMap.put("lineData", frame.getLine_data());
                  frameLists.add(frameMap);
                  if (((FrameEntity)frameList.get(j)).getFrame_id().equals(frameId)) {
                     List scheduleList = ((FrameEntity)frameList.get(j)).getScheduleList();
                     if (scheduleList != null) {
                        for(int k = 0; k < scheduleList.size(); ++k) {
                           V2ContentScheduleBlockResource event = new V2ContentScheduleBlockResource();
                           String contentType = ((ContentsScheduleEntity)scheduleList.get(k)).getContent_type();
                           event.setId(((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_id());
                           if (((ContentsScheduleEntity)scheduleList.get(k)).getPlaylist_type() != null && ((ContentsScheduleEntity)scheduleList.get(k)).getPlaylist_type().equals("5")) {
                              event.setPlaylistType("5");
                              if (((ContentsScheduleEntity)scheduleList.get(k)).getFile_name() != null && ((ContentsScheduleEntity)scheduleList.get(k)).getFile_id() != null) {
                                 event.setFileName(((ContentsScheduleEntity)scheduleList.get(k)).getFile_name());
                                 event.setFileId(((ContentsScheduleEntity)scheduleList.get(k)).getFile_id());
                              } else {
                                 event.setFileName("NOIMAGE_THUMBNAIL.PNG");
                                 event.setFileId("NOIMAGE_THUMBNAIL");
                              }
                           } else if (contentType.equals("PLAYLIST")) {
                              ContentFile thumbContent = playlistDao.getThumbFileInfo(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                              if (thumbContent != null) {
                                 event.setFileName(thumbContent.getFile_name());
                                 event.setFileId(thumbContent.getFile_id());
                              } else {
                                 Playlist playlistInfo = playlistDao.getPlaylistActiveVerInfo(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                                 if (playlistInfo != null) {
                                    List tmpList = pInfo.getContentListOfPlaylist(playlistInfo.getPlaylist_id(), playlistInfo.getVersion_id());
                                    if (tmpList != null && tmpList.size() > 0) {
                                       Content playlistContent = (Content)tmpList.get(0);
                                       if (playlistContent != null) {
                                          event.setFileName(playlistContent.getThumb_file_name());
                                          event.setFileId(playlistContent.getThumb_file_id());
                                       }
                                    }
                                 }
                              }
                           } else {
                              event.setFileName(((ContentsScheduleEntity)scheduleList.get(k)).getFile_name());
                              event.setFileId(((ContentsScheduleEntity)scheduleList.get(k)).getFile_id());
                           }

                           if (!((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_type().equals("00")) {
                              String inputSource = this.getInputSource(((ContentsScheduleEntity)scheduleList.get(k)).getHw_input_source());
                              event.setTitle(inputSource);
                              event.setContentId(inputSource);
                              event.setContentType("HW_IS");
                           } else {
                              event.setContentType(contentType);
                              if (contentType.equals("PLAYLIST")) {
                                 Playlist playlist = pInfo.getPlaylistActiveVerInfo(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                                 if (playlist != null) {
                                    event.setContentId(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                                    event.setTitle(playlist.getPlaylist_name());
                                 }
                              } else {
                                 event.setContentId(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                                 event.setTitle(((ContentsScheduleEntity)scheduleList.get(k)).getContent_name());
                              }
                           }

                           if (ScheduleUtility.isSupportInputSourceRepeat(program.getDevice_type(), program.getDevice_type_version()) && this.checkInputSource(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id())) {
                              event.setTitle(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                           }

                           event.setRepeatType(((ContentsScheduleEntity)scheduleList.get(k)).getRepeat_type());
                           SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                           Date date = sdf.parse(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                           Date endDate = new Date(date.getTime() + (long)(((ContentsScheduleEntity)scheduleList.get(k)).getDuration() * 1000));
                           DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                           event.setPlayerMode(((ContentsScheduleEntity)scheduleList.get(k)).getPlayer_mode());
                           List rangesMapList = new ArrayList();
                           V2ContentScheduleRangeResource rangesMap = new V2ContentScheduleRangeResource();
                           DateFormat endTime = new SimpleDateFormat("HH:mm:ss");
                           String var40 = ((ContentsScheduleEntity)scheduleList.get(k)).getRepeat_type();
                           byte var41 = -1;
                           switch(var40.hashCode()) {
                           case -1361669285:
                              if (var40.equals("day_of_month")) {
                                 var41 = 3;
                              }
                              break;
                           case -43636807:
                              if (var40.equals("day_of_week")) {
                                 var41 = 2;
                              }
                              break;
                           case 3415681:
                              if (var40.equals("once")) {
                                 var41 = 0;
                              }
                              break;
                           case 95346201:
                              if (var40.equals("daily")) {
                                 var41 = 1;
                              }
                           }

                           String color;
                           switch(var41) {
                           case 0:
                              event.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                              event.setEnd(df.format(endDate));
                              break;
                           case 1:
                              event.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                              event.setEnd(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + endTime.format(endDate));
                              event.setStopDate(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date());
                              rangesMap.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " 00:00:00");
                              rangesMap.setEnd(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date() + " 23:59:59");
                              rangesMapList.add(rangesMap);
                              event.setRanges(rangesMapList);
                              event.setDow("[0,1,2,3,4,5,6]");
                              break;
                           case 2:
                              color = ((ContentsScheduleEntity)scheduleList.get(k)).getWeekdays();
                              String[] weekdaysArray = color.split(",");
                              event.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                              event.setEnd(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + endTime.format(endDate));
                              event.setStopDate(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date());
                              rangesMap.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " 00:00:00");
                              rangesMap.setEnd(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date() + " 23:59:59");
                              rangesMapList.add(rangesMap);
                              StringBuffer dow = new StringBuffer();
                              dow.append("[");
                              int index = 0;

                              for(; index < weekdaysArray.length; ++index) {
                                 if (index > 0) {
                                    dow.append(",");
                                 }

                                 String var46 = weekdaysArray[index];
                                 byte var47 = -1;
                                 switch(var46.hashCode()) {
                                 case 101661:
                                    if (var46.equals("fri")) {
                                       var47 = 4;
                                    }
                                    break;
                                 case 108300:
                                    if (var46.equals("mon")) {
                                       var47 = 0;
                                    }
                                    break;
                                 case 113638:
                                    if (var46.equals("sat")) {
                                       var47 = 5;
                                    }
                                    break;
                                 case 114252:
                                    if (var46.equals("sun")) {
                                       var47 = 6;
                                    }
                                    break;
                                 case 114817:
                                    if (var46.equals("thu")) {
                                       var47 = 3;
                                    }
                                    break;
                                 case 115204:
                                    if (var46.equals("tue")) {
                                       var47 = 1;
                                    }
                                    break;
                                 case 117590:
                                    if (var46.equals("wed")) {
                                       var47 = 2;
                                    }
                                 }

                                 switch(var47) {
                                 case 0:
                                    dow.append("1");
                                    break;
                                 case 1:
                                    dow.append("2");
                                    break;
                                 case 2:
                                    dow.append("3");
                                    break;
                                 case 3:
                                    dow.append("4");
                                    break;
                                 case 4:
                                    dow.append("5");
                                    break;
                                 case 5:
                                    dow.append("6");
                                    break;
                                 case 6:
                                    dow.append("0");
                                 }
                              }

                              dow.append("]");
                              event.setRanges(rangesMapList);
                              event.setDow(dow.toString());
                              break;
                           case 3:
                              String monthdays = ((ContentsScheduleEntity)scheduleList.get(k)).getMonthdays();
                              event.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                              event.setEnd(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + endTime.format(endDate));
                              event.setStopDate(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date());
                              rangesMap.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " 00:00:00");
                              rangesMap.setEnd(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date() + " 23:59:59");
                              rangesMap.setMonth(monthdays);
                              rangesMapList.add(rangesMap);
                              event.setRanges(rangesMapList);
                              event.setDow("[0,1,2,3,4,5,6]");
                           }

                           long currentIndex = ((ContentsScheduleEntity)scheduleList.get(k)).getPriority();
                           event.setIndex(currentIndex);
                           if (maxIndex < currentIndex) {
                              maxIndex = currentIndex;
                           }

                           event.setScheduleId(((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_id());
                           event.setRepeatType(((ContentsScheduleEntity)scheduleList.get(k)).getRepeat_type());
                           color = this.getColor(k);
                           event.setColor(color);
                           event.setSize(((ContentsScheduleEntity)scheduleList.get(k)).getFile_size());
                           event.setAllDay(false);
                           event.setCifsSlideTime(((ContentsScheduleEntity)scheduleList.get(k)).getSlide_transition_time());
                           event.setSafetyLock(((ContentsScheduleEntity)scheduleList.get(k)).getSafetyLock());
                           events.add(event);
                        }
                     }
                  }
               }
            }
         }

         program.setCurrentIndex(maxIndex);
         scheduleMgr.updateSchedule(program, CACHEDKEY);
      } catch (Exception var48) {
         this.logger.error("", var48);
      }

      return events;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public List scheduleGetDetailEvent(String programId, String sessionId, String channelNo, String frameId, String scheduleId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      String CACHEDKEY = sessionId + programId;
      boolean bRegLicLfd = false;
      boolean bool_reg_lic_soc = false;
      boolean bool_reg_lic_android = false;
      boolean bool_reg_lic_sinage = false;
      boolean bool_reg_lic_lite = false;
      boolean SlmLicenseCheck = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (bool_reg_lic_soc || bRegLicLfd || bool_reg_lic_android || bool_reg_lic_sinage || bool_reg_lic_lite) {
         bRegLicLfd = true;
         SlmLicenseCheck = true;
      }

      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("scheduleTab");
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      List events = new ArrayList();
      PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
      PlaylistDao playlistDao = new PlaylistDao();

      try {
         long maxIndex = 0L;
         if (program == null) {
            this.logger.info("[MagicInfo_Schedule] program null : " + programId);
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"program"});
         }

         String deviceType = program.getDevice_type();
         List channelList = program.getChannelList();
         if (channelList == null) {
            this.logger.info("[MagicInfo_Schedule] channelList null : " + programId);
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"channel list in the program"});
         }

         for(int i = 0; i < channelList.size(); ++i) {
            if (((ChannelEntity)channelList.get(i)).getChannel_no() == Integer.valueOf(channelNo)) {
               List frameList = ((ChannelEntity)channelList.get(i)).getFrameList();
               if (frameList == null) {
                  this.logger.info("[MagicInfo_Schedule] frameList null : " + programId);
                  throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"frame list in the program"});
               }

               List frameLists = new ArrayList();

               for(int j = 0; j < frameList.size(); ++j) {
                  FrameEntity frame = (FrameEntity)frameList.get(j);
                  LinkedHashMap frameMap = new LinkedHashMap();
                  frameMap.put("frameId", frame.getFrame_id());
                  frameMap.put("frameName", frame.getFrame_name());
                  frameMap.put("frameIndex", frame.getFrame_index());
                  frameMap.put("isMainFrame", frame.getIs_main_frame());
                  frameMap.put("x", frame.getX());
                  frameMap.put("y", frame.getY());
                  frameMap.put("width", frame.getWidth());
                  frameMap.put("height", frame.getHeight());
                  frameMap.put("lineData", frame.getLine_data());
                  frameLists.add(frameMap);
                  if (((FrameEntity)frameList.get(j)).getFrame_id().equals(frameId)) {
                     List scheduleList = ((FrameEntity)frameList.get(j)).getScheduleList();
                     if (scheduleList != null) {
                        for(int k = 0; k < scheduleList.size(); ++k) {
                           if (((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_id().equals(scheduleId)) {
                              V2ContentScheduleBlockResource event = new V2ContentScheduleBlockResource();
                              String contentType = ((ContentsScheduleEntity)scheduleList.get(k)).getContent_type();
                              event.setId(((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_id());
                              if (((ContentsScheduleEntity)scheduleList.get(k)).getPlaylist_type() != null && ((ContentsScheduleEntity)scheduleList.get(k)).getPlaylist_type().equals("5")) {
                                 event.setPlaylistType("5");
                                 if (((ContentsScheduleEntity)scheduleList.get(k)).getFile_name() != null && ((ContentsScheduleEntity)scheduleList.get(k)).getFile_id() != null) {
                                    event.setFileName(((ContentsScheduleEntity)scheduleList.get(k)).getFile_name());
                                    event.setFileId(((ContentsScheduleEntity)scheduleList.get(k)).getFile_id());
                                 } else {
                                    event.setFileName("NOIMAGE_THUMBNAIL.PNG");
                                    event.setFileId("NOIMAGE_THUMBNAIL");
                                 }
                              } else if (contentType.equals("PLAYLIST")) {
                                 ContentFile thumbContent = playlistDao.getThumbFileInfo(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                                 if (thumbContent != null) {
                                    event.setFileName(thumbContent.getFile_name());
                                    event.setFileId(thumbContent.getFile_id());
                                 } else {
                                    Playlist playlistInfo = playlistDao.getPlaylistActiveVerInfo(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                                    if (playlistInfo != null) {
                                       List tmpList = pInfo.getContentListOfPlaylist(playlistInfo.getPlaylist_id(), playlistInfo.getVersion_id());
                                       if (tmpList != null && tmpList.size() > 0) {
                                          Content playlistContent = (Content)tmpList.get(0);
                                          if (playlistContent != null) {
                                             event.setFileName(playlistContent.getThumb_file_name());
                                             event.setFileId(playlistContent.getThumb_file_id());
                                          }
                                       }
                                    }
                                 }
                              } else {
                                 event.setFileName(((ContentsScheduleEntity)scheduleList.get(k)).getFile_name());
                                 event.setFileId(((ContentsScheduleEntity)scheduleList.get(k)).getFile_id());
                              }

                              if (!((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_type().equals("00")) {
                                 String inputSource = this.getInputSource(((ContentsScheduleEntity)scheduleList.get(k)).getHw_input_source());
                                 event.setTitle(inputSource);
                                 event.setContentId(inputSource);
                                 event.setContentType("HW_IS");
                              } else {
                                 event.setContentType(contentType);
                                 if (contentType.equals("PLAYLIST")) {
                                    Playlist playlist = pInfo.getPlaylistActiveVerInfo(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                                    if (playlist != null) {
                                       event.setContentId(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                                       event.setTitle(playlist.getPlaylist_name());
                                    }
                                 } else {
                                    event.setContentId(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                                    event.setTitle(((ContentsScheduleEntity)scheduleList.get(k)).getContent_name());
                                 }
                              }

                              if (ScheduleUtility.isSupportInputSourceRepeat(program.getDevice_type(), program.getDevice_type_version()) && this.checkInputSource(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id())) {
                                 event.setTitle(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                              }

                              event.setRepeatType(((ContentsScheduleEntity)scheduleList.get(k)).getRepeat_type());
                              SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                              Date date = sdf.parse(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                              Date endDate = new Date(date.getTime() + (long)(((ContentsScheduleEntity)scheduleList.get(k)).getDuration() * 1000));
                              DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                              event.setPlayerMode(((ContentsScheduleEntity)scheduleList.get(k)).getPlayer_mode());
                              List rangesMapList = new ArrayList();
                              V2ContentScheduleRangeResource rangesMap = new V2ContentScheduleRangeResource();
                              DateFormat endTime = new SimpleDateFormat("HH:mm:ss");
                              String var40 = ((ContentsScheduleEntity)scheduleList.get(k)).getRepeat_type();
                              byte var41 = -1;
                              switch(var40.hashCode()) {
                              case -1361669285:
                                 if (var40.equals("day_of_month")) {
                                    var41 = 3;
                                 }
                                 break;
                              case -43636807:
                                 if (var40.equals("day_of_week")) {
                                    var41 = 2;
                                 }
                                 break;
                              case 3415681:
                                 if (var40.equals("once")) {
                                    var41 = 0;
                                 }
                                 break;
                              case 95346201:
                                 if (var40.equals("daily")) {
                                    var41 = 1;
                                 }
                              }

                              String color;
                              switch(var41) {
                              case 0:
                                 event.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                                 event.setEnd(df.format(endDate));
                                 break;
                              case 1:
                                 event.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                                 event.setEnd(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + endTime.format(endDate));
                                 event.setStopDate(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date());
                                 rangesMap.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " 00:00:00");
                                 rangesMap.setEnd(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date() + " 23:59:59");
                                 rangesMapList.add(rangesMap);
                                 event.setRanges(rangesMapList);
                                 event.setDow("[0,1,2,3,4,5,6]");
                                 break;
                              case 2:
                                 color = ((ContentsScheduleEntity)scheduleList.get(k)).getWeekdays();
                                 String[] weekdaysArray = color.split(",");
                                 event.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                                 event.setEnd(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + endTime.format(endDate));
                                 event.setStopDate(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date());
                                 rangesMap.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " 00:00:00");
                                 rangesMap.setEnd(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date() + " 23:59:59");
                                 rangesMapList.add(rangesMap);
                                 StringBuffer dow = new StringBuffer();
                                 dow.append("[");

                                 for(int index = 0; index < weekdaysArray.length; ++index) {
                                    if (index > 0) {
                                       dow.append(",");
                                    }

                                    String var46 = weekdaysArray[index];
                                    byte var47 = -1;
                                    switch(var46.hashCode()) {
                                    case 101661:
                                       if (var46.equals("fri")) {
                                          var47 = 4;
                                       }
                                       break;
                                    case 108300:
                                       if (var46.equals("mon")) {
                                          var47 = 0;
                                       }
                                       break;
                                    case 113638:
                                       if (var46.equals("sat")) {
                                          var47 = 5;
                                       }
                                       break;
                                    case 114252:
                                       if (var46.equals("sun")) {
                                          var47 = 6;
                                       }
                                       break;
                                    case 114817:
                                       if (var46.equals("thu")) {
                                          var47 = 3;
                                       }
                                       break;
                                    case 115204:
                                       if (var46.equals("tue")) {
                                          var47 = 1;
                                       }
                                       break;
                                    case 117590:
                                       if (var46.equals("wed")) {
                                          var47 = 2;
                                       }
                                    }

                                    switch(var47) {
                                    case 0:
                                       dow.append("1");
                                       break;
                                    case 1:
                                       dow.append("2");
                                       break;
                                    case 2:
                                       dow.append("3");
                                       break;
                                    case 3:
                                       dow.append("4");
                                       break;
                                    case 4:
                                       dow.append("5");
                                       break;
                                    case 5:
                                       dow.append("6");
                                       break;
                                    case 6:
                                       dow.append("0");
                                    }
                                 }

                                 dow.append("]");
                                 event.setRanges(rangesMapList);
                                 event.setDow(dow.toString());
                                 break;
                              case 3:
                                 String monthdays = ((ContentsScheduleEntity)scheduleList.get(k)).getMonthdays();
                                 event.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                                 event.setEnd(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + endTime.format(endDate));
                                 event.setStopDate(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date());
                                 rangesMap.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " 00:00:00");
                                 rangesMap.setEnd(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date() + " 23:59:59");
                                 rangesMap.setMonth(monthdays);
                                 rangesMapList.add(rangesMap);
                                 event.setRanges(rangesMapList);
                                 event.setDow("[0,1,2,3,4,5,6]");
                              }

                              long currentIndex = ((ContentsScheduleEntity)scheduleList.get(k)).getPriority();
                              event.setIndex(currentIndex);
                              if (maxIndex < currentIndex) {
                                 maxIndex = currentIndex;
                              }

                              event.setScheduleId(((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_id());
                              event.setRepeatType(((ContentsScheduleEntity)scheduleList.get(k)).getRepeat_type());
                              color = this.getColor(k);
                              event.setColor(color);
                              event.setSize(((ContentsScheduleEntity)scheduleList.get(k)).getFile_size());
                              event.setAllDay(false);
                              event.setCifsSlideTime(((ContentsScheduleEntity)scheduleList.get(k)).getSlide_transition_time());
                              event.setSafetyLock(((ContentsScheduleEntity)scheduleList.get(k)).getSafetyLock());
                              events.add(event);
                           }
                        }
                     }
                  }
               }
            }
         }

         program.setCurrentIndex(maxIndex);
         scheduleMgr.updateSchedule(program, CACHEDKEY);
      } catch (Exception var48) {
         this.logger.error("", var48);
      }

      return events;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public V2ContentScheduleTemplateResource scheduleUpdateTemplate(String templateId, V2ContentScheduleTemplateResource resource) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("scheduleTab");
      new LinkedHashMap();
      ArrayList frameData = resource.getFrameData();
      String resolution = resource.getResolution();
      String templateType = resource.getTemplateType();
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      StringBuffer templateData = new StringBuffer();
      JSONArray jsonArray;
      int i;
      JSONObject obj;
      String index;
      String frameName;
      String x;
      String y;
      String width;
      if (resource.getTemplateType().equals("custom")) {
         jsonArray = new JSONArray(frameData);
         int n = jsonArray.length();
         String[] tempResolution = resolution.split("\\*");
         String resolutionW = tempResolution[0];
         String resolutionH = tempResolution[1];
         templateData.append("0_true_0_0_0_" + resolutionW + "_" + resolutionH + "_CustomLayout%%%Frame 0|");

         for(i = 0; i < n; ++i) {
            obj = jsonArray.getJSONObject(i);
            index = String.valueOf(obj.get("index"));
            frameName = "Frame";
            x = String.valueOf(obj.get("x"));
            y = String.valueOf(obj.get("y"));
            width = String.valueOf(obj.get("width"));
            String height = String.valueOf(obj.get("height"));
            templateData.append(index + "_false_" + index + "_" + x + "_" + y + "_" + width + "_" + height + "_CustomLayout%%%" + frameName + " " + index + "|");
         }
      } else {
         jsonArray = new JSONArray(frameData);
         JSONArray lines = jsonArray.getJSONArray(0);
         JSONArray frames = jsonArray.getJSONArray(1);
         int lineCount = lines.length();
         int frameCount = frames.length();

         for(i = 0; i < lineCount; ++i) {
            obj = lines.getJSONObject(i);
            index = String.valueOf(obj.get("sx"));
            frameName = String.valueOf(obj.get("sy"));
            x = String.valueOf(obj.get("ex"));
            y = String.valueOf(obj.get("ey"));
            width = (String)obj.get("type");
            templateData.append(index + "," + frameName + "," + x + "," + y + "," + width);
            if (i + 1 < lineCount) {
               templateData.append("~");
            }
         }

         templateData.append("*$*");

         for(i = 0; i < frameCount; ++i) {
            templateData.append("Frame" + (i + 1));
            if (i == 0) {
               templateData.append(",true");
            } else {
               templateData.append(",false");
            }

            if (i + 1 < frameCount) {
               templateData.append("*@*");
            }
         }
      }

      FrameTemplateEntity template = scheduleInfo.getTemplateEntity((long)Integer.valueOf(templateId));
      if (template != null) {
         template.setTemplate_data(templateData.toString());
         template.setCreate_user_id(userContainer.getUser().getUser_id());
         template.setOrganization(userContainer.getUser().getOrganization());
         scheduleInfo.updateTemplate(template);
      }

      V2ContentScheduleTemplateResource newResource = new V2ContentScheduleTemplateResource();
      newResource.setTemplateId(templateId);
      newResource.setFrameData(frameData);
      newResource.setResolution(resolution);
      newResource.setTemplateType(templateType);
      return newResource;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public V2ScheduleUpdateFrameResource scheduleUpdateFrame(String programId, String channelNo, String frameIds, ContentScheduleFrameResource resource, HttpServletRequest request) throws ConfigException, Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      String sessionId = request.getSession().getId();
      String CACHEDKEY = sessionId + programId;
      V2ScheduleUpdateFrameResource resultResource = new V2ScheduleUpdateFrameResource();
      boolean bRegLicLfd = false;
      boolean bool_reg_lic_soc = false;
      boolean bool_reg_lic_android = false;
      boolean bool_reg_lic_sinage = false;
      boolean bool_reg_lic_lite = false;
      boolean SlmLicenseCheck = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (bool_reg_lic_soc || bRegLicLfd || bool_reg_lic_android || bool_reg_lic_sinage || bool_reg_lic_lite) {
         bRegLicLfd = true;
         SlmLicenseCheck = true;
      }

      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("scheduleTab");
      new LinkedHashMap();
      channelNo = StrUtils.nvl(channelNo == null ? "1" : channelNo);
      String resolution = StrUtils.nvl(resource.getResolution());
      String frameType = StrUtils.nvl(resource.getFrameType());
      String lineData = StrUtils.nvl(resource.getLineData());
      frameIds = StrUtils.nvl(frameIds);
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      int channel = Integer.valueOf(channelNo);
      program.setResolution(resolution);
      List newFrameList = new ArrayList();
      String[] frameIdList = frameIds.split(",");
      String[] frameNameList = null;
      if (resource.getFrameNames() != null && resource.getFrameNames().size() > 0) {
         frameNameList = new String[resource.getFrameNames().size()];

         for(int i = 0; i < resource.getFrameNames().size(); ++i) {
            frameNameList[i] = (String)resource.getFrameNames().get(i);
         }
      }

      String[] defaultContentIdList = null;
      if (resource.getDefaultContentIds() != null && resource.getDefaultContentIds().size() > 0) {
         defaultContentIdList = new String[resource.getDefaultContentIds().size()];

         for(int i = 0; i < resource.getDefaultContentIds().size(); ++i) {
            defaultContentIdList[i] = (String)resource.getDefaultContentIds().get(i);
         }
      }

      String[] defaultContentNameList = null;
      if (resource.getDefaultContentNames() != null && resource.getDefaultContentNames().size() > 0) {
         defaultContentNameList = new String[resource.getDefaultContentNames().size()];

         for(int i = 0; i < resource.getDefaultContentNames().size(); ++i) {
            defaultContentNameList[i] = (String)resource.getDefaultContentNames().get(i);
         }
      }

      String[] frameIsMainList = null;
      if (resource.getFrameIsMains() != null && resource.getFrameIsMains().size() > 0) {
         frameIsMainList = new String[resource.getFrameIsMains().size()];

         for(int i = 0; i < resource.getFrameIsMains().size(); ++i) {
            frameIsMainList[i] = (String)resource.getFrameIsMains().get(i);
         }
      }

      String[] frameAuthorityList = null;
      if (resource.getFrameAuthorityIds() != null && resource.getFrameAuthorityIds().size() > 0) {
         frameAuthorityList = new String[resource.getFrameAuthorityIds().size()];

         for(int i = 0; i < resource.getFrameAuthorityIds().size(); ++i) {
            frameAuthorityList[i] = (String)resource.getFrameAuthorityIds().get(i);
         }
      }

      String[] frameAuthorityNameList = null;
      if (resource.getFrameAuthorityNames() != null && resource.getFrameAuthorityNames().size() > 0) {
         frameAuthorityNameList = new String[resource.getFrameAuthorityNames().size()];

         for(int i = 0; i < resource.getFrameAuthorityNames().size(); ++i) {
            frameAuthorityNameList[i] = (String)resource.getFrameAuthorityNames().get(i);
         }
      }

      String[] frameSize = null;
      if (resource.getData() != null && resource.getData().size() > 0) {
         frameSize = new String[resource.getData().size()];

         for(int i = 0; i < resource.getData().size(); ++i) {
            frameSize[i] = (String)resource.getData().get(i);
         }
      }

      String allDefaultContentId = null;
      String allDefaultContentName = null;
      if (defaultContentIdList != null) {
         if (defaultContentIdList.length > 0 && defaultContentIdList[0] != null) {
            allDefaultContentId = defaultContentIdList[0];
         } else {
            allDefaultContentId = "";
         }
      }

      if (defaultContentNameList != null) {
         if (defaultContentNameList.length > 0 && defaultContentNameList[0] != null) {
            allDefaultContentName = defaultContentNameList[0];
         } else {
            allDefaultContentName = "";
         }
      }

      String allFrameAuthorityId = null;
      if (frameAuthorityList != null) {
         if (frameAuthorityList.length > 0 && frameAuthorityList[0] != null) {
            allFrameAuthorityId = frameAuthorityList[0];
         } else {
            allFrameAuthorityId = "";
         }
      }

      String allFrameAuthorityName = null;
      if (frameAuthorityNameList != null) {
         if (frameAuthorityNameList.length > 0 && frameAuthorityNameList[0] != null) {
            allFrameAuthorityName = frameAuthorityNameList[0];
         } else {
            allFrameAuthorityName = "";
         }
      }

      String defaultFrameId = null;

      for(int i = 0; i < program.getChannelList().size(); ++i) {
         if (((ChannelEntity)program.getChannelList().get(i)).getChannel_no() == channel && ((ChannelEntity)program.getChannelList().get(i)).getFrameList() != null) {
            List frameList = ((ChannelEntity)program.getChannelList().get(i)).getFrameList();
            FrameEntity defaultFrame = (FrameEntity)frameList.get(0);
            if (frameIdList.length > 0) {
               int frameIndex = 0;
               String[] var41 = frameIdList;
               int var42 = frameIdList.length;

               for(int var43 = 0; var43 < var42; ++var43) {
                  String var10000 = var41[var43];
                  String frameId = UUID.randomUUID().toString();
                  FrameEntity frame = null;
                  String[] authorityNames;
                  if (frameIndex > 0) {
                     frame = new FrameEntity();
                     authorityNames = null;
                     if (frameSize[frameIndex - 1] != null) {
                        authorityNames = frameSize[frameIndex - 1].split(":", 4);
                     }

                     if (authorityNames != null && authorityNames.length == 4) {
                        frame = this.createNewFrame(new FrameEntity(), programId, frameId, channel, frameIsMainList[frameIndex], frameIndex, 1, Float.valueOf(authorityNames[0]), Float.valueOf(authorityNames[1]), Float.valueOf(authorityNames[2]), Float.valueOf(authorityNames[3]), "");
                     }
                  } else {
                     frame = defaultFrame;
                     defaultFrame.setFrame_id(frameId);
                     if (frameType != null && !frameType.equals("") && frameType.equals("custom")) {
                        authorityNames = resolution.split("\\*");
                        defaultFrame.setWidth((double)Long.valueOf(authorityNames[0]));
                        defaultFrame.setHeight((double)Long.valueOf(authorityNames[1]));
                        if (frameIdList.length == 1) {
                           if (defaultContentIdList[frameIndex] != null && !defaultContentIdList[frameIndex].equals("null")) {
                              defaultFrame.setDefault_content_id(defaultContentIdList[frameIndex]);
                           }

                           if (defaultContentNameList[frameIndex] != null && !defaultContentNameList[frameIndex].equals("null")) {
                              defaultFrame.setDefault_content_names(defaultContentNameList[frameIndex]);
                           }

                           String[] frameInfo;
                           if (allFrameAuthorityId != null && !allFrameAuthorityId.equals("")) {
                              frameInfo = allFrameAuthorityId.split(":");
                              defaultFrame.setAuthority(frameInfo);
                           } else {
                              defaultFrame.setAuthority((String[])null);
                           }

                           if (allFrameAuthorityName != null && !allFrameAuthorityName.equals("")) {
                              frameInfo = allFrameAuthorityName.split(":");
                              defaultFrame.setAuthorityNames(frameInfo);
                           } else {
                              defaultFrame.setAuthorityNames((String[])null);
                           }

                           newFrameList.add(defaultFrame);
                           frameId = UUID.randomUUID().toString();
                           frameInfo = frameSize[frameIndex].split(":", 4);
                           frame = this.createNewFrame(new FrameEntity(), programId, frameId, channel, frameIsMainList[frameIndex], 1, 1, Float.valueOf(frameInfo[0]), Float.valueOf(frameInfo[1]), Float.valueOf(frameInfo[2]), Float.valueOf(frameInfo[3]), "");
                        }
                     } else {
                        defaultFrame.setWidth(100.0D);
                        defaultFrame.setHeight(100.0D);
                     }
                  }

                  if (lineData != null && lineData.equals("custom")) {
                     frame.setLine_data("CustomLayout");
                  } else {
                     frame.setLine_data(lineData);
                  }

                  frame.setFlag(true);
                  frame.setFrame_name(frameNameList[frameIndex]);
                  if (frame.getFrame_index() == 0) {
                     defaultFrameId = frame.getFrame_id();
                  }

                  frame.setFrame_name(frameNameList[frameIndex]);
                  frame.setDefault_content_id(allDefaultContentId);
                  frame.setDefault_content_names(allDefaultContentName);
                  if (defaultContentIdList[frameIndex] != null && !defaultContentIdList[frameIndex].equals("null")) {
                     frame.setDefault_content_id(defaultContentIdList[frameIndex]);
                  }

                  if (defaultContentNameList[frameIndex] != null && !defaultContentNameList[frameIndex].equals("null")) {
                     frame.setDefault_content_names(defaultContentNameList[frameIndex]);
                  }

                  if (allFrameAuthorityId != null && !allFrameAuthorityId.equals("")) {
                     authorityNames = allFrameAuthorityId.split(":");
                     frame.setAuthority(authorityNames);
                  } else {
                     frame.setAuthority((String[])null);
                  }

                  if (allFrameAuthorityName != null && !allFrameAuthorityName.equals("")) {
                     authorityNames = allFrameAuthorityName.split(":");
                     frame.setAuthorityNames(authorityNames);
                  } else {
                     frame.setAuthorityNames((String[])null);
                  }

                  if (frameAuthorityList[frameIndex] != null && !frameAuthorityList[frameIndex].equals("null")) {
                     authorityNames = frameAuthorityList[frameIndex].split(":");
                     frame.setAuthority(authorityNames);
                  }

                  if (frameAuthorityNameList[frameIndex] != null && !frameAuthorityNameList[frameIndex].equals("null")) {
                     authorityNames = frameAuthorityNameList[frameIndex].split(":");
                     frame.setAuthorityNames(authorityNames);
                  }

                  newFrameList.add(frame);
                  ++frameIndex;
               }
            }

            ((ChannelEntity)program.getChannelList().get(i)).setFrameList(newFrameList);
         }
      }

      scheduleMgr.updateSchedule(program, CACHEDKEY);
      resultResource.setProgram(program);
      resultResource.setDefaultFrameId(defaultFrameId);
      return resultResource;
   }

   private V2ContentScheduleDetailResource getContentScheduleDetails(String programId, LinkedHashMap data) throws SQLException {
      Locale locale = SecurityUtils.getLocale();
      MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
      ScheduleInterface info = DAOFactory.getScheduleInfoImpl("PREMIUM");
      CommonProgramEntity programEntity = info.getProgram(programId);
      V2ContentScheduleDetailResource resource = new V2ContentScheduleDetailResource();
      resource.setProgramId((String)data.get("programId"));
      resource.setVersion((Long)data.get("version"));
      resource.setProgramName((String)data.get("programName"));
      resource.setProgramType((String)data.get("programType"));
      resource.setDeployTime(data.containsKey("deployTime") ? (String)data.get("deployTime") : "");
      resource.setReservationEndDate(data.containsKey("reservationEndDate") ? (String)data.get("reservationEndDate") : "");
      resource.setReservationMonthly(data.containsKey("reservationMonthly") ? (String)data.get("reservationMonthly") : "");
      resource.setReservationRepeatType(data.containsKey("reservationRepeatType") ? (String)data.get("reservationRepeatType") : "");
      resource.setReservationStartDate(data.containsKey("reservationStartDate") ? (String)data.get("reservationStartDate") : "");
      resource.setReservationWeekly(data.containsKey("reservationWeekly") ? (String)data.get("reservationWeekly") : "");
      resource.setIsVwl(data.containsKey("isVwl") ? (String)data.get("isVwl") : "false");
      resource.setSyncPlay(data.containsKey("syncPlay") ? (String)data.get("syncPlay") : "false");
      resource.setIsAdschedule(data.containsKey("isAdschedule") ? (String)data.get("isAdschedule") : "false");
      resource.setSynchronization(data.containsKey("contentSyncOn") ? (String)data.get("contentSyncOn") : "");
      resource.setResume(data.containsKey("resume") ? (String)data.get("resume") : "");
      resource.setDeviceType((String)data.get("deviceType"));
      resource.setDeviceTypeVersion((Float)data.get("deviceTypeVersion"));
      resource.setDescription(programEntity.getDescription());
      resource.setProgramType((String)data.get("programType"));
      resource.setBgmContentName(data.containsKey("bgmName") ? (String)data.get("bgmName") : "");
      resource.setBgmContentId(data.containsKey("bgm") ? (String)data.get("bgm") : "");
      resource.setIsBgmWithContent(data.containsKey("scheduleMuteContent") ? (String)data.get("scheduleMuteContent") : "");
      resource.setProgramGroupId((Long)data.get("programGroupId"));
      resource.setProgramGroupName(data.containsKey("programGroupName") ? (String)data.get("programGroupName") : "");
      resource.setModifyDate((Timestamp)data.get("modifyDate"));
      if (data.containsKey("deviceGroupId")) {
         String strDeviceGroupIds = StrUtils.nvl((String)data.get("deviceGroupId"));
         String strDeviceGroupNames = StrUtils.nvl((String)data.get("deviceGroupName"));
         String[] arrDeviceGroupIds = strDeviceGroupIds.split(",");
         String[] arrDeviceGroupNames = strDeviceGroupNames.split(",");
         int deviceGroupCount = arrDeviceGroupIds.length;
         List deviceGroupResources = new ArrayList();

         for(int i = 0; i < deviceGroupCount; ++i) {
            V2ScheduleDeviceGroup deviceGroupResource = new V2ScheduleDeviceGroup();
            deviceGroupResource.setGroupId(arrDeviceGroupIds[i]);
            deviceGroupResource.setGroupName(arrDeviceGroupNames[i]);
            deviceGroupResources.add(deviceGroupResource);
         }

         resource.setDeviceGroups(deviceGroupResources);
      }

      if (programEntity.getUse_sync_play() != null && programEntity.getUse_sync_play().equals("Y")) {
         resource.setScheduleType(messageMgr.getMessageSource("MIS_SID_SYNC_PLAY", locale));
      } else if (programEntity.getUse_ad_schedule() != null && programEntity.getUse_ad_schedule().equals("Y")) {
         resource.setScheduleType(messageMgr.getMessageSource("COM_DID_LFD_ADVERTISEMENT", locale));
      } else {
         resource.setScheduleType(messageMgr.getMessageSource("COM_TEXT_GENERAL_P", locale));
      }

      return resource;
   }

   private List getChannelsAndFrames(ProgramEntity program) throws SQLException {
      List channelList = program.getChannelList();
      List channels = new ArrayList();
      V2ContentScheduleChannelResource channel;
      if (channelList != null && channelList.size() > 0) {
         for(Iterator var4 = channelList.iterator(); var4.hasNext(); channels.add(channel)) {
            ChannelEntity channelEntity = (ChannelEntity)var4.next();
            channel = new V2ContentScheduleChannelResource();
            int channelNo = channelEntity.getChannel_no();
            channel.setChannelNo(channelNo);
            channel.setChannelName(channelEntity.getChannel_name());
            List frameList = channelEntity.getFrameList();
            if (frameList != null) {
               String mainFrameId = "";
               List frames = new ArrayList();

               for(int j = 0; j < frameList.size(); ++j) {
                  FrameEntity entity = (FrameEntity)frameList.get(j);
                  V2ContentScheduleFrameResource frame = new V2ContentScheduleFrameResource();
                  frame.setFrameId(entity.getFrame_id());
                  frame.setFrameName(entity.getFrame_name());
                  frame.setFrameIndex(entity.getFrame_index());
                  frame.setIsMainFrame(entity.getIs_main_frame().equals("Y"));
                  frame.setX(entity.getX());
                  frame.setY(entity.getY());
                  frame.setWidth(entity.getWidth());
                  frame.setHeight(entity.getHeight());
                  frame.setLineData(entity.getLine_data());
                  if ("Y".equalsIgnoreCase(entity.getIs_main_frame())) {
                     mainFrameId = entity.getFrame_id();
                  }

                  long maxIndex = 0L;
                  String programId = program.getProgram_id();
                  PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
                  ScheduleInterface info = DAOFactory.getScheduleInfoImpl("PREMIUM");
                  CommonProgramEntity programEntity = info.getProgram(programId);
                  List scheduleList = entity.getScheduleList();
                  List scheduleBlocks = new ArrayList();
                  if (scheduleList != null) {
                     for(int k = 0; k < scheduleList.size(); ++k) {
                        V2ContentScheduleBlockResource scheduleBlock = new V2ContentScheduleBlockResource();
                        String contentType = ((ContentsScheduleEntity)scheduleList.get(k)).getContent_type();
                        scheduleBlock.setScheduleId(((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_id());
                        scheduleBlock.setFileName(((ContentsScheduleEntity)scheduleList.get(k)).getFile_name());
                        scheduleBlock.setFileId(((ContentsScheduleEntity)scheduleList.get(k)).getFile_id());
                        scheduleBlock.setContentName(((ContentsScheduleEntity)scheduleList.get(k)).getContent_name());
                        String thumbnailPath;
                        if (((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_type() != null && !((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_type().equals("00")) {
                           thumbnailPath = this.getInputSource(((ContentsScheduleEntity)scheduleList.get(k)).getHw_input_source());
                           scheduleBlock.setTitle(thumbnailPath);
                           scheduleBlock.setContentId(thumbnailPath);
                           scheduleBlock.setContentType("HW_IS");
                        } else {
                           scheduleBlock.setContentType(contentType);
                           if (contentType.equals("PLAYLIST")) {
                              Playlist playlist = pInfo.getPlaylistActiveVerInfo(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                              scheduleBlock.setTitle(playlist.getPlaylist_name());
                              scheduleBlock.setContentId(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                           } else {
                              scheduleBlock.setTitle(((ContentsScheduleEntity)scheduleList.get(k)).getContent_name());
                              scheduleBlock.setContentId(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                           }
                        }

                        if ("iPLAYER".equals(programEntity.getDevice_type()) && this.checkInputSource(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id())) {
                           scheduleBlock.setTitle(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                        }

                        scheduleBlock.setSafetyLock(((ContentsScheduleEntity)scheduleList.get(k)).getSafetyLock());
                        thumbnailPath = "";
                        String tempThumbFileId = "";
                        String tempThumbFileName = "";
                        String contentId;
                        if (!"PLAYLIST".equalsIgnoreCase(contentType)) {
                           if (!"HW_IS".equalsIgnoreCase(contentType) && (((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_type() == null || ((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_type().equals("00"))) {
                              ContentInfo contentDao = ContentInfoImpl.getInstance();
                              Content contentInfo = contentDao.getThumbInfoOfActiveVersion(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                              tempThumbFileId = contentInfo.getThumb_file_id();
                              tempThumbFileName = contentInfo.getThumb_file_name();
                              thumbnailPath = "/servlet/ContentThumbnail?thumb_id=" + tempThumbFileId + "&thumb_filename=" + tempThumbFileName;
                           }
                        } else {
                           PlaylistDao playlistDao = new PlaylistDao();
                           contentId = ((ContentsScheduleEntity)scheduleList.get(k)).getContent_id();
                           ContentFile thumbContent = playlistDao.getThumbFileInfo(contentId);
                           if (thumbContent != null && thumbContent.getFile_id() != null && thumbContent.getFile_name() != null) {
                              tempThumbFileId = thumbContent.getFile_id();
                              tempThumbFileName = thumbContent.getFile_name();
                           } else {
                              Playlist playlistInfo = playlistDao.getPlaylistActiveVerInfo(contentId);
                              List tmpList = pInfo.getContentListOfPlaylist(playlistInfo.getPlaylist_id(), playlistInfo.getVersion_id());
                              if (tmpList != null && tmpList.size() > 0) {
                                 Content playlistContent = (Content)tmpList.get(0);
                                 if (playlistContent != null) {
                                    tempThumbFileName = playlistContent.getThumb_file_name();
                                    tempThumbFileId = playlistContent.getThumb_file_id();
                                 }
                              }
                           }

                           thumbnailPath = "/servlet/ContentThumbnail?thumb_id=" + tempThumbFileId + "&thumb_filename=" + tempThumbFileName;
                        }

                        scheduleBlock.setThumbnailPath(thumbnailPath);
                        scheduleBlock.setRepeatType(((ContentsScheduleEntity)scheduleList.get(k)).getRepeat_type());
                        scheduleBlock.setWeekDays(((ContentsScheduleEntity)scheduleList.get(k)).getWeekdays());
                        scheduleBlock.setMonthDays(((ContentsScheduleEntity)scheduleList.get(k)).getMonthdays());
                        scheduleBlock.setPlayerMode(((ContentsScheduleEntity)scheduleList.get(k)).getPlayer_mode());
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        contentId = null;
                        Date endDate = null;

                        try {
                           Date date = sdf.parse(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                           endDate = new Date(date.getTime() + (long)(((ContentsScheduleEntity)scheduleList.get(k)).getDuration() * 1000));
                        } catch (ParseException var43) {
                           this.logger.error("", var43);
                        }

                        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        List ranges = new ArrayList();
                        V2ContentScheduleRangeResource range = new V2ContentScheduleRangeResource();
                        DateFormat endTime = new SimpleDateFormat("HH:mm:ss");
                        String var35 = ((ContentsScheduleEntity)scheduleList.get(k)).getRepeat_type();
                        byte var36 = -1;
                        switch(var35.hashCode()) {
                        case -1361669285:
                           if (var35.equals("day_of_month")) {
                              var36 = 3;
                           }
                           break;
                        case -43636807:
                           if (var35.equals("day_of_week")) {
                              var36 = 2;
                           }
                           break;
                        case 3415681:
                           if (var35.equals("once")) {
                              var36 = 0;
                           }
                           break;
                        case 95346201:
                           if (var35.equals("daily")) {
                              var36 = 1;
                           }
                        }

                        String color;
                        switch(var36) {
                        case 0:
                           scheduleBlock.setStartDate(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                           scheduleBlock.setEndDate(df.format(endDate));
                           break;
                        case 1:
                           scheduleBlock.setStartDate(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                           scheduleBlock.setEndDate(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + endTime.format(endDate));
                           scheduleBlock.setStopDate(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date());
                           range.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " 00:00:00");
                           range.setEnd(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date() + " 23:59:59");
                           ranges.add(range);
                           scheduleBlock.setRanges(ranges);
                           scheduleBlock.setDayOfWeek("[0,1,2,3,4,5,6]");
                           break;
                        case 2:
                           color = ((ContentsScheduleEntity)scheduleList.get(k)).getWeekdays();
                           String[] weekdaysArray = color.split(",");
                           scheduleBlock.setStartDate(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                           scheduleBlock.setEndDate(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + endTime.format(endDate));
                           scheduleBlock.setStopDate(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date());
                           range.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " 00:00:00");
                           range.setEnd(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date() + " 23:59:59");
                           ranges.add(range);
                           StringBuffer dow = new StringBuffer();
                           dow.append("[");
                           int index = 0;

                           for(; index < weekdaysArray.length; ++index) {
                              if (index > 0) {
                                 dow.append(",");
                              }

                              String var41 = weekdaysArray[index];
                              byte var42 = -1;
                              switch(var41.hashCode()) {
                              case 101661:
                                 if (var41.equals("fri")) {
                                    var42 = 4;
                                 }
                                 break;
                              case 108300:
                                 if (var41.equals("mon")) {
                                    var42 = 0;
                                 }
                                 break;
                              case 113638:
                                 if (var41.equals("sat")) {
                                    var42 = 5;
                                 }
                                 break;
                              case 114252:
                                 if (var41.equals("sun")) {
                                    var42 = 6;
                                 }
                                 break;
                              case 114817:
                                 if (var41.equals("thu")) {
                                    var42 = 3;
                                 }
                                 break;
                              case 115204:
                                 if (var41.equals("tue")) {
                                    var42 = 1;
                                 }
                                 break;
                              case 117590:
                                 if (var41.equals("wed")) {
                                    var42 = 2;
                                 }
                              }

                              switch(var42) {
                              case 0:
                                 dow.append("1");
                                 break;
                              case 1:
                                 dow.append("2");
                                 break;
                              case 2:
                                 dow.append("3");
                                 break;
                              case 3:
                                 dow.append("4");
                                 break;
                              case 4:
                                 dow.append("5");
                                 break;
                              case 5:
                                 dow.append("6");
                                 break;
                              case 6:
                                 dow.append("0");
                              }
                           }

                           dow.append("]");
                           scheduleBlock.setRanges(ranges);
                           scheduleBlock.setDayOfWeek(dow.toString());
                           break;
                        case 3:
                           String monthdays = ((ContentsScheduleEntity)scheduleList.get(k)).getMonthdays();
                           scheduleBlock.setStartDate(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                           scheduleBlock.setEndDate(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + endTime.format(endDate));
                           scheduleBlock.setStopDate(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date());
                           range.setStart(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " 00:00:00");
                           range.setEnd(((ContentsScheduleEntity)scheduleList.get(k)).getStop_date() + " 23:59:59");
                           range.setMonth(monthdays);
                           ranges.add(range);
                           scheduleBlock.setRanges(ranges);
                           scheduleBlock.setDayOfWeek("[0,1,2,3,4,5,6]");
                        }

                        long currentIndex = ((ContentsScheduleEntity)scheduleList.get(k)).getPriority();
                        scheduleBlock.setIndex(currentIndex);
                        if (maxIndex < currentIndex) {
                           maxIndex = currentIndex;
                        }

                        color = this.getColor(k);
                        scheduleBlock.setColor(color);
                        scheduleBlock.setFileSize(((ContentsScheduleEntity)scheduleList.get(k)).getFile_size());
                        scheduleBlock.setAllDay(false);
                        scheduleBlock.setSlideTransitionTime(((ContentsScheduleEntity)scheduleList.get(k)).getSlide_transition_time());
                        scheduleBlocks.add(scheduleBlock);
                     }
                  }

                  frame.setMaxIndex(maxIndex);
                  frame.setScheduleBlocks(scheduleBlocks);
                  frames.add(frame);
               }

               channel.setMainFrameId(mainFrameId);
               channel.setFrames(frames);
            }
         }
      }

      return channels;
   }

   private List getDownloadStatus(String programId) throws SQLException {
      ScheduleInterface info = DAOFactory.getScheduleInfoImpl("PREMIUM");
      Map deviceGroupMap = info.getDeviceGroupIdsAndName(programId);
      String deviceGroupIds = (String)deviceGroupMap.get("device_group_ids");
      String[] groupIdList = deviceGroupIds.split(",");
      List downloadStatusList = new ArrayList();
      String[] var7 = groupIdList;
      int var8 = groupIdList.length;

      for(int var9 = 0; var9 < var8; ++var9) {
         String groupId = var7[var9];
         if (groupId != null && groupId.length() > 0) {
            DownloadStatusInfo downloadInfo = DownloadStatusInfoImpl.getInstacne();
            Object[] rtn = downloadInfo.getDownloadStatusListByProgramId(programId);
            if (rtn != null && rtn.length > 1) {
               Map resultMap = (Map)rtn[1];
               if (resultMap != null) {
                  V2ContentScheduleDownloadStatusResource downloadStatus = new V2ContentScheduleDownloadStatusResource();
                  float total = 0.0F;
                  float complete = 0.0F;
                  if (resultMap.get("deviceCount") != null) {
                     total = (float)(Integer)resultMap.get("deviceCount");
                     downloadStatus.setDeviceUseCount((Integer)resultMap.get("deviceCount"));
                  }

                  if (resultMap.get("completeCount") != null) {
                     complete = (float)(Integer)resultMap.get("completeCount");
                  }

                  downloadStatus.setProgramStatus((int)complete + "/" + (int)total);
                  if (total > 0.0F && complete > 0.0F) {
                     downloadStatus.setCompletePercentage(complete / total * 100.0F);
                  } else {
                     downloadStatus.setCompletePercentage(0.0F);
                  }

                  if (resultMap.get("deviceList") != null) {
                     List deviceList = (List)resultMap.get("deviceList");
                     if (deviceList != null && deviceList.size() > 0) {
                        List rtnDeviceList = downloadInfo.getProgressInfoByDeviceId(programId, deviceList);
                        if (rtnDeviceList != null && rtnDeviceList.size() > 0) {
                           List notConnectedDeviceIds = new ArrayList();

                           for(int i = 0; i < rtnDeviceList.size(); ++i) {
                              Map map = (Map)rtnDeviceList.get(i);
                              if (map.get("complete") != null) {
                                 int tempComplete = -1;
                                 if (map.get("complete").getClass().equals(Integer.TYPE)) {
                                    tempComplete = (Integer)map.get("complete");
                                 } else if (map.get("complete").getClass().equals(Long.TYPE)) {
                                    tempComplete = CommonUtils.safeLongToInt((Long)map.get("complete"));
                                 }

                                 if (tempComplete == 0 && !DeviceUtils.isConnected((String)map.get("device_id"))) {
                                    notConnectedDeviceIds.add((String)map.get("device_id"));
                                 }
                              }
                           }

                           downloadStatus.setNotConnectedDeviceIds(notConnectedDeviceIds);
                        }

                        downloadStatus.setDeviceIds(deviceList);
                     }
                  }

                  downloadStatusList.add(downloadStatus);
               }
            }
         }
      }

      return downloadStatusList;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public V2ContentScheduleItemResource scheduleUpdate(String programId, String channelNo, String frameId, String scheduleId, V2ContentScheduleItemResource resource, HttpServletRequest request) throws ConfigException, Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      String sessionId = request.getSession().getId();
      String CACHEDKEY = sessionId + programId;
      boolean bRegLicLfd = false;
      boolean bool_reg_lic_soc = false;
      boolean bool_reg_lic_android = false;
      boolean bool_reg_lic_sinage = false;
      boolean bool_reg_lic_lite = false;
      boolean SlmLicenseCheck = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (bool_reg_lic_soc || bRegLicLfd || bool_reg_lic_android || bool_reg_lic_sinage || bool_reg_lic_lite) {
         bRegLicLfd = true;
         SlmLicenseCheck = true;
      }

      Locale locale = SecurityUtils.getLocale();
      MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("scheduleTab");
      scheduleId = StrUtils.nvl(scheduleId);
      channelNo = StrUtils.nvl(channelNo);
      frameId = StrUtils.nvl(frameId);
      String contentId = StrUtils.nvl(resource.getContentId());
      String contentType = StrUtils.nvl(resource.getContentType());
      String startDate = StrUtils.nvl(resource.getStartDate());
      String stopDate = StrUtils.nvl(resource.getStopDate());
      String startTime = StrUtils.nvl(resource.getStartTime());
      String duration = StrUtils.nvl(resource.getDuration());
      String repeatType = StrUtils.nvl(resource.getRepeatType().equals("") ? "once" : resource.getRepeatType());
      String weekdays = StrUtils.nvl(resource.getWeekdays().equals("") ? "" : resource.getWeekdays());
      String monthdays = StrUtils.nvl(resource.getMonthday().equals("") ? "" : resource.getMonthday());
      String safetyLock = StrUtils.nvl(resource.getSafetyLock().equals("") ? "false" : resource.getSafetyLock());
      String playerMode = StrUtils.nvl(resource.getPlayerMode().equals("") ? "false" : resource.getPlayerMode());
      String isSync = resource.getIsSync();
      String cifsSlideTime = resource.getCifsSlideTime();
      String isHW = resource.getIsHW();
      String contentName = resource.getContentName();
      String inputSource = resource.getInputSource();
      boolean is_hw = false;
      if (isHW != null && isHW.equals("true")) {
         is_hw = true;
      }

      if (Integer.valueOf(duration) < 1) {
         resource.setStatus("cehck_duration");
         return resource;
      } else {
         ContentsScheduleEntity programEntity = null;
         ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
         if (program == null) {
            this.logger.info("[MagicInfo_Schedule] program null : " + programId);
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"program"});
         } else {
            int cnt = 0;
            ArrayList expiredContentList = new ArrayList();
            ContentInfo contentInfoImpl = ContentInfoImpl.getInstance();
            Content contentEntity = contentInfoImpl.getContentAndFileActiveVerInfo(contentId);
            if (contentEntity != null && contentEntity.getExpiration_date() != null && contentEntity.getExpiration_date().compareTo(DateUtils.getCurrentTime("yyyyMMdd")) < 0) {
               Map tmpmap = new HashMap();
               tmpmap.put("contentId", contentEntity.getContent_id());
               tmpmap.put("contentName", StrUtils.cutCharLen(contentEntity.getContent_name(), 25));
               expiredContentList.add(tmpmap);
               ++cnt;
            }

            if (cnt > 0) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_EXPIRED_CONTENT_NOT_ADD);
            } else {
               List channelList = program.getChannelList();
               if (channelList == null) {
                  this.logger.info("[MagicInfo_Schedule] channelList null : " + programId);
                  throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"channel list in the program"});
               } else {
                  for(int i = 0; i < channelList.size(); ++i) {
                     if (((ChannelEntity)channelList.get(i)).getChannel_no() == Integer.valueOf(channelNo)) {
                        List frameList = ((ChannelEntity)channelList.get(i)).getFrameList();
                        if (frameList == null) {
                           this.logger.info("[MagicInfo_Schedule] frameList null : " + programId);
                           throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"frame list in the program"});
                        }

                        for(int j = 0; j < frameList.size(); ++j) {
                           if (((FrameEntity)frameList.get(j)).getFrame_id().equals(frameId)) {
                              if (j == 0) {
                                 resource.setIsDefaultFrame(true);
                              } else {
                                 resource.setIsDefaultFrame(false);
                              }

                              List scheduleList = ((FrameEntity)frameList.get(j)).getScheduleList();
                              if (scheduleList == null) {
                                 this.logger.info("[MagicInfo_Schedule] scheduleList null : " + programId);
                                 throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"content schedule list in the program"});
                              }

                              for(int k = 0; k < scheduleList.size(); ++k) {
                                 if (((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_id().equals(scheduleId)) {
                                    programEntity = (ContentsScheduleEntity)scheduleList.get(k);
                                    if (is_hw) {
                                       if (!program.getDevice_type().equals("iPLAYER")) {
                                          programEntity.setContent_id("");
                                          programEntity.setContent_type("");
                                          if (inputSource.equals("1000")) {
                                             programEntity.setSchedule_type("01");
                                          } else {
                                             programEntity.setSchedule_type("03");
                                          }

                                          programEntity.setHw_input_source(inputSource);
                                          programEntity.setContent_type("");
                                          programEntity.setPriority(0L);
                                          programEntity.setHw_AtvDtv("-1");
                                          programEntity.setHw_AirCable("-1");
                                          programEntity.setHw_MajorCH("0");
                                          programEntity.setHw_MinorCH("0");
                                          programEntity.setHw_Volume("-1");
                                          programEntity.setHw_sch_ch("-1");
                                       } else {
                                          programEntity.setContent_type("HW_IS");
                                          programEntity.setContent_id(contentId);
                                          programEntity.setIn_effect_direction("");
                                          programEntity.setIn_effect_duration(0);
                                          programEntity.setIn_effect_type("");
                                          programEntity.setOut_effect_direction("");
                                          programEntity.setOut_effect_duration(0);
                                          programEntity.setOut_effect_type("");
                                       }

                                       programEntity.setFile_id((String)null);
                                       programEntity.setFile_name((String)null);
                                    } else {
                                       if (program.getDevice_type().equals("SPLAYER") && contentType != null && (contentType.equals("LFD") || contentType.equals("LFT")) && !((FrameEntity)frameList.get(j)).getLine_data().equals("ZeroFrameOnly")) {
                                          throw new RestServiceException(RestExceptionCode.BAD_REQUEST_LFD_CONTENT_NOT_PLAY_MULTI_FRAME_SCHEDULE);
                                       }

                                       if (isSync != null && isSync.equals("true")) {
                                          programEntity.setIsSync(true);
                                       }

                                       if (contentType.equals("PLAYLIST")) {
                                          PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
                                          Playlist playlist = pInfo.getPlaylistActiveVerInfo(contentId);
                                          if (playlist != null) {
                                             if (playlist.getPlaylist_type().equals("5")) {
                                                programEntity.setPlaylist_type("5");
                                                List ContentList = pInfo.getTagContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
                                                if (ContentList != null && ContentList.size() > 0) {
                                                   programEntity.setFile_id(((Content)ContentList.get(0)).getThumb_file_id());
                                                   programEntity.setFile_name(((Content)ContentList.get(0)).getThumb_file_name());
                                                } else {
                                                   programEntity.setFile_id("NOIMAGE_THUMBNAIL");
                                                   programEntity.setFile_name("NOIMAGE_THUMBNAIL.PNG");
                                                }
                                             } else {
                                                PlaylistDao playlistDao = new PlaylistDao();
                                                ContentFile thumbContent = playlistDao.getThumbFileInfo(contentId);
                                                programEntity.setFile_size(playlistDao.getPlaylistActiveVerInfo(contentId).getTotal_size());
                                                if (thumbContent != null && thumbContent.getFile_id() != null && thumbContent.getFile_name() != null) {
                                                   programEntity.setFile_id(thumbContent.getFile_id());
                                                   programEntity.setFile_name(thumbContent.getFile_name());
                                                } else {
                                                   Playlist playlistInfo = playlistDao.getPlaylistActiveVerInfo(contentId);
                                                   if (playlistInfo != null) {
                                                      ContentInfo contentDao = ContentInfoImpl.getInstance();
                                                      Content contentInfo = contentDao.getThumbInfoOfActiveVersion(playlistInfo.getContent_id());
                                                      if (contentInfo != null) {
                                                         programEntity.setFile_id(contentInfo.getThumb_file_id());
                                                         programEntity.setFile_name(contentInfo.getThumb_file_name());
                                                      } else {
                                                         List tmpList = pInfo.getContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
                                                         if (tmpList != null && tmpList.size() > 0) {
                                                            Content playlistContent = (Content)tmpList.get(0);
                                                            if (playlistContent != null) {
                                                               programEntity.setFile_id(playlistContent.getThumb_file_id());
                                                               programEntity.setFile_name(playlistContent.getThumb_file_name());
                                                            }
                                                         }
                                                      }
                                                   }
                                                }
                                             }
                                          }
                                       } else {
                                          ContentDao contentDao = new ContentDao();
                                          Map thumbContent = contentDao.getThumbFileInfoOfActiveVersion(contentId);
                                          programEntity.setFile_size(contentDao.getContentActiveVerInfo(contentId).getTotal_size());
                                          if (thumbContent != null) {
                                             if (thumbContent.get("file_size") != null) {
                                                programEntity.setFile_size((Long)thumbContent.get("file_size"));
                                             }

                                             if (thumbContent.get("file_id") != null) {
                                                programEntity.setFile_id((String)thumbContent.get("file_id"));
                                             }

                                             if (thumbContent.get("file_name") != null) {
                                                programEntity.setFile_name((String)thumbContent.get("file_name"));
                                             }
                                          }
                                       }

                                       if (playerMode.equals("vwl")) {
                                          programEntity.setPlayer_mode("vwl");
                                       } else {
                                          programEntity.setPlayer_mode("single");
                                       }

                                       if (contentType != null && (contentType.equals("CIFS") || contentType.equals("FTP"))) {
                                          boolean var60 = false;

                                          int slideTime;
                                          try {
                                             slideTime = Integer.valueOf(cifsSlideTime);
                                          } catch (Exception var57) {
                                             slideTime = 0;
                                          }

                                          programEntity.setSlide_transition_time(slideTime);
                                       }

                                       programEntity.setContent_id(contentId);
                                       programEntity.setSchedule_type("00");
                                       programEntity.setContent_type(contentType);
                                       programEntity.setIn_effect_direction("");
                                       programEntity.setIn_effect_duration(0);
                                       programEntity.setIn_effect_type("");
                                       programEntity.setOut_effect_direction("");
                                       programEntity.setOut_effect_duration(0);
                                       programEntity.setOut_effect_type("");
                                    }

                                    if (contentName != null && !contentName.equals("")) {
                                       programEntity.setContent_name(contentName);
                                    }

                                    programEntity.setStart_date(startDate);
                                    programEntity.setStop_date(stopDate);
                                    programEntity.setStart_time(startTime);
                                    programEntity.setDuration(Integer.valueOf(duration));
                                    programEntity.setRepeat_type(repeatType);
                                    programEntity.setSafetyLock(safetyLock);
                                    programEntity.setWeekdays(weekdays);
                                    programEntity.setMonthdays(monthdays);
                                    Date date = new Date();
                                    Timestamp timestamp = new Timestamp(date.getTime());
                                    programEntity.setModify_date(timestamp);
                                    scheduleList.set(k, programEntity);
                                    this.logger.info("[MagicInfo_Schedule] update content programId : " + programId);
                                    scheduleMgr.updateSchedule(program, CACHEDKEY);
                                    resource.setScheduleId(scheduleId);
                                 }
                              }
                           }
                        }
                     }
                  }

                  return resource;
               }
            }
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public V2scheduleDelResource scheduleDel(String programId, String channelNo, String frameId, String scheduleId, String sessionId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      V2scheduleDelResource resource = new V2scheduleDelResource();
      String CACHEDKEY = sessionId + programId;
      new AbilityUtils();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("scheduleTab");
      ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
      new LinkedHashMap();
      if (program == null) {
         this.logger.info("[MagicInfo_Schedule] program null : " + programId);
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"program"});
      } else {
         List channelList = program.getChannelList();
         if (channelList == null) {
            this.logger.info("[MagicInfo_Schedule] channelList null : " + programId);
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"channel list in the program"});
         } else {
            for(int i = 0; i < channelList.size(); ++i) {
               if (((ChannelEntity)channelList.get(i)).getChannel_no() == Integer.valueOf(channelNo)) {
                  List frameList = ((ChannelEntity)channelList.get(i)).getFrameList();
                  if (frameList == null) {
                     this.logger.info("[MagicInfo_Schedule] frameList null : " + programId);
                     throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"frame list in the program"});
                  }

                  for(int j = 0; j < frameList.size(); ++j) {
                     if (((FrameEntity)frameList.get(j)).getFrame_id().equals(frameId)) {
                        List scheduleList = ((FrameEntity)frameList.get(j)).getScheduleList();
                        if (scheduleList == null) {
                           this.logger.info("[MagicInfo_Schedule] scheduleList null : " + programId);
                           throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"content schedule list in the program"});
                        }

                        for(int k = 0; k < scheduleList.size(); ++k) {
                           if (((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_id().equals(scheduleId)) {
                              scheduleList.remove(k);
                              ((FrameEntity)((ChannelEntity)program.getChannelList().get(i)).getFrameList().get(j)).setScheduleList(scheduleList);
                              scheduleMgr.updateSchedule(program, CACHEDKEY);
                              this.logger.info("[MagicInfo_Schedule] delete content programId : " + programId + " scheduleId : " + scheduleId);
                           }
                        }
                     }
                  }
               }
            }

            resource.setProgramId(programId);
            resource.setChannelNo(channelNo);
            resource.setFrameId(frameId);
            resource.setScheduleId(scheduleId);
            return resource;
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public V2CommonResultResource scheduleRestore(V2CommonIds resource, String strProgGrpId, String sessionId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, Long.valueOf(strProgGrpId));
      boolean flag = false;
      List programIds = resource.getIds();

      for(int i = 0; i < programIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, (String)programIds.get(i));
         } catch (Exception var30) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE);
      }

      User loginUser = SecurityUtils.getLoginUser();
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      List successResourceList = new ArrayList();
      List failResourceList = new ArrayList();
      V2CommonResultResource resourceList = new V2CommonResultResource();
      List notiDataList = new ArrayList();
      boolean bRegLicLfd = false;
      boolean bool_reg_lic_soc = false;
      boolean bool_reg_lic_android = false;
      boolean bool_reg_lic_sinage = false;
      boolean bool_reg_lic_lite = false;
      boolean SlmLicenseCheck = false;
      ScheduleInterface infoDao = DAOFactory.getScheduleInfoImpl("PREMIUM");
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (bool_reg_lic_soc || bRegLicLfd || bool_reg_lic_android || bool_reg_lic_sinage || bool_reg_lic_lite) {
         bRegLicLfd = true;
         SlmLicenseCheck = true;
      }

      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("scheduleTab");
      if (programIds.size() > 0) {
         for(int i = 0; i < programIds.size(); ++i) {
            try {
               V2ScheduleRestoreResource result = new V2ScheduleRestoreResource();
               boolean recover = false;
               recover = infoDao.recoverSchedule((String)programIds.get(i), strProgGrpId);
               if (!recover) {
                  throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
               }

               infoDao.updateModifyDate((String)programIds.get(i));
               result.setId((String)programIds.get(i));
               result.setGroupId(strProgGrpId);

               try {
                  ProgramEntity program = scheduleInfo.getProgram((String)programIds.get(i));
                  String orgName = programGroupInfo.getOrgNameByGroupId(Long.valueOf(strProgGrpId));
                  NotificationData notiData = new NotificationData();
                  notiData.setName(program.getProgram_name());
                  notiData.setOrgName(orgName);
                  notiData.setUserName(loginUser.getUser_id());
                  notiDataList.add(notiData);
               } catch (Exception var28) {
                  this.logger.error(var28);
               }

               successResourceList.add(result);
            } catch (Exception var29) {
               this.logger.error(var29);
               V2ScheduleRestoreResource obj = new V2ScheduleRestoreResource();
               obj.setId((String)programIds.get(i));
               obj.setReason(var29.getMessage());
               failResourceList.add(obj);
            }
         }

         try {
            if (notiDataList != null && notiDataList.size() > 0) {
               MailUtil.sendContentScheduleEventMail(notiDataList, "Content Schedule Restore");
            }
         } catch (Exception var27) {
            this.logger.error(var27);
         }
      }

      resourceList.setSuccessList(successResourceList);
      resourceList.setFailList(failResourceList);
      return resourceList;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public V2SchedulegetFrameResource getFrame(String programId, String channelNo, HttpServletRequest request) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      V2SchedulegetFrameResource resource = new V2SchedulegetFrameResource();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      String sessionId = request.getSession().getId();
      String CACHEDKEY = sessionId + programId;
      channelNo = StrUtils.nvl(channelNo).equals("") ? "1" : channelNo;

      try {
         int channel = Integer.valueOf(channelNo);
         ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
         boolean frameChk = false;
         String defaultFrameId = null;

         for(int i = 0; i < program.getChannelList().size(); ++i) {
            if (((ChannelEntity)program.getChannelList().get(i)).getChannel_no() == channel && ((ChannelEntity)program.getChannelList().get(i)).getFrameList() != null) {
               defaultFrameId = ((FrameEntity)((ChannelEntity)program.getChannelList().get(i)).getFrameList().get(0)).getFrame_id();
               if (((ChannelEntity)program.getChannelList().get(i)).getFrameList().size() == 1 && !((FrameEntity)((ChannelEntity)program.getChannelList().get(i)).getFrameList().get(0)).getFlag()) {
                  frameChk = true;
               } else {
                  List frmaeLists = new ArrayList();
                  List frameList = ((ChannelEntity)program.getChannelList().get(i)).getFrameList();
                  if (frameList != null && frameList.size() > 0) {
                     int count = 0;
                     int frameSize = frameList.size();

                     for(Iterator var17 = frameList.iterator(); var17.hasNext(); ++count) {
                        FrameEntity frame = (FrameEntity)var17.next();
                        String frameType = "fixed";
                        if (frame.getLine_data() != null && (frame.getLine_data().equals("CustomLayout") || frame.getLine_data().equals("custom"))) {
                           frameType = "custom";
                        }

                        boolean isMainFrame = false;
                        if (frame.getIs_main_frame() != null && frame.getIs_main_frame().equals("Y")) {
                           isMainFrame = true;
                        }

                        String userGroupId = "";
                        String userGroupName = "";
                        int index;
                        String[] var24;
                        int var25;
                        int var26;
                        String temp;
                        if (frame.getAuthority() != null) {
                           index = 0;
                           var24 = frame.getAuthority();
                           var25 = var24.length;

                           for(var26 = 0; var26 < var25; ++var26) {
                              temp = var24[var26];
                              if (index > 0) {
                                 userGroupId = userGroupId + ",";
                              }

                              userGroupId = userGroupId + temp;
                              ++index;
                           }
                        }

                        if (frame.getAuthorityNames() != null) {
                           index = 0;
                           var24 = frame.getAuthorityNames();
                           var25 = var24.length;

                           for(var26 = 0; var26 < var25; ++var26) {
                              temp = var24[var26];
                              if (index > 0) {
                                 userGroupName = userGroupName + ",";
                              }

                              userGroupName = userGroupName + temp;
                              ++index;
                           }
                        }

                        if (count == 0) {
                           if (frame.getDefault_content_id() != null) {
                              resource.setAllDefaultContentId(frame.getDefault_content_id());
                           }

                           if (frame.getDefault_content_names() != null) {
                              resource.setAllDefaultContentName(frame.getDefault_content_names());
                           }

                           if (userGroupId != null) {
                              resource.setAllAuthorityId(userGroupId);
                           }

                           if (userGroupName != null) {
                              resource.setAllAuthorityName(userGroupName);
                           }

                           if (frameSize == 1) {
                              frmaeLists.add(this.createFrameMap(frame.getFrame_name(), frame.getFrame_id(), frameType, program.getResolution(), isMainFrame, frame.getDefault_content_id(), frame.getDefault_content_names(), userGroupName, userGroupId, (float)frame.getX(), (float)frame.getY(), (float)frame.getWidth(), (float)frame.getHeight(), frame.getLine_data()));
                           }
                        } else {
                           frmaeLists.add(this.createFrameMap(frame.getFrame_name(), frame.getFrame_id(), frameType, program.getResolution(), isMainFrame, frame.getDefault_content_id(), frame.getDefault_content_names(), userGroupName, userGroupId, (float)frame.getX(), (float)frame.getY(), (float)frame.getWidth(), (float)frame.getHeight(), frame.getLine_data()));
                        }
                     }
                  }

                  resource.setFrmaeLists(frmaeLists);
               }
            }
         }

         resource.setResolution(program.getResolution());
         resource.setDefaultFrameId(defaultFrameId);
         resource.setFrameChk(frameChk);
         return resource;
      } catch (Exception var28) {
         this.logger.error("", var28);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public V2ContentScheduleItemResource addScheduleSimple(String programId, String channelNo, String frameId, V2ContentScheduleItemResource resource, HttpServletRequest request) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      String sessionId = request.getSession().getId();
      User loginUser = SecurityUtils.getLoginUser();
      String CACHEDKEY = sessionId + programId;
      boolean bRegLicLfd = false;
      boolean bool_reg_lic_soc = false;
      boolean bool_reg_lic_android = false;
      boolean bool_reg_lic_sinage = false;
      boolean bool_reg_lic_lite = false;
      boolean SlmLicenseCheck = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (bool_reg_lic_soc || bRegLicLfd || bool_reg_lic_android || bool_reg_lic_sinage || bool_reg_lic_lite) {
         bRegLicLfd = true;
         SlmLicenseCheck = true;
      }

      new AbilityUtils();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("scheduleTab");
      UserContainer userContainer = SecurityUtils.getUserContainer();
      LinkedHashMap result = new LinkedHashMap();
      V2ContentScheduleItemResource newResource = new V2ContentScheduleItemResource();
      String contentName = StrUtils.nvl(resource.getContentName());
      channelNo = StrUtils.nvl(channelNo);
      frameId = StrUtils.nvl(frameId);
      String startDate = StrUtils.nvl(resource.getStartDate());
      String stopDate = StrUtils.nvl(resource.getStopDate());
      String startTime = StrUtils.nvl(resource.getStartTime());
      String duration = StrUtils.nvl(resource.getDuration());
      String contentId = StrUtils.nvl(resource.getContentId());
      String contentType = StrUtils.nvl(resource.getContentType());
      String repeatType = StrUtils.nvl(resource.getRepeatType() == null ? "once" : resource.getRepeatType());
      String weekdays = StrUtils.nvl(resource.getWeekdays());
      String monthday = StrUtils.nvl(resource.getMonthday());
      String scheduleType = StrUtils.nvl(resource.getScheduleType() == null ? "00" : resource.getScheduleType());
      String inputSource = StrUtils.nvl(resource.getInputSource());
      String safetyLock = StrUtils.nvl(resource.getSafetyLock() == null ? "false" : resource.getSafetyLock());
      String isSync = resource.getIsSync();
      String cifsSlideTime = resource.getCifsSlideTime();
      String playerMode = StrUtils.nvl(resource.getPlayerMode());
      boolean check = false;
      long currentIndex = 0L;
      int cnt = 0;
      ArrayList expiredContentList = new ArrayList();
      ContentInfo contentInfoImpl = ContentInfoImpl.getInstance();
      Content contentEntity = contentInfoImpl.getContentAndFileActiveVerInfo(contentId);
      if (contentEntity != null && contentEntity.getExpiration_date() != null && contentEntity.getExpiration_date().compareTo(DateUtils.getCurrentTime("yyyyMMdd")) < 0) {
         Map tmpmap = new HashMap();
         tmpmap.put("contentId", contentEntity.getContent_id());
         tmpmap.put("contentName", StrUtils.cutCharLen(contentEntity.getContent_name(), 25));
         expiredContentList.add(tmpmap);
         ++cnt;
      }

      if (cnt > 0) {
         newResource.setMessage(RestExceptionCode.BAD_REQUEST_EXPIRED_CONTENT_NOT_ADD.getMessage());
         newResource.setExpiredContentList(expiredContentList);
         newResource.setStatus("non-addable");
         return newResource;
      } else if (Integer.valueOf(duration) < 1) {
         newResource.setStatus("cehck_duration");
         return newResource;
      } else {
         ProgramEntity program = scheduleMgr.getScheudle(CACHEDKEY);
         if (program == null) {
            this.logger.info("[MagicInfo_Schedule] program null : " + programId);
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"program"});
         } else {
            ContentsScheduleEntity content = new ContentsScheduleEntity();
            scheduleMgr.contentScheduleInit(programId, content);
            content.setContent_name(contentName);
            content.setStart_date(startDate);
            content.setStop_date(stopDate);
            content.setStart_time(startTime);
            content.setDuration(Integer.valueOf(duration));
            content.setContent_id(contentId);
            content.setPlayer_mode(playerMode);
            currentIndex = program.getCurrentIndex();
            String mediaType = contentType;
            ++currentIndex;
            if (contentType.equals("HW_IS")) {
               String deviceType = program.getDevice_type();
               if (ScheduleUtility.isSupportInputSourceRepeat(deviceType, program.getDevice_type_version())) {
                  content.setContent_type("HW_IS");
                  content.setSchedule_type("00");
                  content.setIn_effect_direction("");
                  content.setIn_effect_duration(0);
                  content.setIn_effect_type("");
                  content.setOut_effect_direction("");
                  content.setOut_effect_duration(0);
                  content.setOut_effect_type("");
               } else if (deviceType.equals("SPLAYER") || deviceType.equals("LPLAYER") || deviceType.equals("WPLAYER")) {
                  content.setContent_id("");
                  if (inputSource.equals("1000")) {
                     content.setSchedule_type("01");
                  } else {
                     content.setSchedule_type("03");
                  }

                  content.setHw_input_source(inputSource);
                  content.setContent_type("");
                  content.setPriority(0L);
                  content.setHw_AtvDtv("-1");
                  content.setHw_AirCable("-1");
                  content.setHw_MajorCH("0");
                  content.setHw_MinorCH("0");
                  content.setHw_Volume("-1");
                  content.setHw_sch_ch("-1");
               }
            } else {
               PlaylistDao playlistDao = new PlaylistDao();
               if (contentType.equals("PLAYLIST")) {
                  PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
                  Playlist playlist = pInfo.getPlaylistActiveVerInfo(contentId);
                  if (playlist != null) {
                     if (playlist.getPlaylist_type().equals("5")) {
                        content.setPlaylist_type("5");
                        List ContentList = pInfo.getTagContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
                        if (ContentList != null && ContentList.size() > 0) {
                           content.setFile_id(((Content)ContentList.get(0)).getThumb_file_id());
                           content.setFile_name(((Content)ContentList.get(0)).getThumb_file_name());
                        }
                     } else {
                        ContentFile thumbContent = playlistDao.getThumbFileInfo(contentId);
                        content.setFile_size(playlistDao.getPlaylistActiveVerInfo(contentId).getTotal_size());
                        if (thumbContent != null && thumbContent.getFile_id() != null && thumbContent.getFile_name() != null) {
                           content.setFile_id(thumbContent.getFile_id());
                           content.setFile_name(thumbContent.getFile_name());
                        } else {
                           Playlist playlistInfo = playlistDao.getPlaylistActiveVerInfo(contentId);
                           if (playlistInfo != null) {
                              ContentInfo contentDao = ContentInfoImpl.getInstance();
                              Content contentInfo = contentDao.getThumbInfoOfActiveVersion(playlistInfo.getContent_id());
                              if (contentInfo != null) {
                                 content.setFile_id(contentInfo.getThumb_file_id());
                                 content.setFile_name(contentInfo.getThumb_file_name());
                              } else {
                                 List tmpList = pInfo.getContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
                                 if (tmpList != null && tmpList.size() > 0) {
                                    Content playlistContent = (Content)tmpList.get(0);
                                    if (playlistContent != null) {
                                       content.setFile_id(playlistContent.getThumb_file_id());
                                       content.setFile_name(playlistContent.getThumb_file_name());
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }
               } else {
                  ContentDao contentDao = new ContentDao();
                  Map thumbContent = contentDao.getThumbFileInfoOfActiveVersion(contentId);
                  content.setFile_size(contentDao.getContentActiveVerInfo(contentId).getTotal_size());
                  content.setFile_id((String)thumbContent.get("file_id"));
                  content.setFile_name((String)thumbContent.get("file_name"));
               }

               if (!contentType.equals("CIFS") && !contentType.equals("FTP")) {
                  content.setSlide_transition_time(0);
               } else {
                  int slideTime = 5;

                  try {
                     if (cifsSlideTime != null && !cifsSlideTime.equals("")) {
                        slideTime = Integer.valueOf(cifsSlideTime);
                     }
                  } catch (Exception var56) {
                     slideTime = 5;
                  }

                  content.setSlide_transition_time(slideTime);
               }

               content.setSchedule_type("00");
               content.setContent_type(contentType);
               content.setIn_effect_direction("");
               content.setIn_effect_duration(0);
               content.setIn_effect_type("");
               content.setOut_effect_direction("");
               content.setOut_effect_duration(0);
               content.setOut_effect_type("");
            }

            if (isSync != null && isSync.equals("true")) {
               content.setIsSync(true);
            }

            content.setRepeat_type(repeatType);
            content.setWeekdays(weekdays);
            content.setMonthdays(monthday);
            content.setPriority(currentIndex);
            content.setSafetyLock(safetyLock);
            content.setChannel_no(Integer.valueOf(channelNo));
            Date date = new Date();
            Timestamp timestamp = new Timestamp(date.getTime());
            content.setModify_date(timestamp);
            content.setCreate_date(timestamp);
            content.setUser_id(userContainer.getUser().getUser_id());
            List channelList = program.getChannelList();
            if (channelList == null) {
               this.logger.info("[MagicInfo_Schedule] channelList null : " + programId);
               throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"channel list in the program"});
            } else {
               for(int i = 0; i < channelList.size(); ++i) {
                  if (((ChannelEntity)channelList.get(i)).getChannel_no() == Integer.valueOf(channelNo)) {
                     List frameList = ((ChannelEntity)channelList.get(i)).getFrameList();
                     if (frameList == null) {
                        this.logger.info("[MagicInfo_Schedule] frameList null : " + programId);
                        throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"frame list in the program"});
                     }

                     for(int j = 0; j < frameList.size(); ++j) {
                        if (((FrameEntity)frameList.get(j)).getFrame_id().equals(frameId)) {
                           List scheduleList = ((FrameEntity)frameList.get(j)).getScheduleList();
                           if (program.getDevice_type().equals("SPLAYER") && mediaType != null && (mediaType.equals("LFD") || mediaType.equals("LFT")) && !((FrameEntity)frameList.get(j)).getLine_data().equals("ZeroFrameOnly")) {
                              this.logger.info("[MagicInfo_Schedule] frameList null : " + programId);
                              throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"frame list in the program"});
                           }

                           if (scheduleList == null) {
                              scheduleList = new ArrayList();
                           }

                           if (!content.getSchedule_type().equals("03")) {
                              content.setFrame_index(((FrameEntity)frameList.get(j)).getFrame_index());
                           }

                           ((List)scheduleList).add(content);
                           ((FrameEntity)frameList.get(j)).setScheduleList((List)scheduleList);
                           ((ChannelEntity)channelList.get(i)).setFrameList(frameList);
                           program.setChannelList(channelList);
                           program.setCurrentIndex(currentIndex);
                           scheduleMgr.updateSchedule(program, CACHEDKEY);
                           this.logger.info("[MagicInfo_Schedule] insert content programId : " + programId);
                           result.put("scheduleId", content.getSchedule_id());
                           result.put("status", "success");
                           check = true;
                        }
                     }
                  }
               }

               newResource.setContentName(contentName);
               newResource.setChannelNo(Integer.valueOf(channelNo));
               newResource.setFrameId(frameId);
               newResource.setStartDate(startDate);
               newResource.setStopDate(stopDate);
               newResource.setStartTime(startTime);
               newResource.setDuration(duration);
               newResource.setContentId(contentId);
               newResource.setContentType(contentType);
               newResource.setRepeatType(repeatType);
               newResource.setWeekdays(weekdays);
               newResource.setMonthday(monthday);
               newResource.setScheduleType(scheduleType);
               newResource.setInputSource(inputSource);
               newResource.setSafetyLock(safetyLock);
               newResource.setIsSync(isSync);
               newResource.setCifsSlideTime(cifsSlideTime);
               newResource.setPlayerMode(playerMode);
               return newResource;
            }
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public V2ContentScheduleDiskCheckInfo checkDisk(V2ContentScheduleDiskCheckInfo diskCheckInfo) throws Exception {
      StringBuilder deviceGroupIdsStr = new StringBuilder();
      List deviceGroupIds = diskCheckInfo.getDeviceGroupIds();
      if (deviceGroupIds != null) {
         for(int i = 0; i < deviceGroupIds.size(); ++i) {
            long groupId = (Long)deviceGroupIds.get(i);
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, groupId);
            int deviceCount = this.getDeviceCountInGroup(CommonUtils.safeLongToInt(groupId));
            if (deviceCount > 0) {
               deviceGroupIdsStr.append(groupId);
               if (deviceGroupIds.size() - 1 != i) {
                  deviceGroupIdsStr.append(",");
               }
            }
         }
      }

      if (!deviceGroupIdsStr.toString().isEmpty()) {
         long diskSize = this.getDiskSpaceRepository(deviceGroupIdsStr.toString());
         long scheduleFileSize = diskCheckInfo.getScheduleFileSize();
         diskCheckInfo.setDidDiskSpaceCheck(true);
         diskCheckInfo.setHasEnoughDiskSpace(diskSize > scheduleFileSize);
      } else {
         diskCheckInfo.setDidDiskSpaceCheck(false);
         diskCheckInfo.setHasEnoughDiskSpace((Boolean)null);
      }

      return diskCheckInfo;
   }

   private int getDeviceCountInGroup(int groupId) throws SQLException {
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      return deviceGroupDao.getCntDeviceInDeviceGroup(groupId);
   }

   private long getDiskSpaceRepository(String groupIds) throws SQLException {
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      return deviceGroupDao.getDiskSpaceRepository(groupIds);
   }

   private void updateDevicePublishedStatus(List deviceStatuses, List deviceIds, DownloadStatusInfo downloadInfo, String programId, MonitoringManager motMgr, String status, int completeCountValue) throws SQLException {
      int totalCount = 1;
      int completeCount = false;
      Iterator var10 = deviceIds.iterator();

      while(var10.hasNext()) {
         String deviceId = (String)var10.next();
         int completeCount = 0;
         if (downloadInfo.getStatusByProgramIdAndDeviceId(deviceId, programId).equals(status)) {
            TTV2PublishingDeviceStatus deviceStatus = new TTV2PublishingDeviceStatus();
            DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
            Map group = deviceGroupInfo.getGroupNameByDeviceId(deviceId);
            String groupName = (String)group.get("group_name");
            DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
            String deviceName = deviceDao.getDeviceNameById(deviceId);
            boolean isPowerOn = motMgr.isConnected(deviceId);
            deviceStatus.setIsPowerOn(isPowerOn);
            if (isPowerOn) {
               completeCount = completeCountValue;
            }

            deviceStatus.setDeviceId(deviceId);
            deviceStatus.setDeviceName(deviceName);
            deviceStatus.setDeviceGroupName(groupName);
            deviceStatus.setTotalCount(totalCount);
            deviceStatus.setCompleteCount(completeCount);
            deviceStatuses.add(deviceStatus);
         }
      }

   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public TTV2PublishingProgress getPublishingProgress(String programId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
      MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
      DownloadStatusInfo downloadInfo = DownloadStatusInfoImpl.getInstacne();
      Object[] downloadStatus = downloadInfo.getDownloadStatusListByProgramId(programId);
      TTV2PublishingProgress progress = new TTV2PublishingProgress();
      if (downloadStatus != null && downloadStatus.length > 1) {
         Map countInfoMap = (Map)downloadStatus[1];
         if (countInfoMap != null) {
            int total = 0;
            int complete = 0;
            if (countInfoMap.containsKey("deviceCount") && countInfoMap.get("deviceCount") != null) {
               total = (Integer)countInfoMap.get("deviceCount");
               progress.setTotalDeviceCount(total);
            }

            if (countInfoMap.containsKey("completeCount") && countInfoMap.get("completeCount") != null) {
               complete = (Integer)countInfoMap.get("completeCount");
               progress.setCompleteDeviceCount(complete);
            }

            if (total > 0) {
               float percentage = (float)complete / (float)total * 100.0F;
               progress.setCompletePercentage(String.format("%.1f", percentage));
            }

            if (countInfoMap.containsKey("deviceList") && countInfoMap.get("deviceList") != null) {
               List deviceIds = (List)countInfoMap.get("deviceList");
               if (deviceIds != null && !deviceIds.isEmpty()) {
                  List deviceStatusMapList = downloadInfo.getProgressInfoByDeviceId(programId, deviceIds);
                  List deviceStatuses = new ArrayList();
                  if (deviceStatusMapList != null && !deviceStatusMapList.isEmpty()) {
                     Iterator var12 = deviceStatusMapList.iterator();

                     while(var12.hasNext()) {
                        Map deviceStatusMap = (Map)var12.next();
                        String deviceId = (String)deviceStatusMap.get("device_id");
                        String deviceName = (String)deviceStatusMap.get("device_name");
                        String deviceGroupName = (String)deviceStatusMap.get("device_group_name");
                        String tmpTotal = "0";
                        String tmpComplete = "0";
                        boolean isPowerOn = motMgr.isConnected(deviceId);
                        if (deviceStatusMap.containsKey("total") && deviceStatusMap.get("total") != null) {
                           tmpTotal = String.valueOf(deviceStatusMap.get("total"));
                           tmpTotal = !tmpTotal.isEmpty() ? tmpTotal : "0";
                        }

                        if (isPowerOn && deviceStatusMap.containsKey("complete") && deviceStatusMap.get("complete") != null) {
                           tmpComplete = String.valueOf(deviceStatusMap.get("complete"));
                           tmpComplete = !tmpComplete.isEmpty() ? tmpComplete : "0";
                        }

                        BigDecimal bdTotal = new BigDecimal(tmpTotal);
                        BigDecimal bdComplete = new BigDecimal(tmpComplete);
                        int totalCount = CommonUtils.safeLongToInt((long)bdTotal.intValue());
                        int completeCount = CommonUtils.safeLongToInt((long)bdComplete.intValue());
                        TTV2PublishingDeviceStatus deviceStatus = new TTV2PublishingDeviceStatus();
                        deviceStatus.setDeviceId(deviceId);
                        deviceStatus.setDeviceName(deviceName);
                        deviceStatus.setDeviceGroupName(deviceGroupName);
                        deviceStatus.setTotalCount(totalCount);
                        deviceStatus.setCompleteCount(completeCount);
                        deviceStatus.setIsPowerOn(isPowerOn);
                        deviceStatuses.add(deviceStatus);
                        deviceIds.remove(deviceId);
                     }
                  }

                  this.updateDevicePublishedStatus(deviceStatuses, deviceIds, downloadInfo, programId, motMgr, "SUCCESS", 1);
                  this.updateDevicePublishedStatus(deviceStatuses, deviceIds, downloadInfo, programId, motMgr, "WAITING", 0);
                  progress.setDeviceStatuses(deviceStatuses);
               }
            }
         }
      }

      return progress;
   }

   private FrameEntity createNewFrame(FrameEntity frame, String programId, String frameId, int channel, String mainFrame, int index, int version, float x, float y, float width, float height, String lineData) {
      frame.setProgram_id(programId);
      frame.setFrame_id(frameId);
      frame.setChannel_no(channel);
      frame.setScreen_index(0);
      frame.setFrame_name("Frame " + index);
      frame.setFrame_index(index);
      frame.setVersion((long)version);
      frame.setIs_main_frame(mainFrame);
      frame.setDefault_content_id("");
      frame.setX((double)x);
      frame.setY((double)y);
      frame.setWidth((double)width);
      frame.setHeight((double)height);
      frame.setLine_data(lineData);
      return frame;
   }

   private LinkedHashMap createFrameMap(long frameId, String frameName, String type, String resolution, float x, float y, float width, float height) {
      LinkedHashMap frameMap = new LinkedHashMap();
      if (frameId != Long.MIN_VALUE) {
         frameMap.put("id", frameId);
      }

      frameMap.put("frameName", frameName);
      frameMap.put("templateType", type);
      frameMap.put("resolution", resolution);
      frameMap.put("x", x);
      frameMap.put("y", y);
      frameMap.put("width", width);
      frameMap.put("height", height);
      return frameMap;
   }

   private TTV2FrameTemplateResource createDefaultFrameTemplateResource(String templateName, String templateType, int resolutionW, int resolutionH) {
      TTV2DisplayResolution resolution = new TTV2DisplayResolution();
      resolution.setWidth((double)resolutionW);
      resolution.setHeight((double)resolutionH);
      TTV2FrameTemplateResource resource = new TTV2FrameTemplateResource();
      resource.setTemplateName(templateName);
      if (templateType.equals("custom")) {
         resource.setTemplateType("CUSTOM");
      } else if (templateType.equals("customFixed")) {
         resource.setTemplateType("CUSTOM_FIXED");
      } else if (templateType.equals("fixed")) {
         resource.setTemplateType("FIXED");
      } else {
         resource.setTemplateType("FIXED");
      }

      resource.setResolution(resolution);
      return resource;
   }

   private TTV2LayoutDivisionFrameInfo createDefaultFrameInfo(String frameName, String frameType, float x, float y, float width, float height) {
      TTV2LayoutDivisionFrameInfo frameInfo = new TTV2LayoutDivisionFrameInfo();
      frameInfo.setIndex(0);
      frameInfo.setFrameName(frameName);
      if (frameType.equals("custom")) {
         frameInfo.setFrameType("CUSTOM");
      } else if (frameType.equals("customFixed")) {
         frameInfo.setFrameType("CUSTOM_FIXED");
      } else if (frameType.equals("fixed")) {
         frameInfo.setFrameType("FIXED");
      } else {
         frameInfo.setFrameType("FIXED");
      }

      frameInfo.setX((double)x);
      frameInfo.setY((double)y);
      frameInfo.setWidth((double)width);
      frameInfo.setHeight((double)height);
      return frameInfo;
   }

   private LinkedHashMap createFrameMap(String frameName, String frameId, String templateType, String resolution, boolean mainFrame, String defaultContentId, String defaultContentName, String userGroupName, String userGroupId, float x, float y, float width, float height, String lineData) {
      LinkedHashMap frameMap = new LinkedHashMap();
      frameMap.put("frameName", frameName);
      frameMap.put("frameId", frameId);
      frameMap.put("templateType", templateType);
      frameMap.put("resolution", resolution);
      frameMap.put("mainFrame", mainFrame);
      frameMap.put("defaultContentId", defaultContentId);
      frameMap.put("defaultContentName", defaultContentName);
      frameMap.put("userGroupName", userGroupName);
      frameMap.put("userGroupId", userGroupId);
      frameMap.put("x", x);
      frameMap.put("y", y);
      frameMap.put("width", width);
      frameMap.put("height", height);
      frameMap.put("lineData", lineData);
      return frameMap;
   }

   private float[] returnFramePostion(LinkedHashMap customFrame, String type, float[] sp, float[] ep) {
      float[] rtn = new float[4];
      float startix = -1.0F;
      float startiy = -1.0F;
      float endix = -1.0F;
      float endiy = -1.0F;
      float x = (Float)customFrame.get("x");
      float y = (Float)customFrame.get("y");
      float width = (Float)customFrame.get("width");
      float height = (Float)customFrame.get("height");
      if (type.equals("horizontal")) {
         float x2;
         if (sp[0] < ep[0]) {
            if (sp[0] <= x && ep[0] > x && y < sp[1] && sp[1] < y + height) {
               startix = x;
               startiy = sp[1];
            }

            x2 = x + width;
            if (ep[0] >= x2 && startix != -1.0F) {
               endix = x2;
               endiy = sp[1];
            }
         } else {
            if (sp[0] > x && ep[0] <= x && y < sp[1] && sp[1] < y + height) {
               startix = x;
               startiy = sp[1];
            }

            x2 = x + width;
            if (sp[0] >= x2 && startix != -1.0F) {
               endix = x2;
               endiy = sp[1];
            }
         }
      } else if (type.equals("vertical")) {
         float y2;
         if (sp[1] < ep[1]) {
            if (sp[1] <= y && ep[1] > y && x < sp[0] && sp[0] < x + width) {
               startix = sp[0];
               startiy = y;
            }

            y2 = y + height;
            if (ep[1] >= y2 && startix != -1.0F) {
               endix = sp[0];
               endiy = y2;
            }
         } else {
            if (sp[1] > y && ep[1] <= y && x < sp[0] && sp[0] < x + width) {
               startix = sp[0];
               startiy = y;
            }

            y2 = y + height;
            if (sp[1] >= y2 && startix != -1.0F) {
               endix = sp[0];
               endiy = y2;
            }
         }
      }

      rtn[0] = startix;
      rtn[1] = startiy;
      rtn[2] = endix;
      rtn[3] = endiy;
      return rtn;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public V2ScheduleCommonDeletionResource deleteAllInRecycleBin() throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      User loginUser = SecurityUtils.getLoginUser();
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      ScheduleInterface infoDao = DAOFactory.getScheduleInfoImpl("PREMIUM");
      List notiDataList = new ArrayList();
      ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
      List programList = schInfo.getDeletedProgramIdList(loginUser.getOrganization());
      boolean flag = false;
      if (programList != null && programList.size() > 0) {
         Iterator var10 = programList.iterator();

         while(var10.hasNext()) {
            Map programMap = (Map)var10.next();
            if (programMap.get("program_id") != null) {
               String programId = (String)programMap.get("program_id");
               if (!V2ContentScheduleValidator.isValidActionAsScheduleEditor(programId, RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE)) {
                  flag = true;
                  break;
               }

               try {
                  RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, programId);
               } catch (Exception var20) {
                  flag = true;
                  break;
               }
            }
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE);
      }

      V2ScheduleCommonDeletionResource result = new V2ScheduleCommonDeletionResource();
      if (programList != null && programList.size() > 0) {
         Iterator var22 = programList.iterator();

         while(var22.hasNext()) {
            Map programMap = (Map)var22.next();
            if (programMap.get("program_id") != null) {
               String programId = (String)programMap.get("program_id");
               result.getItemIds().add(programId);
               NotificationData notiData = null;

               try {
                  ProgramEntity program = scheduleInfo.getProgram(programId);
                  if (program != null) {
                     programGroupInfo.getOrgNameByGroupId(program.getProgram_group_id());
                     notiData = new NotificationData();
                     notiData.setName(program.getProgram_name());
                     notiData.setOrgName(program.getProgram_group_name());
                     notiData.setUserName(loginUser.getUser_id());
                  } else {
                     this.logger.error("program is null");
                  }
               } catch (Exception var19) {
                  this.logger.error(var19);
               }

               try {
                  if (infoDao.deleteSchedulePerm(programId, userContainer.getUser().getUser_id(), "REST API v2.0")) {
                     try {
                        if (notiData != null) {
                           notiDataList.add(notiData);
                        } else {
                           this.logger.error("notiData is null");
                        }
                     } catch (Exception var17) {
                        this.logger.error(var17);
                     }
                  } else {
                     result.getNotDeletedItemIds().add(programId);
                  }
               } catch (Exception var18) {
                  this.logger.error(var18);
                  result.getNotDeletedItemIds().add(programId);
               }
            }
         }

         if (notiDataList.size() > 0) {
            MailUtil.sendContentScheduleEventMail(notiDataList, "04");
         }

         return result;
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_RECYCLE_BIN_EMPTY);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Add Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public V2CommonResultResource delete(V2ScheduleDeleteParam resource) throws Exception {
      boolean flag = false;
      List programIds = resource.getIds();

      for(int i = 0; i < programIds.size(); ++i) {
         if (!V2ContentScheduleValidator.isValidActionAsScheduleEditor((String)programIds.get(i), RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE)) {
            flag = true;
            break;
         }

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, (String)programIds.get(i));
         } catch (Exception var32) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE);
      }

      boolean bRegLicLfd = false;
      boolean bool_reg_lic_soc = false;
      boolean bool_reg_lic_android = false;
      boolean bool_reg_lic_sinage = false;
      boolean bool_reg_lic_lite = false;
      boolean SlmLicenseCheck = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (bool_reg_lic_soc || bRegLicLfd || bool_reg_lic_android || bool_reg_lic_sinage || bool_reg_lic_lite) {
         bRegLicLfd = true;
         SlmLicenseCheck = true;
      }

      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      V2CommonResultResource result = new V2CommonResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      AbilityUtils ability = new AbilityUtils();
      MenuEntity resultMenu = new MenuEntity();
      resultMenu.setMenuName("scheduleTab");
      ScheduleInterface infoDao = DAOFactory.getScheduleInfoImpl("PREMIUM");
      List notiDataList = new ArrayList();
      String userId = userContainer.getUser().getUser_id();
      boolean scheduleWrite = ability.checkAuthority("Content Schedule Add") && SlmLicenseCheck;
      boolean scheduleManage = ability.checkAuthority("Content Schedule Write") && SlmLicenseCheck;

      for(int i = 0; i < programIds.size(); ++i) {
         try {
            boolean authority = false;
            if (!scheduleManage && (!scheduleWrite || !scheduleMgr.checkProgramPermission(userId, (String)programIds.get(i)))) {
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
            }

            authority = true;
            NotificationData notiData = null;

            try {
               ProgramEntity program = scheduleInfo.getProgram((String)programIds.get(i));
               String orgName = programGroupInfo.getOrgNameByGroupId(program.getProgram_group_id());
               notiData = new NotificationData();
               notiData.setName(program.getProgram_name());
               notiData.setOrgName(orgName);
               notiData.setUserName(userId);
            } catch (Exception var30) {
               this.logger.error(var30);
            }

            if (!infoDao.deleteSchedule((String)programIds.get(i), userContainer.getUser().getUser_id(), "")) {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
            }

            try {
               if (notiData != null) {
                  notiDataList.add(notiData);
               } else {
                  this.logger.error("notiData is null");
               }
            } catch (Exception var29) {
               this.logger.error(var29);
            }

            infoDao.updateModifyDate((String)programIds.get(i));
            successList.add(programIds.get(i));
         } catch (Exception var31) {
            this.logger.error(var31);
            V2CommonDeleteFail obj = new V2CommonDeleteFail();
            obj.setId((String)programIds.get(i));
            obj.setReason(var31.getMessage());
            failList.add(obj);
         }
      }

      if (notiDataList.size() > 0) {
         MailUtil.sendContentScheduleEventMail(notiDataList, "02");
      }

      result.setSuccessList(successList);
      result.setFailList(failList);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Add Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public V2CommonResultResource deletePermanently(V2ScheduleDeleteParam resource) throws Exception {
      boolean flag = false;
      List programIds = resource.getIds();

      for(int i = 0; i < programIds.size(); ++i) {
         if (!V2ContentScheduleValidator.isValidActionAsScheduleEditor((String)programIds.get(i), RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE)) {
            flag = true;
            break;
         }

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE, (String)programIds.get(i));
         } catch (Exception var21) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE);
      }

      UserContainer userContainer = SecurityUtils.getUserContainer();
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      User loginUser = SecurityUtils.getLoginUser();
      MenuEntity resultMenu = new MenuEntity();
      V2CommonResultResource result = new V2CommonResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      resultMenu.setMenuName("scheduleTab");
      ScheduleInterface infoDao = DAOFactory.getScheduleInfoImpl("PREMIUM");
      new ArrayList();
      List notiDataList = new ArrayList();

      for(int i = 0; i < programIds.size(); ++i) {
         try {
            NotificationData notiData = null;

            try {
               ProgramEntity program = scheduleInfo.getProgram((String)programIds.get(i));
               notiData = new NotificationData();
               notiData.setName(program.getProgram_name());
               notiData.setOrgName(program.getProgram_group_name());
               notiData.setUserName(loginUser.getUser_id());
            } catch (Exception var19) {
               this.logger.error(var19);
            }

            if (!infoDao.deleteSchedulePerm((String)programIds.get(i), userContainer.getUser().getUser_id(), "")) {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
            }

            try {
               if (notiData != null) {
                  notiDataList.add(notiData);
               } else {
                  this.logger.error("notiData is null");
               }
            } catch (Exception var18) {
               this.logger.error(var18);
            }

            successList.add(programIds.get(i));
         } catch (Exception var20) {
            this.logger.error(var20);
            V2CommonDeleteFail obj = new V2CommonDeleteFail();
            obj.setId((String)programIds.get(i));
            obj.setReason(var20.getMessage());
            failList.add(obj);
         }
      }

      if (notiDataList.size() > 0) {
         MailUtil.sendContentScheduleEventMail(notiDataList, "04");
      }

      result.setSuccessList(successList);
      result.setFailList(failList);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public TTV2FrameTemplateResource createUserDefinedFrameTemplate(TTV2FrameTemplateResource resource) throws Exception {
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      FrameTemplateEntity entity = this.composeFrameTemplateEntity(resource);
      long templateId = scheduleInfo.createFrameTemplate(entity);
      if (templateId < 0L) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SCHEDULE_FRAME_TEMPLATE_UPDATE);
      } else {
         FrameTemplateEntity newEntity = scheduleInfo.getTemplateEntity(templateId);
         if (newEntity == null) {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SCHEDULE_FRAME_TEMPLATE_NOT_FOUND_UPDATE);
         } else {
            return this.parseFrameTemplateEntity(newEntity);
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public List getUserDefinedFrameTemplates(int resolutionWidth, int resolutionHeight) throws SQLException {
      List resources = new ArrayList();
      List frameInfos = new ArrayList();
      frameInfos.add(this.createDefaultFrameInfo("Frame 1", "fixed", 0.0F, 0.0F, 100.0F, 100.0F));
      TTV2FrameTemplateResource fixedFrame = this.createDefaultFrameTemplateResource("fixedFrame0", "fixed", resolutionWidth, resolutionHeight);
      fixedFrame.setFrameInfos(frameInfos);
      resources.add(fixedFrame);
      frameInfos = new ArrayList();
      frameInfos.add(this.createDefaultFrameInfo("Frame 1", "fixed", 0.0F, 0.0F, 100.0F, 50.0F));
      frameInfos.add(this.createDefaultFrameInfo("Frame 2", "fixed", 0.0F, 50.0F, 100.0F, 50.0F));
      fixedFrame = this.createDefaultFrameTemplateResource("fixedFrame1", "fixed", resolutionWidth, resolutionHeight);
      fixedFrame.setFrameInfos(frameInfos);
      resources.add(fixedFrame);
      frameInfos = new ArrayList();
      frameInfos.add(this.createDefaultFrameInfo("Frame 1", "fixed", 0.0F, 0.0F, 50.0F, 100.0F));
      frameInfos.add(this.createDefaultFrameInfo("Frame 2", "fixed", 50.0F, 0.0F, 50.0F, 100.0F));
      fixedFrame = this.createDefaultFrameTemplateResource("fixedFrame2", "fixed", resolutionWidth, resolutionHeight);
      fixedFrame.setFrameInfos(frameInfos);
      resources.add(fixedFrame);
      frameInfos = new ArrayList();
      frameInfos.add(this.createDefaultFrameInfo("Frame 1", "fixed", 0.0F, 0.0F, 100.0F, 20.0F));
      frameInfos.add(this.createDefaultFrameInfo("Frame 2", "fixed", 0.0F, 20.0F, 100.0F, 60.0F));
      frameInfos.add(this.createDefaultFrameInfo("Frame 3", "fixed", 0.0F, 80.0F, 100.0F, 20.0F));
      fixedFrame = this.createDefaultFrameTemplateResource("fixedFrame3", "fixed", resolutionWidth, resolutionHeight);
      fixedFrame.setFrameInfos(frameInfos);
      resources.add(fixedFrame);
      frameInfos = new ArrayList();
      frameInfos.add(this.createDefaultFrameInfo("Frame 1", "fixed", 0.0F, 0.0F, 50.0F, 50.0F));
      frameInfos.add(this.createDefaultFrameInfo("Frame 2", "fixed", 50.0F, 0.0F, 50.0F, 50.0F));
      frameInfos.add(this.createDefaultFrameInfo("Frame 3", "fixed", 0.0F, 50.0F, 50.0F, 50.0F));
      frameInfos.add(this.createDefaultFrameInfo("Frame 4", "fixed", 50.0F, 50.0F, 50.0F, 50.0F));
      fixedFrame = this.createDefaultFrameTemplateResource("fixedFrame4", "fixed", resolutionWidth, resolutionHeight);
      fixedFrame.setFrameInfos(frameInfos);
      resources.add(fixedFrame);
      frameInfos = new ArrayList();
      frameInfos.add(this.createDefaultFrameInfo("Frame 1", "fixed", 0.0F, 0.0F, 30.0F, 70.0F));
      frameInfos.add(this.createDefaultFrameInfo("Frame 2", "fixed", 30.0F, 0.0F, 70.0F, 70.0F));
      frameInfos.add(this.createDefaultFrameInfo("Frame 3", "fixed", 0.0F, 70.0F, 100.0F, 30.0F));
      fixedFrame = this.createDefaultFrameTemplateResource("fixedFrame5", "fixed", resolutionWidth, resolutionHeight);
      fixedFrame.setFrameInfos(frameInfos);
      resources.add(fixedFrame);
      frameInfos = new ArrayList();
      frameInfos.add(this.createDefaultFrameInfo("Frame 1", "fixed", 0.0F, 0.0F, 70.0F, 70.0F));
      frameInfos.add(this.createDefaultFrameInfo("Frame 2", "fixed", 0.0F, 70.0F, 70.0F, 30.0F));
      frameInfos.add(this.createDefaultFrameInfo("Frame 3", "fixed", 70.0F, 0.0F, 30.0F, 100.0F));
      fixedFrame = this.createDefaultFrameTemplateResource("fixedFrame6", "fixed", resolutionWidth, resolutionHeight);
      fixedFrame.setFrameInfos(frameInfos);
      resources.add(fixedFrame);
      frameInfos = new ArrayList();
      frameInfos.add(this.createDefaultFrameInfo("Frame 2", "fixed", 0.0F, 30.0F, 70.0F, 70.0F));
      frameInfos.add(this.createDefaultFrameInfo("Frame 1", "fixed", 0.0F, 0.0F, 100.0F, 30.0F));
      frameInfos.add(this.createDefaultFrameInfo("Frame 3", "fixed", 70.0F, 30.0F, 30.0F, 70.0F));
      fixedFrame = this.createDefaultFrameTemplateResource("fixedFrame7", "fixed", resolutionWidth, resolutionHeight);
      fixedFrame.setFrameInfos(frameInfos);
      resources.add(fixedFrame);
      frameInfos = new ArrayList();
      frameInfos.add(this.createDefaultFrameInfo("Frame 1", "fixed", 0.0F, 0.0F, 30.0F, 100.0F));
      frameInfos.add(this.createDefaultFrameInfo("Frame 2", "fixed", 30.0F, 0.0F, 70.0F, 70.0F));
      frameInfos.add(this.createDefaultFrameInfo("Frame 3", "fixed", 30.0F, 70.0F, 70.0F, 30.0F));
      fixedFrame = this.createDefaultFrameTemplateResource("fixedFrame8", "fixed", resolutionWidth, resolutionHeight);
      fixedFrame.setFrameInfos(frameInfos);
      resources.add(fixedFrame);
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String organization = userContainer.getUser().getOrganization();
      String resolution = this.getFormattedResolution(resolutionWidth, resolutionHeight);
      List frameTemplateList = scheduleInfo.getFrameTemplates("ALL", resolution, organization);
      Iterator var9 = frameTemplateList.iterator();

      while(var9.hasNext()) {
         FrameTemplateEntity templateEntity = (FrameTemplateEntity)var9.next();
         TTV2FrameTemplateResource resource = this.parseFrameTemplateEntity(templateEntity);
         resources.add(resource);
      }

      return resources;
   }

   private String getFormattedResolution(int width, int height) {
      return String.format("%d*%d", width, height);
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public TTV2FrameTemplateResource getUserDefinedFrameTemplate(long templateId) throws Exception {
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      FrameTemplateEntity entity = scheduleInfo.getTemplateEntity(templateId);
      if (entity == null) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_TEMPLATE_ID_MATCH_USER_ID_IN_SCHEDULE);
      } else {
         return this.parseFrameTemplateEntity(entity);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public TTV2FrameTemplateResource updateUserDefinedFrameTemplate(long templateId, TTV2FrameTemplateResource resource) throws Exception {
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      FrameTemplateEntity entity = this.composeFrameTemplateEntity(resource);
      entity.setTemplate_id(templateId);
      boolean result = scheduleInfo.updateTemplate(entity);
      if (!result) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SCHEDULE_FRAME_TEMPLATE_UPDATE);
      } else {
         FrameTemplateEntity modifiedEntity = scheduleInfo.getTemplateEntity(templateId);
         if (modifiedEntity == null) {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SCHEDULE_FRAME_TEMPLATE_NOT_FOUND);
         } else {
            return this.parseFrameTemplateEntity(modifiedEntity);
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public V2CommonDeleteResult deleteUserDefinedFrameTemplate(long templateId) {
      V2CommonDeleteResult result = new V2CommonDeleteResult();
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      List deletedIds = new ArrayList();
      List deleteFails = new ArrayList();
      V2CommonDeleteFail deleteFail = new V2CommonDeleteFail();

      try {
         if (scheduleInfo.deleteFrameTemplate(templateId)) {
            deletedIds.add(String.valueOf(templateId));
            result.setDeletedSuccessList(deletedIds);
         } else {
            deleteFail.setId(String.valueOf(templateId));
            deleteFail.setReason("Fail to delete frame template.");
            deleteFails.add(deleteFail);
            result.setDeletedFailList(deleteFails);
         }
      } catch (Exception var9) {
         this.logger.error(var9);
      }

      return result;
   }

   private FrameTemplateEntity composeFrameTemplateEntity(TTV2FrameTemplateResource resource) {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      TTV2DisplayResolution resolution = resource.getResolution();
      String templateType = resource.getTemplateType();
      String templateName = resource.getTemplateName();
      StringBuilder templateData = new StringBuilder();
      int frameCount;
      String orientationType;
      String frameName;
      String ep_x;
      String ep_y;
      String orientationType;
      if (!templateType.equals("CUSTOM")) {
         List lineInfos = resource.getLineInfos();
         List frameInfos = resource.getFrameInfos();
         if (lineInfos == null) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_FIXED_TYPE_LINE_INFO_REQUIRED);
         }

         if (lineInfos.size() > 3) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_FIXED_TYPE_LINE_INFO_ITEM_COUNT_EXCEEDED);
         }

         int lineCount = lineInfos.size();

         TTV2LayoutDivisionLineInfo lineInfo;
         for(frameCount = 0; frameCount < lineCount; ++frameCount) {
            lineInfo = (TTV2LayoutDivisionLineInfo)lineInfos.get(frameCount);
            orientationType = lineInfo.getOrientationType();
            if (orientationType != null && !orientationType.isEmpty()) {
               double sX;
               double eX;
               if ("HORIZONTAL".equals(orientationType)) {
                  sX = lineInfo.getStartY();
                  eX = lineInfo.getEndY();
                  if (sX != eX) {
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_INVALID_HORIZONTAL_LINE_INFO, new String[]{String.valueOf(frameCount)});
                  }
               } else {
                  if (!"VERTICAL".equals(orientationType)) {
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_INVALID_ORIENTATION_TYPE, new String[]{String.valueOf(frameCount)});
                  }

                  sX = lineInfo.getStartX();
                  eX = lineInfo.getEndX();
                  if (sX != eX) {
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_INVALID_VERTICAL_LINE_INFO, new String[]{String.valueOf(frameCount)});
                  }
               }
            }
         }

         for(frameCount = 0; frameCount < lineCount; ++frameCount) {
            lineInfo = (TTV2LayoutDivisionLineInfo)lineInfos.get(frameCount);
            orientationType = String.valueOf(lineInfo.getStartX());
            frameName = String.valueOf(lineInfo.getStartY());
            ep_x = String.valueOf(lineInfo.getEndX());
            ep_y = String.valueOf(lineInfo.getEndY());
            orientationType = lineInfo.getOrientationType().equals("HORIZONTAL") ? "0" : "1";
            templateData.append(orientationType).append(",").append(frameName).append(",").append(ep_x).append(",").append(ep_y).append(",").append(orientationType);
            if (frameCount < lineCount - 1) {
               templateData.append("~");
            }
         }

         templateData.append("*$*");
         if (frameInfos == null || frameInfos.isEmpty()) {
            String resolutionStr = this.getFormattedResolution(resolution.getWidth(), resolution.getHeight());
            frameInfos = this.createFrameInfosByLineInfos(resolutionStr, lineInfos);
         }

         frameCount = frameInfos.size();

         for(int i = 0; i < frameCount; ++i) {
            TTV2LayoutDivisionFrameInfo frameInfo = (TTV2LayoutDivisionFrameInfo)frameInfos.get(i);
            frameName = frameInfo.getFrameName().isEmpty() ? "Frame " + (i + 1) : frameInfo.getFrameName();
            templateData.append(frameName);
            templateData.append(i == 0 ? ",true" : ",false");
            if (i < frameCount - 1) {
               templateData.append("*@*");
            }
         }
      } else {
         label149: {
            String resolutionW = String.valueOf(resolution.getWidth());
            String resolutionH = String.valueOf(resolution.getHeight());
            templateData.append("0_true_0_0_0_").append(resolutionW).append("_").append(resolutionH).append("_CustomLayout%%%").append("Frame").append(" 0|");
            List frameInfos = resource.getFrameInfos();
            if (frameInfos != null && (frameInfos == null || !frameInfos.isEmpty())) {
               frameCount = 0;

               while(true) {
                  if (frameCount >= frameInfos.size()) {
                     break label149;
                  }

                  TTV2LayoutDivisionFrameInfo frameInfo = (TTV2LayoutDivisionFrameInfo)frameInfos.get(frameCount);
                  orientationType = String.valueOf(frameInfo.getIndex());
                  frameName = String.valueOf(frameInfo.getX());
                  ep_x = String.valueOf(frameInfo.getY());
                  ep_y = String.valueOf(frameInfo.getWidth());
                  orientationType = String.valueOf(frameInfo.getHeight());
                  String frameName = frameInfo.getFrameName().isEmpty() ? "Frame " + orientationType : frameInfo.getFrameName();
                  templateData.append(orientationType).append("_false_").append(orientationType).append("_").append(frameName).append("_").append(ep_x).append("_").append(ep_y).append("_").append(orientationType).append("_CustomLayout%%%").append(frameName);
                  if (frameCount < frameInfos.size() - 1) {
                     templateData.append("|");
                  }

                  ++frameCount;
               }
            }

            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_FRAME_INFO_LIST_REQUIRED);
         }
      }

      FrameTemplateEntity entity = new FrameTemplateEntity();
      if (templateType.equals("CUSTOM")) {
         entity.setTemplate_type("custom");
      } else if (templateType.equals("CUSTOM_FIXED")) {
         entity.setTemplate_type("customFixed");
      } else if (templateType.equals("FIXED")) {
         entity.setTemplate_type("fixed");
      }

      entity.setTemplate_name(templateName);
      entity.setTemplate_data(templateData.toString());
      entity.setResolution(this.getFormattedResolution(resolution.getWidth(), resolution.getHeight()));
      entity.setOrganization(userContainer.getUser().getOrganization());
      entity.setCreate_user_id(userContainer.getUser().getUser_id());
      return entity;
   }

   private List createFrameInfosByLineInfos(String resolutionStr, List lineInfos) {
      ArrayList customFrame = new ArrayList();
      long templateId = Long.MIN_VALUE;
      customFrame.add(this.createFrameMap(templateId, "", "customFixed", resolutionStr, 0.0F, 0.0F, 100.0F, 100.0F));

      String templateType;
      label63:
      for(int i = 0; i < lineInfos.size(); ++i) {
         TTV2LayoutDivisionLineInfo lineInfo = (TTV2LayoutDivisionLineInfo)lineInfos.get(i);
         float[] pointS = new float[]{(float)lineInfo.getStartX(), (float)lineInfo.getStartY()};
         float[] pointE = new float[]{(float)lineInfo.getEndX(), (float)lineInfo.getEndY()};
         templateType = "HORIZONTAL".equals(lineInfo.getOrientationType()) ? "horizontal" : "vertical";
         ArrayList frameDataMaps = (ArrayList)customFrame.clone();
         customFrame.clear();
         int frameCount = 1;
         Iterator var13 = frameDataMaps.iterator();

         while(true) {
            while(true) {
               if (!var13.hasNext()) {
                  continue label63;
               }

               LinkedHashMap map = (LinkedHashMap)var13.next();
               float x = (Float)map.get("x");
               float y = (Float)map.get("y");
               float w = (Float)map.get("width");
               float h = (Float)map.get("height");
               float[] position = this.returnFramePostion(x, y, w, h, templateType, pointS, pointE);
               String frameName;
               if (position[0] != -1.0F && position[1] != -1.0F && position[2] != -1.0F && position[3] != -1.0F) {
                  if (templateType.equals("horizontal")) {
                     frameName = "Frame " + frameCount;
                     customFrame.add(this.createFrameMap(templateId, frameName, "customFixed", resolutionStr, x, y, w, h - (position[1] - y)));
                     ++frameCount;
                     frameName = "Frame " + frameCount;
                     customFrame.add(this.createFrameMap(templateId, frameName, "customFixed", resolutionStr, x, position[1], w, h - (position[1] - y)));
                     ++frameCount;
                  } else {
                     frameName = "Frame " + frameCount;
                     customFrame.add(this.createFrameMap(templateId, frameName, "customFixed", resolutionStr, x, y, w - (position[0] - x), h));
                     ++frameCount;
                     frameName = "Frame " + frameCount;
                     customFrame.add(this.createFrameMap(templateId, frameName, "customFixed", resolutionStr, position[0], y, w - (position[0] - x), h));
                     ++frameCount;
                  }
               } else {
                  frameName = "Frame " + frameCount;
                  customFrame.add(this.createFrameMap(templateId, frameName, "customFixed", resolutionStr, x, y, w, h));
                  ++frameCount;
               }
            }
         }
      }

      if (!customFrame.isEmpty()) {
         List frameInfos = new ArrayList();

         for(int i = 0; i < customFrame.size(); ++i) {
            LinkedHashMap map = (LinkedHashMap)customFrame.get(i);
            TTV2LayoutDivisionFrameInfo frameInfo = new TTV2LayoutDivisionFrameInfo();
            frameInfo.setIndex(i);
            frameInfo.setX((double)(Float)map.get("x"));
            frameInfo.setY((double)(Float)map.get("y"));
            frameInfo.setWidth((double)(Float)map.get("width"));
            frameInfo.setHeight((double)(Float)map.get("height"));
            templateType = (String)map.get("templateType");
            if (templateType.equals("custom")) {
               frameInfo.setFrameType("CUSTOM");
            } else if (templateType.equals("customFixed")) {
               frameInfo.setFrameType("CUSTOM_FIXED");
            } else if (templateType.equals("fixed")) {
               frameInfo.setFrameType("FIXED");
            } else {
               frameInfo.setFrameType("FIXED");
            }

            frameInfo.setFrameName((String)map.get("frameName"));
            frameInfos.add(frameInfo);
         }

         return frameInfos;
      } else {
         return null;
      }
   }

   private String getFormattedResolution(double width, double height) {
      return this.getFormattedResolution((int)width, (int)height);
   }

   private TTV2FrameTemplateResource parseFrameTemplateEntity(FrameTemplateEntity e) {
      TTV2FrameTemplateResource resource = new TTV2FrameTemplateResource();
      resource.setTemplateId(e.getTemplate_id());
      resource.setTemplateName(e.getTemplate_name());
      if (e.getTemplate_type().equals("custom")) {
         resource.setTemplateType("CUSTOM");
      } else if (e.getTemplate_type().equals("customFixed")) {
         resource.setTemplateType("CUSTOM_FIXED");
      } else if (e.getTemplate_type().equals("fixed")) {
         resource.setTemplateType("FIXED");
      }

      if (!e.getResolution().isEmpty()) {
         String[] resolutionStr = e.getResolution().split("\\*");
         double resolutionW = Double.parseDouble(resolutionStr[0]);
         double resolutionH = Double.parseDouble(resolutionStr[1]);
         TTV2DisplayResolution resolution = new TTV2DisplayResolution();
         resolution.setWidth(resolutionW);
         resolution.setHeight(resolutionH);
         resource.setResolution(resolution);
      }

      String templateData = e.getTemplate_data();
      String[] strBlocks;
      String resolutionStr;
      if (e.getTemplate_type().equals("custom")) {
         strBlocks = templateData.split("\\|");
         List frameInfos = new ArrayList();

         for(int i = 1; i < strBlocks.length; ++i) {
            TTV2LayoutDivisionFrameInfo frameInfo = new TTV2LayoutDivisionFrameInfo();
            resolutionStr = strBlocks[i];
            String[] frameBlockSplit = resolutionStr.split("_");
            frameInfo.setIndex(Integer.parseInt(frameBlockSplit[0]));
            frameInfo.setX(Double.parseDouble(frameBlockSplit[3]));
            frameInfo.setY(Double.parseDouble(frameBlockSplit[4]));
            frameInfo.setWidth(Double.parseDouble(frameBlockSplit[5]));
            frameInfo.setHeight(Double.parseDouble(frameBlockSplit[6]));
            frameInfo.setFrameType("CUSTOM");
            frameInfo.setFrameName(frameBlockSplit[7].split("%%%")[1]);
            frameInfos.add(frameInfo);
         }

         if (!frameInfos.isEmpty()) {
            resource.setFrameInfos(frameInfos);
         }
      } else {
         strBlocks = templateData.split("\\*\\$\\*");
         if (strBlocks.length > 1) {
            String[] lineDataSet = strBlocks[0].split("~");
            String[] frameDataSet = strBlocks[1].split("\\*@\\*");
            Long templateId = e.getTemplate_id();
            resolutionStr = e.getResolution();
            List lineInfos = new ArrayList();
            ArrayList customFrame = new ArrayList();
            customFrame.add(this.createFrameMap(templateId, "", "customFixed", resolutionStr, 0.0F, 0.0F, 100.0F, 100.0F));
            int lineCount = 1;
            String[] var12 = lineDataSet;
            int i = lineDataSet.length;

            for(int var14 = 0; var14 < i; ++var14) {
               String lineData = var12[var14];
               String[] coordSet = lineData.split(",");
               if (coordSet.length > 4) {
                  float[] pointS = new float[]{Float.valueOf(coordSet[0]), Float.valueOf(coordSet[1])};
                  float[] pointE = new float[]{Float.valueOf(coordSet[2]), Float.valueOf(coordSet[3])};
                  String orientation = coordSet[4].equals("0") ? "horizontal" : "vertical";
                  ArrayList frameDataMaps = (ArrayList)customFrame.clone();
                  customFrame.clear();
                  int frameCount = 1;
                  Iterator var22 = frameDataMaps.iterator();

                  while(true) {
                     while(var22.hasNext()) {
                        LinkedHashMap map = (LinkedHashMap)var22.next();
                        float x = (Float)map.get("x");
                        float y = (Float)map.get("y");
                        float w = (Float)map.get("width");
                        float h = (Float)map.get("height");
                        float[] position = this.returnFramePostion(x, y, w, h, orientation, pointS, pointE);
                        String frameName;
                        if (position[0] != -1.0F && position[1] != -1.0F && position[2] != -1.0F && position[3] != -1.0F) {
                           if (orientation.equals("horizontal")) {
                              frameName = frameDataSet[frameCount - 1].split(",")[0];
                              frameName = frameName.isEmpty() ? "Frame " + frameCount : frameName;
                              customFrame.add(this.createFrameMap(templateId, frameName, "customFixed", resolutionStr, x, y, w, h - (position[1] - y)));
                              ++frameCount;
                              frameName = frameDataSet[frameCount - 1].split(",")[0];
                              frameName = frameName.isEmpty() ? "Frame " + frameCount : frameName;
                              customFrame.add(this.createFrameMap(templateId, frameName, "customFixed", resolutionStr, x, position[1], w, h - (position[1] - y)));
                              ++frameCount;
                           } else {
                              frameName = frameDataSet[frameCount - 1].split(",")[0];
                              frameName = frameName.isEmpty() ? "Frame " + frameCount : frameName;
                              customFrame.add(this.createFrameMap(templateId, frameName, "customFixed", resolutionStr, x, y, w - (position[0] - x), h));
                              ++frameCount;
                              frameName = frameDataSet[frameCount - 1].split(",")[0];
                              frameName = frameName.isEmpty() ? "Frame " + frameCount : frameName;
                              customFrame.add(this.createFrameMap(templateId, frameName, "customFixed", resolutionStr, position[0], y, w - (position[0] - x), h));
                              ++frameCount;
                           }
                        } else {
                           frameName = frameDataSet[frameCount - 1].split(",")[0];
                           frameName = frameName.isEmpty() ? "Frame " + frameCount : frameName;
                           customFrame.add(this.createFrameMap(templateId, frameName, "customFixed", resolutionStr, x, y, w, h));
                           ++frameCount;
                        }
                     }

                     TTV2LayoutDivisionLineInfo lineInfo = new TTV2LayoutDivisionLineInfo();
                     lineInfo.setIndex(lineCount++);
                     lineInfo.setStartX(Double.parseDouble(coordSet[0]));
                     lineInfo.setStartY(Double.parseDouble(coordSet[1]));
                     lineInfo.setEndX(Double.parseDouble(coordSet[2]));
                     lineInfo.setEndY(Double.parseDouble(coordSet[3]));
                     lineInfo.setOrientationType(coordSet[4].equals("0") ? "HORIZONTAL" : "VERTICAL");
                     lineInfos.add(lineInfo);
                     break;
                  }
               }
            }

            if (!lineInfos.isEmpty()) {
               resource.setLineInfos(lineInfos);
            }

            if (!customFrame.isEmpty()) {
               List frameInfos = new ArrayList();

               for(i = 0; i < customFrame.size(); ++i) {
                  LinkedHashMap map = (LinkedHashMap)customFrame.get(i);
                  TTV2LayoutDivisionFrameInfo frameInfo = new TTV2LayoutDivisionFrameInfo();
                  frameInfo.setIndex(i);
                  frameInfo.setX((double)(Float)map.get("x"));
                  frameInfo.setY((double)(Float)map.get("y"));
                  frameInfo.setWidth((double)(Float)map.get("width"));
                  frameInfo.setHeight((double)(Float)map.get("height"));
                  String templateType = (String)map.get("templateType");
                  if (templateType.equals("custom")) {
                     frameInfo.setFrameType("CUSTOM");
                  } else if (templateType.equals("customFixed")) {
                     frameInfo.setFrameType("CUSTOM_FIXED");
                  } else if (templateType.equals("fixed")) {
                     frameInfo.setFrameType("FIXED");
                  } else {
                     frameInfo.setFrameType("FIXED");
                  }

                  frameInfo.setFrameName((String)map.get("frameName"));
                  frameInfos.add(frameInfo);
               }

               if (!frameInfos.isEmpty()) {
                  resource.setFrameInfos(frameInfos);
               }
            }
         }
      }

      return resource;
   }

   private float[] returnFramePostion(float x, float y, float width, float height, String type, float[] sp, float[] ep) {
      float[] rtn = new float[4];
      float startix = -1.0F;
      float startiy = -1.0F;
      float endix = -1.0F;
      float endiy = -1.0F;
      float y2;
      if (type.equals("horizontal")) {
         if (sp[0] < ep[0]) {
            if (sp[0] <= x && ep[0] > x && y < sp[1] && sp[1] < y + height) {
               startix = x;
               startiy = sp[1];
            }

            y2 = x + width;
            if (ep[0] >= y2 && startix != -1.0F) {
               endix = y2;
               endiy = sp[1];
            }
         } else {
            if (sp[0] > x && ep[0] <= x && y < sp[1] && sp[1] < y + height) {
               startix = x;
               startiy = sp[1];
            }

            y2 = x + width;
            if (sp[0] >= y2 && startix != -1.0F) {
               endix = y2;
               endiy = sp[1];
            }
         }
      } else if (type.equals("vertical")) {
         if (sp[1] < ep[1]) {
            if (sp[1] <= y && ep[1] > y && x < sp[0] && sp[0] < x + width) {
               startix = sp[0];
               startiy = y;
            }

            y2 = y + height;
            if (ep[1] >= y2 && startix != -1.0F) {
               endix = sp[0];
               endiy = y2;
            }
         } else {
            if (sp[1] > y && ep[1] <= y && x < sp[0] && sp[0] < x + width) {
               startix = sp[0];
               startiy = y;
            }

            y2 = y + height;
            if (sp[1] >= y2 && startix != -1.0F) {
               endix = sp[0];
               endiy = y2;
            }
         }
      }

      rtn[0] = startix;
      rtn[1] = startiy;
      rtn[2] = endix;
      rtn[3] = endiy;
      return rtn;
   }

   private String getColor(int index) {
      String[] colors = new String[]{"#80cbff", "#6ee6a9", "#ff92b1", "#b22222", "#ff8c00", "#7B68EE"};
      if (index >= colors.length) {
         index %= colors.length;
      }

      return colors[index];
   }

   private String getInputSource(String inputsource) {
      byte var3 = -1;
      switch(inputsource.hashCode()) {
      case 52:
         if (inputsource.equals("4")) {
            var3 = 4;
         }
         break;
      case 56:
         if (inputsource.equals("8")) {
            var3 = 5;
         }
         break;
      case 1569:
         if (inputsource.equals("12")) {
            var3 = 3;
         }
         break;
      case 1570:
         if (inputsource.equals("13")) {
            var3 = 21;
         }
         break;
      case 1571:
         if (inputsource.equals("14")) {
            var3 = 22;
         }
         break;
      case 1598:
         if (inputsource.equals("20")) {
            var3 = 0;
         }
         break;
      case 1602:
         if (inputsource.equals("24")) {
            var3 = 2;
         }
         break;
      case 1629:
         if (inputsource.equals("30")) {
            var3 = 1;
         }
         break;
      case 1630:
         if (inputsource.equals("31")) {
            var3 = 20;
         }
         break;
      case 1631:
         if (inputsource.equals("32")) {
            var3 = 6;
         }
         break;
      case 1632:
         if (inputsource.equals("33")) {
            var3 = 9;
         }
         break;
      case 1633:
         if (inputsource.equals("34")) {
            var3 = 13;
         }
         break;
      case 1634:
         if (inputsource.equals("35")) {
            var3 = 10;
         }
         break;
      case 1635:
         if (inputsource.equals("36")) {
            var3 = 14;
         }
         break;
      case 1636:
         if (inputsource.equals("37")) {
            var3 = 17;
         }
         break;
      case 1637:
         if (inputsource.equals("38")) {
            var3 = 18;
         }
         break;
      case 1668:
         if (inputsource.equals("48")) {
            var3 = 25;
         }
         break;
      case 1669:
         if (inputsource.equals("49")) {
            var3 = 11;
         }
         break;
      case 1691:
         if (inputsource.equals("50")) {
            var3 = 15;
         }
         break;
      case 1692:
         if (inputsource.equals("51")) {
            var3 = 12;
         }
         break;
      case 1693:
         if (inputsource.equals("52")) {
            var3 = 16;
         }
         break;
      case 1726:
         if (inputsource.equals("64")) {
            var3 = 19;
         }
         break;
      case 1784:
         if (inputsource.equals("80")) {
            var3 = 8;
         }
         break;
      case 1789:
         if (inputsource.equals("85")) {
            var3 = 23;
         }
         break;
      case 1821:
         if (inputsource.equals("96")) {
            var3 = 7;
         }
         break;
      case 1822:
         if (inputsource.equals("97")) {
            var3 = 24;
         }
         break;
      case 1824:
         if (inputsource.equals("99")) {
            var3 = 28;
         }
         break;
      case 48626:
         if (inputsource.equals("101")) {
            var3 = 26;
         }
         break;
      case 48627:
         if (inputsource.equals("102")) {
            var3 = 27;
         }
         break;
      case 1507423:
         if (inputsource.equals("1000")) {
            var3 = 29;
         }
      }

      switch(var3) {
      case 0:
         return "PC";
      case 1:
         return "BNC";
      case 2:
         return "DVI";
      case 3:
         return "AV";
      case 4:
         return "S-VIDEO";
      case 5:
         return "COMPONENT";
      case 6:
         return "MAGICINFO";
      case 7:
         return "MAGICINFO-LITE";
      case 8:
         return "PLUGIN_MODULE";
      case 9:
         return "HDMI1";
      case 10:
         return "HDMI2";
      case 11:
         return "HDMI3";
      case 12:
         return "HDMI4";
      case 13:
         return "HDMI1_PC";
      case 14:
         return "HDMI2_PC";
      case 15:
         return "HDMI3_PC";
      case 16:
         return "HDMI4_PC";
      case 17:
         return "DISPLAY_PORT";
      case 18:
         return "DISPLAY_PORT2";
      case 19:
         return "DTV";
      case 20:
         return "DVI_VIDEO";
      case 21:
         return "AV2";
      case 22:
         return "EXT";
      case 23:
         return "HD_BASE_T";
      case 24:
         return "WIFI";
      case 25:
         return "ATV";
      case 26:
         return "WEB_BROWSER";
      case 27:
         return "SAMSUNG_WORKSPACE";
      case 28:
         return "URL_LAUNCHER";
      case 29:
         return "PANEL_OFF";
      default:
         return "";
      }
   }

   private boolean checkInputSource(String inputsource) {
      byte var3 = -1;
      switch(inputsource.hashCode()) {
      case -1732246028:
         if (inputsource.equals("DISPLAY_PORT2")) {
            var3 = 18;
         }
         break;
      case -1523257200:
         if (inputsource.equals("URL_LAUNCHER")) {
            var3 = 28;
         }
         break;
      case -872551335:
         if (inputsource.equals("HDMI1_PC")) {
            var3 = 13;
         }
         break;
      case -872521544:
         if (inputsource.equals("HDMI2_PC")) {
            var3 = 14;
         }
         break;
      case -872491753:
         if (inputsource.equals("HDMI3_PC")) {
            var3 = 15;
         }
         break;
      case -872461962:
         if (inputsource.equals("HDMI4_PC")) {
            var3 = 16;
         }
         break;
      case -332973570:
         if (inputsource.equals("DISPLAY_PORT")) {
            var3 = 17;
         }
         break;
      case -198252672:
         if (inputsource.equals("MAGICINFO-LITE")) {
            var3 = 7;
         }
         break;
      case 2101:
         if (inputsource.equals("AV")) {
            var3 = 3;
         }
         break;
      case 2547:
         if (inputsource.equals("PC")) {
            var3 = 0;
         }
         break;
      case 65155:
         if (inputsource.equals("ATV")) {
            var3 = 25;
         }
         break;
      case 65181:
         if (inputsource.equals("AV2")) {
            var3 = 21;
         }
         break;
      case 65911:
         if (inputsource.equals("BNC")) {
            var3 = 1;
         }
         break;
      case 68038:
         if (inputsource.equals("DTV")) {
            var3 = 19;
         }
         break;
      case 68087:
         if (inputsource.equals("DVI")) {
            var3 = 2;
         }
         break;
      case 69121:
         if (inputsource.equals("EXT")) {
            var3 = 22;
         }
         break;
      case 2664151:
         if (inputsource.equals("WIDI")) {
            var3 = 24;
         }
         break;
      case 68595609:
         if (inputsource.equals("HDMI1")) {
            var3 = 9;
         }
         break;
      case 68595610:
         if (inputsource.equals("HDMI2")) {
            var3 = 10;
         }
         break;
      case 68595611:
         if (inputsource.equals("HDMI3")) {
            var3 = 11;
         }
         break;
      case 68595612:
         if (inputsource.equals("HDMI4")) {
            var3 = 12;
         }
         break;
      case 74707987:
         if (inputsource.equals("DVI_VIDEO")) {
            var3 = 20;
         }
         break;
      case 327400016:
         if (inputsource.equals("SAMSUNG_WORKSPACE")) {
            var3 = 27;
         }
         break;
      case 426427640:
         if (inputsource.equals("PLUGIN_MODULE")) {
            var3 = 8;
         }
         break;
      case 620789691:
         if (inputsource.equals("MAGICINFO")) {
            var3 = 6;
         }
         break;
      case 1305403508:
         if (inputsource.equals("PANEL_OFF")) {
            var3 = 29;
         }
         break;
      case 1386687709:
         if (inputsource.equals("COMPONENT")) {
            var3 = 5;
         }
         break;
      case 1624881929:
         if (inputsource.equals("HD_BASE_T")) {
            var3 = 23;
         }
         break;
      case 1803761917:
         if (inputsource.equals("WEB_BROWSER")) {
            var3 = 26;
         }
         break;
      case 2018338401:
         if (inputsource.equals("S-VIDEO")) {
            var3 = 4;
         }
      }

      switch(var3) {
      case 0:
      case 1:
      case 2:
      case 3:
      case 4:
      case 5:
      case 6:
      case 7:
      case 8:
      case 9:
      case 10:
      case 11:
      case 12:
      case 13:
      case 14:
      case 15:
      case 16:
      case 17:
      case 18:
      case 19:
      case 20:
      case 21:
      case 22:
      case 23:
      case 24:
      case 25:
      case 26:
      case 27:
      case 28:
      case 29:
         return true;
      default:
         return false;
      }
   }
}
