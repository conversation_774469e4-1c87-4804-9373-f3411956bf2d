package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.OpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupLayout;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupVwLayoutResponseData;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

public class GetVideowallLayoutUrlOpenApiMethod extends OpenApiMethod<DeviceGroupLayout, DeviceGroupVwLayoutResponseData> {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.repository.GetVideowallLayoutUrlOpenApiMethod.class);
  
  private final String token;
  
  private final String groupId;
  
  public GetVideowallLayoutUrlOpenApiMethod(RestTemplate restTemplate, String token, String groupId) {
    super(restTemplate);
    this.token = token;
    this.groupId = groupId;
  }
  
  protected String getOpenApiClassName() {
    return "PremiumDeviceService";
  }
  
  protected String getOpenApiMethodName() {
    return "getVWTInfoByGroupID";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("token", this.token);
    vars.put("groupId", this.groupId);
    return vars;
  }
  
  Class<DeviceGroupVwLayoutResponseData> getResponseClass() {
    return DeviceGroupVwLayoutResponseData.class;
  }
  
  DeviceGroupLayout convertResponseData(DeviceGroupVwLayoutResponseData responseData) {
    DeviceGroupLayout groupLayout;
    if (responseData.getCode().equals("0")) {
      groupLayout = responseData.getResponseClass();
    } else {
      logger.error("Error to get VWTInfoByGroupID, ID: " + this.groupId + ", Error: " + responseData.getErrorMessage());
      groupLayout = new DeviceGroupLayout();
    } 
    return groupLayout;
  }
}
