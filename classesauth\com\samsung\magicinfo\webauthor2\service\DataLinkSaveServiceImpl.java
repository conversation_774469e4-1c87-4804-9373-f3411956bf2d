package com.samsung.magicinfo.webauthor2.service;

import com.google.common.base.Optional;
import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.model.ContentSaveElements;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.FileInfo;
import com.samsung.magicinfo.webauthor2.model.LFDContent;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.datalink.DataLinkDescriptor;
import com.samsung.magicinfo.webauthor2.model.datalink.LFTContent;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.repository.DlkLocalFileRepository;
import com.samsung.magicinfo.webauthor2.repository.OpenAPIDataLinkRepository;
import com.samsung.magicinfo.webauthor2.service.CSDFileService;
import com.samsung.magicinfo.webauthor2.service.CidMappingService;
import com.samsung.magicinfo.webauthor2.service.ContentSaveService;
import com.samsung.magicinfo.webauthor2.service.DataLinkSaveService;
import com.samsung.magicinfo.webauthor2.service.FileHashService;
import com.samsung.magicinfo.webauthor2.service.LFDContentService;
import com.samsung.magicinfo.webauthor2.service.datalink.AddDLKInfoXmlFactory;
import com.samsung.magicinfo.webauthor2.service.datalink.ConvertTableSearchAlgCreate;
import com.samsung.magicinfo.webauthor2.service.datalink.ConvertTableSearchAlgUpdate;
import com.samsung.magicinfo.webauthor2.service.datalink.DLKXmlFactory;
import com.samsung.magicinfo.webauthor2.service.upload.ContentMIPUploadService;
import com.samsung.magicinfo.webauthor2.util.PlayTimeUtil;
import com.samsung.magicinfo.webauthor2.util.UserData;
import com.samsung.magicinfo.webauthor2.xml.dlkinfo.ConvertTableListType;
import com.samsung.magicinfo.webauthor2.xml.transferfile.response.TransferFilesResponseType;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;
import org.springframework.stereotype.Service;

@Service
public class DataLinkSaveServiceImpl implements DataLinkSaveService {
  private static final String DEFAULT_FILE_ID = "00000000-0000-0000-0000-000000000000";
  
  private final OpenAPIDataLinkRepository misDataLinkRepository;
  
  private final DlkLocalFileRepository dlkLocalFileRepository;
  
  private final LFDContentService lfdContentService;
  
  private final FileHashService fileHashService;
  
  private final CidMappingService cidMappingService;
  
  private final CSDFileService csdFileService;
  
  private final ContentSaveService contentSaveService;
  
  private final ContentMIPUploadService contentMIPUploadService;
  
  private final DLKXmlFactory dlkXmlFactory;
  
  private final AddDLKInfoXmlFactory addDLKInfoXmlFactory;
  
  private final UserData userData;
  
  private final ContentSaveElements contentSaveElements;
  
  @Inject
  public DataLinkSaveServiceImpl(OpenAPIDataLinkRepository misDataLinkRepository, DlkLocalFileRepository dlkLocalFileRepository, LFDContentService lfdContentService, FileHashService fileHashService, CidMappingService cidMappingService, CSDFileService csdFileService, ContentSaveService contentSaveService, ContentMIPUploadService contentMIPUploadService, DLKXmlFactory dlkXmlFactory, AddDLKInfoXmlFactory addDLKInfoXmlFactory, UserData userData, ContentSaveElements contentSaveElements) {
    this.misDataLinkRepository = misDataLinkRepository;
    this.dlkLocalFileRepository = dlkLocalFileRepository;
    this.lfdContentService = lfdContentService;
    this.fileHashService = fileHashService;
    this.cidMappingService = cidMappingService;
    this.csdFileService = csdFileService;
    this.contentSaveService = contentSaveService;
    this.contentMIPUploadService = contentMIPUploadService;
    this.dlkXmlFactory = dlkXmlFactory;
    this.addDLKInfoXmlFactory = addDLKInfoXmlFactory;
    this.userData = userData;
    this.contentSaveElements = contentSaveElements;
  }
  
  public Path saveLocalDataLinkDescriptor(DataLinkDescriptor dataLinkDescriptor) {
    Optional<LFDContent> optLFTContent = Optional.fromNullable(this.lfdContentService.getLFDContent(dataLinkDescriptor.getLftContentId()));
    if (optLFTContent.isPresent()) {
      LFDContent lftContent = (LFDContent)optLFTContent.get();
      dataLinkDescriptor.setLftContent(new LFTContent(dataLinkDescriptor.getLftContentId(), lftContent.getFileId(), lftContent
            .getFileName(), lftContent.getFileHash(), lftContent.getFileSize()));
      dataLinkDescriptor.setPlayTime(lftContent.getLfdInfo().getPlayTime());
    } 
    String xml = this.dlkXmlFactory.marshal(dataLinkDescriptor);
    Optional<ConvertTableListType> optConvertTableList = Optional.fromNullable(this.userData.popDlkConvertTableList());
    if (optConvertTableList.isPresent()) {
      ConvertTableListType actualConvertTableListType = (ConvertTableListType)optConvertTableList.get();
      updateDataLinkDescriptor(dataLinkDescriptor, actualConvertTableListType);
      this.userData.putDlkConvertTableList(actualConvertTableListType);
    } else {
      this.userData.putDlkConvertTableList(createConvertTableList(dataLinkDescriptor));
    } 
    this.dlkLocalFileRepository.saveDLKFile(xml, dataLinkDescriptor.getDlkName(), this.userData.getWorkspaceFolderName());
    Path pathTofile = Paths.get(this.dlkLocalFileRepository.getDLKFile(this.userData.getWorkspaceFolderName()).getAbsolutePath(), new String[0]);
    dataLinkDescriptor.setOptDLKFileInfo(FileInfo.fromFile(dataLinkDescriptor.getOptDLKContentId(), pathTofile.toFile()));
    String cid = this.cidMappingService.cidMapping(dataLinkDescriptor.getOptDLKContentId());
    MediaSource dlkMediaSource = new MediaSource();
    dlkMediaSource.setMediaType(MediaType.DLK);
    dlkMediaSource.setFileType("dlk");
    dlkMediaSource.setContentName(dataLinkDescriptor.getDlkContentName());
    dlkMediaSource.setFileName(dataLinkDescriptor.getDlkName() + ".DLK");
    dlkMediaSource.setMediaSize(dataLinkDescriptor.getOptDLKFileInfo().getSize());
    dlkMediaSource.setMediaDuration(PlayTimeUtil.convertPlayTime(dataLinkDescriptor.getPlayTime()).doubleValue());
    dlkMediaSource.setPath(pathTofile.toString());
    dlkMediaSource.setContentId(cid);
    dlkMediaSource.setFileId(Strings.isNullOrEmpty(dataLinkDescriptor.getOptDLKFileInfo().getFileId()) ? "00000000-0000-0000-0000-000000000000" : dataLinkDescriptor.getOptDLKFileInfo().getFileId());
    dlkMediaSource.setFileHash(dataLinkDescriptor.getOptDLKFileInfo().getFileHash());
    dlkMediaSource.setMediaHeight(Integer.parseInt(dataLinkDescriptor.getHeight()));
    dlkMediaSource.setMediaWidth(Integer.parseInt(dataLinkDescriptor.getWidth()));
    List<MediaSource> listMediaSources = new ArrayList<>();
    listMediaSources.add(dlkMediaSource);
    this.contentSaveElements.setMediaSources(listMediaSources);
    this.contentSaveElements.setProjectName(dlkMediaSource.getFileName());
    this.contentSaveElements.setContentName(dlkMediaSource.getContentName());
    this.contentSaveElements.setWidth(Integer.parseInt(dataLinkDescriptor.getWidth()));
    this.contentSaveElements.setHeight(Integer.parseInt(dataLinkDescriptor.getHeight()));
    this.contentSaveElements.setPlayerType(DeviceType.iPLAYER);
    this.contentSaveElements.setXml(xml);
    return pathTofile;
  }
  
  public String upload(DataLinkDescriptor dataLinkDescriptor) {
    saveLocalDataLinkDescriptor(dataLinkDescriptor);
    this.contentSaveService.addThumbnailMediaSource();
    String cid = this.contentSaveElements.getProjectContentId();
    String csd = this.csdFileService.generateCSD(this.contentSaveElements, this.userData.getUserId());
    TransferFilesResponseType csdResp = this.csdFileService.postProjectCsdToMips(csd, cid, dataLinkDescriptor.getLftContentId());
    this.csdFileService.updateUserDataSaveElements(csdResp, this.contentSaveElements);
    this.fileHashService.fileHashRefresh(this.contentSaveElements.getXml());
    this.contentMIPUploadService.upload(this.contentSaveElements);
    int actualDlkVersion = Integer.parseInt(this.contentSaveElements.getVersion());
    String dlkVersionId = String.valueOf(actualDlkVersion + 1);
    List<String> notXmlContents = findNotXmlContentsIds(this.contentSaveElements.getMediaSources());
    addDlkInfo(cid, dlkVersionId, dataLinkDescriptor.getLftContentId(), notXmlContents);
    return cid;
  }
  
  public void addDlkInfo(String dlkContentId, String dlkVersionId, String lftContentId, List<String> contentsList) {
    ConvertTableListType convertTableListType = this.userData.popDlkConvertTableList();
    String convertTableXml = this.addDLKInfoXmlFactory.marshall(convertTableListType);
    this.misDataLinkRepository.addDlkInfo(dlkContentId, dlkVersionId, lftContentId, contentsList, convertTableXml);
  }
  
  private ConvertTableListType createConvertTableList(DataLinkDescriptor dataLinkDescriptor) {
    ConvertTableSearchAlgCreate convertTableSearchAlgCreate = new ConvertTableSearchAlgCreate();
    convertTableSearchAlgCreate.searchConvertTable(dataLinkDescriptor);
    return new ConvertTableListType((List)convertTableSearchAlgCreate.getResult());
  }
  
  private void updateDataLinkDescriptor(DataLinkDescriptor dataLinkDescriptor, ConvertTableListType convertTableListType) {
    ConvertTableSearchAlgUpdate convertTableSearchAlgUpdate = new ConvertTableSearchAlgUpdate(convertTableListType);
    convertTableSearchAlgUpdate.searchConvertTable(dataLinkDescriptor);
  }
  
  private List<String> findNotXmlContentsIds(List<MediaSource> sources) {
    List<String> notXmlContents = new ArrayList<>();
    for (MediaSource source : sources) {
      if (!Strings.isNullOrEmpty(source.getContentId()))
        notXmlContents.add(source.getContentId()); 
    } 
    return notXmlContents;
  }
}
