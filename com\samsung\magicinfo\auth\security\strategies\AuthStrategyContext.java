package com.samsung.magicinfo.auth.security.strategies;

import com.samsung.magicinfo.auth.security.strategies.model.AuthModel;

public class AuthStrategyContext {
   private AuthStrategy strategy;

   public AuthStrategyContext(AuthStrategy strategy) {
      super();
      this.strategy = strategy;
   }

   public AuthModel init(AuthModel authModel) {
      return this.strategy.init(authModel);
   }

   public AuthModel active(AuthModel authModel) {
      return this.strategy.active(authModel);
   }

   public boolean valid(AuthModel authModel) {
      return this.strategy.valid(authModel);
   }
}
