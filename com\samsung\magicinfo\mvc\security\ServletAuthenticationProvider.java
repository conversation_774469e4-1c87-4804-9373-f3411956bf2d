package com.samsung.magicinfo.mvc.security;

import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.mvc.security.exception.UserDeletedAuthenticationException;
import com.samsung.magicinfo.mvc.security.exception.UserNotApprovedAuthenticationException;
import com.samsung.magicinfo.mvc.security.exception.UserRejectedAuthenticationException;
import com.samsung.magicinfo.service.user.LDAPService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

@Service("servletAuthenticationProvider")
public class ServletAuthenticationProvider extends SimpleAuthenticationProvider {
   private static final String FAILED_USER_NOT_APPROVED = "Authentication failed: User '%s' is not approved";
   private static final String FAILED_USER_REJECTED_MESSAGE = "Authentication failed: User '%s' was rejected";
   private static final String FAILED_USER_DELETED_MESSAGE = "Authentication failed: User '%s' was deleted";
   private static final String UNEXPECTED_EXCEPTION_MESSAGE = "An unexpected exception ocurred when validating '%s'";
   private static final String IS_REJECT = "IS_REJECT";
   protected final UserInfo userInfo;

   @Autowired
   public ServletAuthenticationProvider(UserDetailsService userDetailsService, UserInfo userInfo, PasswordEncoder passwordEncoder, UserInfo userInfo1) {
      super(userDetailsService, passwordEncoder);
      this.userInfo = userInfo1;
   }

   protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
      super.additionalAuthenticationChecks(userDetails, authentication);
      if (this.isDeleted(userDetails)) {
         throw new UserDeletedAuthenticationException(String.format("Authentication failed: User '%s' was deleted", userDetails.getUsername()));
      } else if (this.isRejected(userDetails)) {
         throw new UserRejectedAuthenticationException(String.format("Authentication failed: User '%s' was rejected", userDetails.getUsername()));
      } else if (this.isNotApproved(userDetails)) {
         throw new UserNotApprovedAuthenticationException(String.format("Authentication failed: User '%s' is not approved", userDetails.getUsername()));
      }
   }

   private boolean isRejected(UserDetails userDetails) throws AuthenticationException {
      try {
         Map rejectMap = this.userInfo.getIsRejectByUserId(userDetails.getUsername());
         return !"N".equals((String)rejectMap.get("IS_REJECT"));
      } catch (Exception var3) {
         throw new InternalAuthenticationServiceException(String.format("An unexpected exception ocurred when validating '%s'", userDetails.getUsername()), var3);
      }
   }

   private boolean isDeleted(UserDetails userDetails) throws AuthenticationException {
      try {
         return !"N".equals(this.userInfo.getIsDeletedByUserId(userDetails.getUsername()));
      } catch (Exception var3) {
         throw new InternalAuthenticationServiceException(String.format("An unexpected exception ocurred when validating '%s'", userDetails.getUsername()), var3);
      }
   }

   private boolean isNotApproved(UserDetails userDetails) throws AuthenticationException {
      try {
         return !"Y".equals(this.userInfo.getIsApprovedByUserId(userDetails.getUsername()));
      } catch (Exception var3) {
         throw new InternalAuthenticationServiceException(String.format("An unexpected exception ocurred when validating '%s'", userDetails.getUsername()), var3);
      }
   }

   protected String getPassword(UsernamePasswordAuthenticationToken authentication) {
      String presentedPassword = authentication.getCredentials().toString();

      try {
         String ldapPassword = LDAPService.getLDAPPassword(authentication.getPrincipal().toString(), presentedPassword);
         return ldapPassword != null ? ldapPassword : presentedPassword;
      } catch (Exception var4) {
         throw new InternalAuthenticationServiceException("Error when veryfiing LDAP user", var4);
      }
   }
}
