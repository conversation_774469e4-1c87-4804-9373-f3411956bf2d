<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.samsung.magicinfo.framework.advertisement.dao.FaceAudienceBasedDaoMapper">

<sql id="where_deviceIdList">
	<if test="deviceIdList.length == 0">
		(DEVICE_ID='NONE' )
	</if>
	<if test="deviceIdList != null and deviceIdList.length > 0">
		<foreach item="item"  index="index" collection="deviceIdList" open="(" separator="OR" close=")">	
		DEVICE_ID = #{item}	
		</foreach>		
	</if>
</sql>

<sql id="castStartTimeToStringDate">
	START_TIME::DATE
</sql>

<sql id="castStartTimeToStringDate" databaseId="mssql">
	CONVERT(VARCHAR(10), START_TIME, 120)
</sql>
<sql id="castStartTimeToStringDate" databaseId="mysql">
	DATE_FORMAT(START_TIME, '%Y-%m-%d')
</sql>

<sql id="truncDayCurrDateMinusOneDayAndCastToDate">
	DATE_TRUNC('DAY',CURRENT_DATE - interval '1days')::DATE
</sql>
<sql id="truncDayCurrDateMinusOneDayAndCastToDate" databaseId="mssql">
	CONVERT(VARCHAR(10), DATEADD(dd, -1, DATEADD(DAY, DATEDIFF(DAY, 0, GETDATE()) , 0)), 120)
</sql>
<sql id="truncDayCurrDateMinusOneDayAndCastToDate" databaseId="mysql">
	DATE_FORMAT(NOW() - INTERVAL 1 DAY, '%Y-%m-%d')
</sql>

<sql id="conditionStartTimeEqToTruncYearCurrentDate">
	<choose>
	 	<when test="isThis">
	 		 AND START_TIME = DATE_TRUNC('YEAR',CURRENT_DATE)
	 	</when>
	 	<otherwise>
	 		  AND START_TIME = DATE_TRUNC('YEAR',CURRENT_DATE - interval '1 years')
	 	</otherwise>
	</choose>
</sql>
<sql id="conditionStartTimeEqToTruncYearCurrentDate" databaseId="mssql">
	<choose>
	 	<when test="isThis">
	 		 AND START_TIME = DATEADD(YEAR, DATEDIFF(YEAR, 0, GETDATE()) , 0)
	 	</when>
	 	<otherwise>
	 		  AND START_TIME = DATEADD(yy, -1, DATEADD(YEAR, DATEDIFF(YEAR, 0, GETDATE()) , 0))
	 	</otherwise>
	</choose>
</sql>

<sql id="conditionStartTimeEqToTruncYearCurrentDate" databaseId="mysql">
	<choose>
	 	<when test="isThis">
	 		 AND START_TIME = DATE_FORMAT(NOW(), '%Y-01-01')
	 	</when>
	 	<otherwise>
	 		  AND START_TIME = DATE_FORMAT(NOW() - INTERVAL 1 YEAR, '%Y-01-01')
	 	</otherwise>
	</choose>
</sql>


<sql id="conditionStartTimeToCurrentMonth">
	<choose>
		<when test="isThis">
	 		AND START_TIME = DATE_TRUNC('MONTH',CURRENT_DATE)
	 	</when>
		<otherwise>
		 	AND START_TIME = DATE_TRUNC('MONTH',CURRENT_DATE - interval '1 months')
		</otherwise>
	</choose>
</sql>

<sql id="conditionStartTimeToCurrentMonth" databaseId="mssql">
	<choose>
		<when test="isThis">
			AND START_TIME = DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0)
		</when>
		<otherwise>
			AND START_TIME = DATEADD(MONTH, DATEDIFF(MONTH, 0, DATEADD(MONTH, -1, GETDATE())), 0)
		</otherwise>
	</choose>
</sql>


<sql id="conditionYearStartTimeEqToCurrentYear">
	<choose>
	 	<when test="isThis">
	 		AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE) 
	 	</when>
	 	<otherwise>
	 		AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE - interval '1 years') 
	 	</otherwise>
	</choose>
</sql>

<sql id="conditionYearStartTimeEqToCurrentYear" databaseId="mssql">
	<choose>
	 	<when test="isThis">
	 		AND YEAR(START_TIME) = YEAR(GETDATE())
	 	</when>
	 	<otherwise>
	 		AND YEAR(START_TIME) = YEAR(GETDATE()) - 1
	 	</otherwise>
	</choose>
</sql>
<sql id="conditionYearStartTimeEqToCurrentYear" databaseId="mysql">
	<choose>
	 	<when test="isThis">
	 		AND YEAR(START_TIME) = YEAR(NOW()) 
	 	</when>
	 	<otherwise>
	 		AND YEAR(START_TIME) = YEAR(NOW()) - 1 
	 	</otherwise>
	</choose>
</sql>

<sql id="conditionMonthStartTimeEqCurrentMonth">
	<choose>
	 	<when test="isThis">
	 		AND EXTRACT(MONTH FROM START_TIME) = EXTRACT(MONTH FROM CURRENT_DATE)
	 	</when>
	 	<otherwise>
	 		AND EXTRACT(MONTH FROM START_TIME) = EXTRACT(MONTH FROM CURRENT_DATE - interval '1 months') 
	 	</otherwise>
	</choose>
</sql>
<sql id="conditionMonthStartTimeEqCurrentMonth" databaseId="mssql">
	<choose>
	 	<when test="isThis">
	 		AND MONTH(START_TIME) = MONTH(GETDATE())
	 	</when>
	 	<otherwise>
            AND MONTH(START_TIME) = CASE WHEN MONTH(GETDATE()) = 1 THEN 12 ELSE (MONTH(GETDATE()) - 1) END
	 	</otherwise>
	</choose>
</sql>
<sql id="conditionMonthStartTimeEqCurrentMonth" databaseId="mysql">
	<choose>
	 	<when test="isThis">
	 		AND MONTH(START_TIME) = MONTH(NOW())
	 	</when>
	 	<otherwise>
	 		AND MONTH(START_TIME) = MONTH(NOW()) -1 
	 	</otherwise>
	</choose>
</sql>

<sql id="conditionYearAndQuarterStartTimeEqCurrYearAndQuarter">
	<choose>
	 	<when test="isThis">
	 		AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE) AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE)
	 	</when>
	 	<otherwise>
	 		AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE - interval '3 months') AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE - interval '3 months')
	 	</otherwise>
	</choose>
</sql>
<sql id="conditionYearAndQuarterStartTimeEqCurrYearAndQuarter" databaseId="mssql">
	<choose>
	 	<when test="isThis">
	 		AND YEAR(START_TIME) = YEAR(GETDATE()) AND LOG_QUARTER = DATENAME(Quarter, CAST(CONVERT(VARCHAR(8), DATEADD(month, 0 ,GETDATE())) AS DATETIME))
	 	</when>
	 	<otherwise>
	 		AND YEAR(START_TIME) = YEAR(DATEADD(month, -3 ,GETDATE())) AND LOG_QUARTER = DATENAME(Quarter, CAST(CONVERT(VARCHAR(8), DATEADD(month, -3 ,GETDATE())) AS DATETIME))
	 	</otherwise>
	</choose>
</sql>
<sql id="conditionYearAndQuarterStartTimeEqCurrYearAndQuarter" databaseId="mysql">
	<choose>
	 	<when test="isThis">
	 		AND YEAR(START_TIME) = YEAR(NOW()) AND LOG_QUARTER = QUARTER(NOW())
	 	</when>
	 	<otherwise>
	 		AND YEAR(START_TIME) = YEAR(NOW()) AND LOG_QUARTER = QUARTER(NOW()) -1
	 	</otherwise>
	</choose>
</sql>

	 <insert id="addAudiencePopHourInfoList">
        INSERT INTO MI_STATISTICS_AMS_AUDIENCE_HOUR(CONTENT_ID, CONTENT_NAME, START_TIME, DEVICE_ID, DEVICE_NAME, GENDER, AGE, TOTAL_COUNT, DWELL_TIME, ATTENTION_COUNT, ATTENTION_TIME) VALUES
         <foreach collection="entities" open="(" close=")" separator="),(" item="entity" index="index">
            #{audienceKey.contentId}, #{entity.contentName}, #{entity.time}, #{deviceId}, #{entity.deviceName},#{audienceKey.gender}, #{audienceKey.age},
            #{entity.totalCount}, #{entity.dwellTime}, #{entity.attentionCount}, #{entity.attentionTime}
        </foreach>
    </insert>
    
    <insert id="addAudiencePopDayInfoList">
        INSERT INTO MI_STATISTICS_AMS_AUDIENCE_DAY (CONTENT_ID, CONTENT_NAME, START_TIME, DEVICE_ID, DEVICE_NAME, TOTAL_COUNT, DWELL_TIME
        , PLAY_DOW, WEEK_OF_YEAR, GENDER, AGE, ATTENTION_COUNT, ATTENTION_TIME)
        VALUES
        <foreach collection="map" open="(" close=")" separator="),(" item="item" index="key">
            #{key.contentId}, #{item.content_name}, #{day}, #{deviceId}, #{item.device_name}, #{item.total_count},
            #{item.dwell_time}, #{dow}, #{woy}, #{key.gender}, #{key.age}, #{item.attention_count}, #{item.attention_time}
        </foreach>
    </insert>
    
    <insert id="addAudiencePopMonthInfoList">
        INSERT INTO MI_STATISTICS_AMS_AUDIENCE_MONTH (CONTENT_ID, CONTENT_NAME, START_TIME, LOG_QUARTER, DEVICE_ID, DEVICE_NAME, TOTAL_COUNT, DWELL_TIME, GENDER, AGE, ATTENTION_COUNT, ATTENTION_TIME) VALUES
        <foreach collection="map" open="(" close=")" separator="),(" item="item" index="key">
            #{key.contentId}, #{item.content_name}, #{month}, #{quarter}, #{deviceId}, #{item.device_name}, #{item.total_count},
            #{item.dwell_time}, #{key.gender}, #{key.age}, #{item.attention_count}, #{item.attention_time}
        </foreach>
    </insert>
    
    <insert id="addAudiencePopYearInfoList">
        INSERT INTO MI_STATISTICS_AMS_AUDIENCE_YEAR( CONTENT_ID, CONTENT_NAME, START_TIME, DEVICE_ID , DEVICE_NAME, TOTAL_COUNT, DWELL_TIME, GENDER, AGE, ATTENTION_COUNT, ATTENTION_TIME ) VALUES
        <foreach collection="map" open="(" close=")" separator="),(" item="item" index="key">
            #{key.contentId}, #{item.content_name}, #{startTime}, #{deviceId}, #{item.device_name}, #{item.total_count},
            #{item.dwell_time}, #{key.gender}, #{key.age}, #{item.attention_count}, #{item.attention_time}
        </foreach>
    </insert>
    
    <select id="getAudiencePopDayInfo"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.AudienceInfoEntity">
        SELECT
            *
        FROM MI_STATISTICS_AMS_AUDIENCE_DAY
        WHERE START_TIME = #{day} AND CONTENT_ID = #{audienceKey.contentId} AND DEVICE_ID = #{device_id}
              AND GENDER = #{audienceKey.gender} AND AGE = #{audienceKey.age}
    </select>
    
     <update id="setAudiencePopDayInfo">
        UPDATE MI_STATISTICS_AMS_AUDIENCE_DAY
        SET TOTAL_COUNT = #{entitie.total_count}, DWELL_TIME = #{entitie.dwell_time}, ATTENTION_COUNT = #{entitie.attention_count}, ATTENTION_TIME = #{entitie.attention_time}
        WHERE START_TIME = #{day} AND CONTENT_ID = #{audienceKey.contentId} AND DEVICE_ID = #{device_id}
              AND GENDER = #{audienceKey.gender}
              AND AGE = #{audienceKey.age}
    </update>
    
     <select id="getAudiencePopMonthInfo"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.AudienceInfoEntity">
        SELECT
            *
        FROM MI_STATISTICS_AMS_AUDIENCE_MONTH
        WHERE START_TIME = #{month} AND CONTENT_ID = #{audienceKey.contentId} AND DEVICE_ID = #{device_id}
              AND GENDER = #{audienceKey.gender} AND AGE = #{audienceKey.age}
    </select>
    
     <update id="setAudiencePopMonthInfo">
        UPDATE MI_STATISTICS_AMS_AUDIENCE_MONTH
        SET TOTAL_COUNT = #{entitie.total_count}, DWELL_TIME = #{entitie.dwell_time}, ATTENTION_COUNT = #{entitie.attention_count}, ATTENTION_TIME = #{entitie.attention_time}
        WHERE START_TIME = #{month} AND CONTENT_ID = #{audienceKey.contentId} AND DEVICE_ID = #{device_id}
              AND GENDER = #{audienceKey.gender}
              AND AGE = #{audienceKey.age}
    </update>
    
    <select id="getAudiencePopYearInfo"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.AudienceInfoEntity">
        SELECT
            *
        FROM MI_STATISTICS_AMS_AUDIENCE_YEAR
        WHERE START_TIME = #{year} AND CONTENT_ID = #{audienceKey.contentId} AND DEVICE_ID = #{device_id}
              AND GENDER = #{audienceKey.gender} AND AGE = #{audienceKey.age}
    </select>
    
     <update id="setAudiencePopYearInfo">
        UPDATE MI_STATISTICS_AMS_AUDIENCE_YEAR
        SET TOTAL_COUNT = #{entitie.total_count}, DWELL_TIME = #{entitie.dwell_time}, ATTENTION_COUNT = #{entitie.attention_count}, ATTENTION_TIME = #{entitie.attention_time}
        WHERE START_TIME = #{year} AND CONTENT_ID = #{audienceKey.contentId} AND DEVICE_ID = #{device_id}
              AND GENDER = #{audienceKey.gender}
              AND AGE = #{audienceKey.age}
    </update>
    
    <select id="getYesterdayListBy" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceAudienceBasedEntity">
		SELECT  A.*
		FROM ( SELECT * FROM 
			<choose>
				<when test="unit  == 'DAY'">
					MI_STATISTICS_AMS_AUDIENCE_DAY
				</when>
				<when test="unit  == 'HOUR'">
					MI_STATISTICS_AMS_AUDIENCE_HOUR
				</when>
			</choose>
			<where>
				<include refid="where_deviceIdList"/>
				AND <include refid="castStartTimeToStringDate"/> = <include refid="truncDayCurrDateMinusOneDayAndCastToDate"/>
			</where>
			) A
			<choose>
				<when test="type  == 'graph'">
				ORDER BY START_TIME, CONTENT_ID 
				</when>
				<when test="type  == 'table'">
				ORDER BY CONTENT_ID, START_TIME 
				</when>
		</choose>
	</select>
	
	 <select id="getWeekListBy" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceAudienceBasedEntity">
		SELECT  A.*
		FROM ( SELECT * FROM 
			<choose>
				<when test="unit  == 'WEEK'">
					MI_STATISTICS_AMS_AUDIENCE_DAY
				</when>
				<when test="unit  == 'DAY'">
					MI_STATISTICS_AMS_AUDIENCE_DAY
				</when>
				<when test="unit  == 'HOUR'">
					MI_STATISTICS_AMS_AUDIENCE_HOUR
				</when>
			</choose>
			<where>
				<include refid="where_deviceIdList"/>
				AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}
			</where>
			) A
			<choose>
				<when test="type  == 'graph'">
				ORDER BY START_TIME, CONTENT_ID 
				</when>
				<when test="type  == 'table'">
				ORDER BY CONTENT_ID, START_TIME 
				</when>
		</choose>
	</select>
	
	<select id="getMonthListByMonth" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceAudienceBasedEntity">
		SELECT A.*
		FROM ( SELECT * FROM 
			MI_STATISTICS_AMS_AUDIENCE_MONTH
			 <where>
			 	<include refid="where_deviceIdList"/>
			 	<include refid="conditionStartTimeToCurrentMonth"/>
			 </where>
		) A
		<choose>
			<when test="type  == 'graph'">
			ORDER BY START_TIME, CONTENT_ID 
			</when>
			<when test="type  == 'table'">
			ORDER BY CONTENT_ID, START_TIME 
			</when>
		</choose>
	</select>
	
	<select id="getMonthListByDay" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceAudienceBasedEntity">
		SELECT A.*
		FROM ( SELECT * FROM 
			MI_STATISTICS_AMS_AUDIENCE_DAY
			 <where>
			 	<include refid="where_deviceIdList"/>
			 	<include refid="conditionMonthStartTimeEqCurrentMonth"/>
			 </where>
		) A
		<choose>
			<when test="type  == 'graph'">
			ORDER BY START_TIME, CONTENT_ID 
			</when>
			<when test="type  == 'table'">
			ORDER BY CONTENT_ID, START_TIME 
			</when>
		</choose>
	</select>
	
	<select id="getMonthListByHour" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceAudienceBasedEntity">
		SELECT A.*
		FROM ( SELECT * FROM 
			MI_STATISTICS_AMS_AUDIENCE_HOUR
			 <where>
			 	<include refid="where_deviceIdList"/>
			 	<include refid="conditionMonthStartTimeEqCurrentMonth"/>
			 </where>
		) A
		<choose>
			<when test="type  == 'graph'">
			ORDER BY START_TIME, CONTENT_ID 
			</when>
			<when test="type  == 'table'">
			ORDER BY CONTENT_ID, START_TIME 
			</when>
		</choose>
	</select>
	
	<select id="getMonthListByDow" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceAudienceBasedEntity">
		SELECT A.*
		FROM ( SELECT * FROM 
			MI_STATISTICS_AMS_AUDIENCE_DAY
			 <where>
			 	<include refid="where_deviceIdList"/>
			 	<include refid="conditionMonthStartTimeEqCurrentMonth"/>
			 </where>
			 ORDER BY CONTENT_ID, START_TIME 
		) A
		ORDER BY PLAY_DOW
	</select>
	
	<select id="getQuarterListByQuarter" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceAudienceBasedEntity">
		SELECT A.*
		FROM(
			SELECT *
			 FROM MI_STATISTICS_AMS_AUDIENCE_MONTH 
			 <where>
			 	<include refid="where_deviceIdList"/>
			 	<include refid="conditionYearAndQuarterStartTimeEqCurrYearAndQuarter"/>
			 </where>
		) A
		ORDER BY CONTENT_ID, START_TIME 
	</select>
	
	<select id="getYearListByYear" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceAudienceBasedEntity">
		SELECT A.*
		FROM(
			SELECT *
			 FROM MI_STATISTICS_AMS_AUDIENCE_YEAR
			 <where>
			 	<include refid="where_deviceIdList"/>
			 	<include refid="conditionStartTimeEqToTruncYearCurrentDate"/>
			 </where>
		) A
		<choose>
			<when test="type  == 'graph'">
			ORDER BY START_TIME, CONTENT_ID 
			</when>
			<when test="type  == 'table'">
			ORDER BY CONTENT_ID, START_TIME 
			</when>
		</choose> 
	</select>
    
    <select id="getYearListByQuarter" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceAudienceBasedEntity">
			SELECT A.*
			FROM(
				SELECT *
				 FROM MI_STATISTICS_AMS_AUDIENCE_MONTH 
				 <where>
				 	<include refid="where_deviceIdList"/>
				 	<include refid="conditionYearStartTimeEqToCurrentYear"/>
				 </where>
			) A
			<choose>
				<when test="type  == 'graph'">
				ORDER BY START_TIME, CONTENT_ID 
				</when>
				<when test="type  == 'table'">
				ORDER BY CONTENT_ID, START_TIME 
			</when>
		</choose>
	</select>
			
	<select id="getCustomList" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceAudienceBasedEntity">
		SELECT A.*
		FROM( SELECT * FROM
			<choose>
				<when test="unit  == 'DAY'">
					MI_STATISTICS_AMS_AUDIENCE_DAY
				</when>
				<when test="unit  == 'HOUR'">
					MI_STATISTICS_AMS_AUDIENCE_HOUR
				</when>
				<when test="unit  == 'WEEK'">
					MI_STATISTICS_AMS_AUDIENCE_DAY
				</when>
				<when test="unit  == 'MONTH'">
					MI_STATISTICS_AMS_AUDIENCE_MONTH
				</when>
				<when test="unit  == 'YEAR'">
					MI_STATISTICS_AMS_AUDIENCE_YEAR
				</when>
				<when test="unit  == 'DOW'">
					MI_STATISTICS_AMS_AUDIENCE_DAY
				</when>
			</choose>
			<where>
				<include refid="where_deviceIdList"/>
				AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}
			</where>
			) A
			<choose>
				<when test="type  == 'graph'">
				ORDER BY START_TIME, CONTENT_ID 
				</when>
				<when test="type  == 'table'">
				ORDER BY CONTENT_ID, START_TIME 
				</when>
		</choose>
		
	</select>
	
	<select id="getQuarterListByMonth" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceAudienceBasedEntity">
		SELECT A.*
		FROM(
			SELECT *
			 FROM MI_STATISTICS_AMS_AUDIENCE_MONTH 
			 <where>
			 	<include refid="where_deviceIdList"/>
				<include refid="conditionYearAndQuarterStartTimeEqCurrYearAndQuarter"/>
			 </where>
		) A ORDER BY LOG_QUARTER
	</select>
	
	<insert id="addTrafficPopHourInfoList">
        INSERT INTO MI_STATISTICS_AMS_TRAFFIC_HOUR( START_TIME, CONTENT_ID, CONTENT_NAME, DEVICE_ID, DEVICE_NAME, TOTAL_COUNT) VALUES
         <foreach collection="entities" open="(" close=")" separator="),(" item="entity" index="index">
            #{entity.start_time}, #{trafficKey.contentId}, #{entity.content_name}, #{deviceId}, #{entity.device_name}, #{entity.total_count}
        </foreach>
    </insert>
    
    <insert id="addTrafficPopDayInfoList">
        INSERT INTO MI_STATISTICS_AMS_TRAFFIC_DAY (START_TIME, CONTENT_ID, CONTENT_NAME, DEVICE_ID, DEVICE_NAME, TOTAL_COUNT, PLAY_DOW, WEEK_OF_YEAR)
        VALUES 
        <foreach collection="map" open="(" close=")" separator="),(" item="item" index="key">
            #{day},  #{key.contentId}, #{item.content_name}, #{deviceId}, #{item.device_name}, #{item.total_count}, #{dow}, #{woy}
        </foreach>
    </insert>
    
     <select id="getTrafficPopMonthInfo"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceTrafficEntity">
        SELECT
            *
        FROM MI_STATISTICS_AMS_TRAFFIC_MONTH
        WHERE START_TIME = #{month} AND  DEVICE_ID = #{deviceId} AND CONTENT_ID = #{trafficKey.contentId}
    </select>
    
    
     <update id="setTrafficPopMonthInfo">
        UPDATE MI_STATISTICS_AMS_TRAFFIC_MONTH
        SET TOTAL_COUNT = #{totalCount}
        WHERE START_TIME = #{month}  AND DEVICE_ID = #{deviceId} AND CONTENT_ID = #{contentId}
    </update>
    
    <insert id="addTrafficPopMonthInfoList">
        INSERT INTO MI_STATISTICS_AMS_TRAFFIC_MONTH (START_TIME, CONTENT_ID, CONTENT_NAME, LOG_QUARTER, DEVICE_ID, DEVICE_NAME, TOTAL_COUNT) VALUES
        <foreach collection="map" open="(" close=")" separator="),(" item="item" index="key">
             #{month}, #{key.contentId}, #{item.content_name}, #{quarter}, #{deviceId}, #{item.device_name}, #{item.total_count}
        </foreach>
    </insert>
    
    <select id="getTrafficPopYearInfo"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceTrafficEntity">
        SELECT
            *
        FROM MI_STATISTICS_AMS_TRAFFIC_YEAR
        WHERE START_TIME = #{year}  AND DEVICE_ID = #{deviceId} AND CONTENT_ID = #{trafficKey.contentId}
    </select>
    
    <update id="setTrafficPopYearInfo">
        UPDATE MI_STATISTICS_AMS_TRAFFIC_YEAR
        SET TOTAL_COUNT = #{totalCount}
        WHERE START_TIME = #{year}  AND DEVICE_ID = #{deviceId} AND CONTENT_ID = #{contentId}
    </update>
    
    <insert id="addTrafficPopYearInfoList">
        INSERT INTO MI_STATISTICS_AMS_TRAFFIC_YEAR( START_TIME, CONTENT_ID, CONTENT_NAME, DEVICE_ID, DEVICE_NAME, TOTAL_COUNT ) VALUES
          <foreach collection="map" open="(" close=")" separator="),(" item="item" index="key">
          	#{year}, #{key.contentId}, #{item.content_name}, #{deviceId}, #{item.device_name}, #{item.total_count}
          </foreach>
    </insert>
    
	<select id="getTrafficYesterdayListBy" resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceTrafficEntity">
		SELECT  A.*
		FROM ( SELECT * FROM 
			<choose>
				<when test="unit  == 'DAY'">
					MI_STATISTICS_AMS_TRAFFIC_DAY
				</when>
				<when test="unit  == 'HOUR'">
					MI_STATISTICS_AMS_TRAFFIC_HOUR
				</when>
			</choose>
			<where>
				<include refid="where_deviceIdList"/>
				AND <include refid="castStartTimeToStringDate"/> = <include refid="truncDayCurrDateMinusOneDayAndCastToDate"/>
			</where>
			) A
			<choose>
				<when test="type  == 'graph'">
				ORDER BY START_TIME, CONTENT_ID
				</when>
				<when test="type  == 'table'">
				ORDER BY CONTENT_ID, START_TIME
				</when>
			</choose>
			
	</select>
	
	<select id="getTrafficWeekListBy" resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceTrafficEntity">
		SELECT  A.*
		FROM ( SELECT * FROM 
			<choose>
				<when test="unit  == 'WEEK'">
					MI_STATISTICS_AMS_TRAFFIC_DAY
				</when>
				<when test="unit  == 'DAY'">
					MI_STATISTICS_AMS_TRAFFIC_DAY
				</when>
				<when test="unit  == 'HOUR'">
					MI_STATISTICS_AMS_TRAFFIC_HOUR
				</when>
			</choose>
			<where>
				<include refid="where_deviceIdList"/>
				AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}
			</where>
			) A
			<choose>
				<when test="type  == 'graph'">
				ORDER BY START_TIME, CONTENT_ID
				</when>
				<when test="type  == 'table'">
				ORDER BY CONTENT_ID, START_TIME
				</when>
			</choose>
	</select>
	
	<select id="getTrafficMonthListByDow" resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceTrafficEntity">
		SELECT A.*
		FROM ( SELECT * FROM 
			MI_STATISTICS_AMS_TRAFFIC_DAY
			 <where>
			 	<include refid="where_deviceIdList"/>
			 	<include refid="conditionMonthStartTimeEqCurrentMonth"/>
			 </where>
			 ORDER BY START_TIME
		) A
		ORDER BY PLAY_DOW
	</select>
	
	<select id="getTrafficMonthListByMonth" resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceTrafficEntity">
		SELECT A.*
		FROM ( SELECT * FROM 
			MI_STATISTICS_AMS_TRAFFIC_MONTH
			 <where>
			 	<include refid="where_deviceIdList"/>
			 	<include refid="conditionStartTimeToCurrentMonth"/>
			 </where>
		) A
		<choose>
			<when test="type  == 'graph'">
			ORDER BY START_TIME, CONTENT_ID 
			</when>
			<when test="type  == 'table'">
			ORDER BY CONTENT_ID, START_TIME 
			</when>
		</choose>
	</select>
	
	<select id="getTrafficQuarterListByQuarter" resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceTrafficEntity">
		SELECT A.*
		FROM(
			SELECT *
			 FROM MI_STATISTICS_AMS_TRAFFIC_MONTH 
			 <where>
			 	<include refid="where_deviceIdList"/>
			 	<include refid="conditionYearAndQuarterStartTimeEqCurrYearAndQuarter"/>
			 </where>
		) A
		ORDER BY START_TIME
	</select>
	
	<select id="getTrafficYearListByQuarter" resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceTrafficEntity">
			SELECT A.*
			FROM(
				SELECT *
				 FROM MI_STATISTICS_AMS_TRAFFIC_MONTH 
				 <where>
				 	<include refid="where_deviceIdList"/>
				 	<include refid="conditionYearStartTimeEqToCurrentYear"/>
				 </where>
			) A
		<choose>
			<when test="type  == 'graph'">
			ORDER BY START_TIME, CONTENT_ID 
			</when>
			<when test="type  == 'table'">
			ORDER BY CONTENT_ID, START_TIME 
			</when>
		</choose>
	</select>
	
	<select id="getTrafficYearListByYear" resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceTrafficEntity">
		SELECT A.*
		FROM(
			SELECT *
			 FROM MI_STATISTICS_AMS_TRAFFIC_YEAR
			 <where>
			 	<include refid="where_deviceIdList"/>
			 	<include refid="conditionStartTimeEqToTruncYearCurrentDate"/>
			 </where>
		) A
		<choose>
			<when test="type  == 'graph'">
			ORDER BY START_TIME, CONTENT_ID 
			</when>
			<when test="type  == 'table'">
			ORDER BY CONTENT_ID, START_TIME 
			</when>
		</choose>
	</select>
	
	<select id="getTrafficCustomList" resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceTrafficEntity">
		SELECT A.*
		FROM( SELECT * FROM
			<choose>
				<when test="unit  == 'DAY'">
					MI_STATISTICS_AMS_TRAFFIC_DAY
				</when>
				<when test="unit  == 'HOUR'">
					MI_STATISTICS_AMS_TRAFFIC_HOUR
				</when>
				<when test="unit  == 'WEEK'">
					MI_STATISTICS_AMS_TRAFFIC_DAY
				</when>
				<when test="unit  == 'MONTH'">
					MI_STATISTICS_AMS_TRAFFIC_MONTH
				</when>
				<when test="unit  == 'YEAR'">
					MI_STATISTICS_AMS_TRAFFIC_YEAR
				</when>
				<when test="unit  == 'DOW'">
					MI_STATISTICS_AMS_TRAFFIC_DAY
				</when>
			</choose>
			<where>
				<include refid="where_deviceIdList"/>
				AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}
			</where>
			) A
			<choose>
				<when test="type  == 'graph'">
				ORDER BY START_TIME, CONTENT_ID 
				</when>
				<when test="type  == 'table'">
				ORDER BY CONTENT_ID, START_TIME 
				</when>
			</choose>
		
	</select>
	
	<select id="getTrafficMonthListByDay" resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceTrafficEntity">
		SELECT A.*
		FROM ( SELECT * FROM 
			MI_STATISTICS_AMS_TRAFFIC_DAY
			 <where>
			 	<include refid="where_deviceIdList"/>
			 	<include refid="conditionMonthStartTimeEqCurrentMonth"/>
			 </where>
		) A
		<choose>
			<when test="type  == 'graph'">
			ORDER BY START_TIME, CONTENT_ID 
			</when>
			<when test="type  == 'table'">
			ORDER BY CONTENT_ID, START_TIME 
			</when>
		</choose>
		
	</select>
	
	<select id="getTrafficMonthListByWeek" resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceTrafficEntity">
		SELECT A.*
		FROM ( SELECT * FROM 
			MI_STATISTICS_AMS_TRAFFIC_DAY
			 <where>
			 	<include refid="where_deviceIdList"/>
			 	<include refid="conditionMonthStartTimeEqCurrentMonth"/>
			 </where>
		) A
		<choose>
			<when test="type  == 'graph'">
			ORDER BY START_TIME, CONTENT_ID 
			</when>
			<when test="type  == 'table'">
			ORDER BY CONTENT_ID, START_TIME 
			</when>
		</choose>
	</select>
	
	<select id="getTrafficMonthListByHour" resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceTrafficEntity">
		SELECT A.*
		FROM ( SELECT * FROM 
			MI_STATISTICS_AMS_TRAFFIC_HOUR
			 <where>
			 	<include refid="where_deviceIdList"/>
			 	<include refid="conditionMonthStartTimeEqCurrentMonth"/>
			 </where>
		) A
		<choose>
			<when test="type  == 'graph'">
			ORDER BY START_TIME, CONTENT_ID 
			</when>
			<when test="type  == 'table'">
			ORDER BY CONTENT_ID, START_TIME 
			</when>
		</choose>
	</select>
	
</mapper>