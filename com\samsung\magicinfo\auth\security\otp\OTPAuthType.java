package com.samsung.magicinfo.auth.security.otp;

import java.io.Serializable;

public class OTPAuthType implements Serializable {
   private static final long serialVersionUID = -827824541134490531L;
   String secretKey;
   String qrCode;
   String deviceKey;
   long deviceId;

   public OTPAuthType() {
      super();
   }

   public String getSecretKey() {
      return this.secretKey;
   }

   public void setSecretKey(String secretKey) {
      this.secretKey = secretKey;
   }

   public String getQrCode() {
      return this.qrCode;
   }

   public void setQrCode(String qrCode) {
      this.qrCode = qrCode;
   }

   public String getDeviceKey() {
      return this.deviceKey;
   }

   public void setDeviceKey(String deviceKey) {
      this.deviceKey = deviceKey;
   }

   public long getDeviceId() {
      return this.deviceId;
   }

   public void setDeviceId(long deviceId) {
      this.deviceId = deviceId;
   }
}
