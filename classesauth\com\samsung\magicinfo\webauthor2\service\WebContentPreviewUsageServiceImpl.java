package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.repository.inmemory.dao.PreviewUsageDao;
import com.samsung.magicinfo.webauthor2.repository.inmemory.model.PreviewUsage;
import com.samsung.magicinfo.webauthor2.service.WebContentPreviewUsageService;
import java.util.List;
import javax.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class WebContentPreviewUsageServiceImpl implements WebContentPreviewUsageService {
  public static final String PREVIEW_DIRECTORY_ROOT = "preview";
  
  private static final Logger logger = LoggerFactory.getLogger(WebContentPreviewUsageService.class);
  
  private PreviewUsageDao previewUsageDao;
  
  @Inject
  public WebContentPreviewUsageServiceImpl(PreviewUsageDao previewUsageDao) {
    this.previewUsageDao = previewUsageDao;
  }
  
  public List<PreviewUsage> findAll() {
    return this.previewUsageDao.findAll();
  }
  
  public List<PreviewUsage> findOlderThanDays(int Days) {
    return this.previewUsageDao.findOlderThanDays(Days);
  }
  
  public void insert(PreviewUsage previewUsage) {
    this.previewUsageDao.insert(previewUsage);
  }
  
  public void update(PreviewUsage previewUsage) {
    this.previewUsageDao.update(previewUsage);
  }
  
  public void delete(String contentId) {
    this.previewUsageDao.delete(contentId);
  }
  
  public PreviewUsage findByContentId(String contentId) {
    return this.previewUsageDao.findByContentId(contentId);
  }
}
