package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.exception.service.VWLXmlParseFileException;
import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.VWLCanvasContent;
import com.samsung.magicinfo.webauthor2.model.VWLContent;
import com.samsung.magicinfo.webauthor2.service.ContentService;
import com.samsung.magicinfo.webauthor2.service.RemoteContentService;
import com.samsung.magicinfo.webauthor2.service.VWLContentService;
import com.samsung.magicinfo.webauthor2.util.FileHashUtil;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import javax.servlet.ServletContext;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathFactory;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

@Service
public class VWLContentServiceImpl implements VWLContentService {
  private final ContentService contentService;
  
  private final RemoteContentService remoteContentService;
  
  private ServletContext servletContext;
  
  @Autowired
  public VWLContentServiceImpl(ContentService contentService, RemoteContentService remoteContentService, ServletContext servletContext) {
    this.contentService = contentService;
    this.remoteContentService = remoteContentService;
    this.servletContext = servletContext;
  }
  
  public VWLContent getVWLContent(String contentId) {
    Content content = this.contentService.getContent(contentId);
    if (content == null || content.getType() != MediaType.VWL)
      return null; 
    String insertContents = this.servletContext.getRealPath("insertContents");
    Path workspaceFolder = Paths.get(insertContents, new String[0]);
    Path vwlContent = this.remoteContentService.getContentFileFromMagicInfoServer(workspaceFolder, content.getFileId(), content
        .getFileName());
    File file = vwlContent.toFile();
    Set<VWLCanvasContent> canvasContentSet = findCanvasSet(file);
    String hashFile = FileHashUtil.getHash(file);
    long fileSize = file.length();
    return new VWLContent(content, hashFile, fileSize, canvasContentSet);
  }
  
  public Page<Content> getVWLContentList(Pageable pageable, DeviceType deviceType) {
    return this.contentService.getContentResources(pageable, deviceType, Arrays.asList(new MediaType[] { MediaType.VWL }));
  }
  
  private Set<VWLCanvasContent> findCanvasSet(File file) {
    Set<VWLCanvasContent> canvasContentSet = new HashSet<>();
    DocumentBuilderFactory domFactory = DocumentBuilderFactory.newInstance();
    domFactory.setNamespaceAware(true);
    DocumentBuilder builder = null;
    try {
      builder = domFactory.newDocumentBuilder();
      Document doc = builder.parse(file);
      XPathFactory factory = XPathFactory.newInstance();
      XPath xpath = factory.newXPath();
      XPathExpression expr = xpath.compile("//FileItems/FileItem");
      NodeList list = (NodeList)expr.evaluate(doc, XPathConstants.NODESET);
      for (int i = 0; i < list.getLength(); i++) {
        Node node = list.item(i);
        String fileName = FilenameUtils.getName(node.getTextContent());
        String fileId = node.getAttributes().getNamedItem("FileID").getNodeValue();
        canvasContentSet.add(new VWLCanvasContent(fileId, fileName));
      } 
    } catch (Exception e) {
      throw new VWLXmlParseFileException(e.getMessage());
    } 
    return canvasContentSet;
  }
}
