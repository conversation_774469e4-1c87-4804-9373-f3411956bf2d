package com.samsung.magicinfo.framework.setup.dao;

import com.samsung.magicinfo.framework.setup.entity.CompanyInfoEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseHistoryEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseStatusEntity;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

public interface SlmLicenseDaoMapper {
   Integer addSlmLicenseInfo(@Param("license") SlmLicenseEntity var1, @Param("reg_date") Timestamp var2) throws SQLException;

   boolean addSlmLicenseInfoForE2E(@Param("license") SlmLicenseEntity var1, @Param("reg_date") Timestamp var2, @Param("deviceId") String var3) throws SQLException;

   Integer deleteLicenseInfo(@Param("license_key") String var1) throws SQLException;

   boolean deleteMappingInfoOfSlmLicenseOrg(@Param("productCode") String var1) throws SQLException;

   Integer deleteLicenseInfoForE2EByDeviceId(@Param("deviceId") String var1);

   Integer setSlmLicenseHistory(@Param("licenseHistory") SlmLicenseHistoryEntity var1, @Param("reg_date") Timestamp var2) throws SQLException;

   Integer deleteSlmLicenseHistory(@Param("license_key") String var1, @Param("confirm") Boolean var2) throws SQLException;

   boolean updateSlmLicenseInfo(@Param("license") SlmLicenseEntity var1) throws SQLException;

   boolean updateSlmLicenseInfoForE2E(@Param("license") SlmLicenseEntity var1) throws SQLException;

   boolean extendSlmLicenseInfoForE2E(@Param("license") SlmLicenseEntity var1) throws SQLException;

   String getActivationKey(@Param("licenseKey") String var1) throws SQLException;

   boolean deleteAllLicense() throws SQLException;

   List getLicenseList(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("sort") String var3, @Param("dir") String var4) throws SQLException;

   int getLicenseListCount() throws SQLException;

   List getLicenseListForE2E(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("sort") String var3, @Param("dir") String var4, @Param("searchText") String var5) throws SQLException;

   int getLicenseListCountForE2E() throws SQLException;

   SlmLicenseEntity getSlmLicense(@Param("licenseKey") String var1) throws SQLException;

   SlmLicenseEntity getSlmLicenseForE2E(@Param("licenseKey") String var1) throws SQLException;

   SlmLicenseEntity getSlmLicenseForE2EByDeviceId(@Param("deviceId") String var1) throws SQLException;

   List getAllSlmlLicense() throws SQLException;

   List getAllSlmlLicenseForE2E() throws SQLException;

   List getProductCodeSlmLicense(@Param("productCode") String var1) throws SQLException;

   int getCntSlmLicenseByProductCode(@Param("productCode") String var1) throws SQLException;

   int getCntSlmLicenseByProductCodeForAssigningLicenseEachOrganization(@Param("productCode") String var1, @Param("licenseTypeList") List var2) throws SQLException;

   List getCntSlmLicensebyProductCodeAndLicenseType(@Param("productCode") String var1, @Param("licenseType") String var2) throws SQLException;

   boolean addDeviceSamples(@Param("mac") String var1, @Param("num") int var2) throws SQLException;

   boolean addDeviceSamplesSql2(@Param("mac") String var1) throws SQLException;

   boolean addSocDeviceSamples(@Param("mac") String var1, @Param("num") int var2) throws SQLException;

   boolean addSocDeviceSamplesSql2(@Param("mac") String var1) throws SQLException;

   boolean addLiteDeviceSamples(@Param("mac") String var1, @Param("num") int var2) throws SQLException;

   boolean addLiteDeviceSamplesSql2(@Param("mac") String var1) throws SQLException;

   boolean TestAddLicenseKey(@Param("license") SlmLicenseEntity var1) throws SQLException;

   Map getHwUniqueKey() throws SQLException;

   int setHwUniqueKey(String var1) throws SQLException;

   List getAllSlmLicenseHistory() throws SQLException;

   List getAllSlmLicenseHistoryByPageNumber(@Param("limit") int var1, @Param("offset") int var2) throws SQLException;

   int getCntSlmLicenseHistory() throws SQLException;

   SlmLicenseHistoryEntity getSlmLicenseHistory(@Param("licenseKey") String var1) throws SQLException;

   int getCntAllSlmLicenseHistoryForE2E() throws SQLException;

   List getAllSlmLicenseHistoryForE2E(@Param("limit") int var1, @Param("offset") int var2) throws SQLException;

   int getCntSlmLicenseHistoryForE2EByDeviceId(@Param("deviceId") String var1) throws SQLException;

   List getSlmLicenseHistoryForE2EByDeviceId(@Param("deviceId") String var1, @Param("limit") int var2, @Param("offset") int var3) throws SQLException;

   int getCntSlmlicense() throws SQLException;

   int getLicenseFromProductAndType(@Param("productCode") String var1, @Param("license_type") String var2) throws SQLException;

   boolean setValid(@Param("licenseKey") String var1, @Param("state") boolean var2) throws SQLException;

   boolean addLicenseStatus(@Param("status") SlmLicenseStatusEntity var1) throws SQLException;

   List getListLicenseStatus() throws SQLException;

   int chkLicenseStatus(@Param("productCode") String var1) throws SQLException;

   boolean delLicenseStatus(@Param("productCode") String var1) throws SQLException;

   SlmLicenseStatusEntity getLicenseStaus(@Param("productCode") String var1) throws SQLException;

   List getCntSlmLicensebyProductCodeList(@Param("productCodeList") List var1) throws SQLException;

   CompanyInfoEntity getCompanyInfo() throws SQLException;

   boolean deleteCompanyInfo() throws SQLException;

   boolean insertCompanyInfo(@Param("companyInfoEntity") CompanyInfoEntity var1) throws SQLException;

   boolean setCompanyInfo(@Param("companyInfoEntity") CompanyInfoEntity var1) throws SQLException;

   Integer setSlmLicenseHistoryForE2E(@Param("licenseHistory") SlmLicenseHistoryEntity var1, @Param("reg_date") Timestamp var2) throws SQLException;

   boolean updateDeviceTypeListByProductCode(@Param("productCode") String var1, @Param("deviceTypeList") String var2) throws SQLException;

   boolean addDeviceTypeListByProductCode(@Param("productCode") String var1, @Param("productName") String var2, @Param("deviceTypeList") String var3) throws SQLException;

   List getLicenseInfoAssignedToOrganization() throws SQLException;

   List getLicenseInfoAssignedToOrganizationWithOrgName(@Param("orgName") String var1) throws SQLException;

   boolean updateMaxLicenseCount(@Param("productCode") String var1, @Param("groupId") Long var2, @Param("maxLicenseCount") Long var3) throws SQLException;

   boolean deleteMaxLicenseCount(@Param("productCode") String var1, @Param("groupId") Long var2) throws SQLException;

   boolean addOrgSlmLicenseInfo(@Param("productCode") String var1, @Param("groupId") Long var2, @Param("maxLicenseCount") Long var3) throws SQLException;
}
