package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.exception.repository.ContentNotFoundException;
import com.samsung.magicinfo.webauthor2.model.BasicFileInfo;
import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.ContentThumbnailBasic;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.service.ContentService;
import com.samsung.magicinfo.webauthor2.webapi.assembler.ContentResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.resource.ContentResource;
import java.util.Arrays;
import java.util.List;
import javax.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.PagedResourcesAssembler;
import org.springframework.hateoas.PagedResources;
import org.springframework.hateoas.ResourceAssembler;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/contents"})
public class ContentQueryController {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.webapi.controller.ContentQueryController.class);
  
  private final ContentService contentService;
  
  private final ContentResourceAssembler contentResourceAssembler;
  
  @Inject
  public ContentQueryController(ContentService contentService, ContentResourceAssembler contentResourceAssembler) {
    this.contentService = contentService;
    this.contentResourceAssembler = contentResourceAssembler;
  }
  
  @GetMapping
  public HttpEntity<PagedResources<ContentResource>> getContentsWithPaging(@PageableDefault(page = 0, size = 50) Pageable pageable, @RequestParam String playerType, @RequestParam(name = "searchType", required = false, defaultValue = "all") String searchType, @RequestParam(name = "mediaType", required = false) List<String> sMediaType, @RequestParam(name = "searchText", required = false) String searchText, PagedResourcesAssembler<Content> assembler) {
    List<MediaType> mediaTypeList = MediaType.fromStringList(sMediaType);
    String compatiblePlayerType = DeviceType.getCompatiblePlayerType(playerType);
    Page<Content> page = this.contentService.getContentResources(searchText, pageable, DeviceType.valueOf(compatiblePlayerType), mediaTypeList, searchType);
    PagedResources<ContentResource> contentResources = assembler.toResource(page, (ResourceAssembler)this.contentResourceAssembler);
    return (HttpEntity<PagedResources<ContentResource>>)ResponseEntity.ok(contentResources);
  }
  
  @GetMapping({"/getThumbnails"})
  public HttpEntity<List<ContentThumbnailBasic>> getContentThumbnails(@RequestParam(required = false) String contentId, String resolution) {
    List<ContentThumbnailBasic> thumbnails = this.contentService.getContentThumbnails(contentId, resolution);
    return (HttpEntity<List<ContentThumbnailBasic>>)ResponseEntity.ok(thumbnails);
  }
  
  @GetMapping({"/getThumbnailBySize"})
  public HttpEntity<ContentThumbnailBasic> getContentThumbnailBySize(@RequestParam(required = false) String fileId, String size) {
    ContentThumbnailBasic thumbnail = this.contentService.getContentThumbnailBySize(fileId, size);
    return (HttpEntity<ContentThumbnailBasic>)ResponseEntity.ok(thumbnail);
  }
  
  @PostMapping({"/findByFiles"})
  public HttpEntity<List<ContentResource>> findContents(@RequestBody List<BasicFileInfo> fileInfos) {
    List<Content> contents = this.contentService.findContents(fileInfos);
    return (HttpEntity<List<ContentResource>>)ResponseEntity.ok(this.contentResourceAssembler.toResources(contents));
  }
  
  @PostMapping({"/findByIds"})
  public HttpEntity<List<ContentResource>> findContentsByIdList(@RequestBody String[] contentIds) {
    List<Content> contents = this.contentService.findContentsByIdList(Arrays.asList(contentIds));
    return (HttpEntity<List<ContentResource>>)ResponseEntity.ok(this.contentResourceAssembler.toResources(contents));
  }
  
  @RequestMapping(value = {"/{contentId}"}, method = {RequestMethod.GET})
  public HttpEntity<ContentResource> getContent(@PathVariable String contentId) {
    Content content = this.contentService.getContent(contentId);
    ContentResource contentResources = this.contentResourceAssembler.toResource(content);
    return (HttpEntity<ContentResource>)ResponseEntity.ok(contentResources);
  }
  
  @ExceptionHandler({IllegalArgumentException.class})
  public HttpEntity<String> illegalArgumentException(IllegalArgumentException ex) {
    logger.error(ex.getMessage(), ex);
    return (HttpEntity<String>)ResponseEntity.badRequest().body("Player type not found");
  }
  
  @ExceptionHandler({ContentNotFoundException.class})
  public HttpEntity<String> contentNotFoundException(ContentNotFoundException ex) {
    logger.error(ex.getMessage(), (Throwable)ex);
    return (HttpEntity<String>)ResponseEntity.notFound().build();
  }
  
  @ExceptionHandler({Exception.class})
  public HttpEntity<String> internalServerException(Exception ex) {
    logger.error(ex.getMessage(), ex);
    return (HttpEntity<String>)ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex.getMessage());
  }
}
