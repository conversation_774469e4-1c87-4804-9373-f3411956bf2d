package com.samsung.magicinfo.rc.framework.queue.entity;

public class ClientEntity {
  private String KeyCode;
  
  private String SetPanel;
  
  private String reqScreenMode;
  
  private String reqOpMode;
  
  private String text;
  
  private String mouse;
  
  public void setKeyCode(String KeyCode) {
    this.KeyCode = KeyCode;
  }
  
  public void setSetPanel(String SetPanel) {
    this.SetPanel = SetPanel;
  }
  
  public void setReqScreenMode(String reqScreenMode) {
    this.reqScreenMode = reqScreenMode;
  }
  
  public void setReqOpMode(String reqOpMode) {
    this.reqOpMode = reqOpMode;
  }
  
  public void setText(String text) {
    this.text = text;
  }
  
  public void setMouse(String mouse) {
    this.mouse = mouse;
  }
  
  public String toString() {
    return "ClientEntity(KeyCode=" + getKeyCode() + ", SetPanel=" + getSetPanel() + ", reqScreenMode=" + getReqScreenMode() + ", reqOpMode=" + getReqOpMode() + ", text=" + getText() + ", mouse=" + getMouse() + ")";
  }
  
  public String getKeyCode() {
    return this.KeyCode;
  }
  
  public String getSetPanel() {
    return this.SetPanel;
  }
  
  public String getReqScreenMode() {
    return this.reqScreenMode;
  }
  
  public String getReqOpMode() {
    return this.reqOpMode;
  }
  
  public String getText() {
    return this.text;
  }
  
  public String getMouse() {
    return this.mouse;
  }
}
