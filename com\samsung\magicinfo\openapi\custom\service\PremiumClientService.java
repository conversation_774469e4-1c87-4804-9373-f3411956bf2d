package com.samsung.magicinfo.openapi.custom.service;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.HWUniqueKey;
import com.samsung.magicinfo.framework.common.manager.EncryptionManager;
import com.samsung.magicinfo.framework.common.manager.EncryptionManagerImpl;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.monitoring.entity.ContentList;
import com.samsung.magicinfo.framework.monitoring.entity.CurrentPlayingEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ScheduleInfoEntity;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.scheduler.entity.FrameEntity;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.openapi.custom.openEntity.device.ApprovalResult;
import com.samsung.magicinfo.openapi.custom.openEntity.device.KeepAlive;
import com.samsung.magicinfo.openapi.custom.openEntity.device.TriggeringResult;
import com.samsung.magicinfo.openapi.impl.DeviceUtil;
import com.samsung.magicinfo.openapi.impl.OpenApiExceptionCode;
import com.samsung.magicinfo.openapi.impl.OpenApiServiceException;
import com.samsung.magicinfo.protocol.queue.RequestContext;
import com.samsung.magicinfo.protocol.queue.ReverseBindingQueueManager;
import com.samsung.magicinfo.protocol.util.TimeUtil;
import com.samsung.magicinfo.protocol.util.dao.ServerSetupDao;
import java.io.File;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.ftpserver.db.DownloadInfo;
import org.apache.ftpserver.db.DownloadInfoImpl;
import org.apache.ftpserver.ftplet.FtpException;
import org.apache.ftpserver.ftplet.UserManager;
import org.apache.ftpserver.usermanager.DbUserManagerFactory;
import org.apache.ftpserver.usermanager.impl.BaseUser;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.Scope;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.w3.x2003.x05.soapEnvelope.Envelope;
import org.w3.x2003.x05.soapEnvelope.EnvelopeDocument;

@Service("openApiPremiumClientService")
@Scope("prototype")
public class PremiumClientService {
   static Logger logger = LoggingManagerV2.getLogger(PremiumClientService.class);
   private String token = null;

   public PremiumClientService() {
      super();
   }

   public String getToken() {
      return this.token;
   }

   public void setToken(String token) {
      this.token = token;
   }

   @PreAuthorize("#deviceIn != null")
   public String requestBootstrapping(Device deviceIn) throws OpenApiServiceException, FtpException {
      if (deviceIn.getMac_address() != null && !deviceIn.getMac_address().equals("") && deviceIn.getDevice_name() != null && !deviceIn.getDevice_name().equals("") && deviceIn.getIp_address() != null && !deviceIn.getIp_address().equals("") && deviceIn.getPort() != null && deviceIn.getPort() != 0L) {
         DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
         Device device = new Device();
         DeviceUtil.setDefaultDevice(device);
         String deviceId = deviceIn.getMac_address();
         device.setDevice_id(deviceId);
         device.setMac_address(deviceId);
         device.setDevice_name(deviceIn.getDevice_name());
         device.setIp_address(deviceIn.getIp_address());
         device.setSubnet_mask(deviceIn.getSubnet_mask());
         device.setPort(deviceIn.getPort());
         device.setDevice_model_code("7000");
         device.setDevice_type_version(1.0F);
         if (deviceIn.getDevice_type() != null && !deviceIn.getDevice_type().isEmpty()) {
            device.setDevice_type(deviceIn.getDevice_type());
         } else {
            device.setDevice_type("iPLAYER");
         }

         Timestamp toDate = new Timestamp(System.currentTimeMillis());
         device.setLast_connection_time(toDate);
         device.setCreate_date(toDate);
         if (deviceIn.getDevice_model_name() != null && !deviceIn.getDevice_model_name().equals("")) {
            device.setDevice_model_name(deviceIn.getDevice_model_name());
         } else {
            device.setDevice_model_name("ExtraDisplay");
         }

         if (deviceIn.getDisk_space_repository() != null) {
            device.setDisk_space_repository(deviceIn.getDisk_space_repository());
         }

         if (deviceIn.getMagicinfo_server_url() != null && !deviceIn.getMagicinfo_server_url().equals("")) {
            device.setMagicinfo_server_url(deviceIn.getMagicinfo_server_url());
         }

         if (deviceIn.getCabinet_group_layout() != null && !deviceIn.getCabinet_group_layout().equals("")) {
            device.setCabinet_group_layout(deviceIn.getCabinet_group_layout());
         }

         if (deviceIn.getTrigger_interval() != null) {
            device.setTrigger_interval(deviceIn.getTrigger_interval());
         }

         if (deviceIn.getScreen_capture_interval() != null) {
            device.setScreen_capture_interval(deviceIn.getScreen_capture_interval());
         }

         if (deviceIn.getChild_monitoring_interval() != null) {
            device.setChild_monitoring_interval(deviceIn.getChild_monitoring_interval());
         }

         if (deviceIn.getMonitoring_interval() != null) {
            device.setMonitoring_interval(deviceIn.getMonitoring_interval());
         }

         if (deviceIn.getCpu_type() != null && !deviceIn.getCpu_type().equals("")) {
            device.setCpu_type(deviceIn.getCpu_type());
         }

         if (deviceIn.getIp_setting_type() != null && !deviceIn.getIp_setting_type().equals("")) {
            device.setIp_setting_type(deviceIn.getIp_setting_type());
         }

         if (deviceIn.getGateway() != null && !deviceIn.getGateway().equals("")) {
            device.setGateway(deviceIn.getGateway());
         }

         if (deviceIn.getDns_server_main() != null && !deviceIn.getDns_server_main().equals("")) {
            device.setDns_server_main(deviceIn.getDns_server_main());
         }

         if (deviceIn.getDns_server_sub() != null && !deviceIn.getDns_server_sub().equals("")) {
            device.setDns_server_sub(deviceIn.getDns_server_sub());
         }

         if (deviceIn.getApplication_version() != null && !deviceIn.getApplication_version().equals("")) {
            device.setApplication_version(deviceIn.getApplication_version());
         }

         if (deviceIn.getFirmware_version() != null && !deviceIn.getFirmware_version().equals("")) {
            device.setFirmware_version(deviceIn.getFirmware_version());
         }

         if (deviceIn.getOs_image_version() != null && !deviceIn.getOs_image_version().equals("")) {
            device.setOs_image_version(deviceIn.getOs_image_version());
         }

         if (deviceIn.getFtp_connect_mode() != null && !deviceIn.getFtp_connect_mode().equals("")) {
            device.setFtp_connect_mode(deviceIn.getFtp_connect_mode());
         }

         if (deviceIn.getHdd_size() != null) {
            device.setHdd_size(deviceIn.getHdd_size());
         }

         if (deviceIn.getMem_size() != null) {
            device.setMem_size(deviceIn.getMem_size());
         }

         if (deviceIn.getLocation() != null && !deviceIn.getLocation().equals("")) {
            device.setLocation(deviceIn.getLocation());
         }

         if (deviceIn.getScreen_rotation() != null) {
            device.setScreen_rotation(deviceIn.getScreen_rotation());
         }

         if (deviceIn.getScreen_size() != null && !deviceIn.getScreen_size().equals("")) {
            device.setScreen_size(deviceIn.getScreen_size());
         }

         if (deviceIn.getSerial_decimal() != null && !deviceIn.getSerial_decimal().equals("")) {
            device.setSerial_decimal(deviceIn.getSerial_decimal());
         }

         if (deviceIn.getSystem_restart_interval() != null && !deviceIn.getSystem_restart_interval().equals("")) {
            device.setSystem_restart_interval(deviceIn.getSystem_restart_interval());
         }

         if (deviceIn.getTunneling_server() != null && !deviceIn.getTunneling_server().equals("")) {
            device.setTunneling_server(deviceIn.getTunneling_server());
         }

         if (deviceIn.getNetwork_adapter() != null && !deviceIn.getNetwork_adapter().equals("")) {
            device.setNetwork_adapter(deviceIn.getNetwork_adapter());
         }

         if (deviceIn.getVideo_adapter() != null && !deviceIn.getVideo_adapter().equals("")) {
            device.setVideo_adapter(deviceIn.getVideo_adapter());
         }

         if (deviceIn.getVideo_memory() != null) {
            device.setVideo_memory(deviceIn.getVideo_memory());
         }

         try {
            boolean result = deviceDao.setDeviceOperationInfo(device);
            if (!result) {
               deviceDao.addDeviceOperationInfo(device);
               MonitoringManager var7 = MonitoringManagerImpl.getInstance();
            }

            deviceDao.setDevicePostBootstrap(device);
         } catch (Exception var17) {
            throw new OpenApiServiceException(OpenApiExceptionCode.CL003[0], OpenApiExceptionCode.CL003[1]);
         }

         String currentGMTTimeStr = TimeUtil.getCurrentGMTTimeStr();
         BaseUser user = new BaseUser();
         ServerSetupDao serverSetupDao = new ServerSetupDao();
         String encrySecretKey = "";

         try {
            encrySecretKey = serverSetupDao.getDefaultPassword();
         } catch (SQLException var16) {
            logger.error("", var16);
         }

         EncryptionManager encMgr = EncryptionManagerImpl.getInstance();
         String secretKey = encMgr.getDecryptionPassword("", encrySecretKey);
         String newPassword = currentGMTTimeStr + deviceId + secretKey;
         String encPass = HWUniqueKey.MD5(newPassword);
         String newPassword_8l = encPass.substring(0, 8).toUpperCase();
         user.setName(deviceId);
         user.setPassword(newPassword_8l);
         UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
         userMgr.save(user);
         return currentGMTTimeStr;
      } else {
         throw new OpenApiServiceException(OpenApiExceptionCode.CL001[0], OpenApiExceptionCode.CL001[1]);
      }
   }

   public ApprovalResult isApproval(String deviceId) throws OpenApiServiceException {
      if (deviceId != null && !deviceId.equals("")) {
         Device device;
         try {
            DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
            device = deviceDao.getDeviceMinInfo(deviceId);
            if (device == null) {
               throw new OpenApiServiceException(OpenApiExceptionCode.CL004[0], OpenApiExceptionCode.CL004[1]);
            }
         } catch (Exception var9) {
            throw new OpenApiServiceException(OpenApiExceptionCode.CL004[0], OpenApiExceptionCode.CL004[1]);
         }

         if (!device.getIs_approved()) {
            throw new OpenApiServiceException(OpenApiExceptionCode.CL005[0], OpenApiExceptionCode.CL005[1]);
         } else {
            Map user;
            try {
               DownloadInfo downloadDao = DownloadInfoImpl.getInstance();
               user = downloadDao.getUserByName(deviceId);
               if (user == null) {
                  throw new OpenApiServiceException(OpenApiExceptionCode.CL006[0], OpenApiExceptionCode.CL006[1]);
               }
            } catch (Exception var8) {
               throw new OpenApiServiceException(OpenApiExceptionCode.CL002[0], OpenApiExceptionCode.CL002[1]);
            }

            ApprovalResult approvalResult = new ApprovalResult();

            try {
               approvalResult.setDeviceName(device.getDevice_name());
               approvalResult.setContentsPath("contents_home" + File.separatorChar);
               approvalResult.setSchedulePath("upload" + File.separatorChar + "schedule" + File.separatorChar);
               approvalResult.setCapturePath("upload" + File.separatorChar + "capture" + File.separatorChar);
               approvalResult.setFtpUser((String)user.get("userid"));
               approvalResult.setFtpPwd((String)user.get("userpassword"));
               return approvalResult;
            } catch (Exception var7) {
               throw new OpenApiServiceException(OpenApiExceptionCode.CL002[0], OpenApiExceptionCode.CL002[1]);
            }
         }
      } else {
         throw new OpenApiServiceException(OpenApiExceptionCode.CL001[0], OpenApiExceptionCode.CL001[1]);
      }
   }

   public String sendKeepAlive(KeepAlive keepAlive) throws OpenApiServiceException {
      if (keepAlive.getDeviceId() != null && !keepAlive.getDeviceId().equals("") && keepAlive.getPanelStatus() != null && !keepAlive.getPanelStatus().equals("")) {
         DeviceInfo deviceDao = null;
         Device device = null;
         String deviceId = keepAlive.getDeviceId();

         try {
            deviceDao = DeviceInfoImpl.getInstance();
            device = deviceDao.getDevice(deviceId);
            if (device == null) {
               throw new OpenApiServiceException(OpenApiExceptionCode.CL004[0], OpenApiExceptionCode.CL004[1]);
            }
         } catch (Exception var28) {
            throw new OpenApiServiceException(OpenApiExceptionCode.CL004[0], OpenApiExceptionCode.CL004[1]);
         }

         if (!device.getIs_approved()) {
            throw new OpenApiServiceException(OpenApiExceptionCode.CL005[0], OpenApiExceptionCode.CL005[1]);
         } else {
            MonitoringManager mgr = MonitoringManagerImpl.getInstance();
            ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
            ContentInfoImpl conInfo = ContentInfoImpl.getInstance();

            try {
               mgr.setConnectionNow(device);
               deviceDao.setLastConnectionTime(deviceId);
            } catch (Exception var27) {
               throw new OpenApiServiceException(OpenApiExceptionCode.CL004[0], OpenApiExceptionCode.CL004[1]);
            }

            CurrentPlayingEntity curEntity = new CurrentPlayingEntity();
            curEntity.setPanelStatus(0L);
            curEntity.setInputSource(96);
            curEntity.setDirectChannel("");
            curEntity.setProgramId("");
            curEntity.setProgramName("");
            curEntity.setVersion(0L);

            try {
               if (keepAlive.getPanelStatus() != null && !keepAlive.getPanelStatus().equals("")) {
                  curEntity.setPanelStatus((long)Integer.parseInt(keepAlive.getPanelStatus()));
               }

               if (keepAlive.getInputSorce() != null && !keepAlive.getInputSorce().equals("")) {
                  curEntity.setInputSource(Integer.parseInt(keepAlive.getInputSorce()));
               }

               String programId = keepAlive.getContentProgramId();
               if (keepAlive.getContentProgramId() != null && !keepAlive.getContentProgramId().equals("")) {
                  curEntity.setProgramId(programId);
                  String programName = schInfo.getProgramName(programId);
                  curEntity.setProgramName(programName);
               }

               if (keepAlive.getContentVersion() != null && !keepAlive.getContentVersion().equals("")) {
                  curEntity.setVersion(Long.parseLong(keepAlive.getContentVersion()));
               }

               List frameList = schInfo.getFramesInfo(programId, 0);
               List contentList = new ArrayList();
               String contentStr = keepAlive.getContentId();
               String contentName;
               if (contentStr != null && !contentStr.equals("")) {
                  String[] contents = contentStr.split(";");

                  for(int i = 0; i < contents.length; ++i) {
                     if (contents[i] != null && !contents[i].equals("")) {
                        String[] contentId = contents[i].split(":", 3);
                        ContentList content = new ContentList();
                        if (contentId[2].equals("C0C336FC-0EAA-416c-AC92-697C4A103EDF")) {
                           content.setFrameIndex(contentId[1]);
                           content.setContentId(contentId[2]);

                           for(int j = 0; j < frameList.size(); ++j) {
                              if (((FrameEntity)frameList.get(j)).getFrame_index() == Integer.parseInt(contentId[1])) {
                                 content.setFrameName(((FrameEntity)frameList.get(j)).getFrame_name());
                                 if (((FrameEntity)frameList.get(j)).getIs_main_frame().equals("Y")) {
                                    content.setMainFrame(true);
                                 } else {
                                    content.setMainFrame(false);
                                 }
                                 break;
                              }
                           }
                        } else {
                           contentName = conInfo.getContentName(contentId[2]);
                           Map contentThumbnail = conInfo.getThumbFileInfoOfActiveVersion(contentId[2]);
                           if (contentThumbnail == null) {
                              continue;
                           }

                           content.setFrameIndex(contentId[1]);
                           content.setContentId(contentId[2]);
                           content.setContentName(contentName);
                           content.setThumbnailFileId((String)contentThumbnail.get("FILE_ID"));
                           content.setThumbnailFileName((String)contentThumbnail.get("FILE_NAME"));

                           for(int j = 0; j < frameList.size(); ++j) {
                              if (((FrameEntity)frameList.get(j)).getFrame_index() == Integer.parseInt(contentId[1])) {
                                 content.setFrameName(((FrameEntity)frameList.get(j)).getFrame_name());
                                 if (((FrameEntity)frameList.get(j)).getIs_main_frame().equals("Y")) {
                                    content.setMainFrame(true);
                                 } else {
                                    content.setMainFrame(false);
                                 }
                                 break;
                              }
                           }
                        }

                        contentList.add(content);
                     }
                  }
               }

               curEntity.setContentLists(contentList);
               mgr.setPlayingContent(deviceId, curEntity);
               if (keepAlive.getDiskSpaceRepository() != null && keepAlive.getDiskSpaceRepository().equals("")) {
                  deviceDao.updateDiskspaceChannel(deviceId, Long.parseLong(keepAlive.getDiskSpaceRepository()), "");
               }

               String pId = "";
               long version = 1L;
               pId = deviceDao.getProgramIdByDeviceId(deviceId);
               version = deviceDao.getVersionByProgramId(pId);
               if (!pId.equals(curEntity.getProgramId()) || !curEntity.getProgramId().equals("dd328c1a-19ab-4bd8-a604-5073dadd1382") && version != curEntity.getVersion()) {
                  logger.info("KeepAlive openAPI:deploy content schedule");
                  schInfo.deploySchedule(deviceId, pId, version);
               }

               ScheduleInfoEntity schEntity = mgr.getScheduleStatus(deviceId);
               contentName = keepAlive.getMessageId();
               long messageVersion = Long.parseLong(keepAlive.getMessageVersion() == null ? "0" : keepAlive.getMessageVersion());
               if (schEntity != null && (!schEntity.getMessageId().equals(contentName) || schEntity.getMessageVersion() != messageVersion)) {
                  MessageInfo msgInfo = MessageInfoImpl.getInstance();
                  boolean isDeployed = true;
                  String gId = "";

                  try {
                     gId = deviceDao.getDeviceGroupIdByDeviceId(deviceId);
                     if (gId != null && !gId.equals("")) {
                        MessageInfo minfo = MessageInfoImpl.getInstance();
                        List mlist = minfo.getMappedMessageIdByGroupId(gId);

                        for(int i = 0; i < mlist.size(); ++i) {
                           Map temp = (Map)mlist.get(i);
                           if (((String)temp.get("message_id")).equals("00000000-0000-0000-0000-000000000000")) {
                              isDeployed = false;
                              break;
                           }
                        }
                     } else {
                        isDeployed = false;
                     }
                  } catch (Exception var29) {
                     logger.info("[PremiumDeviceService] No gId by deviceId");
                     isDeployed = false;
                  }

                  if (isDeployed) {
                     logger.info("KeepAlive openAPI:deploy message schedule");
                     msgInfo.deployMessage(deviceId, schEntity.getMessageId(), schEntity.getMessageVersion());
                  }
               }

               return "SUCCESS";
            } catch (Exception var30) {
               throw new OpenApiServiceException(OpenApiExceptionCode.CL004[0], OpenApiExceptionCode.CL004[1]);
            }
         }
      } else {
         throw new OpenApiServiceException(OpenApiExceptionCode.CL001[0], OpenApiExceptionCode.CL001[1]);
      }
   }

   public TriggeringResult getTriggeringInfo(String deviceId) throws OpenApiServiceException {
      DeviceInfo deviceDao = null;
      Device device = null;

      try {
         deviceDao = DeviceInfoImpl.getInstance();
         device = deviceDao.getDevice(deviceId);
         if (device == null) {
            throw new OpenApiServiceException(OpenApiExceptionCode.CL004[0], OpenApiExceptionCode.CL004[1]);
         }
      } catch (Exception var9) {
         throw new OpenApiServiceException(OpenApiExceptionCode.CL004[0], OpenApiExceptionCode.CL004[1]);
      }

      if (!device.getIs_approved()) {
         throw new OpenApiServiceException(OpenApiExceptionCode.CL005[0], OpenApiExceptionCode.CL005[1]);
      } else {
         TriggeringResult triggeringResult = new TriggeringResult();

         try {
            RequestContext req = ReverseBindingQueueManager.getInstance().dequeueFromWaitingQueue(deviceId);
            if (req == null) {
               return triggeringResult;
            } else if (req.getTriggerCycle() == null && req.getPlayerRequest() == null) {
               EnvelopeDocument requestEnvelope = req.getWebServiceContext().getMessageContext().getSOAPMessage().getMessage();
               Envelope envelope = requestEnvelope.getEnvelope();
               envelope.addNewHeader();
               triggeringResult.setSoapMessage("<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"yes\"?>\n" + req.getWebServiceContext().getMessageContext().getSOAPMessage().getMessage().toString());
               return triggeringResult;
            } else {
               logger.error("deviceId=" + deviceId + " req set playerRequest." + req.getPlayerRequest());
               return triggeringResult;
            }
         } catch (Exception var8) {
            return triggeringResult;
         }
      }
   }
}
