package com.samsung.magicinfo.webauthor2.service.upload;

import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.FileItemsDescriptor;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import java.io.IOException;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

public interface WebContentUploadService {
  List<MediaSource> getUpdatedMediaSources(MultipartFile paramMultipartFile, String paramString1, String paramString2, DeviceType paramDeviceType, String paramString3, String paramString4, String paramString5, int paramInt);
  
  List<MediaSource> getUpdatedMediaSources(String paramString1, String paramString2, String paramString3, DeviceType paramDeviceType, String paramString4, String paramString5, String paramString6, int paramInt);
  
  MediaSource setWebContentThumbnail(MultipartFile paramMultipartFile) throws IOException, FileItemValidationException;
  
  MediaSource setWebContentThumbnail(String paramString) throws IOException, FileItemValidationException;
  
  String uploadWebContent(String paramString);
  
  String initializeFileUploadProcess(String paramString1, String paramString2, String paramString3) throws IOException, FileItemValidationException;
  
  MediaSource storeSupportFileItem(String paramString1, String paramString2, MultipartFile paramMultipartFile) throws IOException, FileItemValidationException;
  
  MediaSource storeSupportFileItem(String paramString1, String paramString2, String paramString3) throws IOException, FileItemValidationException;
  
  List<MediaSource> getUpdatedMediaSources(FileItemsDescriptor paramFileItemsDescriptor);
  
  String validateFileName(String paramString);
}
