package com.samsungcms.kpimagicinfo.security;

import java.net.Socket;
import java.security.cert.X509Certificate;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.X509ExtendedTrustManager;

final class null extends X509ExtendedTrustManager {
  public void checkClientTrusted(X509Certificate[] x509Certificates, String s) {}
  
  public void checkServerTrusted(X509Certificate[] x509Certificates, String s) {}
  
  public X509Certificate[] getAcceptedIssuers() {
    return new X509Certificate[0];
  }
  
  public void checkClientTrusted(X509Certificate[] x509Certificates, String s, Socket socket) {}
  
  public void checkServerTrusted(X509Certificate[] x509Certificates, String s, Socket socket) {}
  
  public void checkClientTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) {}
  
  public void checkServerTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) {}
}
