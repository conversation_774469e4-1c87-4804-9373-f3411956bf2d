package com.samsung.magicinfo.rc.model.client;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.samsung.magicinfo.rc.model.client.ResponseClass;
import java.io.Serializable;

@JacksonXmlRootElement(localName = "response")
public class Response implements Serializable {
  @JacksonXmlProperty(isAttribute = true)
  private int code;
  
  @JacksonXmlProperty(localName = "errorMessage")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  private String errorMessage;
  
  @JacksonXmlProperty(localName = "responseClass")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  private ResponseClass responseClass;
  
  public int getCode() {
    return this.code;
  }
  
  public void setCode(int code) {
    this.code = code;
  }
  
  public String getErrorMessage() {
    return this.errorMessage;
  }
  
  public void setErrorMessage(String errorMessage) {
    this.errorMessage = errorMessage;
  }
  
  public ResponseClass getResponseClass() {
    return this.responseClass;
  }
  
  public void setResponseClass(ResponseClass responseClass) {
    this.responseClass = responseClass;
  }
}
