package com.samsung.common.utils;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeHolidayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeTimerConf;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.apache.logging.log4j.Logger;
import org.springframework.context.support.ResourceBundleMessageSource;

public class MDCTimeStrUtils {
   static Logger logger = LoggingManagerV2.getLogger(MDCTimeStrUtils.class);

   public MDCTimeStrUtils() {
      super();
   }

   public static String newClockToDate(String str) {
      return newClockToDate(str, "yyyy-MM-dd hh:mm a");
   }

   public static String newClockToDate(String str, String dateFormat) {
      String currentTimeString = "";
      String defaultDateFormat = "yyyy-MM-dd hh:mm a";
      if (str != null && str.length() > 0) {
         String[] aval = str.split(";", -1);
         replaceEmptyToZero(aval);
         String clock_day = "";
         String clock_h = "";
         String clock_m = "";
         String clock_mon = "";
         String clock_y1 = "";
         String clock_y2 = "";
         String clock_ampm = "";
         if (aval != null && aval.length > 1) {
            clock_day = aval[0].toString().trim();
            clock_h = aval[1].toString().trim();
            clock_m = aval[2].toString().trim();
            clock_mon = aval[3].toString().trim();
            clock_y1 = aval[4].toString().trim();
            clock_y2 = aval[5].toString().trim();
            clock_ampm = aval[6].toString().trim();
         }

         if (clock_y1.equals("255")) {
            currentTimeString = "-";
            return currentTimeString;
         }

         if (clock_day.equals("") || clock_h.equals("") || clock_m.equals("") || clock_mon.equals("") || clock_y1.equals("") || clock_y2.equals("") || clock_ampm.equals("")) {
            currentTimeString = "-";
            return currentTimeString;
         }

         if (StrUtils.nvl(clock_ampm).equals("1")) {
            clock_ampm = "AM";
         } else {
            clock_ampm = "PM";
         }

         int ubYY1 = Integer.parseInt(clock_y1) * 256;
         int ubYYYY = ubYY1 + Integer.parseInt(clock_y2);
         if (!clock_y1.equals("") && !clock_h.equals("")) {
            currentTimeString = ubYYYY + "-" + StrUtils.getLeftFilledString(clock_mon, "0", 2) + "-" + StrUtils.getLeftFilledString(clock_day, "0", 2) + " " + clock_h + ":" + StrUtils.getLeftFilledString(clock_m, "0", 2) + " " + clock_ampm;
         } else if (clock_y1.equals("") && !clock_h.equals("")) {
            currentTimeString = clock_h + ":" + StrUtils.getLeftFilledString(clock_m, "0", 2) + " " + clock_ampm;
         }
      }

      if (dateFormat == null) {
         return currentTimeString;
      } else {
         SimpleDateFormat old_sdf = new SimpleDateFormat(defaultDateFormat);
         SimpleDateFormat new_sdf = new SimpleDateFormat(dateFormat);

         Date d;
         try {
            d = old_sdf.parse(currentTimeString);
         } catch (ParseException var14) {
            d = null;
         }

         return new_sdf.format(d);
      }
   }

   public static String oldClockToDate(String str) {
      String currentTimeString = "";
      if (str != null && str.length() > 0) {
         if (str.contains("255")) {
            currentTimeString = "-";
            return currentTimeString;
         }

         String[] aval = str.split(";", -1);
         replaceEmptyToZero(aval);
         String clock_h = "";
         String clock_m = "";
         String clock_ampm = "";
         if (aval != null && aval.length > 1) {
            clock_ampm = aval[0].toString().trim();
            clock_h = aval[1].toString().trim();
            clock_m = aval[2].toString().trim();
         }

         if (clock_ampm.equals("") || clock_h.equals("") || clock_m.equals("")) {
            currentTimeString = "-";
            return currentTimeString;
         }

         if (StrUtils.nvl(clock_ampm).equals("1")) {
            clock_ampm = "AM";
         } else {
            clock_ampm = "PM";
         }

         currentTimeString = clock_h + ":" + StrUtils.getLeftFilledString(clock_m, "0", 2) + " " + clock_ampm;
      }

      return currentTimeString;
   }

   public static String newTimerToDate(String str) {
      String strOnTimer = "";
      String strOffTimer = "";
      if (str != null && str.length() > 0) {
         String[] aVal = str.split(";", -1);
         replaceEmptyToZero(aVal);
         String strAmPm = "";
         if (aVal != null && aVal.length > 1) {
            if (aVal[3].equals("1")) {
               if (StrUtils.nvl(aVal[2]).equals("1")) {
                  strAmPm = "AM";
               } else {
                  strAmPm = "PM";
               }

               strOnTimer = aVal[0] + ":" + StrUtils.getLeftFilledString(aVal[1], "0", 2) + " " + strAmPm;
            }

            if (aVal[7].equals("1")) {
               if (StrUtils.nvl(aVal[6]).equals("1")) {
                  strAmPm = "AM";
               } else {
                  strAmPm = "PM";
               }

               strOffTimer = aVal[4] + ":" + StrUtils.getLeftFilledString(aVal[5], "0", 2) + " " + strAmPm;
            }

            if (strOnTimer.equals("") && strOffTimer.equals("")) {
               strOnTimer = strOnTimer + "-";
            } else if (!strOnTimer.equals("") || !strOffTimer.equals("")) {
               strOnTimer = strOnTimer + "~";
            }
         } else {
            strOnTimer = "-";
         }
      }

      return strOnTimer + strOffTimer;
   }

   public static void replaceEmptyToZero(String[] aVal) {
      for(int i = 0; i < aVal.length; ++i) {
         if (aVal[i].equals("")) {
            aVal[i] = "0";
         }
      }

   }

   public static String oldTimerToDate(String onTimeStr, String offTimeStr) {
      String strOnTimer = "";
      String strOffTimer = "";
      if (onTimeStr != null && onTimeStr.length() > 0 && offTimeStr != null && offTimeStr.length() > 0) {
         if (onTimeStr.startsWith("1;12;0;;") || onTimeStr.startsWith("1;12;0;0")) {
            onTimeStr = "1;12;0;0;0;0";
         }

         if (offTimeStr.startsWith("1;12;0;;") || offTimeStr.startsWith("1;12;0;0")) {
            offTimeStr = "1;12;0;0;0;0";
         }

         String[] onVal = onTimeStr.split(";", -1);
         replaceEmptyToZero(onVal);
         String[] offVal = offTimeStr.split(";", -1);
         replaceEmptyToZero(offVal);
         String strAmPm = "";
         if (onVal != null && onVal.length > 1 && onVal[4].equals("1")) {
            if (StrUtils.nvl(onVal[0]).equals("1")) {
               strAmPm = "AM";
            } else {
               strAmPm = "PM";
            }

            strOnTimer = onVal[1] + ":" + StrUtils.getLeftFilledString(onVal[2], "0", 2) + " " + strAmPm;
         }

         if (offVal != null && offVal.length > 1 && (offVal.length == 4 && offVal[3].equals("1") || offVal.length == 6 && offVal[4].equals("1"))) {
            if (StrUtils.nvl(offVal[0]).equals("1")) {
               strAmPm = "AM";
            } else {
               strAmPm = "PM";
            }

            strOffTimer = offVal[1] + ":" + StrUtils.getLeftFilledString(offVal[2], "0", 2) + " " + strAmPm;
         }

         if (strOnTimer.equals("") && strOffTimer.equals("")) {
            strOnTimer = strOnTimer + "-";
         } else if (!strOnTimer.equals("") || !strOffTimer.equals("")) {
            strOnTimer = strOnTimer + "~";
         }
      }

      return strOnTimer + strOffTimer;
   }

   public static int getHolidayCnt(String str) {
      int cnt = 0;
      if (str != null && str.length() > 0) {
         String[] aVal = str.split(",");

         for(int i = 0; i < aVal.length; ++i) {
            if (aVal[i].contains(";")) {
               ++cnt;
            }
         }
      }

      return cnt;
   }

   public static String timerObjToStr(DeviceTimeTimerConf timer) {
      String ret = "";
      if (timer != null) {
         if (timer.getTimer_data_count() == 15) {
            ret = timer.getTimer_on_h() + ";" + timer.getTimer_on_m() + ";" + timer.getTimer_on_ampm() + ";" + timer.getTimer_on_status() + ";" + timer.getTimer_off_h() + ";" + timer.getTimer_off_m() + ";" + timer.getTimer_off_ampm() + ";" + timer.getTimer_off_status() + ";" + timer.getTimer_repeat() + ";" + timer.getTimer_manual_weekday() + ";" + timer.getTimer_off_repeat() + ";" + timer.getTimer_off_manual_weekday() + ";" + timer.getTimer_volume() + ";" + timer.getTimer_source() + ";" + timer.getTimer_holiday_enable();
         } else {
            ret = timer.getTimer_on_h() + ";" + timer.getTimer_on_m() + ";" + timer.getTimer_on_ampm() + ";" + timer.getTimer_on_status() + ";" + timer.getTimer_off_h() + ";" + timer.getTimer_off_m() + ";" + timer.getTimer_off_ampm() + ";" + timer.getTimer_off_status() + ";" + timer.getTimer_repeat() + ";" + timer.getTimer_manual_weekday() + ";" + timer.getTimer_volume() + ";" + timer.getTimer_source() + ";" + timer.getTimer_holiday_enable();
         }
      }

      return ret;
   }

   public static String holidayObjToStr(List param) {
      StringBuffer sb = new StringBuffer();
      if (param != null) {
         for(int i = 0; i < param.size(); ++i) {
            if (i != 0) {
               sb.append(",");
            }

            DeviceTimeHolidayConf data = (DeviceTimeHolidayConf)param.get(i);
            sb.append(i + ";" + data.getMonth1() + ";" + data.getDay1() + ";" + data.getMonth2() + ";" + data.getDay2());
         }
      }

      return sb.toString();
   }

   public static String newTimerToOnTime(String str) {
      String strOnTimer = "-";
      if (str != null && str.length() > 0) {
         String[] aVal = str.split(";", -1);
         replaceEmptyToZero(aVal);
         String strAmPm = "";
         if (aVal != null && aVal.length > 1 && aVal[3].equals("1")) {
            if (StrUtils.nvl(aVal[2]).equals("1")) {
               strAmPm = "AM";
            } else {
               strAmPm = "PM";
            }

            strOnTimer = aVal[0] + ":" + StrUtils.getLeftFilledString(aVal[1], "0", 2) + " " + strAmPm;
         }
      }

      return strOnTimer;
   }

   public static String newTimerToOffTime(String str) {
      String strOffTimer = "-";
      if (str != null && str.length() > 0) {
         String[] aVal = str.split(";", -1);
         replaceEmptyToZero(aVal);
         String strAmPm = "";
         if (aVal != null && aVal.length > 1 && aVal[7].equals("1")) {
            if (StrUtils.nvl(aVal[6]).equals("1")) {
               strAmPm = "AM";
            } else {
               strAmPm = "PM";
            }

            strOffTimer = aVal[4] + ":" + StrUtils.getLeftFilledString(aVal[5], "0", 2) + " " + strAmPm;
         }
      }

      return strOffTimer;
   }

   public static String codeToInputSourceStr(String str) {
      String ret = "-";
      if (str != null && str.length() > 0) {
         switch(Integer.parseInt(str)) {
         case 4:
            ret = "S-Video";
            break;
         case 5:
         case 6:
         case 7:
         case 9:
         case 10:
         case 11:
         case 15:
         case 16:
         case 17:
         case 18:
         case 19:
         case 21:
         case 22:
         case 23:
         case 25:
         case 26:
         case 27:
         case 28:
         case 29:
         case 39:
         case 40:
         case 41:
         case 42:
         case 43:
         case 44:
         case 45:
         case 46:
         case 47:
         case 53:
         case 54:
         case 55:
         case 56:
         case 57:
         case 58:
         case 59:
         case 60:
         case 61:
         case 62:
         case 63:
         case 65:
         case 66:
         case 67:
         case 68:
         case 69:
         case 70:
         case 71:
         case 72:
         case 73:
         case 74:
         case 75:
         case 76:
         case 77:
         case 78:
         case 79:
         case 81:
         case 82:
         case 83:
         case 84:
         case 86:
         case 87:
         case 88:
         case 89:
         case 90:
         case 91:
         case 92:
         case 93:
         case 94:
         case 95:
         case 98:
         case 100:
         default:
            ret = "-";
            break;
         case 8:
            ret = "Component";
            break;
         case 12:
            ret = "AV";
            break;
         case 13:
            ret = "AV2";
            break;
         case 14:
            ret = "Ext";
            break;
         case 20:
            ret = "PC";
            break;
         case 24:
            ret = "DVI";
            break;
         case 30:
            ret = "BNC";
            break;
         case 31:
            ret = "DVI_VIDEO";
            break;
         case 32:
            ret = "MagicInfo";
            break;
         case 33:
            ret = "HDMI1";
            break;
         case 34:
            ret = "HDMI1_PC";
            break;
         case 35:
            ret = "HDMI2";
            break;
         case 36:
            ret = "HDMI2_PC";
            break;
         case 37:
            ret = "Display Port";
            break;
         case 38:
            ret = "Display Port2";
            break;
         case 48:
            ret = "ATV";
            break;
         case 49:
            ret = "HDMI3";
            break;
         case 50:
            ret = "HDMI3_PC";
            break;
         case 51:
            ret = "HDMI4";
            break;
         case 52:
            ret = "HDMI4_PC";
            break;
         case 64:
            ret = "DTV";
            break;
         case 80:
            ret = "Plug In Module";
            break;
         case 85:
            ret = "HDBaseT";
            break;
         case 96:
            ret = "MagicInfo-Lite";
            break;
         case 97:
            ret = "WiDi";
            break;
         case 99:
            ret = "URL Launcher";
            break;
         case 101:
            ret = "Web Browser";
            break;
         case 102:
            ret = "Samsung Workspace";
         }
      }

      return ret;
   }

   public static String codeToHolidayApplyStr(String str, String dontApplyStr, String applyStr) {
      String ret = "-";
      if (str != null && str.length() > 0) {
         switch(Integer.parseInt(str)) {
         case 0:
            ret = dontApplyStr;
            break;
         case 1:
            ret = applyStr;
            break;
         default:
            ret = "-";
         }
      }

      return ret;
   }

   public static String codeToHolidayOnOffApplyStr(String str, String dontApplyBothStr, String applyBothStr, String onTimerOnlyApply, String offTimerOnlyApply) {
      String ret = "-";
      if (str != null && str.length() > 0) {
         switch(Integer.parseInt(str)) {
         case 0:
            ret = dontApplyBothStr;
            break;
         case 1:
            ret = applyBothStr;
            break;
         case 2:
            ret = onTimerOnlyApply;
            break;
         case 3:
            ret = offTimerOnlyApply;
            break;
         default:
            ret = "-";
         }
      }

      return ret;
   }

   public static String codeToRepeatDay(String repeatStr, String manualWeekday, Map map, Locale locale) {
      StringBuffer retBuff = new StringBuffer("");
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      if (repeatStr != null && repeatStr.length() > 0) {
         switch(Integer.parseInt(repeatStr)) {
         case 0:
            retBuff.append(rms.getMessage("TEXT_ONCE_P", (Object[])null, locale));
            break;
         case 1:
            retBuff.append(rms.getMessage("TEXT_EVERYDAY_P", (Object[])null, locale));
            break;
         case 2:
            retBuff.append(rms.getMessage("COM_TEXT_DAY_MONDAY_P", (Object[])null, locale) + "~" + rms.getMessage("COM_TEXT_DAY_FRIDAY_P", (Object[])null, locale));
            break;
         case 3:
            retBuff.append(rms.getMessage("COM_TEXT_DAY_MONDAY_P", (Object[])null, locale) + "~" + rms.getMessage("COM_TEXT_DAY_SATURDAY_P", (Object[])null, locale));
            break;
         case 4:
            retBuff.append(rms.getMessage("COM_TEXT_DAY_SATURDAY_P", (Object[])null, locale) + "~" + rms.getMessage("COM_TEXT_DAY_SUNDAY_P", (Object[])null, locale));
            break;
         case 5:
            String manualWeekdayStr = StrUtils.fillString(Integer.toBinaryString(Integer.parseInt(manualWeekday)), '0', 7);
            if (manualWeekdayStr.equals("1111111")) {
               retBuff.append(rms.getMessage("TEXT_EVERYDAY_P", (Object[])null, locale));
            } else if (manualWeekdayStr.equals("0111110")) {
               retBuff.append(rms.getMessage("COM_TEXT_DAY_MONDAY_P", (Object[])null, locale) + "~" + rms.getMessage("COM_TEXT_DAY_FRIDAY_P", (Object[])null, locale));
            } else {
               for(int i = 0; i < manualWeekdayStr.length(); ++i) {
                  if (manualWeekdayStr.charAt(manualWeekdayStr.length() - i - 1) == '1') {
                     if (retBuff.toString().length() > 0) {
                        retBuff.append("/");
                     }

                     retBuff.append(map.get(i).toString());
                  }
               }

               return retBuff.toString();
            }
            break;
         default:
            retBuff.append("-");
         }
      }

      return retBuff.toString();
   }

   public static String oldTimerToOnTime(String str) {
      String ret = "-";
      if (str != null && str.length() > 0) {
         String[] onVal = str.split(";", -1);
         replaceEmptyToZero(onVal);
         String strAmPm = "";
         if (onVal != null && onVal.length > 1 && onVal[4].equals("1")) {
            if (StrUtils.nvl(onVal[0]).equals("1")) {
               strAmPm = "AM";
            } else {
               strAmPm = "PM";
            }

            ret = onVal[1] + ":" + StrUtils.getLeftFilledString(onVal[2], "0", 2) + " " + strAmPm;
         }
      }

      return ret;
   }

   public static String oldTimerToOffTime(String str) {
      String ret = "-";
      if (str != null && str.length() > 0) {
         String[] offVal = str.split(";", -1);
         replaceEmptyToZero(offVal);
         String strAmPm = "";
         if (offVal != null && offVal.length > 1 && (offVal.length == 4 && offVal[3].equals("1") || offVal.length == 6 && offVal[4].equals("1"))) {
            if (StrUtils.nvl(offVal[0]).equals("1")) {
               strAmPm = "AM";
            } else {
               strAmPm = "PM";
            }

            ret = offVal[1] + ":" + StrUtils.getLeftFilledString(offVal[2], "0", 2) + " " + strAmPm;
         }
      }

      return ret;
   }

   public static String getModelKind(String deviceModelCode) {
      String modelKind = "NEW";

      try {
         if (!deviceModelCode.equalsIgnoreCase(String.valueOf(7000)) && !deviceModelCode.equalsIgnoreCase(String.valueOf(7001)) && !deviceModelCode.equalsIgnoreCase(String.valueOf(7002)) && !deviceModelCode.equalsIgnoreCase("DEFAULT") && Integer.parseInt(deviceModelCode, 10) > 55) {
            modelKind = "NEW";
         } else {
            modelKind = "OLD";
         }

         return modelKind;
      } catch (Exception var3) {
         logger.fatal("Error by invalid deviceModelCode from device " + deviceModelCode);
         return "OLD";
      }
   }

   public static int getTimerCnt(String deviceModelCode) {
      try {
         byte timerCnt;
         if (!deviceModelCode.equalsIgnoreCase(String.valueOf(7000)) && !deviceModelCode.equalsIgnoreCase(String.valueOf(7001)) && !deviceModelCode.equalsIgnoreCase(String.valueOf(7002)) && !deviceModelCode.equalsIgnoreCase("DEFAULT") && Integer.parseInt(deviceModelCode, 10) > 55) {
            int intModelCode = Integer.parseInt(deviceModelCode);
            if ((intModelCode < 70 || intModelCode == 72) && intModelCode != 9998) {
               timerCnt = 3;
            } else {
               timerCnt = 7;
            }
         } else {
            timerCnt = 1;
         }

         return timerCnt;
      } catch (Exception var3) {
         logger.fatal("Error by invalid deviceModelCode from device " + deviceModelCode);
         return 1;
      }
   }

   public static boolean isNullTimerOfVWL2(String timer) {
      return timer.startsWith("12;0;1;;12;0;1;;;;;;");
   }

   public static String convert13to15(String timer) {
      try {
         String[] tmpTimerArr = timer.split(";");
         if (tmpTimerArr.length == 13) {
            timer = tmpTimerArr[0] + ";" + tmpTimerArr[1] + ";" + tmpTimerArr[2] + ";" + tmpTimerArr[3] + ";" + tmpTimerArr[4] + ";" + tmpTimerArr[5] + ";" + tmpTimerArr[6] + ";" + tmpTimerArr[7] + ";" + tmpTimerArr[8] + ";" + tmpTimerArr[9] + ";" + tmpTimerArr[8] + ";" + tmpTimerArr[9] + ";" + tmpTimerArr[10] + ";" + tmpTimerArr[11] + ";" + tmpTimerArr[12];
         }
      } catch (Exception var2) {
         logger.error("", var2);
      }

      return timer;
   }

   public static String convert15to13(String timer) {
      try {
         if (timer != null) {
            String[] tmpTimerArr = timer.split(";");
            if (tmpTimerArr.length == 15) {
               timer = tmpTimerArr[0] + ";" + tmpTimerArr[1] + ";" + tmpTimerArr[2] + ";" + tmpTimerArr[3] + ";" + tmpTimerArr[4] + ";" + tmpTimerArr[5] + ";" + tmpTimerArr[6] + ";" + tmpTimerArr[7] + ";" + tmpTimerArr[8] + ";" + tmpTimerArr[9] + ";" + tmpTimerArr[12] + ";" + tmpTimerArr[13] + ";" + tmpTimerArr[14];
            }
         }
      } catch (Exception var2) {
         logger.error("", var2);
      }

      return timer;
   }
}
