package com.samsung.magicinfo.framework.device.preconfig.dao;

import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceServiceConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSoftwareConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.preconfig.entity.DevicePreconfig;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

public interface DevicePreconfigDaoMapper {
   List getPreconfigList(@Param("condition") SelectCondition var1) throws SQLException;

   int getPreconfigListTotalCount(@Param("condition") SelectCondition var1) throws SQLException;

   int addDevicePreconfigInfo(@Param("preconfig") DevicePreconfig var1) throws SQLException;

   DevicePreconfig getPreconfigInfo(@Param("preconfigId") String var1) throws SQLException;

   DevicePreconfig getPreconfigInfoByDeviceId(@Param("deviceId") String var1) throws SQLException;

   boolean deletePreconfig(@Param("preconfigIdList") String[] var1) throws SQLException;

   int addPreconfigGroupMapping(@Param("map") Map var1) throws SQLException;

   boolean deletePreconfigGroupMapping(@Param("preconfigId") String var1) throws SQLException;

   boolean deletePreconfigGroupMappingByGroup(@Param("deviceGroupList") Long[] var1) throws SQLException;

   List getGroupMappingByPreconfig(@Param("preconfigId") String var1) throws SQLException;

   boolean updatePreconfigInfo(@Param("preconfig") DevicePreconfig var1) throws SQLException;

   int addServiceConfig(@Param("service") DeviceServiceConf var1) throws SQLException;

   int addSoftwareConfig(@Param("software") DeviceSoftwareConf var1) throws SQLException;

   boolean updateServiceConfig(@Param("service") DeviceServiceConf var1) throws SQLException;

   List getServiceConfigInfo(@Param("preconfigId") String var1) throws SQLException;

   List getSoftwareConfigInfo(@Param("preconfigId") String var1) throws SQLException;

   boolean deleteServiceConfig(@Param("preconfigId") String var1) throws SQLException;

   boolean deleteSoftwareConfig(@Param("preconfigId") String var1) throws SQLException;

   boolean setDeployStatus(@Param("preconfigId") String var1, @Param("deviceId") String var2, @Param("status") String var3);

   boolean setDeployStatusReportTime(@Param("preconfigId") String var1, @Param("deviceId") String var2, @Param("reportTime") Date var3);

   int addDeployStatus(@Param("preconfigId") String var1, @Param("deviceId") String var2, @Param("status") String var3) throws SQLException;

   int setDeployStatusByGroup(@Param("preconfigId") String var1, @Param("status") String var2, @Param("groupId") Long var3) throws SQLException;

   boolean deleteDeployStatus(@Param("preconfigId") String var1) throws SQLException;

   boolean deleteDeployStatusByGroup(@Param("deviceGroupList") Long[] var1) throws SQLException;

   int checkExistServiceConfig(@Param("service") DeviceServiceConf var1) throws SQLException;

   List getDeployStatusByPreconfigId(@Param("preconfigId") String var1) throws SQLException;

   Map getDeployStatusByDeviceId(@Param("deviceId") String var1) throws SQLException;

   boolean deletePreconfigFromDevice(String var1);
}
