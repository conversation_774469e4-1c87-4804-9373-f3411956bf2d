package com.samsung.magicinfo.auth.security.otp;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.sql.Timestamp;

public class UserAuthDevice implements Serializable {
   private static final long serialVersionUID = -1508177918479338721L;
   @JsonProperty("userId")
   private String user_id;
   @JsonProperty("authEnable")
   private boolean auth_enable;
   @JsonProperty("secretKey")
   private String secret_key;
   @JsonProperty("createDate")
   private Timestamp create_date;
   @JsonProperty("expiredDate")
   private Timestamp expired_date;
   @JsonProperty("latestDate")
   private Timestamp latest_date;
   @JsonProperty("authDeviceId")
   private Long auth_device_id;
   @JsonProperty("authDeviceName")
   private String auth_device_name;
   @JsonProperty("osName")
   private String os_name;
   @JsonProperty("osVersion")
   private String os_version;
   @JsonProperty("browserName")
   private String browser_name;
   @JsonProperty("browserVersion")
   private String browser_version;

   public UserAuthDevice() {
      super();
   }

   public String getOs_name() {
      return this.os_name;
   }

   public void setOs_name(String os_name) {
      this.os_name = os_name;
   }

   public String getOs_version() {
      return this.os_version;
   }

   public void setOs_version(String os_version) {
      this.os_version = os_version;
   }

   public String getBrowser_name() {
      return this.browser_name;
   }

   public void setBrowser_name(String browser_name) {
      this.browser_name = browser_name;
   }

   public String getBrowser_version() {
      return this.browser_version;
   }

   public void setBrowser_version(String browser_version) {
      this.browser_version = browser_version;
   }

   public String getUser_id() {
      return this.user_id;
   }

   public void setUser_id(String user_id) {
      this.user_id = user_id;
   }

   public boolean isAuth_enable() {
      return this.auth_enable;
   }

   public void setAuth_enable(boolean auth_enable) {
      this.auth_enable = auth_enable;
   }

   public Long getAuth_device_id() {
      return this.auth_device_id;
   }

   public void setAuth_device_id(Long auth_device_id) {
      this.auth_device_id = auth_device_id;
   }

   public String getAuth_device_name() {
      return this.auth_device_name;
   }

   public void setAuth_device_name(String auth_device_name) {
      this.auth_device_name = auth_device_name;
   }

   public String getSecret_key() {
      return this.secret_key;
   }

   public void setSecret_key(String secret_key) {
      this.secret_key = secret_key;
   }

   public Timestamp getCreate_date() {
      return this.create_date;
   }

   public void setCreate_date(Timestamp create_date) {
      this.create_date = create_date;
   }

   public Timestamp getExpired_date() {
      return this.expired_date;
   }

   public void setExpired_date(Timestamp expired_date) {
      this.expired_date = expired_date;
   }

   public Timestamp getLatest_date() {
      return this.latest_date;
   }

   public void setLatest_date(Timestamp latest_date) {
      this.latest_date = latest_date;
   }
}
