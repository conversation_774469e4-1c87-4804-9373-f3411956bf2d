package com.samsung.magicinfo.framework.content.manager;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.XPathQueryBuilder;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.sql.SQLException;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.w3c.dom.Text;
import org.xml.sax.SAXException;

public class ContentXmlManager {
   Logger logger = LoggingManagerV2.getLogger(ContentXmlManager.class);

   public ContentXmlManager() {
      super();
   }

   public void modifyTemplateInfo(ContentFile templateFile, String srcDlkFile, String targetDlkFile, Long dlkVersionId, String templateContentId, String dlkContentId) throws ConfigException, SQLException, Exception {
      DocumentBuilderFactory docFctory = DocumentUtils.getDocumentBuilderFactoryInstance();
      Element dataLinkContentMetaElement = null;
      FileInputStream inputStreamTemplate = null;

      try {
         DocumentBuilder builder = docFctory.newDocumentBuilder();
         inputStreamTemplate = new FileInputStream(srcDlkFile);
         Document doc = builder.parse(inputStreamTemplate);
         XPathFactory factory = XPathFactory.newInstance();
         XPath xpath = factory.newXPath();
         XPathExpression expr = null;
         Object result = null;

         try {
            expr = xpath.compile((new XPathQueryBuilder("//DataLinkContentMeta")).build());
            result = expr.evaluate(doc, XPathConstants.NODESET);
            NodeList dataLinkContentMetaList = (NodeList)result;
            Element fileIdElement = doc.createElement("FileID");
            Text fileIdText = doc.createTextNode(templateFile.getFile_id());
            fileIdElement.appendChild(fileIdText);
            Element fileNameElement = doc.createElement("FileName");
            Text fileNameText = doc.createTextNode(templateFile.getFile_name());
            fileNameElement.appendChild(fileNameText);
            Element fileHashValueElement = doc.createElement("FileHashValue");
            Text fileHashValueText = doc.createTextNode(templateFile.getHash_code());
            fileHashValueElement.appendChild(fileHashValueText);
            Element fileSizeElement = doc.createElement("FileSize");
            Text fileSizeText = doc.createTextNode(Long.toString(templateFile.getFile_size()));
            fileSizeElement.appendChild(fileSizeText);
            dataLinkContentMetaElement = (Element)dataLinkContentMetaList.item(0);
            Element lfdElement = (Element)dataLinkContentMetaElement.getElementsByTagName("LFDContent").item(0);
            lfdElement.removeChild(dataLinkContentMetaElement.getElementsByTagName("FileID").item(0));
            lfdElement.removeChild(dataLinkContentMetaElement.getElementsByTagName("FileName").item(0));
            lfdElement.removeChild(dataLinkContentMetaElement.getElementsByTagName("FileHashValue").item(0));
            lfdElement.removeChild(dataLinkContentMetaElement.getElementsByTagName("FileSize").item(0));
            lfdElement.appendChild(fileIdElement);
            lfdElement.appendChild(fileNameElement);
            lfdElement.appendChild(fileHashValueElement);
            lfdElement.appendChild(fileSizeElement);
         } catch (XPathExpressionException var43) {
            this.logger.error("", var43);
         }

         if (dataLinkContentMetaElement != null) {
            dataLinkContentMetaElement.removeAttribute("virtual");
            dataLinkContentMetaElement.setAttribute("version", dlkVersionId.toString());
            doc.replaceChild(dataLinkContentMetaElement, doc.getChildNodes().item(0));
         }

         TransformerFactory factory2 = DocumentUtils.getTransformerFactoryInstance();
         Transformer trans = factory2.newTransformer();
         trans.setOutputProperty("method", "xml");
         trans.setOutputProperty("encoding", "UTF-8");
         trans.setOutputProperty("indent", "yes");
         trans.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "10");
         File output = SecurityUtils.getSafeFile(targetDlkFile);
         trans.transform(new DOMSource(doc), new StreamResult(output.getPath()));
         ContentInfo contentService = ContentInfoImpl.getInstance();
         contentService.setActiveVersion(dlkContentId, dlkVersionId, true);
         System.out.println("[CXml] modify operation is finished ");
      } catch (ParserConfigurationException var44) {
         this.logger.error("", var44);
      } catch (SAXException var45) {
         this.logger.error("", var45);
      } catch (IOException var46) {
         this.logger.error("", var46);
      } catch (TransformerConfigurationException var47) {
         this.logger.error("", var47);
      } catch (TransformerException var48) {
         this.logger.error("", var48);
      } finally {
         try {
            if (inputStreamTemplate != null) {
               inputStreamTemplate.close();
               inputStreamTemplate = null;
            }
         } catch (Exception var42) {
            this.logger.error("", var42);
         }

      }

   }

   public void modifyMediaSlideInfo(String prevMediaSlideMainFileId, ContentFile mediaSlideFile, String srcDlkFile, String templateContentId, String dlkContentId) throws ConfigException, SQLException, Exception {
      DocumentBuilderFactory docFctory = DocumentUtils.getDocumentBuilderFactoryInstance();
      System.out.println("[CXml] modifyMediaSlideInfo old : " + prevMediaSlideMainFileId);
      System.out.println("[CXml] modifyMediaSlideInfo src : " + srcDlkFile);
      FileInputStream inputStreamTemplate = null;

      try {
         DocumentBuilder builder = docFctory.newDocumentBuilder();
         inputStreamTemplate = new FileInputStream(srcDlkFile);
         Document document = builder.parse(inputStreamTemplate);
         NodeList elementList = document.getElementsByTagName("Element");
         int eleLength = elementList.getLength();
         XPath xpath = XPathFactory.newInstance().newXPath();

         for(int ele = 0; ele < eleLength; ++ele) {
            NodeList dataList = document.getElementsByTagName("Data");

            for(int datalength = dataList.getLength(); datalength != 0; --datalength) {
               Element e2 = (Element)xpath.evaluate("//FileInfo[. = '" + prevMediaSlideMainFileId + File.separator + mediaSlideFile.getFile_name() + "']", document, XPathConstants.NODE);
               if (e2 != null) {
                  Element value1 = (Element)e2.getParentNode();
                  value1.setTextContent(mediaSlideFile.getFile_id() + File.separator + mediaSlideFile.getFile_name());
                  Element fileInfo = document.createElement("FileInfo");
                  fileInfo.appendChild(document.createTextNode(mediaSlideFile.getFile_id() + File.separator + mediaSlideFile.getFile_name()));
                  value1.appendChild(fileInfo);
                  fileInfo.setAttribute("filesize", mediaSlideFile.getFile_size().toString());
               }
            }
         }

         TransformerFactory transformerFactory = DocumentUtils.getTransformerFactoryInstance();
         Transformer transformer = transformerFactory.newTransformer();
         DOMSource source = new DOMSource(document);
         StreamResult result = new StreamResult(srcDlkFile);
         transformer.transform(source, result);
      } catch (ParserConfigurationException var31) {
         this.logger.error("", var31);
      } catch (SAXException var32) {
         this.logger.error("", var32);
      } catch (IOException var33) {
         this.logger.error("", var33);
      } finally {
         try {
            if (inputStreamTemplate != null) {
               inputStreamTemplate.close();
               inputStreamTemplate = null;
            }
         } catch (Exception var30) {
            this.logger.error("", var30);
         }

      }

   }
}
