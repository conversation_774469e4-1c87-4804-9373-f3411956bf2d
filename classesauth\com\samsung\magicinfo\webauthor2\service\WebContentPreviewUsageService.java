package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.repository.inmemory.model.PreviewUsage;
import java.util.List;

public interface WebContentPreviewUsageService {
  List<PreviewUsage> findAll();
  
  List<PreviewUsage> findOlderThanDays(int paramInt);
  
  void insert(PreviewUsage paramPreviewUsage);
  
  void update(PreviewUsage paramPreviewUsage);
  
  void delete(String paramString);
  
  PreviewUsage findByContentId(String paramString);
}
