package com.samsung.magicinfo.rc.common.exception;

public class ExceptionCode {
  public static final String[] HTTP200 = new String[] { "200", "HTTP", "OK" };
  
  public static final String[] HTTP202 = new String[] { "202", "HTTP", "Accepted" };
  
  public static final String[] HTTP204 = new String[] { "204", "HTTP", "No Content" };
  
  public static final String[] HTTP400 = new String[] { "400", "HTTP", "Bad Request" };
  
  public static final String[] HTTP401 = new String[] { "401", "HTTP", "Unauthorized" };
  
  public static final String[] HTTP403 = new String[] { "403", "HTTP", "Forbidden" };
  
  public static final String[] HTTP404 = new String[] { "404", "HTTP", "Not Found" };
  
  public static final String[] HTTP405 = new String[] { "405", "HTTP", "Method Not Allowed" };
  
  public static final String[] HTTP500 = new String[] { "500", "HTTP", "Internal Server Error" };
  
  public static final String[] HTTP501 = new String[] { "501", "HTTP", "Not Implemented" };
  
  public static final String[] HTTP503 = new String[] { "503", "HTTP", "Service Unavailable" };
  
  public static final String[] HTTP601 = new String[] { "601", "HTTP", "Key Exchange Error" };
  
  public static final String[] HTTP602 = new String[] { "602", "HTTP", "Trigger Error" };
}
