package com.samsung.magicinfo.openapi.web;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.FileUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.FileManager;
import com.samsung.magicinfo.framework.common.FileManagerImpl;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.manager.ContentCodeInfo;
import com.samsung.magicinfo.framework.content.manager.ContentCodeInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfo;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.openapi.auth.TokenRegistry;
import com.samsung.magicinfo.openapi.custom.service.OpenApiServiceMethodsToErrorCodesMapper;
import com.samsung.magicinfo.openapi.custom.service.OpenApiServiceMethodsToNullAuthorization;
import com.samsung.magicinfo.openapi.file.DSCFileItem;
import com.samsung.magicinfo.openapi.file.DSCFileItemFactory;
import com.samsung.magicinfo.openapi.impl.ClassUtil;
import com.samsung.magicinfo.openapi.impl.OpenApiExceptionCode;
import com.samsung.magicinfo.openapi.impl.OpenApiParameter;
import com.samsung.magicinfo.openapi.impl.OpenApiParameterValidator;
import com.samsung.magicinfo.openapi.impl.OpenApiServiceException;
import com.samsung.magicinfo.openapi.impl.OpenApiServiceManager;
import com.samsung.magicinfo.openapi.impl.OpenApiServiceMethod;
import com.samsung.magicinfo.openapi.impl.XmlFormatException;
import com.samsung.magicinfo.openapi.impl.XmlTransferableUtil;
import com.samsung.magicinfo.openapi.interfaces.OpenApiException;
import com.samsung.magicinfo.openapi.interfaces.OpenServiceGrabber;
import com.samsung.magicinfo.openapi.interfaces.XmlTransferableGrabber;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.Map.Entry;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.beanutils.MethodUtils;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.AbstractController;

public class OpenApiController extends AbstractController {
   public static final String OPEN_API_SERVICE_BEAN_PREFIX = "openApi";
   static Logger logger = LoggingManagerV2.getLogger(OpenApiController.class);
   static OpenApiServiceManager serviceManager;
   static OpenServiceGrabber openServiceGrabber;
   static XmlTransferableGrabber xmlTransferableGrabber;
   @Autowired
   private SpringBeansProvider springBeansProvider;

   public OpenApiController() {
      super();
   }

   private boolean supportContentType(String fileExt) {
      return ContentUtils.fexts.contains(fileExt.toLowerCase());
   }

   public ModelAndView handleRequestInternal(HttpServletRequest request, HttpServletResponse response) throws Exception {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      ModelAndView mav = new ModelAndView();
      mav.setViewName("rest/resultXML");
      Hashtable parameterMap = null;
      parameterMap = this.getParameters(request);
      String service = (String)parameterMap.get("service");
      String strXmldoc = (String)parameterMap.get("xml");
      OpenApiResponse oaResp = null;
      boolean authOk = true;

      try {
         String body;
         if (authOk) {
            if (service != null) {
               MultipartHttpServletRequest multipartRequest;
               MultipartFile multipart;
               if (service.equals("CommonContentService.uploadContent")) {
                  multipartRequest = (MultipartHttpServletRequest)request;
                  multipart = multipartRequest.getFile("file");
                  oaResp = this.doUploadContent(multipart, parameterMap);
               } else if (service.equals("PremiumDeviceService.uploadCustomizeFile")) {
                  multipartRequest = (MultipartHttpServletRequest)request;
                  multipart = multipartRequest.getFile("file");
                  String fileName = multipart.getOriginalFilename();
                  String fileExtension = FilenameUtils.getExtension(fileName);
                  if (!this.supportContentType(fileExtension)) {
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CONTENT_UPLOAD_NOT_SUPPORT_FILE);
                  }

                  String customFileLocation = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separator + "custom_files";
                  File file = new File(SecurityUtils.directoryTraversalChecker(customFileLocation + File.separator + fileName, (String)null));
                  if (!file.getParentFile().isDirectory()) {
                     file.getParentFile().mkdir();
                  }

                  multipart.transferTo(file);
                  parameterMap.put("file", file);
                  oaResp = this.doRest(parameterMap);
               } else if (!service.equals("CommonContentService.addDlkInfo")) {
                  oaResp = this.doRest(parameterMap);
               } else {
                  body = IOUtils.toString(request.getInputStream());
                  if (!body.isEmpty()) {
                     Map reqMap = CommonUtils.convertJsonToMap(body);
                     Iterator var11 = reqMap.entrySet().iterator();

                     while(var11.hasNext()) {
                        Entry entry = (Entry)var11.next();
                        if (!"userId".equals(entry.getKey()) && !"token".equals(entry.getKey())) {
                           parameterMap.put(entry.getKey(), entry.getValue());
                        }
                     }
                  }

                  oaResp = this.doRest(parameterMap);
               }
            } else if (strXmldoc != null) {
               oaResp = this.doPost(parameterMap);
            } else {
               if (oaResp == null) {
                  oaResp = new OpenApiResponse();
               }

               oaResp.setErrorCode(OpenApiExceptionCode.E001[0]);
               oaResp.setMessage(OpenApiExceptionCode.E001[1]);
            }
         }

         body = oaResp.serializeXST();
         logger.debug(body);
         mav.addObject("xmldoc", body.toString());
      } catch (Exception var15) {
         if (oaResp == null) {
            oaResp = new OpenApiResponse();
         }

         oaResp.setErrorCode(OpenApiExceptionCode.E201[0]);
         oaResp.setMessage(OpenApiExceptionCode.E201[1] + var15.getMessage());
      }

      return mav;
   }

   private Hashtable getParameters(HttpServletRequest request) {
      Hashtable map = new Hashtable();
      Enumeration names = request.getParameterNames();

      while(names.hasMoreElements()) {
         String name = (String)names.nextElement();
         map.put(name, request.getParameter(name));
      }

      return map;
   }

   private OpenApiResponse doRest(Hashtable requestParameterMap) {
      Object serviceBean = null;
      OpenApiResponse oaResp = new OpenApiResponse();

      try {
         String serviceId = ((String)requestParameterMap.get("service")).trim();
         OpenApiRequest oaReq = new OpenApiRequest();
         oaReq.parepareService(serviceId);
         oaReq.prepareUserInfo((String)requestParameterMap.get("loginId"), (String)requestParameterMap.get("email"), (String)requestParameterMap.get("epId"), (String)requestParameterMap.get("socialNumber"));
         OpenApiServiceMethod oMethod = oaReq.getServiceMethod();
         OpenApiParameter[] oaParams = oMethod.getParameters();
         oaReq.setParameter(this.getParameterList(requestParameterMap, oaParams));
         if (!OpenApiServiceManager.getInstance().isAllowed(serviceId)) {
            oaResp.setErrorCode(OpenApiExceptionCode.E002[0]);
            oaResp.setMessage(OpenApiExceptionCode.E002[1]);
            return oaResp;
         }

         String token = (String)requestParameterMap.get("token");
         TokenRegistry tr = TokenRegistry.getTokenRegistry();
         serviceBean = this.getService(oaReq.getServiceId());
         if (OpenApiServiceManager.getInstance().isRequireUserInfo(serviceId) && oaReq.getUserInfo() == null) {
            oaResp.setErrorCode(OpenApiExceptionCode.E005[0]);
            oaResp.setMessage(OpenApiExceptionCode.E005[1]);
            return oaResp;
         }

         if (OpenApiServiceManager.getInstance().isRequireDeviceType(serviceId)) {
            ArrayList parameter = (ArrayList)oaReq.getParameter();
            boolean hasDeviceType = false;
            Iterator var12 = parameter.iterator();

            while(var12.hasNext()) {
               Object p = var12.next();
               if (this.checkDeviceType(p)) {
                  hasDeviceType = true;
                  break;
               }
            }

            if (!hasDeviceType) {
               if (serviceId.equals("PremiumScheduleService.addProgramWithBasicInformation")) {
                  parameter.add("N/A");
                  oaReq.setParameter(parameter);
                  requestParameterMap.put("deviceType", "N/A");
               } else {
                  parameter.add("iPLAYER");
                  oaReq.setParameter(parameter);
                  requestParameterMap.put("deviceType", "iPLAYER");
               }
            }
         }

         if (serviceBean != null) {
            Method method = ClassUtil.getMethod(serviceBean, oaReq.getServiceMethodId());
            if (token != null) {
               try {
                  ClassUtil.invokeSetToken(serviceBean, token);
               } catch (NoSuchMethodException var20) {
                  logger.error("", var20);
               }
            }

            Object[] params = null;
            if (method != null) {
               Class[] pt = method.getParameterTypes();
               if (pt.length != oaReq.getParameterSize()) {
                  oaResp.setErrorCode(OpenApiExceptionCode.E004[0]);
                  oaResp.setMessage(OpenApiExceptionCode.E004[1]);
                  return oaResp;
               }

               if (pt.length > 0) {
                  try {
                     params = this.getParameters(oaReq, pt, oaParams);

                     for(int i = 0; i < oaParams.length; ++i) {
                        String[] classString = oaParams[i].getType().split("\\.");
                        if (classString.length > 0) {
                           String strType = classString[classString.length - 1];
                           String strParamName = oaParams[i].getParamName();
                           OpenApiParameterValidator openApiParameterValidator = new OpenApiParameterValidator();
                           if (!strType.equals("File")) {
                              String xmlValue = (String)requestParameterMap.get(strParamName);
                              if (xmlValue == null) {
                                 oaResp.setErrorCode(OpenApiExceptionCode.V002[0]);
                                 oaResp.setMessage(OpenApiExceptionCode.V002[1]);
                                 return oaResp;
                              }

                              String errorMessage = openApiParameterValidator.isValidator(xmlValue, strType);
                              if (errorMessage != null) {
                                 oaResp.setErrorCode(OpenApiExceptionCode.V002[0]);
                                 oaResp.setMessage(OpenApiExceptionCode.V002[1] + " (" + errorMessage + ")");
                                 return oaResp;
                              }
                           }
                        }
                     }
                  } catch (Exception var24) {
                     logger.error("", var24);
                     throw var24;
                  }
               }
            }

            logger.info("========================================");
            logger.info("token " + token);
            logger.info(oaReq.getServiceId() + "." + oaReq.getServiceMethodId());

            for(int i = 0; i < oaReq.getParameterSize(); ++i) {
               logger.info(" [" + oMethod.getParameters()[i].getParamName() + "] " + oaReq.getParameter().get(i));
            }

            if (!oMethod.isNoAuthCheck()) {
               UserContainer uc = (UserContainer)tr.getUserObject(token);
               SaasController saasController = new SaasController(uc, oaReq.serviceId, oaReq.serviceMethodId, uc.getScope(), oaReq, oaParams);
               saasController.isValid();
            }

            try {
               Object result = this.invokeOpenApiServiceMethod(serviceBean, oaReq.getServiceMethodId(), params);
               oaResp.setResult(result);
               return oaResp;
            } catch (IllegalAccessException var21) {
               oaResp.setErrorCode(OpenApiExceptionCode.E991[0]);
               if (var21.getMessage() == null) {
                  oaResp.setMessage(OpenApiExceptionCode.E991[1]);
               } else {
                  oaResp.setMessage(var21.getMessage());
               }
            } catch (NoSuchMethodException var22) {
               oaResp.setErrorCode(OpenApiExceptionCode.E992[0]);
               if (var22.getMessage() == null) {
                  oaResp.setMessage(OpenApiExceptionCode.E992[1]);
               } else {
                  oaResp.setMessage(var22.getMessage());
               }
            } catch (InvocationTargetException var23) {
               oaResp.setErrorCode(OpenApiExceptionCode.E993[0]);
               if (var23.getTargetException() instanceof OpenApiException) {
                  OpenApiException oae = (OpenApiException)var23.getTargetException();
                  oaResp.setMessage(oae.getMessage());
                  oaResp.setErrorCode(oae.getCode());
               } else if (var23.getTargetException() != null) {
                  oaResp.setMessage(var23.getTargetException().getMessage());
               } else if (var23.getMessage() == null) {
                  oaResp.setMessage(OpenApiExceptionCode.E993[1]);
               } else {
                  oaResp.setMessage(var23.getMessage());
               }
            }
         }
      } catch (Exception var25) {
         if (var25 instanceof OpenApiException) {
            oaResp.setErrorCode(((OpenApiException)var25).getCode());
            oaResp.setMessage(var25.getMessage());
         } else {
            oaResp.setErrorCode(OpenApiExceptionCode.E999[0]);
            if (var25.getMessage() == null) {
               oaResp.setMessage(OpenApiExceptionCode.E999[1]);
            } else {
               oaResp.setMessage(var25.getMessage());
            }
         }
      }

      return oaResp;
   }

   private boolean checkDeviceType(Object p) {
      return p.equals("iPLAYER") || p.equals("SPLAYER") || p.equals("S2PLAYER") || p.equals("S3PLAYER") || p.equals("S4PLAYER") || p.equals("S5PLAYER") || p.equals("S6PLAYER") || p.equals("S7PLAYER") || p.equals("S9PLAYER") || p.equals("S10PLAYER") || p.equals("ALL") || p.equals("LPLAYER") || p.equals("APLAYER") || p.equals("WPLAYER") || p.equals("3rdPartyPLAYER");
   }

   private Object[] getParameters(OpenApiRequest oaReq, Class[] pt, OpenApiParameter[] oaParams) throws Exception {
      List parameters = oaReq.getParameter();
      return XmlTransferableUtil.mappingParameters(parameters, pt, oaParams);
   }

   private List getParameterList(Hashtable request, OpenApiParameter[] params) {
      List list = new ArrayList();
      Object para = null;

      for(int j = 0; j < params.length; ++j) {
         if (params[j].getParamName() != null) {
            para = request.get(params[j].getParamName());
            if (para != null) {
               list.add(para);
            }
         }
      }

      return list;
   }

   private OpenApiResponse doPost(Hashtable requestParameterMap) {
      Object serviceBean = null;
      String strXmldoc = (String)requestParameterMap.get("xml");
      TokenRegistry tr = TokenRegistry.getTokenRegistry();
      OpenApiResponse oaResp = new OpenApiResponse();

      try {
         OpenApiRequest oaReq = this.parseRequestXML(strXmldoc);
         String serviceId = oaReq.getServiceId() + "." + oaReq.getServiceMethodId();
         if (!OpenApiServiceManager.getInstance().isAllowed(serviceId)) {
            oaResp.setErrorCode(OpenApiExceptionCode.E002[0]);
            oaResp.setMessage(OpenApiExceptionCode.E002[1]);
            return oaResp;
         }

         String token = (String)requestParameterMap.get("token");
         UserContainer uc = (UserContainer)tr.getUserObject(token);
         serviceBean = this.getService(oaReq.getServiceId());
         if (OpenApiServiceManager.getInstance().isRequireUserInfo(serviceId) && oaReq.getUserInfo() == null) {
            oaResp.setErrorCode(OpenApiExceptionCode.E005[0]);
            oaResp.setMessage(OpenApiExceptionCode.E005[1]);
            return oaResp;
         }

         Object result;
         if (OpenApiServiceManager.getInstance().isRequireDeviceType(serviceId)) {
            ArrayList parameter = (ArrayList)oaReq.getParameter();
            boolean hasDeviceType = false;
            Iterator var12 = parameter.iterator();

            while(var12.hasNext()) {
               result = var12.next();
               if (this.checkDeviceType(result)) {
                  hasDeviceType = true;
                  break;
               }
            }

            if (!hasDeviceType) {
               if (serviceId.equals("PremiumScheduleService.addProgramWithBasicInformation")) {
                  parameter.add("N/A");
                  oaReq.setParameter(parameter);
                  requestParameterMap.put("deviceType", "N/A");
               } else {
                  parameter.add("iPLAYER");
                  oaReq.setParameter(parameter);
                  requestParameterMap.put("deviceType", "iPLAYER");
               }
            }
         }

         if (serviceBean != null) {
            Method method = ClassUtil.getMethod(serviceBean, oaReq.getServiceMethodId());
            if (token != null) {
               try {
                  ClassUtil.invokeSetToken(serviceBean, token);
               } catch (NoSuchMethodException var15) {
                  logger.error("", var15);
               }
            }

            Object[] params = null;
            if (method != null) {
               Class[] pt = method.getParameterTypes();
               if (pt.length != oaReq.getParameterSize()) {
                  oaResp.setErrorCode(OpenApiExceptionCode.E004[0]);
                  oaResp.setMessage(OpenApiExceptionCode.E004[1]);
                  return oaResp;
               }

               if (oaReq.getParameterSize() > 0) {
                  params = oaReq.getParameter().toArray();
               }

               try {
                  result = this.invokeOpenApiServiceMethod(serviceBean, oaReq.getServiceMethodId(), params);
                  oaResp.setResult(result);
                  return oaResp;
               } catch (IllegalAccessException var16) {
                  logger.error("", var16);
                  oaResp.setErrorCode(OpenApiExceptionCode.E991[0]);
                  if (var16.getMessage() == null) {
                     oaResp.setMessage(OpenApiExceptionCode.E991[1]);
                  } else {
                     oaResp.setMessage(var16.getMessage());
                  }
               } catch (NoSuchMethodException var17) {
                  logger.error("", var17);
                  oaResp.setErrorCode(OpenApiExceptionCode.E992[0]);
                  if (var17.getMessage() == null) {
                     oaResp.setMessage(OpenApiExceptionCode.E992[1]);
                  } else {
                     oaResp.setMessage(var17.getMessage());
                  }
               } catch (InvocationTargetException var18) {
                  logger.error("", var18);
                  oaResp.setErrorCode(OpenApiExceptionCode.E993[0]);
                  if (var18.getTargetException() instanceof OpenApiException) {
                     OpenApiException oae = (OpenApiException)var18.getTargetException();
                     oaResp.setMessage(oae.getMessage());
                     oaResp.setErrorCode(oae.getCode());
                  } else if (var18.getTargetException() != null) {
                     oaResp.setMessage(var18.getTargetException().getMessage());
                  } else if (var18.getMessage() == null) {
                     oaResp.setMessage(OpenApiExceptionCode.E993[1]);
                  } else {
                     oaResp.setMessage(var18.getMessage());
                  }
               }
            }
         }
      } catch (Exception var19) {
         if (var19 instanceof OpenApiException) {
            oaResp.setErrorCode(((OpenApiException)var19).getCode());
            oaResp.setMessage(var19.getMessage());
         } else {
            oaResp.setErrorCode(OpenApiExceptionCode.E999[0]);
            if (var19.getMessage() == null) {
               oaResp.setMessage(OpenApiExceptionCode.E999[1]);
            } else {
               oaResp.setMessage(var19.getMessage());
            }
         }
      }

      return oaResp;
   }

   private OpenApiResponse doUploadContent(MultipartFile contentFile, Hashtable requestParameterMap) {
      OpenApiResponse oaResp = new OpenApiResponse();
      if (contentFile != null && requestParameterMap != null) {
         ContentInfo cmsDao = ContentInfoImpl.getInstance();
         ContentCodeInfo codeDao = ContentCodeInfoImpl.getInstance();
         UserInfo uInfo = UserInfoImpl.getInstance();
         FileManager fileManager = FileManagerImpl.getInstance();
         boolean bExistFile = false;
         boolean bMustAddContent = false;
         boolean contentsApprovalEnable = false;
         String token = (String)requestParameterMap.get("token");
         TokenRegistry tr = TokenRegistry.getTokenRegistry();
         UserContainer uc = (UserContainer)tr.getUserObject(token);
         String userId = uc.getUser().getUser_id();
         String contentName = "test";
         String categoryId = "";
         if (requestParameterMap.get("groupId") == null) {
            oaResp.setMessage("Enter content Group Id");
            return oaResp;
         }

         if (requestParameterMap.get("categoryIds") == null) {
            categoryId = null;
         } else {
            categoryId = (String)requestParameterMap.get("categoryIds");
         }

         long groupId = Long.valueOf((String)requestParameterMap.get("groupId"));

         long orgCreatorId;
         try {
            orgCreatorId = cmsDao.getRootId(userId);
         } catch (SQLException var57) {
            oaResp.setMessage(var57.getMessage());
            return oaResp;
         }

         String CONTENTS_HOME = null;
         String THUMBNAIL_HOME = null;

         try {
            CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
            THUMBNAIL_HOME = CommonConfig.get("THUMBNAIL_HOME").replace('/', File.separatorChar);
            File cmsHome = SecurityUtils.getSafeFile(CONTENTS_HOME);
            if (!cmsHome.exists()) {
               cmsHome.mkdir();
            }
         } catch (ConfigException var56) {
            logger.error("", var56);
         }

         try {
            String fileID = UUID.randomUUID().toString().toUpperCase();
            String contentId = UUID.randomUUID().toString().toUpperCase();
            String fileName = contentFile.getOriginalFilename();
            File folder = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separatorChar + fileID, (String)null));
            File file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separatorChar + fileID + File.separatorChar + fileName, (String)null));
            folder.mkdir();
            contentFile.transferTo(file);
            contentName = fileName.substring(0, fileName.lastIndexOf("."));
            String fext = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
            String mediaType = null;
            long fileSize = file.length();
            mediaType = StrUtils.nvl(codeDao.getMediaTypeByFileType(fext.toUpperCase()));
            if (mediaType == null) {
               oaResp.setMessage("NO Media Type");
               return oaResp;
            }

            String hashId = FileUtils.getHash(file);
            ContentFile cmsContentFile = new ContentFile();
            if (!fext.equalsIgnoreCase("mpeg") && !fext.equalsIgnoreCase("mpg") && !fext.equalsIgnoreCase("wmv") && !fext.equalsIgnoreCase("avi") && !fext.equalsIgnoreCase("mov") && !fext.equalsIgnoreCase("mp4") && !fext.equalsIgnoreCase("asf")) {
               cmsContentFile.setIs_streaming("N");
            } else {
               cmsContentFile.setIs_streaming("Y");
            }

            if (cmsDao.isExistFileByHash(fileName, fileSize, hashId)) {
               bExistFile = true;
            }

            if (bExistFile) {
               ContentFile existFile = cmsDao.getMainFileInfo(contentId);
               if (existFile == null) {
                  existFile = cmsDao.getMainFileInfoOfTmpVer(contentId);
               }

               if (existFile != null) {
                  if (existFile.getFile_name().equals(cmsContentFile.getFile_name()) && existFile.getFile_size().equals(cmsContentFile.getFile_size()) && existFile.getHash_code().equalsIgnoreCase(cmsContentFile.getHash_code())) {
                     bMustAddContent = false;
                  } else {
                     bMustAddContent = true;
                  }
               }
            } else {
               bMustAddContent = true;
            }

            if (bExistFile) {
               file.delete();
               fileID = cmsDao.getFileIDByHash(fileName, fileSize, hashId);
               file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separatorChar + fileID + File.separatorChar + fileName, (String)null));
            }

            cmsContentFile.setFile_id(fileID);
            cmsContentFile.setHash_code(hashId);
            cmsContentFile.setFile_type("MAIN");
            cmsContentFile.setFile_name(fileName);
            cmsContentFile.setFile_size(fileSize);
            cmsContentFile.setCreator_id(userId);
            cmsContentFile.setFile_path(CONTENTS_HOME + File.separator + fileID);
            Content content = new Content();
            content.setVersion_id(1L);
            content.setContent_id(contentId);
            content.setGroup_id(groupId);
            content.setShare_flag(1);
            content.setContent_meta_data("");
            content.setCreator_id(userId);
            content.setMedia_type(mediaType);
            content.setOrganization_id(uInfo.getRootGroupIdByUserId(userId));
            content.setTotal_size(fileSize);
            content.setIs_active("Y");
            content.setOrg_creator_id(String.valueOf(orgCreatorId));
            ContentFile cmsThumbFile = null;
            logger.error("[ContentFileUploadServlet] content name : " + content.getContent_name() + " content Media Type : " + content.getMedia_type());
            if (content.getMedia_type().equalsIgnoreCase("LFD") || content.getMedia_type().equalsIgnoreCase("LFT") || content.getMedia_type().equalsIgnoreCase("VWL") || content.getMedia_type().equalsIgnoreCase("PROM")) {
               oaResp.setMessage("Not Support Type");
               return oaResp;
            }

            File fileCmsHome;
            File fileCmsFile;
            String image_url;
            String thumb_url;
            if (content.getMedia_type().equalsIgnoreCase("SOUND")) {
               content.setThumb_file_id("SOUND_THUMBNAIL");
               content.setThumb_file_name("SOUND_THUMBNAIL.PNG");
               String[] fileMeta;
               String play_time;
               if (fext.equals("MP3")) {
                  try {
                     fileMeta = fileManager.getFileMeta(file);
                     if (fileMeta[0] != null) {
                        play_time = fileMeta[0];
                        if (play_time.length() > 8) {
                           content.setPlay_time(play_time.substring(0, 8));
                           content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                        } else {
                           content.setPlay_time(play_time);
                        }
                     } else {
                        play_time = fileManager.getMP3PlayTime(file);
                        if (play_time.length() > 8) {
                           content.setPlay_time(play_time.substring(0, 8));
                           content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                        } else {
                           content.setPlay_time(play_time);
                        }
                     }
                  } catch (Exception var55) {
                     content.setPlay_time("");
                  }
               } else {
                  fileMeta = fileManager.getFileMeta(file);
                  if (fileMeta[0] != null) {
                     play_time = fileMeta[0];
                     if (play_time.length() > 8) {
                        content.setPlay_time(play_time.substring(0, 8));
                        content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                     } else {
                        content.setPlay_time(play_time);
                     }
                  } else {
                     content.setPlay_time("");
                  }
               }
            } else if (!content.getMedia_type().equalsIgnoreCase("IMAGE") && !content.getMedia_type().equalsIgnoreCase("MOVIE")) {
               if (content.getMedia_type().equalsIgnoreCase("OFFICE")) {
                  content.setThumb_file_id("OFFICE_THUMBNAIL");
                  content.setThumb_file_name("OFFICE_THUMBNAIL.PNG");
               } else if (content.getMedia_type().equalsIgnoreCase("FLASH")) {
                  content.setThumb_file_id("FLASH_THUMBNAIL");
                  content.setThumb_file_name("FLASH_THUMBNAIL.PNG");
               } else if (content.getMedia_type().equalsIgnoreCase("PDF")) {
                  content.setThumb_file_id("PDF_THUMBNAIL");
                  content.setThumb_file_name("PDF_THUMBNAIL.PNG");
               } else {
                  content.setThumb_file_id("ETC_THUMBNAIL");
                  content.setThumb_file_name("ETC_THUMBNAIL.PNG");
               }
            } else {
               cmsThumbFile = new ContentFile();
               String thumbnailFileId = UUID.randomUUID().toString().toUpperCase();
               fileCmsHome = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separatorChar + thumbnailFileId, (String)null));
               fileCmsHome.mkdir();
               Map thumbnailMap = fileManager.createThumbnailFile(file, thumbnailFileId, content);
               fileCmsFile = null;
               if (thumbnailMap != null && thumbnailMap.get("file") != null) {
                  fileCmsFile = (File)thumbnailMap.get("file");
               }

               image_url = FileUtils.getHash(fileCmsFile);
               if (image_url == null) {
                  content.setThumb_file_id("NOIMAGE_THUMBNAIL");
                  content.setThumb_file_name("NOIMAGE_THUMBNAIL.PNG");
               } else {
                  if (cmsDao.isExistFileByHash(fileName + ".png", fileCmsFile.length(), image_url)) {
                     thumbnailFileId = cmsDao.getFileIDByHash(fileName + ".png", fileCmsFile.length(), image_url);
                  }

                  content.setThumb_file_id(thumbnailFileId);
                  content.setThumb_file_name(fileName + ".png");
               }

               cmsThumbFile.setFile_id(thumbnailFileId);
               cmsThumbFile.setHash_code(image_url);
               cmsThumbFile.setFile_type("THUMBNAIL");
               cmsThumbFile.setFile_name(fileName + ".png");
               cmsThumbFile.setFile_size(fileCmsFile.length());
               cmsThumbFile.setCreator_id(userId);
               cmsThumbFile.setFile_path(CONTENTS_HOME + File.separator + thumbnailFileId);
               if (thumbnailMap != null && thumbnailMap.get("resolution") != null) {
                  content.setResolution((String)thumbnailMap.get("resolution"));
               }

               if (thumbnailMap != null && thumbnailMap.get("playTime") != null) {
                  thumb_url = (String)thumbnailMap.get("playTime");
                  if (thumb_url.length() > 8) {
                     content.setPlay_time(thumb_url.substring(0, 8));
                     content.setPlay_time_milli(thumb_url.substring(9, thumb_url.length()));
                  } else {
                     content.setPlay_time(thumb_url);
                  }
               }
            }

            if (contentName.length() > 55) {
               contentName = contentName.substring(0, 55);
            }

            content.setContent_name(contentName);
            content.setMain_file_id(fileID);
            content.setMain_file_Extension(fext.toUpperCase());
            if (contentsApprovalEnable) {
               if (content.getMain_file_Extension().equalsIgnoreCase("LFT")) {
                  content.setApproval_status("APPROVED");
               } else {
                  AbilityUtils abilityUtils = new AbilityUtils();
                  if (abilityUtils.isContentApprovalAuthority(userId)) {
                     content.setApproval_status("APPROVED");
                  } else {
                     content.setApproval_status("UNAPPROVED");
                  }
               }
            } else {
               content.setApproval_status("APPROVED");
            }

            List fileListToSave = new ArrayList();
            fileListToSave.add(cmsContentFile);
            if (cmsThumbFile != null) {
               fileListToSave.add(cmsThumbFile);
            }

            content.setArr_file_list(fileListToSave);
            if (categoryId != null) {
               CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
               categoryInfo.setCategoryFromContentId(categoryId, contentId);
            }

            cmsDao.addContent(content);

            try {
               fileCmsHome = SecurityUtils.getSafeFile(THUMBNAIL_HOME);
               if (!fileCmsHome.exists()) {
                  boolean fSuccess = fileCmsHome.mkdir();
                  if (!fSuccess) {
                     logger.error("mkdir Fail");
                  }
               }

               String filePath = THUMBNAIL_HOME + File.separator + content.getThumb_file_id();
               fileCmsFile = SecurityUtils.getSafeFile(filePath);
               if (!fileCmsFile.exists()) {
                  boolean fSuccess = fileCmsFile.mkdir();
                  if (!fSuccess) {
                     logger.error("mkdir Fail");
                  }
               }

               image_url = CONTENTS_HOME + "/" + content.getThumb_file_id() + "/" + content.getThumb_file_name();
               thumb_url = THUMBNAIL_HOME + "/" + content.getThumb_file_id() + "/" + content.getThumb_file_name();
               File checkFile = SecurityUtils.getSafeFile(thumb_url);
               if (!checkFile.exists()) {
                  File thumbFile = SecurityUtils.getSafeFile(image_url);
                  if (thumbFile.exists()) {
                     BufferedImage bufferedImage = ImageIO.read(thumbFile);
                     if (bufferedImage == null) {
                        logger.error("[ContentFileUploadServlet] null thumbnail image : " + thumbFile.getPath());
                        throw new NullPointerException();
                     }

                     int orgWidth = bufferedImage.getWidth();
                     int orgHeight = bufferedImage.getHeight();
                     int smallWidth = 50;
                     int smallHeight = 38;
                     int mediumWidth = 165;
                     int mediumHeight = 109;
                     if ((long)(orgWidth / mediumWidth) > (long)(orgHeight / mediumHeight)) {
                        mediumHeight = orgHeight * mediumWidth / orgWidth;
                        if (mediumHeight % 2 != 0) {
                           ++mediumHeight;
                        }
                     } else {
                        mediumWidth = orgWidth * mediumHeight / orgHeight;
                        if (mediumWidth % 2 != 0) {
                           ++mediumWidth;
                        }
                     }

                     if ((long)(orgWidth / smallWidth) > (long)(orgHeight / smallHeight)) {
                        smallHeight = orgHeight * smallWidth / orgWidth;
                        if (smallHeight % 2 != 0) {
                           ++smallHeight;
                        }
                     } else {
                        smallWidth = orgWidth * smallHeight / orgHeight;
                        if (smallWidth % 2 != 0) {
                           ++smallWidth;
                        }
                     }

                     if (mediumWidth < 1) {
                        mediumWidth = 1;
                     }

                     if (mediumHeight < 1) {
                        mediumHeight = 1;
                     }

                     if (smallWidth < 1) {
                        smallWidth = 1;
                     }

                     if (smallHeight < 1) {
                        smallHeight = 1;
                     }

                     File thumb_File = SecurityUtils.getSafeFile(thumb_url);
                     File thumb_smallFile = SecurityUtils.getSafeFile(thumb_url + "_SMALL.PNG");
                     File thumb_mediumFile = SecurityUtils.getSafeFile(thumb_url + "_MEDIUM.PNG");
                     ImageIO.write(fileManager.createResizedCopy(bufferedImage, orgWidth, orgHeight), "PNG", thumb_File);
                     ImageIO.write(fileManager.createResizedCopy(bufferedImage, smallWidth, smallHeight), "PNG", thumb_smallFile);
                     ImageIO.write(fileManager.createResizedCopy(bufferedImage, mediumWidth, mediumHeight), "PNG", thumb_mediumFile);
                     Map hdThumbnailMap = fileManager.createThumbnailFile(file, THUMBNAIL_HOME, content.getThumb_file_id(), content, 1280, 720, "_HD.PNG");
                     if (hdThumbnailMap != null && hdThumbnailMap.get("status") != null && hdThumbnailMap.get("status").equals("error")) {
                        logger.error("HD Thumbnail create error.");
                     }
                  }
               }
            } catch (Exception var58) {
               oaResp.setMessage("ContentFileUploadServlet] error create thumbnail");
               return oaResp;
            }

            cmsDao.setActiveVersion(contentId, true);
            cmsDao.setContentInfo(content.getContent_id(), content.getContent_name(), content.getContent_meta_data(), content.getShare_flag());
            cmsDao.setContentGroup(content.getContent_id(), content.getGroup_id());
         } catch (IllegalStateException var59) {
            logger.error("", var59);
         } catch (IOException var60) {
            logger.error("", var60);
         } catch (SQLException var61) {
            logger.error("", var61);
         } catch (ConfigException var62) {
            logger.error("", var62);
         } catch (Exception var63) {
            logger.error("", var63);
         }
      }

      oaResp.setMessage("Upload successfully");
      return oaResp;
   }

   private Object invokeOpenApiServiceMethod(Object serviceBean, String serviceMethodId, Object[] params) throws Exception {
      Object result = null;
      String serviceId = serviceBean.getClass().getSimpleName();

      try {
         result = MethodUtils.invokeMethod(serviceBean, serviceMethodId, params);
      } catch (Exception var8) {
         if (ExceptionUtils.indexOfThrowable(var8, AccessDeniedException.class) < 0) {
            throw var8;
         }

         if (!OpenApiServiceMethodsToNullAuthorization.shouldReturnNull(serviceId, serviceMethodId)) {
            String[] exceptionCode = OpenApiExceptionCode.U110;
            if (OpenApiServiceMethodsToErrorCodesMapper.containsErrorCodeFor(serviceId, serviceMethodId)) {
               exceptionCode = OpenApiServiceMethodsToErrorCodesMapper.getErrorCodeForServiceMethod(serviceId, serviceMethodId);
            }

            throw new OpenApiServiceException(exceptionCode[0], exceptionCode[1]);
         }
      }

      return result;
   }

   private OpenApiRequest parseRequestXML(String strXmldoc) throws XmlFormatException, Exception {
      return OpenApiRequest.parseInstance(strXmldoc);
   }

   private Object getService(String serviceId) {
      return this.springBeansProvider.getBean("openApi" + serviceId);
   }

   private File getFile(HttpServletRequest request) throws FileUploadException {
      String realPath = "c:/Temp";
      DSCFileItemFactory factory = new DSCFileItemFactory(10240, SecurityUtils.getSafeFile(realPath));
      ServletFileUpload upload = new ServletFileUpload(factory);
      List items = upload.parseRequest(request);
      Iterator iter = items.iterator();
      DSCFileItem item = null;
      if (iter.hasNext()) {
         item = (DSCFileItem)iter.next();
         return item.getDSCTempFile();
      } else {
         return null;
      }
   }
}
