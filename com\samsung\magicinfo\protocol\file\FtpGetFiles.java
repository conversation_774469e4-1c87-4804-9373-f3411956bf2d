package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.FileUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentXmlManager;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.msgqueue.producer.MsgProducer;
import com.samsung.magicinfo.msgqueue.producer.MsgProducerImpl;
import com.samsung.magicinfo.msgqueue.util.AMQUtil;
import com.samsung.magicinfo.msgqueue.vo.MsgVO;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.scheduler.ScheduleManager;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.StringWriter;
import java.nio.CharBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.Charset;
import java.nio.charset.CharsetEncoder;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.logging.log4j.Logger;
import org.apache.xml.security.utils.Base64;
import org.quartz.JobDetail;
import org.quartz.Scheduler;
import org.quartz.SimpleTrigger;
import org.springframework.jms.JmsException;

public class FtpGetFiles {
   private Logger logger = LoggingManagerV2.getLogger(FtpGetFiles.class);
   String miUserId = "";
   long groupId = 0L;
   String contentId = "";
   String contentName = "";
   String serverIp = "";
   int serverPort = 0;
   String loginId = "";
   String password = "";
   String localPathByIp = "";
   String directory = "";
   long refreshInterval = 0L;
   boolean scheduledJob = false;
   String canRefresh = "";
   long loginRetryMaxCount = 0L;
   long loginRetryCount = 0L;
   String canLoginRetry = "";
   boolean result = false;
   List fileListToSave = new ArrayList();
   FTPClient client = null;
   ContentInfo contentInfo = ContentInfoImpl.getInstance();
   String CONTENTS_HOME = "";
   boolean editMode = false;
   boolean settingChanged = false;
   List remoteFiles = new ArrayList();
   long totalSizeOfFiles = 0L;
   boolean fileChanged = false;
   String mainFileId = UUID.randomUUID().toString().toUpperCase();
   String characterEncoding = "";
   boolean isFilesToDownload = false;
   MsgVO msgVO = null;
   String pollingStatus = "SUCCESS";
   String statusDescription = "";

   FtpGetFiles(String miUserId, long groupId, String contentId, String contentName, String serverIp, int serverPort, String loginId, String password, String localPathByIp, String directory, long refreshInterval, boolean scheduledJob, String canRefresh, long loginRetryMaxCount, String canLoginRetry) {
      super();
      this.miUserId = miUserId;
      this.groupId = groupId;
      this.contentId = contentId;
      this.contentName = contentName;
      this.serverIp = serverIp;
      this.serverPort = serverPort;
      this.loginId = loginId;
      this.password = password;
      this.localPathByIp = localPathByIp;
      this.directory = directory;
      this.refreshInterval = refreshInterval;
      this.scheduledJob = scheduledJob;
      this.canRefresh = canRefresh;
      this.loginRetryMaxCount = loginRetryMaxCount;
      this.canLoginRetry = canLoginRetry;
   }

   FtpGetFiles(MsgVO msgVO) {
      super();
      this.miUserId = msgVO.getMiUserId();
      this.groupId = msgVO.getGroupId();
      this.contentId = msgVO.getContentId();
      this.contentName = msgVO.getContentName();
      this.serverIp = msgVO.getServerIp();
      this.serverPort = msgVO.getServerPort();
      this.loginId = msgVO.getLoginId();
      this.password = msgVO.getPassword();
      this.localPathByIp = msgVO.getLocalPathByIp();
      this.directory = msgVO.getDirectory();
      this.refreshInterval = msgVO.getRefreshInterval();
      this.scheduledJob = msgVO.isScheduledJob();
      this.canRefresh = msgVO.getCanRefresh();
      this.loginRetryMaxCount = msgVO.getLoginRetryMaxCount();
      this.loginRetryCount = msgVO.getLoginRetryCount();
      this.canLoginRetry = msgVO.getCanLoginRetry();
      this.result = msgVO.isResult();
      this.fileListToSave.addAll(msgVO.getFileListToSave());
      this.CONTENTS_HOME = msgVO.getCONTENTS_HOME();
      this.editMode = msgVO.isEditMode();
      this.settingChanged = msgVO.isSettingChanged();
      this.remoteFiles.addAll(msgVO.getRemoteFiles());
      this.totalSizeOfFiles = msgVO.getTotalSizeOfFiles();
      this.fileChanged = msgVO.isFileChanged();
      this.mainFileId = msgVO.getMainFileId();
      this.characterEncoding = msgVO.getCharacterEncoding();
      this.isFilesToDownload = msgVO.isFilesToDownload();
      this.msgVO = (MsgVO)msgVO.clone();
      this.pollingStatus = msgVO.getPollingStatus();
      this.statusDescription = msgVO.getStatusDescription();
   }

   public boolean getFtpFiles() throws SQLException {
      boolean isSuccess = false;
      this.logger.error("#####[INFO][MagicInfo_FTP] STARTED to get FTP Files : " + this.contentName + "[" + this.contentId + "]");

      try {
         this.CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
         if (this.checkEditMode()) {
            if (!this.accessToRemoteServer()) {
               this.logger.error("@@@@@[MagicInfo_FTP] FAILED to access to RemoteServer : " + this.contentName + "[" + this.contentId + "]");
               this.result = false;
            } else if (!this.getFileList()) {
               this.logger.error("@@@@@[MagicInfo_FTP] FAILED to get File List : " + this.contentName + "[" + this.contentId + "]");
               this.client.logout();
               this.result = false;
            } else {
               this.checkChangedFiles();
               this.removeChangedTempFiles();
               this.updateContentInfo();
               if (AMQUtil.isEnabledRCDS() && this.isFilesToDownload) {
                  isSuccess = this.requestDownload();
               } else {
                  this.downloadChangedFiles();
                  this.createCSDFile();
                  this.updatePlaylistInfo();
                  this.setScheduleJobFtp();
                  this.setScheduleJobDlk();
               }
            }
         }
      } catch (Exception var11) {
         this.logger.error("[MagicInfo_FTP] " + var11.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var11);
         this.logger.error(this.getMembers());
         if ("SUCCESS".equals(this.pollingStatus)) {
            this.pollingStatus = "FAIL";
            this.statusDescription = "UNEXPECTED ERROR";
         }
      } finally {
         if (!AMQUtil.isEnabledRCDS() || !isSuccess) {
            this.contentInfo.setIsReadyForNextFtpThread("Y", this.contentId);
            this.createPollingInfo();
         }

         if (this.client != null && this.client.isConnected()) {
            try {
               this.client.disconnect();
               this.result = true;
            } catch (Exception var10) {
               this.logger.error("[MagicInfo_FTP] " + var10.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var10);
               this.logger.error(this.getMembers());
            }
         }

      }

      this.logger.error("#####[INFO][MagicInfo_FTP] ENDED to get FTP Files : " + this.contentName + "[" + this.contentId + "][" + this.result + "]");
      return this.result;
   }

   public boolean getFtpFilesNext() throws SQLException {
      this.logger.error("#####[INFO][MagicInfo_FTP] STARTED to get FTP Files NEXT!! : " + this.contentName + "[" + this.contentId + "]");

      try {
         if (this.editMode && this.settingChanged) {
            this.contentInfo.updateFtpSettingByContentId(this.contentId, this.contentName, this.serverIp, this.serverPort, this.loginId, Base64.encode(this.password.getBytes()), this.directory, this.refreshInterval, this.canRefresh, this.loginRetryMaxCount, this.loginRetryCount, this.canLoginRetry);
            if ("N".equals(this.canRefresh)) {
               String schedulerJobName = "FTP_" + this.contentId;
               String schedulerJobGroup = "UpdateFtpContentService";
               CommonUtils.deleteJob(schedulerJobName, schedulerJobGroup);
            }
         }

         if ("SUCCESS".equals(this.pollingStatus)) {
            this.updateDownloadedFileInfos();
            this.updatePlaylistInfo();
            this.createCSDFile();
            this.setScheduleJobFtp();
            this.setScheduleJobDlk();
         }
      } catch (Exception var6) {
         this.logger.error("[MagicInfo_FTP_Next] " + var6.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var6);
         this.logger.error(this.getMembers());
         if ("SUCCESS".equals(this.pollingStatus)) {
            this.pollingStatus = "FAIL";
            this.statusDescription = "UNEXPECTED ERROR";
         }
      } finally {
         this.contentInfo.setIsReadyForNextFtpThread("Y", this.contentId);
         this.createPollingInfo();
         this.result = true;
      }

      this.logger.error("#####[INFO][MagicInfo_FTP] ENDED to get FTP Files NEXT!! : " + this.contentName + "[" + this.contentId + "][" + this.result + "]");
      return this.result;
   }

   private boolean checkEditMode() {
      boolean methodResult = true;

      try {
         List ftpSettingInfo = this.contentInfo.getFtpContentSettingByContentId(this.contentId);
         if (ftpSettingInfo != null && ftpSettingInfo.size() > 0) {
            this.editMode = true;
            Map ftpInfoValues = (Map)ftpSettingInfo.get(0);
            long oldValue = (Long)ftpInfoValues.get("refresh_interval");
            if (oldValue != this.refreshInterval) {
               this.settingChanged = true;
               this.logger.debug("[MagicInfo_FTP] editMode " + this.contentId + " " + oldValue + " " + ftpInfoValues);
            }

            this.loginRetryCount = Long.parseLong(ftpInfoValues.get("login_retry_count").toString());
         }

         if (this.editMode) {
            ContentFile contentFile = this.contentInfo.getMainFileInfo(this.contentId);
            if (contentFile != null) {
               this.mainFileId = contentFile.getFile_id();
               this.logger.debug("[MagicInfo_FTP] editMode : oldMainFile id = " + this.mainFileId);
            } else {
               this.logger.error("[MagicInfo_FTP] editMode is true but main_file do not exist : " + this.contentName + "[" + this.contentId + "]");
            }
         }
      } catch (Exception var6) {
         this.logger.error("[MagicInfo_FTP] " + var6.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var6);
         this.pollingStatus = "FAIL";
         this.statusDescription = "UNEXPECTED ERROR";
         methodResult = false;
      }

      return methodResult;
   }

   private boolean accessToRemoteServer() {
      boolean methodResult = true;

      String schedulerJobName;
      try {
         if (this.scheduledJob) {
            this.password = new String(Base64.decode(this.password));
         }

         if (StringUtils.isEmpty(this.miUserId)) {
            this.miUserId = "admin";
         }

         this.characterEncoding = System.getProperty("file.encoding");
         if (StringUtils.isEmpty(this.characterEncoding)) {
            this.characterEncoding = "euckr";
         }

         this.client = new FTPClient();
         this.client.setControlEncoding(this.characterEncoding);
         this.client.setAutodetectUTF8(true);
         this.client.setDefaultPort(this.serverPort);
         this.client.connect(this.serverIp, this.serverPort);
         int reply = this.client.getReplyCode();
         if (!FTPReply.isPositiveCompletion(reply)) {
            this.logger.error("[MagicInfo_FTP] FAILED to connect to remote server : " + this.contentName + "[" + this.contentId + "]");
            this.client.disconnect();
            this.pollingStatus = "FAIL";
            this.statusDescription = "CONNECTION FAIL";
            methodResult = false;
         } else if (!this.client.login(this.loginId, this.password)) {
            this.logger.error("[MagicInfo_FTP] FAILED to login to remote server : " + this.contentName + "[" + this.contentId + "]");
            this.client.logout();
            this.pollingStatus = "FAIL";
            this.statusDescription = "LOGIN FAIL";
            methodResult = false;
            if (this.editMode && "Y".equals(this.canLoginRetry)) {
               if (this.loginRetryCount >= this.loginRetryMaxCount) {
                  this.canRefresh = "N";
                  this.canLoginRetry = "N";
                  this.loginRetryCount = 0L;
               } else {
                  ++this.loginRetryCount;
               }

               this.contentInfo.updateFtpSettingByContentId(this.contentId, this.contentName, this.serverIp, this.serverPort, this.loginId, Base64.encode(this.password.getBytes()), this.directory, this.refreshInterval, this.canRefresh, this.loginRetryMaxCount, this.loginRetryCount, this.canLoginRetry);
               if ("N".equals(this.canRefresh)) {
                  schedulerJobName = "FTP_" + this.contentId;
                  String schedulerJobGroup = "UpdateFtpContentService";
                  CommonUtils.deleteJob(schedulerJobName, schedulerJobGroup);
               }
            }
         } else {
            if (this.editMode && this.loginRetryCount > 0L) {
               this.loginRetryCount = 0L;
               this.contentInfo.updateFtpSettingByContentId(this.contentId, this.contentName, this.serverIp, this.serverPort, this.loginId, Base64.encode(this.password.getBytes()), this.directory, this.refreshInterval, this.canRefresh, this.loginRetryMaxCount, this.loginRetryCount, this.canLoginRetry);
            }

            this.client.setFileType(2);
            this.client.enterLocalPassiveMode();
            if (!this.client.changeWorkingDirectory(this.directory)) {
               this.logger.error("[MagicInfo_FTP] INVALID PATH : " + this.contentName + "[" + this.contentId + "]");
               this.client.logout();
               this.pollingStatus = "FAIL";
               this.statusDescription = "INVALID PATH";
               methodResult = false;
            }
         }
      } catch (Exception var5) {
         schedulerJobName = var5.getMessage();
         this.logger.error("[MagicInfo_FTP] " + schedulerJobName + "|" + this.contentName + "[" + this.contentId + "]", var5);
         this.pollingStatus = "FAIL";
         if (schedulerJobName.contains("timed out")) {
            this.statusDescription = "CONNECTION TIMEOUT";
         } else {
            this.statusDescription = "UNEXPECTED ERROR";
         }

         methodResult = false;
      }

      return methodResult;
   }

   private boolean getFileList() throws IOException, SQLException {
      FTPFile[] ftpFiles = this.client.listFiles();
      if (ftpFiles == null) {
         return false;
      } else {
         FTPFile[] var2 = ftpFiles;
         int var3 = ftpFiles.length;

         for(int var4 = 0; var4 < var3; ++var4) {
            FTPFile file = var2[var4];
            if (file.isFile() && !file.isDirectory()) {
               boolean validType = false;
               String[] tempName = file.getName().split("[.]");
               int sizeOfSplitName = false;
               if (tempName.length > 0) {
                  int sizeOfSplitName = tempName.length - 1;
                  validType = this.contentInfo.getCodeFile(tempName[sizeOfSplitName].toUpperCase()).equalsIgnoreCase("");
               }

               if (!validType) {
                  this.remoteFiles.add(this.makeRemoteFileInfo(file.getName(), file.getSize(), "NONE", "N"));
               }
            }
         }

         return true;
      }
   }

   private boolean validateFileName(String fileName) {
      return Pattern.compile("[&<>]").matcher(fileName).find();
   }

   private void checkChangedFiles() throws SQLException {
      for(int i = 0; i < this.remoteFiles.size(); ++i) {
         String[] arrRemoteFile = ((String)this.remoteFiles.get(i)).split("[|]");
         String ftpFileName = arrRemoteFile[0];
         boolean isFileNameValid = Pattern.compile("([\\\\/:&*?<>|])|(%)([0-9a-fA-F])([0-9a-fA-F])").matcher(ftpFileName).find();
         if (!isFileNameValid) {
            long newFileSize = Long.valueOf(arrRemoteFile[1]);
            long oldFileSize = 0L;
            String src = this.localPathByIp + File.separator + ftpFileName;
            File oldFile = SecurityUtils.getSafeFile(src);
            if (oldFile.exists()) {
               oldFileSize = oldFile.length();
            }

            String localFileId = this.contentInfo.getFileIdFromContentByFileNameAndSize(this.contentId, ftpFileName, oldFileSize);
            if (!oldFile.exists() || localFileId == null || oldFileSize != newFileSize && ftpFileName.equals(oldFile.getName())) {
               this.logger.error("Kim 1.3 compare size OLD vs NEW : " + Long.toString(oldFileSize) + "[ vs ]" + Long.toString(newFileSize));
               this.logger.error("Kim 1.4 compare name OLD vs NEW : " + oldFile.getName() + "[ vs ]" + ftpFileName);
               this.fileChanged = true;
               this.isFilesToDownload = true;
               if (oldFile.exists() && localFileId != null) {
                  this.remoteFiles.set(i, this.makeRemoteFileInfo(ftpFileName, newFileSize, "MODIFIED", "N"));
               } else {
                  this.remoteFiles.set(i, this.makeRemoteFileInfo(ftpFileName, newFileSize, "NEW", "N"));
               }
            }

            this.totalSizeOfFiles += newFileSize;
         }
      }

   }

   private void removeChangedTempFiles() throws Exception {
      List preContentFileList = this.contentInfo.getFileList(this.contentId);
      Iterator var2 = preContentFileList.iterator();

      while(var2.hasNext()) {
         ContentFile contentFile = (ContentFile)var2.next();
         boolean existsServerFile = false;
         String preContentFileName = contentFile.getFile_name();
         long preContentFileSize = contentFile.getFile_size();

         String oldFileHash;
         for(int i = 0; i < this.remoteFiles.size(); ++i) {
            String[] arrRemoteFile = ((String)this.remoteFiles.get(i)).split("[|]");
            oldFileHash = arrRemoteFile[0];
            long ftpFileSize = Long.valueOf(arrRemoteFile[1]);
            if (preContentFileName.equalsIgnoreCase(oldFileHash) && preContentFileSize == ftpFileSize) {
               existsServerFile = true;
               break;
            }
         }

         if (!existsServerFile && !preContentFileName.equalsIgnoreCase("FtpMetadata.FTP")) {
            this.logger.info("[MagicInfo_FTP] will remove Previous file : " + this.contentName + "[" + this.contentId + "] " + preContentFileName);
            String src = this.localPathByIp + File.separator + preContentFileName;
            File preContentFile = SecurityUtils.getSafeFile(src);
            if (preContentFile.exists()) {
               this.logger.info("[MagicInfo_FTP] local file exists : " + this.contentName + "[" + this.contentId + "] " + preContentFileName);
               oldFileHash = FileUtils.getHash(preContentFile);
               String oldFileId = null;
               if (StringUtils.isEmpty(oldFileHash)) {
                  this.logger.info("[MagicInfo_FTP] Old File Hash is NULL : " + this.contentName + "[" + this.contentId + "] " + preContentFileName);
                  oldFileId = this.contentInfo.getFileIdFromContentByFileNameAndSize(this.contentId, preContentFileName, preContentFileSize);
               } else {
                  oldFileId = this.contentInfo.getFileIDByHash(preContentFile.getName(), preContentFile.length(), oldFileHash);
               }

               if (oldFileId == null) {
                  this.logger.info("[MagicInfo_FTP] Old File ID is NULL : " + this.contentName + "[" + this.contentId + "] " + preContentFileName);
               } else {
                  this.contentInfo.deleteFileFromContentMap(this.contentId, oldFileId);
                  if (this.contentInfo.isDeletableFileFromContents(oldFileId)) {
                     this.contentInfo.deleteFile(oldFileId);
                  }
               }

               if (!preContentFile.delete()) {
                  this.logger.error("[MagicInfo_FTP] Can NOT remove Previous File : " + this.contentName + "[" + this.contentId + "] " + preContentFileName);
               } else {
                  this.logger.info("[MagicInfo_FTP] REMOVED Previous File : " + this.contentName + "[" + this.contentId + "] " + preContentFileName);
               }
            } else {
               this.logger.info("[MagicInfo_FTP] local file does NOT exist : " + this.contentName + "[" + this.contentId + "] " + preContentFileName);
               this.contentInfo.deleteFileFromContentMapByFileName(this.contentId, preContentFileName);
            }

            this.fileChanged = true;
         }
      }

   }

   private void updateContentInfo() throws SQLException, ConfigException {
      Content content = new Content();
      ServerSetupInfo serverSetupInfo = ServerSetupInfoImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      long orgId = userInfo.getRootGroupIdByUserId(this.miUserId);
      Map infoMap = serverSetupInfo.getServerInfoByOrgId(orgId);
      boolean contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
      String fileNameStyle = "FTP_" + ContentUtils.getFolderIp(this.serverIp) + '_' + this.loginId + '_' + this.directory.replace('/', '_');
      if (StringUtils.isEmpty(this.contentName)) {
         this.contentName = fileNameStyle;
      }

      content.setContent_id(this.contentId);
      content.setCreator_id(this.miUserId);
      content.setContent_name(this.contentName);
      content.setIs_deleted("N");
      content.setShare_flag(1);
      content.setOrganization_id(userInfo.getRootGroupIdByUserId(this.miUserId));
      content.setVersion_id(0L);
      content.setMedia_type("FTP");
      content.setThumb_file_id("FTP_THUMBNAIL");
      content.setTotal_size(this.totalSizeOfFiles);
      content.setIs_active("N");
      content.setMain_file_id(this.mainFileId);
      content.setIs_linear_vwl("N");
      content.setScreen_count(0);
      content.setContent_meta_data("");
      content.setX_count(0);
      content.setY_count(0);
      content.setX_range(0);
      content.setY_range(0);
      content.setIs_streaming("N");
      content.setMain_file_Extension("FTP");
      if (contentsApprovalEnable) {
         if (content.getMain_file_Extension().equalsIgnoreCase("LFT")) {
            content.setApproval_status("APPROVED");
         } else {
            AbilityUtils abilityUtils = new AbilityUtils();
            if (abilityUtils.isContentApprovalAuthority(this.miUserId)) {
               content.setApproval_status("APPROVED");
            } else {
               content.setApproval_status("UNAPPROVED");
            }
         }
      } else {
         content.setApproval_status("APPROVED");
      }

      this.logger.info("[MagicInfo_FTP] editMode/fileChanged/settingChanged : " + this.editMode + "/" + this.fileChanged + "/" + this.settingChanged + "|" + this.contentName + "[" + this.contentId + "]");
      if (this.editMode) {
         if (this.fileChanged) {
            this.contentInfo.updateContentVersionInfoByContentId(this.totalSizeOfFiles, this.contentId);
         }

         if (this.settingChanged) {
            this.contentInfo.updateFtpSettingByContentId(this.contentId, this.contentName, this.serverIp, this.serverPort, this.loginId, Base64.encode(this.password.getBytes()), this.directory, this.refreshInterval, this.canRefresh, this.loginRetryMaxCount, this.loginRetryCount, this.canLoginRetry);
         }
      } else {
         this.contentInfo.addContentInfo(content);
         this.contentInfo.addContentVersionInfo(content);
         this.contentInfo.addMapGroupContent(this.contentId, this.groupId);
         this.contentInfo.addFtpSetting(this.contentId, this.contentName, this.serverPort, this.serverIp, this.loginId, Base64.encode(this.password.getBytes()), this.directory, this.refreshInterval, "N", this.canRefresh, this.loginRetryMaxCount, this.loginRetryCount, this.canLoginRetry);
         this.contentInfo.setApprovalStatus(content.getContent_id(), content.getApproval_status(), "");
         if (content.getApproval_status() != null && content.getApproval_status().equalsIgnoreCase("UNAPPROVED")) {
            List approverList = userInfo.getContentApproverListByGroupId(userInfo.getRootGroupIdByUserId(content.getCreator_id()));
            if (approverList != null) {
               Iterator var10 = approverList.iterator();

               while(var10.hasNext()) {
                  Map approver = (Map)var10.next();
                  String tmpUserId = (String)approver.get("user_id");
                  this.contentInfo.addContentApproverMap(content.getContent_id(), tmpUserId);
               }
            }
         }
      }

   }

   private void downloadChangedFiles() throws SQLException {
      if (!this.isFilesToDownload) {
         this.logger.debug("[MagicInfo_FTP] No Files To Download : " + this.contentName + "[" + this.contentId + "]");
      } else {
         File localPathByIpDir = SecurityUtils.getSafeFile(this.localPathByIp);
         if (!localPathByIpDir.exists()) {
            localPathByIpDir.mkdir();
         }

         for(int i = 0; i < this.remoteFiles.size(); ++i) {
            String[] arrRemoteFile = ((String)this.remoteFiles.get(i)).split("[|]");
            String fileName = arrRemoteFile[0];
            long fileSize = Long.valueOf(arrRemoteFile[1]);
            String fileStatus = arrRemoteFile[2];
            this.logger.info("[MagicInfo_FTP] RemoteFile : " + this.contentName + "[" + this.contentId + "] " + fileName + "|" + fileSize + "|" + fileStatus);
            boolean isFileNameValid = Pattern.compile("([\\\\/:&*?<>|])|(%)([0-9a-fA-F])([0-9a-fA-F])").matcher(fileName).find();
            if (!isFileNameValid) {
               String localFileHashCode = this.contentInfo.getHashCodeFromContentByFileNameAndSize(fileName, "FTP_CONTENT", fileSize);
               String fileId = "";
               if (StringUtils.isEmpty(localFileHashCode)) {
                  fileId = UUID.randomUUID().toString().toUpperCase();
               } else {
                  fileId = this.contentInfo.getFileIDByHash(fileName, fileSize, localFileHashCode);
               }

               this.fileListToSave.add(fileId);
               if (!"NONE".equalsIgnoreCase(fileStatus)) {
                  this.logger.info("[MagicInfo_FTP] DOWNLOADING : " + this.contentName + "[" + this.contentId + "] " + fileName + "|" + fileSize + "|" + fileStatus);
                  String src = this.localPathByIp + File.separator + fileName;
                  File fileIdDir = SecurityUtils.getSafeFile(this.CONTENTS_HOME + File.separator + fileId);
                  String dest = fileIdDir + File.separator + fileName;
                  File localPathByIpFile = SecurityUtils.getSafeFile(this.localPathByIp, fileName);
                  FileOutputStream fileOutputStream = null;

                  try {
                     fileOutputStream = new FileOutputStream(localPathByIpFile);
                     boolean isSuccess = this.client.retrieveFile(fileName, fileOutputStream);
                     int reply = this.client.getReplyCode();
                     if (isSuccess) {
                        String ftpFileHashCode = FileUtils.getHash(localPathByIpFile);
                        if (!fileIdDir.exists()) {
                           fileIdDir.mkdir();
                           (new FileOutputStream(dest)).close();
                        }

                        this.logger.debug("[MagicInfo_FTP] copy_src  : " + src);
                        this.logger.debug("[MagicInfo_FTP] copy_dest : " + dest);
                        this.copyDirectory(SecurityUtils.getSafeFile(src), SecurityUtils.getSafeFile(dest));
                        if (!this.contentInfo.isExistFileByHash(fileName, SecurityUtils.getSafeFile(dest).length(), ftpFileHashCode)) {
                           ContentFile contentFile;
                           if (localFileHashCode != null) {
                              if (!localFileHashCode.equalsIgnoreCase(ftpFileHashCode)) {
                                 contentFile = new ContentFile();
                                 contentFile.setFile_id(fileId);
                                 contentFile.setFile_name(fileName);
                                 contentFile.setFile_size(SecurityUtils.getSafeFile(dest).length());
                                 contentFile.setFile_path(this.CONTENTS_HOME + File.separator + fileId);
                                 contentFile.setHash_code(ftpFileHashCode);
                                 contentFile.setCreator_id(this.miUserId);
                                 contentFile.setFile_type("CONTENT");
                                 contentFile.setIs_streaming("N");
                                 this.contentInfo.addFile(contentFile);
                              }
                           } else {
                              contentFile = new ContentFile();
                              contentFile.setFile_id(fileId);
                              contentFile.setFile_name(fileName);
                              contentFile.setFile_size(SecurityUtils.getSafeFile(dest).length());
                              contentFile.setFile_path(this.CONTENTS_HOME + File.separator + fileId);
                              contentFile.setHash_code(ftpFileHashCode);
                              contentFile.setCreator_id(this.miUserId);
                              contentFile.setFile_type("CONTENT");
                              contentFile.setIs_streaming("N");
                              this.contentInfo.addFile(contentFile);
                           }
                        } else {
                           fileId = this.contentInfo.getFileIDByHash(fileName, SecurityUtils.getSafeFile(dest).length(), ftpFileHashCode);
                        }

                        this.remoteFiles.set(i, this.makeRemoteFileInfo(fileName, fileSize, fileStatus, "Y"));
                        long version = this.contentInfo.getVersionInfoByContentId(this.contentId);
                        this.contentInfo.addMapContentFile(this.contentId, version, fileId);
                     } else {
                        if (reply == 550) {
                           localPathByIpFile.delete();
                        }

                        this.logger.error("[MagicInfo_FTP] FAILED to get file from FTP " + fileName + " " + fileSize);
                     }
                  } catch (Exception var29) {
                     this.logger.error("[MagicInfo_FTP] " + var29.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var29);
                  } finally {
                     try {
                        if (fileOutputStream != null) {
                           fileOutputStream.close();
                        }
                     } catch (IOException var28) {
                        var28.printStackTrace();
                     }

                  }
               }
            }
         }

      }
   }

   private void createCSDFile() throws Exception {
      if (!this.fileChanged) {
         this.logger.debug("[MagicInfo_FTP] No Changed Files : " + this.contentName + "[" + this.contentId + "]");
      } else {
         List fileList = this.contentInfo.getFileListByContentId(this.contentId);
         long version = this.contentInfo.getVersionInfoByContentId(this.contentId) + 1L;
         File csdFolder = SecurityUtils.getSafeFile(this.CONTENTS_HOME + File.separator + "contents_meta" + File.separator + this.contentId);
         File csdFile;
         if (csdFolder.exists()) {
            csdFile = SecurityUtils.getSafeFile(this.CONTENTS_HOME + File.separator + "contents_meta" + File.separator + this.contentId + File.separator + "ContentsMetadata.CSD");
            csdFile.delete();
         }

         csdFile = this.createCSD(fileList);
         if (csdFile != null && csdFile.exists()) {
            String srcFile = this.CONTENTS_HOME + File.separator + "contents_meta" + File.separator + this.contentId + File.separator + "ContentsMetadata.CSD";
            ContentFile contentFile = new ContentFile();
            String hashCode = "";
            long fileSize = 0L;
            String newMainFileId = UUID.randomUUID().toString().toUpperCase();
            String oldMainFileId = this.mainFileId;
            String newFilePath = this.CONTENTS_HOME + File.separator + newMainFileId;
            String newFilePathFile = this.CONTENTS_HOME + File.separator + newMainFileId + File.separator + "FtpMetadata.FTP";
            String oldFilePath = this.CONTENTS_HOME + File.separator + oldMainFileId + File.separator;
            File newMetaFile = SecurityUtils.getSafeFile(newFilePathFile);
            File oldMetaFile = SecurityUtils.getSafeFile(oldFilePath + "FtpMetadata.FTP");
            File oldMetaFileFolder = SecurityUtils.getSafeFile(oldFilePath);
            File newFilePathFolder = SecurityUtils.getSafeFile(newFilePath);
            if (!newFilePathFolder.exists()) {
               newFilePathFolder.mkdir();
            }

            this.logger.debug("[MagicInfo_FTP] csd_src  : " + srcFile);
            this.logger.debug("[MagicInfo_FTP] csd_dest : " + newFilePathFile);
            this.copyFile(SecurityUtils.getSafeFile(srcFile), newMetaFile);
            hashCode = FileUtils.getHash(newMetaFile);
            fileSize = newMetaFile.length();
            contentFile.setFile_id(newMainFileId);
            contentFile.setFile_name("FtpMetadata.FTP");
            contentFile.setFile_size(fileSize);
            contentFile.setFile_path(newFilePath);
            contentFile.setHash_code(hashCode);
            contentFile.setCreator_id(this.miUserId);
            contentFile.setFile_type("FTP_MAIN");
            contentFile.setIs_streaming("N");
            this.fileListToSave.add(newMainFileId);
            this.contentInfo.deleteFile(oldMainFileId);
            oldMetaFile.delete();
            oldMetaFileFolder.delete();
            this.contentInfo.addFile(contentFile);
            this.contentInfo.updateContentVersionInfoWithFileId(this.contentId, newMainFileId, version);
         }

         this.contentInfo.setIsActive(this.contentId, "Y");
         this.logger.info("[MagicInfo_FTP] Updated CSD & DB : " + this.contentName + "[" + this.contentId + "]");
      }
   }

   private void setScheduleJobFtp() throws Exception {
      if (this.editMode && !this.settingChanged && !this.fileChanged) {
         this.logger.debug("[MagicInfo_FTP] No files in Server : " + this.contentName + "[" + this.contentId + "]");
      } else {
         Scheduler scheduler = ScheduleManager.getSchedulerInstance();
         int startingDelay = Integer.valueOf(String.valueOf(this.refreshInterval));
         String schedulerJobName = "FTP_" + this.contentId;
         String schedulerJobGroup = "UpdateFtpContentService";
         CommonUtils.deleteJob(schedulerJobName, schedulerJobGroup);
         this.logger.info("[MagicInfo_FTP] UPDATE_FTP_CONTENT_SERVICE : " + schedulerJobName + " " + this.refreshInterval + "|" + this.contentName + "[" + this.contentId + "]");
         if (this.refreshInterval > 0L) {
            if ("Y".equals(this.canRefresh)) {
               JobDetail jobDetail = CommonUtils.getJobDetail(schedulerJobName, schedulerJobGroup, FtpContentScheduleJob.class);
               Calendar currTime = Calendar.getInstance();
               currTime.add(12, startingDelay);
               SimpleTrigger trigger = CommonUtils.getSimpleTrigger(schedulerJobName, schedulerJobGroup, currTime.getTime(), this.refreshInterval * 60L * 1000L);

               try {
                  scheduler.scheduleJob(jobDetail, trigger);
               } catch (Exception var9) {
                  this.logger.error("[MagicInfo_FTP] " + var9.getMessage() + "|" + this.contentName + "[" + this.contentId + "]");
                  this.logger.error(var9);
               }

               this.logger.info("[MagicInfo_FTP] START to schedule : " + this.contentName + "[" + this.contentId + "]");
            }

            ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
            scheduleInfo.setScheduleTrigger(this.contentId);
            EventInfo eventInfo = EventInfoImpl.getInstance();
            eventInfo.setContentTrigger(this.contentId);
         } else {
            this.logger.info("[MagicInfo_FTP] UPDATE_FTP_CONTENT_SERVICE : refresh_interval is 0 : " + this.contentName + "[" + this.contentId + "]");
         }

      }
   }

   private void setScheduleJobDlk() throws ConfigException, Exception {
      if (!this.fileChanged) {
         this.logger.debug("[MagicInfo_FTP] No Changed files : " + this.contentName + "[" + this.contentId + "]");
      } else {
         List dlkContentList = this.contentInfo.getDlkContentIdByIputDataContentId(this.contentId);
         if (dlkContentList != null && dlkContentList.size() > 0) {
            for(int i = 0; i < dlkContentList.size(); ++i) {
               ContentXmlManager contentXmlManager = new ContentXmlManager();
               Map dlkContent = (Map)dlkContentList.get(i);
               String dlkContentId = dlkContent.get("DLK_CONTENT_ID").toString();
               long nextVersion = this.contentInfo.getContentNextVer(dlkContentId);
               String strVersionId = String.valueOf(nextVersion);
               String dlkMainFileId = this.contentInfo.getMainFileInfo(dlkContentId).getFile_id();
               String prevMediaSlideMainFileId = this.mainFileId;
               String mediaSlideMainFileId = this.contentInfo.getMainFileInfo(this.contentId).getFile_id();
               ContentFile dlkFile = this.contentInfo.getFileInfo(dlkMainFileId);
               String dlkFilePath = dlkFile.getFile_path() + File.separator + dlkFile.getFile_name();
               ContentFile mediaSlideFile = this.contentInfo.getFileInfo(mediaSlideMainFileId);
               this.logger.debug("[MagicInfo_FTP] New mediaSlideFile ID : " + mediaSlideFile.getFile_id());
               this.logger.debug("[MagicInfo_FTP] dlkContentId/contentID/strVersionID/nextVersion : " + dlkContentId + "/" + this.contentId + "/" + strVersionId + "/" + nextVersion);
               contentXmlManager.modifyMediaSlideInfo(prevMediaSlideMainFileId, mediaSlideFile, dlkFilePath, this.contentId, dlkContentId);
               String hashCode = FileUtils.getHash(SecurityUtils.getSafeFile(dlkFilePath));
               long fileSize = SecurityUtils.getSafeFile(dlkFilePath).length();
               this.logger.info("[MagicInfo_FTP] updateHashCodeByMainFileId : " + dlkMainFileId + " " + hashCode + " " + fileSize);
               this.contentInfo.updateHashCodeByMainFileId(dlkMainFileId, fileSize, hashCode);
               this.contentInfo.updateVersionAndMainFileIdInContentVersionInfo(nextVersion, dlkMainFileId, dlkContentId);
               this.logger.info("[MagicInfo_FTP] Start to schedule : " + this.contentName + "[" + this.contentId + "]");
               ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
               scheduleInfo.setContentTrigger(dlkContentId);
            }
         }

      }
   }

   private void updateDownloadedFileInfos() throws SQLException {
      if (!this.isFilesToDownload) {
         this.logger.debug("[MagicInfo_FTP] No Files To Download : " + this.contentName + "[" + this.contentId + "]");
      } else {
         for(int i = 0; i < this.remoteFiles.size(); ++i) {
            String[] arrRemoteFile = ((String)this.remoteFiles.get(i)).split("[|]");
            String fileName = arrRemoteFile[0];
            long fileSize = Long.valueOf(arrRemoteFile[1]);
            String fileStatus = arrRemoteFile[2];
            String isDownloaded = arrRemoteFile[3];
            this.logger.debug("[MagicInfo_FTP] " + fileName + ", " + fileSize + ", " + fileStatus + ", isDownloaded(" + isDownloaded + ")");
            String localFileHashCode = this.contentInfo.getHashCodeFromContentByFileNameAndSize(fileName, "FTP_CONTENT", fileSize);
            String fileId = "";
            if (StringUtils.isEmpty(localFileHashCode)) {
               fileId = UUID.randomUUID().toString().toUpperCase();
            } else {
               fileId = this.contentInfo.getFileIDByHash(fileName, fileSize, localFileHashCode);
            }

            this.fileListToSave.add(fileId);
            if (!"NONE".equalsIgnoreCase(fileStatus) && "Y".equalsIgnoreCase(isDownloaded)) {
               this.logger.debug("[MagicInfo_FTP] Not exist or mismatched : " + fileName);
               String src = this.localPathByIp + File.separator + fileName;
               File fileIdDir = SecurityUtils.getSafeFile(this.CONTENTS_HOME + File.separator + fileId);
               String dest = fileIdDir + File.separator + fileName;
               File localPathByIpFile = SecurityUtils.getSafeFile(this.localPathByIp, fileName);

               try {
                  String ftpFileHashCode = FileUtils.getHash(localPathByIpFile);
                  if (!fileIdDir.exists()) {
                     fileIdDir.mkdir();
                     (new FileOutputStream(dest)).close();
                  }

                  this.logger.debug("[MagicInfo_FTP] copy_src  : " + src);
                  this.logger.debug("[MagicInfo_FTP] copy_dest : " + dest);
                  this.copyDirectory(SecurityUtils.getSafeFile(src), SecurityUtils.getSafeFile(dest));
                  if (!this.contentInfo.isExistFileByHash(fileName, SecurityUtils.getSafeFile(dest).length(), ftpFileHashCode)) {
                     ContentFile contentFile;
                     if (localFileHashCode != null) {
                        if (!localFileHashCode.equalsIgnoreCase(ftpFileHashCode)) {
                           contentFile = new ContentFile();
                           contentFile.setFile_id(fileId);
                           contentFile.setFile_name(fileName);
                           contentFile.setFile_size(SecurityUtils.getSafeFile(dest).length());
                           contentFile.setFile_path(this.CONTENTS_HOME + File.separator + fileId);
                           contentFile.setHash_code(ftpFileHashCode);
                           contentFile.setCreator_id(this.miUserId);
                           contentFile.setFile_type("CONTENT");
                           contentFile.setIs_streaming("N");
                           this.contentInfo.addFile(contentFile);
                        }
                     } else {
                        contentFile = new ContentFile();
                        contentFile.setFile_id(fileId);
                        contentFile.setFile_name(fileName);
                        contentFile.setFile_size(SecurityUtils.getSafeFile(dest).length());
                        contentFile.setFile_path(this.CONTENTS_HOME + File.separator + fileId);
                        contentFile.setHash_code(ftpFileHashCode);
                        contentFile.setCreator_id(this.miUserId);
                        contentFile.setFile_type("CONTENT");
                        contentFile.setIs_streaming("N");
                        this.contentInfo.addFile(contentFile);
                     }
                  } else {
                     fileId = this.contentInfo.getFileIDByHash(fileName, SecurityUtils.getSafeFile(dest).length(), ftpFileHashCode);
                  }

                  long version = this.contentInfo.getVersionInfoByContentId(this.contentId);
                  this.contentInfo.addMapContentFile(this.contentId, version, fileId);
               } catch (Exception var17) {
                  this.logger.error("[MagicInfo_FTP_Next] " + var17.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var17);
               }
            }
         }

      }
   }

   public void updatePlaylistInfo() {
      try {
         PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
         List playlistInfoList = playlistInfo.getPlaylistInfoByContentId(this.contentId);

         for(int idx = 0; idx < playlistInfoList.size(); ++idx) {
            Map mapPlaylistInfo = (Map)playlistInfoList.get(idx);
            String sPlaylistId = mapPlaylistInfo.get("playlist_id").toString();
            long lVersionId = Long.parseLong(mapPlaylistInfo.get("version_id").toString());
            Playlist curPlaylist = playlistInfo.getPlaylistVerInfo(sPlaylistId, lVersionId);
            if (curPlaylist != null) {
               List playlistContentList = playlistInfo.getContentList(curPlaylist.getPlaylist_id(), curPlaylist.getVersion_id());
               long lTotalSize = 0L;

               for(int idx2 = 0; idx2 < playlistContentList.size(); ++idx2) {
                  PlaylistContent playlistContent = (PlaylistContent)playlistContentList.get(idx2);
                  Content curContent = this.contentInfo.getContentActiveVerInfo(playlistContent.getContent_id());
                  if (curContent != null && curContent.getTotal_size() != null) {
                     lTotalSize += curContent.getTotal_size();
                  }
               }

               playlistInfo.setTotalSize(curPlaylist.getPlaylist_id(), curPlaylist.getVersion_id(), lTotalSize);
               this.logger.info("[SF00179387]UPDATED totalSize of Playlist[" + curPlaylist.getPlaylist_id() + "][" + curPlaylist.getPlaylist_name() + "]ver[" + curPlaylist.getVersion_id() + "][" + lTotalSize + "]");
            }
         }
      } catch (Exception var15) {
         this.logger.error("[SF00179387]can NOT update totalSize of Playlist", var15);
      }

   }

   public void createPollingInfo() {
      try {
         if (!this.editMode || this.fileChanged || "FAIL".equals(this.pollingStatus)) {
            Calendar currTime = Calendar.getInstance();
            if (!"FAIL".equals(this.pollingStatus)) {
               List ftpSettingList = this.contentInfo.getFtpContentSettingByContentId(this.contentId);
               if (ftpSettingList != null && ftpSettingList.size() > 0) {
                  for(int i = 0; i < this.remoteFiles.size(); ++i) {
                     String[] arrRemoteFile = ((String)this.remoteFiles.get(i)).split("[|]");
                     String remoteFileName = arrRemoteFile[0];
                     long remoteFileSize = Long.valueOf(arrRemoteFile[1]);
                     String remoteFileStatus = arrRemoteFile[2];
                     String remoteFileDownload = arrRemoteFile[3];
                     if ("NONE".equals(remoteFileStatus)) {
                        remoteFileStatus = "";
                     } else if (!"Y".equals(remoteFileDownload)) {
                        remoteFileStatus = "FAIL";
                     }

                     this.contentInfo.addPollingFileInfo(this.contentId, currTime.getTime(), remoteFileName, remoteFileSize, remoteFileStatus, this.miUserId);
                  }
               }
            }

            this.contentInfo.addPollingInfo(this.contentId, currTime.getTime(), this.pollingStatus, this.remoteFiles.size(), this.statusDescription, this.miUserId);
         }
      } catch (Exception var10) {
         this.logger.error("[MagicInfo_FTP] " + var10.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var10);
      }

   }

   public synchronized void copyDirectory(File sourceDir, File targetDir) {
      FileInputStream fileInputStream = null;
      FileOutputStream fileOutputStream = null;

      try {
         this.logger.debug("[MagicInfo_FTP] source[" + sourceDir.getName() + "] target[" + targetDir.getName() + "]");
         if (!targetDir.exists()) {
            targetDir.mkdir();
            (new FileOutputStream(targetDir)).close();
         }

         fileInputStream = new FileInputStream(sourceDir);
         fileOutputStream = new FileOutputStream(targetDir);
         int byteToDownload = 8192;
         byte[] byteArray = new byte[byteToDownload];
         boolean var7 = false;

         int n;
         while((n = fileInputStream.read(byteArray)) > 0) {
            fileOutputStream.write(byteArray, 0, n);
         }

         this.logger.info("[MagicInfo_FTP] Copied SUCCESSFULLY!! : " + sourceDir.getName() + ", " + targetDir.getName());
      } catch (Exception var16) {
         this.logger.error("[MagicInfo_FTP] " + var16.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var16);
      } finally {
         try {
            if (fileOutputStream != null) {
               fileOutputStream.close();
            }

            if (fileInputStream != null) {
               fileInputStream.close();
            }
         } catch (Exception var15) {
            this.logger.error("[MagicInfo_FTP] " + var15.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var15);
         }

      }

   }

   public File createCSD(List fileList) {
      FileOutputStream fileOutputStream = null;
      FileChannel fileChannel = null;

      File var29;
      try {
         File metaFolder = SecurityUtils.getSafeFile(this.CONTENTS_HOME + File.separator + "contents_meta");
         if (!metaFolder.exists()) {
            metaFolder.mkdir();
         }

         File csdFolder = SecurityUtils.getSafeFile(this.CONTENTS_HOME + File.separator + "contents_meta" + File.separator + this.contentId);
         if (!csdFolder.exists()) {
            csdFolder.mkdir();
         }

         File csdFile = SecurityUtils.getSafeFile(this.CONTENTS_HOME + File.separator + "contents_meta" + File.separator + this.contentId + File.separator + "ContentsMetadata.CSD");
         StringBuffer stringBuffer;
         if (!csdFile.exists() && !csdFile.createNewFile()) {
            stringBuffer = null;
            return stringBuffer;
         }

         stringBuffer = new StringBuffer("");
         stringBuffer.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n");
         stringBuffer.append("<FTPContent cid=\"").append(this.contentId).append("\">\n");
         stringBuffer.append("  <User>").append(this.miUserId).append("</User>\n");
         stringBuffer.append("  <Title>").append(this.contentName).append("</Title>\n");
         long category = this.groupId;
         stringBuffer.append("<Category>").append(category).append("</Category>\n");
         stringBuffer.append("\t<FtpFileContents>\n");

         try {
            List contentFileList = this.contentInfo.getFileList(this.contentId);
            Iterator var11 = contentFileList.iterator();

            while(var11.hasNext()) {
               ContentFile contentFile = (ContentFile)var11.next();
               if (contentFile != null && !contentFile.getFile_name().equalsIgnoreCase("FtpMetadata.FTP")) {
                  stringBuffer.append("\t\t<FileItem>\n");
                  stringBuffer.append("\t\t\t<FileId>").append(contentFile.getFile_id()).append("</FileId>\n");
                  stringBuffer.append("\t\t\t<FileName>").append(contentFile.getFile_name()).append("</FileName>\n");
                  stringBuffer.append("\t\t\t<FileSize>").append(contentFile.getFile_size()).append("</FileSize>\n");
                  stringBuffer.append("\t\t\t<FileHashValue>").append(contentFile.getHash_code()).append("</FileHashValue>\n");
                  stringBuffer.append("\t\t</FileItem>\n");
               }
            }
         } catch (Exception var24) {
            this.logger.error("[MagicInfo_FTP] " + var24.getMessage() + this.contentName + "[" + this.contentId + "]", var24);
         }

         stringBuffer.append("\t</FtpFileContents>\n");
         stringBuffer.append("</FTPContent>");
         fileOutputStream = new FileOutputStream(csdFile);
         fileChannel = fileOutputStream.getChannel();
         Charset charSet = Charset.forName("UTF-8");
         CharsetEncoder encoder = charSet.newEncoder();
         fileChannel.write(encoder.encode(CharBuffer.wrap(stringBuffer.toString())));
         fileOutputStream.close();
         fileChannel.close();
         var29 = csdFile;
      } catch (Exception var25) {
         this.logger.error("[MagicInfo_FTP] " + var25.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var25);
         return null;
      } finally {
         try {
            if (fileOutputStream != null) {
               fileOutputStream.close();
            }

            if (fileChannel != null) {
               fileChannel.close();
            }
         } catch (Exception var23) {
            this.logger.error("[MagicInfo_FTP] " + var23.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var23);
         }

      }

      return var29;
   }

   public synchronized void copyFile(File srcFile, File destFile) {
      FileInputStream fileInputStream = null;
      FileOutputStream fileOutputStream = null;
      boolean var5 = false;

      try {
         fileInputStream = new FileInputStream(srcFile);
         fileOutputStream = new FileOutputStream(destFile);

         int data;
         while((data = fileInputStream.read()) != -1) {
            fileOutputStream.write(data);
         }

         this.logger.debug("[MagicInfo_FTP] copy file SUCCESSFULLY! : " + srcFile.getName() + ", " + destFile.getName());
      } catch (Exception var15) {
         this.logger.error("[MagicInfo_FTP] " + var15.getMessage() + this.contentName + "|" + srcFile.getName() + "|" + destFile.getName(), var15);
      } finally {
         try {
            if (fileInputStream != null) {
               fileInputStream.close();
            }

            if (fileOutputStream != null) {
               fileOutputStream.close();
            }
         } catch (Exception var14) {
            this.logger.error("[MagicInfo_FTP] " + var14.getMessage() + this.contentName + "|" + srcFile.getName() + "|" + destFile.getName(), var14);
         }

      }

   }

   public String makeRemoteFileInfo(String fileName, long fileSize, String fileStatus, String isDownload) {
      return fileName + "|" + fileSize + "|" + fileStatus + "|" + isDownload;
   }

   public boolean requestDownload() {
      if (!this.isFilesToDownload) {
         this.logger.debug("[MagicInfo_FTP] No files To Download : " + this.contentName + "[" + this.contentId + "]");
         return false;
      } else {
         MsgVO msgVO = new MsgVO();
         msgVO.setType("MSG_FTP_DOWNLOAD_START");
         msgVO.setMiUserId(this.miUserId);
         msgVO.setGroupId(this.groupId);
         msgVO.setContentId(this.contentId);
         msgVO.setContentName(this.contentName);
         msgVO.setServerIp(this.serverIp);
         msgVO.setServerPort(this.serverPort);
         msgVO.setLoginId(this.loginId);
         msgVO.setPassword(this.password);
         msgVO.setLocalPathByIp(this.localPathByIp);
         msgVO.setDirectory(this.directory);
         msgVO.setRefreshInterval(this.refreshInterval);
         msgVO.setScheduledJob(this.scheduledJob);
         msgVO.setCanRefresh(this.canRefresh);
         msgVO.setLoginRetryMaxCount(this.loginRetryMaxCount);
         msgVO.setLoginRetryCount(this.loginRetryCount);
         msgVO.setCanLoginRetry(this.canLoginRetry);
         msgVO.setResult(this.result);
         List tmpFileListToSave = new ArrayList();
         tmpFileListToSave.addAll(this.fileListToSave);
         msgVO.setFileListToSave(tmpFileListToSave);
         msgVO.setCONTENTS_HOME(this.CONTENTS_HOME);
         msgVO.setEditMode(this.editMode);
         msgVO.setSettingChanged(this.settingChanged);
         List tmpRemoteFiles = new ArrayList();
         tmpRemoteFiles.addAll(this.remoteFiles);
         msgVO.setRemoteFiles(tmpRemoteFiles);
         msgVO.setTotalSizeOfFiles(this.totalSizeOfFiles);
         msgVO.setFileChanged(this.fileChanged);
         msgVO.setMainFileId(this.mainFileId);
         msgVO.setCharacterEncoding(this.characterEncoding);
         msgVO.setFilesToDownload(this.isFilesToDownload);
         msgVO.setPollingStatus(this.pollingStatus);
         msgVO.setStatusDescription(this.statusDescription);
         MsgProducer msgProducer = MsgProducerImpl.getInstance();

         try {
            msgProducer.send(msgVO);
         } catch (JmsException var7) {
            this.logger.error("[MagicInfo_FTP] " + var7.getMessage() + " " + msgVO.getContentName() + "[" + msgVO.getContentId() + "]", var7);
            StringWriter sw = new StringWriter();
            this.logger.error(sw.toString());
            this.pollingStatus = "FAIL";
            this.statusDescription = "UNEXPECTED ERROR AMQ";
            return false;
         }

         this.logger.info("<<<<<<<<<<[MagicInfo_FTP] " + msgVO.getType() + " " + msgVO.getContentName() + "[" + msgVO.getContentId() + "]");
         return true;
      }
   }

   public String getMembers() {
      String retVal = "miUserId           : " + this.miUserId + "\ngroupId            : " + this.groupId + "\ncontentId          : " + this.contentId + "\ncontentName        : " + this.contentName + "\nserverIp           : " + this.serverIp + "\nloginId            : " + this.loginId + "\npassword           : " + this.password + "\nlocalPathByIp      : " + this.localPathByIp + "\ndirectory          : " + this.directory + "\nrefreshInterval    : " + this.refreshInterval + "\nscheduledJob       : " + this.scheduledJob + "\ncanRefresh         : " + this.canRefresh + "\nloginRetryMaxCount : " + this.loginRetryMaxCount + "\nloginRetryCount    : " + this.loginRetryCount + "\ncanLoginRetry      : " + this.canLoginRetry + "\nresult             : " + this.result + "\nCONTENTS_HOME      : " + this.CONTENTS_HOME + "\neditMode           : " + this.editMode + "\nsettingChanged     : " + this.settingChanged + "\ntotalSizeOfFiles   : " + this.totalSizeOfFiles + "\nfileChanged        : " + this.fileChanged + "\nmainFileId         : " + this.mainFileId + "\ncharacterEncoding  : " + this.characterEncoding + "\nisFilesToDownload  : " + this.isFilesToDownload + "\nfileListToSave     : \n";

      int i;
      for(i = 0; i < this.fileListToSave.size(); ++i) {
         retVal = retVal + " " + (i + 1) + ") " + (String)this.fileListToSave.get(i) + "\n";
      }

      retVal = retVal + "remoteFiles        : \n";

      for(i = 0; i < this.remoteFiles.size(); ++i) {
         retVal = retVal + " " + (i + 1) + ") " + (String)this.remoteFiles.get(i) + "\n";
      }

      retVal = retVal + "serverPort         : " + this.serverPort + "\n";
      retVal = retVal + "pollingStatus      : " + this.pollingStatus + "\n";
      retVal = retVal + "statusDescription  : " + this.statusDescription + "\n";
      return retVal;
   }
}
