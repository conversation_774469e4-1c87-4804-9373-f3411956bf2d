package com.samsung.magicinfo.framework.role.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.RoleUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.magicinfo.framework.role.entity.Role;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class RoleDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(RoleDao.class);

   public RoleDao() {
      super();
   }

   public RoleDao(SqlSession session) {
      super(session);
   }

   public Boolean addAbilityListByRoleId(Long roleId, List abilityMapList) throws SQLException {
      if (abilityMapList.size() == 0) {
         return false;
      } else {
         SqlSession session = this.openNewSession(false);

         Boolean var13;
         try {
            this.deleteAllAbilityByRoleId(roleId, session);
            Iterator var4 = abilityMapList.iterator();

            while(var4.hasNext()) {
               Object object = var4.next();
               Long ablityId = (Long)((Map)object).get("ability_id");
               if (((RoleDaoMapper)this.getMapper(session)).addAbilityListByRoleId(roleId, ablityId) == 0) {
                  session.rollback();
                  Boolean var7 = false;
                  return var7;
               }
            }

            session.commit();
            var13 = true;
         } catch (SQLException var11) {
            session.rollback();
            throw var11;
         } finally {
            session.close();
         }

         return var13;
      }
   }

   /** @deprecated */
   @Deprecated
   public Boolean addInitAbilityByRoleId(Long roleId) throws SQLException {
      throw new SQLException("all params are not set");
   }

   public Boolean deleteAllAbilityByRoleId(Long roleId, SqlSession session) throws SQLException {
      RoleDaoMapper mapper = (RoleDaoMapper)this.getMapper();
      if (session != null) {
         mapper = (RoleDaoMapper)this.getMapper(session);
      }

      return mapper.deleteAllAbilityByRoleId(roleId);
   }

   public Boolean deleteAllAbilityByRoleId(Long roleId) throws SQLException {
      return this.deleteAllAbilityByRoleId(roleId, (SqlSession)null);
   }

   public List getAbilityListByRoleId(Long roleId) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).getAbilityListByRoleId(roleId);
   }

   public Role getAllByRoleId(Long roleId) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).getAllByRoleId(roleId);
   }

   public long addRole(Role role) throws SQLException {
      long roleId = (long)SequenceDB.getNextValue("MI_USER_INFO_ROLE");
      if (roleId <= 0L) {
         return -1L;
      } else {
         long result = ((RoleDaoMapper)this.getMapper()).addRole(role, roleId);
         return result == 0L ? -1L : roleId;
      }
   }

   public Boolean setRole(Role role) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).setRole(role);
   }

   public String getNameByRoleId(Long roleId) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).getNameByRoleId(roleId);
   }

   public List getAllRole(Map map, int startPos, int pageSize) throws SQLException {
      List list = null;

      try {
         Map paramsMap = new HashMap();
         paramsMap.putAll(map);
         String sortColumn;
         if (map.get("searchText") != null && !map.get("searchText").equals("")) {
            sortColumn = map.get("searchText").toString();
            sortColumn = sortColumn.replaceAll("_", "^_");
            sortColumn = sortColumn.toUpperCase();
            paramsMap.put("searchText", sortColumn);
         }

         if (map.get("sortColumn") != null && !"".equals(map.get("sortColumn"))) {
            sortColumn = ((String)map.get("sortColumn")).toUpperCase();
            if (sortColumn.equalsIgnoreCase("USER_COUNT")) {
               paramsMap.put("sortColumn", "B." + sortColumn);
            } else if (sortColumn.equalsIgnoreCase("ROLE_NAME")) {
               paramsMap.put("sortColumn", "A." + sortColumn);
            } else if (sortColumn.equalsIgnoreCase("GROUP_NAME")) {
               paramsMap.put("sortColumn", "E." + sortColumn);
            }
         } else {
            paramsMap.put("sortColumn", "ROLE_NAME");
         }

         if (map.get("sortOrder") != null && !"".equals(map.get("sortOrder"))) {
            paramsMap.put("sortOrder", ((String)map.get("sortOrder")).toUpperCase());
         } else {
            paramsMap.put("sortOrder", "ASC");
         }

         int offset = 0;
         if (startPos > 0) {
            offset = startPos - 1;
         }

         paramsMap.put("offset", offset);
         paramsMap.put("pageSize", pageSize);
         paramsMap.put("scope", RoleUtils.SCOPE_ALL);
         List allList = ((RoleDaoMapper)this.getMapper()).getAllRole(paramsMap);
         paramsMap.put("scope", RoleUtils.SCOPE_GROUP);
         list = ((RoleDaoMapper)this.getMapper()).getAllRole(paramsMap);
         Role serverAdmin = null;
         boolean flag = map.get("organization").equals("ROOT") && map.get("isSearch").equals(true);
         Iterator var10 = allList.iterator();

         while(true) {
            while(var10.hasNext()) {
               Role allRole = (Role)var10.next();
               if (flag && serverAdmin == null && allRole.getRole_name().equals("Server Administrator")) {
                  serverAdmin = allRole;
               }

               Iterator var12 = list.iterator();

               while(var12.hasNext()) {
                  Role groupRole = (Role)var12.next();
                  if (allRole.getRole_name().equals(groupRole.getRole_name())) {
                     groupRole.setUser_count(groupRole.getUser_count() + allRole.getUser_count());
                     break;
                  }
               }
            }

            if (flag) {
               list.add(serverAdmin);
            }
            break;
         }
      } catch (SQLException var14) {
         this.logger.error("", var14);
      }

      return list;
   }

   public int getCountAllRole(Map map) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.putAll(map);
      String sortColumn;
      if (map.get("searchText") != null && !map.get("searchText").equals("")) {
         sortColumn = map.get("searchText").toString();
         sortColumn = sortColumn.replaceAll("_", "^_");
         sortColumn = sortColumn.toUpperCase();
         paramsMap.put("searchText", sortColumn);
      }

      if (map.get("sortColumn") != null && !"".equals(map.get("sortColumn"))) {
         sortColumn = ((String)map.get("sortColumn")).toUpperCase();
         if (sortColumn.equalsIgnoreCase("USER_COUNT")) {
            paramsMap.put("sortColumn", "B." + sortColumn);
         } else if (sortColumn.equalsIgnoreCase("ROLE_NAME")) {
            paramsMap.put("sortColumn", "A." + sortColumn);
         } else if (sortColumn.equalsIgnoreCase("GROUP_NAME")) {
            paramsMap.put("sortColumn", "E." + sortColumn);
         }
      } else {
         paramsMap.put("sortColumn", "ROLE_NAME");
      }

      if (map.get("sortOrder") != null && !"".equals(map.get("sortOrder"))) {
         paramsMap.put("sortOrder", ((String)map.get("sortOrder")).toUpperCase());
      } else {
         paramsMap.put("sortOrder", "ASC");
      }

      return ((RoleDaoMapper)this.getMapper()).getCountAllRole(paramsMap);
   }

   public Boolean addMapRoleUser(Long roleId, String userId) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).addMapRoleUser(roleId, userId);
   }

   public Role getRoleByUserId(String userId) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).getRoleByUserId(userId);
   }

   public Long getRoleIdMapRoleUserByUserId(String userId) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).getRoleIdMapRoleUserByUserId(userId);
   }

   public Boolean setRoleIdMapRoleUserByUserId(Long roleId, String userId) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).setRoleIdMapRoleUserByUserId(roleId, userId);
   }

   public int getCountRoleByRoleName(String roleName) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).getCountRoleByRoleName(roleName);
   }

   public List getChildRoleListByRootGroupId(Map map) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).getChildRoleListByRootGroupId(map);
   }

   public int getCountChildRoleListByRootGroupId(Map map) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).getCountChildRoleListByRootGroupId(map);
   }

   public Role getAllByRoleName(String roleName) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).getAllByRoleName(roleName);
   }

   public int getCountUserByRoleId(Long roleId) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).getCountUserByRoleId(roleId);
   }

   public List getMappedUseListrByRoleId(Long roleId) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).getMappedUseListrByRoleId(roleId);
   }

   public Long getRoleIdByRoleName(String roleName) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).getRoleIdByRoleName(roleName);
   }

   public boolean checkDeviceRead(String roleName) throws SQLException {
      if (roleName != null) {
         Long roleId = this.getRoleIdByRoleName(roleName);
         boolean result = this.checkDeviceReadByRoleId(roleId);
         return result;
      } else {
         return false;
      }
   }

   public boolean checkDeviceReadByRoleId(Long roleId) throws SQLException {
      Long object = ((RoleDaoMapper)this.getMapper()).checkDeviceReadByRoleId(roleId);
      return object > 0L;
   }

   public Boolean deleteRoleByRoleId(Long roleId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      Boolean var3;
      try {
         if (!this.deleteAllAbilityByRoleId(roleId, session)) {
            session.rollback();
            var3 = false;
            return var3;
         }

         if (((RoleDaoMapper)this.getMapper(session)).deleteRoleByRoleId(roleId) == 0) {
            session.rollback();
            var3 = false;
            return var3;
         }

         session.commit();
         var3 = true;
      } catch (SQLException var7) {
         session.rollback();
         throw var7;
      } finally {
         session.close();
      }

      return var3;
   }

   public List getUserListByRoleId(Long roleId, Map map) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.putAll(map);
      paramsMap.put("roleId", roleId);
      String sortColumn = "";
      if (map.get("sortColumn") != null && !"".equals(map.get("sortColumn"))) {
         sortColumn = ((String)map.get("sortColumn")).toUpperCase();
         if (sortColumn.equalsIgnoreCase("GROUP_NAME")) {
            sortColumn = "C." + sortColumn;
         } else {
            sortColumn = "D." + sortColumn;
         }
      }

      String sortOrder = "";
      if (map.get("sortOrder") != null && !"".equals(map.get("sortOrder"))) {
         sortOrder = ((String)map.get("sortOrder")).toUpperCase();
      }

      if (!sortColumn.equals("") && !sortOrder.equals("")) {
         paramsMap.put("sortColumn", sortColumn);
         paramsMap.put("sortOrder", sortOrder);
      }

      return ((RoleDaoMapper)this.getMapper()).getUserListByRoleId(paramsMap);
   }

   public List getAllRoleList(Map map) throws SQLException {
      try {
         Map paramsMap = new HashMap();
         paramsMap.putAll(map);
         String sortColumn;
         if (map.get("searchText") != null && !map.get("searchText").equals("")) {
            sortColumn = map.get("searchText").toString();
            sortColumn = sortColumn.replaceAll("_", "^_");
            paramsMap.put("searchText", sortColumn);
         }

         if (map.get("sortColumn") != null && !"".equals(map.get("sortColumn"))) {
            sortColumn = ((String)map.get("sortColumn")).toUpperCase();
            if (sortColumn.equalsIgnoreCase("USER_COUNT")) {
               paramsMap.put("sortColumn", "B." + sortColumn);
            } else {
               paramsMap.put("sortColumn", "A." + sortColumn);
            }
         } else {
            paramsMap.put("sortColumn", "ROLE_NAME");
         }

         if (map.get("sortOrder") != null && !"".equals(map.get("sortOrder"))) {
            paramsMap.put("sortOrder", ((String)map.get("sortOrder")).toUpperCase());
         } else {
            paramsMap.put("sortOrder", "ASC");
         }

         paramsMap.put("scope", RoleUtils.SCOPE_ALL);
         List allList = ((RoleDaoMapper)this.getMapper()).getAllRoleList(paramsMap);
         paramsMap.put("scope", RoleUtils.SCOPE_GROUP);
         List list = ((RoleDaoMapper)this.getMapper()).getAllRoleList(paramsMap);
         Iterator var5 = allList.iterator();

         while(true) {
            while(var5.hasNext()) {
               Role allRole = (Role)var5.next();
               Iterator var7 = list.iterator();

               while(var7.hasNext()) {
                  Role groupRole = (Role)var7.next();
                  if (allRole.getRole_name().equals(groupRole.getRole_name())) {
                     groupRole.setUser_count(groupRole.getUser_count() + allRole.getUser_count());
                     break;
                  }
               }
            }

            return list;
         }
      } catch (SQLException var9) {
         this.logger.error("", var9);
         return null;
      }
   }

   public int getCntRuleManagerAbilityByRoleId(long roleId) throws SQLException {
      return ((RoleDaoMapper)this.getMapper()).getCntRuleManagerAbilityByRoleId(roleId);
   }

   public List getRoleExport(Map map) {
      try {
         Map paramsMap = new HashMap();
         paramsMap.putAll(map);
         String sortColumn;
         if (map.get("searchText") != null && !map.get("searchText").equals("")) {
            sortColumn = map.get("searchText").toString();
            sortColumn = sortColumn.replaceAll("_", "^_");
            paramsMap.put("searchText", sortColumn);
         }

         if (map.get("sortColumn") != null && !"".equals(map.get("sortColumn"))) {
            sortColumn = ((String)map.get("sortColumn")).toUpperCase();
            if (sortColumn.equalsIgnoreCase("USER_COUNT")) {
               paramsMap.put("sortColumn", "B." + sortColumn);
            } else if (sortColumn.equalsIgnoreCase("ORGANIZATION")) {
               paramsMap.put("sortColumn", "ORGANIZATION");
            } else {
               paramsMap.put("sortColumn", "A." + sortColumn);
            }
         } else {
            paramsMap.put("sortColumn", "ROLE_NAME");
         }

         if (map.get("sortOrder") != null && !"".equals(map.get("sortOrder"))) {
            paramsMap.put("sortOrder", ((String)map.get("sortOrder")).toUpperCase());
         } else {
            paramsMap.put("sortOrder", "ASC");
         }

         paramsMap.put("scope", RoleUtils.SCOPE_GROUP);
         List list = ((RoleDaoMapper)this.getMapper()).getRoleExport(paramsMap);
         paramsMap.put("scope", RoleUtils.SCOPE_ALL);
         paramsMap.put("sortColumn", "ROLE_NAME");
         List allList = ((RoleDaoMapper)this.getMapper()).getAllRoleList(paramsMap);
         Iterator var5 = allList.iterator();

         while(true) {
            while(var5.hasNext()) {
               Role allRole = (Role)var5.next();
               Iterator var7 = list.iterator();

               while(var7.hasNext()) {
                  Role groupRole = (Role)var7.next();
                  if (allRole.getRole_name().equals(groupRole.getRole_name())) {
                     groupRole.setUser_count(groupRole.getUser_count() + allRole.getUser_count());
                     break;
                  }
               }
            }

            return list;
         }
      } catch (SQLException var9) {
         this.logger.error("", var9);
         return null;
      }
   }
}
