package com.samsung.common.utils;

import java.util.Vector;

public class StrParser {
   private String source;
   private boolean returnDelims;
   private Vector delims;
   private boolean parsed;
   private Vector token;
   private int curIndex;

   public StrParser(String str) {
      this(str, false);
   }

   public StrParser(String str, boolean rtnDelims) {
      super();
      this.source = null;
      this.returnDelims = false;
      this.delims = null;
      this.parsed = false;
      this.token = null;
      this.curIndex = 0;
      this.source = str;
      this.returnDelims = rtnDelims;
      this.delims = new Vector();
   }

   public void addDelimiter(String delim) {
      this.delims.add(delim);
   }

   private void parse() {
      this.token = new Vector();
      int point = 0;
      int index = false;
      int min = false;
      int mindex = 0;
      String delim = null;

      while(true) {
         int min = this.source.length();

         for(int i = 0; i < this.delims.size(); ++i) {
            int index = this.source.indexOf((String)this.delims.get(i), point);
            if (index != -1 && index < min) {
               min = index;
               mindex = i;
            }
         }

         if (min == this.source.length()) {
            if (point < this.source.length()) {
               this.token.add(this.source.substring(point));
            }

            this.parsed = true;
            return;
         }

         if (point < min) {
            this.token.add(this.source.substring(point, min));
         }

         delim = (String)this.delims.get(mindex);
         if (this.returnDelims) {
            this.token.add(delim);
         }

         point = min + delim.length();
      }
   }

   public String nextToken() {
      if (!this.parsed) {
         this.parse();
      }

      return this.curIndex == this.token.size() ? null : (String)this.token.get(this.curIndex++);
   }
}
