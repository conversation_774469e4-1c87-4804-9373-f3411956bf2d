package com.samsung.common.cache;

import java.util.List;
import net.spy.memcached.CASResponse;
import net.spy.memcached.CASValue;

public interface BasicCache {
   void set(String var1, Object var2) throws Exception;

   void set(String var1, int var2, Object var3) throws Exception;

   CASValue gets(String var1, Object var2) throws Exception;

   Object get(String var1) throws Exception;

   void clean() throws Exception;

   void delete(String var1) throws Exception;

   CASResponse cas(String var1, long var2, Object var4) throws Exception;

   Object cas(String var1, Object var2, MutatorOperation var3);

   boolean isEmpty(String var1) throws Exception;

   Object getMapCache(String var1, String var2, int var3);

   boolean putMapCache(String var1, String var2, Object var3);

   void removeMapCache(String var1, String var2);

   List readAllMap(String var1);

   int getSizeMap(String var1);

   boolean enQueue(String var1, Object var2);

   Object deQueue(String var1);

   List readAllQueue(String var1);

   Object getQueue(String var1);

   boolean isExistServiceInQueue(String var1, String var2) throws Exception;
}
