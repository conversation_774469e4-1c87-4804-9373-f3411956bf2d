package com.samsung.magicinfo.rc.framework.user.entity;

import java.sql.Timestamp;

public class User {
  private int count;
  
  private Timestamp create_date;
  
  private Timestamp last_login_date;
  
  private Timestamp modify_date;
  
  public void setCount(int count) {
    this.count = count;
  }
  
  public void setCreate_date(Timestamp create_date) {
    this.create_date = create_date;
  }
  
  public void setLast_login_date(Timestamp last_login_date) {
    this.last_login_date = last_login_date;
  }
  
  public void setModify_date(Timestamp modify_date) {
    this.modify_date = modify_date;
  }
  
  public int getCount() {
    return this.count;
  }
  
  public Timestamp getCreate_date() {
    return this.create_date;
  }
  
  public Timestamp getLast_login_date() {
    return this.last_login_date;
  }
  
  public Timestamp getModify_date() {
    return this.modify_date;
  }
}
