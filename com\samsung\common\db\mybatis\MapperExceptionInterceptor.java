package com.samsung.common.db.mybatis;

import org.apache.commons.proxy.Interceptor;
import org.apache.commons.proxy.Invocation;
import org.apache.ibatis.exceptions.PersistenceException;

public class MapperExceptionInterceptor implements Interceptor {
   private static MapperExceptionInterceptor interceptor = new MapperExceptionInterceptor();

   public MapperExceptionInterceptor() {
      super();
   }

   public static Object addInterceptor(Object mapper, Class mapperClass) {
      return ProxyFactoryManager.getInstance().createInterceptorProxy(mapper, interceptor, new Class[]{mapperClass});
   }

   public Object intercept(Invocation invocation) throws Throwable {
      try {
         return invocation.proceed();
      } catch (PersistenceException var3) {
         if (var3.getCause() != null) {
            throw var3.getCause();
         } else {
            throw var3;
         }
      }
   }
}
