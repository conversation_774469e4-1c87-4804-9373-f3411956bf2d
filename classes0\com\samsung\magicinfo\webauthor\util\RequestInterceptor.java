package com.samsung.magicinfo.webauthor.util;

import com.samsung.magicinfo.webauthor.util.UserData;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

@Component
public class RequestInterceptor extends HandlerInterceptorAdapter {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor.util.RequestInterceptor.class);
  
  private static final String ANONYMOUS = "anonymous";
  
  @Autowired
  private UserData userData;
  
  public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
    if (this.userData.getUserId() != null && !"".equals(this.userData.getUserId())) {
      MDC.put("userName", this.userData.getUserId());
    } else {
      MDC.put("userName", "anonymous");
    } 
    return true;
  }
}
