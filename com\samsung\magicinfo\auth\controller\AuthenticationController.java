package com.samsung.magicinfo.auth.controller;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.auth.model.json.request.AuthenticationRequest;
import com.samsung.magicinfo.auth.model.json.request.HmacAuthenticationRequest;
import com.samsung.magicinfo.auth.model.json.response.AuthenticationResponse;
import com.samsung.magicinfo.auth.security.MfaService;
import com.samsung.magicinfo.auth.security.TokenUtils;
import com.samsung.magicinfo.framework.kpi.annotation.KPI;
import com.samsung.magicinfo.framework.kpi.annotation.LogProperty;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserContractManager;
import com.samsung.magicinfo.framework.user.manager.UserContractManagerImpl;
import com.samsung.magicinfo.mvc.security.ExtendedWebAuthenticationDetailsSource;
import com.samsung.magicinfo.mvc.security.HmacAuthenticationToken;
import com.samsung.magicinfo.mvc.security.exception.LoginFailRetryCount;
import com.samsung.magicinfo.mvc.security.exception.UserNotApprovedAuthenticationException;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import java.sql.SQLException;
import javax.servlet.http.HttpServletRequest;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mobile.device.Device;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/auth"})
public class AuthenticationController {
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   @Autowired
   private AuthenticationManager authenticationManager;
   private TokenUtils tokenUtils = new TokenUtils();
   @Lazy
   @Autowired
   private MfaService mfaService;
   @Autowired
   private UserDetailsService userDetailsService;

   public AuthenticationController() {
      super();
   }

   @KPI
   @LogProperty(
      eventType = "Login"
   )
   @RequestMapping(
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity authenticationRequest(@RequestBody AuthenticationRequest authenticationRequest, Device device, HttpServletRequest context) throws AuthenticationException {
      String token = null;
      String userId = null;

      try {
         userId = authenticationRequest.getUsername();
         UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(authenticationRequest.getUsername(), authenticationRequest.getPassword());
         ExtendedWebAuthenticationDetailsSource authenticationDetailsSource = new ExtendedWebAuthenticationDetailsSource();
         usernamePasswordAuthenticationToken.setDetails(authenticationDetailsSource.buildDetails(context));
         Authentication authentication = this.authenticationManager.authenticate(usernamePasswordAuthenticationToken);
         this.mfaService.isValidMfa(authenticationRequest);
         SecurityContextHolder.getContext().setAuthentication(authentication);
         UserDetails userDetails = this.userDetailsService.loadUserByUsername(authenticationRequest.getUsername());
         token = this.tokenUtils.generateToken(userDetails);
      } catch (LoginFailRetryCount var11) {
         this.logger.error("[TO_DELETE] LoginFailRetryCount" + var11.toString());
         return new ResponseEntity(HttpStatus.LOCKED);
      } catch (UserNotApprovedAuthenticationException var12) {
         this.logger.error("[TO_DELETE] UserNotApprovedAuthenticationException=>" + var12.toString());
         return new ResponseEntity(HttpStatus.NOT_ACCEPTABLE);
      } catch (RestServiceException var13) {
         if (MfaService.OTP_EXCEPTION_LIST.contains(var13.getErrorCode())) {
            throw var13;
         }

         return this.addLoginFailCount(userId);
      } catch (Exception var14) {
         return this.addLoginFailCount(userId);
      }

      if (token != null) {
         UserContractManagerImpl userDao = UserContractManagerImpl.getInstance();

         try {
            userDao.deleteUserFailCount(userId);
         } catch (SQLException var10) {
            this.logger.error("", var10);
         }

         User user = SecurityUtils.getLoginUser();
         AuthenticationResponse authenticationResponse = new AuthenticationResponse(token, user.getDate_format(), user.getTime_format(), user.getSchedule_first_day());
         return ResponseEntity.ok(authenticationResponse);
      } else {
         return new ResponseEntity(HttpStatus.UNAUTHORIZED);
      }
   }

   private ResponseEntity addLoginFailCount(String userId) {
      UserContractManager userDao = UserContractManagerImpl.getInstance();
      if (userId != null) {
         try {
            userDao.addUserFailCount(userId);
         } catch (SQLException var4) {
            this.logger.error("", var4);
            return new ResponseEntity(HttpStatus.UNAUTHORIZED);
         }
      }

      return new ResponseEntity(HttpStatus.UNAUTHORIZED);
   }

   @RequestMapping(
      value = {"/refresh"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity authenticationRequest(HttpServletRequest request) {
      String token = request.getHeader(TokenUtils.tokenHeader);
      String username = this.tokenUtils.getUsernameFromToken(token);
      this.userDetailsService.loadUserByUsername(username);
      if (this.tokenUtils.canTokenBeRefreshed(token)) {
         String refreshedToken = this.tokenUtils.refreshToken(token);
         return ResponseEntity.ok(new AuthenticationResponse(refreshedToken));
      } else {
         return ResponseEntity.badRequest().body((Object)null);
      }
   }

   @RequestMapping(
      value = {"/hmac"},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity hamcAuthenticationRequest(@RequestBody HmacAuthenticationRequest authenticationRequest, HttpServletRequest context) throws AuthenticationException {
      String token = null;
      String userId = null;

      try {
         userId = authenticationRequest.getUserid();
         HmacAuthenticationToken hmacToken = new HmacAuthenticationToken(authenticationRequest.getUserid(), authenticationRequest.getHash(), authenticationRequest.getTime());
         ExtendedWebAuthenticationDetailsSource authenticationDetailsSource = new ExtendedWebAuthenticationDetailsSource();
         hmacToken.setDetails(authenticationDetailsSource.buildDetails(context));
         Authentication authentication = this.authenticationManager.authenticate(hmacToken);
         SecurityContextHolder.getContext().setAuthentication(authentication);
         UserDetails userDetails = this.userDetailsService.loadUserByUsername(userId);
         token = this.tokenUtils.generateToken(userDetails);
      } catch (Exception var9) {
         this.logger.error(var9);
         return new ResponseEntity(HttpStatus.UNAUTHORIZED);
      }

      return token != null ? ResponseEntity.ok(new AuthenticationResponse(token)) : new ResponseEntity(HttpStatus.UNAUTHORIZED);
   }
}
