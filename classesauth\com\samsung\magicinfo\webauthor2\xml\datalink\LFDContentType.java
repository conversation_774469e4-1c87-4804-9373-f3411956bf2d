package com.samsung.magicinfo.webauthor2.xml.datalink;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "LFDContentType", propOrder = {"fileID", "fileName", "fileHashValue", "fileSize"})
public class LFDContentType {
  @XmlElement(name = "FileID", required = true)
  protected String fileID;
  
  @XmlElement(name = "FileName", required = true)
  protected String fileName;
  
  @XmlElement(name = "FileHashValue", required = true)
  protected String fileHashValue;
  
  @XmlElement(name = "FileSize")
  protected long fileSize;
  
  @XmlAttribute(name = "id")
  protected String id;
  
  public String getFileID() {
    return this.fileID;
  }
  
  public void setFileID(String value) {
    this.fileID = value;
  }
  
  public String getFileName() {
    return this.fileName;
  }
  
  public void setFileName(String value) {
    this.fileName = value;
  }
  
  public String getFileHashValue() {
    return this.fileHashValue;
  }
  
  public void setFileHashValue(String value) {
    this.fileHashValue = value;
  }
  
  public long getFileSize() {
    return this.fileSize;
  }
  
  public void setFileSize(long value) {
    this.fileSize = value;
  }
  
  public String getId() {
    return this.id;
  }
  
  public void setId(String value) {
    this.id = value;
  }
}
