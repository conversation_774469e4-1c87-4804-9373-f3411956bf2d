package com.samsung.magicinfo.framework.device.preconfig.dao;

import com.samsung.common.db.PagedListInfo;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceServiceConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSoftwareConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.preconfig.entity.DevicePreconfig;
import com.samsung.magicinfo.framework.device.warningRule.dao.DeviceWarningRuleDao;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class DevicePreconfigDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(DeviceWarningRuleDao.class);

   public DevicePreconfigDao() {
      super();
   }

   public DevicePreconfigDao(SqlSession session) {
      super(session);
   }

   public PagedListInfo getPreconfigList(Map condition) throws SQLException {
      SelectCondition condObj = (SelectCondition)condition.get("condition");
      List pgList = ((DevicePreconfigDaoMapper)this.getMapper()).getPreconfigList(condObj);
      int totalCnt = ((DevicePreconfigDaoMapper)this.getMapper()).getPreconfigListTotalCount(condObj);
      return new PagedListInfo(pgList, totalCnt);
   }

   public int addDevicePreconfigInfo(DevicePreconfig preconfig) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).addDevicePreconfigInfo(preconfig);
   }

   public DevicePreconfig getPreconfigInfo(String preconfigId) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).getPreconfigInfo(preconfigId);
   }

   public DevicePreconfig getPreconfigInfoByDeviceId(String deviceId) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).getPreconfigInfoByDeviceId(deviceId);
   }

   public boolean deletePreconfig(String[] preconfigIdList) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).deletePreconfig(preconfigIdList);
   }

   public int addPreconfigGroupMapping(Map map) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).addPreconfigGroupMapping(map);
   }

   public boolean updatePreconfigInfo(DevicePreconfig preconfig) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).updatePreconfigInfo(preconfig);
   }

   public int addSoftwareConfig(DeviceSoftwareConf service) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).addSoftwareConfig(service);
   }

   public int addServiceConfig(DeviceServiceConf service) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).addServiceConfig(service);
   }

   public boolean updateServiceConfig(DeviceServiceConf service) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).updateServiceConfig(service);
   }

   public List getSoftwareConfigInfo(String preconfigId) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).getSoftwareConfigInfo(preconfigId);
   }

   public List getServiceConfigInfo(String preconfigId) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).getServiceConfigInfo(preconfigId);
   }

   public boolean deleteSoftwareConfig(String preconfigId) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).deleteSoftwareConfig(preconfigId);
   }

   public boolean deleteServiceConfig(String preconfigId) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).deleteServiceConfig(preconfigId);
   }

   public boolean setDeployStatus(String preconfigId, String deviceId, String status) {
      return preconfigId != null && deviceId != null ? ((DevicePreconfigDaoMapper)this.getMapper()).setDeployStatus(preconfigId, deviceId, status) : false;
   }

   public boolean setDeployStatusReportTime(String preconfigId, String deviceId, Date reportTime) {
      return ((DevicePreconfigDaoMapper)this.getMapper()).setDeployStatusReportTime(preconfigId, deviceId, reportTime);
   }

   public boolean deletePreconfigGroupMapping(String preconfigId) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).deletePreconfigGroupMapping(preconfigId);
   }

   public boolean deletePreconfigGroupMappingByGroup(Long[] deviceGroupList) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).deletePreconfigGroupMappingByGroup(deviceGroupList);
   }

   public int addDeployStatus(String preconfigId, String deviceId, String status) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).addDeployStatus(preconfigId, deviceId, status);
   }

   public int setDeployStatusByGroup(String preconfigId, String status, Long groupId) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).setDeployStatusByGroup(preconfigId, status, groupId);
   }

   public boolean deleteDeployStatus(String preconfigId) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).deleteDeployStatus(preconfigId);
   }

   public List getGroupMappingByPreconfig(String preconfigId) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).getGroupMappingByPreconfig(preconfigId);
   }

   public int checkExistServiceConfig(DeviceServiceConf service) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).checkExistServiceConfig(service);
   }

   public boolean deleteDeployStatusByGroup(Long[] deviceGroupList) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).deleteDeployStatusByGroup(deviceGroupList);
   }

   public List getDeployStatusByPreconfigId(@Param("preconfigId") String preconfigId) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).getDeployStatusByPreconfigId(preconfigId);
   }

   public Map getDeployStatusByDeviceId(@Param("deviceId") String deviceId) throws SQLException {
      return ((DevicePreconfigDaoMapper)this.getMapper()).getDeployStatusByDeviceId(deviceId);
   }

   public boolean deletePreconfigFromDevice(String deviceId) {
      return ((DevicePreconfigDaoMapper)this.getMapper()).deletePreconfigFromDevice(deviceId);
   }
}
