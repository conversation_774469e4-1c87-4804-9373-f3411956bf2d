package com.samsung.magicinfo.restapi.contents.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Size;

@JsonInclude(Include.NON_EMPTY)
public class V2ContentWebAuthorResource {
   @ApiModelProperty(
      dataType = "string",
      value = "Login user ID",
      example = "admin"
   )
   @Size(
      max = 64,
      message = "[ContentFilter][userId] max size is 64."
   )
   private String userId = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Value issued for the identified user"
   )
   private String token = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Start page location of \"Web Author\" feature"
   )
   private String webAuthorPath = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Locale information"
   )
   private String language = "";

   public V2ContentWebAuthorResource() {
      super();
   }

   public String getUserId() {
      return this.userId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }

   public String getToken() {
      return this.token;
   }

   public void setToken(String token) {
      this.token = token;
   }

   public String getWebAuthorPath() {
      return this.webAuthorPath;
   }

   public void setWebAuthorPath(String webAuthorPath) {
      this.webAuthorPath = webAuthorPath;
   }

   public String getLanguage() {
      return this.language;
   }

   public void setLanguage(String language) {
      this.language = language;
   }
}
