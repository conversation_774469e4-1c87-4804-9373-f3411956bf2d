package com.samsung.magicinfo.webauthor2.xml.dlkinfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ConvertTableMapType", propOrder = {"pageNumb", "elementName", "splitGroupId", "splitGroupName", "dataIndex", "convertTableName"})
public class ConvertTableMapType {
  @XmlElement(name = "page_no")
  private int pageNumb;
  
  @XmlElement(name = "element_name")
  private String elementName;
  
  @XmlElement(name = "split_group_id")
  private int splitGroupId;
  
  @XmlElement(name = "split_group_name")
  private String splitGroupName;
  
  @XmlElement(name = "data_index")
  private int dataIndex;
  
  @XmlElement(name = "convert_table_name")
  private String convertTableName;
  
  public ConvertTableMapType() {}
  
  public ConvertTableMapType(int pageNumb, String elementName, int splitGroupId, String splitGroupName, int dataIndex, String convertTableName) {
    this.pageNumb = pageNumb;
    this.elementName = elementName;
    this.splitGroupId = splitGroupId;
    this.splitGroupName = splitGroupName;
    this.dataIndex = dataIndex;
    this.convertTableName = convertTableName;
  }
  
  public int getPageNumb() {
    return this.pageNumb;
  }
  
  public void setPageNumb(int pageNumb) {
    this.pageNumb = pageNumb;
  }
  
  public String getElementName() {
    return this.elementName;
  }
  
  public void setElementName(String elementName) {
    this.elementName = elementName;
  }
  
  public int getSplitGroupId() {
    return this.splitGroupId;
  }
  
  public void setSplitGroupId(int splitGroupId) {
    this.splitGroupId = splitGroupId;
  }
  
  public String getSplitGroupName() {
    return this.splitGroupName;
  }
  
  public void setSplitGroupName(String splitGroupName) {
    this.splitGroupName = splitGroupName;
  }
  
  public int getDataIndex() {
    return this.dataIndex;
  }
  
  public void setDataIndex(int dataIndex) {
    this.dataIndex = dataIndex;
  }
  
  public String getConvertTableName() {
    return this.convertTableName;
  }
  
  public void setConvertTableName(String convertTableName) {
    this.convertTableName = convertTableName;
  }
}
