package com.samsung.magicinfo.rc.common.http;

public class RCResponseBody<T> {
  private T data;
  
  private String apiVersion;
  
  private String errorCode;
  
  private String errorMessage;
  
  private String status;
  
  public void setData(T data) {
    this.data = data;
  }
  
  public void setApiVersion(String apiVersion) {
    this.apiVersion = apiVersion;
  }
  
  public void setErrorCode(String errorCode) {
    this.errorCode = errorCode;
  }
  
  public void setErrorMessage(String errorMessage) {
    this.errorMessage = errorMessage;
  }
  
  public void setStatus(String status) {
    this.status = status;
  }
  
  RCResponseBody(T data, String apiVersion, String errorCode, String errorMessage, String status) {
    this.data = data;
    this.apiVersion = apiVersion;
    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
    this.status = status;
  }
  
  public static <T> RCResponseBodyBuilder<T> builder() {
    return new RCResponseBodyBuilder();
  }
  
  public T getData() {
    return this.data;
  }
  
  public String getApiVersion() {
    return this.apiVersion;
  }
  
  public String getErrorCode() {
    return this.errorCode;
  }
  
  public String getErrorMessage() {
    return this.errorMessage;
  }
  
  public String getStatus() {
    return this.status;
  }
}
