package com.samsungcms.kpimagicinfo.service;

import com.samsungcms.kpimagicinfo.service.Scheduler;
import com.samsungcms.kpimagicinfo.util.Utils;
import java.util.Date;
import java.util.concurrent.ScheduledFuture;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.Trigger;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Service;

@Service
public class SchedulerService {
  private static final Logger LOGGER = LogManager.getLogger(com.samsungcms.kpimagicinfo.service.SchedulerService.class);
  
  @Autowired
  TaskScheduler taskScheduler;
  
  ScheduledFuture<?> scheduledFuture;
  
  private String cron = "*/2 * * * * *";
  
  @Autowired
  Scheduler scheduler;
  
  @Autowired
  Utils utils;
  
  @Value("${debug.sendImmediately}")
  private boolean sendImmediately;
  
  public void changeCron(String cron) {
    try {
      if (this.scheduledFuture != null)
        this.scheduledFuture.cancel(true); 
      this.scheduledFuture = null;
      this.cron = cron;
      LOGGER.info(" [changeCron] " + this.cron);
      start();
    } catch (Exception e) {
      LOGGER.error(e);
    } 
  }
  
  public void start() {
    try {
      LOGGER.info(" [start]");
      ScheduledFuture<?> future = this.taskScheduler.schedule(run(), (Trigger)new CronTrigger(this.cron));
      this.scheduledFuture = future;
      if (this.sendImmediately) {
        LOGGER.info("sendImmediately");
        this.scheduler.getPolicy();
      } 
    } catch (Exception e) {
      LOGGER.error(e);
    } 
  }
  
  private Runnable run() {
    return () -> {
        Date today = new Date();
        LOGGER.info(today + " [run]");
        if (this.utils.getPrivacyPolicyEnable())
          this.scheduler.getPolicy(); 
      };
  }
}
