package com.samsung.magicinfo.restapi.playlist.model;

import com.samsung.magicinfo.framework.kpi.annotation.LogProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Timestamp;
import javax.validation.constraints.Size;

@ApiModel(
   description = "(eventTime) is based on server local time."
)
public class V2ContentTagLogResource {
   @ApiModelProperty(
      dataType = "long",
      value = "Log Id"
   )
   private Long logId = 0L;
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000",
      dataType = "string",
      value = "Id of specific content , gets the Id of the content."
   )
   private String contentId = null;
   @ApiModelProperty(
      dataType = "long",
      value = "Id of specific version , displays the version Id of the content."
   )
   private Long versionId = 0L;
   @LogProperty(
      valueType = "NAME"
   )
   @ApiModelProperty(
      dataType = "string",
      value = "Name of specific content , shows the name of the content."
   )
   private String contentName = null;
   @ApiModelProperty(
      dataType = "string",
      value = "Thumbnail file ID to reference when displaying thumbnails of content"
   )
   private String thumbFileId = null;
   @ApiModelProperty(
      dataType = "string",
      value = "Thumbnail file name to reference when displaying thumbnails of content"
   )
   private String thumbFileName = null;
   @ApiModelProperty(
      dataType = "timeStamp",
      value = "Event time value"
   )
   private Timestamp eventTime = null;
   @ApiModelProperty(
      dataType = "string",
      value = "Event type value"
   )
   private String eventType = null;
   @ApiModelProperty(
      example = "admin",
      value = "Creator of specific playlist, shows the author of a particular playlist.",
      dataType = "string"
   )
   @Size(
      max = 64,
      message = "[V2PlaylistResource][creatorId] max size is 64."
   )
   private String creatorId = null;
   @ApiModelProperty(
      dataType = "string",
      value = "Login user id"
   )
   private String userId = null;
   @ApiModelProperty(
      allowableValues = "LFD,IMAGE,MOVIE,OFFICE,PDF,FLASH,SOUND,DLK,FTP,CIFS,STRM,VWL,HTML,URL,SAPP,DLKT",
      example = "IMAGE",
      dataType = "string",
      value = "Media type of content , set media type of content"
   )
   private String mediaType = null;
   @ApiModelProperty(
      dataType = "long",
      value = "Organization ID of the content"
   )
   private long organizationId = 0L;
   @ApiModelProperty(
      dataType = "long",
      value = "ID of the tag"
   )
   private long tagId;
   @ApiModelProperty(
      dataType = "string",
      value = "IP address of the content"
   )
   private String ipAddress = null;
   @ApiModelProperty(
      dataType = "string",
      value = "Event Description"
   )
   private String eventDesc = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Tag name of the content"
   )
   private String tagName;

   public Long getLogId() {
      return this.logId;
   }

   public void setLogId(Long logId) {
      this.logId = logId;
   }

   public String getContentId() {
      return this.contentId;
   }

   public void setContentId(String contentId) {
      this.contentId = contentId;
   }

   public Long getVersionId() {
      return this.versionId;
   }

   public void setVersionId(Long versionId) {
      this.versionId = versionId;
   }

   public String getContentName() {
      return this.contentName;
   }

   public void setContentName(String contentName) {
      this.contentName = contentName;
   }

   public String getThumbFileId() {
      return this.thumbFileId;
   }

   public void setThumbFileId(String thumbFileId) {
      this.thumbFileId = thumbFileId;
   }

   public String getThumbFileName() {
      return this.thumbFileName;
   }

   public void setThumbFileName(String thumbFileName) {
      this.thumbFileName = thumbFileName;
   }

   public Timestamp getEventTime() {
      return this.eventTime;
   }

   public void setEventTime(Timestamp eventTime) {
      this.eventTime = eventTime;
   }

   public String getEventType() {
      return this.eventType;
   }

   public void setEventType(String eventType) {
      this.eventType = eventType;
   }

   public String getCreatorId() {
      return this.creatorId;
   }

   public void setCreatorId(String creatorId) {
      this.creatorId = creatorId;
   }

   public String getUserId() {
      return this.userId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }

   public String getMediaType() {
      return this.mediaType;
   }

   public void setMediaType(String mediaType) {
      this.mediaType = mediaType;
   }

   public long getOrganizationId() {
      return this.organizationId;
   }

   public void setOrganizationId(long organizationId) {
      this.organizationId = organizationId;
   }

   public long getTagId() {
      return this.tagId;
   }

   public void setTagId(long tagId) {
      this.tagId = tagId;
   }

   public String getIpAddress() {
      return this.ipAddress;
   }

   public void setIpAddress(String ipAddress) {
      this.ipAddress = ipAddress;
   }

   public String getEventDesc() {
      return this.eventDesc;
   }

   public void setEventDesc(String eventDesc) {
      this.eventDesc = eventDesc;
   }

   public String getTagName() {
      return this.tagName;
   }

   public void setTagName(String tagName) {
      this.tagName = tagName;
   }

   public V2ContentTagLogResource() {
      super();
   }
}
