package com.samsung.magicinfo.webauthor2.repository.model;

import com.samsung.magicinfo.webauthor2.repository.model.DatalinkServerEntityData;
import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;

public class DataLinkServerListResultListData implements Serializable {
  @XmlElement
  private Integer totalCount;
  
  @XmlElementWrapper(name = "resultList")
  @XmlElement(name = "DatalinkServerEntity")
  private List<DatalinkServerEntityData> resultList;
  
  public Integer getTotalCount() {
    return this.totalCount;
  }
  
  public List<DatalinkServerEntityData> getResultList() {
    return this.resultList;
  }
}
