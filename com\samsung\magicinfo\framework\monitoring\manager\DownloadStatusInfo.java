package com.samsung.magicinfo.framework.monitoring.manager;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;

public interface DownloadStatusInfo {
   int PUBLISHED = 0;
   int PUBLISHING = 1;
   int WAITING = 2;
   int FAILED = 3;
   int NODEVICE = 4;
   String PERCENT_100 = "100 %";
   String DEVICE_COUNT_C = "deviceCount";
   String SUCCESS = "SUCCESS";
   String COMPLETE_COUNT_C = "completeCount";
   String FAIL_COUNT_C = "failCount";

   void deleteDownloadStatusWithoutContentIds(String var1, List var2) throws SQLException;

   void deleteDownloadStatus(String var1) throws SQLException;

   void deleteDownloadStatus(String var1, SqlSession var2) throws SQLException;

   void deleteDownloadStatus(String var1, String var2) throws SQLException;

   void initDownloadStatus(String var1, List var2, List var3, boolean var4, String var5, String var6) throws SQLException;

   Object[] getDownloadStatusListByProgramId(String var1) throws SQLException;

   Object[] getDownloadStatusListByProgramId(String var1, int var2) throws SQLException;

   List getProgressInfoByDeviceId(String var1, List var2) throws SQLException;

   int getCntDownloadStatusByDeviceId(String var1, String var2) throws SQLException;

   int updateScheduleDeployStatusByDeviceId(String var1, String var2) throws SQLException;

   void deleteScheduleDeployStatusByDeviceId(String var1) throws SQLException;

   void deleteDownloadStatusByProgramId(String var1) throws SQLException;

   String getDownloadProgress(String var1, String var2, String var3) throws SQLException;

   void addDownloadStatus(String var1, List var2, List var3) throws SQLException;

   void updateProgressByDeviceIdsAndContentId(String var1, String var2, String var3) throws SQLException;

   void deleteDownloadStatusInCache(String var1);

   Map getContentDownloadsByDeviceId(String var1) throws SQLException;

   void updateContentDownloadsProgressByDeviceId(String var1, Map var2);

   void addScheduleDeployStatusWithDevices(String var1, List var2, String var3) throws SQLException;

   int getContentScheduleStatus(String var1);

   void updateScheduleDeployStatusWithDevices(String var1, List var2, String var3) throws SQLException;

   void addScheduleDeployStatus(String var1, List var2) throws SQLException;

   void deleteDownloadStatusInCache(String var1, List var2);

   void updateContentDownloadsProgressByDeviceIdAndContentIds(String var1, List var2, String var3);

   List getDownloadStatusesByProgramId(String var1) throws SQLException;

   void deleteDownloadStatusByDeviceId(String var1) throws SQLException;

   String getStatusByProgramIdAndDeviceId(String var1, String var2) throws SQLException;
}
