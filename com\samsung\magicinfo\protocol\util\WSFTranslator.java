package com.samsung.magicinfo.protocol.util;

import com.samsung.common.exception.BasicException;
import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DeviceLogger;
import com.samsung.magicinfo.protocol.constants.OperationConstants;
import com.samsung.magicinfo.protocol.context.LogicalMessage;
import com.samsung.magicinfo.protocol.context.LogicalMessageContext;
import com.samsung.magicinfo.protocol.entity.DownloadFile;
import com.samsung.magicinfo.protocol.entity.MOMsg;
import com.samsung.magicinfo.protocol.entity.WSAddressInfo;
import com.samsung.magicinfo.protocol.processor.client.ClientProcessor;
import com.samsung.magicinfo.protocol.processor.client.ClientProcessorFactory;
import com.samsung.magicinfo.protocol.processor.service.ServiceProcessor;
import com.samsung.magicinfo.protocol.processor.service.ServiceProcessorFactory;
import java.util.ArrayList;
import org.apache.logging.log4j.Logger;
import org.apache.xmlbeans.XmlObject;

public class WSFTranslator {
   Logger logger = LoggingManagerV2.getLogger(WSFTranslator.class);

   public WSFTranslator() {
      super();
   }

   public MOMsg convertContextToMOMsg(LogicalMessageContext logicalContext, String routine) throws BasicException {
      MOMsg moMsg = new MOMsg();
      ArrayList attachmentList = (ArrayList)logicalContext.getProperty("WS_MIME_PROPERTY");
      WSAddressInfo addressInfo = (WSAddressInfo)logicalContext.getProperty("WS_ADDRESSINFO_PROPERTY");
      XmlObject bodyObj = logicalContext.getLogicalMessage().getMessage();
      XmlObject obj = null;

      try {
         if ("CLIENT".equals(routine)) {
            String action = addressInfo.getAction();
            if (!OperationConstants.ACTION_LIST[11].equals(action)) {
               ClientProcessor clientProcessor = ClientProcessorFactory.getInstance().newProcessor(addressInfo.getAction());
               obj = clientProcessor.processOperation(bodyObj);
            }
         } else {
            ServiceProcessor serviceProcessor = ServiceProcessorFactory.getInstance().newProcessor(addressInfo.getAction());
            obj = serviceProcessor.processOperation(bodyObj);
         }
      } catch (Exception var10) {
         throw new BasicException(ExceptionCode.SRM600[0], ExceptionCode.SRM600[1], ExceptionCode.SRM600[2]);
      }

      if (addressInfo.getAction().equals(OperationConstants.ACTION_LIST[20])) {
         this.logger.debug("Fault SET");
         moMsg.setType(2);
         moMsg.setFaultXML(obj);
      } else {
         moMsg.setType(1);
         moMsg.setOperationXML(obj);
      }

      if (attachmentList != null && attachmentList.size() > 0) {
         moMsg.setDownloadFile((DownloadFile)attachmentList.get(0));
      }

      moMsg.setWsAddressInfo(addressInfo);
      if (DeviceLogger.isDeviceLoggerOn(addressInfo.getDeviceSN())) {
         Logger d_logger = DeviceLogger.getDeviceLogger(addressInfo.getDeviceSN());
         d_logger.info("[MagicInfo_MO][" + addressInfo.getDeviceSN() + "][DeviceLogger:Recv] = " + moMsg.toString());
      } else {
         this.logger.info("[MagicInfo_MO][" + addressInfo.getDeviceSN() + "][SRM:Recv] MOMsg = " + moMsg.toString());
      }

      return moMsg;
   }

   public LogicalMessageContext convertMOMsgToContext(MOMsg moMsg) {
      WSAddressInfo addressInfo = moMsg.getWsAddressInfo();
      if (DeviceLogger.isDeviceLoggerOn(addressInfo.getDeviceSN())) {
         if (moMsg.getWsAddressInfo() != null) {
            Logger d_logger = DeviceLogger.getDeviceLogger(addressInfo.getDeviceSN());
            d_logger.info("[MagicInfo_MO][" + addressInfo.getDeviceSN() + "][DeviceLogger:Send] = " + moMsg.toString());
         } else {
            this.logger.info("[MagicInfo_MO] addressInfo is null");
         }
      } else {
         this.logger.info("[MagicInfo_MO][" + addressInfo.getDeviceSN() + "][SRM:Send] MOMsg = " + moMsg.toString());
      }

      LogicalMessageContext logicalContext = new LogicalMessageContext();
      if (moMsg.getDownloadFile() != null) {
         ArrayList attachmentList = new ArrayList();
         attachmentList.add(moMsg.getDownloadFile());
         logicalContext.setProperty("WS_MIME_PROPERTY", attachmentList);
      }

      if (moMsg.getWsAddressInfo() != null) {
         logicalContext.setProperty("WS_ADDRESSINFO_PROPERTY", moMsg.getWsAddressInfo());
      }

      if (moMsg.getOperationXML() != null) {
         logicalContext.setLogicalMessage(new LogicalMessage(moMsg.getOperationXML()));
      } else if (moMsg.getFaultXML() != null) {
         logicalContext.setLogicalMessage(new LogicalMessage(moMsg.getFaultXML()));
      }

      return logicalContext;
   }
}
