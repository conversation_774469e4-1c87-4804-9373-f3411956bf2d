package com.samsung.magicinfo.ums.service;

import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.RoleUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceMenuManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceMenuManagerImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.ums.model.DashboardUserResource;
import com.samsung.magicinfo.ums.model.UserFilter;
import com.samsung.magicinfo.ums.model.UserOrganResource;
import com.samsung.magicinfo.ums.model.UserResource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.apache.ftpserver.ftplet.UserManager;
import org.apache.ftpserver.usermanager.DbUserManagerFactory;
import org.apache.ftpserver.usermanager.impl.BaseUser;
import org.apache.logging.log4j.Logger;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("UserService")
@Transactional
public class UserServiceImpl implements UserService {
   protected Logger logger = LoggingManagerV2.getLogger(UserServiceImpl.class);
   public static final String ROOT = "ROOT";
   public static final String ADMINISTRATORS = "Administrators";
   public static final String YES = "Y";
   public static final String NO = "N";
   public static final String USER_DASHBOARD = "userInfoDashboard";
   public static final String ORGANIZATION = "organization";
   public static final String TOTAL_IN_COUNT = "totalInCount";
   public static final String TOTAL_OUT_COUNT = "totalOutCount";
   public static final String UNAPPROVED_COUNT = "unapprovedCount";
   public static final String ORGANIZATION_LIST = "organizationList";
   protected final ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
   Locale locale = null;

   public UserServiceImpl() {
      super();
   }

   public ResponseBody listMyInfo() {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      UserResource userResource = new UserResource();
      User user = SecurityUtils.getLoginUser();
      String userId = SecurityUtils.getLoginUserId();
      String userName = user.getUser_name();
      String team = user.getTeam();
      String position = user.getJob_position();
      String phone = user.getPhone_num();
      String mobile = user.getMobile_num();
      String email = user.getEmail();
      String organization = user.getOrganization();
      String group = user.getGroup_name();
      String role = user.getRole_name();
      if (organization.equals("ROOT")) {
         organization = "Administrators";
      }

      userResource.setUserId(userId);
      userResource.setUserName(userName);
      userResource.setEmail(email);
      userResource.setMobile(mobile);
      userResource.setPhone(phone);
      userResource.setOrganization(organization);
      userResource.setGroupName(group);
      userResource.setRole(role);
      userResource.setTeam(team);
      userResource.setPosition(position);
      responseBody.setItems(userResource);
      responseBody.setStatus("Success");
      return responseBody;
   }

   public ResponseBody updateMyInfo(UserFilter filter) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      UserResource userResource = new UserResource();
      UserInfo userInfo = UserInfoImpl.getInstance();
      User user = SecurityUtils.getLoginUser();
      if (filter.getPassword().equals("")) {
         return null;
      } else if (filter.getConfirmPassword().equals("")) {
         return null;
      } else if (filter.getPassword().length() >= 8 && filter.getPassword().length() <= 50) {
         if (!filter.getPassword().equals(filter.getConfirmPassword())) {
            return null;
         } else {
            user.setUser_name(filter.getUserName());
            user.setTeam(filter.getTeam());
            user.setJob_position(filter.getPosition());
            user.setPhone_num(filter.getPhone());
            user.setMobile_num(filter.getMobile());
            user.setEmail(filter.getEmail());
            user.setPassword(filter.getPassword());
            user.setIs_reset_pwd("N");

            try {
               userInfo.setUser(user);
               BaseUser ftpUser = new BaseUser();
               ftpUser.setName(user.getUser_id());
               ftpUser.setPassword(filter.getPassword());
               UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
               userMgr.save(ftpUser);
               userResource.setUserId(user.getUser_id());
               userResource.setUserName(user.getUser_name());
               userResource.setEmail(user.getEmail());
               userResource.setMobile(user.getMobile_num());
               userResource.setPhone(user.getPhone_num());
               userResource.setOrganization(user.getOrganization());
               userResource.setGroupName(user.getGroup_name());
               userResource.setRole(user.getRole_name());
               userResource.setTeam(user.getTeam());
               userResource.setPosition(user.getJob_position());
               responseBody.setItems(userResource);
               responseBody.setStatus("Success");
            } catch (Exception var8) {
               this.logger.error("", var8);
               responseBody.setErrorMessage(var8.getMessage());
               responseBody.setStatus("Fail");
            }

            return responseBody;
         }
      } else {
         return null;
      }
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody listDashboardUserInfo() {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      Map map = new HashMap();
      DashboardUserResource data = new DashboardUserResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String user = userContainer.getUser().getOrganization();

      try {
         map.put("organization", user);
         UserInfo userInfo = UserInfoImpl.getInstance();
         int totalInCount = userInfo.getCountAllUser(map);
         int totalOutCount = userInfo.getCountAllWithdrawalUser(map);
         data.setTotalInCount(totalInCount);
         data.setTotalOutCount(totalOutCount);
         if (SecurityUtils.checkUserApprovalPermission()) {
            int nonApprovalCount = userInfo.getCountAllNonApprovedUser(map);
            data.setUnapprovedCount(nonApprovalCount);
         }

         responseBody.setItems(data);
         responseBody.setStatus("Success");
      } catch (Exception var10) {
         this.logger.error("", var10);
         responseBody.setErrorMessage(var10.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody createUser(UserFilter filter, boolean signUpPage) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      UserResource userResource = new UserResource();
      User user = SecurityUtils.getLoginUser();
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      PlaylistInfo playlintInfo = PlaylistInfoImpl.getInstance();
      DeviceMenuManager menuDao = DeviceMenuManagerImpl.getInstance();
      BaseUser ftpUser = new BaseUser();
      UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
      ftpUser.setName(filter.getUserId());
      ftpUser.setPassword(filter.getPassword());
      user.setUser_id(filter.getUserId());
      user.setPassword(filter.getPassword());
      user.setUser_name(filter.getUserName());
      user.setEmail(filter.getEmail());
      user.setMobile_num(filter.getMobile());
      user.setPhone_num(filter.getPhone());
      user.setOrganization(filter.getOrganization());
      user.setGroup_name(filter.getGroupName());
      user.setRole_name(filter.getRole());
      user.setTeam(filter.getTeam());
      user.setJob_position(filter.getPosition());
      user.setIs_deleted("N");

      try {
         Long rootGroupID = userGroupInfo.getOrgGroupIdByName(filter.getOrganization());
         if (-1L == rootGroupID) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"Organization"});
         }

         user.setRoot_group_id(rootGroupID);
         if (signUpPage) {
            user.setIs_approved("N");
            userInfo.addUser(user, false);
         } else {
            user.setIs_approved("Y");
            userInfo.addUser(user, true);
            userMgr.save(ftpUser);
            contentInfo.addDefaultGroup(filter.getUserId());
            playlintInfo.addDefaultGroup(filter.getUserId());
            menuDao.addDeviceMapMenuUser(filter.getUserId());
         }

         userResource.setUserId(user.getUser_id());
         userResource.setUserName(user.getUser_name());
         userResource.setEmail(user.getEmail());
         userResource.setMobile(user.getMobile_num());
         userResource.setPhone(user.getPhone_num());
         userResource.setOrganization(user.getOrganization());
         userResource.setGroupName(user.getGroup_name());
         userResource.setRole(user.getRole_name());
         userResource.setTeam(user.getTeam());
         userResource.setPosition(user.getJob_position());
         responseBody.setItems(userResource);
         responseBody.setStatus("Success");
      } catch (RestServiceException var14) {
         throw var14;
      } catch (Exception var15) {
         this.logger.error("", var15);
         responseBody.setErrorMessage(var15.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public ResponseBody checkDuplicatedUser(String userId) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      UserInfo userInfo = UserInfoImpl.getInstance();
      String ldapFullId = "";

      try {
         if (ldapFullId != null && !ldapFullId.equals("")) {
            if (userInfo.getCountByUserIdForCheck(userId) != 0 && userInfo.getCountByLDAPUserFullIdForCheck(ldapFullId) == 0) {
            }
         } else if (userInfo.getCountByUserIdForCheck(userId) == 0) {
            responseBody.setStatus("Success");
            return responseBody;
         }
      } catch (Exception var6) {
         this.logger.error("", var6);
      }

      responseBody.setStatus("Fail");
      responseBody.setErrorCode(ExceptionCode.RES903[0]);
      responseBody.setErrorMessage(ExceptionCode.RES903[2]);
      return responseBody;
   }

   public ResponseBody listUserOrgan() {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      UserOrganResource result = new UserOrganResource();
      UserInfo userInfo = UserInfoImpl.getInstance();
      ArrayList organizationList = new ArrayList();

      try {
         List list = userInfo.getOrganization();
         UserContainer userContainer = SecurityUtils.getUserContainer();
         if (RoleUtils.isServerAdminRole(userContainer.getUser())) {
            for(int i = 0; i < list.size(); ++i) {
               Map data = new HashMap();
               data.put("groupId", ((Map)list.get(i)).get("group_id"));
               data.put("groupName", ((Map)list.get(i)).get("group_name"));
               organizationList.add(data);
            }
         }

         result.setORGANIZATION_LIST(organizationList);
         responseBody.setItems(result);
         responseBody.setStatus("Success");
      } catch (Exception var9) {
         this.logger.error("", var9);
         responseBody.setErrorMessage(var9.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }
}
