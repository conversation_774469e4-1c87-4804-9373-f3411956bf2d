package com.samsung.magicinfo.rc.common.http;

import com.samsung.magicinfo.rc.common.http.RCResponseBody;

public class RCResponseBodyBuilder<T> {
  private T data;
  
  private String apiVersion;
  
  private String errorCode;
  
  private String errorMessage;
  
  private String status;
  
  public RCResponseBodyBuilder<T> data(T data) {
    this.data = data;
    return this;
  }
  
  public RCResponseBodyBuilder<T> apiVersion(String apiVersion) {
    this.apiVersion = apiVersion;
    return this;
  }
  
  public RCResponseBodyBuilder<T> errorCode(String errorCode) {
    this.errorCode = errorCode;
    return this;
  }
  
  public RCResponseBodyBuilder<T> errorMessage(String errorMessage) {
    this.errorMessage = errorMessage;
    return this;
  }
  
  public RCResponseBodyBuilder<T> status(String status) {
    this.status = status;
    return this;
  }
  
  public RCResponseBody<T> build() {
    return new RCResponseBody(this.data, this.apiVersion, this.errorCode, this.errorMessage, this.status);
  }
  
  public String toString() {
    return "RCResponseBody.RCResponseBodyBuilder(data=" + this.data + ", apiVersion=" + this.apiVersion + ", errorCode=" + this.errorCode + ", errorMessage=" + this.errorMessage + ", status=" + this.status + ")";
  }
}
