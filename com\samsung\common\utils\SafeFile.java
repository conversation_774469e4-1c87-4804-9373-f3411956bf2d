package com.samsung.common.utils;

import java.io.File;
import java.net.URI;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.owasp.esapi.errors.ValidationException;

public class SafeFile extends File {
   private static final long serialVersionUID = 2814546811656171330L;
   private static final Pattern PERCENTS_PAT = Pattern.compile("(%)([0-9a-fA-F])([0-9a-fA-F])");
   private static final Pattern FILE_BLACKLIST_PAT = Pattern.compile("([\\\\/:*?<>|])");
   private static final Pattern DIR_BLACKLIST_PAT = Pattern.compile("([*?<>|])");

   public SafeFile(String path) throws ValidationException {
      super(path);
      this.doDirCheck(this.getParent());
      this.doFileCheck(this.getName());
   }

   public SafeFile(String parent, String child) throws ValidationException {
      super(parent, child);
      this.doDirCheck(this.getParent());
      this.doFileCheck(this.getName());
   }

   public SafeFile(File parent, String child) throws ValidationException {
      super(parent, child);
      this.doDirCheck(this.getParent());
      this.doFileCheck(this.getName());
   }

   public SafeFile(URI uri) throws ValidationException {
      super(uri);
      this.doDirCheck(this.getParent());
      this.doFileCheck(this.getName());
   }

   private void doDirCheck(String path) throws ValidationException {
      Matcher m1 = DIR_BLACKLIST_PAT.matcher(path);
      if (m1.find()) {
         throw new ValidationException("Invalid directory", "Directory path (" + path + ") contains illegal character: " + m1.group());
      } else {
         Matcher m2 = PERCENTS_PAT.matcher(path);
         if (m2.find()) {
            throw new ValidationException("Invalid directory", "Directory path (" + path + ") contains encoded characters: " + m2.group());
         } else {
            int ch = this.containsUnprintableCharacters(path);
            if (ch != -1) {
               throw new ValidationException("Invalid directory", "Directory path (" + path + ") contains unprintable character: " + ch);
            }
         }
      }
   }

   private void doFileCheck(String path) throws ValidationException {
      Matcher m1 = FILE_BLACKLIST_PAT.matcher(path);
      if (m1.find()) {
         throw new ValidationException("Invalid directory", "Directory path (" + path + ") contains illegal character: " + m1.group());
      } else {
         Matcher m2 = PERCENTS_PAT.matcher(path);
         if (m2.find()) {
            throw new ValidationException("Invalid file", "File path (" + path + ") contains encoded characters: " + m2.group());
         } else {
            int ch = this.containsUnprintableCharacters(path);
            if (ch != -1) {
               throw new ValidationException("Invalid file", "File path (" + path + ") contains unprintable character: " + ch);
            }
         }
      }
   }

   private int containsUnprintableCharacters(String s) {
      for(int i = 0; i < s.length(); ++i) {
         char ch = s.charAt(i);
         if (ch < ' ') {
            return ch;
         }
      }

      return -1;
   }
}
