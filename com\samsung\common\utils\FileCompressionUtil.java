package com.samsung.common.utils;

import com.samsung.common.logger.LoggingManagerV2;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.zip.CRC32;
import java.util.zip.CheckedInputStream;
import java.util.zip.CheckedOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;
import org.apache.logging.log4j.Logger;

public class FileCompressionUtil {
   static Logger logger = LoggingManagerV2.getLogger(FileCompressionUtil.class);
   private final String PATH_SEP = "\\";
   public final int BUFFER = 2048;
   static final int BUFFER_512 = 512;
   static final long TOOBIG = 104857600L;
   static final int TOOMANY = 1024;

   public FileCompressionUtil() {
      super();
   }

   public void zipFilesInPath(String zipFileName, String filePath) throws IOException {
      FileOutputStream dest = new FileOutputStream(zipFileName);
      ZipOutputStream out = new ZipOutputStream(new BufferedOutputStream(dest));

      try {
         byte[] data = new byte[2048];
         File folder = new File(filePath);
         List files = Arrays.asList(folder.list());
         Iterator var8 = files.iterator();

         while(var8.hasNext()) {
            String file = (String)var8.next();
            FileInputStream fi = null;
            BufferedInputStream origin = null;

            try {
               fi = new FileInputStream(filePath + "\\" + file);
               origin = new BufferedInputStream(fi, 2048);
               out.putNextEntry(new ZipEntry(file));

               int count;
               while((count = origin.read(data, 0, 2048)) != -1) {
                  out.write(data, 0, count);
               }
            } catch (IOException var21) {
               logger.error(var21);
            } finally {
               if (origin != null) {
                  origin.close();
               }

               if (fi != null) {
                  fi.close();
               }

            }
         }
      } finally {
         out.close();
         dest.close();
      }

   }

   public void zipFileToFile(String zipFileName, String sourceFileName) throws IOException {
      FileOutputStream dest = new FileOutputStream(zipFileName);
      ZipOutputStream out = new ZipOutputStream(new BufferedOutputStream(dest));

      try {
         byte[] data = new byte[2048];
         FileInputStream fi = null;
         BufferedInputStream origin = null;

         try {
            fi = new FileInputStream(sourceFileName);
            origin = new BufferedInputStream(fi, 2048);
            out.putNextEntry(new ZipEntry(sourceFileName));

            int count;
            while((count = origin.read(data, 0, 2048)) != -1) {
               out.write(data, 0, count);
            }
         } catch (IOException var17) {
            logger.error(var17);
         } finally {
            if (origin != null) {
               origin.close();
            }

            if (fi != null) {
               fi.close();
            }

         }
      } finally {
         out.close();
         dest.close();
      }

   }

   public long zipFilesInPathWithChecksum(String zipFileName, String folderPath) throws IOException {
      FileOutputStream dest = new FileOutputStream(zipFileName);
      CheckedOutputStream checkStream = new CheckedOutputStream(dest, new CRC32());
      ZipOutputStream out = new ZipOutputStream(new BufferedOutputStream(checkStream));

      try {
         byte[] data = new byte[2048];
         File folder = new File(folderPath);
         List files = Arrays.asList(folder.list());
         Iterator var9 = files.iterator();

         while(var9.hasNext()) {
            String file = (String)var9.next();
            FileInputStream fi = null;
            BufferedInputStream origin = null;

            try {
               fi = new FileInputStream(folderPath + "\\" + file);
               origin = new BufferedInputStream(fi, 2048);
               out.putNextEntry(new ZipEntry(file));

               int count;
               while((count = origin.read(data, 0, 2048)) != -1) {
                  out.write(data, 0, count);
               }
            } catch (IOException var22) {
               logger.error(var22);
            } finally {
               if (fi != null) {
                  fi.close();
               }

               if (origin != null) {
                  origin.close();
               }

            }
         }
      } finally {
         out.close();
         checkStream.close();
         dest.flush();
         dest.close();
      }

      return checkStream.getChecksum().getValue();
   }

   public final void unzipFilesToPath(String zipFileName, String fileExtractPath) throws IOException {
      int entries = 0;
      long total = 0L;
      FileInputStream fis = new FileInputStream(zipFileName);
      Throwable var8 = null;

      try {
         ZipInputStream zis = new ZipInputStream(new BufferedInputStream(fis));
         Throwable var10 = null;

         try {
            ZipEntry entry;
            try {
               while((entry = zis.getNextEntry()) != null) {
                  if (this.isWriteZipEntry(entry, new File(fileExtractPath))) {
                     logger.info("Extracting: " + entry);
                     byte[] data = new byte[512];
                     String name = this.validateFilename(entry.getName(), ".");
                     if (entry.isDirectory()) {
                        logger.info("Creating directory " + name);
                        (new File(name)).mkdir();
                     } else {
                        FileOutputStream fos = new FileOutputStream(fileExtractPath + "\\" + entry.getName());

                        int count;
                        BufferedOutputStream dest;
                        for(dest = new BufferedOutputStream(fos, 512); total + 512L <= 104857600L && (count = zis.read(data, 0, 512)) != -1; total += (long)count) {
                           dest.write(data, 0, count);
                        }

                        dest.flush();
                        dest.close();
                        zis.closeEntry();
                        ++entries;
                        if (entries > 1024) {
                           throw new IllegalStateException("Too many files to unzip.");
                        }

                        if (total + 512L > 104857600L) {
                           throw new IllegalStateException("File being unzipped is too big.");
                        }
                     }
                  }
               }
            } catch (Throwable var37) {
               var10 = var37;
               throw var37;
            }
         } finally {
            if (zis != null) {
               if (var10 != null) {
                  try {
                     zis.close();
                  } catch (Throwable var36) {
                     var10.addSuppressed(var36);
                  }
               } else {
                  zis.close();
               }
            }

         }
      } catch (Throwable var39) {
         var8 = var39;
         throw var39;
      } finally {
         if (fis != null) {
            if (var8 != null) {
               try {
                  fis.close();
               } catch (Throwable var35) {
                  var8.addSuppressed(var35);
               }
            } else {
               fis.close();
            }
         }

      }

   }

   private String validateFilename(String filename, String intendedDir) throws IOException {
      File f = new File(filename);
      String canonicalPath = f.getCanonicalPath();
      File iD = new File(intendedDir);
      String canonicalID = iD.getCanonicalPath();
      if (canonicalPath.startsWith(canonicalID)) {
         return canonicalPath;
      } else {
         throw new IllegalStateException("File is outside extraction target directory.");
      }
   }

   private boolean isWriteZipEntry(ZipEntry entry, File destinationDir) {
      File file = new File(destinationDir, entry.getName());
      return file.toPath().normalize().startsWith(destinationDir.toPath());
   }

   public boolean unzipFilesToPathWithChecksum(String zipFileName, String fileExtractPath, long checksum) throws IOException {
      boolean checksumMatches = false;
      FileInputStream fis = new FileInputStream(zipFileName);
      CheckedInputStream checkStream = new CheckedInputStream(fis, new CRC32());
      ZipInputStream zis = new ZipInputStream(new BufferedInputStream(checkStream));

      try {
         ZipEntry entry = null;

         while((entry = zis.getNextEntry()) != null) {
            byte[] data = new byte[2048];
            FileOutputStream fos = null;
            BufferedOutputStream dest = null;

            try {
               fos = new FileOutputStream(fileExtractPath + "\\" + entry.getName());
               dest = new BufferedOutputStream(fos, 2048);

               int count;
               while((count = zis.read(data, 0, 2048)) != -1) {
                  dest.write(data, 0, count);
               }
            } catch (IOException var23) {
               logger.error(var23);
            } finally {
               if (dest != null) {
                  dest.flush();
                  dest.close();
               }

               if (fos != null) {
                  fos.close();
               }

            }
         }
      } finally {
         zis.close();
         fis.close();
         checkStream.close();
      }

      if (checkStream.getChecksum().getValue() == checksum) {
         checksumMatches = true;
      }

      return checksumMatches;
   }

   public File compress(File file) {
      String fileName = file.getAbsolutePath();
      int Idx = fileName.lastIndexOf(".");
      String zipFile = fileName;

      try {
         zipFile = fileName.substring(0, Idx);
      } catch (StringIndexOutOfBoundsException var11) {
      } finally {
         zipFile = zipFile + ".zip";
      }

      try {
         this.zip(file.getAbsolutePath(), zipFile);
      } catch (Throwable var10) {
         logger.error("", var10);
      }

      return new File(file.getParent() + "/" + zipFile);
   }

   public void compressFileToFile(File file) {
      String fileName = file.getAbsolutePath();
      int Idx = fileName.lastIndexOf(".");
      String zipFile = fileName;

      try {
         zipFile = fileName.substring(0, Idx);
      } catch (StringIndexOutOfBoundsException var11) {
      } finally {
         zipFile = zipFile + ".zip";
      }

      try {
         this.zip(file.getAbsolutePath(), zipFile);
      } catch (Throwable var10) {
         logger.error("", var10);
      }

   }

   private void zip(String inputFolder, String targetZippedFolder) throws IOException {
      FileOutputStream fileOutputStream = null;
      ZipOutputStream zipOutputStream = null;
      File inputFile = null;

      try {
         fileOutputStream = new FileOutputStream(targetZippedFolder);
         zipOutputStream = new ZipOutputStream(fileOutputStream);
         inputFile = new File(inputFolder);
         if (inputFile.isFile()) {
            this.zipFile(inputFile, "", zipOutputStream);
         } else if (inputFile.isDirectory()) {
            this.zipFolder(zipOutputStream, inputFile, "");
         }
      } catch (IOException var10) {
         logger.error(var10);
      } finally {
         if (zipOutputStream != null) {
            zipOutputStream.close();
         }

         if (fileOutputStream != null) {
            fileOutputStream.close();
         }

      }

   }

   private void zipFolder(ZipOutputStream zipOutputStream, File inputFolder, String parentName) throws IOException {
      String myname = parentName + inputFolder.getName() + "\\";
      ZipEntry folderZipEntry = new ZipEntry(myname);
      zipOutputStream.putNextEntry(folderZipEntry);
      File[] contents = inputFolder.listFiles();
      File[] var7 = contents;
      int var8 = contents.length;

      for(int var9 = 0; var9 < var8; ++var9) {
         File f = var7[var9];
         if (f.isFile()) {
            this.zipFile(f, myname, zipOutputStream);
         } else if (f.isDirectory()) {
            this.zipFolder(zipOutputStream, f, myname);
         }
      }

      zipOutputStream.closeEntry();
   }

   private void zipFile(File inputFile, String parentName, ZipOutputStream zipOutputStream) throws IOException {
      ZipEntry zipEntry = new ZipEntry(parentName + inputFile.getName());
      zipOutputStream.putNextEntry(zipEntry);
      FileInputStream fileInputStream = null;

      try {
         fileInputStream = new FileInputStream(inputFile);
         byte[] buf = new byte[1024000];

         int bytesRead;
         while((bytesRead = fileInputStream.read(buf)) > 0) {
            zipOutputStream.write(buf, 0, bytesRead);
         }
      } catch (IOException var11) {
         logger.error(var11);
      } finally {
         if (fileInputStream != null) {
            fileInputStream.close();
         }

      }

      zipOutputStream.closeEntry();
   }
}
