<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.samsung.magicinfo.framework.user.dao.UserDaoMapper">

    <!-- [2019.10 RC] KSY : IS_RESET_PWD 컬럼 추가 -->
    <insert id="addUser">
        INSERT INTO MI_USER_INFO_USER (USER_ID, USER_NAME, PASSWORD, EMAIL, ORGANIZATION, TEAM, JOB_POSITION,
        PHONE_NUM, MOBILE_NUM, CREATE_DATE, LAST_LOGIN_DATE, MODIFY_DATE,
        IS_APPROVED, IS_DELETED, ROOT_GROUP_ID, OS_TYPE, SERIAL_NUM, USING_MOBILE,
        IS_REJECT, REJECT_REASON, LDAP_INFO, LDAP_USER_ID, IS_FIRST_LOGIN, IS_RESET_PWD)
        VALUES (#{user.user_id}, #{user.user_name}, #{user.password}, #{user.email}, #{user.organization},
        #{user.team}, #{user.job_position}, #{user.phone_num}, #{user.mobile_num},
        <include refid="utils.currentTimestamp"/>,<include refid="utils.currentTimestamp"/>,
        <include refid="utils.currentTimestamp"/>, #{user.is_approved}, #{user.is_deleted}, #{user.root_group_id},
        #{user.os_type}, #{user.serial_num}, #{user.using_mobile}, 'N', null, #{ldap_info}, #{user.ldap_user_id}, <include refid="utils.true"/> , #{user.is_reset_pwd} )
    </insert>


    <select resultType="com.samsung.magicinfo.auth.security.otp.UserAuthDevice" id="getUserAuthDevice">
        SELECT
            *
        FROM MI_USER_MAP_MFA_DEVICE
        WHERE USER_ID = #{userId}
    </select>


    <select resultType="com.samsung.magicinfo.auth.security.otp.UserAuthDevice" id="getUserStoredDevice">
        SELECT
            AUTH_DEVICE_ID, OS_NAME, OS_VERSION, BROWSER_NAME, BROWSER_VERSION, LATEST_DATE
        FROM MI_USER_MAP_MFA_DEVICE
        WHERE USER_ID = #{userId}
    </select>


    <insert id="addAuthDeviceInfo">
        INSERT INTO MI_USER_MAP_MFA_DEVICE (AUTH_DEVICE_ID, USER_ID, OS_NAME, OS_VERSION, BROWSER_NAME, BROWSER_VERSION, CREATE_DATE, EXPIRED_DATE, AUTH_DEVICE_NAME, SECRET_KEY, LATEST_DATE)
        VALUES (#{userAuthDevice.auth_device_id}, #{userAuthDevice.user_id}, #{userAuthDevice.os_name},  #{userAuthDevice.os_version}, #{userAuthDevice.browser_name}, #{userAuthDevice.browser_version}, <include refid="utils.currentTimestamp"/>, #{userAuthDevice.expired_date}, #{userAuthDevice.auth_device_name}, #{userAuthDevice.secret_key}, <include refid="utils.currentTimestamp"/>)
    </insert>

    <delete id="deleteUserDevice">
        DELETE FROM MI_USER_MAP_MFA_DEVICE
        WHERE AUTH_DEVICE_ID = #{authDeviceId}
    </delete>


    <delete id="deleteUserDeviceByUserId">
        DELETE FROM MI_USER_MAP_MFA_DEVICE
        WHERE USER_ID = #{userId}
    </delete>


    <delete id="deleteExpiredUserDevice">
        DELETE FROM MI_USER_MAP_MFA_DEVICE
        WHERE EXPIRED_DATE &lt; <include refid="utils.currentTimestamp"/>
    </delete>

    <update id="updateUserDeviceExpiredDate">
        <choose>
            <when test="day == 0">
                UPDATE MI_USER_MAP_MFA_DEVICE SET EXPIRED_DATE = CREATE_DATE + INTERVAL '#{day} day'
            </when>
            <otherwise>
                UPDATE MI_USER_MAP_MFA_DEVICE SET EXPIRED_DATE = NULL
            </otherwise>
        </choose>
    </update>

    <update id="updateUserDeviceExpiredDate" databaseId="mssql">
        <choose>
            <when test="day == 0">
                UPDATE MI_USER_MAP_MFA_DEVICE SET EXPIRED_DATE = DATEADD(dd, #{day}, CREATE_DATE)
            </when>
            <otherwise>
                UPDATE MI_USER_MAP_MFA_DEVICE SET EXPIRED_DATE = NULL
            </otherwise>
        </choose>
    </update>


    <update id="updateUserDeviceByUserId">
        UPDATE MI_USER_MAP_MFA_DEVICE
        SET LATEST_DATE =  <include refid="utils.currentTimestamp"/>
        WHERE USER_ID = #{userId}
    </update>



    <delete id="deleteAllMfaDevice">
        DELETE FROM MI_USER_MAP_MFA_DEVICE
    </delete>

    <update id="setUserSecretKey">
        UPDATE MI_USER_INFO_USER
        SET SECRET_KEY =  #{secretKey}
        <if test="userId != null">
            WHERE USER_ID = #{userId}
        </if>
    </update>

    <update id="setShortcut">
        UPDATE MI_USER_INFO_USER
        SET SHORTCUT = #{shortcut}
        WHERE USER_ID = #{userId}
    </update>

    <update id="setEmailSendingOptions">
        UPDATE MI_USER_INFO_USER
        SET GET_FAULT = #{get_fault}, GET_ALARM = #{get_alarm}
        WHERE USER_ID = #{user_id}
    </update>

    <update id="setEmailSendingDisconnectAlarm">
        UPDATE MI_USER_INFO_USER
        SET DISCONNECT_ALARM = #{disconnect_alarm}
        WHERE USER_ID = #{user_id}
    </update>

    <!-- [2019.10 RC] KSY : PASSWORD_CHANGE_DATE 컬럼 추가 -->
    <update id="setUser">
        UPDATE MI_USER_INFO_USER
        SET
        PASSWORD = #{user.password},
        PASSWORD_CHANGE_DATE = #{user.password_change_date},
        USER_NAME = #{user.user_name},
        EMAIL = #{user.email},
        ORGANIZATION = #{user.organization},
        TEAM = #{user.team},
        JOB_POSITION = #{user.job_position},
        PHONE_NUM = #{user.phone_num},
        MOBILE_NUM = #{user.mobile_num},
        APPROVAL_TYPE = #{user.approval_type},
        IS_APPROVED = #{user.is_approved},
        IS_DELETED = #{user.is_deleted},
        ROOT_GROUP_ID = #{user.root_group_id},
        OS_TYPE = #{user.os_type},
        SERIAL_NUM = #{user.serial_num},
        USING_MOBILE = #{user.using_mobile},
        IS_RESET_PWD = #{user.is_reset_pwd},
        MODIFY_DATE = <include refid="utils.currentTimestamp"/>,
        SCHEDULE_FIRST_DAY = #{user.schedule_first_day},
        DATE_FORMAT = #{user.date_format},
        TIME_FORMAT = #{user.time_format}
        <if test="user.ldap_info != null and user.ldap_info != '' ">
            , LDAP_INFO= #{user.ldap_info}
        </if>
        WHERE USER_ID = #{user.user_id}
    </update>

    <!-- [2019.10 RC] KSY -->
    <!-- Ldap 사용자 제외 -->
    <update id="setIsResetPasswordBatch">
        UPDATE MI_USER_INFO_USER
        SET  MODIFY_DATE = <include refid="utils.currentTimestamp"/>
        , IS_RESET_PWD = 'Y'
        <![CDATA[  WHERE  PASSWORD_CHANGE_DATE <  #{passwordChangeDate}  ]]>
        AND IS_DELETED = 'N'
        AND ( LDAP_INFO is null OR RTRIM(LTRIM(LDAP_INFO)) = '' )
    </update>

    <update id="setIsApprovedByUserId">
        UPDATE MI_USER_INFO_USER SET IS_APPROVED = #{approved}, MODIFY_DATE =
        <include refid="utils.currentTimestamp"/>
        WHERE USER_ID = #{userId}
    </update>

    <update id="setLoginDateByUserId">
        UPDATE MI_USER_INFO_USER SET LAST_LOGIN_DATE =
        <include refid="utils.currentTimestamp"/>
        , MODIFY_DATE =
        <include refid="utils.currentTimestamp"/>
        WHERE USER_ID = #{userId}
    </update>

    <update id="setIsDeletedByUserId">
        UPDATE MI_USER_INFO_USER SET IS_DELETED = 'Y', MODIFY_DATE =<include refid="utils.currentTimestamp"/>,
        WITHDRAWAL_DATE =
        <include refid="utils.currentTimestamp"/>
        , REJECT_REASON = #{reason} ,
        DELETE_TYPE = #{delete_type}
        WHERE USER_ID = #{userId}
    </update>

    <update id="setOrganizationByUserIdList">
        UPDATE MI_USER_INFO_USER SET ORGANIZATION = #{organization},
        MODIFY_DATE =
        <include refid="utils.currentTimestamp"/>
        WHERE USER_ID IN
        <foreach item="item" index="index" collection="userIdList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="setRejectUser">
        UPDATE MI_USER_INFO_USER SET IS_REJECT = 'Y' , REJECT_REASON = #{rejectReason},
        REJECT_DATE =
        <include refid="utils.currentTimestamp"/>
        WHERE USER_ID = #{userId}
    </update>

    <delete id="deleteDashboardUserInfoByUserId">
		DELETE FROM MI_USER_MAP_DASHBOARD
		WHERE USER_ID = #{userId}
	</delete>

    <delete id="deleteUser">
        DELETE FROM MI_USER_INFO_USER
        WHERE USER_ID = #{userId}
    </delete>

    <delete id="deleteMappingInfoByUserID_GroupUser">
        DELETE FROM MI_USER_MAP_GROUP_USER
        WHERE USER_ID = #{userId}
    </delete>

    <delete id="deleteMappingInfoByUserID_RoleUser">
        DELETE FROM MI_USER_MAP_ROLE_USER
        WHERE USER_ID = #{userId}
    </delete>

    <delete id="deleteMappingInfoByUserID_MenuUser">
        DELETE FROM MI_DMS_MAP_MENU_USER
        WHERE USER_ID = #{userId}
    </delete>

    <delete id="deleteLDAPUser">
        DELETE FROM MI_USER_INFO_USER WHERE user_id IN
		(
			SELECT A.user_id
			FROM MI_USER_INFO_USER A, MI_USER_MAP_ROLE_USER B, MI_USER_INFO_ROLE C
			WHERE A.root_group_id = #{orgId} AND A.user_id = B.user_id AND B.role_id = C.role_id AND C.role_name != 'Administrator' AND ldap_info IS NOT NULL AND A.ldap_info != ''
        )
    </delete>

    <update id="removeLdapInfomation">
		UPDATE MI_USER_INFO_USER SET LDAP_USER_ID = '', LDAP_INFO = '' WHERE ROOT_GROUP_ID = #{orgId} 
	</update>

    <select id="getAllByUserId" resultType="com.samsung.magicinfo.framework.user.entity.User">
        SELECT
            A.*,
            B.GROUP_ID   AS GROUP_ID,
            B.GROUP_NAME AS GROUP_NAME,
            D.ROLE_NAME  AS ROLE_NAME
        FROM MI_USER_INFO_USER A, MI_USER_INFO_GROUP B, MI_USER_MAP_GROUP_USER C,
            MI_USER_INFO_ROLE D, MI_USER_MAP_ROLE_USER E
        WHERE A.USER_ID = #{userId} AND A.USER_ID = E.USER_ID AND E.ROLE_ID = D.ROLE_ID
              AND A.USER_ID = C.USER_ID AND C.GROUP_ID = B.GROUP_ID AND A.IS_DELETED = 'N'
    </select>

    <select resultType="java.lang.Integer" id="getCountByUserId">
        SELECT
            COUNT(USER_ID)
        FROM MI_USER_INFO_USER
        WHERE USER_ID = #{userId}
    </select>

    <select resultType="java.lang.Integer" id="getCountByUserIdForCheck">
        SELECT
            COUNT(USER_ID)
        FROM MI_USER_INFO_USER
        WHERE LOWER(USER_ID) = LOWER(#{userId})
    </select>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getAllUser">
        SELECT A.*, B.GROUP_NAME AS GROUP_NAME, D.ROLE_NAME AS ROLE_NAME
        <include refid="getAllUser_body"/>
        <include refid="sortColumnAndOrder"/>

    </select>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getAllUser" databaseId="mssql">
        SELECT * FROM
        (
        SELECT A.*, B.GROUP_NAME AS GROUP_NAME, D.ROLE_NAME AS ROLE_NAME, ROW_NUMBER() OVER(<include
            refid="notAmbiguousSortColumnAndOrder"/>) as rownum
        <include refid="getAllUser_body"/>
        ) as SubQuery
        ORDER BY rownum
    </select>

    <sql id="getAllUser_body">
        FROM MI_USER_INFO_USER A, MI_USER_INFO_GROUP B, MI_USER_MAP_GROUP_USER C, MI_USER_INFO_ROLE D,
        MI_USER_MAP_ROLE_USER E
        WHERE A.USER_ID = E.USER_ID AND E.ROLE_ID = D.ROLE_ID AND A.USER_ID = C.USER_ID AND C.GROUP_ID = B.GROUP_ID AND
        A.IS_APPROVED = 'Y' AND A.IS_DELETED = 'N'
        <if test="map.organization != null and map.organization != '' and map.organization != 'ROOT'">
            AND A.ORGANIZATION = #{map.organization}
        </if>
        <if test="map.root_group_id != null and map.root_group_id != '' and map.root_group_id != 0">
            AND A.ROOT_GROUP_ID = #{map.root_group_id}
        </if>
        <if test="map.searchText != null and map.searchText != ''">
            AND ( 	(UPPER(A.USER_NAME) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ) OR
            (UPPER(A.USER_ID) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ))
        </if>
        <if test="condition != null">
            <include refid="customSearchCondition"/>
        </if>
    </sql>

    <select resultType="java.lang.Integer" id="getCountAllUser">
        SELECT
        COUNT(A.USER_ID)
        FROM MI_USER_INFO_USER A, MI_USER_INFO_GROUP B, MI_USER_MAP_GROUP_USER C, MI_USER_INFO_ROLE D,
        MI_USER_MAP_ROLE_USER E
        WHERE A.USER_ID = E.USER_ID AND E.ROLE_ID = D.ROLE_ID AND A.USER_ID = C.USER_ID AND C.GROUP_ID = B.GROUP_ID AND
        A.IS_APPROVED = 'Y' AND A.IS_DELETED = 'N'
        <if test="map.organization != null and map.organization != '' and map.organization != 'ROOT'">
            AND A.ORGANIZATION = #{map.organization}
        </if>
        <if test="map.root_group_id != null and map.root_group_id != '' and map.root_group_id != 0">
            AND A.ROOT_GROUP_ID = #{map.root_group_id}
        </if>
        <if test="map.searchText != null and map.searchText != ''">
            AND ( 	(UPPER(A.USER_NAME) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ) OR
            (UPPER(A.USER_ID) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ))
        </if>
        <if test="condition != null">
            <include refid="customSearchCondition"/>
        </if>
    </select>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getGroupedUser">
        SELECT A.*, B.GROUP_NAME AS GROUP_NAME, D.ROLE_NAME AS ROLE_NAME
        <include refid="getGroupedUser_body"/>
        <if test="condition != null">
            <include refid="customSearchCondition"/>
        </if>
        <include refid="sortColumnAndOrder"/>

    </select>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getGroupedUser" databaseId="mssql">
        SELECT * FROM
        (
        SELECT A.*, B.GROUP_NAME AS GROUP_NAME, D.ROLE_NAME AS ROLE_NAME, ROW_NUMBER() OVER(<include
            refid="notAmbiguousSortColumnAndOrder"/>) as rownum
        <include refid="getGroupedUser_body"/>
        <if test="condition != null">
            <include refid="customSearchCondition"/>
        </if>
        ) as SubQuery
        ORDER BY rownum
    </select>

    <sql id="getGroupedUser_body">
        FROM MI_USER_INFO_USER A, MI_USER_INFO_GROUP B, MI_USER_MAP_GROUP_USER C, MI_USER_INFO_ROLE D,
        MI_USER_MAP_ROLE_USER E
        WHERE A.USER_ID = E.USER_ID AND E.ROLE_ID = D.ROLE_ID AND A.USER_ID = C.USER_ID AND C.GROUP_ID = B.GROUP_ID AND
        A.IS_APPROVED = 'Y' AND A.IS_DELETED = 'N' AND A.IS_REJECT = 'N'
        <if test="map.search_text != null and map.search_text != ''">
            AND ( 	(UPPER(A.USER_NAME) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ) OR
            (UPPER(A.USER_ID) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ))
        </if>
        <if test="map.groupIds != null and map.groupIds.size() > 0 ">
            AND B.GROUP_ID IN
            <foreach item="item" index="index" collection="map.groupIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.group_id != null and map.group_id != ''">
            <bind name="safe_groupId" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.group_id)" />
            AND B.GROUP_ID = ${safe_groupId}
        </if>
    </sql>

    <select resultType="java.lang.Integer" id="getCountGroupedUser">
        SELECT
        COUNT(A.USER_ID)
        FROM MI_USER_INFO_USER A, MI_USER_INFO_GROUP B, MI_USER_MAP_GROUP_USER C, MI_USER_INFO_ROLE D,
        MI_USER_MAP_ROLE_USER E
        WHERE A.USER_ID = E.USER_ID AND E.ROLE_ID = D.ROLE_ID AND A.USER_ID = C.USER_ID AND C.GROUP_ID = B.GROUP_ID AND
        A.IS_APPROVED = 'Y' AND A.IS_DELETED = 'N' AND A.IS_REJECT = 'N'
        <if test="map.search_text != null and map.search_text != ''">
            AND ((UPPER(A.USER_NAME) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ) OR
            (UPPER(A.USER_ID) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ))
        </if>
        <if test="map.groupIds != null and map.groupIds.size() > 0 ">
            AND B.GROUP_ID IN
            <foreach item="item" index="index" collection="map.groupIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.group_id != null  and map.group_id != '' ">
            <bind name="safe_groupId" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.group_id)" />
            AND B.GROUP_ID = ${safe_groupId}
        </if>
    </select>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getAllNonApprovedUser">
        SELECT *
        <include refid="getAllNonApprovedUser_body"/>
        <include refid="sortColumnAndOrder"/>

    </select>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getAllNonApprovedUser" databaseId="mssql">
        SELECT * FROM
        (
        SELECT *, ROW_NUMBER() OVER(<include refid="sortColumnAndOrder"/>) as rownum
        <include refid="getAllNonApprovedUser_body"/>
        ) as SubQuery
        ORDER BY rownum
    </select>

    <sql id="getAllNonApprovedUser_body">
        FROM MI_USER_INFO_USER A
        WHERE IS_APPROVED = 'N' AND IS_REJECT = 'N'
        <if test="map.organization != null and map.organization != '' and map.organization != 'ROOT'">
            AND ORGANIZATION = #{map.organization}
        </if>
        <if test="map.searchText != null and map.searchText != ''">
            AND ( 	(UPPER(A.USER_NAME) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ) OR
            (UPPER(A.USER_ID) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ))
        </if>
    </sql>

    <select resultType="java.lang.Integer" id="getCountAllNonApprovedUser">
        SELECT COUNT(USER_ID) FROM MI_USER_INFO_USER A
        WHERE IS_APPROVED = 'N' AND IS_REJECT = 'N'
        <if test="map.organization != null and map.organization != '' and map.organization != 'ROOT'">
            AND ORGANIZATION = #{map.organization}
        </if>
        <if test="map.searchText != null and map.searchText != ''">
            AND ( 	(UPPER(A.USER_NAME) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ) OR
            (UPPER(A.USER_ID) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ))
        </if>
    </select>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getAllWithdrawalUser">
        SELECT *
        <include refid="getAllWithdrawalUser_body"/>
        <include refid="sortColumnAndOrder"/>

    </select>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getAllWithdrawalUser" databaseId="mssql">
        SELECT * FROM
        (
        SELECT *, ROW_NUMBER() OVER(<include refid="sortColumnAndOrder"/>) as rownum
        <include refid="getAllWithdrawalUser_body"/>
        ) as SubQuery
        ORDER BY rownum
    </select>

    <sql id="getAllWithdrawalUser_body">
        FROM MI_USER_INFO_USER A
        WHERE IS_DELETED = 'Y' AND IS_REJECT = 'N'
        <if test="map.organization != null and map.organization != '' and map.organization != 'ROOT'">
            AND ORGANIZATION = #{map.organization}
        </if>
        <if test="map.search_text != null and map.search_text != ''">
            AND ( 	(UPPER(A.USER_NAME) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ) OR
            (UPPER(A.USER_ID) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ))
        </if>
    </sql>

    <select resultType="java.lang.Integer" id="getCountAllWithdrawalUser">
        SELECT
        COUNT(USER_ID)
        FROM MI_USER_INFO_USER A
        WHERE IS_DELETED = 'Y' AND IS_REJECT = 'N'
        <if test="map.organization != null and map.organization != '' and map.organization != 'ROOT'">
            AND ORGANIZATION = #{map.organization}
        </if>
        <if test="map.search_text != null and map.search_text != ''">
            AND ( 	(UPPER(A.USER_NAME) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ) OR
            (UPPER(A.USER_ID) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ))
        </if>
    </select>

    <select resultType="java.lang.String" id="getShortcut">
        SELECT
            SHORTCUT
        FROM MI_USER_INFO_USER
        WHERE USER_ID = #{userId}
    </select>

    <select resultType="com.samsung.magicinfo.framework.content.entity.Group" id="getOrganGroupInfo">
        SELECT
            GROUP_ID,
            P_GROUP_ID,
            GROUP_DEPTH,
            GROUP_NAME
        FROM
            MI_USER_INFO_GROUP
        WHERE GROUP_ID =
              #{groupID}
    </select>

    <select resultType="java.lang.String" id="getAllUserListByEmailSendingOptions">
        SELECT
        USER_ID
        FROM MI_USER_INFO_USER
        WHERE (ROOT_GROUP_ID = #{organization_id} OR ROOT_GROUP_ID = #{rootGroupId})
        AND IS_DELETED = 'N'
        <if test="get_fault != null">
            AND GET_FAULT = #{get_fault}
        </if>
        <if test="get_alarm != null">
            AND GET_ALARM = #{get_alarm}
        </if>
        ORDER BY USER_ID
    </select>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getUserInfo">
        SELECT
            A.USER_ID,
            A.USER_NAME,
            A.EMAIL,
            A.TEAM,
            A.JOB_POSITION,
            A.PHONE_NUM,
            A.MOBILE_NUM,
            A.CREATE_DATE,
            A.LAST_LOGIN_DATE,
            A.MODIFY_DATE,
            A.ORGANIZATION,
            A.SECRET_KEY,
            B.ROOT_GROUP_ID AS ROOT_GROUP_ID,
            B.GROUP_ID      AS GROUP_ID,
            B.GROUP_NAME    AS GROUP_NAME,
            D.ROLE_NAME     AS ROLE_NAME
        FROM MI_USER_INFO_USER A, MI_USER_INFO_GROUP B, MI_USER_MAP_GROUP_USER C,
            MI_USER_INFO_ROLE D, MI_USER_MAP_ROLE_USER E
        WHERE A.USER_ID = #{userID} AND A.USER_ID = E.USER_ID AND E.ROLE_ID = D.ROLE_ID
              AND A.USER_ID = C.USER_ID AND C.GROUP_ID = B.GROUP_ID AND A.IS_DELETED = 'N'
    </select>

    <select resultType="java.lang.String" id="getIsResetPwdByUserId">
        SELECT
            IS_RESET_PWD
        FROM MI_USER_INFO_USER
        WHERE USER_ID = #{userId}
    </select>

    <select resultType="java.lang.String" id="getOsTypeByUserId">
        SELECT
            OS_TYPE
        FROM MI_USER_INFO_USER
        WHERE USER_ID = #{userId}
    </select>

    <select resultType="java.lang.String" id="getImeiByUserId">
        SELECT
            SERIAL_NUM
        FROM MI_USER_INFO_USER
        WHERE USER_ID = #{userId} AND USING_MOBILE = 'Y'
    </select>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getAllUserListBySearch">
        SELECT
        A.*,
        B.GROUP_NAME AS GROUP_NAME,
        D.ROLE_NAME AS ROLE_NAME
        FROM MI_USER_INFO_USER A, MI_USER_INFO_GROUP B, MI_USER_MAP_GROUP_USER C,
        MI_USER_INFO_ROLE D, MI_USER_MAP_ROLE_USER E
        WHERE A.USER_ID = E.USER_ID AND E.ROLE_ID = D.ROLE_ID
        AND A.USER_ID = C.USER_ID AND C.GROUP_ID = B.GROUP_ID AND A.IS_APPROVED = 'Y' AND A.IS_DELETED = 'N'
        <if test="condition != null">
            <include refid="customSearchCondition"/>
        </if>
        ORDER BY A.USER_ID
    </select>

    <select resultType="java.lang.String" id="getSMSList">
        SELECT PHONE_NUM FROM MI_USER_INFO_USER WHERE USER_ID IN
        <foreach item="item" index="index" collection="userIdList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select resultType="java.lang.String" id="getMailList">
        SELECT EMAIL FROM MI_USER_INFO_USER WHERE USER_ID IN
        <foreach item="item" index="index" collection="userIdList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select resultType="java.lang.String" id="getOrganNameByUserId">
        SELECT
            ORGANIZATION
        FROM MI_USER_INFO_USER
        WHERE USER_ID = #{userId}
    </select>
    
    <select id="getAllUserListToMigrate" resultType="com.samsung.magicinfo.framework.user.entity.User">
        SELECT USER_ID , USER_NAME , EMAIL , PHONE_NUM , MOBILE_NUM
        FROM MI_USER_INFO_USER
    </select>

    <update id="migrateUser">
        UPDATE MI_USER_INFO_USER
        SET USER_NAME = #{user.user_name} , EMAIL = #{user.email} , PHONE_NUM = #{user.phone_num} , MOBILE_NUM = #{user.mobile_num}
        WHERE USER_ID = #{user.user_id}
    </update>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getAllUserList">
        SELECT
            A.*,
            B.GROUP_NAME AS GROUP_NAME,
            D.ROLE_NAME  AS ROLE_NAME
        FROM MI_USER_INFO_USER A, MI_USER_INFO_GROUP B, MI_USER_MAP_GROUP_USER C,
            MI_USER_INFO_ROLE D, MI_USER_MAP_ROLE_USER E
        WHERE A.USER_ID = E.USER_ID AND E.ROLE_ID = D.ROLE_ID
              AND A.USER_ID = C.USER_ID AND C.GROUP_ID = B.GROUP_ID AND A.IS_APPROVED = 'Y' AND A.IS_DELETED = 'N'
              AND A.ROOT_GROUP_ID != #{organization_id}
        ORDER BY A.USER_ID
    </select>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getAllUserListNonApproved">
        SELECT
        A.*
        FROM MI_USER_INFO_USER A
        WHERE A.IS_DELETED = 'N' AND A.IS_APPROVED = 'N' AND A.IS_REJECT = 'N'
        <if test="map.organization != null and map.organization != '' and map.organization != 'ROOT'">
            AND A.ORGANIZATION = #{map.organization}
        </if>
        <if test="map.root_group_id != null and map.root_group_id != '' and map.root_group_id != 0">
            AND A.ROOT_GROUP_ID = #{map.root_group_id}
        </if>
        <if test="map.manageGroupIds != null and map.manageGroupIds.size() > 0">
            AND A.ROOT_GROUP_ID IN
            <foreach collection="map.manageGroupIds" item="groupId" open="(" separator="," close=")" >
                #{groupId}
            </foreach>
        </if>
        <if test="map.group_id != null and map.group_id != ''">
            AND B.GROUP_ID = #{map.group_id}
        </if>
        <if test="map.search_text != null and map.search_text != ''">
            AND ( 	(UPPER(A.USER_NAME) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ) OR
            (UPPER(A.USER_ID) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ))
        </if>
        <if test="condition != null">
            <include refid="customSearchCondition"/>
        </if>
        <include refid="sortColumnAndOrder"/>
    </select>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getAllUserListApproved">
        SELECT
        A.*,
        B.GROUP_NAME AS GROUP_NAME,
        B.GROUP_ID AS GROUP_ID,
        D.ROLE_NAME AS ROLE_NAME
        FROM MI_USER_INFO_USER A, MI_USER_INFO_GROUP B, MI_USER_MAP_GROUP_USER C,
        MI_USER_INFO_ROLE D, MI_USER_MAP_ROLE_USER E
        WHERE A.USER_ID = E.USER_ID AND E.ROLE_ID = D.ROLE_ID
        AND A.USER_ID = C.USER_ID AND C.GROUP_ID = B.GROUP_ID AND A.IS_APPROVED = 'Y' AND A.IS_DELETED = 'N'
        <if test="map.organization != null and map.organization != '' and map.organization != 'ROOT'">
            AND A.ORGANIZATION = #{map.organization}
        </if>
        <if test="map.root_group_id != null and map.root_group_id != '' and map.root_group_id != 0">
            AND A.ROOT_GROUP_ID = #{map.root_group_id}
        </if>
        <if test="map.group_id != null">
            AND B.GROUP_ID = #{map.group_id}
        </if>
        <if test="map.search_text != null and map.search_text != ''">
            AND ( 	(UPPER(A.USER_NAME) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ) OR
            (UPPER(A.USER_ID) LIKE
            '%' <include refid="utils.concatenate"/> #{map.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^' ))
        </if>
        <if test="condition != null">
            <include refid="customSearchCondition"/>
        </if>
        <include refid="sortColumnAndOrder"/>
    </select>

    <select resultType="java.util.Map" id="getDeleteInfoByUserId">
        SELECT
            WITHDRAWAL_DATE,
            REJECT_REASON,
            DELETE_TYPE
        FROM MI_USER_INFO_USER
        WHERE USER_ID = #{userId}
    </select>

    <select resultType="java.util.Map" id="getIsRejectByUserId">
        SELECT
            IS_REJECT,
            REJECT_REASON,
            REJECT_DATE,
            CREATE_DATE,
            DELETE_TYPE
        FROM MI_USER_INFO_USER
        WHERE USER_ID = #{userId} AND IS_DELETED = 'N'
    </select>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getAllUserListByRootGroupId">
        SELECT A.*, B.GROUP_NAME AS GROUP_NAME, D.ROLE_NAME AS ROLE_NAME
        FROM MI_USER_INFO_USER A, MI_USER_INFO_GROUP B, MI_USER_MAP_GROUP_USER C,
        MI_USER_INFO_ROLE D, MI_USER_MAP_ROLE_USER E
        WHERE A.USER_ID = E.USER_ID AND E.ROLE_ID = D.ROLE_ID
        AND A.USER_ID = C.USER_ID AND C.GROUP_ID = B.GROUP_ID AND A.IS_APPROVED = 'Y' AND A.IS_DELETED = 'N'
        <if test="organization_id != root_group_id">
            AND A.ROOT_GROUP_ID = #{organization_id}
        </if>
        ORDER BY A.USER_ID
    </select>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getAllRootUserList">
        SELECT A.*, B.GROUP_NAME AS GROUP_NAME, D.ROLE_NAME AS ROLE_NAME
        FROM MI_USER_INFO_USER A, MI_USER_INFO_GROUP B, MI_USER_MAP_GROUP_USER C,
        MI_USER_INFO_ROLE D, MI_USER_MAP_ROLE_USER E
        WHERE A.USER_ID = E.USER_ID AND E.ROLE_ID = D.ROLE_ID
        AND A.USER_ID = C.USER_ID AND C.GROUP_ID = B.GROUP_ID AND A.IS_APPROVED = 'Y' AND A.IS_DELETED = 'N'
        AND A.ROOT_GROUP_ID = '0'
        ORDER BY A.USER_ID;
    </select>

    <select resultType="java.util.Map" id="getAllUserByRootGroupId">
        SELECT
            USER_ID
        FROM MI_USER_INFO_USER
        WHERE ROOT_GROUP_ID = #{rootGroupId}
    </select>

    <select resultType="java.util.Map" id="getAllApprovalUserByRootGroupIdAll">
        SELECT
            A.*,
            B.GROUP_ID   AS GROUP_ID,
            B.GROUP_NAME AS GROUP_NAME,
            D.ROLE_NAME  AS ROLE_NAME
        FROM MI_USER_INFO_USER A, MI_USER_INFO_GROUP B, MI_USER_MAP_GROUP_USER C,
            MI_USER_INFO_ROLE D, MI_USER_MAP_ROLE_USER E
        WHERE A.USER_ID = E.USER_ID AND E.ROLE_ID = D.ROLE_ID
              AND A.USER_ID = C.USER_ID AND C.GROUP_ID = B.GROUP_ID AND A.IS_APPROVED = 'Y' AND A.IS_DELETED = 'N' AND
              A.ROOT_GROUP_ID = #{rootGroupId}
        ORDER BY A.USER_ID ASC
    </select>

    <select resultType="java.util.Map" id="getAllApprovalUserByRootGroupId">
        SELECT
            A.USER_ID
        FROM MI_USER_INFO_USER A, MI_USER_INFO_GROUP B, MI_USER_MAP_GROUP_USER C,
            MI_USER_INFO_ROLE D, MI_USER_MAP_ROLE_USER E
        WHERE A.USER_ID = E.USER_ID AND E.ROLE_ID = D.ROLE_ID
              AND A.USER_ID = C.USER_ID AND C.GROUP_ID = B.GROUP_ID AND A.IS_APPROVED = 'Y' AND A.IS_DELETED = 'N' AND
              A.ROOT_GROUP_ID = #{rootGroupId}
        ORDER BY A.USER_ID ASC
    </select>

    <select resultType="java.lang.String" id="getIsDeletedByUserId">
        SELECT
            IS_DELETED
        FROM MI_USER_INFO_USER
        WHERE USER_ID = #{userId}
    </select>

    <select resultType="java.lang.String" id="getIsApprovedByUserId">
        SELECT
            IS_APPROVED
        FROM MI_USER_INFO_USER
        WHERE USER_ID = #{userId} AND IS_DELETED = 'N'
    </select>

    <select resultType="com.samsung.magicinfo.framework.user.entity.User" id="getUserByUserId">
        SELECT
            *
        FROM MI_USER_INFO_USER
        WHERE USER_ID = #{userId}
    </select>

    <select resultType="java.util.Map" id="getRootGroupIdByUserId">
        SELECT
            ROOT_GROUP_ID
        FROM MI_USER_INFO_USER
        WHERE USER_ID = #{userId} AND IS_DELETED = 'N'
    </select>

    <select resultType="java.lang.String" id="getNameByUserId">
        SELECT
            USER_NAME
        FROM MI_USER_INFO_USER
        WHERE USER_ID = #{userId} AND IS_DELETED = 'N'
    </select>

    <select id="getCountByUserIdIsDeleted" resultType="java.lang.Integer">
        SELECT COUNT(USER_ID) FROM MI_USER_INFO_USER WHERE USER_ID = #{userId} AND IS_DELETED = 'N'
    </select>

    <select id="getCountByLDAPUserFullIdForCheck" resultType="java.lang.Integer">
        SELECT COUNT(USER_ID) FROM MI_USER_INFO_USER WHERE LDAP_INFO = #{ldapFullId}
    </select>

    <select id="getCountByLDAPUserIdForCheck" resultType="java.lang.Integer">
        SELECT COUNT(USER_ID) FROM MI_USER_INFO_USER WHERE LDAP_USER_ID = #{ldapUserId} OR USER_ID = #{ldapUserId}
    </select>

    <select id="getLdapUserInfo" resultType="java.util.Map">
        SELECT USER_ID, LDAP_USER_ID, LDAP_INFO FROM MI_USER_INFO_USER
        WHERE LDAP_USER_ID = #{ldapUserId} OR USER_ID = #{ldapUserId}
        ORDER BY USER_ID ASC
    </select>

    <sql id="sortColumnAndOrder">
        <choose>
            <when test="map.sortColumn != null and map.sortColumn != ''">
                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
                order by ${safe_sortColumn}
            </when>
            <otherwise>
                order by user_name
            </otherwise>
        </choose>
        <choose>
            <when test="map.sortOrder != null and map.sortOrder != ''">
                <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
                ${safe_sortOrder}
            </when>
            <otherwise>
                asc
            </otherwise>
        </choose>
    </sql>

    <sql id="notAmbiguousSortColumnAndOrder">
        <choose>
            <when test="map.sortColumn != null and map.sortColumn != ''">
                <choose>

                    <when test="map.sortColumn.equalsIgnoreCase('USER_ID')">
                        <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
                        order by A.${safe_sortColumn}
                    </when>
                    <otherwise>
                        <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
                        order by ${safe_sortColumn}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by user_name
            </otherwise>
        </choose>
        <choose>
            <when test="map.sortOrder != null and map.sortOrder != ''">
                <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
                ${safe_sortOrder}
            </when>
            <otherwise>
                asc
            </otherwise>
        </choose>
    </sql>

    <sql id="customSearchCondition">
        <if test="condition.user_id != null and condition.user_id != ''">
            AND A.USER_ID LIKE '%' <include refid="utils.concatenate"/>#{condition.user_id}<include refid="utils.concatenate"/>'%' ESCAPE '^'
        </if>
        <if test="condition.user_name != null and condition.user_name != ''">
            AND USER_NAME LIKE '%' <include refid="utils.concatenate"/>#{condition.user_name}<include refid="utils.concatenate"/>'%' ESCAPE '^'
        </if>
        <if test="condition.organization != null and condition.organization != ''">
            AND ORGANIZATION = #{condition.organization}
        </if>
        <if test="condition.group_name != null and condition.group_name != ''">
            AND GROUP_NAME = #{condition.group_name}
        </if>
        <if test="condition.role_name != null and condition.role_name != ''">
            AND D.ROLE_NAME = #{condition.role_name}
        </if>
        <if test="condition.phone_num != null and condition.phone_num != ''">
            AND (PHONE_NUM LIKE '%'<include refid="utils.concatenate"/>#{condition.phone_num}<include refid="utils.concatenate"/>'%' OR MOBILE_NUM LIKE '%'<include refid="utils.concatenate"/>#{condition.phone_num}<include refid="utils.concatenate"/>'%')
        </if>
        <if test="condition.email != null and condition.email != ''">
            AND EMAIL LIKE '%'<include refid="utils.concatenate"/>#{condition.email}<include refid="utils.concatenate"/>'%' ESCAPE '^'
        </if>
        <if test="condition.join_start_date != null">
            AND CREATE_DATE >= #{condition.join_start_date}
        </if>
        <if test="condition.join_end_date != null">
            AND CREATE_DATE &lt;= #{condition.join_end_date}
        </if>
        <if test="condition.last_login_start_date != null">
            AND LAST_LOGIN_DATE >= #{condition.last_login_start_date}
        </if>
        <if test="condition.last_login_end_date != null">
            AND LAST_LOGIN_DATE &lt;= #{condition.last_login_end_date}
        </if>
    </sql>

    <select id="getNewAndModifiedUserByDate" resultType="com.samsung.magicinfo.framework.user.entity.User">
        SELECT * FROM MI_USER_INFO_USER WHERE ((CREATE_DATE &gt;= #{startDate} AND CREATE_DATE &lt;= #{endDate}) OR (MODIFY_DATE &gt;= #{startDate} AND MODIFY_DATE &lt;= #{endDate}))
	</select>

    <select id="getDashboard" resultType="com.samsung.magicinfo.framework.dashboard.entity.UserDashboardEntity">
    	SELECT MI_USER_MAP_DASHBOARD.DASHBOARD_ID, MI_USER_MAP_DASHBOARD.PRIORITY, MI_USER_MAP_DASHBOARD.USER_ID, MI_SYSTEM_INFO_DASHBOARD.DASHBOARD_NAME
    	FROM MI_USER_MAP_DASHBOARD
    	LEFT JOIN MI_SYSTEM_INFO_DASHBOARD ON MI_USER_MAP_DASHBOARD.DASHBOARD_ID = MI_SYSTEM_INFO_DASHBOARD.DASHBOARD_ID
    	WHERE USER_ID = #{userId}
    	ORDER BY PRIORITY ASC
	</select>

    <select id="getDashboardOrderByDashboardId" resultType="com.samsung.magicinfo.framework.dashboard.entity.UserDashboardEntity">
    	SELECT MI_USER_MAP_DASHBOARD.DASHBOARD_ID, MI_USER_MAP_DASHBOARD.PRIORITY, MI_USER_MAP_DASHBOARD.USER_ID, MI_SYSTEM_INFO_DASHBOARD.DASHBOARD_NAME
    	FROM MI_USER_MAP_DASHBOARD
    	LEFT JOIN MI_SYSTEM_INFO_DASHBOARD ON MI_USER_MAP_DASHBOARD.DASHBOARD_ID = MI_SYSTEM_INFO_DASHBOARD.DASHBOARD_ID
    	WHERE USER_ID = #{userId}
    	ORDER BY DASHBOARD_ID ASC
	</select>

    <insert id="addDashboardWithUserId">
        INSERT INTO MI_USER_MAP_DASHBOARD (USER_ID, DASHBOARD_ID, PRIORITY) VALUES (#{userId}, #{dashboardId}, #{priority})
    </insert>

    <select id="getOrganization" resultType="Map">
		SELECT GROUP_ID, GROUP_NAME
		FROM MI_USER_INFO_GROUP
		WHERE P_GROUP_ID = 0
	</select>

    <select id="getDisconnectAlarmUserList" resultType="Map">
		SELECT A.USER_ID, A.EMAIL
		FROM MI_USER_INFO_USER A, MI_SYSTEM_MAP_USER_NOTIFICATION B
		WHERE B.NOTIFICATION_TYPE='DISCONNECTED_DEVICE' AND B.ORGANIZATION_ID = #{orgId} AND A.USER_ID = B.USER_ID
	</select>

    <select id="getContentApprover" resultType="com.samsung.magicinfo.framework.user.entity.User">
        SELECT * FROM MI_USER_INFO_USER WHERE CONTENT_APPROVER = 'Y'
	</select>

    <select id="getContentManagerUserListByOrgId" resultType="java.util.Map">
        SELECT A.USER_ID, A.ORGANIZATION, A.CONTENT_APPROVER, D.ROLE_NAME, F.GROUP_ID
        FROM MI_USER_INFO_USER A, MI_USER_MAP_ROLE_USER B, MI_USER_MAP_ABILITY_ROLE C, MI_USER_INFO_ROLE D, MI_USER_INFO_ABILITY E, MI_USER_MAP_GROUP_USER F
        WHERE A.USER_ID = B.USER_ID AND B.ROLE_ID = D.ROLE_ID AND B.ROLE_ID = C.ROLE_ID AND C.ABILITY_ID = E.ABILITY_ID AND E.ABILITY_NAME = 'Content Manage Authority' AND A.ROOT_GROUP_ID = #{orgId} AND A.IS_DELETED = 'N' AND A.IS_APPROVED = 'Y' AND A.USER_ID = F.USER_ID
	</select>

    <update id="setContentApprover">
        UPDATE MI_USER_INFO_USER
        SET CONTENT_APPROVER = #{contentApprover}
        WHERE USER_ID = #{userId}
    </update>

    <update id="setAllContentApprover">
        UPDATE MI_USER_INFO_USER
        SET CONTENT_APPROVER = #{contentApprover}
    </update>

    <select id="getContentApproverListByGroupId" resultType="java.util.Map">
        SELECT USER_ID FROM MI_USER_INFO_USER WHERE (CONTENT_APPROVER = 'Y' AND ROOT_GROUP_ID = #{rootGroupId}) OR (CONTENT_APPROVER = 'Y' AND ROOT_GROUP_ID = 0)
	</select>

    <update id="setLocale">
		UPDATE MI_USER_INFO_USER
        SET LOCALE = #{locale}
        WHERE USER_ID = #{userId}
	</update>

    <select id="getEmailNotificationUserListByOrgId" resultType="java.util.Map">
        SELECT
        A.USER_ID, A.EMAIL, A.USER_NAME, A.GET_FAULT, A.GET_ALARM, A.ORGANIZATION,
        B.TIMEZONE_NOT_SET, B.SCHEDULE_NOT_PUBLISHED, B.SCHEDULE_FAILED, B.RESERVED_SCHEDULE_FAILED, B.CONTENT_DOWNLOAD_ERROR, B.DISCONNECTED_DEVICE,
        B.EXPIRE_SCHEDULE, B.EXPIRE_PLAYLIST, B.SW_ERROR, B.DL_SERVER_ERROR, B.DN_SERVER_ERROR, B.RM_SERVER_ERROR, B.DEVICE_ERROR, B.DEVICE_WARNING
        FROM MI_USER_INFO_USER A LEFT JOIN MI_USER_MAP_NOTIFICATION_USER B ON A.USER_ID = B.USER_ID AND B.ORGANIZATION_ID = #{orgId}
        WHERE (
        A.ROOT_GROUP_ID = #{orgId}
        <if test="includeRoot">
            OR A.ROOT_GROUP_ID = 0
        </if>
        )
        AND A.IS_DELETED = 'N'
        ORDER BY A.ORGANIZATION, USER_ID
    </select>

    <insert id="addEmailNotificationOption">
    	INSERT INTO MI_SYSTEM_MAP_USER_NOTIFICATION (USER_ID, ORGANIZATION_ID, NOTIFICATION_TYPE)
		SELECT #{userId}, #{orgId}, #{type}
		WHERE
		NOT EXISTS (
			SELECT * FROM MI_SYSTEM_MAP_USER_NOTIFICATION WHERE USER_ID = #{userId} AND ORGANIZATION_ID = #{orgId} AND NOTIFICATION_TYPE = #{type}
		)
    </insert>

    <delete id="deleteEmailNotificationOption">
    	DELETE FROM MI_SYSTEM_MAP_USER_NOTIFICATION WHERE USER_ID = #{userId} AND ORGANIZATION_ID = #{orgId} AND NOTIFICATION_TYPE= #{type}
    </delete>

    <delete id="deleteEmailNotificationByOrdIdAndUserId">
    	DELETE FROM MI_SYSTEM_MAP_USER_NOTIFICATION WHERE USER_ID = #{userId} AND ORGANIZATION_ID = #{orgId}
    </delete>

    <select id="getAlarmUserListByOrgIdAndType" resultType="Map">
        <!--
        SELECT B.USER_ID, B.EMAIL
        FROM MI_USER_MAP_NOTIFICATION_USER A LEFT JOIN MI_USER_INFO_USER B ON A.USER_ID = B.USER_ID
        WHERE A.ORGANIZATION_ID = #{orgId} AND A.${type} = <include refid="utils.true"/>
        -->
        SELECT
        B.USER_ID, B.EMAIL
        FROM
        MI_SYSTEM_MAP_USER_NOTIFICATION A, MI_USER_INFO_USER B
        WHERE
        A.USER_ID = B.USER_ID
        AND A.NOTIFICATION_TYPE = #{type}
        AND A.ORGANIZATION_ID = #{orgId}
        AND IS_DELETED = 'N'
    </select>

    <select id="getOrganNameByRootGroupId" resultType="String">
		SELECT ORGANIZATION
		FROM MI_USER_INFO_USER
		WHERE ROOT_GROUP_ID = #{rootGroupId}
		LIMIT 1
	</select>

    <select id="getOrganNameByRootGroupId" resultType="String" databaseId="mssql">
		SELECT TOP 1 ORGANIZATION
		FROM MI_USER_INFO_USER
		WHERE ROOT_GROUP_ID = #{rootGroupId}
	</select>

    <update id="setIsFirstLoginByUserId">
        UPDATE MI_USER_INFO_USER SET IS_FIRST_LOGIN =
        <include refid="utils.false"/>
        WHERE USER_ID = #{userId}
    </update>

    <update id="setResetPwToken">
    	UPDATE MI_USER_INFO_USER
    	SET ENCRYPTION_TOKEN = #{encryptionToken}, RESET_EXPIRATION_IN_SEC = #{passwordResetExpirationInSec}
    	WHERE USER_ID = #{userId}
    </update>

    <select id="checkResetValidByTokenAndId" resultType="java.lang.Boolean">
    	SELECT EXISTS (SELECT 1 FROM MI_USER_INFO_USER WHERE user_id = #{userId} and encryption_token = #{encryptionToken})
    </select>

    <select id="getAllUserWithEncryptionToken" resultType="com.samsung.magicinfo.framework.user.entity.User">
    	SELECT * FROM MI_USER_INFO_USER WHERE encryption_token != ''
    </select>

    <select id="getCurMngOrgId" resultType="long">
    	SELECT
			CUR_MNG_ORG_ID
		FROM
			MI_USER_MAP_USER_MANAGE_ORG
		WHERE
			USER_ID = #{userId}
    </select>

    <update id="setCurMngOrgId">
    	UPDATE
    		MI_USER_MAP_USER_MANAGE_ORG
    	SET
    		CUR_MNG_ORG_ID = #{mngOrg}
    	WHERE
    		USER_ID = #{userId}
    </update>

    <select id="getMUInfoByMngOrgId" resultType="com.samsung.magicinfo.framework.user.entity.User">
    	SELECT
			A.*
		FROM
			MI_USER_INFO_USER A,
			MI_USER_MAP_USER_MANAGE_ORG B,
			MI_USER_MAP_MANAGE_ORG_GROUP_MANAGE_ORG C
		WHERE
			A.USER_ID = B.USER_ID AND
			B.MNG_ORG_GROUP_ID = C.MNG_ORG_GROUP_ID AND
			C.ORG_GROUP_ID = #{mngOrg}
    </select>

    <update id="setMUByUserId">
    	UPDATE
    		MI_USER_INFO_USER
    	SET
    		IS_MU = #{flag}
    	WHERE
    		USER_ID = #{userId}
    </update>

    <select id="getAdminOfOrganizationByRootGroupId" resultType="com.samsung.magicinfo.framework.user.entity.User">
    	SELECT * FROM MI_USER_INFO_USER A, MI_USER_MAP_ROLE_USER B
		WHERE
			B.ROLE_ID = #{roleId} AND
			A.USER_ID = B.USER_ID AND
			A.ROOT_GROUP_ID = #{rootGroupId} AND
			A.IS_DELETED = 'N'
		ORDER BY CREATE_DATE
		LIMIT 1
    </select>

    <select id="getAdminOfOrganizationByRootGroupId" resultType="com.samsung.magicinfo.framework.user.entity.User" databaseId="mssql">
    	SELECT TOP(1) * FROM MI_USER_INFO_USER A, MI_USER_MAP_ROLE_USER B
		WHERE
			B.ROLE_ID = #{roleId} AND
			A.USER_ID = B.USER_ID AND
			A.ROOT_GROUP_ID = #{rootGroupId} AND
			A.IS_DELETED = 'N'
		ORDER BY CREATE_DATE
    </select>

    <select id="getAdminOfOrganizationNotInDeleteUsers" resultType="com.samsung.magicinfo.framework.user.entity.User">
    	SELECT * FROM MI_USER_INFO_USER A, MI_USER_MAP_ROLE_USER B
		WHERE
			B.ROLE_ID = #{roleId} AND
			A.USER_ID = B.USER_ID AND
			A.ROOT_GROUP_ID = #{rootGroupId} AND
			A.IS_DELETED = 'N' AND
			NOT (A.USER_ID = ANY(#{deleteUsers}))
		ORDER BY CREATE_DATE
		LIMIT 1
    </select>

    <select id="getAdminOfOrganizationNotInDeleteUsers" resultType="com.samsung.magicinfo.framework.user.entity.User" databaseId="mssql">
        SELECT TOP(1) * FROM MI_USER_INFO_USER A, MI_USER_MAP_ROLE_USER B
        WHERE
        B.ROLE_ID = #{roleId} AND
        A.USER_ID = B.USER_ID AND
        A.ROOT_GROUP_ID = #{rootGroupId} AND
        A.IS_DELETED = 'N' AND
        A.USER_ID NOT IN <foreach item="item" index="index" collection="deleteUsers" open="(" separator="," close=")">
        #{item}
    </foreach>
        ORDER BY CREATE_DATE
    </select>

    <select id="getfilterExport" resultType="com.samsung.magicinfo.framework.user.entity.User" >
        SELECT
        A.USER_ID , A.USER_NAME, A.EMAIL, A.PHONE_NUM , A.MOBILE_NUM,  A.PASSWORD_CHANGE_DATE, A.ORGANIZATION, B.GROUP_NAME , D.ROLE_NAME, F.MNG_ORG_GROUP_NAME AS ORGANIZATION_GROUP, A.TEAM, A.JOB_POSITION,A.CREATE_DATE,
        A.LAST_LOGIN_DATE, A.LDAP_USER_ID, A.APPROVAL_TYPE, B.GROUP_ID, A.ROOT_GROUP_ID
        FROM MI_USER_INFO_USER A
        LEFT OUTER JOIN(SELECT MNG_ORG_GROUP_NAME, USER_ID
        FROM MI_USER_MAP_USER_MANAGE_ORG G, MI_CMS_INFO_MANAGE_ORG_GROUP H
        WHERE G.MNG_ORG_GROUP_ID = H.MNG_ORG_GROUP_ID )F ON (A.USER_ID = F.USER_ID),
        MI_USER_INFO_GROUP B, MI_USER_MAP_GROUP_USER C,
        MI_USER_INFO_ROLE D, MI_USER_MAP_ROLE_USER E
        WHERE A.USER_ID = E.USER_ID AND E.ROLE_ID = D.ROLE_ID
        AND A.USER_ID = C.USER_ID AND C.GROUP_ID = B.GROUP_ID
        AND A.IS_APPROVED = 'Y' AND A.IS_DELETED = 'N'
        <if test="map.searchText != null and map.searchText != ''">
            AND (   (UPPER(A.USER_NAME) LIKE
            '%' <include refid="utils.concatenate"/> #{map.searchText} <include refid="utils.concatenate"/> '%' ESCAPE '^' ) OR
            (UPPER(A.USER_ID) LIKE
            '%' <include refid="utils.concatenate"/> #{map.searchText} <include refid="utils.concatenate"/> '%' ESCAPE '^' ))
        </if>
        <if test="map.organizationId != null and map.organizationId != ''">
            AND A.ROOT_GROUP_ID = #{map.organizationId}
        </if>
        <if test="map.groupId != null and map.groupId != ''">
            AND B.GROUP_ID = #{map.groupId}
        </if>
        <if test="map.roleName != null and map.roleName != ''">
            AND D.roleName = #{map.roleName}
        </if>
        <if test="map.startCreatedDate != null">
            AND A.CREATE_DATE >= #{map.startCreatedDate}
        </if>
        <if test="map.endCreatedDate != null">
            AND A.CREATE_DATE &lt;= #{map.endCreatedDate}
        </if>
        <choose>
            <when test="map.sortColumn != null and map.sortColumn != ''">
                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
                order by ${safe_sortColumn}
            </when>
            <otherwise>
                ORDER BY A.USER_ID
            </otherwise>
        </choose>
        <choose>
            <when test="map.sortOrder != null and map.sortOrder != ''">
                <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
                ${safe_sortOrder}
            </when>
            <otherwise>
                asc
            </otherwise>
        </choose>
    </select>

    <select id="getfilterExportUnApporved" resultType="com.samsung.magicinfo.framework.user.entity.User" >
        SELECT
        A.*
        FROM MI_USER_INFO_USER A
        WHERE A.IS_DELETED = 'N' AND A.IS_APPROVED = 'N' AND A.IS_REJECT = 'N'
        <if test="map.searchText != null and map.searchText != ''">
            AND (   (UPPER(A.USER_NAME) LIKE
            '%' <include refid="utils.concatenate"/> #{map.searchText} <include refid="utils.concatenate"/> '%' ESCAPE '^' ) OR
            (UPPER(A.USER_ID) LIKE
            '%' <include refid="utils.concatenate"/> #{map.searchText} <include refid="utils.concatenate"/> '%' ESCAPE '^' ))
        </if>
        <if test="map.organizationId != null and map.organizationId != ''">
            AND A.ROOT_GROUP_ID = #{map.organizationId}
        </if>
        <choose>
            <when test="map.sortColumn != null and map.sortColumn != ''">
                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
                order by ${safe_sortColumn}
            </when>
            <otherwise>
                ORDER BY A.user_name
            </otherwise>
        </choose>
        <choose>
            <when test="map.sortOrder != null and map.sortOrder != ''">
                <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
                ${safe_sortOrder}
            </when>
            <otherwise>
                asc
            </otherwise>
        </choose>
    </select>

    <select id="unapprovedUser" resultType="java.lang.String">
        SELECT USER_ID FROM MI_USER_INFO_USER WHERE IS_APPROVED = 'N' AND USER_ID = #{userId}
    </select>
</mapper>
