package com.samsung.magicinfo.auth.security;

import com.samsung.magicinfo.mvc.security.AuthenticationTypeSelector;
import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.filter.GenericFilterBean;

public class AuthenticationTokenFilter extends GenericFilterBean {
   @Autowired
   @Qualifier("extendedDbAuthenticationManager")
   private final AuthenticationManager authenticationManager;
   private final AuthenticationTypeSelector authenticationTypeSelector;
   @Autowired
   private UserDetailsService userDetailsService;

   public AuthenticationTokenFilter() {
      super();
      this.authenticationManager = null;
      this.authenticationTypeSelector = null;
   }

   public AuthenticationTokenFilter(AuthenticationManager authenticationManager, AuthenticationTypeSelector authenticationTypeSelector) {
      super();
      this.authenticationManager = authenticationManager;
      this.authenticationTypeSelector = authenticationTypeSelector;
   }

   public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException {
      HttpServletRequest request = (HttpServletRequest)req;
      HttpServletResponse response = (HttpServletResponse)res;
      String authToken = request.getHeader(TokenUtils.tokenHeader);
      if (authToken != null) {
         TokenUtils tokenUtils = new TokenUtils();
         String username = tokenUtils.getUsernameFromToken(authToken);
         if (username == null) {
            response.sendError(401);
            return;
         }

         if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            UserDetails userDetails = this.userDetailsService.loadUserByUsername(username);
            if (!tokenUtils.validateToken(authToken, userDetails)) {
               response.sendError(401);
               return;
            }

            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, userDetails.getPassword(), userDetails.getAuthorities());
            if (authentication == null) {
               response.sendError(401);
               return;
            }

            Authentication authResult = this.authenticationManager.authenticate(authentication);
            if (authResult == null) {
               response.sendError(401);
               return;
            }

            this.successfulAuthentication(request, response, chain, authResult);
         }
      } else if (request.getHeader("access-control-request-headers") == null || !request.getHeader("access-control-request-headers").contains(TokenUtils.tokenHeader)) {
         response.sendError(401);
         return;
      }

   }

   protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse response, FilterChain chain, Authentication authResult) throws IOException, ServletException {
      SecurityContextHolder.getContext().setAuthentication(authResult);
      chain.doFilter(request, response);
   }
}
