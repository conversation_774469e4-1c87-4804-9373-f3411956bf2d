package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.exception.WebAuthorAbstractException;
import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.UploadResponse;
import com.samsung.magicinfo.webauthor2.service.upload.SingleContentUploadService;
import java.io.IOException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

@Controller
public class ContentUploadController {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.webapi.controller.ContentUploadController.class);
  
  private SingleContentUploadService singleContentUploadService;
  
  @Autowired
  public ContentUploadController(SingleContentUploadService singleContentUploadService) {
    this.singleContentUploadService = singleContentUploadService;
  }
  
  @PostMapping(value = {"/upload"}, consumes = {"multipart/form-data"})
  public HttpEntity<UploadResponse> uploadContent(@RequestParam(defaultValue = "iPLAYER") String playerType, @RequestParam("upload") MultipartFile fileItem) throws UploaderException, FileItemValidationException, IOException {
    String status = this.singleContentUploadService.upload(fileItem, DeviceType.valueOf(playerType));
    return (HttpEntity<UploadResponse>)ResponseEntity.ok(new UploadResponse(200, status));
  }
  
  @PostMapping({"/uploadImportFile"})
  public HttpEntity<UploadResponse> uploadContent(@RequestParam(defaultValue = "iPLAYER") String playerType, @RequestParam String filePath) throws UploaderException, IOException {
    String status = this.singleContentUploadService.upload(filePath, DeviceType.valueOf(playerType));
    return (HttpEntity<UploadResponse>)ResponseEntity.ok(new UploadResponse(200, status));
  }
  
  @ExceptionHandler({WebAuthorAbstractException.class})
  public HttpEntity<UploadResponse> webAuthorExceptionHandler(WebAuthorAbstractException ex) {
    return (HttpEntity<UploadResponse>)ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
      .body(new UploadResponse(ex.getErrorCode(), ex.getMessage()));
  }
  
  @ExceptionHandler({Exception.class})
  public HttpEntity<UploadResponse> unspecifiedInternalServerErrorHandler(Exception ex) {
    logger.error(ex.getMessage());
    return (HttpEntity<UploadResponse>)ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new UploadResponse(699, ex.getMessage()));
  }
}
