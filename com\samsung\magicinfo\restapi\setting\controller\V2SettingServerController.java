package com.samsung.magicinfo.restapi.setting.controller;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.auth.security.MfaService;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.mvc.handler.ApiVersion;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.setting.model.V2CommonConfigResource;
import com.samsung.magicinfo.restapi.setting.model.V2MfaSettings;
import com.samsung.magicinfo.restapi.setting.model.V2PrivacyPolicyResource;
import com.samsung.magicinfo.restapi.setting.model.V2SettingCommonResource;
import com.samsung.magicinfo.restapi.setting.model.V2SettingOrganResource;
import com.samsung.magicinfo.restapi.setting.model.V2SettingServerResource;
import com.samsung.magicinfo.restapi.setting.model.config.V2LdapServerSettings;
import com.samsung.magicinfo.restapi.setting.service.V2SettingServerService;
import com.samsung.magicinfo.restapi.utils.RestAPIUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(
   value = "Setting Management System",
   description = "Operations pertaining to setting in Statistics Management System",
   tags = {"Setting API Group"}
)
@RestController
@RequestMapping({"/restapi/v2.0/ems/settings"})
@Validated
@ApiVersion({2.0D})
public class V2SettingServerController {
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   @Autowired
   private V2SettingServerService v2SettingServerService;
   @Autowired
   private MfaService mfaService;

   public V2SettingServerController() {
      super();
   }

   @ApiOperation(
      value = "Common config information",
      notes = "Common config information",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/servers/common-config"},
      produces = {"application/json"}
   )
   public ResponseEntity getCommonConfig() throws Exception {
      V2CommonConfigResource resource = this.v2SettingServerService.getCommonConfig();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Server Settings",
      notes = "Server settings",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/servers/configurations"},
      produces = {"application/json"}
   )
   public ResponseEntity getServerSettings() throws Exception {
      Long loginUserOrgId = SecurityUtils.getLoginUserOrganizationId();
      if (loginUserOrgId != 0L) {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
      } else {
         V2SettingServerResource resource = this.v2SettingServerService.getServerSettings();
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Server Settings for each organization",
      notes = "Server settings for each organization",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "orgId",
   value = "Organization Id value.",
   required = false,
   dataType = "long",
   example = ""
)})
   @GetMapping(
      value = {"/servers/configurations/{orgId}"},
      produces = {"application/json"}
   )
   public ResponseEntity getOrganSettings(@PathVariable(value = "orgId",required = true) Long orgId) throws Exception {
      V2SettingOrganResource resource = this.v2SettingServerService.getOrganSettings(orgId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Update Main Settings",
      notes = "Update main settings",
      authorizations = {@Authorization("api_key")}
   )
   @PutMapping(
      value = {"/servers/configurations"},
      produces = {"application/json"}
   )
   public ResponseEntity updateServerSettings(@Valid @RequestBody V2SettingServerResource resource) throws Exception {
      V2SettingServerResource updated = this.v2SettingServerService.updateServerSettings(resource);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(updated);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Update Settings",
      notes = "Update settings for each organization",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "orgId",
   value = "Organization Id value.",
   required = true,
   dataType = "long",
   example = "0"
)})
   @PutMapping(
      value = {"/servers/configurations/{orgId}"},
      produces = {"application/json"}
   )
   public ResponseEntity updateServerSettings(@PathVariable(value = "orgId",required = true) Long orgId, @Valid @RequestBody V2SettingOrganResource resource) throws Exception {
      V2SettingOrganResource updated = this.v2SettingServerService.updateOrganSettings(orgId, resource);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(updated);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Check LDAP connection",
      notes = "",
      authorizations = {@Authorization("api_key")}
   )
   @PostMapping(
      value = {"/servers/ldap-connection-check"},
      produces = {"application/json"}
   )
   public ResponseEntity checkLDAPConnection(@Valid @RequestBody V2LdapServerSettings resource) throws Exception {
      Boolean check = this.v2SettingServerService.checkLdapConnection(resource);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(check);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get server log files",
      notes = "get server log files",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "logType",
   value = "The type of log for getting the path and getting the logs based on the log type",
   required = true,
   dataType = "string",
   example = "0"
)})
   @GetMapping(
      value = {"/logs"},
      produces = {"application/json"}
   )
   public ResponseEntity getServerLogFileListWithType(@RequestParam(name = "logType",defaultValue = "LOG_TOMCAT_POPUP") String logType) throws Exception {
      List resultList = this.v2SettingServerService.getServerLogFileList(logType);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resultList);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get server log files",
      notes = "get server log files",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "startIndex",
   value = "Log query starting point.",
   required = true,
   dataType = "int",
   example = "0"
), @ApiImplicitParam(
   name = "pageSize",
   value = "The number of log lists to show.",
   required = true,
   dataType = "int",
   example = "10"
)})
   @GetMapping(
      value = {"/logs/{startIndex}/{pageSize}"},
      produces = {"application/json"}
   )
   public ResponseEntity getServerLogFileList(@PathVariable(value = "startIndex",required = true) Integer startIndex, @PathVariable(value = "pageSize",required = true) Integer pageSize) throws Exception {
      List resultList = this.v2SettingServerService.getServerLogFileList(startIndex, pageSize);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resultList);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Download log file with the log file name",
      notes = "Download log file with the log file name",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "logFileName",
   value = "After checking the log file name, the log file name you want to download",
   required = true,
   dataType = "String"
)})
   @PostMapping(
      value = {"/logs/{logFileName}/download"},
      produces = {"application/json"}
   )
   public ResponseEntity downloadLogFileWithType(@PathVariable("logFileName") String logFileName, @Valid @RequestBody(required = false) String logType, HttpServletRequest request, HttpServletResponse response) throws Exception {
      if (logType == null) {
         logType = "LOG_RUNTIME_POPUP";
      }

      this.v2SettingServerService.downloadLogfile(logFileName, logType, request, response);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(true);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Stop device log collect forcely",
      notes = "Stop device log collect forcely",
      authorizations = {@Authorization("api_key")}
   )
   @PostMapping(
      value = {"/log-collect/stop"},
      produces = {"application/json"}
   )
   public ResponseEntity stopLogCollect() throws Exception {
      this.v2SettingServerService.stopLogCollect();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(true);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get DB Information For Encryption",
      notes = "Get DB Information For Encryption",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/database-info"},
      produces = {"application/json"}
   )
   public ResponseEntity getDatabaseInformation() throws Exception {
      Map resource = this.v2SettingServerService.getDatabaseInformation();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get privacy policy list",
      notes = "Get privacy policy list"
   )
   @GetMapping(
      value = {"/privacy-policy"},
      produces = {"application/json"}
   )
   public ResponseEntity getPrivacyPolicyList() throws Exception {
      List resource = this.v2SettingServerService.getPrivacyPolicyList();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      responseBody.setTotalCount(resource.size());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get privacy policy",
      notes = "Get privacy policy"
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "id",
   value = "After checking the entire policy list, use the id to check the policy of a specific country",
   required = true,
   dataType = "String"
)})
   @GetMapping(
      value = {"/privacy-policy/{id}"},
      produces = {"application/json"}
   )
   public ResponseEntity getPrivacyPolicy(@NotNull @NotEmpty @PathVariable("id") String id) throws Exception {
      V2PrivacyPolicyResource resource = this.v2SettingServerService.getPrivacyPolicy(id);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get Common Information without authorization",
      notes = "Get Common Information"
   )
   @GetMapping(
      value = {"/common-settings"},
      produces = {"application/json"}
   )
   public ResponseEntity getCommonSettings() throws Exception {
      V2SettingCommonResource resource = this.v2SettingServerService.getCommonSettings();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @GetMapping(
      value = {"/mfa/configurations"},
      produces = {"application/json"}
   )
   public ResponseEntity getAuthConfigurations() throws Exception {
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      List mfaList = serverSetupDao.getServerMfaInfo((String)null);
      V2MfaSettings v2MfaSettings = new V2MfaSettings();
      v2MfaSettings.setMfa_enable(this.mfaService.isEnableMfa());
      v2MfaSettings.setMfaList(mfaList);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(v2MfaSettings);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get ICP certificate link Information",
      notes = "Get ICP Information"
   )
   @GetMapping(
      value = {"/icp"},
      produces = {"application/json"}
   )
   public ResponseEntity getIcpInfo() throws Exception {
      Map resource = this.v2SettingServerService.getIcpInfo();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }
}
