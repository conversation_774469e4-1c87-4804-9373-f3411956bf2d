package com.samsung.common.utils;

import com.samsung.common.logger.LoggingManagerV2;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.logging.log4j.Logger;

public class WebUtils {
   static Logger logger = LoggingManagerV2.getLogger(WebUtils.class);

   public WebUtils() {
      super();
   }

   public static String getHTMLString(String str) {
      str = StrUtils.replace(str, "<", "&lt;");
      str = StrUtils.replace(str, ">", "&gt;");
      str = StrUtils.replace(str, "\"", "&quot;");
      str = StrUtils.replace(str, "\n", "<br>\n");
      str = StrUtils.replace(str, "  ", " &nbsp;");
      str = StrUtils.replace(str, "\t", " &nbsp; &nbsp;");
      return str;
   }

   public static String convertCDATAString(String str) {
      str = "<![CDATA[" + str + "]]>";
      return str;
   }

   public static String convertHTMLString(String str) {
      str = str.replace("<", "&lt;");
      str = str.replace(">", "&gt;");
      str = str.replace("\"", "&quot;");
      str = str.replace("\n", "<br>\n");
      str = str.replace("  ", " &nbsp;");
      str = str.replace("\t", " &nbsp; &nbsp;");
      return str;
   }

   public static boolean isImageFile(String filename) {
      int index = false;
      if (filename == null) {
         return false;
      } else {
         int index;
         if ((index = filename.lastIndexOf(".")) == -1) {
            return false;
         } else {
            String extension = filename.substring(index + 1);
            return extension.toLowerCase().equals("bmp") || extension.toLowerCase().equals("pcx") || extension.toLowerCase().equals("gif") || extension.toLowerCase().equals("jpg") || extension.toLowerCase().equals("png");
         }
      }
   }

   public static boolean isAbsoluteIP(String str) {
      Pattern p = Pattern.compile("(\\d{1,3}|[\\*])(\\.(\\d{1,3}|[\\*])){3}");
      Matcher m = p.matcher(str);
      return m.matches();
   }

   public static String makeTag(String str) {
      String rv = "";
      rv = StrUtils.replace(str, " ", "&nbsp;");
      rv = StrUtils.replace(rv, "\n", "&nbsp;<br>");
      return rv;
   }

   public static String replaceRGB(String color_val) {
      String ret_color = "";
      ret_color = color_val.substring(4, 6) + color_val.substring(2, 4) + color_val.substring(0, 2);
      return ret_color;
   }

   public static String replaceColor(String str, String oldStr) {
      int index = 0;
      int j = false;
      int l = 0;
      int strLeng = 0;
      int chkCnt = false;
      String[] chkStr = new String[1000];
      StringBuffer result = new StringBuffer();
      StringTokenizer chk_list_token = new StringTokenizer(str, " ");

      String[] token_list;
      for(token_list = new String[chk_list_token.countTokens()]; index < token_list.length; token_list[index++] = chk_list_token.nextToken()) {
      }

      int j;
      for(int k = 0; k < index; ++k) {
         j = 0;

         int i;
         for(i = 0; i < index - l; ++i) {
            if (i == 0) {
               j = i;
               strLeng = token_list[i].length();
            } else if (token_list[i].length() >= strLeng) {
               j = i;
               strLeng = token_list[i].length();
            }
         }

         chkStr[l] = token_list[j];
         ++l;

         for(i = j; i < index - l; ++i) {
            token_list[i] = token_list[i + 1];
         }
      }

      for(strLeng = 0; strLeng < oldStr.length(); ++strLeng) {
         chkCnt = false;

         for(j = 0; j < index; ++j) {
            if (strLeng + chkStr[j].length() <= oldStr.length() && oldStr.substring(strLeng, strLeng + chkStr[j].length()).toUpperCase().equals(chkStr[j].toUpperCase())) {
               result.append("<font color=#FF7635>" + oldStr.substring(strLeng, strLeng + chkStr[j].length()) + "</font>");
               strLeng = strLeng + chkStr[j].length() - 1;
               chkCnt = true;
            }
         }

         if (!chkCnt) {
            result.append(oldStr.substring(strLeng, strLeng + 1));
         }
      }

      return result.toString();
   }

   public static String getFileName(String filePath) {
      return filePath != null ? SecurityUtils.getSafeFile(filePath).getName() : "";
   }

   public static String addRslash(String str, char token) {
      StringBuffer buffer = new StringBuffer();

      for(int i = 0; str != null && i < str.length(); ++i) {
         char c = str.charAt(i);
         if (c == token) {
            buffer.append("\\'");
         } else {
            buffer.append(c);
         }
      }

      return buffer.toString();
   }

   public static String print(Object o) {
      return o == null ? "" : o.toString();
   }

   public static String getTreeNodeHtml(long count, long groupId) {
      return " <font class='group_count'>" + count + "</font> <div class='menu' role='" + groupId + "'/>";
   }
}
