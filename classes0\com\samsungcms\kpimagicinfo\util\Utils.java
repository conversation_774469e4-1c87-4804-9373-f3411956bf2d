package com.samsungcms.kpimagicinfo.util;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

@Component
public class Utils {
  private static final Logger LOGGER = LogManager.getLogger(com.samsungcms.kpimagicinfo.util.Utils.class);
  
  public static Properties properties = null;
  
  public static final String URL_UPLOAD = "/upload";
  
  public static final String URL_POLICY = "/policy";
  
  public static final String SID = "SID";
  
  public static final String EXT_JSON = ".json";
  
  public static final String NAME_TYPE_SPLITTED = "SPLITTED";
  
  public static final String NAME_TYPE_WEBAUTHOR = "WEBAUTHOR";
  
  public static final String NAME_TYPE_CS_LOG = "CS_LOG";
  
  public static final String NAME_TYPE_CS_SPLITTED = "CS_SPLITTED";
  
  public static final int MEGA_BYTES = 1048576;
  
  public String getDateString(int day) {
    String result = null;
    try {
      Calendar cal = new GregorianCalendar();
      long time = System.currentTimeMillis();
      SimpleDateFormat dayTime = new SimpleDateFormat("yyyy-MM-dd");
      Date date = new Date(time);
      cal.setTime(date);
      cal.add(5, day * -1);
      result = dayTime.format(cal.getTime());
    } catch (Exception e) {
      LOGGER.error(e);
    } 
    return result;
  }
  
  public String getUserKPILogFilePath() {
    String result = null;
    try {
      Properties properties = getConfigProperties();
      result = properties.getProperty("log4j.appender.USERKPI.File");
    } catch (Exception e) {
      LOGGER.error(e);
    } 
    return result;
  }
  
  public Path getUserKPILogFolderPath() {
    Path result = null;
    try {
      String filePath = getUserKPILogFilePath();
      filePath = filePath.replace("KPI.CS.log", "");
      result = Paths.get(filePath, new String[0]);
    } catch (Exception e) {
      LOGGER.error(e);
    } 
    return result;
  }
  
  public String getConfigFilePath() {
    String result = null;
    try {
      String magicInfoHome = System.getenv("MAGICINFO_PREMIUM_HOME");
      result = magicInfoHome + File.separatorChar + "conf" + File.separatorChar + "config.properties";
    } catch (Exception e) {
      LOGGER.error(e);
    } 
    return result;
  }
  
  public Properties getConfigProperties() {
    Properties result = null;
    try {
      String configFile = getConfigFilePath();
      if (properties == null) {
        FileReader resources = new FileReader(configFile);
        properties = new Properties();
        properties.load(resources);
        LOGGER.info("load config file : " + configFile);
      } 
      result = properties;
    } catch (Exception e) {
      LOGGER.error(e);
    } 
    return result;
  }
  
  public String getKPIPolicyFilePath() {
    String result = null;
    try {
      String magicInfoHome = System.getenv("MAGICINFO_PREMIUM_HOME");
      result = magicInfoHome + File.separatorChar + "runtime" + File.separatorChar + "kpiclient" + File.separatorChar + "conf" + File.separatorChar + "kpipolicy.json";
    } catch (Exception e) {
      LOGGER.error(e);
    } 
    return result;
  }
  
  public String getServerID() {
    String result = null;
    try {
      Properties properties = getConfigProperties();
      result = properties.getProperty("privacy_policy.serverid");
    } catch (Exception e) {
      LOGGER.error(e);
    } 
    return result;
  }
  
  public boolean getPrivacyPolicyEnable() {
    try {
      Properties properties = getConfigProperties();
      String enable = properties.getProperty("privacy_policy.enable");
      if ("TRUE".equalsIgnoreCase(enable) || "FALSE".equalsIgnoreCase(enable))
        return Boolean.parseBoolean(enable); 
    } catch (Exception e) {
      LOGGER.error(e);
    } 
    return false;
  }
  
  public boolean validFileName(String fileName, String type) {
    String pattern = null;
    if ("SPLITTED".equalsIgnoreCase(type)) {
      pattern = "(^KPI.(CS|WebAuthor)).(\\S+).([12]\\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01])).(\\d).((log|json)$)";
    } else if ("WEBAUTHOR".equalsIgnoreCase(type)) {
      pattern = "(^KPI.WebAuthor).(\\S+).([12]\\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01])).(\\d).((log|json)$)";
    } else if ("CS_LOG".equalsIgnoreCase(type)) {
      pattern = "(^KPI.CS.log).([12]\\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01]))$";
    } else if ("CS_SPLITTED".equalsIgnoreCase(type)) {
      pattern = "(^KPI.CS).(\\S+).([12]\\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01])).(\\d).((log|json)$)";
    } 
    Pattern r = Pattern.compile(pattern);
    Matcher m = r.matcher(fileName);
    if (m.find())
      return true; 
    return false;
  }
  
  public File renameFile(File file) {
    File newFile = null;
    try {
      String oldFileName = file.getName();
      if (validFileName(oldFileName, "WEBAUTHOR"))
        return file; 
      String[] split = oldFileName.split("\\.");
      if (split.length > 4)
        return file; 
      int INDEX_KPI = 0;
      int INDEX_TYPE = 1;
      int INDEX_DATE = 3;
      String newFileName = String.format("%s.%s.%s.%s", new Object[] { split[0], split[1], getServerID(), split[3] + ".json" });
      String folder = file.getAbsolutePath().replace(file.getName(), "");
      newFile = new File(folder + File.separatorChar + newFileName);
      if (newFile.exists())
        newFile.delete(); 
      Files.move(file.toPath(), newFile.toPath(), new java.nio.file.CopyOption[0]);
    } catch (IOException e) {
      e.printStackTrace();
    } 
    return newFile;
  }
}
