package com.samsung.magicinfo.protocol.http;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.zip.ZipOutputStream;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

public class ContentFileDownloadServlet extends HttpServlet {
   private static final long serialVersionUID = 1L;
   private Logger logger = LoggingManagerV2.getLogger(ContentFileDownloadServlet.class);

   public ContentFileDownloadServlet() {
      super();
   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      this.doPost(request, response);
   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      String userId = (String)request.getSession().getAttribute("userId");
      String contentIdStr = request.getParameter("contentId");
      String[] contentIds = contentIdStr.split(",");
      String downloadPath = null;

      try {
         downloadPath = CommonConfig.get("UPLOAD_HOME") + File.separator + "contents_download";
         File downloadPahHome = SecurityUtils.getSafeFile(downloadPath);
         if (!downloadPahHome.exists()) {
            downloadPahHome.mkdir();
         }
      } catch (ConfigException var133) {
         this.logger.error("", var133);
      }

      String zipFilePath = downloadPath + File.separator + "download.zip";
      FileOutputStream fos = null;
      ZipOutputStream zos = null;
      InputStream inputStream = null;
      InputStreamReader reader = null;

      String filename;
      try {
         fos = new FileOutputStream(zipFilePath);
         zos = new ZipOutputStream(fos);
         String[] var12 = contentIds;
         int var13 = contentIds.length;

         for(int var14 = 0; var14 < var13; ++var14) {
            filename = var12[var14];
            ContentInfoImpl contentInfo = ContentInfoImpl.getInstance();

            try {
               Content content = contentInfo.getContentAndFileActiveVerInfo(filename);
               List fileList = content.getArr_file_list();
               String mainFileId;
               HashMap supportFileItemsMap;
               String fileId;
               String realFullPath;
               if (StringUtils.isNoneBlank(new CharSequence[]{content.getMedia_type()}) && (content.getMedia_type().equalsIgnoreCase("LFD") || content.getMedia_type().equalsIgnoreCase("SAPP"))) {
                  mainFileId = content.getMain_file_id();
                  supportFileItemsMap = new HashMap();

                  int i;
                  String filePath;
                  label1658:
                  for(i = 0; i < fileList.size(); ++i) {
                     if (mainFileId.equalsIgnoreCase(((ContentFile)fileList.get(i)).getFile_id())) {
                        inputStream = null;
                        reader = null;

                        try {
                           XPath xpath = XPathFactory.newInstance().newXPath();
                           filePath = ((ContentFile)fileList.get(i)).getFile_path() + File.separator + ((ContentFile)fileList.get(i)).getFile_name();
                           File file = new File(filePath);
                           inputStream = new FileInputStream(file);
                           reader = new InputStreamReader(inputStream, "UTF-8");
                           InputSource is = new InputSource(reader);
                           Document document = DocumentUtils.getDocumentBuilderFactoryInstance().newDocumentBuilder().parse(is);
                           NodeList cols = (NodeList)xpath.evaluate("/Content/SupportFileItems/FileItem", document, XPathConstants.NODESET);
                           int j = 0;

                           while(true) {
                              if (j >= cols.getLength()) {
                                 break label1658;
                              }

                              Node node = cols.item(j);
                              if (node instanceof Element) {
                                 Element element = (Element)node;
                                 String fileId = element.getElementsByTagName("FileID").item(0).getTextContent();
                                 fileId = element.getElementsByTagName("RealFullPath").item(0).getTextContent();
                                 realFullPath = element.getElementsByTagName("PureFileItem").item(0).getTextContent();
                                 int startIndex = 0;
                                 int endIndex = fileId.lastIndexOf(realFullPath);
                                 if (fileId.indexOf("./") == 0 || fileId.indexOf(".\\") == 0) {
                                    startIndex = 2;
                                 }

                                 if (startIndex <= endIndex - 1) {
                                    fileId = fileId.substring(startIndex, endIndex - 1);
                                    supportFileItemsMap.put(fileId, fileId);
                                 }
                              }

                              ++j;
                           }
                        } catch (Exception var136) {
                           this.logger.error("[ContentFileDownloadServlet] fail file compress", var136);
                           break;
                        } finally {
                           if (reader != null) {
                              reader.close();
                           }

                           if (inputStream != null) {
                              inputStream.close();
                           }

                        }
                     }
                  }

                  for(i = 0; i < fileList.size(); ++i) {
                     ContentFile contentFile = (ContentFile)fileList.get(i);
                     filePath = ((ContentFile)fileList.get(i)).getFile_path() + File.separator + ((ContentFile)fileList.get(i)).getFile_name();
                     if (supportFileItemsMap.containsKey(contentFile.getFile_id())) {
                        CommonUtils.addToZipFile(filePath, (String)supportFileItemsMap.get(contentFile.getFile_id()), zos);
                     } else {
                        CommonUtils.addToZipFile(filePath, zos);
                     }
                  }
               } else if (StringUtils.isNoneBlank(new CharSequence[]{content.getMedia_type()}) && content.getMedia_type().equalsIgnoreCase("HTML")) {
                  mainFileId = content.getMain_file_id();
                  supportFileItemsMap = new HashMap();
                  List contentFileList = new ArrayList();

                  int i;
                  String filePath;
                  label1691:
                  for(i = 0; i < fileList.size(); ++i) {
                     if (mainFileId.equalsIgnoreCase(((ContentFile)fileList.get(i)).getFile_id())) {
                        inputStream = null;
                        reader = null;

                        try {
                           XPath xpath = XPathFactory.newInstance().newXPath();
                           filePath = ((ContentFile)fileList.get(i)).getFile_path() + File.separator + ((ContentFile)fileList.get(i)).getFile_name();
                           File file = new File(filePath);
                           inputStream = new FileInputStream(file);
                           reader = new InputStreamReader(inputStream, "UTF-8");
                           InputSource is = new InputSource(reader);
                           Document document = DocumentBuilderFactory.newInstance().newDocumentBuilder().parse(is);
                           NodeList cols = (NodeList)xpath.evaluate("/Content/SupportFileItems/FileItem", document, XPathConstants.NODESET);
                           int j = 0;

                           while(true) {
                              if (j >= cols.getLength()) {
                                 break label1691;
                              }

                              Node node = cols.item(j);
                              if (node instanceof Element) {
                                 Element element = (Element)node;
                                 fileId = element.getElementsByTagName("FileID").item(0).getTextContent();
                                 realFullPath = element.getElementsByTagName("RealFullPath").item(0).getTextContent();
                                 String pureFileItem = element.getElementsByTagName("PureFileItem").item(0).getTextContent();
                                 int startIndex = 0;
                                 int endIndex = realFullPath.lastIndexOf(pureFileItem);
                                 if (realFullPath.indexOf("./") == 0 || realFullPath.indexOf(".\\") == 0) {
                                    startIndex = 2;
                                 }

                                 if (startIndex <= endIndex - 1) {
                                    realFullPath = realFullPath.substring(startIndex, endIndex - 1);
                                    supportFileItemsMap.put(realFullPath, fileId);
                                    ContentFile contentFileTemp = new ContentFile();
                                    contentFileTemp.setFile_id(fileId);
                                    contentFileTemp.setFile_path(realFullPath);
                                    contentFileTemp.setFile_name(pureFileItem);
                                    contentFileList.add(contentFileTemp);
                                 }
                              }

                              ++j;
                           }
                        } catch (Exception var138) {
                           this.logger.error("[ContentFileDownloadServlet] fail file compress", var138);
                           break;
                        } finally {
                           if (reader != null) {
                              reader.close();
                           }

                           if (inputStream != null) {
                              inputStream.close();
                           }

                        }
                     }
                  }

                  for(i = 0; i < fileList.size(); ++i) {
                     ContentFile contentFile = (ContentFile)fileList.get(i);
                     filePath = ((ContentFile)fileList.get(i)).getFile_path() + File.separator + ((ContentFile)fileList.get(i)).getFile_name();
                     boolean contain = false;

                     for(int j = 0; j < contentFileList.size(); ++j) {
                        if (contentFile.getFile_id().equals(((ContentFile)contentFileList.get(j)).getFile_id())) {
                           CommonUtils.addToZipFile(filePath, ((ContentFile)contentFileList.get(j)).getFile_path(), zos);
                           contain = true;
                        }
                     }

                     if (!contain) {
                        CommonUtils.addToZipFile(filePath, zos);
                     }
                  }
               } else {
                  for(int i = 0; i < fileList.size(); ++i) {
                     String filePath = ((ContentFile)fileList.get(i)).getFile_path() + File.separator + ((ContentFile)fileList.get(i)).getFile_name();
                     CommonUtils.addToZipFile(filePath, zos);
                  }
               }
            } catch (SQLException var140) {
               this.logger.error("[ContentFileDownloadServlet] fail file compress", var140);
            }
         }
      } catch (Exception var141) {
         this.logger.error("", var141);
      } finally {
         if (zos != null) {
            try {
               zos.close();
            } catch (Exception var130) {
            }
         }

         if (fos != null) {
            try {
               fos.close();
            } catch (Exception var129) {
            }
         }

         if (reader != null) {
            try {
               reader.close();
            } catch (Exception var128) {
            }
         }

         if (inputStream != null) {
            try {
               inputStream.close();
            } catch (Exception var127) {
            }
         }

      }

      File file = new File(SecurityUtils.directoryTraversalChecker(zipFilePath, (String)null));
      String mime = "application/zip;";
      byte[] buffer = new byte[1024];
      filename = "download.zip";
      response.setContentType(mime);
      String userAgent = request.getHeader("User-Agent");
      if (userAgent != null && userAgent.indexOf("MSIE 5.5") > -1) {
         response.setHeader("Content-Disposition", "filename=" + URLEncoder.encode(filename, "UTF-8") + ";");
      } else if (userAgent != null && userAgent.indexOf("MSIE") > -1) {
         response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(filename, "UTF-8") + ";");
      } else {
         response.setHeader("Content-Disposition", "attachment; filename=" + new String(filename.getBytes("utf-8"), "latin1") + ";");
      }

      response.setHeader("Set-Cookie", "fileDownload=true; path=/");
      BufferedInputStream fin = null;
      BufferedOutputStream outs = null;

      try {
         InputStream is = new FileInputStream(file);
         fin = new BufferedInputStream(is);
         outs = new BufferedOutputStream(response.getOutputStream());
         boolean var153 = false;

         int read;
         while((read = fin.read(buffer)) != -1) {
            outs.write(buffer, 0, read);
         }
      } catch (IOException var134) {
      } finally {
         try {
            outs.close();
         } catch (Exception var132) {
            this.logger.error("", var132);
         }

         try {
            fin.close();
         } catch (Exception var131) {
         }

         response.setHeader("Content-Length", String.valueOf(filename.length()));
      }

   }
}
