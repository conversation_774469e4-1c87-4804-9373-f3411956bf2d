package com.samsung.magicinfo.auth.security;

import com.samsung.magicinfo.auth.model.AuthenticationResource;
import com.samsung.magicinfo.auth.security.otp.OTPAuthType;
import com.samsung.magicinfo.auth.security.otp.UserAuthDevice;
import com.samsung.magicinfo.auth.security.state.AuthState;
import com.samsung.magicinfo.auth.security.state.AuthStateContext;
import com.samsung.magicinfo.auth.security.state.impl.ValidAuthState;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Component;

@Component("MfaService")
public class MfaService {
   protected final Log logger = LogFactory.getLog(this.getClass());
   public static final List OTP_EXCEPTION_LIST;

   public MfaService() {
      super();
   }

   public boolean isValidMfa(AuthenticationResource resource) throws Exception {
      if (!this.isEnableMfa()) {
         return false;
      } else {
         try {
            AuthResource authResource = new AuthResource();
            authResource.setUsername(resource.getUsername());
            authResource.setHotp(resource.getHotp());
            authResource.setTotp(resource.getTotp());
            AuthStateContext authStateContext;
            if (resource.getUserAuthDevice() != null && resource.getUserAuthDevice().isAuth_enable()) {
               if (resource.getTotp() == null || resource.getTotp().isEmpty()) {
                  throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_REQUIRED_OTP_VALUE);
               }

               authStateContext = new AuthStateContext();
               authStateContext.setState(new ValidAuthState());
               authStateContext.auth(authResource);
               UserAuthDevice userAuthDevice = this.getUserAuthDevice(resource);
               authResource.setUserAuthDevice(userAuthDevice);
            }

            authStateContext = new AuthStateContext();
            AuthState authState = authStateContext.getState(authResource);
            OTPAuthType otpAuthType = authState.auth(authResource);
            if (otpAuthType != null) {
               Map details = new HashMap();
               details.put("otpAuthInfo", otpAuthType);
               throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_REQUIRED_OTP_SETTING, details);
            } else {
               return true;
            }
         } catch (Exception var7) {
            throw var7;
         }
      }
   }

   private UserAuthDevice getUserAuthDevice(AuthenticationResource resource) {
      UserAuthDevice userAuthDevice = new UserAuthDevice();
      userAuthDevice.setBrowser_name(resource.getUserAuthDevice().getBrowser_name());
      userAuthDevice.setBrowser_version(resource.getUserAuthDevice().getBrowser_version());
      userAuthDevice.setOs_name(resource.getUserAuthDevice().getOs_name());
      userAuthDevice.setOs_version(resource.getUserAuthDevice().getOs_version());
      return userAuthDevice;
   }

   public boolean isEnableMfa() {
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map infoMap = serverSetupDao.getServerCommonInfo();
      boolean mfaEnable = false;
      if (infoMap.get("MFA_ENABLE") != null) {
         mfaEnable = (Boolean)infoMap.get("MFA_ENABLE");
      }

      return mfaEnable;
   }

   static {
      OTP_EXCEPTION_LIST = Arrays.asList(RestExceptionCode.UNAUTHORIZED_REQUIRED_OTP_VALUE.getCode(), RestExceptionCode.UNAUTHORIZED_REQUIRED_OTP_SETTING.getCode(), RestExceptionCode.UNAUTHORIZED_HOTP_INVALID.getCode(), RestExceptionCode.UNAUTHORIZED_TOTP_INVALID.getCode(), RestExceptionCode.UNAUTHORIZED_TOTP_WARNING.getCode(), RestExceptionCode.UNAUTHORIZED_TOTP_LOCK.getCode(), RestExceptionCode.WRONG_FORMAT_OTP_VALUE.getCode());
   }
}
