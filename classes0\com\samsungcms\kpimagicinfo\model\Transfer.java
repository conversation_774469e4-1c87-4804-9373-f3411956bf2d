package com.samsungcms.kpimagicinfo.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Transfer {
  @JsonProperty("period")
  private int period = 7;
  
  @JsonProperty("time")
  private String time = "01:00";
  
  @JsonProperty("used")
  private boolean used = true;
  
  public int getPeriod() {
    return this.period;
  }
  
  public void setPeriod(int period) {
    this.period = period;
  }
  
  public String getTime() {
    return this.time;
  }
  
  public void setTime(String time) {
    this.time = time;
  }
  
  public boolean isUsed() {
    return this.used;
  }
  
  public void setUsed(boolean used) {
    this.used = used;
  }
}
