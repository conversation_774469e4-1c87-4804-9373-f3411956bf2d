package com.samsung.magicinfo.framework.setup.manager;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.magicinfo.framework.setup.entity.DatalinkServerEntity;
import com.samsung.magicinfo.framework.setup.entity.DatalinkServerTableEntity;
import com.samsung.magicinfo.framework.setup.entity.DatalinkTableItem;
import com.samsung.magicinfo.framework.setup.entity.DatalinkTableItemList;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import io.netty.handler.timeout.ReadTimeoutException;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.StringReader;
import java.net.Socket;
import java.net.URL;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.sql.SQLException;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.KeyManager;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509ExtendedTrustManager;
import javax.net.ssl.X509TrustManager;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import javax.xml.parsers.FactoryConfigurationError;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

public class DatalinkServerManager {
   protected static Logger logger = LoggingManagerV2.getLogger(DatalinkServerManager.class);

   public DatalinkServerManager() {
      super();
   }

   public static String updateDatalinkServerTable(DatalinkServerEntity datalinkServer) throws ResourceAccessException, ReadTimeoutException {
      DatalinkServerImpl dlsDao = DatalinkServerImpl.getInstance();
      String getDatalinkTableAPI = "getDataTableList.do";
      String https = datalinkServer.getUse_ssl() ? "https://" : "http://";
      String url = null;
      String body = null;
      Boolean usePrivate = datalinkServer.getPrivate_mode();
      int itemlistSize = false;
      if (usePrivate) {
         url = https + datalinkServer.getPrivate_ip_address() + ":" + datalinkServer.getPrivate_web_port() + "/DataLink/html/" + getDatalinkTableAPI;
      } else {
         url = https + datalinkServer.getIp_address() + ":" + datalinkServer.getPort() + "/DataLink/html/" + getDatalinkTableAPI;
      }

      DatalinkServerTableEntity dlkTableEntity = new DatalinkServerTableEntity();
      RestTemplate restTemplate = getRestTemplate();
      if (datalinkServer.getUse_ssl()) {
         body = updateDatalinkHttps(url);
      } else {
         body = updateDatalinkHttp(restTemplate, url);
      }

      if (body == "fail_to_connect") {
         return "fail_to_connect";
      } else if (body != null && !body.isEmpty() && body != "") {
         try {
            JAXBContext jaxbContext = JAXBContext.newInstance(new Class[]{DatalinkTableItemList.class});
            Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();

            try {
               XMLStreamReader reader = DocumentUtils.getXMLInputFactoryInstance().createXMLStreamReader(new StringReader(body));
               JAXBElement datalinkTableItemList = jaxbUnmarshaller.unmarshal(reader, DatalinkTableItemList.class);
               if (datalinkTableItemList == null || datalinkTableItemList.getValue() == null || ((DatalinkTableItemList)datalinkTableItemList.getValue()).getItem() == null) {
                  return "empty";
               }

               int itemlistSize = ((DatalinkTableItemList)datalinkTableItemList.getValue()).getItem().size();
               if (itemlistSize <= 0) {
                  return "empty";
               }

               for(int i = 0; i < itemlistSize; ++i) {
                  String serviceName = ((DatalinkTableItem)((DatalinkTableItemList)datalinkTableItemList.getValue()).getItem().get(i)).getSvrcName();
                  String dynaName = ((DatalinkTableItem)((DatalinkTableItemList)datalinkTableItemList.getValue()).getItem().get(i)).getDynaName();
                  String tableName = ((DatalinkTableItem)((DatalinkTableItemList)datalinkTableItemList.getValue()).getItem().get(i)).getName();
                  Boolean isDataView = serviceName == null;

                  try {
                     if (dynaName != null && tableName != null) {
                        dlkTableEntity.setIs_dataView(isDataView);
                        dlkTableEntity.setServer_name(datalinkServer.getServer_name());
                        if (serviceName == null) {
                           serviceName = tableName;
                        }

                        dlkTableEntity.setService_name(serviceName);
                        dlkTableEntity.setTable_name(tableName);
                        dlkTableEntity.setDyna_name(dynaName);
                        dlsDao.addDatalinkTableInfo(dlkTableEntity);
                     }
                  } catch (SQLException var20) {
                     logger.error("Error in method updateDatalinkServerTable " + var20.getMessage());
                  }
               }
            } catch (FactoryConfigurationError | XMLStreamException var21) {
               logger.error("Error in method updateDatalinkServerTable Unmarshall xml XMLStreamException" + var21.getMessage());
            }
         } catch (JAXBException var22) {
            logger.error("Error in method updateDatalinkServerTable Unmarshall xml JAXBException " + var22.getMessage());
         }

         return "success";
      } else {
         return "empty";
      }
   }

   private static String updateDatalinkHttp(RestTemplate restTemplate, String url) throws ResourceAccessException, ReadTimeoutException {
      HttpHeaders headers = new HttpHeaders();
      headers.set("Accept", "text/plain;charset=utf-8");
      HttpEntity entity = new HttpEntity(headers);
      ResponseEntity responseData = restTemplate.exchange(url, HttpMethod.GET, entity, String.class, new Object[0]);
      return (String)responseData.getBody();
   }

   private static String updateDatalinkHttps(String datalinkUrl) throws ResourceAccessException, ReadTimeoutException {
      String body = new String();
      HttpHeaders headers = new HttpHeaders();
      headers.set("Accept", "text/plain;charset=utf-8");
      HttpEntity entity = new HttpEntity(headers);

      try {
         URL url = new URL(datalinkUrl);
         HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
         conn.setHostnameVerifier(new HostnameVerifier() {
            public boolean verify(String hostname, SSLSession session) {
               return true;
            }
         });
         SSLContext context = SSLContext.getInstance("TLS");
         TrustManager[] trustAllCerts = new TrustManager[]{new X509ExtendedTrustManager() {
            public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
            }

            public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
               FileInputStream fis = null;

               try {
                  String keyStoreIdentityPath = "";
                  String keyStoreIdentityPassword = "";
                  keyStoreIdentityPath = CommonConfig.get("keystore.identity.path");
                  keyStoreIdentityPassword = CommonConfig.get("keystore.identity.password");
                  KeyStore trustStore = KeyStore.getInstance("JKS");
                  fis = new FileInputStream(keyStoreIdentityPath);
                  trustStore.load(fis, keyStoreIdentityPassword.toCharArray());
                  fis.close();
                  TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
                  tmf.init(trustStore);
                  TrustManager[] tms = tmf.getTrustManagers();
                  ((X509TrustManager)tms[0]).checkServerTrusted(chain, authType);
               } catch (KeyStoreException var23) {
                  DatalinkServerManager.logger.error("External Server Monitoring - KeyStore Exception");
               } catch (NoSuchAlgorithmException var24) {
                  DatalinkServerManager.logger.error("External Server Monitoring - No Such Algorithm Exception");
               } catch (IOException var25) {
                  DatalinkServerManager.logger.error("External Server Monitoring - Input Output Exception");
               } catch (ConfigException var26) {
                  DatalinkServerManager.logger.error("", var26);
               } finally {
                  try {
                     fis.close();
                  } catch (IOException var22) {
                     DatalinkServerManager.logger.error("External Server Monitoring - FIS IOException");
                  }

               }

            }

            public X509Certificate[] getAcceptedIssuers() {
               return null;
            }

            public void checkClientTrusted(X509Certificate[] x509Certificates, String s, Socket socket) throws CertificateException {
            }

            public void checkServerTrusted(X509Certificate[] x509Certificates, String s, Socket socket) throws CertificateException {
            }

            public void checkClientTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) throws CertificateException {
            }

            public void checkServerTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) throws CertificateException {
            }
         }};
         context.init((KeyManager[])null, trustAllCerts, (SecureRandom)null);
         SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(context, NoopHostnameVerifier.INSTANCE);
         Registry registry = RegistryBuilder.create().register("http", new PlainConnectionSocketFactory()).register("https", csf).build();
         PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager(registry);
         cm.setMaxTotal(100);
         CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(csf).setConnectionManager(cm).build();
         HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
         requestFactory.setHttpClient(httpClient);
         RestTemplate restTemplate = new RestTemplate(requestFactory);
         ResponseEntity response = restTemplate.exchange(datalinkUrl, HttpMethod.GET, entity, String.class, new Object[0]);
         body = (String)response.getBody();
      } catch (Exception var15) {
         logger.error("updateDatalinkHttps error : " + var15.getMessage());
      }

      return body;
   }

   private static RestTemplate getRestTemplate() {
      HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
      factory.setConnectTimeout(1000);
      factory.setReadTimeout(1000);
      RestTemplate restTemplate = new RestTemplate(factory);
      return restTemplate;
   }
}
