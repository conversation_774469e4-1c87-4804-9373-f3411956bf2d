package com.samsung.magicinfo.restapi.setting.service;

import com.google.gson.Gson;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.ExternalSystemUtils;
import com.samsung.common.utils.FileUtils;
import com.samsung.common.utils.LDAPUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.common.manager.EncryptionManager;
import com.samsung.magicinfo.framework.common.manager.EncryptionManagerImpl;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceLogCollectEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfo;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfoImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManager;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManagerImpl;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.setup.dao.InsightServerDao;
import com.samsung.magicinfo.framework.setup.dao.LdapServerDao;
import com.samsung.magicinfo.framework.setup.dao.LoginPageDao;
import com.samsung.magicinfo.framework.setup.entity.ESLServerEntity;
import com.samsung.magicinfo.framework.setup.entity.InsightServerEntity;
import com.samsung.magicinfo.framework.setup.entity.LoginPageEntity;
import com.samsung.magicinfo.framework.setup.manager.ConfigEncryptionManagement;
import com.samsung.magicinfo.framework.setup.manager.EslServerInfo;
import com.samsung.magicinfo.framework.setup.manager.EslServerInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.statistics.dao.DeviceConnectionDao;
import com.samsung.magicinfo.framework.user.entity.UserLdapSync;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserLdapSyncInfo;
import com.samsung.magicinfo.framework.user.manager.UserLdapSyncInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserPasswordManager;
import com.samsung.magicinfo.framework.user.manager.UserPasswordManagerImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.scheduler.ScheduleManager;
import com.samsung.magicinfo.protocol.util.BeanUtils;
import com.samsung.magicinfo.protocol.util.EmailAlarmJob;
import com.samsung.magicinfo.protocol.util.ExternalServerMonitoringJob;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.setting.model.V2CommonConfigResource;
import com.samsung.magicinfo.restapi.setting.model.V2PrivacyPolicyResource;
import com.samsung.magicinfo.restapi.setting.model.V2SettingCommonResource;
import com.samsung.magicinfo.restapi.setting.model.V2SettingLogFileResource;
import com.samsung.magicinfo.restapi.setting.model.V2SettingOrganResource;
import com.samsung.magicinfo.restapi.setting.model.V2SettingServerResource;
import com.samsung.magicinfo.restapi.setting.model.config.MFA;
import com.samsung.magicinfo.restapi.setting.model.config.V2AlarmMailingSettings;
import com.samsung.magicinfo.restapi.setting.model.config.V2AutoTimezoneSettings;
import com.samsung.magicinfo.restapi.setting.model.config.V2ExternalServerSettings;
import com.samsung.magicinfo.restapi.setting.model.config.V2GeneralSettings;
import com.samsung.magicinfo.restapi.setting.model.config.V2InsightServerSettings;
import com.samsung.magicinfo.restapi.setting.model.config.V2LdapServerSettings;
import com.samsung.magicinfo.restapi.setting.model.config.V2LogManagementSettings;
import com.samsung.magicinfo.restapi.setting.model.config.V2LoginPageSettings;
import com.samsung.magicinfo.restapi.setting.model.config.V2OrganAlarmMailingSettings;
import com.samsung.magicinfo.restapi.setting.model.config.V2OrganGeneralSettings;
import com.samsung.magicinfo.restapi.setting.model.config.V2PasswordPolicySettings;
import com.samsung.magicinfo.restapi.setting.model.config.V2PrivacyPolicySettings;
import com.samsung.magicinfo.restapi.setting.model.config.V2SmtpServerSettings;
import com.samsung.magicinfo.restapi.setting.model.config.V2StatisticsDataSettings;
import com.samsung.magicinfo.restapi.setting.model.config.V2ThirdpartServiceSettings;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManager;
import edu.emory.mathcs.backport.java.util.Arrays;
import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.StringTokenizer;
import java.util.concurrent.atomic.AtomicReference;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.quartz.JobDetail;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SimpleTrigger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2SettingServerService")
@Transactional
public class V2SettingServerServiceImpl implements V2SettingServerService {
   protected Logger logger = LoggingManagerV2.getLogger(V2SettingServerServiceImpl.class);
   protected Logger pplogger = LoggingManagerV2.getLogger("PP_LOGGER");
   final String PRIVACY_POLICY_FORLDER_NAME = "privacy_policy";
   final String PROSERVICE_PRIVACY_POLICY_FORLDER_NAME = "ProService";
   final String PRIVACY_POLICY_FILE_NOTICE = "notice.htm";
   final String PRIVACY_POLICY_FILE_AGREE = "agree.htm";
   final String PRIVACY_POLICY_FILE_NOTICE_AGREE = "noticeagree.htm";
   private final String DOWNLOAD_LOGS_FOLDER = "downloadlogs";
   int PRIVACY_POLICY_FOLDER_NAME_INDEX_ID = 0;
   int PRIVACY_POLICY_FOLDER_NAME_INDEX_LOCATION = 1;
   ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
   final String[] ENCRYPTION_PROPERTY_KEYS = new String[]{"wsrm.username", "wsrm.password", "org.quartz.dataSource.databaseDS.user", "org.quartz.dataSource.databaseDS.password"};

   public V2SettingServerServiceImpl() {
      super();
   }

   public V2CommonConfigResource getCommonConfig() throws Exception {
      new Gson();
      Map settingsMap = this.serverSetupDao.getServerInfoByOrgId(CommonDataConstants.ROOT_ID);
      V2GeneralSettings general = this.getGeneralSettings(settingsMap);
      InsightServerDao insightServerDao = new InsightServerDao();
      V2InsightServerSettings insightServerSettings = new V2InsightServerSettings();
      InsightServerEntity insightServerEntity = insightServerDao.getInsightServerInfo();
      if (insightServerEntity != null) {
         insightServerSettings.setFromInsightServerEntity(insightServerEntity);
      }

      V2PrivacyPolicySettings privacyPolicy = this.getPrivacyPolicySettings(settingsMap);
      V2CommonConfigResource resource = new V2CommonConfigResource();
      resource.setGeneral(general);
      resource.setPrivacyPolicy(privacyPolicy);
      resource.setInsightServer(insightServerSettings);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public V2SettingServerResource getServerSettings() throws Exception {
      Gson gson = new Gson();
      PIIDataManager piiDataManager = (PIIDataManager)BeanUtils.getBean("PIIDataManager");
      Map settingsMap = this.serverSetupDao.getServerInfoByOrgId(CommonDataConstants.ROOT_ID);
      V2GeneralSettings general = this.getGeneralSettings(settingsMap);
      V2PasswordPolicySettings passwordPolicy = (V2PasswordPolicySettings)gson.fromJson(gson.toJson(settingsMap), V2PasswordPolicySettings.class);
      List mfaList = this.serverSetupDao.getServerMfaInfo((String)null);
      passwordPolicy.setMfa_list(mfaList);
      V2LogManagementSettings logManagement = this.getLogManagementSettings(settingsMap);
      V2SmtpServerSettings smtpServer = (V2SmtpServerSettings)gson.fromJson(gson.toJson(settingsMap), V2SmtpServerSettings.class);
      boolean isUsedSmtpSettings = this.serverSetupDao.hasApplySmtpServerSetting();
      smtpServer.setIs_used_settings(isUsedSmtpSettings);
      if (smtpServer.getSmtp_auth_id() != null) {
         smtpServer.setSmtp_auth_id(piiDataManager.decryptData(smtpServer.getSmtp_auth_id()));
      }

      V2AlarmMailingSettings alarmMailing = (V2AlarmMailingSettings)gson.fromJson(gson.toJson(settingsMap), V2AlarmMailingSettings.class);
      if (alarmMailing.getDisconnect_start_time() != null && alarmMailing.getDisconnect_end_time() != null) {
         SimpleDateFormat date24Format = new SimpleDateFormat("HH:mm");
         alarmMailing.setAlarm_start_time(date24Format.format(alarmMailing.getDisconnect_start_time()));
         alarmMailing.setAlarm_end_time(date24Format.format(alarmMailing.getDisconnect_end_time()));
      }

      LdapServerDao ldapServerDao = new LdapServerDao();
      Map ldapMap = ldapServerDao.getLdapServerInfo(CommonDataConstants.ROOT_ID);
      if (ldapMap != null) {
         settingsMap.putAll(ldapMap);
      }

      V2LdapServerSettings ldapServer = this.getLdapServerSetting(settingsMap);
      boolean isUsedSettings = this.serverSetupDao.hasApplyLdapServerSetting();
      ldapServer.setIs_used_settings(isUsedSettings);
      InsightServerDao insightServerDao = new InsightServerDao();
      V2InsightServerSettings insightServerSettings = new V2InsightServerSettings();
      InsightServerEntity insightServerEntity = insightServerDao.getInsightServerInfo();
      if (insightServerEntity != null) {
         insightServerSettings.setFromInsightServerEntity(insightServerEntity);
      }

      LoginPageDao loginPageDao = new LoginPageDao();
      V2LoginPageSettings loginPageSettings = new V2LoginPageSettings();
      LoginPageEntity loginPageEntity = loginPageDao.getLoginPageInfo();
      loginPageSettings.setFromLoginPageEntity(loginPageEntity);
      V2ExternalServerSettings externalServer = (V2ExternalServerSettings)gson.fromJson(gson.toJson(settingsMap), V2ExternalServerSettings.class);
      V2StatisticsDataSettings statisticsData = this.getStatisticsDataSetting(settingsMap);
      V2AutoTimezoneSettings autoTimezone = this.getAutoTimezoneSetting(settingsMap);
      V2ThirdpartServiceSettings thirdpartService = this.getThirdpartySetting(settingsMap);
      V2PrivacyPolicySettings privacyPolicy = this.getPrivacyPolicySettings(settingsMap);
      V2SettingServerResource resource = new V2SettingServerResource();
      resource.setGeneral(general);
      resource.setPasswordPolicy(passwordPolicy);
      resource.setLogManagement(logManagement);
      resource.setSmtpServer(smtpServer);
      resource.setAlarmMailing(alarmMailing);
      resource.setLdapServer(ldapServer);
      resource.setExternalServer(externalServer);
      resource.setStatisticsData(statisticsData);
      resource.setAutoTimezone(autoTimezone);
      resource.setThirdpartService(thirdpartService);
      resource.setPrivacyPolicy(privacyPolicy);
      resource.setInsightServer(insightServerSettings);
      resource.setLoginPage(loginPageSettings);
      return resource;
   }

   private V2PrivacyPolicySettings getPrivacyPolicySettings(Map settingsMap) {
      Gson gson = new Gson();
      V2PrivacyPolicySettings settings = (V2PrivacyPolicySettings)gson.fromJson(gson.toJson(settingsMap), V2PrivacyPolicySettings.class);

      try {
         Map kpiMap = this.serverSetupDao.getServerSetupKpi();
         if (null == kpiMap) {
            return settings;
         }

         if (null != kpiMap.get("KPI_ENABLE")) {
            settings.setPrivacy_policy_enable((Boolean)kpiMap.get("KPI_ENABLE"));
         }

         if (null != kpiMap.get("LOCATION")) {
            settings.setPrivacy_policy_location((String)kpiMap.get("LOCATION"));
         }
      } catch (Exception var5) {
         this.logger.error(var5);
      }

      return settings;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public V2SettingOrganResource getOrganSettings(Long orgId) throws Exception {
      Long loginUserOrgId = SecurityUtils.getLoginUserOrganizationId();
      if (!orgId.equals(loginUserOrgId) && loginUserOrgId != 0L) {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
      } else {
         Gson gson = new Gson();
         Map settingsMap = this.serverSetupDao.getServerInfoByOrgId(orgId);
         V2OrganGeneralSettings general = (V2OrganGeneralSettings)gson.fromJson(gson.toJson(settingsMap), V2OrganGeneralSettings.class);
         UserInfo userInfo = UserInfoImpl.getInstance();
         List approverList = userInfo.getContentManagerUserListByOrgId(orgId);
         if (approverList != null && approverList.size() > 0) {
            List contentManagers = new ArrayList();

            V2OrganGeneralSettings.ContentManager contentManager;
            for(Iterator var9 = approverList.iterator(); var9.hasNext(); contentManagers.add(contentManager)) {
               Map approver = (Map)var9.next();
               contentManager = (V2OrganGeneralSettings.ContentManager)gson.fromJson(gson.toJson(approver), V2OrganGeneralSettings.ContentManager.class);
               Long groupId = contentManager.getGroup_id();
               UserLdapSync userLdapSync = UserLdapSyncInfoImpl.getInstance().getUserLdapSyncByGroupId(groupId);
               if (userLdapSync != null) {
                  contentManager.setLdap_sync(true);
               } else {
                  contentManager.setLdap_sync(false);
               }
            }

            general.setContent_approvers(contentManagers);
         }

         V2SmtpServerSettings smtpServer = (V2SmtpServerSettings)gson.fromJson(gson.toJson(settingsMap), V2SmtpServerSettings.class);
         V2OrganAlarmMailingSettings alarmMailing = (V2OrganAlarmMailingSettings)gson.fromJson(gson.toJson(settingsMap), V2OrganAlarmMailingSettings.class);
         if (alarmMailing.getDisconnect_start_time() != null && alarmMailing.getDisconnect_end_time() != null) {
            SimpleDateFormat date24Format = new SimpleDateFormat("HH:mm");
            alarmMailing.setAlarm_start_time(date24Format.format(alarmMailing.getDisconnect_start_time()));
            alarmMailing.setAlarm_end_time(date24Format.format(alarmMailing.getDisconnect_end_time()));
         }

         if (alarmMailing.getAlarm_groups_notify_type() != null && alarmMailing.getAlarm_groups_notify_type() == 1) {
            DeviceGroupInfo deviceGroup = DeviceGroupInfoImpl.getInstance();
            DeviceGroup deviceOrgan = deviceGroup.getDeviceOrgGroupByUserOrgId(orgId);
            List alarmGroupList = deviceGroup.getAlarmDeviceGroupList(deviceOrgan.getGroup_id());
            if (alarmGroupList != null && alarmGroupList.size() > 0) {
               List targetGroups = new ArrayList();
               Iterator var14 = alarmGroupList.iterator();

               while(var14.hasNext()) {
                  DeviceGroup group = (DeviceGroup)var14.next();
                  targetGroups.add(group.getGroup_id());
               }

               alarmMailing.setAlarm_target_groupIds(targetGroups);
            }
         }

         try {
            LdapServerDao ldapServerDao = new LdapServerDao();
            new HashMap();
            Map ldapMap;
            if (Boolean.valueOf(settingsMap.get("LDAP_USE_SERVER_SETTING").toString())) {
               ldapMap = ldapServerDao.getLdapServerInfo(CommonDataConstants.ROOT_ID);
            } else {
               ldapMap = ldapServerDao.getLdapServerInfo(orgId);
            }

            settingsMap.putAll(ldapMap);
         } catch (Exception var19) {
            this.logger.error("", var19);
         }

         V2LdapServerSettings ldapServer = this.getLdapServerSetting(settingsMap);
         EslServerInfo eslInfo = EslServerInfoImpl.getInstance();
         ESLServerEntity eslServer = eslInfo.getESLServerInfo(orgId);
         Map mainSettingsMap = this.serverSetupDao.getServerInfoByOrgId(CommonDataConstants.ROOT_ID);
         V2SmtpServerSettings mainSmtpServer = (V2SmtpServerSettings)gson.fromJson(gson.toJson(mainSettingsMap), V2SmtpServerSettings.class);
         LdapServerDao ldapServerDao = new LdapServerDao();
         Map ldapMap = ldapServerDao.getLdapServerInfo(CommonDataConstants.ROOT_ID);
         if (ldapMap != null) {
            mainSettingsMap.putAll(ldapMap);
         }

         V2LdapServerSettings mainLdapServer = this.getLdapServerSetting(mainSettingsMap);
         V2SettingOrganResource resource = new V2SettingOrganResource();
         resource.setGeneral(general);
         resource.setSmtpServer(smtpServer);
         resource.setAlarmMailing(alarmMailing);
         resource.setLdapServer(ldapServer);
         resource.setEslServer(eslServer);
         resource.setSmtpServerMain(mainSmtpServer);
         resource.setLdapServerMain(mainLdapServer);
         return resource;
      }
   }

   private V2LdapServerSettings getLdapServerSetting(Map settingsMap) throws Exception {
      Gson gson = new Gson();
      V2LdapServerSettings ldapServer = (V2LdapServerSettings)gson.fromJson(gson.toJson(settingsMap), V2LdapServerSettings.class);
      if (ldapServer.getLdap_server() != null) {
         String protocol = "";
         if (ldapServer.getLdap_server().indexOf("ldap://") == 0) {
            protocol = "ldap://";
            ldapServer.setUse_ldap_ssl(false);
         } else if (ldapServer.getLdap_server().indexOf("ldaps://") == 0) {
            protocol = "ldaps://";
            ldapServer.setUse_ldap_ssl(true);
         }

         ldapServer.setLdap_server(ldapServer.getLdap_server().substring(protocol.length()));
      }

      if (ldapServer.getLdap_manager_password() != null) {
         ldapServer.setLdap_manager_password(SecurityUtils.getDecryptionPassword(ldapServer.getLdap_manager_password(), ldapServer.getLdap_enc_version()));
      }

      return ldapServer;
   }

   private V2ThirdpartServiceSettings getThirdpartySetting(Map settingsMap) throws ConfigException, SQLException {
      Gson gson = new Gson();
      V2ThirdpartServiceSettings thirdpartService = (V2ThirdpartServiceSettings)gson.fromJson(gson.toJson(settingsMap), V2ThirdpartServiceSettings.class);
      if (CommonConfig.get("thingworx.update.enable") != null) {
         thirdpartService.setUse_noc_update(Boolean.valueOf(CommonConfig.get("thingworx.update.enable")));
      }

      if (CommonConfig.get("thingworx.update.url") != null) {
         thirdpartService.setNoc_module_url(CommonConfig.get("thingworx.update.url"));
      }

      if (CommonConfig.get("rulemanager.enable") != null) {
         thirdpartService.setUse_rule_manager(Boolean.valueOf(CommonConfig.get("rulemanager.enable")));
      }

      if (CommonConfig.get("rulemanager.url") != null) {
         thirdpartService.setRule_manager_url(CommonConfig.get("rulemanager.url"));
      }

      if (CommonConfig.get("rulemanager.apiKey") != null) {
         thirdpartService.setRule_manager_apikey(CommonConfig.get("rulemanager.apiKey"));
      }

      ServerSetupInfo serverSetupInfo = ServerSetupInfoImpl.getInstance();
      List orgList = serverSetupInfo.getRuleMangerEnabledOrgID();
      List organResources = new ArrayList();
      if (orgList != null && orgList.size() > 0) {
         Iterator var7 = orgList.iterator();

         while(var7.hasNext()) {
            Map org = (Map)var7.next();
            V2ThirdpartServiceSettings.Organization organ = new V2ThirdpartServiceSettings.Organization();
            organ.setGroup_name(org.get("group_name").toString());
            organ.setOrganization_id(Long.valueOf(org.get("organization_id").toString()));
            organResources.add(organ);
         }

         thirdpartService.setRule_manager_organs(organResources);
      }

      return thirdpartService;
   }

   private V2AutoTimezoneSettings getAutoTimezoneSetting(Map settingsMap) {
      Gson gson = new Gson();
      V2AutoTimezoneSettings autoTimezone = (V2AutoTimezoneSettings)gson.fromJson(gson.toJson(settingsMap), V2AutoTimezoneSettings.class);
      String dstManual = autoTimezone.getDay_light_saving_manual();
      if (StringUtils.isNotEmpty(dstManual)) {
         String[] manual = dstManual.split(";", 9);
         autoTimezone.setDst_start_month(manual[0]);
         autoTimezone.setDst_start_week(manual[1]);
         autoTimezone.setDst_start_day(manual[2]);
         autoTimezone.setDst_start_time(manual[3]);
         autoTimezone.setDst_end_month(manual[4]);
         autoTimezone.setDst_end_week(manual[5]);
         autoTimezone.setDst_end_day(manual[6]);
         autoTimezone.setDst_end_time(manual[7]);
         autoTimezone.setDst_time_difference(manual[8]);
      }

      return autoTimezone;
   }

   private V2StatisticsDataSettings getStatisticsDataSetting(Map settingsMap) throws ConfigException {
      Gson gson = new Gson();
      V2StatisticsDataSettings statisticsData = (V2StatisticsDataSettings)gson.fromJson(gson.toJson(settingsMap), V2StatisticsDataSettings.class);
      if (CommonConfig.get("pop.enable") != null) {
         statisticsData.setPop_enable(Boolean.valueOf(CommonConfig.get("pop.enable")));
      }

      if (CommonConfig.get("daily.db.clean.period.day.log") != null) {
         statisticsData.setCollect_period(Integer.valueOf(CommonConfig.get("daily.db.clean.period.day.log")));
      }

      if (CommonConfig.get("pop.db.collection.level") != null) {
         statisticsData.setPop_frequency_level(CommonConfig.get("pop.db.collection.level"));
      }

      if (CommonConfig.get("daily.db.clean.period.mon.pop") != null) {
         statisticsData.setClean_month_period(Integer.valueOf(CommonConfig.get("daily.db.clean.period.mon.pop")));
      }

      if (CommonConfig.get("daily.db.clean.period.day.pop") != null) {
         statisticsData.setClean_day_period(Integer.valueOf(CommonConfig.get("daily.db.clean.period.day.pop")));
      }

      return statisticsData;
   }

   private V2LogManagementSettings getLogManagementSettings(Map settingsMap) throws ConfigException, SQLException {
      Gson gson = new Gson();
      V2LogManagementSettings logManagement = (V2LogManagementSettings)gson.fromJson(gson.toJson(settingsMap), V2LogManagementSettings.class);
      if (CommonConfig.get("logger.MAIN_LOGGER.level") != null) {
         logManagement.setLog_level(CommonConfig.get("logger.MAIN_LOGGER.level"));
      }

      if (CommonConfig.get("appender.FILE.policies.size.size") != null) {
         logManagement.setLog_file_size(Integer.valueOf(CommonConfig.get("appender.FILE.policies.size.size").replace("MB", "")));
      }

      if (CommonConfig.get("appender.FILE.strategy.max") != null) {
         logManagement.setMax_logfile_count(Integer.valueOf(CommonConfig.get("appender.FILE.strategy.max")));
      }

      if (CommonConfig.get("device.log_collect") != null) {
         logManagement.setPlayer_log_collect(Boolean.valueOf(CommonConfig.get("device.log_collect")));
         if (logManagement.getPlayer_log_collect()) {
            DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
            List logCollectList = deviceDao.getAllDeviceLogProcess();
            if (logCollectList != null && logCollectList.size() > 0) {
               List devices = new ArrayList();

               for(int i = 0; i < logCollectList.size(); ++i) {
                  devices.add(((DeviceLogCollectEntity)logCollectList.get(i)).getDevice_id());
               }

               logManagement.setLog_collect_devices(devices);
            }
         }
      }

      if (CommonConfig.get("device.log4j.on") != null) {
         logManagement.setDevice_log_collect(Boolean.valueOf(CommonConfig.get("device.log4j.on")));
         if (CommonConfig.get("device.log4j.devices") != null) {
            List devicesList = new ArrayList(Arrays.asList(DeviceUtils.getLogEnabledDevicesListFromConfig()));
            if (((String)devicesList.get(0)).length() == 0) {
               devicesList = new ArrayList();
            }

            DeviceConnectionDao connectionDao = new DeviceConnectionDao();
            List allDevicesList = connectionDao.getApprovedDeviceList();
            List updatedDevicesList = new ArrayList();
            boolean isDeviceListUpdated = false;

            for(int i = 0; i < devicesList.size(); ++i) {
               String deviceId = (String)devicesList.get(i);
               boolean matched = false;

               for(int j = 0; j < allDevicesList.size(); ++j) {
                  if (deviceId.equals(((DeviceMonitoring)allDevicesList.get(j)).getDevice_id())) {
                     matched = true;
                     break;
                  }
               }

               if (matched) {
                  updatedDevicesList.add(deviceId);
               } else {
                  isDeviceListUpdated = true;
               }
            }

            if (isDeviceListUpdated) {
               CommonConfig.set("device.log4j.devices", String.valueOf(updatedDevicesList));
               Map attribMap = new HashMap();
               attribMap.put("device.log4j.devices", String.valueOf(updatedDevicesList));
               List attribKey = new ArrayList(attribMap.keySet());
               CommonConfig.saveOnlyModifiedConfigFile(attribMap, attribKey);
            }

            logManagement.setDevice_Log_Selected_Devices(updatedDevicesList);
         }
      }

      return logManagement;
   }

   private V2GeneralSettings getGeneralSettings(Map settingsMap) throws ConfigException {
      Gson gson = new Gson();
      V2GeneralSettings general = (V2GeneralSettings)gson.fromJson(gson.toJson(settingsMap), V2GeneralSettings.class);
      if (CommonConfig.get("sign_up.enable") != null) {
         general.setSign_up_enable(Boolean.valueOf(CommonConfig.get("sign_up.enable")));
      }

      if (CommonConfig.get("login.retry.count") != null) {
         general.setSignin_failure_count(Integer.valueOf(CommonConfig.get("login.retry.count")));
      }

      if (CommonConfig.get("login.block.min") != null) {
         general.setSignin_block_duration(Integer.valueOf(CommonConfig.get("login.block.min")));
      }

      if (CommonConfig.get("token.lifetime") != null) {
         general.setToken_lifetime(Long.valueOf(CommonConfig.get("token.lifetime")) / 60L);
      }

      if (CommonConfig.get("menu.user.needAuth") != null) {
         general.setUser_need_auth(Boolean.valueOf(CommonConfig.get("menu.user.needAuth")));
      }

      if (CommonConfig.get("menu.settings.myAccount.needAuth") != null) {
         general.setMy_account_need_auth(Boolean.valueOf(CommonConfig.get("menu.settings.myAccount.needAuth")));
      }

      if (CommonConfig.get("use.manual.category") != null) {
         general.setManual_category(Boolean.valueOf(CommonConfig.get("use.manual.category")));
      }

      if (CommonConfig.get("manual.category.select.last.depth.only") != null) {
         general.setManual_category_select_last_depth_only(Boolean.valueOf(CommonConfig.get("manual.category.select.last.depth.only")));
      }

      if (CommonConfig.get("manual.category.last.selected.number") != null) {
         general.setManual_category_max_selected_number(Integer.valueOf(CommonConfig.get("manual.category.last.selected.number")));
      }

      if (CommonConfig.get("manual.category.mandatory.set.upload") != null) {
         general.setManual_category_mandatory_set_upload(Boolean.valueOf(CommonConfig.get("manual.category.mandatory.set.upload")));
      }

      try {
         general.setDb_encryption(SecurityUtils.getDbEncryptEnable());
      } catch (IOException var5) {
         this.logger.error("", var5);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONFIGERROR);
      }

      if (CommonConfig.get("e2e.enable") != null) {
         general.setIsE2E(Boolean.valueOf(CommonConfig.get("e2e.enable")));
         if (CommonConfig.get("e2e.license.system") != null) {
            general.setE2eLicenseSystem(String.valueOf(CommonConfig.get("e2e.license.system")).toUpperCase());
         } else {
            general.setE2eLicenseSystem(ExternalSystemUtils.SYSTEM_PBP);
         }
      }

      return general;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public V2SettingServerResource updateServerSettings(V2SettingServerResource resource) throws Exception {
      this.setServerSettingToDB(resource);
      this.setServerSettingToConfigFile(resource);
      return this.getServerSettings();
   }

   private void setServerSettingToConfigFile(V2SettingServerResource resource) {
      if (CommonConfig.getConfigFileOpenStatus()) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONFIGERROR);
      } else {
         try {
            CommonConfig.setConfigFileOpenStatus(true);
            Map attribMap = new HashMap();
            if (resource.getGeneral() != null) {
               attribMap.putAll(this.setGeneralSettings(resource.getGeneral()));
            }

            if (resource.getLogManagement() != null) {
               attribMap.putAll(this.setLogManagementSettings(resource.getLogManagement()));
            }

            if (resource.getStatisticsData() != null) {
               attribMap.putAll(this.setStatisticsDataSettings(resource.getStatisticsData()));
            }

            if (resource.getThirdpartService() != null) {
               attribMap.putAll(this.setThirdpartServiceSettings(resource.getThirdpartService()));
            }

            if (resource.getPrivacyPolicy() != null) {
               Map pp = this.setPrivacyPolicySettings(resource.getPrivacyPolicy());
               attribMap.putAll(pp);
               Iterator var4 = pp.keySet().iterator();

               while(var4.hasNext()) {
                  String key = (String)var4.next();
                  this.pplogger.info(key + " = " + (String)pp.get(key));
               }
            }

            List attribKey = new ArrayList(attribMap.keySet());
            CommonConfig.saveOnlyModifiedConfigFile(attribMap, attribKey);
         } catch (Exception var9) {
            this.logger.error("", var9);
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONFIGERROR);
         } finally {
            CommonConfig.setConfigFileOpenStatus(false);
         }

      }
   }

   private void setServerSettingToDB(V2SettingServerResource resource) throws Exception {
      Gson gson = new Gson();
      Map settingMap = (Map)gson.fromJson(gson.toJson(resource), Map.class);
      if (settingMap != null && !settingMap.isEmpty()) {
         V2PasswordPolicySettings passwordPolicy = resource.getPasswordPolicy();
         if (passwordPolicy != null && passwordPolicy.getPassword_change_period() != null && passwordPolicy.getPassword_change_period() == 0) {
            UserPasswordManager userPasswordManager = UserPasswordManagerImpl.getInstance();
            userPasswordManager.deleteUserPasswordHistory();
         }

         this.setMfaInfo(resource);
         this.setKpiInfo(resource);
         Map infoMap = this.serverSetupDao.getServerInfoByOrgId(CommonDataConstants.ROOT_ID);
         Iterator var6 = settingMap.keySet().iterator();

         String smtpExistingPassword;
         while(var6.hasNext()) {
            smtpExistingPassword = (String)var6.next();
            infoMap.putAll((Map)settingMap.get(smtpExistingPassword));
         }

         if (resource.getAutoTimezone() != null) {
            infoMap.put("day_light_saving_manual", this.getAutoTimezoneManual(resource.getAutoTimezone()));
         }

         if (resource.getSmtpServer() != null) {
            String smtpPassword = resource.getSmtpServer().getSmtp_auth_pwd();
            smtpExistingPassword = this.getServerSettings().getSmtpServer().getSmtp_auth_pwd();
            if (smtpPassword != null) {
               if (smtpExistingPassword != null && smtpExistingPassword.compareTo(smtpPassword) == 0) {
                  smtpPassword = SecurityUtils.getDecryptionPassword(smtpPassword, resource.getSmtpServer().getSmtp_enc_version());
               }

               String encryptPwd = this.getEncryptPassword(smtpPassword);
               infoMap.put("SMTP_AUTH_PWD", encryptPwd);
            }

            infoMap.put("SMTP_AUTH_PORT", resource.getSmtpServer().getSmtp_auth_port());
         }

         V2LdapServerSettings ldapServerSetting = resource.getLdapServer();
         if (ldapServerSetting != null) {
            Long orgId = CommonDataConstants.ROOT_ID;

            try {
               ldapServerSetting.setLdap_use_server_setting(false);
               this.setLdapServerSetting(ldapServerSetting, orgId);
            } catch (Exception var18) {
               this.logger.error("", var18);
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN);
            }
         }

         V2InsightServerSettings insightServerSettings = resource.getInsightServer();
         if (insightServerSettings != null) {
            try {
               this.setInsightServerSetting(insightServerSettings);
            } catch (Exception var17) {
               this.logger.error("", var17);
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN);
            }
         }

         V2LoginPageSettings loginPageSettings = resource.getLoginPage();
         if (loginPageSettings != null) {
            try {
               this.setLoginPageSetting(loginPageSettings);
            } catch (Exception var16) {
               this.logger.error("", var16);
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN);
            }
         }

         V2ExternalServerSettings externalServerSetting = resource.getExternalServer();
         if (externalServerSetting != null && externalServerSetting.getExt_server_mon_interval() != null) {
            this.addJobSchedule(externalServerSetting.getExt_server_mon_interval(), "ExternalServerMonitoringJobService", "ExternalServerMonitoringJobServiceGroup", ExternalServerMonitoringJob.class);
         }

         V2AlarmMailingSettings alarmMailingSetting = resource.getAlarmMailing();
         if (alarmMailingSetting != null) {
            String jobName;
            if (alarmMailingSetting.getAlarm_start_time() != null && alarmMailingSetting.getAlarm_end_time() != null) {
               new SimpleDateFormat("HH:mm");
               Timestamp endtime;
               if (alarmMailingSetting.getAlarm_start_time() != null) {
                  jobName = "2013-01-01 ".concat(alarmMailingSetting.getAlarm_start_time()).concat(":00");
                  endtime = Timestamp.valueOf(jobName);
                  infoMap.put("disconnect_start_time", endtime);
               }

               if (alarmMailingSetting.getAlarm_end_time() != null) {
                  jobName = "2013-01-01 ".concat(alarmMailingSetting.getAlarm_end_time()).concat(":00");
                  endtime = Timestamp.valueOf(jobName);
                  infoMap.put("disconnect_end_time", endtime);
               }
            }

            if (alarmMailingSetting.getDisconnect_enable() != null) {
               Boolean alarmEnable = alarmMailingSetting.getDisconnect_enable();
               jobName = "EmailAlarmJobService";
               String jobGroupName = "EmailAlarmJobServiceGroup_" + String.valueOf(CommonDataConstants.ROOT_ID);
               if (alarmEnable) {
                  try {
                     this.addJobSchedule(30L, jobName, jobGroupName, EmailAlarmJob.class);
                  } catch (Exception var15) {
                     this.logger.error("", var15);
                  }
               } else {
                  CommonUtils.deleteJob(jobName, jobGroupName);
               }
            }
         }

         infoMap.put("organization_id", CommonDataConstants.ROOT_ID);
         this.serverSetupDao.updateServerInfo(infoMap);
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"V2SettingServerResource"});
      }
   }

   private void setMfaInfo(V2SettingServerResource resource) throws SQLException {
      if (null != resource.getPasswordPolicy()) {
         UserInfo userInfo = UserInfoImpl.getInstance();
         if (resource.getPasswordPolicy().getDelete_mfa_data() != null && resource.getPasswordPolicy().getDelete_mfa_data()) {
            userInfo.deleteAllMFAData();
         } else if (resource.getPasswordPolicy().getMfa_list() != null) {
            Iterator var3 = resource.getPasswordPolicy().getMfa_list().iterator();

            while(var3.hasNext()) {
               MFA mfa = (MFA)var3.next();
               this.serverSetupDao.updateServerMfaInfo(mfa);
               if (mfa.getPeriod() != null) {
                  userInfo.updateUserDeviceExpiredDate(mfa.getPeriod());
               }
            }
         }

      }
   }

   private void setKpiInfo(V2SettingServerResource resource) throws SQLException {
      if (null != resource.getPrivacyPolicy()) {
         Map map = new HashMap();
         map.put("KPI_ENABLE", resource.getPrivacyPolicy().getPrivacy_policy_enable());
         map.put("LOCATION", resource.getPrivacyPolicy().getPrivacy_policy_location());
         this.serverSetupDao.updateServerSetupKpi(map);
      }
   }

   private void setEslServerSetting(ESLServerEntity eslServer, Long orgId) {
      EslServerInfo eslServerInfo = EslServerInfoImpl.getInstance();
      if (eslServer.isEnable()) {
         eslServerInfo.updateESLServer(eslServer, orgId);
      } else {
         eslServerInfo.EnableESLServer(false, orgId);
      }

   }

   private void setLdapServerSetting(V2LdapServerSettings ldapServerSetting, Long orgId) throws Exception {
      LdapServerDao ldapServerDao = new LdapServerDao();
      UserLdapSyncInfo userLdapSyncInfo = UserLdapSyncInfoImpl.getInstance();
      if (ldapServerSetting.getLdap_enable() != null) {
         if (!ldapServerSetting.getLdap_enable()) {
            ldapServerDao.deleteLdapServerInfo(orgId);
            userLdapSyncInfo.deleteLdapSyncByGroupId(orgId);
         } else if (ldapServerSetting.getLdap_use_server_setting()) {
            ldapServerDao.deleteLdapServerInfo(orgId);
         } else {
            if (StringUtils.isEmpty(ldapServerSetting.getLdap_server()) || StringUtils.isEmpty(ldapServerSetting.getLdap_root_dn()) || StringUtils.isEmpty(ldapServerSetting.getLdap_manager_dn()) || StringUtils.isEmpty(ldapServerSetting.getLdap_manager_password())) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"Ldap Server Information"});
            }

            Map originMap = ldapServerDao.getLdapServerInfo(orgId);
            Boolean ldapSSL = ldapServerSetting.getUse_ldap_ssl();
            String ldapServer = ldapServerSetting.getLdap_server();
            if (ldapSSL) {
               ldapServer = "ldaps://".concat(ldapServer);
            } else {
               ldapServer = "ldap://".concat(ldapServer);
            }

            if (originMap != null && (orgId == 0L || (Long)originMap.get("org_id") != 0L)) {
               ldapServerDao.updateLdapServerInfo(orgId, ldapServer, ldapServerSetting.getLdap_root_dn(), ldapServerSetting.getLdap_manager_dn(), this.getEncryptPassword(ldapServerSetting.getLdap_manager_password()));
            } else {
               ldapServerDao.insertLdapServerInfo(orgId, ldapServer, ldapServerSetting.getLdap_root_dn(), ldapServerSetting.getLdap_manager_dn(), this.getEncryptPassword(ldapServerSetting.getLdap_manager_password()));
            }
         }

      }
   }

   private void setInsightServerSetting(V2InsightServerSettings newSettingInfo) throws SQLException {
      InsightServerDao insightServerDao = new InsightServerDao();
      InsightServerEntity insightServerEntity = insightServerDao.getInsightServerInfo();
      V2InsightServerSettings oldSettingInfo = new V2InsightServerSettings();
      oldSettingInfo.setFromInsightServerEntity(insightServerEntity);
      newSettingInfo.updateNullValueFrom(oldSettingInfo);
      this.updateConfigForInsightServer(oldSettingInfo, newSettingInfo);
      insightServerDao.deleteInsightServerInfo();
      insightServerDao.insertInsightServerInfo(null == newSettingInfo.getIp() ? "" : newSettingInfo.getIp(), null == newSettingInfo.getHttp_port() ? 0 : newSettingInfo.getHttp_port(), null == newSettingInfo.getHttps_port() ? 0 : newSettingInfo.getHttps_port(), null == newSettingInfo.getSecurity_key() ? "" : newSettingInfo.getSecurity_key(), null != newSettingInfo.isUse_server() && newSettingInfo.isUse_server());
   }

   private void updateConfigForInsightServer(V2InsightServerSettings oldSettingInfo, V2InsightServerSettings newSettingInfo) {
      if (CommonConfig.getConfigFileOpenStatus()) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONFIGERROR);
      } else {
         try {
            String availableLinks = CommonConfig.get("security.filter.src");
            if (null == availableLinks) {
               availableLinks = "";
            }

            StringTokenizer tokenizer = new StringTokenizer(availableLinks, ",");
            ArrayList availableLinkList = new ArrayList();

            while(tokenizer.hasMoreElements()) {
               availableLinkList.add(tokenizer.nextToken());
            }

            List oldUrls = this.getUrlsForInsight(oldSettingInfo);
            oldUrls.forEach((s) -> {
               availableLinkList.remove(s);
            });
            List newUrls = this.getUrlsForInsight(newSettingInfo);
            newUrls.forEach((s) -> {
               if (!availableLinkList.contains(s)) {
                  availableLinkList.add(s);
               }

            });
            AtomicReference newAvailableLinks = new AtomicReference("");
            availableLinkList.forEach((s) -> {
               if (((String)newAvailableLinks.get()).isEmpty()) {
                  newAvailableLinks.set(s);
               } else {
                  newAvailableLinks.set(newAvailableLinks + "," + s);
               }

            });
            CommonConfig.set("security.filter.src", (String)newAvailableLinks.get());
            CommonConfig.setConfigFileOpenStatus(true);
            Map attribMap = new HashMap();
            attribMap.put("security.filter.src", newAvailableLinks.get());
            List attribKey = new ArrayList(attribMap.keySet());
            CommonConfig.saveOnlyModifiedConfigFile(attribMap, attribKey);
         } catch (Exception var14) {
            this.logger.error("[CommonConfig get ] security.filter.src");
         } finally {
            CommonConfig.setConfigFileOpenStatus(false);
         }

      }
   }

   private List getUrlsForInsight(V2InsightServerSettings info) {
      List urls = new ArrayList();
      if (null != info && null != info.getIp() && !info.getIp().isEmpty()) {
         if (null != info.getHttp_port()) {
            urls.add("http://" + info.getIp() + ":" + info.getHttp_port() + "/");
            urls.add("http://" + info.getIp() + ":" + info.getHttp_port() + "/*");
         }

         if (null != info.getHttps_port()) {
            urls.add("https://" + info.getIp() + ":" + info.getHttps_port() + "/");
            urls.add("https://" + info.getIp() + ":" + info.getHttps_port() + "/*");
         }

         return urls;
      } else {
         return urls;
      }
   }

   private void setLoginPageSetting(V2LoginPageSettings newSettingInfo) throws SQLException {
      LoginPageDao loginPageDao = new LoginPageDao();
      LoginPageEntity loginPageEntity = loginPageDao.getLoginPageInfo();
      if (null == loginPageEntity) {
         loginPageEntity = new LoginPageEntity();
      }

      loginPageEntity.updateValueFrom(newSettingInfo);
      loginPageDao.deleteLoginPageInfo();
      loginPageDao.insertLoginPageInfo(loginPageEntity);
   }

   private String getEncryptPassword(String password) {
      EncryptionManager encMgr = EncryptionManagerImpl.getInstance();
      String encryptPwd = encMgr.getEncryptionPassword("", password);
      return encryptPwd;
   }

   private void addJobSchedule(long period, String jobName, String jobGroupName, Class jobClass) {
      Scheduler scheduler = ScheduleManager.getSchedulerInstance();
      CommonUtils.deleteJob(jobName, jobGroupName);
      JobDetail jobdetail = CommonUtils.getJobDetail(jobName, jobGroupName, jobClass);
      Calendar currTime = Calendar.getInstance();
      currTime.add(12, 1);

      try {
         SimpleTrigger trigger = CommonUtils.getSimpleTrigger(jobName, jobGroupName, currTime.getTime(), 60000L * period);
         scheduler.scheduleJob(jobdetail, trigger);
      } catch (SchedulerException var10) {
         this.logger.error(var10);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_JOBSCHEDULEERROR);
      }
   }

   private Map setThirdpartServiceSettings(V2ThirdpartServiceSettings thirdpartService) throws Exception {
      Map attribMap = new HashMap();
      Boolean useNoc = thirdpartService.getUse_noc_update();
      if (useNoc != null) {
         CommonConfig.set("thingworx.update.enable", String.valueOf(useNoc));
         attribMap.put("thingworx.update.enable", String.valueOf(useNoc));
         this.updateThinworxServiceStatus(thirdpartService);
      }

      if (thirdpartService.getNoc_module_url() != null) {
         CommonConfig.set("thingworx.update.url", thirdpartService.getNoc_module_url());
         attribMap.put("thingworx.update.url", thirdpartService.getNoc_module_url());
      }

      if (thirdpartService.getUse_rule_manager() != null) {
         CommonConfig.set("rulemanager.enable", thirdpartService.getRule_manager_url());
         attribMap.put("rulemanager.enable", thirdpartService.getRule_manager_url());
      }

      if (thirdpartService.getRule_manager_url() != null) {
         CommonConfig.set("rulemanager.url", thirdpartService.getRule_manager_url());
         attribMap.put("rulemanager.url", thirdpartService.getRule_manager_url());
      }

      if (thirdpartService.getRule_manager_apikey() != null) {
         CommonConfig.set("rulemanager.apiKey", thirdpartService.getRule_manager_apikey());
         attribMap.put("rulemanager.apiKey", thirdpartService.getRule_manager_apikey());
      }

      List ruleManagerOrgans = thirdpartService.getRule_manager_organs();
      if (ruleManagerOrgans != null && ruleManagerOrgans.size() > 0) {
         this.serverSetupDao.resetRuleMangerEnabledOrg();

         for(int i = 0; i < ruleManagerOrgans.size(); ++i) {
            this.serverSetupDao.updateRuleMangerEnabledOrg(((V2ThirdpartServiceSettings.Organization)ruleManagerOrgans.get(i)).getOrganization_id());
         }

         this.serverSetupDao.updateRuleMangerEnabledOrg(0L);
      }

      return attribMap;
   }

   private void updateThinworxServiceStatus(V2ThirdpartServiceSettings thirdpartService) throws SQLException {
      Boolean useNoc = thirdpartService.getUse_noc_update();
      DeviceNocManager nocService = DeviceNocManagerImpl.getInstance();
      if (useNoc) {
         boolean setNocResult = nocService.thingworxSetServerInfo();
         if (DeviceUtils.isSupportNOC() || setNocResult) {
            DeviceNocInfo nocDao = DeviceNocInfoImpl.getInstance();
            List originNocGroups = nocDao.getDeviceGroupListForNOC();
            List updateList = Arrays.asList(thirdpartService.getNoc_device_groups());
            if (thirdpartService.getNoc_device_groups() != null && originNocGroups.size() > 0) {
               String deleted = "";
               Iterator var9 = originNocGroups.iterator();

               while(var9.hasNext()) {
                  Map origin = (Map)var9.next();
                  if (!updateList.contains(origin.get("group_id"))) {
                     if (deleted.length() > 0) {
                        deleted = deleted + ",";
                     }

                     deleted = deleted + origin;
                  }
               }

               if (deleted.length() > 0) {
                  nocDao.deleteDeviceByGroup(deleted.toString());
               }
            }

            if (updateList != null) {
               nocDao.updateNocUpdateOnGroup(StringUtils.join(updateList, ","));
            }
         }
      } else {
         nocService.thingworxDeleteServer();
      }

   }

   private String getAutoTimezoneManual(V2AutoTimezoneSettings autotimezone) {
      String manual = autotimezone.getDst_start_month() + ";" + autotimezone.getDst_start_week() + ";" + autotimezone.getDst_start_day() + ";" + autotimezone.getDst_start_time() + ";" + autotimezone.getDst_end_month() + ";" + autotimezone.getDst_end_week() + ";" + autotimezone.getDst_end_day() + ";" + autotimezone.getDst_end_time() + ";" + autotimezone.getDst_time_difference();
      return manual;
   }

   private Map setStatisticsDataSettings(V2StatisticsDataSettings statisticsData) throws ConfigException {
      Map attribMap = new HashMap();
      if (statisticsData.getPop_enable() != null) {
         CommonConfig.set("pop.enable", String.valueOf(statisticsData.getPop_enable()));
         attribMap.put("pop.enable", String.valueOf(statisticsData.getPop_enable()));
      }

      if (statisticsData.getCollect_period() != null) {
         CommonConfig.set("daily.db.clean.period.day.log", String.valueOf(statisticsData.getCollect_period()));
         attribMap.put("daily.db.clean.period.day.log", String.valueOf(statisticsData.getCollect_period()));
      }

      if (statisticsData.getPop_frequency_level() != null) {
         CommonConfig.set("pop.db.collection.level", statisticsData.getPop_frequency_level());
         attribMap.put("pop.db.collection.level", statisticsData.getPop_frequency_level());
      }

      if (statisticsData.getClean_month_period() != null) {
         CommonConfig.set("daily.db.clean.period.mon.pop", String.valueOf(statisticsData.getClean_month_period()));
         attribMap.put("daily.db.clean.period.mon.pop", String.valueOf(statisticsData.getClean_month_period()));
      }

      if (statisticsData.getClean_day_period() != null) {
         CommonConfig.set("daily.db.clean.period.day.pop", String.valueOf(statisticsData.getClean_day_period()));
         attribMap.put("daily.db.clean.period.day.pop", String.valueOf(statisticsData.getClean_day_period()));
      }

      return attribMap;
   }

   private Map setLogManagementSettings(V2LogManagementSettings logManagement) throws ConfigException {
      Map attribMap = new HashMap();
      if (logManagement.getLog_level() != null) {
         CommonConfig.set("logger.MAIN_LOGGER.level", logManagement.getLog_level());
         attribMap.put("logger.MAIN_LOGGER.level", logManagement.getLog_level());
      }

      if (logManagement.getLog_file_size() != null) {
         CommonConfig.set("appender.FILE.policies.size.size", String.valueOf(logManagement.getLog_file_size()));
         attribMap.put("appender.FILE.policies.size.size", logManagement.getLog_file_size() + "MB");
      }

      if (logManagement.getMax_logfile_count() != null) {
         CommonConfig.set("appender.FILE.strategy.max", String.valueOf(logManagement.getMax_logfile_count()));
         attribMap.put("appender.FILE.strategy.max", String.valueOf(logManagement.getMax_logfile_count()));
      }

      if (logManagement.getPlayer_log_collect() != null) {
         CommonConfig.set("device.log_collect", String.valueOf(logManagement.getPlayer_log_collect()));
         attribMap.put("device.log_collect", String.valueOf(logManagement.getPlayer_log_collect()));
      }

      if (logManagement.getDevice_log_collect() != null) {
         CommonConfig.set("device.log4j.on", String.valueOf(logManagement.getDevice_log_collect()));
         attribMap.put("device.log4j.on", String.valueOf(logManagement.getDevice_log_collect()));
      }

      if (logManagement.getDevice_Log_Selected_Devices() != null) {
         CommonConfig.set("device.log4j.devices", String.valueOf(logManagement.getDevice_Log_Selected_Devices()));
         attribMap.put("device.log4j.devices", String.valueOf(logManagement.getDevice_Log_Selected_Devices()));
      }

      return attribMap;
   }

   private Map setGeneralSettings(V2GeneralSettings general) throws ConfigException {
      Map attribMap = new HashMap();
      if (general.getSign_up_enable() != null) {
         CommonConfig.set("sign_up.enable", String.valueOf(general.getSign_up_enable()));
         attribMap.put("sign_up.enable", String.valueOf(general.getSign_up_enable()));
      }

      if (general.getSignin_failure_count() != null) {
         CommonConfig.set("login.retry.count", String.valueOf(general.getSignin_failure_count()));
         attribMap.put("login.retry.count", String.valueOf(general.getSignin_failure_count()));
      }

      if (general.getSignin_block_duration() != null) {
         CommonConfig.set("login.block.min", String.valueOf(general.getSignin_block_duration()));
         attribMap.put("login.block.min", String.valueOf(general.getSignin_block_duration()));
      }

      if (general.getToken_lifetime() != null) {
         Long tokenLifetime = general.getToken_lifetime() * 60L;
         CommonConfig.set("token.lifetime", String.valueOf(tokenLifetime));
         attribMap.put("token.lifetime", String.valueOf(tokenLifetime));
      }

      if (general.getDb_encryption() != null) {
         attribMap.putAll(this.setDbEncryptResult(general.getDb_encryption()));
      }

      return attribMap;
   }

   private Map setPrivacyPolicySettings(V2PrivacyPolicySettings privacyPolicy) throws ConfigException {
      Map attribMap = new HashMap();
      if (privacyPolicy.getPrivacy_policy_enable() != null) {
         CommonConfig.set("privacy_policy.enable", String.valueOf(privacyPolicy.getPrivacy_policy_enable()));
         attribMap.put("privacy_policy.enable", String.valueOf(privacyPolicy.getPrivacy_policy_enable()));
      }

      if (privacyPolicy.getPrivacy_policy_location() != null) {
         CommonConfig.set("privacy_policy.location", privacyPolicy.getPrivacy_policy_location());
         attribMap.put("privacy_policy.location", privacyPolicy.getPrivacy_policy_location());
      }

      return attribMap;
   }

   private Map setDbEncryptResult(Boolean isEnc) throws ConfigException {
      Map attribMap = new HashMap();
      String[] propValues = new String[this.ENCRYPTION_PROPERTY_KEYS.length];
      Properties props = new Properties();
      String confFilePath = CommonConfig.getConfigFilePath();
      String configFileName = CommonConfig.getConfigFileName();
      FileInputStream in = null;

      try {
         in = new FileInputStream(SecurityUtils.directoryTraversalChecker(confFilePath + File.separator + configFileName, (String)null));
         props.load(new BufferedInputStream(in));
      } catch (IOException var16) {
         this.logger.error("", var16);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONFIGERROR);
      } finally {
         if (in != null) {
            try {
               in.close();
            } catch (IOException var15) {
               this.logger.error("", var15);
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONFIGERROR);
            }
         }

      }

      for(int i = 0; i < this.ENCRYPTION_PROPERTY_KEYS.length; ++i) {
         propValues[i] = StrUtils.nvl(props.getProperty(this.ENCRYPTION_PROPERTY_KEYS[i])).trim();
         String encValue;
         if (!isEnc && propValues[i].indexOf("ENC(") == 0) {
            encValue = ConfigEncryptionManagement.getDecryption(propValues[i]);
            CommonConfig.set(this.ENCRYPTION_PROPERTY_KEYS[i], encValue);
            attribMap.put(this.ENCRYPTION_PROPERTY_KEYS[i], encValue);
         } else if (isEnc && propValues[i].indexOf("ENC(") != 0) {
            encValue = ConfigEncryptionManagement.getEncryption(propValues[i]);
            CommonConfig.set(this.ENCRYPTION_PROPERTY_KEYS[i], encValue);
            attribMap.put(this.ENCRYPTION_PROPERTY_KEYS[i], encValue);
         }
      }

      return attribMap;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public V2SettingOrganResource updateOrganSettings(Long orgId, V2SettingOrganResource resource) throws Exception {
      Gson gson = new Gson();
      Map settingMap = (Map)gson.fromJson(gson.toJson(resource), Map.class);
      if (orgId == null) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"OrganizationId"});
      } else if (settingMap != null && !settingMap.isEmpty()) {
         Map infoMap = this.serverSetupDao.getServerInfoByOrgId(orgId);
         Iterator var6 = settingMap.keySet().iterator();

         String smtpPassword;
         while(var6.hasNext()) {
            smtpPassword = (String)var6.next();
            infoMap.putAll((Map)settingMap.get(smtpPassword));
         }

         V2OrganGeneralSettings general = resource.getGeneral();
         if (general != null) {
            List contentApprovers = general.getContent_approvers();
            if (contentApprovers != null && contentApprovers.size() > 0) {
               List contentApproverIds = new ArrayList();

               for(int i = 0; i < contentApprovers.size(); ++i) {
                  V2OrganGeneralSettings.ContentManager contentApprover = (V2OrganGeneralSettings.ContentManager)contentApprovers.get(i);
                  if (contentApprover.getContent_approver() != null && contentApprover.getContent_approver().equalsIgnoreCase("Y")) {
                     contentApproverIds.add(contentApprover.getUser_id());
                  }
               }

               String[] approaverArr = new String[contentApproverIds.size()];
               this.updateContentApprovers(orgId, (String[])contentApproverIds.toArray(approaverArr));
            }
         }

         String endTime;
         String jobName;
         if (resource.getSmtpServer() != null) {
            smtpPassword = resource.getSmtpServer().getSmtp_auth_pwd();
            String smtpExistingPassword = this.getServerSettings().getSmtpServer().getSmtp_auth_pwd();
            endTime = this.getOrganSettings(orgId).getSmtpServer().getSmtp_auth_pwd();
            if (smtpPassword != null) {
               if (smtpExistingPassword != null && smtpExistingPassword.compareTo(smtpPassword) == 0 || endTime != null && endTime.compareTo(smtpPassword) == 0) {
                  smtpPassword = SecurityUtils.getDecryptionPassword(smtpPassword, resource.getSmtpServer().getSmtp_enc_version());
               }

               jobName = this.getEncryptPassword(smtpPassword);
               infoMap.put("SMTP_AUTH_PWD", jobName);
            }

            infoMap.put("SMTP_AUTH_PORT", resource.getSmtpServer().getSmtp_auth_port());
         }

         V2OrganAlarmMailingSettings alarmMailing = resource.getAlarmMailing();
         if (alarmMailing != null) {
            if (alarmMailing.getAlarm_start_time() != null && alarmMailing.getAlarm_end_time() != null) {
               new SimpleDateFormat("HH:mm");
               Timestamp endtime;
               if (alarmMailing.getAlarm_start_time() != null) {
                  endTime = "2013-01-01 ".concat(alarmMailing.getAlarm_start_time()).concat(":00");
                  endtime = Timestamp.valueOf(endTime);
                  infoMap.put("disconnect_start_time", endtime);
               }

               if (alarmMailing.getAlarm_end_time() != null) {
                  endTime = "2013-01-01 ".concat(alarmMailing.getAlarm_end_time()).concat(":00");
                  endtime = Timestamp.valueOf(endTime);
                  infoMap.put("disconnect_end_time", endtime);
               }
            }

            Boolean alarmEnable = null;
            Long period = null;
            if (infoMap.get("disconnect_period") != null) {
               period = ((Number)infoMap.get("disconnect_period")).longValue();
            }

            if (period == null) {
               period = 30L;
            }

            if (alarmMailing.getDisconnect_enable() != null) {
               alarmEnable = alarmMailing.getDisconnect_enable();
            }

            if (alarmMailing.getDisconnect_period() != null) {
               alarmEnable = (Boolean)infoMap.get("disconnect_enable");
               period = alarmMailing.getDisconnect_period().longValue();
            }

            if (alarmEnable != null) {
               jobName = "EmailAlarmJobService";
               String jobGroupName = "EmailAlarmJobServiceGroup_" + orgId;
               if (alarmEnable) {
                  try {
                     this.addJobSchedule(period, jobName, jobGroupName, EmailAlarmJob.class);
                  } catch (Exception var14) {
                     this.logger.error("", var14);
                  }
               } else {
                  CommonUtils.deleteJob(jobName, jobGroupName);
               }
            }
         }

         ESLServerEntity eslServer = resource.getEslServer();
         if (eslServer != null) {
            this.setEslServerSetting(eslServer, orgId);
         }

         V2LdapServerSettings ldapServerSetting = resource.getLdapServer();
         if (ldapServerSetting != null) {
            try {
               this.setLdapServerSetting(ldapServerSetting, orgId);
            } catch (Exception var13) {
               this.logger.error("", var13);
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN);
            }
         }

         infoMap.put("organization_id", orgId);
         this.serverSetupDao.updateServerInfo(infoMap);
         return this.getOrganSettings(orgId);
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"V2SettingServerResource"});
      }
   }

   private void updateContentApprovers(Long orgId, String[] contentApprovers) throws SQLException {
      ContentInfo contentDao = ContentInfoImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      List oldApproverList = userInfo.getContentApproverListByGroupId(orgId);
      String removedList = "";
      List addedList = new ArrayList();
      int idx;
      if (oldApproverList != null && oldApproverList.size() > 0) {
         String userId;
         int idx;
         for(idx = 0; idx < contentApprovers.length; ++idx) {
            userId = contentApprovers[idx];

            for(idx = 0; idx < oldApproverList.size() && !userId.equals(((Map)oldApproverList.get(idx)).get("user_id")); ++idx) {
               if (idx == oldApproverList.size() - 1 && !((Map)oldApproverList.get(idx)).get("user_id").equals(userId)) {
                  addedList.add(userId);
                  userInfo.setContentApprover(contentApprovers[idx], "Y");
               }
            }
         }

         for(idx = 0; idx < oldApproverList.size(); ++idx) {
            userId = (String)((Map)oldApproverList.get(idx)).get("user_id");

            for(idx = 0; idx < contentApprovers.length && !contentApprovers[idx].equals(userId); ++idx) {
               if (idx == contentApprovers.length - 1 && !contentApprovers[idx].equals(userId)) {
                  removedList = removedList + userId + ",";
                  userInfo.setContentApprover(userId, "N");
               }
            }
         }

         List tmpContentList;
         if (!removedList.equals("")) {
            String[] deletedApprovers = removedList.split(",");

            for(int i = 0; i < deletedApprovers.length; ++i) {
               tmpContentList = contentDao.getContentApproverInfoByUserId(deletedApprovers[i]);
               contentDao.deleteContentApproverMapByUserId(deletedApprovers[i]);

               for(int j = 0; j < tmpContentList.size(); ++j) {
                  Map map = (Map)tmpContentList.get(j);
                  List tmpApproverList = contentDao.getContentApproverListByContentId((String)map.get("CONTENT_ID"));
                  if (tmpApproverList == null || tmpApproverList != null && tmpApproverList.size() == 0) {
                     contentDao.setApprovalStatus((String)map.get("CONTENT_ID"), "APPROVED", "");
                  }
               }
            }
         }

         Iterator var17 = addedList.iterator();

         while(var17.hasNext()) {
            userId = (String)var17.next();
            tmpContentList = contentDao.getUnapprovedContentIdList(orgId);
            Iterator var19 = tmpContentList.iterator();

            while(var19.hasNext()) {
               String contentId = (String)var19.next();

               try {
                  contentDao.addContentApproverMap(contentId, userId);
               } catch (Exception var14) {
                  this.logger.error("Approver Update Error. " + var14.getMessage());
               }
            }
         }
      } else {
         for(idx = 0; idx < contentApprovers.length; ++idx) {
            userInfo.setContentApprover(contentApprovers[idx], "Y");
         }
      }

   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public Boolean checkLdapConnection(V2LdapServerSettings resource) throws Exception {
      String ldapAddress = resource.getLdap_server();
      if (resource.getUse_ldap_ssl()) {
         ldapAddress = "ldaps://" + ldapAddress;
      } else {
         ldapAddress = "ldap://" + ldapAddress;
      }

      int result = LDAPUtils.checkLDAPServer(ldapAddress, resource.getLdap_root_dn(), resource.getLdap_manager_dn(), resource.getLdap_manager_password());
      if (result == 0) {
         return true;
      } else {
         switch(result) {
         case -1:
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_LDAP_CONNECT_FAIL);
         case 0:
         default:
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN);
         case 1:
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_LDAP_WRONG_ADDRESS);
         case 2:
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_LDAP_WRONG_IDPASSWORD);
         case 3:
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_LDAP_EXPIRED_PASSWORD);
         case 4:
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_LDAP_LOGIN_FAIL);
         case 5:
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_LDAP_WRONG_DN);
         case 6:
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_LDAP_TIMEOUT);
         }
      }
   }

   private String getLogFilePath(String logType) throws Exception {
      String basePath = System.getenv("MAGICINFO_PREMIUM_HOME");
      if (basePath != null && !basePath.isEmpty()) {
         byte var4 = -1;
         switch(logType.hashCode()) {
         case 877279082:
            if (logType.equals("LOG_RUNTIME_POPUP")) {
               var4 = 1;
            }
            break;
         case 1296960761:
            if (logType.equals("LOG_RM_SERVER_POPUP")) {
               var4 = 0;
            }
         }

         switch(var4) {
         case 0:
            basePath = basePath + "rmserver" + File.separator + "logs";
            break;
         case 1:
            basePath = basePath + "runtime" + File.separator + "logs";
            break;
         default:
            basePath = basePath + "tomcat" + File.separator + "logs";
         }

         return basePath;
      } else {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FTP_INVALID_PATH, new String[]{"Invalid Magic Info Premium Path"});
      }
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public List getServerLogFileList(String logType) throws Exception {
      String filePath = this.getLogFilePath(logType);
      File dirFile = new File(SecurityUtils.directoryTraversalChecker(filePath, (String)null));
      File[] fileList = dirFile.listFiles();
      if (fileList != null && fileList.length != 0) {
         Arrays.sort(fileList, new Comparator() {
            public int compare(File f1, File f2) {
               return Long.compare(f2.lastModified(), f1.lastModified());
            }
         });
         List files = new ArrayList();

         for(int i = 0; i < fileList.length; ++i) {
            if (!fileList[i].isDirectory()) {
               String fileName = fileList[i].getName();
               boolean fileContainsWebAuthor = fileName.contains("webauthor");
               boolean fileContainsLayoutEditor = fileName.contains("layouteditor");
               boolean fileContainsStd = fileName.contains("std");
               boolean toSKip = false;
               byte var13 = -1;
               switch(logType.hashCode()) {
               case -390636736:
                  if (logType.equals("LOG_LAYOUT_MANAGER_POPUP")) {
                     var13 = 1;
                  }
                  break;
               case -60543220:
                  if (logType.equals("LOG_TOMCAT_POPUP")) {
                     var13 = 3;
                  }
                  break;
               case 870953918:
                  if (logType.equals("LOG_WEB_AUTHOR_POPUP")) {
                     var13 = 0;
                  }
                  break;
               case 1747590885:
                  if (logType.equals("LOG_MAGICINFO_PREMIUM_POPUP")) {
                     var13 = 2;
                  }
               }

               switch(var13) {
               case 0:
                  if (!fileContainsWebAuthor) {
                     toSKip = true;
                  }
                  break;
               case 1:
                  if (!fileContainsLayoutEditor) {
                     toSKip = true;
                  }
                  break;
               case 2:
                  if (!fileContainsStd) {
                     toSKip = true;
                  }
                  break;
               case 3:
                  if (fileContainsLayoutEditor || fileContainsWebAuthor || fileContainsStd) {
                     toSKip = true;
                  }
               }

               if (!toSKip) {
                  V2SettingLogFileResource logFile = new V2SettingLogFileResource();
                  logFile.setFile_path(fileList[i].getParent());
                  logFile.setFile_name(fileList[i].getName());
                  logFile.setFile_size(fileList[i].length());
                  logFile.setLast_modified(new Timestamp(fileList[i].lastModified()));
                  files.add(logFile);
               }
            }
         }

         return files;
      } else {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"logFile"});
      }
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public List getServerLogFileList(Integer startIndex, Integer pageSize) throws Exception {
      String logFilePath = CommonConfig.get("appender.FILE.fileName");
      int splitNum = logFilePath.lastIndexOf("/");
      String filePath = logFilePath.substring(0, splitNum + 1);
      File dirFile = new File(SecurityUtils.directoryTraversalChecker(filePath, (String)null));
      File[] fileList = dirFile.listFiles();
      if (fileList != null && fileList.length != 0) {
         Arrays.sort(fileList, new Comparator() {
            public int compare(File f1, File f2) {
               return Long.compare(f1.lastModified(), f2.lastModified());
            }
         });
         List files = new ArrayList();

         for(int i = startIndex; i < startIndex + pageSize && i <= fileList.length - 1; ++i) {
            if (!fileList[i].isDirectory()) {
               V2SettingLogFileResource logFile = new V2SettingLogFileResource();
               logFile.setFile_path(fileList[i].getParent());
               logFile.setFile_name(fileList[i].getName());
               logFile.setFile_size(fileList[i].length());
               logFile.setLast_modified(new Timestamp(fileList[i].lastModified()));
               files.add(logFile);
            }
         }

         return files;
      } else {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"logFile"});
      }
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public void downloadLogfile(String logFileName, String logType, HttpServletRequest request, HttpServletResponse response) throws Exception {
      String downloadFolder = "downloadlogs";
      String filePath = this.getLogFilePath(logType);
      File logfile = new File(SecurityUtils.directoryTraversalChecker(filePath + File.separator + logFileName, (String)null));
      byte[] buffer = new byte[1024];
      V2SettingLogFileResource resource = new V2SettingLogFileResource();
      FileInputStream in = null;
      FileOutputStream fos = null;
      ZipOutputStream zos = null;

      try {
         if (logfile.exists()) {
            File zipFolder = new File(SecurityUtils.directoryTraversalChecker(filePath + File.separator + downloadFolder, (String)null));
            if (!zipFolder.exists()) {
               zipFolder.mkdir();
            }

            SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
            String timeStamp = "." + format.format(System.currentTimeMillis());
            File zipFileCheck = new File(SecurityUtils.directoryTraversalChecker(filePath + File.separator + downloadFolder + File.separator + logFileName + timeStamp + ".zip", (String)null));
            if (!zipFileCheck.exists()) {
               fos = new FileOutputStream(filePath + File.separator + downloadFolder + File.separator + logFileName + timeStamp + ".zip");
               zos = new ZipOutputStream(fos);
               ZipEntry ze = new ZipEntry(logFileName);
               zos.putNextEntry(ze);
               in = new FileInputStream(filePath + File.separator + logFileName);

               int len;
               while((len = in.read(buffer)) > 0) {
                  zos.write(buffer, 0, len);
               }
            }

            resource.setFile_name(logFileName + timeStamp + ".zip");
         }
      } catch (Exception var26) {
         throw new ConfigException(var26.getMessage());
      } finally {
         try {
            if (zos != null) {
               zos.closeEntry();
               zos.close();
            }

            if (in != null) {
               in.close();
            }

            if (fos != null) {
               fos.close();
            }
         } catch (IOException var25) {
            this.logger.error("", var25);
         }

      }

      String zipFileName = resource.getFile_name();
      File zipFile = SecurityUtils.getSafeFile(SecurityUtils.directoryTraversalChecker(filePath + File.separator + downloadFolder + File.separator + zipFileName, (String)null));
      FileUtils.setFileToResponse(request, response, zipFile, "application/zip");
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public void stopLogCollect() throws Exception {
      DeviceInfo devMgr = DeviceInfoImpl.getInstance();
      List logDeviceList = devMgr.getAllDeviceLogProcess();
      if (logDeviceList != null && logDeviceList.size() > 0) {
         for(int i = 0; i < logDeviceList.size(); ++i) {
            DeviceLogCollectEntity dev = (DeviceLogCollectEntity)logDeviceList.get(i);
            DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
            devMgr.updateDeviceLogProcessStatus(dev.getDevice_id(), dev.getType(), dev.getCategory_script(), "END");
            confMgr.setDeviceLogProcessing(dev.getDevice_id(), dev.getCategory_script(), (String)null);
         }
      }

   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public Map getDatabaseInformation() throws Exception {
      if (null != CommonConfig.get("database_info.enable") && !"false".equals(CommonConfig.get("database_info.enable").toLowerCase())) {
         Map data = new HashMap();
         String[] var2 = this.ENCRYPTION_PROPERTY_KEYS;
         int var3 = var2.length;

         for(int var4 = 0; var4 < var3; ++var4) {
            String key = var2[var4];
            String value = CommonConfig.get(key);
            if (value.indexOf("ENC(") == 0) {
               value = ConfigEncryptionManagement.getDecryption(value);
            }

            if (value != null && !value.equals("")) {
               data.put(key, value);
            }
         }

         return data;
      } else {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
      }
   }

   public V2PrivacyPolicyResource getPrivacyPolicy(File folder) throws Exception {
      V2PrivacyPolicyResource privacyPolicy = new V2PrivacyPolicyResource();
      String folderName = folder.getName();
      String[] info = folderName.split("_");
      privacyPolicy.setId(info[this.PRIVACY_POLICY_FOLDER_NAME_INDEX_ID]);
      privacyPolicy.setLocation(info[this.PRIVACY_POLICY_FOLDER_NAME_INDEX_LOCATION]);
      return privacyPolicy;
   }

   public List getPrivacyPolicyList() throws Exception {
      List list = new ArrayList();
      String path = CommonConfig.get("UPLOAD_HOME") + File.separator + "privacy_policy";
      boolean isE2E = false;

      try {
         isE2E = StrUtils.nvl(CommonConfig.get("e2e.enable")).equalsIgnoreCase("true");
      } catch (ConfigException var11) {
      }

      if (isE2E) {
         path = path + File.separator + "ProService";
      }

      File dir = SecurityUtils.getSafeFile(path);
      if (!dir.exists()) {
         return list;
      } else {
         File[] fileList = dir.listFiles();
         File[] var6 = fileList;
         int var7 = fileList.length;

         for(int var8 = 0; var8 < var7; ++var8) {
            File folder = var6[var8];
            if (isE2E || !folder.getPath().contains("ProService")) {
               V2PrivacyPolicyResource privacyPolicy = this.getPrivacyPolicy(folder);
               if (folder.isDirectory()) {
                  this.getPrivacyPolicyFileData(folder, privacyPolicy);
               }

               list.add(privacyPolicy);
            }
         }

         return list;
      }
   }

   private void getPrivacyPolicyFileData(File folder, V2PrivacyPolicyResource privacyPolicy) throws FileNotFoundException, IOException {
      File[] files = folder.listFiles();
      InputStream is = null;
      BufferedReader br = null;

      try {
         File[] var6 = files;
         int var7 = files.length;

         for(int var8 = 0; var8 < var7; ++var8) {
            File policyFile = var6[var8];
            if (policyFile.isFile()) {
               is = new FileInputStream(policyFile);
               br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
               StringBuffer buffer = new StringBuffer();

               for(String line = br.readLine(); line != null; line = br.readLine()) {
                  buffer.append(line + "\n");
               }

               String data = new String(buffer.toString().getBytes("UTF-8"), "UTF-8");
               String var13 = policyFile.getName().toLowerCase();
               byte var14 = -1;
               switch(var13.hashCode()) {
               case -1270421301:
                  if (var13.equals("notice.htm")) {
                     var14 = 0;
                  }
                  break;
               case -165325113:
                  if (var13.equals("noticeagree.htm")) {
                     var14 = 2;
                  }
                  break;
               case 973912735:
                  if (var13.equals("agree.htm")) {
                     var14 = 1;
                  }
               }

               switch(var14) {
               case 0:
                  privacyPolicy.setPolicyNotice(data);
                  break;
               case 1:
                  privacyPolicy.setPolicyAgree(data);
                  break;
               case 2:
                  privacyPolicy.setPolicyNoticeAgree(data);
               }
            }
         }
      } catch (IOException var22) {
         this.logger.error("", var22);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONFIGERROR);
      } finally {
         try {
            if (br != null) {
               br.close();
            }

            if (is != null) {
               is.close();
            }
         } catch (IOException var21) {
            this.logger.error("", var21);
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONFIGERROR);
         }

      }

   }

   public V2PrivacyPolicyResource getPrivacyPolicy(String id) throws Exception {
      V2PrivacyPolicyResource privacyPolicy = new V2PrivacyPolicyResource();
      String path = CommonConfig.get("UPLOAD_HOME") + File.separator + "privacy_policy";
      File dir = SecurityUtils.getSafeFile(path);
      if (!dir.exists()) {
         return privacyPolicy;
      } else {
         File[] fileList = dir.listFiles();
         File[] var6 = fileList;
         int var7 = fileList.length;

         for(int var8 = 0; var8 < var7; ++var8) {
            File folder = var6[var8];
            String folderName = folder.getName();
            if (folderName.startsWith(id + "_")) {
               privacyPolicy = this.getPrivacyPolicy(folder);
               this.getPrivacyPolicyFileData(folder, privacyPolicy);
               return privacyPolicy;
            }
         }

         return privacyPolicy;
      }
   }

   public V2SettingCommonResource getCommonSettings() throws Exception {
      V2SettingCommonResource common = new V2SettingCommonResource();
      if (CommonConfig.get("sign_up.enable") != null) {
         common.setSign_up_enable(Boolean.valueOf(CommonConfig.get("sign_up.enable")));
      }

      return common;
   }

   public Map getIcpInfo() throws Exception {
      String leftCertificate = CommonConfig.get("icp.left.certificate.enable");
      String leftIcon = "";
      String leftLink = "";
      String leftText = "";
      if (Boolean.valueOf(leftCertificate)) {
         leftIcon = CommonConfig.get("icp.left.icon.enable");
         leftLink = CommonConfig.get("icp.left.link");
         leftText = CommonConfig.get("icp.left.text");
      }

      String rightCertificate = CommonConfig.get("icp.right.certificate.enable");
      String rightIcon = "";
      String rightLink = "";
      String rightText = "";
      if (Boolean.valueOf(rightCertificate)) {
         rightIcon = CommonConfig.get("icp.right.icon.enable");
         rightLink = CommonConfig.get("icp.right.link");
         rightText = CommonConfig.get("icp.right.text");
      }

      Map hm = new HashMap();
      hm.put("leftCertificate", leftCertificate);
      hm.put("leftIcon", leftIcon);
      hm.put("leftLink", leftLink);
      hm.put("leftText", leftText);
      hm.put("rightCertificate", rightCertificate);
      hm.put("rightIcon", rightIcon);
      hm.put("rightLink", rightLink);
      hm.put("rightText", rightText);
      return hm;
   }
}
