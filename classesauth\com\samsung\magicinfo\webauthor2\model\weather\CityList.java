package com.samsung.magicinfo.webauthor2.model.weather;

import com.samsung.magicinfo.webauthor2.model.weather.CountryData;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "CityDetails")
@XmlAccessorType(XmlAccessType.FIELD)
public class CityList {
  @XmlElement(name = "Country")
  private List<CountryData> countries = null;
  
  public List<CountryData> getCountries() {
    return this.countries;
  }
  
  public void setCountries(List<CountryData> countries) {
    this.countries = countries;
  }
}
