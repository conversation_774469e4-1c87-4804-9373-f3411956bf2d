package com.samsung.magicinfo.restapi.setting.model.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

public class V2LogManagementSettings {
   @JsonProperty("logLevel")
   @ApiModelProperty(
      allowableValues = "ERROR, WARN, INFO",
      example = "ERROR",
      value = "Set Log Level",
      required = false
   )
   private String log_level;
   @JsonProperty("logFileSize")
   @ApiModelProperty(
      dataType = "int",
      example = "100",
      value = "Set log file size (100 - 500 MB)",
      required = false
   )
   private Integer log_file_size;
   @JsonProperty("maxLogFileCount")
   @ApiModelProperty(
      dataType = "int",
      example = "30",
      value = "Set the number of log files  (1 - 100)",
      required = false
   )
   private Integer max_logfile_count;
   @JsonProperty("playerLogCollect")
   @ApiModelProperty(
      dataType = "boolean",
      example = "false",
      value = "Option to enable/disable Player Log Collect",
      required = false
   )
   private Boolean player_log_collect;
   @JsonProperty("logCollectDevices")
   @ApiModelProperty(
      example = "''",
      value = "Devices that be running log colleting",
      required = false
   )
   private List log_collect_devices;
   @JsonProperty("deviceLogCollect")
   @ApiModelProperty(
      dataType = "boolean",
      example = "false",
      value = "Option to enable/disable Device Log Collect",
      required = false
   )
   private Boolean device_log_collect;
   @JsonProperty("deviceLogSelectedDevices")
   @ApiModelProperty(
      example = "''",
      value = "Devices list for Device Log Collect",
      required = false
   )
   private List device_Log_Selected_Devices;

   public V2LogManagementSettings() {
      super();
   }

   public String getLog_level() {
      return this.log_level;
   }

   public void setLog_level(String log_level) {
      this.log_level = log_level;
   }

   public Integer getLog_file_size() {
      return this.log_file_size;
   }

   public void setLog_file_size(Integer log_file_size) {
      this.log_file_size = log_file_size;
   }

   public Integer getMax_logfile_count() {
      return this.max_logfile_count;
   }

   public void setMax_logfile_count(Integer max_logfile_count) {
      this.max_logfile_count = max_logfile_count;
   }

   public Boolean getPlayer_log_collect() {
      return this.player_log_collect;
   }

   public void setPlayer_log_collect(Boolean player_log_collect) {
      this.player_log_collect = player_log_collect;
   }

   public List getLog_collect_devices() {
      return this.log_collect_devices;
   }

   public void setLog_collect_devices(List log_collect_devices) {
      this.log_collect_devices = log_collect_devices;
   }

   public Boolean getDevice_log_collect() {
      return this.device_log_collect;
   }

   public void setDevice_log_collect(Boolean device_log_collect) {
      this.device_log_collect = device_log_collect;
   }

   public List getDevice_Log_Selected_Devices() {
      return this.device_Log_Selected_Devices;
   }

   public void setDevice_Log_Selected_Devices(List device_Log_Selected_Devices) {
      this.device_Log_Selected_Devices = device_Log_Selected_Devices;
   }
}
