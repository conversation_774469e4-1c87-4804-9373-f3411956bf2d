package com.samsung.magicinfo.rc.common.exception;

public class ErrorResponse {
  String errorMessage;
  
  int code;
  
  public void setErrorMessage(String errorMessage) {
    this.errorMessage = errorMessage;
  }
  
  public void setCode(int code) {
    this.code = code;
  }
  
  ErrorResponse(String errorMessage, int code) {
    this.errorMessage = errorMessage;
    this.code = code;
  }
  
  public static ErrorResponseBuilder builder() {
    return new ErrorResponseBuilder();
  }
  
  public boolean equals(Object o) {
    if (o == this)
      return true; 
    if (!(o instanceof com.samsung.magicinfo.rc.common.exception.ErrorResponse))
      return false; 
    com.samsung.magicinfo.rc.common.exception.ErrorResponse other = (com.samsung.magicinfo.rc.common.exception.ErrorResponse)o;
    if (!other.canEqual(this))
      return false; 
    if (getCode() != other.getCode())
      return false; 
    Object this$errorMessage = getErrorMessage(), other$errorMessage = other.getErrorMessage();
    return !((this$errorMessage == null) ? (other$errorMessage != null) : !this$errorMessage.equals(other$errorMessage));
  }
  
  protected boolean canEqual(Object other) {
    return other instanceof com.samsung.magicinfo.rc.common.exception.ErrorResponse;
  }
  
  public int hashCode() {
    int PRIME = 59;
    result = 1;
    result = result * 59 + getCode();
    Object $errorMessage = getErrorMessage();
    return result * 59 + (($errorMessage == null) ? 43 : $errorMessage.hashCode());
  }
  
  public String toString() {
    return "ErrorResponse(errorMessage=" + getErrorMessage() + ", code=" + getCode() + ")";
  }
  
  public String getErrorMessage() {
    return this.errorMessage;
  }
  
  public int getCode() {
    return this.code;
  }
}
