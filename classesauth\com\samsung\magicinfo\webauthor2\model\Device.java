package com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceData;
import java.util.ArrayList;
import java.util.List;

public class Device {
  private String id;
  
  private String groupId;
  
  private Integer modelCode;
  
  private String modelName;
  
  private String name;
  
  private Integer screenSize;
  
  private String location;
  
  private String resolution;
  
  private String deviceType;
  
  private String deviceTypeVersion;
  
  public String getId() {
    return this.id;
  }
  
  public void setId(String id) {
    this.id = id;
  }
  
  public String getGroupId() {
    return this.groupId;
  }
  
  public void setGroupId(String groupId) {
    this.groupId = groupId;
  }
  
  public Integer getModelCode() {
    return this.modelCode;
  }
  
  public void setModelCode(Integer modelCode) {
    this.modelCode = modelCode;
  }
  
  public String getModelName() {
    return this.modelName;
  }
  
  public void setModelName(String modelName) {
    this.modelName = modelName;
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public Integer getScreenSize() {
    return this.screenSize;
  }
  
  public void setScreenSize(Integer screenSize) {
    this.screenSize = screenSize;
  }
  
  public String getLocation() {
    return this.location;
  }
  
  public void setLocation(String location) {
    this.location = location;
  }
  
  public String getResolution() {
    return this.resolution;
  }
  
  public void setResolution(String resolution) {
    this.resolution = resolution;
  }
  
  public String getDeviceType() {
    return this.deviceType;
  }
  
  public void setDeviceType(String deviceType) {
    this.deviceType = deviceType;
  }
  
  public String getDeviceTypeVersion() {
    return this.deviceTypeVersion;
  }
  
  public void setDeviceTypeVersion(String deviceTypeVersion) {
    this.deviceTypeVersion = deviceTypeVersion;
  }
  
  public Device(String id, Integer modelCode, String modelName, String name, Integer screenSize, String location, String resolution, String deviceType, String deviceTypeVersion, String groupId) {
    this.id = id;
    this.modelCode = modelCode;
    this.modelName = modelName;
    this.name = name;
    this.screenSize = screenSize;
    this.location = location;
    this.resolution = resolution;
    this.deviceType = deviceType;
    this.deviceTypeVersion = deviceTypeVersion;
    this.groupId = groupId;
  }
  
  public static com.samsung.magicinfo.webauthor2.model.Device fromData(DeviceData data) {
    return new com.samsung.magicinfo.webauthor2.model.Device(data.getId(), data.getModelCode(), data.getModelName(), data.getName(), data.getScreenSize(), data.getLocation(), data.getResolution(), data.getDeviceType(), data.getDeviceTypeVersion(), data.getGroupId());
  }
  
  public static List<com.samsung.magicinfo.webauthor2.model.Device> fromData(List<DeviceData> dataList) {
    List<com.samsung.magicinfo.webauthor2.model.Device> result = new ArrayList<>();
    for (DeviceData data : dataList)
      result.add(fromData(data)); 
    return result;
  }
}
