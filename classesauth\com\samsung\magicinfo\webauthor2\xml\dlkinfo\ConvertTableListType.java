package com.samsung.magicinfo.webauthor2.xml.dlkinfo;

import com.samsung.magicinfo.webauthor2.xml.dlkinfo.ConvertTableMapType;
import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlRootElement(name = "List")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ConvertTableListType", propOrder = {"convertTableMap"})
public class ConvertTableListType implements Serializable {
  @XmlElement(name = "ConvertTableMap")
  private List<ConvertTableMapType> convertTableMap;
  
  public ConvertTableListType() {}
  
  public ConvertTableListType(List<ConvertTableMapType> convertTableMap) {
    this.convertTableMap = convertTableMap;
  }
  
  public List<ConvertTableMapType> getConvertTableMap() {
    return this.convertTableMap;
  }
  
  public void setConvertTableMap(List<ConvertTableMapType> convertTableMap) {
    this.convertTableMap = convertTableMap;
  }
}
