package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.DiagnosisConstants;
import com.samsung.common.exception.CMSExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.openapi.auth.AuthenticationInfo;
import com.samsung.magicinfo.openapi.auth.TokenRegistry;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.util.TimeUtil;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.InetAddress;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.zip.ZipOutputStream;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import jodd.util.StringUtil;
import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang3.StringUtils;
import org.apache.ftpserver.db.DownloadInfo;
import org.apache.ftpserver.db.DownloadInfoImpl;
import org.apache.logging.log4j.Logger;

public class FileLoaderServlet extends HttpServlet {
   private Boolean AUTHORIZATION_ENABLE = false;
   private static final long serialVersionUID = -4188887666725307081L;
   private static Logger logger = LoggingManagerV2.getLogger(FileLoaderServlet.class);

   public FileLoaderServlet() {
      super();

      try {
         String authEnable = CommonConfig.get("http.fileloader.authorization.enable");
         if ("true".equalsIgnoreCase(authEnable)) {
            this.AUTHORIZATION_ENABLE = true;
         }
      } catch (Exception var2) {
         logger.fatal("Can't load configuration!!");
      }

   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      this.doPost(request, response);
   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      StrUtils ul = new StrUtils();
      request.setCharacterEncoding("UTF-8");
      String device_id = null;
      String filepath = null;
      String fileoffset = null;
      String fullpath = null;
      String file_tm0 = null;
      String file_tm1 = null;
      String file_tm2 = null;
      String download = null;
      String topPath = null;
      String paramPathConfName = null;
      String file_id = null;
      String realname = null;
      BufferedOutputStream os = null;
      File m_file = null;
      FileInputStream fileIS = null;
      FileChannel fileChannel = null;
      File s3_file = null;
      boolean onS3Storage = false;
      String s3Path = null;
      BufferedReader br = request.getReader();
      DownloadInfo var24 = DownloadInfoImpl.getInstance();

      try {
         String userId;
         try {
            device_id = StrUtils.nvl(request.getParameter("device_id"));
            String cmd;
            String iegbn;
            if (this.AUTHORIZATION_ENABLE) {
               cmd = request.getHeader("Authorization");
               userId = request.getHeader("userId");
               iegbn = request.getHeader("token");
               logger.info("Device Authorization Trying... " + userId + ":" + iegbn + ":" + cmd);

               try {
                  if (!StringUtils.isBlank(cmd)) {
                     if (!DeviceUtils.checkDeviceAuthorization(cmd, device_id)) {
                        response.sendError(401, "Device Authorization Failed.");
                        logger.info("Device Authorization Failed." + cmd);
                        throw new Exception("Device Authorization Failed. " + cmd);
                     }
                  } else {
                     if (StringUtils.isBlank(userId) || StringUtils.isBlank(iegbn)) {
                        throw new Exception("userId or token is empty.");
                     }

                     TokenRegistry tr = TokenRegistry.getTokenRegistry();
                     if (!tr.isValidToken(iegbn)) {
                        throw new Exception("Invalid token.");
                     }

                     AuthenticationInfo authenticationInfo = tr.getAuthenticationInfo(iegbn);
                     if (!userId.equals(authenticationInfo.getUserId())) {
                        throw new Exception("Invalid token (userId is not matched).");
                     }
                  }
               } catch (Exception var43) {
                  logger.fatal(var43.getMessage());
                  response.sendError(401, "Authorization Failed.");
                  return;
               }
            }

            paramPathConfName = StrUtils.nvl(request.getParameter("paramPathConfName"));
            fileoffset = request.getParameter("fileoffset");
            cmd = StrUtils.nvl(request.getParameter("cmd"));
            filepath = br.readLine();
            if (paramPathConfName == null) {
               paramPathConfName = (String)request.getAttribute("paramPathConfName");
            }

            try {
               if (paramPathConfName != null && paramPathConfName.length() > 0) {
                  if (paramPathConfName.equals("PRODUCT_HOME")) {
                     topPath = CommonConfig.get("UPLOAD_HOME") + File.separator + "Product" + File.separator;
                  } else if (paramPathConfName.equals("NOTICE_HOME")) {
                     topPath = CommonConfig.get("UPLOAD_HOME") + File.separator + "notice" + File.separator;
                     if (filepath == null || filepath.equals("")) {
                        filepath = StrUtils.nvl(request.getParameter("filepath"));
                     }
                  } else if (paramPathConfName.equals("USER_HOME")) {
                     topPath = CommonConfig.get("UPLOAD_HOME") + File.separator + "User" + File.separator;
                  } else if (paramPathConfName.equals("ADMIN_HOME")) {
                     topPath = CommonConfig.get("UPLOAD_HOME");
                  } else if (paramPathConfName.equals("IMAGE_HOME")) {
                     topPath = CommonConfig.get("IMAGE_HOME") + File.separator;
                     if (filepath == null || filepath.equals("")) {
                        filepath = StrUtils.nvl(request.getParameter("filepath"));
                     }
                  } else if (paramPathConfName.equals("CONTENTS_HOME")) {
                     topPath = CommonConfig.get("CONTENTS_HOME") + File.separator + "contents_home";
                     logger.error("[MagicInfo_HTTP][" + device_id + "] HTTP Content download paramPathConfName=" + paramPathConfName + ",filePath=" + filepath);
                  } else if (paramPathConfName.equals("CAPTURE_DIR")) {
                     topPath = CommonConfig.get("UPLOAD_HOME") + File.separator + CommonConfig.get("CAPTURE_DIR");
                     if (filepath == null || filepath.equals("")) {
                        filepath = StrUtils.nvl(request.getParameter("filepath"));
                     }
                  } else if (paramPathConfName.equals("REMOTE_JOB_RESULT")) {
                     topPath = CommonConfig.get("CONTENTS_HOME") + File.separator + CommonConfig.get("JOBS_DIR") + File.separator + "result";
                     if (filepath == null || filepath.equals("")) {
                        filepath = StrUtils.nvl(request.getParameter("filepath"));
                     }
                  } else if (!paramPathConfName.equals("ALARM_RULE_HOME") && !paramPathConfName.equals("SOFTWARE_HOME")) {
                     if (paramPathConfName.equals("POP_REPORT")) {
                        topPath = CommonConfig.get("UPLOAD_HOME") + File.separator + CommonConfig.get("POP_LOG_DIR") + File.separator + "report";
                        if (filepath == null || filepath.equals("")) {
                           filepath = StrUtils.nvl(request.getParameter("filepath"));
                        }
                     } else if (paramPathConfName.equals("FACE_REPORT")) {
                        topPath = CommonConfig.get("UPLOAD_HOME") + File.separator + CommonConfig.get("FACE_LOG_DIR") + File.separator + "report";
                        if (filepath == null || filepath.equals("")) {
                           filepath = StrUtils.nvl(request.getParameter("filepath"));
                        }
                     } else if (paramPathConfName.equals("SCHEDULE_HOME")) {
                        topPath = CommonConfig.get("SCHEDULE_HOME");
                        if (filepath == null || filepath.equals("")) {
                           filepath = StrUtils.nvl(request.getParameter("filepath"));
                        }
                     } else if (!paramPathConfName.equals("CONTENT")) {
                        if (paramPathConfName.equals("JNLP_HOME")) {
                           topPath = CommonConfig.get("CONTENTS_HOME") + File.separator + "jnlp";
                           if (filepath == null || filepath.equals("")) {
                              filepath = StrUtils.nvl(request.getParameter("filepath"));
                           }
                        } else if (paramPathConfName.equals("JOBS_RESULT_HOME")) {
                           topPath = CommonConfig.get("CONTENTS_HOME") + File.separatorChar + "jobs_home" + File.separatorChar + "result" + File.separatorChar + device_id;
                           if (filepath == null || filepath.equals("")) {
                              filepath = StrUtils.nvl(request.getParameter("filepath"));
                           }
                        } else if (paramPathConfName.equals("COLLECTED_LOG")) {
                           topPath = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "device_log" + File.separatorChar + "downloads";
                           if (filepath == null || filepath.equals("")) {
                              filepath = makeProtectedZipFiles(device_id, StrUtils.nvl(request.getParameter("filepath")));
                           }
                        } else if (paramPathConfName.equals("DIAGNOSIS_LFD")) {
                           topPath = CommonConfig.get("CONTENTS_HOME") + File.separator + CommonConfig.get("JOBS_DIR") + File.separator + "result" + File.separator + "diagnosis_lfd";
                           if (filepath == null || filepath.equals("")) {
                              filepath = StrUtils.nvl(request.getParameter("filepath"));
                           }
                        } else if (paramPathConfName.equals("DIAGNOSIS_SERVER")) {
                           topPath = CommonConfig.get("CONTENTS_HOME") + File.separator + CommonConfig.get("JOBS_DIR") + File.separator + "result" + File.separator + "diagnosis_server";
                           if (filepath == null || filepath.equals("")) {
                              filepath = StrUtils.nvl(request.getParameter("filepath"));
                           }
                        } else if (paramPathConfName.equals("LOGO_DIR")) {
                           topPath = CommonConfig.get("UPLOAD_HOME");
                           if (filepath == null || filepath.equals("")) {
                              filepath = StrUtils.nvl(request.getParameter("filepath"));
                           }
                        } else if (paramPathConfName.equals("VWT_HOME")) {
                           topPath = CommonConfig.get("VWT_HOME");
                           if (filepath == null || filepath.equals("")) {
                              filepath = StrUtils.nvl(request.getParameter("filepath"));
                           }
                        }
                     } else if (CommonConfig.get("saas.eu.enable") != null && CommonConfig.get("saas.eu.enable").equalsIgnoreCase("TRUE") && CommonConfig.get("s3.UPLOAD_HOME") != null) {
                        s3Path = StrUtils.nvl(CommonConfig.get("s3.UPLOAD_HOME"));
                        onS3Storage = true;
                        if (filepath == null || filepath.equals("")) {
                           filepath = StrUtils.nvl(request.getParameter("filepath"));
                        }

                        logger.info("[MagicInfo_HTTP][" + device_id + "] Entered to set s3Path : " + s3Path + " filepath : " + filepath);
                     } else {
                        topPath = CommonConfig.get("CONTENTS_HOME") + File.separator + "contents_home";
                        if (filepath == null || filepath.equals("")) {
                           filepath = StrUtils.nvl(request.getParameter("filepath"));
                        }
                     }
                  } else {
                     topPath = CommonConfig.get("UPLOAD_HOME");
                     if (filepath == null || filepath.equals("")) {
                        filepath = StrUtils.nvl(request.getParameter("filepath"));
                     }
                  }
               } else {
                  topPath = CommonConfig.get("UPLOAD_HOME");
               }
            } catch (Exception var42) {
               logger.error(var42);
            }

            download = request.getParameter("download");
            String[] tmp = null;
            if (filepath != null) {
               filepath = filepath.replace("\\", "/");
               if (cmd.equals("SAVEAS")) {
                  file_tm0 = URLDecoder.decode(filepath, "8859_1");
                  file_tm1 = URLDecoder.decode(filepath, "EUC-KR");
                  file_tm2 = filepath;
                  filepath = file_tm0;
               } else {
                  file_tm1 = filepath;
               }

               if (filepath.indexOf("/") > -1) {
                  tmp = filepath.split("/");
               } else if (filepath.indexOf("\\") > -1) {
                  tmp = filepath.split("\\");
               }

               if (!onS3Storage) {
                  topPath = topPath.replace("\\", "/");
               }

               realname = file_tm1;
               if (tmp != null && tmp.length > 1) {
                  String var10000 = tmp[tmp.length - 2];
                  realname = tmp[tmp.length - 1];
               }

               if (download != null) {
                  if (download.equals("B")) {
                     response.setHeader("Content-Disposition", "filename=" + realname + ";");
                  } else if (download.equals("D")) {
                     URLEncoder.encode(realname, "UTF-8");
                     response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(realname, "UTF-8") + ";");
                     logger.debug("[MagicInfo_HTTP] TEST real name is " + realname);
                  }
               }

               iegbn = request.getHeader("user-agent");
               if (iegbn != null && iegbn.indexOf("MSIE 5.5") != -1) {
                  response.setHeader("Content-Type", "doesn/matter; charset=UTF-8");
               } else {
                  response.setHeader("Content-Type", "application/octet-stream; charset=UTF-8");
               }

               int dotIdx = realname.lastIndexOf(".");
               String hostName;
               if (dotIdx > 0) {
                  String extension = realname.substring(dotIdx + 1, realname.length());
                  hostName = getContentType(extension.toLowerCase());
                  response.setContentType(hostName);
               } else {
                  response.setContentType("application/unknown");
               }

               os = new BufferedOutputStream(response.getOutputStream());
               if (!onS3Storage) {
                  fullpath = topPath + "/" + file_tm1;
                  File file = SecurityUtils.getSafeFile(fullpath);
                  if (!file.exists()) {
                     fullpath = topPath + "/" + file_tm1;
                     file = SecurityUtils.getSafeFile(fullpath);
                  }

                  if (!file.exists()) {
                     fullpath = topPath + "/" + file_tm2;
                     file = SecurityUtils.getSafeFile(fullpath);
                  }

                  fullpath = SecurityUtils.directoryTraversalChecker(fullpath, request.getRemoteAddr());
               }

               hostName = CommonConfig.get("download.server.node.name");
               if (hostName == null) {
                  hostName = InetAddress.getLocalHost().getHostName();
               }

               if (!onS3Storage) {
                  m_file = SecurityUtils.getSafeFile(fullpath);
                  fileIS = new FileInputStream(m_file);
                  fileChannel = fileIS.getChannel();
                  if (fileoffset == null) {
                     fileoffset = "0";
                  }

                  long fileOffsetLong = Long.parseLong(fileoffset);
                  int binaryRead;
                  if (m_file.length() > 0L) {
                     for(ByteBuffer buf = ByteBuffer.allocate(1024); (binaryRead = fileChannel.read(buf, fileOffsetLong)) != -1; fileOffsetLong += (long)binaryRead) {
                        buf.flip();
                        os.write(buf.array(), 0, binaryRead);
                        buf.clear();
                     }
                  }

                  os.close();
                  fileChannel.close();
                  fileIS.close();
               }
            }
         } catch (FileNotFoundException var44) {
            userId = request.getParameter("id");
            if (!StringUtil.isEmpty(userId)) {
               ContentInfo contentInfo = ContentInfoImpl.getInstance();
               contentInfo.deleteFileInfoIfNoExistFile(userId);
            }
         } catch (Exception var45) {
            Exception e = var45;

            try {
               if (!(e instanceof ClientAbortException)) {
                  response.sendError(609, CMSExceptionCode.APP609[2]);
                  logger.error(e);
               }
            } catch (Exception var41) {
               logger.error(var41);
            }
         }
      } finally {
         if (os != null) {
            os.close();
         }

         if (fileChannel != null) {
            fileChannel.close();
         }

         if (fileIS != null) {
            fileIS.close();
         }

      }

   }

   public static String getContentType(String extension) {
      if (extension == null) {
         return null;
      } else {
         Hashtable fileExts = loadContentTypes();
         String contentType = (String)fileExts.get(extension);
         if (contentType == null) {
            contentType = "application/unknown";
         }

         return contentType;
      }
   }

   private static Hashtable loadContentTypes() {
      Hashtable fileExts = new Hashtable();
      fileExts.put("dwg", "application/acad");
      fileExts.put("ccad", "application/clariscad");
      fileExts.put("dxf", "application/dxf");
      fileExts.put("mdb", "application/msaccess");
      fileExts.put("doc", "application/msword");
      fileExts.put("bin", "application/octet-stream");
      fileExts.put("pdf", "application/pdf");
      fileExts.put("ai", "application/postscript");
      fileExts.put("ps", "application/postscript");
      fileExts.put("eps", "application/postscript");
      fileExts.put("rtf", "application/rtf");
      fileExts.put("xls", "application/vnd.ms-excel");
      fileExts.put("ppt", "application/vnd.ms-powerpoint");
      fileExts.put("cdf", "application/x-cdf");
      fileExts.put("csh", "application/x-csh");
      fileExts.put("dvi", "application/x-dvi");
      fileExts.put("js", "application/x-javascript");
      fileExts.put("latex", "application/x-latex");
      fileExts.put("mif", "application/x-mif");
      fileExts.put("xls", "application/x-msexcel");
      fileExts.put("tcl", "application/x-tcl");
      fileExts.put("tex", "application/x-tex");
      fileExts.put("texinfo", "application/x-texinfo");
      fileExts.put("texi", "application/x-texinfo");
      fileExts.put("t", "application/x-troff");
      fileExts.put("tr", "application/x-troff");
      fileExts.put("roff", "application/x-troff");
      fileExts.put("man", "application/x-troff-man");
      fileExts.put("me", "application/x-troff-me");
      fileExts.put("ms", "application/x-troff-ms");
      fileExts.put("src", "application/x-wais-source");
      fileExts.put("zip", "application/zip");
      fileExts.put("au", "audio/basic");
      fileExts.put("snd", "audio/basic");
      fileExts.put("aif", "audio/x-aiff");
      fileExts.put("aiff", "audio/x-aiff");
      fileExts.put("aifc", "audio/x-aiff");
      fileExts.put("wav", "audio/x-wav");
      fileExts.put("gif", "image/gif");
      fileExts.put("bmp", "image/bmp");
      fileExts.put("png", "image/png");
      fileExts.put("ief", "image/ief");
      fileExts.put("jpeg", "image/jpeg");
      fileExts.put("jpg", "image/jpeg");
      fileExts.put("jpe", "image/jpeg");
      fileExts.put("tiff", "image/tiff");
      fileExts.put("tif", "image/tiff");
      fileExts.put("ras", "image/x-cmu-raster");
      fileExts.put("pnm", "image/x-portable-anymap");
      fileExts.put("pbm", "image/x-portable-bitmap");
      fileExts.put("pgm", "image/x-portable-graymap");
      fileExts.put("ppm", "image/x-portable-pixmap");
      fileExts.put("rgb", "image/x-rgb");
      fileExts.put("xbm", "image/x-xbitmap");
      fileExts.put("xpm", "image/x-xpixmap");
      fileExts.put("xwd", "image/x-xwindowdump");
      fileExts.put("gzip", "multipart/x-gzip");
      fileExts.put("css", "text/css");
      fileExts.put("html", "text/html");
      fileExts.put("htm", "text/html");
      fileExts.put("txt", "text/plain");
      fileExts.put("rtx", "text/richtext");
      fileExts.put("tsv", "text/tab-separated-values");
      fileExts.put("xml", "text/xml");
      fileExts.put("etx", "text/x-setext");
      fileExts.put("xsl", "text/xsl");
      fileExts.put("mpeg", "video/mpeg");
      fileExts.put("mpg", "video/mpeg");
      fileExts.put("mpe", "video/mpeg");
      fileExts.put("qt", "video/quicktime");
      fileExts.put("mov", "video/quicktime");
      fileExts.put("avi", "video/x-msvideo");
      fileExts.put("movie", "video/x-sgi-movie");
      fileExts.put("mp4", "video/mp4");
      return fileExts;
   }

   private static String makeZipFiles(String deviceId, String fileNames) throws Exception {
      String downloadPath = null;
      String DeviceLogPath = null;
      FileOutputStream fos = null;
      ZipOutputStream zos = null;
      String zipFilename = "";

      try {
         logger.error("[MagicInfo_HTTP] Log Collect - make zip fils");
         DeviceLogPath = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "device_log";
         downloadPath = DeviceLogPath + File.separatorChar + "downloads";
         File downloadPahHome = SecurityUtils.getSafeFile(downloadPath);
         if (!downloadPahHome.exists()) {
            downloadPahHome.mkdir();
         }

         TimeUtil util = new TimeUtil();
         Timestamp startTime = DateUtils.dateTime2TimeStamp(TimeUtil.getCurrentGMTTime());
         SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
         String token = sdf.format(startTime);
         zipFilename = deviceId + "_" + token + ".zip";
         String zipFilePath = downloadPath + File.separator + zipFilename;
         fos = new FileOutputStream(zipFilePath);
         zos = new ZipOutputStream(fos);
         String[] logFileList = fileNames.split(",");
         String[] var14 = logFileList;
         int var15 = logFileList.length;

         for(int var16 = 0; var16 < var15; ++var16) {
            String fileName = var14[var16];
            String filePath = DeviceLogPath + File.separatorChar + deviceId + File.separatorChar + fileName;
            CommonUtils.addToZipFile(filePath, zos);
         }
      } catch (IOException | ConfigException var22) {
         logger.error("", var22);
      } finally {
         zos.close();
         fos.close();
      }

      return zipFilename;
   }

   private static String makeProtectedZipFiles(String deviceId, String fileNames) throws Exception {
      String downloadPath = null;
      String DeviceLogPath = null;
      String zipFilename = "";

      try {
         logger.error("[MagicInfo_HTTP] Log Collect - make zip fils");
         DeviceLogPath = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "device_log";
         downloadPath = DeviceLogPath + File.separatorChar + "downloads";
         File downloadPahHome = SecurityUtils.getSafeFile(downloadPath);
         if (!downloadPahHome.exists()) {
            downloadPahHome.mkdir();
         }

         TimeUtil util = new TimeUtil();
         Timestamp startTime = DateUtils.dateTime2TimeStamp(TimeUtil.getCurrentGMTTime());
         SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
         String token = sdf.format(startTime);
         zipFilename = deviceId + "_" + token + ".zip";
         String zipFilePath = downloadPath + File.separator + zipFilename;
         ZipFile zipFile = new ZipFile(zipFilePath);
         ArrayList filesToAdd = new ArrayList();
         String[] logFileList = fileNames.split(",");
         String[] var14 = logFileList;
         int var15 = logFileList.length;

         String password;
         String diagnosticFilePath;
         for(int var16 = 0; var16 < var15; ++var16) {
            password = var14[var16];
            diagnosticFilePath = DeviceLogPath + File.separatorChar + deviceId + File.separatorChar + password;
            filesToAdd.add(new File(diagnosticFilePath));
         }

         String encryption = StrUtils.nvl(CommonConfig.get("device.log_collect.encryption"));
         String keyfilepath = null;
         if (encryption != null && encryption.equalsIgnoreCase("true")) {
            String filePath = DeviceLogPath + File.separatorChar + deviceId + File.separatorChar + DiagnosisConstants.key;
            File keyFile = SecurityUtils.getSafeFile(filePath);
            if (keyFile.exists()) {
               filesToAdd.add(keyFile);
            }

            diagnosticFilePath = DeviceLogPath + File.separatorChar + deviceId + File.separatorChar + DiagnosisConstants.diagnosticKey;
            File diagnosticKeyFile = SecurityUtils.getSafeFile(diagnosticFilePath);
            if (diagnosticKeyFile.exists()) {
               filesToAdd.add(diagnosticKeyFile);
            }
         }

         ZipParameters parameters = new ZipParameters();
         parameters.setCompressionMethod(8);
         parameters.setCompressionLevel(5);
         parameters.setEncryptFiles(true);
         parameters.setEncryptionMethod(99);
         parameters.setAesKeyStrength(3);
         password = token.substring(8, 14);
         logger.error("[MagicInfo_HTTP] zipFilename : " + zipFilename + " , password : " + password);
         parameters.setPassword(password);
         zipFile.addFiles(filesToAdd, parameters);
         if (encryption != null && encryption.equalsIgnoreCase("true") && keyfilepath != null) {
            File delFile = SecurityUtils.getSafeFile((String)keyfilepath);
            delFile.delete();
         }
      } catch (ConfigException var20) {
         logger.error("", var20);
      }

      return zipFilename;
   }
}
