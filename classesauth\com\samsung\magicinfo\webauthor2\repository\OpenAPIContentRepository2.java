package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.ContentGroup;
import com.samsung.magicinfo.webauthor2.model.ContentThumbnailBasic;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.repository.model.ContentData;
import com.samsung.magicinfo.webauthor2.repository.model.criteria.ContentSearchCriteria;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface OpenAPIContentRepository2 {
  ContentData getContent(String paramString);
  
  Page<Content> getContentList(Pageable paramPageable, DeviceType paramDeviceType, ContentSearchCriteria paramContentSearchCriteria);
  
  List<ContentGroup> getContentGroupList();
  
  List<ContentData> getRelatedDLKContent(String paramString);
  
  List<ContentThumbnailBasic> getContentThumbnails(String paramString1, String paramString2);
  
  ContentThumbnailBasic getContentThumbnail(String paramString1, String paramString2);
  
  List<String> getMediaTypeList(DeviceType paramDeviceType);
  
  String deleteContent(String paramString);
}
