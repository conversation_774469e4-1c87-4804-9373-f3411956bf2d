package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.exception.service.FfmpegException;
import com.samsung.magicinfo.webauthor2.service.FFmpegService;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.io.IOException;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.Collections;
import javax.servlet.ServletContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Service
public class FFmpegServiceImpl implements FFmpegService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.FFmpegServiceImpl.class);
  
  private static final String LIB_EXE = "ffmpeg.exe";
  
  private static final String LIB_LOCATION = "WEB-INF/lib/ext";
  
  private RestTemplate restTemplate;
  
  private ServletContext servletContext;
  
  private final UserData userData;
  
  @Autowired
  public FFmpegServiceImpl(RestTemplate restTemplate, ServletContext servletContext, UserData userData) {
    this.restTemplate = restTemplate;
    this.servletContext = servletContext;
    this.userData = userData;
  }
  
  public Path getFFmpeg() {
    try {
      Path ffmpegPath = Paths.get(this.servletContext.getRealPath("WEB-INF/lib/ext"), new String[] { "ffmpeg.exe" });
      Path ffmpegPathParent = ffmpegPath.getParent();
      if (Files.notExists(ffmpegPath, new java.nio.file.LinkOption[0]) && ffmpegPathParent != null) {
        Files.createDirectories(ffmpegPathParent, (FileAttribute<?>[])new FileAttribute[0]);
        downloadFFmpegFromMagicInfoServer(ffmpegPath);
        logger.info("FFmpeg successfully saved to: " + ffmpegPathParent);
      } 
      return ffmpegPath;
    } catch (IOException e) {
      logger.error("Error during FFmpeg download: " + e.getMessage());
      throw new FfmpegException(500, "Ffmpeg error");
    } 
  }
  
  private void downloadFFmpegFromMagicInfoServer(Path ffmpegPath) throws IOException {
    URI uri = UriComponentsBuilder.newInstance().path("/servlet/FFmpegDownload").build().encode().toUri();
    HttpHeaders headers = new HttpHeaders();
    headers.setAccept(Collections.singletonList(MediaType.APPLICATION_OCTET_STREAM));
    headers.add("userId", this.userData.getUserId());
    headers.add("token", this.userData.getToken());
    HttpEntity<String> request = new HttpEntity((MultiValueMap)headers);
    ResponseEntity<byte[]> response = this.restTemplate.exchange(uri, HttpMethod.GET, request, byte[].class);
    if (response.getStatusCode() == HttpStatus.OK)
      Files.write(ffmpegPath, (byte[])response.getBody(), new java.nio.file.OpenOption[0]); 
  }
}
