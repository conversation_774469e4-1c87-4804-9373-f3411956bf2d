package com.samsung.magicinfo.protocol.security.ca;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.protocol.security.ca.dao.CertificateDAO;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigInteger;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Security;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Calendar;
import java.util.Date;
import java.util.Hashtable;
import org.apache.logging.log4j.Logger;
import org.apache.xml.security.utils.Base64;
import org.bouncycastle.jce.X509Principal;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.x509.X509V3CertificateGenerator;

public class CertificateManagerImpl implements CertificateManager {
   private Logger logger = LoggingManagerV2.getLogger(CertificateManagerImpl.class);
   private PrivateKey privatekey;
   private PublicKey publickey;
   private X509Certificate serverCert;
   private CertificateDAO dao;
   private static volatile CertificateManager certiManager;
   private String identityKeyStorePath;
   private String identityKeyStorePass;
   private String trustKeyStorePath;
   private String trustKeyStorePass;

   public static CertificateManager getInstance() throws Exception {
      if (certiManager == null) {
         Class var0 = CertificateManagerImpl.class;
         synchronized(CertificateManagerImpl.class) {
            if (certiManager == null) {
               certiManager = new CertificateManagerImpl();
            }
         }
      }

      return certiManager;
   }

   private CertificateManagerImpl() {
      super();
      Security.addProvider(new BouncyCastleProvider());
      this.dao = new CertificateDAO();

      try {
         this.identityKeyStorePath = CommonConfig.get("keystore.identity.path");
         this.identityKeyStorePass = CommonConfig.get("keystore.identity.password");
         this.trustKeyStorePath = CommonConfig.get("keystore.trust.path");
         this.trustKeyStorePass = CommonConfig.get("keystore.trust.password");
         String alias = CommonConfig.get("server.key.alias");
         String keyPass = CommonConfig.get("server.key.password");
         KeyStore ks = KeyStore.getInstance("JKS");
         FileInputStream fis = null;

         try {
            fis = new FileInputStream(SecurityUtils.directoryTraversalChecker(this.identityKeyStorePath, (String)null));
            ks.load(fis, this.identityKeyStorePass.toCharArray());
         } catch (Exception var15) {
            System.out.println();
            System.out.println("CertificateManagerImpl TRUST PATH==== " + this.trustKeyStorePath);
            System.out.println("CertificateManagerImpl TRUST PASS====" + this.trustKeyStorePass);
            this.logger.error("", var15);
         } finally {
            if (fis != null) {
               try {
                  fis.close();
               } catch (Exception var14) {
                  this.logger.error("", var14);
               }
            }

         }

         this.privatekey = (PrivateKey)ks.getKey(alias, keyPass.toCharArray());
         this.serverCert = (X509Certificate)ks.getCertificate(alias);
         this.publickey = this.serverCert.getPublicKey();
      } catch (Exception var17) {
         this.logger.error("", var17);
      }

   }

   public X509Certificate issueCertificate(String deviceID, PublicKey publickey) throws Exception {
      X509Certificate cert = null;
      Hashtable attrs = new Hashtable();
      Calendar cal = Calendar.getInstance();
      attrs.put(X509Principal.CN, deviceID);
      attrs.put(X509Principal.OU, "SWL");
      attrs.put(X509Principal.O, "SAMSUNG SEC");
      attrs.put(X509Principal.L, "SUWON");
      attrs.put(X509Principal.ST, "KYUNGGI");
      attrs.put(X509Principal.C, "KR");
      X509V3CertificateGenerator certGen = new X509V3CertificateGenerator();
      int serial = (int)System.currentTimeMillis();
      if (serial < 0) {
         serial *= -1;
      }

      certGen.setSerialNumber(BigInteger.valueOf((long)serial));
      certGen.setIssuerDN(this.serverCert.getSubjectX500Principal());
      certGen.setNotBefore(cal.getTime());
      cal.add(1, 10);
      certGen.setNotAfter(cal.getTime());
      certGen.setSubjectDN(new X509Principal(attrs));
      certGen.setPublicKey(publickey);
      certGen.setSignatureAlgorithm("MD5withRSA");
      cert = certGen.generateX509Certificate(this.privatekey);
      cert.checkValidity(new Date(System.currentTimeMillis()));
      cert.verify(this.publickey);
      this.dao.setCertificate(new Long(cert.getSerialNumber().longValue()), deviceID, Base64.encode(cert.getEncoded()));
      KeyStore ks = KeyStore.getInstance("JKS");
      FileInputStream fis = null;

      try {
         fis = new FileInputStream(SecurityUtils.directoryTraversalChecker(this.trustKeyStorePath, (String)null));
         ks.load(fis, this.trustKeyStorePass.toCharArray());
      } catch (Exception var32) {
         this.logger.error("issue certificate TRUST PATH==== " + this.trustKeyStorePath);
         this.logger.error("issue certificate TRUST PASS====" + this.trustKeyStorePass);
      } finally {
         if (fis != null) {
            try {
               fis.close();
            } catch (Exception var31) {
               this.logger.error("", var31);
            }
         }

      }

      if (ks.containsAlias(deviceID)) {
         ks.deleteEntry(deviceID);
      }

      ks.setCertificateEntry(deviceID, cert);
      FileOutputStream fos = null;

      try {
         fos = new FileOutputStream(this.trustKeyStorePath);
         ks.store(fos, this.trustKeyStorePass.toCharArray());
      } finally {
         if (fos != null) {
            try {
               fos.close();
            } catch (Exception var30) {
               this.logger.error("", var30);
            }
         }

      }

      return cert;
   }

   public boolean revokeCertificate(String deviceID) throws Exception {
      int rt = this.dao.revokeCertificate(deviceID);
      return rt != 0;
   }

   public X509Certificate getSvrCertificate() throws Exception {
      return this.serverCert;
   }

   public PrivateKey getSvrPrivateKey() throws Exception {
      return this.privatekey;
   }

   public X509Certificate getClientCertificate(String deviceID) throws Exception {
      String clCertStr = this.dao.getCertificate(deviceID);
      InputStream in = new ByteArrayInputStream(Base64.decode(clCertStr));
      CertificateFactory fact = CertificateFactory.getInstance("X.509", "BC");
      return (X509Certificate)fact.generateCertificate(in);
   }

   public void verifyCertificate(X509Certificate certificate) throws Exception {
      certificate.verify(this.publickey);
   }
}
