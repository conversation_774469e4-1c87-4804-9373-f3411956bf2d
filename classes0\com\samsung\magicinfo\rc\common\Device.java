package com.samsung.magicinfo.rc.common;

import com.samsung.magicinfo.rc.framework.queue.entity.ClientEntity;
import java.util.LinkedList;
import java.util.Queue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Device {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.common.Device.class);
  
  String deviceId;
  
  long interval;
  
  String resolution;
  
  int width;
  
  int height;
  
  boolean supportKeyboard;
  
  boolean supportMouse;
  
  boolean supportRemote;
  
  boolean supportLongPress;
  
  Queue<ClientEntity> queue;
  
  public Device(String deviceId, long interval, String resolution, boolean supportKeyboard, boolean supportMouse, boolean supportLongPress, boolean supportRemote) {
    this.deviceId = deviceId;
    this.interval = interval;
    this.resolution = resolution;
    this.supportRemote = supportRemote;
    this.supportLongPress = supportLongPress;
    try {
      if (resolution != null && !resolution.equals("") && resolution.indexOf("*") > -1) {
        String[] resolutionArray = resolution.split("\\*", 2);
        this.width = Integer.valueOf(resolutionArray[0]).intValue();
        this.height = Integer.valueOf(resolutionArray[1]).intValue();
      } else {
        this.width = 1920;
        this.height = 1080;
      } 
    } catch (Exception e) {
      this.width = 1920;
      this.height = 1080;
      log.error(e.getMessage());
    } 
    this.supportKeyboard = supportKeyboard;
    this.supportMouse = supportMouse;
    this.queue = new LinkedList<>();
  }
  
  public String getResolution() {
    return this.resolution;
  }
  
  public void setResolution(String resolution) {
    this.resolution = resolution;
  }
  
  public String getDeviceId() {
    return this.deviceId;
  }
  
  public void setDeviceId(String deviceId) {
    this.deviceId = deviceId;
  }
  
  public long getInterval() {
    return this.interval;
  }
  
  public void setInterval(long interval) {
    this.interval = interval;
  }
  
  public boolean isSupportKeyboard() {
    return this.supportKeyboard;
  }
  
  public void setSupportKeyboard(boolean supportKeyboard) {
    this.supportKeyboard = supportKeyboard;
  }
  
  public Queue<ClientEntity> getQueue() {
    return this.queue;
  }
  
  public void setQueue(Queue<ClientEntity> queue) {
    this.queue = queue;
  }
  
  public boolean isSupportMouse() {
    return this.supportMouse;
  }
  
  public void setSupportMouse(boolean supportMouse) {
    this.supportMouse = supportMouse;
  }
  
  public int getWidth() {
    return this.width;
  }
  
  public void setWidth(int width) {
    this.width = width;
  }
  
  public int getHeight() {
    return this.height;
  }
  
  public void setHeight(int height) {
    this.height = height;
  }
  
  public boolean isSupportRemote() {
    return this.supportRemote;
  }
  
  public void setSupportRemote(boolean supportRemote) {
    this.supportRemote = supportRemote;
  }
  
  public boolean isSupportLongPress() {
    return this.supportLongPress;
  }
  
  public void setSupportLongPress(boolean supportLongPress) {
    this.supportLongPress = supportLongPress;
  }
}
