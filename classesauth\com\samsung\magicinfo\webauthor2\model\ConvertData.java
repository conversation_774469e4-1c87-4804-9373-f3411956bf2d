package com.samsung.magicinfo.webauthor2.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.samsung.magicinfo.webauthor2.model.ConvertType;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertDataData;
import java.util.ArrayList;
import java.util.List;

public class ConvertData {
  @JsonIgnore
  private String name;
  
  @JsonIgnore
  private ConvertType convertType;
  
  private String from;
  
  private String to;
  
  @JsonIgnore
  private String createdDate;
  
  public ConvertData() {}
  
  public ConvertData(String name, ConvertType convertType, String from, String to, String createdDate) {
    this.name = name;
    this.convertType = convertType;
    this.from = from;
    this.to = to;
    this.createdDate = createdDate;
  }
  
  public static com.samsung.magicinfo.webauthor2.model.ConvertData fromData(ConvertDataData data) {
    return new com.samsung.magicinfo.webauthor2.model.ConvertData(data.getName(), data.getConvertType(), data.getFrom(), data.getTo(), data.getCreatedDate());
  }
  
  public ConvertDataData toData() {
    return new ConvertDataData(this.name, this.convertType, this.from, this.to, this.createdDate);
  }
  
  static List<com.samsung.magicinfo.webauthor2.model.ConvertData> fromData(List<ConvertDataData> data) {
    List<com.samsung.magicinfo.webauthor2.model.ConvertData> tempList = new ArrayList<>();
    for (ConvertDataData dataData : data)
      tempList.add(fromData(dataData)); 
    return tempList;
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public ConvertType getConvertType() {
    return this.convertType;
  }
  
  public void setConvertType(ConvertType convertType) {
    this.convertType = convertType;
  }
  
  public String getFrom() {
    return this.from;
  }
  
  public void setFrom(String from) {
    this.from = from;
  }
  
  public String getTo() {
    return this.to;
  }
  
  public void setTo(String to) {
    this.to = to;
  }
  
  public String getCreatedDate() {
    return this.createdDate;
  }
  
  public void setCreatedDate(String createdDate) {
    this.createdDate = createdDate;
  }
}
