package com.samsung.common.utils;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import java.util.List;
import java.util.Scanner;
import org.apache.logging.log4j.Logger;

public class HWUniqueKey {
   static Logger logger = LoggingManagerV2.getLogger(HWUniqueKey.class);
   private static String DEF_MAC = null;
   private static String DEF_CPUID = null;
   private static String DEF_BOARDID = null;
   private static String DEF_UNIQUEKEY = null;
   private static String KEY_HASH_TYPE = null;

   public HWUniqueKey() {
      super();
   }

   public static String stringXOR(String code, String key) {
      try {
         byte[] keyChar = new byte[key.getBytes().length];
         keyChar = key.getBytes("utf8");
         byte[] codeChar = new byte[code.getBytes().length];
         codeChar = code.getBytes("utf8");
         int i = 0;

         for(int j = 0; i < code.getBytes().length; ++i) {
            codeChar[i] ^= keyChar[i];
            ++j;
            j = j < keyChar.length ? j : 0;
         }

         return new String(codeChar, "utf8");
      } catch (Exception var6) {
         logger.error("", var6);
         return null;
      }
   }

   public static byte[] byteXOR(byte[] data, byte[] key) {
      int orgLength = data.length;
      int keyLength = key.length;
      byte[] converted = new byte[orgLength];
      int i = 0;

      for(int j = 0; i < orgLength; ++j) {
         converted[i] = (byte)(data[i] ^ key[j]);
         if (j >= keyLength - 1) {
            j = 0;
         }

         ++i;
      }

      return converted;
   }

   private static void initHWKeys() {
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();

      try {
         if (DEF_MAC == null) {
            DEF_MAC = serverSetupDao.getSecretValue(3);
         }

         if (DEF_BOARDID == null) {
            DEF_BOARDID = serverSetupDao.getSecretValue(5);
         }

         if (DEF_CPUID == null) {
            DEF_CPUID = serverSetupDao.getSecretValue(4);
         }

         DEF_UNIQUEKEY = DEF_MAC + DEF_BOARDID;
      } catch (SQLException var3) {
         logger.error("", var3);
      }

      try {
         if (CommonConfig.get("slm.license.key_hash_type") != null) {
            KEY_HASH_TYPE = CommonConfig.get("slm.license.key_hash_type");
         }
      } catch (ConfigException var2) {
         logger.error("Error during get config.");
      }

   }

   public static String makeHWUniqueKey() {
      String result = null;
      if (DEF_MAC == null || DEF_BOARDID == null || DEF_CPUID == null) {
         initHWKeys();
      }

      String mac = DEF_MAC;
      String boardId = DEF_BOARDID;

      try {
         SlmLicenseManagerImpl mgr = SlmLicenseManagerImpl.getInstance();
         List hwList = SlmLicenseManagerImpl.getMac();
         mac = (String)hwList.get(0);
      } catch (Exception var6) {
         logger.error("", var6);
         mac = DEF_MAC;
      }

      logger.error("HWuniqueKey Mac address : " + mac);

      try {
         boardId = getMotherboardSN();
      } catch (Exception var5) {
         logger.error("", var5);
         boardId = DEF_BOARDID;
      }

      logger.error("HWuniqueKey Mac address : " + boardId.toUpperCase().trim());

      try {
         if ("SHA2".equals(KEY_HASH_TYPE)) {
            result = SecurityUtils.getHashSha(mac.toUpperCase().trim() + boardId.toUpperCase().trim(), 2);
         } else {
            result = MD5(mac.toUpperCase().trim() + boardId.toUpperCase().trim());
         }
      } catch (Exception var7) {
         logger.error("", var7);
         if ("SHA2".equals(KEY_HASH_TYPE)) {
            result = SecurityUtils.getHashSha(DEF_UNIQUEKEY, 2);
         } else {
            result = MD5(DEF_UNIQUEKEY);
         }
      }

      logger.error("After Hash : " + result.toString());
      return result.length() > 16 ? result.substring(0, 16) : result;
   }

   public static String MD5(String md5) {
      try {
         MessageDigest md = MessageDigest.getInstance("MD5");
         byte[] array = md.digest(md5.getBytes());
         StringBuffer sb = new StringBuffer();

         for(int i = 0; i < array.length; ++i) {
            sb.append(Integer.toHexString(array[i] & 255 | 256).substring(1, 3));
         }

         return sb.toString().toUpperCase();
      } catch (NoSuchAlgorithmException var5) {
         logger.error("", var5);
         return md5.toUpperCase();
      }
   }

   public static String BinToHex(byte[] buf) {
      String res = "";
      String token = "";

      for(int ix = 0; ix < buf.length; ++ix) {
         token = Integer.toHexString(buf[ix]);
         if (token.length() >= 2) {
            token = token.substring(token.length() - 2);
         } else {
            for(int jx = 0; jx < 2 - token.length(); ++jx) {
               token = "0" + token;
            }
         }

         res = res + " " + token;
      }

      return res.toUpperCase();
   }

   private static String getMotherboardSN() {
      String sn = null;
      OutputStream os = null;
      InputStream is = null;
      Runtime runtime = Runtime.getRuntime();
      Process process = null;

      try {
         process = runtime.exec(new String[]{"wmic", "bios", "get", "serialnumber"});
      } catch (IOException var16) {
         throw new RuntimeException(var16);
      }

      os = process.getOutputStream();
      is = process.getInputStream();

      try {
         os.close();
      } catch (IOException var15) {
         throw new RuntimeException(var15);
      }

      Scanner sc = new Scanner(is);

      try {
         while(sc.hasNext()) {
            String next = sc.next();
            if ("SerialNumber".equals(next)) {
               sn = sc.next().trim();
               break;
            }
         }
      } finally {
         try {
            is.close();
            sc.close();
         } catch (IOException var14) {
            throw new RuntimeException(var14);
         }
      }

      if (sn == null) {
         logger.info("Cannot find computer SN");
      } else {
         sn = sn.replaceAll("-", "");
      }

      return sn;
   }
}
