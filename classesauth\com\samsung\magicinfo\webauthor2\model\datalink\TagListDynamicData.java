package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.samsung.magicinfo.webauthor2.model.datalink.TagList;
import com.samsung.magicinfo.webauthor2.model.datalink.TagMatchType;
import java.util.List;

public class TagListDynamicData extends TagList {
  private final String dataLinkTagColumn;
  
  @JsonCreator
  public TagListDynamicData(@JsonProperty("tags") List<String> tags, @JsonProperty("tagMatchType") TagMatchType tagMatchType, @JsonProperty("dataLinkTagColumn") String dataLinkTagColumn) {
    super(tags, tagMatchType);
    this.dataLinkTagColumn = dataLinkTagColumn;
  }
  
  public String getDataLinkTagColumn() {
    return this.dataLinkTagColumn;
  }
}
