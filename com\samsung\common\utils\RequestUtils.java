package com.samsung.common.utils;

import com.samsung.common.config.CommonConfig;
import com.samsung.magicinfo.mvc.security.XssProtectedRequestWrapper;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

public class RequestUtils {
   public RequestUtils() {
      super();
   }

   public static String getOriginalParameter(HttpServletRequest request, String parameterName) {
      if (!(request instanceof XssProtectedRequestWrapper)) {
         throw new IllegalArgumentException("Illegal use. Request skipped XSS validation filter.");
      } else {
         return ((XssProtectedRequestWrapper)request).getOriginalParameter(parameterName);
      }
   }

   public static String getProtocolScheme(HttpServletRequest request) {
      return request.isSecure() ? "https://" : "http://";
   }

   public static String getWebUrl(HttpServletRequest request) throws ConfigException {
      String webUrl = "";
      if (CommonConfig.get("loadbalancer.enable") != null && CommonConfig.get("loadbalancer.enable").equalsIgnoreCase("true")) {
         if (CommonConfig.get("loadbalancer.ssl") != null && CommonConfig.get("loadbalancer.ssl").equalsIgnoreCase("true")) {
            webUrl = webUrl + "https://";
         } else if (CommonConfig.get("loadbalancer.ssl") != null && CommonConfig.get("loadbalancer.ssl").equalsIgnoreCase("false")) {
            webUrl = webUrl + "http://";
         } else if (request.getHeader("x-forwarded-proto") != null && request.getHeader("x-forwarded-proto").equalsIgnoreCase("http")) {
            webUrl = webUrl + "http://";
         } else if (request.getHeader("x-forwarded-proto") != null && request.getHeader("x-forwarded-proto").equalsIgnoreCase("https")) {
            webUrl = webUrl + "https://";
         } else {
            webUrl = webUrl + "https://";
         }

         webUrl = webUrl + request.getServerName() + ":";
         if (CommonConfig.get("loadbalancer.port") != null) {
            webUrl = webUrl + CommonConfig.get("loadbalancer.port");
         } else if (request.getHeader("x-forwarded-port") != null) {
            webUrl = webUrl + request.getHeader("x-forwarded-port");
         } else {
            webUrl = webUrl + String.valueOf(request.getServerPort());
         }

         webUrl = webUrl + "/MagicInfo";
      } else {
         String scheme = request.getHeader("x-forwarded-proto");
         webUrl = "http://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath();
         if (request.isSecure() || "https".equalsIgnoreCase(scheme)) {
            return webUrl.replace("http://", "https://");
         }
      }

      return webUrl;
   }

   public static String getIpAddress() {
      ServletRequestAttributes requestAttributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
      return requestAttributes != null && requestAttributes.getRequest() != null ? requestAttributes.getRequest().getRemoteAddr() : "";
   }
}
