package com.samsung.magicinfo.restapi.user.controller;

import com.samsung.magicinfo.mvc.handler.ApiVersion;
import com.samsung.magicinfo.restapi.user.service.V2PIIMigrationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/restapi/v2.0/ums"})
@Validated
@ApiVersion({2.0D})
public class V2PIIMigrationController {
   @Autowired
   private V2PIIMigrationService v2PIIMigrationService;

   public V2PIIMigrationController() {
      super();
   }

   @PostMapping(
      value = {"/pii/migrate"},
      produces = {"application/json"}
   )
   public void migrateUser() throws Exception {
      this.v2PIIMigrationService.PIIDataMigrator();
   }

   @PostMapping(
      value = {"/pii/rollback"},
      produces = {"application/json"}
   )
   public void rollBackUser() throws Exception {
      this.v2PIIMigrationService.PIIDataRollBack();
   }
}
