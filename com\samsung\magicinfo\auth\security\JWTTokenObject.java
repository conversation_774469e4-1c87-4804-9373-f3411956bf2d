package com.samsung.magicinfo.auth.security;

import java.util.List;

public class JWTTokenObject {
   private String exp;
   private String user_name;
   private List authorities;
   private String jti;
   private String client_id;
   private List scopes;

   public JWTTokenObject() {
      super();
   }

   public String getExp() {
      return this.exp;
   }

   public void setExp(String exp) {
      this.exp = exp;
   }

   public String getUser_name() {
      return this.user_name;
   }

   public void setUser_name(String user_name) {
      this.user_name = user_name;
   }

   public List getAuthorities() {
      return this.authorities;
   }

   public void setAuthorities(List authorities) {
      this.authorities = authorities;
   }

   public String getJti() {
      return this.jti;
   }

   public void setJti(String jti) {
      this.jti = jti;
   }

   public String getClient_id() {
      return this.client_id;
   }

   public void setClient_id(String client_id) {
      this.client_id = client_id;
   }

   public List getScopes() {
      return this.scopes;
   }

   public void setScopes(List scopes) {
      this.scopes = scopes;
   }
}
