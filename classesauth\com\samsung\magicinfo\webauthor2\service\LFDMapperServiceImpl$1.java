package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.service.LFDMapperServiceImpl;
import java.util.List;
import org.xml.sax.ErrorHandler;
import org.xml.sax.SAXException;
import org.xml.sax.SAXParseException;

class null implements <PERSON>rror<PERSON>andler {
  public void warning(SAXParseException exception) throws SAXException {
    exceptions.add(exception);
  }
  
  public void fatalError(SAXParseException exception) throws SAXException {
    exceptions.add(exception);
  }
  
  public void error(SAXParseException exception) throws SAXException {
    exceptions.add(exception);
  }
}
