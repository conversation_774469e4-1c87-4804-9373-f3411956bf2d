package com.samsung.magicinfo.ums.controller;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.magicinfo.framework.kpi.annotation.KPI;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.ums.model.UserFilter;
import com.samsung.magicinfo.ums.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.Authorization;
import io.swagger.models.Response;
import javax.validation.Valid;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Api(
   value = "User Basic Management System",
   description = "Operations pertaining to User Basic in User Basic Management System",
   tags = {"User API Group"}
)
@RestController
@RequestMapping({"/restapi/v1.0/ums/users"})
public class UserController {
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   @Autowired
   private UserService userService;

   public UserController() {
      super();
   }

   @KPI
   @RequestMapping(
      value = {"/me"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiOperation(
      value = "Get specific admin",
      httpMethod = "GET",
      notes = "Fetch the admin user details",
      response = Response.class,
      authorizations = {@Authorization("api_key")}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Given admin user found"
), @ApiResponse(
   code = 404,
   message = "Given admin user not found"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to encoding the data"
), @ApiResponse(
   code = 400,
   message = "Bad request due to decoding the data"
), @ApiResponse(
   code = 412,
   message = "Pre condition failed due to required data not found"
)})
   public ResponseEntity listMyInfo() throws Exception {
      ResponseBody responseBody = this.userService.listMyInfo();
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "get edit page information",
      notes = "get edit page information(basic, organization information).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/me"},
      method = {RequestMethod.PUT},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "MyInfo was updated"
), @ApiResponse(
   code = 422,
   message = "incorrect input entity"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect user information"
)})
   public ResponseEntity updateMyInfo(@Valid @RequestBody UserFilter filter, BindingResult result) throws Exception {
      ResponseBody responseBody = new ResponseBody();
      if (result.hasErrors()) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responseBody, HttpStatus.UNPROCESSABLE_ENTITY);
      } else {
         responseBody = this.userService.updateMyInfo(filter);
         return responseBody.getStatus().equals("Success") ? new ResponseEntity(responseBody, HttpStatus.OK) : new ResponseEntity(responseBody, HttpStatus.BAD_REQUEST);
      }
   }

   @ApiOperation(
      value = "get user information summary",
      notes = "get user information summary(totalOutCount, nonApprovalCount, totalInCount).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/dashboard"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Found the current user dashboard data"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect user information"
)})
   public ResponseEntity listDashboardUserInfo() throws Exception {
      ResponseBody responseBody = new ResponseBody();

      try {
         responseBody = this.userService.listDashboardUserInfo();
         return responseBody.getStatus().equals("Success") ? new ResponseEntity(responseBody, HttpStatus.OK) : new ResponseEntity(responseBody, HttpStatus.BAD_REQUEST);
      } catch (AccessDeniedException var3) {
         this.logger.error("[REST][USER][listDashboardUserInfo] access denied.");
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var3.getMessage());
         return new ResponseEntity(responseBody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         this.logger.error("[REST][USER][listDashboardUserInfo] Exception is occured. " + var4.toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @KPI
   @ApiOperation(
      value = "create new user(approve)",
      notes = "create new user(approve)(basic, organization information).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {""},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 201,
   message = "New User was created"
), @ApiResponse(
   code = 409,
   message = "Duplicated user id"
), @ApiResponse(
   code = 422,
   message = "incorrect input entity"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect user information"
)})
   public ResponseEntity createUser(@Valid @RequestBody UserFilter filter, BindingResult result) throws Exception {
      ResponseBody responseBody = new ResponseBody();

      try {
         if (result.hasErrors()) {
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage(result.getFieldError().getDefaultMessage());
            return new ResponseEntity(responseBody, HttpStatus.UNPROCESSABLE_ENTITY);
         } else if (this.userService.checkDuplicatedUser(filter.getUserId()).getStatus().equals("Fail")) {
            return new ResponseEntity(responseBody, HttpStatus.CONFLICT);
         } else if (filter.getRole() != null && filter.getRole().equalsIgnoreCase("Server Administrator")) {
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage("Can not create the user with the role 'Server Administrator'");
            return new ResponseEntity(responseBody, HttpStatus.UNPROCESSABLE_ENTITY);
         } else {
            boolean signUpPage = false;
            responseBody = this.userService.createUser(filter, signUpPage);
            return responseBody.getStatus().equals("Success") ? new ResponseEntity(responseBody, HttpStatus.CREATED) : new ResponseEntity(responseBody, HttpStatus.BAD_REQUEST);
         }
      } catch (RestServiceException var5) {
         throw var5;
      } catch (AccessDeniedException var6) {
         this.logger.error("[REST][USER][createUser] access denied.");
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var6.getMessage());
         return new ResponseEntity(responseBody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var7) {
         this.logger.error("[REST][USER][createUser] Exception is occured. " + var7.toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var7.getMessage());
         return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "create new user at SignUp page(not approved)",
      notes = "create new user at SignUp page(not approved)(basic, organization information).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/signup"},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 201,
   message = "New User was created"
), @ApiResponse(
   code = 409,
   message = "Duplicated user id"
), @ApiResponse(
   code = 422,
   message = "incorrect input entity"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect user information"
)})
   public ResponseEntity createUserSignUp(@Valid @RequestBody UserFilter filter, BindingResult result) throws Exception {
      ResponseBody responseBody = new ResponseBody();
      if (result.hasErrors()) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responseBody, HttpStatus.UNPROCESSABLE_ENTITY);
      } else if (this.userService.checkDuplicatedUser(filter.getUserId()).equals("Fail")) {
         return new ResponseEntity(responseBody, HttpStatus.CONFLICT);
      } else {
         boolean signUpPage = true;
         responseBody = this.userService.createUser(filter, signUpPage);
         return responseBody.getStatus().equals("Success") ? new ResponseEntity(responseBody, HttpStatus.CREATED) : new ResponseEntity(responseBody, HttpStatus.BAD_REQUEST);
      }
   }

   @ApiOperation(
      value = "get user organization",
      notes = "get user organization(organization name, organization id).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/organization"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Found the organization information"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect user information"
)})
   public ResponseEntity listUserOrgan() throws Exception {
      ResponseBody responseBody = this.userService.listUserOrgan();
      return responseBody.getStatus().equals("Success") ? new ResponseEntity(responseBody, HttpStatus.OK) : new ResponseEntity(responseBody, HttpStatus.BAD_REQUEST);
   }
}
