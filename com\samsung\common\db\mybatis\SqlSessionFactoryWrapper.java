package com.samsung.common.db.mybatis;

import java.sql.Connection;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.TransactionIsolationLevel;

public class SqlSessionFactoryWrapper implements SqlSessionFactory {
   private SqlSessionFactory sessionFactory;

   public SqlSessionFactoryWrapper(SqlSessionFactory sessionFactory) {
      super();
      this.sessionFactory = sessionFactory;
   }

   public Configuration getConfiguration() {
      return this.sessionFactory.getConfiguration();
   }

   public SqlSession openSession() {
      return this.wrapp(this.sessionFactory.openSession());
   }

   public SqlSession openSession(boolean arg0) {
      return this.wrapp(this.sessionFactory.openSession(arg0));
   }

   public SqlSession openSession(Connection arg0) {
      return this.wrapp(this.sessionFactory.openSession(arg0));
   }

   public SqlSession openSession(ExecutorType arg0, boolean arg1) {
      return this.wrapp(this.sessionFactory.openSession(arg0, arg1));
   }

   public SqlSession openSession(ExecutorType arg0, Connection arg1) {
      return this.wrapp(this.sessionFactory.openSession(arg0, arg1));
   }

   public SqlSession openSession(ExecutorType arg0, TransactionIsolationLevel arg1) {
      return this.wrapp(this.sessionFactory.openSession(arg0, arg1));
   }

   public SqlSession openSession(ExecutorType arg0) {
      return this.wrapp(this.sessionFactory.openSession(arg0));
   }

   public SqlSession openSession(TransactionIsolationLevel arg0) {
      return this.wrapp(this.sessionFactory.openSession(arg0));
   }

   public SqlSession wrapp(SqlSession session) {
      return new SqlSessionWrapper(session);
   }
}
