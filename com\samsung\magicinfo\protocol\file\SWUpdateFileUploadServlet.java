package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.device.software.entity.Software;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManagerImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.sql.SQLException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;

public class SWUpdateFileUploadServlet extends HttpServlet {
   private static final long serialVersionUID = 7111415441695311531L;
   private Logger logger = LoggingManagerV2.getLogger(SWUpdateFileUploadServlet.class);

   public SWUpdateFileUploadServlet() {
      super();
   }

   protected void doGet(HttpServletRequest req, HttpServletResponse res) throws ServletException, IOException {
      req.setCharacterEncoding("UTF-8");
      res.setContentType("text/html; charset=UTF-8");
      this.doPost(req, res);
   }

   protected void doPost(HttpServletRequest req, HttpServletResponse res) throws ServletException, IOException, RestServiceException {
      res.setContentType("text/html; charset=UTF-8");
      String swUpdateFileFolderPath = "";
      String swUpdateFilePath = "";
      String id = StrUtils.nvl(req.getParameter("id"));
      String fileName = StrUtils.nvl(req.getParameter("fileName"));
      if (StrUtils.specialCharacterCheckForServerName(fileName)) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID_SPECIAL_CHARACTER1);
      } else {
         String deviceType = StrUtils.nvl(req.getParameter("deviceType"));
         String deviceModelname = StrUtils.nvl(req.getParameter("deviceModelName"));
         String swVer = URLDecoder.decode(StrUtils.nvl(req.getParameter("swVer")), "UTF-8");
         System.out.println(req.getAttributeNames().toString());
         String savedFileName = System.currentTimeMillis() + fileName;
         String fileSavePath = "sw.application";
         fileSavePath = "sw.application";

         try {
            swUpdateFileFolderPath = CommonConfig.get("UPLOAD_HOME").replace('/', File.separatorChar) + File.separator + "admin" + File.separator + "software" + File.separator + "application";
            swUpdateFilePath = swUpdateFileFolderPath + File.separator + savedFileName;
            swUpdateFilePath = SecurityUtils.directoryTraversalChecker(swUpdateFilePath, (String)null);
         } catch (ConfigException var30) {
            this.logger.error("", var30);
         }

         File folder = SecurityUtils.getSafeFile(swUpdateFileFolderPath);
         if (folder != null && !folder.exists()) {
            boolean fSuccess = folder.mkdir();
            if (!fSuccess) {
            }
         }

         File swUpdateFile = SecurityUtils.getSafeFile(swUpdateFilePath);
         InputStream is = req.getInputStream();
         FileOutputStream fos = null;
         byte[] buf = new byte[1024];
         boolean var17 = false;

         try {
            fos = new FileOutputStream(swUpdateFile, true);

            int binaryRead;
            while((binaryRead = is.read(buf)) != -1) {
               fos.write(buf, 0, binaryRead);
            }
         } catch (Exception var31) {
            this.logger.error("", var31);
         } finally {
            try {
               if (is != null) {
                  is.close();
               }

               if (fos != null) {
                  fos.close();
               }
            } catch (IOException var28) {
               this.logger.error("", var28);
            }

         }

         Software software = new Software();
         software.setSoftware_name(fileName);
         software.setFile_name(fileName);
         if (swUpdateFile == null) {
            software.setFile_size(0L);
         } else {
            software.setFile_size(swUpdateFile.length());
         }

         software.setDevice_type(deviceType);
         if (!deviceType.equalsIgnoreCase("iPLAYER")) {
            software.setDevice_model_name(deviceModelname);
         }

         software.setSoftware_type("02");
         software.setCreator_id(id);
         software.setSoftware_version(swVer);
         software.setFile_path(FileUploadCommonHelper.getWebPath(fileSavePath) + "/" + savedFileName);
         software.setIs_auto_update(false);
         SoftwareManagerImpl softwareDao = SoftwareManagerImpl.getInstance();

         try {
            softwareDao.addDeviceSoftware(software, "P");
         } catch (SQLException var29) {
            this.logger.error("", var29);
         }

      }
   }
}
