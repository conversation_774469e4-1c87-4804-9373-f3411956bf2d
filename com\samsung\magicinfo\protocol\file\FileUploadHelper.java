package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.content.constants.ContentConstants;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.manager.ContentCodeInfo;
import com.samsung.magicinfo.framework.content.manager.ContentCodeInfoImpl;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Map;
import java.util.UUID;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.apache.logging.log4j.Logger;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.DOMException;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

public class FileUploadHelper {
   private static Logger logger = LoggingManagerV2.getLogger(FileUploadHelper.class);
   private static String CONTENTS_HOME = "";
   private static String UPLOAD_HOME = "";

   public FileUploadHelper() {
      super();
   }

   public static FileUploadHelper getInstance() {
      return new FileUploadHelper();
   }

   public String saveFileToDisk(String fileName, InputStream stream, String path) {
      FileOutputStream fos = null;
      String absFileName = null;

      Object var7;
      try {
         absFileName = CONTENTS_HOME + File.separator + path + File.separator + fileName;
         File f = SecurityUtils.getSafeFile(CONTENTS_HOME + File.separator + path + File.separator);
         if (!f.exists()) {
            boolean fSuccess = f.mkdirs();
            if (!fSuccess) {
               logger.error("mkdir Fail");
            }
         }

         fos = new FileOutputStream(absFileName);
         byte[] buffer = new byte[8192];
         boolean var8 = false;

         int bytesRead;
         while((bytesRead = stream.read(buffer, 0, 8192)) != -1) {
            fos.write(buffer, 0, bytesRead);
         }

         fos.flush();
         return absFileName;
      } catch (Exception var21) {
         logger.error("", var21);
         var7 = null;
      } finally {
         try {
            fos.close();
         } catch (Exception var20) {
         }

         try {
            stream.close();
         } catch (Exception var19) {
         }

      }

      return (String)var7;
   }

   public String saveUserImageToDisk(String fileName, InputStream stream, String path) {
      FileOutputStream fos = null;
      String absFileName = null;

      Object var7;
      try {
         absFileName = UPLOAD_HOME + File.separator + "User" + File.separator + path + File.separator + fileName;
         File f = SecurityUtils.getSafeFile(UPLOAD_HOME + File.separator + "User" + File.separator + path + File.separator);
         if (!f.exists()) {
            boolean fSuccess = f.mkdirs();
            if (!fSuccess) {
               logger.error("mkdir Fail");
            }
         }

         fos = new FileOutputStream(absFileName);
         byte[] buffer = new byte[8192];
         boolean var8 = false;

         int bytesRead;
         while((bytesRead = stream.read(buffer, 0, 8192)) != -1) {
            fos.write(buffer, 0, bytesRead);
         }

         fos.flush();
         return absFileName;
      } catch (Exception var21) {
         logger.error("", var21);
         var7 = null;
      } finally {
         try {
            fos.close();
         } catch (Exception var20) {
         }

         try {
            stream.close();
         } catch (Exception var19) {
         }

      }

      return (String)var7;
   }

   public String saveProductImageToDisk(String fileName, InputStream stream, String path) {
      FileOutputStream fos = null;
      String absFileName = null;

      Object var7;
      try {
         absFileName = UPLOAD_HOME + File.separator + "Product" + File.separator + path + File.separator + fileName;
         File f = SecurityUtils.getSafeFile(UPLOAD_HOME + File.separator + "Product" + File.separator + path + File.separator);
         if (!f.exists()) {
            boolean fSuccess = f.mkdirs();
            if (!fSuccess) {
               logger.error("mkdir Fail");
            }
         }

         fos = new FileOutputStream(absFileName);
         byte[] buffer = new byte[8192];
         boolean var8 = false;

         int bytesRead;
         while((bytesRead = stream.read(buffer, 0, 8192)) != -1) {
            fos.write(buffer, 0, bytesRead);
         }

         fos.flush();
         return absFileName;
      } catch (Exception var21) {
         logger.error("", var21);
         var7 = null;
      } finally {
         try {
            fos.close();
         } catch (Exception var20) {
         }

         try {
            stream.close();
         } catch (Exception var19) {
         }

      }

      return (String)var7;
   }

   public String saveNoticeFileToDisk(String fileName, InputStream stream, String path) {
      FileOutputStream fos = null;
      String absFileName = null;

      Object var7;
      try {
         absFileName = UPLOAD_HOME + File.separator + "noticeFile" + File.separator + path + File.separator + fileName;
         File f = SecurityUtils.getSafeFile(UPLOAD_HOME + File.separator + "noticeFile" + File.separator + path + File.separator);
         if (!f.exists()) {
            boolean fSuccess = f.mkdirs();
            if (!fSuccess) {
               logger.error("mkdir Fail");
            }
         }

         fos = new FileOutputStream(absFileName);
         byte[] buffer = new byte[8192];
         boolean var8 = false;

         int bytesRead;
         while((bytesRead = stream.read(buffer, 0, 8192)) != -1) {
            fos.write(buffer, 0, bytesRead);
         }

         fos.flush();
         return absFileName;
      } catch (Exception var21) {
         logger.error("", var21);
         var7 = null;
      } finally {
         try {
            fos.close();
         } catch (Exception var20) {
         }

         try {
            stream.close();
         } catch (Exception var19) {
         }

      }

      return (String)var7;
   }

   public String saveFileToDisk(String fileName, MultipartFile file, String path) {
      String absFileName = null;

      try {
         absFileName = this.saveFileToDisk(fileName, file.getInputStream(), path);
      } catch (Exception var6) {
      }

      return absFileName;
   }

   public String saveFileToDisk(MultipartFile file, String pathKey) {
      return this.saveFileToDisk(file.getName(), file, pathKey);
   }

   public ArrayList readLfd(String fileName, String pathKey, String localpath) throws Exception {
      String path = CONTENTS_HOME + File.separator + pathKey + File.separator;
      String lfdName = "";
      ArrayList altRtn = new ArrayList();
      DocumentBuilderFactory dbf = DocumentUtils.getDocumentBuilderFactoryInstance();
      DocumentBuilder parser = dbf.newDocumentBuilder();
      Document doc = parser.parse(SecurityUtils.getSafeFile(fileName));
      Element contentElement = doc.getDocumentElement();
      NodeList pageList = contentElement.getElementsByTagName("Page");

      for(int i = 0; i < pageList.getLength(); ++i) {
         Element page = (Element)pageList.item(i);
         NodeList elementList = page.getElementsByTagName("Element");

         for(int j = 0; j < elementList.getLength(); ++j) {
            Element element = (Element)elementList.item(j);
            NodeList filePathList = element.getElementsByTagName("FilePath");
            if (filePathList != null && filePathList.getLength() > 0) {
               Node fpathNode = filePathList.item(0);
               if (!localpath.equals("")) {
                  if (localpath.equals("fname")) {
                     lfdName = getTextContent(fpathNode);
                  } else {
                     lfdName = localpath + getTextContent(fpathNode);
                  }
               } else {
                  lfdName = path + getTextContent(fpathNode);
               }

               altRtn.add(lfdName);
            }
         }
      }

      return altRtn;
   }

   public Hashtable readCSD(String fileName, String cid, long Vid) throws Exception {
      Hashtable hash = new Hashtable();
      ContentCodeInfo codeDao = ContentCodeInfoImpl.getInstance();
      DocumentBuilderFactory dbf = DocumentUtils.getDocumentBuilderFactoryInstance();
      DocumentBuilder parser = dbf.newDocumentBuilder();
      Document doc = parser.parse(SecurityUtils.getSafeFile(fileName));
      Element contentElement = doc.getDocumentElement();
      if (contentElement.hasChildNodes()) {
         Node UserNode = contentElement.getElementsByTagName("User").item(0);
         Node TitleNode = contentElement.getElementsByTagName("Title").item(0);
         Node CategoryNode = contentElement.getElementsByTagName("Category").item(0);
         Node CommonNode = contentElement.getElementsByTagName("IsShareContents").item(0);
         Node MetaNode = contentElement.getElementsByTagName("Meta").item(0);
         Node RegularFormNode = contentElement.getElementsByTagName("RegularForm").item(0);
         Node BoundaryNode = contentElement.getElementsByTagName("Boundary").item(0);
         Node PROMThumbnailType = contentElement.getElementsByTagName("PROMThumbnailType").item(0);
         Node PlayerType = contentElement.getElementsByTagName("PlayerType").item(0);
         Node PlayerTypeVersion = contentElement.getElementsByTagName("PlayerTypeVersion").item(0);
         if (UserNode.hasChildNodes()) {
            hash.put("User", UserNode.getChildNodes().item(0).getNodeValue());
         }

         if (TitleNode.hasChildNodes()) {
            hash.put("Title", TitleNode.getChildNodes().item(0).getNodeValue());
         }

         if (CategoryNode.hasChildNodes()) {
            hash.put("Category", CategoryNode.getChildNodes().item(0).getNodeValue());
         } else {
            hash.put("Category", "");
         }

         if (CommonNode.hasChildNodes()) {
            hash.put("Share", CommonNode.getChildNodes().item(0).getNodeValue());
         }

         if (PROMThumbnailType != null && PROMThumbnailType.hasChildNodes()) {
            hash.put("PROMThumbnailType", PROMThumbnailType.getChildNodes().item(0).getNodeValue());
         } else {
            hash.put("PROMThumbnailType", "");
         }

         if (MetaNode != null && MetaNode.hasChildNodes()) {
            hash.put("Meta", MetaNode.getChildNodes().item(0).getNodeValue());
         } else {
            hash.put("Meta", "");
         }

         if (RegularFormNode != null && RegularFormNode.hasChildNodes()) {
            hash.put("LinearVWL", RegularFormNode.getChildNodes().item(0).getNodeValue());
            String x_count = RegularFormNode.getAttributes().getNamedItem("width").getNodeValue();
            String y_count = RegularFormNode.getAttributes().getNamedItem("height").getNodeValue();
            if (!x_count.equals("") && !y_count.equals("")) {
               hash.put("X_Count", Integer.valueOf(x_count));
               hash.put("Y_Count", Integer.valueOf(y_count));
               hash.put("SCREEN_COUNT", Integer.valueOf(x_count) * Integer.valueOf(y_count));
            } else {
               hash.put("X_Count", "");
               hash.put("Y_Count", "");
               hash.put("SCREEN_COUNT", "");
            }
         } else {
            hash.put("LinearVWL", "");
            hash.put("X_Count", "");
            hash.put("Y_Count", "");
            hash.put("SCREEN_COUNT", "");
         }

         if (BoundaryNode != null && BoundaryNode.hasChildNodes()) {
            hash.put("X_Range", BoundaryNode.getAttributes().getNamedItem("width").getNodeValue());
            hash.put("Y_Range", BoundaryNode.getAttributes().getNamedItem("height").getNodeValue());
         } else {
            hash.put("X_Range", "");
            hash.put("Y_Range", "");
         }

         if (PlayerType != null && PlayerType.hasChildNodes()) {
            hash.put("PlayerType", PlayerType.getChildNodes().item(0).getNodeValue());
         } else {
            hash.put("PlayerType", "");
         }

         if (PlayerTypeVersion != null && PlayerTypeVersion.hasChildNodes()) {
            hash.put("PlayerTypeVersion", PlayerTypeVersion.getChildNodes().item(0).getNodeValue());
         } else {
            hash.put("PlayerTypeVersion", Float.toString(CommonDataConstants.TYPE_VERSION_1_0));
         }

         NodeList fileList = contentElement.getElementsByTagName("TransferFile");
         hash.put("FileCnt", Integer.toString(fileList.getLength()));
         ArrayList flst = new ArrayList();
         String strFileSize = "0";
         long tSize = 0L;
         String supportfileitems = "false";
         String fext = "";
         String storepath = "";
         String mediaType = "";
         boolean isSFI = false;
         Map isNewMap = new HashMap();

         for(int i = 0; i < fileList.getLength(); ++i) {
            Element fnameElement = (Element)fileList.item(i);
            String type = "";
            String reqIndex = "";
            boolean isNew = false;
            if (fnameElement.hasAttribute("reqIndex")) {
               reqIndex = fnameElement.getAttribute("reqIndex");
            }

            if (fnameElement.hasAttribute("type")) {
               type = fnameElement.getAttribute("type");
            }

            if (fnameElement.hasAttribute("supportfileitems") && fnameElement.getAttribute("supportfileitems").toLowerCase().equals("true")) {
               supportfileitems = "true";
            }

            if (fnameElement.hasAttribute("isNew") && fnameElement.getAttribute("isNew").toLowerCase().equals("true")) {
               isNew = true;
            }

            NodeList StorePathNode = fnameElement.getElementsByTagName("StorePath");
            Element spEle = (Element)StorePathNode.item(0);
            if (spEle.getChildNodes().item(0) != null) {
               storepath = spEle.getChildNodes().item(0).getNodeValue().toString();
               int plen = storepath.length();
               if (plen > 2) {
                  storepath = StrUtils.nvl(storepath.substring(2, storepath.length()));
               } else {
                  storepath = "";
               }
            }

            if (fnameElement.hasChildNodes()) {
               NodeList nodeFileName = fnameElement.getElementsByTagName("FileName");
               NodeList nodeFileSize = fnameElement.getElementsByTagName("FileSize");
               NodeList nodeFileHash = fnameElement.getElementsByTagName("FileHashValue");
               NodeList nodeFileID = fnameElement.getElementsByTagName("FileID");
               NodeList nodeMediaType = null;
               if (fnameElement.getElementsByTagName("MediaType") != null) {
                  nodeMediaType = fnameElement.getElementsByTagName("MediaType");
               }

               Element elFileName = (Element)nodeFileName.item(0);
               Element elFileSize = (Element)nodeFileSize.item(0);
               Element elFileHash = (Element)nodeFileHash.item(0);
               Element elFileID = (Element)nodeFileID.item(0);
               Element elMediaType = null;
               Element elStartUpPage = null;
               if (nodeMediaType != null) {
                  elMediaType = (Element)nodeMediaType.item(0);
               }

               String strFileName = elFileName.getChildNodes().item(0).getNodeValue();
               String strFileHash = elFileHash.getChildNodes().item(0).getNodeValue();
               String strFileID = elFileID.getChildNodes().item(0).getNodeValue();
               ContentFile cmsContentFiles = new ContentFile();
               fext = strFileName.substring(strFileName.lastIndexOf(".") + 1, strFileName.length());
               if (elMediaType != null && !elMediaType.getChildNodes().item(0).getNodeValue().equals("")) {
                  fext = elMediaType.getChildNodes().item(0).getNodeValue();
               }

               if (i == 0) {
                  if (type != null && type.equalsIgnoreCase("HTML")) {
                     hash.put("MediaType", "HTML");
                     mediaType = "HTML";
                  } else if (type != null && ContentConstants.getMediaTypeForAuthor().contains(type.toUpperCase())) {
                     hash.put("MediaType", type.toUpperCase());
                     mediaType = type;
                  } else if (fext.equalsIgnoreCase("SFI")) {
                     isSFI = true;
                  } else {
                     hash.put("MediaType", StrUtils.nvl(codeDao.getMediaTypeByFileType(fext.toUpperCase())));
                     mediaType = StrUtils.nvl(codeDao.getMediaTypeByFileType(fext.toUpperCase()));
                  }
               }

               if (i == 1 && isSFI) {
                  hash.put("MediaType", StrUtils.nvl(codeDao.getMediaTypeByFileType(fext.toUpperCase())));
                  mediaType = StrUtils.nvl(codeDao.getMediaTypeByFileType(fext.toUpperCase()));
               }

               if (!fext.equalsIgnoreCase("mpeg") && !fext.equalsIgnoreCase("mpg") && !fext.equalsIgnoreCase("wmv") && !fext.equalsIgnoreCase("avi") && !fext.equalsIgnoreCase("mov") && !fext.equalsIgnoreCase("mp4") && !fext.equalsIgnoreCase("asf")) {
                  if (i == 0) {
                     hash.put("IsStreaming", false);
                  }

                  cmsContentFiles.setIs_streaming("N");
               } else {
                  if (i == 0) {
                     hash.put("IsStreaming", true);
                  }

                  cmsContentFiles.setIs_streaming("Y");
               }

               if (elFileSize != null && elFileSize.getChildNodes().item(0) != null) {
                  strFileSize = StrUtils.nvl(elFileSize.getChildNodes().item(0).getNodeValue());
               }

               if (!strFileSize.equals("")) {
                  tSize += Long.parseLong(strFileSize);
               }

               if (strFileID == null || strFileID.length() == 0) {
                  strFileID = UUID.randomUUID().toString();
               }

               cmsContentFiles.setFile_id(strFileID);
               cmsContentFiles.setHash_code(strFileHash);
               cmsContentFiles.setFile_type("CONTENT");
               cmsContentFiles.setFile_name(strFileName);
               cmsContentFiles.setFile_size(Long.parseLong(strFileSize));
               reqIndex = reqIndex.replace("\"", "");
               cmsContentFiles.setReqIndex(Integer.parseInt(reqIndex.trim()));
               if (isNew) {
                  isNewMap.put(strFileID, isNew);
               }

               System.out.println("[FUpload] type / mediaType " + type + " " + mediaType);
               NodeList PlayTimeNode;
               NodeList ResolutionNode;
               Element ptEle;
               Element rtEle;
               if (type == null || !type.equalsIgnoreCase("LFD") && !type.equalsIgnoreCase("LFT") && !type.equalsIgnoreCase("HTML") && !ContentConstants.getMediaTypeForAuthor().contains(type)) {
                  if (type != null && type.equals("content")) {
                     if (!mediaType.equalsIgnoreCase("LFD") && !mediaType.equalsIgnoreCase("LFT")) {
                        PlayTimeNode = fnameElement.getElementsByTagName("PlayTime");
                        ResolutionNode = fnameElement.getElementsByTagName("Resolution");
                        ptEle = (Element)PlayTimeNode.item(0);
                        rtEle = (Element)ResolutionNode.item(0);
                        if (ptEle.getChildNodes().item(0) != null) {
                           if (hash.get("PlayTime") == null) {
                              hash.put("PlayTime", ptEle.getChildNodes().item(0).getNodeValue());
                           }
                        } else if (hash.get("PlayTime") == null) {
                           hash.put("PlayTime", "-");
                        }

                        if (rtEle.getChildNodes().item(0) != null) {
                           if (hash.get("Resolution") == null && !rtEle.getChildNodes().item(0).getNodeValue().equalsIgnoreCase("0 x 0")) {
                              hash.put("Resolution", rtEle.getChildNodes().item(0).getNodeValue());
                           }
                        } else if (hash.get("Resolution") == null) {
                           hash.put("Resolution", "-");
                        }
                     } else {
                        PlayTimeNode = fnameElement.getElementsByTagName("PlayTime");
                        ResolutionNode = fnameElement.getElementsByTagName("Resolution");
                        ptEle = (Element)PlayTimeNode.item(0);
                        rtEle = (Element)ResolutionNode.item(0);
                        if (ptEle.getChildNodes().item(0) != null) {
                           if (hash.get("PlayTime") == null) {
                              hash.put("PlayTime", ptEle.getChildNodes().item(0).getNodeValue());
                           }
                        } else if (hash.get("PlayTime") == null) {
                           hash.put("PlayTime", "-");
                        }

                        if (rtEle.getChildNodes().item(0) != null) {
                           if (hash.get("Resolution") == null && !rtEle.getChildNodes().item(0).getNodeValue().equalsIgnoreCase("0 x 0")) {
                              hash.put("Resolution", rtEle.getChildNodes().item(0).getNodeValue());
                           }
                        } else if (hash.get("Resolution") == null) {
                           hash.put("Resolution", "-");
                        }
                     }
                  } else if (type != null && type.equalsIgnoreCase("thumbnail")) {
                     cmsContentFiles.setFile_type("THUMBNAIL");
                     tSize -= Long.parseLong(strFileSize);
                  } else if (type != null && type.equalsIgnoreCase("sfi")) {
                     cmsContentFiles.setFile_type("SFI");
                     tSize -= Long.parseLong(strFileSize);
                  }
               } else {
                  PlayTimeNode = fnameElement.getElementsByTagName("PlayTime");
                  ResolutionNode = fnameElement.getElementsByTagName("Resolution");
                  ptEle = (Element)PlayTimeNode.item(0);
                  rtEle = (Element)ResolutionNode.item(0);
                  if (ptEle.getChildNodes().item(0) != null) {
                     if (hash.get("PlayTime") == null) {
                        hash.put("PlayTime", ptEle.getChildNodes().item(0).getNodeValue());
                     }
                  } else {
                     hash.put("PlayTime", "-");
                  }

                  if (rtEle.getChildNodes().item(0) != null) {
                     if (hash.get("Resolution") == null) {
                        hash.put("Resolution", rtEle.getChildNodes().item(0).getNodeValue());
                     }
                  } else {
                     hash.put("Resolution", "-");
                  }
               }

               flst.add(cmsContentFiles);
            }
         }

         hash.put("TotalSize", tSize);
         hash.put("ContentFileList", flst);
         hash.put("SupportFileItems", supportfileitems);
         hash.put("isNewMap", isNewMap);
      }

      return hash;
   }

   public static String getTextContent(Node node) throws DOMException {
      String textContent = "";
      if (node.getNodeType() == 2) {
         textContent = node.getNodeValue();
      } else {
         Node child = node.getFirstChild();
         if (child != null) {
            Node sibling = child.getNextSibling();
            if (sibling != null) {
               StringBuffer sb = new StringBuffer();
               getTextContent(node, sb);
               textContent = sb.toString();
            } else if (child.getNodeType() == 3) {
               textContent = child.getNodeValue();
            } else {
               textContent = getTextContent(child);
            }
         }
      }

      return textContent;
   }

   private static void getTextContent(Node node, StringBuffer sb) throws DOMException {
      for(Node child = node.getFirstChild(); child != null; child = child.getNextSibling()) {
         if (child.getNodeType() == 3) {
            sb.append(child.getNodeValue());
         } else {
            getTextContent(child, sb);
         }
      }

   }

   static {
      try {
         CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME");
         UPLOAD_HOME = CommonConfig.get("UPLOAD_HOME");
         CONTENTS_HOME = CONTENTS_HOME.replace('/', File.separatorChar) + File.separatorChar + "contents_home";
         UPLOAD_HOME = UPLOAD_HOME.replace('/', File.separatorChar);
      } catch (Exception var1) {
         logger.error(var1);
      }

   }
}
