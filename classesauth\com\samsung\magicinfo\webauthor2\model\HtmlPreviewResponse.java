package com.samsung.magicinfo.webauthor2.model;

public class HtmlPreviewResponse {
  private int progress;
  
  private boolean available;
  
  private String uri;
  
  public HtmlPreviewResponse(int progress, boolean avialable, String uri) {
    this.progress = progress;
    this.available = avialable;
    this.uri = uri;
  }
  
  public int getProgress() {
    return this.progress;
  }
  
  public void setProgress(int progress) {
    this.progress = progress;
  }
  
  public boolean isAvailable() {
    return this.available;
  }
  
  public void setAvailable(boolean available) {
    this.available = available;
  }
  
  public String getUri() {
    return this.uri;
  }
  
  public void setUri(String uri) {
    this.uri = uri;
  }
  
  public String toString() {
    return "HtmlPreviewResponse{progress=" + this.progress + ", available=" + this.available + ", uri='" + this.uri + '\'' + '}';
  }
}
