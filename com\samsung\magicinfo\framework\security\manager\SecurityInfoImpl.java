package com.samsung.magicinfo.framework.security.manager;

import com.samsung.common.cache.CacheFactory;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.security.dao.SecurityDao;
import com.samsung.magicinfo.framework.security.entity.SecurityInfoEntity;
import java.security.SecureRandom;
import java.sql.SQLException;
import java.util.Arrays;
import org.springframework.stereotype.Service;

@Service
public class SecurityInfoImpl implements SecurityInfo {
   private final SecurityDao securityDao = new SecurityDao();
   private static final SecureRandom secureRandom = new SecureRandom();
   private static volatile SecurityInfoImpl instance = null;

   public static SecurityInfoImpl getInstance() throws Exception {
      if (instance == null) {
         Class var0 = SecurityInfoImpl.class;
         synchronized(SecurityInfoImpl.class) {
            if (instance == null) {
               instance = new SecurityInfoImpl();
            }
         }
      }

      return instance;
   }

   public SecurityInfoImpl() throws Exception {
      super();
      this.init();
   }

   private void init() throws Exception {
      SecurityInfoEntity securityInfo = this.securityDao.getSecurityInfo();
      if (null == securityInfo) {
         securityInfo = new SecurityInfoEntity();
         String secretKey = this.makeSecretKey();
         securityInfo.setSecret_key(secretKey);
         CacheFactory.getCache().set("SECRET_KEY", secretKey);
         this.addSecurityInfo(securityInfo);
      }

   }

   public SecurityInfoEntity getSecurityInfo() throws Exception {
      return this.securityDao.getSecurityInfo();
   }

   public void addSecurityInfo(SecurityInfoEntity securityInfoEntity) throws SQLException {
      this.securityDao.addSecurityInfo(securityInfoEntity);
   }

   public String getSecretKey() throws Exception {
      Object secretKey = CacheFactory.getCache().get("SECRET_KEY");
      if (secretKey != null && !((String)secretKey).isEmpty()) {
         return (String)secretKey;
      } else {
         String secretKeyFromDB = this.securityDao.getSecretKey();
         if (secretKeyFromDB == null || secretKeyFromDB.isEmpty()) {
            secretKeyFromDB = this.makeSecretKey();
            this.securityDao.setSecretKey(secretKeyFromDB);
         }

         CacheFactory.getCache().set("SECRET_KEY", secretKeyFromDB);
         return secretKeyFromDB;
      }
   }

   private String makeSecretKey() {
      int secretKeyLength = true;
      byte[] key = new byte[64];
      secureRandom.nextBytes(key);
      String result = "";

      try {
         result = SecurityUtils.getHashSha(Arrays.toString(key), 2);
      } catch (Exception var8) {
      } finally {
         if (null == result) {
            result = "";
         }

      }

      return result.length() > 64 ? result.substring(0, 64) : result;
   }
}
