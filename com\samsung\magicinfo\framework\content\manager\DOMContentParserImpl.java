package com.samsung.magicinfo.framework.content.manager;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.magicinfo.framework.content.entity.Template;
import com.samsung.magicinfo.framework.content.entity.TemplateElement;
import com.samsung.magicinfo.framework.content.entity.TemplateElementItem;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.ArrayList;
import java.util.Iterator;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

public class DOMContentParserImpl implements ContentXmlParserInfo {
   Logger logger = LoggingManagerV2.getLogger(DOMContentParserImpl.class);
   private static final int MEGABYTE = 1048576;

   public DOMContentParserImpl() {
      super();
   }

   private float getDisplaySize(Node displayNode) {
      float size = -1.0F;
      NodeList displayChildNodeList = displayNode.getChildNodes();
      int displayChildNodeListSize = displayChildNodeList.getLength();

      for(int i = 0; i < displayChildNodeListSize; ++i) {
         if (displayChildNodeList.item(i).getNodeName().equalsIgnoreCase("KEYFRAME")) {
            size = Float.parseFloat(displayChildNodeList.item(i).getTextContent());
            break;
         }
      }

      return size;
   }

   public Template getTemplateParsing(String fileName) {
      this.logger.info("=== start getTemplateParsing ===");
      Template template = new Template();
      ArrayList arrTemplateElementList = null;
      float templateDisplayWidth = -1.0F;
      float templateDisplayHeight = -1.0F;
      FileInputStream inputStreamTemplate = null;

      try {
         DocumentBuilderFactory docFctory = DocumentUtils.getDocumentBuilderFactoryInstance();
         DocumentBuilder builder = docFctory.newDocumentBuilder();
         inputStreamTemplate = new FileInputStream(fileName);
         Document doc = builder.parse(inputStreamTemplate);
         Element root = doc.getDocumentElement();
         NodeList contentChildNodeList = root.getChildNodes();
         int contentChildNodeListSize = contentChildNodeList.getLength();

         for(int contentChild_i = 0; contentChild_i < contentChildNodeListSize; ++contentChild_i) {
            if (contentChildNodeList.item(contentChild_i).getNodeName().equalsIgnoreCase("DISPLAYWIDTH")) {
               templateDisplayWidth = this.getDisplaySize(contentChildNodeList.item(contentChild_i));
            } else if (contentChildNodeList.item(contentChild_i).getNodeName().equalsIgnoreCase("DISPLAYHEIGHT")) {
               templateDisplayHeight = this.getDisplaySize(contentChildNodeList.item(contentChild_i));
            } else if (contentChildNodeList.item(contentChild_i).getNodeName().equalsIgnoreCase("DATALINKCONTENTMETA")) {
               arrTemplateElementList = this.getTemplateElementList(contentChildNodeList.item(contentChild_i));
            }

            if (templateDisplayWidth != -1.0F && templateDisplayHeight != -1.0F && arrTemplateElementList != null) {
               break;
            }
         }

         this.logger.info("WIDTH = " + templateDisplayWidth + ", HEIGHT = " + templateDisplayHeight);
      } catch (OutOfMemoryError var30) {
         MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
         MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
         long maxMemory = heapUsage.getMax() / 1048576L;
         long usedMemory = heapUsage.getUsed() / 1048576L;
         this.logger.error("Memory Use :" + usedMemory + "M/" + maxMemory + "M");
         this.logger.error(var30.toString());
      } catch (ParserConfigurationException var31) {
         this.logger.error(var31.toString());
      } catch (FileNotFoundException var32) {
         this.logger.error(var32.toString());
      } catch (SAXException var33) {
         this.logger.error(var33.toString());
      } catch (IOException var34) {
         this.logger.error(var34.toString());
      } finally {
         try {
            if (inputStreamTemplate != null) {
               inputStreamTemplate.close();
               inputStreamTemplate = null;
            }
         } catch (Exception var29) {
            this.logger.error("", var29);
         }

      }

      this.logger.info("=== end getTemplateParsing ===");
      template.setDisplayWidth(templateDisplayWidth);
      template.setDisplayHeight(templateDisplayHeight);
      template.setTemplateElementList(arrTemplateElementList);
      return template;
   }

   private String searchTextContent(NodeList elementNodeList, String nodeName) {
      int elementNodeListSize = elementNodeList.getLength();
      String text = null;

      for(int j = 0; j < elementNodeListSize; ++j) {
         if (elementNodeList.item(j).getNodeName().equalsIgnoreCase(nodeName)) {
            text = elementNodeList.item(j).getTextContent();
            break;
         }
      }

      return text;
   }

   private ArrayList parseInnerDataLink(NodeList elementNodeList) {
      ArrayList innerDataLink = new ArrayList();

      for(int j = 0; j < elementNodeList.getLength(); ++j) {
         if (elementNodeList.item(j).getNodeName().equalsIgnoreCase("Item")) {
            TemplateElementItem templateElementItem = new TemplateElementItem();
            NamedNodeMap attr = elementNodeList.item(j).getAttributes();
            Node typeNode = attr.getNamedItem("type");
            templateElementItem.setType(typeNode.getTextContent());
            Node noNode = attr.getNamedItem("no");
            templateElementItem.setNo(Integer.parseInt(noNode.getTextContent()));
            NodeList elementNameNodeList = elementNodeList.item(j).getChildNodes();

            for(int k = 0; k < elementNameNodeList.getLength(); ++k) {
               if (elementNameNodeList.item(k).getNodeName().equalsIgnoreCase("Name")) {
                  templateElementItem.setName(elementNameNodeList.item(k).getTextContent());
                  break;
               }
            }

            innerDataLink.add(templateElementItem);
         }
      }

      return innerDataLink;
   }

   private TemplateElement getTemplateElement(Node element, int pageI, int splitGroupId) {
      TemplateElement elementData = null;

      try {
         String valueName = null;
         String valuePositionX = null;
         String valuePositionY = null;
         String valueWidth = null;
         String valueHeight = null;
         String valueElementType = null;
         String valueElementNo = null;
         String valueChangeDuration = null;
         String valueKeepLastValue = null;
         ArrayList innerDataLinkList = null;
         NamedNodeMap attr = element.getAttributes();
         Node typeNode = attr.getNamedItem("type");
         valueElementType = typeNode.getTextContent();
         Node noNode = attr.getNamedItem("no");
         valueElementNo = noNode.getTextContent();
         NodeList elementNodeList = element.getChildNodes();
         int elementNodeListSize = elementNodeList.getLength();

         for(int j = 0; j < elementNodeListSize; ++j) {
            if (elementNodeList.item(j).getNodeName().equalsIgnoreCase("NAME")) {
               valueName = elementNodeList.item(j).getTextContent();
            } else if (elementNodeList.item(j).getNodeName().equalsIgnoreCase("POSITIONX")) {
               valuePositionX = elementNodeList.item(j).getTextContent();
            } else if (elementNodeList.item(j).getNodeName().equalsIgnoreCase("POSITIONY")) {
               valuePositionY = elementNodeList.item(j).getTextContent();
            } else if (elementNodeList.item(j).getNodeName().equalsIgnoreCase("WIDTH")) {
               valueWidth = elementNodeList.item(j).getTextContent();
            } else if (elementNodeList.item(j).getNodeName().equalsIgnoreCase("HEIGHT")) {
               valueHeight = elementNodeList.item(j).getTextContent();
            } else if (elementNodeList.item(j).getNodeName().equalsIgnoreCase("CHANGEDURATION")) {
               valueChangeDuration = elementNodeList.item(j).getTextContent();
            } else if (elementNodeList.item(j).getNodeName().equalsIgnoreCase("ERROR")) {
               valueKeepLastValue = this.searchTextContent(elementNodeList.item(j).getChildNodes(), "KeepLastValue");
            } else if (elementNodeList.item(j).getNodeName().equalsIgnoreCase("InnerDataLink")) {
               innerDataLinkList = this.parseInnerDataLink(elementNodeList.item(j).getChildNodes());
            }
         }

         elementData = new TemplateElement();
         elementData.setPage_no(pageI);
         elementData.setSplit_group_id(splitGroupId);
         elementData.setElement_name(valueName);
         elementData.setPosition_x(Float.parseFloat(valuePositionX));
         elementData.setPosition_y(Float.parseFloat(valuePositionY));
         elementData.setWidth(Float.parseFloat(valueWidth));
         elementData.setHeight(Float.parseFloat(valueHeight));
         elementData.setElement_type(valueElementType);
         elementData.setElement_no(Integer.parseInt(valueElementNo));
         elementData.setChange_duration(Integer.parseInt(valueChangeDuration));
         if (valueKeepLastValue != null) {
            elementData.setKeep_last_value(valueKeepLastValue);
         }

         elementData.setInnerDataLinkList(innerDataLinkList);
         this.logger.info("element name = " + valueName);
      } catch (Exception var21) {
         this.logger.error("", var21);
      }

      return elementData;
   }

   private ArrayList seperateInnerDataLinkList(TemplateElement templateElement) {
      ArrayList templateElementList = new ArrayList();
      ArrayList innerDataLinkList = templateElement.getInnerDataLinkList();
      Iterator var4 = innerDataLinkList.iterator();

      while(var4.hasNext()) {
         TemplateElementItem templateElementItem = (TemplateElementItem)var4.next();

         try {
            TemplateElement newTemplateElement = (TemplateElement)templateElement.clone();
            newTemplateElement.setIs_inner_datalink(true);
            newTemplateElement.setItem_no(templateElementItem.getNo());
            newTemplateElement.setItem_name(templateElementItem.getName());
            newTemplateElement.setItem_type(templateElementItem.getType());
            templateElementList.add(newTemplateElement);
            newTemplateElement = null;
         } catch (CloneNotSupportedException var7) {
            this.logger.error("", var7);
         }
      }

      return templateElementList;
   }

   private ArrayList getTemplateElementList(Node node) {
      ArrayList templateElementList = new ArrayList();
      NodeList pageNodeList = node.getChildNodes();
      int totalPageNodeSize = pageNodeList.getLength();

      for(int page_i = 0; page_i < totalPageNodeSize; ++page_i) {
         if (pageNodeList.item(page_i).getNodeName().equalsIgnoreCase("PAGE")) {
            int pageNo = Integer.parseInt(((Element)pageNodeList.item(page_i)).getAttributeNode("no").getTextContent());
            this.logger.info("pageNo = " + pageNo);
            NodeList syncGroupNodeList = pageNodeList.item(page_i).getChildNodes();
            int syncGroupNodeListSize = syncGroupNodeList.getLength();
            boolean justPage = true;

            for(int syncGroup_i = 0; syncGroup_i < syncGroupNodeListSize; ++syncGroup_i) {
               if (syncGroupNodeList.item(syncGroup_i).getNodeName().equalsIgnoreCase("SYNCGROUP")) {
                  int syncGroupId = Integer.parseInt(((Element)syncGroupNodeList.item(syncGroup_i)).getAttributeNode("id").getTextContent());
                  this.logger.info("syncGroupId = " + syncGroupId);
                  NodeList splitGroupNodeList = syncGroupNodeList.item(syncGroup_i).getChildNodes();
                  int splitGroupNodeListSize = splitGroupNodeList.getLength();

                  for(int splitGroup_i = 0; splitGroup_i < splitGroupNodeListSize; ++splitGroup_i) {
                     if (splitGroupNodeList.item(splitGroup_i).getNodeName().equalsIgnoreCase("SPLITGROUP")) {
                        int splitGroupId = Integer.parseInt(((Element)splitGroupNodeList.item(splitGroup_i)).getAttributeNode("id").getTextContent());
                        this.logger.info("splitGroupId = " + splitGroupId);
                        NodeList elementNodeList = splitGroupNodeList.item(splitGroup_i).getChildNodes();
                        String splitGroupName = null;

                        for(int element_i = 0; element_i < elementNodeList.getLength(); ++element_i) {
                           if (elementNodeList.item(element_i).getNodeName().equalsIgnoreCase("NAME")) {
                              splitGroupName = elementNodeList.item(element_i).getTextContent();
                           } else if (elementNodeList.item(element_i).getNodeName().equalsIgnoreCase("ELEMENT")) {
                              this.logger.info("element = " + element_i);
                              TemplateElement tempTemplateElement = this.getTemplateElement(elementNodeList.item(element_i), pageNo, splitGroupId);
                              if (tempTemplateElement != null) {
                                 if (splitGroupName != null) {
                                    tempTemplateElement.setSplit_group_name(splitGroupName);
                                    this.logger.info("splitGroupName = " + splitGroupName);
                                 }

                                 if (tempTemplateElement.getInnerDataLinkList() != null && tempTemplateElement.getInnerDataLinkList().size() > 0) {
                                    templateElementList.addAll(this.seperateInnerDataLinkList(tempTemplateElement));
                                 } else {
                                    tempTemplateElement.setIs_inner_datalink(false);
                                    templateElementList.add(tempTemplateElement);
                                 }

                                 tempTemplateElement = null;
                                 justPage = false;
                              } else {
                                 this.logger.info("[getElementError] pageNo=" + pageNo + " splitGroupId=" + splitGroupId);
                              }
                           }
                        }
                     }
                  }
               }
            }

            if (justPage) {
               TemplateElement pageTemplateElement = new TemplateElement();
               pageTemplateElement.setPage_no(pageNo);
               pageTemplateElement.setElement_name("JustPage" + System.currentTimeMillis());
               templateElementList.add(pageTemplateElement);
               pageTemplateElement = null;
            }
         }
      }

      return templateElementList;
   }
}
