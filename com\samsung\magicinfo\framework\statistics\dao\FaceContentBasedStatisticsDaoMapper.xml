<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.samsung.magicinfo.framework.statistics.dao.FaceContentBasedStatisticsDaoMapper">

<sql id="where_deviceIdList">
	<if test="deviceIdList != null and deviceIdList.length > 0">
		<foreach item="item"  index="index" collection="deviceIdList" open="AND (" separator="OR" close=")">	
		DEVICE_ID = #{item}	
		</foreach>		
	</if>
</sql>

<sql id="where_contentIdList">
	<if test="contentIdList != null and contentIdList.length > 0">
		<foreach item="item"  index="index" collection="contentIdList" open="AND (" separator="OR" close=")">	
		CONTENT_ID = #{item}	
		</foreach>		
	</if>
</sql>

<sql id="where_selectedConditionList">

<foreach index="key" item="condition" collection="conditionMap">
	<if test="condition != null and condition.size() > 0">
		<foreach item="option"  index="index" collection="condition" open="AND (" separator="OR" close=")">	
			<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(key)" />
			${safe_columnName} = #{option}
		</foreach>	
	</if>	
</foreach>
</sql>

<sql id="outerQueryClauses_begin">
	SELECT B.CONTENT_NAME, A.*, TO_CHAR((A.DURATION || ' second')::interval, 'HH24:MI:SS') AS DURATION_STRING FROM(
</sql>
<sql id="outerQueryClauses_begin" databaseId="mssql">
	SELECT B.CONTENT_NAME, A.*, CONVERT(VARCHAR(8), DATEADD(SECOND, DURATION, '19000101'), 8) AS DURATION_STRING FROM(
</sql>
<sql id="outerQueryClauses_begin" databaseId="mysql">
	SELECT B.CONTENT_NAME, A.*, TIME_FORMAT(SEC_TO_TIME(A.DURATION)),'%H:%i:%s') AS DURATION_STRING FROM(
</sql>

<sql id="outerQueryClauses_end">
	) A, MI_CMS_INFO_CONTENT B WHERE A.CONTENT_ID = B.CONTENT_ID
</sql>


<sql id="outerQueryClausesNoContent_begin">
	SELECT A.*, TO_CHAR((A.DURATION || ' second')::interval, 'HH24:MI:SS') AS DURATION_STRING FROM(
</sql>
<sql id="outerQueryClausesNoContent_begin" databaseId="mssql">
	SELECT A.*, CONVERT(VARCHAR(8), DATEADD(SECOND, DURATION, '19000101'), 8) AS DURATION_STRING FROM(
</sql>
<sql id="outerQueryClausesNoContent_begin" databaseId="mysql">
	SELECT A.*, TIME_FORMAT(SEC_TO_TIME(A.DURATION),'%H:%i:%s') AS DURATION_STRING FROM(
</sql>

<sql id="outerQueryClausesNoContent_end">
	) A
</sql>

<select id="getYesterdayListBy" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClauses_begin" /> 
		<choose>
			<when test="unit  == 'DAY'">
                <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT  CONTENT_ID, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
				FROM MI_STATISTICS_FACE_CONTENT_DAY 
			</when>
			<when test="unit  == 'HOUR'">
                <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
				<include refid="dateTruncConverter"/>
			</when>
		</choose>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			<include refid="getYesterdayListByTruncConverter"/>
		</where>
		<choose>
			<when test="unit  == 'DAY'">
                <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			GROUP BY CONTENT_ID  ${safe_selectColumn}
			</when>
			<when test="unit  == 'HOUR'">
                <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			 GROUP BY CONTENT_ID, START_TIME  ${safe_selectColumn} ORDER BY TIME_STRING
			</when>
		</choose>
  	<include refid="outerQueryClauses_end"/>
</select>



<sql id="dateTruncConverter">
	SELECT CONTENT_ID, DATE_TRUNC('HOUR', START_TIME)::TIME AS TIME_STRING,
	SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION
	${safe_selectColumn}
	FROM MI_STATISTICS_FACE_CONTENT_HOUR
</sql>

<sql id="dateTruncConverter" databaseId="mysql">
	SELECT CONTENT_ID, DATE_FORMAT(START_TIME,'%H:00:00') AS TIME_STRING,
	SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION
	${safe_selectColumn}
	FROM MI_STATISTICS_FACE_CONTENT_HOUR
</sql>

<sql id="getYesterdayListByTruncConverter">
AND  START_TIME::DATE = DATE_TRUNC('DAY',CURRENT_DATE - interval '1days')::DATE
</sql>

<sql id="getYesterdayListByTruncConverter" databaseId="mysql">
AND  DATE_FORMAT(START_TIME, '%Y-%m-%d') = DATE_FORMAT(NOW() - INTERVAL 1 DAY, '%Y-%m-%d')
</sql>

<select id="getYesterdayListBy" databaseId="mssql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClauses_begin" /> 
		<choose>
			<when test="unit  == 'DAY'">
                <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
				SELECT CONTENT_ID, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
				FROM MI_STATISTICS_FACE_CONTENT_DAY 
			</when>
			<when test="unit  == 'HOUR'">
                <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
				SELECT CONTENT_ID, CONVERT(VARCHAR(8), DATEADD(HOUR, DATEDIFF(HOUR, 0, START_TIME), 0), 108) AS TIME_STRING,
				SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
				FROM MI_STATISTICS_FACE_CONTENT_HOUR
			</when>
		</choose>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			AND CONVERT(VARCHAR(10), START_TIME, 120) = CONVERT(VARCHAR(10), DATEADD(DAY, DATEDIFF(DAY, 1, GETDATE()), 0), 120)
		</where>
		<choose>
			<when test="unit  == 'DAY'">
                <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			GROUP BY CONTENT_ID  ${safe_selectColumn}
			</when>
			<when test="unit  == 'HOUR'">
                <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			 GROUP BY CONTENT_ID, START_TIME  ${safe_selectColumn} 
			</when>
		</choose>	
  	<include refid="outerQueryClauses_end"/>
  	<if test="unit  == 'HOUR'">
  		ORDER BY TIME_STRING
  	</if>
</select>

<sql id="truncAndConvertCurrentDateToStringDate">
	DATE_TRUNC('DAY',CURRENT_DATE - interval '1days')::DATE 
</sql>
<sql id="truncAndConvertCurrentDateToStringDate" databaseId="mssql">
	CONVERT(VARCHAR(10), DATEADD(DAY, DATEDIFF(DAY, 1, GETDATE()), 0), 120)
</sql>
<sql id="truncAndConvertCurrentDateToStringDate" databaseId="mysql">
	DATE_FORMAT(NOW() - INTERVAL 1 DAY, '%Y-%m-%d')
</sql>

<select id="getYesterdayListByForChart" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
 <include refid="outerQueryClausesNoContent_begin"/> 
	<choose>
		<when test="unit  == 'DAY'">
            <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT  SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION, ${safe_selectColumn}
				FROM MI_STATISTICS_FACE_CONTENT_DAY 
		</when>
		<when test="unit  == 'HOUR'">
            <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT <include refid="truncAndConvertToTimeStartTime"/> AS TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION, ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_HOUR
		</when>
	</choose>
	<where>
		<include refid="where_contentIdList"/> 
		<include refid="where_deviceIdList"/>
		<include refid="where_selectedConditionList"/>  
		 AND <include refid="convertStartTimeToStringDate"/> = <include refid="truncAndConvertCurrentDateToStringDate"/>
	</where>
	<choose>
		<when test="unit  == 'DAY'">
            <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			GROUP BY  ${safe_selectColumn}
		</when>
		<when test="unit  == 'HOUR'">
            <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			 GROUP BY START_TIME,  ${safe_selectColumn}  
		</when>
	</choose>
  <include refid="outerQueryClausesNoContent_end"/>
  <if test="unit  == 'HOUR'">
  	ORDER BY TIME_STRING
  </if>
</select>

<select id="getMonthListByMonth" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClauses_begin"/>
        <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
		SELECT CONTENT_ID, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			<include refid="conditionStartTimeAndIntervalOneMonths"/>		
		</where>
		GROUP BY CONTENT_ID ${safe_selectColumn}
	<include refid="outerQueryClauses_end"/>
</select>

<sql id="conditionStartTimeAndIntervalOneMonths">
	<choose>
		<when test="isThis">
			AND START_TIME = DATE_TRUNC('MONTH',CURRENT_DATE) 
		</when>
		<otherwise>
			AND START_TIME = DATE_TRUNC('MONTH',CURRENT_DATE - interval '1 months')
		</otherwise>
	</choose>	
</sql>
<sql id="conditionStartTimeAndIntervalOneMonths" databaseId="mssql">
	<choose>
		<when test="isThis">
			AND START_TIME = DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0)
		</when>
		<otherwise>
			AND START_TIME = DATEADD(MONTH, DATEDIFF(MONTH, 0, DATEADD(MONTH, -1, GETDATE())), 0)
		</otherwise>
	</choose>
</sql>

<sql id="conditionStartTimeAndIntervalOneMonths" databaseId="mysql">
	<choose>
		<when test="isThis">
			AND START_TIME = DATE_FORMAT(NOW(), '%Y-%m-01 %00:%00:%00')
		</when>
		<otherwise>
			AND START_TIME = DATE_FORMAT(NOW() - INTERVAL 1 MONTH, '%Y-%m-01 %00:%00:%00')
		</otherwise>
	</choose>	
</sql>

<select id="getMonthListByMonthForChart" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClausesNoContent_begin"/>
        <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
		SELECT SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION, ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			<include refid="conditionStartTimeAndIntervalOneMonths"/>	
		</where>
		GROUP BY ${safe_selectColumn}
	<include refid="outerQueryClausesNoContent_end"/>
</select>

<select id="getMonthListByDow" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClauses_begin"/>
        <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
		SELECT PLAY_DOW, CONTENT_ID, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_DAY
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			<include refid="conditionStartTimeAndIntervalOneMonth"/>		
		</where>
		GROUP BY CONTENT_ID, PLAY_DOW ${safe_selectColumn}
	<include refid="outerQueryClauses_end"/>
	ORDER BY PLAY_DOW, CONTENT_ID
</select>

<sql id="conditionStartTimeAndIntervalOneMonth">
	<choose>
		<when test="isThis">
			AND EXTRACT(MONTH FROM START_TIME) = EXTRACT(MONTH FROM CURRENT_DATE)
		</when>
		<otherwise>
			AND EXTRACT(MONTH FROM START_TIME) = EXTRACT(MONTH FROM CURRENT_DATE - interval '1 months')
		</otherwise>
	</choose>	
</sql>
<sql id="conditionStartTimeAndIntervalOneMonth" databaseId="mssql">
	<choose>
		<when test="isThis">
			AND MONTH(START_TIME) = MONTH(GETDATE())
		</when>
		<otherwise>
			AND MONTH(START_TIME) = MONTH(DATEADD(month, -1 ,GETDATE()))
		</otherwise>
	</choose>
</sql>

<sql id="conditionStartTimeAndIntervalOneMonth" databaseId="mysql">
	<choose>
		<when test="isThis">
			AND MONTH(START_TIME) = MONTH(NOW())
		</when>
		<otherwise>
			AND MONTH(START_TIME) = MONTH(NOW()) -1 
		</otherwise>
	</choose>	
</sql>

<select id="getMonthListByDowForChart" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClausesNoContent_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
		SELECT CONTENT_ID, PLAY_DOW, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_DAY
		<where>
			<include refid="where_contentIdList"/>
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/> 
			<include refid="conditionStartTimeAndIntervalOneMonth"/> 	
		</where>
		GROUP BY CONTENT_ID, PLAY_DOW ${safe_selectColumn}
	<include refid="outerQueryClausesNoContent_end"/>
	ORDER BY PLAY_DOW, CONTENT_ID
</select>

<select id="getQuarterListByQuarter" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClauses_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
		SELECT CONTENT_ID, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/> 
			<include refid="conditionYearAndLogQuerter"/> 		
		</where>
		GROUP BY CONTENT_ID  ${safe_selectColumn}
	<include refid="outerQueryClauses_end"/>
</select>

<select id="getQuarterListByQuarterForChart" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClausesNoContent_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
		SELECT SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION, ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			<choose>
				<when test="isThis">
					AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE) AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE)
				</when>
				<otherwise>
					AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE - interval '3 months') AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE - interval '3 months')
				</otherwise>
			</choose>		
		</where>
		GROUP BY ${safe_selectColumn}
	<include refid="outerQueryClausesNoContent_end"/>
</select>

<select id="getQuarterListByMonth" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClauses_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
		SELECT CONTENT_ID, TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn} FROM (
			SELECT <include refid="monthStringFromStartTime"/> AS TIME_STRING, CONTENT_ID, VIEW_COUNT, DURATION
					 FROM MI_STATISTICS_FACE_CONTENT_MONTH
			<where>
				<include refid="where_contentIdList"/> 
				<include refid="where_deviceIdList"/>
				<include refid="where_selectedConditionList"/>  
				<include refid="conditionYearAndLogQuerter"/>		
			</where>
		) AS subquery
		GROUP BY CONTENT_ID, TIME_STRING ${safe_selectColumn}
	<include refid="outerQueryClauses_end"/>
	ORDER BY TIME_STRING, CONTENT_ID
</select>

<sql id="conditionYearAndLogQuerter">
	<choose>
		<when test="isThis">
			AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE) 
			AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE)
		</when>
		<otherwise>
			AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE - interval '3 months')
			AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE - interval '3 months')
		</otherwise>
	</choose>
</sql>
<sql id="conditionYearAndLogQuerter" databaseId="mssql">
	<choose>
		<when test="isThis">
			AND YEAR(START_TIME) = YEAR(GETDATE()) 
			AND LOG_QUARTER = DATENAME(Quarter, CAST(CONVERT(VARCHAR(8), GETDATE()) AS DATETIME))
		</when>
		<otherwise>
			AND YEAR(START_TIME) = YEAR(DATEADD(month, -3 ,GETDATE()))
			AND LOG_QUARTER = DATENAME(Quarter, CAST(CONVERT(VARCHAR(8),  DATEADD(month, -3 ,GETDATE())) AS DATETIME))
		</otherwise>
	</choose>
</sql>

<sql id="conditionYearAndLogQuerter" databaseId="mysql">
	<choose>
		<when test="isThis">
			AND YEAR(START_TIME) = YEAR(NOW()) 
			AND LOG_QUARTER = QUARTER(NOW())
		</when>
		<otherwise>
			AND YEAR(START_TIME) = YEAR(NOW())
			AND LOG_QUARTER = QUARTER(NOW()) -1
		</otherwise>
	</choose>
</sql>

<sql id="monthStringFromStartTime">
	TO_CHAR(START_TIME,'Month')
</sql>

<sql id="monthStringFromStartTime" databaseId="mssql">
	DateName( month , DateAdd( month , MONTH(START_TIME) , 0 ) - 1 )
</sql>

<sql id="monthStringFromStartTime" databaseId="mysql">
	MONTHNAME(START_TIME)
</sql>

<sql id="extractMonthFromStartTime">
	extract(month from START_TIME)
</sql>

<sql id="extractMonthFromStartTime" databaseId="mssql">
	MONTH(START_TIME)
</sql>

<sql id="extractMonthFromStartTime" databaseId="mysql">
	MONTH(START_TIME)
</sql>

<select id="getQuarterListByMonthForChart" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClausesNoContent_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
		SELECT MONTH_ORDER, TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn} FROM (
			SELECT <include refid="extractMonthFromStartTime"/> AS MONTH_ORDER, <include refid="monthStringFromStartTime"/> AS TIME_STRING, VIEW_COUNT, DURATION
					 FROM MI_STATISTICS_FACE_CONTENT_MONTH
			<where>
				<include refid="where_contentIdList"/> 
				<include refid="where_deviceIdList"/>
				<include refid="where_selectedConditionList"/>  
				<include refid="conditionYearAndLogQuerter"/>
			</where>
		) AS subquery
		GROUP BY TIME_STRING, MONTH_ORDER ${safe_selectColumn}
	<include refid="outerQueryClausesNoContent_end"/>
	ORDER BY MONTH_ORDER
</select>

<select id="getYearListByYear" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClauses_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT CONTENT_ID, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_YEAR
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			<include refid="conditionStartTimeAndIntervalOneYears"/>		
		</where>
		GROUP BY CONTENT_ID ${safe_selectColumn}
	<include refid="outerQueryClauses_end"/>
</select>

<sql id="conditionStartTimeAndIntervalOneYears">
	<choose>
		<when test="isThis">
			AND START_TIME = DATE_TRUNC('YEAR',CURRENT_DATE)
		</when>
	<otherwise>
			AND START_TIME = DATE_TRUNC('YEAR',CURRENT_DATE - interval '1 years')
		</otherwise>
	</choose>	
</sql>
<sql id="conditionStartTimeAndIntervalOneYears" databaseId="mssql">
	<choose>
		<when test="isThis">
			AND START_TIME = DATEADD(YEAR, DATEDIFF(YEAR, 0, GETDATE()), 0)
		</when>
	<otherwise>
			AND START_TIME = DATEADD(YEAR, DATEDIFF(YEAR, 0, DATEADD(YEAR, -1, GETDATE())), 0)
		</otherwise>
	</choose>	
</sql>
<sql id="conditionStartTimeAndIntervalOneYears" databaseId="mysql">
	<choose>
		<when test="isThis">
			AND START_TIME = DATE_FORMAT(NOW(), '%Y-01-01')
		</when>
	<otherwise>
			AND START_TIME = DATE_FORMAT(NOW() - INTERVAL 1 YEAR, '%Y-01-01')
		</otherwise>
	</choose>	
</sql>

<select id="getYearListByYearForChart" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClausesNoContent_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
		SELECT SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION, ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_YEAR
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			<include refid="conditionStartTimeAndIntervalOneYears"/>		
		</where>
		GROUP BY ${safe_selectColumn}
	<include refid="outerQueryClausesNoContent_end"/>
</select>

<sql id="conditionYearStartTimeToCurrDate">
	<choose>
		<when test="isThis">
			AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE) 
		</when>
		<otherwise>
			AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE - interval '1 years')
		</otherwise>
	</choose>	
</sql>
<sql id="conditionYearStartTimeToCurrDate" databaseId="mssql">
	<choose>
		<when test="isThis">
			AND YEAR(START_TIME) = YEAR(GETDATE())
		</when>
		<otherwise>
			AND YEAR(START_TIME) = YEAR(GETDATE()) - 1
		</otherwise>
	</choose>
</sql>
<sql id="conditionYearStartTimeToCurrDate" databaseId="mysql">
	<choose>
		<when test="isThis">
			AND YEAR(START_TIME) = YEAR(NOW())
		</when>
		<otherwise>
			AND YEAR(START_TIME) = YEAR(NOW()) - 1
		</otherwise>
	</choose>
</sql>

<select id="getYearListByQuarter" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClauses_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT LOG_QUARTER, CONTENT_ID, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			<include refid="conditionYearStartTimeToCurrDate"/>	
		</where>
		GROUP BY CONTENT_ID, LOG_QUARTER ${safe_selectColumn}
	<include refid="outerQueryClauses_end"/>
	ORDER BY LOG_QUARTER, CONTENT_ID
</select>

<sql id="compareStartTimeYearToCurrentYear">
	<choose>
		<when test="isThis">
			AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE)
		</when>
		<otherwise>
			AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE - interval '1 years')
		</otherwise>
	</choose>	
</sql>
<sql id="compareStartTimeYearToCurrentYear" databaseId="mssql">
	<choose>
		<when test="isThis">
			AND YEAR(START_TIME) = YEAR(GETDATE())
		</when>
		<otherwise>
			AND YEAR(START_TIME) = YEAR(GETDATE()) - 1
		</otherwise>
	</choose>	
</sql>

<sql id="compareStartTimeYearToCurrentYear" databaseId="mysql">
	<choose>
		<when test="isThis">
			AND YEAR(START_TIME) = YEAR(NOW())
		</when>
		<otherwise>
			AND YEAR(START_TIME) = YEAR(NOW()) - 1
		</otherwise>
	</choose>	
</sql>

<select id="getYearListByQuarterForChart" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClausesNoContent_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
		SELECT LOG_QUARTER, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			<include refid="compareStartTimeYearToCurrentYear"/>	
		</where>
		GROUP BY LOG_QUARTER ${safe_selectColumn}
	<include refid="outerQueryClausesNoContent_end"/>
	ORDER BY LOG_QUARTER
</select>

<sql id="truncAndConvertToTimeStartTime">
	DATE_TRUNC('HOUR', START_TIME)::TIME
</sql>
<sql id="truncAndConvertToTimeStartTime" databaseId="mssql">
	CONVERT(VARCHAR(8), DATEADD(HOUR, DATEDIFF(HOUR, 0, START_TIME), 0), 108)
</sql>
<sql id="truncAndConvertToTimeStartTime" databaseId="mysql">
	DATE_FORMAT(START_TIME,'%H:00:00')
</sql>

<select id="getWeekListByHour" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClauses_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT CONTENT_ID, <include refid="truncAndConvertToTimeStartTime"/> AS TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_HOUR
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}				
		</where>
		GROUP BY CONTENT_ID, START_TIME ${safe_selectColumn}
	<include refid="outerQueryClauses_end"/>
</select>

<sql id="ConverterForGetweekListByHourForChart">
	SELECT DATE_TRUNC('HOUR', START_TIME)::TIME AS TIME_STRING, SUM(VIEW_COUNT)
	AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
	FROM MI_STATISTICS_FACE_CONTENT_HOUR
</sql>

<sql id="ConverterForGetweekListByHourForChart" databaseId="mysql">
	SELECT DATE_FORMAT(START_TIME,'%H:00:00') AS TIME_STRING, SUM(VIEW_COUNT) AS
	VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
	FROM MI_STATISTICS_FACE_CONTENT_HOUR
</sql>

<select id="getWeekListByHourForChart" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClausesNoContent_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
		<include refid="ConverterForGetweekListByHourForChart"/>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}	
		</where>
		GROUP BY START_TIME ${safe_selectColumn}
		ORDER BY TIME_STRING
	<include refid="outerQueryClausesNoContent_end"/>
</select>

<select id="getWeekListByHourForChart" databaseId="mssql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClausesNoContent_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
		SELECT START_TIME, TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION FROM
		(
			SELECT START_TIME, CONVERT(VARCHAR(8), DATEADD(HOUR, DATEDIFF(HOUR, 0, START_TIME), 0), 108) AS TIME_STRING, VIEW_COUNT, DURATION
			FROM MI_STATISTICS_FACE_CONTENT_HOUR
			<where>
				<include refid="where_contentIdList"/> 
				<include refid="where_deviceIdList"/>
				<include refid="where_selectedConditionList"/>  
				 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}	
			</where>
		) AS subquery
		GROUP BY START_TIME, TIME_STRING ${safe_selectColumn}
	<include refid="outerQueryClausesNoContent_end"/>
	ORDER BY TIME_STRING
</select>

<sql id="convertStartTimeToStringDate">
	START_TIME::DATE
</sql>
<sql id="convertStartTimeToStringDate" databaseId="mssql">
	CONVERT(VARCHAR(10), START_TIME, 120)
</sql>
<sql id="convertStartTimeToStringDate" databaseId="mysql">
	DATE_FORMAT(START_TIME, '%Y-%m-%d')
</sql>


<select id="getWeekListByDay" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClauses_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT CONTENT_ID, <include refid="convertStartTimeToStringDate"/> AS TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_DAY
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}				
		</where>
		GROUP BY CONTENT_ID, START_TIME ${safe_selectColumn}
	<include refid="outerQueryClauses_end"/>
</select>

<select id="getWeekListByDayForChart" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClausesNoContent_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
		SELECT <include refid="convertStartTimeToStringDate"/> AS TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_DAY
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}	
		</where>
		GROUP BY START_TIME ${safe_selectColumn}
	<include refid="outerQueryClausesNoContent_end"/>
	ORDER BY TIME_STRING
</select>

<select id="getWeekListByWeek" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClauses_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT CONTENT_ID, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_DAY
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}				
		</where>
		GROUP BY CONTENT_ID ${safe_selectColumn}
	<include refid="outerQueryClauses_end"/>
</select>

<select id="getWeekListByWeekForChart" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClausesNoContent_begin"/>
    <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
		 SELECT SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION,  ${safe_selectColumn}
				 FROM MI_STATISTICS_FACE_CONTENT_DAY
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}	
		</where>
		GROUP BY ${safe_selectColumn}
	<include refid="outerQueryClausesNoContent_end"/>
</select>

<select id="getCustomList" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClauses_begin" /> 
		<include refid="conditionForGetCustomList"/>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}
		</where>
		<choose>
			<when test="unit  == 'DAY'">
                <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			GROUP BY CONTENT_ID, TIME_STRING  ${safe_selectColumn}
			</when>
			<when test="unit  == 'HOUR'">
                <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			 GROUP BY CONTENT_ID, START_TIME  ${safe_selectColumn} 
			 ORDER BY TIME_STRING, CONTENT_ID
			</when>
		</choose>
  	<include refid="outerQueryClauses_end"/>
</select>

<select id="getCustomList" databaseId="mssql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
	<include refid="outerQueryClauses_begin" /> 
			<include refid="conditionForGetCustomList"/>
			<where>
				<include refid="where_contentIdList"/> 
				<include refid="where_deviceIdList"/>
				<include refid="where_selectedConditionList"/>  
				 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}
			</where>
		) AS subquery
		<choose>
			<when test="unit  == 'DAY'">
                <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			GROUP BY CONTENT_ID, TIME_STRING  ${safe_selectColumn}
			</when>
			<when test="unit  == 'HOUR'">
                <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			 GROUP BY CONTENT_ID, START_TIME, TIME_STRING  ${safe_selectColumn} 
			</when>
		</choose>
  	<include refid="outerQueryClauses_end"/>
  	<if test="unit  == 'HOUR'">
  		ORDER BY TIME_STRING, CONTENT_ID
  	</if>
</select>

<sql id="conditionForGetCustomList">
	<choose>
		<when test="unit  == 'DAY'">
			<bind name="safe_selectColumn"
				value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT CONTENT_ID, DATE_TRUNC('DAY', START_TIME)::DATE AS
			TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION
			${safe_selectColumn}
			FROM MI_STATISTICS_FACE_CONTENT_DAY
		</when>
		<when test="unit  == 'HOUR'">
			<bind name="safe_selectColumn"
				value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT CONTENT_ID, TO_CHAR(DATE_TRUNC('HOUR', START_TIME),'YYYY-MM-DD
			HH24:MI:SS') AS TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT,
			SUM(DURATION) AS DURATION ${safe_selectColumn}
			FROM MI_STATISTICS_FACE_CONTENT_HOUR
		</when>
	</choose>
</sql>

<sql id="conditionForGetCustomList" databaseId="mssql">
	<choose>
		<when test="unit  == 'DAY'">
			<bind name="safe_selectColumn"
				value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT CONTENT_ID, TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT,
			SUM(DURATION) AS DURATION ${safe_selectColumn} FROM (
			SELECT CONTENT_ID, START_TIME, CONVERT(VARCHAR(10), DATEADD(DAY,
			DATEDIFF(DAY, 0, START_TIME), 0), 120) AS TIME_STRING,
			VIEW_COUNT, DURATION FROM MI_STATISTICS_FACE_CONTENT_DAY
		</when>
		<when test="unit  == 'HOUR'">
			<bind name="safe_selectColumn"
				value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT CONTENT_ID, START_TIME, TIME_STRING, SUM(VIEW_COUNT) AS
			VIEW_COUNT, SUM(DURATION) AS DURATION ${safe_selectColumn} FROM (
			SELECT CONTENT_ID, START_TIME, CONVERT(VARCHAR(19), DATEADD(HOUR,
			DATEDIFF(HOUR, 0, START_TIME), 0), 120) AS TIME_STRING,
			VIEW_COUNT, DURATION FROM MI_STATISTICS_FACE_CONTENT_HOUR
		</when>
	</choose>
</sql>

<sql id="conditionForGetCustomList" databaseId="mysql">
	<choose>
		<when test="unit  == 'DAY'">
			<bind name="safe_selectColumn"
				value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT CONTENT_ID, DATE_FORMAT(START_TIME, '%Y-%m-%d') AS
			TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION
			${safe_selectColumn}
			FROM MI_STATISTICS_FACE_CONTENT_DAY
		</when>
		<when test="unit  == 'HOUR'">
			<bind name="safe_selectColumn"
				value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT CONTENT_ID, DATE_FORMAT(START_TIME, '%Y-%m-%d %H:%i:%s') AS TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT,
			SUM(DURATION) AS DURATION ${safe_selectColumn}
			FROM MI_STATISTICS_FACE_CONTENT_HOUR
		</when>
	</choose>
</sql>

<sql id="conditionForUnitDayOrHour">
	<choose>
		<when test="unit  == 'DAY'">
			SELECT START_TIME, DATE_TRUNC('DAY', START_TIME)::DATE AS TIME_STRING, VIEW_COUNT, DURATION
				FROM MI_STATISTICS_FACE_CONTENT_DAY 
		</when>
		<when test="unit  == 'HOUR'">
			SELECT START_TIME, TO_CHAR(DATE_TRUNC('HOUR', START_TIME),'YYYY-MM-DD HH24:MI:SS') AS TIME_STRING, VIEW_COUNT, DURATION
				 FROM MI_STATISTICS_FACE_CONTENT_HOUR
		</when>
	</choose>
</sql>
<sql id="conditionForUnitDayOrHour" databaseId="mssql">
	<choose>
		<when test="unit  == 'DAY'">
			SELECT START_TIME, CONVERT(VARCHAR(10), DATEADD(DAY, DATEDIFF(DAY, 0, START_TIME), 0), 120) AS TIME_STRING, 
			VIEW_COUNT, DURATION FROM MI_STATISTICS_FACE_CONTENT_DAY 
		</when>
		<when test="unit  == 'HOUR'">
			SELECT START_TIME, CONVERT(VARCHAR(19), DATEADD(HOUR, DATEDIFF(HOUR, 0, START_TIME), 0), 120) AS TIME_STRING,
			VIEW_COUNT, DURATION FROM MI_STATISTICS_FACE_CONTENT_DAY 
		</when>
	</choose>
</sql>

<sql id="conditionForUnitDayOrHour" databaseId="mysql">
	<choose>
		<when test="unit  == 'DAY'">
			SELECT START_TIME, DATE_FORMAT(START_TIME, '%Y-%m-%d') AS TIME_STRING, VIEW_COUNT, DURATION
				FROM MI_STATISTICS_FACE_CONTENT_DAY 
		</when>
		<when test="unit  == 'HOUR'">
			SELECT START_TIME, DATE_FORMAT(START_TIME, '%Y-%m-%d %H:%i:%s') AS TIME_STRING, VIEW_COUNT, DURATION
				 FROM MI_STATISTICS_FACE_CONTENT_HOUR
		</when>
	</choose>
</sql>

<select id="getCustomListForChart" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
 <include refid="outerQueryClausesNoContent_begin"/> 
		<include refid="chooseDayOrHour"/>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>  
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}	
		</where>
	<choose>
		<when test="unit  == 'DAY'">
            <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			GROUP BY TIME_STRING  ${safe_selectColumn}
			ORDER BY TIME_STRING
		</when>
		<when test="unit  == 'HOUR'">
            <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			 GROUP BY START_TIME  ${safe_selectColumn} 
			 ORDER BY TIME_STRING
		</when>
	</choose>
  <include refid="outerQueryClausesNoContent_end"/>
</select>

<select id="getCustomListForChart" databaseId="mssql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceContentBasedEntity">
 <include refid="outerQueryClausesNoContent_begin"/> 
 <include refid="chooseDayOrHour"/>
			<where>
				<include refid="where_contentIdList"/> 
				<include refid="where_deviceIdList"/>
				<include refid="where_selectedConditionList"/>  
				 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}	
			</where>
		) AS subquery
 	<choose>
		<when test="unit  == 'DAY'">
            <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			GROUP BY TIME_STRING ${safe_selectColumn}
		</when>
		<when test="unit  == 'HOUR'">
            <bind name="safe_selectColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			GROUP BY START_TIME, TIME_STRING ${safe_selectColumn} 
		</when>
	</choose>
  <include refid="outerQueryClausesNoContent_end"/>
  <choose>
		<when test="unit  == 'DAY'">
			ORDER BY TIME_STRING
		</when>
		<when test="unit  == 'HOUR'">
			ORDER BY TIME_STRING
		</when>
	</choose>
</select>

<sql id="chooseDayOrHour">
	<choose>
		<when test="unit  == 'DAY'">
			<bind name="safe_selectColumn"
				value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT DATE_TRUNC('DAY', START_TIME)::DATE AS TIME_STRING,
			SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION
			${safe_selectColumn}
			FROM MI_STATISTICS_FACE_CONTENT_DAY
		</when>
		<when test="unit  == 'HOUR'">
			<bind name="safe_selectColumn"
				value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT TO_CHAR(DATE_TRUNC('HOUR', START_TIME),'YYYY-MM-DD
			HH24:MI:SS') AS TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT,
			SUM(DURATION) AS DURATION ${safe_selectColumn}
			FROM MI_STATISTICS_FACE_CONTENT_HOUR
		</when>
	</choose>
</sql>

<sql id="chooseDayOrHour" databaseId="mssql">
	<choose>
		<when test="unit  == 'DAY'">
			<bind name="safe_selectColumn"
				value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS
			DURATION ${safe_selectColumn} FROM (
			SELECT START_TIME, CONVERT(VARCHAR(10), DATEADD(DAY, DATEDIFF(DAY, 0,
			START_TIME), 0), 120) AS TIME_STRING,
			VIEW_COUNT, DURATION FROM MI_STATISTICS_FACE_CONTENT_DAY
		</when>
		<when test="unit  == 'HOUR'">
			<bind name="safe_selectColumn"
				value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT START_TIME, TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT,
			SUM(DURATION) AS DURATION ${safe_selectColumn} FROM (
			SELECT START_TIME, CONVERT(VARCHAR(19), DATEADD(HOUR, DATEDIFF(HOUR, 0,
			START_TIME), 0), 120) AS TIME_STRING,
			VIEW_COUNT, DURATION FROM MI_STATISTICS_FACE_CONTENT_HOUR
		</when>
	</choose>
</sql>

<sql id="chooseDayOrHour" databaseId="mysql">
	<choose>
		<when test="unit  == 'DAY'">
			<bind name="safe_selectColumn"
				value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT DATE_FORMAT(START_TIME, '%Y-%m-%d') AS TIME_STRING,
			SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION
			${safe_selectColumn}
			FROM MI_STATISTICS_FACE_CONTENT_DAY
		</when>
		<when test="unit  == 'HOUR'">
			<bind name="safe_selectColumn"
				value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT DATE_FORMAT(START_TIME, '%Y-%m-%d %H:%i:%s') AS TIME_STRING,
			SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION
			${safe_selectColumn}
			FROM MI_STATISTICS_FACE_CONTENT_HOUR
		</when>
	</choose>
</sql>


</mapper>