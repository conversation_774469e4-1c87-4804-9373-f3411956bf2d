package com.samsung.magicinfo.protocol.util.mail;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.util.BeanUtils;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManager;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import java.util.Properties;
import java.util.StringTokenizer;
import javax.mail.Authenticator;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.Message.RecipientType;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;

public class MailManager implements MailManagerInterface {
   protected Session session;
   private ArrayList toLists;
   private String mailHost;
   private String from;
   private String subject;
   private String body;
   private String toList;
   static Logger logger = LoggingManagerV2.getLogger(MailManager.class);
   private static volatile MailManagerInterface instance;

   private MailManager() {
      super();
   }

   public static MailManagerInterface getInstance() {
      if (instance == null) {
         Class var0 = MailManager.class;
         synchronized(MailManager.class) {
            if (instance == null) {
               instance = new MailManager();
            }
         }
      }

      return instance;
   }

   public boolean isComplete(com.samsung.magicinfo.protocol.entity.Mail mail) {
      this.from = mail.getFrom();
      this.subject = mail.getSubject();
      this.body = mail.getBody();
      this.toList = mail.getToList();
      this.toLists = this.tokenize(this.toList);
      if (this.from != null && this.from.length() != 0) {
         if (this.subject != null && this.subject.length() != 0) {
            if (this.toList.length() == 0) {
               logger.debug("doSend:no recipients");
               return false;
            } else if (this.body != null && this.body.length() != 0) {
               if (this.mailHost != null && this.mailHost.length() != 0) {
                  return true;
               } else {
                  logger.debug("doSend:no server host");
                  return false;
               }
            } else {
               logger.debug("doSend:no body");
               return false;
            }
         } else {
            logger.debug("doSend:no subject");
            return false;
         }
      } else {
         logger.debug("doSend:no from");
         return false;
      }
   }

   public synchronized void sendEmail(com.samsung.magicinfo.protocol.entity.Mail mail, Map map) throws MessagingException {
      String mailEnable = null;
      String timeoutConfigValue = "";
      int timeoutMillisecond = 90000;
      this.mailHost = map.get("SMTP_ADDRESS").toString();
      if (!this.isComplete(mail)) {
         throw new IllegalArgumentException("doSend called before message was complete");
      } else {
         Properties props = new Properties();
         props.put("mail.smtp.host", this.mailHost);

         try {
            timeoutConfigValue = CommonConfig.get("mail.smtp.connection.timeout");
         } catch (ConfigException var22) {
            logger.debug("ConfigException occured. Set timeout value to default value 90000 millisecond");
            timeoutConfigValue = "90000";
         }

         if (!StringUtils.isEmpty(timeoutConfigValue)) {
            timeoutMillisecond = Integer.parseInt(timeoutConfigValue);
         }

         props.put("mail.smtp.timeout", timeoutMillisecond);
         props.put("mail.smtp.connectiontimeout", timeoutMillisecond);
         props.put("mail.smtp.port", map.containsKey("SMTP_AUTH_PORT") ? map.get("SMTP_AUTH_PORT").toString() : "");
         PIIDataManager piiDataManager = (PIIDataManager)BeanUtils.getBean("PIIDataManager");
         String smtpAuthEnable = map.containsKey("SMTP_AUTH_ENABLE") ? map.get("SMTP_AUTH_ENABLE").toString() : "false";
         String smtpSslEnable = map.containsKey("SMTP_AUTH_SSL_ENABLE") ? map.get("SMTP_AUTH_SSL_ENABLE").toString() : "false";
         final String smtpId = map.containsKey("SMTP_AUTH_ID") ? map.get("SMTP_AUTH_ID").toString() : "";
         final String smtpPwd = map.containsKey("SMTP_AUTH_PWD") ? map.get("SMTP_AUTH_PWD").toString() : "";
         String mailPort = map.containsKey("SMTP_AUTH_PORT") ? map.get("SMTP_AUTH_PORT").toString() : "";
         String smtpEncVersion = map.containsKey("SMTP_ENC_VERSION") ? map.get("SMTP_ENC_VERSION").toString() : null;
         smtpId = piiDataManager.decryptData(smtpId);
         props.put("mail.smtp.port", mailPort);
         props.put("mail.smtp.sendpartial", true);
         smtpPwd = SecurityUtils.getDecryptionPassword(smtpPwd, smtpEncVersion);
         if (smtpAuthEnable.equals("true")) {
            String fSmtpId = smtpId;

            try {
               if (CommonConfig.get("e2e.enable") != null && CommonConfig.get("e2e.enable").equalsIgnoreCase("true")) {
                  this.from = "<EMAIL>";
               } else {
                  this.from = fSmtpId;
               }
            } catch (ConfigException var23) {
               this.from = smtpId;
            }

            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.user", smtpId);
            if (smtpSslEnable.equals("true")) {
               props.put("mail.smtp.socketFactory.port", mailPort);
               props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
               props.put("mail.smtp.starttls.enable", "false");
               props.put("mail.smtp.ssl.enable", "true");
               this.session = Session.getInstance(props, new Authenticator() {
                  protected PasswordAuthentication getPasswordAuthentication() {
                     return new PasswordAuthentication(smtpId, smtpPwd);
                  }
               });
            } else {
               props.put("mail.smtp.starttls.enable", "true");
               this.session = Session.getInstance(props, new Authenticator() {
                  protected PasswordAuthentication getPasswordAuthentication() {
                     return new PasswordAuthentication(smtpId, smtpPwd);
                  }
               });
            }

            props.put("mail.smtp.ssl.protocols", "TLSv1.2");
         } else {
            this.session = Session.getInstance(props, (Authenticator)null);
         }

         this.session.setDebug(true);
         Message mesg = new MimeMessage(this.session);
         InternetAddress[] addresses = new InternetAddress[this.toLists.size()];

         for(int i = 0; i < addresses.length; ++i) {
            addresses[i] = new InternetAddress((String)this.toLists.get(i));
         }

         mesg.setRecipients(RecipientType.TO, addresses);
         mesg.setFrom(new InternetAddress(this.from));
         mesg.setSubject(this.subject);
         mesg.setSentDate(new Date());
         Multipart mp = new MimeMultipart();
         BodyPart pixPart = new MimeBodyPart();
         pixPart.setContent(this.body, "text/html;charset=UTF-8");
         mp.addBodyPart(pixPart);
         String attachFile = (String)map.get("attachedFileName");
         if (attachFile != null && !attachFile.equals("")) {
            MimeBodyPart attachPart = new MimeBodyPart();

            try {
               attachPart.attachFile(attachFile);
            } catch (IOException var21) {
               logger.error("", var21);
            }

            mp.addBodyPart(attachPart);
         }

         mesg.setContent(mp);
         Transport.send(mesg);
         logger.info("MailManager.java addresses:" + addresses.toString() + " from:" + this.from);
      }
   }

   public ArrayList tokenize(String tolist) {
      ArrayList arrayList = new ArrayList();
      StringTokenizer tf = new StringTokenizer(tolist, ",");

      while(tf.hasMoreTokens()) {
         arrayList.add(tf.nextToken().trim());
      }

      return arrayList;
   }
}
