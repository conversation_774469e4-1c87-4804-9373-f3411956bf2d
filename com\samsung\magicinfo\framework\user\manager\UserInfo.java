package com.samsung.magicinfo.framework.user.manager;

import com.samsung.common.db.DBListExecuter;
import com.samsung.common.db.PagedListInfo;
import com.samsung.magicinfo.auth.security.otp.UserAuthDevice;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

public interface UserInfo extends DBListExecuter {
   User getAllByUserId(String var1) throws SQLException;

   List getAllUser(Map var1, int var2, int var3) throws SQLException;

   int getCountByUserId(String var1) throws SQLException;

   int getCountByUserIdIsDeleted(String var1) throws SQLException;

   int getCountAllUser(Map var1) throws SQLException;

   List getGroupedUser(Map var1, int var2, int var3) throws SQLException;

   int getCountGroupedUser(Map var1) throws SQLException;

   Boolean addUser(User var1, boolean var2) throws SQLException;

   Boolean addUser(User var1, long var2, boolean var4) throws SQLException;

   Boolean setUser(User var1) throws SQLException;

   Boolean setIsApprovedByUserId(String var1, String var2) throws SQLException;

   Boolean setLoginDateByUserId(String var1) throws SQLException;

   Boolean setIsDeletedByUserId(String var1, String var2, boolean var3) throws SQLException;

   String getNameByUserId(String var1) throws SQLException;

   Long getRootGroupIdByUserId(String var1) throws SQLException;

   int getCountAllNonApprovedUser(Map var1) throws SQLException;

   List getAllNonApprovedUser(Map var1, int var2, int var3) throws SQLException;

   int getCountAllWithdrawalUser(Map var1) throws SQLException;

   List getLdapUserInfo(String var1) throws SQLException;

   List getAllWithdrawalUser(Map var1, int var2, int var3) throws SQLException;

   User getUserByUserId(String var1) throws SQLException;

   int deleteUser(String var1) throws SQLException;

   int deleteLDAPUser(Long var1) throws SQLException;

   Boolean setApprovalByUser(User var1) throws SQLException;

   String getIsApprovedByUserId(String var1) throws SQLException;

   String getIsDeletedByUserId(String var1) throws SQLException;

   PagedListInfo getPagedList(int var1, int var2, Map var3, String var4) throws Exception;

   boolean setOrganizationByUserIdList(String var1, List var2) throws SQLException, ConfigException;

   List getAllApprovalUserByRootGroupId(Long var1, boolean var2) throws SQLException;

   List getAllUserByRootGroupId(Long var1) throws SQLException;

   List getAllRootUserList() throws SQLException;

   List getAllUserListByRootGroupId(Long var1) throws SQLException;

   boolean setRejectUser(String var1, String var2) throws SQLException, ConfigException;

   Map getIsRejectByUserId(String var1) throws SQLException;

   Map getDeleteInfoByUserId(String var1) throws SQLException;

   List getAllUserList(Map var1) throws SQLException;

   List getAllUserList(Long var1) throws SQLException;

   List getAllUserListToMigrate() throws SQLException;

   boolean migrateUser(User var1) throws SQLException;

   int deleteMappingInfoByUserID(String var1) throws SQLException;

   String getOrganNameByUserId(String var1) throws SQLException;

   int getCountByUserIdForCheck(String var1) throws SQLException;

   int getCountByLDAPUserFullIdForCheck(String var1) throws SQLException;

   int getCountByLDAPUserIdForCheck(String var1) throws SQLException;

   String getMailList(String var1) throws SQLException;

   String getSMSList(String var1) throws SQLException;

   List getAllUserListBySearch(Long var1) throws SQLException;

   String getImeiByUserId(String var1) throws SQLException;

   String getOsTypeByUserId(String var1) throws SQLException;

   String getIsResetPwdByUserId(String var1) throws SQLException;

   User getUserInfo(String var1) throws SQLException;

   boolean setEmailSendingOptions(String var1, String var2, String var3) throws SQLException;

   boolean setEmailSendingDisconnectAlarm(String var1, String var2) throws SQLException;

   String getSendToList(Long var1, String var2, String var3) throws SQLException;

   String getShortcut(String var1) throws SQLException;

   boolean setShortcut(String var1, String var2) throws SQLException;

   String getOrganGroupName(Long var1) throws SQLException;

   List getNewAndModifiedUserByDate(String var1, String var2) throws SQLException;

   List getOrganization() throws SQLException;

   List getDisconnectAlarmUserListByOrgId(Long var1) throws SQLException;

   List getContentManagerUserListByOrgId(Long var1) throws SQLException;

   boolean setContentApprover(String var1, String var2) throws SQLException;

   boolean setAllContentApprover(String var1) throws SQLException;

   List getContentApproverListByGroupId(Long var1) throws SQLException;

   List getContentApprover() throws SQLException;

   boolean setLocale(String var1, String var2) throws SQLException;

   List getEmailNotificationUserListByOrgId(Long var1, boolean var2) throws SQLException;

   boolean setEmailNotificationOption(Long var1, String var2, String var3, boolean var4) throws SQLException;

   List getAlarmUserListByOrgIdAndType(Long var1, String var2) throws SQLException;

   String getOrganNameByRootGroupId(long var1) throws SQLException;

   int deleteEmailNotificationByOrdIdAndUserId(Long var1, String var2) throws SQLException;

   Integer deleteDashboardUserInfoByUserId(String var1) throws SQLException;

   boolean chkOrganizationByUserId(String var1, String var2) throws SQLException;

   Boolean setIsFirstLoginByUserId(String var1) throws SQLException;

   boolean setResetPwToken(String var1, long var2, String var4);

   boolean checkResetValidByTokenAndId(String var1, String var2) throws SQLException;

   List getAllUserWithEncryptionToken() throws SQLException;

   long getCurMngOrgId(String var1) throws SQLException;

   boolean setCurMngOrgId(String var1, long var2) throws SQLException;

   List getMUInfoByMngOrgId(long var1) throws SQLException;

   boolean setMUByUserId(String var1, String var2) throws SQLException;

   User getAdminOfOrganizationByRootGroupId(long var1) throws SQLException;

   User getAdminOfOrganizationNotInDeleteUsers(long var1, String[] var3) throws SQLException;

   int setIsResetPasswordBatch() throws SQLException;

   List getfilterExport(Map var1) throws SQLException;

   int addAuthDeviceInfo(UserAuthDevice var1) throws SQLException;

   long getAuthDeviceId() throws SQLException;

   List getUserAuthDevice(String var1) throws SQLException;

   List getUserStoredDevice(String var1) throws SQLException;

   int setUserSecretKey(String var1, String var2) throws SQLException;

   int deleteUserDevice(int var1) throws SQLException;

   int deleteUserDeviceByUserId(String var1) throws SQLException;

   int deleteExpiredUserDevice() throws SQLException;

   int updateUserDeviceExpiredDate(int var1) throws SQLException;

   int updateUserDeviceByUserId(String var1) throws SQLException;

   int deleteAllMFAData() throws SQLException;

   String unapprovedUser(String var1) throws SQLException;
}
