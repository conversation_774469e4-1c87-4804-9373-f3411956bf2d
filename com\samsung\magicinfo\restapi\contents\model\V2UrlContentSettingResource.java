package com.samsung.magicinfo.restapi.contents.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@JsonInclude(Include.NON_EMPTY)
@ApiModel(
   value = "V2UrlContentSettingResource",
   description = "This model be used to streaming, and URL type content setting."
)
public class V2UrlContentSettingResource {
   @ApiModelProperty(
      dataType = "string",
      example = "00000000-0000-0000-0000-000000000000",
      allowEmptyValue = true,
      value = "ID of the content"
   )
   private String contentId = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Media type of the content",
      example = "URL",
      allowableValues = "STRM,URL"
   )
   @Pattern(
      regexp = "STRM|URL|",
      message = "contentTypes : Not allowed value.(STRM, URL)"
   )
   private String type = "";
   @ApiModelProperty(
      dataType = "string",
      example = "0",
      value = "ID of the group to which the content belongs"
   )
   private String groupId = "";
   @ApiModelProperty(
      dataType = "String",
      value = "Login user ID",
      example = "admin"
   )
   @Size(
      max = 64,
      message = "[ContentFilter][userId] max size is 64."
   )
   private String userId = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Name of the URL content"
   )
   private String urlContentName = "";
   @ApiModelProperty(
      dataType = "string",
      value = "IP address of the URL content"
   )
   private String urlAddress = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Metadata for the content"
   )
   private String contentMetaData = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Refresh interval",
      example = "00:01:00"
   )
   private String refreshInterval = "";
   @ApiModelProperty(
      dataType = "string",
      example = "SPLAYER",
      value = "Device type of content corresponding to requested content ID",
      allowableValues = "iPLAYER, SPLAYER"
   )
   private String deviceType = "";
   @ApiModelProperty(
      dataType = "float",
      value = "Device type version of content corresponding to requested content ID"
   )
   private float deviceTypeVersion = 0.0F;

   public V2UrlContentSettingResource() {
      super();
   }

   public String getContentId() {
      return this.contentId;
   }

   public void setContentId(String contentId) {
      this.contentId = contentId;
   }

   public String getType() {
      return this.type;
   }

   public void setType(String type) {
      this.type = type;
   }

   public String getGroupId() {
      return this.groupId;
   }

   public void setGroupId(String groupId) {
      this.groupId = groupId;
   }

   public String getUserId() {
      return this.userId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }

   public String getUrlContentName() {
      return this.urlContentName;
   }

   public void setUrlContentName(String urlContentName) {
      this.urlContentName = urlContentName;
   }

   public String getUrlAddress() {
      return this.urlAddress;
   }

   public void setUrlAddress(String urlAddress) {
      this.urlAddress = urlAddress;
   }

   public String getContentMetaData() {
      return this.contentMetaData;
   }

   public void setContentMetaData(String contentMetaData) {
      this.contentMetaData = contentMetaData;
   }

   public String getRefreshInterval() {
      return this.refreshInterval;
   }

   public void setRefreshInterval(String refreshInterval) {
      this.refreshInterval = refreshInterval;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public void setDeviceType(String deviceType) {
      this.deviceType = deviceType;
   }

   public float getDeviceTypeVersion() {
      return this.deviceTypeVersion;
   }

   public void setDeviceTypeVersion(float deviceTypeVersion) {
      this.deviceTypeVersion = deviceTypeVersion;
   }
}
