package com.samsung.magicinfo.restapi.device.controller;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.rmrule.entity.DeviceRMRule;
import com.samsung.magicinfo.framework.device.rmrule.manager.DeviceRMRuleInfo;
import com.samsung.magicinfo.framework.device.rmrule.manager.DeviceRMRuleInfoImpl;
import com.samsung.magicinfo.framework.kpi.annotation.KPI;
import com.samsung.magicinfo.framework.kpi.annotation.LogProperty;
import com.samsung.magicinfo.mvc.handler.ApiVersion;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonStatusResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonUpdateResult;
import com.samsung.magicinfo.restapi.device.model.V2DeletedDevice;
import com.samsung.magicinfo.restapi.device.model.V2DeviceCabinetSendCmdWrapper;
import com.samsung.magicinfo.restapi.device.model.V2DeviceConversion;
import com.samsung.magicinfo.restapi.device.model.V2DeviceDeleteResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceDepolyFilter;
import com.samsung.magicinfo.restapi.device.model.V2DeviceExternalPowerResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceFilter;
import com.samsung.magicinfo.restapi.device.model.V2DeviceFlipConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGeneral;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLedCabinetResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLicenseConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLogProcess;
import com.samsung.magicinfo.restapi.device.model.V2DevicePreconfigData;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetCopyParam;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetDeleteResource;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetFilter;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetResource;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetResultResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceQuickControl;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSaveChannelConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceServerStatus;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSetSboxLayoutResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceStatusResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSystemUsageResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTagAssignment;
import com.samsung.magicinfo.restapi.device.model.V2DeviceVncConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceWarningRule;
import com.samsung.magicinfo.restapi.device.model.V2RemoteControlIds;
import com.samsung.magicinfo.restapi.device.model.V2ServerConfig;
import com.samsung.magicinfo.restapi.device.service.V2DeviceGeneralService;
import com.samsung.magicinfo.restapi.device.service.V2DeviceService;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.utils.RestAPIUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.rms.model.GeneralInfoResource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.Authorization;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

@Api(
   value = "Device Management System",
   description = "Operations pertaining to device in Device Management System",
   tags = {"Device API Group"}
)
@RestController
@Validated
@ApiVersion({2.0D})
@RequestMapping({"/restapi/v2.0/rms/devices"})
public class V2DeviceController {
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   @Autowired
   private V2DeviceService v2DeviceService;
   @Autowired
   private V2DeviceGeneralService v2DeviceGeneralService;

   public V2DeviceController() {
      super();
   }

   @ApiOperation(
      value = "Get device general info",
      notes = "Get device's general information.\r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;V2DeviceGeneralResource&#62; \r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceIds",
   value = "Value of device IDs.",
   required = true,
   dataType = "V2CommonIds"
)})
   @PostMapping(
      value = {"/general-info"},
      produces = {"application/json"}
   )
   public ResponseEntity getGeneralInfo(@Valid @RequestBody V2CommonIds deviceIds) throws Exception {
      this.logger.info("[REST_v2.0][DEVICE][getGeneralInfo] get device detail information by deviceid. : " + deviceIds.getIds().toString());
      V2CommonBulkResultResource result = this.v2DeviceGeneralService.getGeneralInfo(deviceIds);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result, 0, 0, deviceIds.getIds().size());
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      this.logger.info("[REST_v2.0][DEVICE][getGeneralInfo] done.");
      return new ResponseEntity(responseBody, status);
   }

   @KPI
   @LogProperty(
      eventType = "TEXT_TITLE_EDIT_GENERAL_CONF_P"
   )
   @ApiOperation(
      value = "Update general info : location, device model name",
      notes = "Update selected device's general(informationTab) information. \r\n'successList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were processed successfully.\r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "param",
   value = "Resource for current general inormation editing.",
   dataType = "V2DeviceGeneral"
)})
   @PutMapping(
      value = {"/current-general-info"},
      produces = {"application/json"}
   )
   public ResponseEntity updateCurrentGeneralInfo(@Valid @RequestBody V2DeviceGeneral param) throws Exception {
      this.logger.info("[REST_v2.0][DEVICE][updateCurrentGeneralInfo] update general info : location, device model name : " + param.getDeviceIds().toString());
      if (StringUtils.length(param.getLocation()) > 200) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_INPUT_LENGTH_LIMIT);
      } else {
         V2DeviceReqServiceResource result = this.v2DeviceGeneralService.updateGeneralInfo(param);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
         HttpStatus status = HttpStatus.OK;
         if (!result.getFailList().isEmpty()) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            responseBody.setStatus("Fail");
         }

         this.logger.info("[REST_v2.0][DEVICE][updateCurrentGeneralInfo] done.");
         return new ResponseEntity(responseBody, status);
      }
   }

   @ApiOperation(
      value = "Get updated general result after sending MO",
      notes = "Get updated general result after sending MO \r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;DeviceGeneralConfResource&#62; \r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "body",
   value = "request id for getting current peripherals info for specified device",
   dataType = "V2DeviceReqServiceConf"
)})
   @PostMapping(
      value = {"/current-general-info"},
      produces = {"application/json"}
   )
   public ResponseEntity getUpdatedGeneralInfoResult(@Valid @RequestBody V2DeviceReqServiceConf body) throws Exception {
      this.logger.info("[REST][DEVICE][getUpdatedGeneralInfoResult][" + body.getDeviceIds().toString() + "] get updated general result after sending MO");
      V2CommonBulkResultResource result = this.v2DeviceGeneralService.getCurrentGeneralWithRequestId(body);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      this.logger.info("[REST][DEVICE][getUpdatedGeneralInfoResult] done.");
      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Get a request ticket for getting current general info for specified device",
      notes = "Get a request ticket for getting current general info for specified device.\r\n",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceIds",
   value = "Value of device IDs.",
   required = true,
   dataType = "V2CommonIds"
)})
   @PostMapping(
      value = {"/current-general-info/request-ticket"},
      produces = {"application/json"}
   )
   public ResponseEntity reqGetCurrentGeneralInfo(@Valid @RequestBody V2CommonIds deviceIds) throws Exception {
      this.logger.info("[REST_v2.0][Device][reqGetCurrentGeneralInfo] Get a request ticket for getting current general info for specified device.");
      V2DeviceReqServiceResource result = this.v2DeviceGeneralService.reqGetGeneral(deviceIds);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get device connection status",
      notes = "Get connection status results for multiple devices.\r\n'successList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were processed successfully.\r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceIds",
   value = "Value of device IDs.",
   required = true,
   dataType = "V2CommonIds"
)})
   @PostMapping(
      value = {"/connections-checked"},
      produces = {"application/json"}
   )
   public ResponseEntity isConnected(@Valid @RequestBody V2CommonIds deviceIds) throws Exception {
      this.logger.info("[REST_v2.0][Device][isconnected][" + deviceIds.toString() + "] update general information.");
      V2CommonUpdateResult result = this.v2DeviceService.isConnected(deviceIds);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result, 0, 0, deviceIds.getIds().size());
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Get specific device information",
      notes = "Get basic information and detailed information related to various categories.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "String"
)})
   @GetMapping(
      value = {"/{deviceId}"},
      produces = {"application/json"}
   )
   public ResponseEntity getDeviceView(@PathVariable(value = "deviceId",required = true) @NotEmpty @NotNull String deviceId, HttpServletRequest reqeust) throws Exception {
      this.logger.info("[REST_v2.0][Device][getDeviceView] get device detail information by deviceid.");
      V2DeviceResource result = this.v2DeviceService.getDevice(deviceId, reqeust);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get schedule information of all devices",
      notes = "Get basic information Schedule.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "startIndex",
   value = "Index of start page",
   required = true,
   dataType = "int"
), @ApiImplicitParam(
   name = "pageSize",
   value = "Count of Device to be queried at once",
   required = true,
   dataType = "int"
), @ApiImplicitParam(
   name = "groupId",
   value = "Device GroupId",
   required = false,
   dataType = "String"
)})
   @GetMapping(
      value = {"/getDevicesScheduleInfo"},
      produces = {"application/json"}
   )
   public ResponseEntity getDevicesScheduleInfo(@RequestParam(value = "startIndex",required = true) @Min(value = 1L,message = "Minimum value of startIndex is 1") int startIndex, @RequestParam(value = "pageSize",required = true) @Min(value = 1L,message = "Minimum value of pageSize is 1") int pageSize, @RequestParam(value = "groupId",required = false) String groupId) throws Exception {
      this.logger.info("[REST_v2.0][Device][getDevicesScheduleInfo] get schedule Info of all devices");
      V2PageResource result = this.v2DeviceService.getDevicesScheduleInfo(startIndex, pageSize, groupId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result.getList(), result.getStartIndex(), result.getPageSize(), result.getRecordsTotal());
      this.logger.info("[REST][DEVICE][getDevicesScheduleInfo] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get tag list information for specified device",
      notes = "Get tag information associated with the specified device.\r\nSeparated by \"media\" and \"variable\" type values.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "String"
), @ApiImplicitParam(
   name = "type",
   value = "type",
   required = true,
   dataType = "String",
   allowableValues = "media,variable"
)})
   @GetMapping(
      value = {"/{deviceId}/tags"},
      produces = {"application/json"}
   )
   public ResponseEntity getTagList(@PathVariable(value = "deviceId",required = true) @NotEmpty @NotNull String deviceId, @RequestParam(value = "type",required = true) String type) throws Exception {
      this.logger.info("[REST_v2.0][Device][getTagList] get device detail information by deviceid.");
      List result = this.v2DeviceService.getTagList(deviceId, type);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result, 0, 0, result.size());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @KPI
   @ApiOperation(
      value = "Assign tags to multiple devices",
      notes = "Assign tags to multiple devices.\r\nThere are two types of tag: \"MEDIA\" and \"VARIABLE\".\r\nThe concept of assigning multiple tags of one type to multiple devices. \r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;V2DeviceTagResource&#62; \r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "tagInfo",
   value = "Tag information to assign to device.",
   required = true,
   dataType = "V2DeviceTagAssignment"
)})
   @PutMapping(
      value = {"/tags"},
      produces = {"application/json"}
   )
   public ResponseEntity assignTags(@Valid @RequestBody V2DeviceTagAssignment tagInfo, BindingResult result) throws Exception {
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2CommonBulkResultResource resources = this.v2DeviceService.assignTag(tagInfo);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resources);
         HttpStatus status = HttpStatus.OK;
         if (!resources.getFailList().isEmpty()) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            responseBody.setStatus("Fail");
         }

         return new ResponseEntity(responseBody, status);
      }
   }

   @ApiOperation(
      value = "Get list of playlists to expire",
      notes = "Get a list of playlists whose expiration date is upcoming.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "startIndex",
   value = "Index of start page",
   required = true,
   dataType = "int"
), @ApiImplicitParam(
   name = "pageSize",
   value = "Count of Playlist to be queried at once",
   required = true,
   dataType = "int"
), @ApiImplicitParam(
   name = "searchText",
   value = "Search for playlist name.",
   required = false,
   dataType = "string",
   defaultValue = ""
), @ApiImplicitParam(
   name = "sortColumn",
   value = "Sort by column name.",
   required = false,
   dataType = "string",
   defaultValue = "playlist_name",
   allowableValues = "playlist_name, last_modified_date, creator_id"
), @ApiImplicitParam(
   name = "sortOrder",
   value = "Sort by order.",
   required = false,
   dataType = "string",
   defaultValue = "asc"
)})
   @GetMapping(
      value = {"/playlists/upcoming-expiries"},
      produces = {"application/json"}
   )
   public ResponseEntity checkUpcomingExpiryDatePlaylist(@RequestParam(value = "startIndex",required = true) @Min(value = 1L,message = "Minimum value of startIndex is 1") int startIndex, @RequestParam(value = "pageSize",required = true) @Min(value = 1L,message = "Minimum value of pageSize is 1") int pageSize, @RequestParam(value = "searchText",required = false) String searchText, @RequestParam(value = "sortColumn",required = false) String sortColumn, @RequestParam(value = "sortOrder",required = false) String sortOrder) throws Exception {
      this.logger.info("[REST_v2.0][Device][checkUpcomingExpiryDatePlaylist] get device detail information by deviceid. ");
      V2PageResource list = this.v2DeviceService.checkUpcomingExpiryDateOnPlaylist(startIndex, pageSize, searchText, sortColumn, sortOrder);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(list.getList(), list.getStartIndex(), list.getPageSize(), list.getRecordsTotal());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get list of schedules to expire",
      notes = "Get a list of schedules whose expiration date is upcoming.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "startIndex",
   value = "Index of start page",
   required = true,
   dataType = "int"
), @ApiImplicitParam(
   name = "pageSize",
   value = "Count of Schedule to be queried at once",
   required = true,
   dataType = "int"
), @ApiImplicitParam(
   name = "searchText",
   value = "Search for Schedule name.",
   required = false,
   dataType = "string",
   defaultValue = ""
), @ApiImplicitParam(
   name = "sortColumn",
   value = "Sort by column name.",
   required = false,
   dataType = "string",
   defaultValue = "stop_date",
   allowableValues = "program_name, stop_date, modify_date"
), @ApiImplicitParam(
   name = "sortOrder",
   value = "Sort by order.",
   required = false,
   dataType = "string",
   defaultValue = "desc"
)})
   @GetMapping(
      value = {"/schedules/upcoming-expiries"},
      produces = {"application/json"}
   )
   public ResponseEntity checkUpcomingExpiryDate(@RequestParam(value = "startIndex",required = true) @Min(value = 1L,message = "Minimum value of startIndex is 1") int startIndex, @RequestParam(value = "pageSize",required = true) @Min(value = 1L,message = "Minimum value of pageSize is 1") int pageSize, @RequestParam(value = "searchText",required = false) String searchText, @RequestParam(value = "sortColumn",required = false) String sortColumn, @RequestParam(value = "sortOrder",required = false) String sortOrder) throws Exception {
      this.logger.info("[REST_v2.0][Device][checkUpcomingExpiryDate] get device detail information by deviceid. ");
      V2PageResource list = this.v2DeviceService.checkUpcomingExpiryDateOnSchedule(startIndex, pageSize, searchText, sortColumn, sortOrder);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(list.getList(), list.getStartIndex(), list.getPageSize(), list.getRecordsTotal());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get device tab list of expired schedules in device notifications",
      notes = "Get device tab list of expired schedules in device notifications.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "startIndex",
   value = "Index of start page",
   required = true,
   dataType = "int"
), @ApiImplicitParam(
   name = "pageSize",
   value = "Count of device to be queried at once",
   required = true,
   dataType = "int"
), @ApiImplicitParam(
   name = "searchText",
   value = "Search for device name.",
   required = false,
   dataType = "string",
   defaultValue = ""
), @ApiImplicitParam(
   name = "sortColumn",
   value = "Sort by column name.",
   required = false,
   dataType = "string",
   defaultValue = "stop_date",
   allowableValues = "device_name, device_group_name, program_name, stop_date"
), @ApiImplicitParam(
   name = "sortOrder",
   value = "Sort by order.",
   required = false,
   dataType = "string",
   defaultValue = "desc"
)})
   @GetMapping(
      value = {"/upcoming-expiries"},
      produces = {"application/json"}
   )
   public ResponseEntity checkUpcomingExpiryDateDevice(@RequestParam(value = "startIndex",required = true) @Min(value = 1L,message = "Minimum value of startIndex is 1") int startIndex, @RequestParam(value = "pageSize",required = true) @Min(value = 1L,message = "Minimum value of pageSize is 1") int pageSize, @RequestParam(value = "searchText",required = false) String searchText, @RequestParam(value = "sortColumn",required = false) String sortColumn, @RequestParam(value = "sortOrder",required = false) String sortOrder) throws Exception {
      this.logger.info("[REST_v2.0][Device][checkUpcomingExpiryDateDevice] get device detail information by deviceid. ");
      V2PageResource list = this.v2DeviceService.checkUpcomingExpiryDateDevice(startIndex, pageSize, searchText, sortColumn, sortOrder);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(list.getList(), list.getStartIndex(), list.getPageSize(), list.getRecordsTotal());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Search for device list based on filters",
      notes = "This API is based on GET /restapi/v2.0/rms/devices.\r\nIt is designed so that you can search by using one or several filter-items. Clearing all values returns all lists.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "filter",
   value = "Search filter for device list condition. ",
   dataType = "V2DeviceFilter"
)})
   @PostMapping(
      value = {"/filter"},
      produces = {"application/json"}
   )
   public ResponseEntity filterDeviceList(@Valid @RequestBody V2DeviceFilter filter, HttpServletRequest request, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][filterDeviceList] get all device list with filter.");
      V2PageResource result;
      if ("unapproval".equals(StrUtils.nvl(filter.getGroupType()))) {
         result = this.v2DeviceService.getUnapprovedDeviceList(filter);
      } else {
         result = this.v2DeviceService.getAllDeviceList(filter, request, response);
      }

      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result.getList(), result.getStartIndex(), result.getPageSize(), result.getRecordsTotal());
      this.logger.info("[REST][DEVICE][filterDeviceList] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @KPI
   @ApiOperation(
      value = "Edit Quick-Control Values for Specific Devices",
      notes = "Quick controller for specified device. You can change some values of device using this API. \r\n'successList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were processed successfully. \r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceQuickControl",
   value = "Resource for quick-control function",
   required = true,
   dataType = "V2DeviceQuickControl"
)})
   @PutMapping(
      value = {"/quick-control"},
      produces = {"application/json"}
   )
   public ResponseEntity quickControl(@RequestBody V2DeviceQuickControl deviceQuickControl) throws Exception {
      this.logger.info("[REST_v2.0][Device][quickControl] get device detail information by deviceid. ");
      V2DeviceReqServiceResource result = this.v2DeviceService.quickControl(deviceQuickControl.getDeviceIds(), deviceQuickControl.getMenu(), deviceQuickControl.getValue(), deviceQuickControl.getProductType());
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get schedule download status information",
      notes = "Get content-schedule or event-schedule download status information for a specific device. You can specify the CONTENT or EVENT type in scheduleType.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "string"
), @ApiImplicitParam(
   name = "scheduleType",
   value = "Type of schedule such as content or event",
   required = true,
   dataType = "string",
   allowableValues = "content, event"
)})
   @GetMapping(
      value = {"/{deviceId}/contents/download-status"},
      produces = {"application/json"}
   )
   public ResponseEntity getContentsDownloadStatus(@PathVariable("deviceId") @NotEmpty @NotNull String deviceId, @RequestParam("scheduleType") String scheduleType) throws Exception {
      this.logger.info("[REST_v2.0][Device][contentDownload] get device detail information by deviceid. ");
      ResponseBody responseBody = this.v2DeviceService.getContentsDownloadStatus(deviceId, scheduleType);
      responseBody.setApiVersion("2.0");
      responseBody.setStatus("Success");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @KPI
   @LogProperty(
      eventType = "TEXT_TITLE_DELETE_DEVICE_P"
   )
   @ApiOperation(
      value = "Delete specified devices",
      notes = "Delete multiple specified devices. The API checks whether it can be deleted and returns the result.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceIds",
   value = "Value of device IDs.",
   required = true,
   dataType = "List"
)})
   @PostMapping(
      value = {"/deleted-devices"},
      produces = {"application/json"}
   )
   public ResponseEntity deviceDelete(@Valid @RequestBody List deviceIds, HttpServletRequest request, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][deviceDelete] delete requested devices.");
      V2DeviceDeleteResource result = this.v2DeviceService.deleteDevice(deviceIds, request, response);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result, 0, 0, deviceIds.size());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get list of device content errors",
      notes = "Get list of device content errors",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "startIndex",
   value = "Index of start page. ",
   required = true,
   dataType = "int",
   allowableValues = "range[0, infinity]"
), @ApiImplicitParam(
   name = "pageSize",
   value = "Count of contents to be queried at once.",
   required = true,
   dataType = "int",
   allowableValues = "range[1, infinity]"
), @ApiImplicitParam(
   name = "searchText",
   value = "Search for device name, mac, ip, model, firmware.",
   required = false,
   dataType = "string",
   defaultValue = ""
), @ApiImplicitParam(
   name = "sortColumn",
   value = "Sort by column name.",
   required = false,
   dataType = "string",
   defaultValue = "device_name",
   allowableValues = "device_name, device_id, group_name, device_model_name, last_connection_time"
), @ApiImplicitParam(
   name = "sortOrder",
   value = "Sort by order.",
   required = false,
   dataType = "string",
   defaultValue = "asc"
)})
   @GetMapping(
      value = {"/content-check"},
      produces = {"application/json"}
   )
   public ResponseEntity checkContent(@RequestParam(value = "startIndex",required = true) @Min(value = 1L,message = "Minimum value of startIndex is 1") int startIndex, @RequestParam(value = "pageSize",required = true) @Min(value = 1L,message = "Minimum value of pageSize is 1") int pageSize, @RequestParam(value = "searchText",required = false) String searchText, @RequestParam(value = "sortColumn",required = false) String sortColumn, @RequestParam(value = "sortOrder",required = false) String sortOrder, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][checkContent] get Device check contents. ");
      V2PageResource list = this.v2DeviceService.checkContent(startIndex, pageSize, searchText, sortColumn, sortOrder, response);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(list.getList(), list.getStartIndex(), list.getPageSize(), list.getRecordsTotal());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get a list of undeployed schedules",
      notes = "Get a list of devices that have undeployed schedules.\r\nYou can also use search features such as device name, mac, and IP. ",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "startIndex",
   value = "Index of start page. ",
   required = true,
   dataType = "int",
   allowableValues = "range[0, infinity]"
), @ApiImplicitParam(
   name = "pageSize",
   value = "Count of contents to be queried at once.",
   required = true,
   dataType = "int",
   allowableValues = "range[1, infinity]"
), @ApiImplicitParam(
   name = "searchText",
   value = "Search for device name, mac, ip, model, firmware.",
   required = false,
   dataType = "string",
   defaultValue = ""
), @ApiImplicitParam(
   name = "sortColumn",
   value = "Sort by column name.",
   required = false,
   dataType = "string",
   defaultValue = "device_name",
   allowableValues = "device_name, device_id, group_name, device_model_name, last_connection_time"
), @ApiImplicitParam(
   name = "sortOrder",
   value = "Sort by order.",
   required = false,
   dataType = "string",
   defaultValue = "asc"
)})
   @GetMapping(
      value = {"/schedule-check"},
      produces = {"application/json"}
   )
   public ResponseEntity checkSchedule(@RequestParam(value = "startIndex",required = true) @Min(value = 1L,message = "Minimum value of startIndex is 1") int startIndex, @RequestParam(value = "pageSize",required = true) @Min(value = 1L,message = "Minimum value of pageSize is 1") int pageSize, @RequestParam(value = "searchText",required = false) String searchText, @RequestParam(value = "sortColumn",required = false) String sortColumn, @RequestParam(value = "sortOrder",required = false) String sortOrder, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][checkSchedule] Check the schedule of the device.");
      V2PageResource list = this.v2DeviceService.checkSchedule(startIndex, pageSize, searchText, sortColumn, sortOrder, response);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(list.getList(), list.getStartIndex(), list.getPageSize(), list.getRecordsTotal());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Device check low storage space",
      notes = "Get a list of devices with low storage space.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "startIndex",
   value = "Index of start page. ",
   required = true,
   dataType = "int",
   allowableValues = "range[0, infinity]"
), @ApiImplicitParam(
   name = "pageSize",
   value = "Count of contents to be queried at once.",
   required = true,
   dataType = "int",
   allowableValues = "range[1, infinity]"
), @ApiImplicitParam(
   name = "searchText",
   value = "Search for device name, mac, ip, model, firmware.",
   required = false,
   dataType = "string",
   defaultValue = ""
), @ApiImplicitParam(
   name = "sortColumn",
   value = "Sort by column name.",
   required = false,
   dataType = "string",
   defaultValue = "device_name",
   allowableValues = "device_name, device_id, group_name, device_model_name, last_connection_time"
), @ApiImplicitParam(
   name = "sortOrder",
   value = "Sort by order.",
   required = false,
   dataType = "string",
   defaultValue = "asc"
)})
   @GetMapping(
      value = {"/storage-check"},
      produces = {"application/json"}
   )
   public ResponseEntity checkStorage(@RequestParam(value = "startIndex",required = true) @Min(value = 1L,message = "Minimum value of startIndex is 1") int startIndex, @RequestParam(value = "pageSize",required = true) @Min(value = 1L,message = "Minimum value of pageSize is 1") int pageSize, @RequestParam(value = "searchText",required = false) String searchText, @RequestParam(value = "sortColumn",required = false) String sortColumn, @RequestParam(value = "sortOrder",required = false) String sortOrder, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][checkStorage] get device detail information by deviceid. ");
      V2PageResource list = this.v2DeviceService.checkStorage(startIndex, pageSize, searchText, sortColumn, sortOrder, response);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(list.getList(), list.getStartIndex(), list.getPageSize(), list.getRecordsTotal());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get a list of Timezone Not Set devices",
      notes = "API to get device list of Timezone Not Set.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "startIndex",
   value = "Index of start page. ",
   required = true,
   dataType = "int",
   allowableValues = "range[0, infinity]"
), @ApiImplicitParam(
   name = "pageSize",
   value = "Count of contents to be queried at once.",
   required = true,
   dataType = "int",
   allowableValues = "range[1, infinity]"
), @ApiImplicitParam(
   name = "searchText",
   value = "Search for device name, mac, ip, model, firmware.",
   required = false,
   dataType = "string",
   defaultValue = ""
), @ApiImplicitParam(
   name = "sortColumn",
   value = "Sort by column name.",
   required = false,
   dataType = "string",
   defaultValue = "device_name",
   allowableValues = "device_name, device_id, group_name, device_model_name, last_connection_time"
), @ApiImplicitParam(
   name = "sortOrder",
   value = "Sort by order.",
   required = false,
   dataType = "string",
   defaultValue = "asc"
)})
   @GetMapping(
      value = {"/timezone-check"},
      produces = {"application/json"}
   )
   public ResponseEntity checkTimezone(@RequestParam(value = "startIndex",required = true) @Min(value = 1L,message = "Minimum value of startIndex is 1") int startIndex, @RequestParam(value = "pageSize",required = true) @Min(value = 1L,message = "Minimum value of pageSize is 1") int pageSize, @RequestParam(value = "searchText",required = false) String searchText, @RequestParam(value = "sortColumn",required = false) String sortColumn, @RequestParam(value = "sortOrder",required = false) String sortOrder, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][checkTimezone] get device detail information by deviceid. ");
      V2PageResource list = this.v2DeviceService.checkTimezone(startIndex, pageSize, searchText, sortColumn, sortOrder, response);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(list.getList(), list.getStartIndex(), list.getPageSize(), list.getRecordsTotal());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get a list of device preset",
      notes = "Get a list of preset information including the number of deploys.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "startIndex",
   value = "Index of start page.",
   required = true,
   dataType = "int",
   allowableValues = "range[0, infinity]"
), @ApiImplicitParam(
   name = "pageSize",
   value = "Count of contents to be queried at once.",
   required = true,
   dataType = "int",
   allowableValues = "range[1, infinity]"
)})
   @GetMapping(
      value = {"/preset"},
      produces = {"application/json"}
   )
   public ResponseEntity getPresetList(@RequestParam(value = "startIndex",required = true) @Min(value = 1L,message = "Minimum value of startIndex is 1") int startIndex, @RequestParam(value = "pageSize",required = true) @Min(value = 1L,message = "Minimum value of pageSize is 1") int pageSize) throws Exception {
      this.logger.info("[REST_v2.0][Device][getPresetList] get device detail information by deviceid.");
      V2DevicePresetFilter filter = new V2DevicePresetFilter();
      filter.setStartIndex(startIndex);
      filter.setPageSize(pageSize);
      V2PageResource list = this.v2DeviceService.getPresetList(filter);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(list.getList(), list.getStartIndex(), list.getPageSize(), list.getRecordsTotal());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Search for preset-list based on filters",
      notes = "This API is based on GET /restapi/v2.0/rms/devices/preset.\r\nIt is designed so that you can search by using one or several filter-items. Clearing all values returns all lists.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "filter",
   value = "Search filter for preset list.",
   dataType = "V2DevicePresetFilter"
)})
   @PostMapping(
      value = {"/preset/filter"},
      produces = {"application/json"}
   )
   public ResponseEntity getPresetFilterList(@Valid @RequestBody V2DevicePresetFilter filter) throws Exception {
      this.logger.info("[REST_v2.0][Device][getPresetList] get device detail information by deviceid.");
      V2PageResource list = this.v2DeviceService.getPresetList(filter);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(list.getList(), list.getStartIndex(), list.getPageSize(), list.getRecordsTotal());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get detailed information of the specified preset.",
      notes = "\r\nGet the value information set in the preset.\r\npreconfigId is the same as presetId.\r\nIf you want to know the preconfigId, you can find it in /restapi/v2.0/rms/devices/preset.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "presetId",
   value = "Id of specific preset",
   required = true,
   dataType = "String"
)})
   @GetMapping(
      value = {"/preset/{presetId}"},
      produces = {"application/json"}
   )
   public ResponseEntity preconfigitems(@PathVariable String presetId) throws Exception {
      this.logger.info("[REST_v2.0][Device][preconfigitems] get device detail information by deviceid. ");
      V2DevicePresetResource result = this.v2DeviceService.preconfigitems(presetId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Create and deploy a preset",
      notes = "API to create, save or deploy new presets.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "preconfig",
   value = "Resource for preset creatation.",
   dataType = "V2DevicePreconfigData"
)})
   @PostMapping(
      value = {"/preset"},
      produces = {"application/json"}
   )
   public ResponseEntity savePreconfig(@Valid @RequestBody V2DevicePreconfigData preconfig) throws Exception {
      this.logger.info("[REST_v2.0][Device][savePreconfig] save device preset.");
      V2DevicePresetResource result = this.v2DeviceService.savePreconfig(preconfig);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Edit detail value of specified preset",
      notes = "Edit and distribute the preset values.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "presetId",
   value = "Id of specific preset",
   required = true,
   dataType = "String"
), @ApiImplicitParam(
   name = "preconfig",
   value = "Resource for preset information editing.",
   dataType = "V2DevicePreconfigData"
)})
   @PutMapping(
      value = {"/preset/{presetId}"},
      produces = {"application/json"}
   )
   public ResponseEntity editPreconfig(@Valid @RequestBody V2DevicePreconfigData preconfig, @PathVariable String presetId) throws Exception {
      this.logger.info("[REST_v2.0][Device][editPreconfig] edit device preset. ");
      V2DevicePresetResource result = this.v2DeviceService.editPreconfig(preconfig, presetId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Preset delete function",
      notes = "API for deleting an existing preset.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "presetIds",
   value = "Ids of specificed preset.",
   dataType = "V2CommonIds"
)})
   @PostMapping(
      value = {"/preset/deleted-presets"},
      produces = {"application/json"}
   )
   public ResponseEntity deletePreconfig(@Valid @RequestBody V2CommonIds presetIds) throws Exception {
      this.logger.info("[REST_v2.0][Device][deletePreconfig] delete device preset.");
      V2DevicePresetDeleteResource result = this.v2DeviceService.deletePreconfig(presetIds);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      HttpStatus status = HttpStatus.OK;
      if (null != result.getFailList() && !result.getFailList().isEmpty()) {
         status = HttpStatus.BAD_REQUEST;
         responseBody.setStatus("Fail");
      }

      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Copy specified preset",
      notes = "Copy the properties of the specified preset and save it with a new preset name.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "presetId",
   value = "Id of specific preset",
   required = true,
   dataType = "String"
), @ApiImplicitParam(
   name = "param",
   value = "Resource for preset information copy.",
   dataType = "V2DevicePresetCopyParam"
)})
   @PostMapping(
      value = {"/preset/{presetId}/copy"},
      produces = {"application/json"}
   )
   public ResponseEntity copyPreconfig(@PathVariable String presetId, @Valid @RequestBody V2DevicePresetCopyParam param) throws Exception {
      this.logger.info("[REST_v2.0][Device][preconfigitems] get device detail information by deviceid. ");
      V2DevicePresetResource result = this.v2DeviceService.copyPreconfig(presetId, param);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Deploy RM-rule of devices",
      notes = "Deploy version-specific RM rules to assigned devices.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "version",
   value = "Rule Version",
   required = true,
   dataType = "String"
), @ApiImplicitParam(
   name = "filter",
   value = "Resources for deploying device rmserver rules",
   dataType = "V2DeviceDepolyFilter"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400002, The remote server information is invalid value.\n errorCode : 400002, content type of file data of filter is invalid value.\n errorCode : 400020, The file data of filter value cannot be null or empty.\n errorCode : 400600, More than 100 device IDs have been entered."
), @ApiResponse(
   code = 404,
   message = "Data not found \n errorCode : 404001, The deviceId is not found. \n errorCode : 404001, The list information of the deviceId is not found."
), @ApiResponse(
   code = 500,
   message = "Internal Server Error \n errorCode : 500009, Failed to download rm rule file(s). \n errorCode : 500611, It is failed to create a home directory of the RM Rule file."
)})
   @PostMapping(
      value = {"/rmrule/{version}/deploy"},
      produces = {"application/json"},
      consumes = {"multipart/form-data"}
   )
   public ResponseEntity deployRmRule(@PathVariable String version, @Valid @RequestBody V2DeviceDepolyFilter filter) throws Exception {
      String deviceIds = filter.getDeviceIds();
      if (null != deviceIds && !"".equals(deviceIds)) {
         String[] deviceIdList = deviceIds.split(",");
         if (null != deviceIdList && deviceIdList.length != 0) {
            Integer targetLimit = 100;
            if (deviceIdList.length > targetLimit) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_TARGET_LIMIT_EXCEED, new String[]{targetLimit.toString()});
            } else {
               for(int i = 0; i < deviceIdList.length; ++i) {
                  deviceIdList[i] = deviceIdList[i].trim();
               }

               this.logger.info("device_id : " + deviceIdList.length + ", version : " + version);
               String filename = this.v2DeviceService.saveRmRuleFile(filter.getFile());
               this.logger.info("file is saved as " + filename);
               DeviceRMRule rmRule = new DeviceRMRule();
               String description = filter.getDescription();
               rmRule.setFile_path(File.separator + "rm_rule" + File.separator + filename);
               rmRule.setVersion(version);
               rmRule.setDescription(description);
               DeviceRMRuleInfo rmRuleInfo = DeviceRMRuleInfoImpl.getInstance();
               long result = rmRuleInfo.addRMRule(rmRule);
               if (result < 0L) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"The remote server information"});
               } else {
                  rmRule.setRmrule_id(result);
                  rmRuleInfo.addRMRuleDevice(rmRule, deviceIdList);
                  DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
                  String[] var13 = deviceIdList;
                  int var14 = deviceIdList.length;

                  for(int var15 = 0; var15 < var14; ++var15) {
                     String deviceId = var13[var15];
                     if (DeviceUtils.isConnected(deviceId)) {
                        try {
                           Device device = deviceInfo.getDevice(deviceId);
                           rmRuleInfo.deployToDevice(device, rmRule);
                        } catch (Exception var18) {
                           this.logger.error("Failed to send rmrule. ", var18);
                        }
                     }
                  }

                  ResponseBody responseBody = new ResponseBody();
                  responseBody.setApiVersion("2.0");
                  responseBody.setStatus("Success");
                  return new ResponseEntity(responseBody, HttpStatus.OK);
               }
            }
         } else {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"list information of the deviceId"});
         }
      } else {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"deviceId"});
      }
   }

   @ApiOperation(
      value = "Get all device list",
      notes = "Get all device information.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "startIndex",
   value = "Index of start page.",
   required = true,
   dataType = "int"
), @ApiImplicitParam(
   name = "pageSize",
   value = "Count of device to be queried at once.",
   required = true,
   dataType = "int"
)})
   @GetMapping(
      value = {""},
      produces = {"application/json"}
   )
   public ResponseEntity listAllDevice(@RequestParam(value = "startIndex",required = true) @Min(value = 1L,message = "Minimum value of startIndex is 1") int startIndex, @RequestParam(value = "pageSize",required = true) @Min(value = 1L,message = "Minimum value of pageSize is 1") int pageSize, HttpServletRequest request, HttpServletResponse response) throws Exception {
      this.logger.info("[REST][DEVICE][listAllDevice] get all device list");
      V2DeviceFilter params = new V2DeviceFilter();
      params.setStartIndex(startIndex);
      params.setPageSize(pageSize);
      params.setGroupMode("GROUP");
      V2PageResource result = this.v2DeviceService.getAllDeviceList(params, request, response);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result.getList(), result.getStartIndex(), result.getPageSize(), result.getRecordsTotal());
      this.logger.info("[REST][DEVICE][listAllDevice] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get led-cabinets information belonging to the specified device",
      notes = "Get basic information including power status, resolution, etc. of led-cabinets belonging to the specified device.\r\nly devices with led-cabinets will respond properly.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device ",
   required = true,
   dataType = "String",
   example = "24-f5-aa-df-8c-de"
), @ApiImplicitParam(
   name = "groupId",
   value = "Id of specific cabinet group ",
   required = false,
   dataType = "Long",
   example = "1"
)})
   @GetMapping(
      value = {"/{deviceId}/led-cabinets"},
      produces = {"application/json"}
   )
   public ResponseEntity getDeviceLedCabinets(@PathVariable("deviceId") String deviceId, @RequestParam(value = "groupId",required = false) @Min(value = 1L,message = "Minimum value of groupId is 1") Long groupId) throws Exception {
      V2PageResource list = this.v2DeviceService.getDeviceLedCabinets(deviceId, groupId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(list.getList(), list.getStartIndex(), list.getPageSize(), list.getRecordsTotal());
      responseBody.setIsOutDoorSBox(list.getIsOutDoorSBox());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Request for issuing session ID according to cabinet connection status of device",
      notes = "To check the cabinet connection status of the device, create a requestId and register it with the service. \r\n'successList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were processed successfully. \r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "string"
), @ApiImplicitParam(
   name = "cabinetIds",
   value = "Ids of specific cabinet. For information on \"Cabinet Id\", refter to [GET] /restapi/v2.0/rms/devices/{deviceId}/led-cabinets",
   required = true,
   dataType = "V2CommonIds"
)})
   @PostMapping(
      value = {"/{deviceId}/current-led-cabinets/request-ticket"},
      produces = {"application/json"}
   )
   public ResponseEntity reqDeviceLedCabinetsMdc(@PathVariable("deviceId") String deviceId, @Valid @RequestBody V2CommonIds cabinetIds) throws Exception {
      this.logger.info("[REST_v2.0][Device][reqDeviceCabinetMdc] request for issuing session ID according to cabinet connection status of device.");
      V2DeviceReqServiceResource result = this.v2DeviceService.reqCabinetMdc(deviceId, cabinetIds);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Function to check the connection status of the device using the issued requestId",
      notes = "The ability to check the connection status of a device using the issued requestId. \r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;V2DeviceLedCabinetResource&#62; \r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "string"
), @ApiImplicitParam(
   name = "requestId",
   value = "Request ID issued after registering with the service",
   required = true,
   dataType = "string"
)})
   @GetMapping(
      value = {"/{deviceId}/current-led-cabinets/{requestId}"},
      produces = {"application/json"}
   )
   public ResponseEntity resDeviceLedCabinetsDetails(@PathVariable("deviceId") String deviceId, @PathVariable("requestId") String requestId) throws Exception {
      this.logger.info("[REST_v2.0][Device][reqDeviceCabinetInfo] function to check the connection status of the device using the issued requestId.");
      V2CommonBulkResultResource result = this.v2DeviceService.getCabinetInfo(deviceId, requestId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         responseBody.setStatus("Fail");
      }

      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Information retrieved when modifying the layout of the device cabinet",
      notes = "Login user information and device group IDs obtained when modifying the layout of the device cabinet.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "string"
)})
   @GetMapping(
      value = {"/{deviceId}/led-cabinets/layout"},
      produces = {"application/json"}
   )
   public ResponseEntity deviceSetSboxLayout(@PathVariable("deviceId") String deviceId) throws Exception {
      this.logger.info("[REST_v2.0][Device][reqDeviceCabinetInfo] function to check the connection status of the device using the issued requestId.");
      V2DeviceSetSboxLayoutResource result = this.v2DeviceService.setSboxLayout(deviceId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get detailed information about led-cabinets",
      notes = "Get detailed information about led-cabinets of a specified device by cabinetIds & deviceId",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device ",
   required = true,
   dataType = "String",
   example = "24-f5-aa-df-8c-de"
), @ApiImplicitParam(
   name = "cabinetIds",
   value = "caninet id(CABINET_GROUPID + \"-\" + CABINET_ID)'s array.",
   required = false,
   dataType = "String",
   example = "1-2,2-2"
)})
   @GetMapping(
      value = {"/{deviceId}/led-cabinets/details"},
      produces = {"application/json"}
   )
   public ResponseEntity getDeviceLedCabinetsDetails(@PathVariable("deviceId") String deviceId, @RequestParam(value = "cabinetIds",required = false,defaultValue = "") List cabinetIds, @RequestParam(value = "blockId",required = false) Long blockId, HttpServletRequest request, HttpServletResponse response) throws Exception {
      List list = this.v2DeviceService.getDeviceLedCabinetsDetails(deviceId, cabinetIds, blockId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(list, 1, 0, list.size());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Edit led-cabinets control information for specified device",
      notes = "Edit led-cabinets control information for specified device",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "resource",
   value = "Resource for led-cabninets device editing.",
   required = true,
   dataType = "V2DeviceLedCabinetResource"
), @ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device ",
   required = true,
   dataType = "String",
   example = "24-f5-aa-df-8c-de"
), @ApiImplicitParam(
   name = "cabinetIds",
   value = "caninet id(CABINET_GROUPID + \"-\" + CABINET_ID)'s array.",
   required = false,
   dataType = "String",
   example = "1-2,2-2"
)})
   @PutMapping(
      value = {"/{deviceId}/led-cabinets"},
      produces = {"application/json"}
   )
   public ResponseEntity updateDeviceCabinetInfo(@PathVariable("deviceId") String deviceId, @RequestParam(value = "cabinetIds",required = false,defaultValue = "") List cabinetIds, @Valid @RequestBody V2DeviceLedCabinetResource resource, HttpServletRequest request, HttpServletResponse response) throws Exception {
      List list = this.v2DeviceService.updateDeviceCaninetInfo(deviceId, cabinetIds, resource, request.getSession().getId());
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(list, 1, 0, list.size());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Scan led-cabninets of specified device",
      notes = "Scan led-cabninets of specified device",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device ",
   required = true,
   dataType = "String",
   example = "24-f5-aa-df-8c-de"
), @ApiImplicitParam(
   name = "scanInfos",
   value = "Resource for cabinet-scan information. List use it as an array.",
   required = true,
   dataType = "V2DeviceCabinetScanInfo"
)})
   @PostMapping(
      value = {"/{deviceId}/led-cabinets/scan"},
      produces = {"application/json"}
   )
   public ResponseEntity scanDeviceCabinets(@PathVariable("deviceId") String deviceId, @NotNull @Valid @RequestBody List scanInfos) throws Exception {
      this.v2DeviceService.scanDeviceCabinets(deviceId, scanInfos);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(new ArrayList(), 0, 0, 0);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Cabinet ID SHOW and Automatic Setup of Devices",
      notes = "Cabinet ID SHOW and Automatic Setup of Devices",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device ",
   required = true,
   dataType = "String",
   example = "24-f5-aa-df-8c-de"
), @ApiImplicitParam(
   name = "commonWrapper",
   value = "Device cabinet information resource",
   required = true,
   dataType = "V2DeviceCabinetSendCmdWrapper"
)})
   @PostMapping(
      value = {"/{deviceId}/led-cabinets/commands"},
      produces = {"application/json"}
   )
   public ResponseEntity sendCommandOnCabinets(@PathVariable("deviceId") String deviceId, @NotNull @Valid @RequestBody V2DeviceCabinetSendCmdWrapper commonWrapper) throws Exception {
      this.v2DeviceService.sendCommandOnCabinets(deviceId, commonWrapper);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(new ArrayList(), 0, 0, 0);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get external power information belonging to the specified device",
      notes = "Get status information of external power belonging to the specified device.\r\n",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device ",
   required = true,
   dataType = "String",
   example = "24-f5-aa-df-8c-de"
)})
   @GetMapping(
      value = {"/{deviceId}/external-power"},
      produces = {"application/json"}
   )
   public ResponseEntity getDeviceExternalPower(@PathVariable("deviceId") String deviceId) throws Exception {
      V2DeviceExternalPowerResource v2DeviceExternalPowerResource = this.v2DeviceService.getDeviceExternalPower(deviceId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(v2DeviceExternalPowerResource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Request for issuing session ID according to external power status of device",
      notes = "To check the external power status of the device, create a requestId and register it with the service. \r\n'successList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were processed successfully. \r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "string"
)})
   @PostMapping(
      value = {"/{deviceId}/current-external-power/request-ticket"},
      produces = {"application/json"}
   )
   public ResponseEntity reqDeviceExternalPowerMdc(@PathVariable("deviceId") String deviceId) throws Exception {
      this.logger.info("[REST_v2.0][Device][reqDeviceExternalPowerMdc] request for issuing session ID according to external power status of device.");
      V2DeviceReqServiceResource result = this.v2DeviceService.reqExternalPowerMdc(deviceId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Function to check the connection status of the device using the issued requestId",
      notes = "The ability to check the connection status of a device using the issued requestId. \r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;V2DeviceLedCabinetResource&#62; \r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "string"
), @ApiImplicitParam(
   name = "requestId",
   value = "Request ID issued after registering with the service",
   required = true,
   dataType = "string"
)})
   @GetMapping(
      value = {"/{deviceId}/current-external-power/{requestId}"},
      produces = {"application/json"}
   )
   public ResponseEntity resDeviceExternalPowerDetails(@PathVariable("deviceId") String deviceId, @PathVariable("requestId") String requestId) throws Exception {
      this.logger.info("[REST_v2.0][Device][resDeviceExternalPowerDetails] function to check the connection status of the device using the issued requestId.");
      V2CommonBulkResultResource result = this.v2DeviceService.getExternalPowerInfo(deviceId, requestId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         responseBody.setStatus("Fail");
      }

      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Edit led-cabinets control information for specified device",
      notes = "Edit led-cabinets control information for specified device",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "resource",
   value = "Resource for led-cabninets device editing.",
   required = true,
   dataType = "V2DeviceLedCabinetResource"
), @ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device ",
   required = true,
   dataType = "String",
   example = "24-f5-aa-df-8c-de"
), @ApiImplicitParam(
   name = "cabinetIds",
   value = "caninet id(CABINET_GROUPID + \"-\" + CABINET_ID)'s array.",
   required = false,
   dataType = "String",
   example = "1-2,2-2"
)})
   @PutMapping(
      value = {"/{deviceId}/external-power"},
      produces = {"application/json"}
   )
   public ResponseEntity updateDeviceExternalPowerInfo(@PathVariable("deviceId") String deviceId, @Valid @RequestBody V2DeviceLedCabinetResource resource, HttpServletRequest request, HttpServletResponse response) throws Exception {
      List list = this.v2DeviceService.updateDeviceCaninetInfo(deviceId, (List)null, resource, request.getSession().getId());
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(list, 1, 0, list.size());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Scan external-power of specified device",
      notes = "Scan external-power of specified device",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device ",
   required = true,
   dataType = "String",
   example = "24-f5-aa-df-8c-de"
)})
   @GetMapping(
      value = {"/{deviceId}/external-power/scan/{count}"},
      produces = {"application/json"}
   )
   public ResponseEntity scanDeviceExternalPower(@PathVariable("deviceId") String deviceId, @PathVariable("count") int count) throws Exception {
      this.v2DeviceService.sendCommandToSbox(deviceId, "SCAN_EXTERNAL_POWER", Integer.toString(count));
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(new ArrayList(), 0, 0, 0);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "External Power of Devices",
      notes = "External Power of Devices",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device ",
   required = true,
   dataType = "String",
   example = "24-f5-aa-df-8c-de"
), @ApiImplicitParam(
   name = "commonWrapper",
   value = "Device cabinet information resource",
   required = true,
   dataType = "V2DeviceCabinetSendCmdWrapper"
)})
   @PutMapping(
      value = {"/{deviceId}/external-power/commands"},
      produces = {"application/json"}
   )
   public ResponseEntity sendCommandOnExternalPower(@PathVariable("deviceId") String deviceId, @NotNull @Valid @RequestBody V2DeviceCabinetSendCmdWrapper commonWrapper) throws Exception {
      this.v2DeviceService.sendCommandOnCabinets(deviceId, commonWrapper);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(new ArrayList(), 0, 0, 0);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get a list of device types",
      notes = "Get a list of device types",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/device-types"},
      produces = {"application/json"}
   )
   public ResponseEntity getDeviceListByUserId() throws Exception {
      this.logger.info("[REST_v2.0][Device][getDeviceListByUserId] get list of device by user id.");
      V2DeviceStatusResource list = this.v2DeviceService.getDeviceListByUserId();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(list);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Export device notifications",
      notes = "Export various notification types of device list  to PDF or EXCEL. The available values of type are \"TIMEZONE\", \"STOREGE\", \"SCHEDULE\", \"CONTENT\".",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "exportType",
   value = "Export file type",
   required = true,
   dataType = "string",
   allowableValues = "EXCEL,PDF"
), @ApiImplicitParam(
   name = "type",
   value = "Category type in the notification of the device",
   required = true,
   dataType = "string",
   defaultValue = "CONTENT",
   allowableValues = "TIMEZONE,STOREGE,SCHEDULE,CONTENT"
), @ApiImplicitParam(
   name = "searchText",
   value = "Search for device name, mac, ip, model, firmware.",
   required = false,
   dataType = "string",
   defaultValue = ""
), @ApiImplicitParam(
   name = "sortColumn",
   value = "Sort by column name.",
   required = false,
   dataType = "string",
   defaultValue = "device_name"
), @ApiImplicitParam(
   name = "sortOrder",
   value = "Sort by order.",
   required = false,
   dataType = "string",
   defaultValue = "asc"
), @ApiImplicitParam(
   name = "locale",
   value = "Locale information",
   required = false,
   dataType = "string",
   allowableValues = "de,en,es,fr,it,ja,ko,pt,ru,sv,tr,ar,fa,pl,vi,zh_cn,zh_tw"
)})
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Download completed successfully.\nThe model below is not related to response.\n(When download is complete, 'Code: 200' is transmitted without a response model)"
)})
   @PostMapping(
      value = {"/notification/export"},
      produces = {"application/json"}
   )
   public ModelAndView noticeExport(@RequestParam(value = "exportType",required = true,defaultValue = "EXCEL") String exportType, @RequestParam(value = "type",required = true) String type, @RequestParam(value = "searchText",required = false) String searchText, @RequestParam(value = "sortColumn",required = false) String sortColumn, @RequestParam(value = "sortOrder",required = false) String sortOrder, @RequestParam(value = "locale",required = false) String locale, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][noticeExport] Get from notifications on the device Incomplete download of content, Unscheduled, time zone setting, Export to lack of capacity");
      ModelAndView result = this.v2DeviceService.noticeExport(exportType, type, searchText, sortColumn, sortOrder, response, locale);
      return result;
   }

   @ApiOperation(
      value = "Export device notifications",
      notes = "Export playlist expiration date list during device notification to PDF or EXCEL. \r\nThis API is developed separately with different types and different data structures. Return data based on GET restapi/v2.0/rms/devices/playlists/upcoming-expiries.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "exportType",
   value = "Export file type",
   required = true,
   dataType = "string",
   allowableValues = "EXCEL,PDF"
), @ApiImplicitParam(
   name = "searchText",
   value = "Search for device name, mac, ip, model, firmware.",
   required = false,
   dataType = "string",
   defaultValue = ""
), @ApiImplicitParam(
   name = "sortColumn",
   value = "Sort by column name.",
   required = false,
   dataType = "string",
   defaultValue = "playlist_name",
   allowableValues = "playlist_name, last_modified_date, creator_id"
), @ApiImplicitParam(
   name = "sortOrder",
   value = "Sort by order.",
   required = false,
   dataType = "string",
   defaultValue = "desc"
), @ApiImplicitParam(
   name = "locale",
   value = "Locale information",
   required = false,
   dataType = "string",
   allowableValues = "de,en,es,fr,it,ja,ko,pt,ru,sv,tr,ar,fa,pl,vi,zh_cn,zh_tw"
)})
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Download completed successfully.\nThe model below is not related to response.\n(When download is complete, 'Code: 200' is transmitted without a response model)"
)})
   @PostMapping(
      value = {"/playlists/upcoming-expiries/export"},
      produces = {"application/json"}
   )
   public ModelAndView expiryDatePlaylistExport(@RequestParam(value = "exportType",required = true,defaultValue = "EXCEL") String exportType, @RequestParam(value = "searchText",required = false) String searchText, @RequestParam(value = "sortColumn",required = false) String sortColumn, @RequestParam(value = "sortOrder",required = false) String sortOrder, @RequestParam(value = "locale",required = false) String locale, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][expiryDatePlaylistExport] Get export to playlists to expire on notifications");
      ModelAndView result = this.v2DeviceService.expiryDatePlaylistExport(exportType, searchText, sortColumn, sortOrder, response, locale);
      return result;
   }

   @ApiOperation(
      value = "Export device notifications",
      notes = "Export schedule expiration date list during device notification to PDF or EXCEL. \r\nThis API is developed separately with different types and different data structures. Return data based on GET restapi/v2.0/rms/devices/schedules/upcoming-expiries.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "exportType",
   value = "Export file type",
   required = true,
   dataType = "string",
   allowableValues = "EXCEL,PDF"
), @ApiImplicitParam(
   name = "searchText",
   value = "Search for device name",
   required = false,
   dataType = "string",
   defaultValue = ""
), @ApiImplicitParam(
   name = "sortColumn",
   value = "Sort by column name.",
   required = false,
   dataType = "string",
   defaultValue = "stop_date",
   allowableValues = "program_name, stop_date, modify_date"
), @ApiImplicitParam(
   name = "sortOrder",
   value = "Sort by order.",
   required = false,
   dataType = "string",
   defaultValue = "desc"
), @ApiImplicitParam(
   name = "locale",
   value = "Locale information",
   required = false,
   dataType = "string",
   allowableValues = "de,en,es,fr,it,ja,ko,pt,ru,sv,tr,ar,fa,pl,vi,zh_cn,zh_tw"
)})
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Download completed successfully.\nThe model below is not related to response.\n(When download is complete, 'Code: 200' is transmitted without a response model)"
)})
   @PostMapping(
      value = {"/schedules/upcoming-expiries/export"},
      produces = {"application/json"}
   )
   public ModelAndView expiryDateExport(@RequestParam(value = "exportType",required = true,defaultValue = "EXCEL") String exportType, @RequestParam(value = "searchText",required = false) String searchText, @RequestParam(value = "sortColumn",required = false) String sortColumn, @RequestParam(value = "sortOrder",required = false) String sortOrder, @RequestParam(value = "locale",required = false) String locale, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][expiryDateExport] Get export of schedules to expire from notifications");
      ModelAndView result = this.v2DeviceService.expiryDateExport(exportType, searchText, sortColumn, sortOrder, response, locale);
      return result;
   }

   @ApiOperation(
      value = "Get export device tab of schedule to expire from notification",
      notes = "Download a list of device tabs of the schedule that will expire from the notification as an Excel or PDF file.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "exportType",
   value = "Export file type",
   required = true,
   dataType = "string",
   allowableValues = "EXCEL,PDF"
), @ApiImplicitParam(
   name = "searchText",
   value = "Search for device name",
   required = false,
   dataType = "string",
   defaultValue = ""
), @ApiImplicitParam(
   name = "sortColumn",
   value = "Sort by column name.",
   required = false,
   dataType = "string",
   defaultValue = "stop_date",
   allowableValues = "device_name, device_group_name, program_name, stop_date"
), @ApiImplicitParam(
   name = "sortOrder",
   value = "Sort by order.",
   required = false,
   dataType = "string",
   defaultValue = "desc"
), @ApiImplicitParam(
   name = "locale",
   value = "Locale information",
   required = false,
   dataType = "string",
   allowableValues = "de,en,es,fr,it,ja,ko,pt,ru,sv,tr,ar,fa,pl,vi,zh_cn,zh_tw"
)})
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Download completed successfully.\nThe model below is not related to response.\n(When download is complete, 'Code: 200' is transmitted without a response model)"
)})
   @PostMapping(
      value = {"/upcoming-expiries/export"},
      produces = {"application/json"}
   )
   public ModelAndView expiryDateDeviceExport(@RequestParam(value = "exportType",required = true,defaultValue = "EXCEL") String exportType, @RequestParam(value = "searchText",required = false) String searchText, @RequestParam(value = "sortColumn",required = false) String sortColumn, @RequestParam(value = "sortOrder",required = false) String sortOrder, @RequestParam(value = "locale",required = false) String locale, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][expiryDateDeviceExport] Get export device tab of schedule to expire from notification");
      ModelAndView result = this.v2DeviceService.expiryDateDeviceExport(exportType, searchText, sortColumn, sortOrder, response, locale);
      return result;
   }

   @ApiOperation(
      value = "Export approved-devices list using filters",
      notes = "This is a function to download a list of all approved devices as an Excel or PDF file.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "filter",
   value = "Search filter for device list condition.",
   dataType = "V2DeviceFilter"
), @ApiImplicitParam(
   name = "exportType",
   value = "Export file type",
   required = true,
   dataType = "string",
   allowableValues = "EXCEL,PDF"
), @ApiImplicitParam(
   name = "locale",
   value = "Locale information",
   required = false,
   dataType = "string",
   allowableValues = "de,en,es,fr,it,ja,ko,pt,ru,sv,tr,ar,fa,pl,vi,zh_cn,zh_tw"
)})
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Download completed successfully.\nThe model below is not related to response.\n(When download is complete, 'Code: 200' is transmitted without a response model)"
)})
   @PostMapping(
      value = {"/filter/export"},
      produces = {"application/json"}
   )
   public ModelAndView deviceExport(@Valid @RequestBody V2DeviceFilter filter, @RequestParam(value = "exportType",required = true,defaultValue = "EXCEL") String exportType, @RequestParam(value = "category",required = false,defaultValue = "DEVICE_INFORMATION") String category, @RequestParam(value = "locale",required = false) String locale, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][deviceExport] Get export list of all approved devices");
      ModelAndView result = this.v2DeviceService.deviceExport(filter, exportType, category, response, locale);
      return result;
   }

   @ApiOperation(
      value = "Device process log",
      notes = "Device process log.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "String"
), @ApiImplicitParam(
   name = "body",
   value = "This feature is PUT .../rms/devices{deviceId}/log/process \r\nIt is an item to receive when modifying log data and requesting service, and some required parameter items must be entered and used.",
   required = true,
   dataType = "V2DeviceLogProcess"
)})
   @PutMapping(
      value = {"/{deviceId}/log/process"},
      produces = {"application/json"}
   )
   public ResponseEntity logProcess(@PathVariable(value = "deviceId",required = true) @NotEmpty @NotNull String deviceId, @Valid @RequestBody V2DeviceLogProcess body, HttpServletRequest request) throws Exception {
      this.logger.info("[REST_v2.0][logProcess] Device process log.");
      V2CommonStatusResource result = this.v2DeviceService.logProcess(deviceId, body, request);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Delete collected logs on the specified device",
      notes = "Delete all collected logs of the specified device.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "String",
   example = "24-f5-aa-df-8c-de"
), @ApiImplicitParam(
   name = "script",
   value = "script",
   required = true,
   allowableValues = "platform, wplayer, third_application",
   dataType = "String"
)})
   @DeleteMapping(
      value = {"/{deviceId}/log/clear-collected"},
      produces = {"application/json"}
   )
   public ResponseEntity clearCollected(@PathVariable(value = "deviceId",required = true) @NotEmpty @NotNull String deviceId, @RequestParam("script") String script) throws Exception {
      this.logger.info("[REST_v2.0][Device][clearCollected] Device log clear collected.");
      V2DeletedDevice result = this.v2DeviceService.clearCollected(deviceId, script);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Download the collected log data.",
      notes = "Download the collected log data.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "String",
   example = "fe-34-e3-35-99-3c"
), @ApiImplicitParam(
   name = "filepath",
   value = "File path",
   required = true,
   dataType = "string",
   example = "log_2020-02-13-19-48-12_fe-34-e3-35-99-3c.info"
)})
   @GetMapping(
      value = {"/{deviceId}/log/download"},
      produces = {"application/json"}
   )
   public ResponseEntity logDownload(@PathVariable(value = "deviceId",required = true) @NotEmpty @NotNull String deviceId, @RequestParam(value = "filepath",required = true) String filepath, @RequestParam(value = "script",required = false) String script, HttpServletRequest request, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][logDownload] Device log file download.");
      ResponseBody responseBody = new ResponseBody();
      this.v2DeviceService.logDownload(deviceId, script, filepath, request, response);
      responseBody.setApiVersion("2.0");
      responseBody.setStatus("Success");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get collected logs for specified device",
      notes = "Get collected logs for specified device",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "String",
   example = "24-f5-aa-df-8c-de"
)})
   @GetMapping(
      value = {"/{deviceId}/log/collected"},
      produces = {"application/json"}
   )
   public ResponseEntity getLogCollected(@PathVariable(value = "deviceId",required = true) @NotEmpty @NotNull String deviceId) throws Exception {
      this.logger.info("[REST_v2.0][Device][getLogCollected][" + deviceId + "] Get device log collected.");
      List result = this.v2DeviceService.getLogCollected(deviceId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Check for available conversions devices",
      notes = "Check whether the specified devices can be converted. Convertible device targets are S, W, Lite Type.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceIds",
   value = "Value of device IDs.",
   required = true,
   dataType = "V2CommonIds"
)})
   @PostMapping(
      value = {"/conversions/available-devices"},
      produces = {"application/json"}
   )
   public ResponseEntity getConvertibleDevices(@Valid @RequestBody V2CommonIds deviceIds) throws Exception {
      this.logger.info("[REST_v2.0][Device][getConvertibleDevice] get device detail information by deviceid. ");
      V2CommonUpdateResult result = this.v2DeviceService.getDeviceInfo(deviceIds);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Request the device conversion and get the request id for this action",
      notes = "Issue a request ID for the device conversion feature.\r\nID issued by this function can be used in POST method. \r\n'successList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were processed successfully. \r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "body",
   value = "Resource for conversions.",
   required = true,
   dataType = "V2DeviceConversion"
)})
   @PutMapping(
      value = {"/conversions"},
      produces = {"application/json"}
   )
   public ResponseEntity requestDeviceConversion(@Valid @RequestBody V2DeviceConversion body) throws Exception {
      this.logger.info("[REST_v2.0][Device][requestDeviceConversion] request the device conversion and get the request id.");
      V2DeviceReqServiceResource result = this.v2DeviceService.requestDeviceConversion(body);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         responseBody.setStatus("Fail");
      }

      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Convert device type",
      notes = "It is a function that requests the device connection and converts the device type if successful. The request ID can be issued at PUT method.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "body",
   value = "Resource for conversions.",
   required = true,
   dataType = "V2DeviceReqServiceConf"
)})
   @PostMapping(
      value = {"/conversions"},
      produces = {"application/json"}
   )
   public ResponseEntity getDeviceConversionResult(@Valid @RequestBody V2DeviceReqServiceConf body) throws Exception {
      this.logger.info("[REST_v2.0][Device][getDeviceConversionResult] convert device type");
      List result = this.v2DeviceService.getDeviceConversionResult(body);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Set license expiration date for devices",
      notes = "The ability to change the license expiration date of a device.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @PutMapping(
      value = {"/licenses/expiration-date"},
      produces = {"application/json"}
   )
   public ResponseEntity deviceLicenseExpirationDate(@Valid @RequestBody V2DeviceLicenseConf body) throws Exception {
      this.logger.info("[REST_v2.0][Device][deviceLicenseExpirationDate] set license expiration date for devices");
      V2CommonUpdateResult result = this.v2DeviceService.saveExpiration(body);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Request network mode to device",
      notes = "The ability to request network mode on a device.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @PostMapping(
      value = {"/network-mode"},
      produces = {"application/json"}
   )
   public ResponseEntity deviceNetworkMode(@Valid @RequestBody V2CommonIds deviceIds) throws Exception {
      this.logger.info("[REST_v2.0][Device][deviceNetworkMode] request network mode to device");
      V2CommonUpdateResult result = this.v2DeviceService.networkMode(deviceIds);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Download registried resource",
      notes = "Download registried resources at server.\r\nResource types are logo and general contents. (IMAGE,MOV)",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "filePath",
   value = "File path",
   required = true,
   dataType = "string"
)})
   @GetMapping(
      value = {"/customize/download"},
      produces = {"application/json"}
   )
   public ResponseEntity customizeDownload(@RequestParam(value = "filePath",required = true) String filePath, HttpServletRequest request, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][customizeDownload] this is a download function for customize registration and distribution of devices.");
      ResponseBody responseBody = new ResponseBody();
      this.v2DeviceService.customizeDownload(filePath, request, response);
      responseBody.setApiVersion("2.0");
      responseBody.setStatus("Success");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "System usage of the device.",
      notes = "This function is to check the system usage status of the device in real time.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "string"
)})
   @GetMapping(
      value = {"/{deviceId}/system-usage"},
      produces = {"application/json"}
   )
   public ResponseEntity systemUsage(@PathVariable("deviceId") String deviceId, HttpServletRequest request, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][systemUsage] system usage of the device.");
      V2DeviceSystemUsageResource result = this.v2DeviceService.deviceUsage(deviceId, request, response);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Vnc(Virtual Network Computing) remote control of device",
      notes = "Vnc(Virtual Network Computing) remote control of device",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "string"
), @ApiImplicitParam(
   name = "body",
   value = "Resource for vnc config.",
   required = true,
   dataType = "V2DeviceVncConf"
)})
   @PostMapping(
      value = {"/{deviceId}/vnc"},
      produces = {"application/json"}
   )
   public ResponseEntity deviceVNC(@PathVariable("deviceId") String deviceId, @Valid @RequestBody V2DeviceVncConf body, HttpServletRequest request, HttpServletResponse response) throws Exception {
      ResponseBody responseBody = new ResponseBody();
      this.logger.info("[REST_v2.0][Device][deviceVNC] vnc remote control of device");
      this.v2DeviceService.deviceVnc(deviceId, body, request, response);
      responseBody.setApiVersion("2.0");
      responseBody.setStatus("Success");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Export function of the led box cabinet of the device",
      notes = "It is a function to download the led box cabinet items of the device as an Excel file.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "string"
), @ApiImplicitParam(
   name = "locale",
   value = "Locale information. If there is no input value, it is set as the locale value of user information.",
   required = false,
   dataType = "string",
   allowableValues = "de,en,es,fr,it,ja,ko,pt,ru,sv,tr,ar,fa,pl,vi,zh_cn,zh_tw"
)})
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Download completed successfully.\nThe model below is not related to response.\n(When download is complete, 'Code: 200' is transmitted without a response model)"
)})
   @PostMapping(
      value = {"/{deviceId}/led-cabinets/export"},
      produces = {"application/json"}
   )
   public ModelAndView ledCabinetsExport(@PathVariable("deviceId") String deviceId, @RequestParam(value = "locale",required = false) String locale, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][ledCabinetsExport] export function of the led box cabinet of the device");
      ModelAndView result = this.v2DeviceService.ledCabinetsExport(deviceId, response, locale);
      return result;
   }

   @ApiOperation(
      value = "Warning roll of led type device",
      notes = "This function is used to query the alert roll list of the LED type device.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "string"
)})
   @GetMapping(
      value = {"/{deviceId}/led-cabinets/warning-rule"},
      produces = {"application/json"}
   )
   public ResponseEntity warningCabinets(@PathVariable("deviceId") String deviceId) throws Exception {
      this.logger.info("[REST_v2.0][Device][warningCabinets] warning roll of led type device.");
      V2DeviceWarningRule result = this.v2DeviceService.getWarningRuleInfo(deviceId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @KPI
   @ApiOperation(
      value = "get url for starting and stop rm server",
      notes = "get url for starting and stop rm server.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "string"
), @ApiImplicitParam(
   name = "command",
   value = "start or stop",
   required = true,
   dataType = "string",
   example = "start"
)})
   @GetMapping(
      value = {"/{deviceId}/rmserver/{command}"},
      produces = {"application/json"}
   )
   public ResponseEntity startRmServerVnc(@PathVariable("deviceId") String deviceId, @PathVariable("command") String command, HttpServletRequest request) throws Exception {
      this.logger.info("[REST_v2.0][DEVICE][getRmServerVnc][" + deviceId + "] get url for starting rm server");
      HttpStatus status = HttpStatus.OK;
      GeneralInfoResource resource = this.v2DeviceService.getRmServerVnc(deviceId, command, request);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      if (resource == null) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      this.logger.info("[REST][DEVICE][getRmServerVnc][" + deviceId + "] finish successfully.");
      return new ResponseEntity(responseBody, status);
   }

   @KPI
   @ApiOperation(
      value = "get url for starting and stop remote control server",
      notes = "get url for starting and stop remote control server.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "command",
   value = "start or stop",
   required = true,
   dataType = "string",
   example = "start"
), @ApiImplicitParam(
   name = "deviceIds",
   value = "Ids of specific device",
   required = true,
   dataType = "list"
)})
   @PostMapping(
      value = {"/rc/{command}"},
      produces = {"application/json"}
   )
   public ResponseEntity startRemoteControlServerVnc(@PathVariable("command") String command, @RequestBody V2RemoteControlIds remoteControlIds, HttpServletRequest request) throws Exception {
      HttpStatus status = HttpStatus.OK;
      V2CommonBulkResultResource resource = this.v2DeviceService.getRCServerVnc(remoteControlIds, command, request);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      if (resource == null) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      this.logger.info("[REST][DEVICE][getRCServerVnc] finish successfully.");
      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Save channel information of one or multiple devices",
      notes = "Save channel information of one or multiple devices \r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;V2DeviceGeneralResource&#62; \r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "body",
   value = "Resource for channel-saving.",
   required = true,
   dataType = "V2DeviceSaveChannelConf"
)})
   @PostMapping(
      value = {"/save-channel"},
      produces = {"application/json"}
   )
   public ResponseEntity saveChannel(@Valid @RequestBody V2DeviceSaveChannelConf body, HttpServletRequest request) throws Exception {
      this.logger.info("[REST_v2.0][DEVICE][saveChannel] Save channel information of one or multiple devices");
      this.logger.info("[REST_v2.0][DEVICE][saveChannel] " + body.getDeviceIds().toString());
      V2CommonBulkResultResource result = this.v2DeviceService.saveChannel(body, request);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result, 0, 0, body.getDeviceIds().size());
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      this.logger.info("[REST_v2.0][DEVICE][saveChannel] done. ");
      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Deploy schedule of devices",
      notes = "This is used when distributing schedules\r\nYou must add deviceId as a required value.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "string"
)})
   @PostMapping(
      value = {"/{deviceId}/schedule-deploy"},
      produces = {"application/json"}
   )
   public ResponseEntity deploySchedule(@PathVariable String deviceId) throws Exception {
      this.logger.info("[REST_v2.0][DEVICE][deploySchedule][" + deviceId + "] deploy schedule");
      V2CommonIds resource = this.v2DeviceService.deploySchedule(deviceId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      this.logger.info("[REST][DEVICE][deploySchedule][" + deviceId + "] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get information about one or multiple devices whose device type is Flip.",
      notes = "Get information about one or multiple devices whose device type is Flip. \r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;V2DeviceFlipResource&#62; \r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceIds",
   value = "Value of device IDs.",
   required = true,
   dataType = "V2CommonIds"
)})
   @PostMapping(
      value = {"/all-info"},
      produces = {"application/json"}
   )
   public ResponseEntity getFlipInfo(@Valid @RequestBody V2CommonIds deviceIds) throws Exception {
      this.logger.info("[REST_v2.0][DEVICE][getFlipInfo] get device flip to detail information by deviceid. : " + deviceIds.getIds().toString());
      V2CommonBulkResultResource result = this.v2DeviceService.getFlipInfo(deviceIds);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result, 0, 0, deviceIds.getIds().size());
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      this.logger.info("[REST_v2.0][DEVICE][getFlipInfo] done.");
      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Create device's flip device control Request ID.",
      notes = "Create device's flip device control Request ID. \r\n'successList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were processed successfully. \r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "body",
   value = "Resource for current general-info editing.",
   required = true,
   dataType = "V2DeviceFlipConf"
)})
   @PutMapping(
      value = {"/current-all-info"},
      produces = {"application/json"}
   )
   public ResponseEntity reqSetCommonToDevice(@Valid @RequestBody V2DeviceFlipConf body) throws Exception {
      this.logger.info("[REST_v2.0][DEVICE][reqSetCommonToDevice] create device's flip device control Request ID. deviceIds : " + body.getIds().toString());
      V2DeviceReqServiceResource result = this.v2DeviceService.reqSetCommonToDevice(body);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result, 0, 0, body.getIds().size());
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      this.logger.info("[REST_v2.0][DEVICE][reqSetCommonToDevice] done.");
      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Get status based on all current status information request responses from Flip devices.",
      notes = "Get status based on all current status information request responses from Flip devices. \r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were processed successfully. \r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceIds",
   value = "Value of device IDs.",
   required = true,
   dataType = "V2CommonIds"
)})
   @PostMapping(
      value = {"/current-all-info/request-ticket"},
      produces = {"application/json"}
   )
   public ResponseEntity reqGetCommonAllStatus(@Valid @RequestBody V2CommonIds deviceIds) throws Exception {
      this.logger.info("[REST_v2.0][DEVICE][reqGetCommonAllStatus] get status based on all current status information request responses from Filp devices. deviceIds : " + deviceIds.getIds().toString());
      V2CommonBulkResultResource result = this.v2DeviceService.reqGetCommonAllStatus(deviceIds);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result, 0, 0, deviceIds.getIds().size());
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      this.logger.info("[REST_v2.0][DEVICE][reqGetCommonAllStatus] done.");
      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Get current status flip device control Info with request id",
      notes = "Get flip type device information based on request id.\r\nRequest id can be obtained from POST /restapi/v2.0/rms/devices/flips/current-status.\r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;DeviceControl&#62; type and this value consists of resources for the deviceId that was processed successfully.\r\n'failList' of the response data is List&#60;String&#62; type and this value consists of deviceIds that were not processed.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "body",
   value = "Resource to request current time information.",
   dataType = "V2DeviceReqServiceConf"
)})
   @PostMapping(
      value = {"/current-all-info"},
      produces = {"application/json"}
   )
   public ResponseEntity getCommonGetResult(@Valid @RequestBody V2DeviceReqServiceConf body) throws Exception {
      this.logger.info("[REST_v2.0][DEVICE][getCommonGetResult] get current status flip device control Info with request Id. deviceId : " + body.getDeviceIds().toString());
      V2DeviceReqServiceResource result = this.v2DeviceService.getCommonGetResult(body);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      HttpStatus status = HttpStatus.OK;
      if (!result.getFailList().isEmpty()) {
         responseBody.setStatus("Fail");
      }

      this.logger.info("[REST_v2.0][DEVICE][getCommonGetResult] done.");
      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Get deploy status information of the specified preset.",
      notes = "Get deploy status information of the specified preset.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "presetId",
   value = "Id of specific preset",
   required = true,
   dataType = "String"
)})
   @GetMapping(
      value = {"/preset/{presetId}/deploy-status"},
      produces = {"application/json"}
   )
   public ResponseEntity preconfigDeployStatus(@PathVariable String presetId) throws Exception {
      this.logger.info("[REST_v2.0][Device][preconfigitems] Get deploy status information of the specified preset. ");
      List result = this.v2DeviceService.preconfigDeployStatus(presetId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get the request response from the service.",
      notes = "Get the request response from the service.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "String",
   example = "24-f5-aa-df-8c-de"
)})
   @PostMapping(
      value = {"/{deviceId}/send-boot"},
      produces = {"application/json"}
   )
   public ResponseEntity sendPostboot(@PathVariable String deviceId) throws Exception {
      this.logger.info("[REST_v2.0][DEVICE][sendPostboot]Get the request response from the service.");
      String result = this.v2DeviceService.sendPostboot(deviceId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result, 0, 0, 0);
      this.logger.info("[REST_v2.0][DEVICE][sendPostboot] done.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get preset information of device general information.",
      notes = "Get preset information of device general information.",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceId",
   value = "Id of specific device",
   required = true,
   dataType = "String",
   example = "24-f5-aa-df-8c-de"
), @ApiImplicitParam(
   name = "count",
   value = "Preset service run count",
   required = true,
   dataType = "String",
   example = "0"
)})
   @GetMapping(
      value = {"/{deviceId}/managed-preset"},
      produces = {"application/json"}
   )
   public ResponseEntity getDevicePreconfigResult(@PathVariable String deviceId, @RequestParam(value = "count",required = true,defaultValue = "0") int count) throws Exception {
      this.logger.info("[REST_v2.0][DEVICE][getDevicePreconfigResult] Get preset information of device general information.");
      V2DevicePresetResultResource result = this.v2DeviceService.getDevicePreconfigResult(deviceId, count);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result, 0, 0, 0);
      this.logger.info("[REST_v2.0][DEVICE][getDevicePreconfigResult] done.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get preset server settings",
      notes = "Check preconfig.server.enable value in config.properties\r\nIf true, activate the service tab of the preset.",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/preset/server-settings"},
      produces = {"application/json"}
   )
   public ResponseEntity serverSettings() throws Exception {
      V2ServerConfig resource = this.v2DeviceService.serverSettings();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get to know server connection status",
      notes = "Get to know server connection status",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "serverUrl",
   value = "Url of specific server",
   required = true,
   dataType = "String"
)})
   @GetMapping(
      value = {"/check-server-status"},
      produces = {"application/json"}
   )
   public ResponseEntity checkServerStatus(@RequestParam(value = "serverUrl",required = true) String serverUrl) throws Exception {
      this.logger.info("[REST_v2.0][DEVICE][checkServerStatus] Get to know server connection status.");
      V2DeviceServerStatus result = this.v2DeviceService.checkServerStatus(serverUrl);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      HttpStatus status = HttpStatus.OK;
      if (result.getResult().equalsIgnoreCase("fail")) {
         status = HttpStatus.BAD_REQUEST;
         responseBody.setStatus("Fail");
      }

      this.logger.info("[REST_v2.0][DEVICE][checkServerStatus] done.");
      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "process command to specified device",
      notes = "process command to specified device.\r\n",
      authorizations = {@Authorization("api_key")},
      tags = {"Device API Group"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "deviceIds",
   value = "Value of device IDs.",
   required = true,
   dataType = "V2CommonIds"
), @ApiImplicitParam(
   name = "command",
   value = "command",
   required = true,
   allowableValues = "CLEANUP_THIRD_APPLICATION_LOG, UNINSTALL_THIRD_APPLICATION",
   dataType = "String"
), @ApiImplicitParam(
   name = "option",
   value = "option",
   allowableValues = "KEEP, DELETE",
   dataType = "String"
)})
   @PutMapping(
      value = {"/process-command"},
      produces = {"application/json"}
   )
   public ResponseEntity processCommand(@Valid @RequestBody V2CommonIds deviceIds, @RequestParam("command") String command, @RequestParam(value = "option",required = false) String option) throws Exception {
      this.logger.info("[REST_v2.0][Device][processCommand] process command to specified device.");
      V2CommonBulkResultResource result = this.v2DeviceService.processCommand(deviceIds, command, option);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "get jwt session expiry for remote control",
      notes = "get jwt session expiry for remote control"
   )
   @GetMapping({"/sessionExpiry"})
   public ResponseEntity getSessionExpiry() throws ConfigException {
      this.logger.info("[REST_v2.0][Device][getSessionExpiry] jwt session expiry for remote control");
      Integer result = this.v2DeviceService.getSessionExpiry();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get remote logger settings",
      notes = "Check remote.logger.scripts.path & remote.logger.url value in config.properties\r\n",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/remote-logger"},
      produces = {"application/json"}
   )
   public ResponseEntity remoteLoggerSettings() throws Exception {
      String resource = this.v2DeviceService.remoteLoggerSettings();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get remote logger settings",
      notes = "Check remote.logger.scripts.path & remote.logger.url value in config.properties\r\n",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/fetch-remote-log-scripts-list"},
      produces = {"application/json"}
   )
   public ResponseEntity fetchRemoteLogScriptsList() throws Exception {
      List resource = this.v2DeviceService.fetchRemoteLogScriptsList();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get parsed Log Script File in a String",
      notes = "Check the selected Filepath if size greater than 0 than parse it and return in a String.\r\n",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "filePath",
   value = "full path of specific logScript file",
   required = true,
   dataType = "String"
)})
   @GetMapping(
      value = {"/get-log-script"},
      produces = {"application/json"}
   )
   public ResponseEntity getLogScript(@RequestParam(value = "filePath",required = true) String filePath) throws Exception {
      String resource = this.v2DeviceService.parseLogScriptFile(filePath);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      HttpStatus status = HttpStatus.OK;
      if (resource.isEmpty()) {
         status = HttpStatus.NOT_FOUND;
         responseBody.setStatus("Fail");
      }

      return new ResponseEntity(responseBody, status);
   }
}
