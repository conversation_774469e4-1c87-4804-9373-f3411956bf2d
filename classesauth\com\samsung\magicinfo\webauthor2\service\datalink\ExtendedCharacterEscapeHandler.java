package com.samsung.magicinfo.webauthor2.service.datalink;

import com.sun.xml.bind.marshaller.CharacterEscapeHandler;
import java.io.IOException;
import java.io.Writer;

public class ExtendedCharacterEscapeHandler implements CharacterEscapeHandler {
  public void escape(char[] chars, int start, int length, boolean isAttValue, Writer out) throws IOException {
    for (int i = start; i < start + length; i++) {
      char ch = chars[i];
      if (ch == '&') {
        out.write("&amp;");
      } else if (ch == '<') {
        out.write("&lt;");
      } else if (ch == '>') {
        out.write("&gt;");
      } else if (ch == '"') {
        out.write("&quot;");
      } else if (ch == '\'') {
        out.write("&apos;");
      } else {
        out.write(ch);
      } 
    } 
  }
}
