package com.samsung.common.db.mybatis;

import java.lang.reflect.ParameterizedType;
import java.sql.Connection;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionManager;
import org.apache.ibatis.session.TransactionIsolationLevel;

public class SqlSessionBaseDao {
   private Class mapperClass;
   private SqlSessionManager sessionManager;
   private SqlSession transactionSession;

   public SqlSessionBaseDao() {
      super();
      this.mapperClass = (Class)((ParameterizedType)this.getClass().getGenericSuperclass()).getActualTypeArguments()[0];
      this.sessionManager = SqlSessionManager.newInstance(SqlSessionFactoryManager.getInstance().getSqlSessionFactory());
   }

   public SqlSessionBaseDao(SqlSession transactionSession) {
      this();
      this.transactionSession = transactionSession;
   }

   private SqlSession getSession() {
      return (SqlSession)(this.isTransactionalSession() ? this.transactionSession : this.sessionManager);
   }

   protected Object getMapper() {
      return this.isTransactionalSession() ? this.transactionSession.getMapper(this.mapperClass) : MapperExceptionInterceptor.addInterceptor(this.sessionManager.getMapper(this.mapperClass), this.mapperClass);
   }

   protected Object getMapper(SqlSession session) {
      return session != null ? session.getMapper(this.mapperClass) : this.getMapper();
   }

   public SqlSession openNewSession(boolean autoCommit) {
      return this.sessionManager.openSession(autoCommit);
   }

   public SqlSession openNewSession(Connection connection) {
      return this.sessionManager.openSession(connection);
   }

   public SqlSession openNewSession(ExecutorType execType, boolean autoCommit) {
      return this.sessionManager.openSession(execType, autoCommit);
   }

   public SqlSession openNewSession(ExecutorType execType, Connection connection) {
      return this.sessionManager.openSession(execType, connection);
   }

   public SqlSession openNewSession(ExecutorType execType, TransactionIsolationLevel level) {
      return this.sessionManager.openSession(execType, level);
   }

   public SqlSession openNewSession(ExecutorType execType) {
      return this.sessionManager.openSession(execType);
   }

   public SqlSession openNewSession(TransactionIsolationLevel level) {
      return this.sessionManager.openSession(level);
   }

   protected boolean isTransactionalSession() {
      return this.transactionSession != null;
   }
}
