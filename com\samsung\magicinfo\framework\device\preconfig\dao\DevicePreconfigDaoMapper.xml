<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.samsung.magicinfo.framework.device.preconfig.dao.DevicePreconfigDaoMapper">
	
	<select id="getPreconfigList" resultType="com.samsung.magicinfo.framework.device.preconfig.entity.DevicePreconfig">
		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.start_index)" />
        <bind name="safe_limit" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.page_size)" />	  	
		SELECT
		    Q.ORGANIZATION_NAME, P.PRECONFIG_ID, P.NAME, P.VERSION, P.DESCRIPTION, P.ORGANIZATION_ID, P.CREATE_DATE, P.UPDATE_TIME,
			SUM(CASE WHEN S.DEPLOY_STATUS = 'SUCCESS' THEN 1 ELSE 0 END) AS COMPLETED_COUNT,
			COUNT(S.PRECONFIG_ID) AS TOTAL_COUNT
		FROM 
			MI_DMS_INFO_PRECONFIG P
		LEFT JOIN ( SELECT ( CASE WHEN N.ROOT_GROUP_ID = 0 THEN 'Common' ELSE M.GROUP_NAME END ) AS ORGANIZATION_NAME, N.ROOT_GROUP_ID
					FROM MI_USER_INFO_GROUP M, MI_USER_INFO_GROUP N
					WHERE M.GROUP_ID = N.ROOT_GROUP_ID GROUP BY M.GROUP_NAME, N.ROOT_GROUP_ID) Q ON P.ORGANIZATION_ID = Q.ROOT_GROUP_ID
				LEFT JOIN (SELECT PDS.* FROM MI_DMS_INFO_PRECONFIG_DEPLOY_STATUS PDS, MI_DMS_INFO_DEVICE DEVICE
				WHERE  
				 PDS.DEVICE_ID = DEVICE.DEVICE_ID
				) S ON P.PRECONFIG_ID = S.PRECONFIG_ID
				<if test="condition.org_id != null">
					WHERE P.ORGANIZATION_ID = 0 OR P.ORGANIZATION_ID = #{condition.org_id}
				</if>
		GROUP BY 
			P.PRECONFIG_ID, P.NAME, P.VERSION, P.DESCRIPTION, P.ORGANIZATION_ID, P.CREATE_DATE, P.UPDATE_TIME, Q.ORGANIZATION_NAME
		<choose>
            <when test="condition.sort_name != null and condition.sort_name != ''">
                <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.order_dir)" />
                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sort_name)" />
                ORDER BY
				${safe_sortColumn} ${safe_sortOrder} , P.UPDATE_TIME
            </when>
            <otherwise>ORDER BY P.UPDATE_TIME</otherwise>
        </choose>
		LIMIT ${safe_limit} + ${safe_startPos} OFFSET ${safe_startPos}
	</select>

	<select id="getPreconfigList" resultType="com.samsung.magicinfo.framework.device.preconfig.entity.DevicePreconfig" databaseId="mssql">
		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.start_index)" />
        <bind name="safe_limit" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.page_size)" />	        	
		SELECT * FROM (
			SELECT
		        Q.ORGANIZATION_NAME , P.PRECONFIG_ID, P.NAME, P.VERSION, P.DESCRIPTION, P.ORGANIZATION_ID, P.CREATE_DATE, P.UPDATE_TIME,
				SUM(CASE WHEN S.DEPLOY_STATUS = 'SUCCESS' THEN 1 ELSE 0 END) AS COMPLETED_COUNT,
				COUNT(S.PRECONFIG_ID) AS TOTAL_COUNT,
				ROW_NUMBER() OVER( ORDER BY
					<choose>
			            <when test="condition.sort_name != null and condition.sort_name != ''">
			                <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.order_dir)" />
			                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sort_name)" />
		                    ${safe_sortColumn} ${safe_sortOrder} , P.UPDATE_TIME
			            </when>
			            <otherwise>P.UPDATE_TIME</otherwise>
			        </choose> 
				) AS ROWNUM
			FROM 
				MI_DMS_INFO_PRECONFIG P
                LEFT JOIN ( SELECT ( CASE WHEN N.ROOT_GROUP_ID = 0 THEN 'Common' ELSE M.GROUP_NAME END ) AS ORGANIZATION_NAME, N.ROOT_GROUP_ID
								FROM MI_USER_INFO_GROUP M, MI_USER_INFO_GROUP N
								WHERE M.GROUP_ID = N.ROOT_GROUP_ID GROUP BY M.GROUP_NAME, N.ROOT_GROUP_ID ) Q ON P.ORGANIZATION_ID = Q.ROOT_GROUP_ID
				LEFT JOIN (SELECT  PDS.PRECONFIG_ID, PDS.DEPLOY_STATUS FROM MI_DMS_INFO_PRECONFIG_DEPLOY_STATUS PDS, MI_DMS_INFO_DEVICE DEVICE
					WHERE  
					 PDS.DEVICE_ID = DEVICE.DEVICE_ID
					) S ON P.PRECONFIG_ID = S.PRECONFIG_ID
					<if test="condition.org_id != null">
						WHERE P.ORGANIZATION_ID = 0 OR P.ORGANIZATION_ID = #{condition.org_id}
					</if>
			GROUP BY 
				P.PRECONFIG_ID, P.NAME, P.VERSION, P.DESCRIPTION, P.ORGANIZATION_ID, P.CREATE_DATE, P.UPDATE_TIME, Q.ORGANIZATION_NAME
		) SUB
		WHERE ROWNUM > ${safe_startPos} AND ROWNUM &lt;= ${safe_limit} + ${safe_startPos}
		ORDER BY ROWNUM
	</select>
	
	<select id="getPreconfigListTotalCount" resultType="int">
		SELECT 
			COUNT(PRECONFIG_ID)
		FROM
			MI_DMS_INFO_PRECONFIG
        <if test="condition.org_id != null">
            WHERE ORGANIZATION_ID = 0 OR ORGANIZATION_ID = #{condition.org_id}
        </if>
	</select>
	
	<sql id="selectCondition">
	
	</sql>
	
	<insert id="addDevicePreconfigInfo">
         INSERT INTO MI_DMS_INFO_PRECONFIG (
         PRECONFIG_ID, NAME, VERSION, DESCRIPTION, CREATE_DATE, UPDATE_TIME, ORGANIZATION_ID)
  		 VALUES (#{preconfig.preconfig_id}, #{preconfig.name}, #{preconfig.version}, #{preconfig.description}, 
   		 <include refid="utils.currentTimestamp"/>, <include refid="utils.currentTimestamp"/>, #{preconfig.organization_id});
    </insert>
    
    <insert id="addPreconfigGroupMapping">
         INSERT INTO mi_dms_map_preconfig_group(
            preconfig_id, device_group_id)
   		 VALUES (#{map.preconfig_id}, #{map.device_group_id});
    </insert>
    
    <delete id="deletePreconfigGroupMapping">
        DELETE FROM mi_dms_map_preconfig_group 
        WHERE 
        	PRECONFIG_ID = #{preconfigId}
    </delete>
    
     <delete id="deletePreconfigGroupMappingByGroup">
        DELETE FROM mi_dms_map_preconfig_group 
        WHERE DEVICE_GROUP_ID IN
        <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
            #{deviceGroupId}
        </foreach>
    </delete>
    
    <select id="getGroupMappingByPreconfig" resultType="map">
		SELECT B.GROUP_ID, B.GROUP_NAME FROM mi_dms_map_preconfig_group A, MI_DMS_INFO_GROUP B 
		WHERE A.DEVICE_GROUP_ID = B.GROUP_ID AND A.PRECONFIG_ID = #{preconfigId}
	</select>	
	
    <select id="getPreconfigInfo" resultType="com.samsung.magicinfo.framework.device.preconfig.entity.DevicePreconfig">
		SELECT 
			A.*, B.GROUP_NAME AS ORGANIZATION_NAME
		FROM
			MI_DMS_INFO_PRECONFIG A, MI_DMS_INFO_GROUP B WHERE A.PRECONFIG_ID = #{preconfigId}
			AND A.ORGANIZATION_ID = B.GROUP_ID
	</select>
	
    <select id="getPreconfigInfoByDeviceId" resultType="com.samsung.magicinfo.framework.device.preconfig.entity.DevicePreconfig">
		SELECT 
			*
		FROM
			MI_DMS_INFO_PRECONFIG 
		WHERE PRECONFIG_ID IN (
			SELECT  DISTINCT PRECONFIG_ID FROM MI_DMS_MAP_PRECONFIG_GROUP WHERE DEVICE_GROUP_ID = (
				SELECT GROUP_ID FROM MI_DMS_MAP_GROUP_DEVICE WHERE DEVICE_ID = #{deviceId}
			)
		) 
	</select>	
	
	<select id="getDeployStatusByPreconfigId" resultType="map">
		SELECT 
			A.PRECONFIG_ID, A.DEVICE_ID, A.UPDATE_TIME,
			(CASE WHEN A.DEPLOY_STATUS = 'SUCCESS' THEN 1 ELSE 0 END) AS PUBLISH_STATUS,
			C.GROUP_NAME AS DEVICE_GROUP_NAME,
			D.DEVICE_NAME
		FROM
			MI_DMS_INFO_PRECONFIG_DEPLOY_STATUS A,
			MI_DMS_MAP_GROUP_DEVICE B,
			MI_DMS_INFO_GROUP C,
			MI_DMS_INFO_DEVICE D
		WHERE 
			A.DEVICE_ID = B.DEVICE_ID AND
			A.DEVICE_ID = D.DEVICE_ID AND
			B.GROUP_ID = C.GROUP_ID AND
			A.PRECONFIG_ID = #{preconfigId}
	</select>
	
	<select id="getDeployStatusByDeviceId" resultType="map">
		SELECT 
			PRECONFIG_ID, DEVICE_ID, UPDATE_TIME,
			(CASE WHEN DEPLOY_STATUS = 'SUCCESS' THEN 1 ELSE 0 END) AS PUBLISH_STATUS,
			REPORT_TIME
		FROM
			MI_DMS_INFO_PRECONFIG_DEPLOY_STATUS
		WHERE 
			DEVICE_ID = #{deviceId}
	</select>
	
    <delete id="deletePreconfig">
        DELETE FROM MI_DMS_INFO_PRECONFIG WHERE 
       	<foreach item="preconfigId" collection="preconfigIdList" open="(" separator=" OR " close=")">
			PRECONFIG_ID = #{preconfigId}
		</foreach>
        
    </delete>

   	<update id="updatePreconfigInfo">
		UPDATE MI_DMS_INFO_PRECONFIG 
		<set>
			<if test="preconfig.name != null">
				NAME = #{preconfig.name},
			</if>
			
			<if test="preconfig.description != null">
				DESCRIPTION = #{preconfig.description},
			</if>
			
			<if test="preconfig.organization_id != null">
				ORGANIZATION_ID = #{preconfig.organization_id},
			</if>
			
			VERSION = VERSION + 1 ,
			UPDATE_TIME = <include refid="utils.currentTimestamp"/>
		</set>
		WHERE PRECONFIG_ID = #{preconfig.preconfig_id}
	</update>
	
	<insert id="addServiceConfig">
         INSERT INTO MI_DMS_INFO_SERVICE_CONFIG (
            PRECONFIG_ID, SERVICE_TYPE, HOST, PATH, PORT, PROTOCOL)
   		 VALUES (#{service.preconfig_id}, #{service.service_type}, #{service.host}, #{service.path}, #{service.port}, #{service.protocol});
    </insert>
    
	<insert id="addSoftwareConfig">
         INSERT INTO MI_DMS_INFO_SOFTWARE_CONFIG (PRECONFIG_ID, SOFTWARE_ID, SOFTWARE_TYPE)
   		 VALUES (#{software.preconfig_id}, #{software.software_id}, #{software.software_type});
    </insert>    

	<update id="updateServiceConfig">
		UPDATE MI_DMS_INFO_SERVICE_CONFIG 
		<set>
			<bind name="cnt" value="0"/>
			<if test="service.host != null">
				<bind name="cnt" value="1"/>
				HOST = #{service.host}
			</if>
			<if test="service.path != null">
				<if test="cnt > 0">,</if>
				PATH = #{service.path}
				<bind name="cnt" value="1"/>
			</if>
			<if test="service.port != null">
				<if test="cnt > 0">,</if>
				PORT = #{service.port}
				<bind name="cnt" value="1"/>
			</if>
			<if test="service.protocol != null">
				<if test="cnt > 0">,</if>
				PROTOCOL = #{service.protocol}
			</if>
		</set>
		WHERE PRECONFIG_ID = #{service.preconfig_id} 
			AND SERVICE_TYPE =  #{service.service_type}
	</update>
	
	<select id="checkExistServiceConfig" resultType="java.lang.Integer">
		SELECT 
			COUNT(*)
		FROM
			MI_DMS_INFO_SERVICE_CONFIG
		WHERE PRECONFIG_ID = #{service.preconfig_id} 
			<if test="service.service_type != null">
				AND SERVICE_TYPE =  #{service.service_type}
			</if>
	</select>
	
	<select id="getServiceConfigInfo" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceServiceConf">
		SELECT 
			*
		FROM
			MI_DMS_INFO_SERVICE_CONFIG
		WHERE
			PRECONFIG_ID = #{preconfigId}
		ORDER BY SERVICE_TYPE
	</select>
	
	<select id="getSoftwareConfigInfo" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSoftwareConf">
		SELECT 
			*
		FROM
			MI_DMS_INFO_SOFTWARE_CONFIG
		WHERE
			PRECONFIG_ID = #{preconfigId}
		ORDER BY SOFTWARE_TYPE
	</select>
		
	<delete id="deleteServiceConfig">
        DELETE FROM MI_DMS_INFO_SERVICE_CONFIG 
        WHERE
			PRECONFIG_ID = #{preconfigId}
    </delete>
    
    <delete id="deleteSoftwareConfig">
        DELETE FROM MI_DMS_INFO_SOFTWARE_CONFIG 
        WHERE
			PRECONFIG_ID = #{preconfigId}
    </delete>
    
    <insert id="addDeployStatus">
         INSERT INTO MI_DMS_INFO_PRECONFIG_DEPLOY_STATUS(
            PRECONFIG_ID, DEVICE_ID, DEPLOY_STATUS, UPDATE_TIME)
   		 VALUES (#{preconfigId}, #{deviceId}, #{status}, <include refid="utils.currentTimestamp"/>);
    </insert>
    
    <delete id="deleteDeployStatus">
        DELETE FROM MI_DMS_INFO_PRECONFIG_DEPLOY_STATUS 
        WHERE
			PRECONFIG_ID = #{preconfigId}
    </delete>
    
    <delete id="deleteDeployStatusByGroup">
        DELETE FROM MI_DMS_INFO_PRECONFIG_DEPLOY_STATUS 
        WHERE DEVICE_ID IN (
			SELECT DEVICE_ID FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID IN 
			<foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
            	#{deviceGroupId}
       		</foreach>
		) 
    </delete>
    
    <insert id="setDeployStatusByGroup">
    	INSERT INTO MI_DMS_INFO_PRECONFIG_DEPLOY_STATUS(
            PRECONFIG_ID, DEVICE_ID, DEPLOY_STATUS, UPDATE_TIME)
        (SELECT #{preconfigId}, DEVICE_ID, #{status}, <include refid="utils.currentTimestamp"/>  FROM 
		MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = #{groupId})
    </insert>

    <update id="setDeployStatus">
    	UPDATE MI_DMS_INFO_PRECONFIG_DEPLOY_STATUS SET
			PRECONFIG_ID = #{preconfigId},
			DEPLOY_STATUS = #{status},
			UPDATE_TIME = <include refid="utils.currentTimestamp"/>
		WHERE
			DEVICE_ID = #{deviceId}
    </update>
    
    <update id="setDeployStatusReportTime">
    	UPDATE MI_DMS_INFO_PRECONFIG_DEPLOY_STATUS SET
			REPORT_TIME = #{reportTime} 
		WHERE
			PRECONFIG_ID = #{preconfigId}
			AND DEVICE_ID = #{deviceId}
    </update>

	<update id="deletePreconfigFromDevice">
		UPDATE MI_DMS_INFO_DEVICE SET
			PRE_CONFIG_VERSION = null
		WHERE
		    DEVICE_ID = #{deviceId}
	</update>
</mapper>