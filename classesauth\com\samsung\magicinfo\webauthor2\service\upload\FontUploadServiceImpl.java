package com.samsung.magicinfo.webauthor2.service.upload;

import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.ContentSaveElements;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.ImageDimension;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.svg.FontDescription;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.repository.FileHashRefreshRepository;
import com.samsung.magicinfo.webauthor2.service.CSDFileService;
import com.samsung.magicinfo.webauthor2.service.CidMappingService;
import com.samsung.magicinfo.webauthor2.service.upload.FontUploadService;
import com.samsung.magicinfo.webauthor2.service.upload.HttpContentUploadService;
import com.samsung.magicinfo.webauthor2.service.upload.JobStateService;
import com.samsung.magicinfo.webauthor2.util.FileHashUtil;
import com.samsung.magicinfo.webauthor2.util.ImageDimensionUtil;
import com.samsung.magicinfo.webauthor2.util.PlayTimeUtil;
import com.samsung.magicinfo.webauthor2.util.SupportedFormatUtils;
import com.samsung.magicinfo.webauthor2.util.UserData;
import com.samsung.magicinfo.webauthor2.xml.transferfile.response.TransferFileResponseType;
import com.samsung.magicinfo.webauthor2.xml.transferfile.response.TransferFilesResponseType;
import java.awt.Font;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class FontUploadServiceImpl implements FontUploadService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.upload.FontUploadServiceImpl.class);
  
  private CidMappingService cidMappingService;
  
  private CSDFileService csdFileService;
  
  private FileHashRefreshRepository fileHashRefreshRepository;
  
  private HttpContentUploadService httpContentUploadService;
  
  private JobStateService jobStateService;
  
  private ServletContext servletContext;
  
  private UserData userData;
  
  private ContentSaveElements contentSaveElements;
  
  @Autowired
  public FontUploadServiceImpl(CidMappingService cidMappingService, CSDFileService csdFileService, FileHashRefreshRepository fileHashRefreshRepository, HttpContentUploadService httpContentUploadService, JobStateService jobStateService, ServletContext servletContext, UserData userData, ContentSaveElements contentSaveElements) {
    this.cidMappingService = cidMappingService;
    this.csdFileService = csdFileService;
    this.fileHashRefreshRepository = fileHashRefreshRepository;
    this.httpContentUploadService = httpContentUploadService;
    this.jobStateService = jobStateService;
    this.servletContext = servletContext;
    this.userData = userData;
    this.contentSaveElements = contentSaveElements;
  }
  
  public FontDescription getFontDescription(String fileName) {
    try {
      FontDescription fontDesc = analyzeFont(fileName);
      return fontDesc;
    } catch (IOException e) {
      logger.error(e.getMessage());
      throw new UploaderException("Failed to upload Font " + e.getMessage());
    } 
  }
  
  private FontDescription analyzeFont(String fileName) throws IOException {
    FontDescription fontDesc = new FontDescription();
    String serverDirectoryPath = this.servletContext.getRealPath("insertContents");
    String userWorkspaceDirectory = this.userData.getWorkspaceFolderName();
    Path fontFilePath = Paths.get(serverDirectoryPath, new String[] { userWorkspaceDirectory, fileName });
    File fontFile = fontFilePath.toFile();
    try {
      Font customFont = Font.createFont(0, fontFile);
      fontDesc.setFontFileName(fileName);
      fontDesc.setFontFace(customFont.getFontName());
      fontDesc.setFontFamily(customFont.getFamily());
      fontDesc.setFontLogicalName(customFont.getName());
      fontDesc.setFontWeight(customFont.isBold() ? "bold" : "normal");
      fontDesc.setFontStyle(customFont.isItalic() ? "italic" : "normal");
      return fontDesc;
    } catch (IOException|java.awt.FontFormatException e) {
      throw new UploaderException("Failed to upload Font " + e.getMessage());
    } 
  }
  
  public List<MediaSource> getUpdatedMediaSources(MultipartFile font, String fileName, DeviceType deviceType) {
    String contentId = this.cidMappingService.cidMapping();
    List<MediaSource> fontMediaSource = getMediaSourceFromMultipartFile(font, fileName);
    String csdXml = this.csdFileService.generateCSD(fontMediaSource, contentId, deviceType);
    TransferFilesResponseType csdResponse = this.csdFileService.postSingleFileCsdToMips(csdXml, contentId);
    logger.debug("CSD : " + csdXml);
    logger.debug("CSD RESPONSE, CID : " + csdResponse.getContentId());
    for (TransferFileResponseType fs : csdResponse.getTransferFiles())
      logger.debug("CSD FILE : IsNew - " + fs.isNew() + ", Idx - " + fs.getReqIndex() + ", FileID - " + fs.getFileId()); 
    List<MediaSource> updatedMediaSources = this.csdFileService.updateMediaSources(fontMediaSource, csdResponse);
    ((MediaSource)updatedMediaSources.get(0)).setContentId(contentId);
    this.contentSaveElements.setMediaSources(updatedMediaSources);
    return updatedMediaSources;
  }
  
  public String uploadFont(String lfdXml) {
    List<MediaSource> updatedMediaSources = this.contentSaveElements.getMediaSources();
    MediaSource mediaSourceXml = updatedMediaSources.get(0);
    String contentId = mediaSourceXml.getContentId();
    try {
      Path lfdFilePath = Paths.get(mediaSourceXml.getPath(), new String[0]);
      File lfdFile = lfdFilePath.toFile();
      Files.deleteIfExists(lfdFilePath);
      FileUtils.writeStringToFile(lfdFile, lfdXml, StandardCharsets.UTF_8);
      String newHash = FileHashUtil.getHash(lfdFile);
      mediaSourceXml.setFileHash(newHash);
      this.fileHashRefreshRepository.fileHashRefresh(contentId, mediaSourceXml.getFileId(), newHash);
      this.httpContentUploadService.uploadListOfMediaSources(updatedMediaSources, contentId);
      boolean contentIsDuplicate = false;
      this.jobStateService.jobStateSuccess(this.userData.getUserId(), this.userData.getToken(), contentId, "1", contentIsDuplicate);
    } catch (Exception e) {
      logger.error("Failed to upload font : " + e.getMessage());
      this.jobStateService.jobStateFail(this.userData.getUserId(), this.userData.getToken(), contentId, "1");
      throw new UploaderException("Failed to upload Font " + e.getMessage());
    } 
    return contentId;
  }
  
  private List<MediaSource> getMediaSourceFromMultipartFile(MultipartFile font, String fileName) {
    List<MediaSource> mediaSourcesToUpload = new ArrayList<>();
    try {
      String serverDirectoryPath = this.servletContext.getRealPath("insertContents");
      String userWorkspaceDirectory = this.userData.getWorkspaceFolderName();
      MediaSource fontFile = getFontMediaSource(font, fileName, serverDirectoryPath, userWorkspaceDirectory);
      MediaSource xmlFile = getLfdMediaSource(serverDirectoryPath, userWorkspaceDirectory, fontFile, font.getOriginalFilename());
      MediaSource thumbnailFile = getThumbnailMediaSource();
      mediaSourcesToUpload.add(xmlFile);
      mediaSourcesToUpload.add(fontFile);
      mediaSourcesToUpload.add(thumbnailFile);
      return mediaSourcesToUpload;
    } catch (IOException e) {
      logger.error(e.getMessage());
      return Collections.emptyList();
    } 
  }
  
  private MediaSource getThumbnailMediaSource() {
    String name = "font_lfd_thumbnail.png";
    Path thumbnailFilePath = Paths.get(this.servletContext.getRealPath("images"), new String[] { name });
    File thumbnailFile = new File(thumbnailFilePath.toString());
    MediaSource msThumbnail = new MediaSource();
    msThumbnail.setData("");
    msThumbnail.setFileName(name);
    String extension = FilenameUtils.getExtension(name);
    msThumbnail.setMediaType(SupportedFormatUtils.getMediaTypeForExtension(extension));
    msThumbnail.setFileType("thumbnail");
    msThumbnail.setFileHash(FileHashUtil.getHash(thumbnailFile));
    msThumbnail.setMediaSize(thumbnailFile.length());
    fillImageDimensions(msThumbnail, thumbnailFile);
    msThumbnail.setPath(thumbnailFilePath.toString());
    msThumbnail.setFileId(UUID.randomUUID().toString().toUpperCase());
    return msThumbnail;
  }
  
  private MediaSource getFontMediaSource(MultipartFile font, String fileName, String serverDirectoryPath, String userWorkspaceDirectory) throws IOException {
    Path fontFilePath = Paths.get(serverDirectoryPath, new String[] { userWorkspaceDirectory, font.getOriginalFilename() });
    FileUtils.copyInputStreamToFile(font.getInputStream(), fontFilePath.toFile());
    File fontFile = fontFilePath.toFile();
    MediaSource msFont = new MediaSource();
    try {
      Font customFont = Font.createFont(0, fontFile);
      msFont.setTitle(customFont.getFontName());
    } catch (IOException|java.awt.FontFormatException e) {
      logger.error("Error on creating font from Font.createFont.");
      throw new UploaderException("Failed to upload Font " + e.getMessage());
    } 
    msFont.setFileName(fontFile.getName());
    msFont.setFileType(FilenameUtils.getExtension(fontFile.getName()));
    msFont.setMediaType(MediaType.FONT);
    msFont.setMediaSize(Files.size(fontFilePath));
    msFont.setFileId(UUID.randomUUID().toString().toUpperCase());
    msFont.setMediaWidth(1920);
    msFont.setMediaHeight(1080);
    msFont.setFileHash(FileHashUtil.getHash(fontFile));
    msFont.setPath(fontFilePath.toString());
    return msFont;
  }
  
  private MediaSource getLfdMediaSource(String serverDirectoryPath, String userWorkspaceDirectory, MediaSource fontFile, String fontFileName) throws IOException {
    Path lfdFilePath = Paths.get(serverDirectoryPath, new String[] { userWorkspaceDirectory, fontFile.getFileName() + ".lfd" });
    File lfdFile = lfdFilePath.toFile();
    Files.deleteIfExists(lfdFilePath);
    FileUtils.writeStringToFile(lfdFile, "<?xml version=\"1.0\" encoding=\"utf-8\"?>", StandardCharsets.UTF_8);
    MediaSource xmlFile = new MediaSource();
    xmlFile.setTitle(fontFile.getTitle());
    xmlFile.setFileName(lfdFile.getName());
    xmlFile.setFileType(FilenameUtils.getExtension(lfdFile.getName()));
    xmlFile.setMediaType(MediaType.FONT);
    xmlFile.setFileId(UUID.randomUUID().toString().toUpperCase());
    xmlFile.setMediaWidth(1920);
    xmlFile.setMediaHeight(1080);
    xmlFile.setMediaDuration(PlayTimeUtil.convertPlayTime("60000").doubleValue());
    xmlFile.setPath(lfdFilePath.toString());
    xmlFile.setFileHash(FileHashUtil.getHash(lfdFile));
    xmlFile.setMediaSize(calculateLfdSize(fontFile, fontFileName, "Text"));
    return xmlFile;
  }
  
  private long calculateLfdSize(MediaSource fontfile, String fontFileName, String contentName) {
    long baseXmlSize = 16482L;
    long fontSizeStringLength = Long.toString(fontfile.getMediaSize()).length();
    long fontFileNameLength = fontFileName.length();
    long contentNameLength = contentName.length();
    long fontFaceLength = 0L;
    long fontFamilyLength = 0L;
    long fontStyleLength = 0L;
    long fontWeightLength = 0L;
    String serverDirectoryPath = this.servletContext.getRealPath("insertContents");
    String userWorkspaceDirectory = this.userData.getWorkspaceFolderName();
    Path fontFilePath = Paths.get(serverDirectoryPath, new String[] { userWorkspaceDirectory, fontFileName });
    File fontFile = fontFilePath.toFile();
    try {
      Font customFont = Font.createFont(0, fontFile);
      fontFaceLength = customFont.getFontName().length();
      fontFamilyLength = customFont.getFamily().length();
      String weight = customFont.isBold() ? "bold" : "normal";
      String style = customFont.isItalic() ? "italic" : "normal";
      fontStyleLength = style.length();
      fontWeightLength = weight.length();
    } catch (IOException|java.awt.FontFormatException e) {
      throw new UploaderException("Failed to upload Font " + e.getMessage());
    } 
    return baseXmlSize + fontSizeStringLength + fontFileNameLength * 2L + contentNameLength + fontFaceLength + fontFamilyLength + fontStyleLength + fontWeightLength;
  }
  
  private void fillImageDimensions(MediaSource mediaSource, File thumbnailFile) {
    MediaType mediaType = mediaSource.getMediaType();
    if (mediaType != null && mediaType == MediaType.IMAGE)
      try {
        ImageDimension imageDimensions = ImageDimensionUtil.getImageDimensions(thumbnailFile.toPath());
        mediaSource.setMediaWidth(imageDimensions.getWidth());
        mediaSource.setMediaHeight(imageDimensions.getHeight());
      } catch (IOException e) {
        logger.error("Error on setting image dimensions.");
      }  
  }
}
