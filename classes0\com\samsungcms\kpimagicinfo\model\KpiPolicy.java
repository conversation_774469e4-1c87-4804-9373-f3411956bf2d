package com.samsungcms.kpimagicinfo.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.samsungcms.kpimagicinfo.model.Server;
import com.samsungcms.kpimagicinfo.model.Transfer;
import org.springframework.stereotype.Component;

@Component
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class KpiPolicy {
  @JsonProperty("version")
  private int version = 0;
  
  @JsonProperty("enabled")
  private boolean enabled = true;
  
  @JsonProperty("transfer")
  private Transfer transfer = new Transfer();
  
  @JsonProperty("server")
  private Server server = new Server();
  
  public int getVersion() {
    return this.version;
  }
  
  public void setVersion(int version) {
    this.version = version;
  }
  
  public boolean isEnabled() {
    return this.enabled;
  }
  
  public void setEnabled(boolean enabled) {
    this.enabled = enabled;
  }
  
  public Transfer getTransfer() {
    return this.transfer;
  }
  
  public void setTransfer(Transfer transfer) {
    this.transfer = transfer;
  }
  
  public Server getServer() {
    return this.server;
  }
  
  public void setServer(Server server) {
    this.server = server;
  }
}
