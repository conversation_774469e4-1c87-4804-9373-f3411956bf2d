package com.samsung.magicinfo.framework.scheduler.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SequenceDB;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramGroup;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.protocol.exception.ActionNotSupportedException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class ProgramGroupDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(ProgramGroupDao.class);

   public ProgramGroupDao() {
      super();
   }

   public ProgramGroupDao(SqlSession sqlSession) {
      super(sqlSession);
   }

   public int addGroup(ProgramGroup programGroup) throws SQLException, ActionNotSupportedException {
      if (programGroup.getP_group_id() >= -1L && programGroup.getP_group_id() != 999999L) {
         SqlSession sqlSession = this.openNewSession(false);

         byte var6;
         try {
            ProgramGroupDaoMapper mapper = (ProgramGroupDaoMapper)this.getMapper(sqlSession);
            int group_id = false;
            int cnt = false;
            int group_id = this.createNewGroupId();
            if (group_id == -1) {
               sqlSession.rollback();
               var6 = -1;
               return var6;
            }

            int cnt = mapper.createGroup(group_id, programGroup);
            if (cnt > 0) {
               sqlSession.commit();
               int var14 = group_id;
               return var14;
            }

            sqlSession.rollback();
            var6 = -1;
         } catch (SQLException var10) {
            sqlSession.rollback();
            throw var10;
         } finally {
            sqlSession.close();
         }

         return var6;
      } else {
         throw new ActionNotSupportedException("MESSAGE_SCHEDULE_CANT_ADD_GROUP_SEL_LOCATION_P");
      }
   }

   public boolean delGroup(int group_id) throws SQLException {
      if (group_id <= 0) {
         this.logger.error("Block to delete schedule group of root_group_id");
         return false;
      } else {
         int cnt = false;
         List groupList = this.getChildGroupIdList(group_id, true);
         int programOrgGroupId = this.getProgramOrgGroupId(group_id);
         SqlSession sqlSession = this.openNewSession(false);

         try {
            ProgramGroupDaoMapper mapper = (ProgramGroupDaoMapper)this.getMapper(sqlSession);

            int cnt;
            for(int i = 0; i < groupList.size(); ++i) {
               mapper.deleteProgram((Long)groupList.get(i));
               mapper.deleteGroupMap((long)programOrgGroupId, (Long)groupList.get(i));
               cnt = mapper.deleteGroup((Long)groupList.get(i));
               if (cnt <= 0) {
                  sqlSession.rollback();
                  boolean var8 = false;
                  return var8;
               }
            }

            mapper.deleteProgram((long)group_id);
            mapper.deleteGroupMap((long)programOrgGroupId, (long)group_id);
            cnt = mapper.deleteGroup((long)group_id);
            boolean var15;
            if (cnt <= 0) {
               sqlSession.rollback();
               var15 = false;
               return var15;
            } else {
               sqlSession.commit();
               var15 = true;
               return var15;
            }
         } catch (SQLException var12) {
            sqlSession.rollback();
            throw var12;
         } finally {
            sqlSession.close();
         }
      }
   }

   public String getProgramOrgNameByGroupId(Long group_id) throws SQLException {
      Map groupMap = ((ProgramGroupDaoMapper)this.getMapper()).getProgramOrgNameByGroupId(group_id);
      if (groupMap == null) {
         return null;
      } else {
         return (Long)groupMap.get("group_depth") <= 1L ? (String)groupMap.get("group_name") : this.getProgramOrgNameByGroupId((Long)groupMap.get("p_group_id"));
      }
   }

   public boolean delDeviceWithContents(int group_id) throws SQLException {
      ((ProgramGroupDaoMapper)this.getMapper()).deleteDeviceWithContents("dd328c1a-19ab-4bd8-a604-5073dadd1383", group_id);
      return true;
   }

   public List getDeviceGroupMappedInProgramByGroupId(long g_id) throws SQLException {
      return ((ProgramGroupDaoMapper)this.getMapper()).getDeviceGroupMappedInProgramByGroupId(g_id);
   }

   public List getChildProgramList(int group_id, boolean recursive) throws SQLException {
      List programList = ((ProgramGroupDaoMapper)this.getMapper()).getChildProgramList(group_id);
      if (recursive) {
         List groupIdList = this.getChildGroupIdList(group_id, true);
         Iterator iter = groupIdList.iterator();

         while(iter.hasNext()) {
            Long group = (Long)iter.next();
            List subGroupProgramList = this.getChildProgramList(group.intValue(), false);
            if (subGroupProgramList != null && subGroupProgramList.size() != 0) {
               programList.addAll(subGroupProgramList);
            }
         }
      }

      return programList;
   }

   public List getActiveChildProgramList(int group_id, boolean recursive) throws SQLException {
      List programList = ((ProgramGroupDaoMapper)this.getMapper()).getActiveChildProgramList(group_id);
      if (recursive) {
         List groupIdList = this.getChildGroupIdList(group_id, true);
         Iterator iter = groupIdList.iterator();

         while(iter.hasNext()) {
            Long group = (Long)iter.next();
            List subGroupProgramList = this.getActiveChildProgramList(group.intValue(), false);
            if (subGroupProgramList != null && subGroupProgramList.size() != 0) {
               programList.addAll(subGroupProgramList);
            }
         }
      }

      return programList;
   }

   public List getChildGroupIdList(int group_id, boolean recursive) throws SQLException {
      List rtList = new ArrayList();
      List groupIdList = ((ProgramGroupDaoMapper)this.getMapper()).getChildGroupIdList(group_id, 999999);
      if (groupIdList != null) {
         for(int i = 0; i < groupIdList.size(); ++i) {
            if (recursive) {
               Long group = (Long)groupIdList.get(i);
               rtList.add(group);
               List temp = this.getChildGroupIdList(group.intValue(), recursive);
               if (temp != null && temp.size() != 0) {
                  rtList.addAll(temp);
               }
            } else {
               rtList.add((Long)groupIdList.get(i));
            }
         }
      }

      return rtList;
   }

   public List getChildGroupIdList(int group_id) throws SQLException {
      return ((ProgramGroupDaoMapper)this.getMapper()).getChildGroupLists(group_id, 999999);
   }

   public List getChildProgramIdList(int group_id, boolean recursive) throws SQLException {
      List rtnList = new ArrayList();
      List programIdList = ((ProgramGroupDaoMapper)this.getMapper()).getChildProgramIdList(group_id);
      if (programIdList != null) {
         for(int i = 0; i < programIdList.size(); ++i) {
            rtnList.add(programIdList.get(i));
         }

         if (recursive) {
            List groupIdList = this.getChildGroupIdList(group_id, true);
            Iterator iter = groupIdList.iterator();

            while(iter.hasNext()) {
               Long group = (Long)iter.next();
               List subGroupProgramIdList = this.getChildProgramIdList(group.intValue(), false);
               if (subGroupProgramIdList != null && subGroupProgramIdList.size() != 0) {
                  rtnList.addAll(subGroupProgramIdList);
               }
            }
         }
      }

      return rtnList;
   }

   public List getChildGroupList(int group_id, boolean recursive) throws SQLException {
      return recursive ? ((ProgramGroupDaoMapper)this.getMapper()).getAllProgramGroups(new Long((long)group_id)) : ((ProgramGroupDaoMapper)this.getMapper()).getChildGroupList(group_id, 999999);
   }

   public List getChildGroupList(int group_id, boolean recursive, String deviceType) throws SQLException {
      List tmp = new ArrayList();
      List groupList = ((ProgramGroupDaoMapper)this.getMapper()).getChildGroupListWithDevice(group_id, 999999, deviceType);
      if (!recursive) {
         return groupList;
      } else {
         Iterator iter = groupList.iterator();

         while(iter.hasNext()) {
            ProgramGroup programGroup = (ProgramGroup)iter.next();
            tmp.add(programGroup);
            tmp.addAll(this.getChildGroupList(programGroup.getGroup_id().intValue(), recursive, deviceType));
         }

         return tmp;
      }
   }

   public ProgramGroup getGroup(int group_id) throws SQLException {
      return ((ProgramGroupDaoMapper)this.getMapper()).getGroup(group_id);
   }

   public List getGroupList() throws SQLException {
      return ((ProgramGroupDaoMapper)this.getMapper()).getGroupList();
   }

   public int getParentGroupId(int group_id) throws SQLException {
      return ((ProgramGroupDaoMapper)this.getMapper()).getParentGroupId(group_id);
   }

   public boolean moveGroup(int group_id, int new_parent_group_id) throws SQLException {
      return false;
   }

   public boolean moveGroup(ProgramGroup programGroup) throws SQLException {
      if (programGroup.getGroup_id() <= 0L) {
         this.logger.error("Block to move schedule group of root_group_id");
         return false;
      } else {
         return ((ProgramGroupDaoMapper)this.getMapper()).moveGroup(programGroup) > 0;
      }
   }

   public boolean setGroup(ProgramGroup programGroup) throws SQLException {
      if (programGroup.getGroup_id() <= 0L) {
         this.logger.error("Block to delete schedule group of root_group_id");
         return false;
      } else {
         return ((ProgramGroupDaoMapper)this.getMapper()).updateGroup(programGroup) > 0;
      }
   }

   public boolean updateGroupName(ProgramGroup progGroup) throws SQLException {
      return ((ProgramGroupDaoMapper)this.getMapper()).updateGroupName(progGroup) > 0;
   }

   private int createNewGroupId() throws SQLException {
      return SequenceDB.getNextValue("MI_CDS_INFO_PROGRAM_GROUP");
   }

   public int getProgramGroupForUser(String strOrg) throws SQLException {
      Integer group = ((ProgramGroupDaoMapper)this.getMapper()).getProgramGroupForUser(strOrg);
      return group == null ? -1 : group;
   }

   public int getProgramOrgGroupId(int groupId) throws SQLException {
      Map info = ((ProgramGroupDaoMapper)this.getMapper()).getProgramOrgGroupId(groupId);
      int newGroupParentId = ((Long)info.get("P_GROUP_ID")).intValue();
      return newGroupParentId != 0 ? this.getProgramOrgGroupId(newGroupParentId) : ((Long)info.get("GROUP_ID")).intValue();
   }

   public int getProgramGroupForOrg(String strOrg) throws SQLException {
      Integer group = ((ProgramGroupDaoMapper)this.getMapper()).getProgramGroupForOrg(strOrg);
      return group == null ? -1 : group;
   }

   public boolean addGroupForOrg(String strOrgName) throws SQLException {
      ProgramGroup programGroup = null;
      SqlSession sqlSession = this.openNewSession(false);

      boolean var9;
      try {
         ProgramGroupDaoMapper mapper = (ProgramGroupDaoMapper)this.getMapper(sqlSession);
         int group_id_base = false;
         int cnt2 = false;
         int group_id = this.createNewGroupId();
         int group_id_base = this.createNewGroupId();
         if (group_id == -1 || group_id_base == -1) {
            sqlSession.rollback();
            var9 = false;
            return var9;
         }

         programGroup = new ProgramGroup();
         programGroup.setP_group_id(0L);
         programGroup.setGroup_depth(1L);
         programGroup.setGroup_name(strOrgName);
         programGroup.setDescription("Organization");
         int cnt = mapper.createGroup(group_id, programGroup);
         programGroup = new ProgramGroup();
         programGroup.setP_group_id((long)group_id);
         programGroup.setGroup_depth(2L);
         programGroup.setGroup_name("default");
         programGroup.setDescription("Default Group");
         int cnt2 = mapper.createGroup(group_id_base, programGroup);
         if (cnt2 <= 0 || cnt <= 0) {
            sqlSession.rollback();
            var9 = false;
            return var9;
         }

         sqlSession.commit();
         var9 = true;
      } catch (SQLException var13) {
         sqlSession.rollback();
         throw var13;
      } finally {
         sqlSession.close();
      }

      return var9;
   }

   public boolean canDeleteOrgGroups(String strOrg) throws SQLException {
      int iOrgRootGroup = this.getProgramGroupForUser(strOrg);
      boolean result = false;
      List children = this.getChildProgramList(iOrgRootGroup, true);
      if (children != null && children.size() != 0) {
         if (children.size() > 0) {
            result = false;
         }
      } else {
         result = true;
      }

      return result;
   }

   public boolean deleteOrgGroups(String strOrg) throws SQLException {
      int cnt = false;
      int orgGroupId = this.getProgramGroupForOrg(strOrg);
      if (orgGroupId <= 0) {
         return false;
      } else {
         List groupList = this.getChildGroupIdList(orgGroupId, true);
         SqlSession sqlSession = this.openNewSession(false);

         boolean var16;
         try {
            ProgramGroupDaoMapper mapper = (ProgramGroupDaoMapper)this.getMapper(sqlSession);

            int cnt;
            for(int i = groupList.size() - 1; i > 0; --i) {
               Long childGroupId = (Long)groupList.get(i);
               mapper.deleteProgramRow(childGroupId);
               mapper.deleteGroupMapRow(childGroupId);
               cnt = mapper.deleteGroupRow(childGroupId);
               if (cnt <= 0) {
                  sqlSession.rollback();
                  boolean var9 = false;
                  return var9;
               }
            }

            cnt = mapper.deleteGroupRow((long)orgGroupId);
            if (cnt <= 0) {
               sqlSession.rollback();
               var16 = false;
               return var16;
            }

            sqlSession.commit();
            var16 = true;
         } catch (SQLException var13) {
            sqlSession.rollback();
            throw var13;
         } finally {
            sqlSession.close();
         }

         return var16;
      }
   }

   public boolean setOrgName(String originName, String newName) throws SQLException {
      int cnt = false;
      SqlSession sqlSession = this.openNewSession(false);

      boolean var6;
      try {
         ProgramGroupDaoMapper mapper = (ProgramGroupDaoMapper)this.getMapper(sqlSession);
         int cnt = mapper.setGroupName(originName, newName);
         if (cnt != 1) {
            sqlSession.rollback();
            var6 = false;
            return var6;
         }

         sqlSession.commit();
         var6 = true;
      } catch (SQLException var10) {
         sqlSession.rollback();
         var6 = false;
         return var6;
      } finally {
         sqlSession.close();
      }

      return var6;
   }

   public boolean updateGroupTypeByProgramId(String deviceType, String programId) throws SQLException {
      SqlSession sqlSession = this.openNewSession(false);

      boolean var6;
      try {
         ProgramGroupDaoMapper mapper = (ProgramGroupDaoMapper)this.getMapper(sqlSession);
         int cnt = mapper.updateGroupTypeByProgramId(deviceType, programId);
         if (cnt == 1) {
            sqlSession.commit();
            var6 = true;
            return var6;
         }

         sqlSession.rollback();
         var6 = false;
      } catch (SQLException var10) {
         sqlSession.rollback();
         boolean var5 = false;
         return var5;
      } finally {
         sqlSession.close();
      }

      return var6;
   }

   public List getRootGroupById(String cmd, String organization) throws SQLException {
      return ((ProgramGroupDaoMapper)this.getMapper()).getRootGroupById(cmd, organization);
   }

   public List getGroupById(String cmd, Long id) throws SQLException {
      return ((ProgramGroupDaoMapper)this.getMapper()).getGroupById(cmd, id);
   }

   public List getMessageGroupById(String cmd, Long id) throws SQLException {
      return ((ProgramGroupDaoMapper)this.getMapper()).getMessageGroupById(cmd, id);
   }

   public List getMessageRootGroupById(String cmd, String organization) throws SQLException {
      return ((ProgramGroupDaoMapper)this.getMapper()).getMessageRootGroupById(cmd, organization);
   }

   public List getEventRootGroupById(String cmd, String organization) throws SQLException {
      return ((ProgramGroupDaoMapper)this.getMapper()).getEventRootGroupById(cmd, organization);
   }

   public List getEventGroupById(String cmd, Long id) throws SQLException {
      return ((ProgramGroupDaoMapper)this.getMapper()).getEventGroupById(cmd, id);
   }

   public List getProgramGroupIdByOrganization(int groupId) throws SQLException {
      ArrayList rtn = null;

      try {
         List list = ((ProgramGroupDaoMapper)this.getMapper()).getGroupIdByOrganizationGroupIdWithRecursive(groupId);
         if (list != null && list.size() > 0) {
            rtn = new ArrayList();
            Iterator var8 = list.iterator();

            while(var8.hasNext()) {
               Map map = (Map)var8.next();
               rtn.add((Long)map.get("group_id"));
            }
         }
      } catch (Exception var7) {
         this.logger.error("", var7);
         this.logger.info("[MagicInfo_ProgramGroup] not support with query");
         List list = this.getChildGroupList(groupId, true);
         if (list != null && list.size() > 0) {
            rtn = new ArrayList();
            Iterator var5 = list.iterator();

            while(var5.hasNext()) {
               ProgramGroup group = (ProgramGroup)var5.next();
               rtn.add(group.getGroup_id());
            }
         }
      }

      return rtn;
   }

   public Integer getOrganizationIdByName(String groupName) throws SQLException {
      return ((ProgramGroupDaoMapper)this.getMapper()).getOrganizationIdByName(groupName);
   }

   public Long getTotalOrganizationProgramCountByGroupId(long pGroupId, long groupId) throws SQLException {
      Long count = 0L;

      try {
         if (pGroupId < 1L) {
            count = ((ProgramGroupDaoMapper)this.getMapper()).getTotalOrganizationAllProgramCountByGroupId(groupId);
         } else {
            count = ((ProgramGroupDaoMapper)this.getMapper()).getTotalOrganizationProgramCountByGroupId(groupId);
         }
      } catch (Exception var7) {
         this.logger.error("", var7);
         count = 0L;
      }

      return count;
   }

   public List getParentGroupNameByGroupId(long groupId) throws SQLException {
      return ((ProgramGroupDaoMapper)this.getMapper()).getParentGroupNameByGroupId(groupId);
   }

   public List getProgramGroupByUserId(String userId) throws SQLException {
      UserGroupInfo userInfo = UserGroupInfoImpl.getInstance();
      List list = userInfo.getUserManageGroupListByUserId(userId);
      List programGroups = null;
      if (list != null && list.size() > 0) {
         programGroups = new ArrayList();
         Iterator var5 = list.iterator();

         while(var5.hasNext()) {
            UserGroup userGroup = (UserGroup)var5.next();
            List programs = ((ProgramGroupDaoMapper)this.getMapper()).getRootGroupById((String)null, userGroup.getGroup_name());
            if (programs != null && programs.size() > 0) {
               ProgramGroup progrm = (ProgramGroup)programs.get(0);
               programGroups.add(progrm);
            }
         }
      }

      return programGroups;
   }

   public int getProgramGroupTotalCount() throws SQLException {
      return ((ProgramGroupDaoMapper)this.getMapper()).getProgramGroupTotalCount();
   }
}
