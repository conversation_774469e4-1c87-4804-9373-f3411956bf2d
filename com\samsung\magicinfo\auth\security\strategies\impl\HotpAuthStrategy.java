package com.samsung.magicinfo.auth.security.strategies.impl;

import com.eatthepath.otp.HmacOneTimePasswordGenerator;
import com.samsung.common.cache.CacheFactory;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.auth.security.otp.OTPAuthType;
import com.samsung.magicinfo.auth.security.otp.UserAuthDevice;
import com.samsung.magicinfo.auth.security.strategies.AuthStrategy;
import com.samsung.magicinfo.auth.security.strategies.Strategy;
import com.samsung.magicinfo.auth.security.strategies.model.AuthModel;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.setting.model.config.MFA;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.sql.Timestamp;
import java.util.Base64;
import java.util.Calendar;
import java.util.Iterator;
import java.util.List;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;
import org.apache.logging.log4j.Logger;

@Strategy(
   type = AuthStrategy.class
)
public class HotpAuthStrategy implements AuthStrategy {
   public static final int KEYSIZE = 64;
   public static final int DEFAULT_EXPIRED_DAY = 30;
   public static final String DEFAULT_AUTH_DEVICE_NAME = "";
   static Logger logger = LoggingManagerV2.getLogger(HotpAuthStrategy.class);

   public HotpAuthStrategy() {
      super();
   }

   public AuthModel init(AuthModel authModel) {
      try {
         HmacOneTimePasswordGenerator hotp = new HmacOneTimePasswordGenerator();
         KeyGenerator keyGenerator = KeyGenerator.getInstance(hotp.getAlgorithm());
         keyGenerator.init(64);
         Key key = keyGenerator.generateKey();
         String secretKey = Base64.getEncoder().encodeToString(key.getEncoded());
         authModel.setSecretKey(secretKey);
         OTPAuthType otpAuthType = new OTPAuthType();
         otpAuthType.setDeviceKey(authModel.getSecretKey());
         UserInfo userInfo = UserInfoImpl.getInstance();
         otpAuthType.setDeviceId(userInfo.getAuthDeviceId());
         authModel.setOtpAuthType(otpAuthType);
         CacheFactory.getCache().set("INIT_HOTP" + authModel.getUserId(), authModel);
         return authModel;
      } catch (Exception var8) {
         logger.info(var8.getMessage());
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_HOTP_INVALID);
      }
   }

   public AuthModel active(AuthModel authModel) {
      try {
         HmacOneTimePasswordGenerator hotp = new HmacOneTimePasswordGenerator();
         AuthModel initAuthModel = (AuthModel)CacheFactory.getCache().get("INIT_HOTP" + authModel.getUserId());
         Key key = new SecretKeySpec(initAuthModel.getSecretKey().getBytes(StandardCharsets.US_ASCII), "RAW");
         if (hotp.generateOneTimePassword(key, initAuthModel.getOtpAuthType().getDeviceId()) != authModel.getOtp()) {
            throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_HOTP_INVALID);
         } else {
            UserInfo userInfo = UserInfoImpl.getInstance();
            UserAuthDevice userAuthDevice = initAuthModel.getUserAuthDevice();
            userAuthDevice.setUser_id(authModel.getUserId());
            Timestamp currentTime = new Timestamp(System.currentTimeMillis());
            userAuthDevice.setCreate_date(currentTime);
            Calendar cal = Calendar.getInstance();
            cal.setTime(currentTime);
            int expiredDay = 30;
            ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
            List mfaList = serverSetupDao.getServerMfaInfo("STORED_DEVICE");
            if (mfaList != null && mfaList.size() > 0) {
               expiredDay = ((MFA)mfaList.get(0)).getPeriod();
            }

            cal.add(5, expiredDay);
            userAuthDevice.setExpired_date(new Timestamp(cal.getTimeInMillis()));
            userAuthDevice.setSecret_key(initAuthModel.getSecretKey());
            userAuthDevice.setAuth_device_id(initAuthModel.getOtpAuthType().getDeviceId());
            userAuthDevice.setAuth_device_name("");
            userAuthDevice.setAuth_enable(true);
            userInfo.addAuthDeviceInfo(userAuthDevice);
            CacheFactory.getCache().delete("INIT_HOTP" + authModel.getUserId());
            return null;
         }
      } catch (Exception var12) {
         logger.info(var12.getMessage());
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_HOTP_INVALID);
      }
   }

   public boolean valid(AuthModel authModel) {
      try {
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         List mfaList = serverSetupDao.getServerMfaInfo("STORED_DEVICE");
         if (mfaList == null) {
            throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_HOTP_INVALID);
         }

         if (mfaList.size() == 0) {
            throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_HOTP_INVALID);
         }

         if (!((MFA)mfaList.get(0)).getAuth_enable()) {
            throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_HOTP_INVALID);
         }

         HmacOneTimePasswordGenerator hotp = new HmacOneTimePasswordGenerator();
         UserInfo userInfo = UserInfoImpl.getInstance();
         Key key = null;
         List userAuthDeviceList = userInfo.getUserAuthDevice(authModel.getUserId());
         Iterator var8 = userAuthDeviceList.iterator();

         while(var8.hasNext()) {
            UserAuthDevice userAuthDevice = (UserAuthDevice)var8.next();
            key = new SecretKeySpec(userAuthDevice.getSecret_key().getBytes(StandardCharsets.US_ASCII), "RAW");
            if (hotp.generateOneTimePassword(key, userAuthDevice.getAuth_device_id()) == authModel.getOtp()) {
               userInfo.updateUserDeviceByUserId(authModel.getUserId());
               return true;
            }
         }
      } catch (Exception var10) {
         logger.error(var10.getMessage());
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_HOTP_INVALID);
      }

      throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_HOTP_INVALID);
   }
}
