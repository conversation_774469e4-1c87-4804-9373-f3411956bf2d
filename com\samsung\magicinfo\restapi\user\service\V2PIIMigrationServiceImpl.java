package com.samsung.magicinfo.restapi.user.service;

import com.samsung.magicinfo.framework.setup.dao.NotificationHistoryDao;
import com.samsung.magicinfo.framework.setup.dao.SlmLicenseDao;
import com.samsung.magicinfo.framework.setup.entity.CompanyInfoEntity;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManager;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2PIIMigrationService")
@Transactional
public class V2PIIMigrationServiceImpl implements V2PIIMigrationService {
   @Autowired
   private PIIDataManager piiDataManager;
   SlmLicenseDao licenseDao = null;
   NotificationHistoryDao notificationHistoryDao = null;

   public V2PIIMigrationServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority','User Write Authority','User Approval Authority')")
   public void PIIDataMigrator() throws Exception {
      UserInfo userInfo = UserInfoImpl.getInstance();
      List userList = userInfo.getAllUserListToMigrate();

      for(int i = 0; i < userList.size(); ++i) {
         User user = (User)userList.get(i);
         user.setUser_name(this.piiDataManager.encryptData(user.getUser_name(), "name"));
         user.setEmail(this.piiDataManager.encryptData(user.getEmail(), "email"));
         user.setPhone_num(this.piiDataManager.encryptData(user.getPhone_num(), "phone"));
         user.setMobile_num(this.piiDataManager.encryptData(user.getMobile_num(), "phone"));
         userInfo.migrateUser(user);
      }

      SlmLicenseManagerImpl companyInfo = SlmLicenseManagerImpl.getInstance();
      this.licenseDao = new SlmLicenseDao();
      CompanyInfoEntity company = companyInfo.getCompanyInfo();
      if (company != null) {
         company.setPhone(this.piiDataManager.encryptData(company.getPhone(), "phone"));
         company.setAddress(this.piiDataManager.encryptData(company.getAddress(), "address"));
         company.setEmail(this.piiDataManager.encryptData(company.getEmail(), "email"));
         this.licenseDao.setCompanyInfo(company);
      }

      this.notificationHistoryDao = new NotificationHistoryDao();
      List notificationUserHistoryList = this.notificationHistoryDao.getAllNotificationUserHistory();

      for(int i = 0; i < notificationUserHistoryList.size(); ++i) {
         Long historyId = (Long)((Map)notificationUserHistoryList.get(i)).get("history_id");
         String userId = ((Map)notificationUserHistoryList.get(i)).get("user_id").toString();
         String email = ((Map)notificationUserHistoryList.get(i)).get("email").toString();
         this.notificationHistoryDao.setUserNotificationHistory(historyId, userId, this.piiDataManager.encryptData(email, "email"));
      }

   }

   @PreAuthorize("hasAnyAuthority('User Read Authority','User Write Authority','User Approval Authority')")
   public void PIIDataRollBack() throws Exception {
      UserInfo userInfo = UserInfoImpl.getInstance();
      List userList = userInfo.getAllUserListToMigrate();

      for(int i = 0; i < userList.size(); ++i) {
         User user = (User)userList.get(i);
         user.setUser_name(this.piiDataManager.decryptData(user.getUser_name()));
         user.setEmail(this.piiDataManager.decryptData(user.getEmail()));
         user.setPhone_num(this.piiDataManager.decryptData(user.getPhone_num()));
         user.setMobile_num(this.piiDataManager.decryptData(user.getMobile_num()));
         userInfo.migrateUser(user);
      }

      SlmLicenseManagerImpl companyInfo = SlmLicenseManagerImpl.getInstance();
      this.licenseDao = new SlmLicenseDao();
      CompanyInfoEntity company = companyInfo.getCompanyInfo();
      if (company != null) {
         company.setPhone(this.piiDataManager.decryptData(company.getPhone()));
         company.setAddress(this.piiDataManager.decryptData(company.getAddress()));
         company.setEmail(this.piiDataManager.decryptData(company.getEmail()));
         this.licenseDao.setCompanyInfo(company);
      }

      this.notificationHistoryDao = new NotificationHistoryDao();
      List notificationUserHistoryList = this.notificationHistoryDao.getAllNotificationUserHistory();

      for(int i = 0; i < notificationUserHistoryList.size(); ++i) {
         Long historyId = (Long)((Map)notificationUserHistoryList.get(i)).get("history_id");
         String userId = ((Map)notificationUserHistoryList.get(i)).get("user_id").toString();
         String email = ((Map)notificationUserHistoryList.get(i)).get("email").toString();
         this.notificationHistoryDao.setUserNotificationHistory(historyId, userId, this.piiDataManager.decryptData(email));
      }

   }
}
