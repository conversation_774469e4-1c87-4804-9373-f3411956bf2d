package com.samsung.magicinfo.framework.device.service.upload;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.preconfig.entity.DevicePreconfigResultXml;
import com.samsung.magicinfo.framework.device.preconfig.manager.DevicePreconfigInfo;
import com.samsung.magicinfo.framework.device.preconfig.manager.DevicePreconfigInfoImpl;
import com.samsung.magicinfo.protocol.exception.ServiceException;
import com.samsung.magicinfo.protocol.rmql.RMQL;
import com.samsung.magicinfo.protocol.rmql.RMQLDriver;
import com.samsung.magicinfo.protocol.rmql.ResultSet;
import com.samsung.magicinfo.protocol.servicemanager.ServiceOpActivity;
import com.samsung.magicinfo.protocol.util.RMQLDriverUtil;
import com.samsung.magicinfo.protocol.util.RMQLInstanceCreator;
import com.samsung.magicinfo.protocol.util.TimeUtil;
import java.util.HashMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;

public class DeviceUploadServiceActivity extends ServiceOpActivity {
   Logger logger = LoggingManagerV2.getLogger(DeviceUploadServiceActivity.class);
   private ResultSet rs;

   public DeviceUploadServiceActivity() {
      super();
   }

   public Object process(HashMap params) throws ServiceException {
      try {
         this.rs = (ResultSet)params.get("resultset");
         String deviceId = this.rs.getAttribute("DEVICE_ID");
         String eventPath = this.rs.getAttribute("MO_EVENT");
         String eventID = this.rs.getAttribute("EVENT_ID");
         if (deviceId == null) {
            throw new ServiceException("No device_sn found");
         } else if (eventPath == null) {
            throw new ServiceException("No mo_event found");
         } else if (eventID == null) {
            throw new ServiceException("No event_id found");
         } else if (this.rs == null) {
            throw new ServiceException("No result found");
         } else {
            String type = null;
            String filename = null;
            String status = null;
            Long filesize = null;
            if (StringUtils.isNotEmpty(this.rs.getString(".MO.MONITORING_INFO.UPLOAD.TYPE"))) {
               type = this.rs.getString(".MO.MONITORING_INFO.UPLOAD.TYPE");
               if (StringUtils.isNotEmpty(this.rs.getString(".MO.MONITORING_INFO.UPLOAD.FILE_NAME"))) {
                  filename = this.rs.getString(".MO.MONITORING_INFO.UPLOAD.FILE_NAME");
                  if (StringUtils.isNotEmpty(this.rs.getString(".MO.MONITORING_INFO.UPLOAD.FILE_SIZE"))) {
                     filesize = this.rs.getLong(".MO.MONITORING_INFO.UPLOAD.FILE_SIZE");
                     if (StringUtils.isNotEmpty(this.rs.getString(".MO.MONITORING_INFO.UPLOAD.STATUS"))) {
                        status = this.rs.getString(".MO.MONITORING_INFO.UPLOAD.STATUS");
                        Device device = DeviceInfoImpl.getInstance().getDevice(deviceId);
                        if ("PRE_CONFIG_RESULT".equals(type) && "SUCCESS".equals(status)) {
                           DevicePreconfigInfo preconfigInfo = DevicePreconfigInfoImpl.getInstance();

                           try {
                              DevicePreconfigResultXml result = preconfigInfo.getPreconfigResultXml(deviceId);
                              int lastIndex = result.getVersion().lastIndexOf(45);
                              String preconfigId = result.getVersion().substring(0, lastIndex);
                              preconfigInfo.setDeployStatusReportTime(preconfigId, deviceId, TimeUtil.getCurrentGMTTime());
                           } catch (Exception var14) {
                              this.logger.error("Error during getting Preconfig Result XML" + deviceId);
                           }
                        }

                        RMQL rmql = RMQLInstanceCreator.getInstance(device, "NOTIFY", (Long)null);
                        rmql.addParam("RESULT", "SUCCESS");
                        RMQLDriver driver = RMQLDriverUtil.getWSRMQLDriver();
                        return driver.createAppBOForResponse(rmql);
                     } else {
                        throw new ServiceException("Upload status is required");
                     }
                  } else {
                     throw new ServiceException("Upload file size is required");
                  }
               } else {
                  throw new ServiceException("Upload file name is required");
               }
            } else {
               throw new ServiceException("Upload type is required");
            }
         }
      } catch (Exception var15) {
         throw new ServiceException(var15);
      }
   }
}
