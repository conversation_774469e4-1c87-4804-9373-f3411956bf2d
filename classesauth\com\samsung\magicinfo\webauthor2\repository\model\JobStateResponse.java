package com.samsung.magicinfo.webauthor2.repository.model;

import org.springframework.http.HttpStatus;

public class JobStateResponse {
  private HttpStatus statusCode;
  
  private String redirectUrl;
  
  public JobStateResponse(HttpStatus statusCode, String redirectUrl) {
    this.statusCode = statusCode;
    this.redirectUrl = redirectUrl;
  }
  
  public HttpStatus getStatusCode() {
    return this.statusCode;
  }
  
  public String getRedirectUrl() {
    return this.redirectUrl;
  }
}
