package com.samsungcms.kpimagicinfo.test;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import com.fasterxml.jackson.module.jsonSchema.JsonSchemaGenerator;
import com.samsungcms.kpimagicinfo.model.KpiPolicy;

public class JSONSchemaTest {
  public static void main(String[] args) {
    try {
      ObjectMapper mapper = new ObjectMapper();
      JsonSchemaGenerator schemaGen = new JsonSchemaGenerator(mapper);
      JsonSchema schema = schemaGen.generateSchema(KpiPolicy.class);
      String jsonSchemaAsString = mapper.writeValueAsString(schema);
      System.out.println(jsonSchemaAsString);
    } catch (JsonMappingException e) {
      e.printStackTrace();
    } catch (JsonProcessingException e) {
      e.printStackTrace();
    } 
  }
}
