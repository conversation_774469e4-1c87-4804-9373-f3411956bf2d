package com.samsung.magicinfo.restapi.user.service;

import com.samsung.common.cache.CacheFactory;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.UserConstants;
import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.export.PdfBuilder;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.ExternalSystemUtils;
import com.samsung.common.utils.RoleUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.content.dao.LogDao;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.LogInfo;
import com.samsung.magicinfo.framework.content.manager.LogInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceGroupDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceMenuManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceMenuManagerImpl;
import com.samsung.magicinfo.framework.mu.manager.OrganizationGroupInfo;
import com.samsung.magicinfo.framework.mu.manager.OrganizationGroupInfoImpl;
import com.samsung.magicinfo.framework.role.entity.Role;
import com.samsung.magicinfo.framework.role.manager.RoleInfo;
import com.samsung.magicinfo.framework.role.manager.RoleInfoImpl;
import com.samsung.magicinfo.framework.setup.dao.LdapServerDao;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserLogManager;
import com.samsung.magicinfo.framework.user.manager.UserLogManagerImpl;
import com.samsung.magicinfo.framework.user.manager.UserPasswordManager;
import com.samsung.magicinfo.framework.user.manager.UserPasswordManagerImpl;
import com.samsung.magicinfo.protocol.entity.Mail;
import com.samsung.magicinfo.protocol.util.MailTemplet;
import com.samsung.magicinfo.protocol.util.MailUtil;
import com.samsung.magicinfo.protocol.util.mail.MailManager;
import com.samsung.magicinfo.protocol.util.mail.MailManagerInterface;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonDeleteFail;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonOrganizationData;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGroupTreeResource;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManager;
import com.samsung.magicinfo.restapi.user.model.V2AdminInfoResource;
import com.samsung.magicinfo.restapi.user.model.V2AllQueryUserListsFilter;
import com.samsung.magicinfo.restapi.user.model.V2ApprovalResource;
import com.samsung.magicinfo.restapi.user.model.V2CheckIdDuplicationResource;
import com.samsung.magicinfo.restapi.user.model.V2CurrentLocaleUserResource;
import com.samsung.magicinfo.restapi.user.model.V2CurrentOrgUserResource;
import com.samsung.magicinfo.restapi.user.model.V2DashboardUserInfoResource;
import com.samsung.magicinfo.restapi.user.model.V2EditUserFilter;
import com.samsung.magicinfo.restapi.user.model.V2EditUserResource;
import com.samsung.magicinfo.restapi.user.model.V2ErrorMessageResource;
import com.samsung.magicinfo.restapi.user.model.V2MeResource;
import com.samsung.magicinfo.restapi.user.model.V2MultiOrganizationInfoResource;
import com.samsung.magicinfo.restapi.user.model.V2OrganizationId;
import com.samsung.magicinfo.restapi.user.model.V2ResetPasswordResource;
import com.samsung.magicinfo.restapi.user.model.V2RoleListQueryDataResource;
import com.samsung.magicinfo.restapi.user.model.V2SaveUserResource;
import com.samsung.magicinfo.restapi.user.model.V2UserDeleteResource;
import com.samsung.magicinfo.restapi.user.model.V2UserEncyptionTokenResource;
import com.samsung.magicinfo.restapi.user.model.V2UserExportResource;
import com.samsung.magicinfo.restapi.user.model.V2UserInfoResource;
import com.samsung.magicinfo.restapi.user.model.V2UserInfoResource4Password;
import com.samsung.magicinfo.restapi.user.model.V2UserPasswordEncyptionTokenResource;
import com.samsung.magicinfo.restapi.user.model.V2UserResource;
import com.samsung.magicinfo.restapi.user.model.V2UserWithdrawalResource;
import com.samsung.magicinfo.restapi.user.utils.UserUtils;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.restapi.utils.V2TokenUtils;
import com.samsung.magicinfo.service.statistics.DeviceStatisticsDownloadService;
import com.samsung.magicinfo.service.user.LDAPService;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import net.spy.memcached.CASValue;
import org.apache.commons.lang3.StringUtils;
import org.apache.ftpserver.ftplet.FtpException;
import org.apache.ftpserver.ftplet.UserManager;
import org.apache.ftpserver.usermanager.DbUserManagerFactory;
import org.apache.ftpserver.usermanager.impl.BaseUser;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

@Service("V2UserService")
@Transactional
public class V2UserServiceImpl implements V2UserService {
   protected Logger logger = LoggingManagerV2.getLogger(V2UserServiceImpl.class);
   public static final String ROOT = "ROOT";
   public static final String ADMINISTRATORS = "Administrators";
   public static final String YES = "Y";
   public static final String NO = "N";
   public static final String USER_DASHBOARD = "userInfoDashboard";
   public static final String ORGANIZATION = "organization";
   public static final String TOTAL_IN_COUNT = "totalInCount";
   public static final String TOTAL_OUT_COUNT = "totalOutCount";
   public static final String UNAPPROVED_COUNT = "unapprovedCount";
   public static final String ORGANIZATION_LIST = "organizationList";
   private DeviceStatisticsDownloadService downloadService = null;
   private static Map errorList = init();
   @Autowired
   private V2UserMfaService v2UserMfaService;
   @Autowired
   private V2RoleService v2RoleService;
   @Autowired
   private PIIDataManager piiDataManager;

   public V2UserServiceImpl() {
      super();
   }

   public void setDownloadService(DeviceStatisticsDownloadService downloadService) {
      this.downloadService = downloadService;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody checkDuplicatedUser(String userId) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      UserInfo userInfo = UserInfoImpl.getInstance();
      String ldapFullId = "";

      try {
         if (ldapFullId != null && !ldapFullId.equals("")) {
            if (userInfo.getCountByUserIdForCheck(userId) != 0 && userInfo.getCountByLDAPUserFullIdForCheck(ldapFullId) == 0) {
            }
         } else if (userInfo.getCountByUserIdForCheck(userId) == 0) {
            responseBody.setStatus("Success");
            return responseBody;
         }
      } catch (Exception var6) {
         this.logger.error("", var6);
      }

      responseBody.setStatus("Fail");
      responseBody.setErrorCode(ExceptionCode.RES903[0]);
      responseBody.setErrorMessage(ExceptionCode.RES903[2]);
      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody checkId(String userId) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      String user_id = StrUtils.nvl(userId);
      LinkedHashMap data = new LinkedHashMap();

      try {
         UserInfo userInfo = UserInfoImpl.getInstance();
         data.put("check", userInfo.getCountByUserIdForCheck(user_id));
      } catch (Exception var6) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var6.getMessage());
      }

      responseBody.setItems(data);
      responseBody.setStatus("Success");
      return responseBody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody getUserOrganization() {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      LinkedHashMap data = new LinkedHashMap();

      try {
         UserInfo userInfo = UserInfoImpl.getInstance();
         List list = userInfo.getOrganization();
         data.put("organizationList", list);
      } catch (Exception var5) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var5.getMessage());
      }

      responseBody.setItems(data);
      responseBody.setStatus("Success");
      return responseBody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody getAllOrganizationGroup() {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      LinkedHashMap result = new LinkedHashMap();

      try {
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         userGroupInfo.getAllOrganizationGroup();
         List orgList = userGroupInfo.getAllOrganizationGroup();
         result.put("org_list", orgList);
      } catch (Exception var5) {
         this.logger.error(var5);
      }

      responseBody.setItems(result);
      responseBody.setStatus("Success");
      return responseBody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody getLdapOrgList(String organization) throws SQLException, Exception {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      organization = StrUtils.nvl(organization);
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      LinkedHashMap result = new LinkedHashMap();
      ServerSetupInfo serverSetupInfo = ServerSetupInfoImpl.getInstance();
      List ldapUserList = null;
      long selectedOrgId = userGroupInfo.getOrgGroupIdByName(organization);
      if (serverSetupInfo.isLdapEnable(selectedOrgId)) {
         ldapUserList = LDAPService.getLDAPUserList(selectedOrgId);
         if (ldapUserList == null) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_LDAP_ORGANIZATION);
         } else {
            result.put("ldapUserList", ldapUserList);
            responseBody.setStatus("Success");
            responseBody.setItems(result);
            responseBody.setStatus("Success");
            return responseBody;
         }
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NOT_USE_LDAP);
      }
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody searchLDAPUser(String userId, String ldapLocation, String searchID, String orgName) throws SQLException {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      LinkedHashMap result = new LinkedHashMap();
      ldapLocation = StrUtils.nvl(ldapLocation);
      searchID = StrUtils.nvl(searchID);
      orgName = StrUtils.nvl(orgName);
      long selectedOrgId = userGroupInfo.getOrgGroupIdByName(orgName);
      List ldapUserList = null;
      ldapUserList = LDAPService.getSearchLDAPUserList(selectedOrgId, ldapLocation, searchID);
      responseBody.setStatus("Success");
      result.put("ldapUserList", ldapUserList);
      responseBody.setItems(result);
      return responseBody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody getOrganization() {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      LinkedHashMap result = new LinkedHashMap();

      try {
         UserContainer userContainer = SecurityUtils.getUserContainer();
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         User loginUser = userContainer.getUser();
         if (loginUser.getRoot_group_id() == 0L) {
            List manageGroupList = userGroupInfo.getUserManageGroupListByUserId(loginUser.getUser_id());
            if (manageGroupList != null && manageGroupList.size() > 0) {
               result.put("group_list", manageGroupList);
            } else {
               List organList = userGroupInfo.getAllOrganizationGroup();
               result.put("group_list", organList);
            }
         } else {
            UserGroup group = userGroupInfo.getGroupById(loginUser.getRoot_group_id());
            List organList = new ArrayList();
            organList.add(group);
            result.put("group_list", organList);
         }

         responseBody.setStatus("Success");
      } catch (Exception var8) {
         responseBody.setStatus("Fail");
      }

      responseBody.setItems(result);
      return responseBody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody addOrganization(boolean canWriteUser) throws Exception {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      LinkedHashMap result = new LinkedHashMap();
      LdapServerDao ldapServerDao = new LdapServerDao();
      if (canWriteUser) {
         boolean ldapStatus = ldapServerDao.getLdapServerStatus(0L);
         List ldapUserList = null;
         if (ldapStatus) {
            ldapUserList = LDAPService.getLDAPUserList(0L);
         }

         if (ldapUserList != null) {
            result.put("ldapStatus", ldapStatus);
            result.put("ldapUserList", ldapUserList);
         }

         responseBody.setStatus("Success");
         responseBody.setItems(result);
         return responseBody;
      } else {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_NOT_HAVE, new String[]{"write"});
      }
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority')")
   public V2PageResource allQueryUserLists(V2AllQueryUserListsFilter filter) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      this.logger.debug("..........UserListAjaxController Started.............." + new Date());
      Long groupId = userContainer.getUser().getRoot_group_id();
      String sort = filter.getSortColumn();
      String sortOrder = filter.getSortOrder();
      String userStatus = filter.getUserStatus();
      if (sort == null || sort.equals("")) {
         sort = "USER_ID";
      }

      if (userStatus.equals("NONAPPROVED") && !sort.equalsIgnoreCase("APPROVAL_TYPE") && !sort.equalsIgnoreCase("USER_ID") && !sort.equalsIgnoreCase("USER_NAME") && !sort.equalsIgnoreCase("ORGANIZATION")) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"sortColumn"});
      } else if (userStatus.equals("WITHDRAWN") && !sort.equalsIgnoreCase("USER_ID") && !sort.equalsIgnoreCase("USER_NAME") && !sort.equalsIgnoreCase("WITHDRAWAL_DATE")) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"sortColumn"});
      } else if (userStatus.equals("APPROVED") && !sort.equalsIgnoreCase("USER_ID") && !sort.equalsIgnoreCase("USER_NAME") && !sort.equalsIgnoreCase("ORGANIZATION") && !sort.equalsIgnoreCase("GROUP_NAME") && !sort.equalsIgnoreCase("ROLE_NAME") && !sort.equalsIgnoreCase("LDAP_USER_ID") && !sort.equalsIgnoreCase("CREATE_DATE")) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"sortColumn"});
      } else {
         if (filter.getGroupId() != null && filter.getGroupId() != 0L) {
            groupId = filter.getGroupId();
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, groupId);
         }

         String searchText = StrUtils.nvl(filter.getSearchText());
         String organizationId = filter.getOrganizationId();
         String roleName = filter.getRoleName();
         String startCreatedDate = filter.getStartCreatedDate();
         String endCreatedDate = filter.getEndCreatedDate();
         int pageSize = filter.getPageSize();
         int startIndex = filter.getStartIndex();
         if (organizationId != null && !organizationId.equalsIgnoreCase("")) {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, Long.valueOf(organizationId));
         }

         String pUserId = SecurityUtils.getLoginUserId();
         UserInfo userInfo = UserInfoImpl.getInstance();
         User pUser = userInfo.getAllByUserId(pUserId);
         Long root_group_id = pUser.getRoot_group_id();
         ListManager listMgr = new ListManager(userInfo, "list");
         listMgr.addSearchInfo("sortColumn", sort);
         listMgr.addSearchInfo("sortOrder", sortOrder);
         searchText = searchText.replaceAll("\\[", "^[");
         searchText = searchText.replaceAll("]", "^]");
         searchText = searchText.replaceAll("%", "^%");
         searchText = searchText.toUpperCase();
         listMgr.addSearchInfo("searchText", "");
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         String organization;
         UserGroup userGroup;
         if (userContainer.getUser().isMu()) {
            userGroup = userGroupInfo.getGroupById(Long.valueOf(userContainer.getUser().getRoot_group_id()));
            organization = userGroup.getGroup_name();
         } else {
            organization = pUser.getOrganization();
         }

         listMgr.addSearchInfo("organization", organization);
         userGroup = userGroupInfo.getGroupById(groupId);
         List pageMgr;
         if (userGroup.getP_group_id() == 0L) {
            pageMgr = userGroupInfo.getChildGroupList(groupId, true);
            List idList = new ArrayList();
            Iterator var24 = pageMgr.iterator();

            while(var24.hasNext()) {
               UserGroup group = (UserGroup)var24.next();
               idList.add(group.getGroup_id());
            }

            listMgr.addSearchInfo("groupIds", idList);
         } else {
            listMgr.addSearchInfo("group_id", groupId.toString());
         }

         listMgr.addSearchInfo("role_name", roleName);
         listMgr.addSearchInfo("search_start_date", startCreatedDate);
         listMgr.addSearchInfo("search_end_date", endCreatedDate);
         listMgr.setLstSize(Integer.valueOf(pageSize));
         String var47 = userStatus.toUpperCase();
         byte var49 = -1;
         switch(var47.hashCode()) {
         case -591108476:
            if (var47.equals("WITHDRAWN")) {
               var49 = 2;
            }
            break;
         case 1011411774:
            if (var47.equals("GROUPED")) {
               var49 = 0;
            }
            break;
         case 1652394084:
            if (var47.equals("NONAPPROVED")) {
               var49 = 1;
            }
         }

         switch(var49) {
         case 0:
            listMgr.setSection("getGroupedUserList");
            break;
         case 1:
            listMgr.setSection("getNonApprovedUserList");
            break;
         case 2:
            listMgr.setSection("getWithdrawalUserList");
            break;
         default:
            if (organizationId != null && !organizationId.equalsIgnoreCase("")) {
               listMgr.addSearchInfo("root_group_id", Long.valueOf(organizationId));
            } else {
               listMgr.addSearchInfo("root_group_id", root_group_id);
            }

            listMgr.setSection("getUserList");
         }

         this.logger.debug("AjaxController before query.............." + new Date());
         pageMgr = null;
         List userList = listMgr.V2dbexecute(startIndex, pageSize);
         int offSet = 0;
         if (startIndex > 0) {
            offSet = startIndex - 1;
         }

         List offSetList = new ArrayList();
         if (!searchText.equals("")) {
            List updatedUserList = new ArrayList();

            for(int i = 0; i < ((List)userList).size(); ++i) {
               String decryptedName = this.piiDataManager.decryptData(((User)((List)userList).get(i)).getUser_name());
               ((User)((List)userList).get(i)).setUser_name(decryptedName);
               if (((User)((List)userList).get(i)).getUser_id().toUpperCase().contains(searchText) || decryptedName.toUpperCase().contains(searchText)) {
                  updatedUserList.add(((List)userList).get(i));
               }
            }

            userList = updatedUserList;
         }

         int totalRows = ((List)userList).size();
         if (((List)userList).size() > offSet) {
            offSetList = ((List)userList).subList(offSet, Math.min(offSet + pageSize, ((List)userList).size()));
         }

         userList = offSetList;
         PageManager pageMgr = listMgr.getPageManager();
         this.logger.debug("AjaxController after query.............." + new Date());
         this.logger.debug("AjaxController before making json data.............." + new Date());
         String adminGroupName = "Administrators";
         List dataList = new ArrayList();

         for(int i = 0; i < ((List)userList).size(); ++i) {
            V2UserResource data = new V2UserResource();
            V2UserInfoResource userInfoData = new V2UserInfoResource();
            V2MultiOrganizationInfoResource multiOrganizationInfoData = new V2MultiOrganizationInfoResource();
            User user = (User)((List)userList).get(i);
            String groupName = user.getGroup_name();
            userInfoData.setUserId(user.getUser_id());
            userInfoData.setUserName(UserUtils.getUnRecognizedData(this.piiDataManager.decryptData(user.getUser_name())));
            data.setUserInfo(userInfoData);
            boolean isMultiOrganizationManager = false;
            String organizationGroupName = "";
            String approvalStr;
            if (user.isMu()) {
               isMultiOrganizationManager = true;
               approvalStr = user.getUser_id();
               OrganizationGroupInfo organizationGroupInfo = OrganizationGroupInfoImpl.getInstance();
               organizationGroupName = userGroupInfo.getCurMngOrgGroup(approvalStr);
               if (organizationGroupName != null) {
                  List mngOrgIdList = organizationGroupInfo.getMngOrgIdListByUserId(approvalStr);
                  userGroupInfo.getCurMngOrgGroup(approvalStr);
                  Long currentOrganizationId = userInfo.getCurMngOrgId(approvalStr);
                  String currentOrganizationName = userInfo.getOrganGroupName(currentOrganizationId);
                  if (!StrUtils.isEmpty(currentOrganizationName)) {
                     Long organizationGroupId = organizationGroupInfo.getOrganizationGroupIdByOrganizationGroupName(organizationGroupName);
                     multiOrganizationInfoData.setOrganizationGroupId(organizationGroupId);
                     multiOrganizationInfoData.setOrganizationGroupName(organizationGroupName);
                     multiOrganizationInfoData.setCurrentOrganizationId(currentOrganizationId);
                     multiOrganizationInfoData.setCurrentOrganizationName(currentOrganizationName);
                     List orgList = new ArrayList();
                     Iterator var44 = mngOrgIdList.iterator();

                     while(var44.hasNext()) {
                        Long orgId = (Long)var44.next();
                        V2CommonOrganizationData orgData = new V2CommonOrganizationData();
                        orgData.setOrganizationId(orgId);
                        orgData.setOrganizationName(userGroupInfo.getGroupNameByGroupId(orgId));
                        orgList.add(orgData);
                     }

                     multiOrganizationInfoData.setOrganizationList(orgList);
                     data.setMultiOrganizationInfo(multiOrganizationInfoData);
                  }
               }
            }

            data.setIsMultiOrganizationManager(isMultiOrganizationManager);
            data.setCurMngOrg("ROOT".equals(user.getOrganization()) && StringUtils.isEmpty(organizationGroupName) ? "ALL" : organizationGroupName);
            if (userStatus.equalsIgnoreCase("NONAPPROVED")) {
               approvalStr = "";
               if (user.getApproval_type().equals("TEXT_MOVE_ORGAN_P")) {
                  approvalStr = "MOVE ORGANIZATION";
               } else if (user.getApproval_type().equals("TEXT_NEW_P")) {
                  approvalStr = "NEW";
               }

               data.setUnApprovedCause(approvalStr);
            } else if (userStatus.equalsIgnoreCase("WITHDRAWN")) {
               data.setWithdrawalDate(user.getWithdrawal_date());
            } else {
               if (groupName.equals("ROOT")) {
                  groupName = adminGroupName;
               }

               data.setGroupName(groupName);
               data.setRoleName(user.getRole_name().replaceAll("<", "&lt"));
               boolean device_permission = DeviceUtils.isDeviceGroupAuth(user);
               data.setHasDevicePermission(device_permission);
               String ldapId = user.getLdap_user_id();
               if (StringUtils.isBlank(ldapId)) {
                  ldapId = "";
               }

               data.setLdapId(ldapId);
               data.setJoinDate(user.getCreate_date());
            }

            data.setOrganizationName(user.getOrganization());
            dataList.add(data);
         }

         V2PageResource resource = V2PageResource.createPageResource(dataList, pageMgr);
         if (!searchText.equals("")) {
            resource = V2PageResource.createPageResourceForSearch(dataList, pageMgr, totalRows);
         }

         this.logger.debug("AjaxController after making json data.............." + new Date());
         return resource;
      }
   }

   public V2CurrentOrgUserResource currentMngOrganization(V2OrganizationId body) throws Exception {
      Long organizationId = body.getOrganizationId();
      V2CurrentOrgUserResource resource = new V2CurrentOrgUserResource();
      UserContainer container = SecurityUtils.getUserContainer();
      String mngOrgName = "";
      UserInfo userInfo = UserInfoImpl.getInstance();
      String userId = container.getUser().getUser_id();
      if (!container.getUser().isMu()) {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_USER_NOT_ID_MU);
      } else {
         OrganizationGroupInfo organizationGroupInfo = OrganizationGroupInfoImpl.getInstance();
         List mngOrgIdList = organizationGroupInfo.getMngOrgIdListByUserId(userId);
         if (!mngOrgIdList.contains(organizationId)) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_MU_ORGANIZATION);
         } else {
            try {
               userInfo.setCurMngOrgId(userId, organizationId);
               container.getUser().setRoot_group_id(organizationId);
               UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
               mngOrgName = userGroupInfo.getGroupNameByGroupId(organizationId);
               container.getUser().setOrganization(mngOrgName);
               ContentInfo contentInfo = ContentInfoImpl.getInstance();
               contentInfo.addDefaultGroup(userId, organizationId);
               PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
               playlistInfo.addDefaultGroup(userId, organizationId);
               V2TokenUtils.updateUserInfoOnUserContainer(SecurityUtils.getUserContainer().getUser());
            } catch (Exception var13) {
               this.logger.error("", var13);
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
            }

            resource.setCurrentOrganizationName(mngOrgName);
            resource.setCurrentOrganizationId(organizationId);
            return resource;
         }
      }
   }

   public V2CurrentLocaleUserResource currentMngLocale(String locale) throws Exception {
      V2CurrentLocaleUserResource resource = new V2CurrentLocaleUserResource();
      locale = StrUtils.nvl(locale).equals("") ? "en" : locale;
      Locale setLocale = new Locale("en");
      UserInfo userInfo = UserInfoImpl.getInstance();
      String userId = SecurityUtils.getUserContainer().getUser().getUser_id();
      String[] languages = new String[]{"de", "en", "es", "fr", "it", "ja", "ko", "pt", "ru", "sv", "tr", "zh", "ar", "fa", "pl", "vi", "fi"};
      String[] var7 = languages;
      int var8 = languages.length;

      for(int var9 = 0; var9 < var8; ++var9) {
         String compareStr = var7[var9];
         if (locale.startsWith(compareStr)) {
            if ((locale.equals("zh_CN") || locale.equals("zh_cn") || locale.equals("zh_TW") || locale.equals("zh_tw")) && locale.indexOf("_") > 0) {
               String[] zhLocale = locale.split("_");
               setLocale = new Locale("zh", zhLocale[1]);
            } else {
               byte var12 = -1;
               switch(locale.hashCode()) {
               case 3121:
                  if (locale.equals("ar")) {
                     var12 = 2;
                  }
                  break;
               case 3201:
                  if (locale.equals("de")) {
                     var12 = 3;
                  }
                  break;
               case 3241:
                  if (locale.equals("en")) {
                     var12 = 1;
                  }
                  break;
               case 3246:
                  if (locale.equals("es")) {
                     var12 = 4;
                  }
                  break;
               case 3259:
                  if (locale.equals("fa")) {
                     var12 = 5;
                  }
                  break;
               case 3267:
                  if (locale.equals("fi")) {
                     var12 = 15;
                  }
                  break;
               case 3276:
                  if (locale.equals("fr")) {
                     var12 = 6;
                  }
                  break;
               case 3371:
                  if (locale.equals("it")) {
                     var12 = 7;
                  }
                  break;
               case 3383:
                  if (locale.equals("ja")) {
                     var12 = 8;
                  }
                  break;
               case 3428:
                  if (locale.equals("ko")) {
                     var12 = 0;
                  }
                  break;
               case 3580:
                  if (locale.equals("pl")) {
                     var12 = 13;
                  }
                  break;
               case 3588:
                  if (locale.equals("pt")) {
                     var12 = 9;
                  }
                  break;
               case 3651:
                  if (locale.equals("ru")) {
                     var12 = 10;
                  }
                  break;
               case 3683:
                  if (locale.equals("sv")) {
                     var12 = 11;
                  }
                  break;
               case 3710:
                  if (locale.equals("tr")) {
                     var12 = 12;
                  }
                  break;
               case 3763:
                  if (locale.equals("vi")) {
                     var12 = 14;
                  }
               }

               switch(var12) {
               default:
                  locale = "en";
               case 0:
               case 1:
               case 2:
               case 3:
               case 4:
               case 5:
               case 6:
               case 7:
               case 8:
               case 9:
               case 10:
               case 11:
               case 12:
               case 13:
               case 14:
               case 15:
                  setLocale = new Locale(locale);
               }
            }
            break;
         }
      }

      try {
         userInfo.setLocale(userId, setLocale.toString());
         SecurityUtils.getUserContainer().getUser().setLocale(setLocale.toString());
         V2TokenUtils.updateUserInfoOnUserContainer(SecurityUtils.getUserContainer().getUser());
         resource.setLocale(setLocale.toString());
         resource.setUserId(userId);
         return resource;
      } catch (Exception var13) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      }
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2SaveUserResource saveUser(HttpServletRequest request, V2SaveUserResource resource) throws Exception {
      UserLogManager logMgr = UserLogManagerImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      AbilityUtils ability = new AbilityUtils();
      boolean canWriteUser = ability.checkAuthority("User Write");
      String adminGroupName = "Administrators";
      String userId = StrUtils.nvl(resource.getUserInfo().getUserId());
      if (userId.length() >= 3 && userId.length() <= 64) {
         Long groupId = resource.getGroupId();
         if (groupId != null && userId != "") {
            RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, groupId);
         }

         String password = StrUtils.nvl(resource.getUserInfo().getPassword());
         String team = StrUtils.nvl(resource.getUserInfo().getTeam());
         String ldapFullId = resource.getLdapFullId();
         String nickname = resource.getNickname();
         String userName = resource.getUserInfo().getUserName();
         String email = resource.getUserInfo().getEmail();
         String phoneNum = resource.getUserInfo().getPhoneNum();
         String mobileNum = resource.getUserInfo().getMobileNum();
         String roleName = resource.getRoleName();
         String groupName = resource.getGroupName();
         String jobPosition = resource.getUserInfo().getJobPosition();
         String orgGroupName = resource.getOrganizationGroupName();
         String organization = resource.getOrganizationName();
         User loginUser = SecurityUtils.getUserContainer().getUser();
         if (loginUser != null && canWriteUser && (RoleUtils.isServerAdminRole(loginUser) || roleName != null && !RoleUtils.isServerAdminRole(roleName))) {
            if (!loginUser.getIs_admin().equals("Y") && !loginUser.getOrganization().equals("ROOT")) {
               if (!loginUser.getOrganization().equals(organization)) {
                  throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
               }
            } else {
               groupName = userGroupInfo.getGroupNameByGroupId(groupId);
               if (organization.equals(adminGroupName)) {
                  organization = "ROOT";
                  groupName = "ROOT";
               }
            }

            if (RoleUtils.isServerAdminRole(roleName)) {
               if (!organization.equals("ROOT") || !groupName.equals("ROOT")) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SERVER_ADMIN_ORGANIZATION_NAME_N_GROUP_NAME_ROOT);
               }

               if (!StrUtils.nvl(orgGroupName).equals("")) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SERVER_ADMIN_CANNOT_HAVE_ORGANIZATION_GROUP);
               }
            }

            User user = new User();
            RoleInfo roleDao = RoleInfoImpl.getInstance();
            long roleId = roleDao.getRoleIdByRoleName(roleName);
            if (!this.checkAssignableRoles(roleName)) {
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
            } else {
               if (StringUtils.isNotEmpty(ldapFullId)) {
                  String ldapSimpleId = StrUtils.nvl(resource.getLdapSimpleId()).equals("") ? "" : resource.getLdapSimpleId();
                  if (StringUtils.isBlank(ldapSimpleId)) {
                     ldapSimpleId = userId;
                  }

                  if (nickname != null && !nickname.equals("")) {
                     user.setUser_id(nickname);
                     user.setLdap_user_id(ldapSimpleId);
                     userId = nickname;
                  } else {
                     user.setUser_id(userId);
                     user.setLdap_user_id(ldapSimpleId);
                  }
               } else {
                  user.setUser_id(userId);
                  user.setLdap_user_id("");
               }

               user.setUser_name(this.piiDataManager.encryptData(userName, "name"));
               if (password == null || password.equals("")) {
                  password = "LDAP";
               }

               user.setPassword(password);
               user.setEmail(this.piiDataManager.encryptData(email, "email"));
               user.setPhone_num(this.piiDataManager.encryptData(phoneNum, "phone"));
               user.setMobile_num(this.piiDataManager.encryptData(mobileNum, "phone"));
               user.setRole_name(roleName);
               user.setGroup_name(groupName);
               user.setOrganization(organization);
               user.setTeam(team);
               user.setJob_position(jobPosition);
               user.setIs_approved("Y");
               user.setIs_deleted("N");
               user.setRoot_group_id(userGroupInfo.getOrgGroupIdByName(organization));
               user.setIs_reset_pwd("Y");
               user.setLdap_info(ldapFullId);
               if (ldapFullId == null || ldapFullId.equals("")) {
                  UserPasswordManager userPassword = UserPasswordManagerImpl.getInstance();
                  String rtn = userPassword.matchPasswordCheck(user.getUser_id(), user.getPassword(), user.getPassword(), user);
                  if (!"success".equals(rtn)) {
                     throw new Exception(rtn);
                  }
               }

               int resultCnt = roleDao.getCntRuleManagerAbilityByRoleId(roleId);
               if (resultCnt > 0 && !organization.equals("ROOT")) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_ASSIGN_RULEMANAGER_ONLY_ADMIN_GROUP);
               } else {
                  userInfo.addUser(user, groupId, true);
                  BaseUser ftpUser = new BaseUser();
                  ftpUser.setName(userId);
                  ftpUser.setPassword(password);
                  UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
                  userMgr.save(ftpUser);
                  ContentInfo contentInfo = ContentInfoImpl.getInstance();
                  contentInfo.addDefaultGroup(userId);
                  PlaylistInfo playlintInfo = PlaylistInfoImpl.getInstance();
                  playlintInfo.addDefaultGroup(userId);
                  DeviceMenuManager menuDao = DeviceMenuManagerImpl.getInstance();
                  menuDao.addDeviceMapMenuUser(userId);
                  if (CommonConfig.get("rulemanager.enable") != null && CommonConfig.get("rulemanager.enable").equals("true")) {
                     ExternalSystemUtils exSystemUtils = new ExternalSystemUtils();
                     exSystemUtils.ruleManagerAddUser(userId);
                  }

                  if (orgGroupName != null && !orgGroupName.isEmpty()) {
                     userGroupInfo.setCurMngOrgGroup(userId, orgGroupName);
                     userInfo.setMUByUserId(userId, "Y");
                  }

                  return resource;
               }
            }
         } else {
            throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
         }
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_USERID_LENGTH_INCORRECT);
      }
   }

   private List checkMUUserResoruce(String userId, String organizationGroupName) throws Exception {
      OrganizationGroupInfo organizationGroupInfo = OrganizationGroupInfoImpl.getInstance();
      List originOrgIds = organizationGroupInfo.getMngOrgIdListByUserId(userId);
      List targetOrgIdList = organizationGroupInfo.getMngOrgIdListByOrganizationGroupName(organizationGroupName);
      List checkOrgIds = originOrgIds;
      if (StrUtils.isNotEmpty(organizationGroupName)) {
         checkOrgIds = (List)originOrgIds.stream().filter((o) -> {
            return !targetOrgIdList.contains(o);
         }).collect(Collectors.toList());
      }

      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
      List adminUsers = new ArrayList();
      Iterator var10 = checkOrgIds.iterator();

      while(true) {
         Long orgId;
         long contentCnt;
         long playlistCnt;
         do {
            if (!var10.hasNext()) {
               return adminUsers;
            }

            orgId = (Long)var10.next();
            contentCnt = contentInfo.getCntAllContents(userId, orgId);
            playlistCnt = playlistInfo.getCntAllPlaylists(userId, orgId);
         } while(contentCnt <= 0L && playlistCnt <= 0L);

         LinkedHashMap map = UserUtils.makeAdminInfo(orgId);
         V2AdminInfoResource admin = new V2AdminInfoResource();
         admin.setOrganizationName((String)map.get("organization"));
         admin.setUserName((String)map.get("name"));
         admin.setEmail((String)map.get("email"));
         adminUsers.add(admin);
      }
   }

   private boolean checkAssignableRoles(String roleName) throws Exception {
      List assignableRoles = this.v2RoleService.assignableRoles();
      int size = assignableRoles.size();

      for(int i = 0; i < size; ++i) {
         if (((V2RoleListQueryDataResource)assignableRoles.get(i)).getRoleName().equals(roleName)) {
            return true;
         }
      }

      return false;
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2EditUserResource editUser(String userId, V2EditUserFilter filter, HttpServletRequest request) throws Exception {
      userId = StrUtils.nvl(userId);
      String team = filter.getTeam().replaceAll("<", "&lt;").replaceAll(">", "&gt;");
      boolean copyContentsToNewOrganization = true;
      copyContentsToNewOrganization = filter.isCopyContentsToNewOrganization();
      String roleName = StrUtils.nvl(filter.getRoleName()).replaceAll("<", "&lt;").replaceAll(">", "&gt;");
      String userName = StrUtils.nvl(filter.getUserName()).replaceAll("<", "&lt;").replaceAll(">", "&gt;");
      String email = StrUtils.nvl(filter.getEmail()).replaceAll("<", "&lt;").replaceAll(">", "&gt;");
      String phoneNum = StrUtils.nvl(filter.getPhoneNum()).replaceAll("<", "&lt;").replaceAll(">", "&gt;");
      String mobileNum = StrUtils.nvl(filter.getMobileNum()).replaceAll("<", "&lt;").replaceAll(">", "&gt;");
      String jobPosition = StrUtils.nvl(filter.getJobPosition()).replaceAll("<", "&lt;").replaceAll(">", "&gt;");
      Long groupId = filter.getGroupId();
      String organizationName = StrUtils.nvl(filter.getOrganizationName()).replaceAll("<", "&lt;").replaceAll(">", "&gt;");
      String organizationGroupName = StrUtils.nvl(filter.getOrganizationGroupName()).replaceAll("<", "&lt;").replaceAll(">", "&gt;");
      if ("admin".equalsIgnoreCase(userId) && !"Server Administrator".equalsIgnoreCase(roleName)) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_ADMIN_ROLE_NOT_CHANGE);
      } else {
         if (groupId != null) {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, groupId, userId);
         }

         UserInfo userInfo = UserInfoImpl.getInstance();
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         RoleInfo roleInfo = RoleInfoImpl.getInstance();
         UserLogManager logMgr = UserLogManagerImpl.getInstance();
         OrganizationGroupInfo organizationGroupInfo = OrganizationGroupInfoImpl.getInstance();
         User editUser = userInfo.getAllByUserId(userId);
         V2EditUserResource resource = new V2EditUserResource();
         if (editUser.isMu() && (filter.getCheckHasResources() == null || filter.getCheckHasResources())) {
            List adminUsers = this.checkMUUserResoruce(userId, organizationGroupName);
            if (!adminUsers.isEmpty()) {
               resource.setAdminUsers(adminUsers);
               V2UserInfoResource userInfoData = new V2UserInfoResource();
               userInfoData.setUserId(userId);
               userInfoData.setTeam(team);
               userInfoData.setUserName(this.piiDataManager.encryptData(userName, "name"));
               userInfoData.setEmail(this.piiDataManager.encryptData(email, "email"));
               userInfoData.setPhoneNum(this.piiDataManager.encryptData(phoneNum, "phone"));
               userInfoData.setMobileNum(this.piiDataManager.encryptData(mobileNum, "phone"));
               userInfoData.setJobPosition(jobPosition);
               resource.setUserInfo(userInfoData);
               resource.setCopyContentsToNewOrganization(copyContentsToNewOrganization);
               resource.setRoleName(roleName);
               resource.setGroupId(groupId);
               resource.setOrganizationName(organizationName);
               resource.setOrganizationGroupName(organizationGroupName);
               return resource;
            }
         }

         if (roleName.equals("")) {
            roleName = editUser.getRole_name();
         }

         if (userName.equals("")) {
            userName = editUser.getUser_name();
         }

         if (email.equals("")) {
            email = editUser.getEmail();
         }

         if (phoneNum == null) {
            phoneNum = editUser.getPhone_num();
         }

         if (mobileNum == null) {
            mobileNum = editUser.getMobile_num();
         }

         if (groupId == null || groupId == 0L) {
            groupId = editUser.getGroup_id();
         }

         if (organizationName.equals("")) {
            organizationName = editUser.getOrganization();
         }

         if (organizationGroupName == null) {
            organizationGroupName = userGroupInfo.getCurMngOrgGroup(userId);
         }

         AbilityUtils ability = new AbilityUtils();
         boolean canWriteUser = ability.checkAuthority("User Write");
         User loginUser = SecurityUtils.getUserContainer().getUser();
         User selectedUser = userInfo.getUserInfo(userId);
         String selectedUserOrgName = selectedUser.getOrganization();
         Long selectedUserOrgId = userGroupInfo.getOrgGroupIdByName(selectedUserOrgName);
         Long selectedUserRoleId = roleInfo.getRoleIdByRoleName(selectedUser.getRole_name());
         if (loginUser == null || !canWriteUser || !RoleUtils.isServerAdminRole(loginUser) && (roleName == null || RoleUtils.isServerAdminRole(roleName))) {
            throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
         } else {
            String userID = StrUtils.nvl(userId);
            User user = userInfo.getAllByUserId(userID);
            team = team == null ? user.getTeam() : team;
            roleName = roleName.equals("") ? user.getRole_name() : roleName;
            userName = userName.equals("") ? user.getUser_name() : userName;
            email = email.equals("") ? user.getEmail() : email;
            phoneNum = phoneNum == null ? user.getPhone_num() : phoneNum;
            mobileNum = mobileNum == null ? user.getMobile_num() : mobileNum;
            jobPosition = jobPosition == null ? user.getJob_position() : jobPosition;
            if (userID.equals("admin") && !loginUser.getUser_id().equals("admin")) {
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
            } else if (!this.checkAssignableRoles(roleName)) {
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
            } else if (!RoleUtils.isAdminRole(loginUser) && !RoleUtils.isServerAdminRole(loginUser) && (selectedUser.getRole_name().equals("Administrator") || selectedUser.getRole_name().equals("Server Administrator"))) {
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
            } else {
               if (RoleUtils.isServerAdminRole(roleName)) {
                  if (!organizationName.equals("ROOT") || groupId != 0L) {
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SERVER_ADMIN_ORGANIZATION_NAME_N_GROUP_NAME_ROOT);
                  }

                  if (!StrUtils.nvl(organizationGroupName).equals("")) {
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SERVER_ADMIN_CANNOT_HAVE_ORGANIZATION_GROUP);
                  }
               }

               if (!RoleUtils.isServerAdminRole(loginUser) && !RoleUtils.isAdminRole(loginUser) && loginUser.getRoot_group_id() != user.getRoot_group_id()) {
                  throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
               } else {
                  user.setUser_name(this.piiDataManager.encryptData(userName, "name"));
                  user.setEmail(this.piiDataManager.encryptData(email, "email"));
                  user.setPhone_num(this.piiDataManager.encryptData(phoneNum, "phone"));
                  user.setMobile_num(this.piiDataManager.encryptData(mobileNum, "phone"));
                  user.setTeam(team);
                  user.setJob_position(jobPosition);
                  Long roleIdl = roleInfo.getRoleIdByRoleName(roleName);
                  String groupName;
                  if (!groupId.toString().equals("")) {
                     groupName = userGroupInfo.getGroupNameByGroupId(groupId);
                     user.setGroup_name(groupName);
                  } else {
                     User tmpUser = userInfo.getUserInfo(userID);
                     groupId = tmpUser.getGroup_id();
                     groupName = userGroupInfo.getGroupNameByGroupId(tmpUser.getGroup_id());
                     user.setGroup_name(groupName);
                  }

                  int orgAdminCnt = userGroupInfo.getOrgAdminCount(selectedUserOrgId);
                  if (RoleUtils.isAdminRole(selectedUser) && orgAdminCnt <= 1 && roleIdl != selectedUserRoleId && !selectedUserOrgName.equals("ROOT")) {
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUIRE_AN_ADMIN_IN_ORGANIZATION);
                  } else {
                     RoleInfo roleDao = RoleInfoImpl.getInstance();
                     long roleId = roleDao.getRoleIdByRoleName(roleName);
                     int resultCnt = roleDao.getCntRuleManagerAbilityByRoleId(roleId);
                     if (resultCnt > 0 && !selectedUserOrgName.equals("ROOT") && !roleName.equals("Server Administrator")) {
                        throw new RestServiceException(RestExceptionCode.BAD_REQUEST_ASSIGN_RULEMANAGER_ONLY_ADMIN_GROUP);
                     } else {
                        ContentInfoImpl contentInfo;
                        PlaylistInfo playlistInfo;
                        if (roleName.equals("Server Administrator") && !organizationName.equals("ROOT")) {
                           if (copyContentsToNewOrganization) {
                              contentInfo = ContentInfoImpl.getInstance();
                              contentInfo.copyContents(userID, userGroupInfo.getOrgGroupIdByName("ROOT"));
                           }

                           user.setOrganization("ROOT");
                           user.setGroup_name("ROOT");
                           user.setRoot_group_id(0L);
                           userInfo.deleteMappingInfoByUserID(user.getUser_id());
                           userInfo.setUser(user);
                           contentInfo = ContentInfoImpl.getInstance();
                           contentInfo.addDefaultGroup(user.getUser_id());
                           playlistInfo = PlaylistInfoImpl.getInstance();
                           playlistInfo.addDefaultGroup(user.getUser_id());
                           DeviceMenuManager menuDao = DeviceMenuManagerImpl.getInstance();
                           menuDao.addDeviceMapMenuUser(user.getUser_id());
                           userInfo.setApprovalByUser(user);
                        } else {
                           userGroupInfo.setUserGroupMapGroupUserIdByUserId(userID, groupId);
                        }

                        roleInfo.setRoleIdMapRoleUserByUserId(roleIdl, userID);
                        user.setRole_name(roleName);
                        userInfo.setUser(user);
                        if (organizationGroupName != null && !organizationGroupName.isEmpty()) {
                           if (!user.isMu()) {
                              contentInfo = ContentInfoImpl.getInstance();
                              contentInfo.transferContentToAdmin(user);
                              playlistInfo = PlaylistInfoImpl.getInstance();
                              playlistInfo.transferPlaylistToAdmin(user);
                              userInfo.setMUByUserId(userId, "Y");
                           } else {
                              List fromOrgIdList = organizationGroupInfo.getMngOrgIdListByUserId(user.getUser_id());
                              List toOrgIdList = organizationGroupInfo.getMngOrgIdListByOrganizationGroupName(organizationGroupName);
                              fromOrgIdList.removeAll(toOrgIdList);
                              ContentInfo contentInfo = ContentInfoImpl.getInstance();
                              PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
                              Iterator var42 = fromOrgIdList.iterator();

                              label158:
                              while(true) {
                                 long orgId;
                                 long contentCnt;
                                 long playlistCnt;
                                 do {
                                    if (!var42.hasNext()) {
                                       break label158;
                                    }

                                    orgId = (Long)var42.next();
                                    contentCnt = contentInfo.getCntAllContents(user.getUser_id(), orgId);
                                    playlistCnt = playlistInfo.getCntAllPlaylists(user.getUser_id(), orgId);
                                 } while(contentCnt <= 0L && playlistCnt <= 0L);

                                 contentInfo.transferContentToAdmin(user, orgId);
                                 playlistInfo.transferPlaylistToAdmin(user, orgId);
                              }
                           }

                           userGroupInfo.setCurMngOrgGroup(userId, organizationGroupName);
                        } else if (user.isMu()) {
                           contentInfo = ContentInfoImpl.getInstance();
                           contentInfo.transferContentToAdmin(user);
                           playlistInfo = PlaylistInfoImpl.getInstance();
                           playlistInfo.transferPlaylistToAdmin(user);
                           userGroupInfo.unsetCurMngOrgGroup(userId);
                           userInfo.setMUByUserId(userId, "N");
                        }

                        V2UserInfoResource userInfoData = new V2UserInfoResource();
                        userInfoData.setUserId(userId);
                        userInfoData.setTeam(team);
                        userInfoData.setUserName(this.piiDataManager.encryptData(userName, "name"));
                        userInfoData.setEmail(this.piiDataManager.encryptData(email, "email"));
                        userInfoData.setPhoneNum(this.piiDataManager.encryptData(phoneNum, "phone"));
                        userInfoData.setMobileNum(this.piiDataManager.encryptData(mobileNum, "phone"));
                        userInfoData.setJobPosition(jobPosition);
                        resource.setUserInfo(userInfoData);
                        resource.setCopyContentsToNewOrganization(copyContentsToNewOrganization);
                        resource.setRoleName(roleName);
                        resource.setGroupId(groupId);
                        resource.setOrganizationName(organizationName);
                        resource.setOrganizationGroupName(organizationGroupName);
                        return resource;
                     }
                  }
               }
            }
         }
      }
   }

   public V2UserResource getUser(String userId) throws Exception {
      V2UserResource resource = new V2UserResource();
      UserInfo userInfo = UserInfoImpl.getInstance();
      User inputUser = null;
      User loginUser = null;
      loginUser = SecurityUtils.getUserContainer().getUser();
      if (userId != null && !userId.equalsIgnoreCase("")) {
         inputUser = userInfo.getUserByUserId(userId);
         if (inputUser.getIs_deleted().equals("N") && inputUser.getIs_approved().equals("Y")) {
            inputUser = userInfo.getAllByUserId(userId);
         }

         if (!userId.equalsIgnoreCase(loginUser.getUser_id())) {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, userId);
            if (!SecurityUtils.checkReadPermissionWithOrgAndId("User", userId)) {
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_NOT_HAVE, new String[]{"read"});
            }
         }

         return this.getUserInfomation(inputUser, resource);
      } else {
         return this.getUserInfomation(loginUser, resource);
      }
   }

   public V2MeResource getMe() throws Exception {
      User user = SecurityUtils.getUserContainer().getUser();
      String userId = user.getUser_id();
      V2MeResource r2 = new V2MeResource();
      V2UserResource r1 = this.getUser(userId);
      BeanUtils.copyProperties(r1, r2);
      r2.setIsMu(user.getIs_mu());
      long mngOrgId = -1L;
      String mngOrgName = "";
      new ArrayList();
      if (user.isMu()) {
         mngOrgId = user.getRoot_group_id();
         r2.setMngOrgId(user.getRoot_group_id());
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         List userManageGroupList = null;
         userManageGroupList = userGroupInfo.getMngGroupListByUserId(user.getUser_id());
         r2.setMngOrgGroupList(userManageGroupList);
      }

      r2.setFirstLogin(user.getIs_first_login());
      return r2;
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2UserWithdrawalResource withdrawnUser(HttpServletRequest request, V2UserDeleteResource resource) throws Exception {
      List userIds = resource.getIds();
      String reason = resource.getReason();
      boolean flag = false;

      for(int i = 0; i < userIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, (String)userIds.get(i));
         } catch (Exception var40) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER);
      }

      AbilityUtils ability = new AbilityUtils();
      boolean canWriteUser = ability.checkAuthority("User Write");
      UserInfo userInfo = UserInfoImpl.getInstance();
      RoleInfo roleInfo = RoleInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      UserLogManager logMgr = UserLogManagerImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      V2UserWithdrawalResource newResource = new V2UserWithdrawalResource();
      List withdrawalUserId = new ArrayList();
      List withdrawalFailUserId = new ArrayList();
      String pUserId = SecurityUtils.getLoginUserId();
      if (!canWriteUser) {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
      } else {
         reason = StrUtils.nvl(reason);
         String orgName = userContainer.getUser().getOrganization();
         Long userOrgId = userGroupInfo.getOrgGroupIdByName(orgName);
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         serverSetupDao.getServerInfoByOrgId(userOrgId);
         ContentInfo cInfo = ContentInfoImpl.getInstance();
         LogInfo logInfo = LogInfoImpl.getInstance();

         for(int i = 0; i < userIds.size(); ++i) {
            String userId = (String)userIds.get(i);

            V2CommonDeleteFail failData;
            try {
               if (userId.equals(SecurityUtils.getLoginUserId())) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_YOURSELF_NOT_DELETE);
               }

               User deleteUser = userInfo.getUserByUserId(userId);
               if (deleteUser == null) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"userId"});
               }

               Role delUserRole = roleInfo.getRoleByUserId(deleteUser.getUser_id());
               User executer = userInfo.getUserByUserId(SecurityUtils.getLoginUserId());
               Role executerRole = roleInfo.getRoleByUserId(SecurityUtils.getLoginUserId());
               long orgId = userGroupInfo.getOrgGroupIdByName(deleteUser.getOrganization());
               int orgAdminCnt = userGroupInfo.getOrgAdminCount(orgId);
               if (deleteUser.getUser_id().equals("admin")) {
                  throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
               }

               if (RoleUtils.isServerAdminRole(delUserRole.getRole_name()) && !executer.getUser_id().equals("admin")) {
                  throw new RestServiceException(RestExceptionCode.FORBIDDEN_SERVER_ADMINISTRATOR_NOT_DELETE);
               }

               if (!RoleUtils.isServerAdminRole(executerRole.getRole_name()) && RoleUtils.isAdminRole(delUserRole.getRole_name())) {
                  throw new RestServiceException(RestExceptionCode.FORBIDDEN_NOT_SERVER_ADMINISTRATOR);
               }

               if (RoleUtils.isServerAdminRole(executerRole.getRole_name()) && orgAdminCnt <= 1 && RoleUtils.isAdminRole(delUserRole.getRole_name()) && !deleteUser.getOrganization().equals("ROOT")) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUIRE_AN_ADMIN_IN_ORGANIZATION);
               }

               List tmpContentList = cInfo.getContentApproverInfoByUserId(userId);
               cInfo.deleteContentApproverMapByUserId(userId);
               userInfo.setContentApprover(userId, "N");

               for(int j = 0; j < tmpContentList.size(); ++j) {
                  Map map = (Map)tmpContentList.get(j);
                  List tmpApproverList = cInfo.getContentApproverListByContentId((String)map.get("CONTENT_ID"));
                  if (tmpApproverList == null || tmpApproverList.size() == 0) {
                     cInfo.setApprovalStatus((String)map.get("CONTENT_ID"), "APPROVED", "");
                  }
               }

               boolean userDeleteResult = userInfo.setIsDeletedByUserId(reason, userId, false);
               if (userDeleteResult && CommonConfig.get("rulemanager.enable") != null && CommonConfig.get("rulemanager.enable").equals("true")) {
                  ExternalSystemUtils exSystemUtils = new ExternalSystemUtils();
                  exSystemUtils.ruleManagerDeleteUser(deleteUser.getUser_id());
               }

               ContentInfo contentInfo = ContentInfoImpl.getInstance();
               contentInfo.transferContentToAdmin(deleteUser);
               contentInfo.deleteGroupByCreatorId(deleteUser.getUser_id());
               PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
               playlistInfo.transferPlaylistToAdmin(deleteUser);
               playlistInfo.deleteGroupByCreatorId(deleteUser.getUser_id());

               try {
                  LogDao logdao = new LogDao();
                  logdao.deleteLogByUserId(userId);
               } catch (Exception var37) {
                  this.logger.error(var37);
               }

               userInfo.deleteDashboardUserInfoByUserId(userId);
               userGroupInfo.unsetCurMngOrgGroup(userId);
               withdrawalUserId.add(userId);
            } catch (RestServiceException var38) {
               failData = new V2CommonDeleteFail();
               failData.setId(userId);
               failData.setReason(var38.getErrorMessage());
               failData.setReasonCode(var38.getErrorCode());
               withdrawalFailUserId.add(failData);
            } catch (Exception var39) {
               failData = new V2CommonDeleteFail();
               failData.setId(userId);
               failData.setReason(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN.getMessage());
               failData.setReasonCode(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN.getCode());
               withdrawalFailUserId.add(failData);
            }
         }

         newResource.setWithdrawalSuccessList(withdrawalUserId);
         newResource.setWithdrawalFailList(withdrawalFailUserId);
         newResource.setReason(reason);
         return newResource;
      }
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2CommonBulkResultResource removeUser(V2CommonIds body) throws SQLException, Exception {
      AbilityUtils ability = new AbilityUtils();
      boolean canWriteUser = ability.checkAuthority("User Write");
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserLogManager logMgr = UserLogManagerImpl.getInstance();
      List userId = body.getIds();
      boolean flag = false;

      for(int i = 0; i < userId.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, (String)userId.get(i));
         } catch (Exception var18) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER);
      }

      V2CommonBulkResultResource data = new V2CommonBulkResultResource();
      List removedUserId = new ArrayList();
      List removedFailUserId = new ArrayList();
      if (!canWriteUser) {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
      } else {
         for(int i = 0; i < userId.size(); ++i) {
            String delUserId = (String)userId.get(i);

            try {
               User removeUser = userInfo.getUserByUserId(delUserId);
               if (removeUser != null && removeUser.getIs_deleted().equalsIgnoreCase("Y")) {
                  int removeUserResult = userInfo.deleteUser(delUserId);
                  if (removeUserResult == 1) {
                     try {
                        removedUserId.add(delUserId);
                     } catch (Exception var16) {
                        this.logger.error(var16.getMessage());
                     }
                  } else {
                     removedFailUserId.add(delUserId);
                  }
               }
            } catch (Exception var17) {
               this.logger.error(var17);
               removedFailUserId.add(delUserId);
            }
         }

         List failDataList = new ArrayList();
         Iterator var21 = removedFailUserId.iterator();

         while(var21.hasNext()) {
            String failUserId = (String)var21.next();
            V2CommonDeleteFail failData = new V2CommonDeleteFail();
            failData.setId(failUserId);
            failDataList.add(failData);
         }

         data.setFailList(failDataList);
         data.setSuccessList(removedUserId);
         return data;
      }
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2ApprovalResource approvalCheck(V2ApprovalResource resource) throws Exception {
      String approvalStatus = resource.getApprovalStatus();
      Long groupId = resource.getGroupId();
      Long roleId = resource.getRoleId();
      String rejectReason = resource.getRejectReason();
      List userIds = resource.getIds();
      boolean flag = false;

      for(int i = 0; i < userIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, (String)userIds.get(i));
         } catch (Exception var11) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER);
      }

      UserInfo userInfo = UserInfoImpl.getInstance();
      if (approvalStatus.equals("APPROVED")) {
         return this.approval(userIds, groupId, roleId);
      } else if (approvalStatus.equals("REJECTED")) {
         for(int i = 0; i < userIds.size(); ++i) {
            User rejectUser = userInfo.getUserByUserId((String)userIds.get(i));
            if (rejectUser.getApproval_type().equals("TEXT_MOVE_ORGAN_P")) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_ORGANIZATION_MOVE_NOT_REJECT);
            }
         }

         return this.rejectUser(userIds, rejectReason);
      } else {
         throw new IllegalArgumentException("Invalid approval value");
      }
   }

   private V2ApprovalResource approval(List userIds, Long groupId, Long roleId) throws Exception {
      boolean flag = false;

      for(int i = 0; i < userIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, groupId, (String)userIds.get(i));
         } catch (Exception var22) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER);
      }

      if (groupId == null) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"groupId"});
      } else if (roleId == null) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"roleId"});
      } else if (roleId == 1L && groupId != 0L) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SERVER_ADMIN_CANNOT_APPLIED_SUB_ORGANIZATION_USERS);
      } else {
         AbilityUtils ability = new AbilityUtils();
         boolean canWriteUser = ability.checkAuthority("User Write");
         UserInfo userInfo = UserInfoImpl.getInstance();
         RoleInfo roleInfo = RoleInfoImpl.getInstance();
         UserLogManager logMgr = UserLogManagerImpl.getInstance();
         V2ApprovalResource newResource = new V2ApprovalResource();
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         String pUserId = SecurityUtils.getLoginUserId();
         if (canWriteUser) {
            Iterator var13 = userIds.iterator();

            while(var13.hasNext()) {
               String userId = (String)var13.next();
               User user = userInfo.getUserByUserId(userId);
               String roleName = roleInfo.getNameByRoleId(roleId);
               roleName = StrUtils.nvl(roleName);
               UserGroup userGroup = userGroupInfo.getGroupById(groupId);
               user.setRole_name(roleName);
               user.setGroup_name(userGroup.getGroup_name());
               if (user.getApproval_type().equals("TEXT_NEW_P")) {
                  BaseUser ftpUser = new BaseUser();
                  ftpUser.setName(user.getUser_id());
                  ftpUser.setPassword(user.getPassword());
                  UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
                  userMgr.save(ftpUser);
                  ContentInfo contentInfo = ContentInfoImpl.getInstance();
                  contentInfo.addDefaultGroup(user.getUser_id());
                  PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
                  playlistInfo.addDefaultGroup(user.getUser_id());
               } else {
                  userInfo.deleteMappingInfoByUserID(user.getUser_id());
               }

               userInfo.setApprovalByUser(user);
               DeviceMenuManager menuDao = DeviceMenuManagerImpl.getInstance();
               menuDao.addDeviceMapMenuUser(user.getUser_id());
               newResource.setApprovalStatus("APPROVED");
               newResource.setGroupId(groupId);
               newResource.setRoleId(roleId);
            }

            return newResource;
         } else {
            throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
         }
      }
   }

   public boolean checkUserIdPresent(String userId) throws Exception {
      userId = StrUtils.nvl(userId);
      boolean userIdPresent = true;
      UserInfo userInfo = UserInfoImpl.getInstance();
      if (userInfo.getCountByUserIdForCheck(userId) == 0) {
         userIdPresent = false;
      }

      return userIdPresent;
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2CheckIdDuplicationResource checkIdDuplication(String userId, String ldapFullId) throws Exception {
      userId = StrUtils.nvl(userId);
      ldapFullId = StrUtils.nvl(ldapFullId);
      UserInfo userInfo = UserInfoImpl.getInstance();
      V2CheckIdDuplicationResource newResource = new V2CheckIdDuplicationResource();
      int duResult = 0;
      boolean isDuplicated = true;
      if (ldapFullId != null && !ldapFullId.equals("")) {
         if (!userId.equals("admin") && userInfo.getCountByLDAPUserFullIdForCheck(ldapFullId) <= 0) {
            if (userInfo.getCountByUserIdForCheck(userId) == 0) {
               duResult = 1;
               isDuplicated = false;
            } else {
               duResult = 20;
            }
         } else {
            duResult = 0;
            isDuplicated = true;
         }
      } else if (userInfo.getCountByUserIdForCheck(userId) == 0) {
         duResult = 1;
         isDuplicated = false;
      }

      newResource.setUserId(userId);
      newResource.setLdapFullId(ldapFullId);
      newResource.setResult(duResult);
      newResource.setDuplicated(isDuplicated);
      return newResource;
   }

   private V2ApprovalResource rejectUser(List userIds, String rejectReason) throws Exception {
      boolean flag = false;

      for(int i = 0; i < userIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, (String)userIds.get(i));
         } catch (Exception var36) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER);
      }

      String pUserId = SecurityUtils.getLoginUserId();
      UserInfo userInfo = UserInfoImpl.getInstance();
      V2ApprovalResource newResource = new V2ApprovalResource();
      AbilityUtils ability = new AbilityUtils();
      boolean canWriteUser = ability.checkAuthority("User Write");
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      UserLogManager logMgr = UserLogManagerImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      rejectReason = StrUtils.nvl(rejectReason);
      String orgName = userContainer.getUser().getOrganization();
      Long userOrgId = userGroupInfo.getOrgGroupIdByName(orgName);
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map infoMap = serverSetupDao.getServerInfoByOrgId(userOrgId);
      if (canWriteUser) {
         boolean mailEnable = (Boolean)infoMap.get("SMTP_ENABLE");
         String templetURL = CommonConfig.get("mail.templet.url");
         String templetPath = CommonConfig.get("mail.templet.path");
         Iterator var19 = userIds.iterator();

         while(var19.hasNext()) {
            String userId = (String)var19.next();
            User rejectUser = userInfo.getUserByUserId(userId);
            if (!rejectUser.getApproval_type().equals("TEXT_MOVE_ORGAN_P")) {
               Mail mail = new Mail();
               mail.setFrom(SecurityUtils.getUserContainer().getUser().getEmail());
               mail.setToList(rejectUser.getEmail());
               mail.setSubject("Reject Join Request");
               String contentTitle = "Sign up failed due to the following reason.";
               String subTitle = "Reject Join Request";
               String content = "Reject Reason : ";
               MailTemplet mt = new MailTemplet(templetPath + "/mail_templete.html");
               mt.set("templetURL", templetURL);
               mt.set("mailTitle", contentTitle);
               mt.set("subTitle", subTitle);
               String reasonBr = rejectReason.replaceAll("\r\n", "<br>");
               mt.set("contentName", content);
               mt.set("content", reasonBr);
               mail.setBody(mt.getContent());
               boolean rejectUserResult = false;
               if (mailEnable) {
                  try {
                     MailManagerInterface mailManager = MailManager.getInstance();
                     mailManager.sendEmail(mail, infoMap);
                  } catch (Exception var34) {
                     this.logger.error("Failed to send mail.");
                  } finally {
                     userInfo.setRejectUser(rejectReason, userId);
                  }
               } else {
                  userInfo.setRejectUser(rejectReason, userId);
               }

               userInfo.deleteUser(userId);
            }
         }

         newResource.setApprovalStatus("REJECTED");
         newResource.setRejectReason(rejectReason);
         return newResource;
      } else {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
      }
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody resetPassword(HttpServletRequest request, String userId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, userId);
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      Locale locale = SecurityUtils.getLocale();
      String pUserId = SecurityUtils.getLoginUserId();
      String MessageKey = "TEXT_ROOT_GROUP_NAME_P";
      UserLogManager logMgr = UserLogManagerImpl.getInstance();
      AbilityUtils ability = new AbilityUtils();
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      boolean canWriteUser = ability.checkAuthority("User Write");
      LinkedHashMap result = new LinkedHashMap();
      String adminGroupName = "Administrators";
      if (canWriteUser) {
         userId = StrUtils.nvl(userId);
         User user = userInfo.getAllByUserId(userId);
         String userGroup = user.getGroup_name();
         List groupList = userGroupInfo.getAllOrganizationGroup();
         if (userGroup.equals("ROOT")) {
            ;
         }

         String tempPW = this.getPasswd();
         user.setPassword(tempPW);
         user.setIs_reset_pwd("Y");
         userInfo.setUser(user);
         if (SecurityUtils.getLoginUserId().equals(userId)) {
            SecurityUtils.getUserContainer().getUser().setPassword(user.getPassword());
            V2TokenUtils.updateUserInfoOnUserContainer(SecurityUtils.getUserContainer().getUser());
         }

         BaseUser ftpUser = new BaseUser();
         ftpUser.setName(userId);
         ftpUser.setPassword(tempPW);
         UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
         userMgr.save(ftpUser);
         result.put("status", "success");
         result.put("tempPW", tempPW);
         responseBody.setItems(result);
         return responseBody;
      } else {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
      }
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody checkTransfer(String newUserId) throws SQLException, Exception {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      boolean checked = false;
      UserInfo userInfo = UserInfoImpl.getInstance();
      LinkedHashMap result = new LinkedHashMap();
      Locale locale = SecurityUtils.getLocale();
      if (!newUserId.equals(SecurityUtils.getLoginUserId()) && userInfo.getCountByUserId(newUserId) > 0 && userInfo.getIsDeletedByUserId(newUserId).equals("N") && userInfo.getIsApprovedByUserId(newUserId).equals("Y")) {
         User newAdmin = userInfo.getAllByUserId(newUserId);
         if (newAdmin.getRoot_group_id().equals(SecurityUtils.getLoginUser().getRoot_group_id())) {
            checked = true;
         }
      }

      String transferMSG = "";
      if (checked) {
         responseBody.setStatus("Success");
         String MessageKey = "MESSAGE_USER_AVAILABLE_ID_TRANSFER_ADMIN_P";
         transferMSG = "You can transfer administrator privileges to the user ID.";
         responseBody.setErrorMessage(transferMSG);
         responseBody.setItems(result);
         return responseBody;
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_ADMIN_NOT_TRANSFER);
      }
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody saveTransfer(HttpServletRequest request, String userId, String newAdminID, String newRoleName) throws SQLException, Exception {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      boolean checked = false;
      LinkedHashMap result = new LinkedHashMap();
      Locale locale = SecurityUtils.getLocale();
      RoleInfo roleInfo = RoleInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      User user = SecurityUtils.getLoginUser();
      AbilityUtils ability = new AbilityUtils();
      boolean canWriteUser = ability.checkAuthority("User Write");
      if (canWriteUser) {
         long roleID = roleInfo.getRoleIdByRoleName(newRoleName);
         long adminRoleId = roleInfo.getRoleIdByRoleName(user.getRole_name());
         user.setRole_name(roleInfo.getNameByRoleId(roleID));
         userInfo.setUser(user);
         roleInfo.setRoleIdMapRoleUserByUserId(roleID, SecurityUtils.getLoginUserId());
         User newAdmin = userInfo.getAllByUserId(newAdminID);
         newAdmin.setRole_name(roleInfo.getNameByRoleId(adminRoleId));
         userInfo.setUser(newAdmin);
         roleInfo.setRoleIdMapRoleUserByUserId(adminRoleId, newAdminID);
         long groupId = userGroupInfo.getGroupIdByName(newAdmin.getGroup_name(), newAdmin.getRoot_group_id());
         userGroupInfo.setCanDeleteByGroupId(groupId);
         String MessageKey = "MESSAGE_USER_SUCCESS_TRANSFER_ADMIN_P";
         String transferMSG = "The administrator privileges have been transferred successfully.<br>Please log in again.";
         responseBody.setStatus("Success");
         responseBody.setErrorMessage(transferMSG);
         responseBody.setItems(result);
         return responseBody;
      } else {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
      }
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody getUsingRoleUsers(String role_id) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      LinkedHashMap result = new LinkedHashMap();
      RoleInfo roleInfo = RoleInfoImpl.getInstance();
      String roleId = StrUtils.nvl(role_id);

      try {
         List userRoleList = roleInfo.getMappedUseListrByRoleId(Long.parseLong(roleId));
         int size = userRoleList.size();
         UserContainer userContainer = SecurityUtils.getUserContainer();
         String orgName = userContainer.getUser().getOrganization();
         if (size > 0) {
            HashMap userId = new HashMap();

            for(int i = 0; i < userRoleList.size(); ++i) {
               if (orgName.equals("ROOT")) {
                  userId.put("user_" + i, ((Map)userRoleList.get(i)).get("user_id"));
               } else if (((Map)userRoleList.get(i)).get("organization").toString().equals(orgName)) {
                  userId.put("user_" + i, ((Map)userRoleList.get(i)).get("user_id"));
               }
            }

            result.put("user_list", userId);
         }
      } catch (SQLException | NumberFormatException var12) {
         this.logger.error("", var12);
      }

      responseBody.setStatus("Success");
      responseBody.setItems(result);
      return responseBody;
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody checkDevicePermission(String userId) throws SQLException, Exception {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      LinkedHashMap result = new LinkedHashMap();
      userId = StrUtils.nvl(userId);
      UserInfo userInfo = UserInfoImpl.getInstance();
      User user = userInfo.getAllByUserId(userId);
      if (user.isMu()) {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_USER_ID_MU);
      } else if (DeviceUtils.isDeviceGroupAuth(user)) {
         responseBody.setStatus("Success");
         if (user.getRoot_group_id() > 0L) {
            result.put("organization", user.getRoot_group_id());
         }

         responseBody.setItems(result);
         return responseBody;
      } else {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_NOT_HAVE);
      }
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ResponseBody updateDeviceGroup(String strGroupId, String userId) throws SQLException, Exception {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      LinkedHashMap result = new LinkedHashMap();
      userId = StrUtils.nvl(userId);
      DeviceGroupDao device_dao = new DeviceGroupDao();
      boolean dbResult = device_dao.addPermissionsDeviceGroup(userId, strGroupId);
      if (dbResult) {
         responseBody.setStatus("Success");
         result.put("permission", true);
         responseBody.setItems(result);
         return responseBody;
      } else {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      }
   }

   private String getPasswd() {
      Random rand = new Random(System.currentTimeMillis());
      StringBuffer sb = new StringBuffer("");
      int prev = -1;

      for(int i = 0; i < 18; ++i) {
         int rnd = Math.abs(rand.nextInt(13));
         if (prev > 0 && prev % 3 == rnd % 3) {
            --i;
         } else {
            prev = rnd;
            char ch;
            switch(rnd) {
            case 0:
            case 3:
            case 6:
            case 9:
            case 12:
               ch = (char)((int)(Math.random() * 26.0D + 65.0D));
               sb.append(ch);
               break;
            case 1:
            case 4:
            case 7:
            case 10:
               ch = (char)((int)(Math.random() * 26.0D + 97.0D));
               sb.append(ch);
               break;
            case 2:
            case 5:
            case 8:
            case 11:
               sb.append(Math.abs(rand.nextInt(10)));
            }
         }
      }

      return sb.toString();
   }

   public V2UserInfoResource4Password sendEmailForAuthenticationCode(V2UserInfoResource4Password resource4Password) throws Exception {
      UserInfo userInfo = UserInfoImpl.getInstance();
      this.checkUserByIdNameEmail(userInfo, resource4Password);
      this.sendAuthenticationCode(userInfo, resource4Password, resource4Password.getLocale());
      return resource4Password;
   }

   public V2UserEncyptionTokenResource isValidEmailAuthenticationCode(V2UserEncyptionTokenResource resource) throws Exception {
      CASValue casObj = null;
      String resetPasswordHash = null;
      long resetPasswordTime = 0L;
      long currTime = System.currentTimeMillis();
      Map resetPwInfoMap = null;

      try {
         casObj = CacheFactory.getCache().gets(this.getAuthCacheKeyPrefix(resource.getAuthType()) + "_MAP" + resource.getUserId(), new HashMap());
         resetPwInfoMap = (Map)casObj.getValue();
         if (resetPwInfoMap.size() == 0) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"authentication token(" + resource.getEncryptionToken() + ")"});
         }

         resetPasswordHash = (String)resetPwInfoMap.get(this.getAuthCacheKeyPrefix(resource.getAuthType()) + "_HASH_VALUE");
         resetPasswordTime = (Long)resetPwInfoMap.get(this.getAuthCacheKeyPrefix(resource.getAuthType()) + "_SETTING_TIME");
      } catch (SQLException var10) {
         this.logger.error("isValidEmailAuthenticationCode error!" + var10.getMessage());
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR);
      } catch (RestServiceException var11) {
         if (var11.getRestExceptionCode().getCode().equals("404001")) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"authentication token"});
         }
      } catch (Exception var12) {
         this.logger.error("isValidEmailAuthenticationCode error!" + var12.getMessage());
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR);
      }

      if (currTime - resetPasswordTime > 180000L) {
         this.deleteResetPasswordInfo(resource.getUserId(), resource.getAuthType());
         throw new RestServiceException(RestExceptionCode.REQUEST_TIMEOUT_USER_PW_AUTH_CODE);
      } else if (!resetPasswordHash.equals(resource.getEncryptionToken())) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_USER_PW_AUTH_CODE_NOT_MATCH);
      } else {
         V2UserEncyptionTokenResource ret = new V2UserEncyptionTokenResource();
         ret.setUserId(resource.getUserId());
         if ("OTP".equalsIgnoreCase(resource.getAuthType())) {
            this.v2UserMfaService.resetMyMfa(resource.getUserId());
         }

         return ret;
      }
   }

   private String getAuthCacheKeyPrefix(String authType) {
      String authTypeKey = "RESET_PASSWORD";
      if ("OTP".equalsIgnoreCase(authType)) {
         authTypeKey = "RESET_OTP";
      }

      return authTypeKey;
   }

   public V2UserPasswordEncyptionTokenResource saveResetPassword(V2UserPasswordEncyptionTokenResource resource) throws Exception {
      Boolean canResetPassword = false;
      String resetPasswordHash = null;
      UserInfo userInfo = UserInfoImpl.getInstance();
      User user = null;
      new HashMap();
      new MappingJackson2JsonView();
      Map resetPwInfoMap = null;
      CASValue casObj = null;

      try {
         casObj = CacheFactory.getCache().gets(this.getAuthCacheKeyPrefix(resource.getAuthType()) + "_MAP" + resource.getUserId(), new HashMap());
         resetPwInfoMap = (Map)casObj.getValue();
         if (resetPwInfoMap.size() == 0) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_PW_TOKEN);
         }

         resetPasswordHash = (String)resetPwInfoMap.get(this.getAuthCacheKeyPrefix(resource.getAuthType()) + "_HASH_VALUE");
         canResetPassword = (Boolean)resetPwInfoMap.get(this.getAuthCacheKeyPrefix(resource.getAuthType()) + "_AUTHENTICATED_FLAG");
      } catch (RestServiceException var15) {
         if (var15.getRestExceptionCode().getCode().equals("404701")) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_PW_TOKEN);
         }
      } catch (Exception var16) {
         this.logger.error("saveResetPassword error!" + var16.getMessage());
      }

      if (!resetPasswordHash.equals(resource.getEncryptionToken())) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_USER_PW_AUTH_CODE_NOT_MATCH);
      } else if (canResetPassword != null && canResetPassword) {
         user = userInfo.getAllByUserId(resource.getUserId());
         if (user == null) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{resource.getUserId()});
         } else {
            UserPasswordManager userPassword = UserPasswordManagerImpl.getInstance();
            userPassword.isValidPassword(resource.getUserId(), resource.getPassword(), resource.getPasswordConfirm(), user);
            user.setPassword(resource.getPassword());
            user.setIs_reset_pwd("N");
            BaseUser ftpUser = new BaseUser();
            ftpUser.setName(user.getUser_id());
            ftpUser.setPassword(resource.getPassword());
            UserManager userMgr = (new DbUserManagerFactory()).createUserManager();

            try {
               userMgr.save(ftpUser);
               userInfo.setUser(user);
            } catch (FtpException var14) {
               this.logger.error("saveResetPassword error! " + var14.getMessage());
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR);
            }

            this.deleteResetPasswordInfo(resource.getUserId(), resource.getAuthType());
            V2UserPasswordEncyptionTokenResource ret = new V2UserPasswordEncyptionTokenResource();
            ret.setUserId(resource.getUserId());
            return ret;
         }
      } else {
         this.logger.error("saveResetPassword error! : canResetPassword is null or not true.");
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN);
      }
   }

   private void deleteResetPasswordInfo(String userId, String authType) {
      try {
         CacheFactory.getCache().delete(this.getAuthCacheKeyPrefix(authType) + "_MAP" + userId);
      } catch (Exception var4) {
         this.logger.error("UserPasswordResetController error!" + var4.getMessage());
      }

   }

   private User checkUserByIdNameEmail(UserInfo userInfo, V2UserInfoResource4Password resource) throws Exception {
      User user = null;

      try {
         user = userInfo.getAllByUserId(resource.getUserId());
      } catch (Exception var5) {
         throw new RestServiceException(RestExceptionCode.INVALID_DETAILS, new String[]{resource.getEmail()});
      }

      if (!resource.getEmail().equalsIgnoreCase(user.getEmail())) {
         throw new RestServiceException(RestExceptionCode.INVALID_DETAILS, new String[]{resource.getEmail()});
      } else {
         return user;
      }
   }

   private long getUserSMTPInfo(UserInfo userInfo, V2UserInfoResource4Password resource) throws Exception {
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Long orgId = null;
      Map serverSetupMap = null;
      String smtpEnable = null;
      orgId = userInfo.getRootGroupIdByUserId(resource.getUserId());
      serverSetupMap = serverSetupDao.getServerInfoByOrgId(orgId);
      smtpEnable = serverSetupMap.get("SMTP_ENABLE").toString();
      return smtpEnable != null && !smtpEnable.equals("") && !smtpEnable.equalsIgnoreCase("false") ? orgId : -2L;
   }

   private void sendAuthenticationCode(UserInfo userInfo, V2UserInfoResource4Password resource, String userLocale) throws Exception {
      new Locale(userLocale);
      new HashMap();
      boolean isExistUser = false;
      Map resetPwValues = null;
      Long orgId = null;
      if (orgId = this.getUserSMTPInfo(userInfo, resource) == -2L) {
         throw new RestServiceException(RestExceptionCode.SERVICE_UNAVAILABLE_SMTP_SERVER_NOT_EXIST);
      } else {
         String resetPasswordHash = null;
         long passwordResetExpirationInMilliSec = System.currentTimeMillis();
         String mailTitle = null;
         String mailContent = null;
         resetPasswordHash = resource.getUserId() + resource.getEmail() + passwordResetExpirationInMilliSec;
         resetPasswordHash = SecurityUtils.getEncryptedPassword(resetPasswordHash);
         CASValue casObj = null;

         try {
            casObj = CacheFactory.getCache().gets(this.getAuthCacheKeyPrefix(resource.getAuthType()) + "_MAP" + resource.getUserId(), new HashMap());
         } catch (Exception var17) {
            this.logger.error("V2UserMgr Service CacheFactory.getCache().gets error!" + var17.getMessage());
         }

         resetPwValues = (Map)casObj.getValue();
         if (resetPwValues == null) {
            resetPwValues = new HashMap();
         }

         ((Map)resetPwValues).put(this.getAuthCacheKeyPrefix(resource.getAuthType()) + "_HASH_VALUE", resetPasswordHash);
         ((Map)resetPwValues).put(this.getAuthCacheKeyPrefix(resource.getAuthType()) + "_SETTING_TIME", passwordResetExpirationInMilliSec);
         ((Map)resetPwValues).put(this.getAuthCacheKeyPrefix(resource.getAuthType()) + "_AUTHENTICATED_FLAG", true);

         try {
            CacheFactory.getCache().cas(this.getAuthCacheKeyPrefix(resource.getAuthType()) + "_MAP" + resource.getUserId(), casObj.getCas(), resetPwValues);
         } catch (Exception var16) {
            this.logger.error("V2UserMgr Service CacheFactory.getCache().cas error!" + var16.getMessage());
         }

         if ("OTP".equalsIgnoreCase(resource.getAuthType())) {
            mailTitle = "MagicINFO otp reset link";
         } else {
            mailTitle = "MagicINFO password reset link";
         }

         mailContent = "Here is your authentication code. ";
         mailContent = mailContent + "Enter this authentication code in the Authentication Code field.<br>";
         mailContent = mailContent + "Authentication Code: " + resetPasswordHash;
         MailUtil.makeMail(mailTitle, mailContent, resource.getUserId(), orgId, "");
         this.logger.info("UserPasswordResetController: mail sent to " + resource.getUserId() + " orgId:" + orgId);
      }
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2ErrorMessageResource getErrorMessage(String errorCode) {
      String errorMessage = (String)errorList.get(Integer.parseInt(errorCode));
      V2ErrorMessageResource resource = new V2ErrorMessageResource();
      resource.setErrorMessage(errorMessage);
      return resource;
   }

   private static Map init() {
      Map list = new HashMap();
      list.put(1, "Select users that belong to the same organization.");
      list.put(2, "You cannot change the user group of the administrator group.");
      list.put(3, "Unable to delete yourself.");
      list.put(4, "You cannot delete an administrator.");
      list.put(5, "You cannot change the user role of the administrator.");
      list.put(6, "Must be 5-20 characters");
      list.put(7, "User IDs can only contain alphanumeric characters and periods(.).");
      list.put(8, "The user ID you entered is available.");
      list.put(9, "The user ID you entered is already in use.<br>Please enter a different ID.");
      list.put(10, "The max length is 60.");
      list.put(11, "Enter the user ID.");
      list.put(12, "Enter the user name.");
      list.put(13, "You cannot use a name that consists of spaces only.");
      list.put(14, "Enter the password.");
      list.put(15, "Enter the password again for confirmation.");
      list.put(16, "The confirmation password must be identical with the password.");
      list.put(17, "Select the organization.");
      list.put(18, "Select a user group.");
      list.put(19, "Select a user role.");
      list.put(20, "Enter the e-mail address.");
      list.put(21, "The max length is 200.");
      list.put(22, "Invalid e-mail address format.<br>Enter a correct e-mail address.");
      list.put(23, "Invalid phone number.<br>Enter the numbers in front and behind of the -.");
      list.put(24, "Check whether the user ID is duplicate.");
      list.put(25, "You cannot change the user organization of the administrator.");
      list.put(26, "Email sent successfully.");
      list.put(27, "Failed to send the mail.");
      list.put(28, "Select a user(s) to reject.");
      list.put(29, "You cannot reject a user whose organization has been changed.");
      list.put(30, "You cannot edit the default roles.");
      list.put(31, "Select a role(s) to delete.");
      list.put(32, "You cannot delete the default roles.");
      list.put(33, "Unable to delete the role as it is being used by a user(s).");
      list.put(34, "Select at least one ability from the ability list.");
      list.put(35, "Enter the role name.");
      list.put(36, "The role name you entered is available.");
      list.put(37, "The role name you entered is already in use. Enter a different name.");
      list.put(38, "Enter the role scope.");
      list.put(39, "Check whether the role name is duplicate.");
      list.put(40, "You can transfer administrator privileges to the user ID.");
      list.put(41, "The user ID does not exist or you cannot transfer administrator privileges to the user ID.");
      list.put(42, "Enter the administrator ID of the new organization.");
      list.put(43, "Check whether the administrator ID of the new organization is valid.");
      list.put(44, "The password has been changed successfully.");
      list.put(45, "Your password has been reset by an administrator. Please change your password.");
      list.put(46, "MIS_MESSAGE_LICENSE_REQUEST_P");
      list.put(47, "Check whether the nickname is duplicate.");
      list.put(48, "Invalid mobile phone number.<br>Enter the numbers in front and behind of the -.");
      list.put(49, "Select");
      list.put(93, "Use a password of 10 to 50 characters, using a combination of letters and numbers.");
      list.put(94, "For your password, you must use a combination of English letters and numbers.");
      list.put(96, "For your password, you cannot use a three digit serial number or repeat the same character three or more times.");
      list.put(97, "Only alphanumeric characters are allowed.");
      list.put(98, "The user ID you entered is already in use.<br>Please enter a different ID.");
      list.put(99, "This is required.");
      list.put(100, "The user was registered successfully.");
      list.put(101, "Failed to register the user.");
      list.put(102, "The nickname you entered is already in use.");
      list.put(103, "Only numbers are allowed.");
      list.put(104, "No data");
      list.put(105, "Special characters are not allowed.");
      list.put(106, "IP address is incorrect.");
      list.put(107, "A password must be 8 to 50 characters long.");
      list.put(108, "Choose a password of 10 to 50 alphanumeric characters, or a password of 8 to 50 characters, using a combination of letters, numbers and symbols.");
      list.put(109, "Blank space not allowed");
      return list;
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2ResetPasswordResource issueTemporaryPassword(String userId, HttpServletRequest request) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, userId);
      UserInfo userInfo = UserInfoImpl.getInstance();
      User user = userInfo.getAllByUserId(userId);
      String roleName = user.getRole_name();
      String pUserId = SecurityUtils.getLoginUserId();
      UserLogManager logMgr = UserLogManagerImpl.getInstance();
      AbilityUtils ability = new AbilityUtils();
      boolean canWriteUser = ability.checkAuthority("User Write");
      V2ResetPasswordResource resource = new V2ResetPasswordResource();
      if (!canWriteUser) {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
      } else {
         if (StringUtils.equals(roleName, "Server Administrator") || StringUtils.equals(roleName, "Administrator")) {
            User loginUser = SecurityUtils.getUserContainer().getUser();
            if (!RoleUtils.isServerAdminRole(loginUser) && !RoleUtils.isAdminRole(loginUser)) {
               throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
            }
         }

         String tempPW = this.getPasswd();
         user.setPassword(tempPW);
         user.setIs_reset_pwd("Y");
         userInfo.setUser(user);
         if (SecurityUtils.getLoginUserId().equals(userId)) {
            SecurityUtils.getUserContainer().getUser().setPassword(user.getPassword());
            V2TokenUtils.updateUserInfoOnUserContainer(SecurityUtils.getUserContainer().getUser());
         }

         BaseUser ftpUser = new BaseUser();
         ftpUser.setName(userId);
         ftpUser.setPassword(tempPW);
         UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
         userMgr.save(ftpUser);
         resource.setResetPassword(tempPW);
         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public ModelAndView userExport(V2UserExportResource filter, String exportType, HttpServletResponse response, String localeData) throws Exception {
      if (StrUtils.nvl(localeData).equals("")) {
         String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
         if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
            localeData = userLocale;
         } else {
            localeData = "en";
         }
      }

      Locale locale = new Locale(localeData);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      User pUser = SecurityUtils.getLoginUser();
      UserInfo userInfo = UserInfoImpl.getInstance();
      String groupType = filter.getGroupType();
      String searchText = StrUtils.nvl(filter.getSearchText());
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      searchText = searchText.toUpperCase();
      Long organizationId = filter.getOrganizationId();
      if (organizationId == null) {
         organizationId = pUser.getRoot_group_id();
      }

      Long groupId = filter.getGroupId();
      if (groupId != null) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, groupId);
      }

      String roleName = filter.getRoleName();
      String startCreatedDate = filter.getStartCreatedDate();
      String endCreatedDate = filter.getEndCreatedDate();
      String sortColumn = filter.getSortColumn();
      if (sortColumn.equalsIgnoreCase("organization_name")) {
         sortColumn = sortColumn.replace("_name", "");
      }

      String sortOrder = filter.getSortOrder();
      Long root_group_id = SecurityUtils.getLoginUser().getRoot_group_id();
      String fileExtension = "xls";
      if (exportType.equalsIgnoreCase("PDF")) {
         fileExtension = "pdf";
      }

      String root_group_name = rms.getMessage("TEXT_ROOT_GROUP_NAME_P", (Object[])null, new Locale(locale.getLanguage()));
      Map dataMap = new HashMap();
      String user_id;
      String user_name;
      String email;
      String mobileNumber;
      String sheetName;
      String resultList;
      String jobPosition;
      if (!groupType.equalsIgnoreCase("ROLE")) {
         user_id = rms.getMessage("COM_DID_ADMIN_USER_USERID", (Object[])null, new Locale(locale.getLanguage()));
         user_name = rms.getMessage("COM_TEXT_USER_NAME_P", (Object[])null, new Locale(locale.getLanguage()));
         email = rms.getMessage("COM_TEXT_EMAIL_P", (Object[])null, new Locale(locale.getLanguage()));
         String phoneNumber = rms.getMessage("COM_MIS_TEXT_PHONE_NUMBER_P", (Object[])null, new Locale(locale.getLanguage()));
         mobileNumber = rms.getMessage("TEXT_MOBILE_NUM_P", (Object[])null, new Locale(locale.getLanguage()));
         sheetName = rms.getMessage("MIS_SID_PW_MODIFIED_DATE", (Object[])null, new Locale(locale.getLanguage()));
         String organization = rms.getMessage("SETUP_NEW_STRING29_P", (Object[])null, new Locale(locale.getLanguage()));
         String group_name = rms.getMessage("COM_TABLE_GROUP_NAME_P", (Object[])null, new Locale(locale.getLanguage()));
         resultList = rms.getMessage("TABLE_ROLE_NAME_P", (Object[])null, new Locale(locale.getLanguage()));
         String organizationGroup = rms.getMessage("MIS_SID_ORGANIZATION_GROUP", (Object[])null, new Locale(locale.getLanguage()));
         String team = rms.getMessage("DID_ADMIN_USER_TEAM", (Object[])null, new Locale(locale.getLanguage()));
         jobPosition = rms.getMessage("DID_ADMIN_USER_POSITION", (Object[])null, new Locale(locale.getLanguage()));
         String create_date = rms.getMessage("TEXT_JOIN_DATE_P", (Object[])null, new Locale(locale.getLanguage()));
         String last_login_date = rms.getMessage("TEXT_LAST_LOGIN_DATE_P", (Object[])null, new Locale(locale.getLanguage()));
         String approval_type = rms.getMessage("TABLE_APPROVAL_TYPE_P", (Object[])null, new Locale(locale.getLanguage()));
         String ldap_user_id = "LDAP ID";
         String fileName = "UserList." + fileExtension;
         String sheetName = "User";
         String[] columnNames = new String[]{"user_id", "user_name", "email", "phone_num", "mobile_num", "password_change_date", "organization", "group_name", "role_name", "organization_group", "team", "job_position", "ldap_user_id", "create_date", "last_login_date"};
         String[] fieldNames = new String[]{user_id, user_name, email, phoneNumber, mobileNumber, sheetName, organization, group_name, resultList, organizationGroup, team, jobPosition, ldap_user_id, create_date, last_login_date};
         List resultList = null;
         SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
         Timestamp startTime = null;
         Timestamp endTime = null;
         String endDate;
         Date parsedDate;
         if (!StrUtils.nvl(startCreatedDate).equals("")) {
            endDate = startCreatedDate + " 00:00:00";
            parsedDate = dateFormat.parse(endDate);
            startTime = new Timestamp(parsedDate.getTime());
         }

         if (!StrUtils.nvl(endCreatedDate).equals("")) {
            endDate = endCreatedDate + " 23:59:59";
            parsedDate = dateFormat.parse(endDate);
            endTime = new Timestamp(parsedDate.getTime());
         }

         Map map = new HashMap();
         map.put("groupType", groupType);
         map.put("searchText", searchText);
         map.put("sortColumn", sortColumn);
         map.put("sortOrder", sortOrder);
         map.put("organizationId", organizationId);
         if (groupType.equalsIgnoreCase("GROUPED")) {
            map.put("groupId", groupId);
            map.put("roleName", roleName);
            map.put("startCreatedDate", startTime);
            map.put("endCreatedDate", endTime);
         } else if (groupType.equalsIgnoreCase("UNAPPROVED")) {
            map.put("organization", organization);
            columnNames = new String[]{"approval_type", "user_id", "user_name", "email", "phone_num", "mobile_num", "organization", "team", "job_position"};
            fieldNames = new String[]{approval_type, user_id, user_name, email, phoneNumber, mobileNumber, organization, team, jobPosition};
         } else {
            if (organizationId != null) {
               map.put("groupId", groupId);
            }

            map.put("roleName", roleName);
            map.put("startCreatedDate", startTime);
            map.put("endCreatedDate", endTime);
            map.put("root_group_id", root_group_id);
         }

         resultList = userInfo.getfilterExport(map);
         int dataListSize = false;
         Object[] dataList = null;
         if (resultList != null) {
            int dataListSize = resultList.size();
            dataList = new Object[dataListSize];

            for(int index = 0; index < dataListSize; ++index) {
               User user = (User)resultList.get(index);
               if (!groupType.equalsIgnoreCase("UNAPPROVED")) {
                  if (user.getGroup_name().equals("ROOT")) {
                     user.setGroup_name(root_group_name);
                  }
               } else {
                  String sid = UserConstants.getSID(user.getApproval_type());
                  String type = rms.getMessage(sid, (Object[])null, rms.getMessage(sid, (Object[])null, new Locale("en")), locale);
                  user.setApproval_type(type);
               }

               user.setUser_id(user.getUser_id());
               user.setUser_name(this.piiDataManager.decryptData(user.getUser_name()));
               user.setEmail(this.piiDataManager.decryptData(user.getEmail()));
               user.setPhone_num(this.piiDataManager.decryptData(user.getPhone_num()));
               user.setMobile_num(this.piiDataManager.decryptData(user.getMobile_num()));
               dataList[index] = user;
            }
         }

         dataMap.put("fileName", fileName);
         dataMap.put("sheetName", sheetName);
         dataMap.put("columnNames", columnNames);
         dataMap.put("fieldNames", fieldNames);
         dataMap.put("dataList", dataList);
      } else if (groupType.equalsIgnoreCase("ROLE")) {
         user_id = rms.getMessage("TABLE_ROLE_NAME_P", (Object[])null, new Locale(locale.getLanguage()));
         user_name = rms.getMessage("TABLE_USER_COUNT_P", (Object[])null, new Locale(locale.getLanguage()));
         email = rms.getMessage("SETUP_NEW_STRING29_P", (Object[])null, new Locale(locale.getLanguage()));
         rms.getMessage("TEXT_ROLE_SCOPE_P", (Object[])null, new Locale(locale.getLanguage()));
         mobileNumber = "RoleList." + fileExtension;
         sheetName = "Role";
         String[] columnNames = new String[]{"role_name", "organization", "user_count"};
         String[] fieldNames = new String[]{user_id, email, user_name};
         resultList = null;
         RoleInfo roleInfo = RoleInfoImpl.getInstance();
         Role role = roleInfo.getAllByRoleName(SecurityUtils.getLoginUser().getRole_name());
         jobPosition = userInfo.getOrganNameByRootGroupId(organizationId);
         Map map = new HashMap();
         map.put("searchText", searchText);
         map.put("sortColumn", sortColumn);
         map.put("sortOrder", sortOrder);
         map.put("organization", jobPosition);
         map.put("root_group_id", root_group_id);
         map.put("scope", RoleUtils.SCOPE_GROUP);
         List resultList = roleInfo.getRoleExport(map);
         int dataListSize = false;
         Object[] dataList = null;
         if (resultList != null) {
            int dataListSize = resultList.size();
            dataList = new Object[dataListSize];

            for(int index = 0; index < dataListSize; ++index) {
               role = (Role)resultList.get(index);
               dataList[index] = role;
            }
         }

         dataMap.put("fileName", mobileNumber);
         dataMap.put("sheetName", sheetName);
         dataMap.put("columnNames", columnNames);
         dataMap.put("fieldNames", fieldNames);
         dataMap.put("dataList", dataList);
      }

      if (exportType.equalsIgnoreCase("PDF")) {
         PdfBuilder pdfView = new PdfBuilder();
         return new ModelAndView(pdfView, dataMap);
      } else {
         this.downloadService = new DeviceStatisticsDownloadService();
         this.downloadService.downloadExcelFile(dataMap, response);
         return null;
      }
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public List permissionGroup(String userId, Long selectGroupId) throws Exception {
      if (userId != null && selectGroupId != null) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_NOT_SAMETIME, new String[]{"userId and groupId"});
      } else if (StrUtils.isEmpty(userId) && StrUtils.isEmpty(selectGroupId.toString())) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_NOT_NULL_OR_EMPTY, new String[]{"userId and groupId"});
      } else {
         List res = new ArrayList();
         UserContainer userContainer = SecurityUtils.getUserContainer();
         UserInfo userInfo = UserInfoImpl.getInstance();
         DeviceGroupInfo deviceGroup = DeviceGroupInfoImpl.getInstance();
         User user = null;
         long userOrganizationId = 0L;
         List groupList = new ArrayList();
         List deviceList;
         Iterator var12;
         DeviceGroup group;
         if (userId != null) {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, userId);
            userId = StrUtils.nvl(userId);
            user = userInfo.getAllByUserId(userId);
            if (user == null) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_USER_NOT_EXIST);
            }

            if (!DeviceUtils.isDeviceGroupAuth(user)) {
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
            }

            if (user.isMu()) {
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_USER_ID_MU);
            }

            deviceList = deviceGroup.getPermissionsDeviceGroupList(userId);
            if (deviceList != null && deviceList.size() > 0) {
               var12 = deviceList.iterator();

               while(var12.hasNext()) {
                  group = (DeviceGroup)var12.next();
                  groupList.add(group.getGroup_id());
               }
            }

            String getOrgName = userInfo.getOrganNameByUserId(userId);
            if (!StrUtils.isEmpty(getOrgName)) {
               userOrganizationId = deviceGroup.getOrganGroupIdByName(getOrgName);
            }
         }

         String type;
         if (selectGroupId != null) {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, selectGroupId);
            UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
            long userOrgId = userGroupInfo.getOrganizationGroupIdByGroupId(Long.valueOf(selectGroupId));
            ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
            Map settingsMap = serverSetupDao.getServerInfoByOrgId(userOrgId);
            boolean isDevicePermission = (Boolean)settingsMap.get("device_permissions");
            if (!isDevicePermission) {
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_NOT_HAVE, new String[]{"device"});
            }

            user = userContainer.getUser();
            if (userId == null) {
               userId = user.getUser_id();
            }

            if (!StrUtils.isEmpty(selectGroupId.toString()) && user.getRole_name() != null && user.getRole_name().equals("Server Administrator")) {
               type = userGroupInfo.getGroupNameByGroupId(userOrgId);
               userOrganizationId = deviceGroup.getOrganGroupIdByName(type);
            }
         }

         deviceList = null;
         if (userContainer.getUser().getRoot_group_id() == 0L) {
            List deviceOrgIds = DeviceUtils.getOrgGroupIdByUserId(userContainer.getUser().getUser_id());
            if (deviceOrgIds != null && deviceOrgIds.size() > 0) {
               deviceList = deviceGroup.getAllDeviceGroupsByGroupName(deviceOrgIds, (String)null);
            }
         }

         if (deviceList == null) {
            deviceList = deviceGroup.getAllDeviceGroupsByGroupName(userOrganizationId, (String)null);
         }

         if (deviceList != null && deviceList.size() > 0) {
            var12 = deviceList.iterator();

            while(var12.hasNext()) {
               group = (DeviceGroup)var12.next();
               new V2DeviceGroupTreeResource();
               Long groupId = group.getGroup_id();
               Long parentGroupId = group.getP_group_id();
               type = "";
               String groupName = "";
               Long deviceCount = 0L;
               Long priority = 0L;
               boolean isDataExist = false;
               boolean disabled = false;
               boolean opened = false;
               boolean selected = false;
               Long depth = group.getGroup_depth();
               Long minPriority = null;
               if (group.getGroup_depth() == 1L) {
                  type = "ORGANIZATION";
                  groupName = group.getGroup_name();
                  disabled = true;
                  parentGroupId = group.getP_group_id();
               } else {
                  parentGroupId = group.getP_group_id();
                  minPriority = group.getMin_priority();
                  String groupType = group.getGroup_type();
                  String deviceType = DeviceUtils.getDeviceType(minPriority, groupType);
                  type = deviceType;
                  if (priority > minPriority) {
                     disabled = true;
                  }

                  groupName = group.getGroup_name();
                  deviceCount = group.getDevice_count();
                  if (groupList != null && groupList.size() > 0 && groupList.contains(group.getGroup_id())) {
                     selected = true;
                     opened = true;
                  }
               }

               V2DeviceGroupTreeResource treeResource = V2DeviceGroupTreeResource.V2DeviceGroupTreeResourceBuilder.aV2DeviceGroupTreeResource().groupId(groupId).groupName(groupName).parentGroupId(parentGroupId).priority(minPriority).type(type).resourceCount(deviceCount).isDataExist(isDataExist).statusDisapled(disabled).statusOpened(opened).statusSelected(selected).groupDepth(depth).videoWallMode(false).deviceCountIncludedSubGroup(0L).build();
               res.add(treeResource);
            }

            return res;
         } else {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"device information"});
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2DashboardUserInfoResource listDashboardUserInfo() throws SQLException {
      Map map = new HashMap();
      V2DashboardUserInfoResource data = new V2DashboardUserInfoResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String user = userContainer.getUser().getOrganization();
      map.put("organization", user);
      UserInfo userInfo = UserInfoImpl.getInstance();
      int totalInCount = userInfo.getCountAllUser(map);
      int totalOutCount = userInfo.getCountAllWithdrawalUser(map);
      int nonApprovalCount = userInfo.getCountAllNonApprovedUser(map);
      data.setTotalInCount(totalInCount);
      data.setTotalOutCount(totalOutCount);
      data.setUnapprovedCount(nonApprovalCount);
      return data;
   }

   private V2UserResource getUserInfomation(User user, V2UserResource resource) throws Exception {
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      boolean isMultiOrganizationManager = false;
      String approvalStr;
      if (user.isMu()) {
         V2MultiOrganizationInfoResource multiUserInfoData = new V2MultiOrganizationInfoResource();
         isMultiOrganizationManager = true;
         String multiUserId = user.getUser_id();
         OrganizationGroupInfo organizationGroupInfo = OrganizationGroupInfoImpl.getInstance();
         String organizationGroupName = userGroupInfo.getCurMngOrgGroup(multiUserId);
         if (organizationGroupName != null) {
            List mngOrgIdList = organizationGroupInfo.getMngOrgIdListByUserId(multiUserId);
            userGroupInfo.getCurMngOrgGroup(multiUserId);
            Long currentOrganizationId = userInfo.getCurMngOrgId(multiUserId);
            approvalStr = userInfo.getOrganGroupName(currentOrganizationId);
            if (!StrUtils.isEmpty(approvalStr)) {
               Long organizationGroupId = organizationGroupInfo.getOrganizationGroupIdByOrganizationGroupName(organizationGroupName);
               multiUserInfoData.setOrganizationGroupId(organizationGroupId);
               multiUserInfoData.setOrganizationGroupName(organizationGroupName);
               multiUserInfoData.setCurrentOrganizationId(currentOrganizationId);
               multiUserInfoData.setCurrentOrganizationName(approvalStr);
               List orgList = new ArrayList();
               Iterator var15 = mngOrgIdList.iterator();

               while(var15.hasNext()) {
                  Long orgId = (Long)var15.next();
                  V2CommonOrganizationData orgData = new V2CommonOrganizationData();
                  orgData.setOrganizationId(orgId);
                  orgData.setOrganizationName(userGroupInfo.getGroupNameByGroupId(orgId));
                  orgList.add(orgData);
               }

               multiUserInfoData.setOrganizationList(orgList);
               resource.setMultiOrganizationInfo(multiUserInfoData);
            } else {
               isMultiOrganizationManager = false;
            }
         }
      }

      resource.setIsMultiOrganizationManager(isMultiOrganizationManager);
      AbilityUtils ability = new AbilityUtils();
      boolean canWriteUser = ability.checkAuthority("User Write");
      boolean hasDeviceSecurity = ability.checkAuthority("Device Security");
      boolean isServerAdmin = false;
      if (user.getRole_name() != null && RoleUtils.isServerAdminRole(user)) {
         isServerAdmin = true;
      }

      String curMngOrg = "";
      if (isMultiOrganizationManager) {
         curMngOrg = userGroupInfo.getCurMngOrgGroup(user.getUser_id());
      }

      String userStatus = "APPROVED";
      if (user.getIs_deleted().equals("Y")) {
         userStatus = "WITHDRAWN";
      } else if (user.getIs_approved().equals("N")) {
         userStatus = "NONAPPROVED";
      }

      if (userStatus.equalsIgnoreCase("NONAPPROVED")) {
         approvalStr = "";
         if (user.getApproval_type().equals("TEXT_MOVE_ORGAN_P")) {
            approvalStr = "MOVE ORGANIZATION";
         } else if (user.getApproval_type().equals("TEXT_NEW_P")) {
            approvalStr = "NEW";
         }

         resource.setUnApprovedCause(approvalStr);
      } else if (userStatus.equalsIgnoreCase("WITHDRAWN")) {
         resource.setWithdrawalDate(user.getWithdrawal_date());
      } else {
         boolean device_permission = DeviceUtils.isDeviceGroupAuth(user);
         resource.setHasDevicePermission(device_permission);
         String ldapId = user.getLdap_user_id();
         if (StringUtils.isBlank(ldapId)) {
            ldapId = "";
         }

         resource.setLdapId(ldapId);
         resource.setJoinDate(user.getCreate_date());
      }

      V2UserInfoResource userInfoData = new V2UserInfoResource();
      userInfoData.setUserId(user.getUser_id());
      userInfoData.setUserName(this.piiDataManager.decryptData(user.getUser_name()));
      userInfoData.setEmail(this.piiDataManager.decryptData(user.getEmail()));
      userInfoData.setTeam(user.getTeam());
      userInfoData.setJobPosition(user.getJob_position());
      userInfoData.setPhoneNum(this.piiDataManager.decryptData(user.getPhone_num()));
      userInfoData.setMobileNum(this.piiDataManager.decryptData(user.getMobile_num()));
      resource.setUserInfo(userInfoData);
      resource.setServerAdmin(isServerAdmin);
      resource.setOrganizationName(user.getOrganization());
      resource.setGroupId(user.getGroup_id());
      resource.setRoleName(user.getRole_name());
      resource.setSignUpDate(user.getCreate_date());
      resource.setLastLoginDate(user.getLast_login_date());
      resource.setCanWriteUser(String.valueOf(canWriteUser));
      resource.setCurMngOrg(curMngOrg);
      resource.setPasswordChangeDate(user.getPassword_change_date());
      resource.setUserStatus(userStatus);
      resource.setLocale(StrUtils.nvl(user.getLocale()));
      resource.setHasDeviceSecurity(hasDeviceSecurity);
      Boolean needPasswordReset = false;
      Boolean isFirstLogin = false;
      if (user.getLogin_count() < 2L) {
         isFirstLogin = true;
      }

      if (!StringUtils.isNotBlank(user.getLdap_info())) {
         if (user.getIs_reset_pwd() != null && user.getIs_reset_pwd().equals("Y")) {
            needPasswordReset = true;
         }
      } else {
         resource.setLdapId(user.getLdap_user_id());
      }

      boolean changePW = false;
      boolean isExpiredPW = false;
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map serverInfoMap = serverSetupDao.getServerInfoByOrgId(0L);
      if (needPasswordReset) {
         String passwordLoginChangeEnabled = "";
         if (serverInfoMap.get("PASSWORD_LOGIN_CHANGE_ENABLED") != null) {
            passwordLoginChangeEnabled = serverInfoMap.get("PASSWORD_LOGIN_CHANGE_ENABLED").toString();
         }

         if (Boolean.valueOf(passwordLoginChangeEnabled)) {
            changePW = true;
         }

         long month = (Long)serverInfoMap.get("PASSWORD_CHANGE_PERIOD");
         if (month > 0L) {
            DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd");
            DateTime now = new DateTime();
            DateTime d1 = DateTime.parse(now.plusMonths(-1 * (int)month).toString("yyyy-MM-dd"), fmt);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String passwordChangeDate = sdf.format(user.getPassword_change_date());
            DateTime d2 = DateTime.parse(passwordChangeDate, fmt);
            if (d1.compareTo(d2) > 0) {
               isExpiredPW = true;
            }
         }
      }

      resource.setNeedPasswordReset(needPasswordReset);
      resource.setIsFirstLogin(isFirstLogin);
      resource.setPasswordChangeAfterLogin(changePW);
      resource.setIsExpiredPassword(isExpiredPW);
      return resource;
   }
}
