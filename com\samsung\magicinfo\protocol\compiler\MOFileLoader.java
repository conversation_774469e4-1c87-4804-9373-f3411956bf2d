package com.samsung.magicinfo.protocol.compiler;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.protocol.compiler.parser.Xerces;
import java.io.File;
import java.io.FileNotFoundException;
import java.util.HashMap;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import javax.xml.validation.Validator;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;
import org.xml.sax.SAXNotRecognizedException;
import org.xml.sax.SAXNotSupportedException;
import org.xml.sax.SAXParseException;

public class MOFileLoader {
   Logger logger = LoggingManagerV2.getLogger(MOFileLoader.class);
   private final String XML_NAMESPACE = "xmlns";
   private final String LOCATION = "location";
   protected String moFileDir = "";

   public MOFileLoader() {
      super();
   }

   public ValidateResult validateMOSchema(String moSchemaPath) {
      try {
         this.getValidatorFromSchemaFile(moSchemaPath);
      } catch (SAXParseException var3) {
         return new ValidateResult(false, var3.getMessage());
      } catch (Exception var4) {
         return new ValidateResult(false, var4.getMessage());
      }

      return new ValidateResult(true, "");
   }

   private Validator getValidatorFromSchemaFile(String schemaFilePath) throws SAXParseException, Exception {
      boolean schemaFullChecking = false;
      Validator validator = null;

      try {
         Xerces xerces = new Xerces();
         SchemaFactory factory = DocumentUtils.getSchemaFactoryInstance();
         factory.setErrorHandler(xerces);

         try {
            factory.setFeature("http://apache.org/xml/features/validation/schema-full-checking", schemaFullChecking);
         } catch (SAXNotRecognizedException var12) {
         } catch (SAXNotSupportedException var13) {
         }

         File moSchemaFile = SecurityUtils.getSafeFile(schemaFilePath);
         StreamSource source = new StreamSource(moSchemaFile);
         Schema schema = factory.newSchema(source);
         validator = schema.newValidator();
         validator.setErrorHandler(xerces);

         try {
            validator.setFeature("http://apache.org/xml/features/validation/schema-full-checking", schemaFullChecking);
         } catch (SAXNotRecognizedException var10) {
         } catch (SAXNotSupportedException var11) {
         }

         return validator;
      } catch (SAXParseException var14) {
         throw var14;
      } catch (Exception var15) {
         throw var15;
      }
   }

   public ValidateResult validateMOTypeSchema(String moTypeSchemaPath) {
      try {
         this.getValidatorFromSchemaFile(moTypeSchemaPath);
      } catch (SAXParseException var4) {
         return new ValidateResult(false, var4.getMessage());
      } catch (Exception var5) {
         Exception e = var5;
         if (var5 instanceof SAXException) {
            Exception nested = ((SAXException)var5).getException();
            if (nested != null) {
               e = nested;
            }
         }

         return new ValidateResult(false, e.getMessage());
      }

      return new ValidateResult(true, "");
   }

   public ValidateResult validateMOFile(String moFilePath, String moSchemaPath) {
      try {
         SchemaFactory sf = DocumentUtils.getSchemaFactoryInstance();
         Schema schema = sf.newSchema(SecurityUtils.getSafeFile(moSchemaPath));
         Validator validator = schema.newValidator();
         StreamSource ss = new StreamSource(SecurityUtils.getSafeFile(moFilePath));
         validator.validate(ss);
      } catch (Exception var7) {
         return new ValidateResult(false, var7.getMessage());
      }

      return new ValidateResult(true, "");
   }

   public Document mergeMOFiles(String moFilePath) throws Exception {
      Document mergedDoc = null;
      this.moFileDir = this.getDirFromFilePath(moFilePath);

      try {
         Xerces parser = new Xerces();
         Document moDoc = null;
         moDoc = parser.parse(moFilePath);
         Node moSchemaNode = this.parseMOSchemaDoc(moDoc);
         HashMap namespaceHashMap = this.saveNamespace(moSchemaNode);
         HashMap importedXMLDocHashMap = this.parseImportedXML(moSchemaNode);
         mergedDoc = this.mergeMOFile(moDoc, namespaceHashMap, importedXMLDocHashMap);
         return mergedDoc;
      } catch (Exception var8) {
         this.logger.error("", var8);
         throw var8;
      }
   }

   protected HashMap parseImportedXML(Node moSchemaNode) throws Exception {
      Xerces parser = new Xerces();
      HashMap importedXMLDocHashMap = new HashMap();
      String namespace = null;
      String location = null;
      String fullpath = null;
      NodeList moSchemaChildNodeList = moSchemaNode.getChildNodes();

      for(int j = 0; j < moSchemaChildNodeList.getLength(); ++j) {
         Node moSchemaChildNode = moSchemaChildNodeList.item(j);
         if (moSchemaChildNode.getNodeType() == 1 && moSchemaChildNode.getNodeName().equals("IMPORT")) {
            NamedNodeMap attribs = moSchemaChildNode.getAttributes();
            if (attribs != null) {
               for(int i = 0; i < attribs.getLength(); ++i) {
                  Node attNode = attribs.item(i);
                  String attName = attNode.getNodeName().trim();
                  String attValue = attNode.getNodeValue().trim();
                  if (attName.equals("namespace")) {
                     namespace = attValue;
                  }

                  if (attName.equals("location")) {
                     location = attValue;
                     fullpath = this.moFileDir + attValue;
                  }

                  if (namespace != null && location != null) {
                     this.logger.debug(namespace + " : " + namespace);
                     this.logger.debug("location : " + location);
                     Document doc = null;

                     try {
                        doc = parser.parse(fullpath);
                     } catch (FileNotFoundException var17) {
                        throw new Exception("Import MO 파일 " + location + "을 찾을 수 없습니다. 파일이 없거나 유효한 파일명이 아닙니다.");
                     }

                     importedXMLDocHashMap.put(namespace, doc);
                  }
               }

               namespace = null;
               location = null;
            }
         }
      }

      return importedXMLDocHashMap;
   }

   public String getDirFromFilePath(String fileName) {
      int index = fileName.lastIndexOf("/");
      if (index >= 0) {
         return fileName.substring(0, index + 1);
      } else {
         index = fileName.lastIndexOf("\\");
         return index >= 0 ? fileName.substring(0, index + 1) : "";
      }
   }

   public Node parseMOSchemaDoc(Document moDoc) {
      Node moSchemaNode = null;
      if (moDoc.hasChildNodes()) {
         NodeList nodeList = moDoc.getChildNodes();

         for(int i = 0; i < nodeList.getLength(); ++i) {
            Node node = nodeList.item(i);
            if (node.getNodeType() == 1 && node.getNodeName().trim().equals("MOSCHEMA")) {
               moSchemaNode = node;
               break;
            }
         }
      }

      return moSchemaNode;
   }

   protected HashMap saveNamespace(Node moSchemaNode) {
      HashMap namespaceHashMap = new HashMap();
      NamedNodeMap attribs = moSchemaNode.getAttributes();
      if (attribs != null) {
         for(int i = 0; i < attribs.getLength(); ++i) {
            Node attNode = attribs.item(i);
            String attName = attNode.getNodeName().trim();
            String attValue = attNode.getNodeValue().trim();
            if (attName != null && attName.indexOf("xmlns") >= 0 && attValue != null) {
               namespaceHashMap.put(attName, attValue);
            }
         }
      }

      return namespaceHashMap;
   }

   public Node parseSchemaImportDoc(Document moDoc) {
      Node schemaImportNode = null;
      NodeList nodeList = moDoc.getElementsByTagName("SCHEMA_IMPORT");
      if (null != nodeList) {
         schemaImportNode = nodeList.item(0);
      }

      return schemaImportNode;
   }

   public String getLocation(Node importNode) {
      String location = "";
      NamedNodeMap attribs = importNode.getAttributes();
      if (attribs != null) {
         for(int i = 0; i < attribs.getLength(); ++i) {
            Node attNode = attribs.item(i);
            String attName = attNode.getNodeName().trim();
            String attValue = attNode.getNodeValue().trim();
            if (attName != null && attName.indexOf("location") >= 0 && attValue != null) {
               location = attValue;
            }
         }
      }

      return location;
   }

   protected Document mergeMOFile(Document moDoc, HashMap namespaceHashMap, HashMap importedXMLDocHashMap) throws Exception {
      Document mergedMODoc = null;
      if (moDoc.hasChildNodes()) {
         NodeList nodeList = moDoc.getChildNodes();

         for(int i = 0; i < nodeList.getLength(); ++i) {
            Node node = nodeList.item(i);
            if (node.getNodeType() == 1 && node.getNodeName().trim().equals("MOSCHEMA")) {
               NodeList moSchemaChildNodeList = node.getChildNodes();

               for(int j = 0; j < moSchemaChildNodeList.getLength(); ++j) {
                  Node moSchemaChildNode = moSchemaChildNodeList.item(j);
                  if (moSchemaChildNode.getNodeType() == 1 && moSchemaChildNode.getNodeName().equals("OBJECT")) {
                     mergedMODoc = this.parse4Merge(moDoc, moSchemaChildNode, namespaceHashMap, importedXMLDocHashMap);
                  }
               }
            }
         }
      }

      return mergedMODoc;
   }

   private Document parse4Merge(Document moDoc, Node objectNode, HashMap namespaceHashMap, HashMap importedXMLDocHashMap) throws Exception {
      String qName = null;
      if (objectNode.hasChildNodes()) {
         NodeList nodeList = objectNode.getChildNodes();

         for(int i = 0; i < nodeList.getLength(); ++i) {
            Node node = nodeList.item(i);
            if (node.getNodeType() == 1) {
               if (node.getNodeName().equals("OBJECTREF")) {
                  NodeList objectRefNodeList = node.getChildNodes();

                  Node parentNode;
                  Node newNode;
                  for(int j = 0; j < objectRefNodeList.getLength(); ++j) {
                     parentNode = objectRefNodeList.item(j);
                     if (parentNode.getNodeType() == 1 && parentNode.getNodeName().equals("NAME")) {
                        newNode = parentNode.getFirstChild();
                        qName = newNode.getNodeValue();
                     }
                  }

                  Node importedNode = this.generateImportedNode(qName, namespaceHashMap, importedXMLDocHashMap);
                  if (importedNode != null) {
                     parentNode = node.getParentNode();
                     newNode = moDoc.importNode(importedNode, true);
                     parentNode.replaceChild(newNode, node);
                  } else {
                     System.out.println("importedNode == null");
                  }
               }

               if (node.getNodeName().equals("OBJECT")) {
                  moDoc = this.parse4Merge(moDoc, node, namespaceHashMap, importedXMLDocHashMap);
               }
            }
         }
      }

      return moDoc;
   }

   private Node generateImportedNode(String qName, HashMap namespaceHashMap, HashMap importedXMLDocHashMap) throws Exception {
      int delimPos = qName.indexOf(":");
      String prefix = qName.substring(0, delimPos);
      String localName = qName.substring(delimPos + 1);
      String namespace = (String)namespaceHashMap.get("xmlns:" + prefix);
      Document importedDoc = (Document)importedXMLDocHashMap.get(namespace);
      if (importedDoc == null) {
         return null;
      } else {
         Node importedNode = this.gernerateNodeFragment(importedDoc, localName);
         return importedNode;
      }
   }

   private Node gernerateNodeFragment(Document importedDoc, String nodeFragmentName) {
      Node nodeFragment = null;
      if (importedDoc.hasChildNodes()) {
         NodeList nodeList = importedDoc.getChildNodes();

         for(int i = 0; i < nodeList.getLength(); ++i) {
            Node node = nodeList.item(i);
            if (node.getNodeType() == 1 && node.getNodeName().trim().equals("MOSCHEMA")) {
               NodeList moSchemaChildNodeList = node.getChildNodes();

               for(int j = 0; j < moSchemaChildNodeList.getLength(); ++j) {
                  Node moSchemaChildNode = moSchemaChildNodeList.item(j);
                  if (moSchemaChildNode.getNodeType() == 1 && moSchemaChildNode.getNodeName().equals("OBJECT")) {
                     nodeFragment = this.parseNodeFragment(moSchemaChildNode, nodeFragmentName);
                  }
               }
            }
         }
      }

      return nodeFragment;
   }

   private Node parseNodeFragment(Node objectNode, String nodeFragmentName) {
      Node nodeFragment = null;
      String nodeName = null;
      if (objectNode.hasChildNodes()) {
         NodeList nodeList = objectNode.getChildNodes();

         for(int i = 0; i < nodeList.getLength(); ++i) {
            Node node = nodeList.item(i);
            if (node.getNodeType() == 1) {
               if (node.getNodeName().trim().equals("NAME")) {
                  Node nodeNameNode = node.getFirstChild();
                  nodeName = nodeNameNode.getNodeValue();
                  if (nodeName != null && nodeName.equals(nodeFragmentName)) {
                     nodeFragment = objectNode;
                     break;
                  }
               }

               if (node.getNodeName().equals("OBJECT")) {
                  nodeFragment = this.parseNodeFragment(node, nodeFragmentName);
                  if (nodeFragment != null) {
                     break;
                  }
               }
            }
         }
      }

      return nodeFragment;
   }

   public Document getMOTypeSchemaDocument(String moTypeSchemaPath) {
      Document moTypeSchemaDoc = null;

      try {
         Xerces parser = new Xerces();
         moTypeSchemaDoc = parser.parse(moTypeSchemaPath);
      } catch (Exception var4) {
         System.out.println(var4);
      }

      return moTypeSchemaDoc;
   }

   public DuplicateNodeResult hasDuplicateMOTreeNodeName(Document mergedDoc) {
      DuplicateNodeResult isDuplicate = new DuplicateNodeResult(false, "");
      if (mergedDoc.hasChildNodes()) {
         NodeList nodeList = mergedDoc.getChildNodes();

         for(int i = 0; i < nodeList.getLength(); ++i) {
            Node node = nodeList.item(i);
            if (node.getNodeType() == 1 && node.getNodeName().trim().equals("MOSCHEMA")) {
               NodeList moSchemaChildNodeList = node.getChildNodes();

               for(int j = 0; j < moSchemaChildNodeList.getLength(); ++j) {
                  Node moSchemaChildNode = moSchemaChildNodeList.item(j);
                  if (moSchemaChildNode.getNodeType() == 1 && moSchemaChildNode.getNodeName().equals("OBJECT")) {
                     isDuplicate = this.parseDuplicateMOTreeNodeName(moSchemaChildNode);
                     if (isDuplicate.isDuplicate()) {
                        return isDuplicate;
                     }
                  }
               }
            }
         }
      }

      return isDuplicate;
   }

   public DuplicateNodeResult parseDuplicateMOTreeNodeName(Node objectNode) {
      DuplicateNodeResult isDuplicate = new DuplicateNodeResult(false, "");
      String nodeName = null;
      if (objectNode.hasChildNodes()) {
         NodeList nodeList = objectNode.getChildNodes();

         for(int i = 0; i < nodeList.getLength(); ++i) {
            Node node = nodeList.item(i);
            if (node.getNodeType() == 1) {
               if (node.getNodeName().trim().equals("NAME")) {
                  Node nodeNameNode = node.getFirstChild();
                  nodeName = nodeNameNode.getNodeValue();
                  isDuplicate = this.isDuplicateNodeName(objectNode, nodeName);
                  if (isDuplicate.isDuplicate()) {
                     return isDuplicate;
                  }
               }

               if (node.getNodeName().equals("OBJECT")) {
                  isDuplicate = this.parseDuplicateMOTreeNodeName(node);
                  if (isDuplicate.isDuplicate()) {
                     return isDuplicate;
                  }
               }
            }
         }
      }

      return isDuplicate;
   }

   private DuplicateNodeResult isDuplicateNodeName(Node objectNode, String nodeName) {
      DuplicateNodeResult isDuplicate = new DuplicateNodeResult(false, "");
      String siblingNodeName = null;
      Node siblingNode = objectNode.getNextSibling();
      if (siblingNode != null) {
         if (siblingNode.getNodeType() == 1 && siblingNode.getNodeName().equals("OBJECT")) {
            NodeList nodeList = siblingNode.getChildNodes();

            for(int i = 0; i < nodeList.getLength(); ++i) {
               Node node = nodeList.item(i);
               if (node.getNodeType() == 1 && node.getNodeName().trim().equals("NAME")) {
                  Node nodeNameNode = node.getFirstChild();
                  siblingNodeName = nodeNameNode.getNodeValue();
                  if (nodeName.equals(siblingNodeName)) {
                     return new DuplicateNodeResult(true, siblingNodeName);
                  }
               }
            }
         }

         isDuplicate = this.isDuplicateNodeName(siblingNode, nodeName);
      }

      return isDuplicate;
   }
}
