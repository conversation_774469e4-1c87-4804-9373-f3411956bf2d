package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.exception.repository.CannotAddConvertTableException;
import com.samsung.magicinfo.webauthor2.model.ConvertTable;
import com.samsung.magicinfo.webauthor2.model.ConvertTableUpdateWrapper;
import com.samsung.magicinfo.webauthor2.service.ConvertTableService;
import com.samsung.magicinfo.webauthor2.webapi.assembler.ConvertTablesResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.resource.ConvertTableResource;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/convertTables"})
public class ConvertTableQueryController {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.webapi.controller.ConvertTableQueryController.class);
  
  private ConvertTablesResourceAssembler convertTablesResourceAssembler;
  
  private ConvertTableService convertTableService;
  
  @Autowired
  public ConvertTableQueryController(ConvertTablesResourceAssembler convertTablesResourceAssembler, ConvertTableService convertTableService) {
    this.convertTablesResourceAssembler = convertTablesResourceAssembler;
    this.convertTableService = convertTableService;
  }
  
  @GetMapping
  public HttpEntity<List<ConvertTableResource>> getConvertTablesList() {
    List<ConvertTable> convertTables = this.convertTableService.getConvertTableList();
    return (HttpEntity<List<ConvertTableResource>>)ResponseEntity.ok(this.convertTablesResourceAssembler.toResources(convertTables));
  }
  
  @PostMapping({"/add"})
  public HttpEntity<String> addConvertTable(@RequestBody ConvertTable convertTable) {
    String result = this.convertTableService.addConvertTable(convertTable);
    return (HttpEntity<String>)ResponseEntity.status(HttpStatus.CREATED).body(result);
  }
  
  @PostMapping({"/delete"})
  public HttpEntity<String> deleteConvertTable(@RequestBody String convertTableName) {
    String result = this.convertTableService.delete(convertTableName);
    return (HttpEntity<String>)ResponseEntity.ok(result);
  }
  
  @PutMapping({"/modify"})
  public HttpEntity<String> modifyConvertTable(@RequestBody ConvertTableUpdateWrapper updateWrapper) {
    String result = this.convertTableService.modify(updateWrapper.getOldTable(), updateWrapper.getNewTable());
    return (HttpEntity<String>)ResponseEntity.ok(result);
  }
  
  @ExceptionHandler({CannotAddConvertTableException.class})
  public HttpEntity<Void> cannotAddDataTable(CannotAddConvertTableException ex) {
    logger.error(ex.getMessage(), (Throwable)ex);
    return (HttpEntity<Void>)ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).build();
  }
  
  @ExceptionHandler({Exception.class})
  public HttpEntity<Void> unknownError(Exception ex) {
    logger.error(ex.getMessage(), ex);
    return (HttpEntity<Void>)ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
  }
}
