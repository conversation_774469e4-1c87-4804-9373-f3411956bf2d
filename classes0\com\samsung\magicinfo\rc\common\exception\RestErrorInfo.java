package com.samsung.magicinfo.rc.common.exception;

import io.swagger.annotations.ApiModelProperty;
import java.util.Map;

public class RestErrorInfo {
  @ApiModelProperty("Information on the URL that the error occurred.")
  public final String url;
  
  @ApiModelProperty("Predefined error code number. Refer to 'RestExceptionCode' class.")
  public final String errorCode;
  
  @ApiModelProperty("Predefined error message. Refer to 'RestExceptionCode' class.")
  public final String errorMessage;
  
  @ApiModelProperty("Detail information of the error.")
  public final Map<String, Object> errorDetails;
  
  public RestErrorInfo(String url, String errorCode, String errorMessage, Map<String, Object> errorDetails) {
    this.url = url;
    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
    this.errorDetails = errorDetails;
  }
  
  public RestErrorInfo(String url, String errorCode, String errorMessage) {
    this.url = url;
    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
    this.errorDetails = null;
  }
  
  public String getUrl() {
    return this.url;
  }
  
  public String getErrorCode() {
    return this.errorCode;
  }
  
  public String getErrorMessage() {
    return this.errorMessage;
  }
  
  public Map<String, Object> getErrorDetails() {
    return this.errorDetails;
  }
}
