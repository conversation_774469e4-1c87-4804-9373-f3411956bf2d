package com.samsung.common.utils;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.common.manager.EncryptionManager;
import com.samsung.magicinfo.framework.common.manager.EncryptionManagerImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.sun.jndi.ldap.ctl.PasswordExpiredResponseControl;
import com.sun.jndi.ldap.ctl.PasswordExpiringResponseControl;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import javax.naming.NameClassPair;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.PartialResultException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import javax.naming.directory.DirContext;
import javax.naming.directory.InitialDirContext;
import javax.naming.directory.SearchControls;
import javax.naming.directory.SearchResult;
import javax.naming.ldap.Control;
import javax.naming.ldap.InitialLdapContext;
import javax.naming.ldap.LdapContext;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.directwebremoting.WebContext;
import org.directwebremoting.WebContextFactory;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.ldap.support.LdapEncoder;

public class LDAPUtils {
   protected static Logger logger = LoggingManagerV2.getLogger(LDAPUtils.class);
   private static String AUTH_SIMPLE = "simple";
   public static final String USER_TYPE_MAGICINFO = "MAGICINFO";
   public static final String USER_TYPE_LDAP = "LDAP";
   public static final int UNKNOWN_ERROR = -1;
   public static final int LOGIN_SUCCESS = 0;
   public static final int UNKNOWN_HOST_EXCEPTION = 1;
   public static final int AUTHENTICATION_EXCEPTION = 2;
   public static final int PASSWORD_EXPIRED = 3;
   public static final int OPERATIONS_ERROR = 4;
   public static final int INVALID_DN_SYNTAX = 5;
   public static final int TIMEOUT_ERROR = 6;
   public static final int NICKNAME_CHECK = 20;
   public static final String USER_INFO_MAP_KEY_ID = "id";
   public static final String USER_INFO_MAP_KEY_FULL_ID = "full_id";
   public static final String USER_INFO_MAP_KEY_VIEW = "view_id";
   public static final String USER_INFO_MAP_KEY_MAIL = "mail";
   public static final String USER_INFO_MAP_KEY_PHONE = "phone";
   public static final String USER_INFO_MAP_KEY_MOBILE = "mobile";
   public static final String USER_INFO_MAP_KEY_USERNAME = "username";
   public static final int SEARCH_OPTION_CONTAINS = 0;
   public static final int SEARCH_OPTION_STARTSWITH = 1;
   public static final int SEARCH_OPTION_IS = 2;
   public static final int SEARCH_OPTION_DEFAULT = 1;
   private static HashMap messageMap = new HashMap();

   public LDAPUtils() {
      super();
   }

   private static void initMessageMap() {
      messageMap.put(-1, "MIS_MESSAGE_LDAP_SERVER_CONNECT_FAILED_P");
      messageMap.put(0, "MIS_MESSAGE_SUCCESS_LOGIN_P");
      messageMap.put(1, "MIS_MESSAGE_WRONG_LDAP_SERVER_ADDRESS_P");
      messageMap.put(2, "MIS_MESSAGE_ID_PASSWORD_ENTERED_WRONG_P");
      messageMap.put(3, "MIS_MESSAGE_EXPIRED_PASSWORD_P");
      messageMap.put(4, "MIS_TEXT_CIFS_SERVER_LOGIN_FAILURE_P");
      messageMap.put(5, "MIS_MESSAGE_DN_INFO_WRONG_P");
      messageMap.put(6, "MIS_MESSAGE_TIME_OUT_P");
   }

   public static String getMessage(int code) {
      WebContext ctx = WebContextFactory.get();
      HttpServletRequest request = ctx.getHttpServletRequest();
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      HashMap map = getMessage();
      String message = (String)map.get(code);
      String errorMessage = rms.getMessage(message, (Object[])null, request.getLocale());
      return errorMessage;
   }

   public static String getMessageId(int code) {
      HashMap map = getMessage();
      String message = (String)map.get(code);
      return message;
   }

   public static HashMap getMessage() {
      if (messageMap.size() < 1) {
         initMessageMap();
      }

      return messageMap;
   }

   public static int checkLDAPServer(String url, String dn, String id, String password) {
      int result = true;
      Hashtable env = setInfo(url, dn, id, password);

      try {
         LdapContext ctx = checkLogin(env);
         boolean expired = isExpired(ctx);
         byte result;
         if (expired) {
            result = 3;
         } else {
            result = 0;
         }

         getSearch(env, dn, "(|(objectclass=organizationalUnit)(objectclass=ou))", 1);
         return result;
      } catch (NamingException var11) {
         int result = -1;
         if (var11.getRootCause() != null) {
            if (var11.getRootCause().toString().contains("UnknownHostException")) {
               result = 1;
            } else if (var11.getRootCause().toString().contains("timed out")) {
               result = 6;
            }
         } else if (var11.getLocalizedMessage() != null) {
            if (var11.getLocalizedMessage().toString().contains("AcceptSecurityContext")) {
               result = 2;
            } else if (var11.getLocalizedMessage().toString().contains("Invalid Credentials")) {
               result = 2;
            } else if (var11.getLocalizedMessage().toString().contains("error code 1")) {
               result = 4;
            } else if (var11.getLocalizedMessage().toString().contains("error code 34")) {
               result = 5;
            }
         }

         logger.error("", var11);
         return result;
      } finally {
         ;
      }
   }

   public static Hashtable setInfo(String url, String dn, String id, String password) {
      Hashtable env = new Hashtable();
      env.put("java.naming.factory.initial", "com.sun.jndi.ldap.LdapCtxFactory");
      env.put("java.naming.provider.url", url);
      env.put("java.naming.security.authentication", AUTH_SIMPLE);
      env.put("java.naming.security.principal", id);
      env.put("java.naming.security.credentials", password);
      return env;
   }

   public static LdapContext checkLogin(Hashtable env) throws NamingException {
      InitialLdapContext ctx = null;

      try {
         ctx = new InitialLdapContext(env, (Control[])null);
         return ctx;
      } catch (Exception var3) {
         logger.error("[LDAP LOGIN STATUS] login fail error code : " + var3.getMessage());
         throw var3;
      }
   }

   public static boolean isExpired(LdapContext ctx) throws NamingException {
      Control[] respControls;
      if ((respControls = ctx.getResponseControls()) != null) {
         long secPwdExpire = 0L;

         for(int i = 0; i < respControls.length; ++i) {
            if (respControls[i] instanceof PasswordExpiringResponseControl) {
               secPwdExpire = ((PasswordExpiringResponseControl)respControls[i]).timeRemaining();
               secPwdExpire = (new Date()).getTime() + secPwdExpire * 1000L;
            }

            if (respControls[i] instanceof PasswordExpiredResponseControl) {
               return true;
            }
         }
      }

      ctx.close();
      return false;
   }

   public static boolean isExpired(Hashtable env) throws NamingException {
      boolean result = false;
      LdapContext ctx = new InitialLdapContext(env, (Control[])null);
      result = isExpired((LdapContext)ctx);
      return result;
   }

   public static NamingEnumeration getSearch(Hashtable env, String dn, String filter) throws NamingException {
      DirContext ctx = null;
      NamingEnumeration answer = null;

      try {
         ctx = new InitialDirContext(env);
         SearchControls cons = new SearchControls();
         cons.setSearchScope(2);
         answer = ctx.search(dn, filter, cons);
      } finally {
         if (ctx != null) {
            try {
               ctx.close();
            } catch (Exception var11) {
               logger.error("", var11);
            }
         }

      }

      return answer;
   }

   public static NamingEnumeration getSearch(Hashtable env, String dn, String filter, int searchScope) throws NamingException {
      DirContext ctx = null;
      NamingEnumeration answer = null;

      try {
         ctx = new InitialDirContext(env);
         SearchControls cons = new SearchControls();
         cons.setSearchScope(searchScope);
         answer = ctx.search(dn, filter, cons);
      } finally {
         if (ctx != null) {
            try {
               ctx.close();
            } catch (Exception var12) {
               logger.error("", var12);
            }
         }

      }

      return answer;
   }

   public static ArrayList getInfoList(Hashtable env, String dn) {
      ArrayList resultList = null;
      InitialDirContext initCtx = null;

      try {
         initCtx = new InitialDirContext(env);
         DirContext ctx = (DirContext)initCtx.lookup(dn);
         resultList = showContext(ctx, 1);
      } catch (NamingException var13) {
         logger.error("", var13);
      } finally {
         try {
            initCtx.close();
         } catch (NamingException var12) {
            logger.error("", var12);
         }

      }

      return resultList;
   }

   public static ArrayList showContext(DirContext ctx, int depth) {
      ArrayList resultList = new ArrayList();

      try {
         NamingEnumeration e = ctx.list("");

         while(e.hasMore()) {
            NameClassPair entry = (NameClassPair)e.next();
            resultList.add(entry.getName());
         }

         e.close();
      } catch (NamingException var5) {
         if (!var5.getMessage().contains("Unprocessed Continuation Reference(s)")) {
            logger.error("", var5);
         }
      }

      return resultList;
   }

   public static ArrayList getInfoList(Hashtable env, String dn, boolean viewAttr) throws NamingException {
      ArrayList resultList = new ArrayList();
      DirContext initCtx = new InitialDirContext(env);
      DirContext ctx = null;

      try {
         ctx = (DirContext)initCtx.lookup(dn);
         resultList.add(ctx.getNameInNamespace());
         if (viewAttr) {
            showAttributes(initCtx.getAttributes(ctx.getNameInNamespace()), resultList);
         }

         showContext(ctx, viewAttr, resultList);
      } catch (NamingException var10) {
         showContext(ctx, viewAttr, resultList);
      } finally {
         initCtx.close();
      }

      return resultList;
   }

   public static void showContext(DirContext ctx, boolean viewAttr, ArrayList resultList) throws NamingException {
      NamingEnumeration e = ctx.list("");

      while(e.hasMore()) {
         NameClassPair entry = (NameClassPair)e.next();
         if (viewAttr) {
            showAttributes(ctx.getAttributes(entry.getName()), resultList);
         }

         Object obj = ctx.lookup(entry.getName());
         if (obj instanceof DirContext) {
            resultList.add(ctx.getNameInNamespace());
            showContext((DirContext)obj, viewAttr, resultList);
         }
      }

      e.close();
   }

   public static void showAttributes(Attributes attrs, ArrayList resultList) throws NamingException {
      NamingEnumeration e = attrs.getAll();

      while(e.hasMore()) {
         Attribute attr = (Attribute)e.next();
         resultList.add(attr.toString());
      }

      e.close();
   }

   public static String getMailAttribute(Attributes attrs) throws NamingException {
      String result = null;
      NamingEnumeration e = attrs.getAll();

      while(e.hasMore()) {
         Attribute attr = (Attribute)e.next();
         if ("mail".equalsIgnoreCase(attr.toString())) {
            result = attr.toString();
         }
      }

      e.close();
      return result;
   }

   public static String sanitize(String input) {
      String s = "";

      for(int i = 0; i < input.length(); ++i) {
         char c = input.charAt(i);
         if (c == '(') {
            s = s + "\\28";
         } else if (c == ')') {
            s = s + "\\29";
         } else if (c == '\\') {
            s = s + "\\5c";
         } else if (c == 0) {
            s = s + "\\00";
         } else if (c <= 127) {
            s = s + String.valueOf(c);
         } else if (c >= 128) {
            try {
               byte[] utf8bytes = String.valueOf(c).getBytes("UTF8");
               byte[] var5 = utf8bytes;
               int var6 = utf8bytes.length;

               for(int var7 = 0; var7 < var6; ++var7) {
                  byte b = var5[var7];
                  s = s + String.format("\\%02x", b);
               }
            } catch (UnsupportedEncodingException var9) {
            }
         }
      }

      return s;
   }

   public static ArrayList getSearchUserList(Hashtable env, String dn, String selectDir, String filter, int searchScope) {
      ArrayList resultList = new ArrayList();

      try {
         filter = sanitize(filter);
         int searchOption = 1;
         String searchOptionString = CommonConfig.get("ldap.user.search.option");
         if (StringUtils.isNotBlank(searchOptionString)) {
            try {
               searchOption = Integer.parseInt(searchOptionString);
               if (searchOption > 2) {
                  searchOption = 1;
               }
            } catch (Exception var22) {
            }
         }

         if ((searchOption == 1 || searchOption == 0) && filter.charAt(filter.length() - 1) != '*') {
            filter = filter + "*";
         }

         if (searchOption == 0 && filter.charAt(0) != '*') {
            filter = "*" + filter;
         }

         String searchDn = "";
         if (selectDir.equals("root")) {
            searchDn = dn;
         } else {
            searchDn = selectDir + "," + dn;
         }

         String customFilter = "(&(|(sAMAccountName=" + filter + ")(uid=" + filter + ")(cn=" + filter + "))(objectclass=person))";
         String configSearchFilter = CommonConfig.get("ldap.user.search.attributes");
         if (StringUtils.isNotBlank(configSearchFilter)) {
            try {
               String newCustomFilter = "(&(|";
               String[] userFilterAttributes = configSearchFilter.split(",");
               String[] var13 = userFilterAttributes;
               int var14 = userFilterAttributes.length;

               for(int var15 = 0; var15 < var14; ++var15) {
                  String attr = var13[var15];
                  newCustomFilter = newCustomFilter + "(" + attr + "=" + filter + ")";
               }

               newCustomFilter = newCustomFilter + ")(objectclass=person))";
               customFilter = newCustomFilter;
            } catch (Exception var23) {
            }
         }

         HashMap userMap;
         for(NamingEnumeration e = getSearch(env, searchDn, customFilter, searchScope); e.hasMore(); resultList.add(userMap)) {
            userMap = new HashMap();
            NameClassPair entry = (NameClassPair)e.next();
            userMap.put("id", LdapEncoder.nameDecode(entry.getName()));
            userMap.put("full_id", entry.getNameInNamespace());
            if (entry instanceof SearchResult) {
               SearchResult sr = (SearchResult)entry;
               Attributes attrs = sr.getAttributes();
               Map attrMap = new HashMap();
               attrMap.put("id", CommonConfig.get("ldap.user.attribute.id"));
               attrMap.put("username", CommonConfig.get("ldap.user.attribute.username"));
               attrMap.put("mobile", CommonConfig.get("ldap.user.attribute.mobile"));
               attrMap.put("phone", CommonConfig.get("ldap.user.attribute.phone"));
               attrMap.put("mail", CommonConfig.get("ldap.user.attribute.email"));
               Iterator var17 = attrMap.entrySet().iterator();

               while(var17.hasNext()) {
                  Entry elem = (Entry)var17.next();
                  String attrName = (String)elem.getValue();
                  if (StringUtils.isNotBlank(attrName)) {
                     Attribute attr = attrs.get(attrName);
                     String value = getFirstValueOfAttribute(attr);
                     if (StringUtils.isNotBlank(value)) {
                        userMap.put(elem.getKey(), value);
                     }
                  }
               }

               String value = getFirstValueOfAttribute(attrs.get("displayname"));
               if (StringUtils.isNotBlank(value)) {
                  userMap.put("displayname", value);
               }

               String fullId = "";
               if (attrs != null) {
                  if (attrs.get("cn") != null && !attrs.get("cn").equals("")) {
                     fullId = attrs.get("cn").toString();
                  }

                  if (attrs.get("uid") != null && !attrs.get("uid").equals("")) {
                     if (StringUtils.isNotBlank(fullId)) {
                        fullId = fullId + ",";
                     }

                     fullId = fullId + attrs.get("uid").toString();
                  }
               }

               if (StringUtils.isBlank(fullId)) {
                  fullId = entry.getNameInNamespace();
               }

               userMap.put("view_id", fullId);
            }
         }
      } catch (ConfigException | NamingException var24) {
         logger.error("", var24);
      }

      return resultList;
   }

   public static String getFirstValueOfAttribute(Attribute attr) {
      if (attr == null) {
         return "";
      } else {
         try {
            NamingEnumeration e = attr.getAll();
            if (e.hasMore()) {
               String value = e.next().toString();
               return value;
            }
         } catch (NamingException var3) {
            logger.error("", var3);
         }

         return "";
      }
   }

   public static ArrayList getUserList(Hashtable env, String dn) {
      ArrayList resultList = new ArrayList();

      try {
         NamingEnumeration e = getSearch(env, dn, "(objectclass=person)");

         while(e.hasMore()) {
            Map userMap = new HashMap();
            NameClassPair entry = (NameClassPair)e.next();
            userMap.put("id", entry.getName());
            userMap.put("full_id", entry.getNameInNamespace());
            resultList.add(userMap);
         }

         return resultList;
      } catch (NamingException var9) {
         logger.error("", var9);
         return resultList;
      } finally {
         ;
      }
   }

   public static String getDec_password(String ldap_manager_dn, String ldap_manager_password, String encVersion) {
      return ldap_manager_dn != null && ldap_manager_password != null ? SecurityUtils.getDecryptionPassword(ldap_manager_password, encVersion) : null;
   }

   public static String getEncPassword(String user_id, String password) {
      if (user_id != null && password != null) {
         EncryptionManager encMgr = EncryptionManagerImpl.getInstance();
         String encPass = encMgr.getEncryptionPassword(user_id, password);
         return encPass;
      } else {
         return null;
      }
   }

   public static String getDecPassword(String user_id, String password) {
      if (user_id != null && password != null) {
         EncryptionManager encMgr = EncryptionManagerImpl.getInstance();
         String decPass = encMgr.getDecryptionPassword(user_id, password);
         return decPass;
      } else {
         return null;
      }
   }

   public static List getOrganUnitList(Hashtable env, String dn) {
      ArrayList resultList = new ArrayList();

      try {
         NamingEnumeration e = getSearch(env, dn, "(|(objectclass=organizationalUnit)(objectclass=ou))");

         while(e.hasMore()) {
            Map userMap = new HashMap();
            NameClassPair entry = (NameClassPair)e.next();
            userMap.put("id", entry.getName());
            userMap.put("full_id", entry.getNameInNamespace());
            resultList.add(userMap);
         }

         return resultList;
      } catch (NamingException var9) {
         logger.error("", var9);
         return resultList;
      } finally {
         ;
      }
   }

   public static LDAPUtils.LdapTreeNode getLdapTreeNode(LDAPUtils.LdapTreeNode node, String name) {
      if (node.name.equals(name)) {
         return node;
      } else {
         LDAPUtils.LdapTreeNode res = null;
         Iterator var3 = node.child.iterator();

         while(var3.hasNext()) {
            LDAPUtils.LdapTreeNode next = (LDAPUtils.LdapTreeNode)var3.next();
            res = getLdapTreeNode(next, name);
            if (res != null) {
               break;
            }
         }

         return res;
      }
   }

   public static LDAPUtils.LdapTreeNode getOrganUnitTree(Hashtable env, String dn) {
      LDAPUtils.LdapTreeNode rootNode = new LDAPUtils.LdapTreeNode("root", "");
      ArrayList listNodeInfo = new ArrayList();

      String name;
      String fullName;
      try {
         NamingEnumeration e = getSearch(env, dn, "(|(objectclass=organizationalUnit)(objectclass=ou)(objectclass=container))");

         while(e.hasMore()) {
            NameClassPair entry = (NameClassPair)e.next();
            name = entry.getName();
            fullName = entry.getNameInNamespace();
            listNodeInfo.add(new LDAPUtils.NodeInfo(name, fullName));
         }
      } catch (PartialResultException var13) {
         logger.error("", var13);
      } catch (NamingException var14) {
         logger.error("", var14);
      } catch (Exception var15) {
         logger.error("", var15);
      }

      Collections.sort(listNodeInfo);
      Iterator var16 = listNodeInfo.iterator();

      while(var16.hasNext()) {
         LDAPUtils.NodeInfo ni = (LDAPUtils.NodeInfo)var16.next();
         name = ni.getName();
         fullName = ni.getFullName();
         LDAPUtils.LdapTreeNode newNode = new LDAPUtils.LdapTreeNode(name, fullName);
         String upperName = name.toUpperCase();
         int index = upperName.indexOf("OU=", 3);
         if (index < 0) {
            index = upperName.indexOf("CN=", 3);
         }

         if (index >= 0) {
            String parentName = name.substring(index).trim();
            LDAPUtils.LdapTreeNode node = getLdapTreeNode(rootNode, parentName);
            if (node != null) {
               node.add(newNode);
            }
         } else {
            rootNode.add(newNode);
         }
      }

      return rootNode;
   }

   public static void main(String[] args) {
   }

   public static class NodeInfo implements Comparable {
      private String name;
      private String fullName;

      public NodeInfo(String name, String fullName) {
         super();
         this.name = name;
         this.fullName = fullName;
      }

      public String getName() {
         return this.name;
      }

      public void setName(String name) {
         this.name = name;
      }

      public String getFullName() {
         return this.fullName;
      }

      public void setFullName(String fullName) {
         this.fullName = fullName;
      }

      public int getNameLength() {
         return this.name.length();
      }

      public int compareTo(LDAPUtils.NodeInfo o) {
         if (this.getNameLength() > o.getNameLength()) {
            return 1;
         } else {
            return this.getNameLength() < o.getNameLength() ? -1 : 0;
         }
      }
   }

   public static class LdapTreeNode {
      private String name;
      private String fullName;
      private List child;

      public LdapTreeNode(String name, String fullName) {
         super();
         this.name = name;
         this.fullName = fullName;
         this.child = new ArrayList();
      }

      public String getName() {
         return this.name;
      }

      public void setName(String name) {
         this.name = name;
      }

      public String getFullName() {
         return this.fullName;
      }

      public void setFullName(String fullName) {
         this.fullName = fullName;
      }

      public List getChild() {
         return this.child;
      }

      public void setChild(List child) {
         this.child = child;
      }

      public void add(LDAPUtils.LdapTreeNode node) {
         this.child.add(node);
      }

      public String toString() {
         return this.name;
      }
   }
}
