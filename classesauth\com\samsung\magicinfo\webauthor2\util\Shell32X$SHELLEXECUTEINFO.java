package com.samsung.magicinfo.webauthor2.util;

import com.samsung.magicinfo.webauthor2.util.Shell32X;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import com.sun.jna.WString;
import com.sun.jna.platform.win32.WinDef;
import com.sun.jna.platform.win32.WinNT;
import com.sun.jna.platform.win32.WinReg;
import java.util.Arrays;
import java.util.List;

public class SHELLEXECUTEINFO extends Structure {
  public int cbSize = size();
  
  public int fMask;
  
  public WinDef.HWND hwnd;
  
  public WString lpVerb;
  
  public WString lpFile;
  
  public WString lpParameters;
  
  public WString lpDirectory;
  
  public int nShow;
  
  public WinDef.HINSTANCE hInstApp;
  
  public Pointer lpIDList;
  
  public WString lpClass;
  
  public WinReg.HKEY hKeyClass;
  
  public int dwHotKey;
  
  public WinNT.HANDLE hMonitor;
  
  public WinNT.HANDLE hProcess;
  
  protected List getFieldOrder() {
    return Arrays.asList(new String[] { 
          "cbSize", "fMask", "hwnd", "lpVerb", "lpFile", "lpParameters", "lpDirectory", "nShow", "hInstApp", "lpIDList", 
          "lpClass", "hKeyClass", "dwHotKey", "hMonitor", "hProcess" });
  }
}
