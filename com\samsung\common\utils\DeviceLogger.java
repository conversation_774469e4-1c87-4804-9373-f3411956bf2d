package com.samsung.common.utils;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import edu.emory.mathcs.backport.java.util.Arrays;
import java.util.ArrayList;
import java.util.List;
import org.apache.logging.log4j.Logger;

public class DeviceLogger {
   static Logger logger = LoggingManagerV2.getLogger(DeviceLogger.class);
   static final int maxDeviceIdIn = 10;
   static List deviceIdList = new ArrayList();
   static Boolean deviceLogOn = null;
   static String deviceLogLevel = null;
   static String deviceLogPath = null;
   static List deviceLogEnableDevices = new ArrayList();

   public DeviceLogger() {
      super();
   }

   private static void getDeviceLoggerConfig() {
      try {
         deviceLogOn = Boolean.parseBoolean(CommonConfig.get("device.log4j.on"));
         deviceLogLevel = CommonConfig.get("device.log4j.level");
         deviceLogPath = CommonConfig.get("device.log4j.path");
         deviceLogEnableDevices = Arrays.asList(DeviceUtils.getLogEnabledDevicesListFromConfig());
      } catch (Exception var1) {
      }

      if (deviceLogOn == null) {
         deviceLogOn = false;
      }

      if (deviceLogLevel == null) {
         deviceLogLevel = "INFO";
      }

      if (deviceLogPath == null) {
         deviceLogPath = ".";
      }

      logger.info("DeviceLogger getDeviceLog4jConfig, on=" + deviceLogOn + ",level=" + deviceLogLevel + ",path=" + deviceLogPath);
   }

   public static boolean isDeviceLoggerOn(String deviceId) {
      if (deviceLogOn == null) {
         getDeviceLoggerConfig();
      }

      return deviceLogOn && deviceLogEnableDevices.contains(deviceId);
   }

   public static void updateDeviceLogger() {
      getDeviceLoggerConfig();
   }

   public static boolean isDeviceIn(String deviceId) {
      for(int i = 0; i < deviceIdList.size(); ++i) {
         if (deviceId.equals(deviceIdList.get(i))) {
            return true;
         }
      }

      return false;
   }

   public static Logger getDeviceLogger(String deviceId) {
      Logger m_logger = null;

      try {
         if (deviceLogOn != null && deviceLogOn) {
            String loggerName = "deviceId_" + deviceId;
            m_logger = LoggingManagerV2.getLogger(loggerName);
            if (!isDeviceIn(deviceId)) {
               String path = deviceLogPath + deviceId + ".log";
               LoggingManagerV2.initDeviceLoggerConfig(loggerName, path, deviceLogLevel);
               deviceIdList.add(deviceId);
               logger.info("DeviceLogger set !!!. deivceId =" + deviceId + ",path=" + path + ",deviceId_size=" + deviceIdList.size());
            }
         }

         return m_logger;
      } catch (Exception var4) {
         logger.error("", var4);
         return null;
      }
   }
}
