package com.samsung.magicinfo.webauthor2.webapi.resource;

import com.samsung.magicinfo.webauthor2.model.Tag;
import java.io.Serializable;
import org.springframework.hateoas.Link;
import org.springframework.hateoas.Resource;

public class TagResource extends Resource<Tag> implements Serializable {
  private static final long serialVersionUID = 1L;
  
  public TagResource(Tag tag, Iterable<Link> links) {
    super(tag, links);
  }
  
  public TagResource(Tag tag, Link... links) {
    super(tag, links);
  }
}
