package com.samsung.magicinfo.webauthor2.repository.model;

import com.samsung.magicinfo.webauthor2.repository.model.ContentThumbnailResultListData;
import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.FIELD)
public class ContentThumbnailListResponseData implements Serializable {
  private static final long serialVersionUID = -5082259751150662871L;
  
  @XmlAttribute
  private String code;
  
  @XmlElement
  private String errorMessage;
  
  @XmlElement
  private ContentThumbnailResultListData responseClass;
  
  public String getCode() {
    return this.code;
  }
  
  public String getErrorMessage() {
    return this.errorMessage;
  }
  
  public ContentThumbnailResultListData getResponseClass() {
    return this.responseClass;
  }
}
