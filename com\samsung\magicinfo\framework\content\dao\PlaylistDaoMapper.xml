<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.samsung.magicinfo.framework.content.dao.PlaylistDaoMapper">

	

	<select id="getGroup" resultType="com.samsung.magicinfo.framework.content.entity.Group">

		SELECT 

			GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, CREATOR_ID, CREATE_DATE

		FROM

			MI_CMS_INFO_PLAYLIST_GROUP

		WHERE

			GROUP_ID = #{groupId}

	</select>



	<select id="getPlaylistName" resultType="string">

		SELECT PLAYLIST_NAME FROM MI_CMS_INFO_PLAYLIST

		<if test="playlistId != null">

			WHERE PLAYLIST_ID = #{playlistId}

		</if>

	</select>



	<select id="getPlaylistAllVerInfo" resultType="com.samsung.magicinfo.framework.content.entity.Playlist">

		SELECT

			A.PLAYLIST_ID, B.VERSION_ID, PLAYLIST_NAME, B.CREATOR_ID, B.CREATE_DATE,

			A.LAST_MODIFIED_DATE, B.TOTAL_SIZE, B.PLAY_TIME, A.IS_DELETED, B.IS_ACTIVE,

			B.IS_SHUFFLE, A.SHARE_FLAG, A.DEVICE_TYPE,  A.DEVICE_TYPE_VERSION, A.IS_VWL, PLAYLIST_META_DATA , C.GROUP_NAME,

			C.GROUP_ID , CONTENT_COUNT , A.PLAYLIST_TYPE, B.AMS_MODE, B.AMS_DIRECT_PLAY

		FROM

			MI_CMS_INFO_PLAYLIST A,

			MI_CMS_INFO_PLAYLIST_VERSION B,

			MI_CMS_INFO_PLAYLIST_GROUP C,

			MI_CMS_MAP_GROUP_PLAYLIST D

		WHERE

			A.PLAYLIST_ID = B.PLAYLIST_ID AND C.GROUP_ID = D.GROUP_ID

			AND D.PLAYLIST_ID = A.PLAYLIST_ID AND A.PLAYLIST_ID = #{playlistId}

		ORDER BY

			VERSION_ID DESC

	</select>



	<select id="getPlaylistActiveVerInfo" resultType="com.samsung.magicinfo.framework.content.entity.Playlist">

		SELECT

			A.PLAYLIST_ID, B.VERSION_ID , PLAYLIST_NAME, B.CREATOR_ID, B.CREATE_DATE, A.ORGANIZATION_ID, F.GROUP_NAME AS ORGANIZATION_NAME, A.LAST_MODIFIED_DATE, A.IGNORE_TAG, A.EVENNESS_PLAYBACK,

			B.TOTAL_SIZE, B.PLAY_TIME, A.IS_DELETED, B.IS_ACTIVE,B.IS_SHUFFLE, A.SHARE_FLAG, A.DEVICE_TYPE, A.IS_VWL, A.PLAYLIST_TYPE, A.DEFAULT_CONTENT_DURATION,

			PLAYLIST_META_DATA , C.GROUP_NAME ,C.GROUP_ID, CONTENT_ID , CONTENT_COUNT,  A.DEVICE_TYPE_VERSION , B.AMS_MODE, B.AMS_DIRECT_PLAY, B.HAS_SUB_PLAYLIST

		FROM

			MI_CMS_INFO_PLAYLIST A,

			MI_CMS_INFO_PLAYLIST_VERSION B,

			MI_CMS_INFO_PLAYLIST_GROUP C,

			MI_CMS_MAP_GROUP_PLAYLIST D,

			(
				(SELECT CONTENT_ID, VERSION_ID, CONTENT_ORDER FROM MI_CMS_MAP_PLAYLIST_CONTENT WHERE PLAYLIST_ID = #{playlistId} UNION SELECT CONTENT_ID, VERSION_ID, CONTENT_ORDER FROM MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST WHERE PLAYLIST_ID = #{playlistId})
				UNION
				SELECT NULL AS CONTENT_ID, VERSION_ID, TAG_ORDER AS CONTENT_ORDER FROM MI_CMS_MAP_PLAYLIST_TAG WHERE PLAYLIST_ID = #{playlistId}
			 ) E,

			MI_USER_INFO_GROUP F

		WHERE

			A.PLAYLIST_ID = B.PLAYLIST_ID AND IS_ACTIVE = 'Y' AND C.GROUP_ID = D.GROUP_ID

			AND D.PLAYLIST_ID = A.PLAYLIST_ID

			AND F.GROUP_ID = A.ORGANIZATION_ID

			AND E.VERSION_ID = B.VERSION_ID AND A.PLAYLIST_ID = #{playlistId} ORDER BY E.CONTENT_ORDER LIMIT 1

	</select>

	<select id="getPlaylistActiveVerInfo" resultType="com.samsung.magicinfo.framework.content.entity.Playlist" databaseId="mssql">

		SELECT TOP 1

			A.PLAYLIST_ID, B.VERSION_ID , PLAYLIST_NAME, B.CREATOR_ID, B.CREATE_DATE,  A.ORGANIZATION_ID, F.GROUP_NAME AS ORGANIZATION_NAME, A.LAST_MODIFIED_DATE, A.IGNORE_TAG, A.EVENNESS_PLAYBACK,

			B.TOTAL_SIZE, B.PLAY_TIME, A.IS_DELETED, B.IS_ACTIVE,B.IS_SHUFFLE, A.SHARE_FLAG, A.DEVICE_TYPE, A.IS_VWL, A.PLAYLIST_TYPE, A.DEFAULT_CONTENT_DURATION,

			PLAYLIST_META_DATA , C.GROUP_NAME ,C.GROUP_ID, CONTENT_ID , CONTENT_COUNT,  A.DEVICE_TYPE_VERSION , B.AMS_MODE, B.AMS_DIRECT_PLAY, B.HAS_SUB_PLAYLIST

		FROM

			MI_CMS_INFO_PLAYLIST A,

			MI_CMS_INFO_PLAYLIST_VERSION B,

			MI_CMS_INFO_PLAYLIST_GROUP C,

			MI_CMS_MAP_GROUP_PLAYLIST D,

			(
				(SELECT CONTENT_ID, VERSION_ID, CONTENT_ORDER FROM MI_CMS_MAP_PLAYLIST_CONTENT WHERE PLAYLIST_ID = #{playlistId} UNION SELECT CONTENT_ID, VERSION_ID, CONTENT_ORDER FROM MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST WHERE PLAYLIST_ID = #{playlistId})
				UNION
				SELECT NULL AS CONTENT_ID, VERSION_ID, TAG_ORDER AS CONTENT_ORDER FROM MI_CMS_MAP_PLAYLIST_TAG WHERE PLAYLIST_ID = #{playlistId}
			 ) E,

			MI_USER_INFO_GROUP F

		WHERE

			A.PLAYLIST_ID = B.PLAYLIST_ID AND IS_ACTIVE = 'Y' AND C.GROUP_ID = D.GROUP_ID

			AND D.PLAYLIST_ID = A.PLAYLIST_ID

			AND F.GROUP_ID = A.ORGANIZATION_ID

			AND E.VERSION_ID = B.VERSION_ID AND A.PLAYLIST_ID = #{playlistId} ORDER BY E.CONTENT_ORDER

	</select>

	<select id="getPlaylistActiveVerInfoForSync" resultType="com.samsung.magicinfo.framework.content.entity.SyncPlaylist">
		SELECT
		A.PLAYLIST_ID, PLAYLIST_NAME, B.CREATOR_ID, B.CREATE_DATE, A.LAST_MODIFIED_DATE, A.PLAYLIST_TYPE, B.PLAY_TIME,
		E.CONTENT_ID , E.CONTENT_ORDER, E.SYNC_PLAY_ID, FILES.FILE_NAME, FILES.FILE_ID, E.CONTENT_DURATION
		FROM
		MI_CMS_INFO_PLAYLIST A, MI_CMS_INFO_PLAYLIST_VERSION B, MI_CMS_MAP_PLAYLIST_CONTENT E
		LEFT JOIN MI_CMS_INFO_CONTENT_VERSION CONTENT_VERSION ON CONTENT_VERSION.CONTENT_ID = E.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_FILE FILES ON CONTENT_VERSION.THUMB_FILE_ID = FILES.FILE_ID
		WHERE
		A.PLAYLIST_ID = B.PLAYLIST_ID AND B.IS_ACTIVE = 'Y'
		AND E.PLAYLIST_ID = A.PLAYLIST_ID
		AND E.VERSION_ID = B.VERSION_ID AND A.PLAYLIST_ID = #{playlistId}
		AND CONTENT_VERSION.IS_ACTIVE = 'Y'
		ORDER BY SYNC_PLAY_ID, CONTENT_ORDER
	</select>



	<select id="getPlaylistActiveVersionId" resultType="long">

		SELECT VERSION_ID FROM MI_CMS_INFO_PLAYLIST_VERSION WHERE PLAYLIST_ID = #{playlistId} AND IS_ACTIVE = 'Y'

	</select>

	<select id="getCountPlaylistVersionId" resultType="int">

		SELECT COUNT(DISTINCT VERSION_ID) FROM MI_CMS_INFO_PLAYLIST_VERSION WHERE PLAYLIST_ID = #{playlistId}

	</select>

	<select id="getPlaylistTypeFromPlaylistId" resultType="String">

		SELECT PLAYLIST_TYPE FROM MI_CMS_INFO_PLAYLIST WHERE PLAYLIST_ID = #{playlistId}

	</select>


	<select id="getPlaylistVerInfo" resultType="com.samsung.magicinfo.framework.content.entity.Playlist">

		SELECT DISTINCT

			A.PLAYLIST_ID, B.VERSION_ID, PLAYLIST_NAME, B.CREATOR_ID, B.CREATE_DATE,

			A.LAST_MODIFIED_DATE, B.TOTAL_SIZE, B.PLAY_TIME, A.IS_DELETED, B.IS_ACTIVE,

			B.IS_SHUFFLE, A.SHARE_FLAG,  A.DEVICE_TYPE, A.DEVICE_TYPE_VERSION, A.IS_VWL, A.PLAYLIST_TYPE, PLAYLIST_META_DATA , C.GROUP_NAME,

			CONTENT_COUNT, C.GROUP_ID , B.AMS_MODE, B.AMS_DIRECT_PLAY, B.HAS_SUB_PLAYLIST

		FROM

			MI_CMS_INFO_PLAYLIST A,

			MI_CMS_INFO_PLAYLIST_VERSION B,

			MI_CMS_INFO_PLAYLIST_GROUP C,

			MI_CMS_MAP_GROUP_PLAYLIST D,

			(
				SELECT PLAYLIST_ID, VERSION_ID FROM MI_CMS_MAP_PLAYLIST_CONTENT WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
				UNION
				SELECT PLAYLIST_ID, VERSION_ID FROM MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
				UNION
				SELECT PLAYLIST_ID, VERSION_ID FROM MI_CMS_MAP_PLAYLIST_TAG WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
			) E

		WHERE

			A.PLAYLIST_ID = B.PLAYLIST_ID AND C.GROUP_ID = D.GROUP_ID AND

			D.PLAYLIST_ID = A.PLAYLIST_ID

			AND E.VERSION_ID = B.VERSION_ID AND A.PLAYLIST_ID = #{playlistId} AND B.VERSION_ID = #{versionId}

	</select>



	<select id="getContentListOfPlaylist" resultType="com.samsung.magicinfo.framework.content.entity.Content">

		SELECT

			B.CONTENT_ID AS CONTENT_ID, B.DEVICE_TYPE,

			E.CONTENT_ID, E.CONTENT_NAME, E.CREATOR_ID, E.CREATE_DATE, E.CONTENT_META_DATA, E.LAST_MODIFIED_DATE, E.IS_DELETED, E.SHARE_FLAG, E.SESSION_ID,

			E.ORGANIZATION_ID, E.AGE, E.POLLING_INTERVAL, E.APPROVAL_STATUS, E.APPROVAL_OPINION,

			B.THUMB_FILE_ID, D.FILE_NAME AS THUMB_FILE_NAME,

			B.MEDIA_TYPE, C.CONTENT_ORDER, B.RESOLUTION,

			C.CONTENT_DURATION, C.CONTENT_DURATION_MILLI, B.PLAY_TIME, B.PLAY_TIME_MILLI , C.SYNC_PLAY_ID, C.AMS_RECOG_TYPE, C.GENDER, C.RANDOM_COUNT, C.IS_SUB_PLAYLIST

		FROM

			MI_CMS_INFO_PLAYLIST_VERSION A,

			MI_CMS_INFO_CONTENT_VERSION  B,

			(
				SELECT PLAYLIST_ID, PLAYLIST_CONTENTS.VERSION_ID, PLAYLIST_CONTENTS.CONTENT_ID, CONTENT_ORDER, CONTENT_DURATION, CONTENT_DURATION_MILLI, SYNC_PLAY_ID, GENDER, AMS_RECOG_TYPE, RANDOM_COUNT, NULL AS IS_SUB_PLAYLIST
				FROM MI_CMS_MAP_PLAYLIST_CONTENT PLAYLIST_CONTENTS
				WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
				UNION
				SELECT PLAYLIST_ID, VERSION_ID,
				(
				SELECT CONTENT_ID
				FROM MI_CMS_INFO_PLAYLIST_VERSION VERSIONS
				LEFT JOIN MI_CMS_MAP_PLAYLIST_CONTENT MAP_CONTENTS ON VERSIONS.PLAYLIST_ID = MAP_CONTENTS.PLAYLIST_ID AND VERSIONS.VERSION_ID = MAP_CONTENTS.VERSION_ID
				WHERE VERSIONS.PLAYLIST_ID = MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST.CONTENT_ID AND VERSIONS.IS_ACTIVE = 'Y'
				ORDER BY CONTENT_ORDER ASC
				LIMIT 1
				 ) AS CONTENT_ID
				, CONTENT_ORDER, NULL AS CONTENT_DURATION, NULL AS CONTENT_DURATION_MILLI, SYNC_PLAY_ID, NULL AS GENDER, NULL AS AMS_RECOG_TYPE, NULL AS RANDOM_COUNT, IS_SUB_PLAYLIST
				FROM MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST
				WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
			) C,

			MI_CMS_INFO_FILE D,

			MI_CMS_INFO_CONTENT E

		WHERE

			A.PLAYLIST_ID = #{playlistId} AND A.VERSION_ID = #{versionId}

			AND A.PLAYLIST_ID = C.PLAYLIST_ID AND A.VERSION_ID = C.VERSION_ID AND

			C.CONTENT_ID = B.CONTENT_ID AND E.CONTENT_ID = B.CONTENT_ID

			AND B.IS_ACTIVE = 'Y' AND D.FILE_ID = B.THUMB_FILE_ID

		ORDER BY

			C.SYNC_PLAY_ID::INTEGER, C.CONTENT_ORDER  ASC

	</select>

	<select id="getContentListOfPlaylist" resultType="com.samsung.magicinfo.framework.content.entity.Content" databaseId="mssql">

		SELECT

			B.CONTENT_ID AS CONTENT_ID, B.DEVICE_TYPE,

			E.CONTENT_ID, E.CONTENT_NAME, E.CREATOR_ID, E.CREATE_DATE, E.CONTENT_META_DATA, E.LAST_MODIFIED_DATE, E.IS_DELETED, E.SHARE_FLAG, E.SESSION_ID,

			E.ORGANIZATION_ID, E.AGE, E.POLLING_INTERVAL, E.APPROVAL_STATUS, E.APPROVAL_OPINION,

			B.THUMB_FILE_ID, D.FILE_NAME AS THUMB_FILE_NAME,

			B.MEDIA_TYPE, C.CONTENT_ORDER, B.RESOLUTION,

			C.CONTENT_DURATION, C.CONTENT_DURATION_MILLI, B.PLAY_TIME, B.PLAY_TIME_MILLI , C.SYNC_PLAY_ID, C.AMS_RECOG_TYPE, C.GENDER, C.RANDOM_COUNT, C.IS_SUB_PLAYLIST

		FROM

			MI_CMS_INFO_PLAYLIST_VERSION A,

			MI_CMS_INFO_CONTENT_VERSION  B,

			(
				SELECT PLAYLIST_ID, PLAYLIST_CONTENTS.VERSION_ID, PLAYLIST_CONTENTS.CONTENT_ID, CONTENT_ORDER, CONTENT_DURATION, CONTENT_DURATION_MILLI, SYNC_PLAY_ID, GENDER, AMS_RECOG_TYPE, RANDOM_COUNT, NULL AS IS_SUB_PLAYLIST
				FROM MI_CMS_MAP_PLAYLIST_CONTENT PLAYLIST_CONTENTS
				WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
				UNION
				SELECT PLAYLIST_ID, VERSION_ID,
				(
				SELECT TOP 1 CONTENT_ID
				FROM MI_CMS_INFO_PLAYLIST_VERSION VERSIONS
				LEFT JOIN MI_CMS_MAP_PLAYLIST_CONTENT MAP_CONTENTS ON VERSIONS.PLAYLIST_ID = MAP_CONTENTS.PLAYLIST_ID AND VERSIONS.VERSION_ID = MAP_CONTENTS.VERSION_ID
				WHERE VERSIONS.PLAYLIST_ID = MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST.CONTENT_ID AND VERSIONS.IS_ACTIVE = 'Y'
				ORDER BY CONTENT_ORDER ASC
				 ) AS CONTENT_ID
				, CONTENT_ORDER, NULL AS CONTENT_DURATION, NULL AS CONTENT_DURATION_MILLI, SYNC_PLAY_ID, NULL AS GENDER, NULL AS AMS_RECOG_TYPE, NULL AS RANDOM_COUNT, IS_SUB_PLAYLIST
				FROM MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST
				WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
			) C,

			MI_CMS_INFO_FILE D,

			MI_CMS_INFO_CONTENT E

		WHERE

			A.PLAYLIST_ID = #{playlistId} AND A.VERSION_ID = #{versionId}

			AND A.PLAYLIST_ID = C.PLAYLIST_ID AND A.VERSION_ID = C.VERSION_ID AND

			C.CONTENT_ID = B.CONTENT_ID AND E.CONTENT_ID = B.CONTENT_ID

			AND B.IS_ACTIVE = 'Y' AND D.FILE_ID = B.THUMB_FILE_ID

		ORDER BY

			CONVERT(INT, C.SYNC_PLAY_ID), C.CONTENT_ORDER  ASC

	</select>

		<select id="getContentListOfSyncGroup" resultType="com.samsung.magicinfo.framework.content.entity.Content">

		SELECT

			B.CONTENT_ID AS CONTENT_ID, B.DEVICE_TYPE, E.*,

			B.THUMB_FILE_ID, D.FILE_NAME AS THUMB_FILE_NAME,

			B.MEDIA_TYPE, C.CONTENT_ORDER,

			C.CONTENT_DURATION, B.PLAY_TIME , C.SYNC_PLAY_ID, C.AMS_RECOG_TYPE

		FROM

			MI_CMS_INFO_PLAYLIST_VERSION A,

			MI_CMS_INFO_CONTENT_VERSION  B,

			MI_CMS_MAP_PLAYLIST_CONTENT C,

			MI_CMS_INFO_FILE D,

			MI_CMS_INFO_CONTENT E

		WHERE

			A.PLAYLIST_ID = #{playlistId} AND A.VERSION_ID = #{versionId}

			AND A.PLAYLIST_ID = C.PLAYLIST_ID AND A.VERSION_ID = C.VERSION_ID AND

			C.CONTENT_ID = B.CONTENT_ID AND E.CONTENT_ID = B.CONTENT_ID

			AND B.IS_ACTIVE = 'Y' AND D.FILE_ID = B.THUMB_FILE_ID

		ORDER BY

			C.SYNC_PLAY_ID, C.CONTENT_ORDER  ASC

	</select>

	<select id="getSearchList" resultType="com.samsung.magicinfo.framework.content.entity.Playlist">

		<include refid="getSearchListSelect"/>

		<include refid="getSearchListFromWhere"/>

		<include refid="getBaseOrderByContentSearch"/>

	</select>



	<select id="getSearchListPage" resultType="com.samsung.magicinfo.framework.content.entity.Playlist">

		<include refid="getSearchListSelect"/>

		<include refid="getSearchListFromWhere"/>

		<include refid="getBaseOrderByContentSearch"/>

		LIMIT #{pageSize} OFFSET #{startPos}

	</select>



	<select id="getSearchListPage" resultType="com.samsung.magicinfo.framework.content.entity.Playlist" databaseId="mssql">

		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />

        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />

        SELECT * FROM

        (

	        <include refid="getSearchListSelect"/>, ROW_NUMBER() OVER(<include refid="getBaseOrderByContentSearch"/>) as rownum

	        <include refid="getSearchListFromWhere"/>

	        ) as SubQuery

	    WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit}

	    ORDER BY rownum

	</select>



	<select id="getSearchListCnt" resultType="int">

		SELECT COUNT(A.PLAYLIST_ID)

		<include refid="getSearchListFromWhere"/>

	</select>



	<sql id="getSearchListSelect">

		SELECT

			A.PLAYLIST_ID, B.VERSION_ID , PLAYLIST_NAME, B.CREATOR_ID, B.CREATE_DATE, A.LAST_MODIFIED_DATE,

			B.TOTAL_SIZE, B.PLAY_TIME, A.IS_DELETED, B.IS_ACTIVE, B.IS_SHUFFLE, A.SHARE_FLAG,  A.DEVICE_TYPE, A.DEVICE_TYPE_VERSION, A.IS_VWL,

			PLAYLIST_META_DATA , C.GROUP_NAME , CONTENT_COUNT, CONTENT_ID, A.IGNORE_TAG, A.EVENNESS_PLAYBACK

	</sql>



	<sql id="getSearchListFromWhere">

		FROM

			MI_CMS_INFO_PLAYLIST A,

			MI_CMS_INFO_PLAYLIST_VERSION B,

			MI_CMS_INFO_PLAYLIST_GROUP C,

			MI_CMS_MAP_GROUP_PLAYLIST D,

			MI_CMS_MAP_PLAYLIST_CONTENT E,

			MI_USER_INFO_USER F

		WHERE

			A.PLAYLIST_ID = B.PLAYLIST_ID AND IS_ACTIVE = 'Y' AND C.GROUP_ID = D.GROUP_ID AND D.PLAYLIST_ID = A.PLAYLIST_ID

			AND E.PLAYLIST_ID  = A.PLAYLIST_ID AND E.VERSION_ID = B.VERSION_ID AND 
			
			( F.ROOT_GROUP_ID = A.ORGANIZATION_ID or exists (
					SELECT 1 FROM MI_USER_MAP_USER_MANAGE_ORG M, MI_USER_MAP_MANAGE_ORG_GROUP_MANAGE_ORG N
					WHERE 
						M.USER_ID = F.USER_ID AND	
						M.MNG_ORG_GROUP_ID = N.MNG_ORG_GROUP_ID AND
						N.ORG_GROUP_ID = A.ORGANIZATION_ID )
			)

			AND F.USER_ID = A.CREATOR_ID AND A.IS_DELETED = 'N' AND E.CONTENT_ORDER = 1 AND AND E.SYNC_PLAY_ID = '0'

			<if test="organizationId != 0 and organizationId != null">

				AND A.ORGANIZATION_ID = #{organizationId}

			</if>

			<if test="searchText != null and searchText.length() > 0">

				AND (UPPER(PLAYLIST_NAME) LIKE '%'<include refid="utils.concatenate"/>#{searchText}<include refid="utils.concatenate"/>'%' ESCAPE '^' OR UPPER(PLAYLIST_META_DATA) LIKE '%'<include refid="utils.concatenate"/>#{searchText}<include refid="utils.concatenate"/>'%' ESCAPE '^')

			</if>

			<if test="search != null">

				<include refid="getContentSearch"/>

			</if>

	</sql>



	<sql id="getBaseOrderByContentSearch">

		<choose>

			<when test="sortColumn != null and sortColumn.length() > 0">

                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(sortColumn)" />

                ORDER BY ${safe_sortColumn}

				<if test="sortOrder != null and sortOrder.length() > 0">

					<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(sortOrder)" />

    				${safe_sortOrder}

				</if>

			</when>

			<otherwise>ORDER BY A.LAST_MODIFIED_DATE DESC</otherwise>

		</choose>

	</sql>



	<sql id="getContentSearch">

		<if test="searchKeyword != null">

			AND (UPPER(PLAYLIST_NAME) LIKE '%'<include refid="utils.concatenate"/>#{searchKeyword}<include refid="utils.concatenate"/>'%' ESCAPE '^'

					OR UPPER(PLAYLIST_META_DATA) LIKE '%'<include refid="utils.concatenate"/>#{searchKeyword}<include refid="utils.concatenate"/>'%' ESCAPE '^')

		</if>

		<if test="search.getSearch_group_type() != null and search.getSearch_group_type().length() > 0">

			<choose>

				<when test="search.getSearch_group_type().equalsIgnoreCase(searchConstGROUPED)">

					<if test="search.getSearch_group_name() != null and search.getSearch_group_name().length() > 0">

						AND C.GROUP_NAME = #{search.search_group_name}

					</if>

					<if test="search.getSearch_creator() != null and search.getSearch_creator().length() > 0">

						AND A.CREATOR_ID = #{search.search_creator}

					</if>

					<if test="search.getSearch_creator() == null or search.getSearch_creator().length() == 0">

						AND A.CREATOR_ID = #{search.creator_id}

					</if>

				</when>

				<when test="search.getSearch_group_type().equalsIgnoreCase(searchConstUNGROUPED)">

					AND D.GROUP_ID = #{searchRootId}

					<if test="search.getSearch_creator() != null and search.getSearch_creator().length() > 0">

						AND A.CREATOR_ID = #{search.search_creator}

					</if>

					<if test="search.getSearch_creator() == null or search.getSearch_creator().length() == 0">

						AND A.CREATOR_ID = #{search.creator_id}

					</if>

				</when>

				<when test="search.getSearch_group_type().equalsIgnoreCase(searchConstSHARED)">

					<if test="search.getSearch_creator() != null and search.getSearch_creator().length() == 0">

						AND (A.SHARE_FLAG = #{searchConstSHARE_FLAG_DEFAULT} OR A.CREATOR_ID = #{search.creator_id})

					</if>

					<if test="search.getSearch_creator().equals(search.getCreator_id())">

						AND A.CREATOR_ID = #{search.search_creator}

					</if>

					<if test="search.getSearch_creator() != null and search.getSearch_creator().length() > 0 and !search.getSearch_creator().equals(search.getCreator_id())">

						AND A.SHARE_FLAG = #{searchConstSHARE_FLAG_DEFAULT} AND A.CREATOR_ID = #{search.search_creator}

					</if>

					<if test="search.getSearch_creator() != null and search.getSearch_creator().length() > 0">

						AND A.CREATOR_ID LIKE '%'<include refid="utils.concatenate"/>#{searchCreator}<include refid="utils.concatenate"/>'%' ESCAPE '^'

					</if>

				</when>

				<when test="search.getSearch_group_type().equalsIgnoreCase(searchConstORGAN)">

					<if test="search.getSearch_creator() != null and search.getSearch_creator().length() > 0">

						AND A.CREATOR_ID LIKE '%'<include refid="utils.concatenate"/>#{searchCreator}<include refid="utils.concatenate"/>'%' ESCAPE '^'

					</if>

				</when>

				<otherwise>

					<if test="search.getSearch_creator() != null and search.getSearch_creator().length() > 0">

						AND A.CREATOR_ID = #{search.search_creator}

					</if>

					<if test="search.getSearch_creator() == null or search.getSearch_creator().length() == 0">

						AND A.CREATOR_ID = #{search.creator_id}

					</if>

				</otherwise>

			</choose>

		</if>

		<if test="search.getSearch_start_date() != null">

			AND A.LAST_MODIFIED_DATE &gt; #{searchStartDate}

		</if>

		<if test="search.getSearch_end_date() != null">

			AND A.LAST_MODIFIED_DATE &lt; #{searchEndDate}

		</if>

	</sql>



	<select id="getPlaylistList" resultType="com.samsung.magicinfo.framework.content.entity.Playlist">

		<choose>

			<when test="isDeviceTypeFilter != null and isDeviceTypeFilter">

				<include refid="getDeviceTypeFilterSelectClause"/>

			</when>

			<otherwise>

				<include refid="getSelectClauseFalse"/>

			</otherwise>

		</choose>

		<include refid="getFromClauseFalse"/>

		<include refid="getWhereClause"/>

		<choose>

			<when test="listType.equalsIgnoreCase(ConstGROUP_TYPE_UNGROUPED)">

				AND D.GROUP_ID = #{groupRootId}

			</when>

			<when test="listType.equalsIgnoreCase(ConstGROUP_TYPE_GROUPED)">

				<if test="groupID != null">

					 AND D.GROUP_ID = #{groupIDLong}

				</if>

			</when>

			<when test="listType.equalsIgnoreCase(ConstGROUP_TYPE_USER)">

				<if test="viewRange != null and viewRange.equalsIgnoreCase('shared')">

					 AND A.SHARE_FLAG = #{ConstSHARE_FLAG_DEFAULT}

				</if>

			</when>

			<when test="listType.equalsIgnoreCase(ConstGROUP_TYPE_ORGAN)">

				<include refid="getContentListWhereOrgan"/>

			</when>
			<when test="ConstGROUP_TYPE_ALL.equalsIgnoreCase(listType)">

				<if test="!canReadUnshared">

					AND (A.CREATOR_ID = #{creatorID} OR A.SHARE_FLAG = 1)

				</if>

			</when>
		</choose>

		<if test="!listType.equalsIgnoreCase(ConstGROUP_TYPE_SHARED) and !listType.equalsIgnoreCase(ConstGROUP_TYPE_ORGAN) and !ConstGROUP_TYPE_ALL.equalsIgnoreCase(listType)">

			<if test="creatorID != null and creatorID.length() > 0 and organizationId != 0">

				AND A.CREATOR_ID = #{creatorID}

			</if>

		</if>

		<if test="isDeviceTypeFilter == null">

			<choose>

				<when test="sortColumn != null and sortColumn.length() > 0">

                    <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(sortColumn)" />

					ORDER BY ${safe_sortColumn}

					<if test="sortOrder != null and sortOrder.length() > 0">

					<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(sortOrder)" />

						${safe_sortOrder}

					</if>

				</when>

				<otherwise>

					ORDER BY A.LAST_MODIFIED_DATE DESC

				</otherwise>

			</choose>

		</if>

	</select>



	<select id="getPlaylistListPage" resultType="com.samsung.magicinfo.framework.content.entity.Playlist">

		<include refid="getSelectClause"/>

		<include refid="getFromClause"/>

		<include refid="getWhereClause"/>

		<include refid="getWhereClausePaged"/>

		<choose>

			<when test="sortColumn != null and sortColumn.length() > 0">

                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(sortColumn)" />

				ORDER BY ${safe_sortColumn}

				<if test="sortOrder != null and sortOrder.length() > 0">

					<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(sortOrder)" />

    				${safe_sortOrder}

				</if>

			</when>

			<otherwise>

				ORDER BY A.LAST_MODIFIED_DATE DESC

			</otherwise>

		</choose>

        <if test="pageSize != null and startPos != null ">
            LIMIT #{pageSize} OFFSET #{startPos}
        </if>


	</select>



	<select id="getPlaylistListPage" resultType="com.samsung.magicinfo.framework.content.entity.Playlist" databaseId="mssql">

		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />

        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />

        SELECT * FROM

        (

	        <include refid="getSelectClause"/>, ROW_NUMBER() OVER

	        (

	        	<choose>

					<when test="sortColumn != null and sortColumn.length() > 0">

                        <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(sortColumn)" />

                        <choose>
                            <when test="sortColumn.equalsIgnoreCase('CREATOR_ID')">
                                ORDER BY B.CREATOR_ID
                            </when>
                            <otherwise>
                                ORDER BY ${safe_sortColumn}
                            </otherwise>
                        </choose>

						<if test="sortOrder != null and sortOrder.length() > 0">

							<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(sortOrder)" />

    						${safe_sortOrder}

						</if>

					</when>

					<otherwise>ORDER BY A.LAST_MODIFIED_DATE DESC</otherwise>

				</choose>

			) as rownum

	        <include refid="getFromClause"/>

	        <include refid="getWhereClause"/>

			<include refid="getWhereClausePaged"/>

	        ) as SubQuery

        <if test="pageSize != null and startPos != null ">
            WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit}
            ORDER BY rownum
        </if>

	</select>



	<select id="getPlaylistListCnt" resultType="int">

		SELECT COUNT(A.PLAYLIST_ID)

		<include refid="getFromClause"/>

		<include refid="getWhereClause"/>
		<include refid="getWhereClausePaged"/>

	</select>



	<sql id="getWhereClausePaged">

		<if test="device_type != null and !''.equals(device_type)">

			<bind name="deviceTypeArray" value="device_type.split(',')"/>

		</if>

		<if test="playlist_type_filter != null">
			<foreach item="item" index="index" collection="playlist_type_filter" open=" AND (" separator=" OR " close=")">
				A.PLAYLIST_TYPE = #{item}
			</foreach>
		</if>

		<if test="adSchedule != null and adSchedule.equalsIgnoreCase('N')">
			AND A.PLAYLIST_TYPE != '3'
		</if>

		<if test="adSchedule != null and adSchedule.equalsIgnoreCase('Y')">
			AND A.PLAYLIST_TYPE = '3'
		</if>

		<choose>

			<when test="listType.equalsIgnoreCase(ConstGROUP_TYPE_UNGROUPED)">

				AND D.GROUP_ID = #{groupRootId}

			</when>

			<when test="listType.equalsIgnoreCase(ConstGROUP_TYPE_GROUPED)">

				<if test="groupID != null">

					 AND D.GROUP_ID = #{groupIDLong}

				</if>
				<if test="groupIds != null">
					<foreach item="item" index="index" collection="groupIds" open=" AND (" separator=" OR " close=")">
						D.GROUP_ID = #{item}
					</foreach>
				</if>

			</when>

			<when test="listType.equalsIgnoreCase(ConstGROUP_TYPE_ORGAN) or listType.equalsIgnoreCase(ConstGROUP_TYPE_DELETED) ">
				  <include refid="getContentListWhereOrgan"/>
			</when>

		</choose>

		<if test="isSelect != null and isSelect.equalsIgnoreCase('TRUE') and selId != null and selId.length() > 0">

			AND A.PLAYLIST_ID = #{selId}

		</if>

		<if test="!listType.equalsIgnoreCase(ConstGROUP_TYPE_SHARED) and !listType.equalsIgnoreCase(ConstGROUP_TYPE_ORGAN)">

			<if test="creatorID != null and creatorID.length() > 0 and organizationId != 0">

				<choose>
					<when test="ConstGROUP_TYPE_ALL.equalsIgnoreCase(listType)">

						<if test="!canReadUnshared">

							AND (A.CREATOR_ID = #{creatorID} OR A.SHARE_FLAG = 1)

						</if>

					</when>

					<otherwise>
                        <choose>
                        <when test="!listType.equalsIgnoreCase(ConstGROUP_TYPE_DELETED)">
                            <choose> <!--and -->
                                <when test="canReadUnshared">
                                    AND A.CREATOR_ID = #{creatorID}
                                </when>

                                <otherwise>

                                    AND (A.CREATOR_ID = #{creatorID} AND A.SHARE_FLAG = 1)

                                </otherwise>

                            </choose>
                        </when>
                        <otherwise>
                            <if test="! hasContentManage">
                                AND (A.CREATOR_ID = #{creatorID} OR A.SHARE_FLAG = 1)
                            </if>
                        </otherwise>
                        </choose>
					</otherwise>

				</choose>

			</if>

		</if>

        <choose>

            <when test="minimumPriority!= null and minimumPriority.longValue() > 0l">

                AND A.DEVICE_TYPE = G.DEVICE_TYPE AND A.DEVICE_TYPE_VERSION = G.DEVICE_TYPE_VERSION AND G.PRIORITY &lt;=

                #{minimumPriority}

            </when>

            <when test="deviceTypeArray != null">

                AND

                <foreach item="deviceType" collection="deviceTypeArray" separator="OR" open="(" close=")">

                    <choose>

                        <when test="deviceType == ConstTYPE_SOC">

                            (DEVICE_TYPE = #{ConstTYPE_SOC} AND DEVICE_TYPE_VERSION = #{ConstTYPE_VERSION_1_0})

                        </when>

                        <when test="deviceType == ConstTYPE_SOC2">

                            (DEVICE_TYPE = #{ConstTYPE_SOC} AND DEVICE_TYPE_VERSION = #{ConstTYPE_VERSION_2_0})

                        </when>

                        <when test="deviceType == ConstTYPE_SOC3">

                            (DEVICE_TYPE = #{ConstTYPE_SOC} AND DEVICE_TYPE_VERSION = #{ConstTYPE_VERSION_3_0})

                        </when>

                        <when test="deviceType == ConstTYPE_PREMIUM">

                            ( DEVICE_TYPE = #{ConstTYPE_PREMIUM} )

                        </when>

                        <otherwise>

                            ( DEVICE_TYPE = #{deviceType} )

                        </otherwise>

                    </choose>

                </foreach>

            </when>

        </choose>

        <if test="is_vwl_mode != null">

            <choose>

                <when test="is_vwl_mode.equalsIgnoreCase('Y')">

                    AND A.IS_VWL = 'Y'

                </when>

                <otherwise>

                    AND A.IS_VWL = 'N'

                </otherwise>

            </choose>

        </if>
         <if test="use_sync_play != null">

            <choose>

                <when test="!use_sync_play.equalsIgnoreCase('Y')">

                    AND A.PLAYLIST_TYPE != #{ConstPLAYLIST_TYPE_SYNCPLAY}

                </when>

            </choose>

        </if>

        <if test="playlistIdList != null and !playlistIdList.equals('')">
			<foreach item="item" index="index" collection="playlistIdList" open=" AND (" separator=" OR " close=")">
				<if test="item != null and !item.equals('')">
					A.PLAYLIST_ID = #{item}
				</if>
			</foreach>
		</if>

		<if test="userFilter != null and !userFilter.equals('')">
			<foreach item="item" index="index" collection="userFilter" open=" AND (" separator=" OR " close=")">
				A.CREATOR_ID = #{item}
			</foreach>
		</if>

	</sql>



	<sql id="getContentListWhereOrgan">

		<if test="whereOrganMap != null">

			<foreach item="item" index="key" collection="whereOrganMap" open=" AND ((" separator=") OR (" close="))">

            	A.CREATOR_ID = #{key}

            	<if test="item != null">

            		AND A.SHARE_FLAG = #{item}

            	</if>

            </foreach>

		</if>

	</sql>



	<sql id="getDeviceTypeFilterSelectClause">

		SELECT DISTINCT(A.DEVICE_TYPE), A.DEVICE_TYPE_VERSION

	</sql>



	<sql id="getSelectClause">

		<choose>

			<when test="minimumPriority!=null and minimumPriority.longValue() > 0l">

				SELECT DISTINCT A.PLAYLIST_ID, A.DEVICE_TYPE, A.DEVICE_TYPE_VERSION, A.IS_VWL, B.VERSION_ID ,

				PLAYLIST_NAME, B.CREATOR_ID, B.CREATE_DATE, A.LAST_MODIFIED_DATE,

				B.TOTAL_SIZE, B.PLAY_TIME, A.IS_DELETED, B.IS_ACTIVE, B.IS_SHUFFLE,

				A.SHARE_FLAG, PLAYLIST_META_DATA , C.GROUP_NAME , CONTENT_COUNT,

				G.PRIORITY, A.PLAYLIST_TYPE, A.IGNORE_TAG, A.EVENNESS_PLAYBACK, B.HAS_SUB_PLAYLIST, C.GROUP_ID

			</when>

			<otherwise>

				<include refid="getSelectClauseFalse"/>

			</otherwise>



		</choose>

	</sql>

	<sql id="getSelectClauseFalse">

		SELECT DISTINCT A.PLAYLIST_ID, A.DEVICE_TYPE, A.DEVICE_TYPE_VERSION, A.IS_VWL, B.VERSION_ID ,

		PLAYLIST_NAME, B.CREATOR_ID, B.CREATE_DATE, A.LAST_MODIFIED_DATE,

		B.TOTAL_SIZE, B.PLAY_TIME, A.IS_DELETED, B.IS_ACTIVE, B.IS_SHUFFLE,

		A.SHARE_FLAG, PLAYLIST_META_DATA , C.GROUP_NAME , CONTENT_COUNT,

		A.PLAYLIST_TYPE, A.IGNORE_TAG, A.EVENNESS_PLAYBACK, B.HAS_SUB_PLAYLIST, C.GROUP_ID

	</sql>



	<sql id="getFromClause">

		<choose>

			<when test="minimumPriority!=null and minimumPriority.longValue() > 0l">

				FROM MI_CMS_INFO_PLAYLIST A, MI_CMS_INFO_PLAYLIST_VERSION

				B, MI_CMS_INFO_PLAYLIST_GROUP C , MI_CMS_MAP_GROUP_PLAYLIST D ,

				MI_USER_INFO_USER F

				MI_DMS_INFO_DEVICE_PRIORITY G


			</when>

			<otherwise>

				<include refid="getFromClauseFalse"/>

			</otherwise>

		</choose>

	</sql>



	<sql id="getFromClauseFalse">

		FROM MI_CMS_INFO_PLAYLIST A, MI_CMS_INFO_PLAYLIST_VERSION

		B, MI_CMS_INFO_PLAYLIST_GROUP C , MI_CMS_MAP_GROUP_PLAYLIST D ,

		MI_USER_INFO_USER F

	</sql>



	<sql id="getWhereClause">

		WHERE

				A.PLAYLIST_ID = B.PLAYLIST_ID AND 
				IS_ACTIVE = 'Y' AND 
				C.GROUP_ID = D.GROUP_ID	AND 
				D.PLAYLIST_ID = A.PLAYLIST_ID AND 
				
				( F.ROOT_GROUP_ID = A.ORGANIZATION_ID or exists (
				SELECT 1 FROM MI_USER_MAP_USER_MANAGE_ORG M, MI_USER_MAP_MANAGE_ORG_GROUP_MANAGE_ORG N
				WHERE 
					M.USER_ID = F.USER_ID AND	
					M.MNG_ORG_GROUP_ID = N.MNG_ORG_GROUP_ID AND
					N.ORG_GROUP_ID = A.ORGANIZATION_ID )
				) AND
				F.USER_ID = A.CREATOR_ID AND
		<choose>
			<when test="listType.equalsIgnoreCase(ConstGROUP_TYPE_DELETED)">
				A.IS_DELETED = 'Y'
			</when>
			<otherwise>
				A.IS_DELETED = 'N'
			</otherwise>
		</choose>

		<choose>
			<when test="organizationId != null">
				AND A.ORGANIZATION_ID = #{organizationId}
			</when>
			<otherwise>
				<if test="userManageGroupList != null and userManageGroupList.size() > 0">
					AND A.ORGANIZATION_ID IN
					<foreach item="group" collection="userManageGroupList"
						open=" (" separator="," close=")">
							#{group.group_id}
					</foreach>
				</if>
			</otherwise>
		</choose>


		<if test="deviceType != null and deviceType!= ''" >
			<choose>
				<when test="deviceType.equals(ConstTYPE_PREMIUM)">
					AND A.DEVICE_TYPE != #{ConstTYPE_APLAYER} 
					AND A.DEVICE_TYPE != #{ConstTYPE_WPLAYER}
				</when>
				<when test="deviceType.equals(ConstTYPE_SOC)">
					AND (A.DEVICE_TYPE = #{deviceType}
					<if test="deviceTypeVersion != null">
						AND A.DEVICE_TYPE_VERSION &lt;= #{deviceTypeVersion}
					</if>
					
					OR A.DEVICE_TYPE = #{ConstTYPE_LITE} )
				</when>
				<otherwise>
					AND A.DEVICE_TYPE = #{deviceType}
				</otherwise> 
			</choose>
		</if> 


		<if test="isVwl != null">
			<choose>
				<when test="isVwl == Y">
				</when>
				<otherwise>
					AND IS_VWL = 'N'
				</otherwise>
			</choose>
		</if>
		<if test="isSync != null">
			<choose>
				<when test="isSync == 0">
					AND PLAYLIST_TYPE = '0'
				</when>
				<otherwise>
				</otherwise>
			</choose>
		</if>
		<if test="searchText != null and searchText.length() > 0">

			AND (UPPER(PLAYLIST_NAME) LIKE '%'<include refid="utils.concatenate"/>#{searchText}<include refid="utils.concatenate"/>'%' ESCAPE '^')

		</if>
		<if test="searchCreator != null and searchCreator.length() > 0">
			AND (UPPER(A.CREATOR_ID) LIKE '%'<include refid="utils.concatenate"/>#{searchCreator}<include refid="utils.concatenate"/>'%' ESCAPE '^')
		</if>
		<if test="searchStartDate != null and searchStartDate.length() > 0">
            <bind name="safe_startDate" value="@com.samsung.common.utils.DaoTools@safeDateTimeString(searchStartDate)" />
			AND A.LAST_MODIFIED_DATE &gt; '${safe_startDate}'
		</if>
		<if test="searchEndDate != null and searchEndDate.length() > 0">
            <bind name="safe_endDate" value="@com.samsung.common.utils.DaoTools@safeDateTimeString(searchEndDate)" />
			AND A.LAST_MODIFIED_DATE &lt; '${safe_endDate}'
		</if>

	</sql>



	<select id="getPlaylistListPaged" resultType="com.samsung.magicinfo.framework.content.entity.Playlist">

		<include refid="getPlaylistListPaged_select"/>

		<include refid="getPlaylistListPaged_body"/>

		ORDER BY

			A.LAST_MODIFIED_DATE DESC

		LIMIT #{resultsCount} OFFSET #{startIndex}

	</select>



	<select id="getPlaylistListPaged" resultType="com.samsung.magicinfo.framework.content.entity.Playlist" databaseId="mssql">

		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startIndex)" />

        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startIndex + resultsCount)" />

        SELECT * FROM

        (

	        <include refid="getPlaylistListPaged_select"/>, ROW_NUMBER() OVER(ORDER BY A.LAST_MODIFIED_DATE DESC ) as rownum

	        <include refid="getPlaylistListPaged_body"/>

	        ) as SubQuery

	    WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit}

	    ORDER BY rownum

	</select>



	<select id="getPlaylistListByDeviceType" resultType="com.samsung.magicinfo.framework.content.entity.Playlist">

		<include refid="getPlaylistListByDeviceType_select"/>

		<include refid="getPlaylistListByDeviceType_body"/>

		ORDER BY A.LAST_MODIFIED_DATE DESC

		LIMIT #{resultsCount} OFFSET #{startIndex}

	</select>


    <select id="getPlaylistListCountByDeviceType" resultType="int">

        SELECT COUNT(A.PLAYLIST_ID)

        <include refid="getPlaylistListByDeviceType_body"/>

    </select>


	<sql id="getPlaylistListByDeviceType_select">

	SELECT A.PLAYLIST_ID, B.VERSION_ID, PLAYLIST_NAME, B.CREATOR_ID, B.CREATE_DATE, A.LAST_MODIFIED_DATE, A.DEVICE_TYPE, A.DEVICE_TYPE_VERSION,

		B.TOTAL_SIZE, B.PLAY_TIME, A.IS_DELETED, B.IS_ACTIVE, B.IS_SHUFFLE, A.SHARE_FLAG, A.IS_VWL, A.PLAYLIST_TYPE, PLAYLIST_META_DATA,

		C.GROUP_NAME, CONTENT_COUNT,  CONTENT_ID

		</sql>



	<select id="getPlaylistListByDeviceType" resultType="com.samsung.magicinfo.framework.content.entity.Playlist" databaseId="mssql">

		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startIndex)" />

        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startIndex + resultsCount)" />

        SELECT * FROM

        (

		<include refid="getPlaylistListByDeviceType_select"/>, ROW_NUMBER() OVER(ORDER BY A.LAST_MODIFIED_DATE DESC ) as rownum

		<include refid="getPlaylistListByDeviceType_body"/>) as SubQuery

		WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit}

		ORDER BY rownum

	</select>


    <select id="getPlaylistListCountByDeviceType" resultType="int"  databaseId="mssql">

        SELECT COUNT(A.PLAYLIST_ID)

        <include refid="getPlaylistListByDeviceType_body"/>

    </select>


	<sql id="getPlaylistListPaged_select">

		SELECT

			A.PLAYLIST_ID, B.VERSION_ID, PLAYLIST_NAME, B.CREATOR_ID, B.CREATE_DATE, A.LAST_MODIFIED_DATE,

			B.TOTAL_SIZE, B.PLAY_TIME, A.IS_DELETED, B.IS_ACTIVE, B.IS_SHUFFLE, A.SHARE_FLAG, A.DEVICE_TYPE, A.DEVICE_TYPE_VERSION, A.IS_VWL, PLAYLIST_META_DATA, A.PLAYLIST_TYPE,

			C.GROUP_NAME, CONTENT_COUNT,  CONTENT_ID

	</sql>

	<sql id="getPlaylistListByDeviceType_body">

		FROM MI_CMS_INFO_PLAYLIST A, MI_CMS_INFO_PLAYLIST_VERSION B, MI_CMS_INFO_PLAYLIST_GROUP C, MI_CMS_MAP_GROUP_PLAYLIST D,

		MI_CMS_MAP_PLAYLIST_CONTENT E, MI_USER_INFO_USER F

		WHERE A.PLAYLIST_ID = B.PLAYLIST_ID AND IS_ACTIVE = 'Y' AND C.GROUP_ID = D.GROUP_ID AND D.PLAYLIST_ID = A.PLAYLIST_ID

		AND E.PLAYLIST_ID = A.PLAYLIST_ID AND E.VERSION_ID = B.VERSION_ID AND 
		
		( F.ROOT_GROUP_ID = A.ORGANIZATION_ID or exists (
					SELECT 1 FROM MI_USER_MAP_USER_MANAGE_ORG M, MI_USER_MAP_MANAGE_ORG_GROUP_MANAGE_ORG N
					WHERE 
						M.USER_ID = F.USER_ID AND	
						M.MNG_ORG_GROUP_ID = N.MNG_ORG_GROUP_ID AND
						N.ORG_GROUP_ID = A.ORGANIZATION_ID )
		)

		AND F.USER_ID = A.CREATOR_ID AND A.IS_DELETED = 'N' AND E.CONTENT_ORDER = 1 AND A.CREATOR_ID = #{userId}

		AND (A.PLAYLIST_TYPE = '0' OR A.PLAYLIST_TYPE = '1')
		
		<if test="deviceType != null and deviceType!= ''" >
			<choose>
				<when test="deviceType.equalsIgnoreCase(type)">
					AND  A.DEVICE_TYPE = #{type} AND A.DEVICE_TYPE != 'APLAYER' AND A.DEVICE_TYPE != 'WPLAYER'
				</when>
				<when test="deviceType.equalsIgnoreCase('SPLAYER')">
					AND ((A.DEVICE_TYPE = #{deviceType}
					<if test="deviceTypeVersion != null">
						AND A.DEVICE_TYPE_VERSION &gt;= #{deviceTypeVersion}
					</if>
					) OR A.DEVICE_TYPE = #{type})
				</when>
				<otherwise>
					AND A.DEVICE_TYPE = #{deviceType}
				</otherwise> 
			</choose>
		</if>
		<if test="selectedDeviceType != null and selectedDeviceType.size() > 0">
			AND 
			<foreach item="item" collection="selectedDeviceType" open="(" separator=" OR " close=")">
				( A.DEVICE_TYPE = #{item.deviceType} AND A.DEVICE_TYPE_VERSION = #{item.deviceTypeVersion} )
            </foreach>
		</if>

		<!-- <choose>
			<when test="deviceType != null and deviceType!= ''" >
				<choose>
					<when test="deviceType.equalsIgnoreCase(type)">
						AND  A.DEVICE_TYPE = #{type} AND A.DEVICE_TYPE != 'APLAYER' AND A.DEVICE_TYPE != 'WPLAYER'
					</when>
					<when test="deviceType.equalsIgnoreCase('SPLAYER')">
						AND ((A.DEVICE_TYPE = #{deviceType}
						<if test="deviceTypeVersion != null">
							AND A.DEVICE_TYPE_VERSION &gt;= #{deviceTypeVersion}
						</if>
						) OR A.DEVICE_TYPE = #{type})
					</when>
					<otherwise>
						AND A.DEVICE_TYPE = #{deviceType}
					</otherwise> 
				</choose>
			</when>
			<otherwise>
				<if test="selectedDeviceType != null and selectedDeviceType.size() > 0">
					NOT
					<foreach item="item" collection="selectedDeviceType" open="(" separator=" OR " close=")">
						( A.DEVICE_TYPE = #{item.deviceType} AND A.DEVICE_TYPE_VERSION = #{item.deviceTypeVersion} )
		            </foreach>
				</if>
			</otherwise>
		</choose> --> 

	</sql>



	<sql id="getPlaylistListPaged_body">

		FROM

			MI_CMS_INFO_PLAYLIST A,

			MI_CMS_INFO_PLAYLIST_VERSION B,

			MI_CMS_INFO_PLAYLIST_GROUP C,

			MI_CMS_MAP_GROUP_PLAYLIST D,

			MI_CMS_MAP_PLAYLIST_CONTENT E,

			MI_USER_INFO_USER F

		WHERE

			A.PLAYLIST_ID = B.PLAYLIST_ID AND IS_ACTIVE = 'Y' AND C.GROUP_ID = D.GROUP_ID AND D.PLAYLIST_ID = A.PLAYLIST_ID

			AND E.PLAYLIST_ID = A.PLAYLIST_ID AND E.VERSION_ID = B.VERSION_ID AND 
			
			( F.ROOT_GROUP_ID = A.ORGANIZATION_ID or exists (
					SELECT 1 FROM MI_USER_MAP_USER_MANAGE_ORG M, MI_USER_MAP_MANAGE_ORG_GROUP_MANAGE_ORG N
					WHERE 
						M.USER_ID = F.USER_ID AND	
						M.MNG_ORG_GROUP_ID = N.MNG_ORG_GROUP_ID AND
						N.ORG_GROUP_ID = A.ORGANIZATION_ID )
			)

			AND F.USER_ID = A.CREATOR_ID AND A.IS_DELETED = 'N' AND E.CONTENT_ORDER = 1 AND A.CREATOR_ID = #{userId}

	</sql>



	<select id="getPlaylistListToDeleteContent" resultType="com.samsung.magicinfo.framework.content.entity.Playlist">

		<include refid="getPlaylistListToDeleteContent_select"/>

		<include refid="getPlaylistListToDeleteContent_body"/>

		ORDER BY

			A.LAST_MODIFIED_DATE DESC

		LIMIT #{resultsCount} OFFSET #{startIndex}

	</select>



	<select id="getPlaylistListToDeleteContent" resultType="com.samsung.magicinfo.framework.content.entity.Playlist" databaseId="mssql">

		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startIndex)" />

        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startIndex + resultsCount)" />

        SELECT * FROM

        (

	        <include refid="getPlaylistListToDeleteContent_select"/>, ROW_NUMBER() OVER(ORDER BY A.LAST_MODIFIED_DATE DESC) as rownum

	        <include refid="getPlaylistListToDeleteContent_body"/>

	        ) as SubQuery

	    WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit}

	    ORDER BY rownum

	</select>



	<sql id="getPlaylistListToDeleteContent_select">

		SELECT

			DISTINCT (A.PLAYLIST_ID), B.VERSION_ID, PLAYLIST_NAME, B.CREATOR_ID,

			B.CREATE_DATE, A.LAST_MODIFIED_DATE, B.TOTAL_SIZE, B.PLAY_TIME, A.IS_DELETED,

			B.IS_ACTIVE, B.IS_SHUFFLE, A.SHARE_FLAG, PLAYLIST_META_DATA, A.DEVICE_TYPE, A.DEVICE_TYPE_VERSION, A.IS_VWL, A.PLAYLIST_TYPE,

			C.GROUP_NAME , CONTENT_COUNT, E.CONTENT_ID

	</sql>



	<sql id="getPlaylistListToDeleteContent_body">

		FROM

			MI_CMS_INFO_PLAYLIST A,

			MI_CMS_INFO_PLAYLIST_VERSION B,

			MI_CMS_INFO_PLAYLIST_GROUP C,

			MI_CMS_MAP_GROUP_PLAYLIST D,

			(

				SELECT

					E.PLAYLIST_ID,

					E.VERSION_ID,

					E.CONTENT_ID

				FROM

					MI_CMS_MAP_PLAYLIST_CONTENT E

				WHERE

					<foreach item="item" collection="contentIdList" open="(" separator=" OR " close=")">

		            	E.CONTENT_ID = #{item}

		            </foreach>

		     ) AS E,

			MI_USER_INFO_USER F

		WHERE

			A.PLAYLIST_ID = B.PLAYLIST_ID AND IS_ACTIVE = 'Y' AND C.GROUP_ID = D.GROUP_ID AND D.PLAYLIST_ID = A.PLAYLIST_ID

			AND E.PLAYLIST_ID = A.PLAYLIST_ID AND E.VERSION_ID = B.VERSION_ID AND 
			
			( F.ROOT_GROUP_ID = A.ORGANIZATION_ID or exists (
					SELECT 1 FROM MI_USER_MAP_USER_MANAGE_ORG M, MI_USER_MAP_MANAGE_ORG_GROUP_MANAGE_ORG N
					WHERE 
						M.USER_ID = F.USER_ID AND	
						M.MNG_ORG_GROUP_ID = N.MNG_ORG_GROUP_ID AND
						N.ORG_GROUP_ID = A.ORGANIZATION_ID )
			)

			AND F.USER_ID = A.CREATOR_ID AND A.IS_DELETED = 'N' AND A.CREATOR_ID = #{userId}

	</sql>



	<select id="getAllDeletedPlaylistList" resultType="map">

		SELECT

			PLAYLIST_ID

		FROM

			MI_CMS_INFO_PLAYLIST

		WHERE

			IS_DELETED = 'Y' AND CREATOR_ID = #{creatorId} AND ORGANIZATION_ID = #{organizationId}

	</select>



	<select id="getAllPlaylistList" resultType="map">

		SELECT

			A.PLAYLIST_ID

		FROM

			MI_CMS_INFO_PLAYLIST A,

			MI_CMS_MAP_GROUP_PLAYLIST B,

			MI_CMS_INFO_PLAYLIST_VERSION C

		WHERE

			A.PLAYLIST_ID = B.PLAYLIST_ID AND A.PLAYLIST_ID = C.PLAYLIST_ID

			AND C.IS_ACTIVE = 'Y' AND A.IS_DELETED = 'N' AND A.CREATOR_ID = #{creatorId}

			AND A.ORGANIZATION_ID = #{organizationId}

	</select>



	<select id="getAllPlaylistListGroup" resultType="map">

		SELECT

			A.PLAYLIST_ID

		FROM

			MI_CMS_INFO_PLAYLIST A,

			MI_CMS_MAP_GROUP_PLAYLIST B,

			MI_CMS_INFO_PLAYLIST_VERSION C

		WHERE

			A.PLAYLIST_ID = B.PLAYLIST_ID AND A.PLAYLIST_ID = C.PLAYLIST_ID

			AND C.IS_ACTIVE = 'Y' AND A.IS_DELETED = 'N' AND A.CREATOR_ID = #{creatorId}

			AND A.ORGANIZATION_ID = #{organizationId}

			<if test="groupId != null">

				AND GROUP_ID = #{groupId}

			</if>

	</select>



	<select id="getPlaylistListByUser" resultType="com.samsung.magicinfo.framework.content.entity.Playlist">

		SELECT *

		FROM

			MI_CMS_INFO_PLAYLIST A,

			MI_CMS_MAP_GROUP_PLAYLIST B,

			MI_CMS_INFO_PLAYLIST_VERSION C

		WHERE

			A.PLAYLIST_ID = B.PLAYLIST_ID AND A.PLAYLIST_ID = C.PLAYLIST_ID

			AND C.IS_ACTIVE = 'Y' AND A.IS_DELETED = 'N' AND A.CREATOR_ID = #{creatorId}

			<if test="organizationId != null">

				AND A.ORGANIZATION_ID = #{organizationId}

			</if>

			<if test="!canReadUnsharedPlaylist">

				AND A.SHARE_FLAG = #{constFLAG_DEFAULT}

			</if>

	</select>

	<sql id="getContentListSelect">
		SELECT
		A.*
		FROM
		(
			SELECT PLAYLIST_ID, PLAYLIST_CONTENTS.VERSION_ID, PLAYLIST_CONTENTS.CONTENT_ID, CONTENT_ORDER, CONTENT_DURATION, CONTENT_DURATION_MILLI,
			SYNC_PLAY_ID, AGE, GENDER, AMS_RECOG_TYPE, RANDOM_COUNT, EFFECT_IN_NAME, EFFECT_IN_DURATION, EFFECT_IN_DIRECTION, EFFECT_OUT_NAME , EFFECT_OUT_DURATION, EFFECT_OUT_DIRECTION, START_DATE, EXPIRED_DATE, EFFECT_IN_DELAY_DURATION, EFFECT_OUT_DELAY_DURATION, EFFECT_IN_DELAY_DIRECTION, EFFECT_OUT_DELAY_DIRECTION, EFFECT_IN_DELAY_DIV, EFFECT_OUT_DELAY_DIV, START_TIME, EXPIRED_TIME, REPEAT_TYPE, PLAY_WEIGHT, IS_INDEPENDENT_PLAY, CONTIGUOUS,
			TOTAL_SIZE, PLAY_TIME, MEDIA_TYPE,
			NULL AS IS_SUB_PLAYLIST
			FROM MI_CMS_MAP_PLAYLIST_CONTENT PLAYLIST_CONTENTS, MI_CMS_INFO_CONTENT_VERSION CONTENTS
			WHERE PLAYLIST_ID = #{playlistId} AND PLAYLIST_CONTENTS.VERSION_ID = #{versionId} AND CONTENTS.IS_ACTIVE = 'Y' AND PLAYLIST_CONTENTS.CONTENT_ID = CONTENTS.CONTENT_ID
			UNION
			SELECT PLAYLIST_ID, VERSION_ID, CONTENT_ID, CONTENT_ORDER,
			NULL AS CONTENT_DURATION, NULL AS CONTENT_DURATION_MILLI, SYNC_PLAY_ID, NULL AS AGE, NULL AS GENDER, NULL AS AMS_RECOG_TYPE, NULL AS RANDOM_COUNT, NULL AS EFFECT_IN_NAME, NULL AS EFFECT_IN_DURATION, NULL AS EFFECT_IN_DIRECTION, NULL AS EFFECT_OUT_NAME , NULL AS EFFECT_OUT_DURATION, NULL AS EFFECT_OUT_DIRECTION, NULL AS START_DATE, NULL AS EXPIRED_DATE, NULL AS EFFECT_IN_DELAY_DURATION, NULL AS EFFECT_OUT_DELAY_DURATION, NULL AS EFFECT_IN_DELAY_DIRECTION, NULL AS EFFECT_OUT_DELAY_DIRECTION, NULL AS EFFECT_IN_DELAY_DIV, NULL AS EFFECT_OUT_DELAY_DIV, NULL AS START_TIME, NULL AS EXPIRED_TIME, NULL AS REPEAT_TYPE, NULL AS PLAY_WEIGHT, NULL AS IS_INDEPENDENT_PLAY, NULL AS CONTIGUOUS,
			NULL AS TOTAL_SIZE, NULL AS PLAY_TIME, NULL AS MEDIA_TYPE, IS_SUB_PLAYLIST
			FROM MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST
			WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
		) A
	</sql>

	<select id="getContentList" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistContent">
		<include refid="getContentListSelect"/>
		 ORDER BY A.SYNC_PLAY_ID::INTEGER, A.CONTENT_ORDER ASC
	</select>

	<select id="getContentList" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistContent" databaseId="mssql">
		<include refid="getContentListSelect"/>
		ORDER BY CONVERT(INT, A.SYNC_PLAY_ID), A.CONTENT_ORDER ASC
	</select>

	<select id="getTagList" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistContent">
		SELECT PLAYLIST.TAG_ID, PLAYLIST_ID, VERSION_ID, TAG.TAG_NAME, TAG.TAG_TYPE, TAG_ORDER, START_DATE, EXPIRED_DATE, START_TIME, EXPIRED_TIME, TAG_DURATION, NUMBER_STR
		FROM MI_CMS_MAP_PLAYLIST_TAG PLAYLIST LEFT JOIN MI_TAG_INFO_TAG TAG ON PLAYLIST.TAG_ID = TAG.TAG_ID
		WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
		ORDER BY TAG_ORDER ASC
	</select>

	<select id="getTagListWithExpiredDate" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistContent">
		SELECT PLAYLIST.TAG_ID, PLAYLIST_ID, VERSION_ID, TAG.TAG_NAME, TAG.TAG_TYPE, TAG_ORDER, START_DATE, EXPIRED_DATE, START_TIME, EXPIRED_TIME, TAG_DURATION, NUMBER_STR
		FROM MI_CMS_MAP_PLAYLIST_TAG PLAYLIST LEFT JOIN MI_TAG_INFO_TAG TAG ON PLAYLIST.TAG_ID = TAG.TAG_ID
		WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId} <if test="expiredDate != null"> AND ( EXPIRED_DATE IS NULL OR EXPIRED_DATE >= to_date(#{expiredDate}, 'yyyy-MM-dd'))</if>
		ORDER BY TAG_ORDER ASC
	</select>

	<select id="getTagListWithExpiredDate" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistContent" databaseId="mssql">
		SELECT PLAYLIST.TAG_ID, PLAYLIST_ID, VERSION_ID, TAG.TAG_NAME, TAG.TAG_TYPE, TAG_ORDER, START_DATE, EXPIRED_DATE, START_TIME, EXPIRED_TIME, TAG_DURATION, NUMBER_STR
		FROM MI_CMS_MAP_PLAYLIST_TAG PLAYLIST LEFT JOIN MI_TAG_INFO_TAG TAG ON PLAYLIST.TAG_ID = TAG.TAG_ID
		WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId} <if test="expiredDate != null"> AND ( EXPIRED_DATE IS NULL OR EXPIRED_DATE >= CAST(#{expiredDate} AS DATE))</if>
		ORDER BY TAG_ORDER ASC
	</select>

	<insert id="addTagConditionMapping">
		INSERT INTO MI_CMS_MAP_PLAYLIST_TAG_CONDITION (PLAYLIST_ID, VERSION_ID, TAG_ID, TAG_CONDITION_ID)
		VALUES (#{playlistId}, #{versionId}, #{tagId}, #{tagConditionId})
	</insert>



	<select id="getContentCount" resultType="int">

		SELECT COUNT(PLAYLIST_ID) FROM MI_CMS_MAP_PLAYLIST_CONTENT WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}

	</select>

	<select id="getCountSyncGroup" resultType="int">

		SELECT COUNT(DISTINCT(SYNC_PLAY_ID)) FROM MI_CMS_MAP_PLAYLIST_CONTENT

		WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}

	</select>

	<select id="getMaxCountSyncContent" resultType="int">

		SELECT MAX(CONTENT_ORDER) FROM MI_CMS_MAP_PLAYLIST_CONTENT

		WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}

	</select>

	<select id="getMainContentId" resultType="string">

		SELECT

			CONTENT_ID

		FROM

			MI_CMS_MAP_PLAYLIST_CONTENT A,

			MI_CMS_INFO_PLAYLIST_VERSION B

		WHERE

			IS_ACTIVE = 'Y' AND A.PLAYLIST_ID = B.PLAYLIST_ID AND A.VERSION_ID = B.VERSION_ID

			AND B.PLAYLIST_ID = #{playlistId} AND CONTENT_ORDER = 1 LIMIT 1

	</select>

	<select id="getMainContentId" resultType="string" databaseId="mssql">

		SELECT

			TOP 1 CONTENT_ID

		FROM

			MI_CMS_MAP_PLAYLIST_CONTENT A,

			MI_CMS_INFO_PLAYLIST_VERSION B

		WHERE

			IS_ACTIVE = 'Y' AND A.PLAYLIST_ID = B.PLAYLIST_ID AND A.VERSION_ID = B.VERSION_ID

			AND B.PLAYLIST_ID = #{playlistId} AND CONTENT_ORDER = 1

	</select>

	<select id="getTagPlaylistActiveVerContentList" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistContent">

	</select>

	<select id="getActiveVerContentList" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistContent">
		SELECT
		A.*
		FROM
		(
			 SELECT SUB_PLAYLIST.PLAYLIST_ID, SUB_PLAYLIST.VERSION_ID, SUB_PLAYLIST.CONTENT_ID, SUB_PLAYLIST.CONTENT_ORDER,
			 NULL AS CONTENT_DURATION, NULL AS CONTENT_DURATION_MILLI, SYNC_PLAY_ID, NULL AS AGE, NULL AS GENDER, NULL AS AMS_RECOG_TYPE, NULL AS RANDOM_COUNT, NULL AS EFFECT_IN_NAME, NULL AS EFFECT_IN_DURATION, NULL AS EFFECT_IN_DIRECTION, NULL AS EFFECT_OUT_NAME , NULL AS EFFECT_OUT_DURATION, NULL AS EFFECT_OUT_DIRECTION, NULL AS START_DATE, NULL AS EXPIRED_DATE, NULL AS EFFECT_IN_DELAY_DURATION, NULL AS EFFECT_OUT_DELAY_DURATION, NULL AS EFFECT_IN_DELAY_DIRECTION, NULL AS EFFECT_OUT_DELAY_DIRECTION, NULL AS EFFECT_IN_DELAY_DIV, NULL AS EFFECT_OUT_DELAY_DIV, NULL AS START_TIME, NULL AS EXPIRED_TIME, NULL AS REPEAT_TYPE, NULL AS PLAY_WEIGHT, NULL AS IS_INDEPENDENT_PLAY, NULL AS CONTIGUOUS,
			 IS_SUB_PLAYLIST
			 FROM MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST SUB_PLAYLIST, MI_CMS_INFO_PLAYLIST_VERSION VERSIONS
			 WHERE SUB_PLAYLIST.PLAYLIST_ID = #{playlistId}
             AND VERSIONS.IS_ACTIVE = 'Y' AND SUB_PLAYLIST.VERSION_ID = VERSIONS.VERSION_ID AND VERSIONS.PLAYLIST_ID = SUB_PLAYLIST.PLAYLIST_ID
             UNION
             SELECT PLAYLIST_CONTENTS.PLAYLIST_ID, PLAYLIST_CONTENTS.VERSION_ID, PLAYLIST_CONTENTS.CONTENT_ID, CONTENT_ORDER, CONTENT_DURATION, CONTENT_DURATION_MILLI,
			 SYNC_PLAY_ID, AGE, GENDER, AMS_RECOG_TYPE, RANDOM_COUNT, EFFECT_IN_NAME, EFFECT_IN_DURATION, EFFECT_IN_DIRECTION, EFFECT_OUT_NAME , EFFECT_OUT_DURATION, EFFECT_OUT_DIRECTION, START_DATE, EXPIRED_DATE, EFFECT_IN_DELAY_DURATION, EFFECT_OUT_DELAY_DURATION, EFFECT_IN_DELAY_DIRECTION, EFFECT_OUT_DELAY_DIRECTION, EFFECT_IN_DELAY_DIV, EFFECT_OUT_DELAY_DIV, START_TIME, EXPIRED_TIME, REPEAT_TYPE, PLAY_WEIGHT, IS_INDEPENDENT_PLAY, CONTIGUOUS,
			 NULL AS IS_SUB_PLAYLIST
			 FROM MI_CMS_MAP_PLAYLIST_CONTENT PLAYLIST_CONTENTS, MI_CMS_INFO_PLAYLIST_VERSION VERSION
			 WHERE PLAYLIST_CONTENTS.PLAYLIST_ID = #{playlistId} AND VERSION.IS_ACTIVE = 'Y' AND PLAYLIST_CONTENTS.PLAYLIST_ID = VERSION.PLAYLIST_ID AND VERSION.VERSION_ID = PLAYLIST_CONTENTS.VERSION_ID
		 ) A
		 ORDER BY A.SYNC_PLAY_ID, A.CONTENT_ORDER ASC
	</select>

	<select id="getActiveVerContentListForDownloadCheck" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistContent">
		SELECT
		A.*
		FROM
		(
			 SELECT VERSIONS.PLAYLIST_ID, VERSIONS.VERSION_ID, CONTENTS.CONTENT_ID, SUB_PLAYLIST.CONTENT_ORDER,
			 CONTENTS.CONTENT_DURATION, CONTENTS.CONTENT_DURATION_MILLI, CONTENTS.SYNC_PLAY_ID, CONTENTS.AGE, CONTENTS.GENDER, CONTENTS.AMS_RECOG_TYPE, CONTENTS.RANDOM_COUNT, CONTENTS.EFFECT_IN_NAME, CONTENTS.EFFECT_IN_DURATION, CONTENTS.EFFECT_IN_DIRECTION, CONTENTS.EFFECT_OUT_NAME , NULL AS EFFECT_OUT_DURATION, NULL AS EFFECT_OUT_DIRECTION, NULL AS START_DATE, NULL AS EXPIRED_DATE, NULL AS EFFECT_IN_DELAY_DURATION, NULL AS EFFECT_OUT_DELAY_DURATION, NULL AS EFFECT_IN_DELAY_DIRECTION, NULL AS EFFECT_OUT_DELAY_DIRECTION, NULL AS EFFECT_IN_DELAY_DIV, NULL AS EFFECT_OUT_DELAY_DIV, NULL AS START_TIME, NULL AS EXPIRED_TIME, NULL AS REPEAT_TYPE, NULL AS PLAY_WEIGHT, NULL AS IS_INDEPENDENT_PLAY, NULL AS CONTIGUOUS,
			 SUB_PLAYLIST.IS_SUB_PLAYLIST
			 FROM MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST SUB_PLAYLIST, MI_CMS_INFO_PLAYLIST_VERSION VERSIONS, MI_CMS_MAP_PLAYLIST_CONTENT CONTENTS
			 WHERE SUB_PLAYLIST.PLAYLIST_ID = #{playlistId} AND CONTENTS.PLAYLIST_ID = SUB_PLAYLIST.CONTENT_ID
			 AND VERSIONS.PLAYLIST_ID = CONTENTS.PLAYLIST_ID AND VERSIONS.IS_ACTIVE = 'Y' AND CONTENTS.VERSION_ID = VERSIONS.VERSION_ID
			UNION
             SELECT PLAYLIST_CONTENTS.PLAYLIST_ID, PLAYLIST_CONTENTS.VERSION_ID, PLAYLIST_CONTENTS.CONTENT_ID, CONTENT_ORDER, CONTENT_DURATION, CONTENT_DURATION_MILLI,
			 SYNC_PLAY_ID, AGE, GENDER, AMS_RECOG_TYPE, RANDOM_COUNT, EFFECT_IN_NAME, EFFECT_IN_DURATION, EFFECT_IN_DIRECTION, EFFECT_OUT_NAME , EFFECT_OUT_DURATION, EFFECT_OUT_DIRECTION, START_DATE, EXPIRED_DATE, EFFECT_IN_DELAY_DURATION, EFFECT_OUT_DELAY_DURATION, EFFECT_IN_DELAY_DIRECTION, EFFECT_OUT_DELAY_DIRECTION, EFFECT_IN_DELAY_DIV, EFFECT_OUT_DELAY_DIV, START_TIME, EXPIRED_TIME, REPEAT_TYPE, PLAY_WEIGHT, IS_INDEPENDENT_PLAY, CONTIGUOUS,
			 NULL AS IS_SUB_PLAYLIST
			 FROM MI_CMS_MAP_PLAYLIST_CONTENT PLAYLIST_CONTENTS, MI_CMS_INFO_PLAYLIST_VERSION VERSION
			 WHERE PLAYLIST_CONTENTS.PLAYLIST_ID = #{playlistId} AND VERSION.IS_ACTIVE = 'Y' AND PLAYLIST_CONTENTS.PLAYLIST_ID = VERSION.PLAYLIST_ID AND VERSION.VERSION_ID = PLAYLIST_CONTENTS.VERSION_ID
		 ) A
		 ORDER BY A.SYNC_PLAY_ID, A.CONTENT_ORDER ASC
	</select>



	<select id="getContentEffectInfo" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistContent">

		SELECT

			CONTENT_ID, CONTENT_ORDER, A.AGE, A.GENDER, EFFECT_IN_NAME, A.AMS_RECOG_TYPE,

			EFFECT_IN_DURATION, EFFECT_IN_DIRECTION, EFFECT_OUT_NAME , EFFECT_OUT_DURATION, B.AMS_MODE, B.AMS_DIRECT_PLAY,

			EFFECT_OUT_DIRECTION, CONTENT_DURATION, EFFECT_IN_DELAY_DURATION, EFFECT_OUT_DELAY_DURATION, EFFECT_IN_DELAY_DIRECTION, EFFECT_OUT_DELAY_DIRECTION, EFFECT_IN_DELAY_DIV, EFFECT_OUT_DELAY_DIV,

			START_TIME, EXPIRED_TIME, REPEAT_TYPE, PLAY_WEIGHT, IS_INDEPENDENT_PLAY, CONTIGUOUS

		FROM

			MI_CMS_MAP_PLAYLIST_CONTENT A,

			MI_CMS_INFO_PLAYLIST_VERSION B

		WHERE

			A.PLAYLIST_ID = B.PLAYLIST_ID AND A.VERSION_ID = B.VERSION_ID

			AND A.PLAYLIST_ID = #{playlistId} AND A.VERSION_ID = #{versionId} AND A.CONTENT_ID = #{contentId}

	</select>



	<select id="getContentEffectInfoByOrder" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistContent">

		SELECT

			*

		FROM

			MI_CMS_MAP_PLAYLIST_CONTENT A, MI_CMS_INFO_PLAYLIST_VERSION B

		WHERE

			A.PLAYLIST_ID = B.PLAYLIST_ID AND A.VERSION_ID = B.VERSION_ID

			AND A.PLAYLIST_ID = #{playlistId} AND A.VERSION_ID = #{versionId} AND A.CONTENT_ORDER = #{contentOrder} LIMIT 1

	</select>

	<select id="getContentEffectInfoByOrder" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistContent" databaseId="mssql">

		SELECT

			TOP 1 *

		FROM

			MI_CMS_MAP_PLAYLIST_CONTENT A, MI_CMS_INFO_PLAYLIST_VERSION B

		WHERE

			A.PLAYLIST_ID = B.PLAYLIST_ID AND A.VERSION_ID = B.VERSION_ID

			AND A.PLAYLIST_ID = #{playlistId} AND A.VERSION_ID = #{versionId} AND A.CONTENT_ORDER = #{contentOrder}

	</select>



	<select id="getEffectInfoByEffectName" resultType="com.samsung.magicinfo.framework.content.entity.Effect">

		SELECT * FROM MI_CMS_INFO_PLAYLIST_EFFECT WHERE EFFECT_NAME = #{effectName}

	</select>

	<select id="getSocEffectInfoByEffectName" resultType="com.samsung.magicinfo.framework.content.entity.Effect">

		SELECT * FROM MI_CMS_INFO_SOC_PLAYLIST_EFFECT WHERE EFFECT_NAME = #{effectName}

	</select>



	<select id="countPlaylistID" resultType="int">

		SELECT COUNT(PLAYLIST_ID) FROM MI_CMS_INFO_PLAYLIST WHERE PLAYLIST_ID = #{playlistId}

	</select>



	<select  id="countPlaylistVersion" resultType="int">

		SELECT COUNT(PLAYLIST_ID) FROM MI_CMS_INFO_PLAYLIST_VERSION WHERE PLAYLIST_ID = #{playlistId}  AND VERSION_ID = #{versionId}

	</select>



	<select id="countUpdatablePlaylist" resultType="int">

		SELECT COUNT(PLAYLIST_ID) FROM MI_CMS_INFO_PLAYLIST WHERE PLAYLIST_ID = #{playlistId}

	</select>



	<select id="countLockedPlaylist" resultType="int">

		SELECT COUNT(PLAYLIST_ID) FROM MI_CMS_INFO_PLAYLIST WHERE SESSION_ID IS NOT NULL AND PLAYLIST_ID = #{playlistId} AND SESSION_ID != #{sessionId}

	</select>





	<insert id="addPlaylistInfo" parameterType="map">

		INSERT INTO MI_CMS_INFO_PLAYLIST (PLAYLIST_ID, PLAYLIST_NAME, PLAYLIST_META_DATA, SHARE_FLAG, DEVICE_TYPE, IS_DELETED , LAST_MODIFIED_DATE , CREATOR_ID, CREATE_DATE, ORGANIZATION_ID, IS_VWL, DEVICE_TYPE_VERSION, PLAYLIST_TYPE, DEFAULT_CONTENT_DURATION, IGNORE_TAG, EVENNESS_PLAYBACK)

		VALUES (#{playlist_id}, #{playlist_name}, #{playlist_meta_data}, #{share_flag}, #{device_type}, #{is_deleted}, CURRENT_TIMESTAMP , #{creator_id}, CURRENT_TIMESTAMP, #{organization_id}, #{is_vwl}, #{device_type_version}, #{playlist_type}, #{default_content_duration}, #{ignore_tag}, #{evenness_playback})

	</insert>



	<insert id="addPlaylistVersionInfo" parameterType="com.samsung.magicinfo.framework.content.entity.Playlist">

		INSERT INTO  MI_CMS_INFO_PLAYLIST_VERSION (PLAYLIST_ID, VERSION_ID, CREATOR_ID, CREATE_DATE, PLAY_TIME, TOTAL_SIZE,  IS_ACTIVE, IS_SHUFFLE , CONTENT_COUNT, AMS_MODE, AMS_DIRECT_PLAY, HAS_SUB_PLAYLIST)

		VALUES (#{playlist_id}, #{version_id}, #{creator_id}, CURRENT_TIMESTAMP, #{play_time}, #{total_size}, #{is_active}, #{is_shuffle} , #{content_count}, #{ams_mode}, #{ams_direct_play}, #{has_sub_playlist})

	</insert>



	<select id="getPlaylistNextVer" resultType="long">

		SELECT MAX(VERSION_ID) FROM MI_CMS_INFO_PLAYLIST_VERSION WHERE PLAYLIST_ID = #{playlistId}

	</select>



	<update id="setOtherVersionInactive">

		UPDATE MI_CMS_INFO_PLAYLIST_VERSION SET IS_ACTIVE = 'N' WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID NOT IN (#{versionId})

	</update>


	<delete id="deleteMapGroupPlaylist">

		DELETE FROM MI_CMS_MAP_GROUP_PLAYLIST WHERE PLAYLIST_ID = #{playlistId}

	</delete>



	<insert id="addMapGroupPlaylist">

		INSERT INTO  MI_CMS_MAP_GROUP_PLAYLIST (PLAYLIST_ID, GROUP_ID)

		VALUES (#{playlistId}, #{groupId})

	</insert>



	<insert id="addMapPlaylistContentInsert">
		<choose>
			<when test="is_sub_playlist == true">
				INSERT INTO  MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST (PLAYLIST_ID, VERSION_ID, CONTENT_ID, CONTENT_ORDER, IS_SUB_PLAYLIST)
				VALUES (#{playlist_id}, #{version_id}, #{content_id}, #{content_order}, #{is_sub_playlist});
			</when>
			<otherwise>
				INSERT INTO  MI_CMS_MAP_PLAYLIST_CONTENT (PLAYLIST_ID, VERSION_ID, CONTENT_ID,  CONTENT_ORDER, AGE, GENDER, EFFECT_IN_NAME, EFFECT_IN_DURATION, EFFECT_IN_DIRECTION, EFFECT_OUT_NAME, EFFECT_OUT_DURATION, EFFECT_OUT_DIRECTION, CONTENT_DURATION,

				EFFECT_IN_DELAY_DURATION, EFFECT_OUT_DELAY_DURATION, EFFECT_IN_DELAY_DIRECTION, EFFECT_OUT_DELAY_DIRECTION, EFFECT_IN_DELAY_DIV, EFFECT_OUT_DELAY_DIV, CONTENT_DURATION_MILLI, SYNC_PLAY_ID, AMS_RECOG_TYPE, RANDOM_COUNT,

				START_TIME, EXPIRED_TIME, REPEAT_TYPE, PLAY_WEIGHT, IS_INDEPENDENT_PLAY, CONTIGUOUS)

				VALUES ( #{playlist_id}, #{version_id}, #{content_id}, #{content_order}, #{age}, #{gender}, #{effect_in_name} ,#{effect_in_duration} , #{effect_in_direction} , #{effect_out_name}, #{effect_out_duration}, #{effect_out_direction}, #{content_duration},

				#{effect_in_delay_duration}, #{effect_out_delay_duration}, #{effect_in_delay_direction}, #{effect_out_delay_direction}, #{effect_in_delay_div}, #{effect_out_delay_div}, #{content_duration_milli}, #{sync_play_id}, #{ams_recog_type}, #{random_count},

				#{start_time}, #{expired_time}, #{repeat_type}, #{play_weight}, #{is_independent_play}, #{contiguous});
			</otherwise>
		</choose>
	</insert>

	<insert id="addMapPlaylistTagInsert">
		INSERT INTO  MI_CMS_MAP_PLAYLIST_TAG (PLAYLIST_ID, VERSION_ID, TAG_ID, TAG_ORDER, CONTENT_DURATION_MILLI, START_TIME, EXPIRED_TIME, TAG_DURATION, NUMBER_STR)
		VALUES ( #{tag.playlist_id}, #{tag.version_id}, #{tag.tag_id}, #{tag.tag_order}, #{tag.content_duration_milli}, #{tag.start_time}, #{tag.expired_time}, #{tag.tag_duration}, #{tag.number_str});
	</insert>



	<update id="addMapPlaylistContentUpdate">

		UPDATE MI_CMS_MAP_PLAYLIST_CONTENT

		<set>

			<if test="expired_date != null">EXPIRED_DATE = #{expired_date},</if>

			<if test="start_date != null">START_DATE = #{start_date}</if>

		</set>

		WHERE PLAYLIST_ID = #{playlist_id} AND VERSION_ID = #{version_id} AND CONTENT_ID = #{content_id} AND CONTENT_ORDER = #{content_order}

	</update>

	<update id="addMapPlaylistTagUpdate">

		UPDATE MI_CMS_MAP_PLAYLIST_TAG

		<set>

			<if test="tag.expired_date != null">EXPIRED_DATE = #{tag.expired_date},</if>

			<if test="tag.start_date != null">START_DATE = #{tag.start_date}</if>

		</set>

		WHERE PLAYLIST_ID = #{tag.playlist_id} AND VERSION_ID = #{tag.version_id} AND TAG_ID = #{tag.tag_id}

	</update>



	<insert id="setContentTag">

		INSERT INTO MI_TAG_MAP_CONTENT_TAG (playlist_id, version_id, content_id, content_order, match_type, tag_id)

		VALUES (#{playlistId}, #{versionId}, #{contentId}, #{contentOrder}, #{matchType}, #{tagId})

	</insert>

	<insert id="setPlaylistTag">

		INSERT INTO MI_TAG_MAP_PLAYLIST_TAG (playlist_id, version_id, playlist_tag_id, tag_order, match_type, tag_id)

		VALUES (#{playlistId}, #{versionId}, #{playlistTagId}, #{tagOrder}, #{matchType}, #{tagId})

	</insert>



	<update id="setPlaylistModifiedDate">

		UPDATE MI_CMS_INFO_PLAYLIST SET LAST_MODIFIED_DATE = CURRENT_TIMESTAMP WHERE PLAYLIST_ID = #{playlistId}

	</update>



	<select id="getMaxContentOrder" resultType="long">

		SELECT MAX(CONTENT_ORDER) FROM MI_CMS_MAP_PLAYLIST_CONTENT WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}

	</select>



	<select id="getPlaylistMaxVer" resultType="long">

		SELECT MAX(VERSION_ID) FROM MI_CMS_INFO_PLAYLIST_VERSION WHERE PLAYLIST_ID = #{playlistId}

	</select>



	<update id="setVersionPlaylistActive">

		UPDATE MI_CMS_INFO_PLAYLIST_VERSION SET IS_ACTIVE = 'Y' WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}

	</update>



	<update id="setPlaylistEffect">

		UPDATE MI_CMS_MAP_PLAYLIST_CONTENT

		<set>

	    	<if test="effect_in_name != null">EFFECT_IN_NAME = #{effect_in_name},</if>

	    	<if test="effect_in_duration != null">EFFECT_IN_DURATION = #{effect_in_duration},</if>

	    	<if test="effect_in_direction != null">EFFECT_IN_DIRECTION = #{effect_in_direction},</if>

	    	<if test="effect_out_name != null">EFFECT_OUT_NAME = #{effect_out_name},</if>

	    	<if test="effect_out_duration != null">EFFECT_OUT_DURATION = #{effect_out_duration},</if>

	    	<if test="effect_out_direction != null">EFFECT_OUT_DIRECTION = #{effect_out_direction},</if>

	    	<if test="effect_in_delay_duration != null">EFFECT_IN_DELAY_DURATION = #{effect_in_duration},</if>

	    	<if test="effect_in_delay_direction != null">EFFECT_IN_DELAY_DIRECTION = #{effect_in_duration},</if>

	    	<if test="effect_in_delay_div != null">EFFECT_IN_DELAY_DIV = #{effect_in_direction},</if>

	    	<if test="effect_out_duration != null">EFFECT_OUT_DELAY_DURATION = #{effect_out_duration},</if>

	    	<if test="effect_out_direction != null">EFFECT_OUT_DELAY_DIRECTION = #{effect_out_direction},</if>

	    	<if test="effect_out_delay_div != null">EFFECT_OUT_DELAY_DIV = #{effect_out_direction},</if>

	    	<if test="content_duration != null">CONTENT_DURATION = #{content_duration},</if>

	    	<if test="expired_date != null">EXPIRED_DATE = #{expired_date},</if>

	    	<if test="start_date != null">START_DATE = #{start_date},</if>

	    	<if test="gender != null">GENDER = #{gender},</if>

	    	<if test="age != null">AGE = #{age},</if>

	    	<if test="ams_recog_type != null">AMS_RECOG_TYPE = #{ams_recog_type}</if>

	    	<if test="start_time != null">START_TIME = #{start_time}</if>

	    	<if test="expired_time != null">EXPIRED_TIME = #{expired_time}</if>

	    	<if test="repeat_type != null">REPEAT_TYPE = #{repeat_type}</if>

	    	<if test="play_weight != null">PLAY_WEIGHT = #{play_weight}</if>

	    	<if test="is_independent_play != null">IS_INDEPENDENT_PLAY = #{is_independent_play}</if>

	    	<if test="contiguous != null">CONTIGUOUS = #{contiguous}</if>

	    </set>

		WHERE PLAYLIST_ID = #{playlist_id}

	</update>



	<update id="setPlaylistInfo">

		UPDATE MI_CMS_INFO_PLAYLIST

		<set>

	    	<if test="playlistName != null and playlistName.length() > 0">PLAYLIST_NAME = #{playlistName},</if>

	    	<if test="playlistMetaData != null">PLAYLIST_META_DATA = #{playlistMetaData},</if>

	    	<if test="shareFlag >= 0">SHARE_FLAG = #{shareFlag},</if>

	    	<if test="ignoreTag >= 0">IGNORE_TAG = #{ignoreTag},</if>

	    	<if test="evenessPlayback >= 0">EVENNESS_PLAYBACK = #{evenessPlayback},</if>

	    </set>

	    WHERE PLAYLIST_ID = #{playlistId}

	</update>



	<update id="deletePlaylistChangeStatus">

		UPDATE MI_CMS_INFO_PLAYLIST SET IS_DELETED = 'Y' WHERE PLAYLIST_ID = #{playlistId}

	</update>



	<update id="deletePlaylistChangeGroup">

		UPDATE MI_CMS_MAP_GROUP_PLAYLIST SET GROUP_ID = #{groupId} WHERE PLAYLIST_ID = #{playlistId}

	</update>



	<update id="setPlaylistUnlockBySessionID">

		UPDATE MI_CMS_INFO_PLAYLIST SET SESSION_ID = NULL WHERE SESSION_ID = #{sessionId}

	</update>



	<update id="restorePlaylist">

		UPDATE MI_CMS_INFO_PLAYLIST SET IS_DELETED = 'N' WHERE PLAYLIST_ID = #{playlistId}

	</update>



	<delete id="deletePlaylistCompletely">

		DELETE FROM MI_CMS_INFO_PLAYLIST WHERE PLAYLIST_ID = #{playlistId} AND IS_DELETED = 'Y'

	</delete>

	<delete id="deleteMapSubPlaylist">

		DELETE FROM MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST WHERE CONTENT_ID = #{subPlaylistId} AND IS_SUB_PLAYLIST = <include refid="utils.true"/>

	</delete>

	<select id="getPlaylistListBySubPlaylist" resultType="map">
		SELECT A.PLAYLIST_ID, B.VERSION_ID,B.PLAY_TIME,B.TOTAL_SIZE FROM MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST A, MI_CMS_INFO_PLAYLIST_VERSION B
		WHERE
		A.CONTENT_ID = #{subPlaylistId} AND A.PLAYLIST_ID = B.PLAYLIST_ID AND A.VERSION_ID=B.VERSION_ID AND B.IS_ACTIVE = 'Y' AND A.IS_SUB_PLAYLIST = <include refid="utils.true"/>
	</select>

	<select id="getPlaylistCountBySubPlaylist" resultType="int">
		SELECT
			COUNT(A.PLAYLIST_ID)
			FROM
				MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST A, MI_CMS_INFO_PLAYLIST_VERSION B
				WHERE
					A.CONTENT_ID = #{subPlaylistId}
					AND
					A.PLAYLIST_ID = B.PLAYLIST_ID
					AND
					A.VERSION_ID = B.VERSION_ID
					AND
					B.IS_ACTIVE = 'Y'
					AND
					A.IS_SUB_PLAYLIST = <include refid = "utils.true"/>
	</select>

	<select id="getCountSubPlaylistOfPlaylist" resultType="int">
		SELECT
			COUNT(B.CONTENT_ID)
		FROM
			MI_CMS_INFO_PLAYLIST_VERSION A,
			MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST B
		WHERE
			A.PLAYLIST_ID = #{playlistId}
			AND A.PLAYLIST_ID = B.PLAYLIST_ID
			AND A.VERSION_ID = B.VERSION_ID
			AND A.IS_ACTIVE = 'Y'
			AND B.IS_SUB_PLAYLIST = <include refid="utils.true"/>
	</select>


	<update id="setPlaylistLock">

		UPDATE MI_CMS_INFO_PLAYLIST SET SESSION_ID = #{sessionId}  WHERE PLAYLIST_ID = #{playlistId} AND SESSION_ID IS NULL

	</update>



	<update id="setPlaylistGroup">

		UPDATE MI_CMS_MAP_GROUP_PLAYLIST SET GROUP_ID = #{groupId} WHERE PLAYLIST_ID = #{playlistId}

	</update>



	<update id="setPlaylistShare">

		UPDATE MI_CMS_INFO_PLAYLIST SET SHARE_FLAG = #{shareFlag} WHERE PLAYLIST_ID = #{playlistId}

	</update>



	<update id="setPlaylistMetaData">

		UPDATE MI_CMS_INFO_PLAYLIST SET PLAYLIST_META_DATA = #{metaData} WHERE PLAYLIST_ID = #{playlistId}

	</update>



	<select id="getRootId" resultType="long">

		SELECT GROUP_ID FROM MI_CMS_INFO_PLAYLIST_GROUP WHERE CREATOR_ID = #{userId} AND ORGANIZATION_ID = #{organizationId} AND P_GROUP_ID = #{pGroupId}

	</select>



	<select id="countExistGroupName" resultType="int">

		SELECT COUNT(GROUP_ID) FROM MI_CMS_INFO_PLAYLIST_GROUP WHERE GROUP_NAME = #{groupName}  AND CREATOR_ID = #{userId} AND ORGANIZATION_ID = #{organizationId}

	</select>



	<select id="getGroupId" resultType="long">

		SELECT GROUP_ID FROM MI_CMS_MAP_GROUP_PLAYLIST WHERE PLAYLIST_ID = #{playlistId}

	</select>



	<select id="getGroupName" resultType="string">

		SELECT GROUP_NAME FROM MI_CMS_INFO_PLAYLIST_GROUP WHERE GROUP_ID = #{groupId}

	</select>



	<select id="getGroupInfo" resultType="com.samsung.magicinfo.framework.content.entity.Group">

		SELECT

			GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, CREATOR_ID, CREATE_DATE

		FROM

			MI_CMS_INFO_PLAYLIST_GROUP

		WHERE

			GROUP_ID = #{groupId}

	</select>



	<select id="getGroupList" resultType="com.samsung.magicinfo.framework.content.entity.Group">

		SELECT

			GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, CREATOR_ID, CREATE_DATE

		FROM

			MI_CMS_INFO_PLAYLIST_GROUP

		WHERE

			CREATOR_ID = #{creatorId} AND ORGANIZATION_ID = #{organizationId}

	</select>



	<select id="getGroupListPage" resultType="com.samsung.magicinfo.framework.content.entity.Group">

		SELECT

			GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, CREATOR_ID, CREATE_DATE

		FROM

			MI_CMS_INFO_PLAYLIST_GROUP

		WHERE

			CREATOR_ID = #{creatorId} AND ORGANIZATION_ID = #{organizationId}

		LIMIT #{pageSize} OFFSET #{startPos}

	</select>



	<select id="getGroupListPage" resultType="com.samsung.magicinfo.framework.content.entity.Group" databaseId="mssql">

		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />

        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />

        SELECT * FROM

        (

	        SELECT GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, CREATOR_ID, CREATE_DATE, ROW_NUMBER() OVER(ORDER BY GROUP_ID) as rownum

	        FROM

				MI_CMS_INFO_PLAYLIST_GROUP

			WHERE

				CREATOR_ID = #{creatorId} AND ORGANIZATION_ID = #{organizationId}

	        ) as SubQuery

	    WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit}

	    ORDER BY rownum

	</select>



	<select id="getGroupListCnt" resultType="int">

		SELECT

			COUNT(GROUP_ID)

		FROM

			MI_CMS_INFO_PLAYLIST_GROUP

		WHERE

			CREATOR_ID = #{creatorId} AND ORGANIZATION_ID = #{organizationId}

	</select>



	<insert id="addGroup" parameterType="com.samsung.magicinfo.framework.content.entity.Group">

		INSERT INTO  MI_CMS_INFO_PLAYLIST_GROUP (GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, CREATOR_ID, CREATE_DATE, ORGANIZATION_ID)

		VALUES (#{group_id}, #{p_group_id}, #{group_depth}, #{group_name}, #{creator_id}, CURRENT_TIMESTAMP, #{organization_id})

	</insert>



	<update id="setGroupInfo" parameterType="com.samsung.magicinfo.framework.content.entity.Group">

		UPDATE MI_CMS_INFO_PLAYLIST_GROUP

		<set>

			<if test="p_group_id != null">P_GROUP_ID = #{p_group_id},</if>

			<if test="group_name != null and group_name.length() > 0">GROUP_NAME = #{group_name},</if>

			<if test="group_depth != null">GROUP_DEPTH = #{group_depth},</if>

		</set>

		WHERE GROUP_ID = #{group_id}

	</update>



	<update id="deleteGroup">

		DELETE FROM MI_CMS_INFO_PLAYLIST_GROUP WHERE GROUP_ID = #{groupId}

	</update>



	<select id="countDeletableGroup" resultType="long">

		SELECT COUNT(GROUP_ID) FROM MI_CMS_INFO_PLAYLIST_GROUP WHERE P_GROUP_ID = #{groupId}

	</select>



	<select id="getGroupedPlaylistIdList" resultType="map">

		SELECT

			A.PLAYLIST_ID

		FROM

			MI_CMS_INFO_PLAYLIST A,

			MI_CMS_INFO_PLAYLIST_GROUP B,

			MI_CMS_MAP_GROUP_PLAYLIST C

		WHERE

			B.GROUP_ID = C.GROUP_ID AND C.PLAYLIST_ID = A.PLAYLIST_ID

			AND C.GROUP_ID = #{groupId} AND IS_DELETED ='N'

	</select>



	<select id="getChildGroupList" resultType="com.samsung.magicinfo.framework.content.entity.Group">

		SELECT *

		FROM

			MI_CMS_INFO_PLAYLIST_GROUP

		WHERE

			P_GROUP_ID = #{groupId} AND CREATOR_ID = #{creatorId} AND ORGANIZATION_ID = #{organizationId}

		ORDER BY

			GROUP_NAME ASC

	</select>



	<select id="getChildGroupIdList" resultType="map">

		SELECT

			GROUP_ID

		FROM

			MI_CMS_INFO_PLAYLIST_GROUP

		WHERE

			P_GROUP_ID = #{groupId} AND GROUP_ID != #{pGroupId}

	</select>



	<delete id="deletePlaylistVersionMap">

		DELETE FROM MI_CMS_MAP_PLAYLIST_CONTENT WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}

	</delete>

	<delete id="deleteTagPlaylistVersionMap">

		DELETE FROM MI_CMS_MAP_PLAYLIST_TAG WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}

	</delete>

	<delete id="deleteTagPlaylistVersion">
		DELETE FROM MI_CMS_MAP_PLAYLIST_TAG WHERE PLAYLIST_ID = #{playlistId}
	</delete>

	<delete id="deleteTagPlaylistConditionPerm">
		DELETE FROM MI_CMS_MAP_PLAYLIST_TAG_CONDITION WHERE PLAYLIST_ID = #{playlistId}
	</delete>

	<delete id="deleteTagPlaylistCondition">
		DELETE FROM MI_CMS_MAP_PLAYLIST_TAG_CONDITION WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
	</delete>



	<delete id="deletePlaylistVersion">

		DELETE FROM MI_CMS_INFO_PLAYLIST_VERSION WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId} AND CREATOR_ID = #{userId}

	</delete>



	<select id="getPlaylistEffectList" resultType="com.samsung.magicinfo.framework.content.entity.Effect">

		SELECT * FROM MI_CMS_INFO_PLAYLIST_EFFECT
		
	</select>



	<delete id="deleteContentTag">

		<choose>

			<when test="tagId == 0">

				DELETE FROM MI_TAG_MAP_CONTENT_TAG WHERE PLAYLIST_ID = #{playlistId} AND CONTENT_ID = #{contentId}

			</when>

			<otherwise>

				DELETE FROM MI_TAG_MAP_CONTENT_TAG WHERE PLAYLIST_ID = #{playlistId} AND CONTENT_ID = #{contentId} AND TAG_ID = #{tagId}

			</otherwise>

		</choose>

	</delete>



	<select id="getContentTag" resultType="map">

		SELECT

			TAG_ID, MATCH_TYPE

		FROM

			MI_TAG_MAP_CONTENT_TAG

		WHERE

			CONTENT_ID = #{contentId} AND PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}

	</select>



	<select id="getContentTagOrder" resultType="map">

		SELECT

				A.TAG_ID, A.MATCH_TYPE, B.TAG_VALUE

		FROM

			MI_TAG_MAP_CONTENT_TAG A,
			MI_TAG_INFO_TAG B

		WHERE

			A.TAG_ID = B.TAG_ID AND A.PLAYLIST_ID = #{playlistId} AND A.VERSION_ID = #{versionId} AND A.CONTENT_ID = #{contentId} AND A.CONTENT_ORDER = #{contentOrder}

	</select>

	<select id="getTagPlaylistTagOrder" resultType="map">

		SELECT A.TAG_ID, A.MATCH_TYPE, B.TAG_VALUE
		FROM
			MI_TAG_MAP_PLAYLIST_TAG A,
			MI_TAG_INFO_TAG B
		WHERE
			A.TAG_ID = B.TAG_ID AND A.PLAYLIST_ID = #{playlistId} AND A.VERSION_ID = #{versionId} AND A.PLAYLIST_TAG_ID = #{playlistTagId} AND A.TAG_ORDER = #{tagOrder}

	</select>




	<select id="getPlaylistContentTagList" resultType="com.samsung.magicinfo.framework.setup.entity.ContentTagEntity">

		SELECT

			CONTENT_ID, CONTENT_ORDER, MATCH_TYPE, TAG_ID

		FROM

			MI_TAG_MAP_CONTENT_TAG

		WHERE

			PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}

	</select>

	<sql id="getContentOrderForExpiredContent_from">
		FROM MI_TAG_MAP_CONTENT_TAG
		WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId} AND CONTENT_ID = #{contentId}
	</sql>

	<select id="getContentOrderForExpiredContent" resultType="int">

		SELECT CONTENT_ORDER
		<include refid="getContentOrderForExpiredContent_from"/>
		LIMIT 1

	</select>

	<select id="getContentOrderForExpiredContent" resultType="int" databaseId="mssql">

		SELECT TOP 1 CONTENT_ORDER
		<include refid="getContentOrderForExpiredContent_from"/>

	</select>



	<update id="setContentDuraionByContentID">

		UPDATE MI_CMS_MAP_PLAYLIST_CONTENT SET content_duration = #{contentDuration} WHERE content_id = #{contentId}

	</update>



	<update id="setContentDuraionMilliByContentID">



		UPDATE MI_CMS_MAP_PLAYLIST_CONTENT SET content_duration_milli = #{milli} WHERE content_id = #{contentId}



	</update>

	<select id="getPlaylistIDListByContentID" resultType="string">

		SELECT PLAYLIST_ID FROM MI_CMS_MAP_PLAYLIST_CONTENT WHERE CONTENT_ID = #{contentId}

	</select>



	<update id="setPlaytime">

		UPDATE MI_CMS_INFO_PLAYLIST_VERSION SET PLAY_TIME = #{playTime} WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}

	</update>



	<select id="getSumOfContentDuration" resultType="long">
        <!-- KDH 00179387 동기화재생 플레이리스트를 고려한 컨텐츠 재생시간 합계 -->
		SELECT SUM(CONTENT_DURATION) FROM MI_CMS_MAP_PLAYLIST_CONTENT WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId} AND SYNC_PLAY_ID = '0'

	</select>



	<select id="isDelete" resultType="string">

		SELECT IS_DELETED FROM MI_CMS_INFO_PLAYLIST WHERE PLAYLIST_ID = #{playlistId}

	</select>



	<select id="getCreatorIdByPlaylistId" resultType="string">

		SELECT CREATOR_ID FROM MI_CMS_INFO_PLAYLIST WHERE PLAYLIST_ID = #{playlistId}

	</select>



	<update id="unlockAllSession">

		UPDATE MI_CMS_INFO_PLAYLIST SET SESSION_ID = NULL

	</update>



	<update id="setPlaylistUnlock">

		UPDATE MI_CMS_INFO_PLAYLIST SET SESSION_ID = NULL WHERE PLAYLIST_ID = #{playlistId} AND SESSION_ID = #{sessionId}

	</update>



	<select id="getContentTagInSchedule" resultType="Map">

		SELECT DISTINCT TAG_ID, MATCH_TYPE FROM MI_TAG_MAP_CONTENT_TAG

		WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}

	</select>

	<select id="getVWLEffectInfoByEffectName" resultType="com.samsung.magicinfo.framework.content.entity.Effect">

		SELECT * FROM MI_CMS_INFO_VWL_PLAYLIST_EFFECT

		WHERE EFFECT_NAME = #{effectName}

	</select>

	<delete id="deleteContentFromPlaylist">
		DELETE FROM MI_CMS_MAP_PLAYLIST_CONTENT WHERE CONTENT_ID = #{contentId}
	</delete>

	<select id="getContentOrderListOfPlaylist" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistContent">
	SELECT * FROM MI_CMS_MAP_PLAYLIST_CONTENT WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId} ORDER BY CONTENT_ORDER
	</select>

    <select id="getSubPlaylistContentOrderListOfPlaylist" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistContent">
	SELECT * FROM MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId} ORDER BY CONTENT_ORDER
	</select>

	<update id="updateContentOrder">
		UPDATE MI_CMS_MAP_PLAYLIST_CONTENT SET CONTENT_ORDER = #{newContentOrder} WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId} AND CONTENT_ORDER = #{oldContentOrder}
	</update>

	<update id="updateSubPlaylistContentOrder">
		UPDATE MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST SET CONTENT_ORDER = #{newContentOrder} WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId} AND CONTENT_ORDER = #{oldContentOrder}
	</update>

	<update id="updateContentCount">
		UPDATE MI_CMS_INFO_PLAYLIST_VERSION SET CONTENT_COUNT = #{contentCount} WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
	</update>

	<sql id="getSyncGroupInfoSelect">
		SELECT * FROM MI_CMS_MAP_SYNC_GROUP WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
	</sql>

	<select id="getSyncGroupInfo" resultType="com.samsung.magicinfo.framework.content.entity.SyncPlaylist">
		<include refid="getSyncGroupInfoSelect"/> ORDER BY SYNC_PLAY_ID::INTEGER ASC
	</select>

	<select id="getSyncGroupInfo" resultType="com.samsung.magicinfo.framework.content.entity.SyncPlaylist" databaseId="mssql">
		<include refid="getSyncGroupInfoSelect"/> ORDER BY CONVERT(INT, SYNC_PLAY_ID) ASC
	</select>

	<insert id="addMapSyncGroupInfo">
		INSERT INTO  MI_CMS_MAP_SYNC_GROUP (PLAYLIST_ID, VERSION_ID, SYNC_PLAY_ID, IS_SYNC)
		VALUES (#{playlistId}, #{versionId}, #{syncPlayId}, #{isSync})
	</insert>

	<update id="updateMapSyncGroupInfo">
		UPDATE MI_CMS_MAP_SYNC_GROUP SET IS_SYNC = #{isSync} WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId} AND SYNC_PLAY_ID = #{syncPlayId}
	</update>

	<delete id="deleteMapSyncGroupInfo">
		DELETE FROM MI_CMS_MAP_SYNC_GROUP WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
	</delete>

	<select id="getUsedPlaylistCount" resultType="int">
		SELECT COUNT(DISTINCT PLAYLIST.PLAYLIST_ID)
		FROM MI_CMS_INFO_PLAYLIST PLAYLIST
		LEFT JOIN MI_CDS_INFO_SCHEDULE SCHEDULE ON PLAYLIST.PLAYLIST_ID = SCHEDULE.CONTENT_ID
		WHERE SCHEDULE.CONTENT_ID IS NOT NULL
		<if test="organizationId != 0">
			AND PLAYLIST.ORGANIZATION_ID = #{organizationId}
		</if>
	</select>

	<select id="getThumbFileInfo" resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT FILE_INFO.FILE_ID, FILE_INFO.FILE_NAME
		FROM MI_CMS_INFO_CONTENT_VERSION CONTENT_VERSION, MI_CMS_INFO_FILE FILE_INFO
		WHERE CONTENT_ID = (
            SELECT CONTENT_ID
            FROM (
                SELECT CONTENT_ID, CONTENT_ORDER
                FROM MI_CMS_INFO_PLAYLIST_VERSION VERSIONS
                LEFT JOIN MI_CMS_MAP_PLAYLIST_CONTENT MAPS ON MAPS.PLAYLIST_ID = VERSIONS.PLAYLIST_ID AND MAPS.VERSION_ID = VERSIONS.VERSION_ID
                WHERE VERSIONS.PLAYLIST_ID =  #{contentId} AND SYNC_PLAY_ID = '0' AND VERSIONS.IS_ACTIVE = 'Y'
                UNION
                SELECT
				(SELECT CONTENT_ID
                    FROM MI_CMS_INFO_PLAYLIST_VERSION VERSIONS
                    LEFT JOIN MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST SUBS ON SUBS.PLAYLIST_ID = VERSIONS.PLAYLIST_ID
                    WHERE VERSIONS.PLAYLIST_ID = MAPS.CONTENT_ID AND SYNC_PLAY_ID = '0' AND VERSIONS.IS_ACTIVE = 'Y' AND VERSIONS.VERSION_ID = SUBS.VERSION_ID
                    LIMIT 1
                    ) AS CONTENT_ID, CONTENT_ORDER
                FROM MI_CMS_INFO_PLAYLIST_VERSION VERSIONS
                LEFT JOIN MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST MAPS ON MAPS.PLAYLIST_ID = VERSIONS.PLAYLIST_ID AND MAPS.VERSION_ID = VERSIONS.VERSION_ID
                WHERE VERSIONS.PLAYLIST_ID =  #{contentId} AND SYNC_PLAY_ID = '0' AND VERSIONS.IS_ACTIVE = 'Y'
            ) CONTENTS
            ORDER BY CONTENT_ORDER ASC
            LIMIT 1
		) AND CONTENT_VERSION.IS_ACTIVE = 'Y' AND CONTENT_VERSION.THUMB_FILE_ID = FILE_INFO.FILE_ID
	</select>

	<select id="getThumbFileInfo" resultType="com.samsung.magicinfo.framework.content.entity.ContentFile" databaseId="mssql">
		SELECT FILE_INFO.FILE_ID, FILE_INFO.FILE_NAME
		FROM MI_CMS_INFO_CONTENT_VERSION CONTENT_VERSION, MI_CMS_INFO_FILE FILE_INFO
		WHERE CONTENT_ID = (
            SELECT TOP 1 CONTENT_ID
            FROM (
                SELECT CONTENT_ID, CONTENT_ORDER
                FROM MI_CMS_INFO_PLAYLIST_VERSION VERSIONS
                LEFT JOIN MI_CMS_MAP_PLAYLIST_CONTENT MAPS ON MAPS.PLAYLIST_ID = VERSIONS.PLAYLIST_ID AND MAPS.VERSION_ID = VERSIONS.VERSION_ID
                WHERE VERSIONS.PLAYLIST_ID =  #{contentId} AND SYNC_PLAY_ID = '0' AND VERSIONS.IS_ACTIVE = 'Y'

                UNION

                SELECT
				(SELECT TOP 1 CONTENT_ID
                    FROM MI_CMS_INFO_PLAYLIST_VERSION VERSIONS
                    LEFT JOIN MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST SUBS ON SUBS.PLAYLIST_ID = VERSIONS.PLAYLIST_ID
                    WHERE VERSIONS.PLAYLIST_ID = MAPS.CONTENT_ID AND SYNC_PLAY_ID = '0' AND VERSIONS.IS_ACTIVE = 'Y' AND VERSIONS.VERSION_ID = SUBS.VERSION_ID
                    ) AS CONTENT_ID, CONTENT_ORDER
                FROM MI_CMS_INFO_PLAYLIST_VERSION VERSIONS
                LEFT JOIN MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST MAPS ON MAPS.PLAYLIST_ID = VERSIONS.PLAYLIST_ID AND MAPS.VERSION_ID = VERSIONS.VERSION_ID
                WHERE VERSIONS.PLAYLIST_ID =  #{contentId} AND SYNC_PLAY_ID = '0' AND VERSIONS.IS_ACTIVE = 'Y'
            ) CONTENTS
            ORDER BY CONTENT_ORDER ASC
		) AND CONTENT_VERSION.IS_ACTIVE = 'Y' AND CONTENT_VERSION.THUMB_FILE_ID = FILE_INFO.FILE_ID
	</select>

	<select id="getTagPlaylistThumbFileInfo" resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT FILE_INFO.FILE_ID, FILE_INFO.FILE_NAME
		FROM MI_CMS_INFO_CONTENT_VERSION CONTENT_VERSION, MI_CMS_INFO_FILE FILE_INFO
		WHERE CONTENT_ID = (
			SELECT CONTENT_ID
			FROM MI_CMS_INFO_PLAYLIST
			LEFT JOIN MI_CMS_MAP_PLAYLIST_TAG PLAYLIST ON PLAYLIST.PLAYLIST_ID = MI_CMS_INFO_PLAYLIST.PLAYLIST_ID
			LEFT JOIN MI_TAG_MAP_CONTENT TAGS ON PLAYLIST.TAG_ID = TAGS.TAG_ID,
			MI_CMS_INFO_PLAYLIST_VERSION PLAYLIST_VERSION
			WHERE MI_CMS_INFO_PLAYLIST.PLAYLIST_ID = #{playlistId} AND PLAYLIST.SYNC_PLAY_ID = '0' AND PLAYLIST.TAG_ORDER = '1' AND PLAYLIST_VERSION.IS_ACTIVE = 'Y'
			ORDER BY TAG_ORDER ASC
			LIMIT 1
		) AND CONTENT_VERSION.IS_ACTIVE = 'Y' AND CONTENT_VERSION.THUMB_FILE_ID = FILE_INFO.FILE_ID
	</select>

	<select id="getTagPlaylistThumbFileInfo" resultType="com.samsung.magicinfo.framework.content.entity.ContentFile" databaseId="mssql">
		SELECT FILE_INFO.FILE_ID, FILE_INFO.FILE_NAME
		FROM MI_CMS_INFO_CONTENT_VERSION CONTENT_VERSION, MI_CMS_INFO_FILE FILE_INFO
		WHERE CONTENT_ID = (
			SELECT TOP 1 CONTENT_ID
			FROM MI_CMS_INFO_PLAYLIST
			LEFT JOIN MI_CMS_MAP_PLAYLIST_TAG PLAYLIST ON PLAYLIST.PLAYLIST_ID = MI_CMS_INFO_PLAYLIST.PLAYLIST_ID
			LEFT JOIN MI_TAG_MAP_CONTENT TAGS ON PLAYLIST.TAG_ID = TAGS.TAG_ID,
			MI_CMS_INFO_PLAYLIST_VERSION PLAYLIST_VERSION
			WHERE MI_CMS_INFO_PLAYLIST.PLAYLIST_ID = #{playlistId} AND PLAYLIST.SYNC_PLAY_ID = '0' AND PLAYLIST.TAG_ORDER = '1' AND PLAYLIST_VERSION.IS_ACTIVE = 'Y'
			ORDER BY TAG_ORDER ASC
		) AND CONTENT_VERSION.IS_ACTIVE = 'Y' AND CONTENT_VERSION.THUMB_FILE_ID = FILE_INFO.FILE_ID
	</select>

	<select id="getTagContentListOfPlaylist" resultType="com.samsung.magicinfo.framework.content.entity.Content">
		SELECT CONTENTS.CONTENT_NAME, CONTENT_VERSION.*, FILES.FILE_NAME AS THUMB_FILE_NAME
		FROM
		(
			SELECT CONTENT_ID
			FROM MI_TAG_MAP_CONTENT
			<if test="listMap != null and listMap.size() > 0">
			WHERE
				<foreach collection="listMap" item="obj" separator=" OR " open="(" close=")">
					<choose>
	            		<when test="obj.tag_condition_id != null">
	            			(TAG_ID = #{obj.tag_id} AND TAG_CONDITION_ID = #{obj.tag_condition_id})
	            		</when>
	            		<otherwise>
	            			(TAG_ID = #{obj.tag_id})
	            		</otherwise>
	            	</choose>
				</foreach>
			</if>
		) TAGS
		LEFT JOIN MI_CMS_INFO_CONTENT_VERSION CONTENT_VERSION ON TAGS.CONTENT_ID = CONTENT_VERSION.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_CONTENT CONTENTS ON CONTENT_VERSION.CONTENT_ID = CONTENTS.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_FILE FILES ON CONTENT_VERSION.THUMB_FILE_ID = FILES.FILE_ID
		WHERE IS_ACTIVE = 'Y' AND CONTENTS.APPROVAL_STATUS = 'APPROVED'
		ORDER BY CONTENTS.CONTENT_NAME ASC
	</select>

	<select id="getTagContentListOfPlaylistSize" resultType="int">
		SELECT count(CONTENTS.CONTENT_NAME)
		FROM
		(
			SELECT DISTINCT CONTENT_ID
			FROM MI_TAG_MAP_CONTENT
			<if test="listMap != null and listMap.size() > 0">
			WHERE
				<foreach collection="listMap" item="obj" separator=" OR " open="(" close=")">
					<choose>
	            		<when test="obj.tag_condition_id != null">
	            			(TAG_ID = #{obj.tag_id} AND TAG_CONDITION_ID = #{obj.tag_condition_id})
	            		</when>
	            		<otherwise>
	            			(TAG_ID = #{obj.tag_id})
	            		</otherwise>
	            	</choose>
				</foreach>
			</if>
		) TAGS
		LEFT JOIN MI_CMS_INFO_CONTENT_VERSION CONTENT_VERSION ON TAGS.CONTENT_ID = CONTENT_VERSION.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_CONTENT CONTENTS ON CONTENT_VERSION.CONTENT_ID = CONTENTS.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_FILE FILES ON CONTENT_VERSION.THUMB_FILE_ID = FILES.FILE_ID
		LEFT JOIN MI_TAG_MAP_CONTENT MAP_CONTENT ON TAGS.CONTENT_ID = MAP_CONTENT.CONTENT_ID
		WHERE IS_ACTIVE = 'Y' AND CONTENTS.APPROVAL_STATUS = 'APPROVED'
	</select>

	<select id="getPlaylistTagConditionList" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistTag">
		SELECT MAP.*, CONDITION.TAG_CONDITION FROM MI_CMS_MAP_PLAYLIST_TAG_CONDITION MAP
		LEFT JOIN MI_TAG_INFO_TAG_CONDITION CONDITION ON MAP.TAG_CONDITION_ID = CONDITION.TAG_CONDITION_ID
		WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId} AND TAG_ID = #{tagId}
	</select>

	<select id="getTagConditionList" resultType="map">
		SELECT TAG_LIST.PLAYLIST_ID, TAG_LIST.TAG_ID, TAG_LIST.TAG_CONDITION_ID, TAG_INFO.TAG_TYPE, TAG_LIST.NUMBER_STR FROM (
			SELECT PLAYLIST_ID, TAG_ID, NULL AS TAG_CONDITION_ID, TAG_ORDER, NUMBER_STR FROM MI_CMS_MAP_PLAYLIST_TAG TAG
			WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
			UNION
			SELECT PLAYLIST_ID, TAG_ID, TAG_CONDITION_ID, NULL AS TAG_ORDER, NULL AS NUMBER_STR FROM MI_CMS_MAP_PLAYLIST_TAG_CONDITION CONDITION
			WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
		) TAG_LIST
		LEFT JOIN MI_TAG_INFO_TAG TAG_INFO ON TAG_LIST.TAG_ID = TAG_INFO.TAG_ID
		ORDER BY TAG_LIST.TAG_ORDER
	</select>

	<select id="getTagConditionWithTagIdList" resultType="map">
		SELECT TAG_LIST.PLAYLIST_ID, TAG_LIST.TAG_ID, TAG_LIST.TAG_CONDITION_ID FROM (
			SELECT PLAYLIST_ID, TAG_ID, NULL AS TAG_CONDITION_ID FROM MI_CMS_MAP_PLAYLIST_TAG TAG
			WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId} AND TAG_ID = #{tagId}
			UNION
			SELECT PLAYLIST_ID, TAG_ID, TAG_CONDITION_ID FROM MI_CMS_MAP_PLAYLIST_TAG_CONDITION CONDITION
			WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId} AND TAG_ID = #{tagId}
		) TAG_LIST
	</select>

	<select id="getPlaylistIdfromCategory" resultType="java.util.Map">
		SELECT DISTINCT ON (PLAYLIST_ID) PLAYLIST_ID FROM MI_CATEGORY_MAP_PLAYLIST
		WHERE
			<foreach item="item" index="index" collection="categoryList" open="" separator=" OR " close="">
            	GROUP_ID = #{item} :: Integer
            </foreach>
	</select>

	<select id="getPlaylistIdfromCategory" resultType="java.util.Map" databaseId="mssql">
		SELECT DISTINCT PLAYLIST_ID FROM MI_CATEGORY_MAP_PLAYLIST
		WHERE
			<foreach item="item" index="index" collection="categoryList" open="" separator=" OR " close="">
            	GROUP_ID = #{item}
            </foreach>
	</select>

	<select id="checkExistTagCondition" resultType="int">
		SELECT COUNT(PLAYLIST_ID) FROM MI_CMS_MAP_PLAYLIST_TAG_CONDITION WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId} AND TAG_ID = #{tagId} AND TAG_CONDITION_ID = #{conditionId}
	</select>

	<select id="checkTagPlaylist" resultType="int">
		SELECT COUNT(PLAYLIST_ID) FROM MI_CMS_INFO_PLAYLIST WHERE PLAYLIST_ID = #{playlistId} AND PLAYLIST_TYPE = #{playlistType}
	</select>

	<select id="getCntContentAtTagPlaylist" resultType="map">
		SELECT COUNT(DISTINCT TAG.CONTENT_ID), SUM(DISTINCT VERSION.TOTAL_SIZE) AS TOTAL_SIZE
		FROM MI_TAG_MAP_CONTENT TAG
		LEFT JOIN MI_CMS_INFO_CONTENT_VERSION VERSION ON TAG.CONTENT_ID = VERSION.CONTENT_ID
		WHERE
		VERSION.IS_ACTIVE = 'Y'
		AND TAG_ID = #{tagId}
		<if test="conditionIds != null and conditionIds.length > 0">
			<foreach item="item" index="index" collection="conditionIds" open="AND ( " separator=" OR " close=")">
				<choose>
	            	<when test="item.equals('NOT_ASSIGN')">
	            		TAG_CONDITION_ID = -1
	            	</when>
	            	<otherwise>
	            		TAG_CONDITION_ID = #{item} :: int
	            	</otherwise>
	            </choose>
			</foreach>
		</if>
	</select>

	<select id="getCntContentAtTagPlaylist" resultType="map" databaseId="mssql">
		SELECT COUNT(DISTINCT TAG.CONTENT_ID) AS COUNT, SUM(DISTINCT VERSION.TOTAL_SIZE) AS TOTAL_SIZE
		FROM MI_TAG_MAP_CONTENT TAG
		LEFT JOIN MI_CMS_INFO_CONTENT_VERSION VERSION ON TAG.CONTENT_ID = VERSION.CONTENT_ID
		WHERE
		VERSION.IS_ACTIVE = 'Y'
		AND TAG_ID = #{tagId}
		<if test="conditionIds != null and conditionIds.length > 0">
			<foreach item="item" index="index" collection="conditionIds" open="AND ( " separator=" OR " close=")">
				<choose>
	            	<when test="item.equals('NOT_ASSIGN')">
	            		TAG_CONDITION_ID = -1
	            	</when>
	            	<otherwise>
	            		TAG_CONDITION_ID = #{item}
	            	</otherwise>
	            </choose>
			</foreach>
		</if>
	</select>

	<select id="getThumbContentAtTagPlaylist" resultType="map">
		SELECT FILE_ID, FILE_NAME, CONTENT.EXPIRATION_DATE, CONTENT.CONTENT_ID, CONTENT.CONTENT_NAME, CONTENT.LAST_MODIFIED_DATE,
		VERSION.CREATOR_ID, VERSION.MEDIA_TYPE, VERSION.PLAY_TIME FROM MI_TAG_MAP_CONTENT TAG
		LEFT JOIN MI_CMS_INFO_CONTENT CONTENT ON CONTENT.CONTENT_ID = TAG.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_CONTENT_VERSION VERSION ON CONTENT.CONTENT_ID = VERSION.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_FILE FILE ON THUMB_FILE_ID = FILE.FILE_ID
		WHERE TAG_ID = #{tagId}
		<if test="conditionIds != null and conditionIds.length > 0">
			<foreach item="item" index="index" collection="conditionIds" open="AND ( " separator=" OR " close=")">
				<choose>
	            	<when test="item.equals('NOT_ASSIGN')">
	            		TAG_CONDITION_ID = -1
	            	</when>
	            	<otherwise>
	            		TAG_CONDITION_ID = #{item} :: int
	            	</otherwise>
	            </choose>
			</foreach>
		</if>
		ORDER BY CONTENT.CONTENT_NAME ASC
		LIMIT 1
	</select>

	<select id="getAllThumbContentAtTagPlaylist" resultType="map">
		SELECT FILE_ID, FILE_NAME, CONTENT.EXPIRATION_DATE, CONTENT.CONTENT_ID, CONTENT.CONTENT_NAME, CONTENT.LAST_MODIFIED_DATE,
		VERSION.CREATOR_ID, VERSION.MEDIA_TYPE, VERSION.PLAY_TIME FROM MI_TAG_MAP_CONTENT TAG
		LEFT JOIN MI_CMS_INFO_CONTENT CONTENT ON CONTENT.CONTENT_ID = TAG.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_CONTENT_VERSION VERSION ON CONTENT.CONTENT_ID = VERSION.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_FILE FILE ON THUMB_FILE_ID = FILE.FILE_ID
		WHERE TAG_ID = #{tagId} AND VERSION.IS_ACTIVE = 'Y'
		<if test="conditionIds != null and conditionIds.length > 0">
			<foreach item="item" index="index" collection="conditionIds" open="AND ( " separator=" OR " close=")">
				<choose>
					<when test="item.equals('NOT_ASSIGN')">
						TAG_CONDITION_ID = -1
					</when>
					<otherwise>
						TAG_CONDITION_ID = #{item} :: int
					</otherwise>
				</choose>
			</foreach>
		</if>
		ORDER BY CONTENT.CONTENT_NAME ASC
	</select>

	<select id="getThumbContentAtTagPlaylistAll" resultType="map">
		SELECT DISTINCT (FILE_ID), FILE_NAME, CONTENT.CONTENT_ID, CONTENT.CONTENT_NAME,VERSION.MEDIA_TYPE, VERSION.RESOLUTION, VERSION.VERSION_ID, VERSION.CREATOR_ID, VERSION.CREATE_DATE
		FROM MI_TAG_MAP_CONTENT TAG
		LEFT JOIN MI_CMS_INFO_CONTENT CONTENT ON CONTENT.CONTENT_ID = TAG.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_CONTENT_VERSION VERSION ON CONTENT.CONTENT_ID = VERSION.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_FILE FILE ON THUMB_FILE_ID = FILE.FILE_ID
		WHERE TAG_ID = #{tagId} AND VERSION.IS_ACTIVE = 'Y' AND CONTENT.APPROVAL_STATUS = 'APPROVED'
		<if test="conditionIds != null and conditionIds.length > 0">
			<foreach item="item" index="index" collection="conditionIds" open="AND ( " separator=" OR " close=")">
				<choose>
	            	<when test="item.equals('NOT_ASSIGN')">
	            		TAG_CONDITION_ID = -1
	            	</when>
	            	<otherwise>
	            		TAG_CONDITION_ID = #{item} :: int
	            	</otherwise>
	            </choose>
			</foreach>
		</if>
		ORDER BY CONTENT.CONTENT_NAME ASC
	</select>

	<select id="getThumbContentAtTagPlaylistAll" resultType="map" databaseId="mssql">
		SELECT DISTINCT (FILE_ID), FILE_NAME, CONTENT.CONTENT_ID, CONTENT.CONTENT_NAME,VERSION.MEDIA_TYPE, VERSION.RESOLUTION, VERSION.VERSION_ID, VERSION.CREATOR_ID, VERSION.CREATE_DATE
		FROM MI_TAG_MAP_CONTENT TAG
		LEFT JOIN MI_CMS_INFO_CONTENT CONTENT ON CONTENT.CONTENT_ID = TAG.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_CONTENT_VERSION VERSION ON CONTENT.CONTENT_ID = VERSION.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_FILE FILES ON THUMB_FILE_ID = FILES.FILE_ID
		WHERE TAG_ID = #{tagId} AND VERSION.IS_ACTIVE = 'Y' AND CONTENT.APPROVAL_STATUS = 'APPROVED'
		<if test="conditionIds != null and conditionIds.length > 0">
			<foreach item="item" index="index" collection="conditionIds" open="AND ( " separator=" OR " close=")">
				<choose>
	            	<when test="item.equals('NOT_ASSIGN')">
	            		TAG_CONDITION_ID = -1
	            	</when>
	            	<otherwise>
	            		TAG_CONDITION_ID = #{item}
	            	</otherwise>
	            </choose>
			</foreach>
		</if>
		ORDER BY CONTENT.CONTENT_NAME ASC
	</select>

	<select id="getThumbContentAtTagPlaylist" resultType="map" databaseId="mssql">
		SELECT TOP 1 FILE_ID, FILE_NAME, CONTENT.EXPIRATION_DATE, CONTENT.CONTENT_ID, CONTENT.CONTENT_NAME, VERSION.CREATOR_ID,
		VERSION.MEDIA_TYPE, VERSION.PLAY_TIME FROM MI_TAG_MAP_CONTENT TAG
		LEFT JOIN MI_CMS_INFO_CONTENT CONTENT ON TAG.CONTENT_ID = CONTENT.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_CONTENT_VERSION VERSION ON CONTENT.CONTENT_ID = VERSION.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_FILE FILES ON THUMB_FILE_ID = FILES.FILE_ID
		WHERE TAG_ID = #{tagId}
		<if test="conditionIds != null and conditionIds.length > 0">
			<foreach item="item" index="index" collection="conditionIds" open="AND ( " separator=" OR " close=")">
				<choose>
	            	<when test="item.equals('NOT_ASSIGN')">
	            		TAG_CONDITION_ID = -1
	            	</when>
	            	<otherwise>
	            		TAG_CONDITION_ID = #{item}
	            	</otherwise>
	            </choose>
			</foreach>
		</if>
		ORDER BY CONTENT.CONTENT_NAME ASC
	</select>

	<select id="getAllThumbContentAtTagPlaylist" resultType="map" databaseId="mssql">
		SELECT FILE_ID, FILE_NAME, CONTENT.EXPIRATION_DATE, CONTENT.CONTENT_ID, CONTENT.CONTENT_NAME, VERSION.CREATOR_ID,
		VERSION.MEDIA_TYPE, VERSION.PLAY_TIME FROM MI_TAG_MAP_CONTENT TAG
		LEFT JOIN MI_CMS_INFO_CONTENT CONTENT ON TAG.CONTENT_ID = CONTENT.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_CONTENT_VERSION VERSION ON CONTENT.CONTENT_ID = VERSION.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_FILE FILES ON THUMB_FILE_ID = FILES.FILE_ID
		WHERE TAG_ID = #{tagId} AND VERSION.IS_ACTIVE = 'Y'
		<if test="conditionIds != null and conditionIds.length > 0">
			<foreach item="item" index="index" collection="conditionIds" open="AND ( " separator=" OR " close=")">
				<choose>
					<when test="item.equals('NOT_ASSIGN')">
						TAG_CONDITION_ID = -1
					</when>
					<otherwise>
						TAG_CONDITION_ID = #{item}
					</otherwise>
				</choose>
			</foreach>
		</if>
		ORDER BY CONTENT.CONTENT_NAME ASC
	</select>

	<select id="getTagPlaylistTagList" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistContent">
		SELECT * FROM MI_CMS_MAP_PLAYLIST_TAG
		WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
	</select>

	<select id="getTagPlaylistTagConditionList" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistTagCondition">
		SELECT *
		FROM MI_CMS_MAP_PLAYLIST_TAG_CONDITION
		WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
	</select>

	<select id="getConditionIdWithTagNumber" resultType="map">
		SELECT DISTINCT CONTENT.TAG_CONDITION_ID
		FROM MI_TAG_MAP_CONTENT CONTENT
		LEFT JOIN MI_TAG_INFO_TAG_CONDITION TAG_CONDITION ON CONTENT.TAG_CONDITION_ID = TAG_CONDITION.TAG_CONDITION_ID
		WHERE CONTENT.TAG_ID = #{tagId}
		<if test="(tagConditionEqual != null and tagConditionEqual.length > 0) or (tagConditionUp != null and tagConditionUp.length > 0) or (tagConditionDown != null and tagConditionDown.length > 0)">
			 and (
			<choose>
				<when test="(tagConditionUp != null and tagConditionUp.length > 0) or (tagConditionDown != null and tagConditionDown.length > 0)">
					<if test="tagConditionUp != null and tagConditionUp.length > 0">
		        		<foreach item="item" index="index" collection="tagConditionUp" open=" (" separator=" OR " close=")">
		            		CAST(coalesce(TAG_CONDITION, '0') AS INTEGER) <![CDATA[<=]]> #{item}::INTEGER
		        		</foreach>
			        </if>
			        <if test="tagConditionDown != null and tagConditionDown.length > 0">
			        	<foreach item="item" index="index" collection="tagConditionDown" open=" AND (" separator=" OR " close=")">
			            	CAST(coalesce(TAG_CONDITION, '0') AS INTEGER) <![CDATA[>=]]> #{item}::INTEGER
			        	</foreach>
		        	</if>
		        	<if test="tagConditionEqual != null and tagConditionEqual.length > 0">
						<foreach item="item" index="index" collection="tagConditionEqual" open=" OR (" separator=" OR " close=")">
			            	TAG_CONDITION = #{item}
			        	</foreach>
					</if>
				</when>
				<otherwise>
					<if test="tagConditionEqual != null and tagConditionEqual.length > 0">
						<foreach item="item" index="index" collection="tagConditionEqual" open=" (" separator=" OR " close=")">
			           		TAG_CONDITION = #{item}
			       		</foreach>
			       	</if>
				</otherwise>
			</choose>
			)
		</if>
		ORDER BY CONTENT.TAG_CONDITION_ID
	</select>

	<select id="getConditionIdWithTagNumber" resultType="map" databaseId="mssql">
		SELECT DISTINCT CONTENT.TAG_CONDITION_ID
		FROM MI_TAG_MAP_CONTENT CONTENT
		LEFT JOIN MI_TAG_INFO_TAG_CONDITION TAG_CONDITION ON CONTENT.TAG_CONDITION_ID = TAG_CONDITION.TAG_CONDITION_ID
		WHERE CONTENT.TAG_ID = #{tagId}
		<if test="(tagConditionEqual != null and tagConditionEqual.length > 0) or (tagConditionUp != null and tagConditionUp.length > 0) or (tagConditionDown != null and tagConditionDown.length > 0)">
			 and (
			<choose>
				<when test="(tagConditionUp != null and tagConditionUp.length > 0) or (tagConditionDown != null and tagConditionDown.length > 0)">
					<if test="tagConditionUp != null and tagConditionUp.length > 0">
		        		<foreach item="item" index="index" collection="tagConditionUp" open=" (" separator=" OR " close=")">
		            		CAST(TAG_CONDITION AS INTEGER) <![CDATA[<=]]> #{item}
		        		</foreach>
			        </if>
			        <if test="tagConditionDown != null and tagConditionDown.length > 0">
			        	<foreach item="item" index="index" collection="tagConditionDown" open=" AND (" separator=" OR " close=")">
			            	CAST(TAG_CONDITION AS INTEGER) <![CDATA[>=]]> #{item}
			        	</foreach>
		        	</if>
		        	<if test="tagConditionEqual != null and tagConditionEqual.length > 0">
						<foreach item="item" index="index" collection="tagConditionEqual" open=" OR (" separator=" OR " close=")">
			            	TAG_CONDITION = #{item}
			        	</foreach>
					</if>
				</when>
				<otherwise>
					<if test="tagConditionEqual != null and tagConditionEqual.length > 0">
						<foreach item="item" index="index" collection="tagConditionEqual" open=" (" separator=" OR " close=")">
			           		TAG_CONDITION = #{item}
			       		</foreach>
			       	</if>
				</otherwise>
			</choose>
			)
		</if>
		ORDER BY CONTENT.TAG_CONDITION_ID
	</select>

	<select id="getContentListFromTagId" resultType="map">
		SELECT DISTINCT CONTENT_ID FROM MI_TAG_MAP_CONTENT
		<if test="listMap != null and listMap.size() > 0">
			WHERE
			<foreach collection="listMap" item="obj" separator=" OR " open="(" close=")">
				<choose>
	           		<when test="obj.tag_condition_id != null">
	           			TAG_ID = #{obj.tag_id} AND TAG_CONDITION_ID = #{obj.tag_condition_id}
	           		</when>
	           		<otherwise>
	           			TAG_ID = #{obj.tag_id}
	           		</otherwise>
	           	</choose>
			</foreach>
		</if>
	</select>

	<select id="getListLinkedPlaylistProgramId" resultType="String">
		SELECT DISTINCT(A.PROGRAM_ID)
        FROM MI_CDS_INFO_PROGRAM A JOIN (SELECT PROGRAM_ID, CONTENT_ID, CONTENT_TYPE FROM MI_CDS_INFO_SCHEDULE UNION SELECT PROGRAM_ID, CONTENT_ID, CONTENT_TYPE FROM MI_CDS_INFO_ADSCHEDULE) B ON A.PROGRAM_ID = B.PROGRAM_ID
        WHERE B.CONTENT_TYPE = 'PLAYLIST' AND B.CONTENT_ID IN (
			SELECT DISTINCT SUBS.PLAYLIST_ID
			FROM MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST SUBS, MI_CMS_INFO_PLAYLIST_VERSION VERSIONS
			WHERE
			SUBS.CONTENT_ID = #{playlistId} AND VERSIONS.PLAYLIST_ID = SUBS.PLAYLIST_ID AND VERSIONS.VERSION_ID = SUBS.VERSION_ID
			AND VERSIONS.IS_ACTIVE = 'Y'
        )
	</select>
	
	<select id="getListLinkedPlaylistPlaylistId" resultType="String">
		SELECT DISTINCT SUBS.PLAYLIST_ID
			FROM MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST SUBS, MI_CMS_INFO_PLAYLIST_VERSION VERSIONS
		WHERE
			SUBS.CONTENT_ID = #{playlistId} AND VERSIONS.PLAYLIST_ID = SUBS.PLAYLIST_ID AND VERSIONS.VERSION_ID = SUBS.VERSION_ID
			AND VERSIONS.IS_ACTIVE = 'Y'
	</select>
	
	<select id="getContentTagListWithPlaylistId" resultType="com.samsung.magicinfo.framework.content.entity.PlaylistContentTag">
		SELECT TAGS.*
		FROM MI_CMS_INFO_PLAYLIST_VERSION PLAYLISTS, MI_TAG_MAP_CONTENT_TAG TAGS
		WHERE PLAYLISTS.PLAYLIST_ID = #{playlistId} AND PLAYLISTS.IS_ACTIVE = 'Y' AND TAGS.PLAYLIST_ID = PLAYLISTS.PLAYLIST_ID AND TAGS.VERSION_ID = PLAYLISTS.VERSION_ID
		ORDER BY TAGS.CONTENT_ID
	</select>

	<select id="getContentListWithTag" resultType="String">
		SELECT TAGS.CONTENT_ID FROM MI_TAG_MAP_CONTENT_TAG TAGS
		WHERE CONTENT_ID = #{contentId} AND PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId} AND CONTENT_ORDER = #{contentOrder} AND MATCH_TYPE = 'and'
		<if test="tagList != null and tagList.size() > 0">
			AND
			<foreach collection="tagList" item="obj" separator=" AND ">
				TAG_ID != #{obj}
			</foreach>
		</if>
	</select>

	<select id="getOrganizationByPlaylistId" resultType="String">
		SELECT ORGANIZATION
		FROM MI_USER_INFO_USER
		WHERE ROOT_GROUP_ID = (
			SELECT ORGANIZATION_ID FROM MI_CMS_INFO_PLAYLIST_GROUP WHERE GROUP_ID = (
				SELECT GROUP_ID FROM MI_CMS_MAP_GROUP_PLAYLIST WHERE PLAYLIST_ID = #{playlistId}
			)
		) LIMIT 1
	</select>

	<select id="getOrganizationByPlaylistId" resultType="String" databaseId="mssql">
		SELECT TOP 1 ORGANIZATION
		FROM MI_USER_INFO_USER
		WHERE ROOT_GROUP_ID = (
			SELECT ORGANIZATION_ID FROM MI_CMS_INFO_PLAYLIST_GROUP WHERE GROUP_ID = (
				SELECT GROUP_ID FROM MI_CMS_MAP_GROUP_PLAYLIST WHERE PLAYLIST_ID = #{playlistId}
			)
		)
	</select>

	<select id="getAllPlaylistGroups" resultType="com.samsung.magicinfo.framework.content.entity.Group">
		WITH RECURSIVE B AS (
    		SELECT *
    		FROM MI_CMS_INFO_PLAYLIST_GROUP
    		WHERE GROUP_ID = #{groupId}
    		UNION ALL
    		SELECT CHILD_GROUP.*
    		FROM MI_CMS_INFO_PLAYLIST_GROUP CHILD_GROUP
    		JOIN B ON CHILD_GROUP.P_GROUP_ID = B.GROUP_ID
    	)
    	SELECT GROUP_ID
    	FROM B
    	ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC
	</select>

	<select id="getAllPlaylistGroups" resultType="com.samsung.magicinfo.framework.content.entity.Group" databaseId="mssql">
		WITH B AS (
    		SELECT *
    		FROM MI_CMS_INFO_PLAYLIST_GROUP
    		WHERE GROUP_ID = #{groupId}
    		UNION ALL
    		SELECT CHILD_GROUP.*
    		FROM MI_CMS_INFO_PLAYLIST_GROUP CHILD_GROUP
    		JOIN B ON CHILD_GROUP.P_GROUP_ID = B.GROUP_ID
    	)
    	SELECT GROUP_ID
    	FROM B
    	ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC
	</select>

	<update id="setHasSubPlaylist">
		UPDATE MI_CMS_INFO_PLAYLIST_VERSION SET HAS_SUB_PLAYLIST = #{hasSubPlaylist} WHERE PLAYLIST_ID = #{playlistId} AND VERSION_ID = #{versionId}
	</update>
	
	<select id="getCountPlaylistToExpire" resultType="Integer">
		SELECT COUNT(PL.PLAYLIST_ID)
		<include refid="getCountPlaylistToExpire_from"/>
	</select>
	
	<select id="getCountPlaylistToExpire" resultType="Integer" databaseId="mssql">
		<include refid="groupRecursiveQuery"/>
		SELECT COUNT(PL.PLAYLIST_ID)
		<include refid="getCountPlaylistToExpire_from"/>
	</select>
	
	<select id="getListPlaylistToExpire" resultType="com.samsung.magicinfo.framework.content.entity.Playlist">
		SELECT PL.*
		<include refid="getCountPlaylistToExpire_from"/>
        <if test="condition != null">
            <if test="condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
                <bind name="safe_sortUpper"	value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
                <bind name="safe_sortOrder"	value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
                ORDER BY PL.${safe_sortUpper} ${safe_sortOrder}
            </if>
        </if>
		<if test="pageSize > -1">
        	LIMIT #{pageSize}
        </if>
        <if test="startPos > -1">
        	OFFSET #{startPos}
        </if>
	</select>
	
	<select id="getListPlaylistToExpire" resultType="com.samsung.magicinfo.framework.content.entity.Playlist" databaseId="mssql">
		<include refid="groupRecursiveQuery"/>
		SELECT * FROM (
			<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
	        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
	        <bind name="safe_sortUpper"	value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
			<bind name="safe_sortOrder"	value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
			SELECT PL.*, ROW_NUMBER() OVER(ORDER BY PL.${safe_sortUpper} ${safe_sortOrder}) as RowNum
			<include refid="getCountPlaylistToExpire_from"/>
		) as subQuery
		WHERE 
            1 = 1
            <if test="safe_startPos > -1">
            and RowNum > ${safe_startPos}
            </if>
            <if test="safe_rownumLimit > -1">
            and RowNum &lt;= ${safe_rownumLimit}
            </if>        
        ORDER BY RowNum
	</select>

	<sql id="getCountPlaylistToExpire_from">
		FROM (
			SELECT PLAYLISTS.*, VERSIONS.VERSION_ID, VERSIONS.PLAY_TIME, VERSIONS.TOTAL_SIZE
			FROM
			(
				SELECT DISTINCT SCHEDULES.CONTENT_ID AS PLAYLIST_ID
				FROM MI_CDS_MAP_PROGRAM_GROUP MAP_PROGRAM
				INNER JOIN MI_CDS_INFO_PROGRAM PROGRAMS ON MAP_PROGRAM.PROGRAM_ID = PROGRAMS.PROGRAM_ID
				INNER JOIN MI_CDS_INFO_SCHEDULE SCHEDULES ON PROGRAMS.PROGRAM_ID = SCHEDULES.PROGRAM_ID
				WHERE EXISTS(
					<include refid="groupRecursiveQuery"/>
					SELECT GROUP_ID
					FROM B 
					WHERE MAP_PROGRAM.GROUP_ID = B.GROUP_ID
				)
				AND PROGRAMS.IS_DEFAULT = 'N' AND PROGRAMS.DELETED = 'N' AND SCHEDULES.CONTENT_TYPE = 'PLAYLIST'
			) A
			INNER JOIN MI_CMS_INFO_PLAYLIST_VERSION VERSIONS ON A.PLAYLIST_ID = VERSIONS.PLAYLIST_ID
			INNER JOIN MI_CMS_MAP_PLAYLIST_CONTENT CONTENTS ON VERSIONS.PLAYLIST_ID = CONTENTS.PLAYLIST_ID AND VERSIONS.VERSION_ID = CONTENTS.VERSION_ID
			INNER JOIN MI_CMS_INFO_PLAYLIST PLAYLISTS ON VERSIONS.PLAYLIST_ID = PLAYLISTS.PLAYLIST_ID
			WHERE VERSIONS.IS_ACTIVE = 'Y' AND (CONTENTS.EXPIRED_DATE IS NOT NULL AND CONTENTS.EXPIRED_DATE &lt;= CAST(#{stopDate} AS DATE))
			<if test="condition != null">
				<if test="condition.src_name != null and condition.src_name != ''">
		            <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
		            <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
	            	AND UPPER(PLAYLISTS.PLAYLIST_NAME) 
	            	LIKE #{srcNamePattern} 
	        	</if>
        	</if>
			UNION
			SELECT PLAYLISTS.*, VERSIONS.VERSION_ID, VERSIONS.PLAY_TIME, VERSIONS.TOTAL_SIZE
			FROM
			(
				SELECT DISTINCT SCHEDULES.CONTENT_ID AS PLAYLIST_ID
				FROM MI_CDS_MAP_PROGRAM_GROUP MAP_PROGRAM
				INNER JOIN MI_CDS_INFO_PROGRAM PROGRAMS ON MAP_PROGRAM.PROGRAM_ID = PROGRAMS.PROGRAM_ID
				INNER JOIN MI_CDS_INFO_SCHEDULE SCHEDULES ON PROGRAMS.PROGRAM_ID = SCHEDULES.PROGRAM_ID
				WHERE EXISTS(
					<include refid="groupRecursiveQuery"/>
					SELECT GROUP_ID
					FROM B 
					WHERE MAP_PROGRAM.GROUP_ID = B.GROUP_ID
				)
				AND PROGRAMS.IS_DEFAULT = 'N' AND PROGRAMS.DELETED = 'N' AND SCHEDULES.CONTENT_TYPE = 'PLAYLIST'
			) A
			INNER JOIN MI_CMS_INFO_PLAYLIST_VERSION VERSIONS ON A.PLAYLIST_ID = VERSIONS.PLAYLIST_ID
			INNER JOIN MI_CMS_MAP_PLAYLIST_TAG CONTENTS ON VERSIONS.PLAYLIST_ID = CONTENTS.PLAYLIST_ID AND VERSIONS.VERSION_ID = CONTENTS.VERSION_ID
			INNER JOIN MI_CMS_INFO_PLAYLIST PLAYLISTS ON VERSIONS.PLAYLIST_ID = PLAYLISTS.PLAYLIST_ID
			WHERE VERSIONS.IS_ACTIVE = 'Y' AND (CONTENTS.EXPIRED_DATE IS NOT NULL AND CONTENTS.EXPIRED_DATE &lt;= CAST(#{stopDate} AS DATE))
			<if test="condition != null">
				<if test="condition.src_name != null and condition.src_name != ''">
		            <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
		            <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
	            	AND UPPER(PLAYLISTS.PLAYLIST_NAME) 
	            	LIKE #{srcNamePattern} 
	        	</if>
        	</if>
		) PL
	</sql>

	<sql id="getCountPlaylistToExpire_from" databaseId="mssql">
		FROM (
			SELECT DISTINCT PLAYLISTS.*, VERSIONS.VERSION_ID, VERSIONS.PLAY_TIME, VERSIONS.TOTAL_SIZE
			FROM B
			INNER JOIN MI_CDS_MAP_PROGRAM_GROUP MAP_PROGRAM ON B.GROUP_ID = MAP_PROGRAM.GROUP_ID
			INNER JOIN MI_CDS_INFO_PROGRAM PROGRAMS ON MAP_PROGRAM.PROGRAM_ID = PROGRAMS.PROGRAM_ID
			INNER JOIN MI_CDS_INFO_SCHEDULE SCHEDULES ON PROGRAMS.PROGRAM_ID = SCHEDULES.PROGRAM_ID
			INNER JOIN MI_CMS_INFO_PLAYLIST_VERSION VERSIONS ON SCHEDULES.CONTENT_ID = VERSIONS.PLAYLIST_ID
			INNER JOIN MI_CMS_MAP_PLAYLIST_CONTENT CONTENTS ON VERSIONS.PLAYLIST_ID = CONTENTS.PLAYLIST_ID AND VERSIONS.VERSION_ID = CONTENTS.VERSION_ID
			INNER JOIN MI_CMS_INFO_PLAYLIST PLAYLISTS ON VERSIONS.PLAYLIST_ID = PLAYLISTS.PLAYLIST_ID
			WHERE PROGRAMS.IS_DEFAULT = 'N' AND PROGRAMS.IS_DEFAULT = 'N' AND SCHEDULES.CONTENT_TYPE = 'PLAYLIST' AND
			VERSIONS.IS_ACTIVE = 'Y'
			AND (CONTENTS.EXPIRED_DATE IS NOT NULL AND CONTENTS.EXPIRED_DATE &lt;= CAST(#{stopDate} AS DATE))
			<if test="condition != null">
				<if test="condition.src_name != null and condition.src_name != ''">
		            <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
		            <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
	            	AND UPPER(PLAYLISTS.PLAYLIST_NAME) 
	            	LIKE #{srcNamePattern} 
	        	</if>
        	</if>
			UNION
			SELECT DISTINCT PLAYLISTS.*, VERSIONS.VERSION_ID, VERSIONS.PLAY_TIME, VERSIONS.TOTAL_SIZE
			FROM B
			INNER JOIN MI_CDS_MAP_PROGRAM_GROUP MAP_PROGRAM ON B.GROUP_ID = MAP_PROGRAM.GROUP_ID
			INNER JOIN MI_CDS_INFO_PROGRAM PROGRAMS ON MAP_PROGRAM.PROGRAM_ID = PROGRAMS.PROGRAM_ID
			INNER JOIN MI_CDS_INFO_SCHEDULE SCHEDULES ON PROGRAMS.PROGRAM_ID = SCHEDULES.PROGRAM_ID
			INNER JOIN MI_CMS_INFO_PLAYLIST_VERSION VERSIONS ON SCHEDULES.CONTENT_ID = VERSIONS.PLAYLIST_ID
			INNER JOIN MI_CMS_MAP_PLAYLIST_TAG CONTENTS ON VERSIONS.PLAYLIST_ID = CONTENTS.PLAYLIST_ID AND VERSIONS.VERSION_ID = CONTENTS.VERSION_ID
			INNER JOIN MI_CMS_INFO_PLAYLIST PLAYLISTS ON VERSIONS.PLAYLIST_ID = PLAYLISTS.PLAYLIST_ID
			WHERE PROGRAMS.IS_DEFAULT = 'N' AND PROGRAMS.IS_DEFAULT = 'N' AND SCHEDULES.CONTENT_TYPE = 'PLAYLIST' AND
			VERSIONS.IS_ACTIVE = 'Y'
			AND (CONTENTS.EXPIRED_DATE IS NOT NULL AND CONTENTS.EXPIRED_DATE &lt;= CAST(#{stopDate} AS DATE))
			<if test="condition != null">
				<if test="condition.src_name != null and condition.src_name != ''">
		            <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
		            <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
	            	AND UPPER(PLAYLISTS.PLAYLIST_NAME) 
	            	LIKE #{srcNamePattern} 
	        	</if>
        	</if>
		) PL
	</sql>

	<sql id="groupRecursiveQuery">
		WITH RECURSIVE B AS (
		SELECT GROUP_ID
		FROM MI_CDS_INFO_PROGRAM_GROUP
		WHERE
		<choose>
			<when test="groupList != null">
				<foreach item="group" collection="groupList" open="(" separator=" OR " close=")">
					GROUP_ID = #{group.group_id}
				</foreach>
			</when>
			<otherwise>
				GROUP_ID = 999999
			</otherwise>
		</choose>
		UNION ALL
		SELECT CHILD_GROUP.GROUP_ID
		FROM MI_CDS_INFO_PROGRAM_GROUP CHILD_GROUP
		JOIN B ON CHILD_GROUP.P_GROUP_ID = B.GROUP_ID
		)
	</sql>

	<sql id="groupRecursiveQuery" databaseId="mssql">
		WITH B AS (
		SELECT GROUP_ID
		FROM MI_CDS_INFO_PROGRAM_GROUP
		WHERE
		<choose>
			<when test="groupList != null">
				<foreach item="group" collection="groupList" open="(" separator=" OR " close=")">
					GROUP_ID = #{group.group_id}
				</foreach>
			</when>
			<otherwise>
				GROUP_ID = 999999
			</otherwise>
		</choose>
		UNION ALL
		SELECT CHILD_GROUP.GROUP_ID
		FROM MI_CDS_INFO_PROGRAM_GROUP CHILD_GROUP
		JOIN B ON CHILD_GROUP.P_GROUP_ID = B.GROUP_ID
		)
	</sql>
 
    <!--
        [Issue-00157258] 플레이리스트에서 사용 중인 컨텐츠를 삭제할 경우, 태그정보 삭제
    -->
    <delete id="deleteContentTagFromPlaylist">
        DELETE
          FROM MI_TAG_MAP_CONTENT_TAG /* 플레이리스트_컨텐츠_태그 매핑정보 */
         WHERE CONTENT_ID = #{contentId}
    </delete>
    
    <!-- 
        [Issue-00157258] 플레이리스트에서 사용 중인 컨텐츠를 삭제할 경우, 태그의 컨텐츠순번 갱신
    -->
    <update id="updateContentOrderOfTag">
        UPDATE MI_TAG_MAP_CONTENT_TAG /* 플레이리스트_컨텐츠_태그 매핑정보 */
           SET CONTENT_ORDER = #{newContentOrder}
         WHERE PLAYLIST_ID = #{playlistId}
           AND VERSION_ID  = #{versionId}
           AND CONTENT_ID  = #{contentId}
    </update>
    <select id="isExistDefaultGroup" resultType="int">
		SELECT COUNT(*) from MI_CMS_INFO_PLAYLIST_GROUP
		WHERE
			CREATOR_ID = #{creator_id} AND
			ORGANIZATION_ID = #{organization_id}
	</select>
	
	<update id="changeGroupIdOf_MI_CMS_MAP_GROUP_PLAYLIST">
		UPDATE MI_CMS_MAP_GROUP_PLAYLIST SET GROUP_ID = #{groupId}
		WHERE 
			PLAYLIST_ID IN 
			(
				SELECT PLAYLIST_ID 
				FROM MI_CMS_INFO_PLAYLIST
				WHERE 
					CREATOR_ID = #{fromUserId} AND
					ORGANIZATION_ID = #{organizationId}
			)
	</update>

	<update id="changeCreatorIdOf_MI_CMS_INFO_PLAYLIST_VERSION">
		UPDATE MI_CMS_INFO_PLAYLIST_VERSION SET CREATOR_ID = #{toUserId}
		WHERE 
			PLAYLIST_ID IN 
			(
				SELECT PLAYLIST_ID 
				FROM MI_CMS_INFO_PLAYLIST
				WHERE 
					CREATOR_ID = #{fromUserId} AND
					ORGANIZATION_ID = #{organizationId}
			)
	</update>

	<update id="changeCreatorIdOf_MI_CMS_INFO_PLAYLIST">
		UPDATE MI_CMS_INFO_PLAYLIST SET CREATOR_ID = #{toUserId}
		WHERE 
			CREATOR_ID = #{fromUserId} AND
			ORGANIZATION_ID = #{organizationId}
	</update>
	
	<delete id="deleteGroupByCreatorId">
		DELETE FROM MI_CMS_INFO_PLAYLIST_GROUP WHERE CREATOR_ID
		= #{creatorId}
	</delete>

	<select id="getCntAllPlaylists" resultType="long">
		SELECT
			COUNT(*)
		FROM
			MI_CMS_INFO_PLAYLIST A
		WHERE			 
			A.CREATOR_ID = #{creatorId} AND
			A.ORGANIZATION_ID = #{organizationId}
	</select>

    <!-- KDH [RQ190703-00340][19.10 RC] 컨텐츠 사용기간 추가(만료) S -->
    <select id="getUpperPlaylist" resultType="java.util.Map">
        SELECT T2.PLAYLIST_ID   AS UPPER_PLAYLIST_ID
              ,T2.PLAYLIST_NAME AS UPPER_PLAYLIST_NAME
          FROM MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST T1
              ,MI_CMS_INFO_PLAYLIST             T2
              ,MI_CMS_INFO_PLAYLIST_VERSION     T3
         WHERE T1.CONTENT_ID  = #{playlistId}
           AND T2.PLAYLIST_ID = T1.PLAYLIST_ID
           AND T3.PLAYLIST_ID = T1.PLAYLIST_ID
           AND T3.VERSION_ID  = T1.VERSION_ID
           AND T3.IS_ACTIVE   = 'Y'
         ORDER BY T2.PLAYLIST_ID
    </select>

	<!-- Tag Playlist에 매핑된 Content의 개수를 반환한다. -->
	<select id="getContentCountInTagPlaylist" resultType="int">
        SELECT COUNT(DISTINCT T3.CONTENT_ID)
          FROM MI_CMS_INFO_PLAYLIST_VERSION T1
              ,MI_CMS_MAP_PLAYLIST_TAG      T2
              ,MI_TAG_MAP_CONTENT           T3
         WHERE T1.PLAYLIST_ID = #{playlistId}
           AND T1.IS_ACTIVE   = 'Y'
           AND T2.PLAYLIST_ID = T1.PLAYLIST_ID
           AND T2.VERSION_ID  = T1.VERSION_ID
           AND T3.TAG_ID      = T2.TAG_ID
    </select>
    <!-- KDH [RQ190703-00340][19.10 RC] 컨텐츠 사용기간 추가(만료) E -->

    <!-- KDH ********** 특정 컨텐츠가 사용된 플레이리스트 조회 S -->
    <select id="getPlaylistInfoByContentId" resultType="Map">
        SELECT DISTINCT
               playlist_id
              ,version_id
          FROM mi_cms_map_playlist_content
         WHERE content_id = #{contentId}
         ORDER BY playlist_id, version_id
    </select>
    <!-- KDH ********** 특정 컨텐츠가 사용된 플레이리스트 조회 E -->
    <!-- KDH ********** 변경된 컨텐츠가 사용된 플레이리스트 전체크기 변경하기 S -->
    <update id="setTotalSize">
		UPDATE mi_cms_info_playlist_version
           SET total_size  = #{totalSize}
         WHERE playlist_id = #{playlistId}
           AND version_id  = #{versionId}
	</update>
    <!-- KDH ********** 변경된 컨텐츠가 사용된 플레이리스트 전체크기 변경하기 E -->
    <!-- KDH ********** 버전별 플레이리스트의 컨텐츠 재생시간 변경하기 S -->
    <update id="setContentDurationByVersionOfPlaylist">
		UPDATE mi_cms_map_playlist_content
           SET content_duration  = #{contentDuration}
         WHERE playlist_id = #{playlistId}
           AND version_id  = #{versionId}
           AND content_id  = #{contentId}
	</update>

    <update id="setContentDurationMilliByVersionOfPlaylist">
		UPDATE mi_cms_map_playlist_content
           SET content_duration_milli  = #{contentDurationMilli}
         WHERE playlist_id = #{playlistId}
           AND version_id  = #{versionId}
           AND content_id  = #{contentId}
	</update>
    <!-- KDH ********** 버전별 플레이리스트의 컨텐츠 재생시간 변경하기 E -->


    <select id="getGroupListByOrganizationId" resultType="com.samsung.magicinfo.framework.content.entity.Group">
		SELECT
			GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, CREATOR_ID, CREATE_DATE
		FROM
			MI_CMS_INFO_PLAYLIST_GROUP
		WHERE ORGANIZATION_ID = #{organizationId}

	</select>
	
	<select id="getPlaylistIdListByPlaylistName" resultType="string">
		SELECT DISTINCT ON (PLAYLIST_ID) PLAYLIST_ID
		FROM
		MI_CMS_INFO_PLAYLIST
		WHERE PLAYLIST_NAME IN
		<foreach item="playlist_name" collection="playlistNameList"
			open="(" separator="," close=")">
			#{playlist_name}
		</foreach>
	</select>
	
	<select id="getPlaylistIdListByRegex" resultType="string">
		SELECT DISTINCT ON (PLAYLIST_ID) PLAYLIST_ID
		FROM
		MI_CMS_INFO_PLAYLIST
		WHERE PLAYLIST_NAME ~ #{regex} AND
		      IS_DELETED = 'N'
	</select>


    <resultMap type="com.samsung.magicinfo.restapi.playlist.model.V2PlaylistScheduleMappingResource" id="playlistScheduleMappingMap">
        <result column="PLAYLIST_ID" property="playlistId"/>
        <result column="PLAYLIST_NAME" property="playlistName"/>
        <collection property="schedules" ofType="com.samsung.magicinfo.restapi.playlist.model.V2ScheduleSimpleResource" resultMap="simpleScheduleMap" />
    </resultMap>

    <resultMap type="com.samsung.magicinfo.restapi.playlist.model.V2ScheduleSimpleResource" id="simpleScheduleMap">
        <result column="PROGRAM_ID" property="programId"/>
        <result column="PROGRAM_NAME" property="programName"/>
    </resultMap>

    <select id="getPlaylistScheduleMapping" resultMap="playlistScheduleMappingMap">
        SELECT
			A.PROGRAM_ID,
			A.PROGRAM_NAME,
			C.PLAYLIST_ID,
			C.PLAYLIST_NAME
        FROM MI_CDS_INFO_PROGRAM A JOIN (SELECT PROGRAM_ID, CONTENT_ID, CONTENT_TYPE FROM MI_CDS_INFO_SCHEDULE UNION SELECT PROGRAM_ID, CONTENT_ID, CONTENT_TYPE FROM MI_CDS_INFO_ADSCHEDULE) B
        ON A.PROGRAM_ID = B.PROGRAM_ID
        INNER JOIN MI_CMS_INFO_PLAYLIST C
        ON B.CONTENT_ID = C.PLAYLIST_ID
        WHERE  B.CONTENT_TYPE = 'PLAYLIST'
        AND C.PLAYLIST_ID IN
        <foreach collection="playlistIds" item="playlistId" open="(" separator="," close=")">
            #{playlistId}
        </foreach>
    </select>

    <select id="getPlayListGroupBySearch" resultType="com.samsung.magicinfo.framework.content.entity.Group">

        SELECT GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME,
        CREATOR_ID, CREATE_DATE
        FROM MI_CMS_INFO_PLAYLIST_GROUP
        WHERE ORGANIZATION_ID = #{organizationId}
        <if test="userId != null and userId.length() > 0">
            AND CREATOR_ID = #{userId}
        </if>

        <if test="searchText != null and searchText.length() > 0">
              <bind name="searchText" value="'%' + searchText + '%'" />
               AND GROUP_NAME LIKE #{searchText} ESCAPE '^'
        </if>
    </select>
    <select id="getParentsGroupList" resultType="com.samsung.magicinfo.restapi.common.model.V2ParentsGroup">

        WITH RECURSIVE GROUP_IDS AS (
            SELECT GROUPS.*
            FROM MI_CMS_INFO_PLAYLIST_GROUP GROUPS
            WHERE GROUP_ID = #{pGroupId}
            UNION ALL
            SELECT PARENT_GROUP.*
            FROM MI_CMS_INFO_PLAYLIST_GROUP PARENT_GROUP
            JOIN GROUP_IDS ON PARENT_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
        )
        SELECT
            GROUP_IDS.GROUP_ID as groupId,
            GROUP_IDS.P_GROUP_ID as pGroupId,
            GROUP_IDS.GROUP_DEPTH as groupDepth,
            GROUP_IDS.GROUP_NAME as groupName,
            GROUP_IDS.CREATOR_ID as creatorId,
            GROUP_IDS.ORGANIZATION_ID as organizationId
        FROM GROUP_IDS
        WHERE P_GROUP_ID > -1
        ORDER BY GROUP_DEPTH ASC
    </select>

    <select id="getParentsGroupList" resultType="com.samsung.magicinfo.restapi.common.model.V2ParentsGroup" databaseId="mssql">
        WITH GROUP_IDS AS (
            SELECT GROUPS.*
            FROM MI_CMS_INFO_PLAYLIST_GROUP GROUPS
            WHERE GROUP_ID = #{pGroupId}
            UNION ALL
            SELECT PARENT_GROUP.*
            FROM MI_CMS_INFO_PLAYLIST_GROUP PARENT_GROUP
            JOIN GROUP_IDS ON PARENT_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
        )
        SELECT
            GROUP_IDS.GROUP_ID as groupId,
            GROUP_IDS.P_GROUP_ID as pGroupId,
            GROUP_IDS.GROUP_DEPTH as groupDepth,
            GROUP_IDS.GROUP_NAME as groupName,
            GROUP_IDS.CREATOR_ID as creatorId,
            GROUP_IDS.ORGANIZATION_ID as organizationId
        FROM GROUP_IDS
        WHERE P_GROUP_ID > -1
        ORDER BY GROUP_DEPTH ASC
    </select>


    <select id="getOrganizationIdByGroupId" resultType="java.lang.Long">
        SELECT
          ORGANIZATION_ID
        FROM
        MI_CMS_INFO_PLAYLIST_GROUP
        WHERE GROUP_ID = #{groupId}
    </select>

	<select id="getSubGroupList" resultType="com.samsung.magicinfo.framework.content.entity.Group">
        SELECT *
        FROM
            MI_CMS_INFO_PLAYLIST_GROUP
        WHERE
            P_GROUP_ID = #{groupId} 
            <if test="organizationId != null">
                AND ORGANIZATION_ID = #{organizationId}
            </if>
        ORDER BY
            GROUP_NAME ASC
    </select>
    
    <!-- [SF00188402] -->
	<select id="getActivePlaylistCountOne" resultType="map">
	((SELECT 
		PLAYLIST_ID,VERSION_ID 
 	FROM 
 		MI_CMS_MAP_PLAYLIST_CONTENT 
 	WHERE 
 		CONTENT_ID=#{contentId}) 
 	EXCEPT 
 	(SELECT 
 		PLAYLIST_ID,VERSION_ID
 	FROM 
 		MI_CMS_MAP_PLAYLIST_CONTENT 
 	WHERE 
 		CONTENT_ID != #{contentId}))
 		
	UNION
	
	((SELECT
		B.PLAYLIST_ID, B.VERSION_ID
	FROM
		MI_CMS_MAP_PLAYLIST_CONTENT A,
		MI_CMS_INFO_PLAYLIST_VERSION B,
		MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST C
	WHERE
		A.CONTENT_ID = #{contentId} AND
		B.PLAYLIST_ID=C.PLAYLIST_ID AND
		B.VERSION_ID=C.VERSION_ID AND
		C.CONTENT_ID = A.PLAYLIST_ID)
	 EXCEPT
	 (SELECT
		B.PLAYLIST_ID, B.VERSION_ID
	FROM
		MI_CMS_MAP_PLAYLIST_CONTENT A,
		MI_CMS_INFO_PLAYLIST_VERSION B,
		MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST C
	WHERE
		B.PLAYLIST_ID=C.PLAYLIST_ID AND
		B.VERSION_ID=C.VERSION_ID AND
		C.CONTENT_ID = A.PLAYLIST_ID AND
	 	A.CONTENT_ID != #{contentId}
	UNION
	SELECT 
 		PLAYLIST_ID,VERSION_ID
 	FROM 
 		MI_CMS_MAP_PLAYLIST_CONTENT 
 	WHERE 
 		CONTENT_ID != #{contentId}))			
	</select>
	
	<!-- [SF00188402] -->
	<select id="isExistMapPlaylistID" resultType="int">
	SELECT
		COUNT(PLAYLIST_ID)
		FROM MI_CMS_MAP_PLAYLIST_CONTENT
		WHERE PLAYLIST_ID=#{playlistId} AND VERSION_ID!=#{playlistVersionId}
	</select>

	<select id="getContentCountByPlaylist" resultType="Map">
	  SELECT PLAYLIST_ID, COUNT(CONTENT_ID) AS CONTENT_COUNT
	  FROM MI_CMS_MAP_PLAYLIST_CONTENT
	  GROUP BY PLAYLIST_ID
	</select>

	<select id="getContentTypeCountByPlaylist" resultType="Map">
	  SELECT PLAYLIST_ID, MEDIA_TYPE, COUNT(C.CONTENT_ID) AS CONTENT_COUNT
		FROM MI_CMS_MAP_PLAYLIST_CONTENT AS P
		LEFT OUTER JOIN (
		SELECT CONTENT_ID, MEDIA_TYPE
		FROM MI_CMS_INFO_CONTENT_VERSION
		WHERE IS_ACTIVE = 'Y'
		) AS C
		ON P.CONTENT_ID = C.CONTENT_ID
		GROUP BY PLAYLIST_ID, MEDIA_TYPE
		ORDER BY PLAYLIST_ID

	</select>


	<select id="getPlaylistCountByPlaylistType" resultType="Map">
  			SELECT PLAYLIST_TYPE, COUNT(PLAYLIST_ID) AS PLAYLIST_COUNT
		FROM MI_CMS_INFO_PLAYLIST AS P
		GROUP BY PLAYLIST_TYPE
	</select>

	<select id="getPlaylistGroupTotalCount" resultType="int">

		SELECT COUNT(GROUP_ID) FROM MI_CMS_INFO_PLAYLIST_GROUP WHERE P_GROUP_ID != -1

	</select>


</mapper>

