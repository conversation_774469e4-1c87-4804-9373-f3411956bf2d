package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.BasicFileInfo;
import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.ContentGroup;
import com.samsung.magicinfo.webauthor2.model.ContentThumbnailBasic;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ContentService {
  Page<Content> getContentResources(Pageable paramPageable, DeviceType paramDeviceType);
  
  Page<Content> getContentResources(Pageable paramPageable, DeviceType paramDeviceType, List<MediaType> paramList);
  
  Page<Content> getContentResources(String paramString, Pageable paramPageable, DeviceType paramDeviceType, List<MediaType> paramList);
  
  Page<Content> getContentResources(String paramString1, Pageable paramPageable, DeviceType paramDeviceType, List<MediaType> paramList, String paramString2);
  
  Content getContent(String paramString);
  
  List<Content> getRelatedDLKContent(String paramString);
  
  List<Content> findContents(List<BasicFileInfo> paramList);
  
  List<Content> findContentsByIdList(List<String> paramList);
  
  List<MediaType> getMediaTypeList(DeviceType paramDeviceType);
  
  List<ContentThumbnailBasic> getContentThumbnails(String paramString1, String paramString2);
  
  ContentThumbnailBasic getContentThumbnailBySize(String paramString1, String paramString2);
  
  String deleteContent(String paramString);
  
  List<ContentGroup> getContentGroupResources();
}
