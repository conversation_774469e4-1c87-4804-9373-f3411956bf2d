package com.samsung.magicinfo.webauthor2.service;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.model.ContentSaveElements;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.FileInfo;
import com.samsung.magicinfo.webauthor2.model.FileItemsDescriptor;
import com.samsung.magicinfo.webauthor2.model.ImageDimension;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.NewFileInfo;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.service.CSDFileService;
import com.samsung.magicinfo.webauthor2.service.ContentSaveService;
import com.samsung.magicinfo.webauthor2.service.FileService;
import com.samsung.magicinfo.webauthor2.service.upload.UploadHelperService;
import com.samsung.magicinfo.webauthor2.util.CleanPreviewFolder;
import com.samsung.magicinfo.webauthor2.util.FileHashUtil;
import com.samsung.magicinfo.webauthor2.util.ImageDimensionUtil;
import com.samsung.magicinfo.webauthor2.util.MultipartFilenameValidator;
import com.samsung.magicinfo.webauthor2.util.PlayTimeUtil;
import com.samsung.magicinfo.webauthor2.util.SupportedFormatUtils;
import com.samsung.magicinfo.webauthor2.util.UserData;
import com.samsung.magicinfo.webauthor2.util.WebResourceService;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
public class ContentSaveServiceImpl implements ContentSaveService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.ContentSaveServiceImpl.class);
  
  private static final String DEFAULT_FILE_ID = "00000000-0000-0000-0000-000000000000";
  
  private FileService fileService;
  
  private CSDFileService csdFileService;
  
  private WebResourceService webResourceService;
  
  private UploadHelperService uploadHelperService;
  
  private MultipartFilenameValidator multipartFilenameValidator;
  
  private ServletContext servletContext;
  
  private ContentSaveElements contentSaveElements;
  
  private UserData userData;
  
  private String serverUrl;
  
  @Autowired
  public ContentSaveServiceImpl(FileService fileService, CSDFileService csdFileService, WebResourceService webResourceService, UploadHelperService uploadHelperService, ServletContext servletContext, ContentSaveElements contentSaveElements, UserData userData, MultipartFilenameValidator multipartFilenameValidator) {
    this.fileService = fileService;
    this.csdFileService = csdFileService;
    this.webResourceService = webResourceService;
    this.uploadHelperService = uploadHelperService;
    this.servletContext = servletContext;
    this.contentSaveElements = contentSaveElements;
    this.userData = userData;
    this.multipartFilenameValidator = multipartFilenameValidator;
  }
  
  public List<MediaSource> getFilledMediaSources(FileItemsDescriptor fileItemsDescriptor, HttpServletRequest request) {
    List<MediaSource> mediaSources = fileItemsDescriptor.getMediaSources();
    String errorMessage = null;
    errorMessage = validateInvalidFileName(mediaSources);
    if (!Strings.isNullOrEmpty(errorMessage))
      throw new FileItemValidationException(500, errorMessage); 
    this.serverUrl = getURLWithContextPath(request);
    fillLftOrLfdInfo(mediaSources.get(0));
    for (ListIterator<MediaSource> iter = mediaSources.listIterator(1); iter.hasNext();)
      fillMissingInfoInContents(iter.next()); 
    addThumbnailMediaSource();
    this.contentSaveElements.setMediaSources(mediaSources);
    updateXmlProjectedSize(mediaSources);
    return mediaSources;
  }
  
  private void updateXmlProjectedSize(List<MediaSource> mediaSources) {
    List<Long> originalFileSizes = this.contentSaveElements.getFileSizes();
    List<Long> updatedFileSizes = new ArrayList<>();
    for (MediaSource ms : mediaSources)
      updatedFileSizes.add(Long.valueOf(ms.getMediaSize())); 
    int sizeDifference = 0;
    for (int i = 1; i < originalFileSizes.size(); i++) {
      int newLength = Long.toString(((Long)updatedFileSizes.get(i)).longValue()).length();
      int oldLength = Long.toString(((Long)originalFileSizes.get(i)).longValue()).length();
      int diff = newLength - oldLength;
      sizeDifference += diff;
    } 
    ((MediaSource)mediaSources.get(0)).setMediaSize(((MediaSource)mediaSources.get(0)).getMediaSize() + sizeDifference);
  }
  
  public List<MediaSource> getUpdatedMediaSources(List<MediaSource> mediaSources) {
    String userId = this.userData.getUserId();
    String cid = ((MediaSource)this.contentSaveElements.getMediaSources().get(0)).getContentId();
    this.csdFileService.updateSaveElements(userId, this.contentSaveElements, cid);
    return mediaSources;
  }
  
  public MediaSource getMediaSourceFromNewFile(NewFileInfo fileItemsDescriptor) throws IOException {
    String errorMessage = null;
    errorMessage = this.multipartFilenameValidator.validateNameForContents(fileItemsDescriptor.getMediaSource().getFileName());
    if (!Strings.isNullOrEmpty(errorMessage))
      throw new FileItemValidationException(500, errorMessage); 
    String message = this.multipartFilenameValidator.validateName(fileItemsDescriptor.getMediaSource().getFileName());
    if (!Strings.isNullOrEmpty(message))
      throw new FileItemValidationException(500, message); 
    Path filePath = Paths.get(this.servletContext.getRealPath("insertContents"), new String[] { fileItemsDescriptor.getMediaSource().getFileName() });
    if (!CleanPreviewFolder.isPathValid(String.valueOf(filePath)))
      throw new FileItemValidationException("InvalidFilePathError"); 
    if (Files.exists(filePath, new java.nio.file.LinkOption[0]))
      FileUtils.deleteQuietly(filePath.toFile()); 
    try {
      FileUtils.writeStringToFile(filePath.toFile(), fileItemsDescriptor.getFileData(), StandardCharsets.UTF_8);
    } catch (IOException ex) {
      Logger.getLogger(com.samsung.magicinfo.webauthor2.service.ContentSaveServiceImpl.class.getName()).log(Level.SEVERE, (String)null, ex);
    } 
    MediaSource mediaSource = this.uploadHelperService.getDetailsFromFile(filePath);
    mediaSource.setIsNew(true);
    mediaSource.setData("insertContents/" + fileItemsDescriptor.getMediaSource().getFileName());
    return mediaSource;
  }
  
  public void addThumbnailMediaSource() {
    MediaSource projectThumbnailMediaSource = new MediaSource();
    String pathToThumbnail = this.contentSaveElements.getPathToThumbnail();
    projectThumbnailMediaSource.setData("");
    projectThumbnailMediaSource.setFileName(FilenameUtils.getName(pathToThumbnail));
    fillFileType(projectThumbnailMediaSource);
    fillMediaType(projectThumbnailMediaSource);
    projectThumbnailMediaSource.setFileType("thumbnail");
    Path thumbnailFilePath = Paths.get(pathToThumbnail, new String[0]);
    projectThumbnailMediaSource.setPath(pathToThumbnail);
    fillHash(projectThumbnailMediaSource, thumbnailFilePath);
    fillLength(projectThumbnailMediaSource, thumbnailFilePath);
    fillImageDimensions(projectThumbnailMediaSource, thumbnailFilePath);
    fillFileId(projectThumbnailMediaSource);
    this.contentSaveElements.setProjectThumbnailMediaSource(projectThumbnailMediaSource);
  }
  
  private void fillLftOrLfdInfo(MediaSource mediaSource) {
    try {
      String xml = this.contentSaveElements.getXml();
      Path xmlPath = writeXmlToFile(this.contentSaveElements.getProjectName(), xml);
      mediaSource.setPath(xmlPath.toString());
      fillHash(mediaSource, xmlPath);
      fillLength(mediaSource, xmlPath);
      mediaSource.setMediaWidth(this.contentSaveElements.getWidth());
      mediaSource.setMediaHeight(this.contentSaveElements.getHeight());
      mediaSource.setMediaDuration(PlayTimeUtil.convertPlayTime(this.contentSaveElements.getPlayTime()).doubleValue());
      fillFileType(mediaSource);
      fillMediaType(mediaSource);
      if (Strings.isNullOrEmpty(mediaSource.getFileId()))
        mediaSource.setFileId("00000000-0000-0000-0000-000000000000"); 
    } catch (IOException e) {
      logger.error("Error during setting xml media source properties: id {}", mediaSource.getContentId());
    } 
  }
  
  private void fillFileId(MediaSource mediaSource) {
    fillFileIdIfEmpty(mediaSource);
  }
  
  private Path writeXmlToFile(String projectName, String xml) throws IOException {
    String insertContents = this.servletContext.getRealPath("insertContents");
    String userWorkspaceDirectory = this.userData.getWorkspaceFolderName();
    Path xmlPath = Paths.get(insertContents, new String[] { userWorkspaceDirectory, projectName });
    if (Files.exists(xmlPath, new java.nio.file.LinkOption[0]))
      FileUtils.deleteQuietly(xmlPath.toFile()); 
    FileUtils.writeStringToFile(xmlPath.toFile(), xml, StandardCharsets.UTF_8);
    return xmlPath;
  }
  
  private void fillMissingInfoInContents(MediaSource mediaSource) {
    String fileId = mediaSource.getFileId();
    if (!Strings.isNullOrEmpty(fileId) && !mediaSource.getData().contains("textImage")) {
      fillMissingWithFileId(mediaSource);
    } else {
      fillMissingInfoWithNoFileId(mediaSource);
    } 
  }
  
  private void fillMissingWithFileId(MediaSource mediaSource) {
    try {
      FileInfo fileInfo = this.fileService.getFileInfo(mediaSource.getFileId());
      mediaSource.setFileHash(fileInfo.getFileHash());
      mediaSource.setFileName(fileInfo.getFileName());
      mediaSource.setMediaSize(fileInfo.getSize());
      Path path = Paths.get(fileInfo.getFileLocalPath(), new String[] { fileInfo.getFileName() });
      mediaSource.setPath(path.toString());
    } catch (FileNotFoundException e) {
      fillMissingInfoWithNoFileId(mediaSource);
    } 
  }
  
  private void fillMissingInfoWithNoFileId(MediaSource mediaSource) {
    Assert.notNull(mediaSource.getData(), "Property Data is required.");
    try {
      fillName(mediaSource);
      fillData(mediaSource);
      Path downloadMediaSourceFile = downloadMediaSource(mediaSource);
      fillPath(mediaSource, downloadMediaSourceFile);
      fillThumbnailLink(mediaSource);
      fillThumbnailLargeLink(mediaSource);
      fillHash(mediaSource, downloadMediaSourceFile);
      fillLength(mediaSource, downloadMediaSourceFile);
      fillFileType(mediaSource);
      fillMediaType(mediaSource);
      fillImageDimensions(mediaSource, downloadMediaSourceFile);
      fillFileId(mediaSource);
    } catch (IOException e) {
      logger.error("Error during setting content properties.");
    } 
  }
  
  private void fillFileIdIfEmpty(MediaSource mediaSource) {
    if (Strings.isNullOrEmpty(mediaSource.getFileId()))
      mediaSource.setFileId(generateRandomFileId()); 
  }
  
  private void fillPath(MediaSource mediaSource, Path downloadMediaSourceFile) {
    mediaSource.setPath(downloadMediaSourceFile.toString());
  }
  
  private Path downloadMediaSource(MediaSource mediaSource) throws IOException {
    String insertContents = this.servletContext.getRealPath("insertContents");
    String importLfd = this.servletContext.getRealPath("import");
    String userWorkspaceDirectory = this.userData.getWorkspaceFolderName();
    if (mediaSource.getData().contains("textImage"))
      return Paths.get(insertContents, new String[] { userWorkspaceDirectory, "textImage", mediaSource.getFileName() }); 
    if (mediaSource.getData().contains("playConfig.json"))
      return Paths.get(insertContents, new String[] { mediaSource.getFileName() }); 
    if (mediaSource.getData().contains("import/"))
      return Paths.get(importLfd, new String[] { mediaSource.getData().split("import/")[1] }); 
    Path downloadPath = Paths.get(insertContents, new String[] { userWorkspaceDirectory, mediaSource.getFileName() });
    if (Files.exists(downloadPath, new java.nio.file.LinkOption[0]))
      FileUtils.deleteQuietly(downloadPath.toFile()); 
    this.webResourceService.copyResourceToFile(mediaSource.getData(), downloadPath.toFile());
    return downloadPath;
  }
  
  private void fillImageDimensions(MediaSource mediaSource, Path filePath) {
    if (mediaSource.getMediaType() == null && mediaSource.getMediaType() == MediaType.IMAGE)
      try {
        ImageDimension imageDimensions = ImageDimensionUtil.getImageDimensions(filePath);
        mediaSource.setMediaWidth(imageDimensions.getWidth());
        mediaSource.setMediaHeight(imageDimensions.getHeight());
      } catch (IOException e) {
        logger.error("Error on setting image dimensions.");
      }  
  }
  
  private void fillMediaType(MediaSource mediaSource) {
    MediaType mediaTypeForExtension = SupportedFormatUtils.getMediaTypeForExtension(mediaSource.getFileType());
    if (mediaTypeForExtension != null)
      mediaSource.setMediaType(mediaTypeForExtension); 
  }
  
  private String fillFileType(MediaSource mediaSource) {
    String extension = FilenameUtils.getExtension(mediaSource.getFileName());
    mediaSource.setFileType(extension);
    return extension;
  }
  
  private String fillName(MediaSource mediaSource) {
    String name = FilenameUtils.getName(mediaSource.getData());
    mediaSource.setFileName(name);
    return name;
  }
  
  private void fillLength(MediaSource mediaSource, Path filePath) {
    try {
      mediaSource.setMediaSize(Files.size(filePath));
    } catch (IOException e) {
      mediaSource.setMediaSize(0L);
    } 
  }
  
  private void fillHash(MediaSource mediaSource, Path filePath) {
    String hash = FileHashUtil.getHash(filePath.toFile());
    mediaSource.setFileHash(hash);
  }
  
  private void fillThumbnailLargeLink(MediaSource mediaSource) {
    String thumbnailLargeLink = mediaSource.getThumbLg();
    if (!Strings.isNullOrEmpty(thumbnailLargeLink) && 
      !thumbnailLargeLink.startsWith("http")) {
      String newUrl = this.serverUrl + "/" + thumbnailLargeLink;
      mediaSource.setThumbLg(newUrl);
    } 
  }
  
  private void fillThumbnailLink(MediaSource mediaSource) {
    String thumbnailLink = mediaSource.getThumb();
    if (!Strings.isNullOrEmpty(thumbnailLink) && 
      !thumbnailLink.startsWith("http")) {
      String newUrl = this.serverUrl + "/" + thumbnailLink;
      mediaSource.setThumb(newUrl);
    } 
  }
  
  private void fillData(MediaSource mediaSource) {
    String fileWithRelativePath = mediaSource.getData();
    if (!fileWithRelativePath.startsWith("http")) {
      String newUrl = this.serverUrl + "/" + fileWithRelativePath;
      mediaSource.setData(newUrl);
    } 
  }
  
  private String getURLWithContextPath(HttpServletRequest request) {
    return request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request
      .getContextPath();
  }
  
  public void saveProjectProperties(FileItemsDescriptor fileItemsDescriptor) {
    this.contentSaveElements.setProjectName(((MediaSource)fileItemsDescriptor.getMediaSources().get(0)).getFileName());
    this.contentSaveElements.setContentName(((MediaSource)fileItemsDescriptor.getMediaSources().get(0)).getContentName());
    this.contentSaveElements.setHeight(fileItemsDescriptor.getHeight());
    this.contentSaveElements.setWidth(fileItemsDescriptor.getWidth());
    this.contentSaveElements.setPlayerType(DeviceType.valueOf(fileItemsDescriptor.getPlayerType()));
    this.contentSaveElements.setPlayTime(PlayTimeUtil.covertPlayTimeFromSeconds(fileItemsDescriptor.getPlayTime()));
    this.contentSaveElements.setXml(fileItemsDescriptor.getXml());
    List<Long> originalFileSizes = new ArrayList<>();
    for (MediaSource ms : fileItemsDescriptor.getMediaSources())
      originalFileSizes.add(Long.valueOf(ms.getMediaSize())); 
    this.contentSaveElements.setFileSizes(originalFileSizes);
  }
  
  private String generateRandomFileId() {
    return UUID.randomUUID().toString().toUpperCase();
  }
  
  private String pathStringFormatForCSD(String path) {
    if (Strings.isNullOrEmpty(path) || path.equals("/"))
      return ".\\"; 
    if (path.length() > 1 && !path.startsWith("/"))
      path = "/" + path; 
    if (path.length() > 1 && !path.endsWith("/"))
      path = path + "/"; 
    return "." + path.replace("/", "\\");
  }
  
  private String validateInvalidFileName(List<MediaSource> mediaSources) {
    ListIterator<MediaSource> iter = mediaSources.listIterator();
    String errorMessage = null;
    while (iter.hasNext()) {
      MediaSource ms = iter.next();
      if (!Strings.isNullOrEmpty(ms.getFileName()))
        errorMessage = this.multipartFilenameValidator.validateNameForContents(ms.getFileName()); 
      if (!Strings.isNullOrEmpty(errorMessage))
        return errorMessage; 
    } 
    return null;
  }
}
