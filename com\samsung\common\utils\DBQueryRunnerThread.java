package com.samsung.common.utils;

import com.samsung.common.logger.LoggingManagerV2;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import org.apache.logging.log4j.Logger;

class DBQueryRunnerThread extends Thread {
   static Logger logger = LoggingManagerV2.getLogger(DBQueryRunnerThread.class);
   private Object deviceMapper = null;
   private String invokeMethodName = null;
   private Object device = null;

   public DBQueryRunnerThread(Object deviceDaoMapper, String invokeMethodName, Object device) {
      super();
      this.deviceMapper = deviceDaoMapper;
      this.device = device;
      this.invokeMethodName = invokeMethodName;
   }

   public void run() {
      try {
         Method method = this.deviceMapper.getClass().getMethod(this.invokeMethodName, this.device.getClass());
         method.invoke(this.deviceMapper, this.device);
      } catch (SecurityException | IllegalAccessException | IllegalArgumentException | InvocationTargetException | NoSuchMethodException var2) {
         logger.error("", var2);
      }

   }
}
