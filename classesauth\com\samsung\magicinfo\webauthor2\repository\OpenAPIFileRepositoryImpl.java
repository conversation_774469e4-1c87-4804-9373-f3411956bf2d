package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.repository.GetContentFileListOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.GetFileInfoOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.GetFileTypeListOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.OpenAPIFileRepository;
import com.samsung.magicinfo.webauthor2.repository.model.FileInfoData;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.web.client.RestTemplate;

@Repository
public class OpenAPIFileRepositoryImpl implements OpenAPIFileRepository {
  private final RestTemplate restTemplate;
  
  private final UserData userData;
  
  @Autowired
  public OpenAPIFileRepositoryImpl(RestTemplate restTemplate, UserData userData) {
    this.restTemplate = restTemplate;
    this.userData = userData;
  }
  
  public List<String> getFileTypeList(MediaType mediaType, DeviceType deviceType) {
    GetFileTypeListOpenApiMethod openApiMethod = new GetFileTypeListOpenApiMethod(mediaType.toString(), deviceType.toString(), this.restTemplate, this.userData.getToken());
    return (List<String>)openApiMethod.callMethod();
  }
  
  public FileInfoData getFileInfo(String fileId) {
    GetFileInfoOpenApiMethod openApiMethod = new GetFileInfoOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken(), fileId);
    return (FileInfoData)openApiMethod.callMethod();
  }
  
  public List<String> getContentFileList(String contentId) {
    GetContentFileListOpenApiMethod openApiMethod = new GetContentFileListOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken(), contentId);
    return (List<String>)openApiMethod.callMethod();
  }
}
