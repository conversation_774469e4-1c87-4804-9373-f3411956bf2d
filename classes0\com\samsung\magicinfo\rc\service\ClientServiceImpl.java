package com.samsung.magicinfo.rc.service;

import com.samsung.magicinfo.rc.common.Device;
import com.samsung.magicinfo.rc.common.batch.CheckingServerAjaxTime;
import com.samsung.magicinfo.rc.common.exception.OpenApiExceptionCode;
import com.samsung.magicinfo.rc.common.exception.OpenApiServiceException;
import com.samsung.magicinfo.rc.common.memory.ServerAuthorityMemory;
import com.samsung.magicinfo.rc.common.memory.ServerCaptureImageMemory;
import com.samsung.magicinfo.rc.common.memory.ServerTokenMemory;
import com.samsung.magicinfo.rc.common.queue.ServerQueue;
import com.samsung.magicinfo.rc.common.queue.ServerTimeQueue;
import com.samsung.magicinfo.rc.framework.queue.entity.ClientEntity;
import com.samsung.magicinfo.rc.model.client.Response;
import com.samsung.magicinfo.rc.model.client.ResponseClass;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class ClientServiceImpl {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.service.ClientServiceImpl.class);
  
  @Autowired
  ServerCaptureImageMemory imageHashMap;
  
  @Autowired
  ServerQueue serverQueue;
  
  @Autowired
  ServerTimeQueue serverTimeQueue;
  
  @Autowired
  CheckingServerAjaxTime checkingServerAjaxTime;
  
  @Autowired
  ServerAuthorityMemory serverAuthorityMemory;
  
  @Autowired
  ServerTokenMemory serverTokenMemory;
  
  public Response status(String deviceId) {
    Response response = new Response();
    int deviceChk = 0;
    Object[] server = this.serverQueue.getConnection();
    if (this.serverQueue.getStatusQueue((deviceId == null) ? "0" : deviceId).booleanValue())
      deviceChk = 1; 
    if (((Integer)server[0]).intValue() == ServerQueue.SUCCESS) {
      response.setCode(0);
      response.setResponseClass(new ResponseClass("connections:" + server[1] + "|device:" + deviceChk + "|version:" + "2"));
    } else {
      response.setCode(999);
      response.setErrorMessage("error");
      log.info("error code : " + (Integer)server[0] + " message : " + server[1]);
    } 
    return response;
  }
  
  public Response ready(String deviceIdStr, String token, String authority) {
    Response response = new Response();
    String[] deviceIds = deviceIdStr.split("\\,");
    for (String deviceId : deviceIds) {
      if (!verifyDeviceId(deviceId)) {
        log.info("error code : " + OpenApiExceptionCode.INVALIDE_DEVICE[0] + " message : " + OpenApiExceptionCode.INVALIDE_DEVICE[1]);
        response.setCode(Integer.valueOf(OpenApiExceptionCode.INVALIDE_DEVICE[0]).intValue());
        response.setErrorMessage(OpenApiExceptionCode.INVALIDE_DEVICE[1]);
        return response;
      } 
    } 
    for (String deviceId : deviceIds) {
      this.serverTokenMemory.startToken(deviceId, token);
      this.serverAuthorityMemory.startAuthority(deviceId, authority);
      response.setCode(0);
    } 
    return response;
  }
  
  public Response start(String deviceId, int interval, String resolution, String suppKeyboard, String suppPosition, String suppLongPress, String suppRemote) {
    Response response = new Response();
    log.info("RM start " + deviceId);
    boolean isSupportedKeyboard = false;
    boolean isSupportedMouse = false;
    boolean isSupportRemote = true;
    boolean isSupportLongPress = false;
    String isSupportResolution = null;
    if (!verifyDeviceId(deviceId)) {
      log.info("error code : " + OpenApiExceptionCode.INVALIDE_DEVICE[0] + " message : " + OpenApiExceptionCode.INVALIDE_DEVICE[1]);
      response.setCode(Integer.valueOf(OpenApiExceptionCode.INVALIDE_DEVICE[0]).intValue());
      response.setErrorMessage(OpenApiExceptionCode.INVALIDE_DEVICE[1]);
    } 
    if ("1".equals(suppKeyboard))
      isSupportedKeyboard = true; 
    if ("1".equals(suppPosition))
      isSupportedMouse = true; 
    if (resolution != null && !"".equals(resolution))
      isSupportResolution = resolution; 
    if (suppRemote != null && "0".equals(suppRemote))
      isSupportRemote = false; 
    if (suppLongPress != null && "1".equals(suppLongPress))
      isSupportLongPress = true; 
    log.info("init device (deviceId : " + deviceId + ", interval : " + interval + " resolution : " + isSupportResolution + " keyboard : " + isSupportedKeyboard + " supportMouse : " + isSupportedMouse + " isSupportLongPress : " + isSupportLongPress + " isSupportRemote : " + isSupportRemote + ")");
    try {
      this.serverQueue.startQueue(new Device(deviceId, interval, isSupportResolution, isSupportedKeyboard, isSupportedMouse, isSupportLongPress, isSupportRemote));
      response.setCode(0);
    } catch (OpenApiServiceException e) {
      log.error("", (Throwable)e);
    } 
    log.warn("StartQueue : " + deviceId);
    return response;
  }
  
  public Response getTriggeringInfo(MultipartFile file, String deviceId, String attachMode) {
    Response response = new Response();
    try {
      byte[] byteArr = file.getBytes();
      String extendName = getExtendName(file.getOriginalFilename().trim());
      String fileName = deviceId + "." + extendName;
      this.imageHashMap.inputImage(deviceId, byteArr);
    } catch (IOException e) {
      log.error("", e);
    } 
    try {
      Object[] server = this.serverQueue.getQueue(deviceId);
      this.serverTimeQueue.inputTimeQueue(deviceId);
      String resString = "";
      if (((Integer)server[0]).intValue() == ServerQueue.SUCCESS) {
        ClientEntity client = (ClientEntity)server[1];
        if (client != null) {
          if (client.getText() != null && !client.getText().equals("")) {
            resString = "setText:" + client.getText();
          } else if (client.getMouse() != null && !client.getMouse().equals("")) {
            resString = client.getMouse();
          } else if (client.getKeyCode() != null && !client.getKeyCode().equals("")) {
            resString = "setKeyCode:" + client.getKeyCode();
            if (client.getSetPanel() != null)
              resString = resString + ",setPanel:" + client.getSetPanel(); 
            if (client.getReqScreenMode() != null)
              resString = resString + ",reqScreenMode:" + client.getReqScreenMode(); 
          } else if (client.getReqScreenMode() != null) {
            resString = resString + "setKeyCode:null,setPanel:null,reqScreenMode:" + client.getReqScreenMode();
          } else {
            resString = null;
          } 
          if (client.getReqOpMode() != null && client.getReqOpMode().equals("1"))
            resString = "reqOpMode:" + client.getReqOpMode(); 
          if (resString != null)
            log.error("[getTriggering] - device : " + deviceId + ", resString : " + resString); 
          ResponseClass responseClass = new ResponseClass();
          if (resString != null && !resString.equals("") && (resString.startsWith("setText") || resString.startsWith("setPosX"))) {
            responseClass.setResultText(resString);
          } else {
            responseClass.setResultValue(resString);
          } 
          response.setResponseClass(responseClass);
          response.setCode(0);
        } 
      } 
    } catch (OpenApiServiceException e) {
      if (e.getMessage().equals(OpenApiExceptionCode.DONT_HAVE_DEVICE_ID_IN_QUEEUE[1])) {
        log.error("[RC][" + deviceId + "] send stop to a device ");
        ResponseClass responseClass = new ResponseClass();
        responseClass.setResultValue("reqOpMode:1");
        response.setResponseClass(responseClass);
        response.setCode(0);
      } 
    } catch (Exception e) {
      log.error(e.getMessage());
    } 
    return response;
  }
  
  public Response stop(String deviceId, String token) {
    Response response = new Response();
    log.info("RM stop " + deviceId);
    if (this.serverTokenMemory.containsKey(deviceId)) {
      if (!this.serverTokenMemory.checkToken(deviceId, token)) {
        response.setCode(Integer.valueOf(OpenApiExceptionCode.INVALIDE_DEVICE[0]).intValue());
        response.setErrorMessage(OpenApiExceptionCode.INVALIDE_DEVICE[1]);
      } else {
        try {
          this.serverQueue.DestroyQueue(deviceId);
          this.serverTimeQueue.stop(deviceId);
          this.checkingServerAjaxTime.stop(deviceId);
          this.serverTokenMemory.deleteToken(deviceId);
          this.serverAuthorityMemory.deleteAuthority(deviceId);
        } catch (Exception e) {
          log.error("[RMService] stop error! " + deviceId + " e: " + e.getMessage());
        } 
      } 
    } else {
      response.setCode(Integer.valueOf(OpenApiExceptionCode.ALREADY_DISCONNECTION[0]).intValue());
      response.setErrorMessage(OpenApiExceptionCode.ALREADY_DISCONNECTION[1]);
    } 
    return response;
  }
  
  public boolean verifyDeviceId(String DeviceId) {
    boolean rtn = false;
    Pattern p = Pattern.compile("^[a-zA-Z0-9]{1,2}-[a-zA-Z0-9]{1,2}-[a-zA-Z0-9]{1,2}-[a-zA-Z0-9]{1,2}-[a-zA-Z0-9]{1,2}-[a-zA-Z0-9]{1,2}$");
    Matcher m = p.matcher(DeviceId);
    if (m.find()) {
      rtn = true;
    } else {
      log.info("verify device id fail! deviceId : " + DeviceId);
    } 
    return rtn;
  }
  
  private String getExtendName(String fileName) {
    String[] str = fileName.split("\\.");
    int size = str.length;
    String ret = fileName;
    if (size > 0)
      ret = str[size - 1]; 
    return ret;
  }
}
