package com.samsung.magicinfo.restapi.utils;

import com.samsung.common.utils.RoleUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ShareFolderInfo;
import com.samsung.magicinfo.framework.content.manager.ShareFolderInfoImpl;
import com.samsung.magicinfo.restapi.exception.BaseRestException;
import java.sql.SQLException;
import java.util.Iterator;
import java.util.List;

class ContentAuthorityCheck extends RestAPIAuthorityCheck {
   final ContentInfo contentInfo = ContentInfoImpl.getInstance();

   ContentAuthorityCheck() {
      super();
   }

   void checkAuthorityById(UserContainer userContainer, String contentId) throws Exception {
      Long organizationId = this.getOrganizationId(userContainer);
      Content content = this.contentInfo.getContentActiveVerInfo(contentId);
      boolean isTlfd = false;
      if (content == null) {
         content = this.contentInfo.getTLFDInfo(contentId);
         isTlfd = true;
      }

      if (content == null) {
         throw new BaseRestException("INVALID_PARAM", "The content does not exist : " + contentId);
      } else {
         Long contentOrgId;
         if (isTlfd) {
            contentOrgId = this.contentInfo.getTLFDOrganizationIdByGroupId(content.getGroup_id());
         } else {
            contentOrgId = this.contentInfo.getOrganizationIdByGroupId(this.contentInfo.getGroupId(contentId));
         }

         if (organizationId != contentOrgId) {
            ShareFolderInfo sInfo = ShareFolderInfoImpl.getInstance();
            Long sharedFolderId = sInfo.getShareFolderIdByContentId(contentId);
            if (sharedFolderId != null) {
               List orgList = sInfo.getOrgListByShareFolderId(sharedFolderId);
               boolean isAccess = false;
               Iterator var11 = orgList.iterator();

               while(var11.hasNext()) {
                  Long orgs = (Long)var11.next();
                  if (organizationId == orgs) {
                     isAccess = true;
                     break;
                  }
               }

               if (!isAccess) {
                  this.throwAuthException();
               }
            } else {
               this.throwAuthException();
            }
         }

      }
   }

   void checkAuthorityByGroupId(UserContainer userContainer, Long groupId) throws Exception {
      Long organizationId = this.getOrganizationId(userContainer);
      Long targetOrgId = this.contentInfo.getOrganizationIdByGroupId(groupId);
      if (targetOrgId == null) {
         targetOrgId = this.contentInfo.getTLFDOrganizationIdByGroupId(groupId);
      }

      if (organizationId != targetOrgId && !this.checkSharedFolder(organizationId, targetOrgId)) {
         this.throwAuthException();
      }

   }

   void checkPostAuthority(UserContainer userContainer, Long groupId) throws Exception {
      if (RoleUtils.isAdminRole(userContainer.getUser())) {
         this.checkAuthorityByGroupId(userContainer, groupId);
      } else {
         Group group = this.contentInfo.getGroupInfo(groupId);
         Long organizationId = this.getOrganizationId(userContainer);
         if (group == null) {
            if (this.checkSharedFolder(organizationId, groupId)) {
               return;
            }

            throw new BaseRestException("INVALID_PARAM", "The group does not exist : " + groupId);
         }

         if (group.getCreator_id() != null && group.getOrganization_id() != organizationId) {
            this.throwAuthException();
         }
      }

   }

   boolean checkSharedFolder(Long organizationId, Long groupId) throws SQLException {
      ShareFolderInfo sInfo = ShareFolderInfoImpl.getInstance();
      List orgList = sInfo.getOrgListByShareFolderId(groupId);
      boolean isAccess = false;
      Iterator var6 = orgList.iterator();

      while(var6.hasNext()) {
         Long orgId = (Long)var6.next();
         if (organizationId == orgId) {
            isAccess = true;
            break;
         }
      }

      return isAccess;
   }
}
