<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.samsung.magicinfo.framework.statistics.dao.FaceHumanBasedStatisticsDaoMapper">

<sql id="toCharSecondDurationInterval">
	TO_CHAR((A.DURATION || ' second')::interval, 'HH24:MI:SS')
</sql>
<sql id="toCharSecondDurationInterval" databaseId="mssql">
	CONVERT(VARCHAR(8), DATEADD(SECOND, DURATION, '19000101'), 8)
</sql>
<sql id="toCharSecondDurationInterval" databaseId="mysql">
	TIME_FORMAT(SEC_TO_TIME(A.DURATION),'%H:%i:%s')
</sql>

<sql id="where_deviceIdList">
	<if test="deviceIdList.length == 0">
		(DEVICE_ID='NONE' )
	</if>
	<if test="deviceIdList != null and deviceIdList.length > 0">
		<foreach item="item"  index="index" collection="deviceIdList" open="(" separator="OR" close=")">	
		DEVICE_ID = #{item}	
		</foreach>		
	</if>
</sql>

<sql id="conditionStartTimeEqToTruncYearCurrentDate">
	<choose>
	 	<when test="isThis">
	 		 AND START_TIME = DATE_TRUNC('YEAR',CURRENT_DATE)
	 	</when>
	 	<otherwise>
	 		  AND START_TIME = DATE_TRUNC('YEAR',CURRENT_DATE - interval '1 years')
	 	</otherwise>
	</choose>
</sql>
<sql id="conditionStartTimeEqToTruncYearCurrentDate" databaseId="mssql">
	<choose>
	 	<when test="isThis">
	 		 AND START_TIME = DATEADD(YEAR, DATEDIFF(YEAR, 0, GETDATE()) , 0)
	 	</when>
	 	<otherwise>
	 		  AND START_TIME = DATEADD(yy, -1, DATEADD(YEAR, DATEDIFF(YEAR, 0, GETDATE()) , 0))
	 	</otherwise>
	</choose>
</sql>

<sql id="conditionStartTimeEqToTruncYearCurrentDate" databaseId="mysql">
	<choose>
	 	<when test="isThis">
	 		 AND START_TIME = DATE_FORMAT(NOW(), '%Y-01-01')
	 	</when>
	 	<otherwise>
	 		  AND START_TIME = DATE_FORMAT(NOW() - INTERVAL 1 YEAR, '%Y-01-01')
	 	</otherwise>
	</choose>
</sql>

<sql id="where_selectedConditionList">

<foreach index="key" item="condition" collection="conditionMap">
	<bind name="safe_key" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(key)" />
    <if test="condition.size() == 0">
		AND ( ${safe_key}='NONE')
	</if>	
	<if test="condition.size() != 0">
		<foreach item="option"  index="index" collection="condition" open="AND (" separator="OR" close=")">	
			${safe_key} = #{option}	
		</foreach>	
	</if>	
</foreach>
</sql>

<sql id="truncHourStartTimeAndCastToTime">
	DATE_TRUNC('HOUR', START_TIME)::TIME
</sql>
<sql id="truncHourStartTimeAndCastToTime" databaseId="mssql">
	CONVERT(VARCHAR(8), DATEADD(HOUR, DATEDIFF(HOUR, 0, START_TIME), 0), 108)
</sql>
<sql id="truncHourStartTimeAndCastToTime" databaseId="mysql">
	DATE_FORMAT(START_TIME,'%H:00:00')
</sql>


<sql id="castStartTimeToStringDate">
	START_TIME::DATE
</sql>
<sql id="castStartTimeToStringDate" databaseId="mssql">
	CONVERT(VARCHAR(10), START_TIME, 120)
</sql>
<sql id="castStartTimeToStringDate" databaseId="mysql">
	DATE_FORMAT(START_TIME, '%Y-%m-%d')
</sql>


<sql id="truncDayCurrDateMinusOneDayAndCastToDate">
	DATE_TRUNC('DAY',CURRENT_DATE - interval '1days')::DATE
</sql>
<sql id="truncDayCurrDateMinusOneDayAndCastToDate" databaseId="mssql">
	CONVERT(VARCHAR(10), DATEADD(dd, -1, DATEADD(DAY, DATEDIFF(DAY, 0, GETDATE()) , 0)), 120)
</sql>
<sql id="truncDayCurrDateMinusOneDayAndCastToDate" databaseId="mysql">
	DATE_FORMAT(NOW() - INTERVAL 1 DAY, '%Y-%m-%d')
</sql>


<select id="getYesterdayListBy" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceHumanBasedEntity">
SELECT A.*, <include refid="toCharSecondDurationInterval"/> AS DURATION_STRING 
FROM(
	<choose>
		<when test="unit  == 'DAY'">
			<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT  SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION, ${safe_columnName}
			 FROM MI_STATISTICS_FACE_HUMAN_DAY
		</when>
		<when test="unit  == 'HOUR'">
			<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT <include refid="truncHourStartTimeAndCastToTime"/> AS TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION,  ${safe_columnName}
			 FROM MI_STATISTICS_FACE_HUMAN_HOUR 
		</when>
	</choose>
	<where>
		<include refid="where_deviceIdList"/>
		<include refid="where_selectedConditionList"/>
		AND <include refid="castStartTimeToStringDate"/> = <include refid="truncDayCurrDateMinusOneDayAndCastToDate"/>
	</where>
	<choose>
		<when test="unit  == 'DAY'">
			<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			GROUP BY  ${safe_columnName}
		</when>
		<when test="unit  == 'HOUR'">
			<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			GROUP BY START_TIME,  ${safe_columnName}
		</when>
	</choose>
) A
<if test="unit  == 'HOUR'">ORDER BY TIME_STRING</if>
</select>

<sql id="conditionStartTimeToCurrentMonth">
	<choose>
		<when test="isThis">
	 		AND START_TIME = DATE_TRUNC('MONTH',CURRENT_DATE)
	 	</when>
		<otherwise>
		 	AND START_TIME = DATE_TRUNC('MONTH',CURRENT_DATE - interval '1 months')
		</otherwise>
	</choose>
</sql>
<sql id="conditionStartTimeToCurrentMonth" databaseId="mssql">
	<choose>
		<when test="isThis">
	 		AND START_TIME = DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0)
	 	</when>
		<otherwise>
		 	AND START_TIME = DATEADD(MONTH, DATEDIFF(MONTH, 0, DATEADD(MONTH, -1, GETDATE())), 0)
		</otherwise>
	</choose>
</sql>
<sql id="conditionStartTimeToCurrentMonth" databaseId="mysql">
	<choose>
		<when test="isThis">
	 		AND START_TIME = DATE_FORMAT(NOW(), '%Y-%m-01 %00:%00:%00')
	 	</when>
		<otherwise>
		 	AND START_TIME = DATE_FORMAT(NOW() - INTERVAL 1 MONTH, '%Y-%m-01 %00:%00:%00 ')
		</otherwise>
	</choose>
</sql>


<select id="getMonthListByMonth" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceHumanBasedEntity">
<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />	
SELECT A.*, <include refid="toCharSecondDurationInterval"/> AS DURATION_STRING 
FROM(
	SELECT SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION, ${safe_columnName}
	 FROM MI_STATISTICS_FACE_HUMAN_MONTH 
	 <where>
	 	<include refid="where_deviceIdList"/>
		<include refid="where_selectedConditionList"/>
	 	<include refid="conditionStartTimeToCurrentMonth"/>
	 </where>
	 GROUP BY  ${safe_columnName}
) A
</select>

<sql id="conditionMonthStartTimeEqCurrentMonth">
	<choose>
	 	<when test="isThis">
	 		AND EXTRACT(MONTH FROM START_TIME) = EXTRACT(MONTH FROM CURRENT_DATE)
	 	</when>
	 	<otherwise>
	 		AND EXTRACT(MONTH FROM START_TIME) = EXTRACT(MONTH FROM CURRENT_DATE - interval '1 months') 
	 	</otherwise>
	</choose>
</sql>
<sql id="conditionMonthStartTimeEqCurrentMonth" databaseId="mssql">
	<choose>
	 	<when test="isThis">
	 		AND MONTH(START_TIME) = MONTH(GETDATE())
	 	</when>
	 	<otherwise>
            AND MONTH(START_TIME) = CASE WHEN MONTH(GETDATE()) = 1 THEN 12 ELSE (MONTH(GETDATE()) - 1) END
	 	</otherwise>
	</choose>
</sql>
<sql id="conditionMonthStartTimeEqCurrentMonth" databaseId="mysql">
	<choose>
	 	<when test="isThis">
	 		AND MONTH(START_TIME) = MONTH(NOW())
	 	</when>
	 	<otherwise>
	 		AND MONTH(START_TIME) = MONTH(NOW()) -1 
	 	</otherwise>
	</choose>
</sql>


<select id="getMonthListByDow" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceHumanBasedEntity">
<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
SELECT A.*, <include refid="toCharSecondDurationInterval"/> AS DURATION_STRING 
FROM(
	SELECT PLAY_DOW, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION, ${safe_columnName}
	 FROM MI_STATISTICS_FACE_HUMAN_DAY 
	 <where>
	 	<include refid="where_deviceIdList"/>
		<include refid="where_selectedConditionList"/>
		<include refid="conditionMonthStartTimeEqCurrentMonth"/>
	 </where>
	 GROUP BY PLAY_DOW, ${safe_columnName}
) A ORDER BY PLAY_DOW
</select>

<select id="getQuarterListByQuarter" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceHumanBasedEntity">
<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
SELECT A.*, <include refid="toCharSecondDurationInterval"/> AS DURATION_STRING 
FROM(
	SELECT SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION,  ${safe_columnName}
	 FROM MI_STATISTICS_FACE_HUMAN_MONTH 
	 <where>
	 	<include refid="where_deviceIdList"/>
		<include refid="where_selectedConditionList"/>
	 	<include refid="conditionYearAndQuarterStartTimeEqCurrYearAndQuarter"/>
	 </where>
	 GROUP BY ${safe_columnName}
) A
</select>

<sql id="conditionYearAndQuarterStartTimeEqCurrYearAndQuarter">
	<choose>
	 	<when test="isThis">
	 		AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE) AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE)
	 	</when>
	 	<otherwise>
	 		AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE - interval '3 months') AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE - interval '3 months')
	 	</otherwise>
	</choose>
</sql>
<sql id="conditionYearAndQuarterStartTimeEqCurrYearAndQuarter" databaseId="mssql">
	<choose>
	 	<when test="isThis">
	 		AND YEAR(START_TIME) = YEAR(GETDATE()) AND LOG_QUARTER = DATENAME(Quarter, CAST(CONVERT(VARCHAR(8), DATEADD(month, 0 ,GETDATE())) AS DATETIME))
	 	</when>
	 	<otherwise>
	 		AND YEAR(START_TIME) = YEAR(DATEADD(month, -3 ,GETDATE())) AND LOG_QUARTER = DATENAME(Quarter, CAST(CONVERT(VARCHAR(8), DATEADD(month, -3 ,GETDATE())) AS DATETIME))
	 	</otherwise>
	</choose>
</sql>
<sql id="conditionYearAndQuarterStartTimeEqCurrYearAndQuarter" databaseId="mysql">
	<choose>
	 	<when test="isThis">
	 		AND YEAR(START_TIME) = YEAR(NOW()) AND LOG_QUARTER = QUARTER(NOW())
	 	</when>
	 	<otherwise>
	 		AND YEAR(START_TIME) = YEAR(NOW()) AND LOG_QUARTER = QUARTER(NOW()) -1
	 	</otherwise>
	</choose>
</sql>


<sql id="extractMonthFromStartTime">
	extract(month from START_TIME)
</sql>
<sql id="extractMonthFromStartTime" databaseId="mssql">
	MONTH(START_TIME)
</sql>
<sql id="extractMonthFromStartTime" databaseId="mysql">
	MONTH(START_TIME)
</sql>

<sql id="monthNameFromStartTime">
	TO_CHAR(START_TIME,'Month')
</sql>
<sql id="monthNameFromStartTime" databaseId="mssql">
	DateName( month , DateAdd( month , MONTH(START_TIME) , 0 ) - 1 )
</sql>
<sql id="monthNameFromStartTime" databaseId="mysql">
	MONTHNAME(START_TIME)
</sql>

<select id="getQuarterListByMonth" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceHumanBasedEntity">
<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
SELECT A.*, <include refid="toCharSecondDurationInterval"/> AS DURATION_STRING 
FROM(
	SELECT <include refid="extractMonthFromStartTime"/> AS MONTH_ORDER, <include refid="monthNameFromStartTime"/> AS TIME_STRING, 
	SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION,  ${safe_columnName}
	 FROM MI_STATISTICS_FACE_HUMAN_MONTH 
	 <where>
	 	<include refid="where_deviceIdList"/>
		<include refid="where_selectedConditionList"/>
		<include refid="conditionYearAndQuarterStartTimeEqCurrYearAndQuarter"/>
	 </where>
	 GROUP BY TIME_STRING, MONTH_ORDER, ${safe_columnName} 
) A ORDER BY MONTH_ORDER
</select>

<select id="getQuarterListByMonth" databaseId="mssql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceHumanBasedEntity">
<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
SELECT A.*, <include refid="toCharSecondDurationInterval"/> AS DURATION_STRING 
FROM(
	SELECT MONTH_ORDER, TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION, ${safe_columnName} FROM (
		SELECT <include refid="extractMonthFromStartTime"/> AS MONTH_ORDER, <include refid="monthNameFromStartTime"/> AS TIME_STRING, 
		VIEW_COUNT, DURATION, ${safe_columnName}
		 FROM MI_STATISTICS_FACE_HUMAN_MONTH 
		 <where>
		 	<include refid="where_deviceIdList"/>
			<include refid="where_selectedConditionList"/>
			<include refid="conditionYearAndQuarterStartTimeEqCurrYearAndQuarter"/>
		 </where>
	) AS subquery
	GROUP BY TIME_STRING, MONTH_ORDER, ${safe_columnName} 
) A ORDER BY MONTH_ORDER
</select>

<select id="getYearListByYear" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceHumanBasedEntity">
<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
SELECT A.*, <include refid="toCharSecondDurationInterval"/> AS DURATION_STRING 
FROM(
	SELECT SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION,  ${safe_columnName}
	 FROM MI_STATISTICS_FACE_HUMAN_YEAR 
	 <where>
	 	<include refid="where_deviceIdList"/>
		<include refid="where_selectedConditionList"/>
	 	<include refid="conditionStartTimeEqToTruncYearCurrentDate"/>
	 </where>
	GROUP BY ${safe_columnName}
) A
</select>

<sql id="conditionYearStartTimeEqToCurrentYear">
	<choose>
	 	<when test="isThis">
	 		AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE) 
	 	</when>
	 	<otherwise>
	 		AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE - interval '1 years') 
	 	</otherwise>
	</choose>
</sql>
<sql id="conditionYearStartTimeEqToCurrentYear" databaseId="mssql">
	<choose>
	 	<when test="isThis">
	 		AND YEAR(START_TIME) = YEAR(GETDATE())
	 	</when>
	 	<otherwise>
	 		AND YEAR(START_TIME) = YEAR(GETDATE()) - 1
	 	</otherwise>
	</choose>
</sql>
<sql id="conditionYearStartTimeEqToCurrentYear" databaseId="mysql">
	<choose>
	 	<when test="isThis">
	 		AND YEAR(START_TIME) = YEAR(NOW()) 
	 	</when>
	 	<otherwise>
	 		AND YEAR(START_TIME) = YEAR(NOW()) - 1 
	 	</otherwise>
	</choose>
</sql>

<select id="getYearListByQuarter" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceHumanBasedEntity">
<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
SELECT A.*, <include refid="toCharSecondDurationInterval"/> AS DURATION_STRING 
FROM(
	SELECT LOG_QUARTER, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION,  ${safe_columnName}
	 FROM MI_STATISTICS_FACE_HUMAN_MONTH 
	 <where>
	 	<include refid="where_deviceIdList"/>
		<include refid="where_selectedConditionList"/>
	 	<include refid="conditionYearStartTimeEqToCurrentYear"/>
	 </where>
	 GROUP BY LOG_QUARTER, ${safe_columnName}
) A ORDER BY LOG_QUARTER
</select>

<select id="getWeekListByHour" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceHumanBasedEntity">
<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
SELECT A.*, <include refid="toCharSecondDurationInterval"/> AS DURATION_STRING 
FROM(
	SELECT <include refid="truncHourStartTimeAndCastToTime"/> AS TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION, ${safe_columnName}
	 FROM MI_STATISTICS_FACE_HUMAN_HOUR 
	 <where>
	 	<include refid="where_deviceIdList"/>
		<include refid="where_selectedConditionList"/>
	 	AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}
	 </where>
	 GROUP BY START_TIME, ${safe_columnName}
) A ORDER BY TIME_STRING
</select>

<select id="getWeekListByDay" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceHumanBasedEntity">
<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
SELECT A.*, <include refid="toCharSecondDurationInterval"/> AS DURATION_STRING 
FROM(
	SELECT <include refid="castStartTimeToStringDate"/> AS TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION, ${safe_columnName}
	 FROM MI_STATISTICS_FACE_HUMAN_DAY 
	 <where>
	 	<include refid="where_deviceIdList"/>
		<include refid="where_selectedConditionList"/>
	 	AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}
	 </where>
	 GROUP BY START_TIME, ${safe_columnName}
) A ORDER BY TIME_STRING
</select>

<select id="getWeekListByWeek" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceHumanBasedEntity">
<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
SELECT A.*, <include refid="toCharSecondDurationInterval"/> AS DURATION_STRING 
FROM(
	SELECT SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION,  ${safe_columnName}
	 FROM MI_STATISTICS_FACE_HUMAN_DAY 
	 <where>
	 	<include refid="where_deviceIdList"/>
		<include refid="where_selectedConditionList"/>
	 	AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}
	 </where>
	 GROUP BY ${safe_columnName}
) A
</select>

<sql id="truncDayStartTimeAndCastToDate">
	DATE_TRUNC('DAY', START_TIME)::DATE
</sql>
<sql id="truncDayStartTimeAndCastToDate" databaseId="mssql">
	CONVERT(VARCHAR(10), DATEADD(DAY, DATEDIFF(DAY, 0, START_TIME), 0), 120)
</sql>
<sql id="truncDayStartTimeAndCastToDate" databaseId="mysql">
	DATE_FORMAT(START_TIME, '%Y-%m-%d')
</sql>

<select id="getCustomList" resultType="com.samsung.magicinfo.framework.statistics.entity.content.FaceHumanBasedEntity">
SELECT A.*, <include refid="toCharSecondDurationInterval"/> AS DURATION_STRING 
FROM(
	<choose>
		<when test="unit  == 'DAY'">
			<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION, ${safe_columnName} FROM (
			SELECT <include refid="truncDayStartTimeAndCastToDate"/> AS TIME_STRING, VIEW_COUNT, DURATION, ${safe_columnName}
			FROM MI_STATISTICS_FACE_HUMAN_DAY
		</when>
		<when test="unit  == 'HOUR'">
			<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			SELECT START_TIME, TIME_STRING, SUM(VIEW_COUNT) AS VIEW_COUNT, SUM(DURATION) AS DURATION, ${safe_columnName} FROM (
			SELECT START_TIME, <include refid="truncDayStartTimeAndCastToDate"/> AS TIME_STRING, VIEW_COUNT, DURATION, ${safe_columnName}
			 FROM MI_STATISTICS_FACE_HUMAN_HOUR 
		</when>
	</choose>
	<where>
		<include refid="where_deviceIdList"/>
		<include refid="where_selectedConditionList"/>
		AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}
	</where>
	) AS subquery
	<choose>
		<when test="unit  == 'DAY'">
			<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			GROUP BY TIME_STRING, ${safe_columnName}
		</when>
		<when test="unit  == 'HOUR'">
			<bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(selectColumn)" />
			 GROUP BY START_TIME, TIME_STRING,  ${safe_columnName}
		</when>
	</choose>
) A
<choose>
	<when test="unit  == 'DAY'">
		ORDER BY TIME_STRING
	</when>
	<when test="unit  == 'HOUR'">
		ORDER BY TIME_STRING
	</when>
</choose>
</select>


</mapper>