package com.samsung.magicinfo.rc.model.api;

import com.samsung.magicinfo.rc.model.api.UserSession;
import java.util.List;

public class UserSessionBuilder {
  private String id;
  
  private String token;
  
  private String accessToken;
  
  private String refreshToken;
  
  private String from;
  
  private List<String> deviceIds;
  
  private String locale;
  
  private int sessionExpiry;
  
  public UserSessionBuilder id(String id) {
    this.id = id;
    return this;
  }
  
  public UserSessionBuilder token(String token) {
    this.token = token;
    return this;
  }
  
  public UserSessionBuilder accessToken(String accessToken) {
    this.accessToken = accessToken;
    return this;
  }
  
  public UserSessionBuilder refreshToken(String refreshToken) {
    this.refreshToken = refreshToken;
    return this;
  }
  
  public UserSessionBuilder from(String from) {
    this.from = from;
    return this;
  }
  
  public UserSessionBuilder deviceIds(List<String> deviceIds) {
    this.deviceIds = deviceIds;
    return this;
  }
  
  public UserSessionBuilder locale(String locale) {
    this.locale = locale;
    return this;
  }
  
  public UserSessionBuilder sessionExpiry(int sessionExpiry) {
    this.sessionExpiry = sessionExpiry;
    return this;
  }
  
  public UserSession build() {
    return new UserSession(this.id, this.token, this.accessToken, this.refreshToken, this.from, this.deviceIds, this.locale, this.sessionExpiry);
  }
  
  public String toString() {
    return "UserSession.UserSessionBuilder(id=" + this.id + ", token=" + this.token + ", accessToken=" + this.accessToken + ", refreshToken=" + this.refreshToken + ", from=" + this.from + ", deviceIds=" + this.deviceIds + ", locale=" + this.locale + ", sessionExpiry=" + this.sessionExpiry + ")";
  }
}
