package com.samsung.magicinfo.auth.security.state.impl;

import com.samsung.magicinfo.auth.security.AuthResource;
import com.samsung.magicinfo.auth.security.otp.OTPAuthType;
import com.samsung.magicinfo.auth.security.state.AuthState;
import com.samsung.magicinfo.auth.security.strategies.AuthStrategyContext;
import com.samsung.magicinfo.auth.security.strategies.impl.HotpAuthStrategy;
import com.samsung.magicinfo.auth.security.strategies.impl.TotpAuthStrategy;
import com.samsung.magicinfo.auth.security.strategies.model.AuthModel;

public class ActiveAuthState implements AuthState {
   public ActiveAuthState() {
      super();
   }

   public OTPAuthType auth(AuthResource resource) {
      AuthModel authModel = new AuthModel();
      authModel.setUserId(resource.getUsername());
      AuthStrategyContext authContext = null;
      if (resource.getTotp() != null) {
         authModel.setOtp(resource.getTotp());
         authContext = new AuthStrategyContext(new TotpAuthStrategy());
      } else if (resource.getHotp() != null) {
         authModel.setOtp(resource.getHotp());
         authContext = new AuthStrategyContext(new HotpAuthStrategy());
      }

      authContext.active(authModel);
      return null;
   }
}
