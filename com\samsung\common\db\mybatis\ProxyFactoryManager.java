package com.samsung.common.db.mybatis;

import org.apache.commons.proxy.ProxyFactory;

public class ProxyFactoryManager {
   private static volatile ProxyFactory instance;

   private ProxyFactoryManager() {
      super();
   }

   public static ProxyFactory getInstance() {
      if (instance == null) {
         Class var0 = ProxyFactoryManager.class;
         synchronized(ProxyFactoryManager.class) {
            if (instance == null) {
               instance = createProxyFactory();
            }
         }
      }

      return instance;
   }

   private static ProxyFactory createProxyFactory() {
      return new ProxyFactory();
   }
}
