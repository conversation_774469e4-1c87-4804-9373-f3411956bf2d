package com.samsung.magicinfo.webauthor2.webapi.resource;

import com.samsung.magicinfo.webauthor2.model.ConvertTable;
import java.io.Serializable;
import org.springframework.hateoas.Link;
import org.springframework.hateoas.Resource;

public class ConvertTableResource extends Resource<ConvertTable> implements Serializable {
  private static final long serialVersionUID = 1L;
  
  public ConvertTableResource(ConvertTable convertTable, Iterable<Link> links) {
    super(convertTable, links);
  }
  
  public ConvertTableResource(ConvertTable convertTable, Link... links) {
    super(convertTable, links);
  }
}
