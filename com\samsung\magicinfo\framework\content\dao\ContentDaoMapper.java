package com.samsung.magicinfo.framework.content.dao;

import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.entity.TemplateDisplay;
import com.samsung.magicinfo.framework.content.entity.TemplateElement;
import com.samsung.magicinfo.framework.content.entity.TemplateElementData;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.restapi.contents.model.V2AdsContentPublisherInfo;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

public interface ContentDaoMapper {
   String getContentName(String var1) throws SQLException;

   String getContentOrgCreatorId(String var1) throws SQLException;

   List getContentAllVerInfo(String var1) throws SQLException;

   Map getAdsContentActiveVersionInfo(@Param("contentId") String var1) throws SQLException;

   Content getContentActiveVerInfo(@Param("contentId") String var1) throws SQLException;

   List getContentActiveVerInfoTemporary(String var1) throws SQLException;

   Content getThumbInfoOfActiveVersion(String var1) throws SQLException;

   Map getThumbFileInfoOfActiveVersion(String var1) throws SQLException;

   Map getThumbFileInfoOfActiveVersionTemporary(String var1) throws SQLException;

   Content getContentVerInfo(@Param("contentId") String var1, @Param("versionId") Long var2) throws SQLException;

   List getSearchList(Map var1) throws SQLException;

   List getSearchListPage(Map var1) throws SQLException;

   int getSearchListCnt(Map var1) throws SQLException;

   List getAllDeletedContentList(@Param("creatorId") String var1, @Param("organizationId") Long var2) throws SQLException;

   List getAllDeletedContentListWithoutCreatorId(@Param("organizationId") Long var1, @Param("userManageGroupList") List var2) throws SQLException;

   List getAllContentList(Map var1) throws SQLException;

   List getContentList(Map var1) throws SQLException;

   List getContentListPage(Map var1) throws SQLException;

   int getContentListCnt(Map var1) throws SQLException;

   int getRejectCnt(@Param("organizationId") long var1) throws SQLException;

   ContentFile getFileInfo(@Param("fileId") String var1) throws SQLException;

   List getFilesByIds(@Param("fileIds") List var1) throws SQLException;

   ContentFile getMainFileInfo(@Param("contentId") String var1) throws SQLException;

   ContentFile getRulesetFileInfo(@Param("rulesetId") String var1) throws SQLException;

   List getMainFileInfoTemporary(@Param("contentId") String var1) throws SQLException;

   String getMainFileName(@Param("contentId") String var1) throws SQLException;

   ContentFile getThumbFileInfo(@Param("contentId") String var1) throws SQLException;

   List getContentUseInPlaylist(@Param("contentId") String var1) throws SQLException;

   List getContentUseInSchedule(@Param("contentId") String var1) throws SQLException;

   ContentFile getSfiFileInfo(@Param("contentId") String var1) throws SQLException;

   ContentFile getMainFileInfoOfTmpVer(@Param("contentId") String var1) throws SQLException;

   ContentFile getThumbFileInfoOfTempVer(@Param("contentId") String var1) throws SQLException;

   String getFileHash(@Param("fileId") String var1) throws SQLException;

   Long getFileSize(@Param("fileId") String var1) throws SQLException;

   String getFileName(@Param("fileId") String var1) throws SQLException;

   List getThumbFileList(@Param("contentId") String var1) throws SQLException;

   List getFileList(@Param("contentId") String var1) throws SQLException;

   List getFileListForApi(@Param("contentId") String var1) throws SQLException;

   List getFileListByContentAndVersion(@Param("contentId") String var1, @Param("versionId") Long var2) throws SQLException;

   List getFileListPage(@Param("contentId") String var1, @Param("versionId") Long var2, @Param("startPos") int var3, @Param("pageSize") int var4) throws SQLException;

   int getFileListCnt(@Param("contentId") String var1, @Param("versionId") Long var2) throws SQLException;

   List getActiveFileList(@Param("contentId") String var1) throws SQLException;

   List getActiveFileListByType(@Param("contentId") String var1, @Param("isThumbnail") boolean var2) throws SQLException;

   List getActiveFileListPage(@Param("contentId") String var1, @Param("startPos") int var2, @Param("pageSize") int var3) throws SQLException;

   int getActiveFileListCnt(@Param("contentId") String var1) throws SQLException;

   int existFileByIDCnt(@Param("fileId") String var1) throws SQLException;

   int numberOfExistingFileByHash(@Param("fileName") String var1, @Param("fileSize") Long var2, @Param("hashCode") String var3) throws SQLException;

   String getFileIdByHash(@Param("fileName") String var1, @Param("hashCode") String var2) throws SQLException;

   String getFileIDByHash(@Param("fileName") String var1, @Param("fileSize") Long var2, @Param("hashCode") String var3) throws SQLException;

   String getFileIDByHashCreator(@Param("fileName") String var1, @Param("fileSize") Long var2, @Param("hashCode") String var3, @Param("creatorId") String var4) throws SQLException;

   int numberOfExistingFileByIDAndHash(@Param("fileId") String var1, @Param("fileName") String var2, @Param("hashCode") String var3) throws SQLException;

   int numberOfExistActiveContentID(@Param("contentId") String var1, @Param("creatorId") String var2, @Param("organizationId") long var3) throws SQLException;

   int numberOfExistContentForCidMapping(@Param("contentId") String var1, @Param("creatorId") String var2, @Param("organizationId") long var3) throws SQLException;

   int numberOfExistContentID(@Param("contentId") String var1) throws SQLException;

   int numberOfExistContentVersion(@Param("contentId") String var1, @Param("versionId") Long var2) throws SQLException;

   int numberOfUpdatableContent(@Param("contentId") String var1) throws SQLException;

   int numberOfDeletableFile(@Param("fileId") String var1, @Param("contentId") String var2) throws SQLException;

   int numberOfDeletableFileByVersion(@Param("fileId") String var1, @Param("contentId") String var2, @Param("versionId") long var3) throws SQLException;

   int numberOfLockedContent(@Param("contentId") String var1, @Param("sessionId") String var2) throws SQLException;

   int numberOfUsedForPlaylist(@Param("contentId") String var1) throws SQLException;

   int numberOfUsedForLitePlaylist(@Param("contentId") String var1) throws SQLException;

   int setOtherAdsContentVersionInactive(@Param("contentId") String var1, @Param("versionId") Long var2) throws SQLException;

   int setOtherVersionInactive(@Param("contentId") String var1, @Param("versionId") Long var2) throws SQLException;

   int setAdsContentActiveVersion(@Param("contentId") String var1, @Param("versionId") Long var2) throws SQLException;

   int setVersionContentActive(@Param("contentId") String var1, @Param("versionId") Long var2) throws SQLException;

   List getPlaylistListUsingContent(@Param("contentId") String var1) throws SQLException;

   List getActivePlaylistListUsingContent(@Param("contentId") String var1) throws SQLException;

   List getPlaylistVersionListUsingContent(@Param("playlistId") String var1, @Param("contentId") String var2) throws SQLException;

   Long getContentMaxVer(@Param("contentId") String var1) throws SQLException;

   List getLitePlaylistListUsingContent(@Param("contentId") String var1) throws SQLException;

   Long getContentNextVer(@Param("contentId") String var1) throws SQLException;

   Long getContentActiveVer(@Param("contentId") String var1) throws SQLException;

   int deleteMapContentVersionFile(@Param("contentId") String var1, @Param("versionId") Long var2) throws SQLException;

   int deleteContentVersion(@Param("contentId") String var1, @Param("versionId") Long var2) throws SQLException;

   int addAdsContentVersionInfo(Map var1) throws SQLException;

   int addContentVersionInfo(Content var1) throws SQLException;

   int addContentInfo(Content var1) throws SQLException;

   int deleteMapGroupContent(String var1) throws SQLException;

   int addMapContentFile(@Param("contentId") String var1, @Param("versionId") Long var2, @Param("fileId") String var3) throws SQLException;

   int addMapGroupContent(@Param("contentId") String var1, @Param("groupId") Long var2) throws SQLException;

   int numberOfMapContentFile(@Param("contentId") String var1, @Param("versionId") Long var2, @Param("fileId") String var3) throws SQLException;

   int setContentInfo(@Param("contentId") String var1, @Param("contentName") String var2, @Param("contentMetaData") String var3, @Param("shareFlag") int var4, @Param("pollingInterval") int var5) throws SQLException;

   int setIsdeleteContent(@Param("contentId") String var1) throws SQLException;

   int setRootGroupToMapContentGroup(@Param("contentId") String var1, @Param("groupId") Long var2) throws SQLException;

   int deleteMaxVersionContent(@Param("contentId") String var1) throws SQLException;

   int restoreContent(@Param("contentId") String var1) throws SQLException;

   int deleteContentCompletely(@Param("contentId") String var1) throws SQLException;

   int setContentLock(@Param("contentId") String var1, @Param("sessionId") String var2) throws SQLException;

   int setContentUnlockBySessionID(@Param("sessionId") String var1) throws SQLException;

   int deleteAllContentLockData() throws SQLException;

   int deleteAllPlaylistLockData() throws SQLException;

   int setContentModifiedDate(@Param("contentId") String var1) throws SQLException;

   int setContentGroup(@Param("contentId") String var1, @Param("groupId") Long var2) throws SQLException;

   int setContentShare(@Param("contentId") String var1, @Param("shareFlag") Long var2) throws SQLException;

   int setContentMetaData(@Param("contentId") String var1, @Param("metaData") String var2) throws SQLException;

   int addFile(ContentFile var1) throws SQLException;

   int deleteFile(@Param("fileId") String var1) throws SQLException;

   int deleteFileInfoIfNoExistFile(@Param("fileId") String var1) throws SQLException;

   int setFileHashCode(@Param("fileId") String var1, @Param("hashCode") String var2) throws SQLException;

   String getHashCodeFromContentByFileName(@Param("fileName") String var1, @Param("fileType") String var2) throws SQLException;

   String getHashCodeFromContentByFileNameAndSize(@Param("fileName") String var1, @Param("fileType") String var2, @Param("fileSize") long var3) throws SQLException;

   int setFilePath(@Param("fileId") String var1, @Param("filePath") String var2) throws SQLException;

   Long getRootId(@Param("userId") String var1, @Param("organizationId") Long var2, @Param("ungrouped") Long var3) throws SQLException;

   Long getTLFDRootId(@Param("userId") String var1, @Param("organizationId") Long var2, @Param("ungrouped") Long var3) throws SQLException;

   int numberOfExistingGroupName(@Param("groupName") String var1, @Param("userId") String var2, @Param("organizationId") Long var3) throws SQLException;

   long getGroupId(@Param("groupName") String var1, @Param("userId") String var2, @Param("organizationId") long var3) throws SQLException;

   Long getGroupIdByContentId(@Param("contentId") String var1) throws SQLException;

   String getGroupName(@Param("groupId") Long var1) throws SQLException;

   Group getGroupInfo(@Param("groupId") Long var1) throws SQLException;

   List getGroupList(@Param("creatorId") String var1, @Param("organizationId") Long var2) throws SQLException;

   List getOrganizationList() throws SQLException;

   Long addGroup(Group var1) throws SQLException;

   Long addGroupTable(@Param("table") String var1, @Param("group") Group var2) throws SQLException;

   int setGroupInfo(Group var1) throws SQLException;

   int setGroupInfoTable(@Param("table") String var1, @Param("group") Group var2) throws SQLException;

   Long getCountDeletableGroup(Long var1) throws SQLException;

   int deleteGroup(Long var1) throws SQLException;

   int deleteGroupTable(@Param("table") String var1, @Param("groupId") Long var2) throws SQLException;

   List getGroupedContentIdList(Long var1) throws SQLException;

   List getChildGroupList(@Param("groupId") Long var1, @Param("organizationId") Long var2, @Param("creatorId") String var3) throws SQLException;

   List getChildGroupIdList(@Param("groupId") Long var1) throws SQLException;

   List getCmsGroupItems(@Param("pGroupId") Long var1, @Param("creatorId") String var2, @Param("groupDepth") Long var3, @Param("organizationId") Long var4) throws Exception;

   List getCmsSelfGroupItems(@Param("groupId") Long var1, @Param("creatorId") String var2, @Param("groupDepth") Long var3, @Param("organizationId") Long var4) throws Exception;

   List getAllContentByGroupIdForAuth(Map var1) throws SQLException;

   Long getMaxDepth(@Param("table") String var1) throws SQLException;

   List getDefaultContentGroup(@Param("table") String var1, @Param("creatorId") String var2, @Param("organizationId") Long var3, @Param("sortType") String var4, @Param("skipId") String var5) throws SQLException;

   List getSpecificDepthUserGroupList(@Param("table") String var1, @Param("depth") int var2, @Param("creatorId") String var3, @Param("organizationId") Long var4, @Param("sortType") String var5, @Param("skipId") String var6) throws SQLException;

   int updateVwlVersion(@Param("contentId") String var1, @Param("vwlVersion") int var2) throws SQLException;

   List getAllContentListByUser(Map var1) throws SQLException;

   List getAllContentListByType(Map var1) throws SQLException;

   int updateDatalinkLFDToContentInfo(@Param("contentId") String var1, @Param("isTemplate") String var2, @Param("pageCount") long var3) throws SQLException;

   int updateDatalinkLFDToPageCount(@Param("contentId") String var1, @Param("versionId") long var2, @Param("pageCount") long var4) throws SQLException;

   List getFtpContentSettingList() throws SQLException;

   List getFtpContentSettingByContentId(@Param("contentId") String var1) throws SQLException;

   ContentFile getContentFileInfoByFileId(@Param("fileId") String var1) throws SQLException;

   List getFileListByContentId(@Param("contentId") String var1) throws SQLException;

   Content getContentInfoByContentName(@Param("contentName") String var1) throws SQLException;

   List getVersionInfoByContentId(@Param("contentId") String var1) throws SQLException;

   void updateContentVersionInfoWithFileId(@Param("contentId") String var1, @Param("mainFileId") String var2, @Param("version") long var3) throws SQLException;

   List getCifsContentSettingList() throws SQLException;

   List getCifsContentSettingByContentId(@Param("contentId") String var1) throws SQLException;

   List getUrlContentSettingByContentId(@Param("contentId") String var1) throws SQLException;

   List getAdsContentSettingByContentId(@Param("contentId") String var1) throws SQLException;

   void updateAdsSettingAsDeleted(@Param("contentId") String var1, @Param("isDeleted") String var2) throws SQLException;

   void updateAdsSettingContentName(@Param("contentId") String var1, @Param("contentName") String var2) throws SQLException;

   int addFtpSetting(Map var1) throws SQLException;

   int addCifsSetting(Map var1) throws SQLException;

   int addUrlSetting(Map var1) throws SQLException;

   int updateUrlSetting(Map var1) throws SQLException;

   void updateFtpSettingAsDeleted(@Param("contentId") String var1, @Param("isDeleted") String var2) throws SQLException;

   void updateCifsSettingAsDeleted(@Param("contentId") String var1, @Param("isDeleted") String var2) throws SQLException;

   void updateUrlSettingAsDeleted(@Param("contentId") String var1, @Param("isDeleted") String var2) throws SQLException;

   List getCodeFile(Map var1) throws SQLException;

   List getContentIdListByContentFileId(@Param("mainFileId") String var1) throws SQLException;

   int countContentForCidMappingOfUploader(@Param("contentId") String var1, @Param("creatorId") String var2, @Param("organizationId") Long var3) throws SQLException;

   String getDeletedContentByContentId(@Param("contentId") String var1) throws SQLException;

   int addMapTemplateContent(Map var1) throws SQLException;

   String getLfdContentIdByDlkContentId(@Param("dlkContentId") String var1, @Param("constant") String var2) throws SQLException;

   List getContentIdListByDlkContentId(@Param("dlkContentId") String var1, @Param("versionId") long var2, @Param("constant") String var4) throws SQLException;

   int countExistTempateContentMapping(@Param("contentId") String var1, @Param("constant") String var2) throws SQLException;

   int updateUsedTemplateByContentId(@Param("lfdContentId") String var1, @Param("usedTemplate") String var2) throws SQLException;

   String getContentIdByTemplateThumbnailFileId(@Param("fileName") String var1, @Param("creatorId") String var2) throws SQLException;

   void updateAsTemplateByContentId(@Param("contentId") String var1, @Param("creatorId") String var2) throws SQLException;

   int updateAsTemplateByContentIdAndVersionId(@Param("contentId") String var1, @Param("isTemplate") String var2, @Param("versionId") Long var3) throws SQLException;

   List getFtpUserIdByContentId(@Param("contentId") String var1) throws SQLException;

   List getFtpIpByContentId(@Param("contentId") String var1) throws SQLException;

   List getFtpPathByContentId(@Param("contentId") String var1) throws SQLException;

   List getCifsUserIdByContentId(@Param("contentId") String var1) throws SQLException;

   List getCifsIpByContentId(@Param("contentId") String var1) throws SQLException;

   List getCifsPathByContentId(@Param("contentId") String var1) throws SQLException;

   int updateContentVersionInfoByContentId(@Param("totalSize") Long var1, @Param("contentId") String var2) throws SQLException;

   int updateFtpSettingByContentId(Map var1) throws SQLException;

   int updateCifsSettingByContentId(Map var1) throws SQLException;

   int deleteFileByFileNameByContentId(@Param("fileId") String var1) throws SQLException;

   int deleteMapContentFile(@Param("contentId") String var1, @Param("fileId") String var2) throws SQLException;

   List selectForDeleteFileByFileNameByContentId(@Param("contentId") String var1, @Param("fileName") String var2) throws SQLException;

   List getContentByTemplateId(@Param("templateId") String var1, @Param("contentType") String var2) throws SQLException;

   List getDlkContentsIncludedElementContentId(@Param("contentId") String var1, @Param("contentType") String var2) throws SQLException;

   int addTemplateElement(@Param("contentId") String var1, @Param("versionId") long var2, @Param("temp") TemplateElement var4) throws SQLException;

   int addTemplateDisplaySize(@Param("contentId") String var1, @Param("versionId") long var2, @Param("displayWidth") float var4, @Param("displayHeight") float var5) throws SQLException;

   TemplateDisplay getTemplateDisplaySize(@Param("contentId") String var1, @Param("versionId") long var2) throws SQLException;

   List getTemplateElementList(@Param("contentId") String var1, @Param("versionId") long var2) throws SQLException;

   List getTemplateElementDataList(@Param("contentId") String var1, @Param("versionId") long var2) throws SQLException;

   int addTemplateElementData(@Param("lftContentId") String var1, @Param("lftVersionId") long var2, @Param("dlkContentId") String var4, @Param("dlkVersionId") long var5, @Param("temp") TemplateElementData var7) throws SQLException;

   int setTemplateContentParsed(@Param("contentId") String var1, @Param("versionId") long var2) throws SQLException;

   int setContentFileSize(@Param("fileId") String var1, @Param("fileSize") long var2) throws SQLException;

   int setContentTotalSize(@Param("contentId") String var1, @Param("versionId") long var2, @Param("totalSize") long var4) throws SQLException;

   int getContentParsingState(@Param("contentId") String var1) throws SQLException;

   List getAllTemplateElementDataList(@Param("dlkContentId") String var1, @Param("dlkVersionId") long var2) throws SQLException;

   void updateThumbnailIdOfDlkByContentId(@Param("templateContentId") String var1, @Param("versionId") int var2, @Param("contentId") String var3) throws SQLException;

   List getDlkContentIdByTemplateId(@Param("templateContentId") String var1) throws SQLException;

   List getFileInfoByContentIdVersionId(@Param("contentId") String var1, @Param("versionId") int var2) throws SQLException;

   List getActiveVersionByContentId(@Param("contentId") String var1) throws SQLException;

   List getMediaTypeByContentId(@Param("contentId") String var1) throws SQLException;

   void updateHashCodeByMainFileId(@Param("dlkMainFileId") String var1, @Param("fileSize") long var2, @Param("hashCode") String var4) throws SQLException;

   void updateVersionAndMainFileIdInContentVersionInfo(@Param("version") long var1, @Param("mainFileId") String var3, @Param("contentId") String var4) throws SQLException;

   List getFtpPasswordByContentId(@Param("contentId") String var1) throws SQLException;

   List getCifsPasswordByContentId(@Param("contentId") String var1) throws SQLException;

   List getDlkContentIdByIputDataContentId(@Param("iputDataContentId") String var1) throws SQLException;

   List getCreatorIdByContentId(@Param("contentId") String var1) throws SQLException;

   List getIsReadyForNextFtpThread(@Param("contentId") String var1) throws SQLException;

   void setIsReadyForNextFtpThread(@Param("isReady") String var1, @Param("contentId") String var2) throws SQLException;

   List getIsReadyForNextCifsThread(@Param("contentId") String var1) throws SQLException;

   void setIsReadyForNextCifsThread(@Param("isReady") String var1, @Param("contentId") String var2) throws SQLException;

   List getTempContentList() throws SQLException;

   int deleteTempContentCompletely(@Param("contentId") String var1) throws SQLException;

   int setContentContentUnlock() throws SQLException;

   int setPlaylistContentUnlock() throws SQLException;

   int setContentUnlock(@Param("contentId") String var1, @Param("sessionId") String var2) throws SQLException;

   void setIsActive(@Param("contentId") String var1, @Param("isActive") String var2) throws SQLException;

   void setApprovalStatus(@Param("contentId") String var1, @Param("approvalStatus") String var2, @Param("approvalOpinion") String var3) throws SQLException;

   int setTemplateElementDataVersionUp(@Param("newDlkVersionId") Long var1, @Param("dlkContentId") String var2, @Param("dlkVersionId") Long var3) throws SQLException;

   int setMappingDlkContentVersionUp(@Param("contentId") String var1, @Param("newVersionId") Long var2, @Param("versionId") Long var3) throws SQLException;

   List getMediaTypeByDeviceType(@Param("deviceType") String var1) throws SQLException;

   List getAllDeviceType() throws SQLException;

   List checkFileTypeByDeviceTypeAndContendID(@Param("deviceType") String var1, @Param("deviceTypeVersion") float var2, @Param("contentId") String var3) throws SQLException;

   int setAMSLastMemory(@Param("gender") String var1, @Param("age") String var2, @Param("contentId") String var3) throws SQLException;

   List getAMSLastMemory(@Param("contentId") String var1) throws SQLException;

   List isDelete(String var1) throws SQLException;

   int update_UpdateFileSize(@Param("fileSize") Long var1, @Param("fileID") String var2) throws SQLException;

   void update2_UpdateFileSize(@Param("totalSize") Long var1, @Param("fileID") String var2) throws SQLException;

   Long getTotalSize(@Param("fileID") String var1) throws SQLException;

   String getFilePath(@Param("fileID") String var1) throws SQLException;

   int setContentActive(@Param("contentID") String var1, @Param("versionID") Long var2) throws SQLException;

   int setDeviceTypeVersionByMediaType(@Param("device_type_version") float var1, @Param("content_id") String var2) throws SQLException;

   Long getRoot_GroupId(@Param("groupId") long var1) throws SQLException;

   int updateContentVersionInfoWithFileIdContentIdVersion(@Param("contentId") String var1, @Param("version") long var2) throws SQLException;

   String getMainFileIdFromContentId(@Param("contentId") String var1) throws SQLException;

   int updateVwlModelCountInfo(@Param("modelCountInfo") String var1, @Param("content_id") String var2) throws SQLException;

   String getModelCountInfo(@Param("content_id") String var1) throws SQLException;

   int updateMultiVWL(@Param("content_id") String var1) throws SQLException;

   int addMapVwlDevice(@Param("content_id") String var1, @Param("deivce_group_id") Long var2) throws SQLException;

   Map getMappingDevicesFilePath(@Param("content_id") String var1) throws SQLException;

   Map getMappingDeviceInformation(@Param("contentID") String var1) throws SQLException;

   List getMediaTypeByDeviceTypeAndVersion(@Param("deviceType") String var1, @Param("deviceTypeVersion") Float var2) throws SQLException;

   List getFileTypeByDeviceTypeAndVersion(@Param("deviceType") String var1, @Param("deviceTypeVersion") Float var2) throws SQLException;

   List getDeviceGroupListByMultiVwlContentId(@Param("content_id") String var1) throws SQLException;

   Map getContentPriority(@Param("content_id") String var1) throws SQLException;

   List getContentIdListByFileAndContentIds(@Param("mainFileId") String var1, @Param("content_id") String var2) throws SQLException;

   boolean isUsedAfterVersion(@Param("mainFileId") String var1, @Param("versionId") long var2) throws SQLException;

   int isUsedThumbAfterVersion(@Param("thumbFileId") String var1, @Param("versionId") long var2) throws SQLException;

   List getFileListByContentIdAndVersion(@Param("contentId") String var1, @Param("versionId") long var2) throws SQLException;

   void setDeleteLock(@Param("fileID") String var1, @Param("deleteLock") String var2) throws SQLException;

   String getDeleteLock(@Param("fileID") String var1) throws SQLException;

   int setVersionId(@Param("contentId") String var1, @Param("oldVersionId") long var2, @Param("newVersionId") long var4) throws SQLException, ConfigException, Exception;

   void deleteContentFromDLK(@Param("contentId") String var1) throws SQLException;

   List getPlaylistInfoUsingContent(@Param("contentId") String var1) throws SQLException;

   void deleteContentIdFromDlkContentIdMap(String var1) throws SQLException;

   long getLastestDlkVersionId(@Param("contentId") String var1) throws SQLException;

   long getLastestDlkDataVersionId(@Param("contentId") String var1) throws SQLException;

   long deleteOldVersionContent(@Param("contentId") String var1, @Param("maxVersionId") long var2) throws SQLException;

   List getLitePlaylistInfoUsingContent(@Param("contentId") String var1) throws SQLException;

   int isExistMapVersionfile(@Param("contentId") String var1, @Param("versionId") long var2, @Param("fileId") String var4) throws Exception;

   List getContentInfoByContentIdAndVersion(@Param("contentId") String var1, @Param("versionId") long var2) throws SQLException;

   int getUsedContentCount(@Param("organizationId") long var1) throws SQLException;

   int getUnapprovedContentCnt(@Param("organizationId") long var1) throws SQLException;

   int getUnapprovedContentCnt() throws SQLException;

   String getContentActiveVerVwtId(@Param("contentId") String var1) throws SQLException;

   List getcontentIdfromTag(@Param("tagList") String[] var1, @Param("tagInputType") String var2) throws SQLException;

   List getcontentIdfromCategory(@Param("categoryList") String[] var1) throws SQLException;

   List getCategorywithTagFilter(@Param("categoryList") String[] var1, @Param("tagList") String[] var2, @Param("tagInputType") String var3) throws SQLException;

   List getContentListWithThumbnailFromTagId(@Param("tagId") long var1) throws SQLException;

   List getContentListWithThumbnailFromTagBoolean(@Param("tagId") long var1, @Param("tagConditionId") long var3) throws SQLException;

   List getContentListWithThumbnailFromTagNumber(@Param("tagId") long var1, @Param("tagConditionEqual") String[] var3, @Param("tagConditionUp") String[] var4, @Param("tagConditionDown") String[] var5) throws SQLException;

   List getTagContentList(@Param("tagId") long var1) throws SQLException;

   List getTagFromContentId(@Param("contentId") String var1) throws SQLException;

   List getContentApproverListByContentId(@Param("contentId") String var1) throws SQLException;

   List getContentApproverInfoByUserId(@Param("userId") String var1) throws SQLException;

   int addContentApproverMap(@Param("contentId") String var1, @Param("userId") String var2) throws SQLException;

   int deleteContentApproverMap(@Param("contentId") String var1, @Param("userId") String var2) throws SQLException;

   int deleteContentApproverMapByContentId(@Param("contentId") String var1) throws SQLException;

   boolean setThumbnailMap(@Param("contentId") String var1, @Param("fileId") String var2, @Param("idx") long var3, @Param("mode") String var5) throws SQLException;

   List getMovieThumbnails(@Param("contentId") String var1, @Param("mode") String var2) throws SQLException;

   boolean deleteContentApproverMapByUserId(@Param("userId") String var1) throws SQLException;

   List getThumbMovieFileList(@Param("contentId") String var1) throws SQLException;

   boolean deleteThumbMovieMap(@Param("contentId") String var1) throws SQLException;

   String getThumbIdByMainFileId(@Param("main_file_id") String var1) throws SQLException;

   int getTLFDListCnt(Map var1) throws SQLException;

   List getTLFDListPage(Map var1) throws SQLException;

   List getTLFDGroupList(@Param("creatorId") String var1, @Param("organizationId") Long var2, @Param("userManageGroupList") List var3) throws SQLException;

   List getTLFDChildGroupList(@Param("groupId") Long var1, @Param("organizationId") Long var2, @Param("creatorId") String var3, @Param("userManageGroupList") List var4) throws SQLException;

   List getTLFDGroupIdsByOrgId(@Param("orgId") long var1) throws SQLException;

   Long getTLFDOrganizationIdByGroupId(@Param("groupId") long var1) throws SQLException;

   List getTLFDListByGroupId(@Param("groupId") long var1) throws SQLException;

   int addMapGroupTLFD(@Param("contentId") String var1, @Param("groupId") Long var2) throws SQLException;

   int setTLFDGroup(@Param("contentId") String var1, @Param("groupId") Long var2) throws SQLException;

   Content getTLFDInfo(@Param("contentID") String var1) throws SQLException;

   Long getTLFDRoot_GroupId(@Param("groupId") long var1) throws SQLException;

   int addTLFDGroup(@Param("groupId") Long var1, @Param("groupName") String var2, @Param("pGroupId") Long var3, @Param("groupDepth") Long var4, @Param("creatorId") String var5, @Param("organizationId") Long var6) throws SQLException;

   List getSupportedDeviceTypeByContentType(@Param("contentType") String var1) throws SQLException;

   boolean updateContentForStartPageRefreshInterval(@Param("content") Content var1) throws SQLException;

   int numberOfSyncPlayUsingContent(@Param("contentId") String var1) throws SQLException;

   List getAllContentGroups(@Param("groupId") long var1) throws SQLException;

   Content getThumbnailByFileId(@Param("fileId") String var1) throws SQLException;

   Map getThumbnailByThumbnailFileId(@Param("thumbnailFileId") String var1) throws SQLException;

   List getShareFolderList(@Param("groupId") long var1) throws SQLException;

   List getContentPollingHistory(@Param("contentId") String var1) throws SQLException;

   int isExistDefaultGroup(@Param("creator_id") String var1, @Param("organization_id") long var2) throws SQLException;

   long getDefaultContentGroupId(@Param("creator_id") String var1, @Param("organization_id") String var2) throws SQLException;

   int addPollingInfo(Map var1) throws SQLException;

   int addPollingFileInfo(Map var1) throws SQLException;

   int deletePollingInfo(@Param("contentId") String var1) throws SQLException;

   int deletePollingFileInfo(@Param("contentId") String var1) throws SQLException;

   int deleteOldPollingInfo(@Param("contentId") String var1) throws SQLException;

   int deleteOldPollingFileInfo(@Param("contentId") String var1) throws SQLException;

   String getOneContentByFileId(@Param("fileId") String var1) throws SQLException;

   String getFileIdFromContentByFileNameAndSize(@Param("contentId") String var1, @Param("fileName") String var2, @Param("fileSize") long var3) throws SQLException;

   List getPollableCifsContentSettingList() throws SQLException;

   List getPollableFtpContentSettingList() throws SQLException;

   void setIsReadyForAllCifsThread(@Param("isReady") String var1) throws SQLException;

   void setIsReadyForAllFtpThread(@Param("isReady") String var1) throws SQLException;

   int getCntContentByOrganizationId(@Param("organizationId") Long var1) throws SQLException;

   void changeGroupIdOf_MI_CMS_MAP_GROUP_CONTENT(@Param("groupId") Long var1, @Param("fromUserId") String var2, @Param("organizationId") Long var3) throws SQLException;

   void changeCreatorIdOf_MI_CMS_INFO_CONTENT(@Param("fromUserId") String var1, @Param("toUserId") String var2, @Param("organizationId") Long var3) throws SQLException;

   void updateRulesetGroup(@Param("current_creator") String var1, @Param("new_creator") String var2) throws SQLException;

   void changeCreatorIdOf_MI_CMS_INFO_CONTENT_VERSION(@Param("fromUserId") String var1, @Param("toUserId") String var2, @Param("organizationId") Long var3) throws SQLException;

   void changeCreatorIdOf_MI_CMS_INFO_FILE(@Param("fromUserId") String var1, @Param("toUserId") String var2, @Param("organizationId") Long var3) throws SQLException;

   int deleteGroupByCreatorId(@Param("creatorId") String var1) throws SQLException;

   long getCntAllContents(@Param("creatorId") String var1, @Param("organizationId") Long var2) throws SQLException;

   String getPlayTimeOfLftByDlk(@Param("contentId") String var1) throws SQLException;

   boolean setContentPlayTime(@Param("contentId") String var1, @Param("playTime") String var2) throws SQLException;

   boolean setExpirationDate(@Param("contentId") String var1, @Param("expirationDate") String var2) throws SQLException;

   List getExpiredContentList(@Param("curDate") String var1) throws SQLException;

   int countContentApproverMap(@Param("contentId") String var1, @Param("userId") String var2) throws SQLException;

   List getGroupListByOrganizationId(@Param("organizationId") Long var1) throws SQLException;

   List getTLFDGroupListByOrgId(@Param("organizationId") Long var1) throws SQLException;

   List getContentIdListByContentName(@Param("contentNameList") String[] var1) throws SQLException;

   List getContentIdListByRegex(@Param("regex") String var1) throws SQLException;

   List getContentGroupBySearch(@Param("searchText") String var1, @Param("organizationId") long var2, @Param("userId") String var4) throws SQLException;

   List getTLFDGroupBySearch(@Param("searchText") String var1, @Param("organizationId") Long var2) throws SQLException;

   List getParentsGroupList(@Param("pGroupId") int var1) throws SQLException;

   Long getOrganizationIdByGroupId(@Param("groupId") Long var1) throws SQLException;

   int isExistTLFDGroup(@Param("groupId") Long var1) throws SQLException;

   List getContentIdsByFileId(@Param("fileId") String var1) throws SQLException;

   List getSubGroupList(@Param("groupId") Long var1, @Param("organizationId") Long var2) throws SQLException;

   Group getTLFDGroupInfo(@Param("groupId") Long var1) throws SQLException;

   int V2GetUnapprovedContentCnt() throws SQLException;

   int V2GetUnapprovedContentCnt(@Param("organizationId") Long var1) throws SQLException;

   int getAllContentCount() throws SQLException;

   List getContentCountByContentType() throws SQLException;

   List getContentCountByContentResolution() throws SQLException;

   int getContentGroupTotalCount() throws SQLException;

   List getValueIdsByContentIdAndIndexId(@Param("contentId") String var1, @Param("indexId") String var2) throws SQLException;

   List getAssignedAdvertisementByContentId(@Param("contentId") String var1, @Param("type") String var2) throws SQLException;

   int assignAdvertisement(@Param("contentId") String var1, @Param("indexId") String var2, @Param("valueId") Long var3) throws SQLException;

   int removeAssignedAdvertisement(@Param("contentId") String var1, @Param("indexId") String var2, @Param("valueId") Long var3) throws SQLException;

   int addProductCodeHistory(@Param("contentId") String var1, @Param("historyId") String var2) throws SQLException;

   int addProductCodeHistoryValue(@Param("historyId") String var1, @Param("valueId") Long var2, @Param("value") String var3) throws SQLException;

   List getContentIdListByMappedValueId(@Param("valueId") Long var1) throws SQLException;

   int deleteProductCodeHistoryByDate(@Param("date") String var1) throws SQLException;

   List getProductCodeHistoryByContentId(@Param("contentId") String var1) throws SQLException;

   List getProductCodeHistoryValueListByHistoryId(@Param("historyId") String var1) throws SQLException;

   int getContentFileCount(@Param("type") String var1) throws SQLException;

   List getUnapprovedContentIdList(@Param("organizationId") Long var1) throws SQLException;

   List getAdsContentPublisherInfoSuggestionListByUser(@Param("userId") String var1, @Param("count") int var2) throws SQLException;

   V2AdsContentPublisherInfo getPublisherInfoById(@Param("publisherId") String var1, @Param("userId") String var2) throws SQLException;

   String getAdUnitIdById(@Param("adUnitId") String var1, @Param("userId") String var2) throws SQLException;

   List getAdsContentAdUnitIdSuggestionListByUser(@Param("userId") String var1, @Param("count") int var2) throws SQLException;

   int existPublisherInfoByPublisherId(@Param("publisherId") String var1, @Param("userId") String var2) throws SQLException;

   int existAdUnitIdById(@Param("adUnitId") String var1, @Param("userId") String var2) throws SQLException;

   boolean updateAdsContentPublisherSuggestionInfo(@Param("publisherInfo") V2AdsContentPublisherInfo var1, @Param("userId") String var2) throws SQLException;

   boolean updateAdsContentAdUnitIdSuggestionInfo(@Param("adUnitId") String var1, @Param("userId") String var2) throws SQLException;

   int addAdsContentPublisherInfoSuggestion(@Param("publisherInfo") V2AdsContentPublisherInfo var1, @Param("userId") String var2) throws SQLException;

   int addAdsContentAdUnitIdSuggestion(@Param("adUnitId") String var1, @Param("userId") String var2) throws SQLException;

   int deletePublisherInfoById(@Param("publisherId") String var1, @Param("userId") String var2) throws SQLException;

   int deleteAdUnitIdInfoById(@Param("adUnitId") String var1, @Param("userId") String var2) throws SQLException;

   int getCountByOrganId(@Param("organizationId") Long var1) throws SQLException;
}
