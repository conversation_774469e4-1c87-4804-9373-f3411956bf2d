package com.samsung.magicinfo.webauthor.ftp;

import com.samsung.magicinfo.webauthor.ftp.FtpClientConfiguration;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;

public class ConfigurationBuilder {
  private final int ftpPort;
  
  private String controlEncoding = "UTF-8";
  
  private int dataTimeout = 60;
  
  private int bufferSize = 1048576;
  
  private boolean remoteVerificationEnabled = false;
  
  private boolean sslEnabled = true;
  
  public ConfigurationBuilder(int ftpPort) {
    this.ftpPort = ftpPort;
  }
  
  public ConfigurationBuilder controlEncoding(String encoding) {
    this.controlEncoding = encoding;
    return this;
  }
  
  public ConfigurationBuilder dataTimeout(int timeout) {
    this.dataTimeout = timeout;
    return this;
  }
  
  public ConfigurationBuilder bufferSize(int size) {
    this.bufferSize = size;
    return this;
  }
  
  public ConfigurationBuilder remoteVerificationEnabled(boolean verificationEnabled) {
    this.remoteVerificationEnabled = verificationEnabled;
    return this;
  }
  
  public ConfigurationBuilder sslEnabled(boolean isEnabled) {
    this.sslEnabled = isEnabled;
    return this;
  }
  
  public FtpClientConfiguration getFtpClient() throws IOException, NoSuchAlgorithmException {
    return new FtpClientConfiguration(this, null);
  }
}
