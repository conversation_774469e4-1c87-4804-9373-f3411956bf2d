package com.samsung.magicinfo.restapi.setting.service;

import com.samsung.common.utils.DocumentUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfo;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfoImpl;
import com.samsung.magicinfo.framework.setup.dao.RmServerDao;
import com.samsung.magicinfo.framework.setup.entity.RmServerEntity;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.restapi.setting.model.RemoteServerListFilter;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.ConnectException;
import java.net.SocketException;
import java.net.URL;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.net.ssl.HttpsURLConnection;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXParseException;

@Service
public class V2RemoteServerServiceImpl implements V2RemoteServerService {
   private static final int MAX_RM_SERVER = 100;
   RmServerDao dao = new RmServerDao();

   public V2RemoteServerServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public RmServerEntity getRmServer(String serverName) throws SQLException {
      return this.dao.getRmServerInfo(serverName);
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public Integer addRmServer(RmServerEntity rmServerEntity) throws SQLException, ConfigException {
      int result = this.dao.addRmServer(rmServerEntity);
      if (result > 0) {
         this.setDeviceRmServerTriggering();
      }

      return null;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public Integer updateRmServer(RmServerEntity rmServerEntity) throws SQLException {
      int result = this.dao.updateRmServer(rmServerEntity);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public Integer deleteRmServer(String serverName) throws SQLException {
      int result = this.dao.deleteRmServer(serverName);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public V2PageResource getRmServerList(RemoteServerListFilter filter) throws Exception {
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map infoMap = serverSetupDao.getServerInfoByOrgId(0L);
      Boolean rmMonEnable = (Boolean)infoMap.get("EXT_SERVER_RM_MON_ENABLE");
      Map condition = new HashMap();
      int startIndex = 1;
      int pageSize = 10;
      if (filter != null) {
         condition.put("searchText", filter.getSearch_text());
         condition.put("sortColumn", filter.getOrderCol());
         condition.put("sortOrder", filter.getOrderDir());
      }

      if (filter != null && filter.getStartIndex() != null) {
         startIndex = Integer.parseInt(filter.getStartIndex());
      }

      if (filter != null && filter.getPageSize() != null) {
         pageSize = Integer.parseInt(filter.getPageSize());
      }

      List list = this.dao.getRmServerListPage(condition, startIndex, pageSize);
      if (list != null) {
         Iterator var9 = list.iterator();

         label34:
         while(true) {
            while(true) {
               if (!var9.hasNext()) {
                  break label34;
               }

               RmServerEntity entity = (RmServerEntity)var9.next();
               if (rmMonEnable != null && rmMonEnable) {
                  entity.setStatus(this.getRMStatusByJob(entity));
               } else {
                  entity.setStatus(this.getRemoteServerState(entity));
               }
            }
         }
      }

      Integer totalCount = this.dao.getRmServerListCnt(condition);
      V2PageResource resource = V2PageResource.createPageResource(list, totalCount);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public List getRmServerList() throws Exception {
      List retList = null;
      retList = this.dao.getRmServerListPage((Map)null, 1, 100);
      return retList;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public String getRmServerMOString() {
      StringBuffer rtn = new StringBuffer("");

      try {
         List rmServerEntityList = this.getRmServerList();
         if (rmServerEntityList == null) {
            return rtn.toString();
         }

         for(int i = 0; i < rmServerEntityList.size(); ++i) {
            if (i > 0) {
               rtn.append("@");
            }

            rtn.append(((RmServerEntity)rmServerEntityList.get(i)).getServer_name() == null ? "" : ((RmServerEntity)rmServerEntityList.get(i)).getServer_name()).append(";");
            rtn.append(((RmServerEntity)rmServerEntityList.get(i)).getIp_address() == null ? "" : ((RmServerEntity)rmServerEntityList.get(i)).getIp_address()).append(";");
            rtn.append(((RmServerEntity)rmServerEntityList.get(i)).getPort() == null ? "8080" : ((RmServerEntity)rmServerEntityList.get(i)).getPort()).append(";");
            rtn.append(((RmServerEntity)rmServerEntityList.get(i)).getUse_ssl() == null ? "0" : (((RmServerEntity)rmServerEntityList.get(i)).getUse_ssl() ? "1" : "0")).append(";");
         }
      } catch (Exception var4) {
         return rtn.toString();
      }

      return rtn.toString();
   }

   public String getRemoteServerState(RmServerEntity rmEntity) throws Exception {
      DocumentBuilderFactory factory = DocumentUtils.getDocumentBuilderFactoryInstance();
      DocumentBuilder builder = factory.newDocumentBuilder();
      HttpClient httpclient = new DefaultHttpClient();
      String rmserver_status = null;
      String return_code = null;
      String rmServer = null;
      if (rmEntity.getPrivate_mode()) {
         if (rmEntity.getPrivate_ssl()) {
            rmServer = "https://" + rmEntity.getPrivate_ip_address() + ":" + rmEntity.getPrivate_port() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
         } else {
            rmServer = "http://" + rmEntity.getPrivate_ip_address() + ":" + rmEntity.getPrivate_port() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
         }
      } else if (rmEntity.getUse_ssl()) {
         rmServer = "https://" + rmEntity.getIp_address() + ":" + rmEntity.getPort() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
      } else {
         rmServer = "http://" + rmEntity.getIp_address() + ":" + rmEntity.getPort() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
      }

      HttpPost httpget;
      BasicHttpParams httpParams;
      HttpResponse Rmserver_response;
      HttpEntity entity;
      String line;
      if (!rmEntity.getPrivate_mode() && rmEntity.getUse_ssl() || rmEntity.getPrivate_mode() && rmEntity.getPrivate_ssl()) {
         URL url = new URL(rmServer);
         SecurityUtils.trustAllCertificates();
         HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
         conn.setConnectTimeout(1000);
         httpget = null;
         httpParams = null;
         Rmserver_response = null;

         try {
            conn.connect();
            conn.setInstanceFollowRedirects(true);
            InputStream in = conn.getInputStream();
            InputStreamReader isr = new InputStreamReader(in);
            BufferedReader reader = new BufferedReader(isr);
            entity = null;

            String line;
            for(line = new String(); (line = reader.readLine()) != null; line = line + line) {
            }

            reader.close();
            Document doc = builder.parse(new InputSource(new StringReader(line)));
            doc.getDocumentElement().normalize();
            NodeList headNodeList = doc.getElementsByTagName("response");
            Element subItem = (Element)headNodeList.item(0);
            return_code = subItem.getAttribute("code");
            if (return_code.equals("0")) {
               rmserver_status = "ON";
            } else {
               rmserver_status = "OFF";
            }

            isr.close();
            in.close();
         } catch (Exception var29) {
            rmserver_status = "OFF";
         }
      } else {
         BufferedReader rd = null;
         InputStreamReader isr = null;

         try {
            new URL(rmServer);
            httpget = new HttpPost(rmServer);
            httpParams = new BasicHttpParams();
            HttpConnectionParams.setConnectionTimeout(httpParams, 1000);
            httpclient = new DefaultHttpClient(httpParams);
            Rmserver_response = httpclient.execute(httpget);
            entity = Rmserver_response.getEntity();
            if (entity != null) {
               isr = new InputStreamReader(Rmserver_response.getEntity().getContent());
               rd = new BufferedReader(isr);
               line = null;

               String resultXml;
               for(resultXml = new String(); (line = rd.readLine()) != null; resultXml = resultXml + line) {
               }

               Document doc = builder.parse(new InputSource(new StringReader(resultXml)));
               doc.getDocumentElement().normalize();
               NodeList headNodeList = doc.getElementsByTagName("response");
               Element subItem = (Element)headNodeList.item(0);
               return_code = subItem.getAttribute("code");
               if (return_code.equals("0")) {
                  rmserver_status = "ON";
               } else {
                  rmserver_status = "OFF";
               }

               rd.close();
               rd = null;
               isr.close();
               isr = null;
            }

            httpget.abort();
            httpclient.getConnectionManager().shutdown();
         } catch (ClientProtocolException var30) {
            rmserver_status = "OFF";
         } catch (IllegalStateException var31) {
            rmserver_status = "OFF";
         } catch (ConnectException var32) {
            rmserver_status = "OFF";
         } catch (ConnectTimeoutException var33) {
            rmserver_status = "OFF";
         } catch (SocketException var34) {
            rmserver_status = "OFF";
         } catch (SAXParseException var35) {
            rmserver_status = "OFF";
         } catch (Exception var36) {
            rmserver_status = "OFF";
         } finally {
            if (rd != null) {
               rd.close();
            }

            if (isr != null) {
               isr.close();
            }

            httpclient.getConnectionManager().shutdown();
         }
      }

      return rmserver_status;
   }

   public boolean setDeviceRmServerTriggering() {
      try {
         MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
         MonitoringManagerInfo dao = MonitoringManagerInfoImpl.getInstance("PREMIUM");
         DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
         Map connectionInfoMap = null;
         String deviceId = null;
         int sendCount = 0;
         int len = false;
         List connectionInfo = dao.getConnectionInfo();
         int len = connectionInfo.size();
         if (len > 0) {
            this.getRmServerMOString();
         }

         for(int i = 0; i < len; ++i) {
            connectionInfoMap = (Map)connectionInfo.get(i);
            deviceId = (String)connectionInfoMap.get("device_id");
            if (monMgr.isConnected(deviceId)) {
               DeviceSystemSetupConf info = new DeviceSystemSetupConf();
               info.setDevice_id(deviceId);
               confManager.reqSetSystemSetupToDevice(info, "");
               ++sendCount;
            }

            if (sendCount % 20 == 0) {
               Thread.sleep(1L);
            }
         }
      } catch (Exception var11) {
      }

      return true;
   }

   private String getRMStatusByJob(RmServerEntity rmEntity) throws Exception {
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Integer errCnt = 0;
      Map infoMap = serverSetupDao.getServerInfoByOrgId(0L);
      Integer errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
      errCnt = serverSetupDao.getExternalServerErrCount("RM", rmEntity.getIp_address());
      if (errThresholdCnt != null && errCnt != null) {
         return errThresholdCnt <= errCnt ? "OFF" : "ON";
      } else {
         return "ERROR";
      }
   }
}
