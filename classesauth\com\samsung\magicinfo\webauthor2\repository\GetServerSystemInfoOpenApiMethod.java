package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.OpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.model.ServerSystemInfoData;
import com.samsung.magicinfo.webauthor2.repository.model.ServerSystemInfoDataResponse;
import java.util.HashMap;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetServerSystemInfoOpenApiMethod extends OpenApiMethod<ServerSystemInfoData, ServerSystemInfoDataResponse> {
  private final String userId;
  
  private final String token;
  
  public GetServerSystemInfoOpenApiMethod(RestTemplate restTemplate, String userId, String token) {
    super(restTemplate);
    this.userId = userId;
    this.token = token;
  }
  
  protected String getOpenApiClassName() {
    return "CommonSettingService";
  }
  
  protected String getOpenApiMethodName() {
    return "getSystemInfo";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", this.userId);
    vars.put("token", this.token);
    return vars;
  }
  
  Class<ServerSystemInfoDataResponse> getResponseClass() {
    return ServerSystemInfoDataResponse.class;
  }
  
  ServerSystemInfoData convertResponseData(ServerSystemInfoDataResponse responseData) {
    ServerSystemInfoData resultData = responseData.getResponseClass();
    return resultData;
  }
}
