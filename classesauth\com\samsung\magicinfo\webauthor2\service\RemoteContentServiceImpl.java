package com.samsung.magicinfo.webauthor2.service;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.repository.MagicInfoRemoteContentException;
import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.repository.RemoteContentRepository;
import com.samsung.magicinfo.webauthor2.service.RemoteContentService;
import com.samsung.magicinfo.webauthor2.util.MultipartFilenameValidator;
import java.io.IOException;
import java.nio.file.Path;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RemoteContentServiceImpl implements RemoteContentService {
  private final RemoteContentRepository remoteContentRepository;
  
  private final MultipartFilenameValidator multipartFilenameValidator;
  
  @Autowired
  public RemoteContentServiceImpl(RemoteContentRepository remoteContentRepository, MultipartFilenameValidator multipartFilenameValidator) {
    this.remoteContentRepository = remoteContentRepository;
    this.multipartFilenameValidator = multipartFilenameValidator;
  }
  
  public byte[] getContentFileFromMagicInfoServer(String fileId, String fileName) {
    return this.remoteContentRepository.getContentFileFromMagicInfoServer(fileId, fileName);
  }
  
  public String getVwlFileFromMagicInfoServer(String fileId, String fileName) {
    return this.remoteContentRepository.getVwlContent(fileId, fileName);
  }
  
  public String getXmlFileContents(String fileId, String fileName) {
    return this.remoteContentRepository.getXmlFileContents(fileId, fileName);
  }
  
  public Path getContentFileFromMagicInfoServer(Path workspaceFolder, String fileId, String fileName) {
    try {
      return this.remoteContentRepository.getContentFileFromMagicInfoServer(workspaceFolder, fileId, fileName);
    } catch (IOException ex) {
      throw new MagicInfoRemoteContentException(ex.getMessage());
    } 
  }
  
  public Path getContentFileFromMagicInfoServer(Path workspaceFolder, String relativePath, String fileId, String fileName) {
    try {
      return this.remoteContentRepository.getContentFileFromMagicInfoServer(workspaceFolder, relativePath, fileId, fileName);
    } catch (IOException ex) {
      throw new MagicInfoRemoteContentException(ex.getMessage());
    } 
  }
  
  public Path getFontFileFromMagicInfoServer(String fileId, String fileName) {
    String message = this.multipartFilenameValidator.validateName(fileName);
    if (!Strings.isNullOrEmpty(message))
      throw new FileItemValidationException(500, message); 
    try {
      return this.remoteContentRepository.getFontFileFromMagicInfoServer(fileId, fileName);
    } catch (IOException ex) {
      throw new MagicInfoRemoteContentException(ex.getMessage());
    } 
  }
}
