package com.samsung.magicinfo.rc.model.api;

import java.util.List;

public class UserSession {
  private String id;
  
  private String token;
  
  private String accessToken;
  
  private String refreshToken;
  
  private String from;
  
  private List<String> deviceIds;
  
  private String locale;
  
  private int sessionExpiry;
  
  public void setId(String id) {
    this.id = id;
  }
  
  public void setToken(String token) {
    this.token = token;
  }
  
  public void setAccessToken(String accessToken) {
    this.accessToken = accessToken;
  }
  
  public void setRefreshToken(String refreshToken) {
    this.refreshToken = refreshToken;
  }
  
  public void setFrom(String from) {
    this.from = from;
  }
  
  public void setDeviceIds(List<String> deviceIds) {
    this.deviceIds = deviceIds;
  }
  
  public void setLocale(String locale) {
    this.locale = locale;
  }
  
  public void setSessionExpiry(int sessionExpiry) {
    this.sessionExpiry = sessionExpiry;
  }
  
  UserSession(String id, String token, String accessToken, String refreshToken, String from, List<String> deviceIds, String locale, int sessionExpiry) {
    this.id = id;
    this.token = token;
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    this.from = from;
    this.deviceIds = deviceIds;
    this.locale = locale;
    this.sessionExpiry = sessionExpiry;
  }
  
  public static UserSessionBuilder builder() {
    return new UserSessionBuilder();
  }
  
  public String toString() {
    return "UserSession(id=" + getId() + ", token=" + getToken() + ", accessToken=" + getAccessToken() + ", refreshToken=" + getRefreshToken() + ", from=" + getFrom() + ", deviceIds=" + getDeviceIds() + ", locale=" + getLocale() + ", sessionExpiry=" + getSessionExpiry() + ")";
  }
  
  public String getId() {
    return this.id;
  }
  
  public String getToken() {
    return this.token;
  }
  
  public String getAccessToken() {
    return this.accessToken;
  }
  
  public String getRefreshToken() {
    return this.refreshToken;
  }
  
  public String getFrom() {
    return this.from;
  }
  
  public List<String> getDeviceIds() {
    return this.deviceIds;
  }
  
  public String getLocale() {
    return this.locale;
  }
  
  public int getSessionExpiry() {
    return this.sessionExpiry;
  }
}
