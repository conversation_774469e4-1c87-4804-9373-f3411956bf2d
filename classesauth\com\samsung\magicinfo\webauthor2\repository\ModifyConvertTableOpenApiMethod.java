package com.samsung.magicinfo.webauthor2.repository;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.repository.CannotAddConvertTableException;
import com.samsung.magicinfo.webauthor2.exception.repository.CannotModifyConvertTableException;
import com.samsung.magicinfo.webauthor2.repository.model.OpenAPIResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertTableData;
import com.samsung.magicinfo.webauthor2.util.JaxbUtil;
import java.io.StringReader;
import java.net.URI;
import java.util.Collections;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

public class ModifyConvertTableOpenApiMethod {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.repository.ModifyConvertTableOpenApiMethod.class);
  
  private static final char DOT_CHAR = '.';
  
  private final RestTemplate restTemplate;
  
  private final JaxbUtil jaxbUtil;
  
  private final String userId;
  
  private final String token;
  
  private final ConvertTableData newConvertTableData;
  
  private final ConvertTableData oldConvertTableData;
  
  public ModifyConvertTableOpenApiMethod(RestTemplate restTemplate, String userId, String token, ConvertTableData oldConvertTableData, ConvertTableData newConvertTableData, JaxbUtil jaxbUtil) {
    this.restTemplate = restTemplate;
    this.userId = userId;
    this.token = token;
    this.newConvertTableData = newConvertTableData;
    this.oldConvertTableData = oldConvertTableData;
    this.jaxbUtil = jaxbUtil;
  }
  
  private MultiValueMap<String, String> getBody() {
    String newConvertTable = this.jaxbUtil.convertTable(this.newConvertTableData);
    String oldConvertTable = this.jaxbUtil.convertTable(this.oldConvertTableData);
    LinkedMultiValueMap linkedMultiValueMap = new LinkedMultiValueMap();
    linkedMultiValueMap.add("oldConvertTable", oldConvertTable);
    linkedMultiValueMap.add("newConvertTable", newConvertTable);
    return (MultiValueMap<String, String>)linkedMultiValueMap;
  }
  
  private String convertResponseData(OpenAPIResponseData responseData) {
    if (responseData.getErrorMessage() != null)
      throw new CannotAddConvertTableException(responseData.getCode(), responseData.getErrorMessage()); 
    String response = responseData.getResponseClass();
    if (Strings.isNullOrEmpty(response) || !response.equals("true"))
      throw new CannotModifyConvertTableException(responseData.getCode() + ":" + response); 
    return response;
  }
  
  private URI getRestPath() {
    UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.newInstance().path("/openapi/open").queryParam("service", new Object[] { "CommonContentService.modifyConvertTable" }).queryParam("userId", new Object[] { this.userId }).queryParam("token", new Object[] { this.token });
    return uriComponentsBuilder.build().toUri();
  }
  
  public String callPostMethod() throws JAXBException {
    HttpHeaders headers = new HttpHeaders();
    headers.setAccept(Collections.singletonList(MediaType.APPLICATION_XML));
    HttpEntity<MultiValueMap<String, String>> request = new HttpEntity(getBody(), (MultiValueMap)headers);
    logger.info("Call OpenAPI method from address: " + getRestPath());
    ResponseEntity<String> responseData = this.restTemplate.exchange(getRestPath(), HttpMethod.POST, request, String.class);
    logger.debug("ModifyConvertTable response: {}", responseData);
    Unmarshaller jaxbUnmarshaller = createMarshaller();
    OpenAPIResponseData responseObj = (OpenAPIResponseData)jaxbUnmarshaller.unmarshal(new StringReader((String)responseData
          .getBody()));
    return convertResponseData(responseObj);
  }
  
  private Unmarshaller createMarshaller() throws JAXBException {
    JAXBContext jaxbContext = JAXBContext.newInstance(new Class[] { OpenAPIResponseData.class });
    Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
    return jaxbUnmarshaller;
  }
}
