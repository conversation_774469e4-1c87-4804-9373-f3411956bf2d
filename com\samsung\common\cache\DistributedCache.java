package com.samsung.common.cache;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.MemcacheFailoverUtils;
import com.samsung.magicinfo.protocol.queue.RequestContext;
import java.util.LinkedList;
import java.util.List;
import java.util.ListIterator;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import net.spy.memcached.AddrUtil;
import net.spy.memcached.BinaryConnectionFactory;
import net.spy.memcached.CASMutation;
import net.spy.memcached.CASMutator;
import net.spy.memcached.CASResponse;
import net.spy.memcached.CASValue;
import net.spy.memcached.MemcachedClient;
import net.spy.memcached.internal.OperationFuture;
import org.apache.logging.log4j.Logger;

public class DistributedCache implements BasicCache {
   private static final String NAMESPACE = "MAGICNFO:5d41402abc4b2a76b9719d91101";
   static Logger logger = LoggingManagerV2.getLogger(DistributedCache.class);
   private static DistributedCache instance = null;
   private static MemcachedClient[] m = null;
   private static int threadPool = 20;

   public DistributedCache() {
      super();

      try {
         String pool = CommonConfig.get("memcached.pool.count");
         if (pool != null) {
            threadPool = Integer.valueOf(pool);
         }

         m = new MemcachedClient[threadPool];
         String memcachedUrl = CommonConfig.get("memcached.url");

         for(int i = 0; i < threadPool; ++i) {
            MemcachedClient c = new MemcachedClient(new BinaryConnectionFactory(), AddrUtil.getAddresses(memcachedUrl));
            m[i] = c;
         }
      } catch (Exception var5) {
         logger.error("" + var5);
      }

   }

   public static DistributedCache getInstance() {
      if (instance == null) {
         Class var0 = DistributedCache.class;
         synchronized(DistributedCache.class) {
            if (instance == null) {
               logger.info("Creating a new instance");
               instance = new DistributedCache();
            }
         }
      }

      return instance;
   }

   public void set(String key, Object o) throws Exception {
      this.getCache().set("MAGICNFO:5d41402abc4b2a76b9719d91101" + key, 0, o);
   }

   public void set(String key, int timeToLive, Object o) throws Exception {
      this.getCache().set("MAGICNFO:5d41402abc4b2a76b9719d91101" + key, timeToLive, o);
   }

   public CASResponse cas(String key, long casId, Object o) throws Exception {
      return this.getCache().cas("MAGICNFO:5d41402abc4b2a76b9719d91101" + key, casId, o);
   }

   public Object get(String key) throws Exception {
      Object o = this.getCache().get("MAGICNFO:5d41402abc4b2a76b9719d91101" + key);
      return o;
   }

   public CASValue gets(String key, Object autoInitObj) throws Exception {
      return this.gets(key, autoInitObj, 0);
   }

   private CASValue gets(String key, Object autoInitObj, int retryCnt) throws Exception {
      CASValue o = this.getCache().gets("MAGICNFO:5d41402abc4b2a76b9719d91101" + key);
      if (o == null) {
         logger.info("[MagicInfo_Cached] Memory init key :" + key);
         if (autoInitObj == null) {
            return null;
         }

         OperationFuture of = null;
         int retryCntForAdd = 0;

         try {
            do {
               of = this.getCache().add("MAGICNFO:5d41402abc4b2a76b9719d91101" + key, 0, autoInitObj);
               Thread.sleep(5L);
            } while(!of.getStatus().isSuccess() && retryCntForAdd++ < MemcacheFailoverUtils.MAXIMUM_CNT);

            if (retryCntForAdd >= MemcacheFailoverUtils.MAXIMUM_CNT) {
               throw new Exception();
            }
         } catch (Exception var8) {
            logger.error("[MagicInfo_Cached] Critical Exception during add operation .. retryCnt:" + retryCntForAdd, var8);
         }

         if (retryCnt >= MemcacheFailoverUtils.MAXIMUM_CNT) {
            logger.error("[MagicInfo_Cached] Critical infinite-loop exception..");
            return null;
         }

         o = this.gets(key, autoInitObj, retryCnt + 1);
      }

      return o;
   }

   public void delete(String key) throws Exception {
      this.getCache().delete("MAGICNFO:5d41402abc4b2a76b9719d91101" + key);
   }

   public MemcachedClient getCache() {
      MemcachedClient c = null;

      try {
         Random random = new Random();
         int i = random.nextInt(threadPool);
         c = m[i];
         random = null;
      } catch (Exception var4) {
         logger.error("" + var4);
      }

      return c;
   }

   public void clean() throws Exception {
      this.getCache().flush();
   }

   public boolean isEmpty(String key) throws Exception {
      Object o = this.get(key);
      return o == null;
   }

   public Object cas(String key, Object item, MutatorOperation operation) {
      return this.checkAndSet("MAGICNFO:5d41402abc4b2a76b9719d91101" + key, 0, TimeUnit.SECONDS, item, operation);
   }

   private Object checkAndSet(String key, int exp, TimeUnit timeUnit, final Object item, final MutatorOperation operation) {
      CASMutation mutation = new CASMutation() {
         public Object getNewValue(Object current) {
            return operation.mutate(current, item);
         }
      };
      CASMutator mutator = new CASMutator(this.getCache(), this.getCache().getTranscoder());
      Object initialValue = operation.initialValue(item);

      try {
         return mutator.cas(key, initialValue, 0, mutation);
      } catch (Exception var10) {
         throw new CacheException("Error when performing CAS operation on cache", var10);
      }
   }

   public boolean putMapCache(String cacaheKey, String hashKey, Object o) {
      return false;
   }

   public boolean enQueue(String cacheKey, Object o) {
      return false;
   }

   public Object deQueue(String cacheKey) {
      return null;
   }

   public List readAllQueue(String cacheKey) {
      return null;
   }

   public Object getMapCache(String cacheKey, String hashKey, int command) {
      return null;
   }

   public void removeMapCache(String cacheKey, String hashKey) {
   }

   public List readAllMap(String cacheKey) {
      return null;
   }

   public int getSizeMap(String cacheKey) {
      return 0;
   }

   public Object getQueue(String cacheKey) {
      return null;
   }

   public boolean isExistServiceInQueue(String cacheKey, String service) throws Exception {
      try {
         LinkedList queue = (LinkedList)this.get(cacheKey);
         if (queue != null) {
            ListIterator iterator = (ListIterator)queue.iterator();

            while(iterator.hasNext()) {
               RequestContext request = (RequestContext)iterator.next();
               if (request != null && request.getWebServiceContext().getServiceID().equals(service)) {
                  return true;
               }
            }
         }

         return false;
      } catch (Exception var6) {
         logger.error("[MagicInfo_WaitingQueue] check bootstrap fail!");
         throw var6;
      }
   }
}
