package com.samsung.magicinfo.webauthor2.webapi.resource;

import com.samsung.magicinfo.webauthor2.model.DataLinkTableRow;
import java.io.Serializable;
import org.springframework.hateoas.Link;
import org.springframework.hateoas.Resource;

public class DataLinkTableRowResource extends Resource<DataLinkTableRow> implements Serializable {
  private static final long serialVersionUID = 6392461041199823169L;
  
  public DataLinkTableRowResource(DataLinkTableRow content, Link... links) {
    super(content, links);
  }
  
  public DataLinkTableRowResource(DataLinkTableRow content, Iterable<Link> links) {
    super(content, links);
  }
}
