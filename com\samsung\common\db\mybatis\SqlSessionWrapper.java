package com.samsung.common.db.mybatis;

import java.sql.Connection;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.cursor.Cursor;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.session.SqlSession;

public class SqlSessionWrapper implements SqlSession {
   private SqlSession session;

   public SqlSessionWrapper(SqlSession session) {
      super();
      this.session = session;
   }

   public void clearCache() {
      this.session.clearCache();
   }

   public void close() {
      this.session.close();
   }

   public void commit() {
      this.session.commit();
   }

   public void commit(boolean arg0) {
      this.session.commit(arg0);
   }

   public int delete(String arg0, Object arg1) {
      return this.session.delete(arg0, arg1);
   }

   public int delete(String arg0) {
      return this.session.delete(arg0);
   }

   public List flushStatements() {
      return this.session.flushStatements();
   }

   public Configuration getConfiguration() {
      return this.session.getConfiguration();
   }

   public Connection getConnection() {
      return this.session.getConnection();
   }

   public Object getMapper(Class arg0) {
      return MapperExceptionInterceptor.addInterceptor(this.session.getMapper(arg0), arg0);
   }

   public int insert(String arg0, Object arg1) {
      return this.session.insert(arg0, arg1);
   }

   public int insert(String arg0) {
      return this.session.insert(arg0);
   }

   public void rollback() {
      this.session.rollback();
   }

   public void rollback(boolean arg0) {
      this.session.rollback(arg0);
   }

   public void select(String arg0, Object arg1, ResultHandler arg2) {
      this.session.select(arg0, arg1, arg2);
   }

   public void select(String arg0, Object arg1, RowBounds arg2, ResultHandler arg3) {
      this.session.select(arg0, arg1, arg2, arg3);
   }

   public void select(String arg0, ResultHandler arg1) {
      this.session.select(arg0, arg1);
   }

   public List selectList(String arg0, Object arg1, RowBounds arg2) {
      return this.session.selectList(arg0, arg1, arg2);
   }

   public List selectList(String arg0, Object arg1) {
      return this.session.selectList(arg0, arg1);
   }

   public List selectList(String arg0) {
      return this.session.selectList(arg0);
   }

   public Map selectMap(String arg0, Object arg1, String arg2, RowBounds arg3) {
      return this.session.selectMap(arg0, arg1, arg2, arg3);
   }

   public Map selectMap(String arg0, Object arg1, String arg2) {
      return this.session.selectMap(arg0, arg1, arg2);
   }

   public Map selectMap(String arg0, String arg1) {
      return this.session.selectMap(arg0, arg1);
   }

   public Object selectOne(String arg0, Object arg1) {
      return this.session.selectOne(arg0, arg1);
   }

   public Object selectOne(String arg0) {
      return this.session.selectOne(arg0);
   }

   public int update(String arg0, Object arg1) {
      return this.session.update(arg0, arg1);
   }

   public int update(String arg0) {
      return this.session.update(arg0);
   }

   public Cursor selectCursor(String arg0) {
      return null;
   }

   public Cursor selectCursor(String arg0, Object arg1) {
      return null;
   }

   public Cursor selectCursor(String arg0, Object arg1, RowBounds arg2) {
      return null;
   }
}
