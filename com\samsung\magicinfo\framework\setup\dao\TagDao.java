package com.samsung.magicinfo.framework.setup.dao;

import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.magicinfo.framework.content.entity.ContentLog;
import com.samsung.magicinfo.framework.setup.entity.TagConditionEntity;
import com.samsung.magicinfo.framework.setup.entity.TagEntity;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.protocol.constants.CommonConstants;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class TagDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(TagDao.class);

   public TagDao() {
      super();
   }

   public static Map getConstantsMap() {
      Map map = new HashMap();
      map.put("ConstTYPE_PREMIUM", "iPLAYER");
      map.put("ConstTYPE_ALL", "ALL");
      map.put("ConstTYPE_LITE", "LPLAYER");
      map.put("ConstTYPE_SOC", "SPLAYER");
      map.put("ConstTYPE_SOC2", "S2PLAYER");
      map.put("ConstTYPE_SOC3", "S3PLAYER");
      map.put("ConstTYPE_SOC4", "S4PLAYER");
      map.put("ConstTYPE_SOC5", "S5PLAYER");
      map.put("ConstTYPE_SOC6", "S6PLAYER");
      map.put("ConstTYPE_SOC7", "S7PLAYER");
      map.put("ConstTYPE_SOC9", "S9PLAYER");
      map.put("ConstTYPE_SOC10", "S10PLAYER");
      map.put("ConstTYPE_APLAYER", "APLAYER");
      map.put("ConstTYPE_WPLAYER", "WPLAYER");
      map.put("TYPE_VERSION_10_0", CommonDataConstants.TYPE_VERSION_10_0);
      map.put("TYPE_VERSION_9_0", CommonDataConstants.TYPE_VERSION_9_0);
      map.put("TYPE_VERSION_7_0", CommonDataConstants.TYPE_VERSION_7_0);
      map.put("TYPE_VERSION_6_0", CommonDataConstants.TYPE_VERSION_6_0);
      map.put("TYPE_VERSION_5_0", CommonDataConstants.TYPE_VERSION_5_0);
      map.put("TYPE_VERSION_4_0", CommonDataConstants.TYPE_VERSION_4_0);
      map.put("TYPE_VERSION_3_0", CommonDataConstants.TYPE_VERSION_3_0);
      map.put("TYPE_VERSION_2_0", CommonDataConstants.TYPE_VERSION_2_0);
      map.put("TYPE_VERSION_1_0", CommonDataConstants.TYPE_VERSION_1_0);
      return map;
   }

   public int addTagInfo(String tagValue, Long tagOrgan, String tagDesc) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).addTagInfo((long)this.createNewId(), tagValue, tagOrgan, tagDesc, 0L);
   }

   public int addTagInfo(String tagValue, Long tagOrgan, String tagDesc, long tagType) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).addTagInfo((long)this.createNewId(), tagValue, tagOrgan, tagDesc, tagType);
   }

   public int updateTagInfo(TagEntity tag) throws SQLException {
      if (tag.getTag_condition() != null && tag.getTag_condition().length > 0) {
      }

      return ((TagDaoMapper)this.getMapper()).updateTagInfo(tag);
   }

   public int deleteTagInfo(int tagId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).deleteTagInfo(tagId);
   }

   public List getTagListPage(Map map, int startPos, int pageSize) throws SQLException {
      List object = null;
      String searchText = (String)map.get("searchText");
      String sortColumn = (String)map.get("sortColumn");
      String sortOrder = (String)map.get("sortOrder");
      String organId = (String)map.get("organId");
      Long tagType = map.get("type") != null ? Long.parseLong((String)map.get("type")) : null;
      ArrayList organIdList = null;

      try {
         if (sortColumn != null && sortColumn.length() > 0) {
            sortColumn = sortColumn.toUpperCase();
            if (sortOrder != null && sortOrder.length() > 0) {
               sortOrder = sortOrder.toUpperCase();
            }
         }

         --startPos;
         if (organId != null && !organId.equalsIgnoreCase("NULL") && !organId.equalsIgnoreCase("ALL")) {
            organIdList = new ArrayList();
            String[] arrOrganId = organId.split(",");
            String[] var18 = arrOrganId;
            int var19 = arrOrganId.length;

            for(int var20 = 0; var20 < var19; ++var20) {
               String id = var18[var20];
               organIdList.add(Long.parseLong(id));
            }
         } else {
            UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
            List manageGroupList = userGroupInfo.getUserManageGroupListByUserId(SecurityUtils.getLoginUserId());
            if (manageGroupList != null && manageGroupList.size() > 0) {
               organIdList = new ArrayList();
               organIdList.add(CommonConstants.COMMON_ORGAN_ID);
               Iterator var13 = manageGroupList.iterator();

               while(var13.hasNext()) {
                  UserGroup userGroup = (UserGroup)var13.next();
                  organIdList.add(userGroup.getGroup_id());
               }
            }
         }

         object = ((TagDaoMapper)this.getMapper()).getTagListPage(startPos, pageSize, searchText, sortColumn, sortOrder, organIdList, tagType);
      } catch (Exception var16) {
         this.logger.error("", var16);
      }

      return object;
   }

   public List getContentTagListPage(Map map, int startPos, int pageSize) throws SQLException {
      List object = null;
      String searchText = (String)map.get("searchText");
      String sortColumn = (String)map.get("sortColumn");
      String sortOrder = (String)map.get("sortOrder");
      String organId = (String)map.get("organId");
      ArrayList organIdList = null;

      try {
         if (sortColumn != null && sortColumn.length() > 0) {
            sortColumn = sortColumn.toUpperCase();
            if (sortOrder != null && sortOrder.length() > 0) {
               sortOrder = sortOrder.toUpperCase();
            }
         }

         --startPos;
         if (organId != null && !organId.equalsIgnoreCase("NULL") && !organId.equalsIgnoreCase("ALL")) {
            organIdList = new ArrayList();
            String[] arrOrganId = organId.split(",");
            String[] var11 = arrOrganId;
            int var12 = arrOrganId.length;

            for(int var13 = 0; var13 < var12; ++var13) {
               String id = var11[var13];
               organIdList.add(Long.parseLong(id));
            }
         }

         object = ((TagDaoMapper)this.getMapper()).getContentTagListPage(startPos, pageSize, searchText, sortColumn, sortOrder, organIdList, map);
      } catch (Exception var15) {
         this.logger.error("", var15);
      }

      return object;
   }

   public int getTagListCnt(Map map) throws SQLException {
      int retCnt = -1;
      String searchText = (String)map.get("searchText");
      String organId = (String)map.get("organId");
      Long tagType = map.get("type") != null ? Long.parseLong((String)map.get("type")) : null;
      ArrayList organIdList = null;

      try {
         if (organId != null && !organId.equalsIgnoreCase("NULL") && !organId.equalsIgnoreCase("ALL")) {
            organIdList = new ArrayList();
            String[] arrOrganId = organId.split(",");
            String[] var8 = arrOrganId;
            int var9 = arrOrganId.length;

            for(int var10 = 0; var10 < var9; ++var10) {
               String id = var8[var10];
               organIdList.add(Long.parseLong(id));
            }
         }

         retCnt = ((TagDaoMapper)this.getMapper()).getTagListCnt(searchText, organIdList, tagType);
      } catch (Exception var12) {
         this.logger.error("", var12);
      }

      return retCnt == 0 ? -1 : retCnt;
   }

   public int getContentTagListCnt(Map map) throws SQLException {
      int retCnt = -1;
      String searchText = (String)map.get("searchText");
      String organId = (String)map.get("organId");
      Long tagType = map.get("type") != null ? Long.parseLong((String)map.get("type")) : null;
      ArrayList organIdList = null;

      try {
         if (organId != null && !organId.equalsIgnoreCase("NULL") && !organId.equalsIgnoreCase("ALL")) {
            organIdList = new ArrayList();
            String[] arrOrganId = organId.split(",");
            String[] var8 = arrOrganId;
            int var9 = arrOrganId.length;

            for(int var10 = 0; var10 < var9; ++var10) {
               String id = var8[var10];
               organIdList.add(Long.parseLong(id));
            }
         }

         retCnt = ((TagDaoMapper)this.getMapper()).getContentTagListCnt(searchText, organIdList, tagType);
      } catch (Exception var12) {
         this.logger.error("", var12);
      }

      return retCnt == 0 ? -1 : retCnt;
   }

   public int getDeviceTagMappingCnt(int tagId) throws SQLException {
      int retCnt = 0;
      int retCnt = retCnt + ((TagDaoMapper)this.getMapper()).getDeviceTagMappingCnt(tagId);
      return retCnt;
   }

   public int getTotalTagMappingCnt(int tagId) throws SQLException {
      int retCnt = 0;

      try {
         retCnt += ((TagDaoMapper)this.getMapper()).getDeviceTagMappingCnt(tagId);
         retCnt += ((TagDaoMapper)this.getMapper()).getContentTagMappingCnt(tagId);
         retCnt += ((TagDaoMapper)this.getMapper()).getContentTagCnt(tagId);
         retCnt += ((TagDaoMapper)this.getMapper()).getPlaylistTagMappingCnt(tagId);
      } catch (Exception var4) {
         this.logger.error("", var4);
      }

      return retCnt;
   }

   public int getContentTagMappingCnt(int tagId) throws SQLException {
      int retCnt = 0;

      try {
         retCnt = ((TagDaoMapper)this.getMapper()).getContentTagMappingCnt(tagId);
      } catch (Exception var4) {
         this.logger.error("", var4);
      }

      return retCnt;
   }

   public int getPlaylistTagMappingCnt(int tagId) throws SQLException {
      int retCnt = 0;

      try {
         retCnt = ((TagDaoMapper)this.getMapper()).getPlaylistTagMappingCnt(tagId);
      } catch (Exception var4) {
         this.logger.error("", var4);
      }

      return retCnt;
   }

   public int getContentTagCnt(int tagId) throws SQLException {
      int retCnt = 0;

      try {
         retCnt = ((TagDaoMapper)this.getMapper()).getContentTagCnt(tagId);
      } catch (Exception var4) {
         this.logger.error("", var4);
      }

      return retCnt;
   }

   public TagEntity getTag(int tagId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getTag(tagId);
   }

   public List getTagValueList(Long organId) throws SQLException {
      List object = null;

      try {
         object = ((TagDaoMapper)this.getMapper()).getTagValueList(organId);
      } catch (Exception var4) {
         this.logger.error("", var4);
      }

      return object;
   }

   public List getTagMappingDeviceId(int tagId) throws SQLException {
      List object = null;

      try {
         object = ((TagDaoMapper)this.getMapper()).getTagMappingDeviceId(tagId);
      } catch (Exception var4) {
         this.logger.error("", var4);
      }

      return object;
   }

   public List getDeviceListByTagIdAndOrgName(int tagId, String orgName) {
      return ((TagDaoMapper)this.getMapper()).getDeviceListByTagIdAndOrgName(tagId, orgName);
   }

   public List getTagMappingContentId(int tagId) throws SQLException {
      List object = null;

      try {
         object = ((TagDaoMapper)this.getMapper()).getTagMappingContentId(tagId);
      } catch (Exception var4) {
         this.logger.error("", var4);
      }

      return object;
   }

   public List getTagContentListByTagIdAndOrgId(int tagId, long orgId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getTagContentListByTagIdAndOrgId(tagId, orgId);
   }

   public List getTagValueListByDeviceId(String deviceId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getTagValueListByDeviceId(deviceId);
   }

   private int createNewId() throws SQLException {
      return SequenceDB.getNextValue("MI_TAG_INFO_TAG");
   }

   public List getPlaylistInfoByTagId(int tagId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getPlaylistInfoByTagId(tagId);
   }

   public List getPlaylistInfoByTagIdAndOrgId(int tagId, long orgId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getPlaylistInfoByTagIdAndOrgId(tagId, orgId);
   }

   public List getTagPlaylistInfoByTagIdAndOrgId(int tagId, long orgId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getTagPlaylistInfoByTagIdAndOrgId(tagId, orgId);
   }

   public List getContentInfoByTagId(int tagId, String playlistId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getContentInfoByTagId(tagId, playlistId);
   }

   public List getTagListByOrganizationId(long organizationId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getTagListByOrganizationId(organizationId);
   }

   public void setContentTagMapping(String contentId, String tagId) {
      try {
         ((TagDaoMapper)this.getMapper()).setContentTagMapping(contentId, Long.valueOf(tagId));
      } catch (Exception var4) {
         this.logger.error("[MagicInfo_Content_TAG] fail setcontentTagMapping e : " + var4.getMessage());
      }

   }

   public void setContentTagMapping(String contentId, String tagId, String tagCondition) {
      try {
         ((TagDaoMapper)this.getMapper()).setContentTagConditionMapping(contentId, Long.valueOf(tagId), Long.valueOf(tagCondition));
      } catch (Exception var5) {
         this.logger.error("[MagicInfo_Content_TAG] fail setcontentTagMapping e : " + var5.getMessage(), var5);
      }

   }

   public List getContentTagList(String contentId) {
      return ((TagDaoMapper)this.getMapper()).getContentTagList(contentId);
   }

   public List getContentTagConditionList(String contentId, long tagId) {
      return ((TagDaoMapper)this.getMapper()).getContentTagConditionList(contentId, tagId);
   }

   public boolean deleteTagInfoFromContentId(String contentId) {
      return ((TagDaoMapper)this.getMapper()).deleteTagInfoFromContentId(contentId);
   }

   public List getContentlistFromTagId(String[] tagId) {
      return ((TagDaoMapper)this.getMapper()).getContentlistFromTagId(tagId);
   }

   public List getTagConditionFromTagId(long tagId) {
      return ((TagDaoMapper)this.getMapper()).getTagConditionFromTagId(tagId, (Long)null);
   }

   public Map getTagConditionFromTagId(long tagId, long conditionId) {
      List data = ((TagDaoMapper)this.getMapper()).getTagConditionFromTagId(tagId, conditionId);
      return data != null && data.size() > 0 ? (Map)data.get(0) : null;
   }

   public List getTagConditionEntityFromTagId(long tagId) {
      return ((TagDaoMapper)this.getMapper()).getTagConditionEntityFromTagId(tagId, (Long)null);
   }

   public TagConditionEntity getTagConditionEntityFromTagId(long tagId, long conditionId) {
      List list = ((TagDaoMapper)this.getMapper()).getTagConditionEntityFromTagId(tagId, conditionId);
      return list != null && list.size() > 0 ? (TagConditionEntity)list.get(0) : null;
   }

   public boolean deleteTagCondition(long tagId) {
      return ((TagDaoMapper)this.getMapper()).deleteTagCondition(tagId);
   }

   public boolean addBoolenCondition(long tagId, long tagConditionId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).addTagMapCondition(tagId, tagConditionId);
   }

   public long addCondition(long tagId, String tagCondition) throws SQLException {
      SqlSession session = this.openNewSession(false);

      long var7;
      try {
         long tagConditionId = (long)SequenceDB.getNextValue("MI_TAG_INFO_TAG_CONDITION");
         if (!((TagDaoMapper)this.getMapper(session)).addCondition(tagConditionId, tagCondition)) {
            session.rollback();
            var7 = -1L;
            return var7;
         }

         if (((TagDaoMapper)this.getMapper(session)).addTagMapCondition(tagId, tagConditionId)) {
            session.commit();
            var7 = tagConditionId;
            return var7;
         }

         session.rollback();
         var7 = -1L;
      } catch (SQLException var12) {
         session.rollback();
         throw var12;
      } finally {
         session.close();
      }

      return var7;
   }

   public boolean deleteCondition(long tagId, long tagConditionId) throws SQLException {
      boolean rtn = false;
      SqlSession session = this.openNewSession(false);

      boolean var7;
      try {
         if (((TagDaoMapper)this.getMapper(session)).deleteTagConditionWithConditionId(tagConditionId)) {
            if (!((TagDaoMapper)this.getMapper(session)).deleteTagConditionInfoWithTagIdConditionId(tagConditionId)) {
               session.rollback();
               var7 = false;
               return var7;
            }

            session.commit();
            rtn = true;
            return rtn;
         }

         session.rollback();
         var7 = false;
      } catch (SQLException var11) {
         session.rollback();
         throw var11;
      } finally {
         session.close();
      }

      return var7;
   }

   public List getPlaylistIdFroTrigger(List tagId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getPlaylistIdFroTrigger(tagId);
   }

   public List getTagIdFromContentId(String contentId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getTagIdFromContentId(contentId);
   }

   public void addLogContenTag(ContentLog log) throws SQLException {
      long id = (long)SequenceDB.getNextValue("MI_TAG_LOG_CONTENT");
      log.setLog_id(id);
      ((TagDaoMapper)this.getMapper()).addLogContenTag(log);
   }

   public List getlogContentTag(List list) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getlogContentTag(list);
   }

   public List getTagInfoWithTagCondition(long tagId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getTagInfoWithTagCondition(tagId);
   }

   public int getContentTagCondition(long tagId, long conditionId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getContentTagCondition(tagId, conditionId);
   }

   public int getCntTagCondition(long tagId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getCntTagCondition(tagId);
   }

   public int chkTagName(String tagName) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).chkTagName(tagName);
   }

   public List getTagConditionId(long tagId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getTagConditionId(tagId);
   }

   public boolean updateCondition(long conditionId, String condition) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).updateCondition(conditionId, condition);
   }

   public boolean deleteForceCondition(long tagId) throws SQLException {
      boolean rtn = false;
      SqlSession session = this.openNewSession(false);

      try {
         List tagConditionId = ((TagDaoMapper)this.getMapper(session)).getConditionId(tagId);
         if (tagConditionId != null && tagConditionId.size() > 0) {
            long conditionId = (Long)((Map)tagConditionId.get(0)).get("tag_condition_id");
            boolean var8;
            if (!((TagDaoMapper)this.getMapper(session)).deleteTagConditionInfoWithTagIdConditionId(conditionId)) {
               session.rollback();
               var8 = false;
               return var8;
            }

            if (!((TagDaoMapper)this.getMapper(session)).deleteTagConditionWithTagId(conditionId)) {
               session.rollback();
               var8 = false;
               return var8;
            }
         }

         session.commit();
         rtn = true;
      } catch (SQLException var12) {
         session.rollback();
         throw var12;
      } finally {
         session.close();
      }

      return rtn;
   }

   public List getTagListSearchByName(String tagName) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getTagListSearchByName(tagName);
   }

   public TagEntity getTagByName(String tagName) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).getTagByName(tagName);
   }

   public boolean deleteRelationOfTagAndContent(long tagId, String contentId) throws SQLException {
      return ((TagDaoMapper)this.getMapper()).deleteRelationOfTagAndContent(tagId, contentId);
   }

   public List getMappedContentListByTagIdAndConditions(Long tagId, String conditionIds, String[] deviceTypeArr) throws SQLException {
      String[] conditionStr = null;
      if (conditionIds != null && !conditionIds.equals("")) {
         conditionStr = conditionIds.split(",");
      }

      return ((TagDaoMapper)this.getMapper()).getMappedContentListByTagIdAndConditions(tagId, conditionStr, deviceTypeArr, getConstantsMap());
   }
}
