package com.samsung.magicinfo.restapi.user.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.samsung.magicinfo.framework.kpi.annotation.LogProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

@JsonInclude(Include.NON_EMPTY)
@ApiModel(
   value = "V2UserInfoResource",
   description = "If the model is used in organization creation, (email, password, userId, and userName) are required."
)
public class V2UserInfoResource {
   @Email
   @Size(
      max = 200
   )
   @ApiModelProperty(
      example = "<EMAIL>",
      value = "Email of specific user",
      required = true
   )
   private String email;
   @ApiModelProperty(
      example = "CL1",
      value = "Job position of a specific user"
   )
   private String jobPosition;
   @ApiModelProperty(
      example = "010-1234-1234",
      value = "Mobile number of a specific user"
   )
   @Size(
      max = 60
   )
   private String mobileNum;
   @Size(
      max = 60
   )
   @ApiModelProperty(
      example = "02-1234-1234",
      value = "Phone number of a specific user"
   )
   private String phoneNum;
   @ApiModelProperty(
      example = "B2B파트",
      value = "Team of a specific user"
   )
   private String team;
   @LogProperty(
      valueType = "NAME"
   )
   @NotEmpty(
      message = "userId cannot be empty"
   )
   @Size(
      min = 3,
      max = 64
   )
   @ApiModelProperty(
      value = "Id value of specific user",
      required = true
   )
   private String userId;
   @NotEmpty(
      message = "userName cannot be empty"
   )
   @Size(
      max = 60
   )
   @ApiModelProperty(
      value = "Name value of specific user",
      required = true
   )
   private String userName;
   @ApiModelProperty(
      value = "Password value of specific user",
      required = true
   )
   private String password;

   public V2UserInfoResource() {
      super();
   }

   public String getEmail() {
      return this.email;
   }

   public void setEmail(String email) {
      this.email = email;
   }

   public String getJobPosition() {
      return this.jobPosition;
   }

   public void setJobPosition(String jobPosition) {
      this.jobPosition = jobPosition;
   }

   public String getMobileNum() {
      return this.mobileNum;
   }

   public void setMobileNum(String mobileNum) {
      this.mobileNum = mobileNum;
   }

   public String getPhoneNum() {
      return this.phoneNum;
   }

   public void setPhoneNum(String phoneNum) {
      this.phoneNum = phoneNum;
   }

   public String getTeam() {
      return this.team;
   }

   public void setTeam(String team) {
      this.team = team;
   }

   public String getUserId() {
      return this.userId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }

   public String getUserName() {
      return this.userName;
   }

   public void setUserName(String userName) {
      this.userName = userName;
   }

   public String getPassword() {
      return this.password;
   }

   public void setPassword(String password) {
      this.password = password;
   }
}
