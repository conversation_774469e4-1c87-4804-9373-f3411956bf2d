package com.samsung.magicinfo.rc.common.swagger;

import io.swagger.annotations.Api;
import java.util.ArrayList;
import java.util.List;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.RequestMethod;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.builders.ResponseMessageBuilder;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.schema.ModelReference;
import springfox.documentation.service.ResponseMessage;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration
@EnableSwagger2
public class SwaggerConfig {
  @Bean
  public Docket api() {
    return (new Docket(DocumentationType.SWAGGER_2))
      .select()
      .apis(RequestHandlerSelectors.withClassAnnotation(Api.class))
      .paths(PathSelectors.ant("/api/**"))
      .build()
      .useDefaultResponseMessages(false)
      .globalResponseMessage(RequestMethod.GET, getResponseMessages(RequestMethod.GET))
      .globalResponseMessage(RequestMethod.POST, getResponseMessages(RequestMethod.POST))
      .globalResponseMessage(RequestMethod.PUT, getResponseMessages(RequestMethod.PUT))
      .globalResponseMessage(RequestMethod.DELETE, getResponseMessages(RequestMethod.DELETE))
      .apiInfo((new ApiInfoBuilder())
        
        .version("2.0")
        .title("Remote Control REST API")
        .description("Documentation for Remote Control Server REST API v1.0")
        .build());
  }
  
  private List<ResponseMessage> getResponseMessages(RequestMethod method) {
    ArrayList<ResponseMessage> messages = new ArrayList<>();
    messages.add((new ResponseMessageBuilder()).code(400).message("Bad Request").responseModel((ModelReference)new ModelRef("RestErrorInfo")).build());
    messages.add((new ResponseMessageBuilder()).code(401).message("Unauthorized").responseModel((ModelReference)new ModelRef("RestErrorInfo")).build());
    messages.add((new ResponseMessageBuilder()).code(403).message("Forbidden").responseModel((ModelReference)new ModelRef("RestErrorInfo")).build());
    messages.add((new ResponseMessageBuilder()).code(404).message("Not Found").responseModel((ModelReference)new ModelRef("RestErrorInfo")).build());
    messages.add((new ResponseMessageBuilder()).code(500).message("Internal Server Error").responseModel((ModelReference)new ModelRef("RestErrorInfo")).build());
    messages.add((new ResponseMessageBuilder()).code(503).message("Service Unavailable").responseModel((ModelReference)new ModelRef("RestErrorInfo")).build());
    if (method == RequestMethod.POST)
      messages.add((new ResponseMessageBuilder()).code(201).message("Created").build()); 
    return messages;
  }
}
