package com.samsung.magicinfo.restapi.playlist.service;

import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.PlaylistUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.content.dao.PlaylistDao;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistInterface;
import com.samsung.magicinfo.framework.setup.entity.TagEntity;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfo;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfoImpl;
import com.samsung.magicinfo.restapi.playlist.model.V2ContentFileResourceResponse;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistContentEffectResource;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistContentResourceResponse;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistContentTagResource;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistItemResource;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistItemResourceResponse;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistListResourceResponse;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistResource;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistVersionResourceResponse;
import com.samsung.magicinfo.restapi.setting.model.V2TagResourceResponse;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class V2PlaylistFactory {
   public V2PlaylistFactory() {
      super();
   }

   public static void playlistContentBuilder(V2PlaylistResource resource, V2PlaylistItemResource item, String playlistId, Content content, Map totalSizeMapByContentId) {
   }

   private static List playlistContentTagsResourceBuilder(ContentInfo cInfo, Content content) throws Exception {
      List tagList = cInfo.getTagFromContentId(content.getContent_id());
      List res = new ArrayList();
      Iterator var4 = tagList.iterator();

      while(var4.hasNext()) {
         TagEntity entity = (TagEntity)var4.next();
         V2TagResourceResponse vr = V2TagResourceResponse.V2TagResourceResponseBuilder.aV2TagResourceResponse().contentCount(entity.getContent_count()).tagId(entity.getTag_id()).tagName(entity.getTag_name()).tagOrgan(entity.getTag_organ()).tagType(entity.getTag_type()).tagValue(entity.getTag_value()).totalSize(entity.getTotal_size()).createDate(DateUtils.timestamp2String(entity.getCreate_date(), "yyyy-MM-dd HH:mm:ss")).tagCondition(Arrays.asList((Object[])Optional.ofNullable(entity.getTag_condition()).orElse(new String[0]))).tagDesc(entity.getTag_desc()).build();
         res.add(vr);
      }

      return res;
   }

   private static List playlistContentFilesResourceBuilder(ContentInfo cotnentInfo, Content content) throws Exception {
      List ffiles = cotnentInfo.getActiveFileList(content.getContent_id());
      List files = new ArrayList();
      Iterator var4 = ffiles.iterator();

      while(var4.hasNext()) {
         ContentFile cf = (ContentFile)var4.next();
         V2ContentFileResourceResponse vf = V2ContentFileResourceResponse.V2ContentFileResourceResponseBuilder.aV2ContentFileResourceResponse().createDate(DateUtils.timestamp2String(cf.getCreate_date(), "yyyy-MM-dd HH:mm:ss")).creatorId(cf.getCreator_id()).fileId(cf.getFile_id()).fileName(cf.getFile_name()).filePath(cf.getFile_path()).fileSize(cf.getFile_size()).fileType(cf.getFile_type()).hashCode(cf.getHash_code()).htmlStartPage(cf.getHtml_start_page()).isStreaming(cf.getIs_streaming()).isUploadCompleted(cf.getIs_upload_completed()).refreshInterval(cf.getRefresh_interval()).reqIndex(cf.getReqIndex()).urlAddress(cf.getUrl_address()).build();
         files.add(vf);
      }

      return files;
   }

   public static V2PlaylistContentResourceResponse playlistContentResourceResponseBuilder(PlaylistInfo playlistInfo, PlaylistContent content) throws Exception {
      Playlist subPlaylist = playlistInfo.getPlaylistActiveVerInfo(content.getContent_id());
      V2PlaylistContentResourceResponse res = null;
      if (subPlaylist != null) {
         ContentFile thumbnailFile = playlistInfo.getThumbFileInfo(content.getContent_id());
         res = V2PlaylistContentResourceResponse.V2PlaylistContentResourceResponseBuilder.aV2PlaylistContentResourceResponse().contentId(subPlaylist.getPlaylist_id()).contentName(subPlaylist.getPlaylist_name()).thumbFileId(StrUtils.nvl(thumbnailFile.getFile_id())).thumbFileName(StrUtils.nvl(thumbnailFile.getFile_name())).totalSize(subPlaylist.getTotal_size()).deviceType(subPlaylist.getDevice_type()).deviceTypeVersion(Float.toString(subPlaylist.getDevice_type_version())).playTime(subPlaylist.getPlay_time()).isSubPlaylist(true).build();
      }

      return res;
   }

   public static List playlistListResourceResponseBuilder(List playlists, String productType) throws Exception {
      PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl(productType);
      PlaylistDao playlistDao = new PlaylistDao();
      ContentInfo contentDao = ContentInfoImpl.getInstance();
      Locale locale = SecurityUtils.getLocale();
      String thumbnailInfo = "";
      List res = new ArrayList();

      V2PlaylistListResourceResponse vrs;
      for(Iterator var8 = playlists.iterator(); var8.hasNext(); res.add(vrs)) {
         Playlist playlist = (Playlist)var8.next();
         boolean hasSubPlaylist = false;
         ContentFile thumbnailFile = null;
         if (playlist.getPlaylist_type().equals("5")) {
            List ContentList = pInfo.getTagContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
            thumbnailFile = new ContentFile();
            if (ContentList != null && ContentList.size() > 0) {
               thumbnailFile.setFile_id(((Content)ContentList.get(0)).getThumb_file_id());
               thumbnailFile.setFile_name(((Content)ContentList.get(0)).getThumb_file_name());
            } else {
               thumbnailFile.setFile_id("NOIMAGE_THUMBNAIL");
               thumbnailFile.setFile_name("NOIMAGE_THUMBNAIL.PNG");
            }
         } else {
            thumbnailFile = pInfo.getThumbFileInfo(playlist.getPlaylist_id());
            if (playlist.getHas_sub_playlist()) {
               hasSubPlaylist = true;
            }
         }

         List tmpList;
         if (thumbnailFile != null) {
            thumbnailInfo = thumbnailFile.getFile_id() + "|" + thumbnailFile.getFile_name();
         } else {
            Playlist playlistInfo = playlistDao.getPlaylistActiveVerInfo(playlist.getPlaylist_id());
            if (playlistInfo != null) {
               Content contentInfo = contentDao.getThumbInfoOfActiveVersion(playlistInfo.getContent_id());
               if (contentInfo != null) {
                  thumbnailInfo = contentInfo.getThumb_file_id() + "|" + contentInfo.getThumb_file_name();
               } else {
                  tmpList = pInfo.getContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
                  if (tmpList != null && tmpList.size() > 0) {
                     Content playlistContent = (Content)tmpList.get(0);
                     if (playlistContent != null) {
                        thumbnailInfo = playlistContent.getThumb_file_id() + "|" + playlistContent.getThumb_file_name();
                     }
                  }
               }
            }
         }

         vrs = V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder.aV2PlaylistListResourceResponse().playlistName(playlist.getPlaylist_name()).playlistId(playlist.getPlaylist_id()).deviceType(playlist.getDevice_type()).deviceTypeVersion(Float.toString(playlist.getDevice_type_version())).contentCount(playlist.getContent_count()).playTime(playlist.getPlay_time()).totalSize(playlist.getTotal_size()).shareFlag(playlist.getShare_flag()).lastModifiedDate(playlist.getLast_modified_date()).groupName(playlist.getGroup_name()).groupId(playlist.getGroup_id()).playlistMetaData(StrUtils.makeTag(playlist.getPlaylist_meta_data())).creatorId(playlist.getCreator_id()).isVwl(playlist.getIs_vwl()).playlistType(playlist.getPlaylist_type()).versionId(playlist.getVersion_id()).ignoreTag(playlist.getIgnore_tag()).evennessPlayback(playlist.getEvenness_playback()).thumbnailInfo(thumbnailInfo).hasSubPlaylist(hasSubPlaylist).build();
         CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
         tmpList = categoryInfo.getCategoryWithPlaylistId(playlist.getPlaylist_id());
         if (tmpList != null && tmpList.size() > 0) {
            vrs.setCategories(tmpList);
         }
      }

      return res;
   }

   public static V2PlaylistListResourceResponse playlistListResourceResponseBuilder() throws Exception {
      V2PlaylistListResourceResponse res = V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder.aV2PlaylistListResourceResponse().build();
      return res;
   }

   public static V2PlaylistContentResourceResponse playlistContentResourceResponseBuilder(ContentInfo cInfo, Content content) throws Exception {
      new LinkedHashMap();
      String playTime = PlaylistUtils.getContentPlayTime(content);
      Map deviceMap = getDeviceType(content);
      String deviceType = (String)deviceMap.get("deviceType");
      String deviceTypeVersion = (String)deviceMap.get("deviceTypeVersion");
      List tags = playlistContentTagsResourceBuilder(cInfo, content);
      List files = playlistContentFilesResourceBuilder(cInfo, content);
      V2PlaylistContentResourceResponse res = V2PlaylistContentResourceResponse.V2PlaylistContentResourceResponseBuilder.aV2PlaylistContentResourceResponse().contentId(StrUtils.nvl(content.getContent_id())).contentName(StrUtils.nvl(content.getContent_name())).thumbFileId(StrUtils.nvl(content.getThumb_file_id())).thumbFileName(StrUtils.nvl(content.getThumb_file_name())).mediaType(StrUtils.nvl(content.getMedia_type())).totalSize(content.getTotal_size() == null ? 0L : content.getTotal_size()).lastModifiedDate(StrUtils.nvl(content.getLast_modified_date().toString())).deviceType(deviceType).deviceTypeVersion(deviceTypeVersion).playTime(playTime).expiredDate(StrUtils.nvl(content.getExpiration_date())).tags(tags).files(files).resolution(content.getResolution()).build();
      return res;
   }

   public static List playlistItemResourceResponseListBuilder(PlaylistInfo playlistInfo, ContentInfo cInfo, Playlist playlist, List contents) throws Exception {
      List list = new ArrayList();
      Iterator var5 = contents.iterator();

      while(var5.hasNext()) {
         PlaylistContent pc = (PlaylistContent)var5.next();
         list.add(playlistItemResourceResponseBuilder(playlistInfo, cInfo, playlist, pc));
      }

      return list;
   }

   private static V2PlaylistContentEffectResource playlistContentEffectResourceBuilder(PlaylistContent content) throws Exception {
      V2PlaylistContentEffectResource effectResource = new V2PlaylistContentEffectResource();
      effectResource.setInName(content.getEffect_in_name());
      effectResource.setInDuration(content.getEffect_in_duration());
      effectResource.setInDirection(content.getEffect_in_direction());
      effectResource.setOutName(content.getEffect_out_name());
      effectResource.setOutDuration(content.getEffect_out_duration());
      effectResource.setOutDirection(content.getEffect_out_direction());
      effectResource.setStartDate(DateUtils.timestamp2StringDate(content.getStart_date()));
      effectResource.setExpiredDate(DateUtils.timestamp2StringDate(content.getExpired_date()));
      effectResource.setAge(content.getAge());
      effectResource.setGender(content.getGender());
      effectResource.setAmsRecogType(content.getAms_recog_type());
      effectResource.setInDelayDuration(content.getEffect_in_delay_duration());
      effectResource.setOutDelayDuration(content.getEffect_out_delay_duration());
      effectResource.setInDelayDirection(content.getEffect_in_delay_direction());
      effectResource.setOutDelayDirection(content.getEffect_out_delay_direction());
      effectResource.setInDelayDiv(content.getEffect_in_delay_div());
      effectResource.setOutDelayDiv(content.getEffect_out_delay_div());
      effectResource.setIsIndependentPlay(content.getIs_independent_play());
      effectResource.setStartTime(content.getStart_time());
      effectResource.setExpiredTime(content.getExpired_time());
      effectResource.setRepeatType(content.getRepeat_type().replace(",", ";"));
      effectResource.setContiguous(content.getContiguous());
      return effectResource;
   }

   public static V2PlaylistItemResourceResponse playlistItemResourceResponseBuilder(PlaylistInfo playlistInfo, ContentInfo cInfo, Playlist playlist, PlaylistContent pc) throws Exception {
      V2PlaylistItemResourceResponse vr = new V2PlaylistItemResourceResponse();
      Map vt;
      if (pc.getIs_sub_playlist()) {
         Playlist subPlaylist = playlistInfo.getPlaylistActiveVerInfo(pc.getContent_id());
         ContentFile thumbnailFile = playlistInfo.getThumbFileInfo(pc.getContent_id());
         vr.setContentName(subPlaylist.getPlaylist_name());
         vr.setThumbFileName(StrUtils.nvl(thumbnailFile.getFile_id()));
         vr.setThumbFileId(StrUtils.nvl(thumbnailFile.getFile_name()));
         vr.setThumbFilePath(StrUtils.nvl(thumbnailFile.getFile_path()));
         vr.setTotalSize(subPlaylist.getTotal_size());
         vr.setDeviceType(subPlaylist.getDevice_type());
         vr.setDeviceTypeVersion(Float.toString(subPlaylist.getDevice_type_version()));
         vr.setPlayTime(subPlaylist.getPlay_time());
         vr.setIsSubPlaylist(true);
      } else {
         Content content = cInfo.getContentAndFileActiveVerInfo(pc.getContent_id());
         List tags = playlistContentTagsResourceBuilder(cInfo, content);
         List files = playlistContentFilesResourceBuilder(cInfo, content);
         vt = getDeviceType(content);
         String deviceType = (String)vt.get("deviceType");
         String deviceTypeVersion = (String)vt.get("deviceTypeVersion");
         vr.setDeviceType(deviceType);
         vr.setDeviceTypeVersion(deviceTypeVersion);
         vr.setTotalSize(content.getTotal_size() == null ? 0L : content.getTotal_size());
         vr.setLastModifiedDate(content.getLast_modified_date());
         vr.setExpiredDate(StrUtils.nvl(content.getExpiration_date()));
         vr.setFiles(files);
         vr.setTags(tags);
         vr.setPlayTime(PlaylistUtils.getContentPlayTime(content));
         vr.setContentName(content.getContent_name());
         vr.setThumbFileName(StrUtils.nvl(content.getThumb_file_name()));
         vr.setThumbFileId(StrUtils.nvl(content.getThumb_file_id()));
         vr.setThumbFilePath(StrUtils.nvl(content.getThumb_file_path()));
         if (PlaylistUtils.getContentPlayTime(content) != null) {
            String[] parsedArray = PlaylistUtils.getContentPlayTime(content).split(":");
            if (parsedArray.length > 1) {
               vr.setPlayTimeInSeconds(Long.valueOf(parsedArray[0]) * 3600L + Long.valueOf(parsedArray[1]) * 60L + Long.valueOf(parsedArray[2]));
            }
         }
      }

      vr.setContentId(pc.getContent_id());
      vr.setContentOrder(pc.getContent_order());
      vr.setContentDuration(pc.getContent_duration());
      vr.setContentDurationMilli(pc.getContent_duration_milli());
      vr.setEffects(playlistContentEffectResourceBuilder(pc));
      vr.setMediaType(pc.getMedia_type());
      vr.setSyncPlayId(Integer.parseInt(pc.getSync_play_id()) + 1 + "");
      vr.setIsSubPlaylist(pc.getIs_sub_playlist());
      vr.setRandomCount(pc.getRandom_count());
      vr.setPlayWeight(pc.getPlay_weight());
      List tagMap = playlistInfo.getContentTag(playlist.getPlaylist_id(), playlist.getVersion_id(), pc.getContent_id(), Integer.parseInt(Long.toString(pc.getContent_order())));
      ArrayList tagIds = new ArrayList();
      ArrayList tagValues = new ArrayList();
      vt = null;
      if (tagMap != null && tagMap.size() > 0) {
         V2PlaylistContentTagResource vt = new V2PlaylistContentTagResource();
         vr.setTagMatchType((String)((Map)tagMap.get(0)).get("match_type"));
         vt.setMatchType((String)((Map)tagMap.get(0)).get("match_type"));

         for(int k = 0; k < tagMap.size(); ++k) {
            tagIds.add((Long)((Map)tagMap.get(k)).get("tag_id"));
            tagValues.add((String)((Map)tagMap.get(k)).get("tag_value"));
         }

         vt.setTagIds(tagIds);
         vt.setTagValues(tagValues);
         vr.setContentTag(vt);
      }

      return vr;
   }

   private static Map getDeviceType(Content content) throws SQLException {
      String deviceType;
      String deviceTypeVersion;
      if ("".equals(StrUtils.nvl(content.getDevice_type()))) {
         Map typeMap = ContentUtils.getContentDeviceTypeAndVersion(content);
         deviceType = (String)typeMap.get("deviceType");
         deviceTypeVersion = typeMap.get("deviceTypeVersion").toString();
      } else {
         deviceType = content.getDevice_type();
         deviceTypeVersion = "" + content.getDevice_type_version();
      }

      Map map = new HashMap();
      map.put("deviceType", deviceType);
      map.put("deviceTypeVersion", deviceTypeVersion);
      return map;
   }

   public static List getPlaylistVersionsResponse(String playlistID, String productType) throws SQLException {
      PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl(productType);
      List playlists = pInfo.getPlaylistAllVerInfo(playlistID);
      List res = new ArrayList();
      if (playlists != null) {
         res = (List)playlists.stream().map((pl) -> {
            return V2PlaylistVersionResourceResponse.V2PlaylistVersionResourceResponseBuilder.aV2PlaylistVersionResourceResponse().isActive("Y".equalsIgnoreCase(pl.getIs_active())).lastModifiedDate(pl.getCreate_date()).createdBy(pl.getCreator_id()).versionId(pl.getVersion_id()).build();
         }).collect(Collectors.toList());
      }

      return (List)res;
   }
}
