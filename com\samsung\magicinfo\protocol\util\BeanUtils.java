package com.samsung.magicinfo.protocol.util;

import org.springframework.context.ApplicationContext;

public class BeanUtils {
   public BeanUtils() {
      super();
   }

   public static Object getBean(String beanName) {
      ApplicationContext applicationContext = ApplicationContextProvider.getApplicationContext();
      return null == applicationContext ? null : applicationContext.getBean(beanName);
   }
}
