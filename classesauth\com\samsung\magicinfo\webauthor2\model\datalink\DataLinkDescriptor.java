package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.samsung.magicinfo.webauthor2.model.FileInfo;
import com.samsung.magicinfo.webauthor2.model.datalink.LFTContent;
import com.samsung.magicinfo.webauthor2.model.datalink.Page;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class DataLinkDescriptor {
  private String dlkName;
  
  private String dlkContentName;
  
  @JsonIgnore
  private LFTContent lftContent;
  
  private String lftContentId;
  
  private String playTime = "00:00:00";
  
  private String width = "0";
  
  private String height = "0";
  
  private final long pollingInterval;
  
  private final long backupForADay;
  
  private final List<Page> pages;
  
  @JsonIgnore
  private String optDLKContentId;
  
  @JsonIgnore
  private FileInfo optDLKFileInfo;
  
  @JsonIgnore
  private int version = 0;
  
  @JsonCreator
  public DataLinkDescriptor(@JsonProperty(value = "dlkName", required = true) String dlkName, @JsonProperty(value = "dlkContentName", required = true) String dlkContentName, @JsonProperty(value = "lftContentId", required = false) String lftContentId, @JsonProperty(value = "playTime", required = false) String playTime, @JsonProperty(value = "width", required = false) String width, @JsonProperty(value = "height", required = false) String height, @JsonProperty(value = "dlkContentId", required = false) String dlkContentId, @JsonProperty(value = "pollingInterval", required = true) long pollingInterval, @JsonProperty(value = "backupForADay", required = true) long backupForADay, @JsonProperty(value = "pages", required = true) List<Page> pages) {
    this.dlkName = dlkName;
    this.dlkContentName = dlkContentName;
    this.lftContentId = lftContentId;
    this.pollingInterval = pollingInterval;
    this.backupForADay = backupForADay;
    this.pages = pages;
    this.optDLKContentId = dlkContentId;
    this.optDLKFileInfo = null;
    this.playTime = playTime;
    this.width = width;
    this.height = height;
  }
  
  public DataLinkDescriptor(long pollingInterval, long backupForADay, List<Page> pages) {
    this.pollingInterval = pollingInterval;
    this.backupForADay = backupForADay;
    this.pages = pages;
    this.optDLKContentId = null;
    this.optDLKFileInfo = null;
  }
  
  public String getDlkName() {
    return this.dlkName;
  }
  
  public void setDlkContentName(String dlkContentName) {
    this.dlkContentName = dlkContentName;
  }
  
  public String getDlkContentName() {
    return this.dlkContentName;
  }
  
  public void setDlkName(String dlkName) {
    this.dlkName = dlkName;
  }
  
  public LFTContent getLftContent() {
    return this.lftContent;
  }
  
  public void setLftContent(LFTContent lftContent) {
    this.lftContent = lftContent;
  }
  
  public String getLftContentId() {
    return this.lftContentId;
  }
  
  public void setLftContentId(String lftContentId) {
    this.lftContentId = lftContentId;
  }
  
  public List<Page> getPages() {
    return this.pages;
  }
  
  public long getPollingInterval() {
    return this.pollingInterval;
  }
  
  public long getBackupForADay() {
    return this.backupForADay;
  }
  
  public String getOptDLKContentId() {
    return this.optDLKContentId;
  }
  
  public void setOptDLKContentId(String optDLKContentId) {
    this.optDLKContentId = optDLKContentId;
  }
  
  public FileInfo getOptDLKFileInfo() {
    return this.optDLKFileInfo;
  }
  
  public void setOptDLKFileInfo(FileInfo optDLKFileInfo) {
    this.optDLKFileInfo = optDLKFileInfo;
  }
  
  public String getPlayTime() {
    return this.playTime;
  }
  
  public void setPlayTime(String playTime) {
    this.playTime = playTime;
  }
  
  public String getWidth() {
    return this.width;
  }
  
  public void setWidth(String width) {
    this.width = width;
  }
  
  public String getHeight() {
    return this.height;
  }
  
  public void setHeight(String height) {
    this.height = height;
  }
  
  public int getVersion() {
    return this.version;
  }
  
  public void setVersion(int version) {
    this.version = version;
  }
}
