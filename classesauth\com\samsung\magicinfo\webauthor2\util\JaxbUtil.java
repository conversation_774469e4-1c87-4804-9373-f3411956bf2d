package com.samsung.magicinfo.webauthor2.util;

import com.samsung.magicinfo.webauthor2.exception.util.ConvertTableException;
import com.samsung.magicinfo.webauthor2.exception.util.SearchCriteriaException;
import com.samsung.magicinfo.webauthor2.model.ConvertTable;
import com.samsung.magicinfo.webauthor2.repository.model.criteria.ContentSearchCriteria;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertDataData;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertTableData;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertTableResultListData;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ResponseConvertTableData;
import com.samsung.magicinfo.webauthor2.xml.csd.CSDFileType;
import com.samsung.magicinfo.webauthor2.xml.csd.CSDTransferFileType;
import java.util.HashMap;
import java.util.Map;
import javax.inject.Inject;
import javax.xml.transform.Result;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Service;
import org.springframework.xml.transform.StringResult;

@Service
public class JaxbUtil {
  private Jaxb2Marshaller jaxb2MarshallerFragment;
  
  @Inject
  public JaxbUtil(Jaxb2Marshaller jaxb2MarshallerFragment) {
    this.jaxb2MarshallerFragment = jaxb2MarshallerFragment;
  }
  
  public String searchCriteria(ContentSearchCriteria criteria) {
    try {
      StringResult result = new StringResult();
      this.jaxb2MarshallerFragment.marshal(criteria, (Result)result);
      return result.toString();
    } catch (Exception e) {
      throw new SearchCriteriaException("Search criteria failed.");
    } 
  }
  
  public String convertTable(ConvertTableData convertTableData) {
    try {
      Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
      Map<String, Object> props = new HashMap<>();
      props.put("jaxb.formatted.output", Boolean.TRUE);
      props.put("jaxb.fragment", Boolean.FALSE);
      props.put("jaxb.encoding", "UTF-8");
      jaxb2Marshaller.setClassesToBeBound(new Class[] { CSDTransferFileType.class, CSDFileType.class, ConvertTableData.class, ConvertDataData.class, ConvertTableResultListData.class, ResponseConvertTableData.class, ConvertTable.class, ContentSearchCriteria.class });
      jaxb2Marshaller.setMarshallerProperties(props);
      StringResult result = new StringResult();
      jaxb2Marshaller.marshal(convertTableData, (Result)result);
      return result.toString();
    } catch (Exception e) {
      throw new ConvertTableException("Convert Table failed.");
    } 
  }
}
