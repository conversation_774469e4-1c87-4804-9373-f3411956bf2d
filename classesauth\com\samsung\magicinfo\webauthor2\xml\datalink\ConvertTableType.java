package com.samsung.magicinfo.webauthor2.xml.datalink;

import com.samsung.magicinfo.webauthor2.xml.datalink.ConvertTableRowType;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ConvertTableType", propOrder = {"name", "rows"})
public class ConvertTableType {
  @XmlAttribute(name = "name")
  private String name;
  
  @XmlElement(name = "Row")
  private List<ConvertTableRowType> rows;
  
  public List<ConvertTableRowType> getRows() {
    return this.rows;
  }
  
  public void setRows(List<ConvertTableRowType> rows) {
    this.rows = rows;
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
}
