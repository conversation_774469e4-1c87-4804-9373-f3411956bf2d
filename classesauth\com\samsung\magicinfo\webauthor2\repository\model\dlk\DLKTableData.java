package com.samsung.magicinfo.webauthor2.repository.model.dlk;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
public class DLKTableData {
  @XmlAttribute
  private String type;
  
  @XmlElement
  private String svrcName;
  
  @XmlElement
  private String dynaName;
  
  @XmlElement
  private String name;
  
  @XmlElement
  private Boolean isDataView;
  
  public String getType() {
    return this.type;
  }
  
  public void setType(String type) {
    this.type = type;
  }
  
  public String getSvrcName() {
    return this.svrcName;
  }
  
  public void setSvrcName(String svrcName) {
    this.svrcName = svrcName;
  }
  
  public String getDynaName() {
    return this.dynaName;
  }
  
  public void setDynaName(String dynaName) {
    this.dynaName = dynaName;
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public Boolean getIsDataView() {
    return this.isDataView;
  }
  
  public void seIsDataView(Boolean isDataView) {
    this.isDataView = isDataView;
  }
}
