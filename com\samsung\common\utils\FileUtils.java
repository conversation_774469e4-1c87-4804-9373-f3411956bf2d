package com.samsung.common.utils;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FilenameFilter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.channels.FileChannel.MapMode;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.Logger;

public class FileUtils {
   static Logger logger = LoggingManagerV2.getLogger(FileUtils.class);
   private static Charset charset = Charset.forName("ISO-8859-15");
   private static CharsetDecoder decoder;

   public FileUtils() {
      super();
   }

   public static String getFileName(String fullPathName) {
      String fileName = null;
      if (fullPathName == null) {
         return null;
      } else {
         int startIdx1 = fullPathName.lastIndexOf(92);
         int startIdx2 = fullPathName.lastIndexOf(47);
         int startIdx = startIdx1;
         if (startIdx1 < startIdx2) {
            startIdx = startIdx2;
         }

         fileName = fullPathName.substring(startIdx + 1, fullPathName.length());
         return fileName;
      }
   }

   public static String SizeToString(Long fileSize) {
      NumberFormat nf = NumberFormat.getIntegerInstance();
      DecimalFormat df = new DecimalFormat("#,##0.00");
      int intSize = false;
      double dval = 0.0D;
      double ttval = 0.0D;
      double doubleSize = 0.0D;
      int kbyteSize = 1024;
      String returnSize = null;
      if (fileSize >= (long)(kbyteSize * kbyteSize * kbyteSize)) {
         ttval = (double)fileSize;
         dval = ttval / (double)(kbyteSize * kbyteSize * kbyteSize);
         return df.format(Math.floor(dval * 100.0D) / 100.0D) + " GB";
      } else {
         int intSize;
         if (fileSize >= (long)(kbyteSize * kbyteSize)) {
            intSize = (int)((double)fileSize / (double)(kbyteSize * kbyteSize) * 100.0D);
            doubleSize = (double)intSize / 100.0D;
            returnSize = df.format(doubleSize);
            if (returnSize.lastIndexOf(".") != -1 && "00".equals(returnSize.substring(returnSize.length() - 2, returnSize.length()))) {
               returnSize = returnSize.substring(0, returnSize.lastIndexOf("."));
            }

            return returnSize + " MB";
         } else if (fileSize >= (long)kbyteSize) {
            intSize = (new Long(fileSize / (long)kbyteSize)).intValue();
            return nf.format((long)intSize) + " KB";
         } else {
            return nf.format(fileSize) + " Byte";
         }
      }
   }

   public static String mbTranslater(long size) {
      NumberFormat nf = NumberFormat.getIntegerInstance();
      DecimalFormat df = new DecimalFormat("#,##0.00");
      int intSize = false;
      double dval = 0.0D;
      double ttval = 0.0D;
      int mbSize = 1;
      if (size >= 1024L) {
         ttval = (double)size;
         dval = ttval / 1024.0D;
         return df.format(dval) + " GB";
      } else if (size > (long)mbSize) {
         int intSize = (new Long(size / (long)mbSize)).intValue();
         return nf.format((long)intSize) + " MB";
      } else {
         return "0 MB";
      }
   }

   public static boolean fileCopy(String srcPath, String trgPath) throws IOException {
      boolean brtn = false;
      FileInputStream fis = null;
      FileOutputStream fos = null;
      FileChannel fcin = null;
      FileChannel fcout = null;

      try {
         fis = new FileInputStream(srcPath);
         fos = new FileOutputStream(trgPath);
         fcin = fis.getChannel();
         fcout = fos.getChannel();
         long size = fcin.size();
         fcin.transferTo(0L, size, fcout);
      } catch (Exception var17) {
         logger.error("", var17);
      } finally {
         try {
            if (fis != null) {
               fis.close();
            }

            if (fos != null) {
               fos.close();
            }

            if (fcin != null) {
               fcin.close();
            }

            if (fcout != null) {
               fcout.close();
            }
         } catch (IOException var16) {
            logger.error("", var16);
         }

      }

      brtn = true;
      return brtn;
   }

   public static File fileMove(String srcPath, String trgPath) throws IOException {
      File srcFile = SecurityUtils.getSafeFile(srcPath);
      File trgFile = SecurityUtils.getSafeFile(trgPath);
      if (srcFile.exists()) {
         if (trgFile.exists()) {
            trgFile.delete();
         }

         String fin;
         String fout;
         String fcin;
         if (!createNewFile(trgFile)) {
            fin = trgFile.getName();
            fout = null;
            fcin = null;
            int dot = fin.lastIndexOf(".");
            if (dot != -1) {
               fout = fin.substring(0, dot);
               fcin = fin.substring(dot);
            } else {
               fout = fin;
               fcin = "";
            }

            String newName;
            for(int count = 0; !createNewFile(trgFile) && count < 9999; trgFile = SecurityUtils.getSafeFile(trgFile.getParent(), newName)) {
               ++count;
               newName = fout + count + fcin;
            }
         }

         fin = null;
         fout = null;
         fcin = null;
         FileChannel fcout = null;

         try {
            FileInputStream fin = new FileInputStream(srcFile);
            FileOutputStream fout = new FileOutputStream(trgFile);
            FileChannel fcin = fin.getChannel();
            fcout = fout.getChannel();
            long size = fcin.size();
            fcin.transferTo(0L, size, fcout);
            fcout.close();
            fcout = null;
            fcin.close();
            fcin = null;
            fout.close();
            fout = null;
            fin.close();
            fin = null;
            srcFile.delete();
         } catch (IOException var34) {
            try {
               if (fin != null) {
                  fin.close();
               }
            } catch (Exception var33) {
               logger.error("", var33);
            }

            try {
               if (fout != null) {
                  fout.close();
               }
            } catch (Exception var32) {
               logger.error("", var32);
            }

            try {
               if (fcin != null) {
                  fcin.close();
               }
            } catch (Exception var31) {
               logger.error("", var31);
            }

            try {
               if (fcout != null) {
                  fcout.close();
               }
            } catch (Exception var30) {
               logger.error("", var30);
            }

            throw var34;
         } finally {
            try {
               if (fin != null) {
                  fin.close();
               }
            } catch (Exception var29) {
               logger.error("", var29);
            }

            try {
               if (fout != null) {
                  fout.close();
               }
            } catch (Exception var28) {
               logger.error("", var28);
            }

            try {
               if (fcin != null) {
                  fcin.close();
               }
            } catch (Exception var27) {
               logger.error("", var27);
            }

            try {
               if (fcout != null) {
                  fcout.close();
               }
            } catch (Exception var26) {
               logger.error("", var26);
            }

         }
      }

      return trgFile;
   }

   private static boolean createNewFile(File file) throws IOException {
      return file.createNewFile();
   }

   public static boolean mkCopyBat(String srcPath, String trgPath, String path) throws InterruptedException, IOException {
      boolean brtn = false;
      Runtime runtime = Runtime.getRuntime();
      Process proc = null;
      String batPath = path + "copyBat.bat";
      StringBuffer sbuf = new StringBuffer();
      sbuf.append("copy %1 %2 ");
      FileOutputStream fos = null;

      try {
         File bfile = SecurityUtils.getSafeFile(batPath);
         if (!bfile.exists()) {
            fos = new FileOutputStream(batPath);
            fos.write(sbuf.toString().getBytes("UTF-8"));
            fos.close();
         }

         String[] cmdArr = new String[]{batPath, srcPath, trgPath};
         boolean validCMD = SecurityUtils.checkCMDInjection(cmdArr);
         if (!validCMD) {
            throw new Exception("Not valid CMD(Prevent cmd injection) CMD is =>" + cmdArr.toString());
         }

         proc = runtime.exec(cmdArr);
         proc.waitFor();
         proc.destroy();
         brtn = true;
      } catch (Exception var15) {
         if (fos != null) {
            fos.close();
         }

         logger.error("", var15);
      } finally {
         if (fos != null) {
            fos.close();
         }

      }

      return brtn;
   }

   public static boolean mkXCopyBat(String srcPath, String trgPath, String path, String cid, String vwl_id) throws Exception {
      boolean brtn = false;
      Runtime runtime = Runtime.getRuntime();
      Process proc = null;
      String batPath = path + "copyBat.bat";
      StringBuffer sbuf = new StringBuffer();
      sbuf.append("copy %1 %2 ");
      FileOutputStream fos = null;
      InputStream is = null;
      BufferedReader br = null;

      try {
         File trg = SecurityUtils.getSafeFile(trgPath);
         if (!trg.exists()) {
            trg.mkdir();
         }

         File bfile = SecurityUtils.getSafeFile(batPath);
         if (!bfile.exists()) {
            fos = new FileOutputStream(batPath);
            fos.write(sbuf.toString().getBytes("UTF-8"));
            fos.close();
         }

         mkAllDir(srcPath, cid, vwl_id);
         File tmpfile = SecurityUtils.getSafeFile(srcPath);
         File[] files = tmpfile.listFiles();

         for(int k = 0; k < files.length; ++k) {
            String[] cmdArr = new String[]{batPath, files[k].getPath(), files[k].getPath().replaceAll(cid, vwl_id)};
            proc = runtime.exec(cmdArr);
            is = proc.getInputStream();
            br = new BufferedReader(new InputStreamReader(is));

            while(br.readLine() != null) {
            }

            br.close();
            is.close();
         }

         brtn = true;
      } catch (IOException var22) {
         logger.error("", var22);
         if (fos != null) {
            fos.close();
         }

         if (is != null) {
            is.close();
         }

         if (br != null) {
            br.close();
         }
      } finally {
         if (fos != null) {
            fos.close();
         }

         if (is != null) {
            is.close();
         }

         if (br != null) {
            br.close();
         }

      }

      return brtn;
   }

   public static int deleteDir(String path) throws Exception {
      return deleteDir(SecurityUtils.getSafeFile(path));
   }

   public static int deleteDir(File file) throws Exception {
      if (!file.exists()) {
         return 1;
      } else {
         File[] files = file.listFiles();

         for(int i = 0; i < files.length; ++i) {
            if (files[i].isDirectory()) {
               deleteDir(files[i]);
            } else {
               files[i].delete();
            }
         }

         boolean flag = file.delete();
         if (flag) {
            return 0;
         } else {
            return 1;
         }
      }
   }

   public static boolean deleteFilesRecursively(File rootFile) {
      File[] allFiles = rootFile.listFiles();
      if (allFiles != null) {
         File[] var2 = allFiles;
         int var3 = allFiles.length;

         for(int var4 = 0; var4 < var3; ++var4) {
            File file = var2[var4];
            deleteFilesRecursively(file);
         }
      }

      return rootFile.delete();
   }

   public static int deleteStatisticsDir(String path, Date date, String type) throws Exception {
      return deleteStatisticsDir(SecurityUtils.getSafeFile(path), date, type);
   }

   public static int deleteStatisticsDir(File file, Date date, String type) throws Exception {
      int deleteCount = 0;
      String tempFileName = null;
      if (!file.exists()) {
         return 0;
      } else {
         File[] files = file.listFiles();

         for(int i = 0; i < files.length; ++i) {
            if (!files[i].isDirectory()) {
               tempFileName = files[i].getName();
               if (!getExtenstion(tempFileName).equalsIgnoreCase("txt")) {
                  long lastModified = files[i].lastModified();
                  Date lastModifiedDate = new Date(lastModified);
                  if (lastModifiedDate.before(date)) {
                     files[i].delete();
                     ++deleteCount;
                  }
               }
            }
         }

         return deleteCount;
      }
   }

   public static int cleanDir(String path, Date date) throws Exception {
      return cleanDir(SecurityUtils.getSafeFile(path), date);
   }

   private static int cleanDir(File file, Date date) {
      int deleteCount = 0;
      if (!file.exists()) {
         return 0;
      } else {
         File[] files = file.listFiles();

         for(int i = 0; i < files.length; ++i) {
            if (!files[i].isDirectory()) {
               long lastModified = files[i].lastModified();
               Date lastModifiedDate = new Date(lastModified);
               if (lastModifiedDate.before(date)) {
                  files[i].delete();
                  ++deleteCount;
               }
            } else {
               cleanDir(files[i], date);
            }
         }

         return deleteCount;
      }
   }

   private static String getExtenstion(String fileName) {
      String[] token = fileName.split("\\.");
      String extension = token[token.length - 1];
      return extension.toLowerCase();
   }

   public static boolean deleteDirProc(String path, String trgPath) throws Exception {
      Runtime runtime = Runtime.getRuntime();
      Process proc = null;
      path = path.replace('/', File.separatorChar);
      String batPath = path + File.separatorChar + "delBat.bat";
      StringBuffer sbuf = new StringBuffer();
      sbuf.append("rmdir /S/Q %1 ");
      boolean brtn = false;
      FileOutputStream fos = null;
      InputStream is = null;
      BufferedReader br = null;

      try {
         File bfile = SecurityUtils.getSafeFile(batPath);
         if (!bfile.exists()) {
            fos = new FileOutputStream(batPath);
            fos.write(sbuf.toString().getBytes("UTF-8"));
            fos.close();
         }

         String[] cmdArr = new String[]{batPath, trgPath};
         proc = runtime.exec(cmdArr);
         is = proc.getInputStream();
         br = new BufferedReader(new InputStreamReader(is));

         while(true) {
            if (br.readLine() == null) {
               br.close();
               is.close();
               brtn = true;
               break;
            }
         }
      } catch (Exception var15) {
         logger.error("", var15);
         if (fos != null) {
            fos.close();
         }

         if (br != null) {
            br.close();
         }

         if (is != null) {
            is.close();
         }
      } finally {
         if (fos != null) {
            fos.close();
         }

      }

      return brtn;
   }

   public static int mkAllDir(String path, String cid, String vid) throws Exception {
      return mkAllDir(SecurityUtils.getSafeFile(path), cid, vid);
   }

   public static int mkAllDir(File file, String cid, String vid) throws Exception {
      if (!file.exists()) {
         return 1;
      } else {
         File[] files = file.listFiles();

         for(int i = 0; i < files.length; ++i) {
            if (files[i].isDirectory()) {
               File vfile = SecurityUtils.getSafeFile(files[i].getPath().replaceAll(cid, vid));
               vfile.mkdir();
               mkAllDir(files[i], cid, vid);
            }
         }

         boolean flag = file.mkdir();
         if (flag) {
            return 0;
         } else {
            return 1;
         }
      }
   }

   public static void removeContentFiles(String srcPath) throws Exception {
      File bak_home = SecurityUtils.getSafeFile(srcPath);
      if (bak_home.exists()) {
         File dir_file = SecurityUtils.getSafeFile(srcPath);
         File[] files = dir_file.listFiles();
         if (files != null) {
            for(int p = 0; p < files.length; ++p) {
               boolean isDeleted = files[p].delete();
               if (!isDeleted) {
                  System.gc();
                  isDeleted = files[p].delete();
                  if (!isDeleted) {
                     files[p].deleteOnExit();
                  }
               }
            }
         }

         boolean isDeleted = dir_file.delete();
         if (!isDeleted) {
            System.gc();
            dir_file.delete();
            if (!isDeleted) {
               dir_file.deleteOnExit();
            }
         }
      }

   }

   public static ArrayList getFiles(String dir, String extName) throws Exception {
      ArrayList dirList = new ArrayList();
      dirList = getDirs(dirList, dir);
      ExtensionFileFilter filter = new ExtensionFileFilter(extName);
      ArrayList fileList = new ArrayList();

      for(int i = 0; i < dirList.size(); ++i) {
         File currentDir = (File)dirList.get(i);
         File[] files = currentDir.listFiles(filter);

         for(int j = 0; j < files.length; ++j) {
            fileList.add(currentDir.getAbsolutePath() + File.separator + files[j].getName());
         }
      }

      return fileList;
   }

   public static ArrayList getDirs(ArrayList dirList, String dir) throws Exception {
      File parentDir = SecurityUtils.getSafeFile(dir);
      dirList.add(parentDir);
      String[] directoryList = parentDir.list();

      for(int i = 0; i < directoryList.length; ++i) {
         String sub = directoryList[i];
         File subDir = SecurityUtils.getSafeFile(dir + File.separator + sub);
         if (subDir.isDirectory()) {
            dirList = getDirs(dirList, dir + File.separator + sub);
         }
      }

      return dirList;
   }

   public static boolean containsPattern(File file, Pattern pattern) throws IOException {
      Matcher pm = pattern.matcher(file.getName());
      return pm.find();
   }

   public static String[] findPattern(File file, Pattern pattern, int group) throws IOException {
      FileInputStream fis = null;
      FileChannel fc = null;

      try {
         ArrayList occurences = new ArrayList();
         fis = new FileInputStream(file);
         fc = fis.getChannel();
         int sz = (int)fc.size();
         MappedByteBuffer bb = fc.map(MapMode.READ_ONLY, 0L, (long)sz);
         CharBuffer cb = decoder.decode(bb);
         Matcher pm = pattern.matcher(cb);

         while(pm.find()) {
            occurences.add(pm.group(group));
         }

         fc.close();
         fc = null;
         fis.close();
         fis = null;
         return (String[])((String[])occurences.toArray(new String[occurences.size()]));
      } catch (IOException var12) {
         try {
            if (fis != null) {
               fis.close();
            }
         } catch (Exception var11) {
            logger.error("", var11);
         }

         try {
            if (fc != null) {
               fc.close();
            }
         } catch (Exception var10) {
            logger.error("", var10);
         }

         throw var12;
      }
   }

   private static List findInternal(File file, Pattern pattern) throws IOException {
      ArrayList fileList = new ArrayList();
      if (file.isDirectory()) {
         String[] children = file.list();

         for(int i = 0; i < children.length; ++i) {
            fileList.addAll(findInternal(SecurityUtils.getSafeFile(file.getAbsolutePath(), children[i]), pattern));
         }
      } else if (file.isFile() && containsPattern(file, pattern)) {
         fileList.add(file);
      }

      return fileList;
   }

   private static List findInternalWithDirectory(File file, Pattern pattern) throws IOException {
      ArrayList fileList = new ArrayList();
      if (file.isDirectory()) {
         fileList.add(file);
         String[] children = file.list();

         for(int i = 0; i < children.length; ++i) {
            fileList.addAll(findInternalWithDirectory(SecurityUtils.getSafeFile(file.getAbsolutePath(), children[i]), pattern));
         }
      } else if (file.isFile() && containsPattern(file, pattern)) {
         fileList.add(file);
      }

      return fileList;
   }

   public static File[] find(File file, String searchString) throws IOException {
      Pattern pattern = Pattern.compile(searchString);
      List fileList = findInternal(file, pattern);
      return (File[])((File[])fileList.toArray(new File[fileList.size()]));
   }

   public static File[] find(File file, String searchString, boolean containsDirectory) throws IOException {
      Pattern pattern = Pattern.compile(searchString);
      List fileList = containsDirectory ? findInternalWithDirectory(file, pattern) : findInternal(file, pattern);
      return (File[])((File[])fileList.toArray(new File[fileList.size()]));
   }

   public static void deletePreviousJnlp(String filename) {
      File jnlpFolder = SecurityUtils.getSafeFile(filename);
      File[] jnlpFolArr = jnlpFolder.listFiles();
      SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
      if (jnlpFolArr != null) {
         for(int i = 0; i < jnlpFolArr.length; ++i) {
            if (!df.format(new Date(jnlpFolArr[i].lastModified())).equals(df.format(new Date(System.currentTimeMillis())))) {
               jnlpFolArr[i].delete();
            }
         }
      }

   }

   public static String getPathFolder(String contentFileId) throws ConfigException {
      String result = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separator + "contents_home" + File.separator + contentFileId;
      return result;
   }

   public static String getPathFile(String contentFileId, String contentFileName) throws ConfigException {
      String result = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separator + "contents_home" + File.separator + contentFileId + File.separator + contentFileName;
      return result;
   }

   public static boolean makeFolder(String newFileFolder) {
      boolean result = false;
      File fileCmsFile = SecurityUtils.getSafeFile(newFileFolder);
      if (!fileCmsFile.exists()) {
         result = fileCmsFile.mkdir();
      } else {
         result = true;
      }

      return result;
   }

   public static boolean deleteDirectoryRecursive(File filePath) {
      if (filePath == null) {
         return false;
      } else {
         if (filePath.exists()) {
            File[] files = filePath.listFiles();
            if (files == null) {
               return false;
            }

            for(int i = 0; i < files.length; ++i) {
               if (files[i] == null) {
                  return false;
               }

               if (files[i].isDirectory()) {
                  deleteDirectoryRecursive(files[i]);
               } else {
                  files[i].delete();
               }
            }
         }

         return filePath.delete();
      }
   }

   public static String formatSizeGB(Object obj) {
      long bytes = -1L;
      if (obj instanceof Long) {
         bytes = (Long)obj;
      } else if (obj instanceof Integer) {
         bytes = (long)(Integer)obj;
      }

      long gbytes = bytes / 1073741824L;
      return gbytes + "GB";
   }

   public static String formatSize(Object obj, boolean mb) {
      long bytes = -1L;
      if (obj instanceof Long) {
         bytes = (Long)obj;
      } else if (obj instanceof Integer) {
         bytes = (long)(Integer)obj;
      }

      if (mb) {
         long mbytes = bytes / 1048576L;
         long rest = (bytes - mbytes * 1048576L) * 100L / 1048576L;
         return mbytes + "." + (rest < 10L ? "0" : "") + rest + " MB";
      } else {
         return bytes / 1024L + " KB";
      }
   }

   public static FilenameFilter remoteFolderfilter() {
      FilenameFilter fnf = new FilenameFilter() {
         public boolean accept(File dir, String name) {
            return name.startsWith("CIFS_") || name.startsWith("FTP_");
         }
      };
      return fnf;
   }

   public static String getHash(File file) throws Exception {
      StringBuffer hash = new StringBuffer("");
      ByteBuffer buf = ByteBuffer.allocate(1024);
      FileInputStream fileIS = null;

      try {
         int index = 0;
         if (file.exists()) {
            fileIS = new FileInputStream(file);
            FileChannel fileChannel = fileIS.getChannel();
            MessageDigest messageDigest = MessageDigest.getInstance("SHA1");

            int nread;
            for(long fileOffsetLong = 0L; (nread = fileChannel.read(buf, fileOffsetLong)) != -1; fileOffsetLong += (long)nread) {
               buf.flip();
               messageDigest.update(buf);
               buf.clear();
            }

            byte[] digest = messageDigest.digest();
            StringBuffer tmpSB = new StringBuffer();
            byte[] var12 = digest;
            int var13 = digest.length;

            for(int var14 = 0; var14 < var13; ++var14) {
               byte b = var12[var14];
               StringBuffer str = new StringBuffer(Integer.toHexString(b & 255));
               if (str.length() == 1) {
                  str = (new StringBuffer("0")).append(str);
               }

               if (index > 7 && index < 16) {
                  tmpSB = tmpSB.append(str);
               } else {
                  tmpSB = str.append(tmpSB);
               }

               if (index == 3 || index == 5 || index == 7 || index == 9 || index == 15) {
                  hash.append(tmpSB).append("-");
                  tmpSB = new StringBuffer();
               }

               ++index;
            }

            hash.append(tmpSB);
         }
      } catch (IOException var22) {
         logger.error("", var22);
         throw new Exception();
      } catch (NoSuchAlgorithmException var23) {
         throw new Exception();
      } catch (Exception var24) {
         logger.error("File Name : " + file.getName(), var24);
      } finally {
         if (fileIS != null) {
            fileIS.close();
         }

      }

      return hash.equals("") ? null : hash.toString().toUpperCase();
   }

   public static byte[] getPictureToByte(String path, String type) throws IOException {
      BufferedImage originalImage = ImageIO.read(new File(path));
      ByteArrayOutputStream baos = new ByteArrayOutputStream();
      ImageIO.write(originalImage, type, baos);
      baos.flush();
      byte[] imageInByte = baos.toByteArray();
      return imageInByte;
   }

   public static byte[] base64Enc(byte[] buffer) {
      return Base64.encodeBase64(buffer);
   }

   public static void setFileToResponse(HttpServletRequest request, HttpServletResponse response, File file, String mime) throws UnsupportedEncodingException {
      byte[] buffer = new byte[1024];
      String filename = file.getName();
      response.setContentType(mime);
      String userAgent = request.getHeader("User-Agent");
      if (userAgent != null && userAgent.indexOf("MSIE 5.5") > -1) {
         response.setHeader("Content-Disposition", "filename=" + URLEncoder.encode(filename, "UTF-8") + ";");
      } else if (userAgent != null && userAgent.indexOf("MSIE") > -1) {
         response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(filename, "UTF-8") + ";");
      } else {
         response.setHeader("Content-Disposition", "attachment; filename=" + new String(filename.getBytes("utf-8"), "latin1") + ";");
      }

      response.setHeader("Set-Cookie", "fileDownload=true; path=/");
      BufferedInputStream fin = null;
      BufferedOutputStream outs = null;

      try {
         InputStream is = new FileInputStream(file);
         fin = new BufferedInputStream(is);
         outs = new BufferedOutputStream(response.getOutputStream());
         boolean var10 = false;

         int read;
         while((read = fin.read(buffer)) != -1) {
            outs.write(buffer, 0, read);
         }
      } catch (IOException var23) {
         logger.error(var23);
      } finally {
         try {
            outs.close();
         } catch (Exception var22) {
            logger.error(var22);
         }

         try {
            fin.close();
         } catch (Exception var21) {
            logger.error(var21);
         }

         response.setHeader("Content-Length", String.valueOf(filename.length()));
      }

   }

   static {
      decoder = charset.newDecoder();
   }
}
