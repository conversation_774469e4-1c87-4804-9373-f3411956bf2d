package com.samsung.magicinfo.framework.device.service.deviceConf;

import com.jamesmurty.utils.XMLBuilder;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceLogCollectEntity;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.file.FileUploadCommonHelper;
import com.samsung.magicinfo.protocol.util.TimeUtil;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.StringReader;
import java.text.ParseException;
import javax.xml.parsers.FactoryConfigurationError;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;
import org.apache.logging.log4j.Logger;

public class LogProcessFileManager {
   static Logger logger = LoggingManagerV2.getLogger(LogProcessFileManager.class);

   public LogProcessFileManager() {
      super();
   }

   public File createLogProcessFile(DeviceLogCollectEntity logCollectEntity, String magicinfo_server_url) throws ConfigException, IOException, ParseException, ParserConfigurationException, FactoryConfigurationError, TransformerException {
      String log_collect_home = CommonConfig.get("UPLOAD_HOME");
      String fileSavePath = FileUploadCommonHelper.getWebPath("LOG_PROCESS");
      File f = SecurityUtils.getSafeFile(log_collect_home + File.separatorChar + fileSavePath);
      if (!f.exists() && !f.mkdir()) {
      }

      String formatStartTime = logCollectEntity.getToken();
      String fileName = "log_" + formatStartTime + "_" + logCollectEntity.getDevice_id() + ".info";
      File logInfoFile = SecurityUtils.getSafeFile(log_collect_home + File.separatorChar + fileSavePath + File.separatorChar + fileName);
      TimeUtil util = new TimeUtil();
      String status = "START";
      String categoryScript = logCollectEntity.getCategory_script();
      String duration = String.valueOf(logCollectEntity.getDuration());
      String currentTime = TimeUtil.getCurrentGMTTimeStr();
      String startTime = "";
      String logSize = StrUtils.nvl(CommonConfig.get("device.log_collect.default_packet_size"));
      String encryption = StrUtils.nvl(CommonConfig.get("device.log_collect.encryption"));
      String smartDiagnosticVersion = String.valueOf(logCollectEntity.getSmartDiagnosticVersion());
      if (logSize == null || logSize.equals("")) {
         logSize = "0.1";
      }

      String url = magicinfo_server_url + "/servlet/DeviceLogUploadServlet";
      if (logCollectEntity.getStatus().equalsIgnoreCase("END")) {
         status = "END";
         categoryScript = "";
         duration = "";
         currentTime = "";
         logSize = "";
         url = "";
         smartDiagnosticVersion = "";
      }

      if ("true".equalsIgnoreCase(encryption)) {
         encryption = "ON";
      } else {
         encryption = "OFF";
      }

      XMLBuilder builder = XMLBuilder.create("log");
      builder.e("command").t(status).up().e("debug_script").t(categoryScript).up().e("duration").t(duration).up().e("current_time").t(currentTime).up().e("start_time").t(startTime).up().e("send_log_size").t(logSize).up().e("log_server_url").t(url).up().e("token").t(formatStartTime).up().e("encryption").t(encryption).up().e("smart_diagnostic_version").t(smartDiagnosticVersion);
      BufferedReader in = null;
      BufferedWriter out = null;

      try {
         in = new BufferedReader(new StringReader(builder.asString()));
         out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(logInfoFile), "UTF-8"));

         String s;
         while((s = in.readLine()) != null) {
            out.write(s);
            out.newLine();
         }
      } catch (Exception var35) {
         logger.error("", var35);
      } finally {
         if (in != null) {
            try {
               in.close();
            } catch (Exception var34) {
            }
         }

         if (out != null) {
            try {
               out.close();
            } catch (Exception var33) {
            }
         }

      }

      return logInfoFile;
   }
}
