package com.samsung.magicinfo.webauthor2.webapi.assembler;

import com.samsung.magicinfo.webauthor2.model.DataLinkTable;
import com.samsung.magicinfo.webauthor2.webapi.controller.DataLinkQueryController;
import com.samsung.magicinfo.webauthor2.webapi.resource.DataLinkTableResource;
import org.springframework.hateoas.ResourceSupport;
import org.springframework.hateoas.mvc.ControllerLinkBuilder;
import org.springframework.hateoas.mvc.ResourceAssemblerSupport;
import org.springframework.stereotype.Component;

@Component
public class DataLinkTableResourceAssembler extends ResourceAssemblerSupport<DataLinkTable, DataLinkTableResource> {
  public DataLinkTableResourceAssembler() {
    super(DataLinkQueryController.class, DataLinkTableResource.class);
  }
  
  public DataLinkTableResource toResource(DataLinkTable dataLinkTable) {
    DataLinkTableResource dataLinkSourceResource = new DataLinkTableResource(dataLinkTable, new org.springframework.hateoas.Link[0]);
    dataLinkSourceResource.add(ControllerLinkBuilder.linkTo(((DataLinkQueryController)ControllerLinkBuilder.methodOn(DataLinkQueryController.class, new Object[0]))
          .getDatalinkTableInfo(dataLinkTable.getServerName(), dataLinkTable.getDynaName())).withRel("tableInfo"));
    return dataLinkSourceResource;
  }
}
