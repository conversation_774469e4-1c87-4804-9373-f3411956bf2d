package com.samsung.magicinfo.framework.device.job.dao;

import com.samsung.common.db.PagedListInfo;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.job.entity.JobEntity;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;

public class JobDAO extends SqlSessionBaseDao {
   public JobDAO() {
      super();
   }

   public boolean addFileId(String fileId, String fileName, long fileSize, String hashCode) throws SQLException {
      return ((JobDAOMapper)this.getMapper()).addFileId(fileId, fileName, fileSize, hashCode);
   }

   public boolean addJob(JobEntity jobEntity, String[] groupIds, String[] deviceIds, String mode) throws SQLException {
      SqlSession session = this.openNewSession(false);
      JobDAOMapper mapper = (Job<PERSON>OMapper)session.getMapper(JobDAOMapper.class);
      boolean result = false;

      try {
         result = mapper.insertJobQuery(jobEntity);
         if (!result) {
            session.rollback();
            boolean var20 = false;
            return var20;
         } else {
            int i;
            if (mode.equalsIgnoreCase("LFD")) {
               for(i = 0; i < groupIds.length; ++i) {
                  if (groupIds[i] != null && !groupIds[i].equals("")) {
                     Long groupId = new Long(groupIds[i]);
                     result = mapper.insertGroupMapQueryLFD(jobEntity.getJob_id(), groupId);
                     if (!result) {
                        session.rollback();
                        boolean var10 = false;
                        return var10;
                     }
                  }
               }
            } else {
               long jobId;
               boolean var12;
               if (mode.equalsIgnoreCase("VIDEOWALL")) {
                  for(i = 0; i < groupIds.length; ++i) {
                     if (groupIds[i] != null && !groupIds[i].equals("")) {
                        jobId = jobEntity.getJob_id();
                        String consoleId = groupIds[i];
                        result = mapper.insertGroupMapQueryVIDEOWALL(jobId, consoleId);
                        if (!result) {
                           session.rollback();
                           var12 = false;
                           return var12;
                        }
                     }
                  }
               } else {
                  for(i = 0; i < groupIds.length; ++i) {
                     if (groupIds[i] != null && !groupIds[i].equals("")) {
                        jobId = jobEntity.getJob_id();
                        Long groupId = new Long(groupIds[i]);
                        result = mapper.insertGroupMapQueryUVENDING(jobId, groupId);
                        if (!result) {
                           session.rollback();
                           var12 = false;
                           return var12;
                        }
                     }
                  }
               }
            }

            for(i = 0; i < deviceIds.length; ++i) {
               if (deviceIds[i] != null && !deviceIds[i].equals("")) {
                  result = mapper.insertDeviceMapQuery(jobEntity.getJob_id(), deviceIds[i]);
                  if (!result) {
                     session.rollback();
                     boolean var19 = false;
                     return var19;
                  }
               }
            }

            session.commit();
            return true;
         }
      } catch (SQLException var16) {
         session.rollback();
         throw var16;
      } finally {
         session.close();
      }
   }

   public boolean addPremiumJob(JobEntity jobEntity, String[] deviceIds) throws SQLException {
      boolean result = false;
      SqlSession session = this.openNewSession(false);

      try {
         JobDAOMapper mapper = (JobDAOMapper)session.getMapper(JobDAOMapper.class);
         result = mapper.insertJobQuery(jobEntity);
         if (!result) {
            session.rollback();
            boolean var13 = false;
            return var13;
         }

         for(int i = 0; i < deviceIds.length; ++i) {
            if (deviceIds[i] != null && !deviceIds[i].equals("")) {
               result = mapper.insertDeviceMapQuery(jobEntity.getJob_id(), deviceIds[i]);
               if (!result) {
                  session.rollback();
                  boolean var7 = false;
                  return var7;
               }
            }
         }

         session.commit();
      } catch (SQLException var11) {
         session.rollback();
         throw var11;
      } finally {
         session.close();
      }

      return true;
   }

   public boolean addLiteJob(JobEntity jobEntity, String[] deviceIds) throws SQLException {
      boolean result = false;
      SqlSession session = this.openNewSession(false);

      boolean var6;
      try {
         JobDAOMapper mapper = (JobDAOMapper)session.getMapper(JobDAOMapper.class);
         result = mapper.insertLiteJobQuery(jobEntity);
         if (result) {
            for(int i = 0; i < deviceIds.length; ++i) {
               if (deviceIds[i] != null && !deviceIds[i].equals("")) {
                  result = mapper.insertLiteDeviceMapQuery(jobEntity.getJob_id(), deviceIds[i]);
                  if (!result) {
                     session.rollback();
                     boolean var7 = false;
                     return var7;
                  }
               }
            }

            session.commit();
            return true;
         }

         session.rollback();
         var6 = false;
      } catch (SQLException var11) {
         session.rollback();
         throw var11;
      } finally {
         session.close();
      }

      return var6;
   }

   public boolean deleteJob(String jobId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      try {
         JobDAOMapper mapper = (JobDAOMapper)session.getMapper(JobDAOMapper.class);
         long jobIdL = Long.parseLong(jobId);
         String fileId = mapper.selectJobQuery(jobIdL);
         mapper.deleteJobQuery(jobIdL);
         mapper.deleteGroupMapQuery(jobIdL);
         mapper.deleteConsoleMapQuery(jobIdL);
         mapper.deleteDeviceMapQuery(jobIdL);
         if (fileId != null) {
            mapper.deleteFileQuery(fileId);
         }

         session.commit();
      } catch (SQLException var10) {
         session.rollback();
         throw var10;
      } finally {
         session.close();
      }

      return true;
   }

   public boolean editJob(JobEntity jobEntity, String[] groupIds, String[] deviceIds, boolean isVwl, String mode) throws SQLException {
      boolean result = false;
      SqlSession session = this.openNewSession(false);

      try {
         JobDAOMapper mapper = (JobDAOMapper)session.getMapper(JobDAOMapper.class);
         mapper.deleteJobQuery(jobEntity.getJob_id());
         if (isVwl) {
            mapper.deleteConsoleMapQuery(jobEntity.getJob_id());
         } else {
            mapper.deleteGroupMapQuery(jobEntity.getJob_id());
         }

         mapper.deleteDeviceMapQuery(jobEntity.getJob_id());
         result = mapper.insertJobQuery(jobEntity);
         if (!result) {
            session.rollback();
            boolean var16 = false;
            return var16;
         }

         int i;
         boolean var10;
         if (isVwl) {
            for(i = 0; i < groupIds.length; ++i) {
               if (groupIds[i] != null && !groupIds[i].equals("")) {
                  result = mapper.insertGroupMapQueryVIDEOWALL(jobEntity.getJob_id(), groupIds[i]);
                  if (!result) {
                     session.rollback();
                     var10 = false;
                     return var10;
                  }
               }
            }
         } else {
            for(i = 0; i < groupIds.length; ++i) {
               if (groupIds[i] != null && !groupIds[i].equals("")) {
                  result = mapper.insertGroupMapQuery(jobEntity.getJob_id(), new Long(groupIds[i]), mode);
                  if (!result) {
                     session.rollback();
                     var10 = false;
                     return var10;
                  }
               }
            }
         }

         for(i = 0; i < deviceIds.length; ++i) {
            if (deviceIds[i] != null && !deviceIds[i].equals("")) {
               result = mapper.insertDeviceMapQuery(jobEntity.getJob_id(), deviceIds[i]);
               if (!result) {
                  session.rollback();
                  var10 = false;
                  return var10;
               }
            }
         }

         session.commit();
      } catch (SQLException var14) {
         session.rollback();
         throw var14;
      } finally {
         session.close();
      }

      return true;
   }

   public List getCanceledDeviceList(String[] jobIds) throws SQLException {
      long[] jobIdsL = new long[jobIds.length];

      for(int i = 0; i < jobIds.length; ++i) {
         jobIdsL[i] = Long.valueOf(jobIds[i]);
      }

      return ((JobDAOMapper)this.getMapper()).getCanceledDeviceList(jobIdsL);
   }

   public String getFileIDByHash(String fileName, Long fileSize, String hashCode) throws SQLException {
      return ((JobDAOMapper)this.getMapper()).getFileIDByHash(fileName, fileSize, hashCode);
   }

   public Map getFileMeta(String fileId) throws SQLException {
      return ((JobDAOMapper)this.getMapper()).getFileMeta(fileId);
   }

   public JobEntity getJob(String jobId) throws SQLException {
      return ((JobDAOMapper)this.getMapper()).getJob(Long.valueOf(jobId));
   }

   public PagedListInfo getJobDeviceList(Map map, int startPos, int pageSize, String type) throws SQLException {
      SelectCondition condObj = (SelectCondition)map.get("condition");
      String sort = condObj.getSort_name();
      String dir = condObj.getOrder_dir();
      String search = condObj.getSrc_name();
      List pgList = null;
      int totalCnt = false;
      --startPos;
      int totalCnt;
      if (type.equalsIgnoreCase("LFD")) {
         pgList = ((JobDAOMapper)this.getMapper()).getJobDeviceListLFD(Long.valueOf(search), sort, dir, startPos, pageSize);
         totalCnt = ((JobDAOMapper)this.getMapper()).getJobDeviceListLFDCount(Long.valueOf(search));
      } else if (type.equalsIgnoreCase("UVENDING")) {
         pgList = ((JobDAOMapper)this.getMapper()).getJobDeviceListUVENDING(Long.valueOf(search), sort, dir, startPos, pageSize);
         totalCnt = ((JobDAOMapper)this.getMapper()).getJobDeviceListUVENDINGCount(Long.valueOf(search));
      } else {
         pgList = ((JobDAOMapper)this.getMapper()).getJobDeviceListVIDEOWALL(Long.valueOf(search), sort, dir, startPos, pageSize);
         totalCnt = ((JobDAOMapper)this.getMapper()).getJobDeviceListVIDEOWALLCount(Long.valueOf(search));
      }

      return new PagedListInfo(pgList, totalCnt);
   }

   public PagedListInfo getJobList(Map map, int startPos, int pageSize) throws SQLException {
      SelectCondition condObj = (SelectCondition)map.get("condition");
      String sort = condObj.getSort_name();
      String dir = condObj.getOrder_dir();
      String search = condObj.getSrc_name();
      --startPos;
      if (search != null && !search.equals("")) {
         search = search.toUpperCase().replaceAll("_", "^_");
         search = "%" + search + "%";
      }

      List pgList = ((JobDAOMapper)this.getMapper()).getJobList(search, sort, dir, (String)map.get("job_type"), (String)map.get("is_canceled"), startPos, pageSize);
      int totalCnt = ((JobDAOMapper)this.getMapper()).getJobListCount(search, (String)map.get("job_type"), (String)map.get("is_canceled"));
      return new PagedListInfo(pgList, totalCnt);
   }

   public List getJobListByDeviceId(String jobId, String deviceId, boolean bShowAll) throws SQLException {
      List jEntity = null;
      if (jobId.equals("")) {
         jEntity = ((JobDAOMapper)this.getMapper()).getJobListByDeviceIdNoJobId(deviceId, bShowAll);
      } else {
         jEntity = ((JobDAOMapper)this.getMapper()).getJobListByDeviceId(deviceId, Long.valueOf(jobId), bShowAll);
      }

      return jEntity;
   }

   public String getJobMapDevice(String jobId) throws SQLException {
      List groupList = ((JobDAOMapper)this.getMapper()).getJobMapDeviceGetGroup(Long.parseLong(jobId));
      boolean isVWL = false;
      List deviceList = ((JobDAOMapper)this.getMapper()).getJobMapDeviceGetDevice(Long.parseLong(jobId));
      StringBuffer mapStrBuff = new StringBuffer();

      int i;
      for(i = 0; i < groupList.size(); ++i) {
         if (isVWL) {
            mapStrBuff.append((Long)((Map)groupList.get(i)).get("device_id") + "↓" + (String)((Map)groupList.get(i)).get("device_name") + ",");
         } else {
            mapStrBuff.append((Long)((Map)groupList.get(i)).get("group_id") + "↓" + (String)((Map)groupList.get(i)).get("group_name") + ",");
         }
      }

      mapStrBuff.append("↔");

      for(i = 0; i < deviceList.size(); ++i) {
         mapStrBuff.append((String)((Map)deviceList.get(i)).get("device_id") + "↓" + (String)((Map)deviceList.get(i)).get("device_name") + ",");
      }

      return mapStrBuff.toString();
   }

   public String getJobName(String jobId) throws SQLException {
      return ((JobDAOMapper)this.getMapper()).getJobName(Long.parseLong(jobId));
   }

   public PagedListInfo getUVJobList(Map map, int startPos, int pageSize) throws SQLException {
      SelectCondition condObj = (SelectCondition)map.get("condition");
      String sort = condObj.getSort_name();
      String dir = condObj.getOrder_dir();
      String search = condObj.getSrc_name();
      --startPos;
      if (search != null && !search.equals("")) {
         search = search.toUpperCase().replaceAll("_", "^_");
         search = "%" + search + "%";
      }

      List pgList = ((JobDAOMapper)this.getMapper()).getUVJobList(search, sort, dir, (String)map.get("job_type"), (String)map.get("is_canceled"), startPos, pageSize);
      int totalCnt = ((JobDAOMapper)this.getMapper()).getUVJobListCount(search, (String)map.get("job_type"), (String)map.get("is_canceled"));
      PagedListInfo pgInfo = new PagedListInfo(pgList, totalCnt);
      return pgInfo;
   }

   public PagedListInfo getVWLJobList(Map map, int startPos, int pageSize) throws SQLException {
      SelectCondition condObj = (SelectCondition)map.get("condition");
      String sort = condObj.getSort_name();
      String dir = condObj.getOrder_dir();
      String search = condObj.getSrc_name();
      --startPos;
      if (search != null && !search.equals("")) {
         search = search.toUpperCase().replaceAll("_", "^_");
         search = "%" + search + "%";
      }

      List pgList = ((JobDAOMapper)this.getMapper()).getVWLJobList(search, sort, dir, (String)map.get("job_type"), (String)map.get("is_canceled"), startPos, pageSize);
      int totalCnt = ((JobDAOMapper)this.getMapper()).getVWLJobListCount(search, (String)map.get("job_type"), (String)map.get("is_canceled"));
      PagedListInfo pgInfo = new PagedListInfo(pgList, totalCnt);
      return pgInfo;
   }

   public Boolean isExistFileByHash(String fileName, Long fileSize, String hashCode) throws SQLException {
      return ((JobDAOMapper)this.getMapper()).isExistFileByHash(fileName, fileSize, hashCode) > 0;
   }

   public boolean setFileUploadStatusAsTrue(String fileID) throws SQLException {
      return ((JobDAOMapper)this.getMapper()).setFileUploadStatusAsTrue(fileID);
   }

   public boolean setJobCanceled(String jobId) throws SQLException {
      return ((JobDAOMapper)this.getMapper()).setJobCanceled(Long.parseLong(jobId));
   }

   public boolean setJobResult(String jobId, String deviceId, boolean result, String fileName) throws SQLException {
      return ((JobDAOMapper)this.getMapper()).setJobResult(Long.parseLong(jobId), deviceId, result, fileName);
   }

   public boolean setGetLogJobResult(String jobId, String deviceId, boolean result, String fileName) throws SQLException {
      List vwlDeviceList = ((JobDAOMapper)this.getMapper()).setGetLogJobResult(deviceId);

      for(int i = 0; i < vwlDeviceList.size(); ++i) {
         this.setJobResult(jobId, (String)((Map)vwlDeviceList.get(i)).get("device_id"), result, fileName);
      }

      return true;
   }

   public boolean updateJobResult(String jobId, String deviceId, boolean result, String fileName) throws SQLException {
      return ((JobDAOMapper)this.getMapper()).updateJobResult(Long.parseLong(jobId), deviceId, result, fileName);
   }

   public boolean setDefaultJobResult(String jobId, String deviceId, String fileName) throws SQLException {
      return ((JobDAOMapper)this.getMapper()).setDefaultJobResult(Long.parseLong(jobId), deviceId, fileName);
   }

   public Boolean isExistJobResult(String jobId, String deviceId) throws SQLException {
      return ((JobDAOMapper)this.getMapper()).isExistJobResult(Long.parseLong(jobId), deviceId) > 0;
   }

   public Boolean getJobResultFlag(String jobId, String deviceId) throws SQLException {
      return ((JobDAOMapper)this.getMapper()).getJobResultFlag(Long.parseLong(jobId), deviceId);
   }

   public JobEntity getLastJobResult(String deviceId) throws SQLException {
      return ((JobDAOMapper)this.getMapper()).getLastJobResult(deviceId);
   }

   public boolean isVwlDevice(String deviceId) throws SQLException {
      return ((JobDAOMapper)this.getMapper()).isVwlDevice(deviceId) > 0;
   }
}
