package com.samsung.magicinfo.framework.device.deviceInfo.manager;

import com.samsung.common.db.DBListExecuter;
import com.samsung.common.db.PagedListInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.BackupPlayEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceControl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceLoopOutEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMemo;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceModel;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DisasterAlertStatusEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.vwlLayout.entity.VwlLayoutMonitor;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.rms.model.DeviceFilter;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

public interface DeviceInfo extends DBListExecuter {
   boolean addDevice(Device var1) throws SQLException;

   boolean addChildDevice(Device var1) throws SQLException;

   boolean addChildDevice(List var1) throws SQLException;

   boolean addDeviceModel(DeviceModel var1) throws SQLException, ConfigException;

   boolean addDeviceOperationInfo(Device var1) throws SQLException;

   boolean setUnapprovedGroupCode(String var1, Long var2) throws SQLException;

   boolean addVwtInfo(VwlLayoutMonitor var1, String var2) throws SQLException;

   boolean deleteVwtInfo(String var1) throws SQLException;

   boolean deleteDevice(String var1) throws SQLException;

   boolean deleteDeviceGroupMapping(String var1, int var2) throws SQLException;

   boolean deleteDeviceModel(String var1) throws SQLException;

   boolean deleteDeviceModels(String[] var1) throws SQLException, ConfigException;

   boolean deleteDevices(String[] var1) throws SQLException;

   int getCntEqualDevName(String var1, Long var2) throws SQLException;

   int getCntModelByModelCode(String var1) throws Exception;

   Device getDevice(String var1) throws SQLException;

   Device getDeviceFromDB(String var1) throws SQLException;

   Device getDeviceWithGroupId(String var1) throws SQLException;

   Device getMonitoringViewDevice(String var1) throws SQLException;

   Map getDeviceAndModel(String var1) throws SQLException;

   DeviceGeneralConf getDeviceGeneralConf(String var1) throws SQLException;

   void setDeviceChildCount(String var1, Long var2) throws SQLException;

   DeviceGeneralConf getDeviceGeneralConf(String var1, boolean var2) throws SQLException;

   DeviceGeneralConf getDeviceTypeInfo(String var1) throws SQLException;

   List getDeviceList() throws SQLException;

   List getChildDeviceIdList(String var1) throws SQLException;

   PagedListInfo getDeviceList(int var1, int var2) throws SQLException, ConfigException;

   PagedListInfo getDeviceList(int var1, int var2, String var3, String var4, String var5) throws SQLException, ConfigException;

   PagedListInfo getDeviceListByGroup(int var1, int var2, String var3, Long var4, String var5) throws SQLException, ConfigException;

   List getDeviceListByGroupId(Long var1) throws SQLException;

   List getDeviceAndTagListByGroupId(Long var1) throws SQLException;

   Long getDeviceUnapprovedGroupCode(String var1) throws SQLException;

   List getDeviceAndTagListByGroupIds(Long[] var1) throws SQLException;

   List getDeviceAndTagListByDeviceIds(String[] var1) throws SQLException;

   List getDeviceAndTagListByDeviceIds(String[] var1, Boolean var2) throws SQLException;

   List getDeviceListByModelName(String var1) throws SQLException;

   List getDeviceListByModelNameAndType(String var1, String var2, String var3) throws SQLException;

   List getDeviceListByModelNameAndGroup(String var1, int var2) throws SQLException;

   Device getDeviceMinInfo(String var1) throws SQLException;

   DeviceModel getDeviceModel(String var1) throws SQLException;

   PagedListInfo getDeviceModelList(int var1, int var2, String var3) throws SQLException, ConfigException;

   List getDeviceModelList(String var1) throws SQLException;

   List getConnectedDeviceModelNameList() throws SQLException;

   List getDeviceModelNameList() throws SQLException;

   String getDeviceNameById(String var1) throws SQLException;

   Device getDeviceOperationInfo(String var1) throws SQLException;

   List getDMInfo(String var1) throws SQLException;

   String getModelNameByDeviceId(String var1) throws SQLException;

   List getModelNameListByDeviceId(String var1) throws SQLException;

   List getMonitoringInfoListByDeviceId(String var1) throws SQLException;

   boolean getDeviceApprovalStatusByDeviceId(String var1) throws SQLException;

   List getMonitoringInfoByDeviceIdList(String[] var1) throws SQLException;

   List getAppVersionList() throws SQLException;

   List getAppVersionListByDeviceType(String var1) throws SQLException;

   List getAppVersionListBy(Map var1) throws SQLException;

   List getDeviceModelNameListBy(Map var1) throws SQLException;

   boolean moveDevice(String var1, int var2) throws SQLException;

   boolean setDevice(Device var1) throws SQLException;

   boolean setDeviceForApproval(Map var1) throws SQLException, ConfigException;

   int setDeviceGroupId(Map var1) throws SQLException;

   boolean setDeviceModel(DeviceModel var1) throws SQLException;

   boolean setDeviceNameAndLocation(String var1, String var2, String var3, String var4, String var5) throws SQLException;

   boolean setDeviceOperationInfo(Device var1) throws SQLException;

   boolean updateDiskspaceChannel(String var1, long var2, String var4) throws SQLException;

   boolean setDevicePostBootstrap(Device var1) throws SQLException, ConfigException;

   int setIsApproved(Map var1) throws SQLException;

   int setNameDeviceAndModel(Map var1) throws SQLException, ConfigException;

   List getRuleVersionList() throws SQLException;

   boolean setLastConnectionTime(String var1) throws SQLException, ConfigException;

   List getFirmwareVersionList() throws SQLException;

   List getOSImageVersionList() throws SQLException;

   List getDeviceResolutionList(String var1) throws SQLException;

   List getBindingDeviceList(Map var1) throws SQLException;

   List getBindingDeviceListPage(Map var1, int var2, int var3) throws SQLException;

   int getBindingDeviceListCnt(Map var1) throws SQLException;

   int getAllDeviceCount(String var1) throws SQLException;

   int getAllDeviceCountByOrganization(String var1, String var2) throws SQLException;

   int getApprovalDeviceCount(String var1) throws SQLException;

   int getNonApprovalDeviceCount(String var1) throws SQLException;

   int getApprovalPremiumDeviceCount() throws SQLException;

   int getApprovalExtraDisplayDeviceCount() throws SQLException;

   List getModelNameListByModelCode(String var1) throws SQLException;

   int setApprovalWithSeq(Map var1) throws SQLException, ConfigException;

   boolean addDeviceBindingInfo(Device var1) throws SQLException;

   boolean deleteDeviceBindingInfo(String var1) throws SQLException;

   List getDeviceMonitoringFilterList(SelectCondition var1) throws SQLException, ConfigException;

   List getApprovedDeviceFilterList(SelectCondition var1) throws SQLException, ConfigException;

   boolean setShutDownConnectionTime(String var1) throws SQLException;

   String getDeviceModelCodeByDeviceId(String var1) throws SQLException;

   String getDeviceGroupIdByDeviceId(String var1) throws SQLException;

   boolean deleteDeviceBindingInfo() throws SQLException;

   List getDeviceIdGroupIdByDeviceName(String var1) throws SQLException;

   int getCntApprovedDeviceList(Map var1) throws SQLException;

   List getDeviceModelTypeList() throws SQLException;

   String getProgramIdByDeviceId(String var1) throws SQLException;

   String getScheduleIdByProgramId(String var1) throws SQLException;

   long getVersionByProgramId(String var1) throws SQLException;

   String getApprovalByDeviceId(String var1) throws SQLException;

   List getDeviceIdByDesc(String var1) throws SQLException;

   List getDeviceIdByAsc(String var1) throws SQLException;

   boolean isVwlConsole(String var1) throws SQLException;

   List getApprovalDeviceIdByAsc(String var1) throws SQLException;

   List getApprovalDeviceIdByDeviceTypeListAsc(List var1) throws SQLException;

   List getApprovalDeviceIdByDeviceTypeListInOrgAssignedLicenseAsc(List var1) throws SQLException;

   int getCntDeviceByDeviceType(String var1) throws SQLException;

   boolean refreshDeviceGroupType(int var1) throws SQLException;

   List getDeviceIdListByGroup(int var1) throws SQLException;

   List getNotChildDeviceIdListByGroup(int var1) throws SQLException;

   String getDeviceModelNameByDeviceId(String var1) throws SQLException;

   String getIsRedundancy(int var1) throws SQLException;

   boolean addRedundancyStatus(String var1, boolean var2) throws SQLException;

   boolean isRedundancyDevice(String var1) throws SQLException;

   List getExpiredDeviceList(int var1) throws SQLException;

   List getConnectedDeviceModelNameListTypeS(String var1, String var2) throws SQLException;

   String getVwtIdByDeviceId(String var1) throws SQLException;

   String getVwtFileName(String var1) throws SQLException;

   boolean addDeviceTypeVersion(String var1, Float var2) throws SQLException;

   boolean addDeviceInfoAtApprove(Device var1) throws SQLException;

   Long getMinimalPriorityByGroupId(long var1) throws SQLException;

   Long getDeviceGroupPriority(long var1) throws SQLException;

   String getDeviceWaitingMo(String var1) throws SQLException;

   boolean deleteWaitingMo(String var1) throws SQLException;

   int setDeviceWaitingMo(String var1, String var2, String var3) throws SQLException;

   boolean addDeviceWaitingMo(String var1) throws SQLException;

   String getDayLightSavingManual(String var1) throws SQLException;

   String getEventScheduleIdByDeviceId(String var1) throws SQLException;

   boolean getExistCenterstage() throws SQLException;

   int insertDisasterAlertStatus(DisasterAlertStatusEntity var1) throws Exception;

   List selectDisasterAlertStatus(String var1) throws Exception;

   List selectDisasterAlertStatusDisconnected(String var1) throws Exception;

   List selectDisasterAlertStatusByDeviceId(String var1) throws Exception;

   List selectSimpleDisasterAlertStatus(String var1) throws Exception;

   List getDisconnectedDisasterAlertByDeviceIdAndAlertId(String var1, String var2) throws Exception;

   int updateDisasterAlertStatus(DisasterAlertStatusEntity var1) throws Exception;

   void deleteDisasterAlertStatus(String var1) throws Exception;

   void deleteDisconnectedDisasterAlertStatus(String var1, String var2) throws Exception;

   int insertExtDeviceInfo(DeviceLoopOutEntity var1) throws Exception;

   List selectExtDeviceInfo(String var1, String var2) throws Exception;

   int updateExtDeviceInfo(DeviceLoopOutEntity var1) throws Exception;

   void deleteExtDeviceInfo(String var1) throws Exception;

   Map getSoftwareUpdate(String var1) throws Exception;

   boolean setKeepAliveInfo(String var1, Long var2, String var3, String var4) throws SQLException;

   boolean setKeepAliveInfo(String var1, Long var2, String var3, String var4, String var5, String var6) throws SQLException;

   List getCheckDeviceListTimezone(Long var1, String var2, boolean var3) throws SQLException;

   List getCheckDeviceListSchedule(Long var1, String var2, boolean var3) throws SQLException;

   int getCheckDeviceListCntSchedule(String[] var1, String var2, List var3, boolean var4, String var5) throws SQLException;

   List getCheckDeviceListScheduleFail(String[] var1, String var2, List var3, boolean var4, String var5) throws SQLException;

   int getCheckDeviceListCntScheduleFail(String[] var1, String var2, List var3, boolean var4, String var5) throws SQLException;

   List getCheckDeviceListReservationScheduleFail(String[] var1, String var2, List var3, boolean var4, String var5) throws SQLException;

   int getCheckDeviceListCntReservationScheduleFail(String[] var1, String var2, List var3, boolean var4, String var5) throws SQLException;

   List getCheckDeviceListContent(Long var1, String var2, boolean var3) throws SQLException;

   int getCheckDeviceListCntContent(String[] var1, String var2, List var3, boolean var4, String var5) throws SQLException;

   List getCheckDeviceListStorage(Long var1, String var2, boolean var3) throws SQLException;

   boolean deleteChildDevice(String var1) throws SQLException;

   boolean addDeviceGroupMapping(int var1, String var2) throws SQLException;

   boolean addDeviceGroupMapping(int var1, List var2) throws SQLException;

   boolean setConnectChildCnt(String var1, Long var2) throws Exception;

   List getListDeviceGeneralConf(List var1) throws SQLException;

   DeviceMemo getDeviceMemo(String var1) throws SQLException;

   boolean setDeviceMemo(String var1, DeviceMemo var2) throws SQLException;

   List getServerDeviceReport() throws SQLException;

   List getDeviceModelCount() throws SQLException;

   List getDeviceFirmwareCount() throws SQLException;

   boolean setDeviceAmsCam(boolean var1, String var2) throws SQLException;

   boolean addBackupPlayer(BackupPlayEntity var1) throws SQLException;

   boolean addBackupTargetPlayer(BackupPlayEntity var1) throws SQLException;

   boolean deleteBackupPlayer(int var1) throws SQLException;

   boolean deleteBackupTargetPlayer(int var1) throws SQLException;

   boolean setBackupBusyLevel(int var1, String var2) throws SQLException;

   boolean setWaitingMoCount(int var1, String var2) throws SQLException;

   boolean setBackupDevice(String var1, String var2) throws SQLException;

   List getBackupPlayers(Long var1) throws SQLException;

   List getBackupPlayerByWaitingMoCount(Long var1) throws SQLException;

   BackupPlayEntity getBackupPlayerByDeviceId(String var1) throws SQLException;

   List getBackupTargetPlayers(Long var1) throws SQLException;

   DeviceMonitoring getProgramInfoByDeviceGroupId(Long var1) throws SQLException;

   List getDeviceAndGroupInfoByGroupId(Long var1) throws SQLException;

   int cntSyncPlayDevice(String var1) throws SQLException;

   boolean updateLastModifiedTime(String var1) throws SQLException;

   boolean updateLogFileName(String var1, String var2, String var3) throws SQLException;

   Timestamp getStatisticsFileRequestTime(String var1) throws SQLException;

   List getDeviceLogProcessInfo(String var1) throws SQLException;

   int addDeviceLogProcessInfo(String var1, String var2, String var3, String var4, Timestamp var5, int var6, int var7, String var8, int var9) throws SQLException;

   boolean updateDeviceLogProcessStatus(String var1, String var2, String var3, String var4) throws SQLException;

   boolean deleteDeviceLogProcessInfoByDeviceId(String var1, String var2) throws SQLException;

   int getLogProcessingDeviceCnt() throws SQLException;

   boolean updateDeviceLogInfo(String var1, String var2, String var3, String var4, Timestamp var5, int var6, int var7, String var8, String var9) throws SQLException;

   List getAllDeviceLogProcess() throws SQLException;

   List getNewAndModifiedDeviceList(String var1, String var2) throws SQLException;

   boolean addDeviceTotalCount(long var1, int var3) throws SQLException;

   void deviceTotalCount(long var1, int var3) throws SQLException;

   void removeDeviceTotalCount(long var1, int var3) throws SQLException;

   long getDeviceTotalCount(long var1) throws SQLException;

   int getDeviceCountBygroupId(long var1) throws SQLException;

   boolean addStatRequestTimeInsertCurrent(String var1) throws SQLException;

   boolean isRequestTimeExist(String var1) throws SQLException;

   boolean getIsOverWriteDeviceName(String var1) throws SQLException;

   boolean checkEmptyEntity(Object var1);

   List getDeviceListByOrgName(String var1) throws SQLException;

   int getDeviceCountForLicense(List var1) throws SQLException;

   List getDeviceListFromDeviceId(List var1) throws SQLException;

   List getFirstChildrenIDsOfSignageGroup(int var1) throws SQLException;

   int getCheckUpcomingExpiryDateCnt() throws SQLException;

   int getCheckUpcomingExpiryDatePlaylistCnt() throws SQLException;

   List getCheckUpcomingExpiryDate(int var1, Long var2) throws SQLException;

   List getCheckUpcomingExpiryDatePlaylistList(int var1, String var2) throws SQLException;

   int getProgramDeviceTypeByGroupId(long var1) throws SQLException;

   int checkFirstReceiveProgress(String var1) throws SQLException;

   int getContentDownloadMode(String var1) throws SQLException;

   List getTagFromDeviceId(String var1) throws SQLException;

   List getTagFromDeviceId(String var1, Boolean var2) throws SQLException;

   int getAllDeviceCountByDeviceTypeList(List var1) throws SQLException;

   boolean chkOrganizationByDeviceId(String var1, String var2) throws Exception;

   boolean updateDeviceMapLocation(String var1, String[] var2) throws SQLException;

   boolean updateDeviceMapLocationByLocation(String var1, String[] var2) throws SQLException;

   List getDeviceMinList(String[] var1) throws SQLException;

   boolean addSboxVwtInfo(String var1, String var2, String var3) throws SQLException;

   boolean deleteSboxVwtInfo(String var1) throws SQLException;

   int setKeepaliveChangedStatus(Boolean var1, String var2) throws SQLException;

   int initKeepaliveChangedStatus() throws SQLException;

   List getDisconnectedDeviceIdList() throws SQLException;

   boolean getRecommendPlayByDeviceId(String var1) throws SQLException;

   int getCntRecommendPlayDevice() throws SQLException;

   boolean setRecommendPlayByDeviceId(String var1, boolean var2) throws SQLException;

   int getCntDeviceMonitoringList(DeviceFilter var1) throws SQLException;

   int getCountDeviceAll(String var1, Long var2, boolean var3) throws SQLException;

   int getCountTimezoneNotSet(String var1, Long var2, boolean var3) throws SQLException;

   int getCountInsufficientCapacity(String var1, Long var2, boolean var3) throws SQLException;

   int getCountScheduleNotPublish(String var1, Long var2, boolean var3) throws SQLException;

   int getCountContentError(String var1, Long var2, boolean var3) throws SQLException;

   boolean deleteDeviceData(String var1) throws SQLException;

   List getRmMonitoringList(String[] var1, Timestamp var2) throws SQLException;

   List getErrorList(String[] var1, String var2, Timestamp var3, Integer var4) throws SQLException;

   List getMaxDeviceTypeVersion() throws SQLException;

   Boolean setDeviceControl(DeviceControl var1) throws SQLException;

   List getPlayingDefaultContentDeviceIdList(String var1) throws SQLException;

   String getCurrentContentIdByDeviceId(String var1) throws SQLException;

   boolean isDoneAtLast(String var1) throws SQLException;

   List getAllNotDonePlayingDefaultContentHistory() throws SQLException;

   List getPlayingDefaultContentHistoryList(String var1) throws SQLException;

   boolean addPlayingDefaultContentHistory(String var1, String var2) throws SQLException;

   int setPlayingDefaultContentDone(String var1) throws SQLException;

   boolean deletePlayingDefaultContentHistoryByDeviceId(String var1) throws SQLException;

   boolean deletePlayingDefaultContentHistoryByOrganizationName(String var1) throws SQLException;

   List getDeviceNameList(String var1) throws SQLException;

   List getDevicesByGroupIds(List var1) throws SQLException;

   List getDevicesByProgramId(String var1) throws SQLException;

   List getDeviceCountByDeviceType() throws SQLException;

   List getDeviceSbox() throws SQLException;
}
