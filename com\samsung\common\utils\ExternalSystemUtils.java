package com.samsung.common.utils;

import com.google.gson.Gson;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.openapi.auth.TokenRegistry;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.LinkedHashMap;
import java.util.Map;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

public class ExternalSystemUtils {
   static Logger logger = LoggingManagerV2.getLogger(ExternalSystemUtils.class);
   public static String SYSTEM_RULE_MANAGER = "SYSTEM_RULE_MANAGER";
   public static String SYSTEM_RSM = "SYSTEM_RSM";
   public static String SYSTEM_PBP = "PBP";

   public ExternalSystemUtils() {
      super();
   }

   public boolean ruleManagerAddUser(String userId) throws ConfigException {
      AbilityUtils ability = new AbilityUtils();
      String ruleManagerurl = StrUtils.nvl(CommonConfig.get("rulemanager.url"));
      String token = TokenRegistry.getTokenRegistry().getTokenFor3rdParty(SYSTEM_RULE_MANAGER, userId);
      String role;
      if (ability.checkAuthority("RuleManager HQ")) {
         role = "R0003";
      } else {
         role = "R0004";
      }

      Map json = new LinkedHashMap();
      json.put("userid", userId);
      json.put("role", role);
      Gson gson = new Gson();
      JSONObject resultJSON = null;
      OutputStream os = null;
      BufferedReader br = null;
      HttpURLConnection conn = null;

      try {
         URL url = new URL(ruleManagerurl + "/api/v1/mi/adduser");
         conn = (HttpURLConnection)url.openConnection();
         conn.setDoOutput(true);
         conn.setRequestProperty("Authorization", token);
         conn.setRequestMethod("POST");
         conn.setRequestProperty("Content-Type", "application/json");
         os = conn.getOutputStream();
         os.write(gson.toJson(json).getBytes());
         os.flush();
         br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
         StringBuffer response = new StringBuffer();
         if (conn.getResponseCode() != 200) {
            throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
         }

         String output;
         while((output = br.readLine()) != null) {
            response.append(output);
            new JSONObject(response.toString());
         }

         conn.disconnect();
      } catch (IOException var23) {
         logger.error("", var23);
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }

            conn.disconnect();
         } catch (Exception var22) {
            logger.error("", var22);
         }

      }

      return true;
   }

   public boolean ruleManagerDeleteUser(String userId) throws ConfigException {
      AbilityUtils ability = new AbilityUtils();
      String ruleManagerurl = StrUtils.nvl(CommonConfig.get("rulemanager.url"));
      String token = TokenRegistry.getTokenRegistry().getTokenFor3rdParty(SYSTEM_RULE_MANAGER, userId);
      String role;
      if (ability.checkAuthority("RuleManager HQ")) {
         role = "HQ_MANAGER";
      } else {
         role = "STORE_MANAGER";
      }

      Map json = new LinkedHashMap();
      json.put("userid", userId);
      json.put("role", role);
      Gson gson = new Gson();
      JSONObject resultJSON = null;
      OutputStream os = null;
      BufferedReader br = null;
      HttpURLConnection conn = null;

      try {
         URL url = new URL(ruleManagerurl + "/api/v1/mi/deluser");
         conn = (HttpURLConnection)url.openConnection();
         conn.setDoOutput(true);
         conn.setRequestProperty("Authorization", token);
         conn.setRequestMethod("POST");
         conn.setRequestProperty("Content-Type", "application/json");
         os = conn.getOutputStream();
         os.write(gson.toJson(json).getBytes());
         os.flush();
         br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
         StringBuffer response = new StringBuffer();
         if (conn.getResponseCode() != 200) {
            throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
         }

         String output;
         while((output = br.readLine()) != null) {
            response.append(output);
            new JSONObject(response.toString());
         }

         conn.disconnect();
      } catch (IOException var23) {
         logger.error("", var23);
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }

            conn.disconnect();
         } catch (Exception var22) {
            logger.error("", var22);
         }

      }

      return true;
   }

   public boolean rsmRegisterMISServer(String rmsUrl, String serverName, boolean httpsSupported, String httpsServerName, String[] services) {
      Map json = new LinkedHashMap();
      json.put("serverName", serverName);
      json.put("httpsSupported", httpsSupported);
      json.put("httpsServerName", httpsServerName);
      json.put("Services", services);
      Gson gson = new Gson();
      JSONObject resultJSON = null;
      OutputStream os = null;
      BufferedReader br = null;
      HttpURLConnection conn = null;

      try {
         URL url = new URL(rmsUrl + "/api/v1/mis");
         conn = (HttpURLConnection)url.openConnection();
         conn.setDoOutput(true);
         conn.setRequestMethod("POST");
         conn.setRequestProperty("Content-Type", "application/json");
         conn.setRequestProperty("Accept", "application/json");
         os = conn.getOutputStream();
         os.write(gson.toJson(json).getBytes());
         os.flush();
         br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
         StringBuffer response = new StringBuffer();
         if (conn.getResponseCode() != 201) {
            throw new RuntimeException("Failed to register MI Server to RMS : HTTP error code : " + conn.getResponseCode());
         }

         String output;
         while((output = br.readLine()) != null) {
            response.append(output);
            new JSONObject(response.toString());
         }

         conn.disconnect();
      } catch (IOException var23) {
         logger.error("", var23);
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }

            conn.disconnect();
         } catch (Exception var22) {
            logger.error("", var22);
         }

      }

      return true;
   }
}
