package com.samsungcms.kpimagicinfo.util;

import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.StandardCharsets;
import java.nio.file.OpenOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.StringUtils;

public class FileSplitter {
  private static final Logger LOGGER = LogManager.getLogger(com.samsungcms.kpimagicinfo.util.FileSplitter.class);
  
  public static List<File> splitFileIntoDir(String srcFilePath, String destDirPath, String splittedFileNameFormat, String header, ByteBuffer buffer) throws IOException {
    List<File> resultFileList = new ArrayList<>();
    byte LINE_FEED = 10;
    byte CARRIAGE_RETURN = 13;
    int fileCounter = 0;
    long totalReadBytes = 0L;
    long totalWriteBytes = 0L;
    Path path = Paths.get(srcFilePath, new String[0]);
    try (FileChannel srcFileChannel = FileChannel.open(path, new OpenOption[] { StandardOpenOption.READ })) {
      long readBytes;
      while ((readBytes = srcFileChannel.read(buffer)) >= 0L) {
        totalReadBytes += readBytes;
        int contentLength = buffer.position();
        int newLinePosition = buffer.position();
        String fileName = String.format(splittedFileNameFormat, new Object[] { Integer.valueOf(fileCounter++) });
        try (FileChannel splittedFileChannel = FileChannel.open(Paths.get(destDirPath, new String[] { fileName }), new OpenOption[] { StandardOpenOption.TRUNCATE_EXISTING, StandardOpenOption.CREATE, StandardOpenOption.WRITE })) {
          writeHeader(header, readBytes, splittedFileChannel);
          LOGGER.info(fileName);
          boolean hasLineFeed = false;
          boolean needCompact = true;
          while (newLinePosition > 0) {
            if (buffer.get(--newLinePosition) == 10) {
              if (newLinePosition + 1 == buffer.capacity())
                needCompact = false; 
              buffer.position(0);
              buffer.limit(++newLinePosition);
              totalWriteBytes += splittedFileChannel.write(buffer);
              splittedFileChannel.close();
              hasLineFeed = true;
              break;
            } 
          } 
          if (!hasLineFeed)
            throw new IllegalArgumentException("버There are no line breaks in the buffer. The buffer size must be greater than the length of one row."); 
          if (needCompact) {
            buffer.limit(contentLength);
            buffer.compact();
          } else {
            buffer.clear();
          } 
          resultFileList.add(new File(destDirPath + File.separatorChar + fileName));
        } 
      } 
    } catch (Exception e) {
      LOGGER.error(e.getMessage());
    } 
    LOGGER.info(String.format("Total Read  Bytes: %d, Total Write Bytes: %d", new Object[] { Long.valueOf(totalReadBytes), Long.valueOf(totalWriteBytes) }));
    return resultFileList;
  }
  
  private static void writeHeader(String header, long readBytes, FileChannel splittedFileChannel) throws IOException {
    if (readBytes > 0L && !StringUtils.isEmpty(header)) {
      byte[] headerBytes = (header + System.lineSeparator()).getBytes(StandardCharsets.UTF_8);
      splittedFileChannel.write(ByteBuffer.wrap(headerBytes));
    } 
  }
}
