package com.samsung.magicinfo.restapi.user.service;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.LeftMenuGroupTreeDao;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManager;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManagerImpl;
import com.samsung.magicinfo.framework.role.dao.RoleDao;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfo;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleGroupInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleGroupInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.MessageGroupInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageGroupInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.dao.LdapServerDao;
import com.samsung.magicinfo.framework.setup.entity.CategoryEntity;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfo;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.user.dao.UserDao;
import com.samsung.magicinfo.framework.user.dao.UserGroupDao;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserLogManager;
import com.samsung.magicinfo.framework.user.manager.UserLogManagerImpl;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.user.controller.V2OrganizationController;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManager;
import com.samsung.magicinfo.restapi.user.model.V2OrganizationCreation;
import com.samsung.magicinfo.restapi.user.model.V2OrganizationResource;
import com.samsung.magicinfo.restapi.user.model.V2UserData;
import com.samsung.magicinfo.restapi.user.model.V2UserGroupResource;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.ftpserver.ftplet.UserManager;
import org.apache.ftpserver.usermanager.DbUserManagerFactory;
import org.apache.ftpserver.usermanager.impl.BaseUser;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2OrganizationService")
@Transactional
public class V2OrganizationServiceImpl implements V2OrganizationService {
   protected Logger logger = LoggingManagerV2.getLogger(V2OrganizationController.class);
   UserGroupInfo userGroupDao = UserGroupInfoImpl.getInstance();
   @Autowired
   private PIIDataManager piiDataManager;

   public V2OrganizationServiceImpl() {
      super();
   }

   public Long getOrgGroupIdByName(String organizationName) throws Exception {
      long orgGroupIdByName = this.userGroupDao.getOrgGroupIdByName(organizationName);
      return orgGroupIdByName;
   }

   public List getUserGroupList(boolean includeSubGroup, boolean includeCount) throws SQLException {
      new ArrayList();
      List result = new ArrayList();
      Map map = new HashMap();
      UserDao userDao = new UserDao();
      LdapServerDao ldapServerDao = new LdapServerDao();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      ServerSetupInfo serverSetupInfo = ServerSetupInfoImpl.getInstance();
      int count = 0;
      Long groupId = userContainer.getUser().getRoot_group_id();
      boolean orgLdapStatus;
      ArrayList idList;
      UserGroup group;
      if (groupId == 0L) {
         List groupList = this.userGroupDao.getChildGroupList(groupId, includeSubGroup);
         Iterator var13 = groupList.iterator();

         V2UserGroupResource userGroupResource;
         Map serverInfo;
         boolean ldapEnable;
         boolean ldapSyncEnable;
         while(var13.hasNext()) {
            UserGroup entity = (UserGroup)var13.next();
            if (includeCount) {
               idList = new ArrayList();
               if (entity.getGroup_id() == entity.getRoot_group_id()) {
                  List userGroupList = this.userGroupDao.getChildGroupList(entity.getGroup_id(), true);
                  Iterator var17 = userGroupList.iterator();

                  while(var17.hasNext()) {
                     UserGroup childGroup = (UserGroup)var17.next();
                     idList.add(childGroup.getGroup_id());
                  }
               } else {
                  idList.add(entity.getGroup_id());
               }

               map.put("groupIds", idList);
               count = userDao.getCountGroupedUser(map);
            }

            orgLdapStatus = ldapServerDao.getLdapServerStatus(entity.getRoot_group_id());
            userGroupResource = new V2UserGroupResource();
            if (entity.getGroup_depth() < 2L) {
               userGroupResource.setResponseDataType("USER_ORGANIZATION");
            } else {
               userGroupResource.setResponseDataType("USER_GROUP");
            }

            userGroupResource.setGroupId(entity.getGroup_id());
            userGroupResource.setOrganizationId(entity.getRoot_group_id());
            userGroupResource.setParentGroupId(entity.getP_group_id());
            userGroupResource.setGroupDepth(entity.getGroup_depth());
            userGroupResource.setGroupName(entity.getGroup_name());
            userGroupResource.setUserCount(count);
            userGroupResource.setLdapStatus(orgLdapStatus);
            serverInfo = serverSetupInfo.getServerInfoByOrgId(entity.getRoot_group_id());
            ldapEnable = Boolean.valueOf(serverInfo.get("ldap_enable").toString());
            ldapSyncEnable = Boolean.valueOf(serverInfo.get("ldap_sync_enable").toString());
            userGroupResource.setLdapEnable(ldapEnable);
            userGroupResource.setLdapSyncEnable(ldapSyncEnable);
            result.add(userGroupResource);
         }

         group = this.userGroupDao.getGroupById(groupId);
         orgLdapStatus = ldapServerDao.getLdapServerStatus(group.getRoot_group_id());
         List rootIdList = new ArrayList();
         rootIdList.add(0L);
         map.put("groupIds", rootIdList);
         count = userDao.getCountGroupedUser(map);
         userGroupResource = new V2UserGroupResource();
         userGroupResource.setResponseDataType("USER_ORGANIZATION");
         userGroupResource.setGroupId(group.getGroup_id());
         userGroupResource.setOrganizationId(group.getRoot_group_id());
         userGroupResource.setParentGroupId(group.getP_group_id());
         userGroupResource.setGroupDepth(group.getGroup_depth());
         userGroupResource.setGroupName(group.getGroup_name());
         userGroupResource.setUserCount(count);
         userGroupResource.setLdapStatus(orgLdapStatus);
         serverInfo = serverSetupInfo.getServerInfoByOrgId(group.getRoot_group_id());
         ldapEnable = Boolean.valueOf(serverInfo.get("ldap_enable").toString());
         ldapSyncEnable = Boolean.valueOf(serverInfo.get("ldap_sync_enable").toString());
         userGroupResource.setLdapEnable(ldapEnable);
         userGroupResource.setLdapSyncEnable(ldapSyncEnable);
         result.add(userGroupResource);
      } else {
         group = this.userGroupDao.getAllByUserGroupId(groupId);
         List userGroupList = this.userGroupDao.getChildGroupList(group.getGroup_id(), true);
         idList = new ArrayList();
         Iterator var24 = userGroupList.iterator();

         while(var24.hasNext()) {
            UserGroup childGroup = (UserGroup)var24.next();
            idList.add(childGroup.getGroup_id());
         }

         map.put("groupIds", idList);
         count = userDao.getCountGroupedUser(map);
         orgLdapStatus = ldapServerDao.getLdapServerStatus(group.getRoot_group_id());
         V2UserGroupResource userGroupResource = new V2UserGroupResource();
         if (group.getGroup_depth() < 2L) {
            userGroupResource.setResponseDataType("USER_ORGANIZATION");
         } else {
            userGroupResource.setResponseDataType("USER_GROUP");
         }

         userGroupResource.setGroupId(group.getGroup_id());
         userGroupResource.setOrganizationId(group.getRoot_group_id());
         userGroupResource.setParentGroupId(group.getP_group_id());
         userGroupResource.setGroupDepth(group.getGroup_depth());
         userGroupResource.setGroupName(group.getGroup_name());
         userGroupResource.setUserCount(count);
         userGroupResource.setLdapStatus(orgLdapStatus);
         result.add(userGroupResource);
      }

      return result;
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2UserData saveOrganization(HttpServletRequest request, V2OrganizationCreation body) throws Exception {
      String userId = body.getUserInfo().getUserId();
      String newOrganization = body.getOrganizationName();
      String userName = body.getUserInfo().getUserName();
      String email = body.getUserInfo().getEmail();
      String phoneNum = body.getUserInfo().getPhoneNum();
      String mobileNum = body.getUserInfo().getMobileNum();
      String jobPosition = body.getUserInfo().getJobPosition();
      String team = body.getUserInfo().getTeam();
      V2UserData resource = new V2UserData();
      Locale locale = SecurityUtils.getLocale();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      newOrganization = StrUtils.nvl(newOrganization).equals("") ? "" : newOrganization;
      if (!userContainer.getUser().getRole_name().equals("Server Administrator")) {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_ONLY_SERVER_ADMIN_CAN_CONTROL);
      } else {
         UserLogManager logMgr = UserLogManagerImpl.getInstance();
         AbilityUtils ability = new AbilityUtils();
         boolean canWriteUser = ability.checkAuthority("User Write");
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         UserInfo userInfo = UserInfoImpl.getInstance();
         String password = StrUtils.nvl(body.getUserInfo().getPassword());
         userName = StrUtils.nvl(userName);
         email = StrUtils.nvl(email);
         phoneNum = StrUtils.nvl(phoneNum);
         mobileNum = StrUtils.nvl(mobileNum);
         jobPosition = StrUtils.nvl(jobPosition);
         team = StrUtils.nvl(team);
         UserGroupDao userGroupDao = new UserGroupDao();
         UserGroup organ = new UserGroup();
         String loginUserId = userContainer.getUser().getUser_id();
         if (newOrganization.equals("ROOT")) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_ORGANIZAION_NAME_NOT_ROOT);
         } else {
            long isOrgExist = 0L;
            isOrgExist = userGroupDao.getCountOrgGroupIdByName(newOrganization);
            if (isOrgExist != 0L) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SAME_ITEM_EXIST, new String[]{"organization name"});
            } else if (!canWriteUser) {
               throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
            } else {
               organ.setGroup_name(newOrganization);
               organ.setGroup_depth(1L);
               organ.setP_group_id(0L);
               organ.setRoot_group_id(userContainer.getUser().getRoot_group_id());
               organ.setUser_count(0L);
               boolean isAdmin = false;
               if (userContainer.getUser().getRoot_group_id() == 0L) {
                  isAdmin = true;
               }

               try {
                  ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
                  Long childDefuaultGroup = userGroupDao.addUserGroup(organ);
                  Long group_depth = organ.getGroup_depth();
                  if (group_depth == 1L) {
                     organ.setGroup_id(childDefuaultGroup);
                     organ.setRoot_group_id(childDefuaultGroup);
                     userGroupDao.updateRootGroupID(organ);
                     serverSetupDao.addDefaultServerInfo(childDefuaultGroup);
                  }

                  if (organ.getP_group_id() == 0L && organ.getGroup_depth() == 1L) {
                     String groupName = organ.getGroup_name();
                     organ.setGroup_name("default");
                     organ.setGroup_depth(2L);
                     organ.setRoot_group_id(childDefuaultGroup);
                     organ.setP_group_id(childDefuaultGroup);
                     organ.setCan_delete("N");
                     userGroupDao.addUserGroup(organ);
                     ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
                     programGroupInfo.addGroupForOrg(groupName);
                     EventScheduleGroupInfo eventScheduleGroupInfo = EventScheduleGroupInfoImpl.getInstance();
                     eventScheduleGroupInfo.addGroupForOrg(groupName);
                     MessageGroupInfo messageGroupInfo = MessageGroupInfoImpl.getInstance();
                     messageGroupInfo.addGroupForOrg(groupName);
                     DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
                     groupDao.addGroupForOrg(groupName, loginUserId);
                     long basicGroupId = groupDao.getGroupIdByOrgBasic(groupName, "default");
                     ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
                     String programId = schInfo.createDefaultProgram(groupName + "_" + "default", Long.toString(basicGroupId), loginUserId);
                     if (programId != null) {
                        groupDao.setDefaultProgramId(basicGroupId, programId);
                     }

                     long group_id = (long)SequenceDB.getNextValue("MI_CATEGORY_INFO_GROUP");
                     CategoryEntity category = new CategoryEntity();
                     category.setGroup_name(groupName);
                     category.setP_group_id(0L);
                     category.setGroup_id(group_id);
                     category.setGroup_depth(0L);
                     category.setDescription("Organization");
                     category.setCreator_id("admin");
                     category.setCreate_date(new Timestamp(System.currentTimeMillis()));
                     CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
                     categoryInfo.addCategory(category);
                  }
               } catch (Exception var44) {
                  this.logger.debug(var44.getMessage());
               }

               User newAdmin = new User();
               newAdmin.setUser_id(userId);
               newAdmin.setPassword(password);
               newAdmin.setUser_name(this.piiDataManager.encryptData(userName, "name"));
               newAdmin.setApproval_type("TEXT_NEW_P");
               newAdmin.setGroup_name("default");
               newAdmin.setIs_approved("Y");
               newAdmin.setIs_deleted("N");
               newAdmin.setIsOrganization(false);
               newAdmin.setOrganization(newOrganization);
               newAdmin.setRole_name("Administrator");
               newAdmin.setEmail(this.piiDataManager.encryptData(email, "email"));
               newAdmin.setPhone_num(this.piiDataManager.encryptData(phoneNum, "phone"));
               newAdmin.setMobile_num(this.piiDataManager.encryptData(mobileNum, "phone"));
               newAdmin.setTeam(team);
               newAdmin.setJob_position(jobPosition);
               newAdmin.setIs_reset_pwd("Y");
               userId = newAdmin.getUser_id();
               password = newAdmin.getPassword();
               if (newAdmin.getLdap_info() != null && !newAdmin.getLdap_info().equals("")) {
                  newAdmin.setPassword("LDAP");
                  password = "LDAP";
               }

               newAdmin.setUser_id(userId);
               long organId = userGroupInfo.getOrgGroupIdByName(newAdmin.getOrganization());
               newAdmin.setRoot_group_id(organId);
               newAdmin.setPassword(password);
               userInfo.addUser(newAdmin, true);
               long userGroupID = userGroupInfo.getGroupIdByName(newAdmin.getGroup_name(), organId);
               userGroupInfo.setCanDeleteByGroupId(userGroupID);
               BaseUser ftpUser = new BaseUser();
               ftpUser.setName(newAdmin.getUser_id());
               ftpUser.setPassword(password);
               UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
               userMgr.save(ftpUser);
               ContentInfo contentInfo = ContentInfoImpl.getInstance();
               contentInfo.addDefaultGroup(userId);
               int newTLFDGroupId = SequenceDB.getNextValue("MI_CMS_INFO_TLFD_GROUP");
               contentInfo.addTLFDGroup((long)newTLFDGroupId, newOrganization, -1L, 1L, loginUserId, organId);
               PlaylistInfo playlintInfo = PlaylistInfoImpl.getInstance();
               playlintInfo.addDefaultGroup(userId);
               DeviceGroupInfo groupInfo = DeviceGroupInfoImpl.getInstance();
               groupInfo.updateCacheDeviceGroup();
               RuleSetInfo rulesetInfo = RuleSetInfoImpl.getInstance();
               rulesetInfo.addOrganAndDefaultGroup(newOrganization);
               resource = (V2UserData)ConvertUtil.convertObject(newAdmin, resource);
               resource.setUserInfo(body.getUserInfo());

               try {
                  if (DeviceUtils.isSupportNOC()) {
                     DeviceNocManager nocService = DeviceNocManagerImpl.getInstance();
                     Boolean nocResult = nocService.thingworxUpdateOrganization();
                     if (nocResult) {
                        this.logger.error("[GetUserController][AddOrgan] success to add new organization.");
                     } else {
                        this.logger.error("[GetUserController][AddOrgan] fail to add new organization.");
                     }
                  }
               } catch (Exception var43) {
                  this.logger.error("[GetUserController][AddOrgan] fail to add new organization." + var43.getMessage());
               }

               return resource;
            }
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2CommonIds deleteOrganization(String organizationId) throws Exception {
      V2CommonIds groupDeletion = new V2CommonIds();
      String table = "MI_USER_INFO_GROUP";
      UserContainer userContainer = SecurityUtils.getUserContainer();
      User user = userContainer.getUser();
      UserGroupInfo userGroupDao = UserGroupInfoImpl.getInstance();
      LeftMenuGroupTreeDao tree_dao = new LeftMenuGroupTreeDao();
      if (Long.parseLong(organizationId) <= 0L) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"organizationId"});
      } else {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, Long.valueOf(organizationId));
         UserGroup group = userGroupDao.getGroupById(Long.parseLong(organizationId));
         if (group == null) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"organization information"});
         } else {
            RoleDao roleInfo = new RoleDao();
            String roleName = roleInfo.getRoleByUserId(user.getUser_id()).getRole_name().toString();
            boolean result = false;
            if (!roleName.equals("Server Administrator")) {
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_ONLY_SERVER_ADMIN_CAN_CONTROL);
            } else {
               Long orgGroupCount = userGroupDao.getOrgCntInOrgGroupById(Long.valueOf(organizationId));
               Long OrgCount = userGroupDao.getOrgCount(Long.valueOf(organizationId));
               if (OrgCount > 0L && orgGroupCount == 2L) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_DELETE_ORGANIZATION_DUE_TO_ONLY_TWO_ORG_IN_ORG_GROUP, new String[]{"organizationId"});
               } else {
                  ContentInfo contentInfo = ContentInfoImpl.getInstance();
                  long cnt = (long)contentInfo.getCntContentByOrganizationId(Long.valueOf(organizationId));
                  if (cnt > 0L) {
                     throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_ORGANIZATION_WITH_CONTENT_DELETE);
                  } else {
                     result = tree_dao.setGroupTreeRemoveUser("UserOrg", table, organizationId, user.getUser_id(), (HttpServletRequest)null, (HttpServletResponse)null);
                     if (result) {
                        List organizationIds = new ArrayList();
                        userGroupDao.deleteOrgGroupFromMultiOrgGroup(Long.parseLong(organizationId));
                        organizationIds.add(organizationId);
                        groupDeletion.setIds(organizationIds);
                        return groupDeletion;
                     } else {
                        throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"organizationId"});
                     }
                  }
               }
            }
         }
      }
   }

   public List getAllOrganizationList() throws Exception {
      UserInfo userInfo = UserInfoImpl.getInstance();
      List list = (List)Optional.ofNullable(userInfo.getOrganization()).orElse(new ArrayList());
      return (List)list.stream().map((o) -> {
         V2OrganizationResource v2o = new V2OrganizationResource();
         v2o.setOrganizationId((Long)o.get("group_id"));
         v2o.setOrganizationName((String)o.get("group_name"));
         return v2o;
      }).collect(Collectors.toList());
   }
}
