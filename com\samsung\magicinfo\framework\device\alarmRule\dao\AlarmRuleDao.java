package com.samsung.magicinfo.framework.device.alarmRule.dao;

import com.samsung.common.db.PagedListInfo;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SequenceDB;
import com.samsung.magicinfo.framework.device.alarmRule.entity.AlarmRule;
import com.samsung.magicinfo.framework.device.alarmRule.entity.AlarmRuleReservation;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class AlarmRuleDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(AlarmRuleDao.class);

   public AlarmRuleDao() {
      super();
   }

   public AlarmRule addAlarmRule(AlarmRule rule) throws SQLException {
      SqlSession session = null;

      AlarmRule var3;
      try {
         session = this.openNewSession(true);
         var3 = this.addAlarmRule(rule, session);
      } catch (SQLException var7) {
         throw var7;
      } finally {
         if (session != null) {
            session.close();
         }

      }

      return var3;
   }

   public AlarmRule addAlarmRule(AlarmRule rule, SqlSession session) throws SQLException {
      long ruleId = (long)SequenceDB.getNextValue("MI_DMS_INFO_RULE");
      rule.setRule_id(ruleId);
      return ((AlarmRuleDaoMapper)this.getMapper(session)).addAlarmRule(rule) > 0 ? rule : null;
   }

   public boolean addAlarmRuleAndMap(AlarmRule alarmRule, String[] version) throws SQLException {
      SqlSession session = this.openNewSession(false);

      try {
         AlarmRule rtnRule = this.addAlarmRule(alarmRule, session);
         boolean var15;
         if (rtnRule == null) {
            session.rollback();
            var15 = false;
            return var15;
         } else if (!this.setDeviceModelApplyRule(rtnRule.getRule_id(), rtnRule.getDevice_model_name(), false, rtnRule.getIs_auto_update(), session)) {
            session.rollback();
            var15 = false;
            return var15;
         } else {
            if (rtnRule.getIs_auto_update() && version != null && version.length > 0) {
               this.setRuleAutoUpdateN(rtnRule.getDevice_model_name(), rtnRule.getRule_id(), session);
               String[] var5 = version;
               int var6 = version.length;

               for(int var7 = 0; var7 < var6; ++var7) {
                  String aVersion = var5[var7];
                  ((AlarmRuleDaoMapper)this.getMapper(session)).deleteMapRuleVersion(aVersion.trim(), rtnRule.getDevice_model_name());
                  if (!this.addRuleVersionMap(rtnRule.getRule_id(), aVersion, rtnRule.getDevice_model_name(), session)) {
                     session.rollback();
                     boolean var9 = false;
                     return var9;
                  }
               }
            }

            session.commit();
            var15 = true;
            return var15;
         }
      } catch (SQLException var13) {
         session.rollback();
         throw var13;
      } finally {
         session.close();
      }
   }

   public Long addReservationInfo(AlarmRuleReservation reservation) throws SQLException {
      Long ruleId = (long)SequenceDB.getNextValue("MI_DMS_INFO_RESERVATION_RULE");
      reservation.setRule_rsv_id(ruleId);
      return ((AlarmRuleDaoMapper)this.getMapper()).addReservationInfo(reservation) > 0 ? ruleId : -1L;
   }

   public boolean addRsvInfoToDevice(AlarmRuleReservation reservation) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper()).addRsvInfoToDevice(reservation) > 0;
   }

   public boolean addRuleVersionMap(Long ruleId, String version, String deviceModelName) throws SQLException {
      SqlSession session = null;

      boolean var5;
      try {
         session = this.openNewSession(true);
         var5 = this.addRuleVersionMap(ruleId, version, deviceModelName, session);
      } catch (SQLException var9) {
         throw var9;
      } finally {
         if (session != null) {
            session.close();
         }

      }

      return var5;
   }

   public boolean addRuleVersionMap(Long ruleId, String version, String deviceModelName, SqlSession session) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper(session)).addRuleVersionMap(ruleId, version, deviceModelName) > 0;
   }

   public boolean delAlarmRule(Long ruleId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         ((AlarmRuleDaoMapper)this.getMapper(session)).delAlarmRuleMapRule(ruleId);
         ((AlarmRuleDaoMapper)this.getMapper(session)).delAlarmRuleMapDevice(ruleId);
         if (((AlarmRuleDaoMapper)this.getMapper(session)).delAlarmRuleInfoRule(ruleId) > 0) {
            session.commit();
            var3 = true;
            return var3;
         }

         session.rollback();
         var3 = false;
      } catch (SQLException var7) {
         session.rollback();
         throw var7;
      } finally {
         session.close();
      }

      return var3;
   }

   public boolean deleteReservationInfo(Long ruleRsvId) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper()).deleteReservationInfo(ruleRsvId) > 0;
   }

   public AlarmRule getAlarmRule(Long ruleId) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper()).getAlarmRule(ruleId);
   }

   public List getAlarmRuleListToExport(Map map) throws SQLException {
      Map params = new HashMap();
      params.put("ruleType", map.get("rule_type"));
      SelectCondition condition = (SelectCondition)map.get("condition");
      String sort = condition.getSort_name();
      String dir = condition.getOrder_dir();
      if (sort != null && !sort.equals("") && dir != null && !dir.equals("")) {
         params.put("sort", sort.toUpperCase());
         params.put("dir", dir.toUpperCase());
      }

      params.put("videoWallDevice", "VideoWall Console");
      if (condition.getSrc_name() != null && !condition.getSrc_name().equals("")) {
         String srcName = condition.getSrc_name().replaceAll("_", "^_");
         params.put("srcName", srcName.toUpperCase());
      }

      return ((AlarmRuleDaoMapper)this.getMapper()).getAlarmRuleListToExport(params);
   }

   public List getAlarmRuleModel(Long ruleId) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper()).getAlarmRuleModel(ruleId);
   }

   public List getLiteAlarmRuleListToExport(Map map) throws SQLException {
      Map params = new HashMap();
      params.put("ruleType", map.get("rule_type"));
      SelectCondition condition = (SelectCondition)map.get("condition");
      String sort = condition.getSort_name();
      String dir = condition.getOrder_dir();
      if (sort != null && !sort.equals("") && dir != null && !dir.equals("")) {
         params.put("sort", sort.toUpperCase());
         params.put("dir", dir.toUpperCase());
      }

      params.put("videoWallDevice", "VideoWall Console");
      if (condition.getSrc_name() != null && !condition.getSrc_name().equals("")) {
         String srcName = condition.getSrc_name().replaceAll("_", "^_");
         params.put("srcName", srcName.toUpperCase());
      }

      return ((AlarmRuleDaoMapper)this.getMapper()).getLiteAlarmRuleListToExport(params);
   }

   public List getLiteReservationListToExport(Map map) throws SQLException {
      Map params = new HashMap();
      params.put("ruleType", map.get("rule_type"));
      SelectCondition condition = (SelectCondition)map.get("condition");
      String sort = condition.getSort_name();
      String dir = condition.getOrder_dir();
      if (sort != null && !sort.equals("") && dir != null && !dir.equals("")) {
         params.put("sort", sort.toUpperCase());
         params.put("dir", dir.toUpperCase());
      }

      params.put("ConstWAITING", "Waiting");
      params.put("ConstFINISHED", "Finished");
      if (condition.getSrc_name() != null && !condition.getSrc_name().equals("")) {
         String srcName = condition.getSrc_name().replaceAll("_", "^_");
         params.put("srcName", srcName.toUpperCase());
      }

      return ((AlarmRuleDaoMapper)this.getMapper()).getLiteReservationListToExport(params);
   }

   public PagedListInfo getLiteRuleList(int startPos, int pageSize, Map condition) throws SQLException {
      Map params = new HashMap();
      params.put("ruleType", condition.get("rule_type"));
      SelectCondition selectCondition = (SelectCondition)condition.get("condition");
      String sort = selectCondition.getSort_name();
      String dir = selectCondition.getOrder_dir();
      if (sort != null && !sort.equals("") && dir != null && !dir.equals("")) {
         params.put("sort", sort.toUpperCase());
         params.put("dir", dir.toUpperCase());
      }

      params.put("videoWallDevice", "VideoWall Console");
      if (selectCondition.getSrc_name() != null && !selectCondition.getSrc_name().equals("")) {
         String srcName = selectCondition.getSrc_name().replaceAll("_", "^_");
         params.put("srcName", srcName.toUpperCase());
      }

      --startPos;
      params.put("startPos", startPos);
      params.put("pageSize", pageSize);
      return new PagedListInfo(((AlarmRuleDaoMapper)this.getMapper()).getLiteRuleList(params), ((AlarmRuleDaoMapper)this.getMapper()).getLiteRuleCount(params));
   }

   public PagedListInfo getLiteRuleReservationList(int startPos, int pageSize, Map condition) throws SQLException {
      Map params = new HashMap();
      params.put("ruleType", condition.get("rule_type"));
      SelectCondition selectCondition = (SelectCondition)condition.get("condition");
      String sort = selectCondition.getSort_name();
      String dir = selectCondition.getOrder_dir();
      if (sort != null && !sort.equals("") && dir != null && !dir.equals("")) {
         params.put("sort", sort.toUpperCase());
         params.put("dir", dir.toUpperCase());
      }

      params.put("ConstWAITING", "Waiting");
      params.put("ConstFINISHED", "Finished");
      if (selectCondition.getSrc_name() != null && !selectCondition.getSrc_name().equals("")) {
         String srcName = selectCondition.getSrc_name().replaceAll("_", "^_");
         params.put("srcName", srcName.toUpperCase());
      }

      --startPos;
      params.put("startPos", startPos);
      params.put("pageSize", pageSize);
      return new PagedListInfo(((AlarmRuleDaoMapper)this.getMapper()).getLiteRuleReservationList(params), ((AlarmRuleDaoMapper)this.getMapper()).getLiteRuleReservationCount(params));
   }

   public List getMemoryCreateAlarmRule() throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper()).getMemoryCreateAlarmRule("00");
   }

   public List getMemoryProcessAlarmRule() throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper()).getMemoryProcessAlarmRule("01");
   }

   public String getReservationIdByRSVId(Long ruleReservationId, String deviceId) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper()).getReservationIdByRSVId(ruleReservationId, deviceId);
   }

   public AlarmRuleReservation getReservationInfo(Long ruleRsvId) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper()).getReservationInfo(ruleRsvId);
   }

   public List getReservationListToExport(Map map) throws SQLException {
      Map params = new HashMap();
      params.put("ruleType", map.get("rule_type"));
      SelectCondition selectCondition = (SelectCondition)map.get("condition");
      String sort = selectCondition.getSort_name();
      String dir = selectCondition.getOrder_dir();
      if (sort != null && !sort.equals("") && dir != null && !dir.equals("")) {
         params.put("sort", sort.toUpperCase());
         params.put("dir", dir.toUpperCase());
      }

      params.put("ConstWAITING", "Waiting");
      params.put("ConstFINISHED", "Finished");
      if (selectCondition.getSrc_name() != null && !selectCondition.getSrc_name().equals("")) {
         String srcName = selectCondition.getSrc_name().replaceAll("_", "^_");
         params.put("srcName", srcName.toUpperCase());
      }

      return ((AlarmRuleDaoMapper)this.getMapper()).getReservationListToExport(params);
   }

   public List getRsvGroupName(Long ruleReservationId) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper()).getRsvGroupName(ruleReservationId);
   }

   public List getRsvMapListByRuleRsvId(Long ruleReservationId) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper()).getRsvMapListByRuleRsvId(ruleReservationId);
   }

   public PagedListInfo getRuleList(int startPos, int pageSize, Map condition) throws SQLException {
      Map params = new HashMap();
      params.put("ruleType", condition.get("rule_type"));
      SelectCondition selectCondition = (SelectCondition)condition.get("condition");
      String sort = selectCondition.getSort_name();
      String dir = selectCondition.getOrder_dir();
      if (sort != null && !sort.equals("") && dir != null && !dir.equals("")) {
         params.put("sort", sort.toUpperCase());
         params.put("dir", dir.toUpperCase());
      }

      params.put("videoWallDevice", "VideoWall Console");
      if (selectCondition.getSrc_name() != null && !selectCondition.getSrc_name().equals("")) {
         String srcName = selectCondition.getSrc_name().replaceAll("_", "^_");
         params.put("srcName", srcName.toUpperCase());
      }

      --startPos;
      params.put("startPos", startPos);
      params.put("pageSize", pageSize);
      return new PagedListInfo(((AlarmRuleDaoMapper)this.getMapper()).getRuleList(params), ((AlarmRuleDaoMapper)this.getMapper()).getRuleCount(params));
   }

   public PagedListInfo getRuleReservationList(int startPos, int pageSize, Map condition) throws SQLException {
      Map params = new HashMap();
      params.put("ruleType", condition.get("rule_type"));
      SelectCondition selectCondition = (SelectCondition)condition.get("condition");
      String sort = selectCondition.getSort_name();
      String dir = selectCondition.getOrder_dir();
      if (sort != null && !sort.equals("") && dir != null && !dir.equals("")) {
         params.put("sort", sort.toUpperCase());
         params.put("dir", dir.toUpperCase());
      }

      params.put("ConstWAITING", "Waiting");
      params.put("ConstFINISHED", "Finished");
      if (selectCondition.getSrc_name() != null && !selectCondition.getSrc_name().equals("")) {
         String srcName = selectCondition.getSrc_name().replaceAll("_", "^_");
         params.put("srcName", srcName.toUpperCase());
      }

      --startPos;
      params.put("startPos", startPos);
      params.put("pageSize", pageSize);
      return new PagedListInfo(((AlarmRuleDaoMapper)this.getMapper()).getRuleReservationList(params), ((AlarmRuleDaoMapper)this.getMapper()).getRuleReservationCount(params));
   }

   public List getRuleVerListById(String ruleId) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper()).getRuleVerListById(Long.parseLong(ruleId));
   }

   public boolean setAlarmRule(AlarmRule rule) throws SQLException {
      SqlSession session = null;

      boolean var3;
      try {
         session = this.openNewSession(true);
         var3 = this.setAlarmRule(rule, session);
      } catch (SQLException var7) {
         throw var7;
      } finally {
         if (session != null) {
            session.close();
         }

      }

      return var3;
   }

   private boolean setAlarmRule(AlarmRule rule, SqlSession session) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper(session)).setAlarmRule(rule) > 0;
   }

   public boolean setAlarmRuleAndMap(AlarmRule alarmRule) throws SQLException {
      SqlSession session = this.openNewSession(false);

      try {
         boolean var12;
         if (!this.setAlarmRule(alarmRule, session)) {
            session.rollback();
            var12 = false;
            return var12;
         } else {
            this.setModelApplyRuleUpdate(alarmRule, session);
            if (alarmRule.getIs_auto_update() != null && alarmRule.getIs_auto_update()) {
               this.setRuleAutoUpdateN(alarmRule.getDevice_model_name(), alarmRule.getRule_id(), session);
            }

            String[] versions = alarmRule.getApplied_ver();
            if (versions != null) {
               for(int i = 0; i < versions.length; ++i) {
                  ((AlarmRuleDaoMapper)this.getMapper(session)).setAlarmRuleAndMapDelete2(versions[i].trim(), alarmRule.getDevice_model_name());
                  Map map = new HashMap();
                  map.put("ruleId", alarmRule.getRule_id());
                  map.put("version", versions[i].trim());
                  map.put("deviceModelName", alarmRule.getDevice_model_name());
                  if (((AlarmRuleDaoMapper)this.getMapper(session)).setAlarmRuleAndMapInsert(map) <= 0) {
                     session.rollback();
                     boolean var6 = false;
                     return var6;
                  }
               }
            }

            session.commit();
            var12 = true;
            return var12;
         }
      } catch (SQLException var10) {
         session.rollback();
         throw var10;
      } finally {
         session.close();
      }
   }

   public boolean setDeviceModelApplyRule(Long ruleId, String deviceModelName, Boolean isLastVersion, Boolean isAutoUpdate) throws SQLException {
      SqlSession session = null;

      boolean var6;
      try {
         session = this.openNewSession(true);
         var6 = this.setDeviceModelApplyRule(ruleId, deviceModelName, isLastVersion, isAutoUpdate, session);
      } catch (SQLException var10) {
         throw var10;
      } finally {
         if (session != null) {
            session.close();
         }

      }

      return var6;
   }

   public boolean setDeviceModelApplyRule(Long ruleId, String deviceModelName, Boolean isLastVersion, Boolean isAutoUpdate, SqlSession session) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper(session)).setDeviceModelApplyRule(ruleId, deviceModelName, isLastVersion, isAutoUpdate) > 0;
   }

   public boolean setDeviceModelRuleMatch(Long ruleId, String deviceModelName) throws SQLException {
      ((AlarmRuleDaoMapper)this.getMapper()).setDeviceModelRuleMatchUpdate(ruleId, deviceModelName);
      return ((AlarmRuleDaoMapper)this.getMapper()).setDeviceModelRuleMatchInsert(ruleId, deviceModelName) > 0;
   }

   public boolean setDownloadStatus(Long ruleReservationId, String deviceId, String downloadStatus) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper()).setDownloadStatus(ruleReservationId, deviceId, downloadStatus) > 0;
   }

   private boolean setModelApplyRuleUpdate(AlarmRule alarmRule, SqlSession session) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper(session)).setModelApplyRuleUpdate(alarmRule) > 0;
   }

   public boolean setProcessRuleLastVersion(String deviceModelName, Long ruleId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var4;
      try {
         ((AlarmRuleDaoMapper)this.getMapper(session)).setProcessRuleLastVersionPreQuery(deviceModelName);
         if (((AlarmRuleDaoMapper)this.getMapper(session)).setProcessRuleLastVersion(deviceModelName, ruleId) > 0) {
            session.commit();
            var4 = true;
            return var4;
         }

         session.rollback();
         var4 = false;
      } catch (SQLException var9) {
         session.rollback();
         boolean var5 = false;
         return var5;
      } finally {
         session.close();
      }

      return var4;
   }

   public boolean setReservationDateToNow(Long ruleRsvId) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper()).setReservationDateToNow(ruleRsvId) > 0;
   }

   public boolean setReservationInfo(AlarmRuleReservation reservation) throws SQLException {
      return ((AlarmRuleDaoMapper)this.getMapper()).setReservationInfo(reservation) > 0;
   }

   public void setRuleAutoUpdateN(String deviceModelName, Long ruleId) throws SQLException {
      SqlSession session = null;

      try {
         session = this.openNewSession(true);
         this.setRuleAutoUpdateN(deviceModelName, ruleId, session);
      } catch (SQLException var8) {
         throw var8;
      } finally {
         session.close();
      }

   }

   public void setRuleAutoUpdateN(String deviceModelName, Long ruleId, SqlSession session) throws SQLException {
      ((AlarmRuleDaoMapper)this.getMapper(session)).setRuleAutoUpdateN(deviceModelName, ruleId);
   }
}
