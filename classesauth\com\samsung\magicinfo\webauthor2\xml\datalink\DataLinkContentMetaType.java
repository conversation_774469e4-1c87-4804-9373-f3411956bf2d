package com.samsung.magicinfo.webauthor2.xml.datalink;

import com.samsung.magicinfo.webauthor2.xml.datalink.LFDContentType;
import com.samsung.magicinfo.webauthor2.xml.datalink.PageType;
import com.samsung.magicinfo.webauthor2.xml.datalink.SettingType;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlRootElement(name = "DataLinkContentMeta")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DataLinkContentMetaType", propOrder = {"page", "lfdContent", "setting"})
public class DataLinkContentMetaType {
  @XmlElement(name = "Page")
  protected List<PageType> page;
  
  @XmlElement(name = "LFDContent", required = true)
  protected LFDContentType lfdContent;
  
  @XmlElement(name = "Setting", required = true)
  protected SettingType setting;
  
  @XmlAttribute(name = "version")
  protected Byte version;
  
  @XmlAttribute(name = "schemaversion")
  protected String schemaVersion;
  
  public List<PageType> getPage() {
    if (this.page == null)
      this.page = new ArrayList<>(); 
    return this.page;
  }
  
  public LFDContentType getLFDContent() {
    return this.lfdContent;
  }
  
  public void setLFDContent(LFDContentType value) {
    this.lfdContent = value;
  }
  
  public SettingType getSetting() {
    return this.setting;
  }
  
  public void setSetting(SettingType value) {
    this.setting = value;
  }
  
  public Byte getVersion() {
    return this.version;
  }
  
  public void setVersion(Byte value) {
    this.version = value;
  }
}
