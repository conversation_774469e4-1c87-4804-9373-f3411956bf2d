package com.samsung.magicinfo.rc.common.security;

import com.samsung.magicinfo.rc.common.security.DeviceWithToken;
import java.util.List;

public class UserDetail {
  List<DeviceWithToken> deviceWithTokens;
  
  String permission;
  
  String accessToken;
  
  String from;
  
  public void setDeviceWithTokens(List<DeviceWithToken> deviceWithTokens) {
    this.deviceWithTokens = deviceWithTokens;
  }
  
  public void setPermission(String permission) {
    this.permission = permission;
  }
  
  public void setAccessToken(String accessToken) {
    this.accessToken = accessToken;
  }
  
  public void setFrom(String from) {
    this.from = from;
  }
  
  public List<DeviceWithToken> getDeviceWithTokens() {
    return this.deviceWithTokens;
  }
  
  public String getPermission() {
    return this.permission;
  }
  
  public String getAccessToken() {
    return this.accessToken;
  }
  
  public String getFrom() {
    return this.from;
  }
}
