package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.datalink.DataLinkDescriptor;
import java.nio.file.Path;
import java.util.List;

public interface DataLinkSaveService {
  Path saveLocalDataLinkDescriptor(DataLinkDescriptor paramDataLinkDescriptor);
  
  String upload(DataLinkDescriptor paramDataLinkDescriptor);
  
  void addDlkInfo(String paramString1, String paramString2, String paramString3, List<String> paramList);
}
