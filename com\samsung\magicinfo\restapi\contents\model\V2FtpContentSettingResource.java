package com.samsung.magicinfo.restapi.contents.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Size;

@JsonInclude(Include.NON_EMPTY)
public class V2FtpContentSettingResource {
   @ApiModelProperty(
      dataType = "string",
      example = "00000000-0000-0000-0000-000000000000",
      allowEmptyValue = true,
      value = "ID of the content"
   )
   private String contentId = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Media type of the content",
      example = "FTP",
      allowableValues = "FTP"
   )
   private String type = "FTP";
   @ApiModelProperty(
      dataType = "string",
      example = "0",
      value = "ID of the group to which the content belongs"
   )
   private String groupId = "";
   @ApiModelProperty(
      dataType = "String",
      value = "Login user ID",
      example = "admin"
   )
   @Size(
      max = 64,
      message = "[ContentFilter][userId] max size is 64."
   )
   private String userId = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Name of the FTP content"
   )
   private String ftpContentName = "";
   @ApiModelProperty(
      dataType = "string",
      example = "***************",
      value = "IP address of the FTP server"
   )
   private String ftpIP = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Login ID of the FTP server"
   )
   private String ftpLoginId = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Password of the FTP server"
   )
   private String ftpPassword = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Directory path of content in the FTP server"
   )
   private String ftpDirectory = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Port number of the ftp server"
   )
   private String portStr = "21";
   @ApiModelProperty(
      dataType = "string",
      value = "Refresh interval of the FTP server"
   )
   private String ftpRefreshInterval = "";
   @ApiModelProperty(
      dataType = "boolean",
      value = "Whether polling is activated"
   )
   private String canRefresh = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Number of login retries"
   )
   private String loginRetryMaxCount = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Whether to enable login retry ('Y' or 'N')"
   )
   private String canLoginRetry = "";

   public V2FtpContentSettingResource() {
      super();
   }

   public String getContentId() {
      return this.contentId;
   }

   public void setContentId(String contentId) {
      this.contentId = contentId;
   }

   public String getFtpContentName() {
      return this.ftpContentName;
   }

   public void setFtpContentName(String ftpContentName) {
      this.ftpContentName = ftpContentName;
   }

   public String getFtpIP() {
      return this.ftpIP;
   }

   public void setFtpIP(String ftpIP) {
      this.ftpIP = ftpIP;
   }

   public String getFtpLoginId() {
      return this.ftpLoginId;
   }

   public void setFtpLoginId(String ftpLoginId) {
      this.ftpLoginId = ftpLoginId;
   }

   public String getFtpPassword() {
      return this.ftpPassword;
   }

   public void setFtpPassword(String ftpPassword) {
      this.ftpPassword = ftpPassword;
   }

   public String getFtpDirectory() {
      return this.ftpDirectory;
   }

   public void setFtpDirectory(String ftpDirectory) {
      this.ftpDirectory = ftpDirectory;
   }

   public String getPortStr() {
      return this.portStr;
   }

   public void setPortStr(String portStr) {
      this.portStr = portStr;
   }

   public String getFtpRefreshInterval() {
      return this.ftpRefreshInterval;
   }

   public void setFtpRefreshInterval(String ftpRefreshInterval) {
      this.ftpRefreshInterval = ftpRefreshInterval;
   }

   public String getType() {
      return this.type;
   }

   public void setType(String type) {
      this.type = type;
   }

   public String getCanRefresh() {
      return this.canRefresh;
   }

   public void setCanRefresh(String canRefresh) {
      this.canRefresh = canRefresh;
   }

   public String getLoginRetryMaxCount() {
      return this.loginRetryMaxCount;
   }

   public void setLoginRetryMaxCount(String loginRetryMaxCount) {
      this.loginRetryMaxCount = loginRetryMaxCount;
   }

   public String getCanLoginRetry() {
      return this.canLoginRetry;
   }

   public void setCanLoginRetry(String canLoginRetry) {
      this.canLoginRetry = canLoginRetry;
   }

   public String getGroupId() {
      return this.groupId;
   }

   public void setGroupId(String groupId) {
      this.groupId = groupId;
   }

   public String getUserId() {
      return this.userId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }
}
