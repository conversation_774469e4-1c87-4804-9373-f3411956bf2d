package com.samsung.magicinfo.webauthor2.model.datalink;

public enum ValueLocationType {
  horizontal("h"),
  vertical("v");
  
  private String shortName;
  
  ValueLocationType(String shortName) {
    this.shortName = shortName;
  }
  
  public String getShortName() {
    return this.shortName;
  }
  
  public static com.samsung.magicinfo.webauthor2.model.datalink.ValueLocationType getValueLocationTypeByShortName(String shortName) {
    if (shortName.equals(horizontal.getShortName()))
      return horizontal; 
    if (shortName.equals(vertical.getShortName()))
      return vertical; 
    throw new IllegalArgumentException("ValueLocationType short name must be eq to 'h' - horizontal or 'v' - vertical ");
  }
}
