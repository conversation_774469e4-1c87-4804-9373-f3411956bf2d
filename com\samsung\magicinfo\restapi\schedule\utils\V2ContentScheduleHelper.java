package com.samsung.magicinfo.restapi.schedule.utils;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.common.MessageSourceManager;
import com.samsung.magicinfo.framework.common.MessageSourceManagerImpl;
import com.samsung.magicinfo.framework.content.dao.PlaylistDao;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.NotificationData;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.monitoring.manager.DownloadStatusInfo;
import com.samsung.magicinfo.framework.monitoring.manager.DownloadStatusInfoImpl;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistInterface;
import com.samsung.magicinfo.framework.ruleset.entity.RuleSet;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfo;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfoImpl;
import com.samsung.magicinfo.framework.scheduler.entity.AdScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.AdSlotEntity;
import com.samsung.magicinfo.framework.scheduler.entity.AdThumbnailEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ChannelEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.DynamicTagEntity;
import com.samsung.magicinfo.framework.scheduler.entity.FrameEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleManager;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleManagerImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleUtility;
import com.samsung.magicinfo.protocol.util.MailUtil;
import com.samsung.magicinfo.restapi.common.model.V2CommonGroupIds;
import com.samsung.magicinfo.restapi.device.utils.RESTDeviceUtils;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.schedule.model.TTV2AdScheduleEventResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2AdScheduleThumbnailInfo;
import com.samsung.magicinfo.restapi.schedule.model.TTV2AdSlotResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2ChannelResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2DeviceGroupInfo;
import com.samsung.magicinfo.restapi.schedule.model.TTV2DisplayResolution;
import com.samsung.magicinfo.restapi.schedule.model.TTV2FrameAuthorityGroup;
import com.samsung.magicinfo.restapi.schedule.model.TTV2FrameResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2ProgramResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2ScheduleEventEffectInfo;
import com.samsung.magicinfo.restapi.schedule.model.TTV2ScheduleEventHWInfo;
import com.samsung.magicinfo.restapi.schedule.model.TTV2ScheduleEventResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2SubframeResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2SyncGroupDeviceTagMap;
import com.samsung.magicinfo.restapi.schedule.model.V2ContentScheduleDeployReservation;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import org.apache.logging.log4j.Logger;
import org.quartz.SchedulerException;

public class V2ContentScheduleHelper {
   protected final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   private String programId = null;
   private TTV2ProgramResource resource = null;
   private ProgramEntity programEntity = null;

   public V2ContentScheduleHelper(String programId, TTV2ProgramResource resource) {
      super();
      this.programId = programId;
      this.resource = resource;
   }

   public V2ContentScheduleHelper(TTV2ProgramResource resource) {
      super();
      this.resource = resource;
   }

   public V2ContentScheduleHelper() {
      super();
      this.resource = null;
   }

   public V2ContentScheduleHelper initialize() {
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      Locale locale = SecurityUtils.getLocale();
      if (this.resource.getProgramType().equals("ADV")) {
         this.programEntity = scheduleMgr.newADScheudle(this.resource.getDeviceType(), this.resource.getDeviceTypeVersion(), "", SecurityUtils.getLoginUserId(), this.resource.getSlotCount(), this.resource.getSlotDuration(), locale);
      } else {
         this.programEntity = scheduleMgr.newScheudle(this.resource.getDeviceType(), this.resource.getDeviceTypeVersion(), "", SecurityUtils.getLoginUserId(), "false", locale);
      }

      return this;
   }

   public V2ContentScheduleHelper fit() throws Exception {
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      String programType = this.programEntity.getProgram_type();
      if (programType.equals("ADV")) {
         this.fitChannels(this.resource.getChannels());
         this.programEntity.setAd_duration((long)this.resource.getSlotDuration());
      } else {
         this.programEntity.setChannelList(this.fitChannels(this.resource.getChannels()));
      }

      if (this.resource.getResolution() != null) {
         String resolution = String.format("%d*%d", (int)this.resource.getResolution().getWidth(), (int)this.resource.getResolution().getHeight());
         this.programEntity.setResolution(resolution);
      }

      scheduleMgr.updateSchedule(this.programEntity, this.programEntity.getProgram_id());
      return this;
   }

   public V2ContentScheduleHelper addEvents() throws SQLException, Exception {
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      List channels = this.resource.getChannels();
      Iterator var3 = channels.iterator();

      while(true) {
         int channelNo;
         ChannelEntity channelEntity;
         TTV2FrameResource frame;
         List subframes;
         Iterator var11;
         String slotId;
         do {
            label68:
            do {
               if (!var3.hasNext()) {
                  this.programEntity.setBgm_content_id(this.resource.getBgmContentId());
                  this.programEntity.setIs_bgm_with_content(this.resource.getIsBgmContentMute() ? "Y" : "N");
                  scheduleMgr.updateSchedule(this.programEntity, this.programEntity.getProgram_id());
                  return this;
               }

               TTV2ChannelResource channel = (TTV2ChannelResource)var3.next();
               channelNo = channel.getChannelNo();
               channelEntity = this.findChannelEntityByChannelNo(channelNo, this.programEntity);
               frame = channel.getFrame();
               String baseFrameId = frame.getFrameId();
               FrameEntity baseFrameEntity = this.findFrameEntityByFrameId(baseFrameId, channelEntity);
               if (!this.resource.getProgramType().equals("ADV")) {
                  if (frame.getHasPermissionForFrame()) {
                     this.logger.info("Updating events info as received from frontend for frame_id: " + frame.getFrameId());
                     this.addContentScheduleEvent(channelNo, baseFrameEntity, frame.getEvents());
                  } else {
                     this.logger.info("User does not have permission on this frame, events info already taken from previous version in fitFrame() for frame_id: " + frame.getFrameId());
                  }
               } else {
                  subframes = frame.getAdSlots();
                  var11 = subframes.iterator();

                  while(true) {
                     AdSlotEntity adSlotEntity;
                     List events;
                     do {
                        if (!var11.hasNext()) {
                           continue label68;
                        }

                        TTV2AdSlotResource adSlot = (TTV2AdSlotResource)var11.next();
                        slotId = adSlot.getSlotId();
                        adSlotEntity = this.findAdSlotEntityBySlotId(slotId, baseFrameEntity);
                        adSlotEntity.setDuration(CommonUtils.safeLongToInt(this.programEntity.getAd_duration()));
                        events = adSlot.getEvents();
                     } while(events == null);

                     Iterator var16 = events.iterator();

                     while(var16.hasNext()) {
                        TTV2AdScheduleEventResource event = (TTV2AdScheduleEventResource)var16.next();
                        String scheduleId = event.getScheduleId();
                        AdScheduleEntity scheduleEntity = this.findAdScheduleEntityByScheduleId(scheduleId, adSlotEntity);
                        PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
                        Playlist playlist = pInfo.getPlaylistActiveVerInfo(event.getContentId());
                        if (playlist == null) {
                           this.logger.error("Can not find matched playlist with playlistId : " + event.getContentId());
                           throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_PLAYLIST_MATCH_CONTENT_ID_IN_EVENT_SCHEDULE);
                        }

                        List contentList = pInfo.getContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
                        if (contentList == null) {
                           this.logger.error("Can not find matched content list with playlistId : " + playlist.getPlaylist_id());
                           throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_CONTENT_LIST_MATCH_PLAYLIST_ID);
                        }

                        scheduleEntity.setThumbnailList(this.loadThumbnails(contentList));
                        scheduleEntity.setContent_name(playlist.getPlaylist_name());
                        scheduleEntity.setProgram_id(this.programEntity.getProgram_id());
                        scheduleEntity.setContent_type("PLAYLIST");
                        scheduleEntity.setContent_id(event.getContentId());
                        scheduleEntity.setSchedule_id(scheduleId);
                        scheduleEntity.setSlot_id(slotId);
                        scheduleEntity.setStart_date(event.getStartDate());
                        scheduleEntity.setStart_time(event.getStartTime());
                        scheduleEntity.setStop_date(event.getStopDate());
                        scheduleEntity.setStop_time(event.getStopTime());
                        scheduleEntity.setUser_id(SecurityUtils.getLoginUserId());
                        scheduleEntity.setFile_size(playlist.getTotal_size());
                        scheduleEntity.setDuration(CommonUtils.safeLongToInt(this.programEntity.getAd_duration()));
                     }
                  }
               }
            } while(frame.getFrames() == null);
         } while(frame.getFrames().isEmpty());

         subframes = frame.getFrames();
         var11 = subframes.iterator();

         while(var11.hasNext()) {
            TTV2SubframeResource subframe = (TTV2SubframeResource)var11.next();
            slotId = subframe.getFrameId();
            FrameEntity frameEntity = this.findFrameEntityByFrameId(slotId, channelEntity);
            if (!this.resource.getProgramType().equals("ADV")) {
               if (subframe.getHasPermissionForFrame()) {
                  this.logger.info("Updating events info as received from frontend for frame_id: " + subframe.getFrameId());
                  this.addContentScheduleEvent(channelNo, frameEntity, subframe.getEvents());
               } else {
                  this.logger.info("User does not have permission on this sub frame, events info already taken from previous version in fitSubframes() for frame_id: " + subframe.getFrameId());
               }
            }
         }
      }
   }

   public V2ContentScheduleHelper saveAsNew() throws Exception {
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      String programType = this.programEntity.getProgram_type();
      String newProgramId = UUID.randomUUID().toString();
      this.programEntity.setProgram_id(newProgramId);
      List channelEntities = this.programEntity.getChannelList();
      if (channelEntities != null) {
         Iterator var5 = channelEntities.iterator();

         label68:
         while(true) {
            List frameEntities;
            do {
               if (!var5.hasNext()) {
                  break label68;
               }

               ChannelEntity channelEntity = (ChannelEntity)var5.next();
               channelEntity.setProgram_id(newProgramId);
               frameEntities = channelEntity.getFrameList();
            } while(frameEntities == null);

            Iterator var8 = frameEntities.iterator();

            label66:
            while(true) {
               String newFrameId;
               List contentsScheduleEntities;
               Iterator var12;
               String newSlotId;
               do {
                  while(true) {
                     if (!var8.hasNext()) {
                        continue label68;
                     }

                     FrameEntity frameEntity = (FrameEntity)var8.next();
                     newFrameId = UUID.randomUUID().toString();
                     frameEntity.setProgram_id(newProgramId);
                     frameEntity.setFrame_id(newFrameId);
                     if (programType.equals("ADV")) {
                        contentsScheduleEntities = frameEntity.getSlotList();
                        break;
                     }

                     contentsScheduleEntities = frameEntity.getScheduleList();
                     if (contentsScheduleEntities != null) {
                        var12 = contentsScheduleEntities.iterator();

                        while(var12.hasNext()) {
                           ContentsScheduleEntity contentsScheduleEntity = (ContentsScheduleEntity)var12.next();
                           newSlotId = UUID.randomUUID().toString();
                           contentsScheduleEntity.setProgram_id(newProgramId);
                           contentsScheduleEntity.setSchedule_id(newSlotId);
                        }
                     }
                  }
               } while(contentsScheduleEntities == null);

               var12 = contentsScheduleEntities.iterator();

               while(true) {
                  List adScheduleEntities;
                  do {
                     if (!var12.hasNext()) {
                        continue label66;
                     }

                     AdSlotEntity adSlotEntity = (AdSlotEntity)var12.next();
                     newSlotId = UUID.randomUUID().toString();
                     adSlotEntity.setProgram_id(newProgramId);
                     adSlotEntity.setFrame_id(newFrameId);
                     adSlotEntity.setSlot_id(newSlotId);
                     adScheduleEntities = adSlotEntity.getScheduleList();
                  } while(adScheduleEntities == null);

                  Iterator var16 = adScheduleEntities.iterator();

                  while(var16.hasNext()) {
                     AdScheduleEntity adScheduleEntity = (AdScheduleEntity)var16.next();
                     String newScheduleId = UUID.randomUUID().toString();
                     adScheduleEntity.setSchedule_id(newScheduleId);
                     adScheduleEntity.setProgram_id(newProgramId);
                     adScheduleEntity.setSlot_id(newSlotId);
                     adScheduleEntity.setUser_id(SecurityUtils.getLoginUserId());
                  }
               }
            }
         }
      }

      scheduleMgr.setScheudle(newProgramId, this.programEntity);
      return this;
   }

   private void addContentScheduleEvent(int channelNo, FrameEntity frameEntity, List events) throws SQLException {
      ContentsScheduleEntity eventEntity;
      if (events != null && !events.isEmpty()) {
         for(Iterator var4 = events.iterator(); var4.hasNext(); this.programEntity.setCurrentIndex(eventEntity.getPriority())) {
            TTV2ScheduleEventResource event = (TTV2ScheduleEventResource)var4.next();
            String scheduleId = event.getScheduleId();
            eventEntity = this.findContentsScheduleEntityByScheduleId(scheduleId, frameEntity);
            eventEntity = this.fillContentsScheduleEntity(eventEntity, this.programEntity.getDevice_type(), this.programEntity.getDevice_type_version(), this.resource.getProgramType(), channelNo, event);
            if (!eventEntity.getSchedule_type().equals("03")) {
               eventEntity.setFrame_index(frameEntity.getFrame_index());
            }
         }
      }

   }

   public V2ContentScheduleHelper checkDisk() {
      if (this.resource.getDeviceGroups() != null && !this.resource.getDeviceGroups().isEmpty()) {
         try {
            String deviceGroupIds = getDeviceGroupIdListAsString(this.resource.getDeviceGroups());
            long diskSize = this.getDiskSpaceRepository(deviceGroupIds);
            if (this.programEntity.getChannelList() != null) {
               long scheduleSize = 0L;
               List channels = this.programEntity.getChannelList();
               Iterator var7 = channels.iterator();

               label48:
               while(true) {
                  ChannelEntity channel;
                  do {
                     if (!var7.hasNext()) {
                        if (diskSize < scheduleSize) {
                           throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_NOT_ENOUGH_STORAGE_SPACE);
                        }

                        return this;
                     }

                     channel = (ChannelEntity)var7.next();
                  } while(channel.getFrameList() == null);

                  List frames = channel.getFrameList();
                  Iterator var10 = frames.iterator();

                  while(true) {
                     FrameEntity frame;
                     do {
                        if (!var10.hasNext()) {
                           continue label48;
                        }

                        frame = (FrameEntity)var10.next();
                     } while(frame.getScheduleList() == null);

                     ContentsScheduleEntity schedule;
                     for(Iterator var12 = frame.getScheduleList().iterator(); var12.hasNext(); scheduleSize += schedule.getFile_size()) {
                        schedule = (ContentsScheduleEntity)var12.next();
                     }
                  }
               }
            }
         } catch (Exception var14) {
            this.logger.error(var14);
         }

         return this;
      } else {
         return this;
      }
   }

   public TTV2ProgramResource deploy() throws SQLException, Exception {
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      Map deployReserveMap = this.resource.getIsDeployReserved() ? this.getDeployReservationMap(this.resource.getDeployReservation()) : null;
      String savedProgramId = null;
      boolean var9 = false;

      ProgramEntity savedProgramEntity;
      label189: {
         try {
            var9 = true;
            if (this.resource.getProgramType().equals("ADV")) {
               scheduleMgr.saveAdProgram(this.programEntity.getProgram_id(), this.resource.getProgramName(), String.valueOf(this.resource.getProgramGroupId()), getDeviceGroupIdListAsString(this.resource.getDeviceGroups()), this.resource.getDescription(), this.resource.getIsDeployReserved() ? "true" : "false", deployReserveMap, "", "REST API v2.0");
               var9 = false;
            } else {
               scheduleMgr.saveProgram(this.programEntity.getProgram_id(), this.resource.getProgramName(), String.valueOf(this.resource.getProgramGroupId()), getDeviceGroupIdListAsString(this.resource.getDeviceGroups()), this.resource.getDescription(), this.resource.getIsDeployReserved() ? "true" : "false", deployReserveMap, this.resource.getIsContentSyncOn() ? "1" : "0", "", this.resource.getBgmContentId(), this.resource.getProgramType().equalsIgnoreCase("SYNC") ? "true" : "false", this.resource.getProgramType().equalsIgnoreCase("VWL") ? "true" : "false", this.resource.getIsBgmContentMute() ? "true" : "false", "REST API v2.0", this.resource.getIsPlayFromLastPlaybackPoint() ? "1" : "0");
               var9 = false;
            }
            break label189;
         } catch (SchedulerException var10) {
            this.resource.setDeployReservationResult("Fail");
            this.resource.setDeployReservationMessage("Deploy reservation failed.(" + var10.getMessage() + ")");
            var9 = false;
         } finally {
            if (var9) {
               ProgramEntity var6 = scheduleMgr.getScheudle(this.programEntity.getProgram_id());
               if (var6 != null) {
                  scheduleMgr.removeSchedule(this.programEntity.getProgram_id());
                  this.sendNotificationData(this.resource.getProgramGroupId(), this.resource.getProgramName(), "00");
                  savedProgramId = this.programEntity.getProgram_id();
               }

            }
         }

         savedProgramEntity = scheduleMgr.getScheudle(this.programEntity.getProgram_id());
         if (savedProgramEntity != null) {
            scheduleMgr.removeSchedule(this.programEntity.getProgram_id());
            this.sendNotificationData(this.resource.getProgramGroupId(), this.resource.getProgramName(), "00");
            savedProgramId = this.programEntity.getProgram_id();
         }

         return savedProgramId == null ? null : this.loadProgramResource(savedProgramId);
      }

      savedProgramEntity = scheduleMgr.getScheudle(this.programEntity.getProgram_id());
      if (savedProgramEntity != null) {
         scheduleMgr.removeSchedule(this.programEntity.getProgram_id());
         this.sendNotificationData(this.resource.getProgramGroupId(), this.resource.getProgramName(), "00");
         savedProgramId = this.programEntity.getProgram_id();
      }

      return savedProgramId == null ? null : this.loadProgramResource(savedProgramId);
   }

   public TTV2ProgramResource update() throws SQLException, Exception {
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      Map deployReserveMap = this.resource.getIsDeployReserved() ? this.getDeployReservationMap(this.resource.getDeployReservation()) : null;
      String savedProgramId = null;
      boolean var9 = false;

      ProgramEntity savedProgramEntity;
      label165: {
         try {
            var9 = true;
            if (this.resource.getProgramType().equals("ADV")) {
               scheduleMgr.updateADProgram(this.programEntity.getProgram_id(), this.resource.getProgramName(), String.valueOf(this.resource.getProgramGroupId()), getDeviceGroupIdListAsString(this.resource.getDeviceGroups()), this.resource.getDescription(), this.resource.getIsDeployReserved() ? "true" : "false", deployReserveMap, "", "REST API v2.0");
               var9 = false;
            } else {
               scheduleMgr.updateProgram(this.programEntity.getProgram_id(), this.resource.getProgramName(), String.valueOf(this.resource.getProgramGroupId()), getDeviceGroupIdListAsString(this.resource.getDeviceGroups()), this.resource.getDescription(), this.resource.getIsDeployReserved() ? "true" : "false", deployReserveMap, this.resource.getIsContentSyncOn() ? "1" : "0", "", this.resource.getBgmContentId(), this.resource.getIsBgmContentMute() ? "true" : "false", "REST API v2.0", this.resource.getIsPlayFromLastPlaybackPoint() ? "1" : "0");
               var9 = false;
            }
            break label165;
         } catch (SchedulerException var10) {
            this.resource.setDeployReservationResult("Fail");
            this.resource.setDeployReservationMessage("Deploy reservation failed.(" + var10.getMessage() + ")");
            var9 = false;
         } finally {
            if (var9) {
               ProgramEntity var6 = scheduleMgr.getScheudle(this.programEntity.getProgram_id());
               if (var6 != null) {
                  scheduleMgr.removeSchedule(this.programEntity.getProgram_id());
                  this.sendNotificationData(this.resource.getProgramGroupId(), this.resource.getProgramName(), "01");
                  savedProgramId = this.programEntity.getProgram_id();
               }

            }
         }

         savedProgramEntity = scheduleMgr.getScheudle(this.programEntity.getProgram_id());
         if (savedProgramEntity != null) {
            scheduleMgr.removeSchedule(this.programEntity.getProgram_id());
            this.sendNotificationData(this.resource.getProgramGroupId(), this.resource.getProgramName(), "01");
            savedProgramId = this.programEntity.getProgram_id();
         }

         return savedProgramId == null ? null : this.loadProgramResource(savedProgramId);
      }

      savedProgramEntity = scheduleMgr.getScheudle(this.programEntity.getProgram_id());
      if (savedProgramEntity != null) {
         scheduleMgr.removeSchedule(this.programEntity.getProgram_id());
         this.sendNotificationData(this.resource.getProgramGroupId(), this.resource.getProgramName(), "01");
         savedProgramId = this.programEntity.getProgram_id();
      }

      return savedProgramId == null ? null : this.loadProgramResource(savedProgramId);
   }

   public V2ContentScheduleHelper load() throws Exception {
      if (this.programId != null && !this.programId.isEmpty()) {
         LinkedHashMap notUsedData = new LinkedHashMap();
         ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
         scheduleMgr.loadProgram(SecurityUtils.getLoginUser().getGroup_id(), SecurityUtils.getLoginUser().getOrganization(), notUsedData, this.programId, "");
         this.programEntity = scheduleMgr.getScheudle(this.programId);
         return this;
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_NOT_NULL_OR_EMPTY, new String[]{"programId"});
      }
   }

   public TTV2ProgramResource loadProgramResource(String programId) throws Exception {
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      ProgramEntity programEntity = scheduleInfo.getProgram(programId);
      if (programEntity == null) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"program entity information"});
      } else {
         TTV2ProgramResource resource = new TTV2ProgramResource();
         resource.setProgramId(programEntity.getProgram_id());
         resource.setVersion(programEntity.getVersion());
         resource.setProgramName(programEntity.getProgram_name());
         resource.setProgramType(programEntity.getProgram_type() != null ? programEntity.getProgram_type() : "LFD");
         String strDeviceType = programEntity.getDevice_type();
         String strDeviceTypeVersion = String.valueOf(programEntity.getDevice_type_version());
         Long deviceTypePriority = CommonUtils.getMinPriorityByDeviceinfo(strDeviceType, strDeviceTypeVersion);
         if (deviceTypePriority != null) {
            resource.setDeviceTypePriority(deviceTypePriority);
         }

         if (programEntity.getProgram_type().equals("ADV")) {
            List slotEntities = scheduleInfo.getSlotListFromProgramId(programId);
            if (slotEntities != null) {
               resource.setSlotCount(slotEntities.size());
            }

            resource.setSlotDuration((int)programEntity.getAd_duration());
         }

         String reservedStartDate = StrUtils.nvl(programEntity.getReservation_start_date());
         String reservedEndDate = StrUtils.nvl(programEntity.getReservation_end_date());
         String reservedDeployTime = StrUtils.nvl(programEntity.getDeploy_time());
         if (!reservedStartDate.isEmpty() && !reservedEndDate.isEmpty() && !reservedDeployTime.isEmpty()) {
            V2ContentScheduleDeployReservation reservation = new V2ContentScheduleDeployReservation();
            reservation.setRepeatType(programEntity.getReservation_repeat_type().toUpperCase());
            reservation.setStartDate(reservedStartDate);
            reservation.setEndDate(reservedEndDate);
            reservation.setDeployTime(reservedDeployTime);
            reservation.setRepeatedDayOfWeekList(ConvertUtil.convertRepeatedDaysFromArrayToList(programEntity.getReservation_weekly()));
            reservation.setRepeatedDateOfMonthList(ConvertUtil.convertRepeatedDatesFromArrayToList(programEntity.getReservation_monthly()));
            resource.setIsDeployReserved(true);
            resource.setDeployReservation(reservation);
            if (this.resource != null) {
               if (this.resource.getDeployReservationResult().equals("Fail")) {
                  resource.setDeployReservationResult(this.resource.getDeployReservationResult());
                  resource.setDeployReservationMessage(this.resource.getDeployReservationMessage());
               } else {
                  resource.setDeployReservationResult("Success");
               }
            }
         }

         resource.setIsContentSyncOn(programEntity.getSynchronization() != null && programEntity.getSynchronization().equals("1"));
         resource.setIsPlayFromLastPlaybackPoint(programEntity.getResume() != null && programEntity.getResume().equals("1"));
         resource.setDeviceType(programEntity.getDevice_type());
         resource.setDeviceTypeVersion(programEntity.getDevice_type_version());
         resource.setDescription(programEntity.getDescription());
         if (programEntity.getBgm_content_id() != null && !programEntity.getBgm_content_id().isEmpty()) {
            String bgmContentId = programEntity.getBgm_content_id();
            resource.setBgmContentId(bgmContentId);
            resource.setBgmContentName(contentInfo.getContentName(bgmContentId));
            resource.setIsBgmContentMute(programEntity.getIs_bgm_with_content().equals("Y"));
         }

         List programGroupMap = scheduleInfo.getProgramGroupIdAndName(programId);

         for(int i = 0; i < programGroupMap.size(); ++i) {
            resource.setProgramGroupId((Long)((Map)programGroupMap.get(i)).get("GROUP_ID"));
            resource.setProgramGroupName((String)((Map)programGroupMap.get(i)).get("GROUP_NAME"));
         }

         Map map = scheduleInfo.getDeviceGroupIdsAndName(programId);
         List channelEntities;
         if (map.containsKey("device_group_ids") && map.containsKey("group_names")) {
            String strDeviceGroupIdList = (String)map.get("device_group_ids");
            String strDeviceGroupNameList = (String)map.get("group_names");
            if (!strDeviceGroupIdList.isEmpty()) {
               List strDeviceGroupIds = Arrays.asList(strDeviceGroupIdList.split(","));
               channelEntities = Arrays.asList(strDeviceGroupNameList.split(","));
               List deviceGroupInfos = new ArrayList();
               int deviceCount = 0;
               DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();

               for(int i = 0; i < strDeviceGroupIds.size(); ++i) {
                  Long deviceGroupId = Long.parseLong((String)strDeviceGroupIds.get(i));
                  String deviceGroupName = (String)channelEntities.get(i);
                  TTV2DeviceGroupInfo info = new TTV2DeviceGroupInfo();
                  info.setGroupId(deviceGroupId);
                  info.setGroupName(deviceGroupName);
                  deviceGroupInfos.add(info);
                  deviceCount += deviceGroupDao.getCntDeviceInDeviceGroup(deviceGroupId.intValue());
               }

               resource.setDeviceCount(deviceCount);
               resource.setDeviceGroups(deviceGroupInfos);
               if (programEntity.getProgram_type().equals("SYNC")) {
                  V2CommonGroupIds idListForDeviceTags = new V2CommonGroupIds();
                  List groupIds = new ArrayList();

                  for(int i = 0; i < strDeviceGroupIds.size(); ++i) {
                     Long deviceGroupId = Long.parseLong((String)strDeviceGroupIds.get(i));
                     groupIds.add(deviceGroupId);
                  }

                  idListForDeviceTags.setIds(groupIds);
                  List tagResources = RESTDeviceUtils.getDeviceTagsByGroupIds(idListForDeviceTags);
                  resource.setDeviceTags(tagResources);
               }
            }
         }

         if (programEntity.getModify_date() != null) {
            resource.setLastModifiedDate(programEntity.getModify_date());
         }

         if (!StrUtils.nvl(programEntity.getResolution()).isEmpty()) {
            String[] resolutionArr = programEntity.getResolution().split("\\*");
            TTV2DisplayResolution resolution = new TTV2DisplayResolution();
            resolution.setWidth(Double.parseDouble(resolutionArr[0]));
            resolution.setHeight(Double.parseDouble(resolutionArr[1]));
            resource.setResolution(resolution);
         }

         DownloadStatusInfo downloadInfo = DownloadStatusInfoImpl.getInstacne();
         Object[] rtn = downloadInfo.getDownloadStatusListByProgramId(programId);
         if (rtn != null && rtn[1] != null) {
            int total = 0;
            int complete = 0;
            Map resultMap = (Map)rtn[1];
            if (resultMap.get("deviceCount") != null) {
               total = (Integer)resultMap.get("deviceCount");
               resource.setPublishDeviceTotal(total);
            }

            if (resultMap.get("completeCount") != null) {
               complete = (Integer)resultMap.get("completeCount");
               resource.setPublishedDeviceUntilNow(complete);
            }

            if (total > 0) {
               float percentage = (float)complete / (float)total * 100.0F;
               resource.setPublishedProgress((int)percentage);
            }
         }

         List channelResources = new ArrayList();
         channelEntities = scheduleInfo.getChannelListByProgramId(programId);
         Iterator var37 = channelEntities.iterator();

         while(var37.hasNext()) {
            ChannelEntity channelEntity = (ChannelEntity)var37.next();
            TTV2ChannelResource channelResource = this.loadChannelResource(programId, resource.getProgramType(), channelEntity);
            channelResources.add(channelResource);
         }

         resource.setChannels(channelResources);
         resource.setBaseFrameId(this.getBaseFrameId(resource.getProgramType(), programEntity));
         return resource;
      }
   }

   private ContentsScheduleEntity loadContentsScheduleEntity(ContentsScheduleEntity entity) throws SQLException {
      String contentIdOfScheduleEvent = entity.getContent_id();
      String contentTypeOfScheduleEvent = entity.getContent_type();
      if (contentTypeOfScheduleEvent != null && contentTypeOfScheduleEvent.equalsIgnoreCase("RULESET")) {
         RuleSetInfo rulesetDao = RuleSetInfoImpl.getInstance();
         RuleSet ruleset = rulesetDao.getRuleset(contentIdOfScheduleEvent);
         entity.setContent_name(ruleset.getName());
         entity.setFile_id("RULESET_THUMBNAIL");
         entity.setFile_name("RULESET_THUMBNAIL.PNG");
      } else {
         ContentInfo contentInfo = ContentInfoImpl.getInstance();
         Content content = contentInfo.getContentActiveVerInfo(contentIdOfScheduleEvent);
         if (content != null) {
            entity.setContent_name(content.getContent_name());
            entity.setFile_id(content.getThumb_file_id());
            entity.setFile_name(content.getThumb_file_name());
            entity.setFile_size(content.getTotal_size());
         } else {
            PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
            Playlist playlist = playlistInfo.getPlaylistActiveVerInfo(contentIdOfScheduleEvent);
            if (playlist != null) {
               String playlistType = playlist.getPlaylist_type();
               String playlistName = playlist.getPlaylist_name();
               if (playlistType.equals("5")) {
                  String playlistId = playlist.getPlaylist_id();
                  long playlistVersionId = playlist.getVersion_id();
                  entity.setContent_name(playlistName);
                  entity.setPlaylist_type("5");
                  List contents = playlistInfo.getTagContentListOfPlaylist(playlistId, playlistVersionId);
                  if (contents != null && contents.size() > 0) {
                     entity.setFile_id(((Content)contents.get(0)).getThumb_file_id());
                     entity.setFile_name(((Content)contents.get(0)).getThumb_file_name());
                  }
               } else {
                  content = contentInfo.getContentActiveVerInfo(playlist.getContent_id());
                  entity.setContent_name(playlistName);
                  entity.setFile_size(playlist.getTotal_size());
                  if (content != null) {
                     entity.setFile_id(content.getThumb_file_id());
                     entity.setFile_name(content.getThumb_file_name());
                  }
               }
            }
         }
      }

      return entity;
   }

   private boolean isCurrentUserAdminOrOwnerOfSchedule(String scheduleCreatorUserId) throws Exception {
      String roleNameofLoginUser = SecurityUtils.getUserContainer().getUser().getRole_name();
      String currentUsersId = SecurityUtils.getUserContainer().getUser().getUser_id();
      return roleNameofLoginUser.equals("Server Administrator") || roleNameofLoginUser.equals("Administrator") || currentUsersId.equals(scheduleCreatorUserId);
   }

   private TTV2ChannelResource loadChannelResource(String programId, String programType, ChannelEntity channelEntity) throws Exception {
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      String scheduleCreatorUserId = scheduleInfo.getCreatorIdByProgramId(programId);
      boolean isUserAdminOrOwner = this.isCurrentUserAdminOrOwnerOfSchedule(scheduleCreatorUserId);
      TTV2ChannelResource channelResource = new TTV2ChannelResource();
      int channelNo = channelEntity.getChannel_no();
      String channelName = channelEntity.getChannel_name();
      String mainFrameId = "";
      TTV2FrameResource frameResource = new TTV2FrameResource();
      List subframeResources = new ArrayList();
      List frameEntities = scheduleInfo.getFrames(programId, channelNo);
      if (frameEntities == null) {
         this.logger.error("Can not find matched frame list with programId(" + programId + ") & channelNo(" + channelNo + ")");
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FRAME_LIST_MATCH_PROGRAM_ID);
      } else {
         boolean areAnyAuthorityGroupsSet = false;
         boolean isUserPresentInAnyFrameAuthorityGroups = false;

         for(int i = 0; i < frameEntities.size(); ++i) {
            FrameEntity frameEntity = (FrameEntity)frameEntities.get(i);
            if (frameEntity.getFrame_index() == 0) {
               frameResource = this.loadFrameResource(programId, programType, channelNo, frameEntity, isUserAdminOrOwner);
               if (frameResource.getAuthorityGroups() != null && frameResource.getAuthorityGroups().size() != 0) {
                  areAnyAuthorityGroupsSet = true;
                  if (frameResource.getHasPermissionForFrame()) {
                     isUserPresentInAnyFrameAuthorityGroups = true;
                  }
               }
            } else {
               TTV2SubframeResource subframeResource = this.loadSubframeResource(programId, programType, channelNo, frameEntity, isUserAdminOrOwner);
               if (subframeResource.getAuthorityGroups() != null && subframeResource.getAuthorityGroups().size() != 0) {
                  areAnyAuthorityGroupsSet = true;
                  if (subframeResource.getHasPermissionForFrame()) {
                     isUserPresentInAnyFrameAuthorityGroups = true;
                  }
               }

               subframeResources.add(subframeResource);
               if (frameResource.getIsMainFrame()) {
                  mainFrameId = frameResource.getFrameId();
               }
            }
         }

         frameResource.setFrames(subframeResources);
         boolean permissionToFrameProperties = isUserAdminOrOwner || !areAnyAuthorityGroupsSet;
         channelResource.sethasPermissionForFrameProperties(permissionToFrameProperties);
         channelResource.sethasPermissionInAuthorityGroups(isUserPresentInAnyFrameAuthorityGroups);
         channelResource.setChannelNo(channelNo);
         channelResource.setChannelName(channelName);
         channelResource.setMainFrameId(mainFrameId);
         channelResource.setFrame(frameResource);
         return channelResource;
      }
   }

   private TTV2FrameResource loadFrameResource(String programId, String programType, int channelNo, FrameEntity frameEntity, boolean isUserAdminOrOwner) throws Exception {
      TTV2FrameResource frameResource = new TTV2FrameResource();
      List adSlotResources;
      if (programType.equals("ADV")) {
         adSlotResources = this.loadAdSlotResources(programId, frameEntity.getFrame_id());
         frameResource.setAdSlots(adSlotResources);
      } else {
         adSlotResources = this.loadScheduleEventResources(programId, programType, channelNo, frameEntity);
         frameResource.setEvents(adSlotResources);
      }

      frameResource.setFrameId(frameEntity.getFrame_id());
      frameResource.setFrameName(frameEntity.getFrame_name());
      frameResource.setFrameIndex(frameEntity.getFrame_index());
      frameResource.setIsMainFrame(frameEntity.getIs_main_frame().equals("Y"));
      frameResource.setX(frameEntity.getX());
      frameResource.setY(frameEntity.getY());
      frameResource.setWidth(frameEntity.getWidth());
      frameResource.setHeight(frameEntity.getHeight());
      frameResource.setDefaultContentId(frameEntity.getDefault_content_id());
      frameResource.setDefaultContentName(frameEntity.getDefault_content_names());
      String lineData = frameEntity.getLine_data();
      if (!StrUtils.nvl(lineData).isEmpty()) {
         if (!lineData.equals("CustomLayout") && !lineData.equals("CUSTOM")) {
            if (lineData.equals("customFixed")) {
               frameResource.setLineData("CUSTOM_FIXED");
            } else if (lineData.equals("fixed")) {
               frameResource.setLineData("FIXED");
            } else if (lineData.equals("ZeroFrameOnly")) {
               frameResource.setLineData("ZERO_FRAME_ONLY");
            } else {
               frameResource.setLineData(lineData);
            }
         } else {
            frameResource.setLineData("CUSTOM");
         }

         frameResource.setFrameType(!lineData.equals("CustomLayout") && !lineData.equals("CUSTOM") ? "FIXED" : "CUSTOM");
      }

      List authorityGroups = this.loadFrameAuthorityGroups(frameEntity);
      if (authorityGroups != null) {
         frameResource.setAuthorityGroups(authorityGroups);
         boolean isUserAuthorized = this.checkFrameAuthorityOfUser(authorityGroups, isUserAdminOrOwner);
         frameResource.setHasPermissionForFrame(isUserAuthorized);
      }

      return frameResource;
   }

   private TTV2SubframeResource loadSubframeResource(String programId, String programType, int channelNo, FrameEntity frameEntity, boolean isUserAdminOrOwner) throws Exception {
      TTV2SubframeResource frameResource = new TTV2SubframeResource();
      List scheduleEventResources = this.loadScheduleEventResources(programId, programType, channelNo, frameEntity);
      frameResource.setEvents(scheduleEventResources);
      frameResource.setFrameId(frameEntity.getFrame_id());
      frameResource.setFrameName(frameEntity.getFrame_name());
      frameResource.setFrameIndex(frameEntity.getFrame_index());
      frameResource.setIsMainFrame(frameEntity.getIs_main_frame().equals("Y"));
      frameResource.setX(frameEntity.getX());
      frameResource.setY(frameEntity.getY());
      frameResource.setWidth(frameEntity.getWidth());
      frameResource.setHeight(frameEntity.getHeight());
      frameResource.setDefaultContentId(frameEntity.getDefault_content_id());
      frameResource.setDefaultContentName(frameEntity.getDefault_content_names());
      String lineData = frameEntity.getLine_data();
      if (!StrUtils.nvl(lineData).isEmpty()) {
         if (!lineData.equals("CustomLayout") && !lineData.equals("CUSTOM")) {
            if (lineData.equals("customFixed")) {
               frameResource.setLineData("CUSTOM_FIXED");
            } else if (lineData.equals("fixed")) {
               frameResource.setLineData("FIXED");
            } else if (lineData.equals("ZeroFrameOnly")) {
               frameResource.setLineData("ZERO_FRAME_ONLY");
            } else {
               frameResource.setLineData(lineData);
            }
         } else {
            frameResource.setLineData("CUSTOM");
         }

         frameResource.setFrameType(!lineData.equals("CustomLayout") && !lineData.equals("CUSTOM") ? "FIXED" : "CUSTOM");
      }

      List authorityGroups = this.loadFrameAuthorityGroups(frameEntity);
      if (authorityGroups != null) {
         frameResource.setAuthorityGroups(authorityGroups);
         boolean isUserAuthorized = this.checkFrameAuthorityOfUser(authorityGroups, isUserAdminOrOwner);
         frameResource.setHasPermissionForFrame(isUserAuthorized);
      }

      return frameResource;
   }

   private List loadFrameAuthorityGroups(FrameEntity frameEntity) {
      String[] authorityGroupIds = frameEntity.getAuthority();
      if (authorityGroupIds != null && authorityGroupIds.length > 0) {
         String[] authorityGroupNames = frameEntity.getAuthorityNames();
         List authorityGroups = new ArrayList();

         for(int i = 0; i < authorityGroupIds.length; ++i) {
            TTV2FrameAuthorityGroup authorityGroup = new TTV2FrameAuthorityGroup();
            authorityGroup.setGroupId(Long.parseLong(authorityGroupIds[i]));
            authorityGroup.setGroupName(authorityGroupNames[i]);
            authorityGroups.add(authorityGroup);
         }

         return authorityGroups;
      } else {
         return null;
      }
   }

   private boolean checkFrameAuthorityOfUser(List authorityGroups, boolean isUserAdminOrOwner) throws Exception {
      if (authorityGroups != null && authorityGroups.size() > 0) {
         boolean isCurrentUserAuthorized = false;
         if (isUserAdminOrOwner) {
            return true;
         } else {
            Long currentUserGroupId = SecurityUtils.getUserContainer().getUser().getGroup_id();

            for(int i = 0; i < authorityGroups.size(); ++i) {
               if (((TTV2FrameAuthorityGroup)authorityGroups.get(i)).getGroupId() == currentUserGroupId) {
                  isCurrentUserAuthorized = true;
                  break;
               }
            }

            return isCurrentUserAuthorized;
         }
      } else {
         return true;
      }
   }

   private String getBaseFrameId(String programType, ProgramEntity programEntity) {
      List frameIds = new ArrayList();
      if (!programType.equals("ADV")) {
         List channelEntities = programEntity.getChannelList();
         if (channelEntities != null) {
            Iterator var5 = channelEntities.iterator();

            while(true) {
               List frameEntities;
               do {
                  if (!var5.hasNext()) {
                     return frameIds.size() > 0 ? (String)frameIds.get(0) : "AUTHORITY";
                  }

                  ChannelEntity channelEntity = (ChannelEntity)var5.next();
                  frameEntities = channelEntity.getFrameList();
               } while(frameEntities == null);

               Iterator var8 = frameEntities.iterator();

               while(var8.hasNext()) {
                  FrameEntity frameEntity = (FrameEntity)var8.next();
                  frameEntity.setFlag(true);
                  if (frameEntity.getAuthorityList() == null) {
                     frameIds.add(frameEntity.getFrame_id());
                  } else {
                     List authorityList = frameEntity.getAuthorityList();
                     if (frameEntity.getFrame_id() != null) {
                        long frameId = Long.parseLong(frameEntity.getFrame_id());
                        if (authorityList.contains(frameId)) {
                           frameIds.add(frameEntity.getFrame_id());
                        }
                     }
                  }
               }
            }
         }
      }

      return frameIds.size() > 0 ? (String)frameIds.get(0) : "AUTHORITY";
   }

   private List loadScheduleEventResources(String programId, String programType, int channelNo, FrameEntity frameEntity) throws SQLException {
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      ProgramEntity programEntity = scheduleInfo.getProgram(programId);
      List resources = new ArrayList();
      List entities = scheduleInfo.getContentListFromProgramidandChannel(programId, channelNo, frameEntity.getScreen_index(), frameEntity.getFrame_index());
      if (entities != null) {
         for(int i = 0; i < entities.size(); ++i) {
            ContentsScheduleEntity entity = this.loadContentsScheduleEntity((ContentsScheduleEntity)entities.get(i));
            TTV2ScheduleEventResource resource = new TTV2ScheduleEventResource();
            resource.setScheduleId(entity.getSchedule_id());
            resource.setScheduleType(entity.getSchedule_type());
            resource.setPriority(entity.getPriority());
            resource.setIsSafetyLockSet(entity.getSafetyLock().equalsIgnoreCase("true"));
            resource.setIsInfinitePlay(StrUtils.nvl(entity.getStop_date(), "2999-12-31").equals("2999-12-31"));
            resource.setStartDate(entity.getStart_date());
            resource.setEndDate(entity.getStop_date());
            resource.setStartTime(entity.getStart_time());
            resource.setDurationInSeconds(entity.getDuration());
            if (entity.getRepeat_type().equalsIgnoreCase("daily")) {
               resource.setRepeatType("daily".toUpperCase());
            } else if (entity.getRepeat_type().equalsIgnoreCase("day_of_week")) {
               resource.setRepeatType("weekly".toUpperCase());
            } else if (entity.getRepeat_type().equalsIgnoreCase("day_of_month")) {
               resource.setRepeatType("monthly".toUpperCase());
            } else {
               resource.setRepeatType("once".toUpperCase());
            }

            if (entity.getWeekdays() != null) {
               resource.setRepeatedDayOfWeekList(ConvertUtil.convertRepeatedDaysFromArrayToList(entity.getWeekdays().toUpperCase()));
            }

            if (entity.getMonthdays() != null) {
               resource.setRepeatedDateOfMonthList(ConvertUtil.convertRepeatedDatesFromArrayToList(entity.getMonthdays()));
            }

            resource.setIsAllDayPlay(entity.getDuration() == 86399);
            resource.setFrameId(frameEntity.getFrame_id());
            resource.setContentId(entity.getContent_id());
            resource.setContentName(entity.getContent_name());
            resource.setContentType(entity.getContent_type());
            resource.setFileSize(entity.getFile_size());
            resource.setThumbnailFileId(entity.getFile_id());
            resource.setThumbnailFileName(entity.getFile_name());
            resource.setPlayerMode(entity.getPlayer_mode());
            resource.setInputSource(DeviceUtils.convertInputSource(entity.getHw_input_source()));
            resource.setColor(this.getColor(i));
            resource.setCifsSlideTransitionTime(entity.getSlide_transition_time());
            String scheduleType = entity.getSchedule_type();
            String inEffectType;
            if (!scheduleType.equals("00")) {
               TTV2ScheduleEventHWInfo hwInfo = new TTV2ScheduleEventHWInfo();
               hwInfo.setHwInputSource(entity.getHw_input_source());
               hwInfo.setHwAtvDtv(entity.getHw_AtvDtv());
               hwInfo.setHwAirCable(entity.getHw_AirCable());
               hwInfo.setHwMajorChannelNo(entity.getHw_MajorCH());
               hwInfo.setHwMinorChannelNo(entity.getHw_MinorCH());
               hwInfo.setHwVolume(entity.getHw_Volume());
               hwInfo.setHwScheduleChannelNo(entity.getHw_sch_ch());
               resource.setHwInfo(hwInfo);
               if (entity.getHw_input_source() != null) {
                  inEffectType = DeviceUtils.convertInputSource(entity.getHw_input_source());
                  resource.setContentId(inEffectType);
                  resource.setContentName(inEffectType);
               }

               resource.setIsHW(true);
            }

            String deviceType = programEntity.getDevice_type();
            if (ScheduleUtility.isSupportInputSourceRepeat(deviceType, programEntity.getDevice_type_version()) && this.checkInputSource(entity.getContent_id())) {
               resource.setContentName(entity.getContent_id());
            }

            inEffectType = entity.getIn_effect_type();
            if (inEffectType != null && !inEffectType.isEmpty()) {
               TTV2ScheduleEventEffectInfo inEffect = new TTV2ScheduleEventEffectInfo();
               inEffect.setEffectType(inEffectType);
               inEffect.setDurationInSeconds(entity.getIn_effect_duration());
               inEffect.setDirection(entity.getIn_effect_direction());
               resource.setInEffect(inEffect);
            }

            String outEffectType = entity.getIn_effect_type();
            if (outEffectType != null && !outEffectType.isEmpty()) {
               TTV2ScheduleEventEffectInfo outEffect = new TTV2ScheduleEventEffectInfo();
               outEffect.setEffectType(outEffectType);
               outEffect.setDurationInSeconds(entity.getIn_effect_duration());
               outEffect.setDirection(entity.getIn_effect_direction());
               resource.setOutEffect(outEffect);
            }

            String contentType = entity.getContent_type();
            if (programType.equals("SYNC") && contentType.equals("PLAYLIST")) {
               PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
               Playlist playlist = pInfo.getPlaylistActiveVerInfo(resource.getContentId());
               if (playlist != null) {
                  String playlistType = playlist.getPlaylist_type();
                  if (playlistType.equals("3")) {
                     List syncGroupDeviceTagMaps = this.getDynamicTagInfo(entity.getSchedule_id());
                     resource.setSyncGroupDeviceTagMaps(syncGroupDeviceTagMaps);
                  }
               }
            }

            ContentInfo contentInfo = ContentInfoImpl.getInstance();
            String contentId = entity.getContent_id();
            Content content = contentInfo.getContentActiveVerInfo(contentId);
            String mediaType = contentInfo.getMediaTypeByContentId(contentId);
            if (content == null && mediaType != null && mediaType.equalsIgnoreCase("TLFD")) {
               content = contentInfo.getTLFDInfo(contentId);
            }

            if (content != null) {
               resource.setExpirationDate(content.getExpiration_date());
            }

            resources.add(resource);
         }
      }

      return resources;
   }

   private List loadAdSlotResources(String programId, String frameId) throws Exception {
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      List slotResources = new ArrayList();
      List slotEntities = scheduleInfo.getSlotListFromProgramIdWithFrameId(programId, frameId);
      if (slotEntities != null) {
         Iterator var6 = slotEntities.iterator();

         while(var6.hasNext()) {
            AdSlotEntity slotEntity = (AdSlotEntity)var6.next();
            TTV2AdSlotResource slotResource = new TTV2AdSlotResource();
            slotResource.setProgramId(slotEntity.getProgram_id());
            slotResource.setFrameId(slotEntity.getFrame_id());
            slotResource.setSlotId(slotEntity.getSlot_id());
            slotResource.setSlotIndex(slotEntity.getSlot_index());
            slotResource.setSlotName(slotEntity.getSlot_name());
            slotResource.setDurationInSeconds(slotEntity.getDuration());
            List eventResources = this.loadAdScheduleEventResources(programId, slotEntity.getSlot_id());
            slotResource.setEvents(eventResources);
            slotResources.add(slotResource);
         }
      }

      return slotResources;
   }

   private List loadAdScheduleEventResources(String programId, String slotId) throws Exception {
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
      List eventResources = new ArrayList();
      List scheduleEntities = scheduleInfo.getAdScheduleListFromProgramIdWithSlotId(programId, slotId);
      if (scheduleEntities != null) {
         Iterator var7 = scheduleEntities.iterator();

         while(true) {
            while(var7.hasNext()) {
               AdScheduleEntity scheduleEntity = (AdScheduleEntity)var7.next();
               TTV2AdScheduleEventResource eventResource = new TTV2AdScheduleEventResource();
               Playlist playlist = pInfo.getPlaylistActiveVerInfo(scheduleEntity.getContent_id());
               if (playlist == null) {
                  this.logger.error("[V2ContentScheduleHelper.loadAdScheduleEvents()] Can not found matched playlist.");
               } else {
                  eventResource.setContentId(playlist.getPlaylist_id());
                  eventResource.setContentName(playlist.getPlaylist_name());
                  eventResource.setContentType(playlist.getPlaylist_type());
                  List contentList = pInfo.getContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
                  if (contentList != null) {
                     List thumbnailResources = new ArrayList();
                     Iterator var13 = contentList.iterator();

                     while(var13.hasNext()) {
                        Content content = (Content)var13.next();
                        thumbnailResources.add(new TTV2AdScheduleThumbnailInfo(content.getThumb_file_id(), content.getThumb_file_name()));
                     }

                     eventResource.setThumbnails(thumbnailResources);
                  }

                  String stopDate = StrUtils.nvl(scheduleEntity.getStop_date(), "2999-12-31");
                  String stopTime = StrUtils.nvl(scheduleEntity.getStop_time(), "23:59:59");
                  eventResource.setProgramId(scheduleEntity.getProgram_id());
                  eventResource.setScheduleId(scheduleEntity.getSchedule_id());
                  eventResource.setSlotId(scheduleEntity.getSlot_id());
                  eventResource.setStartDate(scheduleEntity.getStart_date());
                  eventResource.setStopDate(stopDate);
                  eventResource.setStartTime(scheduleEntity.getStart_time());
                  eventResource.setStopTime(stopTime);
                  eventResource.setDurationInSeconds(scheduleEntity.getDuration());
                  eventResource.setIsInfinitePlay(stopDate.equals("2999-12-31") && stopTime.equals("23:59:59"));
                  eventResource.setUserId(scheduleEntity.getUser_id());
                  eventResource.setCreatedDate(scheduleEntity.getCreate_date());
                  eventResource.setModifiedDate(scheduleEntity.getModify_date());
                  eventResource.setFileSize(playlist.getTotal_size());
                  eventResources.add(eventResource);
               }
            }

            return eventResources;
         }
      } else {
         return eventResources;
      }
   }

   private List loadThumbnails(List contentList) {
      List thumbnails = new ArrayList();
      Iterator var3 = contentList.iterator();

      while(var3.hasNext()) {
         Content content = (Content)var3.next();
         AdThumbnailEntity thumbnail = new AdThumbnailEntity();
         thumbnail.setFileId(content.getThumb_file_id());
         thumbnail.setFileName(content.getThumb_file_name());
         thumbnails.add(thumbnail);
      }

      return thumbnails;
   }

   private List fitChannels(List channelResources) throws Exception {
      Locale locale = SecurityUtils.getLocale();
      MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      String programId = this.programEntity.getProgram_id();
      String programType = this.programEntity.getProgram_type();
      List channelEntities = new ArrayList();

      for(int i = 0; i < channelResources.size(); ++i) {
         TTV2ChannelResource channelResource = (TTV2ChannelResource)channelResources.get(i);
         int channelNo = channelResource.getChannelNo();
         String defaultChannelName = messageMgr.getMessageSource("COM_TV_SID_NEW_CHANNEL", locale);
         String channelName = channelResource.getChannelName() == null ? defaultChannelName : channelResource.getChannelName();
         ChannelEntity channelEntity;
         if (programType.equals("ADV")) {
            channelEntity = (ChannelEntity)this.programEntity.getChannelList().get(i);
            channelEntity.setChannel_no(channelNo);
            channelEntity.setChannel_name(channelName);
            this.fitFrame(channelEntity, channelResource.getFrame());
         } else {
            channelEntity = scheduleMgr.addChannel(programId, channelNo, channelName);
            channelEntity.setFrameList(this.fitFrame(channelEntity, channelResource.getFrame()));
            channelEntities.add(channelEntity);
         }

         channelResource.setChannelNo(channelNo);
         channelResource.setChannelName(channelName);
      }

      return channelEntities;
   }

   private FrameEntity getFrameEntityFromProgramEntity(TTV2FrameResource frameResource) {
      List channelEntities = this.programEntity.getChannelList();

      for(int i = 0; i < channelEntities.size(); ++i) {
         ChannelEntity channelEntity = (ChannelEntity)channelEntities.get(i);
         List frameEntities = channelEntity.getFrameList();

         for(int j = 0; j < frameEntities.size(); ++j) {
            FrameEntity frameEntity = (FrameEntity)frameEntities.get(j);
            if (frameEntity.getFrame_id().equals(frameResource.getFrameId())) {
               return frameEntity;
            }
         }
      }

      return null;
   }

   private List fitFrame(ChannelEntity channelEntity, TTV2FrameResource frameResource) throws Exception {
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      String programId = this.programEntity.getProgram_id();
      String programType = this.programEntity.getProgram_type();
      List frameEntities = new ArrayList();
      boolean isUserAuthorisedForFrame = frameResource.getHasPermissionForFrame();
      FrameEntity frameEntity;
      String frameType;
      String lineData;
      if (programType.equals("ADV")) {
         frameEntity = (FrameEntity)channelEntity.getFrameList().get(0);
         frameType = !StrUtils.nvl(frameResource.getFrameId()).isEmpty() ? frameResource.getFrameId() : frameEntity.getFrame_id();
         lineData = !StrUtils.nvl(frameResource.getFrameName()).isEmpty() ? frameResource.getFrameName() : frameEntity.getFrame_name();
         frameEntity.setFrame_id(frameType);
         frameEntity.setFrame_name(lineData);
         frameEntity.setIs_main_frame("Y");
         frameEntity.setSlotList(this.fitSlots(frameEntity, frameResource));
         frameResource.setFrameId(frameType);
         frameResource.setFrameName(lineData);
         frameResource.setIsMainFrame(true);
      } else if (isUserAuthorisedForFrame) {
         this.logger.info("Updating frame info as received from frontend for frame_id: " + frameResource.getFrameId());
         frameEntity = scheduleMgr.newFrame(programId, frameResource.getFrameId(), frameResource.getFrameName(), 0, channelEntity.getChannel_no(), 0, true, frameResource.getX(), frameResource.getY(), frameResource.getWidth(), frameResource.getHeight(), frameResource.getLineData());
         frameType = frameResource.getFrameType();
         if (frameType != null && !frameType.isEmpty() && frameType.equals("CUSTOM")) {
            TTV2DisplayResolution resolution = this.resource.getResolution();
            frameEntity.setWidth(resolution.getWidth());
            frameEntity.setHeight(resolution.getHeight());
         } else {
            frameEntity.setWidth(100.0D);
            frameEntity.setHeight(100.0D);
         }

         lineData = StrUtils.nvl(frameResource.getLineData());
         if (lineData.isEmpty() || !lineData.equals("CUSTOM") && !lineData.equals("CUSTOM_LAYOUT")) {
            if (lineData.equals("CUSTOM_FIXED")) {
               frameEntity.setLine_data("customFixed");
            } else if (lineData.equals("FIXED")) {
               frameEntity.setLine_data("fixed");
            } else if (lineData.equals("ZERO_FRAME_ONLY")) {
               frameEntity.setLine_data("ZeroFrameOnly");
            } else {
               frameEntity.setLine_data(lineData);
            }
         } else {
            frameEntity.setLine_data("CustomLayout");
         }

         if (frameResource.getDefaultContentId() != null && !frameResource.getDefaultContentId().equals("null")) {
            frameEntity.setDefault_content_id(frameResource.getDefaultContentId());
         }

         if (frameResource.getDefaultContentName() != null && !frameResource.getDefaultContentName().equals("null")) {
            frameEntity.setDefault_content_names(frameResource.getDefaultContentName());
         }

         HashMap authorityGroupInfo = this.getAuthorityGroupInfo(frameResource.getAuthorityGroups());
         frameEntity.setAuthority(authorityGroupInfo != null ? (String[])authorityGroupInfo.get("authorityGroupIds") : null);
         frameEntity.setAuthorityNames(authorityGroupInfo != null ? (String[])authorityGroupInfo.get("authorityGroupNames") : null);
         frameEntity.setScheduleList(this.fitEvents(frameResource.getEvents()));
         frameEntities.add(frameEntity);
         if (frameResource.getFrames() != null && !frameResource.getFrames().isEmpty()) {
            frameEntities.addAll(this.fitSubframes(channelEntity, frameResource.getFrames()));
         }

         frameResource.setFrameId(frameEntity.getFrame_id());
         frameResource.setFrameName(frameEntity.getFrame_name());
      } else {
         this.logger.info("User not authorized on this frame; Getting frame info from previous version of schedule for frame_id: " + frameResource.getFrameId());
         frameEntity = this.getFrameEntityFromProgramEntity(frameResource);
         if (frameEntity == null) {
            this.logger.info("Frame layout updated by user without permission. Unable to find previous version of frame for frame_id: " + frameResource.getFrameId());
            throw new RestServiceException(RestExceptionCode.FORBIDDEN_USER_ID_MU);
         }

         frameEntities.add(frameEntity);
         if (frameResource.getFrames() != null && !frameResource.getFrames().isEmpty()) {
            frameEntities.addAll(this.fitSubframes(channelEntity, frameResource.getFrames()));
         }

         frameResource.setFrameId(frameEntity.getFrame_id());
         frameResource.setFrameName(frameEntity.getFrame_name());
      }

      return frameEntities;
   }

   private FrameEntity getSubframeEntityFromProgramEntity(TTV2SubframeResource frameResource) {
      List channelEntities = this.programEntity.getChannelList();

      for(int i = 0; i < channelEntities.size(); ++i) {
         ChannelEntity channelEntity = (ChannelEntity)channelEntities.get(i);
         List frameEntities = channelEntity.getFrameList();

         for(int j = 0; j < frameEntities.size(); ++j) {
            FrameEntity frameEntity = (FrameEntity)frameEntities.get(j);
            if (frameEntity.getFrame_id().equals(frameResource.getFrameId())) {
               return frameEntity;
            }
         }
      }

      return null;
   }

   private List fitSubframes(ChannelEntity channelEntity, List frameResources) throws Exception {
      ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
      String programId = this.programEntity.getProgram_id();
      List frameEntities = new ArrayList();
      int mainFrameIndex = -1;

      for(int i = 0; i < frameResources.size(); ++i) {
         TTV2SubframeResource frameResource = (TTV2SubframeResource)frameResources.get(i);
         boolean isUserAuthorisedForFrame = frameResource.getHasPermissionForFrame();
         FrameEntity frameEntity;
         if (!isUserAuthorisedForFrame) {
            this.logger.info("User not authorized on this sub frame; Getting frame info from previous version of schedule for frame_id: " + frameResource.getFrameId());
            frameEntity = this.getSubframeEntityFromProgramEntity(frameResource);
            if (frameEntity == null) {
               this.logger.info("Frame layout updated by user without permission. Unable to find previous version of sub frame for frame_id: " + frameResource.getFrameId());
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_USER_ID_MU);
            }

            frameEntities.add(frameEntity);
            frameResource.setFrameId(frameEntity.getFrame_id());
            frameResource.setFrameName(frameEntity.getFrame_name());
            mainFrameIndex = frameResource.getIsMainFrame() ? i : mainFrameIndex;
         } else {
            this.logger.info("Updating sub frame info as received from frontend for frame_id: " + frameResource.getFrameId());
            frameEntity = scheduleMgr.newFrame(programId, frameResource.getFrameId(), frameResource.getFrameName(), i + 1, channelEntity.getChannel_no(), 0, false, frameResource.getX(), frameResource.getY(), frameResource.getWidth(), frameResource.getHeight(), frameResource.getLineData());
            String lineData = StrUtils.nvl(frameResource.getLineData());
            if (!lineData.isEmpty() && (lineData.equals("CUSTOM") || lineData.equals("CUSTOM_LAYOUT"))) {
               frameEntity.setLine_data("CustomLayout");
            } else if (lineData.equals("CUSTOM_FIXED")) {
               frameEntity.setLine_data("customFixed");
            } else if (lineData.equals("FIXED")) {
               frameEntity.setLine_data("fixed");
            } else if (lineData.equals("ZERO_FRAME_ONLY")) {
               frameEntity.setLine_data("ZeroFrameOnly");
            } else {
               frameEntity.setLine_data(lineData);
            }

            if (frameResource.getDefaultContentId() != null && !frameResource.getDefaultContentId().equals("null")) {
               frameEntity.setDefault_content_id(frameResource.getDefaultContentId());
            }

            if (frameResource.getDefaultContentName() != null && !frameResource.getDefaultContentName().equals("null")) {
               frameEntity.setDefault_content_names(frameResource.getDefaultContentName());
            }

            HashMap authorityGroupInfo = this.getAuthorityGroupInfo(frameResource.getAuthorityGroups());
            frameEntity.setAuthority(authorityGroupInfo != null ? (String[])authorityGroupInfo.get("authorityGroupIds") : null);
            frameEntity.setAuthorityNames(authorityGroupInfo != null ? (String[])authorityGroupInfo.get("authorityGroupNames") : null);
            frameEntity.setScheduleList(this.fitEvents(frameResource.getEvents()));
            frameEntities.add(frameEntity);
            frameResource.setFrameId(frameEntity.getFrame_id());
            frameResource.setFrameName(frameEntity.getFrame_name());
            mainFrameIndex = frameResource.getIsMainFrame() ? i : mainFrameIndex;
         }
      }

      if (!frameEntities.isEmpty() && mainFrameIndex >= 0) {
         ((FrameEntity)frameEntities.get(mainFrameIndex)).setIs_main_frame("Y");
         ((TTV2SubframeResource)frameResources.get(mainFrameIndex)).setIsMainFrame(true);
      }

      return frameEntities;
   }

   private HashMap getAuthorityGroupInfo(List authorityGroups) {
      if (authorityGroups != null && !authorityGroups.isEmpty()) {
         int index = 0;
         String[] authorityGroupIds = new String[authorityGroups.size()];
         String[] authorityGroupNames = new String[authorityGroups.size()];

         for(Iterator var5 = authorityGroups.iterator(); var5.hasNext(); ++index) {
            TTV2FrameAuthorityGroup authorityGroup = (TTV2FrameAuthorityGroup)var5.next();
            authorityGroupIds[index] = String.valueOf(authorityGroup.getGroupId());
            authorityGroupNames[index] = authorityGroup.getGroupName();
         }

         HashMap result = new HashMap();
         result.put("authorityGroupIds", authorityGroupIds);
         result.put("authorityGroupNames", authorityGroupNames);
         return result;
      } else {
         return null;
      }
   }

   private List fitEvents(List eventResources) {
      List eventEntities = null;
      if (eventResources != null) {
         ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
         eventEntities = new ArrayList();

         for(int i = 0; i < eventResources.size(); ++i) {
            TTV2ScheduleEventResource eventResource = (TTV2ScheduleEventResource)eventResources.get(i);
            ContentsScheduleEntity eventEntity = scheduleMgr.contentScheduleInit(this.programEntity.getProgram_id(), new ContentsScheduleEntity());
            String scheduleId = !StrUtils.nvl(eventResource.getScheduleId()).isEmpty() ? eventResource.getScheduleId() : eventEntity.getSchedule_id();
            Long eventPriority = eventResource.getPriority() != null ? eventResource.getPriority() : (long)i;
            eventEntity.setSchedule_id(scheduleId);
            eventResource.setScheduleId(scheduleId);
            eventEntity.setPriority(eventPriority);
            eventResource.setPriority(eventPriority);
            eventEntities.add(eventEntity);
         }
      }

      return eventEntities;
   }

   private List fitSlots(FrameEntity frameEntity, TTV2FrameResource frameResource) {
      List slotEntities = frameEntity.getSlotList();
      List slotResources = frameResource.getAdSlots();
      int oldSlotCount = ((List)slotEntities).size();
      int newSlotCount = slotResources.size();
      String id;
      if (oldSlotCount > newSlotCount) {
         List modifiedSlotEntities = new ArrayList();

         for(int i = 0; i < slotResources.size(); ++i) {
            String slotId = ((TTV2AdSlotResource)slotResources.get(i)).getSlotId();

            for(int j = 0; j < ((List)slotEntities).size(); ++j) {
               id = ((AdSlotEntity)((List)slotEntities).get(j)).getSlot_id();
               if (id.equalsIgnoreCase(slotId)) {
                  modifiedSlotEntities.add(((List)slotEntities).get(j));
                  break;
               }
            }
         }

         slotEntities = modifiedSlotEntities;
      }

      String slotId;
      int i;
      String programId;
      if (oldSlotCount < newSlotCount) {
         for(i = oldSlotCount; i < newSlotCount; ++i) {
            MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
            Locale locale = SecurityUtils.getLocale();
            programId = UUID.randomUUID().toString();
            id = messageMgr.getMessageSource("MIS_SID_20_MIX_SLOT", locale);
            slotId = String.valueOf(i + 1);
            id = id.replaceAll("<<A>>", slotId);
            TTV2AdSlotResource slotResource = (TTV2AdSlotResource)slotResources.get(i);
            String frameId = !StrUtils.nvl(slotResource.getFrameId()).isEmpty() ? frameResource.getFrameId() : frameEntity.getFrame_id();
            String slotId = !StrUtils.nvl(slotResource.getSlotId()).isEmpty() ? slotResource.getSlotId() : programId;
            String slotName = !StrUtils.nvl(slotResource.getSlotName()).isEmpty() ? slotResource.getSlotName() : id;
            AdSlotEntity slotEntity = this.newAdSlotEntity(frameId, slotId, slotName);
            ((List)slotEntities).add(slotEntity);
         }
      }

      for(i = 0; i < slotResources.size(); ++i) {
         AdSlotEntity slotEntity = (AdSlotEntity)((List)slotEntities).get(i);
         TTV2AdSlotResource slotResource = (TTV2AdSlotResource)slotResources.get(i);
         programId = this.programEntity.getProgram_id();
         id = !StrUtils.nvl(slotResource.getFrameId()).isEmpty() ? frameResource.getFrameId() : frameEntity.getFrame_id();
         slotId = !StrUtils.nvl(slotResource.getSlotId()).isEmpty() ? slotResource.getSlotId() : slotEntity.getSlot_id();
         String slotName = !StrUtils.nvl(slotResource.getSlotName()).isEmpty() ? slotResource.getSlotName() : slotEntity.getSlot_name();
         int slotIndex = slotResource.getSlotIndex();
         slotEntity = this.newAdSlotEntity(id, slotId, slotName);
         slotResource.setProgramId(programId);
         slotResource.setFrameId(id);
         slotResource.setSlotId(slotId);
         slotResource.setSlotName(slotName);
         slotEntity.setSlot_index(slotIndex);
         slotEntity.setScheduleList(this.fitEvents(slotId, slotResource.getEvents()));
         ((List)slotEntities).set(i, slotEntity);
      }

      return (List)slotEntities;
   }

   private AdSlotEntity newAdSlotEntity(String frameId, String slotId, String slotName) {
      String programId = this.programEntity.getProgram_id();
      AdSlotEntity slotEntity = new AdSlotEntity();
      slotEntity.setProgram_id(programId);
      slotEntity.setFrame_id(frameId);
      slotEntity.setSlot_id(slotId);
      slotEntity.setSlot_name(slotName);
      slotEntity.setDuration((int)this.programEntity.getAd_duration());
      return slotEntity;
   }

   private List fitEvents(String slotId, List eventResources) {
      List eventEntities = new ArrayList();
      if (eventResources != null) {
         Iterator var4 = eventResources.iterator();

         while(var4.hasNext()) {
            TTV2AdScheduleEventResource eventResource = (TTV2AdScheduleEventResource)var4.next();
            String programId = this.programEntity.getProgram_id();
            String scheduleId = !StrUtils.nvl(eventResource.getScheduleId()).isEmpty() ? eventResource.getScheduleId() : UUID.randomUUID().toString();
            AdScheduleEntity eventEntity = this.newAdScheduleEntity(programId, slotId, scheduleId);
            eventResource.setProgramId(programId);
            eventResource.setSlotId(slotId);
            eventResource.setScheduleId(scheduleId);
            eventEntities.add(eventEntity);
         }
      }

      return eventEntities;
   }

   private AdScheduleEntity newAdScheduleEntity(String programId, String slotId, String scheduleId) {
      AdScheduleEntity eventEntity = new AdScheduleEntity();
      eventEntity.setProgram_id(programId);
      eventEntity.setSlot_id(slotId);
      eventEntity.setSchedule_id(scheduleId);
      return eventEntity;
   }

   private ChannelEntity findChannelEntityByChannelNo(int channelNo, ProgramEntity programEntity) {
      List entities = programEntity.getChannelList();
      Iterator var4 = entities.iterator();

      ChannelEntity entity;
      do {
         if (!var4.hasNext()) {
            return null;
         }

         entity = (ChannelEntity)var4.next();
      } while(entity.getChannel_no() != channelNo);

      return entity;
   }

   private FrameEntity findFrameEntityByFrameId(String frameId, ChannelEntity channelEntity) {
      List entities = channelEntity.getFrameList();
      Iterator var4 = entities.iterator();

      FrameEntity entity;
      do {
         if (!var4.hasNext()) {
            return null;
         }

         entity = (FrameEntity)var4.next();
      } while(!entity.getFrame_id().equals(frameId));

      return entity;
   }

   private ContentsScheduleEntity findContentsScheduleEntityByScheduleId(String scheduleId, FrameEntity frameEntity) {
      List entities = frameEntity.getScheduleList();
      Iterator var4 = entities.iterator();

      ContentsScheduleEntity entity;
      do {
         if (!var4.hasNext()) {
            return null;
         }

         entity = (ContentsScheduleEntity)var4.next();
      } while(!entity.getSchedule_id().equals(scheduleId));

      return entity;
   }

   private AdSlotEntity findAdSlotEntityBySlotId(String slotId, FrameEntity frameEntity) {
      List entities = frameEntity.getSlotList();
      Iterator var4 = entities.iterator();

      AdSlotEntity entity;
      do {
         if (!var4.hasNext()) {
            return null;
         }

         entity = (AdSlotEntity)var4.next();
      } while(!entity.getSlot_id().equals(slotId));

      return entity;
   }

   private AdScheduleEntity findAdScheduleEntityByScheduleId(String scheduleId, AdSlotEntity adSlotEntity) {
      List entities = adSlotEntity.getScheduleList();
      Iterator var4 = entities.iterator();

      AdScheduleEntity entity;
      do {
         if (!var4.hasNext()) {
            return null;
         }

         entity = (AdScheduleEntity)var4.next();
      } while(!entity.getSchedule_id().equals(scheduleId));

      return entity;
   }

   private ContentsScheduleEntity fillContentsScheduleEntity(ContentsScheduleEntity content, String minSupportDeviceTypeOfProgram, Float minSupportDeviceTypeVersionOfProgram, String programType, int channelNo, TTV2ScheduleEventResource resource) throws SQLException {
      content.setContent_name(resource.getContentName());
      content.setStart_date(resource.getStartDate());
      content.setStop_date(resource.getEndDate());
      content.setStart_time(resource.getStartTime());
      content.setDuration(resource.getDurationInSeconds());
      content.setContent_id(resource.getContentId());
      content.setPlayer_mode(resource.getPlayerMode());
      String contentType = resource.getContentType();
      long priority = resource.getPriority();
      if (minSupportDeviceTypeOfProgram == null) {
         minSupportDeviceTypeOfProgram = "";
      }

      if (contentType.equals("HW_IS")) {
         if (ScheduleUtility.isSupportInputSourceRepeat(minSupportDeviceTypeOfProgram, minSupportDeviceTypeVersionOfProgram)) {
            content.setContent_type("HW_IS");
            content.setSchedule_type("00");
            content.setHw_input_source(DeviceUtils.convertInputSource(resource.getInputSource()));
            content.setIn_effect_direction("");
            content.setIn_effect_duration(0);
            content.setIn_effect_type("");
            content.setOut_effect_direction("");
            content.setOut_effect_duration(0);
            content.setOut_effect_type("");
         } else if (minSupportDeviceTypeOfProgram.equals("SPLAYER") || minSupportDeviceTypeOfProgram.equals("LPLAYER") || minSupportDeviceTypeOfProgram.equals("WPLAYER")) {
            content.setContent_id("");
            content.setSchedule_type(!DeviceUtils.convertInputSource(resource.getInputSource()).equals("1000") && !DeviceUtils.convertInputSource(resource.getInputSource()).equals("PanelOff") && !DeviceUtils.convertInputSource(resource.getInputSource()).equals("PANEL_OFF") ? "03" : "01");
            content.setHw_input_source(DeviceUtils.convertInputSource(resource.getInputSource()));
            content.setContent_type("HW_IS");
            content.setPriority(0L);
            content.setHw_AtvDtv("-1");
            content.setHw_AirCable("-1");
            content.setHw_MajorCH("0");
            content.setHw_MinorCH("0");
            content.setHw_Volume("-1");
            content.setHw_sch_ch("-1");
         }
      } else {
         if (contentType.equals("PLAYLIST")) {
            PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
            Playlist playlist = pInfo.getPlaylistActiveVerInfo(resource.getContentId());
            if (playlist != null) {
               String playlistType = playlist.getPlaylist_type();
               if (playlistType.equals("5")) {
                  content.setPlaylist_type("5");
                  List ContentList = pInfo.getTagContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
                  if (ContentList != null && ContentList.size() > 0) {
                     content.setFile_id(((Content)ContentList.get(0)).getThumb_file_id());
                     content.setFile_name(((Content)ContentList.get(0)).getThumb_file_name());
                  }
               } else {
                  PlaylistDao playlistDao = new PlaylistDao();
                  ContentFile thumbContent = playlistDao.getThumbFileInfo(resource.getContentId());
                  content.setFile_size(playlistDao.getPlaylistActiveVerInfo(resource.getContentId()).getTotal_size());
                  if (thumbContent != null && thumbContent.getFile_id() != null && thumbContent.getFile_name() != null) {
                     content.setFile_id(thumbContent.getFile_id());
                     content.setFile_name(thumbContent.getFile_name());
                  } else {
                     Playlist playlistInfo = playlistDao.getPlaylistActiveVerInfo(resource.getContentId());
                     if (playlistInfo != null) {
                        ContentInfo contentDao = ContentInfoImpl.getInstance();
                        Content contentInfo = contentDao.getThumbInfoOfActiveVersion(playlistInfo.getContent_id());
                        if (contentInfo != null) {
                           content.setFile_id(contentInfo.getThumb_file_id());
                           content.setFile_name(contentInfo.getThumb_file_name());
                        } else {
                           List tmpList = pInfo.getContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
                           if (tmpList != null && tmpList.size() > 0) {
                              Content playlistContent = (Content)tmpList.get(0);
                              if (playlistContent != null) {
                                 content.setFile_id(playlistContent.getThumb_file_id());
                                 content.setFile_name(playlistContent.getThumb_file_name());
                              }
                           }
                        }
                     }
                  }
               }

               if (programType.equals("SYNC") && playlistType.equals("3")) {
                  this.setDynamicTagInfo(playlist, resource);
               }
            }
         } else if (contentType.equalsIgnoreCase("RULESET")) {
            RuleSetInfo rulesetDao = RuleSetInfoImpl.getInstance();
            rulesetDao.getRuleset(resource.getContentId());
            content.setFile_id("RULESET_THUMBNAIL");
            content.setFile_name("RULESET_THUMBNAIL.PNG");
         } else if (contentType.equalsIgnoreCase("SAPP")) {
            content.setFile_id("SAPP_THUMBNAIL");
            content.setFile_name("SAPP_THUMBNAIL.PNG");
         } else if (!contentType.equals("CIFS") && !contentType.equals("FTP")) {
            ContentInfo contentInfo = ContentInfoImpl.getInstance();
            Map thumbContent = contentInfo.getThumbFileInfoOfActiveVersion(resource.getContentId());
            if (thumbContent != null) {
               contentType = StrUtils.nvl(contentInfo.getMediaTypeByContentId(resource.getContentId()));
               content.setFile_size(contentInfo.getContentActiveVerInfo(resource.getContentId()).getTotal_size());
               content.setFile_id((String)thumbContent.get("file_id"));
               content.setFile_name((String)thumbContent.get("file_name"));
            }
         } else {
            content.setSlide_transition_time(resource.getCifsSlideTransitionTime());
         }

         if (!contentType.equals("CIFS") && !contentType.equals("FTP")) {
            content.setSlide_transition_time(0);
         }

         content.setSchedule_type("00");
         content.setContent_type(contentType);
         content.setIn_effect_direction("");
         content.setIn_effect_duration(0);
         content.setIn_effect_type("");
         content.setOut_effect_direction("");
         content.setOut_effect_duration(0);
         content.setOut_effect_type("");
      }

      if (programType.equalsIgnoreCase("SYNC")) {
         content.setIsSync(true);
      }

      if (resource.getRepeatType().equalsIgnoreCase("daily".toUpperCase())) {
         content.setRepeat_type("daily");
      } else if (resource.getRepeatType().equalsIgnoreCase("weekly".toUpperCase())) {
         content.setRepeat_type("day_of_week");
      } else if (resource.getRepeatType().equalsIgnoreCase("monthly".toUpperCase())) {
         content.setRepeat_type("day_of_month");
      } else {
         content.setRepeat_type("once");
      }

      if (resource.getRepeatedDayOfWeekList() != null) {
         content.setWeekdays(ConvertUtil.convertListToStringWithSeparator(resource.getRepeatedDayOfWeekList(), ",").toLowerCase());
      }

      if (resource.getRepeatedDateOfMonthList() != null) {
         content.setMonthdays(ConvertUtil.convertListToStringWithSeparator(resource.getRepeatedDateOfMonthList(), ","));
      }

      content.setPriority(priority);
      content.setSafetyLock(resource.getIsSafetyLockSet() ? "true" : "false");
      content.setChannel_no(channelNo);
      Date date = new Date();
      Timestamp timestamp = new Timestamp(date.getTime());
      content.setModify_date(timestamp);
      content.setCreate_date(timestamp);
      content.setUser_id(SecurityUtils.getLoginUserId());
      return content;
   }

   private void setDynamicTagInfo(Playlist playlist, TTV2ScheduleEventResource resource) throws SQLException {
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      scheduleInfo.deleteDynaminTagInfo(resource.getScheduleId());
      List syncGroupDeviceTagMaps = resource.getSyncGroupDeviceTagMaps();
      if (syncGroupDeviceTagMaps != null && !syncGroupDeviceTagMaps.isEmpty()) {
         List dynamicTagEntities = new ArrayList();
         Iterator var6 = syncGroupDeviceTagMaps.iterator();

         while(var6.hasNext()) {
            TTV2SyncGroupDeviceTagMap map = (TTV2SyncGroupDeviceTagMap)var6.next();
            long syncGroupId = map.getSyncGroupId();
            List deviceTagIds = map.getDeviceTagIds();

            for(int i = 0; i < deviceTagIds.size(); ++i) {
               DynamicTagEntity dynamicTagEntity = new DynamicTagEntity();
               dynamicTagEntity.setPlaylist_id(playlist.getPlaylist_id());
               dynamicTagEntity.setSchedule_id(resource.getScheduleId());
               dynamicTagEntity.setSync_play_id(String.valueOf(syncGroupId));
               dynamicTagEntity.setTag_id((Integer)deviceTagIds.get(i));
               dynamicTagEntity.setMatch_type("OR");
               dynamicTagEntities.add(dynamicTagEntity);
            }
         }

         scheduleInfo.addDynaminTagInfo(dynamicTagEntities);
      }

   }

   private List getDynamicTagInfo(String scheduleId) throws SQLException {
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      List dynamicTagEntities = scheduleInfo.getDynaminTagInfo(scheduleId);
      if (dynamicTagEntities == null) {
         this.logger.error("Can not find matched DynamicTagEntity.[scheduleId : " + scheduleId + "]");
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"DynamicTagEntity information"});
      } else {
         List syncGroupDeviceTagMaps = new ArrayList();

         for(int i = 0; i < dynamicTagEntities.size(); ++i) {
            DynamicTagEntity entity = (DynamicTagEntity)dynamicTagEntities.get(i);
            long syncGroupId = Long.parseLong(entity.getSync_play_id());
            int deviceTagId = entity.getTag_id();
            TTV2SyncGroupDeviceTagMap syncGroupDeviceTagMap = this.findSyncGroupDeviceTagMapById(syncGroupId, syncGroupDeviceTagMaps);
            if (syncGroupDeviceTagMap == null) {
               syncGroupDeviceTagMap = new TTV2SyncGroupDeviceTagMap();
               List deviceTagIds = new ArrayList();
               deviceTagIds.add(deviceTagId);
               syncGroupDeviceTagMap.setSyncGroupId(syncGroupId);
               syncGroupDeviceTagMap.setDeviceTagIds(deviceTagIds);
               syncGroupDeviceTagMaps.add(syncGroupDeviceTagMap);
            } else {
               syncGroupDeviceTagMap.getDeviceTagIds().add(deviceTagId);
            }
         }

         return syncGroupDeviceTagMaps;
      }
   }

   private TTV2SyncGroupDeviceTagMap findSyncGroupDeviceTagMapById(long syncGroupId, List syncGroupDeviceTagMaps) {
      Iterator var4 = syncGroupDeviceTagMaps.iterator();

      TTV2SyncGroupDeviceTagMap map;
      do {
         if (!var4.hasNext()) {
            return null;
         }

         map = (TTV2SyncGroupDeviceTagMap)var4.next();
      } while(syncGroupId != map.getSyncGroupId());

      return map;
   }

   private Map getDeployReservationMap(V2ContentScheduleDeployReservation reservation) {
      Map deployReserveMap = new HashMap();
      if (this.resource.getIsDeployReserved()) {
         deployReserveMap.put("deployReserveTime", reservation.getDeployTime());
         if (reservation.getRepeatType().equalsIgnoreCase("DAILY")) {
            deployReserveMap.put("deployReserveRepeatType", "daily");
         } else if (reservation.getRepeatType().equalsIgnoreCase("WEEKLY")) {
            deployReserveMap.put("deployReserveRepeatType", "weekly");
         } else if (reservation.getRepeatType().equalsIgnoreCase("MONTHLY")) {
            deployReserveMap.put("deployReserveRepeatType", "monthly");
         } else {
            deployReserveMap.put("deployReserveRepeatType", "daily");
         }

         deployReserveMap.put("deployReserveStartDate", reservation.getStartDate());
         deployReserveMap.put("deployReserveEndDate", reservation.getEndDate());
         deployReserveMap.put("weekly", ConvertUtil.convertListToStringWithSeparator(reservation.getRepeatedDayOfWeekList(), ","));
         deployReserveMap.put("monthly", ConvertUtil.convertListToStringWithSeparator(reservation.getRepeatedDateOfMonthList(), ","));
      }

      return deployReserveMap;
   }

   private void sendNotificationData(long programGroupId, String programName, String eventType) {
      try {
         List notiDataList = new ArrayList();
         ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
         String orgName = programGroupInfo.getOrgNameByGroupId(programGroupId);
         long orgId = (long)programGroupInfo.getProgramOrgGroupId((int)programGroupId);
         NotificationData notiData = new NotificationData();
         notiData.setName(programName);
         notiData.setOrgId(orgId);
         notiData.setOrgName(orgName);
         notiData.setUserName(SecurityUtils.getLoginUserId());
         notiDataList.add(notiData);
         MailUtil.sendContentScheduleEventMail(notiDataList, eventType);
      } catch (Exception var11) {
         this.logger.error(var11);
      }

   }

   private Long getDiskSpaceRepository(String groupIds) throws SQLException {
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      return deviceGroupDao.getDiskSpaceRepository(groupIds);
   }

   private String getColor(int index) {
      String[] colors = new String[]{"#80cbff", "#6ee6a9", "#ff92b1", "#b22222", "#ff8c00", "#7B68EE"};
      return colors[index % colors.length];
   }

   private boolean checkInputSource(String inputsource) {
      byte var3 = -1;
      switch(inputsource.hashCode()) {
      case -1732246028:
         if (inputsource.equals("DISPLAY_PORT2")) {
            var3 = 18;
         }
         break;
      case -1523257200:
         if (inputsource.equals("URL_LAUNCHER")) {
            var3 = 28;
         }
         break;
      case -872551335:
         if (inputsource.equals("HDMI1_PC")) {
            var3 = 13;
         }
         break;
      case -872521544:
         if (inputsource.equals("HDMI2_PC")) {
            var3 = 14;
         }
         break;
      case -872491753:
         if (inputsource.equals("HDMI3_PC")) {
            var3 = 15;
         }
         break;
      case -872461962:
         if (inputsource.equals("HDMI4_PC")) {
            var3 = 16;
         }
         break;
      case -332973570:
         if (inputsource.equals("DISPLAY_PORT")) {
            var3 = 17;
         }
         break;
      case -198252672:
         if (inputsource.equals("MAGICINFO-LITE")) {
            var3 = 7;
         }
         break;
      case 2101:
         if (inputsource.equals("AV")) {
            var3 = 3;
         }
         break;
      case 2547:
         if (inputsource.equals("PC")) {
            var3 = 0;
         }
         break;
      case 65155:
         if (inputsource.equals("ATV")) {
            var3 = 25;
         }
         break;
      case 65181:
         if (inputsource.equals("AV2")) {
            var3 = 21;
         }
         break;
      case 65911:
         if (inputsource.equals("BNC")) {
            var3 = 1;
         }
         break;
      case 68038:
         if (inputsource.equals("DTV")) {
            var3 = 19;
         }
         break;
      case 68087:
         if (inputsource.equals("DVI")) {
            var3 = 2;
         }
         break;
      case 69121:
         if (inputsource.equals("EXT")) {
            var3 = 22;
         }
         break;
      case 2664213:
         if (inputsource.equals("WIFI")) {
            var3 = 24;
         }
         break;
      case 68595609:
         if (inputsource.equals("HDMI1")) {
            var3 = 9;
         }
         break;
      case 68595610:
         if (inputsource.equals("HDMI2")) {
            var3 = 10;
         }
         break;
      case 68595611:
         if (inputsource.equals("HDMI3")) {
            var3 = 11;
         }
         break;
      case 68595612:
         if (inputsource.equals("HDMI4")) {
            var3 = 12;
         }
         break;
      case 74707987:
         if (inputsource.equals("DVI_VIDEO")) {
            var3 = 20;
         }
         break;
      case 327400016:
         if (inputsource.equals("SAMSUNG_WORKSPACE")) {
            var3 = 27;
         }
         break;
      case 426427640:
         if (inputsource.equals("PLUGIN_MODULE")) {
            var3 = 8;
         }
         break;
      case 620789691:
         if (inputsource.equals("MAGICINFO")) {
            var3 = 6;
         }
         break;
      case 1305403508:
         if (inputsource.equals("PANEL_OFF")) {
            var3 = 29;
         }
         break;
      case 1386687709:
         if (inputsource.equals("COMPONENT")) {
            var3 = 5;
         }
         break;
      case 1624881929:
         if (inputsource.equals("HD_BASE_T")) {
            var3 = 23;
         }
         break;
      case 1803761917:
         if (inputsource.equals("WEB_BROWSER")) {
            var3 = 26;
         }
         break;
      case 2018338401:
         if (inputsource.equals("S-VIDEO")) {
            var3 = 4;
         }
      }

      switch(var3) {
      case 0:
      case 1:
      case 2:
      case 3:
      case 4:
      case 5:
      case 6:
      case 7:
      case 8:
      case 9:
      case 10:
      case 11:
      case 12:
      case 13:
      case 14:
      case 15:
      case 16:
      case 17:
      case 18:
      case 19:
      case 20:
      case 21:
      case 22:
      case 23:
      case 24:
      case 25:
      case 26:
      case 27:
      case 28:
      case 29:
         return true;
      default:
         return false;
      }
   }

   public static String getDeviceGroupIdListAsString(List infos) {
      List deviceGroupIds = new ArrayList();
      if (infos != null) {
         infos.forEach((info) -> {
            deviceGroupIds.add(info.getGroupId());
         });
      }

      return ConvertUtil.convertListToStringWithSeparator(deviceGroupIds, ",");
   }
}
