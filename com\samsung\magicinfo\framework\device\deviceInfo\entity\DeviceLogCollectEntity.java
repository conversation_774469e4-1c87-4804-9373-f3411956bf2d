package com.samsung.magicinfo.framework.device.deviceInfo.entity;

import java.io.Serializable;
import java.sql.Timestamp;

public class DeviceLogCollectEntity implements Serializable {
   private static final long serialVersionUID = 9187720401223841084L;
   private String device_id;
   private String type;
   private String category_script;
   private String status;
   private Timestamp start_time;
   private int duration;
   private int packet_size;
   private String token;
   private String encryption_key;
   private String file_name;
   private int smart_diagnostic_version;

   public DeviceLogCollectEntity() {
      super();
   }

   public String getDevice_id() {
      return this.device_id;
   }

   public void setDevice_id(String device_id) {
      this.device_id = device_id;
   }

   public String getType() {
      return this.type;
   }

   public void setType(String type) {
      this.type = type;
   }

   public String getCategory_script() {
      return this.category_script;
   }

   public void setCategory_script(String category_script) {
      this.category_script = category_script;
   }

   public String getStatus() {
      return this.status;
   }

   public void setStatus(String status) {
      this.status = status;
   }

   public Timestamp getStart_time() {
      return this.start_time;
   }

   public void setStart_time(Timestamp start_time) {
      this.start_time = start_time;
   }

   public int getDuration() {
      return this.duration;
   }

   public void setDuration(int duration) {
      this.duration = duration;
   }

   public int getPacket_size() {
      return this.packet_size;
   }

   public void setPacket_size(int packet_size) {
      this.packet_size = packet_size;
   }

   public String getToken() {
      return this.token;
   }

   public void setToken(String token) {
      this.token = token;
   }

   public String getEncryption_key() {
      return this.encryption_key;
   }

   public void setEncryption_key(String encryption_key) {
      this.encryption_key = encryption_key;
   }

   public String getFile_name() {
      return this.file_name;
   }

   public void setFile_name(String file_name) {
      this.file_name = file_name;
   }

   public int getSmartDiagnosticVersion() {
      return this.smart_diagnostic_version;
   }

   public void setSmartDiagnosticVersion(int smart_diagnostic_version) {
      this.smart_diagnostic_version = smart_diagnostic_version;
   }
}
