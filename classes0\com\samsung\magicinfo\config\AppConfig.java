package com.samsung.magicinfo.config;

import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.Tag;
import com.samsung.magicinfo.webauthor2.repository.model.ContentData;
import com.samsung.magicinfo.webauthor2.repository.model.ResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.ResultListData;
import com.samsung.magicinfo.webauthor2.repository.model.criteria.ContentSearchCriteria;
import com.samsung.magicinfo.webauthor2.repository.model.tag.TagResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.tag.TagResultListData;
import com.samsung.magicinfo.webauthor2.xml.csd.CSDFileType;
import com.samsung.magicinfo.webauthor2.xml.csd.CSDTransferFileType;
import com.samsung.magicinfo.webauthor2.xml.transferfile.TransferFileType;
import com.samsung.magicinfo.webauthor2.xml.transferfile.response.TransferFilesResponseType;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.web.client.RestTemplate;
import org.springframework.xml.xpath.Jaxp13XPathTemplate;
import org.springframework.xml.xpath.XPathOperations;

@Configuration
public class AppConfig {
  @Bean
  public RestTemplate restTemplate() throws Exception {
    return new RestTemplate((ClientHttpRequestFactory)clientHttpRequestFactory());
  }
  
  @Bean
  public XPathOperations xpathTemplate() {
    return (XPathOperations)new Jaxp13XPathTemplate();
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2Marshaller() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    jaxb2Marshaller.setClassesToBeBound(new Class[] { ContentSearchCriteria.class, ResponseData.class, ResultListData.class, ContentData.class, Content.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2MarshallerTag() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    jaxb2Marshaller.setClassesToBeBound(new Class[] { TagResultListData.class, TagResponseData.class, Tag.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2MarshallerForTransferFile() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    props.put("jaxb.fragment", Boolean.TRUE);
    jaxb2Marshaller.setClassesToBeBound(new Class[] { TransferFileType.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2MarshallerForResponseTransferFile() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    props.put("jaxb.fragment", Boolean.TRUE);
    jaxb2Marshaller.setClassesToBeBound(new Class[] { TransferFilesResponseType.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2MarshallerForCSD() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    props.put("jaxb.fragment", Boolean.FALSE);
    props.put("jaxb.encoding", "UTF-8");
    jaxb2Marshaller.setClassesToBeBound(new Class[] { CSDTransferFileType.class, CSDFileType.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2MarshallerFragment() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    props.put("jaxb.fragment", Boolean.TRUE);
    jaxb2Marshaller.setClassesToBeBound(new Class[] { ContentSearchCriteria.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public HttpComponentsClientHttpRequestFactory clientHttpRequestFactory() throws IOException, NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
    TrustManager[] trustAllCerts = weakTrustManager();
    SSLContext sc = SSLContext.getInstance("SSL");
    sc.init(null, trustAllCerts, new SecureRandom());
    SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sc, (HostnameVerifier)new NoopHostnameVerifier());
    CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory((LayeredConnectionSocketFactory)csf).setSSLHostnameVerifier((HostnameVerifier)new NoopHostnameVerifier()).build();
    HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
    requestFactory.setHttpClient((HttpClient)httpClient);
    requestFactory.setConnectionRequestTimeout(10000);
    requestFactory.setReadTimeout(10000);
    requestFactory.setConnectTimeout(10000);
    return requestFactory;
  }
  
  @Bean
  public TrustManager[] weakTrustManager() {
    return new TrustManager[] { (TrustManager)new Object(this) };
  }
  
  @Bean
  public static PropertySourcesPlaceholderConfigurer propertySourcesPlaceholderConfigurer() {
    return new PropertySourcesPlaceholderConfigurer();
  }
}
