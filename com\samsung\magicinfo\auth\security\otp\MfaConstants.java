package com.samsung.magicinfo.auth.security.otp;

public class MfaConstants {
   public static final String PASSWORD = "PASSWORD";
   public static final String STORED_DEVICE = "STORED_DEVICE";
   public static final String OTP_APP = "OTP_APP";
   public static final String OTP_APP_WARNING = "OTP_APP_WARNING";
   public static final String FAIL_COUNT = "failCount";
   public static final String RETRY_COUNT = "retryCount";
   public static final String TIMESTAMP = "timestamp";
   public static final String TIMEZONE = "timeZone";
   public static final String MAGICINFO = "MagicINFO";
   public static final String LOGIN_COUNT = "login_count";
   public static final String OTP_AUTH_INFO = "otpAuthInfo";
   public static final int OTP_SIZE = 6;

   public MfaConstants() {
      super();
   }
}
