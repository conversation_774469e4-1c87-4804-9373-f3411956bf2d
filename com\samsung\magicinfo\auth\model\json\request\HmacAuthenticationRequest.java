package com.samsung.magicinfo.auth.model.json.request;

import com.samsung.magicinfo.auth.model.base.ModelBase;

public class HmacAuthenticationRequest extends ModelBase {
   private static final long serialVersionUID = 6624726180748515507L;
   private String userid;
   private String time;
   private String hash;

   public HmacAuthenticationRequest() {
      super();
   }

   public HmacAuthenticationRequest(String userid, String time, String hash) {
      super();
      this.setUserid(userid);
      this.setTime(time);
      this.setHash(hash);
   }

   public String getUserid() {
      return this.userid;
   }

   public void setUserid(String userid) {
      this.userid = userid;
   }

   public String getTime() {
      return this.time;
   }

   public void setTime(String time) {
      this.time = time;
   }

   public String getHash() {
      return this.hash;
   }

   public void setHash(String hash) {
      this.hash = hash;
   }
}
