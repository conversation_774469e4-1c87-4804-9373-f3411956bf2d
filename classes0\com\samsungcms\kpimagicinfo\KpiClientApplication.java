package com.samsungcms.kpimagicinfo;

import com.samsungcms.kpimagicinfo.service.Scheduler;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.ApplicationPidFileWriter;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

@EnableScheduling
@SpringBootApplication
public class KpiClientApplication extends SpringBootServletInitializer {
  private static final Logger LOGGER = LogManager.getLogger(com.samsungcms.kpimagicinfo.KpiClientApplication.class);
  
  @Autowired
  private Scheduler scheduler;
  
  @Value("${debug.fileGeneration}")
  private boolean debugFileGeneration;
  
  @Value("${version}")
  private String version;
  
  public static void main(String[] args) {
    try {
      SpringApplication application = new SpringApplication(new Class[] { com.samsungcms.kpimagicinfo.KpiClientApplication.class });
      application.addListeners(new ApplicationListener[] { (ApplicationListener)new ApplicationPidFileWriter() });
      application.run(args);
    } catch (Exception e) {
      LOGGER.error(e);
    } 
  }
  
  protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
    return builder.sources(new Class[] { com.samsungcms.kpimagicinfo.KpiClientApplication.class });
  }
  
  @Bean
  TaskScheduler threadPoolTaskScheduler() {
    return (TaskScheduler)new ThreadPoolTaskScheduler();
  }
  
  @EventListener({ApplicationReadyEvent.class})
  public void doSomethingAfterStartup() {
    try {
      LOGGER.info("version = " + this.version);
      this.scheduler.loadCron();
    } catch (Exception e) {
      LOGGER.error(e);
    } 
  }
}
