package com.samsung.magicinfo.webauthor2.exception.util;

import com.samsung.magicinfo.webauthor2.exception.WebAuthorAbstractException;

public class InvalidUserInputException extends WebAuthorAbstractException {
  public static final String IllegalArgumentMessage = "Player type not found";
  
  public InvalidUserInputException(String message) {
    super(message);
  }
  
  public InvalidUserInputException(int errCode, String message) {
    super(errCode, message);
  }
}
