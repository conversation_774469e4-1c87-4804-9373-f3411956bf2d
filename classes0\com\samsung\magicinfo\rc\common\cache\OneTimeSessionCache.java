package com.samsung.magicinfo.rc.common.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.samsung.magicinfo.rc.model.api.UserSession;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class OneTimeSessionCache {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.common.cache.OneTimeSessionCache.class);
  
  static LoadingCache<String, UserSession> cache = CacheBuilder.newBuilder()
    .maximumSize(1000L)
    .expireAfterWrite(1L, TimeUnit.MINUTES)
    .build((CacheLoader)new Object());
  
  public void put(String id, UserSession userSession) {
    cache.put(id, userSession);
  }
  
  public void remove(String id) {
    cache.invalidate(id);
  }
  
  public UserSession get(String id) {
    UserSession userSession;
    try {
      userSession = (UserSession)cache.get(id);
    } catch (ExecutionException e) {
      userSession = null;
      e.printStackTrace();
      log.error("", e);
    } 
    return userSession;
  }
}
