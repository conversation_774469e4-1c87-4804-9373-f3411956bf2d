package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class LFTContent {
  private final String contentId;
  
  private final String fileId;
  
  private final String fileName;
  
  private final String fileHash;
  
  private final long fileSize;
  
  @JsonCreator
  public LFTContent(@JsonProperty("contentId") String contentId, @JsonProperty("fileId") String fileId, @JsonProperty("fileName") String fileName, @JsonProperty("fileHash") String fileHash, @JsonProperty("fileSize") long fileSize) {
    this.contentId = contentId;
    this.fileId = fileId;
    this.fileName = fileName;
    this.fileHash = fileHash;
    this.fileSize = fileSize;
  }
  
  public String getContentId() {
    return this.contentId;
  }
  
  public String getFileId() {
    return this.fileId;
  }
  
  public String getFileName() {
    return this.fileName;
  }
  
  public long getFileSize() {
    return this.fileSize;
  }
  
  public String getFileHash() {
    return this.fileHash;
  }
}
