package com.samsung.magicinfo.rc.controller.common;

import com.samsung.magicinfo.rc.common.exception.RestServiceException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.web.ErrorController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
public class CustomErrorController implements ErrorController {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.controller.common.CustomErrorController.class);
  
  @RequestMapping({"/error"})
  public String handleError(HttpServletRequest req, HttpServletResponse res) throws RestServiceException {
    Throwable t = (Throwable)req.getAttribute("javax.servlet.error.exception");
    if (t == null)
      return "index.html"; 
    RestServiceException exception = (RestServiceException)t;
    throw exception;
  }
  
  public String getErrorPath() {
    return "/error";
  }
}
