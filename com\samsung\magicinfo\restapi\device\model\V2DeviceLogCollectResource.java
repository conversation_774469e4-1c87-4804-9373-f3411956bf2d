package com.samsung.magicinfo.restapi.device.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@JsonInclude(Include.NON_NULL)
public class V2DeviceLogCollectResource {
   @ApiModelProperty("Refer to V2DeviceEntityResources")
   private V2DeviceEntityResources collectInfo;
   @ApiModelProperty("Enable log collection for specific devices")
   private Boolean isEnable;
   @ApiModelProperty("Check Remote Logger from Config")
   private Boolean isRemoteLoggerEnable;
   @ApiModelProperty("Valid log count for a specific device")
   private int availableCnt;
   @ApiModelProperty("Log file size of specific device")
   private long freeSizeLong;
   @ApiModelProperty("List of collected log files on specific device")
   private List collectedLogList;

   public V2DeviceLogCollectResource() {
      super();
   }

   public V2DeviceEntityResources getCollectInfo() {
      return this.collectInfo;
   }

   public void setCollectInfo(V2DeviceEntityResources collectInfo) {
      this.collectInfo = collectInfo;
   }

   public Boolean getIsEnable() {
      return this.isEnable;
   }

   public void setIsEnable(Boolean isEnable) {
      this.isEnable = isEnable;
   }

   public Boolean getIsRemoteLoggerEnable() {
      return this.isRemoteLoggerEnable;
   }

   public void setIsRemoteLoggerEnable(Boolean isRemoteLoggerEnable) {
      this.isRemoteLoggerEnable = isRemoteLoggerEnable;
   }

   public int getAvailableCnt() {
      return this.availableCnt;
   }

   public void setAvailableCnt(int availableCnt) {
      this.availableCnt = availableCnt;
   }

   public long getFreeSizeLong() {
      return this.freeSizeLong;
   }

   public void setFreeSizeLong(long freeSizeLong) {
      this.freeSizeLong = freeSizeLong;
   }

   public List getCollectedLogList() {
      return this.collectedLogList;
   }

   public void setCollectedLogList(List collectedLogList) {
      this.collectedLogList = collectedLogList;
   }
}
