package com.samsung.magicinfo.webauthor2.service.upload;

import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import java.io.IOException;
import java.nio.file.Path;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

public interface UploadHelperService {
  List<MediaSource> prepareFilesToUpload(MultipartFile paramMultipartFile, Path paramPath) throws IOException;
  
  List<MediaSource> prepareFilesToUpload(Path paramPath1, Path paramPath2) throws IOException;
  
  MediaSource getDetailsFromFile(Path paramPath) throws IOException;
}
