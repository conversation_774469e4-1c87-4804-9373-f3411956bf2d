package com.samsung.magicinfo.rc.model.client;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.samsung.magicinfo.rc.model.client.Service;

@JacksonXmlRootElement(localName = "Request")
public class Request {
  @JacksonXmlProperty(localName = "service")
  Service service;
  
  public Service getService() {
    return this.service;
  }
  
  public void setService(Service service) {
    this.service = service;
  }
}
