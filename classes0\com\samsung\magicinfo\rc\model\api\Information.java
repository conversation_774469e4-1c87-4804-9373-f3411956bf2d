package com.samsung.magicinfo.rc.model.api;

public class Information {
  private String token;
  
  private String accessToken;
  
  private String from;
  
  public void setToken(String token) {
    this.token = token;
  }
  
  public void setAccessToken(String accessToken) {
    this.accessToken = accessToken;
  }
  
  public void setFrom(String from) {
    this.from = from;
  }
  
  Information(String token, String accessToken, String from) {
    this.token = token;
    this.accessToken = accessToken;
    this.from = from;
  }
  
  public static InformationBuilder builder() {
    return new InformationBuilder();
  }
  
  public String getToken() {
    return this.token;
  }
  
  public String getAccessToken() {
    return this.accessToken;
  }
  
  public String getFrom() {
    return this.from;
  }
}
