package com.samsung.magicinfo.rc.common.exception;

import com.samsung.magicinfo.rc.common.exception.ErrorResponse;

public class ErrorResponseBuilder {
  private String errorMessage;
  
  private int code;
  
  public ErrorResponseBuilder errorMessage(String errorMessage) {
    this.errorMessage = errorMessage;
    return this;
  }
  
  public ErrorResponseBuilder code(int code) {
    this.code = code;
    return this;
  }
  
  public ErrorResponse build() {
    return new ErrorResponse(this.errorMessage, this.code);
  }
  
  public String toString() {
    return "ErrorResponse.ErrorResponseBuilder(errorMessage=" + this.errorMessage + ", code=" + this.code + ")";
  }
}
