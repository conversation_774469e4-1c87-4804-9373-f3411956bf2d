package com.samsung.magicinfo.openapi.impl;

import com.samsung.common.logger.LoggingManagerV2;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.StringReader;
import java.io.StringWriter;
import java.net.URL;
import java.net.URLConnection;
import java.util.HashMap;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

public class XMLUtil {
   static Logger logger = LoggingManagerV2.getLogger(XMLUtil.class);
   private static DocumentBuilderFactory docfac = DocumentBuilderFactory.newInstance();

   public XMLUtil() {
      super();
   }

   public static String getValue(Node node, String tagName) {
      for(node = node.getFirstChild(); node != null; node = node.getNextSibling()) {
         if (node.getNodeName().equals(tagName)) {
            return node.getFirstChild().getNodeValue();
         }
      }

      return "";
   }

   public static int getValueInt(Node node, String tagName) {
      for(node = node.getFirstChild(); node != null; node = node.getNextSibling()) {
         if (node.getNodeName().equals(tagName)) {
            return new Integer(node.getFirstChild().getNodeValue());
         }
      }

      return -1;
   }

   public static Node isChild(Node node, String tagName) {
      for(node = node.getFirstChild(); node != null; node = node.getNextSibling()) {
         if (node.getNodeName().equals(tagName)) {
            return node;
         }
      }

      return null;
   }

   public static String getTextValue(Node n) {
      if (n == null) {
         return null;
      } else {
         return n.getFirstChild() == null ? null : n.getFirstChild().getNodeValue();
      }
   }

   public static Node getFirstElement(NodeList nodelist) {
      return nodelist != null && nodelist.getLength() != 0 ? nodelist.item(0) : null;
   }

   public static Element getFirstNonTextElement(NodeList childNodes) {
      if (childNodes != null && childNodes.getLength() != 0) {
         for(int i = 0; i < childNodes.getLength(); ++i) {
            if (childNodes.item(i).getNodeType() == 1) {
               return (Element)childNodes.item(i);
            }
         }

         return null;
      } else {
         return null;
      }
   }

   public static String getElementText(Element ele, String elementName) {
      return getTextValue(getFirstElement(ele.getElementsByTagName(elementName)));
   }

   public static String getSubElementText(Element ele, String elementName) {
      NodeList children = ele.getChildNodes();
      if (children != null && children.getLength() != 0) {
         for(int i = 0; i < children.getLength(); ++i) {
            Node n = children.item(i);
            if (n.getNodeType() == 1 && n.getNodeName().equals(elementName)) {
               return getTextValue(n);
            }
         }

         return null;
      } else {
         return null;
      }
   }

   public static HashMap getElementValues(Element ele) {
      HashMap map = new HashMap();
      NodeList list = ele.getChildNodes();
      if (list == null) {
         return map;
      } else {
         for(int i = 0; i < list.getLength(); ++i) {
            if (list.item(i).getNodeType() == 1) {
               map.put(list.item(i).getNodeName(), getTextValue((Element)list.item(i)));
            }
         }

         return map;
      }
   }

   public static String getElementText(Document doc, String elementName) {
      return getTextValue(getFirstElement(doc.getElementsByTagName(elementName)));
   }

   public static Element appendElement(Element ele, String eleName) {
      Element subEle = ele.getOwnerDocument().createElement(eleName);
      ele.appendChild(subEle);
      return subEle;
   }

   public static void appendElementText(Element ele, String eleName, String text) {
      Element subEle = ele.getOwnerDocument().createElement(eleName);
      ele.appendChild(subEle);
      if (text != null) {
         Node textEle = ele.getOwnerDocument().createTextNode(text);
         subEle.appendChild(textEle);
      }

   }

   public static Document parseXMLString(String xmlstring) throws XMLParsingException {
      try {
         if (xmlstring != null && !xmlstring.isEmpty()) {
            docfac.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            docfac.setFeature("http://xml.org/sax/features/external-general-entities", false);
            docfac.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            docfac.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
            DocumentBuilder docbuilder = docfac.newDocumentBuilder();
            return docbuilder.parse(new InputSource(new StringReader(xmlstring)));
         } else {
            return null;
         }
      } catch (Exception var2) {
         throw new XMLParsingException("E602", "xml을 파싱 할 수 없습니다. xml:" + xmlstring);
      }
   }

   public static Document parseXMLInputStream(InputStreamReader ir) throws XMLParsingException {
      try {
         if (ir == null) {
            return null;
         } else {
            DocumentBuilder docbuilder = docfac.newDocumentBuilder();
            return docbuilder.parse(new InputSource(ir));
         }
      } catch (Exception var2) {
         throw new XMLParsingException("E603", "parseXMLInputStream 하는 도중 에러가 발생했습니다. xml:" + ir.toString());
      }
   }

   public static Document parseXMLFile(String path) throws ReadFileException, XMLParsingException {
      try {
         DocumentBuilder docbuilder = docfac.newDocumentBuilder();
         return docbuilder.parse(new InputSource(path));
      } catch (IOException var2) {
         throw new ReadFileException("E604", "ReadFile fail : " + path);
      } catch (Exception var3) {
         throw new XMLParsingException("E605", "parse fail : " + path);
      }
   }

   public static Document parseClassPathXMLFile(String path) throws ReadFileException, XMLParsingException {
      FileInputStream xmlstream = null;

      Document var3;
      try {
         xmlstream = new FileInputStream(ConfUtil.getConfRoot() + "/" + path);
         DocumentBuilder docbuilder = docfac.newDocumentBuilder();
         var3 = docbuilder.parse(new InputSource(xmlstream));
      } catch (FileNotFoundException var13) {
         throw new ReadFileException("E606", ConfUtil.getConfRoot() + "/" + path);
      } catch (Exception var14) {
         throw new XMLParsingException("E607", "wrong xml file path : " + path);
      } finally {
         try {
            if (xmlstream != null) {
               xmlstream.close();
            }
         } catch (IOException var12) {
            logger.error("", var12);
         }

      }

      return var3;
   }

   public static void printDocumentAsClassPathXMLFile(String path, Document doc, String encoding) throws WriteFileException {
      OutputStreamWriter osw = null;

      try {
         osw = new OutputStreamWriter(new FileOutputStream(ConfUtil.getConfRoot() + "/" + path, false), encoding);
         osw.write(printDocument(doc, encoding));
      } catch (IOException var12) {
         throw new WriteFileException("E608", ConfUtil.getConfRoot() + "/" + path);
      } finally {
         try {
            if (osw != null) {
               osw.close();
            }
         } catch (IOException var11) {
            logger.error("", var11);
         }

      }

   }

   public static void printDocumentAsXMLFile(String xmlSource, Document doc, String encoding) throws WriteFileException {
      OutputStreamWriter osw = null;

      try {
         osw = new OutputStreamWriter(new FileOutputStream(xmlSource, false), encoding);
         osw.write(printDocument(doc, encoding));
      } catch (IOException var12) {
         throw new WriteFileException("E609", xmlSource);
      } finally {
         try {
            if (osw != null) {
               osw.close();
            }
         } catch (IOException var11) {
            logger.error("", var11);
         }

      }

   }

   public static Document parseXMLURL(String url_str) throws XMLParsingException, ReadFileException, IOException {
      InputStream instr = null;

      Document var5;
      try {
         URL url = new URL(url_str);
         URLConnection con = url.openConnection();
         instr = con.getInputStream();
         DocumentBuilder docbuilder = docfac.newDocumentBuilder();
         var5 = docbuilder.parse(instr);
      } catch (IOException var10) {
         throw new ReadFileException("E610", url_str);
      } catch (Exception var11) {
         throw new XMLParsingException("E611", "wrong url " + url_str);
      } finally {
         instr.close();
      }

      return var5;
   }

   public static Document getEmptyDocument() throws XMLParsingException {
      try {
         return docfac.newDocumentBuilder().newDocument();
      } catch (ParserConfigurationException var1) {
         throw new XMLParsingException("E612", "unknown error");
      }
   }

   public static String printDocument(Document doc, String encoding) {
      XMLWriter xw = new XMLWriter();
      StringWriter sw = new StringWriter();
      xw.setOutput(sw, encoding);
      xw.write(doc);
      return sw.toString();
   }

   public static String normalize(String s, boolean isAttValue) {
      if (s == null) {
         return "";
      } else {
         int len = s.length();
         StringBuffer newStr = new StringBuffer();

         for(int i = 0; i < len; ++i) {
            char c = s.charAt(i);
            switch(c) {
            case '"':
               if (isAttValue) {
                  newStr.append("&quot;");
               }
               break;
            case '&':
               newStr.append("&amp;");
               break;
            case '\'':
               if (isAttValue) {
                  newStr.append("'");
               }
               break;
            case '<':
               newStr.append("&lt;");
               break;
            case '>':
               newStr.append("&gt;");
               break;
            default:
               newStr.append(c);
            }
         }

         return newStr.toString();
      }
   }
}
