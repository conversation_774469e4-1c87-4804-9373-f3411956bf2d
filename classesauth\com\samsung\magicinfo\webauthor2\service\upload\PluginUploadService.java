package com.samsung.magicinfo.webauthor2.service.upload;

import com.google.common.base.Optional;
import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.model.FileItemsDescriptor;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import java.io.IOException;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

public interface PluginUploadService {
  String initializeUploadProcess(Optional<MultipartFile> paramOptional, String paramString1, String paramString2, String paramString3) throws IOException, FileItemValidationException;
  
  MediaSource storeSupportFileItem(String paramString1, String paramString2, MultipartFile paramMultipartFile) throws IOException, FileItemValidationException;
  
  List<MediaSource> getUpdatedMediaSources(FileItemsDescriptor paramFileItemsDescriptor);
  
  String deletePlugin(String paramString);
}
