package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.exception.repository.MagicInfoRemoteContentException;
import com.samsung.magicinfo.webauthor2.util.ServerInfo;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.io.IOException;
import java.net.URI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.UnknownHttpStatusCodeException;
import org.springframework.web.util.UriComponentsBuilder;

@RestController
@RequestMapping({"/thumbnail"})
public class ContentThumbnailController {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.webapi.controller.ContentThumbnailController.class);
  
  private ServerInfo serverInfo;
  
  private RestTemplate restTemplate;
  
  private final UserData userData;
  
  @Autowired
  public ContentThumbnailController(ServerInfo serverInfo, RestTemplate restTemplate, UserData userData) {
    this.serverInfo = serverInfo;
    this.restTemplate = restTemplate;
    this.userData = userData;
  }
  
  @GetMapping({"/{thumbnailFileId}/{thumbnailFileName}"})
  public HttpEntity<?> getFileThumbnail(@PathVariable String thumbnailFileId, @PathVariable String thumbnailFileName) throws IOException {
    URI uri = UriComponentsBuilder.newInstance().path("/servlet/ContentThumbnail").queryParam("thumb_id", new Object[] { thumbnailFileId }).queryParam("thumb_filename", new Object[] { thumbnailFileName }).build().encode().toUri();
    logger.debug("uri: " + uri);
    HttpHeaders headers = new HttpHeaders();
    headers.add("userId", this.userData.getUserId());
    headers.add("token", this.userData.getToken());
    headers.add("cache-control", "no-cache");
    HttpEntity<String> request = new HttpEntity((MultiValueMap)headers);
    try {
      ResponseEntity<byte[]> response = this.restTemplate.exchange(uri, HttpMethod.GET, request, byte[].class);
      return (HttpEntity<?>)ResponseEntity.ok(response.getBody());
    } catch (UnknownHttpStatusCodeException ex) {
      throw new MagicInfoRemoteContentException(ex.getMessage());
    } 
  }
}
