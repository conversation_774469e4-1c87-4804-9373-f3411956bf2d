package com.samsungcms.kpimagicinfo.security;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import org.apache.http.HttpHost;
import org.apache.http.client.HttpClient;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.conn.routing.HttpRoutePlanner;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.DefaultProxyRoutePlanner;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
public class RestTemplateSSL {
  private static final String SSL_HTTPS = "https";
  
  private static final String SSL_TLS = "TLS";
  
  private static final String FALSE = "FALSE";
  
  @Value("${network.proxy.ip}")
  private String proxyIp;
  
  @Value("${network.proxy.port}")
  private String proxyPort;
  
  private static TrustManager[] createTrustManagers() {
    TrustManager[] trustAllCerts = { (TrustManager)new Object() };
    return trustAllCerts;
  }
  
  public RestTemplate restTemplateSSL(HttpComponentsClientHttpRequestFactory requestFactory) throws Exception {
    SSLContext sslContext = SSLContext.getInstance("TLS");
    sslContext.init(null, createTrustManagers(), null);
    SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext, (HostnameVerifier)NoopHostnameVerifier.INSTANCE);
    Registry<ConnectionSocketFactory> registry = RegistryBuilder.create().register("https", csf).build();
    PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager(registry);
    DefaultProxyRoutePlanner routePlanner = null;
    if (!"FALSE".equalsIgnoreCase(this.proxyIp) && !"FALSE".equalsIgnoreCase(this.proxyPort)) {
      HttpHost proxy = new HttpHost(this.proxyIp, Integer.parseInt(this.proxyPort));
      routePlanner = new DefaultProxyRoutePlanner(proxy);
    } else {
      routePlanner = null;
    } 
    CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory((LayeredConnectionSocketFactory)csf).setConnectionManager((HttpClientConnectionManager)cm).setRoutePlanner((HttpRoutePlanner)routePlanner).build();
    requestFactory.setHttpClient((HttpClient)httpClient);
    return new RestTemplate((ClientHttpRequestFactory)requestFactory);
  }
}
