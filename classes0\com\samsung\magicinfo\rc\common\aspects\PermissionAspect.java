package com.samsung.magicinfo.rc.common.aspects;

import com.samsung.magicinfo.rc.common.exception.ExceptionCode;
import com.samsung.magicinfo.rc.common.http.RCResponseBody;
import com.samsung.magicinfo.rc.common.security.AuthenticationToken;
import com.samsung.magicinfo.rc.service.JwtServiceImpl;
import io.jsonwebtoken.Claims;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Component
@Aspect
public class PermissionAspect {
  @Autowired
  JwtServiceImpl jwtManagement;
  
  @Autowired
  CacheManager cacheManager;
  
  @Around("@annotation(PermissionDevice) && args(deviceId)")
  public Object checkDeviceAndTokenPermission(ProceedingJoinPoint pjp, String deviceId) throws Throwable {
    AuthenticationToken authenticationToken = (AuthenticationToken)SecurityContextHolder.getContext().getAuthentication();
    String jwt = authenticationToken.getJwt();
    String sessionId = (String)this.jwtManagement.getClaimsFromJwt(jwt, Claims::getSubject);
    if (!jwt.equals(getJwtBySessionId(sessionId))) {
      RCResponseBody responseBody = RCResponseBody.builder().errorCode(ExceptionCode.HTTP401[0]).errorMessage(ExceptionCode.HTTP401[1]).build();
      return new ResponseEntity(responseBody, HttpStatus.UNAUTHORIZED);
    } 
    if (!this.jwtManagement.validateJwtWithDeviceId(deviceId, jwt)) {
      RCResponseBody responseBody = RCResponseBody.builder().errorCode(ExceptionCode.HTTP405[0]).errorMessage(ExceptionCode.HTTP405[1]).build();
      return new ResponseEntity(responseBody, HttpStatus.METHOD_NOT_ALLOWED);
    } 
    Object result = pjp.proceed();
    return result;
  }
  
  public String getJwtBySessionId(String sessionId) {
    String jwt = String.valueOf(this.cacheManager.getCache("tokens").get(sessionId).get());
    return jwt;
  }
}
