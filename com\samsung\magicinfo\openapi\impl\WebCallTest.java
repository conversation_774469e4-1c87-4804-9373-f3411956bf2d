package com.samsung.magicinfo.openapi.impl;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.RequestUtils;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.util.Enumeration;
import javax.servlet.http.HttpServletRequest;
import org.apache.logging.log4j.Logger;

public class WebCallTest {
   static Logger logger = LoggingManagerV2.getLogger(WebCallTest.class);

   public WebCallTest() {
      super();
   }

   public static String urlCall(String ip, HttpServletRequest request) throws MalformedURLException {
      InputStream is = null;
      InputStreamReader isr = null;
      BufferedReader br = null;
      BufferedWriter bw = null;
      StringBuffer urlString = new StringBuffer(RequestUtils.getProtocolScheme(request) + ip + "/MagicInfo/openapi/open");

      String buf;
      try {
         URL url = new URL(urlString.toString());
         URLConnection urlConn = url.openConnection();
         HttpURLConnection httpConn = (HttpURLConnection)urlConn;
         httpConn.setRequestMethod("POST");
         httpConn.setDoInput(true);
         httpConn.setDoOutput(true);
         httpConn.setRequestProperty("Cache-Control", "no-cache");
         bw = new BufferedWriter(new OutputStreamWriter(httpConn.getOutputStream()));
         logger.debug(getParameters(request));
         bw.write(getParameters(request));
         bw.flush();
         is = httpConn.getInputStream();
         isr = new InputStreamReader(is, "UTF-8");
         br = new BufferedReader(isr);
         StringBuffer xml = new StringBuffer("");
         buf = null;

         while(true) {
            buf = br.readLine();
            if (buf == null) {
               String var12 = xml.toString();
               return var12;
            }

            xml.append(buf);
            xml.append("\n");
         }
      } catch (MalformedURLException var39) {
         logger.error("", var39);
         buf = "error";
         return buf;
      } catch (IOException var40) {
         logger.error("", var40);
         buf = "error";
      } finally {
         try {
            if (br != null) {
               br.close();
               br = null;
            }
         } catch (Exception var38) {
            logger.error("", var38);
         }

         try {
            if (isr != null) {
               isr.close();
               isr = null;
            }
         } catch (Exception var37) {
            logger.error("", var37);
         }

         try {
            if (is != null) {
               is.close();
               is = null;
            }
         } catch (Exception var36) {
            logger.error("", var36);
         }

         try {
            if (bw != null) {
               bw.close();
               bw = null;
            }
         } catch (Exception var35) {
            logger.error("", var35);
         }

      }

      return buf;
   }

   public static String getClassList(String ip, String id, String pw, String protocol) throws MalformedURLException {
      InputStream is = null;
      InputStreamReader isr = null;
      BufferedReader br = null;
      StringBuffer urlString = new StringBuffer(protocol + ip + "/MagicInfo/openapi/auth?cmd=getClassList&id=" + id + "&pw=" + pw);

      String buf;
      try {
         URL url = new URL(urlString.toString());
         URLConnection urlConn = url.openConnection();
         HttpURLConnection httpConn = (HttpURLConnection)urlConn;
         is = httpConn.getInputStream();
         isr = new InputStreamReader(is, "UTF-8");
         br = new BufferedReader(isr);
         StringBuffer xml = new StringBuffer("");
         buf = null;

         while(true) {
            buf = br.readLine();
            if (buf == null) {
               String var13 = xml.toString();
               return var13;
            }

            xml.append(buf);
            xml.append("\n");
         }
      } catch (MalformedURLException var35) {
         logger.error("", var35);
         buf = "error";
         return buf;
      } catch (IOException var36) {
         logger.error("", var36);
         buf = "error";
      } finally {
         try {
            if (br != null) {
               br.close();
               br = null;
            }
         } catch (Exception var34) {
            logger.error("", var34);
         }

         try {
            if (isr != null) {
               isr.close();
               isr = null;
            }
         } catch (Exception var33) {
            logger.error("", var33);
         }

         try {
            if (is != null) {
               is.close();
               is = null;
            }
         } catch (Exception var32) {
            logger.error("", var32);
         }

      }

      return buf;
   }

   public static String getAuthToken(String ip, String id, String pw, String protocolScheme) throws MalformedURLException {
      InputStream is = null;
      InputStreamReader isr = null;
      BufferedReader br = null;
      StringBuffer urlString = new StringBuffer(protocolScheme + ip + "/MagicInfo/openapi/auth?cmd=getAuthToken&id=" + id + "&pw=" + pw);

      String buf;
      try {
         URL url = new URL(urlString.toString());
         URLConnection urlConn = url.openConnection();
         HttpURLConnection httpConn = (HttpURLConnection)urlConn;
         is = httpConn.getInputStream();
         isr = new InputStreamReader(is, "UTF-8");
         br = new BufferedReader(isr);
         StringBuffer xml = new StringBuffer("");
         buf = null;

         while(true) {
            buf = br.readLine();
            if (buf == null) {
               String var13 = xml.toString();
               return var13;
            }

            xml.append(buf);
            xml.append("\n");
         }
      } catch (MalformedURLException var35) {
         logger.error("", var35);
         buf = "error";
         return buf;
      } catch (IOException var36) {
         logger.error("", var36);
         buf = "error";
      } finally {
         try {
            if (br != null) {
               br.close();
               br = null;
            }
         } catch (Exception var34) {
            logger.error("", var34);
         }

         try {
            if (isr != null) {
               isr.close();
               isr = null;
            }
         } catch (Exception var33) {
            logger.error("", var33);
         }

         try {
            if (is != null) {
               is.close();
               is = null;
            }
         } catch (Exception var32) {
            logger.error("", var32);
         }

      }

      return buf;
   }

   private static String getParameters(HttpServletRequest request) {
      StringBuffer rtn = new StringBuffer();
      int cnt = 0;

      for(Enumeration names = request.getParameterNames(); names.hasMoreElements(); ++cnt) {
         String name = (String)names.nextElement();
         if (cnt > 0) {
            rtn.append("&");
         }

         rtn.append(name + "=" + request.getParameter(name));
      }

      return rtn.toString();
   }
}
