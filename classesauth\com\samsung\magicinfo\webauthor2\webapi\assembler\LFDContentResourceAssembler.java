package com.samsung.magicinfo.webauthor2.webapi.assembler;

import com.samsung.magicinfo.webauthor2.model.LFDCanvasContent;
import com.samsung.magicinfo.webauthor2.model.LFDContent;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.webapi.controller.DataLinkQueryController;
import com.samsung.magicinfo.webauthor2.webapi.resource.LFDContentResource;
import org.springframework.hateoas.ResourceAssembler;
import org.springframework.hateoas.ResourceSupport;
import org.springframework.hateoas.mvc.BasicLinkBuilder;
import org.springframework.hateoas.mvc.ControllerLinkBuilder;
import org.springframework.stereotype.Component;

@Component
public class LFDContentResourceAssembler implements ResourceAssembler<LFDContent, LFDContentResource> {
  public LFDContentResource toResource(LFDContent lfdContent) {
    LFDContentResource lfdContentResource = new LFDContentResource(lfdContent, new org.springframework.hateoas.Link[0]);
    lfdContentResource.add(((BasicLinkBuilder)((BasicLinkBuilder)((BasicLinkBuilder)BasicLinkBuilder.linkToCurrentMapping()
        .slash("content"))
        .slash(lfdContent.getThumbnailId()))
        .slash(lfdContent.getThumbnailName()))
        .withRel("thumbnailUrl"));
    lfdContentResource.add(((BasicLinkBuilder)((BasicLinkBuilder)((BasicLinkBuilder)BasicLinkBuilder.linkToCurrentMapping()
        .slash("content"))
        .slash(lfdContent.getFileId()))
        .slash(lfdContent.getFileName()))
        .withRel("mainUrl"));
    int count = 1;
    for (LFDCanvasContent canvasContent : lfdContent.getCanvasContents()) {
      lfdContentResource.add(((BasicLinkBuilder)((BasicLinkBuilder)((BasicLinkBuilder)BasicLinkBuilder.linkToCurrentMapping()
          .slash("content"))
          .slash(canvasContent.getFileId()))
          .slash(canvasContent.getFileName()))
          .withRel("canvasContent_" + count));
      count++;
    } 
    if (lfdContent.getLfdInfo().getType() == MediaType.DLK)
      lfdContentResource.add(ControllerLinkBuilder.linkTo(((DataLinkQueryController)ControllerLinkBuilder.methodOn(DataLinkQueryController.class, new Object[0]))
            .getDataLinkDescriptor(lfdContent.getId())).withRel("dataLinkModel")); 
    return lfdContentResource;
  }
}
