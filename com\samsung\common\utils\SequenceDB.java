package com.samsung.common.utils;

import com.samsung.common.db.mybatis.SqlSessionFactoryManager;
import java.sql.SQLException;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.TransactionIsolationLevel;

public class SequenceDB {
   private static int SEQ_INCREMENT_BY = 1;

   public SequenceDB() {
      super();
   }

   public static int getNextValue(String sequenceName) throws SQLException {
      return getNextValue(sequenceName, SEQ_INCREMENT_BY);
   }

   public static int getNextValue(String sequenceName, int incrementBy) throws SQLException {
      SqlSession session = getSqlSessionFactory().openSession(TransactionIsolationLevel.REPEATABLE_READ);

      int result;
      try {
         SequenceDBMapper mapper = (SequenceDBMapper)session.getMapper(SequenceDBMapper.class);
         int sequenceExists = mapper.incrementValue(sequenceName, incrementBy);
         if (sequenceExists <= 0) {
            session.rollback();
            result = getFirstValue(sequenceName, incrementBy);
         } else {
            result = mapper.getCurrentValue(sequenceName);
            session.commit();
         }
      } catch (Exception var9) {
         session.rollback();
         throw var9;
      } finally {
         session.close();
      }

      return result;
   }

   private static int getFirstValue(String sequenceName, int incrementBy) throws SQLException {
      SqlSession session = getSqlSessionFactory().openSession(TransactionIsolationLevel.READ_UNCOMMITTED);

      int result;
      try {
         SequenceDBMapper mapper = buildSequenceDBMapper(session);
         int sequenceExists = mapper.incrementValue(sequenceName, incrementBy);
         if (sequenceExists <= 0) {
            mapper.createSequence(sequenceName, incrementBy);
         }

         result = mapper.getCurrentValue(sequenceName);
         session.commit();
      } catch (Exception var9) {
         session.rollback();
         throw var9;
      } finally {
         session.close();
      }

      return result;
   }

   private static SqlSessionFactoryManager getDBOperationManager() {
      return SqlSessionFactoryManager.getInstance();
   }

   private static SqlSessionFactory getSqlSessionFactory() {
      return getDBOperationManager().getSqlSessionFactory();
   }

   private static SequenceDBMapper buildSequenceDBMapper(SqlSession session) {
      return (SequenceDBMapper)session.getMapper(SequenceDBMapper.class);
   }
}
