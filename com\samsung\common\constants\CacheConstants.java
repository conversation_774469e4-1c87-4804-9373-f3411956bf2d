package com.samsung.common.constants;

public class CacheConstants {
   public static final String KEEPALIVEINFO_DIRECTCHANNEL_REPOSITORY_LOOKUP_ID = "KEEPALIVEINFO_DIRECTCHANNEL_MEMORY_MAP";
   public static final String SCHEDULEINFO_REPOSITORY_LOOKUP_ID = "SCHEDULEINFO_MEMORY_MAP";
   public static final String DB_CACHE_DEVICE_LOOKUP_ID = "DB_CACHE_DEVICE_MEMORY_MAP";
   public static final String SCHEDULE_FILE_STRING_SCH = "SCHEDULE_FILE_STRING_SCH_MAP";
   public static final String SCHEDULE_FILE_STRING_MSG = "SCHEDULE_FILE_STRING_MSG_MAP";
   public static final String SCHEDULE_FILE_STRING_JOB = "SCHEDULE_FILE_STRING_JOB_MAP";
   public static final String PRE_ASSIGNED_GROUP_MAP = "PRE_ASSIGNED_GROUP_MAP";
   public static final String JWT_TOKEN_SECRET_KEY = "JWT_TOKEN_SECRET_KEY";
   public static final String SECRET_KEY = "SECRET_KEY";
   public static final String BLOCK_TOKEN_LIST = "BLOCK_TOKEN_LIST";
   public static final String SERVER_HEALTH_MAP = "SERVER_HEALTH_MAP";

   public CacheConstants() {
      super();
   }
}
