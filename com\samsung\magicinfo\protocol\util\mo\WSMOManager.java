package com.samsung.magicinfo.protocol.util.mo;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.protocol.compiler.MOCompileException;
import com.samsung.magicinfo.protocol.compiler.MOFiles;
import com.samsung.magicinfo.protocol.compiler.MultiConfigFileGenerator;
import com.samsung.magicinfo.protocol.compiler.ValidateResult;
import com.samsung.magicinfo.protocol.compiler.ws.WSMOCompiler;
import com.samsung.magicinfo.protocol.repository.MORepository;
import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import org.apache.logging.log4j.Logger;

public class WSMOManager implements MOManager {
   private static Logger logger = LoggingManagerV2.getLogger(WSMOManager.class);
   public static final String CONFIG_FILE_PATH_KEY = "mo.files.path";
   public static final String COMPILE_FILE_SAVE_PATH_KEY = "mo.files.upload.savepath";
   private static volatile WSMOManager instance;
   private File configFile;
   private Properties configProps;

   private WSMOManager() throws Exception {
      super();
      String configFileDir = CommonConfig.get("mo.files.upload.savepath");
      File dir = SecurityUtils.getSafeFile(configFileDir);
      boolean fSuccess = dir.mkdir();
      if (!fSuccess) {
      }

      String configFilePath = CommonConfig.get("mo.files.path");
      this.configFile = SecurityUtils.getSafeFile(configFilePath);
      fSuccess = this.configFile.createNewFile();
      if (!fSuccess) {
      }

      this.configProps = new Properties();
      FileInputStream fis = null;

      try {
         fis = new FileInputStream(this.configFile);
         this.configProps.load(fis);
      } finally {
         if (fis != null) {
            try {
               fis.close();
            } catch (Exception var12) {
               logger.error("", var12);
            }
         }

      }

      MORepository.getInstance();
   }

   public static WSMOManager getInstance() throws Exception {
      if (instance == null) {
         Class var0 = WSMOManager.class;
         synchronized(WSMOManager.class) {
            if (instance == null) {
               instance = new WSMOManager();
            }
         }
      }

      return instance;
   }

   private void refresh() {
      FileInputStream fis = null;

      try {
         this.configProps = null;
         this.configProps = new Properties();
         fis = new FileInputStream(this.configFile);
         this.configProps.load(fis);
         fis.close();
      } catch (Exception var11) {
      } finally {
         if (fis != null) {
            try {
               fis.close();
            } catch (Exception var10) {
               logger.error("", var10);
            }
         }

      }

   }

   public boolean addImportedFilePath(String model, String importedFilePath) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      boolean var6;
      try {
         int index = multiConfigFileGenerator.addImportProperty(model);
         if (index >= 0) {
            String key = model + ".ifp." + index;
            boolean isSuccess = multiConfigFileGenerator.set(key, importedFilePath);
            boolean var7 = isSuccess;
            return var7;
         }

         multiConfigFileGenerator.close();
         var6 = false;
      } catch (Exception var11) {
         logger.error("", var11);
         return false;
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
         }

      }

      return var6;
   }

   public boolean addMOFilePath(String model, String moFilePath) throws Exception {
      return false;
   }

   public boolean addMONodeConstraintsFilePath(String model, String moNodeConstraintsFilePath) throws Exception {
      return false;
   }

   public boolean addMOSchemaPath(String model, String moSchemaPath) throws Exception {
      return false;
   }

   public boolean addMOTreeFilePath(String model, String moTreeFilePath) throws Exception {
      return false;
   }

   public boolean addTypeSchemaPath(String model, String typeSchemaPath) throws Exception {
      return false;
   }

   public boolean delImportedFilePath(String model, int index) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         String key = model + ".ifp." + index;
         boolean isSuccess = multiConfigFileGenerator.removeProperty(model, key);
         boolean var6 = isSuccess;
         return var6;
      } catch (Exception var10) {
         logger.error("", var10);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
         }

      }

      return false;
   }

   public boolean delMOFilePath(String model) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         String key = model + ".mfp";
         boolean isSuccess = multiConfigFileGenerator.set(key, "");
         boolean var5 = isSuccess;
         return var5;
      } catch (Exception var9) {
         logger.error("", var9);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
         }

      }

      return false;
   }

   public boolean delMONodeConstraintsFilePath(String model) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         String key = model + ".moc";
         boolean isSuccess = multiConfigFileGenerator.set(key, "");
         boolean var5 = isSuccess;
         return var5;
      } catch (Exception var9) {
         logger.error("", var9);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
         }

      }

      return false;
   }

   public boolean delMOSchemaPath(String model) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         String key = model + ".msp";
         boolean isSuccess = multiConfigFileGenerator.set(key, "");
         boolean var5 = isSuccess;
         return var5;
      } catch (Exception var9) {
         logger.error("", var9);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
         }

      }

      return false;
   }

   public boolean delMOTreeFilePath(String model) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         String key = model + ".mot";
         boolean isSuccess = multiConfigFileGenerator.set(key, "");
         boolean var5 = isSuccess;
         return var5;
      } catch (Exception var9) {
         logger.error("", var9);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
         }

      }

      return false;
   }

   public boolean delTypeSchemaPath(String model) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         String key = model + ".tsp";
         boolean isSuccess = multiConfigFileGenerator.set(key, "");
         boolean var5 = isSuccess;
         return var5;
      } catch (Exception var9) {
         logger.error("", var9);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
         }

      }

      return false;
   }

   public MOFiles getMOFiles(String model) throws Exception {
      this.refresh();
      return this.generateMOFiles(model);
   }

   public List getMOFilesList() throws Exception {
      this.refresh();
      List modelList = new ArrayList();
      List moFilesList = new ArrayList();
      Enumeration e = this.configProps.propertyNames();
      String model = null;

      while(e.hasMoreElements()) {
         model = this.parseModel((String)e.nextElement());
         if (model != null) {
            modelList.add(model);
         }
      }

      MOFiles moFiles = null;

      for(int i = 0; i < modelList.size(); ++i) {
         model = (String)modelList.get(i);
         moFiles = this.generateMOFiles(model);
         moFilesList.add(moFiles);
      }

      return moFilesList;
   }

   private MOFiles generateMOFiles(String model) throws Exception {
      String mnmAttr = model + ".mnm";
      String motAttr = model + ".mot";
      String mocAttr = model + ".moc";
      String mfpAttr = model + ".mfp";
      String mspAttr = model + ".msp";
      String tspAttr = model + ".tsp";
      String ifpAttr = model + ".ifp.";
      String mnmValue = (String)this.configProps.get(mnmAttr);
      String motValue = (String)this.configProps.get(motAttr);
      String mocValue = (String)this.configProps.get(mocAttr);
      String mfpValue = (String)this.configProps.get(mfpAttr);
      String mspValue = (String)this.configProps.get(mspAttr);
      String tspValue = (String)this.configProps.get(tspAttr);
      Enumeration e = this.configProps.propertyNames();
      Map ifpMap = new HashMap();
      String propName = null;
      int ifpAttrPos = true;
      String index = null;

      while(e.hasMoreElements()) {
         propName = (String)e.nextElement();
         int ifpAttrPos = propName.indexOf(ifpAttr);
         if (ifpAttrPos >= 0) {
            index = this.numImportProperties(propName);
            ifpMap.put(index, this.configProps.get(propName));
         }
      }

      return new MOFiles(model, mnmValue, motValue, mocValue, mfpValue, mspValue, tspValue, ifpMap);
   }

   private String numImportProperties(String importProperty) {
      int pos = importProperty.lastIndexOf(".");
      return importProperty.substring(pos + 1);
   }

   private String parseModel(String propertyName) throws Exception {
      String model = null;
      int lblocketPos = propertyName.indexOf("[");
      int rblocketPos = propertyName.indexOf("]");
      if (lblocketPos >= 0 && rblocketPos >= 0) {
         model = propertyName.substring(lblocketPos + 1, rblocketPos);
         return model;
      } else {
         return null;
      }
   }

   public boolean setImportedFilePath(String model, int index, String importedFilePath) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         String key = model + ".ifp." + index;
         boolean isSuccess = multiConfigFileGenerator.set(key, importedFilePath);
         boolean var7 = isSuccess;
         return var7;
      } catch (Exception var11) {
         logger.error("", var11);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
         }

      }

      return false;
   }

   public boolean setMOFilePath(String model, String moFilePath) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         String key = model + ".mfp";
         boolean isSuccess = multiConfigFileGenerator.set(key, moFilePath);
         boolean var6 = isSuccess;
         return var6;
      } catch (Exception var10) {
         logger.error("", var10);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
         }

      }

      return false;
   }

   public boolean setMONodeConstraintsFilePath(String model, String moNodeConstraintsFilePath) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         String key = model + ".moc";
         boolean isSuccess = multiConfigFileGenerator.set(key, moNodeConstraintsFilePath);
         boolean var6 = isSuccess;
         return var6;
      } catch (Exception var10) {
         logger.error("", var10);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
            MORepository.getInstance().refresh();
         }

      }

      return false;
   }

   public boolean setMOSchemaPath(String model, String moSchemaPath) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         String key = model + ".msp";
         boolean isSuccess = multiConfigFileGenerator.set(key, moSchemaPath);
         boolean var6 = isSuccess;
         return var6;
      } catch (Exception var10) {
         logger.error("", var10);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
         }

      }

      return false;
   }

   public boolean setMOTreeFilePath(String model, String moTreeFilePath) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         String key = model + ".mot";
         boolean isSuccess = multiConfigFileGenerator.set(key, moTreeFilePath);
         boolean var6 = isSuccess;
         return var6;
      } catch (Exception var10) {
         logger.error("", var10);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
         }

      }

      return false;
   }

   public boolean setTypeSchemaPath(String model, String typeSchemaPath) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         String key = model + ".tsp";
         boolean isSuccess = multiConfigFileGenerator.set(key, typeSchemaPath);
         boolean var6 = isSuccess;
         return var6;
      } catch (Exception var10) {
         logger.error("", var10);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
            MORepository.getInstance().refresh();
         }

      }

      return false;
   }

   public List getAddDeviceModelList() throws Exception {
      this.refresh();
      DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
      List deviceModelList = deviceMgr.getDeviceModelTypeList();
      List moFileList = this.getMOFilesList();
      Map deviceModel = null;
      String deviceModelId = null;
      MOFiles moFiles = null;
      List newDeviceModelList = new ArrayList();
      boolean equals = true;

      for(int i = 0; i < deviceModelList.size(); ++i) {
         deviceModel = (Map)deviceModelList.get(i);
         deviceModelId = (String)deviceModel.get("device_model_type");
         if (moFileList.size() <= 0) {
            newDeviceModelList.add(deviceModel);
         } else {
            for(int j = 0; j < moFileList.size(); ++j) {
               moFiles = (MOFiles)moFileList.get(j);
               if (deviceModelId.equals(moFiles.getModelName())) {
                  equals = true;
                  break;
               }

               equals = false;
            }

            if (!equals) {
               newDeviceModelList.add(deviceModel);
               equals = true;
            }
         }
      }

      return newDeviceModelList;
   }

   public boolean addAllEmptyProperties(String modelId) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         boolean isSuccess = multiConfigFileGenerator.addBaseProperties(modelId);
         boolean var4 = isSuccess;
         return var4;
      } catch (Exception var8) {
         logger.error("", var8);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
         }

      }

      return false;
   }

   public boolean setModelName(String modelId, String modelName) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         String key = modelId + ".mnm";
         boolean isSuccess = multiConfigFileGenerator.set(key, modelName);
         boolean var6 = isSuccess;
         return var6;
      } catch (Exception var10) {
         logger.error("", var10);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
         }

      }

      return false;
   }

   public boolean hasProperties(String modelId) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         boolean has = multiConfigFileGenerator.hasProperties(modelId);
         boolean var4 = has;
         return var4;
      } catch (Exception var8) {
         logger.error("", var8);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
         }

      }

      return false;
   }

   public boolean removeAll(String modelId) throws Exception {
      MultiConfigFileGenerator multiConfigFileGenerator = new MultiConfigFileGenerator(this.configFile);

      try {
         boolean isSuccess = multiConfigFileGenerator.removeModelProperties(modelId);
         boolean var4 = isSuccess;
         return var4;
      } catch (Exception var8) {
         logger.error("", var8);
      } finally {
         if (multiConfigFileGenerator != null) {
            multiConfigFileGenerator.close();
            MORepository.getInstance().refresh();
         }

      }

      return false;
   }

   public CompileResult compileMoFiles(String modelId) throws Exception {
      CompileResult compileResult = new CompileResult();
      this.refresh();
      MOFiles currentMOFiles = this.getMOFiles(modelId);
      String mfp = currentMOFiles.getMFP();
      String msp = currentMOFiles.getMSP();
      String tsp = currentMOFiles.getTSP();
      Map ifps = currentMOFiles.getIFPS();
      if (mfp != null && !mfp.equals("")) {
         if (msp != null && !msp.equals("")) {
            if (tsp != null && !tsp.equals("")) {
               List webImportedFiles = new ArrayList();
               Iterator iter = ifps.values().iterator();

               while(iter.hasNext()) {
                  webImportedFiles.add(iter.next());
               }

               String destDir = CommonConfig.get("mo.files.upload.savepath");
               destDir = destDir + "/" + modelId;
               WSMOCompiler moCompiler = new WSMOCompiler(modelId, webImportedFiles);

               try {
                  boolean isCompile = moCompiler.moCompile(mfp, tsp, destDir, false, false, false, false, false);
                  if (!isCompile) {
                     compileResult.setHasError(true);
                     ValidateResult validateResult = moCompiler.getCompileResult();
                     List list = validateResult.getMessages();

                     for(int i = 0; i < list.size(); ++i) {
                        compileResult.addMessage((String)list.get(i));
                     }
                  } else {
                     compileResult.setResult(true);
                  }

                  compileResult.setMoTreeFilePath(moCompiler.getMoTreeFilePath());
                  compileResult.setMoNodeConstraintsFilePath(moCompiler.getMoConstraintsFilePath());
                  return compileResult;
               } catch (MOCompileException var16) {
                  compileResult.setResult(false);
                  compileResult.addMessage(var16.getMessage());
                  return compileResult;
               }
            } else {
               compileResult.setResult(false);
               compileResult.addMessage("type schema file is not exist.");
               return compileResult;
            }
         } else {
            compileResult.setResult(false);
            compileResult.addMessage("mo schema file is not exist.");
            return compileResult;
         }
      } else {
         compileResult.setResult(false);
         compileResult.addMessage("mo file is not exist.");
         return compileResult;
      }
   }
}
