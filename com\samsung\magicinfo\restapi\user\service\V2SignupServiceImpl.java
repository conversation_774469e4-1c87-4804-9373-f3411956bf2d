package com.samsung.magicinfo.restapi.user.service;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserContractManager;
import com.samsung.magicinfo.framework.user.manager.UserContractManagerImpl;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserPasswordManager;
import com.samsung.magicinfo.framework.user.manager.UserPasswordManagerImpl;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManager;
import com.samsung.magicinfo.restapi.user.model.V2SignUpUserInfoResource;
import com.samsung.magicinfo.restapi.user.model.V2UserInfoResource;
import com.samsung.magicinfo.restapi.user.model.V2UserResource;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2SignupService")
@Transactional
public class V2SignupServiceImpl implements V2SignupService {
   protected Logger logger = LoggingManagerV2.getLogger(V2SignupServiceImpl.class);
   @Autowired
   private PIIDataManager piiDataManager;
   public static final String ROOT = "ROOT";
   public static final String ORGANIZATION = "organization";

   public V2SignupServiceImpl() {
      super();
   }

   public void checkUserIdIsDuplicated(V2SignUpUserInfoResource resource) throws Exception {
      System.out.println(resource);
      UserInfo userInfo = UserInfoImpl.getInstance();
      if (userInfo.getCountByUserIdForCheck(resource.getUserId()) > 0) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_USERID_IS_DUPLICATED);
      }
   }

   public V2UserResource createUserSignUp(V2SignUpUserInfoResource resource) throws Exception {
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      if (resource.getOrganizationId() != null && resource.getOrganizationId() != 0L) {
         if (userInfo.getCountByUserIdForCheck(resource.getUserId()) > 0) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_USERID_IS_DUPLICATED);
         } else {
            UserContractManager userContractManager = UserContractManagerImpl.getInstance();
            if (!userContractManager.checkValidTimeForSignup()) {
               throw new RestServiceException(RestExceptionCode.SERVICE_UNAVAILABLE_SIGNUP_RETRY);
            } else if (CommonUtils.isIncludeSpecialChar(resource.getUserId())) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_USERID_NOT_ALLOW_PATTERN);
            } else {
               boolean isCreated = this.createUser(resource);
               V2UserResource userResource = new V2UserResource();
               if (isCreated) {
                  User user = userInfo.getUserByUserId(resource.getUserId());
                  V2UserInfoResource userInfoData = new V2UserInfoResource();
                  userInfoData.setUserId(user.getUser_id());
                  userInfoData.setUserName(user.getUser_name());
                  userInfoData.setEmail(user.getEmail());
                  userInfoData.setTeam(user.getTeam());
                  userInfoData.setJobPosition(user.getJob_position());
                  userInfoData.setPhoneNum(user.getPhone_num());
                  userInfoData.setMobileNum(user.getMobile_num());
                  userResource.setUserInfo(userInfoData);
                  userResource.setUserStatus("UNAPPROVED");
                  userResource.setOrganizationName(user.getOrganization());
                  userResource.setGroupId(user.getGroup_id());
                  userResource.setRoleName(user.getRole_name());
                  userResource.setUnApprovedCause("NEW");
                  userResource.setSignUpDate(user.getCreate_date());
                  userResource.setLastLoginDate(user.getLast_login_date());
                  userResource.setPasswordChangeDate(user.getPassword_change_date());
                  return userResource;
               } else {
                  throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR);
               }
            }
         }
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST);
      }
   }

   private boolean createUser(V2SignUpUserInfoResource resource) throws Exception {
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      User user = null;
      boolean isNew = false;
      if (userInfo.getCountByUserId(resource.getUserId()) != 0 && userInfo.getIsDeletedByUserId(resource.getUserId()).equals("Y")) {
         user = userInfo.getUserByUserId(resource.getUserId());
         user.setApproval_type("TEXT_RE_SIGN_P");
      } else {
         user = new User();
         user.setUser_id(resource.getUserId());
         user.setOrganization(userGroupInfo.getGroupNameByGroupId(resource.getOrganizationId()));
         user.setRoot_group_id(resource.getOrganizationId());
         user.setOs_type("");
         user.setSerial_num("");
         user.setUsing_mobile("");
         isNew = true;
      }

      user.setUser_name(resource.getUserName().replaceAll("<", "&lt;").replaceAll(">", "&gt;"));
      user.setPassword(resource.getPassword());
      user.setEmail(resource.getEmail());
      user.setPhone_num(resource.getPhoneNum().replaceAll("<", "&lt;").replaceAll(">", "&gt;"));
      user.setMobile_num(resource.getMobileNum().replaceAll("<", "&lt;").replaceAll(">", "&gt;"));
      user.setTeam(resource.getTeam().replaceAll("<", "&lt;").replaceAll(">", "&gt;"));
      user.setJob_position(resource.getJobPosition().replaceAll("<", "&lt;").replaceAll(">", "&gt;"));
      user.setIs_approved("N");
      user.setIs_deleted("N");
      user.setIs_reset_pwd("N");
      UserPasswordManager userPassword = UserPasswordManagerImpl.getInstance();
      userPassword.isValidPassword(user.getUser_id(), user.getPassword(), user.getPassword(), user);
      boolean createFlag = false;
      if (isNew) {
         createFlag = userInfo.addUser(user, false);
      } else {
         createFlag = userInfo.setUser(user);
      }

      return createFlag;
   }
}
