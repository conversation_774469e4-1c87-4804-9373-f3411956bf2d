package com.samsung.magicinfo.rc.common.security;

import com.samsung.magicinfo.rc.common.exception.RestExceptionCode;
import com.samsung.magicinfo.rc.common.exception.RestServiceException;
import com.samsung.magicinfo.rc.common.security.AuthenticationToken;
import com.samsung.magicinfo.rc.service.JwtServiceImpl;
import io.jsonwebtoken.Claims;
import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.GenericFilterBean;

public class AuthenticationJwtFilter extends GenericFilterBean {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.common.security.AuthenticationJwtFilter.class);
  
  private static JwtServiceImpl jwtManagement = new JwtServiceImpl();
  
  public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
    HttpServletRequest request = (HttpServletRequest)servletRequest;
    HttpServletResponse response = (HttpServletResponse)servletResponse;
    String jwt = request.getHeader("Authorization");
    if (!StringUtils.isEmpty(jwt)) {
      if (!jwtManagement.isExpiredJwt(jwt))
        throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_TOKEN_EXPIRED); 
      String sessionId = (String)jwtManagement.getClaimsFromJwt(jwt, Claims::getSubject);
      if (sessionId == null)
        throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NO_SESSION_ID); 
      AuthenticationToken authenticationToken = new AuthenticationToken(null, null, jwt);
      SecurityContextHolder.getContext().setAuthentication((Authentication)authenticationToken);
      successfulAuthentication(request, response, filterChain);
    } else {
      throw new RestServiceException(RestExceptionCode.UNAUTHORIZED);
    } 
  }
  
  protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {
    chain.doFilter((ServletRequest)request, (ServletResponse)response);
  }
}
