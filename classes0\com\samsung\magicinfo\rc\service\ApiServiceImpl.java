package com.samsung.magicinfo.rc.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.samsung.magicinfo.rc.common.Device;
import com.samsung.magicinfo.rc.common.batch.CheckingServerAjaxTime;
import com.samsung.magicinfo.rc.common.cache.OneTimeSessionCache;
import com.samsung.magicinfo.rc.common.exception.OpenApiServiceException;
import com.samsung.magicinfo.rc.common.exception.RestExceptionCode;
import com.samsung.magicinfo.rc.common.exception.RestServiceException;
import com.samsung.magicinfo.rc.common.http.CustomHttpExecutor;
import com.samsung.magicinfo.rc.common.memory.ServerAuthorityMemory;
import com.samsung.magicinfo.rc.common.memory.ServerCaptureImageMemory;
import com.samsung.magicinfo.rc.common.memory.ServerTokenMemory;
import com.samsung.magicinfo.rc.common.queue.ServerQueue;
import com.samsung.magicinfo.rc.common.security.AuthenticationToken;
import com.samsung.magicinfo.rc.common.security.DeviceWithToken;
import com.samsung.magicinfo.rc.model.api.DeviceAddResource;
import com.samsung.magicinfo.rc.model.api.DeviceControl;
import com.samsung.magicinfo.rc.model.api.DeviceResource;
import com.samsung.magicinfo.rc.model.api.DeviceStopResource;
import com.samsung.magicinfo.rc.model.api.Item;
import com.samsung.magicinfo.rc.model.api.UserSession;
import com.samsung.magicinfo.rc.model.api.VncItems;
import com.samsung.magicinfo.rc.model.api.VncResponse;
import com.samsung.magicinfo.rc.model.remote.RemoteKey;
import com.samsung.magicinfo.rc.service.ClientServiceImpl;
import com.samsung.magicinfo.rc.service.JwtServiceImpl;
import io.jsonwebtoken.Claims;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.http.entity.ByteArrayEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
public class ApiServiceImpl {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.service.ApiServiceImpl.class);
  
  private static final String RC_START_URL = "/restapi/v2.0/rms/devices/rc/start";
  
  @Autowired
  ClientServiceImpl clientService;
  
  @Autowired
  JwtServiceImpl jwtManagement;
  
  @Autowired
  RemoteKey remoteKey;
  
  @Autowired
  ServerQueue serverQueue;
  
  @Autowired
  ServerCaptureImageMemory imageHashMap;
  
  @Autowired
  CheckingServerAjaxTime checkingServerAjaxTime;
  
  @Autowired
  ServerAuthorityMemory serverAuthorityMemory;
  
  @Autowired
  ServerTokenMemory serverTokenMemory;
  
  @Autowired
  OneTimeSessionCache oneTimeSessionCache;
  
  @Autowired
  CustomHttpExecutor customHttpExecutor;
  
  @Autowired
  ObjectMapper objectMapper;
  
  @Autowired
  CacheManager cacheManager;
  
  public Map getUserInformation(String sessionId) {
    Map<Object, Object> rtn = new HashMap<>();
    UserSession userSession = this.oneTimeSessionCache.get(sessionId);
    if (userSession != null) {
      Map claims = this.jwtManagement.createClaims(userSession, "WRITE");
      String authenticationKey = this.jwtManagement.generateJwt(sessionId, claims);
      log.info("[RC] generateJwt " + authenticationKey);
      rtn = new HashMap<>();
      rtn.put("authentication", authenticationKey);
    } else {
      throw new RestServiceException(RestExceptionCode.UNAUTHORIZED);
    } 
    return rtn;
  }
  
  public Device getDeviceFromJwtById(String deviceId) {
    String token = this.jwtManagement.getTokenFromSecurityContext(deviceId);
    Device device = null;
    if (this.serverQueue.getStatusQueue(deviceId).booleanValue()) {
      if (this.serverTokenMemory.checkToken(deviceId, token))
        device = this.serverQueue.getDeviceInfo(deviceId); 
    } else {
      throw new RestServiceException(RestExceptionCode.UNAUTHORIZED);
    } 
    return device;
  }
  
  public String stopDevice(String deviceId) {
    AuthenticationToken authenticationToken = (AuthenticationToken)SecurityContextHolder.getContext().getAuthentication();
    String jwt = authenticationToken.getJwt();
    Claims<String, Date> claims = this.jwtManagement.getClaimsFromJwt(jwt);
    List<Map> devicesFromJwt = (List<Map>)claims.get("devices");
    claims.put("created", new Date(System.currentTimeMillis()));
    claims.put("expired", new Date(System.currentTimeMillis() + 3600000L));
    String newJwt = null;
    DeviceControl deviceControl = new DeviceControl();
    deviceControl.setDeviceIds(new ArrayList(Arrays.asList((Object[])new String[] { deviceId })));
    DeviceStopResource deviceStopResource = stopDevices(deviceControl);
    if (devicesFromJwt != null && deviceStopResource != null && deviceStopResource
      .getSuccessDevices().size() == 1 && deviceStopResource
      .getSuccessDevices().get(0).equals(deviceId)) {
      List<DeviceWithToken> devicesWithToken = new ArrayList<>();
      for (Map device : devicesFromJwt) {
        if (device.get("deviceId") != null && !deviceId.equals(device.get("deviceId"))) {
          DeviceWithToken deviceWithToken = DeviceWithToken.builder().deviceId((String)device.get("deviceId")).token((String)device.get("token")).build();
          devicesWithToken.add(deviceWithToken);
        } 
      } 
      newJwt = updateJwtForDevices(jwt, devicesWithToken);
    } 
    DeviceControl devicecontrol = new DeviceControl();
    devicecontrol.setDeviceIds(new ArrayList(Arrays.asList((Object[])new String[] { deviceId })));
    stopDevices(deviceControl);
    return newJwt;
  }
  
  public DeviceStopResource stopDevices(DeviceControl deviceControl) {
    DeviceStopResource deviceStopResource = new DeviceStopResource();
    List<String> successDevices = new ArrayList<>();
    List<String> failDevices = new ArrayList<>();
    List<String> deviceIds = deviceControl.getDeviceIds();
    if (deviceIds != null && deviceIds.size() > 0)
      for (String deviceId : deviceIds) {
        if (this.serverQueue.getStatusQueue(deviceId).booleanValue()) {
          try {
            this.serverQueue.inputQueue(deviceId, "1");
            log.info("[RC][STOP][" + deviceId + "] stop device");
            successDevices.add(deviceId);
          } catch (OpenApiServiceException e) {
            failDevices.add(deviceId);
            log.error("", (Throwable)e);
          } 
          continue;
        } 
        failDevices.add(deviceId);
        if (this.serverTokenMemory.containsKey(deviceId)) {
          log.warn("[RC][" + deviceId + "] stop deviceIdWithToken");
          this.serverTokenMemory.deleteToken(deviceId);
          this.serverAuthorityMemory.deleteAuthority(deviceId);
        } 
      }  
    deviceStopResource.setSuccessDevices(successDevices);
    deviceStopResource.setFailDevices(failDevices);
    return deviceStopResource;
  }
  
  public void deviceControl(DeviceControl deviceControl) {
    if (deviceControl == null)
      return; 
    List<String> deviceIds = deviceControl.getDeviceIds();
    if (deviceIds != null && deviceIds.size() > 0) {
      for (String deviceId : deviceIds) {
        String token = this.jwtManagement.getTokenFromSecurityContext(deviceId);
        if (this.serverQueue.getStatusQueue(deviceId).booleanValue() && 
          this.serverTokenMemory.checkToken(deviceId, token)) {
          Device device = this.serverQueue.getDeviceInfo(deviceId);
          if (device != null) {
            String mouse = "";
            String keyCode = null;
            if (device.isSupportRemote())
              keyCode = this.remoteKey.getRemoteCode(deviceControl.getKeyCode()); 
            String imageSize = "null";
            if (deviceControl.getImageSize() != null)
              imageSize = deviceControl.getImageSize(); 
            String text = null;
            if (device.isSupportKeyboard() && deviceControl.getText() != null && !StringUtils.isEmpty(deviceControl.getText())) {
              text = deviceControl.getText();
              if (deviceControl.isTextWithEnter())
                text = text + "\\n"; 
            } 
            try {
              log.info("[RC][" + deviceId + "] keyCode : " + keyCode + " imageSize : " + imageSize + " text : " + text);
              this.serverQueue.inputQueue(deviceId, keyCode, imageSize, text, mouse);
            } catch (OpenApiServiceException e) {
              e.printStackTrace();
            } 
            continue;
          } 
          log.warn("[RC][" + deviceId + "] Not support remote! key : " + this.remoteKey);
        } 
      } 
    } else if (deviceControl.getDevices() != null && deviceControl.getDevices().size() > 0) {
      List<DeviceResource> devices = deviceControl.getDevices();
      for (DeviceResource deviceResource : devices) {
        String deviceId = deviceResource.getDeviceId();
        if (!StringUtils.isEmpty(deviceResource.getX()) && !StringUtils.isEmpty(deviceResource.getY())) {
          Device device = this.serverQueue.getDeviceInfo(deviceId);
          double oriWidth = Integer.valueOf(device.getWidth()).intValue();
          double oriHeight = Integer.valueOf(device.getHeight()).intValue();
          double doubleX = Double.valueOf(deviceResource.getX()).doubleValue();
          double doubleY = Double.valueOf(deviceResource.getY()).doubleValue();
          int degree = deviceResource.getRotate();
          double posX = (int)doubleX;
          double posY = (int)doubleY;
          int screenWidth = (deviceResource.getWidth() == null) ? 960 : Integer.valueOf(deviceResource.getWidth()).intValue();
          int screenHeight = (deviceResource.getHeight() == null) ? 540 : Integer.valueOf(deviceResource.getHeight()).intValue();
          boolean isScreenPortrait = (screenWidth < screenHeight);
          boolean isOriPortrait = (oriWidth < oriHeight);
          if (isOriPortrait) {
            if (isScreenPortrait && degree == 90) {
              posX = oriWidth - oriWidth / screenWidth * posX;
              posY = oriHeight / screenHeight * posY;
              double tempPosition = posX;
              posX = posY;
              posY = tempPosition;
            } else if (isScreenPortrait && degree == 0) {
              posX *= oriHeight / screenWidth;
              posY *= oriWidth / screenHeight;
            } else if (degree == 90) {
              double oriX = posX;
              double oriY = posY;
              posX = oriY * oriHeight / screenHeight;
              posY = oriWidth - oriX * oriWidth / screenWidth;
            } else {
              posX = oriWidth / screenHeight * posX;
              posY = oriHeight / screenWidth * posY;
            } 
          } else if (isScreenPortrait && degree == 90) {
            posX = oriHeight - oriWidth / screenHeight * posX;
            posY = oriHeight / screenWidth * posY;
            double tempPosition = posX;
            posX = posY;
            posY = tempPosition;
          } else if (isScreenPortrait && degree == 0) {
            posX = oriWidth / screenWidth * posX;
            posY = oriHeight / screenHeight * posY;
          } else if (degree == 90) {
            double oriX = posX;
            double oriY = posY;
            posY = oriHeight - oriHeight / screenWidth * oriX;
            posX = oriWidth / screenHeight * oriY;
          } else {
            posX = oriWidth / screenWidth * posX;
            posY = oriHeight / screenHeight * posY;
          } 
          String pressType = "";
          if (device.isSupportLongPress() && deviceResource.getPressType() != null && "LONG".equals(deviceResource.getPressType()))
            pressType = ",pressType:LONG"; 
          try {
            String mouse = "setPosX:" + (int)posX + ",setPosY:" + (int)posY + pressType;
            log.info("[RC][" + deviceId + "] mouse position x : " + posX + " y : " + posY + " preeType : " + pressType);
            this.serverQueue.inputQueue(deviceId, null, "null", null, mouse);
          } catch (OpenApiServiceException e) {
            log.error("", (Throwable)e);
          } 
          continue;
        } 
        if (!StringUtils.isEmpty(deviceControl.getText()))
          try {
            String text = deviceControl.getText();
            if (deviceControl.isTextWithEnter())
              text = text + "\\n"; 
            this.serverQueue.inputQueue(deviceId, null, "null", text, null);
          } catch (OpenApiServiceException e) {
            log.error("", (Throwable)e);
          }  
      } 
    } 
  }
  
  public byte[] getScreenCaptureByDeviceId(String deviceId) {
    String token = this.jwtManagement.getTokenFromSecurityContext(deviceId);
    if (!this.serverTokenMemory.checkToken(deviceId, token)) {
      log.error("[RC] error deviceId and token. deviceId : " + deviceId + " token : " + token);
      throw new RestServiceException(RestExceptionCode.UNAUTHORIZED);
    } 
    if (this.serverQueue.containsKey(deviceId))
      try {
        this.checkingServerAjaxTime.inputTimeQueue(deviceId);
        return this.imageHashMap.get(deviceId);
      } catch (OpenApiServiceException e) {
        log.error("", (Throwable)e);
        throw new RestServiceException(RestExceptionCode.METHOD_NOT_ALLOWED);
      }  
    return null;
  }
  
  public DeviceAddResource addDevices(List<String> deviceIds) {
    DeviceAddResource deviceAddResource = new DeviceAddResource();
    List<String> successList = new ArrayList<>();
    List<String> failList = new ArrayList<>();
    AuthenticationToken authenticationToken = (AuthenticationToken)SecurityContextHolder.getContext().getAuthentication();
    String jwt = authenticationToken.getJwt();
    Claims claims = this.jwtManagement.getClaimsFromJwt(jwt);
    String from = (String)claims.get("from");
    String accessToken = (String)claims.get("accessToken");
    List<Map> devicesFromJwt = (List<Map>)claims.get("devices");
    try {
      Map<String, List<String>> devices = new HashMap<>();
      devices.put("deviceIds", deviceIds);
      this;
      String body = this.customHttpExecutor.postForString(from + "/restapi/v2.0/rms/devices/rc/start", accessToken, new ByteArrayEntity(this.objectMapper.writeValueAsString(devices).getBytes(StandardCharsets.UTF_8)));
      VncResponse vncResponse = (VncResponse)this.objectMapper.readValue(body, VncResponse.class);
      if ("success".equalsIgnoreCase(vncResponse.getStatus())) {
        VncItems vncItems = vncResponse.getItems();
        if (!CollectionUtils.isEmpty(vncItems.getSuccessList())) {
          List<DeviceWithToken> devicesWithToken = new ArrayList<>();
          if (devicesFromJwt.size() > 0)
            for (Map map : devicesFromJwt) {
              String deviceId = (String)map.get("deviceId");
              String token = (String)map.get("token");
              devicesWithToken.add(DeviceWithToken.builder().deviceId(deviceId).token(token).build());
            }  
          for (Item item : vncItems.getSuccessList())
            devicesWithToken.add(DeviceWithToken.builder().deviceId(item.getDeviceId()).token(item.getToken()).build()); 
          String newJwt = updateJwtForDevices(jwt, devicesWithToken);
          deviceAddResource.setJwt(newJwt);
        } 
      } 
    } catch (Exception e) {
      log.error("", e);
      throw new RestServiceException(RestExceptionCode.BAD_REQUEST);
    } 
    deviceAddResource.setSuccessList(successList);
    deviceAddResource.setFailList(failList);
    return deviceAddResource;
  }
  
  private String updateJwtForDevices(String jwt, List<DeviceWithToken> devicesWithToken) {
    return this.jwtManagement.updateJwtWithDevices(jwt, devicesWithToken);
  }
}
