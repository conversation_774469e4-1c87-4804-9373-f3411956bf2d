package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.exception.service.UnauthorizedAccessException;
import com.samsung.magicinfo.webauthor2.service.MaintenanceService;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

@Controller
@RequestMapping({"/maintenance"})
public class MaintenancePanelController {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.webapi.controller.MaintenancePanelController.class);
  
  private MaintenanceService maintenanceService;
  
  private UserData userData;
  
  @Autowired
  public MaintenancePanelController(MaintenanceService maintenanceService, UserData userData) {
    this.maintenanceService = maintenanceService;
    this.userData = userData;
  }
  
  @GetMapping
  public String getMaintenancePanel(HttpServletRequest request) {
    logger.info("Accessed maintenance panel.");
    if (request.getSession() != null) {
      UserData requestUserData = (UserData)request.getSession().getAttribute("scopedTarget.userData");
      if (requestUserData != null) {
        String requestToken = requestUserData.getToken();
        String actualToken = this.userData.getToken();
        String role = this.userData.getUserId();
        if (actualToken.isEmpty() || !actualToken.equals(requestToken) || !role.equals("admin"))
          throw new UnauthorizedAccessException("NOT ALLOWED!"); 
      } 
    } 
    return "maintenance";
  }
  
  @PostMapping({"/text"})
  @ResponseBody
  public String postText(@RequestBody String text) throws IOException {
    return this.maintenanceService.saveLogsToFile(text);
  }
  
  @GetMapping({"/text"})
  @ResponseBody
  public HttpEntity<byte[]> getText(@RequestParam String filename) throws IOException {
    byte[] responseBody = this.maintenanceService.readLogsFromFile(filename);
    HttpHeaders headers = new HttpHeaders();
    headers.set("Content-Disposition", "attachment; filename=results.txt");
    headers.setContentLength(responseBody.length);
    return (HttpEntity<byte[]>)((ResponseEntity.BodyBuilder)ResponseEntity.ok().headers(headers)).body(responseBody);
  }
  
  @ExceptionHandler({Exception.class})
  @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
  public String exceptionHandler(Exception ex) {
    logger.error(ex.getMessage(), ex);
    return "common/error";
  }
}
