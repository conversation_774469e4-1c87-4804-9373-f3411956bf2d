package com.samsung.magicinfo.webauthor2.xml.datalink;

import com.samsung.magicinfo.webauthor2.xml.datalink.DataType;
import com.samsung.magicinfo.webauthor2.xml.datalink.ErrorType;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import org.eclipse.persistence.oxm.annotations.XmlCDATA;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ElementType", propOrder = {"name", "changeDuration", "positionX", "positionY", "width", "height", "error", "data"})
public class ElementType {
  @XmlElement(name = "Name", required = true)
  @XmlCDATA
  protected String name;
  
  @XmlElement(name = "ChangeDuration")
  protected long changeDuration;
  
  @XmlElement(name = "PositionX")
  protected String positionX;
  
  @XmlElement(name = "PositionY")
  protected String positionY;
  
  @XmlElement(name = "Width")
  protected String width;
  
  @XmlElement(name = "Height")
  protected String height;
  
  @XmlElement(name = "Error", required = true)
  protected ErrorType error;
  
  @XmlElement(name = "Data")
  protected List<DataType> data;
  
  @XmlAttribute(name = "no")
  protected int no;
  
  @XmlAttribute(name = "type")
  protected String type;
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String value) {
    this.name = value;
  }
  
  public long getChangeDuration() {
    return this.changeDuration;
  }
  
  public void setChangeDuration(long value) {
    this.changeDuration = value;
  }
  
  public String getPositionX() {
    return this.positionX;
  }
  
  public void setPositionX(String value) {
    this.positionX = value;
  }
  
  public String getPositionY() {
    return this.positionY;
  }
  
  public void setPositionY(String value) {
    this.positionY = value;
  }
  
  public String getWidth() {
    return this.width;
  }
  
  public void setWidth(String value) {
    this.width = value;
  }
  
  public String getHeight() {
    return this.height;
  }
  
  public void setHeight(String value) {
    this.height = value;
  }
  
  public ErrorType getError() {
    return this.error;
  }
  
  public void setError(ErrorType value) {
    this.error = value;
  }
  
  public List<DataType> getData() {
    if (this.data == null)
      this.data = new ArrayList<>(); 
    return this.data;
  }
  
  public int getNo() {
    return this.no;
  }
  
  public void setNo(int value) {
    this.no = value;
  }
  
  public String getType() {
    return this.type;
  }
  
  public void setType(String value) {
    this.type = value;
  }
}
