package com.samsungcms.kpimagicinfo.util;

import com.samsungcms.kpimagicinfo.security.RestTemplateSSL;
import java.util.Collections;
import org.apache.http.HttpHost;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.routing.HttpRoutePlanner;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.DefaultProxyRoutePlanner;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

@Component
public class RestClient {
  private static final Logger LOGGER = LogManager.getLogger(com.samsungcms.kpimagicinfo.util.RestClient.class);
  
  private static final String FALSE = "FALSE";
  
  @Value("${network.proxy.ip}")
  private String proxyIp;
  
  @Value("${network.proxy.port}")
  private String proxyPort;
  
  @Value("${network.timeout.connect}")
  private int connectTimeOut;
  
  @Value("${network.timeout.read}")
  private int readTimeOut;
  
  @Value("${policy.send.protocol.https.only}")
  private boolean httpsOnly;
  
  private final RestTemplateSSL restTemplateSSL;
  
  public RestClient(RestTemplateSSL restTemplateSSL) {
    this.restTemplateSSL = restTemplateSSL;
  }
  
  private HttpHeaders makeHttpHeaders(Object param, String token) {
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
    httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON_UTF8));
    if (token != null && !token.isEmpty())
      httpHeaders.set("api_key", token); 
    return httpHeaders;
  }
  
  public HttpEntity<Object> makeHttpEntity(Object param, String token) {
    HttpHeaders requestHeaders = makeHttpHeaders(param, token);
    return new HttpEntity(param, (MultiValueMap)requestHeaders);
  }
  
  public <T> T getByRestTemplate(String url, Object param, boolean isSSL, String token, Class<T> responseType) throws RestClientException {
    HttpEntity<Object> requestEntity = makeHttpEntity(param, token);
    RestTemplate restTemplate = buildRestTemplate(isSSL);
    ResponseEntity<T> responseEntity = restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType, new Object[0]);
    return (T)responseEntity.getBody();
  }
  
  public <T> T getByRestTemplate(String url, Object param, boolean isSSL, String token, ParameterizedTypeReference<T> responseType) throws RestClientException {
    HttpEntity<Object> requestEntity = makeHttpEntity(param, token);
    RestTemplate restTemplate = buildRestTemplate(isSSL);
    ResponseEntity<T> responseEntity = restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType, new Object[0]);
    return (T)responseEntity.getBody();
  }
  
  public <T> T postByRestTemplate(String url, Object param, boolean isSSL, String token, Class<T> responseType) throws RestClientException {
    HttpEntity<Object> requestEntity = makeHttpEntity(param, token);
    RestTemplate restTemplate = buildRestTemplate(isSSL);
    return (T)restTemplate.postForObject(url, requestEntity, responseType, new Object[0]);
  }
  
  public <T> T putByRestTemplate(String url, Object param, boolean isSSL, String token, Class<T> responseType) throws RestClientException {
    HttpEntity<Object> requestEntity = makeHttpEntity(param, token);
    RestTemplate restTemplate = buildRestTemplate(isSSL);
    ResponseEntity<T> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, requestEntity, responseType, new Object[0]);
    return (T)responseEntity.getBody();
  }
  
  public ResponseEntity<String> get(String url, HttpEntity<MultiValueMap<String, Object>> requestEntity) throws RestClientException {
    RestTemplate restTemplate = buildRestTemplate(this.httpsOnly);
    ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class, new Object[0]);
    return response;
  }
  
  public ResponseEntity<String> post(String url, HttpEntity<MultiValueMap<String, Object>> requestEntity) throws RestClientException {
    RestTemplate restTemplate = buildRestTemplate(this.httpsOnly);
    ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class, new Object[0]);
    return response;
  }
  
  private RestTemplate buildRestTemplate(boolean isSSL) {
    RestTemplate restTemplate = null;
    HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
    requestFactory.setConnectionRequestTimeout(this.connectTimeOut);
    requestFactory.setReadTimeout(this.readTimeOut);
    if (isSSL) {
      try {
        restTemplate = this.restTemplateSSL.restTemplateSSL(requestFactory);
      } catch (Exception e) {
        LOGGER.info(e.getMessage());
      } 
    } else {
      DefaultProxyRoutePlanner routePlanner;
      if (!"FALSE".equalsIgnoreCase(this.proxyIp) && !"FALSE".equalsIgnoreCase(this.proxyPort)) {
        HttpHost proxy = new HttpHost(this.proxyIp, Integer.parseInt(this.proxyPort));
        routePlanner = new DefaultProxyRoutePlanner(proxy);
      } else {
        routePlanner = null;
      } 
      CloseableHttpClient httpClient = HttpClients.custom().setRoutePlanner((HttpRoutePlanner)routePlanner).build();
      requestFactory.setHttpClient((HttpClient)httpClient);
      restTemplate = new RestTemplate((ClientHttpRequestFactory)requestFactory);
    } 
    return restTemplate;
  }
}
