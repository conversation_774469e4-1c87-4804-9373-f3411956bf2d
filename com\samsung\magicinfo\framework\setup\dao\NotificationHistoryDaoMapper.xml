<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.samsung.magicinfo.framework.setup.dao.NotificationHistoryDaoMapper">
	<insert id="addHistory">
		INSERT INTO MI_SYSTEM_INFO_NOTIFICATION_HISTORY (ID, SEND_TIME, ORGANIZATION_ID, TYPE, TITLE, RESULT, RESULT_MSG )
		VALUES (#{history.id}, #{history.send_time}, #{history.organization_id}, #{history.type}, #{history.title}, #{history.result}, #{history.result_msg})
	</insert>

	<insert id="addAttachedFile">
		INSERT INTO MI_SYSTEM_INFO_NOTIFICATION_ATTACHED_FILE (FILE_ID, FILE_NAME, FILE_PATH)
		VALUES (#{fileId}, #{fileName}, #{filePath})
	</insert>

	<insert id="addHistoryMapIssueId">
		INSERT INTO MI_SYSTEM_MAP_NOTIFICATION_HISTORY_ISSUE_ID (HISTORY_ID, ISSUE_ID, ISSUE_NAME)
		VALUES (#{historyId}, #{issueId}, #{issueName})
	</insert>

	<insert id="addHistoryMapUserId">
		INSERT INTO MI_SYSTEM_MAP_NOTIFICATION_HISTORY_USER (HISTORY_ID, USER_ID, EMAIL)
		VALUES (#{historyId}, #{userId}, #{email})
	</insert>

    <select id="getAllNotificationUserHistory" resultType="java.util.Map">
		SELECT * FROM MI_SYSTEM_MAP_NOTIFICATION_HISTORY_USER
	</select>

	<update id="setUserNotificationHistory">
		UPDATE MI_SYSTEM_MAP_NOTIFICATION_HISTORY_USER
		SET EMAIL = #{email}
		WHERE HISTORY_ID = #{historyId} AND USER_ID = #{userId}
	</update>
	<insert id="addHistoryMapAttachedFileId">
		INSERT INTO MI_SYSTEM_MAP_NOTIFICATION_HISTORY_ATTACHED_FILE (HISTORY_ID, FILE_ID)
		VALUES (#{historyId}, #{fileId})
	</insert>


	<select id="getCount" resultType="java.lang.Integer">
		SELECT COUNT(ID) FROM (
			SELECT DISTINCT
			A.ID
			FROM
			MI_SYSTEM_INFO_NOTIFICATION_HISTORY A,
			MI_SYSTEM_MAP_NOTIFICATION_HISTORY_USER B,
			MI_USER_INFO_GROUP C
			WHERE
			A.ID = B.HISTORY_ID AND A.ORGANIZATION_ID = C.GROUP_ID
			<!-- <if test="userManageGroupList != null and userManageGroupList.size() > 0">
				AND A.ORGANIZATION_ID IN
				<foreach item="group" collection="userManageGroupList" open=" (" separator="," close=")">
					#{group.group_id}
				</foreach>
			</if>-->

			<if test="condition.org_id != null and condition.org_id != 0L">
				AND A.ORGANIZATION_ID = #{condition.org_id}
			</if>

			<if test="condition.search_text != null and condition.search_text != ''">
				AND (
				UPPER(A.TITLE)   LIKE '%' <include refid="utils.concatenate"/> #{condition.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^'
				)
			</if>
		) T
	</select>

	<select id="getAllNotificationHistoryList" resultType="com.samsung.magicinfo.framework.setup.entity.NotificationHistoryEntity">
		SELECT DISTINCT A.*, C.GROUP_NAME AS ORGANIZATION_NAME
		FROM
			MI_SYSTEM_INFO_NOTIFICATION_HISTORY A,
			MI_SYSTEM_MAP_NOTIFICATION_HISTORY_USER B,
			MI_USER_INFO_GROUP C
		WHERE
			A.ID = B.HISTORY_ID AND A.ORGANIZATION_ID = C.GROUP_ID

			<if test="organization != null and !organization.equals('ROOT')">
			AND C.GROUP_NAME = #{organization}
			</if>

			ORDER BY A.ID ASC
	</select>

	<select id="getPagedNotificationHistoryList" resultType="com.samsung.magicinfo.framework.setup.entity.NotificationHistoryEntity">
		SELECT * FROM (
			SELECT
				DISTINCT A.*, C.GROUP_NAME AS ORGANIZATION_NAME
			FROM
				MI_SYSTEM_INFO_NOTIFICATION_HISTORY A,
				MI_SYSTEM_MAP_NOTIFICATION_HISTORY_USER B,
				MI_USER_INFO_GROUP C
			WHERE
				A.ID = B.HISTORY_ID AND A.ORGANIZATION_ID = C.GROUP_ID

			<!-- <if test="userManageGroupList != null and userManageGroupList.size() > 0">
				AND A.ORGANIZATION_ID IN
				<foreach item="group" collection="userManageGroupList" open=" (" separator="," close=")">
					#{group.group_id}
				</foreach>
			</if> -->
			<if test="condition.org_id != null and condition.org_id != 0L">
				AND A.ORGANIZATION_ID = #{condition.org_id}
			</if>

			<if test="condition.search_text != null and condition.search_text != ''">
				AND (
					  UPPER(A.TITLE)   LIKE '%' <include refid="utils.concatenate"/> #{condition.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^'
				)
			</if>

			<choose>
				<when test="condition.sortColumn != null and condition.sortColumn != ''">
					<bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sortColumn)" />
					ORDER BY ${safe_sortColumn}
					<if test="condition.sortOrder != null and condition.sortOrder != ''">
						<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.sortOrder)" />
						${safe_sortOrder}
					</if>
				</when>
				<otherwise>
					ORDER BY A.ID DESC
				</otherwise>
			</choose>
			) T
		LIMIT #{pageSize} OFFSET #{offset}
	</select>

	<select id="getPagedNotificationHistoryList" resultType="com.samsung.magicinfo.framework.setup.entity.NotificationHistoryEntity" databaseId="mssql">
		SELECT * FROM
		(
			SELECT DISTINCT A.*, C.GROUP_NAME AS ORGANIZATION_NAME
			FROM
				MI_SYSTEM_INFO_NOTIFICATION_HISTORY A,
				MI_SYSTEM_MAP_NOTIFICATION_HISTORY_USER B,
				MI_USER_INFO_GROUP C
			WHERE
				A.ID = B.HISTORY_ID AND A.ORGANIZATION_ID = C.GROUP_ID
				<if test="condition.org_id != null and condition.org_id != 0L">
					AND A.ORGANIZATION_ID = #{condition.org_id}
				</if>
				<if test="condition.search_text != null and condition.search_text != ''">
					AND (
						   UPPER(A.TITLE)   LIKE '%' <include refid="utils.concatenate"/> #{condition.search_text} <include refid="utils.concatenate"/> '%' ESCAPE '^'
					   )
				</if>
		) as SubQuery
		<choose>
			<when test="condition.sortColumn != null and condition.sortColumn != ''">
				<bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sortColumn)" />
				ORDER BY ${safe_sortColumn}
				<if test="condition.sortOrder != null and condition.sortOrder != ''">
					<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.sortOrder)" />
					${safe_sortOrder}
				</if>
			</when>
			<otherwise>
				ORDER BY A.ID DESC
			</otherwise>
		</choose>
		OFFSET ${offset} ROWS FETCH NEXT #{pageSize} ROWS ONLY
	</select>

	<select id="getAttachedFileList" resultType="java.util.Map">
		SELECT
			A.*
		FROM
			MI_SYSTEM_INFO_NOTIFICATION_ATTACHED_FILE A,
			MI_SYSTEM_MAP_NOTIFICATION_HISTORY_ATTACHED_FILE B
		WHERE
			A.FILE_ID = B.FILE_ID
			AND B.HISTORY_ID = #{historyId}
	</select>

	<select id="getAttachedFileListByTime" resultType="java.util.Map">
		SELECT
			C.*
		FROM
			MI_SYSTEM_INFO_NOTIFICATION_HISTORY A,
			MI_SYSTEM_MAP_NOTIFICATION_HISTORY_ATTACHED_FILE B,
			MI_SYSTEM_INFO_NOTIFICATION_ATTACHED_FILE C
		WHERE
			A.ID = B.HISTORY_ID
			AND B.FILE_ID = C.FILE_ID
			AND A.SEND_TIME &lt; #{retentionTime}
	</select>

	<delete id="deleteAttachedFile">
		DELETE FROM
			MI_SYSTEM_INFO_NOTIFICATION_ATTACHED_FILE
		WHERE
			FILE_ID IN (
				SELECT
					C.FILE_ID
				FROM
					MI_SYSTEM_INFO_NOTIFICATION_HISTORY A,
					MI_SYSTEM_MAP_NOTIFICATION_HISTORY_ATTACHED_FILE B,
					MI_SYSTEM_INFO_NOTIFICATION_ATTACHED_FILE C
				WHERE
					A.ID = B.HISTORY_ID
					AND B.FILE_ID = C.FILE_ID
					AND A.SEND_TIME &lt; #{retentionTime}
			)
	</delete>

	<delete id="deleteHistory">
		DELETE FROM MI_SYSTEM_INFO_NOTIFICATION_HISTORY WHERE SEND_TIME &lt; #{retentionTime}
	</delete>

	<select id="getGarbageFileList" resultType="java.util.Map">
		SELECT * FROM MI_SYSTEM_INFO_NOTIFICATION_ATTACHED_FILE A WHERE NOT EXISTS (
			SELECT * FROM MI_SYSTEM_MAP_NOTIFICATION_HISTORY_ATTACHED_FILE B
			WHERE A.FILE_ID = B.FILE_ID
		)
	</select>

	<delete id="deleteGarbageFiles">
		DELETE FROM
			MI_SYSTEM_INFO_NOTIFICATION_ATTACHED_FILE
		WHERE FILE_ID IN (
			SELECT A.FILE_ID FROM MI_SYSTEM_INFO_NOTIFICATION_ATTACHED_FILE A WHERE NOT EXISTS (
				SELECT * FROM MI_SYSTEM_MAP_NOTIFICATION_HISTORY_ATTACHED_FILE B
				WHERE A.FILE_ID = B.FILE_ID
			)
		)
	</delete>

	<select id="getReceiverList" resultType="java.util.Map">
		SELECT
			A.*, B.USER_NAME
		FROM
			MI_SYSTEM_MAP_NOTIFICATION_HISTORY_USER A,
			MI_USER_INFO_USER B
		WHERE
			A.USER_ID = B.USER_ID AND HISTORY_ID = #{historyId}
	</select>

	<select id="getReceiverIDList" resultType="string">
		SELECT
			USER_ID
		FROM
			MI_SYSTEM_MAP_NOTIFICATION_HISTORY_USER
		WHERE
			HISTORY_ID = #{historyId}
	</select>

	<select id="getNotificationHistoryInfo" resultType="com.samsung.magicinfo.framework.setup.entity.NotificationHistoryEntity">
		SELECT DISTINCT A.*, C.GROUP_NAME AS ORGANIZATION_NAME
		FROM
			MI_SYSTEM_INFO_NOTIFICATION_HISTORY A,
			MI_SYSTEM_MAP_NOTIFICATION_HISTORY_USER B,
			MI_USER_INFO_GROUP C
		WHERE
			A.ID = B.HISTORY_ID AND A.ORGANIZATION_ID = C.GROUP_ID
			AND A.ID = #{historyId}
	</select>
</mapper>
