package com.samsung.magicinfo.restapi.auth.controller;

import com.samsung.common.cache.CacheFactory;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.auth.security.MfaService;
import com.samsung.magicinfo.auth.security.strategies.AuthStrategyContext;
import com.samsung.magicinfo.auth.security.strategies.impl.TotpAuthStrategy;
import com.samsung.magicinfo.auth.security.strategies.model.AuthModel;
import com.samsung.magicinfo.framework.kpi.annotation.KPI;
import com.samsung.magicinfo.framework.kpi.annotation.LogProperty;
import com.samsung.magicinfo.framework.security.manager.SecurityInfo;
import com.samsung.magicinfo.framework.security.manager.SecurityInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.mvc.handler.ApiVersion;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.restapi.auth.model.V2AuthServerInfo;
import com.samsung.magicinfo.restapi.auth.model.V2AuthenticationResource;
import com.samsung.magicinfo.restapi.auth.model.V2AuthenticationResponse;
import com.samsung.magicinfo.restapi.auth.model.V2HmacAuthenticationRequest;
import com.samsung.magicinfo.restapi.auth.service.V2AuthenticationService;
import com.samsung.magicinfo.restapi.common.model.V2CommonDeleteResult;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.setting.model.V2SettingServerResource;
import com.samsung.magicinfo.restapi.setting.model.config.V2PasswordPolicySettings;
import com.samsung.magicinfo.restapi.setting.service.V2SettingMyAccountService;
import com.samsung.magicinfo.restapi.setting.service.V2SettingServerService;
import com.samsung.magicinfo.restapi.setting.service.V2SettingServerServiceImpl;
import com.samsung.magicinfo.restapi.user.model.V2UserEncyptionTokenResource;
import com.samsung.magicinfo.restapi.user.model.V2UserInfoResource4Password;
import com.samsung.magicinfo.restapi.user.service.V2UserMfaService;
import com.samsung.magicinfo.restapi.user.service.V2UserService;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.RestAPIUtil;
import com.samsung.magicinfo.restapi.utils.V2TokenUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.Authorization;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(
   value = "Authentication",
   description = "Authentication",
   tags = {"Authentication API Group"}
)
@RestController
@RequestMapping({"/restapi/v2.0/auth"})
@Validated
@ApiVersion({2.0D})
public class V2AuthenticationController {
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   @Autowired
   private V2AuthenticationService v2AuthenticationService;
   @Autowired
   private V2SettingMyAccountService v2SettingMyAccountService;
   @Autowired
   private V2UserService v2UserService;
   @Autowired
   private V2UserMfaService v2UserMfaService;

   public V2AuthenticationController() {
      super();
   }

   @KPI
   @LogProperty(
      eventType = "Login"
   )
   @ApiOperation(
      value = "Get auth token and refesh token.",
      notes = "Get auth token and refesh token"
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "resource",
   value = "Resource for user authentication",
   dataType = "V2AuthenticationResource"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400002, Token is invalid value.\n errorCode : 400002, Refresh toekn is invalid value."
), @ApiResponse(
   code = 401,
   message = "Unauthorized \nerrorCode : 401000, Unauthorized \nerrorCode : 401002, Login failed.\nerrorCode : 401003, Token is expired.\nerrorCode : 401004, Refresh token is expired.\nerrorCode : 401007, The account is locked\nerrorCode : 401008, The account is not approved"
)})
   @PostMapping(
      produces = {"application/json"}
   )
   public ResponseEntity authenticationRequest(@RequestBody V2AuthenticationResource resource, HttpServletRequest context) throws Exception {
      V2AuthenticationResponse res = null;
      if (V2AuthenticationController.GRANT_TYPE.REFRESH.getType().equalsIgnoreCase(resource.getGrantType())) {
         res = this.v2AuthenticationService.refreshTokenAuthentication(resource, context);
      } else if (V2AuthenticationController.GRANT_TYPE.CODE.getType().equalsIgnoreCase(resource.getGrantType())) {
         String jwtToken = V2TokenUtils.getJwtTokenFromAuthServer(resource);
         res = this.v2AuthenticationService.codeTypeAuthentication(resource, jwtToken, context);
      } else {
         res = this.v2AuthenticationService.passwordTypeAuthentication(resource, context);
      }

      return ResponseEntity.ok(res);
   }

   @PostMapping(
      value = {"check"},
      produces = {"application/json"}
   )
   public ResponseEntity authenticationRequestCheck(@RequestBody V2AuthenticationResource resource, HttpServletRequest context) throws Exception {
      new V2AuthenticationResponse();

      V2AuthenticationResponse res;
      try {
         res = this.v2AuthenticationService.checkAuthentication(resource, context);
      } catch (RestServiceException var5) {
         if (MfaService.OTP_EXCEPTION_LIST.contains(var5.getErrorCode())) {
            throw new RestServiceException(RestExceptionCode.NOT_ACCEPTABLE);
         }

         if (var5.getErrorCode().equals(RestExceptionCode.UNAUTHORIZED_USER_LOCKED.getCode())) {
            throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_USER_LOCKED);
         }

         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED);
      } catch (Exception var6) {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED);
      }

      return ResponseEntity.ok(res);
   }

   @PostMapping(
      value = {"code"},
      produces = {"application/json"}
   )
   public ResponseEntity authenticationRequestWithCode(@RequestBody V2AuthenticationResource resource, HttpServletRequest context) throws Exception {
      V2AuthenticationResponse res = new V2AuthenticationResponse();
      String jwtToken = V2TokenUtils.getJwtTokenFromAuthServer(resource);
      if (V2AuthenticationController.GRANT_TYPE.USER_ID.getType().equalsIgnoreCase(resource.getGrantType()) && null != resource.getCode()) {
         String userId = null;

         try {
            Map authCodeMap = (Map)CacheFactory.getCache().get("AUTH_CODE");
            if (null != authCodeMap) {
               userId = (String)authCodeMap.get(resource.getCode());
               res.setUserId(userId);
               return ResponseEntity.ok(res);
            }
         } catch (Exception var7) {
            this.logger.error("", var7);
         }
      }

      if (jwtToken != null) {
         res = this.v2AuthenticationService.codeTypeAuthentication(resource, jwtToken, context);
      }

      return ResponseEntity.ok(res);
   }

   @ApiOperation(
      value = "HMAC authentication",
      notes = "HMAC authentication"
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "resource",
   value = "Resource for HMAC authentication",
   dataType = "V2HmacAuthenticationRequest"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400002, Token is invalid value.\n errorCode : 400002, Refresh toekn is invalid value."
), @ApiResponse(
   code = 401,
   message = "Unauthorized \nerrorCode : 401000, Unauthorized \nerrorCode : 401002, Login failed.\nerrorCode : 401003, Token is expired.\nerrorCode : 401004, Refresh token is expired.\nerrorCode : 401007, The account is locked\nerrorCode : 401008, The account is not approved"
)})
   @PostMapping(
      value = {"hmac"},
      produces = {"application/json"}
   )
   public ResponseEntity hmacAuthenticationRequest(@RequestBody V2HmacAuthenticationRequest resource, HttpServletRequest context) throws Exception {
      V2AuthenticationResponse res = this.v2AuthenticationService.hmacTypeAuthentication(resource, context);
      return ResponseEntity.ok(res);
   }

   @ApiOperation(
      value = "Delete refesh token on storage",
      notes = "Delete refesh token on storage"
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "resource",
   value = "Resource for user authentication",
   dataType = "V2AuthenticationResource"
)})
   @DeleteMapping(
      produces = {"application/json"}
   )
   public ResponseEntity deleteRefreshToken(@RequestBody V2AuthenticationResource resource) throws Exception {
      V2AuthenticationResponse res = new V2AuthenticationResponse();
      String deletedToken = this.v2AuthenticationService.deleteRefreshToken(resource);
      res.setRefreshToken(deletedToken);
      return ResponseEntity.ok(res);
   }

   @GetMapping(
      value = {"/checkAuth"},
      produces = {"application/json"}
   )
   public ResponseEntity checkAuth() throws Exception {
      String useAuth = null;
      V2AuthServerInfo info = new V2AuthServerInfo();

      try {
         useAuth = CommonConfig.get("auth.server.use");
         if (useAuth != null && useAuth.equalsIgnoreCase("true")) {
            info.setUrl(CommonConfig.get("auth.server.url"));
            info.setClient_id(CommonConfig.get("auth.server.client.id"));
            info.setClient_secret(CommonConfig.get("auth.server.client.secret"));
            info.setRedirect_uri(CommonConfig.get("auth.server.client.redirecturl"));
            info.setResponse_type(CommonConfig.get("auth.server.client.responsetype"));
            info.setExit_uri(CommonConfig.get("auth.server.logout.url"));
         }
      } catch (ConfigException var4) {
         this.logger.error("", var4);
      }

      return ResponseEntity.ok(info);
   }

   @ApiOperation(
      value = "get auth device list",
      notes = "get auth device list"
   )
   @GetMapping(
      value = {"/devices/{userId}"},
      produces = {"application/json"}
   )
   public ResponseEntity getAuthDeviceList(@PathVariable String userId) throws Exception {
      new V2AuthenticationResponse();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      User loginUser = userContainer.getUser();
      if (!userId.equalsIgnoreCase(loginUser.getUser_id())) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, userId);
         if (!SecurityUtils.checkReadPermissionWithOrgAndId("User", userId)) {
            throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_NOT_HAVE, new String[]{"read"});
         }
      }

      if (userContainer == null) {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_USER_NOT_APPROVED);
      } else {
         UserInfo userInfo = UserInfoImpl.getInstance();
         List userAuthDeviceList = userInfo.getUserStoredDevice(userId);
         ResponseBody responseBody = new ResponseBody();
         HttpStatus status = HttpStatus.OK;
         responseBody.setStatus("Success");
         responseBody.setItems(userAuthDeviceList);
         responseBody.setTotalCount(userAuthDeviceList.size());
         responseBody.setApiVersion("2.0");
         return new ResponseEntity(responseBody, status);
      }
   }

   @ApiOperation(
      value = "delete auth device",
      notes = "delete auth device"
   )
   @DeleteMapping(
      value = {"/devices/{userId}/{authDeviceId}"},
      produces = {"application/json"}
   )
   public ResponseEntity deleteAuthDevice(@PathVariable String userId, @PathVariable int authDeviceId) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (userContainer == null) {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_USER_NOT_APPROVED);
      } else {
         User loginUser = userContainer.getUser();
         if (!userId.equalsIgnoreCase(loginUser.getUser_id())) {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, userId);
            if (!SecurityUtils.checkWritePermissionWithOrgAndId("User", userId)) {
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_NOT_HAVE, new String[]{"write"});
            }
         }

         UserInfo userInfo = UserInfoImpl.getInstance();
         int result = userInfo.deleteUserDevice(authDeviceId);
         if (result < 1) {
            throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_USER_NOT_APPROVED);
         } else {
            ResponseBody responseBody = new ResponseBody();
            responseBody.setApiVersion("2.0");
            V2CommonDeleteResult v2CommonDeleteResult = new V2CommonDeleteResult();
            List resultList = new ArrayList();
            resultList.add(authDeviceId + "");
            v2CommonDeleteResult.setDeletedSuccessList(resultList);
            responseBody.setItems(v2CommonDeleteResult);
            responseBody.setStatus("Success");
            responseBody.setErrorCode(ExceptionCode.HTTP200[0]);
            responseBody.setErrorMessage(ExceptionCode.HTTP200[2]);
            return new ResponseEntity(responseBody, HttpStatus.OK);
         }
      }
   }

   @ApiOperation(
      value = "Check password, otp",
      notes = "Check password or otp and check user information data when password or otp is entered in edit",
      authorizations = {@Authorization("api_key")}
   )
   @PostMapping(
      value = {"reconfirm"},
      produces = {"application/json"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "password or otp",
   value = "password",
   required = true,
   dataType = "String"
)})
   public ResponseEntity settingUserCheckPassword(@RequestBody V2AuthenticationResource resource, HttpServletRequest context) throws Exception {
      V2SettingServerService v2SettingServerService = new V2SettingServerServiceImpl();
      V2SettingServerResource settingResource = v2SettingServerService.getServerSettings();
      V2PasswordPolicySettings passwordPolicy = settingResource.getPasswordPolicy();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (userContainer == null) {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_USER_NOT_APPROVED);
      } else {
         Boolean check = false;
         if (passwordPolicy.getMfa_enable()) {
            AuthModel authModel = new AuthModel();
            authModel.setUserId(userContainer.getUser().getUser_id());
            AuthStrategyContext authContext = null;
            if (resource.getTotp() != null) {
               authModel.setOtp(Integer.parseInt(resource.getTotp()));
               authContext = new AuthStrategyContext(new TotpAuthStrategy());
               if (!authContext.valid(authModel)) {
                  throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_USER_NOT_APPROVED);
               }

               check = true;
            }
         } else {
            check = this.v2SettingMyAccountService.checkPassword(resource.getPassword());
         }

         if (!check) {
            throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_USER_NOT_APPROVED);
         } else {
            V2AuthenticationResponse res = this.v2AuthenticationService.refreshTokenAuthentication(resource, context);
            ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(res.getToken());
            return new ResponseEntity(responseBody, HttpStatus.OK);
         }
      }
   }

   @ApiOperation(
      value = "Send password token to e-mail",
      notes = "The password token is sent to the specified email."
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "resource",
   value = "Resource for password token.",
   dataType = "V2UserInfoResource4Password"
)})
   @PostMapping(
      value = {"/reset/{authType}"},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 404,
   message = "User Id or User email is not exist."
), @ApiResponse(
   code = 503,
   message = "Service Unavailable \n errorcode : 503900, SMTP information do not exists on the server settings."
)})
   public ResponseEntity sendValidationTokenToEmail(@PathVariable("authType") String authType, @Valid @RequestBody V2UserInfoResource4Password resource) throws Exception {
      resource.setAuthType(authType);
      V2UserInfoResource4Password ret = this.v2UserService.sendEmailForAuthenticationCode(resource);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(ret);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "reset - otp setup information",
      notes = "reset user's otp information"
   )
   @ApiImplicitParams({})
   @PostMapping(
      value = {"/user/reset/{authType}/{userId}"},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 404,
   message = "User Id is not exist."
), @ApiResponse(
   code = 503,
   message = "Service Unavailable \n errorcode : 503900, SMTP information do not exists on the server settings."
)})
   public ResponseEntity resetMfa(@PathVariable("authType") String authType, @NotNull @NotEmpty @PathVariable("userId") String userId) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (userContainer == null) {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_USER_NOT_APPROVED);
      } else {
         ResponseBody responseBody = null;

         try {
            User loginUser = userContainer.getUser();
            if (userId.equalsIgnoreCase(loginUser.getUser_id())) {
               responseBody = this.v2UserMfaService.resetMyMfa(userId);
            } else {
               if (!SecurityUtils.checkReadPermissionWithOrgAndId("User", userId)) {
                  throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
               }

               responseBody = this.v2UserMfaService.resetMfa(userId);
            }
         } catch (Exception var6) {
            this.logger.error("[REST_v2.0][USER][RESET]", var6);
            throw var6;
         }

         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Check if the password token is valid",
      notes = "Check if the password token is valid."
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "resource",
   value = "Resource for validation token.",
   dataType = "V2UserEncyptionTokenResource"
)})
   @PostMapping(
      value = {"/reset/valid/{authType}"},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \nerrorCode : 400729, The authentication code do not match.."
), @ApiResponse(
   code = 404,
   message = "Data not Found \nerrorCode : 404001, The authentication token is not found."
), @ApiResponse(
   code = 408,
   message = "Request timeout \nerrorCode : 408700, The authentication code has expired."
)})
   public ResponseEntity isValidationToken(@PathVariable("authType") String authType, @Valid @RequestBody V2UserEncyptionTokenResource resource) throws Exception {
      resource.setAuthType(authType);
      V2UserEncyptionTokenResource ret = this.v2UserService.isValidEmailAuthenticationCode(resource);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(ret);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "get secret key",
      notes = "get secret key",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/secretKey"},
      produces = {"application/json"}
   )
   public ResponseEntity getSecretKey() {
      String loginUserRole = ((User)Objects.requireNonNull(SecurityUtils.getLoginUser())).getRole_name();
      if (!"Server Administrator".equals(loginUserRole)) {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_NOT_SERVER_ADMINISTRATOR);
      } else {
         String secretKey = "";

         try {
            SecurityInfo securityInfo = SecurityInfoImpl.getInstance();
            secretKey = securityInfo.getSecretKey();
         } catch (Exception var4) {
            this.logger.error("", var4);
         }

         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(secretKey);
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   static enum GRANT_TYPE {
      PASSWORD("password"),
      REFRESH("refresh_token"),
      CODE("code"),
      USER_ID("user_id");

      private String type;

      private GRANT_TYPE(String type) {
         this.type = type;
      }

      public String getType() {
         return this.type;
      }
   }
}
