package com.samsung.magicinfo.restapi.contents.service;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.LeftMenuGroupTreeDao;
import com.samsung.magicinfo.framework.content.dao.ContentDao;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.entity.ShareFolder;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ShareFolderInfo;
import com.samsung.magicinfo.framework.content.manager.ShareFolderInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.restapi.common.model.V2CommonGroupDeletion;
import com.samsung.magicinfo.restapi.common.model.V2CommonGroupRename;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonOrganizationData;
import com.samsung.magicinfo.restapi.common.model.V2SearchGroup;
import com.samsung.magicinfo.restapi.contents.model.V2ContentGroupResource;
import com.samsung.magicinfo.restapi.contents.model.V2SharedGroupResource;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2ContentGroupService")
@Transactional
public class V2ContentGroupServiceImpl implements V2ContentGroupService {
   private final Logger logger = LoggingManagerV2.getLogger(V2ContentGroupServiceImpl.class);
   ContentInfo contentGroupDao = ContentInfoImpl.getInstance();
   ContentDao contentTreeDao = new ContentDao();
   LeftMenuGroupTreeDao treeDao = new LeftMenuGroupTreeDao();

   public V2ContentGroupServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Statistics Manage Authority', 'Content Read Authority', 'Content Write Authority', 'Content Manage Authority', 'Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Add Authority', 'Content Schedule Manage Authority')")
   public V2PageResource getGroups(String groupType, String sharedGroupId, String deviceType, String deviceTypeVersion, boolean isValidExpired, boolean isApproval, String mediaTypes) throws Exception {
      new ArrayList();
      User user = SecurityUtils.getLoginUser();
      String userId = null;
      if (user != null) {
         userId = user.getUser_id();
         List resources;
         Long myDefaultGroupId;
         if (groupType.equalsIgnoreCase("MY_CONTENT_GROUP")) {
            myDefaultGroupId = this.contentGroupDao.getRootId(userId);
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, myDefaultGroupId);
            resources = this.getMyContentGroups(groupType, deviceType, deviceTypeVersion, isValidExpired, isApproval, mediaTypes);
         } else if (groupType.equalsIgnoreCase("SHARED_GROUP")) {
            resources = this.getSharedContentGroups(groupType);
         } else if (groupType.equalsIgnoreCase("TEMPLATE_GROUP")) {
            myDefaultGroupId = this.contentGroupDao.getTLFDRootId(userId);
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.TLFD, myDefaultGroupId);
            resources = this.getTemplateGroups(groupType);
         } else if (groupType.equalsIgnoreCase("ORGANIZATION")) {
            resources = this.getByUserGroups(groupType, sharedGroupId);
         } else {
            myDefaultGroupId = this.contentGroupDao.getRootId(userId);
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, myDefaultGroupId);
            resources = this.getMyContentGroups(groupType);
         }

         return V2PageResource.createPageResource(resources, resources.size());
      } else {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_ACCESS_DENIED);
      }
   }

   @PreAuthorize("hasAnyAuthority('Statistics Manage Authority', 'Content Read Authority', 'Content Write Authority', 'Content Manage Authority', 'Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Add Authority', 'Content Schedule Manage Authority')")
   public V2PageResource getGroups(String groupType) throws Exception {
      return this.getGroups(groupType, (String)null, (String)null, (String)null, false, false, "ALL");
   }

   public List getByUserGroups(String groupType, String sharedGroupId) throws Exception {
      long id = 0L;
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      String pUserId = SecurityUtils.getLoginUserId();
      User pUser = userInfo.getAllByUserId(pUserId);
      List resources = new ArrayList();
      List list = new ArrayList();
      if (pUser.getRoot_group_id() == 0L) {
         if (!pUser.isMu()) {
            list = userGroupInfo.getChildGroupList(id, false);
            UserGroup userGroupForAdmin = userGroupInfo.getGroupById(id);
            resources.add(this.getContentGroupResource(userGroupForAdmin, groupType));
         } else if (StringUtils.isBlank(sharedGroupId)) {
            long mngOrgId = userInfo.getCurMngOrgId(SecurityUtils.getLoginUserId());
            UserGroup userGroup = userGroupInfo.getGroupById(mngOrgId);
            resources.add(this.getContentGroupResource(userGroup, groupType));
         } else {
            ShareFolderInfo shareFolderInfo = ShareFolderInfoImpl.getInstance();
            List fixedMngOrgIdList = shareFolderInfo.getOrgListForCurrentMultiOrg(Long.parseLong(sharedGroupId), pUser.getUser_id());
            List userManageGroupList = null;
            userManageGroupList = userGroupInfo.getMngGroupListByUserId(pUserId);
            List mngOrgIdList = new ArrayList();
            Iterator var15 = userManageGroupList.iterator();

            while(var15.hasNext()) {
               UserGroup user = (UserGroup)var15.next();
               if (user.getGroup_id().equals(userInfo.getCurMngOrgId(pUserId))) {
                  user.setIs_default("Y");
               } else {
                  user.setIs_default("N");
               }

               ((List)list).add(user);
               mngOrgIdList.add(user.getGroup_id());
            }

            fixedMngOrgIdList.removeAll(mngOrgIdList);
            var15 = fixedMngOrgIdList.iterator();

            while(var15.hasNext()) {
               long orgId = (Long)var15.next();
               UserGroup userGroups = userGroupInfo.getGroupById(orgId);
               userGroups.setIs_default("Y");
               ((List)list).add(userGroups);
            }
         }

         for(int i = 0; i < ((List)list).size(); ++i) {
            UserGroup userGroup = userGroupInfo.getGroupById(((UserGroup)((List)list).get(i)).getGroup_id());
            V2ContentGroupResource resource = new V2ContentGroupResource();
            resource.setOrganizationId(((UserGroup)((List)list).get(i)).getGroup_id());
            String groupName = ((UserGroup)((List)list).get(i)).getGroup_name();
            if (userGroup.getP_group_id() == 0L) {
               resource.setGroupName(groupName);
            } else if (((UserGroup)((List)list).get(i)).getGroup_id() == 0L) {
               resource.setGroupName("Administrators");
            }

            resource.setResponseDataType("CONTENT_ORGANIZATION");
            resource.setGroupId(((UserGroup)((List)list).get(i)).getGroup_id());
            resource.setGroupDepth(((UserGroup)((List)list).get(i)).getGroup_depth());
            resource.setOrganizationId(((UserGroup)((List)list).get(i)).getRoot_group_id());
            resource.setResourceCount(this.getContentCountByOrganizationId(((UserGroup)((List)list).get(i)).getRoot_group_id()));
            resource.setGroupType(groupType);
            resource.setParentGroupId(((UserGroup)((List)list).get(i)).getP_group_id());
            resource.setIndex(((UserGroup)((List)list).get(i)).getIndex());
            if ("admin".equals(pUserId)) {
               resource.setReason("N");
            } else {
               resource.setReason(((UserGroup)((List)list).get(i)).getIs_default());
            }

            resources.add(resource);
         }
      } else {
         V2ContentGroupResource resource = new V2ContentGroupResource();
         resource.setResponseDataType("CONTENT_ORGANIZATION");
         resource.setGroupName(pUser.getOrganization());
         resource.setOrganizationId(pUser.getRoot_group_id());
         resource.setResourceCount(this.getContentCountByOrganId(pUser.getRoot_group_id()));
         resource.setGroupType(groupType);
         resources.add(resource);
      }

      return resources;
   }

   private int getContentCountByOrganId(Long organizationId) throws Exception {
      int count = 0;
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      Map conditionMap = new HashMap();
      conditionMap.put("sortColumn", "user_id");
      conditionMap.put("sortOrder", "asc");
      conditionMap.put("searchText", "");
      List userList;
      if (organizationId != 0L) {
         userList = userGroupInfo.getChildGroupList(organizationId, true);
         List idList = new ArrayList();
         Iterator var9 = userList.iterator();

         while(var9.hasNext()) {
            UserGroup group = (UserGroup)var9.next();
            idList.add(group.getGroup_id());
         }

         conditionMap.put("groupIds", idList);
      } else {
         conditionMap.put("group_id", "0");
      }

      userList = userInfo.getGroupedUser(conditionMap, 0, 10000);
      List muUserList = userInfo.getMUInfoByMngOrgId(organizationId);
      if (muUserList != null && !muUserList.isEmpty()) {
         userList.addAll(muUserList);
      }

      for(int i = 0; i < userList.size(); ++i) {
         count += this.getContentCountByUserId(((User)userList.get(i)).getUser_id());
      }

      return count;
   }

   private int getContentCountByOrganizationId(Long organizationId) throws Exception {
      int count = false;
      ContentDao contentDao = new ContentDao();
      int count = contentDao.getCountByOrganId(organizationId);
      return count;
   }

   public List getMyContentGroups(String groupType, String deviceType, String deviceTypeVersion, boolean isValidExpired, boolean isApproval, String mediaTypes) throws Exception {
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      List groups = contentInfo.getGroupList(SecurityUtils.getLoginUserId());
      List resources = new ArrayList();
      Iterator var10 = groups.iterator();

      while(var10.hasNext()) {
         Group group = (Group)var10.next();
         resources.add(this.getContentGroupResource(group, groupType, deviceType, deviceTypeVersion, isValidExpired, isApproval, mediaTypes));
      }

      this.updateResourceCount(resources);
      return resources;
   }

   public void updateResourceCount(List groupList) {
      int size = groupList.size();

      for(int i = 1; i < size; ++i) {
         V2ContentGroupResource res = (V2ContentGroupResource)groupList.get(i);
         int count = res.getResourceCount();
         count += this.totalResourceCount(groupList, res.getGroupId());
         res.setResourceCount(count);
      }

   }

   public int totalResourceCount(List groupList, Long groupId) {
      int count = 0;
      int size = groupList.size();

      for(int i = 1; i < size; ++i) {
         V2ContentGroupResource res = (V2ContentGroupResource)groupList.get(i);
         if (res.getParentGroupId() == groupId) {
            count += res.getResourceCount();
            count += this.totalResourceCount(groupList, res.getGroupId());
         }
      }

      return count;
   }

   public List getMyContentGroups(String groupType) throws Exception {
      return this.getMyContentGroups(groupType, (String)null, (String)null, false, false, "ALL");
   }

   public List getMyContentSubgroups(String groupId, String groupType) throws Exception {
      List resources = new ArrayList();
      User user = SecurityUtils.getLoginUser();
      if (groupId != null && !groupId.equals("0") && !groupId.equals("")) {
         new ArrayList();
         List groups;
         if (user.getGroup_name().equalsIgnoreCase("ROOT")) {
            groups = this.contentGroupDao.getSubGroupList(Long.valueOf(groupId), false, (Long)null);
         } else {
            long userOrgId = user.getRoot_group_id();
            groups = this.contentGroupDao.getSubGroupList(Long.valueOf(groupId), false, userOrgId);
         }

         if (groups != null) {
            for(int i = 0; i < groups.size(); ++i) {
               Group group = (Group)groups.get(i);
               resources.add(this.getContentGroupResource(group, groupType));
            }
         }
      }

      return resources;
   }

   public List getSharedContentGroups(String groupType) throws Exception {
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      User user = SecurityUtils.getLoginUser();
      long mngOrgId = -1L;
      int count = false;
      if (user.isMu()) {
         UserInfo userInfo = UserInfoImpl.getInstance();
         mngOrgId = userInfo.getCurMngOrgId(user.getUser_id());
      } else {
         mngOrgId = user.getRoot_group_id();
      }

      ContentDao contentDao = new ContentDao();
      List list = contentInfo.getShareFolderList(mngOrgId);
      List resources = new ArrayList();
      Map condition = new HashMap();
      condition.put("listType", "SHAREFOLDER");
      condition.put("isMain", "true");

      for(int i = 0; i < list.size(); ++i) {
         ShareFolder shareFolder = (ShareFolder)list.get(i);
         long shareFolderId = ((ShareFolder)list.get(i)).getShare_Folder_id();
         condition.put("share_folder_id", shareFolderId);
         int count = contentDao.getContentListCnt(condition);
         V2ContentGroupResource resource = new V2ContentGroupResource();
         resource.setResponseDataType("CONTENT_GROUP");
         resource.setGroupId(shareFolder.getShare_Folder_id());
         resource.setGroupType(groupType);
         resource.setGroupName(shareFolder.getShare_Folder_name());
         resource.setResourceCount(count);
         resources.add(resource);
      }

      return resources;
   }

   public List getTemplateGroups(String groupType) throws Exception {
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      User user = SecurityUtils.getLoginUser();
      List list = contentInfo.getTLFDGroupList(user.getUser_id());
      List resources = new ArrayList();

      for(int i = 0; i < list.size(); ++i) {
         if (((Group)list.get(i)).getP_group_id() < 0L) {
            resources.add(this.getContentGroupResource((Group)list.get(i), groupType));
         }
      }

      return resources;
   }

   public List getTemplateSubgroups(String groupId, String groupType) throws Exception {
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      User user = SecurityUtils.getLoginUser();
      List resources = new ArrayList();
      if (groupId != null && !groupId.equals(String.valueOf(0L))) {
         List list = contentInfo.getTLFDChildGroupList(Long.valueOf(groupId), false, user.getUser_id());

         for(int i = 0; i < list.size(); ++i) {
            resources.add(this.getContentGroupResource((Group)list.get(i), groupType));
         }
      }

      return resources;
   }

   private V2ContentGroupResource getContentGroupResource(Group group, String groupType, String deviceType, String deviceTypeVersion, boolean isValidExpiration, boolean isApprove, String mediaTypes) throws SQLException {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      User user = userContainer.getUser();
      Map condition = new HashMap();
      ContentDao contentDao = new ContentDao();
      V2ContentGroupResource resource = new V2ContentGroupResource();
      int count = false;
      condition.put("content_type", "CONTENT");
      condition.put("listType", "GROUPED");
      condition.put("creatorID", user.getUser_id());
      condition.put("canReadUnshared", true);
      condition.put("groupID", group.getGroup_id() + "");
      condition.put("isMain", "true");
      if (deviceType != null && deviceTypeVersion != null) {
         condition.put("deviceType", deviceType);
         condition.put("deviceTypeVersion", deviceTypeVersion);
      }

      if (isValidExpiration) {
         condition.put("expirationStatusFilter", "valid_content");
      }

      if (isApprove) {
         condition.put("contentApprovalFilter", "approval_content");
      }

      if (mediaTypes != null && !mediaTypes.equals("") && !mediaTypes.equalsIgnoreCase("ALL")) {
         String[] contentFilterList = mediaTypes.split(",");
         condition.put("contentFilter", contentFilterList);
      }

      if ("TEMPLATE_GROUP".equalsIgnoreCase(groupType)) {
         condition.put("isTLFD", "Y");
         condition.remove("creatorID");
         Long parentGroupId = group.getP_group_id();
         if (parentGroupId == -1L) {
            resource.setResponseDataType("TEMPLATE_ORGANIZATION");
         } else {
            resource.setResponseDataType("TEMPLATE_GROUP");
         }
      } else {
         resource.setResponseDataType("CONTENT_GROUP");
      }

      int count = contentDao.getContentListCnt(condition);
      resource.setCreateDate(group.getCreate_date());
      resource.setCreatorId(group.getCreator_id());
      resource.setGroupDepth(group.getGroup_depth());
      resource.setGroupId(group.getGroup_id());
      resource.setGroupType(groupType);
      resource.setGroupName(group.getGroup_name());
      resource.setIndex(group.getIndex());
      resource.setOrganizationId(group.getOrganization_id());
      resource.setParentGroupId(group.getP_group_id());
      resource.setResourceCount(count);
      return resource;
   }

   private V2ContentGroupResource getContentGroupResource(Group group, String groupType, boolean isValidExpiration, boolean isApproval, String mediaTypes) throws SQLException {
      return this.getContentGroupResource(group, groupType, (String)null, (String)null, isValidExpiration, isApproval, mediaTypes);
   }

   private V2ContentGroupResource getContentGroupResource(Group group, String groupType) throws SQLException {
      return this.getContentGroupResource(group, groupType, (String)null, (String)null, false, false, "ALL");
   }

   private V2ContentGroupResource getContentGroupResource(UserGroup group, String groupType) throws Exception {
      V2ContentGroupResource resource = new V2ContentGroupResource();
      resource.setResponseDataType("CONTENT_ORGANIZATION");
      resource.setGroupId(group.getGroup_id());
      resource.setGroupName(group.getGroup_name());
      resource.setGroupDepth(group.getGroup_depth());
      resource.setOrganizationId(group.getRoot_group_id());
      resource.setResourceCount(this.getContentCountByOrganId(group.getRoot_group_id()));
      resource.setGroupType(groupType);
      resource.setParentGroupId(group.getP_group_id());
      resource.setIndex(group.getIndex());
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2ContentGroupResource getGroup(String groupId, String groupType) throws Exception {
      long id = Long.valueOf(groupId);
      if (!StrUtils.nvl(groupId).equals("")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, id);
      }

      V2ContentGroupResource resource = new V2ContentGroupResource();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      User user = SecurityUtils.getLoginUser();
      Group group;
      if (groupType.equals("TEMPLATE_GROUP")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.TLFD, id);
         group = contentInfo.getTLFDGroupInfo(id);
         resource = this.getContentGroupResource(group, groupType);
      } else if (groupType.equals("ORGANIZATION")) {
         UserGroup group = userGroupInfo.getGroupById(id);
         resource = this.getContentGroupResource(group, groupType);
      } else if (groupType.equals("MY_CONTENT_GROUP")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, id);
         group = this.contentGroupDao.getGroupInfo(id);
         resource = this.getContentGroupResource(group, groupType);
      } else if (groupType.equals("SHARED_GROUP")) {
         long mngOrgId = -1L;
         mngOrgId = user.getRoot_group_id();
         List list = this.contentGroupDao.getShareFolderList(mngOrgId);
         Iterator var12 = list.iterator();

         while(var12.hasNext()) {
            ShareFolder data = (ShareFolder)var12.next();
            if (data.getShare_Folder_id() == Long.valueOf(groupId)) {
               resource.setResponseDataType("CONTENT_GROUP");
               resource.setGroupId(data.getShare_Folder_id());
               resource.setGroupName(data.getShare_Folder_name());
               break;
            }
         }
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public List getSubgroups(String groupId, String groupType) throws Exception {
      new ArrayList();
      List resources;
      if (groupType.equalsIgnoreCase("MY_CONTENT_GROUP")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(groupId));
         resources = this.getMyContentSubgroups(groupId, groupType);
      } else if (groupType.equalsIgnoreCase("TEMPLATE_GROUP")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.TLFD, Long.parseLong(groupId));
         resources = this.getTemplateSubgroups(groupId, groupType);
      } else {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(groupId));
         resources = this.getMyContentSubgroups(groupId, groupType);
      }

      return resources;
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public V2ContentGroupResource createGroup(Group group, String groupType) throws Exception {
      String parentGroupId = group.getP_group_id().toString();
      String newName = group.getGroup_name();
      User user = SecurityUtils.getLoginUser();
      String userId = null;
      if (user != null) {
         userId = user.getUser_id();
         V2ContentGroupResource resource = new V2ContentGroupResource();
         resource.setGroupType(groupType);
         int length;
         if (!groupType.equalsIgnoreCase("MY_CONTENT_GROUP") && !groupType.equalsIgnoreCase("TEMPLATE_GROUP")) {
            if (groupType.equalsIgnoreCase("SHARED_GROUP")) {
               if (user.getRoot_group_id() != 0L && !user.isMu()) {
                  RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
               }

               if (newName != null && !newName.isEmpty()) {
                  length = newName.length();
                  if (100 < length) {
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_EXCEEDED_MAX_GROUP_NAME_LENGTH);
                  }
               }

               ShareFolderInfo shareFolderInfo = ShareFolderInfoImpl.getInstance();
               long folderId = shareFolderInfo.addNewShareFolder(newName);
               if (folderId < 0L) {
                  RestExceptionCode error = RestExceptionCode.INTERNAL_SERVER_ERROR_GROUP_CREATE_FAIL;
                  resource.setStatus("Fail");
                  resource.setReason(error.generateFormattedMessages("share"));
                  resource.setReasonCode(error.getCode());
                  return resource;
               } else {
                  resource.setStatus("Success");
                  resource.setGroupName(newName);
                  resource.setGroupId(folderId);
                  return resource;
               }
            } else {
               return resource;
            }
         } else {
            if (groupType.equalsIgnoreCase("MY_CONTENT_GROUP")) {
               RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(parentGroupId));
            } else {
               RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.TLFD, Long.parseLong(parentGroupId));
            }

            if (newName != null && !newName.isEmpty()) {
               length = newName.length();
               if (100 < length) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_EXCEEDED_MAX_GROUP_NAME_LENGTH);
               }
            }

            Long root_group_id = 0L;
            String menu;
            String table;
            if (groupType.equalsIgnoreCase("MY_CONTENT_GROUP")) {
               table = "MI_CMS_INFO_CONTENT_GROUP";
               menu = "MyContent";
               root_group_id = this.contentTreeDao.getRoot_GroupId(parentGroupId);
            } else {
               table = "MI_CMS_INFO_TLFD_GROUP";
               menu = "TLFDGROUP";
               root_group_id = this.contentTreeDao.getTLFDRoot_GroupId(parentGroupId);
            }

            long depth = this.treeDao.get_GroupDepth(parentGroupId, table);
            int group_id = this.treeDao.setGroupTreeCreate(menu, table, parentGroupId, newName, depth + "", root_group_id, userId);
            resource.setStatus("Success");
            resource.setGroupName(newName);
            resource.setGroupId((long)group_id);
            resource.setParentGroupId(Long.parseLong(parentGroupId));
            return resource;
         }
      } else {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_ACCESS_DENIED);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2CommonGroupDeletion deleteGroup(String groupId, String parentGroupId, String groupType) throws Exception {
      User user = SecurityUtils.getLoginUser();
      V2CommonGroupDeletion deletedGroup = new V2CommonGroupDeletion();
      if (!groupType.equalsIgnoreCase("MY_CONTENT_GROUP") && !groupType.equalsIgnoreCase("TEMPLATE_GROUP")) {
         if (groupType.equalsIgnoreCase("SHARED_GROUP")) {
            if (user.getRoot_group_id() != 0L && !user.isMu()) {
               RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
            }

            ShareFolderInfo shareFolderInfo = ShareFolderInfoImpl.getInstance();
            boolean checkDelete = true;
            if (user.isMu()) {
               UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
               List list = new ArrayList();
               List sharedOrgList = shareFolderInfo.getOrgListByShareFolderId(Long.parseLong(groupId));
               List userOrgList = userGroupInfo.getMngGroupListByUserId(user.getUser_id());
               List userOrgIds = new ArrayList();
               Iterator var30 = userOrgList.iterator();

               while(var30.hasNext()) {
                  UserGroup users = (UserGroup)var30.next();
                  userOrgIds.add(users.getGroup_id());
                  list.add(users.getGroup_id());
                  sharedOrgList.remove(users.getGroup_id());
               }

               var30 = sharedOrgList.iterator();

               while(var30.hasNext()) {
                  long orgId = (Long)var30.next();
                  list.add(orgId);
               }

               if (!list.equals(userOrgIds)) {
                  checkDelete = false;
               }
            }

            boolean result = false;
            if (checkDelete) {
               result = shareFolderInfo.deleteShareFolderByShareFolderId(Long.valueOf(groupId));
            }

            if (result) {
               deletedGroup.setGroupId(Long.parseLong(groupId));
               deletedGroup.setStatus("Success");
               return deletedGroup;
            } else {
               RestExceptionCode error;
               if (!checkDelete) {
                  error = RestExceptionCode.INTERNAL_SERVER_ERROR_SHARED_GROUP_DELETE_FAIL;
               } else {
                  error = RestExceptionCode.INTERNAL_SERVER_ERROR_GROUP_DELETE_FAIL;
               }

               deletedGroup.setGroupId(Long.parseLong(groupId));
               deletedGroup.setStatus("Fail");
               deletedGroup.setReason(error.getMessage());
               deletedGroup.setReasonCode(error.getCode());
               return deletedGroup;
            }
         } else {
            RestExceptionCode error = RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID;
            deletedGroup.setGroupId(Long.parseLong(groupId));
            deletedGroup.setStatus("Fail");
            deletedGroup.setReason(error.generateFormattedMessages("groupType"));
            deletedGroup.setReasonCode(error.getCode());
            return deletedGroup;
         }
      } else {
         if (groupType.equalsIgnoreCase("MY_CONTENT_GROUP")) {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(groupId));
         } else {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.TLFD, Long.parseLong(groupId));
         }

         UserContainer userContainer = SecurityUtils.getUserContainer();
         int iGroupId = Integer.parseInt(groupId);
         long lParentGroupId = -1L;
         String userOrg = userContainer.getUser().getOrganization();
         if (iGroupId == -1) {
            RestExceptionCode error = RestExceptionCode.BAD_REQUEST_CANNOT_DELETE_ROOT_GROUP;
            deletedGroup.setGroupId(Long.parseLong(groupId));
            deletedGroup.setStatus("Fail");
            deletedGroup.setReason(error.getMessage());
            deletedGroup.setReasonCode(error.getCode());
            return deletedGroup;
         } else {
            LeftMenuGroupTreeDao tree_dao = new LeftMenuGroupTreeDao();
            if (parentGroupId != null) {
               ContentInfo contentInfo = ContentInfoImpl.getInstance();
               if ("#".equals(parentGroupId)) {
                  Group group = contentInfo.getGroupInfo((long)iGroupId);
                  lParentGroupId = group.getP_group_id();
               } else {
                  lParentGroupId = Long.parseLong(parentGroupId);
               }

               RestExceptionCode error;
               if (lParentGroupId == -1L) {
                  error = RestExceptionCode.BAD_REQUEST_CANNOT_DELETE_ROOT_GROUP;
                  deletedGroup.setGroupId(Long.parseLong(groupId));
                  deletedGroup.setStatus("Fail");
                  deletedGroup.setReason(error.getMessage());
                  deletedGroup.setReasonCode(error.getCode());
                  return deletedGroup;
               }

               if (!groupType.equalsIgnoreCase("TEMPLATE_GROUP") && contentInfo.checkMappingSchedule((long)iGroupId)) {
                  error = RestExceptionCode.BAD_REQUEST_CANNOT_DELETE_CONTAINS_REFERENCED_ITEMS_IN_GROUP;
                  deletedGroup.setGroupId(Long.parseLong(groupId));
                  deletedGroup.setStatus("Fail");
                  deletedGroup.setReason(error.generateFormattedMessages("contents"));
                  deletedGroup.setReasonCode(error.getCode());
                  return deletedGroup;
               }
            }

            String table = "";
            String menu = "";
            if (groupType.equalsIgnoreCase("TEMPLATE_GROUP")) {
               table = "MI_CMS_INFO_TLFD_GROUP";
               menu = "TLFDGROUP";
            } else {
               table = "MI_CMS_INFO_CONTENT_GROUP";
               menu = "MyContent";
            }

            tree_dao.setGroupTreeRemove(menu, table, userOrg, groupId, user.getUser_id(), (HttpServletRequest)null, (HttpServletResponse)null);
            deletedGroup.setGroupId(Long.parseLong(groupId));
            deletedGroup.setStatus("Success");
            return deletedGroup;
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2CommonGroupRename renameGroup(String groupId, V2ContentGroupResource body) throws Exception {
      V2CommonGroupRename group = new V2CommonGroupRename();
      String groupType = body.getGroupType();
      String newName = body.getGroupName();
      User user = SecurityUtils.getLoginUser();
      int length;
      if (!groupType.equalsIgnoreCase("MY_CONTENT_GROUP") && !groupType.equalsIgnoreCase("TEMPLATE_GROUP")) {
         if (groupType.equalsIgnoreCase("SHARED_GROUP")) {
            if (user.getRoot_group_id() != 0L && !user.isMu()) {
               RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
            }

            if (newName != null && !newName.isEmpty()) {
               length = newName.length();
               if (100 < length) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_EXCEEDED_MAX_GROUP_NAME_LENGTH);
               }
            }

            ShareFolderInfo shareFolderInfo = ShareFolderInfoImpl.getInstance();
            boolean result = shareFolderInfo.renameShareFolderName(Long.parseLong(groupId), body.getGroupName());
            if (result) {
               group.setNewGroupName(body.getGroupName());
               group.setStatus("Success");
               return group;
            } else {
               RestExceptionCode error = RestExceptionCode.INTERNAL_SERVER_ERROR_GROUP_RENAME_FAIL;
               group.setStatus("Fail");
               group.setReason(error.generateFormattedMessages("share"));
               group.setReasonCode(error.getCode());
               return group;
            }
         } else {
            RestExceptionCode error = RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID;
            group.setStatus("Fail");
            group.setReason(error.generateFormattedMessages("groupType"));
            group.setReasonCode(error.getCode());
            return group;
         }
      } else {
         if (groupType.equalsIgnoreCase("MY_CONTENT_GROUP")) {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(groupId));
         } else {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.TLFD, Long.parseLong(groupId));
         }

         if (newName != null && !newName.isEmpty()) {
            length = newName.length();
            if (100 < length) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_EXCEEDED_MAX_GROUP_NAME_LENGTH);
            }
         }

         String table = "";
         String menu = "";
         if (groupType.equalsIgnoreCase("TEMPLATE_GROUP")) {
            table = "MI_CMS_INFO_TLFD_GROUP";
            menu = "TLFDGROUP";
         } else {
            table = "MI_CMS_INFO_CONTENT_GROUP";
            menu = "MyContent";
         }

         LeftMenuGroupTreeDao tree_dao = new LeftMenuGroupTreeDao();
         if (user != null) {
            String userId = user.getUser_id();
            long group_id = Long.parseLong(groupId);
            if (group_id <= 0L) {
               RestExceptionCode error = RestExceptionCode.BAD_REQUEST_CANNOT_DELETE_ROOT_GROUP;
               group.setStatus("Fail");
               group.setReason(error.getMessage());
               group.setReasonCode(error.getCode());
               return group;
            } else {
               tree_dao.setGroupTreeRename(menu, table, groupId, body.getGroupName(), userId);
               ContentInfo contentGroupDao = ContentInfoImpl.getInstance();
               contentGroupDao.getGroupInfo(group_id);
               group.setNewGroupName(body.getGroupName());
               group.setStatus("Success");
               return group;
            }
         } else {
            throw new RestServiceException(RestExceptionCode.FORBIDDEN_ACCESS_DENIED);
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2ContentGroupResource moveGroup(Long groupId, Long destinationGroupId, V2ContentGroupResource body) throws Exception {
      V2ContentGroupResource group = new V2ContentGroupResource();
      String table = "";
      String menu = "";
      String groupType = body.getGroupType();
      String parentGroupId = String.valueOf(destinationGroupId);
      if (groupType.equalsIgnoreCase("TEMPLATE_GROUP")) {
         table = "MI_CMS_INFO_TLFD_GROUP";
         menu = "TLFDGROUP";
         Long gId = this.contentGroupDao.getTLFDOrganizationIdByGroupId(groupId);
         Long pgId = this.contentGroupDao.getTLFDOrganizationIdByGroupId(destinationGroupId);
         if (gId != pgId) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_HAVE_TO_MOVE_SAME_ORGANIZATION);
         }
      } else {
         if (!groupType.equalsIgnoreCase("MY_CONTENT_GROUP")) {
            RestExceptionCode error = RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID;
            group.setGroupId(groupId);
            group.setStatus("Fail");
            group.setReason(error.generateFormattedMessages("groupType"));
            group.setReasonCode(error.getCode());
            return group;
         }

         table = "MI_CMS_INFO_CONTENT_GROUP";
         menu = "MyContent";
      }

      LeftMenuGroupTreeDao tree_dao = new LeftMenuGroupTreeDao();
      if ("#".equals(parentGroupId)) {
         RestExceptionCode error = RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID;
         group.setGroupId(groupId);
         group.setStatus("Fail");
         group.setReason(error.generateFormattedMessages("destinationGroupId."));
         group.setReasonCode(error.getCode());
         return group;
      } else {
         long depth = tree_dao.get_GroupDepth(parentGroupId, table);
         String strGroupId = String.valueOf(groupId);
         boolean result = tree_dao.setGroupTreeMove(menu, table, strGroupId, depth + "", parentGroupId);
         if (result) {
            group.setGroupId(groupId);
            group.setGroupName(body.getGroupName());
            group.setGroupType(groupType);
            group.setParentGroupId(Long.parseLong(parentGroupId));
            group.setGroupDepth(depth);
            group.setStatus("Success");
            return group;
         } else {
            RestExceptionCode error = RestExceptionCode.INTERNAL_SERVER_ERROR_GROUP_MOVE_FAIL;
            group.setGroupId(groupId);
            group.setStatus("Fail");
            group.setReason(error.getMessage());
            group.setReasonCode(error.getCode());
            return group;
         }
      }
   }

   public List getContentGroupBySearchText(String searchText, String groupType) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      List groupList = new ArrayList();
      long organizationId = userGroupInfo.getOrgGroupIdByName(userContainer.getUser().getOrganization());
      User user = SecurityUtils.getLoginUser();
      String userId = null;
      if (user != null) {
         userId = user.getUser_id();
         searchText = StrUtils.nvl(searchText);
         searchText = searchText.replaceAll("\\[", "^[");
         searchText = searchText.replaceAll("]", "^]");
         searchText = searchText.replaceAll("%", "^%");
         new ArrayList();
         List group;
         if (groupType.equals("TEMPLATE_GROUP")) {
            group = cmsDao.getTLFDGroupBySearch(searchText, organizationId);
         } else {
            group = cmsDao.getContentGroupBySearch(searchText, organizationId, userId);
         }

         if (group != null) {
            for(int i = 0; i < group.size(); ++i) {
               V2SearchGroup resource = new V2SearchGroup();
               if (((Group)group.get(i)).getP_group_id() > 0L) {
                  new ArrayList();
                  long pearentGroupId = ((Group)group.get(i)).getP_group_id();
                  int pGroupId = (int)pearentGroupId;
                  List lists = cmsDao.getParentsGroupList(pGroupId);
                  resource.setParentsGroupIds(lists);
               }

               resource.setCreateDate(((Group)group.get(i)).getCreate_date());
               resource.setCreatorId(((Group)group.get(i)).getCreator_id());
               resource.setGroupDepth(((Group)group.get(i)).getGroup_depth());
               resource.setGroupId(((Group)group.get(i)).getGroup_id());
               resource.setGroupName(((Group)group.get(i)).getGroup_name());
               resource.setOrganizationId(((Group)group.get(i)).getOrganization_id());
               resource.setpGroupId(((Group)group.get(i)).getP_group_id());
               groupList.add(resource);
            }
         }

         return groupList;
      } else {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
      }
   }

   public List userGroupList(String userId, Long orgId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, userId);
      UserInfo userInfo = UserInfoImpl.getInstance();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      ContentDao contentDao = new ContentDao();
      int count = false;
      List resources = new ArrayList();
      if (userId != null && !userId.equals("")) {
         User byUser = userInfo.getAllByUserId(userId);
         Long userOrgId = userInfo.getRootGroupIdByUserId(userId);
         List lists;
         if (userOrgId != -1L && byUser.isMu()) {
            if (orgId != null) {
               List userList = userInfo.getMUInfoByMngOrgId(orgId);
               if (!userList.stream().anyMatch((user) -> {
                  return user.getUser_id().equals(byUser.getUser_id());
               })) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_ACCESS_DENIED);
               }

               userOrgId = orgId;
            }

            contentInfo.addDefaultGroup(userId, userOrgId);
            lists = contentInfo.getGroupList(userId, userOrgId);
         } else {
            if (orgId != null && !orgId.equals(byUser.getRoot_group_id())) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_ACCESS_DENIED);
            }

            lists = contentInfo.getGroupList(userId);
         }

         Map condition = new HashMap();
         condition.put("content_type", "CONTENT");
         condition.put("listType", "GROUPED");
         condition.put("creatorID", userId);
         condition.put("canReadUnshared", true);
         condition.put("isMain", "true");

         for(int i = 0; i < lists.size(); ++i) {
            if (((Group)lists.get(i)).getGroup_depth() <= 1L) {
               condition.put("groupID", ((Group)lists.get(i)).getGroup_id() + "");
               int count = contentDao.getContentListCnt(condition);
               V2ContentGroupResource resource = this.getContentGroupResource((Group)((Group)lists.get(i)), (String)null);
               resource.setResponseDataType("CONTENT_GROUP");
               resource.setUserId(userId);
               resource.setUserOrganizationId(String.valueOf(userOrgId));
               resource.setOrganizationId(userOrgId);
               resource.setResourceCount(count);
               resources.add(resource);
            }
         }

         return resources;
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_ENTER_ID);
      }
   }

   private int getContentCountByUserId(String userId) throws Exception {
      ContentDao contentDao = new ContentDao();
      Map condition = new HashMap();
      condition.put("content_type", "CONTENT");
      condition.put("listType", "USER");
      condition.put("creatorID", userId);
      condition.put("canReadUnshared", true);
      condition.put("isMain", "true");
      return contentDao.getContentListCnt(condition);
   }

   private int getContentCountByUserId(String userId, Long organizationId) throws Exception {
      ContentDao contentDao = new ContentDao();
      Map condition = new HashMap();
      condition.put("content_type", "CONTENT");
      condition.put("listType", "USER");
      condition.put("creatorID", userId);
      condition.put("organizationId", organizationId);
      condition.put("canReadUnshared", true);
      condition.put("isMain", "true");
      return contentDao.getContentListCnt(condition);
   }

   public List orgUserList(String organizationId) throws Exception {
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, Long.valueOf(organizationId));
      List resources = new ArrayList();
      Map conditionMap = new HashMap();
      conditionMap.put("sortColumn", "user_id");
      conditionMap.put("sortOrder", "asc");
      conditionMap.put("searchText", "");
      List userList;
      if (!organizationId.equals("0")) {
         userList = userGroupInfo.getChildGroupList(Long.parseLong(organizationId), true);
         List idList = new ArrayList();
         Iterator var8 = userList.iterator();

         while(var8.hasNext()) {
            UserGroup group = (UserGroup)var8.next();
            idList.add(group.getGroup_id());
         }

         conditionMap.put("groupIds", idList);
      } else {
         conditionMap.put("group_id", "0");
      }

      userList = userInfo.getGroupedUser(conditionMap, 0, 10000);
      List muUserList = userInfo.getMUInfoByMngOrgId(Long.parseLong(organizationId));
      if (muUserList != null && !muUserList.isEmpty()) {
         userList.addAll(muUserList);
      }

      for(int i = 0; i < userList.size(); ++i) {
         if (!((User)userList.get(i)).isMu() || !organizationId.equals("0")) {
            V2ContentGroupResource resource = new V2ContentGroupResource();
            resource.setResponseDataType("CONTENT_USER");
            resource.setUserId(((User)userList.get(i)).getUser_id());
            resource.setOrganizationId(Long.valueOf(organizationId));
            resource.setResourceCount(this.getContentCountByUserId(((User)userList.get(i)).getUser_id(), Long.valueOf(organizationId)));
            resource.setUserOrganizationId(organizationId);
            resources.add(resource);
         }
      }

      return resources;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2SharedGroupResource sharedGroupOrgList(String shareFolderId) throws Exception {
      V2SharedGroupResource resource = new V2SharedGroupResource();
      ShareFolderInfo shareFolderInfo = ShareFolderInfoImpl.getInstance();
      String shareFolderName = shareFolderInfo.getShareFolderNameByShareFolderId(Long.valueOf(shareFolderId));
      List sharedOrgList = shareFolderInfo.getOrgListByShareFolderId(Long.valueOf(shareFolderId));
      List fixedMngOrgIdList = shareFolderInfo.getOrgListByShareFolderId(Long.valueOf(shareFolderId));
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      List allOrgList = userGroupInfo.getAllOrganizationGroup();
      User user = SecurityUtils.getLoginUser();
      if (user.isMu()) {
         allOrgList.clear();
         List userManageGroupList = null;
         userManageGroupList = userGroupInfo.getMngGroupListByUserId(user.getUser_id());
         if (userManageGroupList == null) {
            throw new RestServiceException(RestExceptionCode.FORBIDDEN_SHARE_FOLDER_MU);
         }

         List mngOrgIdList = new ArrayList();
         Iterator var13 = userManageGroupList.iterator();

         while(var13.hasNext()) {
            UserGroup userGroup = (UserGroup)var13.next();
            LinkedHashMap mngGroup = new LinkedHashMap();
            mngGroup.put("group_id", userGroup.getGroup_id());
            mngGroup.put("group_name", userGroup.getGroup_name());
            mngGroup.put("isFixed", "N");
            allOrgList.add(mngGroup);
            mngOrgIdList.add(userGroup.getGroup_id());
         }

         fixedMngOrgIdList.removeAll(mngOrgIdList);
         var13 = fixedMngOrgIdList.iterator();

         while(var13.hasNext()) {
            long orgId = (Long)var13.next();
            LinkedHashMap mngGroup = new LinkedHashMap();
            String groupName = userGroupInfo.getGroupNameByGroupId(orgId);
            mngGroup.put("group_id", orgId);
            mngGroup.put("group_name", groupName);
            mngGroup.put("isFixed", "Y");
            allOrgList.add(mngGroup);
         }
      }

      ArrayList resultOrgList = new ArrayList();
      Iterator var19 = allOrgList.iterator();

      while(var19.hasNext()) {
         Map orgGroup = (Map)var19.next();
         V2CommonOrganizationData data = new V2CommonOrganizationData();
         Long organizationId = (Long)orgGroup.get("group_id");
         String organizationName = userInfo.getOrganGroupName(organizationId);
         if (sharedOrgList.contains(organizationId)) {
            data.setOrganizationName(organizationName);
            data.setOrganizationId(organizationId);
            resultOrgList.add(data);
         }
      }

      resource.setShareFolderName(shareFolderName);
      resource.setShareFolderId(Long.valueOf(shareFolderId));
      resource.setCurOrgId(SecurityUtils.getLoginUser().getRoot_group_id());
      resource.setOrganizationDataList(resultOrgList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2SharedGroupResource editSharedGroup(String shareFolderId, @Valid V2CommonIds body) throws Exception {
      V2SharedGroupResource resource = new V2SharedGroupResource();
      ShareFolderInfo shareFolderInfo = ShareFolderInfoImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      String shareFolderName = shareFolderInfo.getShareFolderNameByShareFolderId(Long.valueOf(shareFolderId));
      List organizationIds = body.getIds();
      List dataList = new ArrayList();
      dataList.add(shareFolderId);
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      List list = userGroupInfo.getAllOrganizationGroup();
      List orgDataList = new ArrayList();

      for(int i = 0; i < organizationIds.size(); ++i) {
         boolean organIdCheck = false;

         for(int j = 0; j < list.size(); ++j) {
            Map orgInfoMap = (Map)list.get(j);
            if (((String)organizationIds.get(i)).toString().equalsIgnoreCase(orgInfoMap.get("group_id").toString())) {
               organIdCheck = true;
            }
         }

         if (!organIdCheck) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"OrganizationID"});
         }

         dataList.add(((String)organizationIds.get(i)).toString());
         V2CommonOrganizationData orgData = new V2CommonOrganizationData();
         orgData.setOrganizationId(Long.valueOf((String)organizationIds.get(i)));
         orgData.setOrganizationName(userInfo.getOrganGroupName(Long.valueOf((String)organizationIds.get(i))));
         orgDataList.add(orgData);
      }

      Boolean ret = shareFolderInfo.editShareFolder(dataList);
      if (ret) {
         resource.setShareFolderName(shareFolderName);
         resource.setShareFolderId(Long.valueOf(shareFolderId));
         resource.setOrganizationDataList(orgDataList);
         return resource;
      } else {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SHARE_FOLDER_UPDATE);
      }
   }
}
