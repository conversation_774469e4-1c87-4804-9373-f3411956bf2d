package com.samsung.magicinfo.rc.common.exception;

public class OpenApiExceptionCode {
  public static final String[] SUCCESS = new String[] { "0", "Success" };
  
  public static final String[] INVALIDE_DEVICE = new String[] { "1000", "Does not invalid the DeviceId." };
  
  public static final String[] INVALIDE_MATCH = new String[] { "1001", "Does not match the number of parameters." };
  
  public static final String[] INVALIDE_TOKEN = new String[] { "1002", "Does not match the token." };
  
  public static final String[] INVALIDE_SERVICE = new String[] { "1003", "Does not match the service method." };
  
  public static final String[] NOT_USE_SERVICE = new String[] { "1004", "Does not use the service." };
  
  public static final String[] DONT_HAVE_TOKEN = new String[] { "1005", "Does not have a token." };
  
  public static final String[] INVALID_VALUE_PARAMETER = new String[] { "1006", "Invalid value for parameter." };
  
  public static final String[] SERVICE_CALLED_ERROR = new String[] { "1007", "Service clled is not Open the method." };
  
  public static final String[] ENTER_SERVICE_XML = new String[] { "1008", "During requesting, please enter service or xml variable." };
  
  public static final String[] DONT_HAVE_DEVICE_ID_IN_QUEEUE = new String[] { "1009", "Don't have the device id in queue. Please start service for device id." };
  
  public static final String[] NOT_FOUND_DEVICE_INFORMATION = new String[] { "2000", "Not found Client information." };
  
  public static final String[] ALREADY_DISCONNECTION = new String[] { "2001", "Already disconnection." };
  
  public static final String[] INVALID_INTERVAL_VALUE = new String[] { "2002", "Invalid interval value." };
  
  public static final String[] INTERNAL_ERROR_NOSUCHMETHOD_EX = new String[] { "2003", "Internal Error (NoSuchMethodException)." };
  
  public static final String[] INTERNAL_ERROR_INVOCATION_EX = new String[] { "2004", "Internal Error (InvocationTargetException)." };
  
  public static final String[] INTERNAL_ERROR_ILLEGAL_EX = new String[] { "2005", "Internal Error (IllegalAccessException)." };
  
  public static final String[] START_SERVICE_ALREADY_START = new String[] { "2006", "Start service was already started." };
  
  public static final String[] XML_WRITE_ERROR = new String[] { "2007", "Response XML error occurred during writing." };
  
  public static final String[] NOT_INPUT_SERVICE = new String[] { "5000", "Does not input the service." };
  
  public static final String[] INTERNAL_UNKNOWN_ERROR = new String[] { "6000", "Unkown Error!" };
}
