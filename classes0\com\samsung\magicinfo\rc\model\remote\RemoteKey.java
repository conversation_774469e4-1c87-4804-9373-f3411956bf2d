package com.samsung.magicinfo.rc.model.remote;

import org.springframework.stereotype.Component;

@Component
public class RemoteKey {
  public static final String CH1 = "1";
  
  public static final String CH2 = "2";
  
  public static final String CH3 = "3";
  
  public static final String CH4 = "4";
  
  public static final String CH5 = "5";
  
  public static final String CH6 = "6";
  
  public static final String CH7 = "7";
  
  public static final String CH8 = "8";
  
  public static final String CH9 = "9";
  
  public static final String CH0 = "0";
  
  public static final String POWER = "power";
  
  public static final String OFF = "poweroff";
  
  public static final String SOURCE = "source";
  
  public static final String CHUP = "chup";
  
  public static final String CHDOWN = "chdown";
  
  public static final String VOLUP = "volup";
  
  public static final String VOLDOWN = "voldown";
  
  public static final String DEL = "del";
  
  public static final String CHLIST = "chlist";
  
  public static final String MUTE = "mute";
  
  public static final String MENU = "menu";
  
  public static final String HOME = "home";
  
  public static final String MAGICINFO = "magicinfo";
  
  public static final String TOOLS = "tools";
  
  public static final String DRT_UP = "drt_up";
  
  public static final String INFO = "info";
  
  public static final String DRT_LEFT = "drt_left";
  
  public static final String DRT_OK = "drt_ok";
  
  public static final String DRT_RIGHT = "drt_right";
  
  public static final String RETURN = "return";
  
  public static final String DOWN = "down";
  
  public static final String EXIT = "exit";
  
  public static final String A = "a";
  
  public static final String B = "b";
  
  public static final String C = "c";
  
  public static final String D = "d";
  
  public static final String SET = "set";
  
  public static final String UNSET = "unset";
  
  public static final String LOCK = "lock";
  
  public static final String STOP = "stop";
  
  public static final String REW = "rew";
  
  public static final String PLAY = "play";
  
  public static final String PAUSE = "pause";
  
  public static final String PREV = "prev";
  
  public static final String FACTORY = "factory";
  
  public String getRemoteCode(String keyCode) {
    String code = null;
    if (keyCode == null)
      return null; 
    if (keyCode.equals("1")) {
      code = "04";
    } else if (keyCode.equals("2")) {
      code = "05";
    } else if (keyCode.equals("3")) {
      code = "06";
    } else if (keyCode.equals("4")) {
      code = "08";
    } else if (keyCode.equals("5")) {
      code = "09";
    } else if (keyCode.equals("6")) {
      code = "0A";
    } else if (keyCode.equals("7")) {
      code = "0C";
    } else if (keyCode.equals("8")) {
      code = "0D";
    } else if (keyCode.equals("9")) {
      code = "0E";
    } else if (keyCode.equals("0")) {
      code = "11";
    } else if (keyCode.equals("power")) {
      code = "02";
    } else if (keyCode.equals("poweroff")) {
      code = "98";
    } else if (keyCode.equals("source")) {
      code = "01";
    } else if (keyCode.equals("chup")) {
      code = "12";
    } else if (keyCode.equals("chdown")) {
      code = "10";
    } else if (keyCode.equals("volup")) {
      code = "07";
    } else if (keyCode.equals("voldown")) {
      code = "0B";
    } else if (keyCode.equals("del")) {
      code = "23";
    } else if (keyCode.equals("chlist")) {
      code = "8E";
    } else if (keyCode.equals("mute")) {
      code = "0F";
    } else if (keyCode.equals("menu")) {
      code = "1A";
    } else if (keyCode.equals("home")) {
      code = "79";
    } else if (keyCode.equals("magicinfo")) {
      code = "30";
    } else if (keyCode.equals("tools")) {
      code = "4B";
    } else if (keyCode.equals("drt_up")) {
      code = "60";
    } else if (keyCode.equals("info")) {
      code = "1F";
    } else if (keyCode.equals("drt_left")) {
      code = "65";
    } else if (keyCode.equals("drt_right")) {
      code = "62";
    } else if (keyCode.equals("drt_ok")) {
      code = "68";
    } else if (keyCode.equals("return")) {
      code = "58";
    } else if (keyCode.equals("down")) {
      code = "61";
    } else if (keyCode.equals("exit")) {
      code = "2D";
    } else if (keyCode.equals("a")) {
      code = "6C";
    } else if (keyCode.equals("b")) {
      code = "14";
    } else if (keyCode.equals("c")) {
      code = "15";
    } else if (keyCode.equals("d")) {
      code = "16";
    } else if (keyCode.equals("set")) {
      code = "";
    } else if (keyCode.equals("unset")) {
      code = "";
    } else if (keyCode.equals("lock")) {
      code = "77";
    } else if (keyCode.equals("stop")) {
      code = "46";
    } else if (keyCode.equals("rew")) {
      code = "45";
    } else if (keyCode.equals("play")) {
      code = "47";
    } else if (keyCode.equals("pause")) {
      code = "4A";
    } else if (keyCode.equals("prev")) {
      code = "48";
    } else if (keyCode.equals("factory")) {
      code = "3B";
    } else {
      code = "NULL";
    } 
    return code;
  }
}
