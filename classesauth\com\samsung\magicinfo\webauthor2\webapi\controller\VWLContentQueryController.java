package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.VWLContent;
import com.samsung.magicinfo.webauthor2.service.VWLContentService;
import com.samsung.magicinfo.webauthor2.webapi.assembler.ContentResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.assembler.VWLContentResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.resource.ContentResource;
import com.samsung.magicinfo.webauthor2.webapi.resource.VWLContentResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.PagedResourcesAssembler;
import org.springframework.hateoas.PagedResources;
import org.springframework.hateoas.ResourceAssembler;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/VWLContents"})
public class VWLContentQueryController {
  private final VWLContentService vwlContentService;
  
  private final VWLContentResourceAssembler vwlContentResourceAssembler;
  
  private final ContentResourceAssembler contentResourceAssembler;
  
  @Autowired
  public VWLContentQueryController(VWLContentService vwlContentService, VWLContentResourceAssembler vwlContentResourceAssembler, ContentResourceAssembler contentResourceAssembler) {
    this.vwlContentService = vwlContentService;
    this.vwlContentResourceAssembler = vwlContentResourceAssembler;
    this.contentResourceAssembler = contentResourceAssembler;
  }
  
  @GetMapping
  public HttpEntity<PagedResources<ContentResource>> getContentsWithVWLMediaType(@PageableDefault(size = 100) Pageable pageable, @RequestParam String playerType, PagedResourcesAssembler<Content> assembler) {
    Page<Content> page = this.vwlContentService.getVWLContentList(pageable, DeviceType.valueOf(playerType));
    PagedResources<ContentResource> contentResources = assembler.toResource(page, (ResourceAssembler)this.contentResourceAssembler);
    return (HttpEntity<PagedResources<ContentResource>>)ResponseEntity.ok(contentResources);
  }
  
  @RequestMapping(value = {"/{vwlContentId}"}, method = {RequestMethod.GET})
  public HttpEntity<VWLContentResource> getVWLContent(@PathVariable String vwlContentId) {
    VWLContent content = this.vwlContentService.getVWLContent(vwlContentId);
    if (content == null)
      return (HttpEntity<VWLContentResource>)ResponseEntity.notFound().build(); 
    VWLContentResource contentResources = this.vwlContentResourceAssembler.toResource(content);
    return (HttpEntity<VWLContentResource>)ResponseEntity.ok(contentResources);
  }
}
