package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.samsung.magicinfo.webauthor2.model.datalink.ConvertTable;
import com.samsung.magicinfo.webauthor2.model.datalink.DLKData;
import com.samsung.magicinfo.webauthor2.model.datalink.DLKDataType;
import com.samsung.magicinfo.webauthor2.model.datalink.TagListDynamicData;
import com.samsung.magicinfo.webauthor2.model.datalink.ValueLocation;

public class DynamicDLKData extends DLKData {
  private final String serverName;
  
  private final ValueLocation valueLocation;
  
  private final TagListDynamicData tagList;
  
  private ConvertTable convertTable;
  
  @JsonCreator
  public DynamicDLKData(@JsonProperty("serverName") String serverName, @JsonProperty("valueLocation") ValueLocation valueLocation, @JsonProperty("tagList") TagListDynamicData tagList, @JsonProperty("convertTable") ConvertTable convertTable) {
    super(DLKDataType.Dynamic);
    this.serverName = serverName;
    this.valueLocation = valueLocation;
    this.tagList = tagList;
    this.convertTable = convertTable;
  }
  
  public String getServerName() {
    return this.serverName;
  }
  
  public TagListDynamicData getTagList() {
    return this.tagList;
  }
  
  public ConvertTable getConvertTable() {
    return this.convertTable;
  }
  
  public void setConvertTable(ConvertTable convertTable) {
    this.convertTable = convertTable;
  }
  
  public ValueLocation getValueLocation() {
    return this.valueLocation;
  }
}
