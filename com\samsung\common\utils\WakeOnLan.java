package com.samsung.common.utils;

import com.samsung.common.logger.LoggingManagerV2;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.Inet4Address;
import java.net.InetAddress;
import org.apache.logging.log4j.Logger;

public class WakeOnLan {
   Logger logger = LoggingManagerV2.getLogger(WakeOnLan.class);
   public final int PORT_7 = 7;
   public final int PORT_9 = 9;

   public WakeOnLan() {
      super();
   }

   public void wol(String ipStr, String subnetStr, String macStr) {
      String broadCastIp = "";

      try {
         byte[] macBytes = this.getMacBytes(macStr);
         byte[] bytes = new byte[6 + 16 * macBytes.length];

         int i;
         for(i = 0; i < 6; ++i) {
            bytes[i] = -1;
         }

         for(i = 6; i < bytes.length; i += macBytes.length) {
            System.arraycopy(macBytes, 0, bytes, i, macBytes.length);
         }

         if (subnetStr == null || subnetStr.equals("")) {
            subnetStr = "*************";
         }

         broadCastIp = this.getBroadcastAddress(ipStr, subnetStr);
         InetAddress address = InetAddress.getByName(broadCastIp);
         DatagramPacket packet1 = new DatagramPacket(bytes, bytes.length, address, 7);
         DatagramPacket packet2 = new DatagramPacket(bytes, bytes.length, address, 9);
         DatagramSocket socket = new DatagramSocket();

         int i;
         for(i = 0; i < 10; ++i) {
            socket.send(packet1);
         }

         for(i = 0; i < 10; ++i) {
            socket.send(packet2);
         }

         socket.close();
         Thread.sleep(10L);
         this.logger.error("Wake-on-LAN packet port 7 and 9  sent to " + broadCastIp + "(" + macStr + ").");
      } catch (Exception var12) {
         this.logger.error("Failed to send Wake-on-LAN packet to " + broadCastIp + "(" + macStr + ") : " + var12);
      }

   }

   private byte[] getMacBytes(String macStr) throws IllegalArgumentException {
      byte[] bytes = new byte[6];
      String[] hex = macStr.split("(\\:|\\-)");
      if (hex.length != 6) {
         throw new IllegalArgumentException("Invalid MAC address.");
      } else {
         try {
            for(int i = 0; i < 6; ++i) {
               bytes[i] = (byte)Integer.parseInt(hex[i], 16);
            }

            return bytes;
         } catch (NumberFormatException var5) {
            throw new IllegalArgumentException("Invalid hex digit in MAC address.");
         }
      }
   }

   private String getBroadcastAddress(String ipaddress, String subnetMask) {
      String broadCastIP = null;

      try {
         InetAddress ip = (Inet4Address)InetAddress.getByName(ipaddress);
         Inet4Address netmask = (Inet4Address)InetAddress.getByName(subnetMask);
         InetAddress net2 = getBroadcast((InetAddress)ip, (InetAddress)netmask);
         broadCastIP = net2.getHostAddress();
      } catch (Exception var7) {
         broadCastIP = null;
      }

      return broadCastIP;
   }

   public static Inet4Address getBroadcast(InetAddress ip, InetAddress mask) {
      return (Inet4Address)getBroadcast(ip.getAddress(), mask.getAddress());
   }

   private static InetAddress getBroadcast(byte[] ip, byte[] mask) {
      try {
         return InetAddress.getByAddress(new byte[]{(byte)(~mask[0] | ip[0]), (byte)(~mask[1] | ip[1]), (byte)(~mask[2] | ip[2]), (byte)(~mask[3] | ip[3])});
      } catch (Exception var3) {
         return null;
      }
   }
}
