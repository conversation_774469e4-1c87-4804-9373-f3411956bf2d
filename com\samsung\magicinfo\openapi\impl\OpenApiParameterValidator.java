package com.samsung.magicinfo.openapi.impl;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.Effect;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import java.io.File;
import java.io.StringReader;
import java.net.URL;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import javax.xml.transform.Source;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import javax.xml.validation.Validator;
import org.apache.logging.log4j.Logger;

public class OpenApiParameterValidator {
   static Logger logger = LoggingManagerV2.getLogger(OpenApiParameterValidator.class);
   public static final Locale locale;
   public static final String DEFAULT_GROUP = "default";
   public static final int DEFAULT_SHARE_FLAG = 1;
   public ArrayList arrLocaleType = null;
   public ArrayList arrPremiumIPlayerPlaylistContentType = null;
   public ArrayList arrPremiumSPlayerPlaylistContentType = null;
   public ArrayList arrSupportEffectContentType = null;

   public OpenApiParameterValidator() {
      super();
      this.arrLocaleType = new ArrayList();
      this.arrPremiumIPlayerPlaylistContentType = new ArrayList();
      this.arrPremiumSPlayerPlaylistContentType = new ArrayList();
      this.arrSupportEffectContentType = new ArrayList();
      this.arrPremiumIPlayerPlaylistContentType.add("IMAGE");
      this.arrPremiumIPlayerPlaylistContentType.add("MOVIE");
      this.arrPremiumIPlayerPlaylistContentType.add("SOUND");
      this.arrPremiumIPlayerPlaylistContentType.add("LFD");
      this.arrPremiumIPlayerPlaylistContentType.add("OFFICE");
      this.arrPremiumIPlayerPlaylistContentType.add("FLASH");
      this.arrPremiumIPlayerPlaylistContentType.add("PDF");
      this.arrPremiumIPlayerPlaylistContentType.add("FTP");
      this.arrPremiumIPlayerPlaylistContentType.add("CIFS");
      this.arrPremiumSPlayerPlaylistContentType.add("IMAGE");
      this.arrPremiumSPlayerPlaylistContentType.add("MOVIE");
      this.arrPremiumSPlayerPlaylistContentType.add("LFD");
      this.arrPremiumSPlayerPlaylistContentType.add("OFFICE");
      this.arrPremiumSPlayerPlaylistContentType.add("FLASH");
      this.arrPremiumSPlayerPlaylistContentType.add("PDF");
      this.arrSupportEffectContentType.add("IMAGE");
      this.arrSupportEffectContentType.add("MOVIE");
      this.arrSupportEffectContentType.add("FTP");
      this.arrSupportEffectContentType.add("CIFS");
      this.arrLocaleType.add("de_DE");
      this.arrLocaleType.add("en_US");
      this.arrLocaleType.add("es_ES");
      this.arrLocaleType.add("fr_FR");
      this.arrLocaleType.add("it_IT");
      this.arrLocaleType.add("ja_JP");
      this.arrLocaleType.add("ko_KR");
      this.arrLocaleType.add("pt_PT");
      this.arrLocaleType.add("ru_RU");
      this.arrLocaleType.add("sv_SE");
      this.arrLocaleType.add("tr_TR");
      this.arrLocaleType.add("zh_TW");
   }

   public String isValidator(String xml, String type) {
      String result = null;
      xml = "<?xml version='1.0' encoding='UTF-8'?>" + xml;
      xml = xml.replace("<" + type + ">", "<" + type + " xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance' xsi:noNamespaceSchemaLocation='" + type + ".xsd'>");

      try {
         URL url = Thread.currentThread().getContextClassLoader().getResource("/../conf/openapi_xsd/" + type + ".xsd");
         if (url == null) {
            return null;
         }

         String xsdPath = url.getPath();
         xsdPath = xsdPath.replaceAll("%20", " ");
         logger.debug("xsdPath = " + xsdPath);
         File schemaXsdFile = new File(xsdPath);
         if (!schemaXsdFile.exists()) {
            logger.debug("[ It is not existed ] xsdPath = " + xsdPath);
            return null;
         }

         SchemaFactory sf = DocumentUtils.getSchemaFactoryInstance();
         Source schemaFile = new StreamSource(schemaXsdFile);
         Schema schema = sf.newSchema(schemaFile);
         Validator validator = schema.newValidator();
         StreamSource ss = new StreamSource(new StringReader(xml));
         validator.validate(ss);
      } catch (Exception var12) {
         logger.error("", var12);
         result = var12.getMessage();
      }

      return result;
   }

   public boolean checkOrganName(String organName) throws OpenApiServiceException {
      boolean result = true;
      if (organName != null && !organName.trim().equals("")) {
         if (organName.trim().length() >= 1 && organName.trim().length() <= 20) {
            return result;
         } else {
            throw new OpenApiServiceException(OpenApiExceptionCode.G124[0], OpenApiExceptionCode.G124[1]);
         }
      } else {
         throw new OpenApiServiceException(OpenApiExceptionCode.G119[0], OpenApiExceptionCode.G119[1]);
      }
   }

   public boolean checkOrganAdmin(String organName, User newAdmin) throws OpenApiServiceException {
      boolean result = false;
      if (!newAdmin.getOrganization().equals(organName)) {
         throw new OpenApiServiceException(OpenApiExceptionCode.G121[0], OpenApiExceptionCode.G121[1]);
      } else if (!newAdmin.getGroup_name().equals("default")) {
         throw new OpenApiServiceException(OpenApiExceptionCode.G120[0], OpenApiExceptionCode.G120[1]);
      } else if (!newAdmin.getRole_name().equals("Administrator")) {
         throw new OpenApiServiceException(OpenApiExceptionCode.U108[0], OpenApiExceptionCode.U108[1]);
      } else {
         result = true;
         return result;
      }
   }

   public boolean isContentManagerOver(UserContainer user) {
      boolean result = false;
      boolean canReadAuth1 = user.checkAuthority("Content Read");
      boolean canReadAuth2 = user.checkAuthority("Content Write");
      boolean canReadAuth3 = user.checkAuthority("Content Manage");
      result = canReadAuth1 && canReadAuth2 && canReadAuth3;
      return result;
   }

   public boolean isPremiumPlaylistManagerOver(UserContainer user) {
      boolean result = false;
      boolean canReadAuth1 = user.checkAuthority("Playlist Read");
      boolean canReadAuth2 = user.checkAuthority("Playlist Write");
      boolean canReadAuth3 = user.checkAuthority("Playlist Manage");
      result = canReadAuth1 && canReadAuth2 && canReadAuth3;
      return result;
   }

   public boolean isLitePlaylistManagerOver(UserContainer user) {
      boolean result = false;
      boolean canReadAuth1 = user.checkAuthority("Lite Playlist Read");
      boolean canReadAuth2 = user.checkAuthority("Lite Playlist Write");
      boolean canReadAuth3 = user.checkAuthority("Lite Playlist Manage");
      result = canReadAuth1 && canReadAuth2 && canReadAuth3;
      return result;
   }

   public boolean isUserAllManager(UserContainer user) {
      boolean result = false;
      boolean canReadAuth1 = user.checkAuthority("User Read");
      boolean canReadAuth2 = user.checkAuthority("User Write");
      boolean canReadAuth3 = user.checkAuthority("User Approval");
      result = canReadAuth1 && canReadAuth2 && canReadAuth3;
      if (result && user.getUser().getRoot_group_id() == 0L) {
         result = true;
      } else {
         result = false;
      }

      return result;
   }

   public boolean isUserManagerOver(UserContainer user) {
      boolean result = false;
      boolean canReadAuth1 = user.checkAuthority("User Read");
      boolean canReadAuth2 = user.checkAuthority("User Write");
      boolean canReadAuth3 = user.checkAuthority("User Approval");
      result = canReadAuth1 && canReadAuth2 && canReadAuth3;
      return result;
   }

   public boolean isLiteVersion() {
      boolean result = false;

      try {
         SlmLicenseManager slmLicenseDao = SlmLicenseManagerImpl.getInstance();
         if (slmLicenseDao.getLicenseCountByProductCode("010311") > 0 && slmLicenseDao.getLicenseCountByProductCode("010121") < 1) {
            result = true;
         }
      } catch (SQLException var3) {
         logger.debug(var3.getMessage());
      }

      return result;
   }

   public boolean isLocale(String paramLocale) {
      boolean result = false;
      if (this.arrLocaleType.contains(paramLocale)) {
         result = true;
      }

      return result;
   }

   public boolean checkGroupName(String groupName) {
      boolean result = false;
      if (groupName.length() > 0 && groupName.length() <= 20) {
         result = true;
      }

      return result;
   }

   public boolean checkDeviceUpdateInterval(int interval) {
      boolean result = false;
      if (interval >= 3 && interval <= 300) {
         result = true;
      }

      return result;
   }

   public boolean checkMonitoringInterval(int interval) {
      boolean result = false;
      if (interval >= 1 && interval <= 300) {
         result = true;
      }

      return result;
   }

   public boolean checkPeriod(String start_date, String end_date) throws OpenApiServiceException {
      boolean result = true;
      Calendar calendarEndDate = Calendar.getInstance();
      Calendar calendarStartDate = Calendar.getInstance();
      Calendar calendarToday = Calendar.getInstance();
      String[] endDateInput = end_date.split("-");
      String[] startDateInput = start_date.split("-");
      if (startDateInput.length == 3) {
         calendarStartDate.set(Integer.parseInt(startDateInput[0]), Integer.parseInt(startDateInput[1]) - 1, Integer.parseInt(startDateInput[2]));
         if (endDateInput.length == 3) {
            calendarEndDate.set(Integer.parseInt(endDateInput[0]), Integer.parseInt(endDateInput[1]) - 1, Integer.parseInt(endDateInput[2]));
            Date todayDate = calendarToday.getTime();
            Date startDate = calendarStartDate.getTime();
            Date endDate = calendarEndDate.getTime();
            if (startDate.after(endDate)) {
               throw new OpenApiServiceException(OpenApiExceptionCode.V102[0], OpenApiExceptionCode.V102[1]);
            } else if (endDate.after(todayDate)) {
               throw new OpenApiServiceException(OpenApiExceptionCode.V103[0], OpenApiExceptionCode.V103[1]);
            } else {
               return result;
            }
         } else {
            throw new OpenApiServiceException(OpenApiExceptionCode.V002[0], OpenApiExceptionCode.V002[1] + " - endDate (yyyy-mm-dd)");
         }
      } else {
         throw new OpenApiServiceException(OpenApiExceptionCode.V002[0], OpenApiExceptionCode.V002[1] + " - startDate (yyyy-mm-dd)");
      }
   }

   public boolean checkPremiumPlaylistEffect(PlaylistContent playlistContent, String deviceType) throws OpenApiServiceException {
      boolean result = true;
      ContentInfoImpl cmsDao = ContentInfoImpl.getInstance();

      try {
         Content content = null;

         try {
            content = cmsDao.getContentActiveVerInfo(playlistContent.getContent_id());
         } catch (Exception var13) {
            logger.error(var13);
         }

         if (content == null) {
            throw new OpenApiServiceException(OpenApiExceptionCode.C101[0], OpenApiExceptionCode.C101[1]);
         }

         if (deviceType.equals("iPLAYER") && !this.arrPremiumIPlayerPlaylistContentType.contains(content.getMedia_type())) {
            throw new OpenApiServiceException(OpenApiExceptionCode.P110[0], OpenApiExceptionCode.P110[1]);
         }

         if (deviceType.equals("SPLAYER") && !this.arrPremiumSPlayerPlaylistContentType.contains(content.getMedia_type())) {
            throw new OpenApiServiceException(OpenApiExceptionCode.P112[0], OpenApiExceptionCode.P112[1]);
         }

         if (!this.arrSupportEffectContentType.contains(content.getMedia_type())) {
            playlistContent.setEffect_in_name("");
            playlistContent.setEffect_in_duration(0L);
            playlistContent.setEffect_in_direction("");
            playlistContent.setEffect_out_name("");
            playlistContent.setEffect_out_duration(0L);
            playlistContent.setEffect_out_direction("");
         } else {
            int j;
            Effect inputEffect;
            Map effectDirectionMap;
            if (deviceType.equals("iPLAYER")) {
               PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();
               String[] arrEffectName = new String[]{playlistContent.getEffect_in_name(), playlistContent.getEffect_out_name()};
               Long[] arrEffectDuration = new Long[]{playlistContent.getEffect_in_duration(), playlistContent.getEffect_out_duration()};
               String[] arrEffectDirection = new String[]{playlistContent.getEffect_in_direction(), playlistContent.getEffect_out_direction()};

               for(j = 0; j < 2; ++j) {
                  if (arrEffectName[j] != null && arrEffectName[j].trim().length() > 0) {
                     inputEffect = pInfo.getEffectInfoByEffectName(arrEffectName[j]);
                     if (inputEffect == null) {
                        throw new OpenApiServiceException(OpenApiExceptionCode.P105[0], OpenApiExceptionCode.P105[1] + " - " + arrEffectName[j]);
                     }

                     if (arrEffectDuration[j] <= 0L) {
                        throw new OpenApiServiceException(OpenApiExceptionCode.P106[0], OpenApiExceptionCode.P106[1] + " - " + arrEffectName[j]);
                     }

                     if (inputEffect.getDirection_type() != null && !inputEffect.getDirection_type().trim().equals("")) {
                        effectDirectionMap = inputEffect.getDirection_type_map();
                        effectDirectionMap.put("-1", "");
                        if (!effectDirectionMap.containsKey(arrEffectDirection[j])) {
                           throw new OpenApiServiceException(OpenApiExceptionCode.P107[0], OpenApiExceptionCode.P107[1] + " - " + arrEffectName[j]);
                        }
                     } else if (arrEffectDirection[j] != null && arrEffectDirection[j].trim().length() > 0) {
                        throw new OpenApiServiceException(OpenApiExceptionCode.P107[0], OpenApiExceptionCode.P107[1] + " - " + arrEffectName[j]);
                     }
                  }
               }
            } else if (deviceType.equals("SPLAYER")) {
               playlistContent.setEffect_out_name("");
               playlistContent.setEffect_out_duration(0L);
               playlistContent.setEffect_out_direction("");
               String[] arrEffectName = new String[]{playlistContent.getEffect_in_name()};
               Long[] arrEffectDuration = new Long[]{playlistContent.getEffect_in_duration()};
               String[] arrEffectDirection = new String[]{playlistContent.getEffect_in_direction()};
               PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();

               for(j = 0; j < 1; ++j) {
                  if (arrEffectName[j] != null && arrEffectName[j].trim().length() > 0) {
                     inputEffect = pInfo.getSocEffectInfoByEffectName(arrEffectName[j]);
                     if (inputEffect == null) {
                        throw new OpenApiServiceException(OpenApiExceptionCode.P105[0], OpenApiExceptionCode.P105[1] + " - " + arrEffectName[j]);
                     }

                     if (arrEffectDuration[j] <= 0L) {
                        throw new OpenApiServiceException(OpenApiExceptionCode.P106[0], OpenApiExceptionCode.P106[1] + " - " + arrEffectName[j]);
                     }

                     if (inputEffect.getDirection_type() != null && !inputEffect.getDirection_type().trim().equals("")) {
                        effectDirectionMap = inputEffect.getDirection_type_map();
                        effectDirectionMap.put("-1", "");
                        if (!effectDirectionMap.containsKey(arrEffectDirection[j])) {
                           throw new OpenApiServiceException(OpenApiExceptionCode.P107[0], OpenApiExceptionCode.P107[1] + " - " + arrEffectName[j]);
                        }
                     } else if (arrEffectDirection[j] != null && arrEffectDirection[j].trim().length() > 0) {
                        throw new OpenApiServiceException(OpenApiExceptionCode.P107[0], OpenApiExceptionCode.P107[1] + " - " + arrEffectName[j]);
                     }
                  }
               }
            }
         }
      } catch (SQLException var14) {
         logger.error(var14);
      }

      return result;
   }

   public boolean checkVWLPlaylistEffect(PlaylistContent playlistContent) throws OpenApiServiceException {
      boolean result = true;
      ContentInfoImpl cmsDao = ContentInfoImpl.getInstance();

      try {
         Content content = null;

         try {
            content = cmsDao.getContentActiveVerInfo(playlistContent.getContent_id());
         } catch (Exception var10) {
            logger.error(var10);
         }

         if (content == null) {
            throw new OpenApiServiceException(OpenApiExceptionCode.C101[0], OpenApiExceptionCode.C101[1]);
         }

         PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();
         String[] arrEffectName = new String[]{playlistContent.getEffect_in_name(), playlistContent.getEffect_out_name()};
         Long[] arrEffectDuration = new Long[]{playlistContent.getEffect_in_duration(), playlistContent.getEffect_out_duration()};

         for(int j = 0; j < 2; ++j) {
            if (arrEffectName[j] != null && arrEffectName[j].trim().length() > 0) {
               Effect inputEffect = pInfo.getVWLEffectInfoByEffectName(arrEffectName[j]);
               if (inputEffect == null) {
                  throw new OpenApiServiceException(OpenApiExceptionCode.P105[0], OpenApiExceptionCode.P105[1] + " - " + arrEffectName[j]);
               }

               if (arrEffectDuration[j] <= 0L) {
                  throw new OpenApiServiceException(OpenApiExceptionCode.P106[0], OpenApiExceptionCode.P106[1] + " - " + arrEffectName[j]);
               }
            }
         }
      } catch (SQLException var11) {
         logger.error(var11);
      }

      return result;
   }

   static {
      locale = Locale.ENGLISH;
   }
}
