package com.samsung.magicinfo.framework.user.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.magicinfo.auth.security.otp.UserAuthDevice;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.role.manager.RoleInfo;
import com.samsung.magicinfo.framework.role.manager.RoleInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.entity.UserSearch;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserSearchInfo;
import com.samsung.magicinfo.framework.user.manager.UserSearchInfoImpl;
import com.samsung.magicinfo.protocol.util.BeanUtils;
import com.samsung.magicinfo.protocol.util.dao.ServerSetupDao;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManager;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManagerImpl;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class UserDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(UserDao.class);

   public UserDao() {
      super();
   }

   public UserDao(SqlSession session) {
      super(session);
   }

   public User getAllByUserId(String userId) throws SQLException {
      User userInfo = ((UserDaoMapper)this.getMapper()).getAllByUserId(userId);
      if (userInfo == null) {
         if (UserInfoImpl.getInstance().unapprovedUser(userId) == null) {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RESET_PASSWORD_USER_NOT_EXIST);
         } else {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RESET_PASSWORD_USER_NOT_APPROVED);
         }
      } else {
         PIIDataManager piiDataManager = (PIIDataManagerImpl)BeanUtils.getBean("PIIDataManager");
         userInfo.setUser_name(piiDataManager.decryptData(userInfo.getUser_name()));
         userInfo.setMobile_num(piiDataManager.decryptData(userInfo.getMobile_num()));
         userInfo.setPhone_num(piiDataManager.decryptData(userInfo.getPhone_num()));
         userInfo.setEmail(piiDataManager.decryptData(userInfo.getEmail()));
         return userInfo;
      }
   }

   public int getCountByUserId(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getCountByUserId(userId);
   }

   public int getCountByUserIdIsDeleted(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getCountByUserIdIsDeleted(userId);
   }

   public int getCountByUserIdForCheck(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getCountByUserIdForCheck(userId);
   }

   public int getCountByLDAPUserFullIdForCheck(String ldapFullId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getCountByLDAPUserFullIdForCheck(ldapFullId);
   }

   public int getCountByLDAPUserIdForCheck(String ldapUserId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getCountByLDAPUserIdForCheck(ldapUserId);
   }

   public List getAllUser(Map map, int startPos, int pageSize) throws SQLException {
      try {
         Map paramsMap = new HashMap();
         paramsMap.putAll(map);
         UserSearch userSearch = null;
         if (map.get("search_id") != null && !map.get("search_id").equals("") && !((String)map.get("search_id")).equalsIgnoreCase("null")) {
            Long search_id = Long.parseLong((String)map.get("search_id"));
            userSearch = this.getWhereClauseByCustomSearch(search_id);
         }

         String searchText = (String)map.get("searchText");
         if (searchText != null) {
            StringBuilder sb = (new StringBuilder()).append(searchText.replaceAll("_", "^_"));
            paramsMap.put("search_text", sb.toString());
         }

         try {
            userSearch = this.getCustomSearchCondition(map);
         } catch (ParseException var8) {
            this.logger.error("", var8);
         }

         int offset = 0;
         if (startPos > 0) {
            offset = startPos - 1;
         }

         paramsMap.put("offset", offset);
         paramsMap.put("pageSize", pageSize);
         return ((UserDaoMapper)this.getMapper()).getAllUser(paramsMap, userSearch);
      } catch (SQLException var9) {
         this.logger.error("", var9);
         return new ArrayList();
      }
   }

   public int getCountAllUser(Map map) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.putAll(map);
      UserSearch userSearch = null;
      if (map.get("search_id") != null && !map.get("search_id").equals("") && !((String)map.get("search_id")).equalsIgnoreCase("null")) {
         Long search_id = Long.parseLong((String)map.get("search_id"));
         userSearch = this.getWhereClauseByCustomSearch(search_id);
      }

      String searchText = (String)map.get("searchText");
      if (searchText != null) {
         StringBuilder sb = (new StringBuilder()).append(searchText.replaceAll("_", "^_"));
         paramsMap.put("search_text", sb.toString());
      }

      try {
         userSearch = this.getCustomSearchCondition(map);
      } catch (ParseException var6) {
         this.logger.error("", var6);
      }

      return ((UserDaoMapper)this.getMapper()).getCountAllUser(paramsMap, userSearch);
   }

   public List getGroupedUser(Map map, int startPos, int pageSize) throws SQLException {
      try {
         Map paramsMap = new HashMap();
         paramsMap.putAll(map);
         if (map.get("searchText") != null && !map.get("searchText").equals("")) {
            String searchText = map.get("searchText").toString();
            paramsMap.put("search_text", searchText.replaceAll("_", "^_"));
         }

         UserSearch userSearch = null;

         try {
            userSearch = this.getCustomSearchCondition(map);
         } catch (ParseException var7) {
            this.logger.error("", var7);
         }

         int offset = 0;
         if (startPos > 0) {
            offset = startPos - 1;
         }

         paramsMap.put("offset", offset);
         paramsMap.put("pageSize", pageSize);
         return ((UserDaoMapper)this.getMapper()).getGroupedUser(paramsMap, userSearch);
      } catch (SQLException var8) {
         this.logger.error("", var8);
         return new ArrayList();
      }
   }

   public int getCountGroupedUser(Map map) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.putAll(map);
      if (map.get("searchText") != null && !map.get("searchText").equals("")) {
         String searchText = map.get("searchText").toString();
         paramsMap.put("search_text", searchText.replaceAll("_", "^_"));
      }

      UserSearch userSearch = null;

      try {
         userSearch = this.getCustomSearchCondition(map);
      } catch (ParseException var5) {
         this.logger.error("", var5);
      }

      return ((UserDaoMapper)this.getMapper()).getCountGroupedUser(paramsMap, userSearch);
   }

   public List getAllNonApprovedUser(Map map, int startPos, int pageSize) throws SQLException {
      try {
         Map paramsMap = new HashMap();
         paramsMap.putAll(map);
         if (map.get("searchText") != null && !map.get("searchText").equals("")) {
            String searchText = map.get("searchText").toString();
            paramsMap.put("search_text", searchText.replaceAll("_", "^_"));
         }

         int offset = 0;
         if (startPos > 0) {
            offset = startPos - 1;
         }

         paramsMap.put("offset", offset);
         paramsMap.put("pageSize", pageSize);
         return ((UserDaoMapper)this.getMapper()).getAllNonApprovedUser(paramsMap);
      } catch (SQLException var6) {
         this.logger.error("", var6);
         return new ArrayList();
      }
   }

   public int getCountAllNonApprovedUser(Map map) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.putAll(map);
      if (map.get("searchText") != null && !map.get("searchText").equals("")) {
         String searchText = map.get("searchText").toString();
         paramsMap.put("search_text", searchText.replaceAll("_", "^_"));
      }

      return ((UserDaoMapper)this.getMapper()).getCountAllNonApprovedUser(paramsMap);
   }

   public List getAllWithdrawalUser(Map map, int startPos, int pageSize) throws SQLException {
      try {
         Map paramsMap = new HashMap();
         paramsMap.putAll(map);
         if (map.get("searchText") != null && !map.get("searchText").equals("")) {
            String searchText = map.get("searchText").toString();
            paramsMap.put("search_text", searchText.replaceAll("_", "^_"));
         }

         int offset = 0;
         if (startPos > 0) {
            offset = startPos - 1;
         }

         paramsMap.put("offset", offset);
         paramsMap.put("pageSize", pageSize);
         return ((UserDaoMapper)this.getMapper()).getAllWithdrawalUser(paramsMap);
      } catch (SQLException var6) {
         this.logger.error("", var6);
         return new ArrayList();
      }
   }

   public int getCountAllWithdrawalUser(Map map) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.putAll(map);
      if (map.get("searchText") != null && !map.get("searchText").equals("")) {
         String searchText = map.get("searchText").toString();
         paramsMap.put("search_text", searchText.replaceAll("_", "^_"));
      }

      return ((UserDaoMapper)this.getMapper()).getCountAllWithdrawalUser(paramsMap);
   }

   public List getLdapUserInfo(String ldapUserId) throws SQLException {
      List result = null;

      try {
         result = ((UserDaoMapper)this.getMapper()).getLdapUserInfo(ldapUserId);
      } catch (SQLException var4) {
         this.logger.error("", var4);
      }

      return result;
   }

   public Boolean addUser(User user, boolean isNew) throws SQLException {
      SqlSession session = this.openNewSession(false);

      Boolean var5;
      try {
         PIIDataManager piiDataManager = (PIIDataManagerImpl)BeanUtils.getBean("PIIDataManager");
         user.setUser_name(piiDataManager.encryptData(user.getUser_name(), "name"));
         user.setEmail(piiDataManager.encryptData(user.getEmail(), "email"));
         user.setPhone_num(piiDataManager.encryptData(user.getPhone_num(), "phone"));
         user.setMobile_num(piiDataManager.encryptData(user.getMobile_num(), "phone"));
         String ldap_info;
         if (StringUtils.isBlank(user.getLdap_info())) {
            ldap_info = null;
            user.setLdap_user_id("");
         } else {
            ldap_info = user.getLdap_info();
         }

         Boolean var20;
         if (((UserDaoMapper)this.getMapper(session)).addUser(user, ldap_info) == 0) {
            session.rollback();
            var20 = false;
            return var20;
         }

         if (isNew) {
            UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance(session);
            this.logger.debug("group_name " + user.getGroup_name());
            Long userGroupId = userGroupInfo.getGroupIdByName(user.getGroup_name(), user.getRoot_group_id());
            if (-1L == userGroupId) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"Group name"});
            }

            this.logger.debug("group_name_gggg : " + user.getGroup_name());
            this.logger.debug("group_user_id : " + user.getUser_id());
            Boolean result = userGroupInfo.addMapGroupUser(user.getUser_id(), userGroupId);
            this.logger.debug("group_name " + user.getGroup_name());
            if (!result) {
               session.rollback();
               Boolean var21 = false;
               return var21;
            }

            RoleInfo roleInfo = RoleInfoImpl.getInstance(session);
            Long roleId = roleInfo.getRoleIdByRoleName(user.getRole_name());
            if (!roleInfo.addMapRoleUser(roleId, user.getUser_id())) {
               session.rollback();
               Boolean var11 = false;
               return var11;
            }
         }

         ((UserDaoMapper)this.getMapper(session)).addDashboardWithUserId(user.getUser_id(), 1, 1);
         ((UserDaoMapper)this.getMapper(session)).addDashboardWithUserId(user.getUser_id(), 2, 2);
         session.commit();
         var20 = true;
         return var20;
      } catch (RestServiceException var16) {
         throw var16;
      } catch (SQLException var17) {
         session.rollback();
         this.logger.error("", var17);
         var5 = false;
      } finally {
         session.close();
      }

      return var5;
   }

   public Boolean addUser(User user, long userGroupId, boolean isNew) throws SQLException {
      SqlSession session = this.openNewSession(false);

      try {
         PIIDataManager piiDataManager = (PIIDataManagerImpl)BeanUtils.getBean("PIIDataManager");
         user.setUser_name(piiDataManager.encryptData(user.getUser_name(), "name"));
         user.setEmail(piiDataManager.encryptData(user.getEmail(), "email"));
         user.setPhone_num(piiDataManager.encryptData(user.getPhone_num(), "phone"));
         user.setMobile_num(piiDataManager.encryptData(user.getMobile_num(), "phone"));
         String ldap_info;
         if (StringUtils.isBlank(user.getLdap_info())) {
            ldap_info = null;
            user.setLdap_user_id("");
         } else {
            ldap_info = user.getLdap_info();
         }

         Boolean var19;
         if (((UserDaoMapper)this.getMapper(session)).addUser(user, ldap_info) == 0) {
            session.rollback();
            var19 = false;
            return var19;
         } else {
            if (isNew) {
               UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance(session);
               this.logger.debug("group_name " + user.getGroup_name());
               this.logger.debug("group_name_gggg : " + user.getGroup_name());
               this.logger.debug("group_user_id : " + user.getUser_id());
               Boolean result = userGroupInfo.addMapGroupUser(user.getUser_id(), userGroupId);
               this.logger.debug("group_name " + user.getGroup_name());
               if (!result) {
                  session.rollback();
                  Boolean var20 = false;
                  return var20;
               }

               RoleInfo roleInfo = RoleInfoImpl.getInstance(session);
               Long roleId = roleInfo.getRoleIdByRoleName(user.getRole_name());
               if (!roleInfo.addMapRoleUser(roleId, user.getUser_id())) {
                  session.rollback();
                  Boolean var12 = false;
                  return var12;
               }
            }

            ((UserDaoMapper)this.getMapper(session)).addDashboardWithUserId(user.getUser_id(), 1, 1);
            ((UserDaoMapper)this.getMapper(session)).addDashboardWithUserId(user.getUser_id(), 2, 2);
            session.commit();
            var19 = true;
            return var19;
         }
      } catch (SQLException var16) {
         session.rollback();
         this.logger.error("", var16);
         Boolean var7 = false;
         return var7;
      } finally {
         session.close();
      }
   }

   public Boolean setUser(User user) throws SQLException {
      PIIDataManager piiDataManager = (PIIDataManagerImpl)BeanUtils.getBean("PIIDataManager");
      user.setUser_name(piiDataManager.encryptData(user.getUser_name(), "name"));
      user.setEmail(piiDataManager.encryptData(user.getEmail(), "email"));
      user.setPhone_num(piiDataManager.encryptData(user.getPhone_num(), "phone"));
      user.setMobile_num(piiDataManager.encryptData(user.getMobile_num(), "phone"));
      return ((UserDaoMapper)this.getMapper()).setUser(user);
   }

   public Boolean setIsApprovedByUserId(String isApproved, String userId, SqlSession session) throws SQLException {
      UserDaoMapper mapper = (UserDaoMapper)this.getMapper();
      if (session != null) {
         mapper = (UserDaoMapper)this.getMapper(session);
      }

      return mapper.setIsApprovedByUserId(userId, isApproved);
   }

   public Boolean setIsApprovedByUserId(String isApproved, String userId) throws SQLException {
      return this.setIsApprovedByUserId(isApproved, userId, (SqlSession)null);
   }

   public Boolean setLoginDateByUserId(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).setLoginDateByUserId(userId);
   }

   public Boolean setIsDeletedByUserId(String reason, String userId, boolean isSelf) throws SQLException {
      SqlSession session = this.openNewSession(false);

      Boolean var8;
      try {
         String delete_type;
         if (isSelf) {
            delete_type = "TEXT_WITHDRAWAL_SELF_P";
         } else {
            delete_type = "TEXT_WITHDRAWAL_ADMIN_P";
         }

         Boolean result = ((UserDaoMapper)this.getMapper(session)).setIsDeletedByUserId(userId, reason, delete_type);
         ServerSetupDao serverSetupDao = new ServerSetupDao();
         serverSetupDao.hideUserIdInLogs(session, userId);
         session.commit();
         var8 = result;
      } catch (SQLException var12) {
         session.rollback();
         throw var12;
      } finally {
         session.close();
      }

      return var8;
   }

   public String getNameByUserId(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getNameByUserId(userId);
   }

   public Long getRootGroupIdByUserId(String userId) throws SQLException {
      if (userId != null && !userId.isEmpty()) {
         User user = ((UserDaoMapper)this.getMapper()).getUserByUserId(userId);
         UserInfoImpl userInfo = UserInfoImpl.getInstance();

         try {
            if (user != null && user.isMu() && user.getIs_deleted().equals("N")) {
               long mngOrgId = -1L;
               if (SecurityUtils.getUserContainer() != null && SecurityUtils.getUserContainer().getUser() != null) {
                  mngOrgId = SecurityUtils.getUserContainer().getUser().getRoot_group_id();
               } else {
                  mngOrgId = userInfo.getCurMngOrgId(userId);
               }

               return mngOrgId;
            }
         } catch (Exception var6) {
            userInfo.setMUByUserId(userId, "N");
            this.logger.error("", var6);
         }

         Map result = ((UserDaoMapper)this.getMapper()).getRootGroupIdByUserId(userId);
         return MapUtils.isEmpty(result) ? -1L : (Long)result.get("ROOT_GROUP_ID");
      } else {
         return -1L;
      }
   }

   public User getUserByUserId(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getUserByUserId(userId);
   }

   public int deleteUser(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).deleteUser(userId);
   }

   public int deleteLDAPUser(Long orgId) throws SQLException {
      SqlSession session = this.openNewSession(false);
      boolean var3 = true;

      int result;
      try {
         result = ((UserDaoMapper)this.getMapper(session)).deleteLDAPUser(orgId);
         ((UserDaoMapper)this.getMapper(session)).removeLdapInfomation(orgId);
         session.commit();
      } catch (SQLException var8) {
         session.rollback();
         throw var8;
      } finally {
         session.close();
      }

      return result;
   }

   public Boolean setApprovalByUser(User user) throws SQLException {
      SqlSession session = this.openNewSession(false);

      Boolean var7;
      try {
         if (!this.setIsApprovedByUserId("Y", user.getUser_id(), session)) {
            session.rollback();
            throw new SQLException("error occurred while setting user as approved.");
         }

         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance(session);
         Long userGroupId = 0L;
         if (!user.getGroup_name().equals("ROOT")) {
            if (user.getGroup_id() != null && user.getGroup_id() != 0L) {
               userGroupId = user.getGroup_id();
            } else {
               userGroupId = userGroupInfo.getGroupIdByName(user.getGroup_name(), user.getRoot_group_id());
            }
         }

         if (!userGroupInfo.addMapGroupUser(user.getUser_id(), userGroupId)) {
            session.rollback();
            throw new SQLException("error occurred while mapping user and userGroup.");
         }

         RoleInfo roleInfo = RoleInfoImpl.getInstance(session);
         Long roleId = roleInfo.getRoleIdByRoleName(user.getRole_name());
         if (!roleInfo.addMapRoleUser(roleId, user.getUser_id())) {
            session.rollback();
            throw new SQLException("error occurred while mapping id and role.");
         }

         session.commit();
         var7 = true;
      } catch (SQLException var11) {
         this.logger.error("", var11);
         session.rollback();
         throw var11;
      } finally {
         session.close();
      }

      return var7;
   }

   public String getIsApprovedByUserId(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getIsApprovedByUserId(userId);
   }

   public String getIsDeletedByUserId(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getIsDeletedByUserId(userId);
   }

   private UserSearch getWhereClauseByCustomSearch(Long searchId) throws SQLException {
      UserSearchInfo searchInfo = UserSearchInfoImpl.getInstance();
      UserSearch condition = searchInfo.getUserSearchBySearchId(searchId);
      UserSearch outputUserSearch = new UserSearch();
      String searchText;
      if (condition.getUser_id() != null && !condition.getUser_id().equals("")) {
         searchText = condition.getUser_id();
         searchText = searchText.replaceAll("'", "\\\\'");
         searchText = searchText.replaceAll("_", "^_");
         outputUserSearch.setUser_id(searchText);
      }

      if (condition.getUser_name() != null && !condition.getUser_name().equals("")) {
         searchText = condition.getUser_name();
         searchText = searchText.replaceAll("'", "\\\\'");
         searchText = searchText.replaceAll("_", "^_");
         outputUserSearch.setUser_name(searchText);
      }

      if (condition.getOrganization() != null && !condition.getOrganization().equals("")) {
         outputUserSearch.setOrganization(condition.getOrganization().replaceAll("'", "\\\\'"));
      }

      if (condition.getGroup_name() != null && !condition.getGroup_name().equals("")) {
         outputUserSearch.setGroup_name(condition.getGroup_name().replaceAll("'", "\\\\'"));
      }

      if (condition.getRole_name() != null && !condition.getRole_name().equals("")) {
         outputUserSearch.setRole_name(condition.getRole_name().replaceAll("'", "\\\\'"));
      }

      if (condition.getPhone_num() != null && !condition.getPhone_num().equals("")) {
         outputUserSearch.setPhone_num(condition.getPhone_num());
      }

      if (condition.getEmail() != null && !condition.getEmail().equals("")) {
         searchText = condition.getEmail();
         searchText = searchText.replaceAll("'", "\\\\'");
         searchText = searchText.replaceAll("_", "^_");
         outputUserSearch.setEmail(searchText);
      }

      if (condition.getJoin_start_date() != null) {
         outputUserSearch.setJoin_start_date(condition.getJoin_start_date());
      }

      if (condition.getJoin_end_date() != null) {
         outputUserSearch.setJoin_end_date(condition.getJoin_end_date());
      }

      if (condition.getLast_login_start_date() != null) {
         outputUserSearch.setLast_login_start_date(condition.getLast_login_start_date());
      }

      if (condition.getLast_login_end_date() != null) {
         outputUserSearch.setLast_login_end_date(condition.getLast_login_end_date());
      }

      return outputUserSearch;
   }

   private UserSearch getCustomSearchCondition(Map map) throws SQLException, ParseException {
      UserSearch userSearch = new UserSearch();
      if (map.get("search_organ") != null && !map.get("search_organ").equals("") && !((String)map.get("search_organ")).equalsIgnoreCase("null")) {
         userSearch.setOrganization((String)map.get("search_organ"));
      }

      if (map.get("role_name") != null && !map.get("role_name").equals("") && !((String)map.get("role_name")).equalsIgnoreCase("null")) {
         userSearch.setRole_name(((String)map.get("role_name")).replaceAll("'", "\\\\'"));
      }

      SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
      String endDate;
      Date parsedDate;
      Timestamp endTime;
      if (map.get("search_start_date") != null && !map.get("search_start_date").equals("") && !((String)map.get("search_start_date")).equalsIgnoreCase("null")) {
         endDate = (String)map.get("search_start_date") + " 00:00:00";
         parsedDate = dateFormat.parse(endDate);
         endTime = new Timestamp(parsedDate.getTime());
         userSearch.setJoin_start_date(endTime);
      }

      if (map.get("search_end_date") != null && !map.get("search_end_date").equals("") && !((String)map.get("search_end_date")).equalsIgnoreCase("null")) {
         endDate = (String)map.get("search_end_date") + " 23:59:59";
         parsedDate = dateFormat.parse(endDate);
         endTime = new Timestamp(parsedDate.getTime());
         userSearch.setJoin_end_date(endTime);
      }

      return userSearch;
   }

   public boolean setOrganizationByUserIdList(String organization, List userIdList) throws SQLException {
      if (userIdList != null && !userIdList.isEmpty()) {
         List userIdListParams = new ArrayList();
         Iterator var4 = userIdList.iterator();

         while(var4.hasNext()) {
            Object object = var4.next();
            userIdListParams.add((String)((Map)object).get("user_id"));
         }

         return ((UserDaoMapper)this.getMapper()).setOrganizationByUserIdList(userIdListParams, organization);
      } else {
         return false;
      }
   }

   public List getAllApprovalUserByRootGroupId(Long rootGroupId, boolean isAll) throws SQLException {
      try {
         PIIDataManager piiDataManager = (PIIDataManagerImpl)BeanUtils.getBean("PIIDataManager");
         if (!isAll) {
            return ((UserDaoMapper)this.getMapper()).getAllApprovalUserByRootGroupId(rootGroupId);
         } else {
            List userList = ((UserDaoMapper)this.getMapper()).getAllApprovalUserByRootGroupIdAll(rootGroupId);
            Iterator var5 = userList.iterator();

            while(var5.hasNext()) {
               Map user = (Map)var5.next();
               user.put("user_name", piiDataManager.decryptData(user.get("user_name").toString()));
               user.put("email", piiDataManager.decryptData(user.get("email").toString()));
               user.put("phone_num", piiDataManager.decryptData(user.get("phone_num").toString()));
               user.put("mobile_num", piiDataManager.decryptData(user.get("mobile_num").toString()));
            }

            return userList;
         }
      } catch (SQLException var7) {
         this.logger.error("", var7);
         return null;
      }
   }

   public List getAllUserByRootGroupId(Long rootGroupId) throws SQLException {
      try {
         return ((UserDaoMapper)this.getMapper()).getAllUserByRootGroupId(rootGroupId);
      } catch (SQLException var3) {
         this.logger.error("", var3);
         return null;
      }
   }

   public List getAllRootUserList() throws SQLException {
      try {
         return ((UserDaoMapper)this.getMapper()).getAllRootUserList();
      } catch (SQLException var2) {
         this.logger.error("", var2);
         return null;
      }
   }

   public List getAllUserListByRootGroupId(Long organization_id) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getAllUserListByRootGroupId(organization_id, 0);
   }

   public boolean setRejectUser(String rejectReason, String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).setRejectUser(userId, rejectReason);
   }

   public Map getIsRejectByUserId(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getIsRejectByUserId(userId);
   }

   public Map getDeleteInfoByUserId(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getDeleteInfoByUserId(userId);
   }

   public List getAllUserList(Map map) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.putAll(map);
      if (map.get("group_id") != null && !"".equals(map.get("group_id"))) {
         paramsMap.put("group_id", new Long((String)map.get("group_id")));
      }

      if (map.get("searchText") != null && !map.get("searchText").equals("")) {
         String searchText = map.get("searchText").toString();
         paramsMap.put("search_text", searchText.replaceAll("_", "^_"));
      }

      UserSearch userSearch = null;
      if (map.get("search_id") != null && !"".equals(map.get("search_id")) && !((String)map.get("search_id")).equalsIgnoreCase("null")) {
         Long search_id = Long.parseLong((String)map.get("search_id"));
         userSearch = this.getWhereClauseByCustomSearch(search_id);
      }

      return map.get("group_type") != null && "NONAPPROVED".equals(map.get("group_type")) ? ((UserDaoMapper)this.getMapper()).getAllUserListNonApproved(paramsMap, userSearch) : ((UserDaoMapper)this.getMapper()).getAllUserListApproved(paramsMap, userSearch);
   }

   public List getAllUserList(Long organization_id) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getAllUserList(organization_id);
   }

   public List getAllUserListToMigrate() throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getAllUserListToMigrate();
   }

   public boolean migrateUser(User user) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).migrateUser(user);
   }

   public int deleteMappingInfoByUserID(String userId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var3;
      try {
         ((UserDaoMapper)this.getMapper(session)).deleteMappingInfoByUserID_GroupUser(userId);
         ((UserDaoMapper)this.getMapper(session)).deleteMappingInfoByUserID_RoleUser(userId);
         ((UserDaoMapper)this.getMapper(session)).deleteMappingInfoByUserID_MenuUser(userId);
         session.commit();
         var3 = 1;
      } catch (SQLException var7) {
         this.logger.error("", var7);
         session.rollback();
         throw var7;
      } finally {
         session.close();
      }

      return var3;
   }

   public String getOrganNameByUserId(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getOrganNameByUserId(userId);
   }

   public String getMailList(String userIdList) throws SQLException {
      if (userIdList != null && userIdList.length() > 0) {
         PIIDataManager piiDataManager = (PIIDataManagerImpl)BeanUtils.getBean("PIIDataManager");
         List userIDs = Arrays.asList(userIdList.split(","));
         if (userIDs.isEmpty()) {
            return "";
         } else {
            List smsList = ((UserDaoMapper)this.getMapper()).getMailList(userIDs);

            for(int i = 0; i < smsList.size(); ++i) {
               smsList.set(i, piiDataManager.decryptData((String)smsList.get(i)));
            }

            StringBuilder result = new StringBuilder();

            String phoneNum;
            for(Iterator var6 = smsList.iterator(); var6.hasNext(); result.append(phoneNum)) {
               phoneNum = (String)var6.next();
               if (result.length() > 0) {
                  result.append(",");
               }
            }

            return result.toString();
         }
      } else {
         return "";
      }
   }

   public String getSMSList(String userIdList) throws SQLException {
      if (userIdList != null && userIdList.length() > 0) {
         List userIDs = Arrays.asList(userIdList.split(","));
         if (userIDs.isEmpty()) {
            return "";
         } else {
            List smsList = ((UserDaoMapper)this.getMapper()).getSMSList(userIDs);
            StringBuilder result = new StringBuilder();

            String phoneNum;
            for(Iterator var5 = smsList.iterator(); var5.hasNext(); result.append(phoneNum)) {
               phoneNum = (String)var5.next();
               if (result.length() > 0) {
                  result.append(",");
               }
            }

            return result.toString();
         }
      } else {
         return "";
      }
   }

   public List getAllUserListBySearch(Long search_id) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getAllUserListBySearch(search_id, this.getWhereClauseByCustomSearch(search_id));
   }

   public String getImeiByUserId(String userId) throws SQLException {
      String imeiByUserId = ((UserDaoMapper)this.getMapper()).getImeiByUserId(userId);
      return imeiByUserId != null && !"".equals(imeiByUserId) ? imeiByUserId : "";
   }

   public String getOsTypeByUserId(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getOsTypeByUserId(userId);
   }

   public String getIsResetPwdByUserId(String userId) throws SQLException {
      String result = ((UserDaoMapper)this.getMapper()).getIsResetPwdByUserId(userId);
      return "Y".equalsIgnoreCase(result) ? result : "";
   }

   public User getUserInfo(String userID) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getUserInfo(userID);
   }

   public boolean setEmailSendingOptions(String get_fault, String get_alarm, String user_id) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).setEmailSendingOptions(user_id, get_fault, get_alarm);
   }

   public boolean setEmailSendingDisconnectAlarm(String user_id, String disconnect_alarm) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).setEmailSendingDisconnectAlarm(user_id, disconnect_alarm);
   }

   public String[] getAllUserListByEmailSendingOptions(Long organization_id, String get_fault, String get_alarm) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getAllUserListByEmailSendingOptions(organization_id, get_fault, get_alarm, 0);
   }

   public Group getOrganGroupInfo(Long groupID) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getOrganGroupInfo(groupID);
   }

   public String getShortcut(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getShortcut(userId);
   }

   public boolean setShortcut(String userId, String shortcut) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).setShortcut(userId, shortcut);
   }

   public List getNewAndModifiedUserByDate(String startDate, String endDate) throws SQLException {
      Timestamp start_Date = Timestamp.valueOf(startDate);
      Timestamp end_Date = Timestamp.valueOf(endDate);
      return ((UserDaoMapper)this.getMapper()).getNewAndModifiedUserByDate(start_Date, end_Date);
   }

   public List getDashboard(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getDashboard(userId);
   }

   public List getDashboardOrderByDashboardId(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getDashboardOrderByDashboardId(userId);
   }

   public List getOrganization() throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getOrganization();
   }

   public List getDisconnectAlarmUserListByOrgId(long orgId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getDisconnectAlarmUserList(orgId);
   }

   public List getContentManagerUserListByOrgId(Long orgId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getContentManagerUserListByOrgId(orgId);
   }

   public boolean setContentApprover(String userId, String contentApprover) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).setContentApprover(userId, contentApprover);
   }

   public boolean setAllContentApprover(String contentApprover) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).setAllContentApprover(contentApprover);
   }

   public List getContentApproverListByGroupId(Long rootGroupId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getContentApproverListByGroupId(rootGroupId);
   }

   public List getContentApprover() throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getContentApprover();
   }

   public boolean setLocale(String userId, String locale) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).setLocale(userId, locale);
   }

   public List getEmailNotificationUserListByOrgId(Long orgId, boolean includeRoot) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getEmailNotificationUserListByOrgId(orgId, includeRoot);
   }

   public boolean setEmailNotificationOption(Long orgId, String userId, String type, boolean value) throws SQLException {
      return value ? ((UserDaoMapper)this.getMapper()).addEmailNotificationOption(orgId, userId, type) : ((UserDaoMapper)this.getMapper()).deleteEmailNotificationOption(orgId, userId, type);
   }

   public int deleteEmailNotificationByOrdIdAndUserId(Long orgId, String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).deleteEmailNotificationByOrdIdAndUserId(orgId, userId);
   }

   public List getAlarmUserListByOrgIdAndType(Long orgId, String type) {
      PIIDataManager piiDataManager = (PIIDataManagerImpl)BeanUtils.getBean("PIIDataManager");
      List alarmUserList = ((UserDaoMapper)this.getMapper()).getAlarmUserListByOrgIdAndType(orgId, type);
      Iterator var5 = alarmUserList.iterator();

      while(var5.hasNext()) {
         Map user = (Map)var5.next();
         user.put("email", piiDataManager.decryptData(user.get("email").toString()));
      }

      return alarmUserList;
   }

   public String getOrganNameByRootGroupId(long rootGroupId) {
      return ((UserDaoMapper)this.getMapper()).getOrganNameByRootGroupId(rootGroupId);
   }

   public Integer deleteDashboardUserInfoByUserId(String userId) {
      return ((UserDaoMapper)this.getMapper()).deleteDashboardUserInfoByUserId(userId);
   }

   public Boolean setIsFirstLoginByUserId(String userId) {
      return ((UserDaoMapper)this.getMapper()).setIsFirstLoginByUserId(userId);
   }

   public Boolean checkResetValidByTokenAndId(String userId, String encryptionToken) {
      return ((UserDaoMapper)this.getMapper()).checkResetValidByTokenAndId(userId, encryptionToken);
   }

   public boolean setResetPwToken(String encryptionToken, long passwordResetExpirationInSec, String userId) {
      return ((UserDaoMapper)this.getMapper()).setResetPwToken(encryptionToken, passwordResetExpirationInSec, userId);
   }

   public List getAllUserWithEncryptionToken() {
      return ((UserDaoMapper)this.getMapper()).getAllUserWithEncryptionToken();
   }

   public long getCurMngOrgId(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getCurMngOrgId(userId);
   }

   public boolean setCurMngOrgId(String userId, long mngOrg) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).setCurMngOrgId(userId, mngOrg);
   }

   public List getMUInfoByMngOrgId(long mngOrg) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getMUInfoByMngOrgId(mngOrg);
   }

   public boolean setMUByUserId(String userId, String flag) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).setMUByUserId(userId, flag);
   }

   public User getAdminOfOrganizationByRootGroupId(long rootGroupId) throws SQLException {
      return rootGroupId == 0L ? ((UserDaoMapper)this.getMapper()).getAdminOfOrganizationByRootGroupId(1L, rootGroupId) : ((UserDaoMapper)this.getMapper()).getAdminOfOrganizationByRootGroupId(2L, rootGroupId);
   }

   public User getAdminOfOrganizationNotInDeleteUsers(long rootGroupId, String[] deleteUsers) throws SQLException {
      return rootGroupId == 0L ? ((UserDaoMapper)this.getMapper()).getAdminOfOrganizationNotInDeleteUsers(1L, rootGroupId, deleteUsers) : ((UserDaoMapper)this.getMapper()).getAdminOfOrganizationNotInDeleteUsers(2L, rootGroupId, deleteUsers);
   }

   public int setIsResetPasswordBatch(Timestamp passwordChangeDate) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).setIsResetPasswordBatch(passwordChangeDate);
   }

   public List getfilterExport(Map map) throws SQLException {
      String groupType = (String)map.get("groupType");
      return groupType.equals("UNAPPROVED") ? ((UserDaoMapper)this.getMapper()).getfilterExportUnApporved(map) : ((UserDaoMapper)this.getMapper()).getfilterExport(map);
   }

   public int addAuthDeviceInfo(UserAuthDevice userAuthDevice) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).addAuthDeviceInfo(userAuthDevice);
   }

   public long getAuthDeviceId() throws SQLException {
      long authDeviceId = (long)SequenceDB.getNextValue("MI_USER_MAP_MFA_DEVICE");
      return authDeviceId;
   }

   public int setUserSecretKey(String userId, String secretKey) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).setUserSecretKey(userId, secretKey);
   }

   public List getUserAuthDevice(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getUserAuthDevice(userId);
   }

   public List getUserStoredDevice(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).getUserStoredDevice(userId);
   }

   public int deleteUserDevice(int authDeviceId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).deleteUserDevice(authDeviceId);
   }

   public int deleteUserDeviceByUserId(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).deleteUserDeviceByUserId(userId);
   }

   public int deleteExpiredUserDevice() throws SQLException {
      return ((UserDaoMapper)this.getMapper()).deleteExpiredUserDevice();
   }

   public int updateUserDeviceExpiredDate(int day) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).updateUserDeviceExpiredDate(day);
   }

   public int updateUserDeviceByUserId(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).updateUserDeviceByUserId(userId);
   }

   public int deleteAllMfaDevice() throws SQLException {
      return ((UserDaoMapper)this.getMapper()).deleteAllMfaDevice();
   }

   public String unapprovedUser(String userId) throws SQLException {
      return ((UserDaoMapper)this.getMapper()).unapprovedUser(userId);
   }
}
