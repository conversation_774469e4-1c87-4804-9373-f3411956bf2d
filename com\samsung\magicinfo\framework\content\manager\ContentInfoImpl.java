package com.samsung.magicinfo.framework.content.manager;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.FileManager;
import com.samsung.magicinfo.framework.common.FileManagerImpl;
import com.samsung.magicinfo.framework.content.constants.ContentConstants;
import com.samsung.magicinfo.framework.content.dao.ContentDao;
import com.samsung.magicinfo.framework.content.dao.DeleteFileDao;
import com.samsung.magicinfo.framework.content.dao.DeleteFileDaoImpl;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.entity.TemplateDisplay;
import com.samsung.magicinfo.framework.content.entity.TemplateElement;
import com.samsung.magicinfo.framework.content.entity.TemplateElementData;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.file.FileUploadHelper;
import com.samsung.magicinfo.restapi.contents.model.V2AdsContentPublisherInfo;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.Map.Entry;
import javax.servlet.http.HttpServletRequest;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.apache.commons.io.FilenameUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

public class ContentInfoImpl implements ContentInfo {
   Logger logger = LoggingManagerV2.getLogger(ContentInfoImpl.class);
   ContentDao dao = new ContentDao();
   private static DeleteFileDao deleteFileDao = null;
   static ContentInfoImpl instance = null;

   public static ContentInfoImpl getInstance() {
      Class var0;
      if (instance == null) {
         var0 = ContentInfoImpl.class;
         synchronized(ContentInfoImpl.class) {
            if (instance == null) {
               instance = new ContentInfoImpl((SqlSession)null);
            }
         }
      }

      if (deleteFileDao == null) {
         var0 = ContentInfoImpl.class;
         synchronized(ContentInfoImpl.class) {
            if (deleteFileDao == null) {
               deleteFileDao = DeleteFileDaoImpl.getInstance();
            }
         }
      }

      return instance;
   }

   public static ContentInfoImpl getInstance(SqlSession sqlSession) {
      return new ContentInfoImpl(sqlSession);
   }

   private ContentInfoImpl(SqlSession sqlSession) {
      super();
      if (this.dao == null) {
         this.dao = new ContentDao(sqlSession);
      }

   }

   public Boolean getCanEditOthers(String userID, String groupType, HttpServletRequest request) throws SQLException {
      Boolean canEditOthers = false;
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String loginID = userContainer.getUser().getUser_id();
      if (groupType.equalsIgnoreCase("USER")) {
         UserInfo user = UserInfoImpl.getInstance();
         User myInfo = user.getUserByUserId(loginID);
         User userInfo = user.getUserByUserId(userID);
         if (userInfo != null && !userInfo.getRoot_group_id().equals(myInfo.getRoot_group_id()) && myInfo.getRoot_group_id() == 0L) {
            canEditOthers = true;
         } else {
            AbilityUtils ability = new AbilityUtils();
            canEditOthers = ability.checkAuthority("Content Manage");
         }
      } else if (groupType.equalsIgnoreCase("ORGAN")) {
         AbilityUtils ability = new AbilityUtils();
         canEditOthers = ability.checkAuthority("Content Manage");
      }

      return canEditOthers;
   }

   public String getContentName(String contentID) throws SQLException {
      return this.dao.getContentName(contentID);
   }

   public String getContentOrgCreatorId(String contentID) throws SQLException {
      return this.dao.getContentOrgCreatorId(contentID);
   }

   public List getContentAllVerInfo(String contentID) throws SQLException {
      List contentList = this.dao.getContentAllVerInfo(contentID);
      List retList = new ArrayList();

      for(int i = 0; i < contentList.size(); ++i) {
         Content content = (Content)contentList.get(i);
         ContentFile file = this.dao.getFileInfo(content.getThumb_file_id());
         content.setThumb_file_name(file.getFile_name());
         content.setThumb_file_path(file.getFile_path());
         retList.add(content);
      }

      return retList;
   }

   public Content getContentAndFileActiveVerInfo(String contentID) throws SQLException {
      Content content = this.getContentActiveVerInfo(contentID);
      if (content != null) {
         content.setArr_file_list(this.dao.getActiveFileList(contentID));
      }

      return content;
   }

   public Map getAdsContentActiveVersionInfo(String contentId) throws SQLException {
      return this.dao.getAdsContentActiveVersionInfo(contentId);
   }

   public Content getContentActiveVerInfo(String contentID) throws SQLException {
      Content content = this.dao.getContentActiveVerInfo(contentID);
      if (content != null) {
         ContentFile file = this.dao.getFileInfo(content.getThumb_file_id());
         if (file != null) {
            content.setThumb_file_name(file.getFile_name());
            content.setThumb_file_path(file.getFile_path());
         }
      }

      return content;
   }

   public Content getContentActiveVerInfoTemporary(String contentID) throws SQLException {
      Content content = this.dao.getContentActiveVerInfoTemporary(contentID);
      if (content != null) {
         ContentFile file = this.dao.getFileInfo(content.getThumb_file_id());
         content.setThumb_file_name(file.getFile_name());
         content.setThumb_file_path(file.getFile_path());
      }

      return content;
   }

   public Content getThumbInfoOfActiveVersion(String contentID) throws SQLException {
      return this.dao.getThumbInfoOfActiveVersion(contentID);
   }

   public Content getContentVerInfo(String contentID, Long versionID) throws SQLException {
      Content content = this.dao.getContentVerInfo(contentID, versionID);
      if (content != null) {
         ContentFile file = this.dao.getFileInfo(content.getThumb_file_id());
         content.setThumb_file_name(file.getFile_name());
         content.setThumb_file_path(file.getFile_path());
      }

      return content;
   }

   public List getSearchList(Map map) throws SQLException {
      return this.dao.getSearchList(map);
   }

   public int getSearchListCnt(Map map) throws SQLException {
      return this.dao.getSearchListCnt(map);
   }

   public List getContentList(Map map) throws SQLException {
      return this.dao.getContentList(map);
   }

   public List getAllDeletedContentList(String creatorID) throws SQLException {
      return this.dao.getAllDeletedContentList(creatorID);
   }

   public List getAllDeletedContentListWithoutCreatorId(String creatorId) throws SQLException {
      return this.dao.getAllDeletedContentListWithoutCreatorId(creatorId);
   }

   public List getAllContentList(String creatorID, String mediaType) throws SQLException {
      return this.dao.getAllContentList(creatorID, mediaType);
   }

   public List getAllContentList(String creatorID, Long groupID, String mediaType) throws SQLException {
      return this.dao.getAllContentList(creatorID, groupID, mediaType);
   }

   public ContentFile getFileInfo(String fileID) throws SQLException {
      return this.dao.getFileInfo(fileID);
   }

   public ContentFile getMainFileInfo(String contentID) throws SQLException {
      return this.dao.getMainFileInfo(contentID);
   }

   public ContentFile getRulesetFileInfo(String rulesetId) throws SQLException {
      return this.dao.getRulesetFileInfo(rulesetId);
   }

   public ContentFile getMainFileInfoTemporary(String contentID) throws SQLException {
      return this.dao.getMainFileInfoTemporary(contentID);
   }

   public String getMainFileName(String contentID) throws SQLException {
      return this.dao.getMainFileName(contentID);
   }

   public List getContentUseInPlaylist(String contentId) throws SQLException {
      return this.dao.getContentUseInPlaylist(contentId);
   }

   public List getContentUseInSchedule(String contentId) throws SQLException {
      return this.dao.getContentUseInSchedule(contentId);
   }

   public ContentFile getThumbFileInfo(String contentID) throws SQLException {
      return this.dao.getThumbFileInfo(contentID);
   }

   public ContentFile getSfiFileInfo(String contentID) throws SQLException {
      return this.dao.getSfiFileInfo(contentID);
   }

   public ContentFile getMainFileInfoOfTmpVer(String contentID) throws SQLException {
      return this.dao.getMainFileInfoOfTmpVer(contentID);
   }

   public ContentFile getThumbFileInfoOfTempVer(String contentID) throws SQLException {
      return this.dao.getThumbFileInfoOfTempVer(contentID);
   }

   public String getFileHash(String fileID) throws SQLException {
      return this.dao.getFileHash(fileID);
   }

   public Long getFileSize(String fileID) throws SQLException {
      return this.dao.getFileSize(fileID);
   }

   public String getFileName(String fileID) throws SQLException {
      return this.dao.getFileName(fileID);
   }

   public List getThumbFileList(String contentID) throws SQLException {
      return this.dao.getThumbFileList(contentID);
   }

   public List getThumbMovieFileList(String contentId) throws SQLException {
      return this.dao.getThumbMovieFileList(contentId);
   }

   public boolean deleteThumbMovieMap(String contentId) throws SQLException {
      return this.dao.deleteThumbMovieMap(contentId);
   }

   public Map getThumbFileInfoOfActiveVersion(String contentID) throws SQLException {
      return this.dao.getThumbFileInfoOfActiveVersion(contentID);
   }

   public Map getThumbFileInfoOfActiveVersionTemporary(String contentID) throws SQLException {
      return this.dao.getThumbFileInfoOfActiveVersionTemporary(contentID);
   }

   public List getFileList(String contentID) throws SQLException {
      return this.dao.getFileList(contentID);
   }

   public List getFileListForApi(String contentID) throws SQLException {
      return this.dao.getFileListForApi(contentID);
   }

   public List getFileList(String contentID, Long versionID) throws SQLException {
      return this.dao.getFileList(contentID, versionID);
   }

   public List getActiveFileList(String contentID) throws SQLException {
      return this.dao.getActiveFileList(contentID);
   }

   public Boolean isExistFile(ContentFile file) throws Exception {
      return this.dao.isExistFile(file);
   }

   public Boolean isExistFileByID(String fileID) throws SQLException {
      return this.dao.isExistFileByID(fileID);
   }

   public Boolean isExistFileByHash(String fileName, Long fileSize, String hashCode) throws SQLException {
      return this.dao.isExistFileByHash(fileName, fileSize, hashCode);
   }

   public int numberOfExistingFileByHash(String fileName, Long fileSize, String hashCode) throws SQLException {
      return this.dao.numberOfExistingFileByHash(fileName, fileSize, hashCode);
   }

   public String getFileIDByHash(String fileName, Long fileSize, String hashCode) throws SQLException {
      return this.dao.getFileIDByHash(fileName, fileSize, hashCode);
   }

   public String getFileIDByHashCreator(String fileName, Long fileSize, String hashCode, String creatorId) throws SQLException {
      return this.dao.getFileIDByHashCreator(fileName, fileSize, hashCode, creatorId);
   }

   public Boolean isExistFileByIDAndHash(String fileID, String fileName, String hashCode) throws SQLException {
      return this.dao.isExistFileByIDAndHash(fileID, fileName, hashCode);
   }

   public Boolean isExistContentID(String contentID, String creatorID) throws SQLException {
      return this.dao.isExistContentID(contentID, creatorID);
   }

   public Boolean isExistContentForCidMapping(String contentID, String creatorID) throws SQLException {
      return this.dao.isExistContentForCidMapping(contentID, creatorID);
   }

   public Boolean isExistContentID(String contentID) throws SQLException {
      return this.dao.isExistContentID(contentID);
   }

   public Boolean isExistContentVersion(String contentID, Long versionID) throws SQLException {
      return this.dao.isExistContentVersion(contentID, versionID);
   }

   public Boolean isUpdatableContent(String contentID) throws SQLException {
      return this.dao.isUpdatableContent(contentID);
   }

   public Boolean isDeletableFile(String fileID, String contentID) throws SQLException {
      return this.dao.isDeletableFile(fileID, contentID);
   }

   public Boolean isDeletableFileByVersion(String fileId, String contentId, long versionId) throws SQLException {
      return this.dao.isDeletableFileByVersion(fileId, contentId, versionId);
   }

   public Boolean isDeletableContent(String contentID, String sessionID) throws SQLException {
      return this.dao.isDeletableContent(contentID, sessionID);
   }

   public Boolean isLockedContent(String contentID, String sessionID) throws SQLException {
      return this.dao.isLockedContent(contentID, sessionID);
   }

   public int addContent(Content content) throws SQLException, ConfigException, Exception {
      return this.dao.addContent(content);
   }

   public int addContentForMega(Content content) throws SQLException, ConfigException, Exception {
      return this.dao.addContentForMega(content);
   }

   public int addContentInfo(Content content) throws SQLException, ConfigException {
      return this.dao.addContentInfo(content);
   }

   public int addAdsContentVersionInfo(Map adsSetting) throws SQLException, ConfigException {
      return this.dao.addAdsContentVersionInfo(adsSetting);
   }

   public int addContentVersionInfo(Content content) throws SQLException, ConfigException {
      return this.dao.addContentVersionInfo(content);
   }

   public int addMapContentFile(String contentID, Long versionID, String fileID) throws SQLException {
      return this.dao.addMapContentFile(contentID, versionID, fileID);
   }

   public int addMapGroupContent(String contentID, Long groupID) throws SQLException {
      return this.dao.addMapGroupContent(contentID, groupID);
   }

   public int setContentInfo(String contentID, String contentName, String contentMetaData, int shareFlag) throws SQLException, ConfigException {
      return this.dao.setContentInfo(contentID, contentName, contentMetaData, shareFlag, 0);
   }

   public int setContentInfo(String contentID, String contentName, String contentMetaData, int shareFlag, int pollingInterval) throws SQLException, ConfigException {
      return this.dao.setContentInfo(contentID, contentName, contentMetaData, shareFlag, pollingInterval);
   }

   public Long getContentActiveVer(String contentID) throws SQLException {
      return this.dao.getContentActiveVer(contentID);
   }

   public int deleteMaxVersionContent(String contentID) throws SQLException {
      return this.dao.deleteMaxVersionContent(contentID);
   }

   public int deleteVersionContent(String contentID, Long version) throws SQLException {
      return this.dao.deleteVersionContent(contentID, version);
   }

   public int setContentActive(String contentID, Long version) throws SQLException {
      return this.dao.setContentActive(contentID, version);
   }

   public int deleteContent(String contentID, String userID, String sessionID) throws SQLException, ConfigException {
      int result = false;
      int result = this.dao.deleteContent(contentID, userID, sessionID);
      if (result > 0) {
         result = this.deleteTemplateContent(contentID);
      }

      return result;
   }

   public int restoreContent(String contentID) throws SQLException, ConfigException {
      return this.dao.restoreContent(contentID);
   }

   public int deleteContentCompletely(String contentID) throws SQLException {
      int result = true;
      String lftContentId = this.dao.getLfdContentIdByDlkContentId(contentID);
      int result = this.dao.deleteContentCompletely(contentID);
      if (result > 0) {
         this.dao.updateUsedTemplateByContentId(lftContentId);
      }

      return result;
   }

   public int setContentLock(String contentID, String sessionID) throws SQLException {
      return this.dao.setContentLock(contentID, sessionID);
   }

   public int setContentUnlock(String contentID, String sessionID) throws SQLException {
      return this.dao.setContentUnlock(contentID, sessionID);
   }

   public int setContentUnlockBySessionID(String sessionID) throws SQLException {
      return this.dao.setContentUnlockBySessionID(sessionID);
   }

   public int deleteAllContentLockData() throws SQLException {
      return this.dao.deleteAllContentLockData();
   }

   public int setContentGroup(String contentID, Long groupID) throws SQLException, ConfigException {
      return this.dao.setContentGroup(contentID, groupID);
   }

   public int setContentShare(String contentID, Long shareFlag) throws SQLException, ConfigException {
      return this.dao.setContentShare(contentID, shareFlag);
   }

   public int setContentMetaData(String contentID, String metaData) throws SQLException, ConfigException {
      return this.dao.setContentMetaData(contentID, metaData);
   }

   public void deleteContentUnderVersion(String contentID, long versionID) {
      for(long versionId_i = 1L; versionId_i <= versionID; ++versionId_i) {
         try {
            if (this.isExistContentVersion(contentID, versionId_i)) {
               Content oldContent = this.getContentVerInfo(contentID, versionId_i);
               if (this.isDeletableFileByVersion(oldContent.getMain_file_id(), contentID, versionId_i) && !this.dao.isUsedAfterVersion(oldContent.getMain_file_id(), versionID)) {
                  this.deleteFile(oldContent.getMain_file_id());
               }

               if (this.isDeletableFileByVersion(oldContent.getThumb_file_id(), contentID, versionId_i) && !this.dao.isUsedThumbAfterVersion(oldContent.getThumb_file_id(), versionID)) {
                  this.deleteFile(oldContent.getThumb_file_id());
               }

               List fileList = this.dao.getFileListByContentIdAndVersion(contentID, versionId_i);
               if (fileList != null) {
                  for(int file_i = 0; file_i < fileList.size(); ++file_i) {
                     Map map1 = (Map)fileList.get(file_i);
                     String fileId = (String)map1.get("FILE_ID");
                     if (this.isDeletableFileByVersion(fileId, contentID, versionId_i)) {
                        this.deleteFile(fileId);
                     }
                  }
               }

               this.deleteVersionContent(contentID, versionId_i);
            }
         } catch (SQLException var11) {
            this.logger.error("", var11);
         }
      }

   }

   public Long setActiveVersion(String contentID, boolean bTrigger) throws SQLException, ConfigException, Exception {
      Long versionId = this.dao.setActiveVersion(contentID, bTrigger);
      String content_version_limit_count = StrUtils.nvl(CommonConfig.get("content.version_limit_count"));
      if (content_version_limit_count.length() < 1) {
         content_version_limit_count = "3";
      }

      long versionCount = Long.parseLong(content_version_limit_count);
      long diffVersion = versionId - versionCount;
      if (diffVersion > 0L) {
         this.deleteContentUnderVersion(contentID, diffVersion);
      }

      return versionId;
   }

   public List getPlaylistListUsingContent(String contentId) throws SQLException {
      return this.dao.getPlaylistListUsingContent(contentId);
   }

   public List getActivePlaylistListUsingContent(String contentId) throws SQLException {
      return this.dao.getActivePlaylistListUsingContent(contentId);
   }

   public List getPlaylistVersionListUsingContent(String playlistId, String contentId) throws SQLException {
      return this.dao.getPlaylistVersionListUsingContent(playlistId, contentId);
   }

   public List getLitePlaylistListUsingContent(String contentId) throws SQLException {
      return this.dao.getLitePlaylistListUsingContent(contentId);
   }

   public int setActiveVersion(String contentID, Long versionID, boolean bTrigger) throws SQLException, ConfigException, Exception {
      return this.dao.setActiveVersion(contentID, versionID, bTrigger);
   }

   public int setAdsContentActiveVersion(String contentId, Long versionId) throws SQLException {
      return this.dao.setAdsContentActiveVersion(contentId, versionId);
   }

   public int setActiveVersionForUploader(String contentID, Long versionID, boolean bTrigger) throws SQLException, ConfigException, Exception {
      int result = this.dao.setActiveVersion(contentID, versionID, bTrigger);

      try {
         String content_version_limit_count = StrUtils.nvl(CommonConfig.get("content.version_limit_count"));
         if (content_version_limit_count.length() < 1) {
            content_version_limit_count = "3";
         }

         long versionCount = Long.parseLong(content_version_limit_count);
         long diffVersion = versionID - versionCount;
         if (diffVersion > 0L) {
            this.deleteContentUnderVersion(contentID, diffVersion);
         }
      } catch (Exception var10) {
      }

      return result;
   }

   public int setContentModifiedDate(String contentID) throws SQLException, ConfigException {
      return this.dao.setContentModifiedDate(contentID);
   }

   public int addFile(ContentFile file) throws SQLException, ConfigException {
      return this.dao.addFile(file);
   }

   public int deleteFile(String fileID) throws SQLException {
      int result = 0;
      StringBuffer file_folder = new StringBuffer();
      ArrayList files = new ArrayList();

      try {
         file_folder.append(CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar));
         file_folder.append(File.separator);
         file_folder.append("contents_home");
         file_folder.append(File.separator);
         file_folder.append(fileID);
         File delFolder = SecurityUtils.getSafeFile(file_folder.toString());
         if (delFolder != null) {
            String[] delFiles = delFolder.list();
            if (delFiles != null) {
               for(int i = 0; i < delFiles.length; ++i) {
                  files.add(file_folder.toString() + File.separator + delFiles[i]);
               }
            }
         }

         files.add(file_folder.toString());
         deleteFileDao.insert((List)files);
         result = this.dao.deleteFile(fileID);
      } catch (ConfigException var8) {
         this.logger.error("", var8);
      }

      return result;
   }

   public void deleteFileInfoIfNoExistFile(String fileID) {
      StringBuffer file_folder = new StringBuffer();

      try {
         file_folder.append(CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar));
         file_folder.append(File.separator);
         file_folder.append("contents_home");
         file_folder.append(File.separator);
         file_folder.append(fileID);
         File delFolder = SecurityUtils.getSafeFile(file_folder.toString());
         if (delFolder.exists() && delFolder.listFiles().length > 0) {
            return;
         }

         this.dao.deleteFileInfoIfNoExistFile(fileID);
      } catch (ConfigException | SQLException var4) {
         this.logger.error("", var4);
      }

   }

   public int setFileHashCode(String fileID, String hashCode) throws SQLException {
      return this.dao.setFileHashCode(fileID, hashCode);
   }

   public int setFilePath(String fileId, String filePath) throws SQLException {
      return this.dao.setFilePath(fileId, filePath);
   }

   public String getHashCodeFromContentByFileName(String fileName, String fileType) throws SQLException {
      return this.dao.getHashCodeFromContentByFileName(fileName, fileType);
   }

   public String getHashCodeFromContentByFileNameAndSize(String fileName, String fileType, long fileSize) throws SQLException {
      return this.dao.getHashCodeFromContentByFileNameAndSize(fileName, fileType, fileSize);
   }

   public Long getRootId(String userID) throws SQLException {
      return this.dao.getRootId(userID);
   }

   public Long getTLFDRootId(String userID) throws SQLException {
      return this.dao.getTLFDRootId(userID);
   }

   public Boolean isExistGroupName(String groupName, String userID) throws SQLException {
      return this.dao.isExistGroupName(groupName, userID);
   }

   public Boolean isExistGroupName(String groupName, String userID, long organization_id) throws SQLException {
      return this.dao.isExistGroupName(groupName, userID, organization_id);
   }

   public long getGroupId(String groupName, String userID, long organization_id) throws SQLException {
      return this.dao.getGroupId(groupName, userID, organization_id);
   }

   public Long getGroupId(String contentID) throws SQLException {
      return this.dao.getGroupId(contentID);
   }

   public String getGroupName(Long groupID) throws SQLException {
      return this.dao.getGroupName(groupID);
   }

   public Group getGroupInfo(Long groupID) throws SQLException {
      return this.dao.getGroupInfo(groupID);
   }

   public List getGroupList(String creatorID) throws SQLException {
      return this.dao.getGroupList(creatorID);
   }

   public List getGroupList(String creatorID, long organization_id) throws SQLException {
      return this.dao.getGroupList(creatorID, organization_id);
   }

   public Long addGroup(Group group) throws SQLException, ConfigException {
      return this.dao.addGroup(group);
   }

   public Long addGroup(Group group, long organization_id) throws SQLException, ConfigException {
      return this.dao.addGroup(group, organization_id);
   }

   public Long addDefaultGroup(String userID) throws SQLException, ConfigException {
      return this.dao.addDefaultGroup(userID);
   }

   public Long addDefaultGroup(String userID, long organization_id) throws SQLException, ConfigException {
      return this.dao.addDefaultGroup(userID, organization_id);
   }

   public int setGroupInfo(Group group) throws SQLException {
      return this.dao.setGroupInfo(group);
   }

   public Boolean isDeletableGroup(Long groupID) throws SQLException {
      return this.dao.isDeletableGroup(groupID);
   }

   public List getGroupedContentIdList(Long groupID) throws SQLException {
      return this.dao.getGroupedContentIdList(groupID);
   }

   public int deleteGroup(Long groupID) throws SQLException {
      return this.dao.deleteGroup(groupID);
   }

   public List getChildGroupList(Long group_id, boolean recursive, String creator_id) throws SQLException {
      return this.dao.getChildGroupList(group_id, recursive, creator_id);
   }

   public List getChildGroupList(Long group_id, boolean recursive, String creatorID, long organizationId) throws SQLException {
      return this.dao.getChildGroupList(group_id, recursive, creatorID, organizationId);
   }

   public List getChildGroupIdList(int group_id, boolean recursive) throws SQLException {
      return this.dao.getChildGroupIdList(group_id, recursive);
   }

   public int copyContents(String userId, long targetOrgan) throws SQLException, ConfigException {
      Map groupMap = new HashMap();
      Long oldParentGroupId = this.dao.getRootId(userId);
      List groupList = this.dao.getGroupList(userId);

      for(int i = 0; i < groupList.size(); ++i) {
         Group group = (Group)groupList.get(i);
         Long oldGroupId = group.getGroup_id();
         Long newParentGroupId = -9L;
         Long newGroupId = 0L;
         Iterator contentList;
         if (group.getP_group_id() != -1L) {
            contentList = groupMap.entrySet().iterator();

            while(contentList.hasNext()) {
               Entry entry = (Entry)contentList.next();
               if (((Long)entry.getKey()).equals(group.getP_group_id())) {
                  newParentGroupId = (Long)entry.getValue();
                  break;
               }
            }

            group.setP_group_id(newParentGroupId);
         }

         if (group.getGroup_depth() == 0L || newParentGroupId >= 0L) {
            if (!this.dao.isExistGroupName(group.getGroup_name(), userId, targetOrgan)) {
               newGroupId = this.dao.addGroup(group, targetOrgan);
               groupMap.put(group.getGroup_id(), newGroupId);
            } else {
               newGroupId = this.dao.getGroupId(group.getGroup_name(), userId, targetOrgan);
               groupMap.put(group.getGroup_id(), newGroupId);
            }

            contentList = null;
            Map map = new HashMap();
            map.put("creatorID", userId);
            map.put("groupID", String.valueOf(group.getGroup_id()));
            map.put("listType", "GROUPED");
            List contentList = this.dao.getContentList(map);
            if (contentList != null) {
               for(int j = 0; j < contentList.size(); ++j) {
                  Content content = (Content)contentList.get(j);
                  String oldContentId = content.getContent_id();
                  content.setGroup_id(newGroupId);
                  String newContentId = UUID.randomUUID().toString().toUpperCase();
                  content.setContent_id(newContentId);
                  content.setOrganization_id(targetOrgan);
                  this.dao.addContentInfo(content);
                  List versionList = this.dao.getContentAllVerInfo(oldContentId);

                  for(int k = 0; k < versionList.size(); ++k) {
                     Content version = (Content)versionList.get(k);
                     version.setContent_id(newContentId);
                     this.dao.addContentVersionInfo(version);
                     List fileList = this.dao.getFileList(oldContentId, version.getVersion_id());

                     for(int l = 0; l < fileList.size(); ++l) {
                        ContentFile file = (ContentFile)fileList.get(l);
                        this.dao.addMapContentFile(newContentId, version.getVersion_id(), file.getFile_id());
                     }
                  }

                  this.dao.addMapGroupContent(newContentId, newGroupId);
                  this.dao.setContentGroup(oldContentId, oldParentGroupId);
               }
            }
         }

         if (group.getGroup_depth() > 0L) {
            this.dao.deleteGroup(oldGroupId);
         }
      }

      return 1;
   }

   public PagedListInfo getPagedList(int startPos, int pageSize, Map condition, String section) throws Exception {
      int totCount = 0;
      List retList = null;
      if (section.equals("getSearchList")) {
         totCount = this.dao.getSearchListCnt(condition);
         retList = this.dao.getSearchListPage(condition, startPos, pageSize);
      } else if (section.equals("getContentList")) {
         totCount = this.dao.getContentListCnt(condition);
         retList = this.dao.getContentListPage(condition, startPos, pageSize);
      } else if (section.equals("getActiveFileList")) {
         totCount = this.dao.getActiveFileListCnt((String)condition.get("contentID"));
         retList = this.dao.getActiveFileListPage((String)condition.get("contentID"), startPos, pageSize);
      } else if (section.equals("getFileList")) {
         totCount = this.dao.getFileListCnt((String)condition.get("contentID"), new Long((String)condition.get("versionID")));
         retList = this.dao.getFileListPage((String)condition.get("contentID"), new Long((String)condition.get("versionID")), startPos, pageSize);
      } else if (section.equals("getContentListByFilter")) {
         totCount = this.dao.getContentListByFilterCnt(condition);
         retList = this.dao.getContentListByFilter(condition, startPos, pageSize);
      }

      return new PagedListInfo(retList, totCount);
   }

   public int updateVwlVersion(String contentID, int vwlVersion) throws Exception {
      return this.dao.updateVwlVersion(contentID, vwlVersion);
   }

   public List getAllContentListByType(boolean isAll, String mediaType, boolean canReadUnsharedContent, String currentUserId, Long currentUserOrganId) throws SQLException {
      return this.dao.getAllContentListByType(isAll, mediaType, canReadUnsharedContent, currentUserId, currentUserOrganId);
   }

   public List getAllContentListByUser(String creatorID, boolean canReadUnsharedContent, Long currentUserOrganId) throws SQLException {
      return this.dao.getAllContentListByUser(creatorID, canReadUnsharedContent, currentUserOrganId);
   }

   public boolean updateDatalinkLFDToContentInfo(String contentId, long versionId, long pageCount) throws Exception {
      return this.dao.updateDatalinkLFDToContentInfo(contentId, versionId, pageCount);
   }

   public List getFtpContentSettingList() throws Exception {
      return this.dao.getFtpContentSettingList();
   }

   public List getFtpContentSettingByContentId(String contentId) throws SQLException {
      return this.dao.getFtpContentSettingByContentId(contentId);
   }

   public ContentFile getContentFileInfoByFileId(String fileId) throws SQLException {
      return this.dao.getContentFileInfoByFileId(fileId);
   }

   public List getFileListByContentId(String contentId) throws SQLException {
      return this.dao.getFileListByContentId(contentId);
   }

   public List getFileListByContentIdAndVersion(String contentId, long versionId) throws SQLException {
      return this.dao.getFileListByContentIdAndVersion(contentId, versionId);
   }

   public Content getContentInfoByContentName(String contentName) throws SQLException {
      return this.dao.getContentInfoByContentName(contentName);
   }

   public long getVersionInfoByContentId(String contentId) throws SQLException {
      return this.dao.getVersionInfoByContentId(contentId);
   }

   public void updateContentVersionInfoWithFileId(String contentId, String mainFileId, long version) throws SQLException, ConfigException {
      this.dao.updateContentVersionInfoWithFileId(contentId, mainFileId, version);
   }

   public List getCifsContentSettingList() throws Exception {
      return this.dao.getCifsContentSettingList();
   }

   public List getCifsContentSettingByContentId(String contentId) throws SQLException {
      return this.dao.getCifsContentSettingByContentId(contentId);
   }

   public List getUrlContentSettingByContentId(String contentId) throws SQLException {
      return this.dao.getUrlContentSettingByContentId(contentId);
   }

   public List getAdsContentSettingByContentId(String contentId) throws SQLException {
      return this.dao.getAdsContentSettingByContentId(contentId);
   }

   public void updateAdsSettingAsDeleted(String contentId, String isDeleted) throws SQLException, ConfigException {
      this.dao.updateAdsSettingAsDeleted(contentId, isDeleted);
   }

   public void updateAdsSettingContentName(String contentId, String contentName) throws SQLException {
      this.dao.updateAdsSettingContentName(contentId, contentName);
   }

   public int addFtpSetting(String contentId, String ftpContentName, int port, String ftpIP, String ftpLoginId, String ftpPassword, String ftpDirectory, long ftpRefreshInterval, String isSsl, String canRefresh, long loginRetryMaxCount, long loginRetryCount, String canLoginRetry) throws SQLException, ConfigException {
      return this.dao.addFtpSetting(contentId, ftpContentName, port, ftpIP, ftpLoginId, ftpPassword, ftpDirectory, ftpRefreshInterval, isSsl, canRefresh, loginRetryMaxCount, loginRetryCount, canLoginRetry);
   }

   public int addCifsSetting(String contentId, String cifsContentName, String cifsIP, String cifsLoginId, String cifsPassword, String cifsDirectory, long cifsRefreshInterval, String canRefresh, long loginRetryMaxCount, long loginRetryCount, String canLoginRetry) throws SQLException, ConfigException {
      return this.dao.addCifsSetting(contentId, cifsContentName, cifsIP, cifsLoginId, cifsPassword, cifsDirectory, cifsRefreshInterval, canRefresh, loginRetryMaxCount, loginRetryCount, canLoginRetry);
   }

   public int addUrlSetting(String contentId, String urlContentName, String urlAddress) throws SQLException {
      return this.dao.addUrlSetting(contentId, urlContentName, urlAddress);
   }

   public int updateUrlSetting(String contentId, String urlContentName, String urlAddress) throws SQLException {
      int result = this.dao.updateUrlSetting(contentId, urlContentName, urlAddress);
      if (result == 0) {
         result = this.addUrlSetting(contentId, urlContentName, urlAddress);
      }

      return result;
   }

   public void updateFtpSettingAsDeleted(String contentId, String isDeleted) throws SQLException, ConfigException {
      this.dao.updateFtpSettingAsDeleted(contentId, isDeleted);
   }

   public void updateCifsSettingAsDeleted(String contentId, String isDeleted) throws SQLException, ConfigException {
      this.dao.updateCifsSettingAsDeleted(contentId, isDeleted);
   }

   public void updateUrlSettingAsDeleted(String contentId, String isDeleted) throws SQLException {
      this.dao.updateUrlSettingAsDeleted(contentId, isDeleted);
   }

   public Long getContentNextVer(String contentID) throws SQLException {
      return this.dao.getContentNextVer(contentID);
   }

   public String getCodeFile(String fileType) throws SQLException {
      return this.dao.getCodeFile(fileType);
   }

   public long getGroupIdByContentId(String contentID) throws SQLException {
      return this.dao.getGroupIdByContentId(contentID);
   }

   public List getContentIdListByContentFileId(String contentFileId, String excludeContentId) throws SQLException {
      return this.dao.getContentIdListByContentFileId(contentFileId, excludeContentId);
   }

   public List getContentIdListByContentFileId(String contentFileId) throws SQLException {
      return this.dao.getContentIdListByContentFileId(contentFileId);
   }

   public boolean existContentForCidMappingOfUploader(String contentID, String creatorID) throws SQLException {
      return this.dao.existContentForCidMappingOfUploader(contentID, creatorID);
   }

   public boolean isDeletedContentByContentId(String contentID) throws SQLException {
      return this.dao.isDeletedContentByContentId(contentID);
   }

   public int addTemplateContent(String dlkContentID, long versionID, String contentType, String ContentID) throws SQLException {
      int result = true;
      int result = this.dao.addMapTemplateContent(dlkContentID, versionID, contentType, ContentID);
      if (result > 0) {
         result = this.dao.updateUsedTemplateByContentId(ContentID);
      }

      return result;
   }

   public int deleteTemplateContent(String dlkContentID) throws SQLException {
      int result = 1;
      String lfdContentID = this.dao.getLfdContentIdByDlkContentId(dlkContentID);
      if (lfdContentID != null) {
         result = this.dao.updateUsedTemplateByContentId(lfdContentID);
      }

      return result;
   }

   public String getLfdContentIdByDlkContentId(String dlkContentID) throws SQLException {
      return this.dao.getLfdContentIdByDlkContentId(dlkContentID);
   }

   public List getContentIdListByDlkContentId(String dlkContentID, long versionID) throws SQLException {
      return this.dao.getContentIdListByDlkContentId(dlkContentID, versionID);
   }

   public void addContentIdListForDlkContent(String dlkContentID, long versionID, String ContentID) throws SQLException {
      String[] contentId = ContentID.split(",");

      for(int i = 0; i < contentId.length; ++i) {
         this.dao.addMapTemplateContent(dlkContentID, versionID, "NOTLFT", contentId[i]);
      }

   }

   public String getContentIdByTemplateThumbnailFileId(String fileName, String creatorId) throws SQLException {
      return this.dao.getContentIdByTemplateThumbnailFileId(fileName, creatorId);
   }

   public void updateAsTemplateByContentId(String contentId, String creatorId) throws SQLException, ConfigException {
      this.dao.updateAsTemplateByContentId(contentId, creatorId);
   }

   public String getFtpUserIdByContentId(String contentId) throws SQLException {
      return this.dao.getFtpUserIdByContentId(contentId);
   }

   public String getFtpIpByContentId(String contentId) throws SQLException {
      return this.dao.getFtpIpByContentId(contentId);
   }

   public String getFtpPathByContentId(String contentId) throws SQLException {
      return this.dao.getFtpPathByContentId(contentId);
   }

   public String getCifsUserIdByContentId(String contentId) throws SQLException {
      return this.dao.getCifsUserIdByContentId(contentId);
   }

   public String getCifsIpByContentId(String contentId) throws SQLException {
      return this.dao.getCifsIpByContentId(contentId);
   }

   public String getCifsPathByContentId(String contentId) throws SQLException {
      return this.dao.getCifsPathByContentId(contentId);
   }

   public boolean updateContentVersionInfoByContentId(long totalSize, String cId) throws SQLException, ConfigException {
      return this.dao.updateContentVersionInfoByContentId(totalSize, cId);
   }

   public boolean updateFtpSettingByContentId(String contentId, String ftpContentName, String ftpIP, int port, String ftpLoginId, String ftpPassword, String ftpDirectory, long ftpRefreshInterval, String canRefresh, long loginRetryMaxCount, long loginRetryCount, String canLoginRetry) throws SQLException, ConfigException {
      return this.dao.updateFtpSettingByContentId(contentId, ftpContentName, ftpIP, port, ftpLoginId, ftpPassword, ftpDirectory, ftpRefreshInterval, canRefresh, loginRetryMaxCount, loginRetryCount, canLoginRetry);
   }

   public boolean updateCifsSettingByContentId(String contentId, String cifsContentName, String cifsIP, String cifsLoginId, String cifsPassword, String cifsDirectory, long cifsRefreshInterval, String canRefresh, long loginRetryMaxCount, long loginRetryCount, String canLoginRetry) throws SQLException, ConfigException {
      return this.dao.updateCifsSettingByContentId(contentId, cifsContentName, cifsIP, cifsLoginId, cifsPassword, cifsDirectory, cifsRefreshInterval, canRefresh, loginRetryMaxCount, loginRetryCount, canLoginRetry);
   }

   public int deleteFileByFileNameByContentId(String contentId, String fileName) throws SQLException {
      return this.dao.deleteFileByFileNameByContentId(contentId, fileName);
   }

   public int deleteFileFromContentMap(String contentId, String fileId) throws SQLException {
      return this.dao.deleteFileFromContentMap(contentId, fileId);
   }

   public int deleteFileFromContentMapByFileName(String contentId, String fileName) throws SQLException {
      return this.dao.deleteFileFromContentMapByFileName(contentId, fileName);
   }

   public List getContentInfoByTemplateId(String templateId) throws SQLException {
      List resultList = new ArrayList();
      List contentIdList = this.dao.getContentByTemplateId(templateId);
      if (contentIdList != null && contentIdList.size() != 0) {
         for(int j = 0; j < contentIdList.size(); ++j) {
            Map map = (Map)contentIdList.get(j);
            String dlkContentId = (String)map.get("DLK_CONTENT_ID");
            resultList.add(this.dao.getContentName(dlkContentId));
         }
      }

      return resultList;
   }

   public List getContentByTemplateId(String templateId) throws SQLException {
      List resultList = new ArrayList();
      List contentIdList = this.dao.getContentByTemplateId(templateId);
      if (contentIdList != null && contentIdList.size() != 0) {
         for(int j = 0; j < contentIdList.size(); ++j) {
            Map map = (Map)contentIdList.get(j);
            String dlkContentId = (String)map.get("DLK_CONTENT_ID");
            Content activeContent = this.dao.getContentActiveVerInfo(dlkContentId);
            if (activeContent != null) {
               resultList.add(activeContent);
            }
         }
      }

      return resultList;
   }

   public List getDlkContentInfoByElementContentId(String contentId) throws SQLException {
      List resultList = new ArrayList();
      List contentIdList = this.dao.getDlkContentsIncludedElementContentId(contentId);
      if (contentIdList != null && contentIdList.size() != 0) {
         for(int j = 0; j < contentIdList.size(); ++j) {
            Map map = (Map)contentIdList.get(j);
            String dlkContentId = (String)map.get("DLK_CONTENT_ID");
            resultList.add(this.dao.getContentName(dlkContentId));
         }
      }

      return resultList;
   }

   public List getDlkContentIdsByElementContentId(String contentId) throws SQLException {
      List contentIdList = this.dao.getDlkContentsIncludedElementContentId(contentId);
      return contentIdList;
   }

   public int addTemplateElement(String contentId, long versionId, ArrayList templateElementList) throws SQLException {
      int result = 0;
      int templateElementListSize = templateElementList.size();

      for(int i = 0; i < templateElementListSize; ++i) {
         result = this.dao.addTemplateElement(contentId, versionId, (TemplateElement)templateElementList.get(i));
      }

      return result;
   }

   public int addTemplateElement(String contentId, long versionId, TemplateElement templateElement) throws SQLException {
      int result = false;
      int result = this.dao.addTemplateElement(contentId, versionId, templateElement);
      return result;
   }

   public int addTemplateDisplaySize(String contentId, long versionId, float displayWidth, float displayHeight) throws SQLException {
      int result = this.dao.addTemplateDisplaySize(contentId, versionId, displayWidth, displayHeight);
      return result;
   }

   public TemplateDisplay getTemplateDisplaySize(String content_id, long version_id) throws SQLException {
      TemplateDisplay result = this.dao.getTemplateDisplaySize(content_id, version_id);
      return result;
   }

   public List getTemplateElementList(String content_id, long version_id) throws SQLException {
      List result = this.dao.getTemplateElementList(content_id, version_id);
      return result;
   }

   public List getTemplateElementDataList(String content_id, long version_id) throws SQLException {
      List result = this.dao.getTemplateElementDataList(content_id, version_id);
      return result;
   }

   public String getTemplateElementDataListString(String content_id, long version_id) throws SQLException {
      List list = this.dao.getTemplateElementDataList(content_id, version_id);
      int listSize = list.size();
      StringBuffer line = new StringBuffer("");

      for(int i = 0; i < listSize; ++i) {
         TemplateElementData currentTemplateElementData = (TemplateElementData)list.get(i);
         line.append("p" + currentTemplateElementData.getPage_no() + "|");
         line.append("e" + currentTemplateElementData.getElement_no() + "|");
         line.append("i" + currentTemplateElementData.getItem_no() + "|");
         line.append("d" + currentTemplateElementData.getData_no() + "|");
         line.append(currentTemplateElementData.getElement_name() + "|");
         line.append(currentTemplateElementData.getInput_type() + "|");
         line.append(currentTemplateElementData.getElement_type() + "|");
         line.append(currentTemplateElementData.getItem_type() + "|");
         line.append(currentTemplateElementData.getItem_name() + "|");
         line.append(currentTemplateElementData.getIs_inner_datalink() + "|");
         line.append(currentTemplateElementData.getInput_data() + "|");
         line.append(currentTemplateElementData.getValue_location() + "|");
         line.append(currentTemplateElementData.getContent_src() + "|");
         line.append(currentTemplateElementData.getContent_id() + "|");
         line.append(this.dao.getContentName(currentTemplateElementData.getContent_id()) + "|");
         line.append(currentTemplateElementData.getMain_tag() + "|");
         line.append(currentTemplateElementData.getTag_match_type() + "|");
         line.append(currentTemplateElementData.getView_h_v() + "|");
         line.append(currentTemplateElementData.getServer_address() + "|");
         line.append(currentTemplateElementData.getSub_tag() + "|");
         line.append(currentTemplateElementData.getConvert_table() + "|");
         line.append("END");
         if (i < listSize - 1) {
            line.append("^");
         }
      }

      return line.toString();
   }

   public int addTemplateElementData(String lftContentId, long lftVersionId, String dlkContentId, long dlkVersionId, ArrayList templateElementData) throws SQLException {
      int result = 0;
      int templateElementDataListSize = templateElementData.size();

      for(int i = 0; i < templateElementDataListSize; ++i) {
         result = this.dao.addTemplateElementData(lftContentId, lftVersionId, dlkContentId, dlkVersionId, (TemplateElementData)templateElementData.get(i));
      }

      return result;
   }

   public int addTemplateElementData(String lftContentId, long lftVersionId, String dlkContentId, long dlkVersionId, TemplateElementData templateElementData) throws SQLException {
      int result = false;
      int result = this.dao.addTemplateElementData(lftContentId, lftVersionId, dlkContentId, dlkVersionId, templateElementData);
      return result;
   }

   public int addTemplateElementData(String lftContentId, long lftVersionId, String dlkContentId, long dlkVersionId, String templateElementDataList) throws SQLException {
      int result = 0;
      String[] arrContentData = templateElementDataList.split("\\^");
      int arrContentDataSize = arrContentData.length;

      for(int i = 0; i < arrContentDataSize; ++i) {
         result = this.dao.addTemplateElementData(lftContentId, lftVersionId, dlkContentId, dlkVersionId, arrContentData[i]);
      }

      return result;
   }

   public int setTemplateContentParsed(String contentID, Long versionID) throws SQLException {
      int result = false;
      int result = this.dao.setTemplateContentParsed(contentID, versionID);
      return result;
   }

   public int setTemplateContentFileSize(String contentId, long versionId, String fileId, long fileSize) throws SQLException {
      int result = false;
      this.dao.setContentFileSize(fileId, fileSize);
      int result = this.dao.setContentTotalSize(contentId, versionId, fileSize);
      return result;
   }

   public boolean getContentParsingState(String contentId) throws SQLException {
      boolean result = false;
      result = this.dao.getContentParsingState(contentId);
      return result;
   }

   public List getAllTemplateElementDataList(String dlk_content_id, long dlk_version_id) throws SQLException {
      return this.dao.getAllTemplateElementDataList(dlk_content_id, dlk_version_id);
   }

   public void updateThumbnailIdOfDlkByContentId(String templateContentId, String versionId, String contentId) throws SQLException, ConfigException {
      this.dao.updateThumbnailIdOfDlkByContentId(templateContentId, versionId, contentId);
   }

   public List getDlkContentIdByTemplateId(String templateContentId) throws SQLException {
      return this.dao.getDlkContentIdByTemplateId(templateContentId);
   }

   public String getFileInfoByContentIdVersionId(String contentId, String versionId) throws SQLException {
      return this.dao.getFileInfoByContentIdVersionId(contentId, versionId);
   }

   public void copyFile(File srcFile, File destFile) {
      FileInputStream fis = null;
      FileOutputStream fos = null;
      boolean var5 = false;

      try {
         fis = new FileInputStream(srcFile);
         fos = new FileOutputStream(destFile);

         int data;
         while((data = fis.read()) != -1) {
            fos.write(data);
         }
      } catch (Exception var19) {
         System.out.println("[CDAO] Fail to copy");
      } finally {
         try {
            if (fis != null) {
               fis.close();
            }
         } catch (IOException var18) {
            this.logger.error("", var18);
         }

         try {
            if (fos != null) {
               fos.close();
            }
         } catch (IOException var17) {
            this.logger.error("", var17);
         }

      }

   }

   public String getActiveVersionByContentId(String contentId) throws SQLException {
      return this.dao.getActiveVersionByContentId(contentId);
   }

   public String getMediaTypeByContentId(String contentId) throws SQLException {
      return this.dao.getMediaTypeByContentId(contentId);
   }

   public void updateHashCodeByMainFileId(String dlkMainFileId, long fileSize, String hashCode) throws SQLException, ConfigException {
      this.dao.updateHashCodeByMainFileId(dlkMainFileId, fileSize, hashCode);
   }

   public void updateVersionAndMainFileIdInContentVersionInfo(long version, String mainFileId, String contentId) throws ConfigException, SQLException {
      this.dao.updateVersionAndMainFileIdInContentVersionInfo(version, mainFileId, contentId);
   }

   public String getFtpPasswordByContentId(String contentId) throws SQLException {
      return this.dao.getFtpPasswordByContentId(contentId);
   }

   public String getCifsPasswordByContentId(String contentId) throws SQLException {
      return this.dao.getCifsPasswordByContentId(contentId);
   }

   public List getDlkContentIdByIputDataContentId(String iputDataContentId) throws SQLException {
      return this.dao.getDlkContentIdByIputDataContentId(iputDataContentId);
   }

   public String getCreatorIdByContentId(String contentId) throws SQLException {
      return this.dao.getCreatorIdByContentId(contentId);
   }

   public String getIsReadyForNextFtpThread(String contentId) throws SQLException {
      return this.dao.getIsReadyForNextFtpThread(contentId);
   }

   public void setIsReadyForNextFtpThread(String isReady, String contentId) throws SQLException {
      this.dao.setIsReadyForNextFtpThread(isReady, contentId);
   }

   public String getIsReadyForNextCifsThread(String contentId) throws SQLException {
      return this.dao.getIsReadyForNextCifsThread(contentId);
   }

   public void setIsReadyForNextCifsThread(String isReady, String contentId) throws SQLException {
      this.dao.setIsReadyForNextCifsThread(isReady, contentId);
   }

   public List getTempContentList() throws SQLException {
      return this.dao.getTempContentList();
   }

   public int deleteTempContentCompletely(String contentID) throws SQLException {
      return this.dao.deleteTempContentCompletely(contentID);
   }

   public int setContentUnlock() throws SQLException {
      return this.dao.setContentUnlock();
   }

   public void setIsActive(String contentId, String isActive) throws SQLException {
      this.dao.setIsActive(contentId, isActive);
   }

   public void setApprovalStatus(String contentId, String approvalStatus, String approvalOpinion) throws SQLException {
      this.dao.setApprovalStatus(contentId, approvalStatus, approvalOpinion);
   }

   public int modifyDlkContentByConvertData(Content dlkContent) throws SQLException {
      return this.dao.modifyDlkContentByConvertData(dlkContent);
   }

   public List getMediaTypeByDeviceType(String deviceType) throws SQLException {
      List resultList = new ArrayList();
      List list = this.dao.getMediaTypeByDeviceType(deviceType);
      Iterator var4 = list.iterator();

      while(var4.hasNext()) {
         Map map = (Map)var4.next();
         resultList.add(map.get("media_type"));
      }

      return resultList;
   }

   public List getMediaTypeByDeviceTypeAndVersion(String deviceType, Float deviceTypeVersion) throws SQLException {
      List resultList = new ArrayList();
      List list = this.dao.getMediaTypeByDeviceTypeAndVersion(deviceType, deviceTypeVersion);
      Iterator var5 = list.iterator();

      while(var5.hasNext()) {
         Map map = (Map)var5.next();
         resultList.add(map.get("media_type"));
      }

      return resultList;
   }

   public List getFileTypeByDeviceTypeAndVersion(String deviceType, Float deviceTypeVersion) throws SQLException {
      return this.dao.getFileTypeByDeviceTypeAndVersion(deviceType, deviceTypeVersion);
   }

   public List getAllDeviceType() throws SQLException {
      List resultList = new ArrayList();
      List list = this.dao.getAllDeviceType();
      Iterator var3 = list.iterator();

      while(var3.hasNext()) {
         Map map = (Map)var3.next();
         resultList.add(map.get("device_type"));
      }

      return resultList;
   }

   public String checkFileTypeByDeviceTypeAndContendID(String deviceType, float deviceTypeVersion, String contentId) throws SQLException {
      return this.dao.checkFileTypeByDeviceTypeAndContendID(deviceType, deviceTypeVersion, contentId);
   }

   public String isDeleted(String contentId) throws SQLException {
      return this.dao.isDelete(contentId);
   }

   public int setAMSLastMemory(String contentId, Map data) throws SQLException {
      return this.dao.setAMSLastMemory(contentId, data);
   }

   public Map getAMSLastMemory(String contentId) throws SQLException {
      return this.dao.getAMSLastMemory(contentId);
   }

   public boolean updateVwlModelCountInfo(String content_id, String modelCountInfo) throws SQLException {
      return this.dao.updateVwlModelCountInfo(content_id, modelCountInfo);
   }

   public String getContentActiveVerVwtId(String contentId) throws SQLException {
      return this.dao.getContentActiveVerVwtId(contentId);
   }

   public List getDeviceGroupListByMultiVwlContentId(String content_id) throws SQLException {
      return this.dao.getDeviceGroupListByMultiVwlContentId(content_id);
   }

   public int updateUsedTemplateByContentId(String contentId) throws SQLException {
      int result = true;
      int result = this.dao.updateUsedTemplateByContentId(contentId);
      return result;
   }

   public String addDefaultContentForNewSaasUser(String userId, String password, String token, String verticalFolderName) throws ConfigException, MalformedURLException {
      String resultCID = null;
      String CID = null;
      URLConnection urlcon = null;
      InputStream in = null;
      BufferedReader input = null;
      String webAuthorPath = "http://" + StrUtils.nvl(CommonConfig.get("download.server.ip")) + ":" + StrUtils.nvl(CommonConfig.get("download.server.web.port")) + "/" + StrUtils.nvl(CommonConfig.get("webauthor.context")) + "/UploadTemplate?";
      webAuthorPath = webAuthorPath + "username=" + userId + "&password=" + password + "&token=" + token + "&vertical=" + verticalFolderName;
      URL url = new URL(webAuthorPath);

      try {
         urlcon = url.openConnection();
         urlcon.setDoOutput(true);
         urlcon.setDoInput(true);
         in = urlcon.getInputStream();
         if (in != null) {
            input = new BufferedReader(new InputStreamReader(in));
         }

         if (input == null) {
            throw new NullPointerException("input is null");
         }

         String line;
         while((line = input.readLine()) != null) {
            resultCID = resultCID + line;
         }
      } catch (IOException var23) {
         this.logger.error("", var23);
      } catch (NullPointerException var24) {
         this.logger.error("", var24);
      } finally {
         try {
            if (input != null) {
               input.close();
            }
         } catch (IOException var22) {
            this.logger.error("", var22);
         }

      }

      if (resultCID != null) {
         String[] arr = resultCID.split("/");
         if (arr.length == 2) {
            CID = arr[1];
         }
      }

      return CID;
   }

   public void setDeleteLock(String fileID, String deleteLock) throws SQLException {
      this.dao.setDeleteLock(fileID, deleteLock);
   }

   public String getDeleteLock(String fileID) throws SQLException {
      return this.dao.getDeleteLock(fileID);
   }

   public int setVersionId(String contentId, long oldVersionId, long newVersionId) throws SQLException, ConfigException, Exception {
      return this.dao.setVersionId(contentId, oldVersionId, newVersionId);
   }

   public void deleteContentFromDLK(String contentId) throws SQLException {
      this.dao.deleteContentFromDLK(contentId);
   }

   public List getPlaylistInfoUsingContent(String contentId) throws SQLException {
      return this.dao.getPlaylistInfoUsingContent(contentId);
   }

   public void deleteContentIdFromDlkContentIdMap(String contentId) throws SQLException {
      this.dao.deleteContentIdFromDlkContentIdMap(contentId);
   }

   public long getLastestDlkVersionId(String contentId) throws SQLException {
      return this.dao.getLastestDlkVersionId(contentId);
   }

   public long getMaxContentVersionId(String contentId) throws SQLException {
      return this.dao.getMaxContentVersionId(contentId);
   }

   public long getLastestDlkDataVersionId(String contentId) throws SQLException {
      return this.dao.getLastestDlkDataVersionId(contentId);
   }

   public void deleteOldVersionContent(String contentId, long maxVersionId) throws SQLException {
      this.dao.deleteOldVersionContent(contentId, maxVersionId);
   }

   public List getLitePlaylistInfoUsingContent(String contentId) throws SQLException {
      return this.dao.getLitePlaylistInfoUsingContent(contentId);
   }

   public String checkContentValidation(String contentId, Long version) throws Exception {
      this.logger.error("[checkContentValidation] Start to check content validation. contentId : " + contentId + ", Version : " + version);
      String result = ContentConstants.CONT_VALID[0];
      List mainFileList = this.dao.getContentInfoByContentIdAndVersion(contentId, version);
      Map mainFileMap = (Map)mainFileList.get(0);
      String mainMediaType = (String)mainFileMap.get("MEDIA_TYPE");
      Map categoryMap = ContentConstants.getMediaTypeMap();
      if (!categoryMap.containsKey(mainMediaType)) {
         this.logger.error("[checkContentValidation] It is new Type of content.");
         return ContentConstants.CONT_VALID[0];
      } else {
         List dbFileList = this.dao.getFileListByContentIdAndVersion(contentId, version);
         int dbFileSize = false;
         if (dbFileList == null) {
            this.logger.error("[checkContentValidation] ERROR! No matching file in MI_CMS_MAP_VERSION_FILE. ContentId:" + contentId);
            return ContentConstants.CONT_DB_MAP_NOT_EXIST[0];
         } else {
            int dbFileSize = dbFileList.size();
            String mainFileId;
            if (!mainMediaType.equalsIgnoreCase("VWL") && !mainMediaType.equalsIgnoreCase("LFD") && !mainMediaType.equalsIgnoreCase("HTML") && !ContentConstants.getMediaTypeForAuthor().contains(mainMediaType)) {
               if (mainMediaType.equalsIgnoreCase("FTP") || mainMediaType.equalsIgnoreCase("CIFS")) {
                  mainFileId = (String)mainFileMap.get("MAIN_FILE_ID");
                  if (!this.checkFileListFromRemoteContent(mainFileId)) {
                     return ContentConstants.CONT_DB_NOT_EXIST[0];
                  }
               }
            } else {
               mainFileId = (String)mainFileMap.get("MAIN_FILE_ID");
               if (!this.checkFileListFromMainFile(mainFileId)) {
                  return ContentConstants.CONT_DB_NOT_EXIST[0];
               }
            }

            for(int i = 0; i < dbFileSize; ++i) {
               Map dbFilemap = (Map)dbFileList.get(i);
               String file_id = (String)dbFilemap.get("FILE_ID");
               ContentFile contentFile = this.dao.getFileInfo(file_id);
               if (contentFile == null) {
                  this.logger.error("[checkContentValidation] ERROR! No file info in MI_CMS_INFO_FILE. fileId:" + file_id);
                  return ContentConstants.CONT_DB_NOT_EXIST[0];
               }

               if (!mainMediaType.equalsIgnoreCase("VWL") || !contentFile.getFile_type().equalsIgnoreCase("THUMBNAIL")) {
                  result = this.checkCommonValidation(contentId, version, file_id);
                  if (!result.equalsIgnoreCase(ContentConstants.CONT_VALID[0])) {
                     return result;
                  }
               }
            }

            this.logger.error("[UploadContent] All files are made successfully.");
            return result;
         }
      }
   }

   public boolean checkFileListFromMainFile(String mainFileId) throws Exception {
      String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
      CONTENTS_HOME = CONTENTS_HOME + File.separatorChar + mainFileId + File.separatorChar;
      String filePath = "";
      ContentFile mainContentFile = this.dao.getFileInfo(mainFileId);
      if (mainContentFile == null) {
         this.logger.error("[checkContentValidation] VWL or LFD File is not exist in MI_CMS_INFO_FILE. FileId : " + mainFileId);
         return false;
      } else {
         try {
            filePath = CONTENTS_HOME + mainContentFile.getFile_name();
            DocumentBuilderFactory dbf = DocumentUtils.getDocumentBuilderFactoryInstance();
            DocumentBuilder parser = dbf.newDocumentBuilder();
            Document doc = parser.parse(SecurityUtils.getSafeFile(filePath));
            Element contentElement = doc.getDocumentElement();
            NodeList supportFilesList;
            int i;
            Element supportFilesElement;
            NodeList fileIdList;
            int k;
            Element fnameElement;
            String FileId;
            ContentFile tmpContentFile;
            if (contentElement.getElementsByTagName("FileItems") != null) {
               supportFilesList = contentElement.getElementsByTagName("FileItems");
               if (supportFilesList != null) {
                  for(i = 0; i < supportFilesList.getLength(); ++i) {
                     supportFilesElement = (Element)supportFilesList.item(i);
                     if (supportFilesElement.hasChildNodes()) {
                        fileIdList = supportFilesElement.getElementsByTagName("FileItem");
                        this.logger.error("[checkContentValidation] FileItems length in LFD or VWL : " + fileIdList.getLength());

                        for(k = 0; k < fileIdList.getLength(); ++k) {
                           fnameElement = (Element)fileIdList.item(k);
                           FileId = "";
                           if (fnameElement.getAttributes() != null) {
                              FileId = fnameElement.getAttributes().getNamedItem("FileID").getNodeValue();
                              tmpContentFile = this.dao.getFileInfo(FileId);
                              if (tmpContentFile == null) {
                                 this.logger.error("[checkContentValidation] This File is not exist in MI_CMS_INFO_FILE. FileId : " + FileId);
                                 return false;
                              }
                           }
                        }
                     }
                  }
               }
            }

            if (contentElement.getElementsByTagName("SupportFileItems") != null) {
               supportFilesList = contentElement.getElementsByTagName("SupportFileItems");
               if (supportFilesList != null) {
                  for(i = 0; i < supportFilesList.getLength(); ++i) {
                     supportFilesElement = (Element)supportFilesList.item(i);
                     if (supportFilesElement.hasChildNodes()) {
                        fileIdList = supportFilesElement.getElementsByTagName("FileID");

                        for(k = 0; k < fileIdList.getLength(); ++k) {
                           fnameElement = (Element)fileIdList.item(k);
                           FileId = "";
                           if (fnameElement != null) {
                              FileId = fnameElement.getFirstChild().getNodeValue();
                              tmpContentFile = this.dao.getFileInfo(FileId);
                              if (tmpContentFile == null) {
                                 this.logger.error("[checkContentValidation] This File is not exist in MI_CMS_INFO_FILE. FileId : " + FileId);
                                 return false;
                              }
                           }
                        }
                     }
                  }
               }
            }

            return true;
         } catch (Exception var17) {
            this.logger.error("[checkContentValidation] Can't read VWL/LFD file. fileId : " + mainFileId);
            return false;
         }
      }
   }

   public boolean checkFileListFromRemoteContent(String mainFileId) throws Exception {
      String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
      CONTENTS_HOME = CONTENTS_HOME + File.separatorChar + mainFileId + File.separatorChar;
      String filePath = "";
      ContentFile mainContentFile = this.dao.getFileInfo(mainFileId);
      if (mainContentFile != null) {
         try {
            filePath = CONTENTS_HOME + mainContentFile.getFile_name();
            DocumentBuilderFactory dbf = DocumentUtils.getDocumentBuilderFactoryInstance();
            DocumentBuilder parser = dbf.newDocumentBuilder();
            Document doc = parser.parse(SecurityUtils.getSafeFile(filePath));
            Element contentElement = doc.getDocumentElement();
            NodeList fileList = contentElement.getElementsByTagName("FileId");
            this.logger.error("[checkContentValidation] FileItems length in CIFS or FTP : " + fileList.getLength());

            for(int i = 0; i < fileList.getLength(); ++i) {
               Element fnameElement = (Element)fileList.item(i);
               String FileId = "";
               if (fnameElement != null) {
                  FileId = fnameElement.getFirstChild().getNodeValue();
                  ContentFile tmpContentFile = this.dao.getFileInfo(FileId);
                  if (tmpContentFile == null) {
                     this.logger.error("[checkContentValidation] This File is not exist in MI_CMS_INFO_FILE. FileId : " + FileId);
                     return false;
                  }
               }
            }

            return true;
         } catch (Exception var14) {
            this.logger.error("[checkContentValidation] Can't read CIFS/FTP file. fileId : " + mainFileId);
            return false;
         }
      } else {
         this.logger.error("[checkContentValidation] CIFS/FTP File is not exist in MI_CMS_INFO_FILE. FileId : " + mainFileId);
         return false;
      }
   }

   public int getCountOfFilesfromCSD(String contentId, long version) throws Exception {
      new Hashtable();
      String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
      String cmsMetaPath = CONTENTS_HOME + File.separator + "contents_meta";
      String abs_path = cmsMetaPath + File.separator + contentId;
      File csdFile = SecurityUtils.getSafeFile(abs_path);
      this.logger.error("[checkContentValidation] Start to check content validation. contentId : " + contentId + ", Version : " + version);
      if (!csdFile.exists()) {
         this.logger.error("[checkContentValidation] CSD file folder is not exist.");
         return -1;
      } else {
         Hashtable hash;
         try {
            hash = FileUploadHelper.getInstance().readCSD(abs_path + File.separator + "ContentsMetadata.CSD", contentId, version);
         } catch (Exception var10) {
            this.logger.error("[checkContentValidation] Failed to get CSD Hash.\n" + var10.toString());
            return -1;
         }

         if (hash != null) {
            ArrayList csdFileList = (ArrayList)hash.get("ContentFileList");
            if (csdFileList != null) {
               return csdFileList.size();
            } else {
               this.logger.error("[checkContentValidation]CSD files list is null. ");
               return -1;
            }
         } else {
            this.logger.error("[checkContentValidation] CSD Hash is null.\n");
            return -1;
         }
      }
   }

   public String checkCommonValidation(String contentId, long version, String file_id) throws Exception {
      String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
      ContentFile contentFile = this.dao.getFileInfo(file_id);
      if (contentFile == null) {
         this.logger.error("[checkContentValidation] ERROR! No file info in MI_CMS_INFO_FILE. fileId:" + file_id);
         return ContentConstants.CONT_DB_NOT_EXIST[0];
      } else if (this.dao.isExistMapVersionfile(contentId, version, file_id) > 0) {
         String main_url = CONTENTS_HOME + File.separator + contentFile.getFile_id() + File.separator + contentFile.getFile_name();
         File mainFile = SecurityUtils.getSafeFile(main_url);
         if (!mainFile.exists()) {
            this.logger.error("[checkContentValidation] ERROR! The file is not exist in Path=" + File.separator + contentFile.getFile_id() + File.separator + contentFile.getFile_name());
            return ContentConstants.CONT_FILE_NOT_EXIST[0];
         } else {
            long newfileSize = mainFile.length();
            long dbFileSize = contentFile.getFile_size();
            if (newfileSize != dbFileSize) {
               this.logger.error("[checkContentValidation] ERROR! dbFileSize is not same with realFileSize. FileName : " + contentFile.getFile_name() + " , realfileSize : " + newfileSize + ", dbFileSize : " + dbFileSize);
               return ContentConstants.CONT_FILE_SIZE[0];
            } else {
               this.logger.error("[checkContentValidation] This file is normal. FileId:" + file_id + " , FileName : " + contentFile.getFile_name());
               return ContentConstants.CONT_VALID[0];
            }
         }
      } else {
         this.logger.error("[checkContentValidation] ERROR! No file info in MI_CMS_MAP_VERSION_FILE. fileId:" + file_id);
         return ContentConstants.CONT_DB_MAP_NOT_EXIST[0];
      }
   }

   public int getUsedContentCount(long organizationId) throws SQLException {
      return this.dao.getUsedContentCount(organizationId);
   }

   public int getRejectCnt(long organizationId) throws SQLException {
      return this.dao.getRejectCnt(organizationId);
   }

   public int getUnapprovedContentCnt(long organizationId) throws SQLException {
      return this.dao.getUnapprovedContentCnt(organizationId);
   }

   public int getUnapprovedContentCnt() throws SQLException {
      return this.dao.getUnapprovedContentCnt();
   }

   public List getContentListWithThumbnailFromTagId(long tagId) throws SQLException {
      return this.dao.getContentListWithThumbnailFromTagId(tagId);
   }

   public List getContentListWithThumbnailFromTagBoolean(long tagId, long tagConditionId) throws SQLException {
      return this.dao.getContentListWithThumbnailFromTagBoolean(tagId, tagConditionId);
   }

   public List getContentListWithThumbnailFromTagNumber(long tagId, String[] tagConditionEqual, String[] tagConditionUp, String[] tagConditionDown) throws SQLException {
      return this.dao.getContentListWithThumbnailFromTagNumber(tagId, tagConditionEqual, tagConditionUp, tagConditionDown);
   }

   public List getTagContentList(long tagId) throws SQLException {
      return this.dao.getTagContentList(tagId);
   }

   public List getTagFromContentId(String contentId) throws SQLException {
      return this.dao.getTagFromContentId(contentId);
   }

   public List getContentApproverListByContentId(String contentId) throws SQLException {
      return this.dao.getContentApproverListByContentId(contentId);
   }

   public List getContentApproverInfoByUserId(String userId) throws SQLException {
      return this.dao.getContentApproverInfoByUserId(userId);
   }

   public int deleteContentApproverMap(String contentId, String userId) throws SQLException {
      return this.dao.deleteContentApproverMap(contentId, userId);
   }

   public int deleteContentApproverMapByContentId(String contentId) throws SQLException {
      return this.dao.deleteContentApproverMapByContentId(contentId);
   }

   public boolean setThumbnailMap(String contentId, String fileId, long index, String mode) throws SQLException {
      return this.dao.setThumbnailMap(contentId, fileId, index, mode);
   }

   public List getMovieThumbnails(String contentId, String mode) throws SQLException {
      return this.dao.getMovieThumbnails(contentId, mode);
   }

   public boolean deleteContentApproverMapByUserId(String userId) throws SQLException {
      return this.dao.deleteContentApproverMapByUserId(userId);
   }

   public String getThumbIdByMainFileId(String main_file_id) throws SQLException {
      return this.dao.getThumbIdByMainFileId(main_file_id);
   }

   public List getTLFDGroupList(String creatorID) throws SQLException {
      return this.dao.getTLFDGroupList(creatorID);
   }

   public List getTLFDChildGroupList(Long group_id, boolean recursive, String creator_id) throws SQLException {
      return this.dao.getTLFDChildGroupList(group_id, recursive, creator_id);
   }

   public Long getTLFDOrganizationIdByGroupId(long groupId) throws SQLException {
      return this.dao.getTLFDOrganizationIdByGroupId(groupId);
   }

   public List getTLFDGroupIdsByOrgId(long orgId) throws SQLException {
      return this.dao.getTLFDGroupIdsByOrgId(orgId);
   }

   public List getTLFDListByGroupId(long groupId) throws SQLException {
      return this.dao.getTLFDListByGroupId(groupId);
   }

   public int setTLFDGroup(String contentId, Long groupId) throws SQLException, ConfigException {
      return this.dao.setTLFDGroup(contentId, groupId);
   }

   public Content getTLFDInfo(String contentID) throws SQLException {
      return this.dao.getTLFDInfo(contentID);
   }

   public int addTLFDGroup(Long groupId, String groupName, Long pGroupId, Long groupDepth, String creatorId, Long organizationId) throws SQLException {
      return this.dao.addTLFDGroup(groupId, groupName, pGroupId, groupDepth, creatorId, organizationId);
   }

   public List getSupportedDeviceTypeByContentType(String contentType) throws SQLException {
      return this.dao.getSupportedDeviceTypeByContentType(contentType);
   }

   public boolean updateContentForStartPageRefreshInterval(Content content) throws SQLException {
      return this.dao.updateContentForStartPageRefreshInterval(content);
   }

   public boolean chkOrganizationByContentId(String organizationFromUser, String id) throws Exception {
      UserInfo userInfo = UserInfoImpl.getInstance();
      Content content = this.dao.getContentActiveVerInfo(id);
      long groupId = content.getOrganization_id();
      String organization = userInfo.getOrganNameByRootGroupId(groupId);
      long mngOrgId = SecurityUtils.getUserContainer().getUser().getRoot_group_id();
      if (mngOrgId == groupId) {
         return true;
      } else if (organization != null && organization.equals(organizationFromUser)) {
         return true;
      } else {
         ShareFolderInfo shareFolderInfo = ShareFolderInfoImpl.getInstance();
         return shareFolderInfo.isExistContentByContentIdAndOrgGroupId(id, mngOrgId) > 0;
      }
   }

   public boolean isForceDeletableContent(String contentId) throws SQLException {
      return this.dao.isForceDeletableContent(contentId);
   }

   public boolean checkMappingSchedule(long groupId) throws SQLException {
      return this.dao.checkMappingSchedule(groupId);
   }

   public int addContentApproverMap(String contentId, String userId) throws SQLException {
      return this.dao.addContentApproverMap(contentId, userId);
   }

   public Content getThumbnailByFileId(String fileId) throws SQLException {
      return this.dao.getThumbnailByFileId(fileId);
   }

   public Map getThumbnailByThumbnailFileId(String thumbnailFileId) throws SQLException {
      return this.dao.getThumbnailByThumbnailFileId(thumbnailFileId);
   }

   public List getShareFolderList(long groupId) throws SQLException {
      return this.dao.getShareFolderList(groupId);
   }

   public List getContentPollingHistories(String contentId) throws SQLException {
      return this.dao.getContentPollingHistory(contentId);
   }

   public long getDefaultContentGroupId(String userId, long orgnizationId) throws SQLException {
      return this.dao.getDefaultContentGroupId(userId, orgnizationId);
   }

   public int addPollingInfo(String contentId, Date pollingTime, String pollingStatus, int fileCount, String statusDescription, String creatorId) throws SQLException {
      int result = this.dao.addPollingInfo(contentId, pollingTime, pollingStatus, fileCount, statusDescription, creatorId);
      this.dao.deleteOldPollingInfoAndPollingFileInfo(contentId);
      return result;
   }

   public int addPollingFileInfo(String contentId, Date pollingTime, String fileName, long fileSize, String fileStatus, String creatorId) throws SQLException {
      return this.dao.addPollingFileInfo(contentId, pollingTime, fileName, fileSize, fileStatus, creatorId);
   }

   public int deletePollingInfo(String contentId) throws SQLException {
      return this.dao.deletePollingInfo(contentId);
   }

   public int deletePollingFileInfo(String contentId) throws SQLException {
      return this.dao.deletePollingFileInfo(contentId);
   }

   public boolean isDeletableFileFromContents(String fileId) throws SQLException {
      return this.dao.getOneContentByFileId(fileId) == null;
   }

   public String getFileIdFromContentByFileNameAndSize(String contentId, String fileName, long fileSize) throws SQLException {
      return this.dao.getFileIdFromContentByFileNameAndSize(contentId, fileName, fileSize);
   }

   public List getPollableCifsContentSettingList() throws Exception {
      return this.dao.getPollableCifsContentSettingList();
   }

   public List getPollableFtpContentSettingList() throws Exception {
      return this.dao.getPollableFtpContentSettingList();
   }

   public void setIsReadyForAllCifsThread(String isReady) throws SQLException {
      this.dao.setIsReadyForAllCifsThread(isReady);
   }

   public void setIsReadyForAllFtpThread(String isReady) throws SQLException {
      this.dao.setIsReadyForAllFtpThread(isReady);
   }

   public int getCntContentByOrganizationId(Long organizationId) throws SQLException {
      return this.dao.getCntContentByOrganizationId(organizationId);
   }

   private void changeGroupIdOf_MI_CMS_MAP_GROUP_CONTENT(Long groupId, String fromUserId, Long organizationId) throws SQLException {
      this.dao.changeGroupIdOf_MI_CMS_MAP_GROUP_CONTENT(groupId, fromUserId, organizationId);
   }

   private void changeCreatorIdOf_MI_CMS_INFO_CONTENT(String fromUserId, String toUserId, Long organizationId) throws SQLException {
      this.dao.changeCreatorIdOf_MI_CMS_INFO_CONTENT(fromUserId, toUserId, organizationId);
   }

   private void updateRulesetGroup(String current_creator, String new_creator) throws SQLException {
      this.dao.updateRulesetGroup(current_creator, new_creator);
   }

   private void changeCreatorIdOf_MI_CMS_INFO_CONTENT_VERSION(String fromUserId, String toUserId, Long organizationId) throws SQLException {
      this.dao.changeCreatorIdOf_MI_CMS_INFO_CONTENT_VERSION(fromUserId, toUserId, organizationId);
   }

   private void changeCreatorIdOf_MI_CMS_INFO_FILE(String fromUserId, String toUserId, Long organizationId) throws SQLException {
      this.dao.changeCreatorIdOf_MI_CMS_INFO_FILE(fromUserId, toUserId, organizationId);
   }

   public void deleteGroupByCreatorId(String creatorId) throws SQLException {
      this.dao.deleteGroupByCreatorId(creatorId);
   }

   public void transferContentToAdmin(User user, String[] deleteUsers) throws SQLException {
      try {
         if (user != null && user.isMu()) {
            List userManageGroupList = null;
            UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
            userManageGroupList = userGroupInfo.getMngGroupListByUserId(user.getUser_id());
            UserInfo userInfo = UserInfoImpl.getInstance();
            if (userManageGroupList != null) {
               Iterator var6 = userManageGroupList.iterator();

               while(var6.hasNext()) {
                  UserGroup userGroup = (UserGroup)var6.next();
                  long rootGroupId = userGroup.getRoot_group_id();
                  this.transferContentToAdminSub(user, rootGroupId, deleteUsers);
               }
            }
         } else {
            this.transferContentToAdminSub(user, user.getRoot_group_id(), deleteUsers);
         }
      } catch (Exception var10) {
         this.logger.error("", var10);
      }

   }

   public void transferContentToAdmin(User user) throws SQLException {
      try {
         if (user != null && user.isMu()) {
            List userManageGroupList = null;
            UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
            userManageGroupList = userGroupInfo.getMngGroupListByUserId(user.getUser_id());
            if (userManageGroupList != null) {
               Iterator var4 = userManageGroupList.iterator();

               while(var4.hasNext()) {
                  UserGroup userGroup = (UserGroup)var4.next();
                  long rootGroupId = userGroup.getRoot_group_id();
                  this.transferContentToAdminSub(user, rootGroupId);
               }
            }
         } else {
            this.transferContentToAdminSub(user, user.getRoot_group_id());
         }
      } catch (Exception var8) {
         this.logger.error("", var8);
      }

   }

   public void transferContentToAdmin(User user, long orgId) throws SQLException {
      try {
         this.transferContentToAdminSub(user, orgId);
      } catch (Exception var5) {
         this.logger.error("", var5);
      }

   }

   private void transferContentToAdminSub(User user, long rootGroupId) {
      try {
         UserInfo userInfo = UserInfoImpl.getInstance();
         User admin = userInfo.getAdminOfOrganizationByRootGroupId(rootGroupId);
         long defaultContentGroupId = this.getDefaultContentGroupId(admin.getUser_id(), rootGroupId);
         this.changeGroupIdOf_MI_CMS_MAP_GROUP_CONTENT(defaultContentGroupId, user.getUser_id(), rootGroupId);
         this.changeCreatorIdOf_MI_CMS_INFO_FILE(user.getUser_id(), admin.getUser_id(), rootGroupId);
         this.changeCreatorIdOf_MI_CMS_INFO_CONTENT_VERSION(user.getUser_id(), admin.getUser_id(), rootGroupId);
         this.changeCreatorIdOf_MI_CMS_INFO_CONTENT(user.getUser_id(), admin.getUser_id(), rootGroupId);
         this.updateRulesetGroup(user.getUser_id(), admin.getUser_id());
      } catch (Exception var8) {
         this.logger.error("", var8);
      }

   }

   private void transferContentToAdminSub(User user, long rootGroupId, String[] deleteUsers) {
      try {
         UserInfo userInfo = UserInfoImpl.getInstance();
         User admin = userInfo.getAdminOfOrganizationNotInDeleteUsers(rootGroupId, deleteUsers);
         long defaultContentGroupId = this.getDefaultContentGroupId(admin.getUser_id(), rootGroupId);
         this.changeGroupIdOf_MI_CMS_MAP_GROUP_CONTENT(defaultContentGroupId, user.getUser_id(), rootGroupId);
         this.changeCreatorIdOf_MI_CMS_INFO_FILE(user.getUser_id(), admin.getUser_id(), rootGroupId);
         this.changeCreatorIdOf_MI_CMS_INFO_CONTENT_VERSION(user.getUser_id(), admin.getUser_id(), rootGroupId);
         this.changeCreatorIdOf_MI_CMS_INFO_CONTENT(user.getUser_id(), admin.getUser_id(), rootGroupId);
         this.updateRulesetGroup(user.getUser_id(), admin.getUser_id());
      } catch (Exception var9) {
         this.logger.error("", var9);
      }

   }

   public long getCntAllContents(String creatorId, Long organizationId) throws SQLException {
      try {
         return this.dao.getCntAllContents(creatorId, organizationId);
      } catch (Exception var4) {
         this.logger.error("", var4);
         return -1L;
      }
   }

   public String getPlayTimeOfLftByDlk(String contentId) throws SQLException {
      return this.dao.getPlayTimeOfLftByDlk(contentId);
   }

   public boolean setContentPlayTime(String contentId, String playTime) throws SQLException {
      return this.dao.setContentPlayTime(contentId, playTime);
   }

   public boolean setExpirationDate(String contentId, String expirationDate) throws SQLException {
      return this.dao.setExpirationDate(contentId, expirationDate);
   }

   public List getExpiredContentList() throws SQLException {
      return this.dao.getExpiredContentList();
   }

   public List getGroupList(long organization_id) throws SQLException {
      return this.dao.getGroupList(organization_id);
   }

   public List getTLFDGroupListByOrgId(Long organizationId) throws SQLException {
      return this.dao.getTLFDGroupListByOrgId(organizationId);
   }

   public List getContentIdListByContentName(String[] contentNameList) throws SQLException {
      return this.dao.getContentIdListByContentName(contentNameList);
   }

   public List getContentIdListByRegex(String regex) throws SQLException {
      return this.dao.getContentIdListByRegex(regex);
   }

   public Map getHDThumbnailInfo(String contentId) throws SQLException {
      Map result = new HashMap();
      Content content = this.getContentActiveVerInfo(contentId);

      try {
         if (content.getThumb_file_name().endsWith("_THUMBNAIL")) {
            result.put("result", "failed");
            return result;
         }

         if (!content.getMedia_type().equalsIgnoreCase("IMAGE") && !content.getMedia_type().equalsIgnoreCase("MOVIE")) {
            result.put("result", "failed");
            return result;
         }

         String CONTENTS_HOME = ContentUtils.getContentsHome();
         String THUMBNAIL_HOME = ContentUtils.getThumbnailHome();
         StringBuilder sb = new StringBuilder();
         sb.append(THUMBNAIL_HOME);
         sb.append(File.separator);
         sb.append(content.getThumb_file_id());
         sb.append(File.separator);
         sb.append(FilenameUtils.removeExtension(content.getThumb_file_name()));
         sb.append("_HD.PNG");
         File hdThumbnailFile = new File(sb.toString());
         sb.setLength(0);
         sb.append(CONTENTS_HOME);
         sb.append(File.separator);
         sb.append(content.getMain_file_id());
         sb.append(File.separator);
         sb.append(content.getMain_file_name());
         File contentFile = new File(sb.toString());
         if (!hdThumbnailFile.exists()) {
            FileManager fileManager = FileManagerImpl.getInstance();
            Map hdThumbnailMap = fileManager.createThumbnailFile(contentFile, THUMBNAIL_HOME, content.getThumb_file_id(), content, 1280, 720, "_HD.PNG");
            if (hdThumbnailMap != null && hdThumbnailMap.get("status") != null && hdThumbnailMap.get("status").equals("error")) {
               this.logger.error("HD Thumbnail create error.");
               result.put("result", "failed");
               return result;
            }

            hdThumbnailFile = (File)hdThumbnailMap.get("file");
         }

         result.put("filename", hdThumbnailFile.getName());
         result.put("filepath", hdThumbnailFile.getParent());
         result.put("thumbnailFileId", content.getThumb_file_id());
         result.put("result", "success");
      } catch (Exception var11) {
         result.put("result", "failed");
      }

      return result;
   }

   public List getContentGroupBySearch(String searchText, long organizationId, String userId) throws SQLException {
      return this.dao.getContentGroupBySearch(searchText, organizationId, userId);
   }

   public List getTLFDGroupBySearch(String searchText, Long organizationId) throws SQLException {
      return this.dao.getTLFDGroupBySearch(searchText, organizationId);
   }

   public List getContentGroupBySearch(String searchText, long organizationId) throws SQLException {
      return this.dao.getContentGroupBySearch(searchText, organizationId, (String)null);
   }

   public List getParentsGroupList(int pGroupId) throws SQLException {
      return this.dao.getParentsGroupList(pGroupId);
   }

   public Long getOrganizationIdByGroupId(Long groupId) throws SQLException {
      return this.dao.getOrganizationIdByGroupId(groupId);
   }

   public Long getTLFDOrganizationIdByGroupId(Long groupId) throws SQLException {
      return this.dao.getTLFDOrganizationIdByGroupId(groupId);
   }

   public List getContentIdsByFileId(String fileId) throws SQLException {
      return this.dao.getContentIdsByFileId(fileId);
   }

   public List getSubGroupList(Long group_id, boolean recursive, Long organization) throws SQLException {
      return this.dao.getSubGroupList(group_id, recursive, organization);
   }

   public Group getTLFDGroupInfo(Long groupId) throws SQLException {
      return this.dao.getTLFDGroupInfo(groupId);
   }

   public int V2GetUnapprovedContentCnt() throws SQLException {
      return this.dao.V2GetUnapprovedContentCnt();
   }

   public int V2GetUnapprovedContentCnt(Long organizationId) throws SQLException {
      return this.dao.V2GetUnapprovedContentCnt(organizationId);
   }

   public int getAllContentCount() throws SQLException {
      return this.dao.getAllContentCount();
   }

   public List getContentCountByContentType() throws SQLException {
      return this.dao.getContentCountByContentType();
   }

   public int getContentGroupTotalCount() throws SQLException {
      return this.dao.getContentGroupTotalCount();
   }

   public List getAssignedAdvertisementByContentId(String contentId, String type) throws SQLException {
      return this.dao.getAssignedAdvertisementByContentId(contentId, type);
   }

   public List getValueIdsByContentIdAndIndexId(String contentId, String indexId) throws SQLException {
      return this.dao.getValueIdsByContentIdAndIndexId(contentId, indexId);
   }

   public boolean setAdvertisement(String contentId, String indexId, List assignValueIds, List removeValueIds) {
      return this.dao.setAdvertisement(contentId, indexId, assignValueIds, removeValueIds);
   }

   public int deleteProductCodeHistoryByDate(String date) throws SQLException {
      return this.dao.deleteProductCodeHistoryByDate(date);
   }

   public List getContentProductCodeHistory(String contentId) throws SQLException {
      return this.dao.getContentProductCodeHistory(contentId);
   }

   public int removeAssignedAdvertisement(String contentId) throws SQLException {
      return this.dao.removeAssignedAdvertisement(contentId);
   }

   public int getContentFileCount(String type) throws SQLException {
      return this.dao.getContentFileCount(type);
   }

   public List getUnapprovedContentIdList(Long orgId) throws SQLException {
      return this.dao.getUnapprovedContentIdList(orgId);
   }

   public List getContentIdListByTemplateId(String templateId) throws SQLException {
      return this.dao.getContentByTemplateId(templateId);
   }

   public List getAdsContentPublisherInfoSuggestionListByUser(String userId, int count) throws SQLException {
      return this.dao.getAdsContentPublisherInfoSuggestionListByUser(userId, count);
   }

   public List getAdsContentAdUnitIdSuggestionListByUser(String userId, int count) throws SQLException {
      return this.dao.getAdsContentAdUnitIdSuggestionListByUser(userId, count);
   }

   public int existPublisherInfoByPublisherId(String publisherId, String userId) throws SQLException {
      return this.dao.existPublisherInfoByPublisherId(publisherId, userId);
   }

   public int existAdUnitIdById(String adUnitId, String userId) throws SQLException {
      return this.dao.existAdUnitIdById(adUnitId, userId);
   }

   public boolean updateAdsContentPublisherSuggestionInfo(V2AdsContentPublisherInfo publisherInfo, String userId) throws SQLException {
      return this.dao.updateAdsContentPublisherSuggestionInfo(publisherInfo, userId);
   }

   public boolean updateAdsContentAdUnitIdSuggestionInfo(String adUnitId, String userId) throws SQLException {
      return this.dao.updateAdsContentAdUnitIdSuggestionInfo(adUnitId, userId);
   }

   public int addAdsContentPublisherInfoSuggestion(V2AdsContentPublisherInfo publisherInfo, String userId) throws SQLException {
      return this.dao.addAdsContentPublisherInfoSuggestion(publisherInfo, userId);
   }

   public int addAdsContentAdUnitIdSuggestion(String adUnitId, String userId) throws SQLException {
      return this.dao.addAdsContentAdUnitIdSuggestion(adUnitId, userId);
   }

   public V2AdsContentPublisherInfo getPublisherInfoById(String publisherId, String userId) throws SQLException {
      return this.dao.getPublisherInfoById(publisherId, userId);
   }

   public String getAdUnitIdById(String adUnitId, String userId) throws SQLException {
      return this.dao.getAdUnitIdById(adUnitId, userId);
   }

   public int deletePublisherInfoById(String publisherId, String userId) throws SQLException {
      return this.dao.deletePublisherInfoById(publisherId, userId);
   }

   public int deleteAdUnitIdInfoById(String adUnitId, String userId) throws SQLException {
      return this.dao.deleteAdUnitIdInfoById(adUnitId, userId);
   }
}
