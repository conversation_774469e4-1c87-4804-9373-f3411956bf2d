package com.samsung.magicinfo.rc.model.api;

import com.samsung.magicinfo.rc.model.api.DeviceResource;
import java.util.List;

public class DeviceControl {
  private String token;
  
  private List<String> deviceIds;
  
  private String keyCode;
  
  private String imageSize;
  
  private String text;
  
  private boolean textWithEnter;
  
  private List<DeviceResource> devices;
  
  public void setToken(String token) {
    this.token = token;
  }
  
  public void setDeviceIds(List<String> deviceIds) {
    this.deviceIds = deviceIds;
  }
  
  public void setKeyCode(String keyCode) {
    this.keyCode = keyCode;
  }
  
  public void setImageSize(String imageSize) {
    this.imageSize = imageSize;
  }
  
  public void setText(String text) {
    this.text = text;
  }
  
  public void setTextWithEnter(boolean textWithEnter) {
    this.textWithEnter = textWithEnter;
  }
  
  public void setDevices(List<DeviceResource> devices) {
    this.devices = devices;
  }
  
  public String getToken() {
    return this.token;
  }
  
  public List<String> getDeviceIds() {
    return this.deviceIds;
  }
  
  public String getKeyCode() {
    return this.keyCode;
  }
  
  public String getImageSize() {
    return this.imageSize;
  }
  
  public String getText() {
    return this.text;
  }
  
  public boolean isTextWithEnter() {
    return this.textWithEnter;
  }
  
  public List<DeviceResource> getDevices() {
    return this.devices;
  }
}
