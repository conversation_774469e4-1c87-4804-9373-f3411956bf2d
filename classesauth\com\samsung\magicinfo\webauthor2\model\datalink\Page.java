package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.samsung.magicinfo.webauthor2.model.datalink.SyncGroup;
import java.util.List;

public class Page {
  private final List<SyncGroup> syncGroups;
  
  private String name;
  
  @JsonCreator
  public Page(@JsonProperty("name") String name, @JsonProperty("syncGroups") List<SyncGroup> syncGroups) {
    this.syncGroups = syncGroups;
    this.name = name;
  }
  
  public List<SyncGroup> getSyncGroups() {
    return this.syncGroups;
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
}
