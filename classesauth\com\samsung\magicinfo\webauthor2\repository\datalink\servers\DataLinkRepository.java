package com.samsung.magicinfo.webauthor2.repository.datalink.servers;

import com.samsung.magicinfo.webauthor2.model.DataLinkServer;
import com.samsung.magicinfo.webauthor2.model.DataLinkTable;
import com.samsung.magicinfo.webauthor2.repository.model.dlk.DLKTableData;
import com.samsung.magicinfo.webauthor2.repository.model.dlk.DLKTableRowData;
import java.util.List;

public interface DataLinkRepository {
  List<DLKTableData> getDataTableList(DataLinkServer paramDataLinkServer);
  
  List<DLKTableRowData> getDataTableInfo(DataLinkServer paramDataLinkServer, DataLinkTable paramDataLinkTable);
}
