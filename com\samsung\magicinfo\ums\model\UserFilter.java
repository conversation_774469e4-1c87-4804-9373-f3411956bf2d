package com.samsung.magicinfo.ums.model;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

public class UserFilter {
   @ApiModelProperty(
      example = "testId",
      value = "[doNotUse] updateMyInfo",
      required = true
   )
   @Size(
      min = 3,
      max = 64
   )
   private String userId = "";
   @ApiModelProperty(
      example = "test1010",
      required = true
   )
   @Size(
      min = 8,
      max = 50
   )
   private String password = "";
   @ApiModelProperty(
      example = "test1010",
      required = true
   )
   @Size(
      min = 8,
      max = 50
   )
   private String confirmPassword = "";
   @ApiModelProperty(
      example = "testUser",
      required = true
   )
   @Size(
      max = 60
   )
   private String userName = "";
   @ApiModelProperty(
      example = "<EMAIL>",
      value = "[optional] updateMyInfo",
      required = true
   )
   @Pattern(
      regexp = "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$",
      message = "Not email pattern."
   )
   @Size(
      max = 200
   )
   private String email = "";
   @ApiModelProperty(
      hidden = true
   )
   @Size(
      max = 60
   )
   private String mobile = "";
   @ApiModelProperty(
      hidden = true
   )
   @Size(
      max = 60
   )
   private String phone = "";
   @ApiModelProperty(
      example = "SAMSUNG",
      value = "[doNotUse] updateMyInfo"
   )
   private String organization = "";
   @ApiModelProperty(
      example = "default",
      value = "[doNotUse] createUserSignUp, updateMyInfo"
   )
   private String groupName = "";
   @ApiModelProperty(
      example = "Administrator",
      value = "[doNotUse] createUserSignUp, updateMyInfo"
   )
   private String role = "";
   @ApiModelProperty(
      hidden = true
   )
   @Size(
      max = 60
   )
   private String team = "";
   @ApiModelProperty(
      hidden = true
   )
   @Size(
      max = 60
   )
   private String position = "";

   public UserFilter() {
      super();
   }

   public String getUserId() {
      return this.userId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }

   public String getPassword() {
      return this.password;
   }

   public void setPassword(String password) {
      this.password = password;
   }

   public String getConfirmPassword() {
      return this.confirmPassword;
   }

   public void setConfirmPassword(String confirmPassword) {
      this.confirmPassword = confirmPassword;
   }

   public String getUserName() {
      return this.userName;
   }

   public void setUserName(String userName) {
      this.userName = userName;
   }

   public String getEmail() {
      return this.email;
   }

   public void setEmail(String email) {
      this.email = email;
   }

   public String getMobile() {
      return this.mobile;
   }

   public void setMobile(String mobile) {
      this.mobile = mobile;
   }

   public String getPhone() {
      return this.phone;
   }

   public void setPhone(String phone) {
      this.phone = phone;
   }

   public String getOrganization() {
      return this.organization;
   }

   public void setOrganization(String organization) {
      this.organization = organization;
   }

   public String getGroupName() {
      return this.groupName;
   }

   public void setGroupName(String groupName) {
      this.groupName = groupName;
   }

   public String getRole() {
      return this.role;
   }

   public void setRole(String role) {
      this.role = role;
   }

   public String getTeam() {
      return this.team;
   }

   public void setTeam(String team) {
      this.team = team;
   }

   public String getPosition() {
      return this.position;
   }

   public void setPosition(String position) {
      this.position = position;
   }
}
