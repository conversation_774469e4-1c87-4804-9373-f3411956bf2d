package com.samsung.common.utils;

import com.google.common.util.concurrent.RateLimiter;
import java.util.concurrent.TimeUnit;

public class RateLimiterThrottler implements Throttler {
   private RateLimiter rateLimiter;

   public RateLimiterThrottler(int permits) {
      super();
      this.rateLimiter = RateLimiter.create((double)permits);
   }

   public boolean tryAcquire() {
      return this.rateLimiter.tryAcquire();
   }

   public boolean tryAcquire(long timeout, TimeUnit timeUnit) {
      return this.rateLimiter.tryAcquire(timeout, timeUnit);
   }

   public void release() {
   }
}
