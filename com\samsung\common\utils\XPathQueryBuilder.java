package com.samsung.common.utils;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import org.apache.commons.lang.Validate;

public class XPathQueryBuilder {
   private static final Pattern VALID_KEY_PATTERN = Pattern.compile("[a-zA-Z]+[a-zA-Z0-9_]*");
   private static final Pattern KEY_IN_QUERY_PATTERN = Pattern.compile("(:){1}[a-zA-Z0-9_]*");
   private final String query;
   private Map escapedLiterals = new HashMap();

   public XPathQueryBuilder(String query) {
      super();
      Validate.notEmpty(query, "XPath query is required");
      this.query = query;
   }

   public XPathQueryBuilder setLiteral(String key, String literal) {
      Validate.notNull(literal, "XPath literal may not be null");
      Validate.isTrue(VALID_KEY_PATTERN.matcher(key).matches(), "Literal key is invalid (does not match pattern)");
      this.escapedLiterals.put(key, XPathUtils.escapeXPathLiteral(literal));
      return this;
   }

   public XPathQueryBuilder setLiteral(String key, Integer literal) {
      Validate.notNull(literal, "XPath literal may not be null");
      Validate.isTrue(VALID_KEY_PATTERN.matcher(key).matches(), "Literal key is invalid (does not match pattern)");
      this.escapedLiterals.put(key, "\"" + literal + "\"");
      return this;
   }

   public XPathQueryBuilder setLiteral(String key, Long literal) {
      Validate.notNull(literal, "XPath literal may not be null");
      Validate.isTrue(VALID_KEY_PATTERN.matcher(key).matches(), "Literal key is invalid (does not match pattern)");
      this.escapedLiterals.put(key, "\"" + literal + "\"");
      return this;
   }

   public String getEscapedPlaceholderReplacement(String key) {
      return (String)this.escapedLiterals.get(key);
   }

   public String build() {
      String output = this.query;
      Set placeholders = this.extractLiteralPlaceholders(this.query);

      String placeholder;
      String colonless;
      for(Iterator var3 = placeholders.iterator(); var3.hasNext(); output = output.replace(placeholder, (CharSequence)this.escapedLiterals.get(colonless))) {
         placeholder = (String)var3.next();
         colonless = placeholder.substring(1);
         if (!this.escapedLiterals.containsKey(colonless)) {
            throw new IllegalArgumentException("Not found query literal value: " + colonless);
         }
      }

      return output;
   }

   public XPathExpression compile() throws XPathExpressionException {
      XPathFactory factory = XPathFactory.newInstance();
      XPath xpath = factory.newXPath();
      return xpath.compile(this.build());
   }

   private Set extractLiteralPlaceholders(String query) {
      Set keys = new HashSet();
      Matcher m = KEY_IN_QUERY_PATTERN.matcher(query);

      while(m.find()) {
         keys.add(m.group());
      }

      return Collections.unmodifiableSet(keys);
   }
}
