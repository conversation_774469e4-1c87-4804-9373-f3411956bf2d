package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.repository.model.UserInfo;

public interface UserService {
  boolean isUserAdmin(String paramString);
  
  void setUserAuthority(String paramString);
  
  boolean hasContentLockAuthority();
  
  boolean hasContentUploadAuthority();
  
  boolean hasContentAddElementAuthority();
  
  UserInfo getUserInfo(String paramString);
}
