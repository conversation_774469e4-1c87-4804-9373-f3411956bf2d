package com.samsung.magicinfo.webauthor2.util;

import com.samsung.magicinfo.webauthor2.util.AdaptableTrustManager;
import com.samsung.magicinfo.webauthor2.util.SecuritySetting;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.Socket;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.KeyStore;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509ExtendedTrustManager;
import javax.net.ssl.X509TrustManager;

class LocalAdaptableTrustManager extends X509ExtendedTrustManager {
  private SecuritySetting securitySetting = SecuritySetting.DISABLED;
  
  private X509TrustManager defaultTrustManager = null;
  
  public LocalAdaptableTrustManager() throws CertificateException, FileNotFoundException, IOException {
    this(SecuritySetting.DISABLED, null, null);
  }
  
  public LocalAdaptableTrustManager(SecuritySetting securitySetting, String keyPath, String keyPassword) throws CertificateException, FileNotFoundException, IOException {
    try {
      TrustManagerFactory factory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
      if (securitySetting.equals(SecuritySetting.DEFAULT)) {
        KeyStore trustStore = KeyStore.getInstance("JKS");
        Path keyStoreIndentityPath = Paths.get(keyPath, new String[0]);
        trustStore.load(new FileInputStream(keyStoreIndentityPath.toFile()), keyPassword.toCharArray());
        factory.init(trustStore);
      } else {
        factory.init((KeyStore)null);
      } 
      TrustManager[] tms = factory.getTrustManagers();
      if (tms.length == 0) {
        AdaptableTrustManager.access$000().error("No default Trust Managers present for:" + 
            TrustManagerFactory.getDefaultAlgorithm());
        throw new NoSuchAlgorithmException("No default Trust Managers present for:" + 
            TrustManagerFactory.getDefaultAlgorithm());
      } 
      this.defaultTrustManager = (X509TrustManager)tms[0];
    } catch (NoSuchAlgorithmException|java.security.KeyStoreException|CertificateException|IOException e) {
      AdaptableTrustManager.access$000().error(e.getMessage());
    } 
    this.securitySetting = securitySetting;
    AdaptableTrustManager.access$000().info("SSL Adjustable TM initialized with level:" + this.securitySetting.toString());
  }
  
  public void checkClientTrusted(X509Certificate[] x509Certificates, String s, Socket socket) throws CertificateException {
    checkClientTrusted(x509Certificates, s);
  }
  
  public void checkServerTrusted(X509Certificate[] x509Certificates, String s, Socket socket) throws CertificateException {
    checkServerTrusted(x509Certificates, s);
  }
  
  public void checkClientTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) throws CertificateException {
    checkClientTrusted(x509Certificates, s);
  }
  
  public void checkServerTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) throws CertificateException {
    checkServerTrusted(x509Certificates, s);
  }
  
  public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
    if (this.securitySetting.equals(SecuritySetting.DISABLED))
      return; 
    try {
      this.defaultTrustManager.checkClientTrusted(x509Certificates, s);
    } catch (CertificateException e) {
      if (x509Certificates != null && x509Certificates.length == 1) {
        AdaptableTrustManager.access$000().debug("Self Signed Certificate detected");
        X509Certificate cert = x509Certificates[0];
        if (isDefaultMIScertificate(cert))
          return; 
      } 
      AdaptableTrustManager.access$000().debug(e.getMessage());
      throw e;
    } 
  }
  
  public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
    if (this.securitySetting.equals(SecuritySetting.DISABLED))
      return; 
    try {
      this.defaultTrustManager.checkServerTrusted(x509Certificates, s);
    } catch (CertificateException e) {
      AdaptableTrustManager.access$000().debug("Certificate exception while checking server" + e.getMessage());
      if (x509Certificates != null && x509Certificates.length == 1) {
        AdaptableTrustManager.access$000().debug("Self Signed Certificate detected");
        X509Certificate cert = x509Certificates[0];
        if (!isDefaultMIScertificate(cert))
          cert.checkValidity(); 
      } else {
        AdaptableTrustManager.access$000().debug(e.getMessage());
        throw e;
      } 
    } 
  }
  
  private boolean isDefaultMIScertificate(X509Certificate cert) {
    String misCertSerial = "1220404191";
    String misSigAlgName = "MD5withRSA";
    String misIssuer = "CN=MagicInfo, OU=VD, O=SAMSUNG ELECTRONICS, L=SUWON, ST=GYEONGGI, C=KR";
    if (cert.getSerialNumber().toString().equals(misCertSerial) && cert
      .getSigAlgName().equals(misSigAlgName) && cert
      .getIssuerDN().toString().equals(misIssuer)) {
      AdaptableTrustManager.access$000().error("Possibly Default MIS certificate with MD5! encoding.");
      return false;
    } 
    return false;
  }
  
  public X509Certificate[] getAcceptedIssuers() {
    return this.defaultTrustManager.getAcceptedIssuers();
  }
}
