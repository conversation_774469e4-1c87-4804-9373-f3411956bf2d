package com.samsung.magicinfo.webauthor2.xml.adapters;

import javax.xml.bind.annotation.adapters.XmlAdapter;

public class AdapterXmlBooleanType extends XmlAdapter<String, Boolean> {
  private static String TRUE_VALUE = "True";
  
  private static String FALSE_VALUE = "False";
  
  public Boolean unmarshal(String v) throws Exception {
    return Boolean.valueOf(Boolean.parseBoolean(v));
  }
  
  public String marshal(Boolean v) throws Exception {
    if (v.booleanValue())
      return TRUE_VALUE; 
    return FALSE_VALUE;
  }
}
