package com.samsung.magicinfo.framework.setup.manager;

import com.google.gson.Gson;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.ExternalSystemUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.setup.entity.SlmActivationKeyInfo;
import com.samsung.magicinfo.openapi.auth.TokenRegistry;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.sec.gsbn.lms.ag.common.site.SiteInformation;
import com.sec.gsbn.lms.ag.key.verify.LicenseKeyVerifiedException;
import com.sec.gsbn.lms.ag.sdk.activation.ActivationKeyHandler;
import com.sec.gsbn.lms.ag.sdk.activation.activate.ActivationKeyManager;
import com.sec.gsbn.lms.ag.sdk.license.verify.LicenseKeyVerify;
import com.sec.gsbn.lms.ag.sdk.protocols.ActivationKeyRequestManager;
import com.sec.gsbn.lms.ag.sdk.protocols.DeActivationRequestManager;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class SlmSdkManagerImpl implements SlmSdkManager {
   static String connectionDomain;
   static String slmRestAPIDomain;
   static String smlRestAPIUSER = "";
   static String smlRestAPIPW = "";
   static String PBPRestAPIDomain;
   static int timeOut = 10000;
   static int slmTimeOut = 20000;
   static int slmReadTimeOut = 15000;
   static boolean isSSL;
   SlmLicenseManager liceseMgr;
   Logger logger = LoggingManagerV2.getLogger(SlmSdkManagerImpl.class);

   private SlmSdkManagerImpl() {
      super();
      Object var1;
      if (connectionDomain == null) {
         var1 = null;

         try {
            if (CommonConfig.get("slm.license.domain") == null) {
               connectionDomain = "www.secslm.com";
            } else {
               connectionDomain = CommonConfig.get("slm.license.domain");
            }

            if (CommonConfig.get("slm.license.ssl") == null) {
               isSSL = true;
            } else {
               isSSL = Boolean.valueOf(CommonConfig.get("slm.license.ssl"));
            }
         } catch (ConfigException var6) {
            this.logger.error("", var6);
         }
      }

      if (slmRestAPIDomain == null) {
         var1 = null;

         try {
            if (CommonConfig.get("slm.restAPI.domain") == null) {
               slmRestAPIDomain = "www.secslm.com";
            } else {
               slmRestAPIDomain = CommonConfig.get("slm.restAPI.domain");
            }

            if (CommonConfig.get("slm.restAPI.ssl") == null) {
               isSSL = true;
            } else {
               isSSL = Boolean.valueOf(CommonConfig.get("slm.restAPI.ssl"));
            }
         } catch (ConfigException var5) {
            this.logger.error("", var5);
         }
      }

      if (PBPRestAPIDomain == null) {
         var1 = null;

         try {
            if (CommonConfig.get("pbp.restAPI.domain") == null) {
               PBPRestAPIDomain = "www.pbp.com";
            } else {
               PBPRestAPIDomain = CommonConfig.get("pbp.restAPI.domain");
            }

            if (CommonConfig.get("pbp.restAPI.ssl") == null) {
               isSSL = true;
            } else {
               isSSL = Boolean.valueOf(CommonConfig.get("pbp.restAPI.ssl"));
            }
         } catch (ConfigException var4) {
            this.logger.error("", var4);
         }
      }

      try {
         smlRestAPIUSER = StrUtils.nvl(CommonConfig.get("slm.restAPI.user"));
         smlRestAPIPW = StrUtils.nvl(CommonConfig.get("slm.restAPI.pw"));
      } catch (ConfigException var3) {
         this.logger.error("", var3);
      }

      this.liceseMgr = SlmLicenseManagerImpl.getInstance();
      this.logger.trace("SLM domain:" + connectionDomain + ",isSSL:" + isSSL + ",timeout:" + timeOut);
   }

   public static SlmSdkManagerImpl getInstance() {
      return new SlmSdkManagerImpl();
   }

   public Object[] sdkNewActivationRequest(String license, String hwUniqueKey) {
      Object[] rtn = null;
      ActivationKeyRequestManager manager = new ActivationKeyRequestManager(connectionDomain);
      manager.setIsSSL(isSSL);

      try {
         rtn = manager.newActivationRequest(timeOut, license, hwUniqueKey);
      } catch (Exception var10) {
         this.logger.error("", var10);
         this.logger.info("[SLM_SDK] NewActivationRequest. licenseKey : " + license + " hwUniquekey : " + hwUniqueKey);
         rtn[0] = 4000;
      }

      if ((Integer)rtn[0] == 0) {
         try {
            this.liceseMgr.setSlmLicenseHistory("01", license, "NewActivationRequest success", true);
         } catch (ConfigException var8) {
            this.logger.error("", var8);
         } catch (SQLException var9) {
            this.logger.error("", var9);
         }

         this.logger.info("[SLM_SDK] NewActivationRequest success. key:" + (String)rtn[1]);
      } else {
         try {
            this.liceseMgr.setSlmLicenseHistory("01", license, "NewActivationRequest fail. errCode:" + (Integer)rtn[0], true);
         } catch (ConfigException var6) {
            this.logger.error("", var6);
         } catch (SQLException var7) {
            this.logger.error("", var7);
         }

         this.logger.error("[SLM_SDK] NewActivationRequest fail. errCode:" + (Integer)rtn[0]);
      }

      return rtn;
   }

   public Object[] sdkNewActivationRequest(String license, String hwUniqueKey, SiteInformation information) {
      Object[] rtn = null;
      ActivationKeyRequestManager manager = new ActivationKeyRequestManager(connectionDomain);
      manager.setIsSSL(isSSL);

      try {
         this.logger.info("[SLM_SDK] NewActivationRequest. licenseKey : " + license + " hwUniqueKey : " + hwUniqueKey + " informationName : " + information.getCompanyName());
         rtn = manager.newActivationRequest(timeOut, license, hwUniqueKey, information);
      } catch (Exception var11) {
         this.logger.error("", var11);
         rtn[0] = 4000;
      }

      if ((Integer)rtn[0] == 0) {
         try {
            this.liceseMgr.setSlmLicenseHistory("01", license, "NewActivationRequest success", true);
         } catch (ConfigException var9) {
            this.logger.error("", var9);
         } catch (SQLException var10) {
            this.logger.error("", var10);
         }

         this.logger.info("[SLM_SDK] NewActivationRequest success. key:" + (String)rtn[1]);
      } else {
         try {
            this.liceseMgr.setSlmLicenseHistory("01", license, "NewActivationRequest fail. errCode:" + (Integer)rtn[0], true);
         } catch (ConfigException var7) {
            this.logger.error("", var7);
         } catch (SQLException var8) {
            this.logger.error("", var8);
         }

         this.logger.error("[SLM_SDK] NewActivationRequest fail. errCode:" + (Integer)rtn[0]);
      }

      return rtn;
   }

   public Object[] sdkRequestGetActivation(String license, String hwUniqueKey) {
      Object[] rtn = null;
      ActivationKeyRequestManager manager = new ActivationKeyRequestManager(connectionDomain);
      manager.setIsSSL(isSSL);

      try {
         rtn = manager.requestGetActivation(timeOut, license, hwUniqueKey);
         this.logger.info("[SLM_SDK] Activation check. licenseKey : " + license + " hwUniqueKey : " + hwUniqueKey + " return code : " + rtn[0]);
      } catch (Exception var6) {
         this.logger.error("", var6);
         rtn[0] = 4000;
      }

      return rtn;
   }

   public SiteInformation createSiteInfomation(String company, String division, String address, String phone, String email) {
      SiteInformation information = new SiteInformation();
      information.setCompanyName(company);
      information.setDeptName(division);
      information.setCompanyaddress(address);
      information.setTelephone(phone);
      information.setEmailAddress(email);
      return information;
   }

   public int sdkNewActivationActive(String license, String activationKey) {
      int rtn = false;
      ActivationKeyManager manager = new ActivationKeyManager();

      int rtn;
      try {
         this.logger.info("[SLM_SDK] NewActivationActive. licenseKey : " + license + " activationKey : " + activationKey);
         rtn = manager.newActivationActivate(license, activationKey);
      } catch (Exception var10) {
         this.logger.error("", var10);
         rtn = Integer.valueOf(4000);
      }

      if (rtn == 0) {
         try {
            this.liceseMgr.setSlmLicenseHistory("01", license, "NewActivationActive success", true);
         } catch (ConfigException var8) {
            this.logger.error("", var8);
         } catch (SQLException var9) {
            this.logger.error("", var9);
         }

         this.logger.info("[SLM_SDK] NewActivationActive success. key:" + activationKey);
      } else {
         try {
            this.liceseMgr.setSlmLicenseHistory("01", license, "NewActivationActive fail. errCode:" + rtn, true);
         } catch (ConfigException var6) {
            this.logger.error("", var6);
         } catch (SQLException var7) {
            this.logger.error("", var7);
         }

         this.logger.error("[SLM_SDK] NewActivationActive fail. errCode:" + rtn);
      }

      return rtn;
   }

   public Object[] sdkChangeActivationRequest(String activationKey) {
      Object[] rtn = null;
      ActivationKeyRequestManager manager = new ActivationKeyRequestManager(connectionDomain);
      manager.setIsSSL(isSSL);

      try {
         this.logger.info("[SLM_SDK] ChangeActivationRequestFromString. activationKey : " + activationKey);
         rtn = manager.changeActivationRequestFromString(timeOut, activationKey);
      } catch (Exception var10) {
         this.logger.error("", var10);
         rtn[0] = 4000;
      }

      String license = this.liceseMgr.getLicenseKeyfromActivationKey(activationKey);
      if ((Integer)rtn[0] == 0) {
         try {
            this.liceseMgr.setSlmLicenseHistory("01", license, "ChangeActivationRequestFromString success", true);
         } catch (ConfigException var8) {
            this.logger.error("", var8);
         } catch (SQLException var9) {
            this.logger.error("", var9);
         }

         this.logger.info("[SLM_SDK] ChangeActivationRequestFromString success. key:" + (String)rtn[1]);
      } else {
         try {
            this.liceseMgr.setSlmLicenseHistory("01", license, "ChangeActivationRequestFromString fail. errCode:" + (Integer)rtn[0], true);
         } catch (ConfigException var6) {
            this.logger.error("", var6);
         } catch (SQLException var7) {
            this.logger.error("", var7);
         }

         this.logger.error("[SLM_SDK] ChangeActivationRequestFromString fail. errCode:" + (Integer)rtn[0]);
      }

      return rtn;
   }

   public int sdkChangeActivationActive(String oldActKeyString, String newActKeyString) {
      int rtn = false;
      ActivationKeyManager manager = new ActivationKeyManager();

      int rtn;
      try {
         this.logger.info("[SLM_SDK] sdkChangeActivationActive. oldActKeyString : " + oldActKeyString + " newActKeyString : " + newActKeyString);
         rtn = manager.changeActivationActivateFromString(oldActKeyString, newActKeyString);
      } catch (Exception var11) {
         this.logger.error("", var11);
         rtn = Integer.valueOf(4000);
      }

      String license = this.liceseMgr.getLicenseKeyfromActivationKey(newActKeyString);
      if (rtn == 0) {
         try {
            this.liceseMgr.setSlmLicenseHistory("01", license, "sdkChangeActivationActive success", true);
         } catch (ConfigException var9) {
            this.logger.error("", var9);
         } catch (SQLException var10) {
            this.logger.error("", var10);
         }

         this.logger.info("[SLM_SDK] sdkChangeActivationActive success.");
      } else {
         try {
            this.liceseMgr.setSlmLicenseHistory("01", license, "sdkChangeActivationActive fail. errCode:" + rtn, true);
         } catch (ConfigException var7) {
            this.logger.error("", var7);
         } catch (SQLException var8) {
            this.logger.error("", var8);
         }

         this.logger.error("[SLM_SDK] sdkChangeActivationActive fail. errCode:" + rtn);
      }

      return rtn;
   }

   public int sdkRequestDeActivationProcess(String activationKey) {
      int rtn = false;
      DeActivationRequestManager manager = new DeActivationRequestManager(connectionDomain);
      manager.setIsSSL(isSSL);

      int rtn;
      try {
         this.logger.info("[SLM_SDK] requestDeActivationProcess. activationKey : " + activationKey);
         rtn = manager.requestDeActivateProcess(activationKey, timeOut);
      } catch (Exception var10) {
         this.logger.error("", var10);
         rtn = 4000;
      }

      String license = this.liceseMgr.getLicenseKeyfromActivationKey(activationKey);
      if (rtn == 0) {
         try {
            this.liceseMgr.setSlmLicenseHistory("01", license, "requestDeActivationProcess success", true);
         } catch (ConfigException var8) {
            this.logger.error("", var8);
         } catch (SQLException var9) {
            this.logger.error("", var9);
         }

         this.logger.info("[SLM_SDK] requestDeActivationProcess success. key:" + activationKey);
      } else {
         try {
            this.liceseMgr.setSlmLicenseHistory("01", license, "requestDeActivationProcess fail. errCode:" + rtn, true);
         } catch (ConfigException var6) {
            this.logger.error("", var6);
         } catch (SQLException var7) {
            this.logger.error("", var7);
         }

         this.logger.error("[SLM_SDK] requestDeActivationProcess fail. errCode:" + rtn);
      }

      return rtn;
   }

   public Object[] sdkGenerateDeactivationKey(String activationKey) {
      Object[] rtn = new Object[2];
      DeActivationRequestManager manager = new DeActivationRequestManager(connectionDomain);
      manager.setIsSSL(isSSL);

      try {
         this.logger.info("[SLM_SDK] generateDeactivationKey. activationKey : " + activationKey);
         rtn[1] = manager.generateDeactivationKey(activationKey);
         rtn[0] = 0;
      } catch (Exception var10) {
         this.logger.error("", var10);
         rtn[0] = 4000;
      }

      String license = this.liceseMgr.getLicenseKeyfromActivationKey(activationKey);
      if ((Integer)rtn[0] == 0) {
         try {
            this.liceseMgr.setSlmLicenseHistory("01", license, "generateDeactivationKey success", true);
         } catch (ConfigException var8) {
            this.logger.error("", var8);
         } catch (SQLException var9) {
            this.logger.error("", var9);
         }

         this.logger.info("[SLM_SDK] generateDeactivationKey success. deactkey:" + (String)rtn[1]);
      } else {
         try {
            this.liceseMgr.setSlmLicenseHistory("01", license, "generateDeactivationKey fail. errCode:" + rtn[0], true);
         } catch (ConfigException var6) {
            this.logger.error("", var6);
         } catch (SQLException var7) {
            this.logger.error("", var7);
         }

         this.logger.error("[SLM_SDK] generateDeactivationKey fail. errCode:" + (Integer)rtn[0]);
      }

      return rtn;
   }

   public Object[] sdkGetActivationKeyParseInfo(String activationKey) {
      Object[] rtn = new Object[2];
      ActivationKeyHandler keyHandler = new ActivationKeyHandler(activationKey);
      rtn[0] = keyHandler.verify();
      if ((Integer)rtn[0] != 0) {
         this.logger.error("[SLM_SDK] ActivationKeyHandler verify fail. errCode:" + (Integer)rtn[0] + ",actKey=" + activationKey);
         return rtn;
      } else {
         rtn[0] = keyHandler.parserLoad();
         if ((Integer)rtn[0] != 0) {
            this.logger.error("[SLM_SDK] ActivationKeyHandler parserLoad fail. errCode:" + (Integer)rtn[0] + ",actKey=" + activationKey);
            return rtn;
         } else {
            SlmActivationKeyInfo actKeyInfo = new SlmActivationKeyInfo();

            try {
               actKeyInfo.setActivation_key(activationKey);
               actKeyInfo.setLicense_key(keyHandler.getLicenseKey());
               actKeyInfo.setHwUnique_key(keyHandler.getHwUniqueKey());
               actKeyInfo.setProduct_list(keyHandler.getProducts());
            } catch (Exception var6) {
               this.logger.error("", var6);
               rtn[0] = 4000;
            }

            rtn[1] = actKeyInfo;
            return rtn;
         }
      }
   }

   public int sdkLicenseKeyVerify(String licenseKey) {
      LicenseKeyVerify licenseKeyVerify = new LicenseKeyVerify();
      int rtn = 0;

      try {
         if (LicenseKeyVerify.verifingKey(licenseKey)) {
            rtn = 0;
         }
      } catch (LicenseKeyVerifiedException var5) {
         this.logger.error("[SLM_SDK] sdkLicenseKeyVerify License verify fail.");
         rtn = var5.getErrorCode();
      }

      return rtn;
   }

   public Exception getCommunicationException() {
      ActivationKeyRequestManager manager = new ActivationKeyRequestManager(connectionDomain);
      return manager.getCommunicationException();
   }

   public JSONObject requestNewLicenseKey(String sysCd, String accountCode, String brandCode, String domainName, String licenseKeyIssudType, String rmk, String modelCd, int issudQty) throws Exception {
      TokenRegistry tokenRegistry = TokenRegistry.getTokenRegistry();
      String token = tokenRegistry.issueTokenFor3rdParty(ExternalSystemUtils.SYSTEM_PBP, "");
      Map json = new LinkedHashMap();
      json.put("sysCd", sysCd);
      json.put("accountCode", accountCode);
      json.put("brandCode", brandCode);
      json.put("licenseKeyIssudType", licenseKeyIssudType);
      json.put("rmk", rmk);
      json.put("domainInfo", domainName);
      List list = new ArrayList();
      Map json2 = new LinkedHashMap();
      json2.put("modelCd", modelCd);
      json2.put("issudQty", String.valueOf(issudQty));
      list.add(json2);
      json.put("issudDtlInfo", list);
      Gson gson = new Gson();
      JSONObject resultJSON = null;
      JSONObject errorJSON = null;
      OutputStream os = null;
      BufferedReader br = null;
      URL url = null;
      HttpURLConnection conn = null;
      StringBuffer response = new StringBuffer();

      try {
         String output;
         try {
            url = new URL(PBPRestAPIDomain + "/lynkcloud/store/mi/licenseKeyInfo");
            conn = (HttpURLConnection)url.openConnection();
            conn.setDoOutput(true);
            conn.setRequestProperty("Authorization", "Bearer " + token);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
            conn.setRequestProperty("Accept", "application/json;charset=utf-8");
            conn.setConnectTimeout(slmTimeOut);
            conn.setReadTimeout(slmReadTimeOut);
            os = conn.getOutputStream();
            os.write(gson.toJson(json).getBytes("UTF-8"));
            os.flush();
            br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
            if (conn.getResponseCode() != 200) {
               this.liceseMgr.setSlmLicenseHistoryForE2E("E01", "", "", "", "requestNewLicenseKey HTTP fail", true);
               throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
            } else {
               while((output = br.readLine()) != null) {
                  response.append(output);
                  resultJSON = new JSONObject(response.toString());
               }

               conn.disconnect();
               return resultJSON;
            }
         } catch (Exception var31) {
            this.logger.error("Failed to call iNewLicenseKey " + var31.toString(), var31);

            for(br = new BufferedReader(new InputStreamReader(conn.getErrorStream(), "UTF-8")); (output = br.readLine()) != null; errorJSON = new JSONObject(response.toString())) {
               response.append(output);
            }

            this.logger.error("", var31);
            this.logger.error(errorJSON.toString());
            throw var31;
         }
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }
         } catch (Exception var30) {
            this.logger.error("During closing resource.");
         }

      }
   }

   public JSONObject requestNewActivation(String sysCd, String issudNo, String licenseKey, String hardwareType, String hardwareTypeVal, String location, String deviceName, String accountCode, String brandCode) throws Exception {
      TokenRegistry tokenRegistry = TokenRegistry.getTokenRegistry();
      String token = tokenRegistry.issueTokenFor3rdParty(ExternalSystemUtils.SYSTEM_PBP, "");
      Map json = new LinkedHashMap();
      json.put("sysCd", sysCd);
      json.put("issudNo", issudNo);
      json.put("licenseKey", licenseKey);
      json.put("hardwareType", hardwareType);
      json.put("hardwareTypeVal", hardwareTypeVal);
      json.put("accountCode", accountCode);
      json.put("brandCode", brandCode);
      json.put("deviceNm", deviceName);
      Map json2 = new LinkedHashMap();
      json2.put("rmk", location);
      json.put("installInfoDto", json2);
      Gson gson = new Gson();
      JSONObject resultJSON = null;
      JSONObject errorJSON = null;
      OutputStream os = null;
      BufferedReader br = null;
      URL url = null;
      HttpURLConnection conn = null;
      StringBuffer response = new StringBuffer();

      try {
         String output;
         try {
            url = new URL(PBPRestAPIDomain + "/lynkcloud/store/mi/activateLicense");
            conn = (HttpURLConnection)url.openConnection();
            conn.setDoOutput(true);
            conn.setRequestProperty("Authorization", "Bearer " + token);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
            conn.setRequestProperty("Accept", "application/json;charset=utf-8");
            conn.setConnectTimeout(slmTimeOut);
            conn.setReadTimeout(slmReadTimeOut);
            os = conn.getOutputStream();
            os.write(gson.toJson(json).getBytes("UTF-8"));
            os.flush();
            br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
            if (conn.getResponseCode() != 200) {
               this.liceseMgr.setSlmLicenseHistoryForE2E("E01", hardwareTypeVal, licenseKey, issudNo, "requestNewActivation HTTP fail", true);
               throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
            } else {
               while((output = br.readLine()) != null) {
                  response.append(output);
                  resultJSON = new JSONObject(response.toString());
               }

               conn.disconnect();
               return resultJSON;
            }
         } catch (Exception var31) {
            this.logger.error("Failed to call iNewActivation on" + hardwareTypeVal, var31);

            for(br = new BufferedReader(new InputStreamReader(conn.getErrorStream(), "UTF-8")); (output = br.readLine()) != null; errorJSON = new JSONObject(response.toString())) {
               response.append(output);
            }

            this.logger.error("", var31);
            this.logger.error(errorJSON.toString());
            throw var31;
         }
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }
         } catch (Exception var30) {
            this.logger.error("During closing resource.");
         }

      }
   }

   public JSONObject requestReActivation(String sysCd, String issudNo, String licenseKey, String hardwareType, String hardwareTypeVal) throws Exception {
      TokenRegistry tokenRegistry = TokenRegistry.getTokenRegistry();
      String token = tokenRegistry.issueTokenFor3rdParty(ExternalSystemUtils.SYSTEM_PBP, "");
      Map json = new LinkedHashMap();
      json.put("sysCd", sysCd);
      json.put("issudNo", issudNo);
      json.put("licenseKey", licenseKey);
      json.put("hardwareType", hardwareType);
      json.put("hardwareTypeVal", hardwareTypeVal);
      Gson gson = new Gson();
      JSONObject resultJSON = null;
      JSONObject errorJSON = null;
      OutputStream os = null;
      BufferedReader br = null;
      URL url = null;
      HttpURLConnection conn = null;
      StringBuffer response = new StringBuffer();

      try {
         String output;
         try {
            url = new URL(PBPRestAPIDomain + "/lynkcloud/store/mi/reactivateLicense");
            conn = (HttpURLConnection)url.openConnection();
            conn.setDoOutput(true);
            conn.setRequestProperty("Authorization", "Bearer " + token);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
            conn.setRequestProperty("Accept", "application/json;charset=utf-8");
            conn.setConnectTimeout(slmTimeOut);
            conn.setReadTimeout(slmReadTimeOut);
            os = conn.getOutputStream();
            os.write(gson.toJson(json).getBytes("UTF-8"));
            os.flush();
            br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
            if (conn.getResponseCode() != 200) {
               this.liceseMgr.setSlmLicenseHistoryForE2E("E01", hardwareTypeVal, licenseKey, issudNo, "requestNewActivation HTTP fail", true);
               throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
            } else {
               while((output = br.readLine()) != null) {
                  response.append(output);
                  resultJSON = new JSONObject(response.toString());
               }

               conn.disconnect();
               return resultJSON;
            }
         } catch (Exception var26) {
            this.logger.error("Failed to call reActivation on" + hardwareTypeVal, var26);

            for(br = new BufferedReader(new InputStreamReader(conn.getErrorStream(), "UTF-8")); (output = br.readLine()) != null; errorJSON = new JSONObject(response.toString())) {
               response.append(output);
            }

            this.logger.error("", var26);
            this.logger.error(errorJSON.toString());
            throw var26;
         }
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }
         } catch (Exception var25) {
            this.logger.error("During closing resource.");
         }

      }
   }

   public JSONObject requestDeActivation(String sysCd, String issudNo, String licenseKey, String hardwareType, String hardwareTypeVal) throws Exception {
      TokenRegistry tokenRegistry = TokenRegistry.getTokenRegistry();
      String token = tokenRegistry.issueTokenFor3rdParty(ExternalSystemUtils.SYSTEM_PBP, "");
      Map json = new LinkedHashMap();
      json.put("sysCd", sysCd);
      json.put("ifWorkType", "2");
      json.put("deActivaKey", "");
      json.put("issudNo", issudNo);
      json.put("licenseKey", licenseKey);
      json.put("hardwareType", hardwareType);
      json.put("hardwareTypeVal", hardwareTypeVal);
      Gson gson = new Gson();
      JSONObject resultJSON = null;
      JSONObject errorJSON = null;
      OutputStream os = null;
      BufferedReader br = null;
      URL url = null;
      HttpURLConnection conn = null;
      StringBuffer response = new StringBuffer();

      try {
         String output;
         try {
            url = new URL(PBPRestAPIDomain + "/lynkcloud/store/mi/deactivateLicense");
            conn = (HttpURLConnection)url.openConnection();
            conn.setDoOutput(true);
            conn.setRequestProperty("Authorization", "Bearer " + token);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
            conn.setRequestProperty("Accept", "application/json;charset=utf-8");
            conn.setConnectTimeout(slmTimeOut);
            conn.setReadTimeout(slmReadTimeOut);
            os = conn.getOutputStream();
            os.write(gson.toJson(json).getBytes("UTF-8"));
            os.flush();

            for(br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8")); (output = br.readLine()) != null; resultJSON = new JSONObject(response.toString())) {
               response.append(output);
            }

            conn.disconnect();
         } catch (Exception var27) {
            this.logger.error("Failed to call deActivation on" + hardwareTypeVal, var27);

            for(br = new BufferedReader(new InputStreamReader(conn.getErrorStream(), "UTF-8")); (output = br.readLine()) != null; errorJSON = new JSONObject(response.toString())) {
               response.append(output);
            }

            this.logger.error("", var27);
            this.logger.error(errorJSON.toString());
            if (!"PBP-MI-1004".equalsIgnoreCase(errorJSON.getString("errorCode"))) {
               throw new RuntimeException("Failed : HTTP error code : " + errorJSON.getString("errorCode"));
            }

            this.logger.error("Device already deleted from SPP, so suppress error to delete device from MIS also");
            resultJSON = errorJSON;
         }
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }
         } catch (Exception var26) {
            this.logger.error("During closing resource.");
         }

      }

      return resultJSON;
   }

   public JSONObject requestModifyLocation(String sysCd, String licenseKey, String deviceId, String location) throws Exception {
      TokenRegistry tokenRegistry = TokenRegistry.getTokenRegistry();
      String token = tokenRegistry.issueTokenFor3rdParty(ExternalSystemUtils.SYSTEM_PBP, "");
      Map json = new LinkedHashMap();
      json.put("sysCd", sysCd);
      json.put("licenseKey", licenseKey);
      json.put("hardwareTypeVal", deviceId);
      json.put("rmk", location);
      Gson gson = new Gson();
      JSONObject resultJSON = null;
      JSONObject errorJSON = null;
      OutputStream os = null;
      BufferedReader br = null;
      URL url = null;
      HttpURLConnection conn = null;
      StringBuffer response = new StringBuffer();

      try {
         String output;
         try {
            url = new URL(PBPRestAPIDomain + "/lynkcloud/store/mi/changeDeviceLocation");
            conn = (HttpURLConnection)url.openConnection();
            conn.setDoOutput(true);
            conn.setRequestProperty("Authorization", "Bearer " + token);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
            conn.setRequestProperty("Accept", "application/json;charset=utf-8");
            conn.setConnectTimeout(slmTimeOut);
            conn.setReadTimeout(slmReadTimeOut);
            os = conn.getOutputStream();
            os.write(gson.toJson(json).getBytes("UTF-8"));
            os.flush();
            br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
            if (conn.getResponseCode() != 200) {
               this.liceseMgr.setSlmLicenseHistoryForE2E("E01", "", licenseKey, "", "requestModifyLocation HTTP fail", true);
               throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
            }

            while((output = br.readLine()) != null) {
               response.append(output);
               resultJSON = new JSONObject(response.toString());
            }

            conn.disconnect();
         } catch (MalformedURLException var27) {
            this.logger.error("", var27);
         } catch (IOException var28) {
            for(br = new BufferedReader(new InputStreamReader(conn.getErrorStream(), "UTF-8")); (output = br.readLine()) != null; errorJSON = new JSONObject(response.toString())) {
               response.append(output);
            }

            this.logger.error("", var28);
            this.logger.error(errorJSON.toString());
            throw var28;
         }
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }
         } catch (Exception var26) {
            this.logger.error("During closing resource.");
         }

      }

      return resultJSON;
   }

   public JSONArray requestGetBrandList(String[] brandCodeArr) throws Exception {
      TokenRegistry tokenRegistry = TokenRegistry.getTokenRegistry();
      String token = tokenRegistry.issueTokenFor3rdParty(ExternalSystemUtils.SYSTEM_PBP, "");
      Map json = new LinkedHashMap();
      List list = new ArrayList();

      for(int i = 0; i < brandCodeArr.length; ++i) {
         Map json2 = new LinkedHashMap();
         json2.put("brandCode", brandCodeArr[i]);
         list.add(json2);
      }

      json.put("brands", list);
      Gson gson = new Gson();
      JSONArray resultJSON = null;
      JSONObject errorJSON = null;
      OutputStream os = null;
      BufferedReader br = null;
      URL url = null;
      HttpURLConnection conn = null;
      StringBuffer response = new StringBuffer();

      try {
         String output;
         try {
            url = new URL(PBPRestAPIDomain + "/lynkcloud/store/mi/accounts");
            conn = (HttpURLConnection)url.openConnection();
            conn.setDoOutput(true);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Authorization", "Bearer " + token);
            conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
            conn.setRequestProperty("Accept", "application/json;charset=utf-8");
            conn.setConnectTimeout(slmTimeOut);
            conn.setReadTimeout(slmReadTimeOut);
            os = conn.getOutputStream();
            os.write(gson.toJson(json).getBytes("UTF-8"));
            os.flush();
            br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
            if (conn.getResponseCode() != 200) {
               throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
            }

            while((output = br.readLine()) != null) {
               response.append(output);
               resultJSON = new JSONArray(response.toString());
            }

            conn.disconnect();
         } catch (MalformedURLException var25) {
            this.logger.error("", var25);
         } catch (IOException var26) {
            for(br = new BufferedReader(new InputStreamReader(conn.getErrorStream(), "UTF-8")); (output = br.readLine()) != null; errorJSON = new JSONObject(response.toString())) {
               response.append(output);
            }

            this.logger.error("", var26);
            this.logger.error(errorJSON.toString());
            throw var26;
         }
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }
         } catch (Exception var24) {
            this.logger.error("During closing resource.");
         }

      }

      return resultJSON;
   }

   public JSONObject requestChangeDevice(String sysCd, String issudNo, String licenseKey, String hardwareType, String oldDeviceId, String newDeviceId, String location, String deviceName) throws Exception {
      TokenRegistry tokenRegistry = TokenRegistry.getTokenRegistry();
      String token = tokenRegistry.issueTokenFor3rdParty(ExternalSystemUtils.SYSTEM_PBP, "");
      Map json = new LinkedHashMap();
      json.put("sysCd", sysCd);
      json.put("issudNo", issudNo);
      json.put("licenseKey", licenseKey);
      json.put("hardwareType", hardwareType);
      json.put("hardwareTypeVal", newDeviceId);
      json.put("deviceNm", deviceName);
      Map json3 = new LinkedHashMap();
      json3.put("rmk", location);
      json.put("installInfoDto", json3);
      Gson gson = new Gson();
      JSONObject resultJSON = null;
      JSONObject errorJSON = null;
      OutputStream os = null;
      BufferedReader br = null;
      URL url = null;
      HttpURLConnection conn = null;
      StringBuffer response = new StringBuffer();

      try {
         String output;
         try {
            url = new URL(PBPRestAPIDomain + "/lynkcloud/store/mi/changeDevice");
            conn = (HttpURLConnection)url.openConnection();
            conn.setDoOutput(true);
            conn.setRequestProperty("Authorization", "Bearer " + token);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
            conn.setRequestProperty("Accept", "application/json;charset=utf-8");
            conn.setConnectTimeout(slmTimeOut);
            conn.setReadTimeout(slmReadTimeOut);
            os = conn.getOutputStream();
            os.write(gson.toJson(json).getBytes("UTF-8"));
            os.flush();
            br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
            if (conn.getResponseCode() != 200) {
               this.liceseMgr.setSlmLicenseHistoryForE2E("E01", oldDeviceId, licenseKey, issudNo, "swapDevice HTTP fail", true);
               throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
            } else {
               while((output = br.readLine()) != null) {
                  response.append(output);
                  resultJSON = new JSONObject(response.toString());
               }

               conn.disconnect();
               return resultJSON;
            }
         } catch (Exception var30) {
            this.logger.error("Failed to call swapDevice on" + oldDeviceId + "to " + newDeviceId, var30);

            for(br = new BufferedReader(new InputStreamReader(conn.getErrorStream(), "UTF-8")); (output = br.readLine()) != null; errorJSON = new JSONObject(response.toString())) {
               response.append(output);
            }

            this.logger.error("", var30);
            this.logger.error(errorJSON.toString());
            throw var30;
         }
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }
         } catch (Exception var29) {
            this.logger.error("During closing resource.");
         }

      }
   }

   public JSONArray requestGetAccountList(String soldToCode) throws ConfigException, JSONException, SQLException {
      Map json = new LinkedHashMap();
      json.put("soldToCode", soldToCode);
      Gson gson = new Gson();
      JSONArray resultJSON = null;
      OutputStream os = null;
      BufferedReader br = null;

      try {
         URL url = new URL(slmRestAPIDomain + "/spt/rs/slm/gn/mi/iGetAccountList");
         HttpURLConnection conn = (HttpURLConnection)url.openConnection();
         conn.setDoOutput(true);
         String authString = smlRestAPIUSER + ":" + smlRestAPIPW;
         String authStringEnc = new String(Base64.encodeBase64(authString.getBytes()));
         conn.setRequestProperty("Authorization", "Basic " + authStringEnc);
         conn.setRequestMethod("POST");
         conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
         conn.setRequestProperty("Accept", "application/json;charset=utf-8");
         conn.setConnectTimeout(slmTimeOut);
         conn.setReadTimeout(slmReadTimeOut);
         os = conn.getOutputStream();
         os.write(gson.toJson(json).getBytes("UTF-8"));
         os.flush();
         br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
         StringBuffer response = new StringBuffer();
         if (conn.getResponseCode() != 200) {
            this.liceseMgr.setSlmLicenseHistoryForE2E("E01", "", soldToCode, "", "requestGetAccountList HTTP fail", true);
            throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
         }

         String output;
         while((output = br.readLine()) != null) {
            response.append(output);
            resultJSON = new JSONArray(response.toString());
         }

         conn.disconnect();
      } catch (MalformedURLException var23) {
         this.logger.error("", var23);
      } catch (IOException var24) {
         this.logger.error("", var24);
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }
         } catch (Exception var22) {
            this.logger.error("During closing resource.");
         }

      }

      return resultJSON;
   }

   public JSONObject requestNewLicenseKey_SLMDirect(String sysCd, String soldToCode, String domainName, String secorgId, String licenseKeyIssudType, String rmk, String modelCd, int issudQty) throws Exception {
      Map json = new LinkedHashMap();
      json.put("sysCd", sysCd);
      json.put("soldToCode", soldToCode);
      json.put("licenseKeyIssudType", licenseKeyIssudType);
      json.put("rmk", rmk);
      json.put("domainInfo", domainName);
      List list = new ArrayList();
      Map json2 = new LinkedHashMap();
      json2.put("modelCd", modelCd);
      json2.put("issudQty", String.valueOf(issudQty));
      list.add(json2);
      json.put("issudDtlInfo", list);
      Gson gson = new Gson();
      JSONObject resultJSON = null;
      OutputStream os = null;
      BufferedReader br = null;

      try {
         URL url = new URL(slmRestAPIDomain + "/spt/rs/slm/gn/mi/iNewLicenseKey");
         HttpURLConnection conn = (HttpURLConnection)url.openConnection();
         conn.setDoOutput(true);
         String authString = smlRestAPIUSER + ":" + smlRestAPIPW;
         String authStringEnc = new String(Base64.encodeBase64(authString.getBytes()));
         conn.setRequestProperty("Authorization", "Basic " + authStringEnc);
         conn.setRequestMethod("POST");
         conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
         conn.setRequestProperty("Accept", "application/json;charset=utf-8");
         conn.setConnectTimeout(slmTimeOut);
         conn.setReadTimeout(slmReadTimeOut);
         os = conn.getOutputStream();
         os.write(gson.toJson(json).getBytes("UTF-8"));
         os.flush();
         br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
         StringBuffer response = new StringBuffer();
         if (conn.getResponseCode() != 200) {
            this.liceseMgr.setSlmLicenseHistoryForE2E("E01", "", "", "", "requestNewLicenseKey HTTP fail", true);
            throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
         } else {
            String output;
            while((output = br.readLine()) != null) {
               response.append(output);
               resultJSON = new JSONObject(response.toString());
            }

            conn.disconnect();
            return resultJSON;
         }
      } catch (Exception var29) {
         this.logger.error("Failed to call iNewLicenseKey " + var29.toString(), var29);
         throw var29;
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }
         } catch (Exception var28) {
            this.logger.error("During closing resource.");
         }

      }
   }

   public JSONObject requestNewActivation_SLMDirect(String sysCd, String issudNo, String licenseKey, String hardwareType, String hardwareTypeVal, SiteInformation siteInformation, String location, String deviceName) throws Exception {
      Map json = new LinkedHashMap();
      json.put("sysCd", sysCd);
      json.put("issudNo", issudNo);
      json.put("licenseKey", licenseKey);
      json.put("hardwareType", hardwareType);
      json.put("hardwareTypeVal", hardwareTypeVal);
      json.put("deviceNm", deviceName);
      Map json2 = new LinkedHashMap();
      json2.put("companyNm", siteInformation.getCompanyName());
      json2.put("deptNm", siteInformation.getDeptName());
      json2.put("addr", siteInformation.getCompanyaddress());
      json2.put("telNo", siteInformation.getTelephone());
      json2.put("emailAddr", siteInformation.getEmailAddress());
      json2.put("rmk", location);
      json.put("installInfoDto", json2);
      Gson gson = new Gson();
      JSONObject resultJSON = null;
      OutputStream os = null;
      BufferedReader br = null;

      try {
         URL url = new URL(slmRestAPIDomain + "/spt/rs/slm/gn/mi/iNewActivation");
         HttpURLConnection conn = (HttpURLConnection)url.openConnection();
         conn.setDoOutput(true);
         String authString = smlRestAPIUSER + ":" + smlRestAPIPW;
         String authStringEnc = new String(Base64.encodeBase64(authString.getBytes()));
         conn.setRequestProperty("Authorization", "Basic " + authStringEnc);
         conn.setRequestMethod("POST");
         conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
         conn.setRequestProperty("Accept", "application/json;charset=utf-8");
         conn.setConnectTimeout(slmTimeOut);
         conn.setReadTimeout(slmReadTimeOut);
         os = conn.getOutputStream();
         os.write(gson.toJson(json).getBytes("UTF-8"));
         os.flush();
         br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
         StringBuffer response = new StringBuffer();
         if (conn.getResponseCode() != 200) {
            this.liceseMgr.setSlmLicenseHistoryForE2E("E01", hardwareTypeVal, licenseKey, issudNo, "requestNewActivation HTTP fail", true);
            throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
         } else {
            String output;
            while((output = br.readLine()) != null) {
               response.append(output);
               resultJSON = new JSONObject(response.toString());
            }

            conn.disconnect();
            return resultJSON;
         }
      } catch (Exception var28) {
         this.logger.error("Failed to call iNewActivation on" + hardwareTypeVal, var28);
         this.logger.error(var28.toString());
         throw var28;
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }
         } catch (Exception var27) {
            this.logger.error("During closing resource.");
         }

      }
   }

   public JSONObject requestReActivation_SLMDirect(String sysCd, String issudNo, String licenseKey, String hardwareType, String hardwareTypeVal) throws Exception {
      Map json = new LinkedHashMap();
      json.put("sysCd", sysCd);
      json.put("issudNo", issudNo);
      json.put("licenseKey", licenseKey);
      json.put("hardwareType", hardwareType);
      json.put("hardwareTypeVal", hardwareTypeVal);
      Gson gson = new Gson();
      JSONObject resultJSON = null;
      OutputStream os = null;
      BufferedReader br = null;

      try {
         URL url = new URL(slmRestAPIDomain + "/spt/rs/slm/gn/mi/iReActivation");
         HttpURLConnection conn = (HttpURLConnection)url.openConnection();
         conn.setDoOutput(true);
         String authString = smlRestAPIUSER + ":" + smlRestAPIPW;
         String authStringEnc = new String(Base64.encodeBase64(authString.getBytes()));
         conn.setRequestProperty("Authorization", "Basic " + authStringEnc);
         conn.setRequestMethod("POST");
         conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
         conn.setRequestProperty("Accept", "application/json;charset=utf-8");
         conn.setConnectTimeout(slmTimeOut);
         conn.setReadTimeout(slmReadTimeOut);
         os = conn.getOutputStream();
         os.write(gson.toJson(json).getBytes("UTF-8"));
         os.flush();
         br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
         StringBuffer response = new StringBuffer();
         if (conn.getResponseCode() != 200) {
            this.liceseMgr.setSlmLicenseHistoryForE2E("E01", hardwareTypeVal, licenseKey, issudNo, "requestNewActivation HTTP fail", true);
            throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
         } else {
            String output;
            while((output = br.readLine()) != null) {
               response.append(output);
               resultJSON = new JSONObject(response.toString());
            }

            conn.disconnect();
            return resultJSON;
         }
      } catch (Exception var24) {
         this.logger.error("Failed to call reActivation on" + hardwareTypeVal, var24);
         this.logger.error(var24.toString());
         throw var24;
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }
         } catch (Exception var23) {
            this.logger.error("During closing resource.");
         }

      }
   }

   public JSONObject requestDeActivation_SLMDirect(String sysCd, String issudNo, String licenseKey, String hardwareType, String hardwareTypeVal) throws Exception {
      Map json = new LinkedHashMap();
      json.put("sysCd", sysCd);
      json.put("ifWorkType", "2");
      json.put("deActivaKey", "");
      json.put("issudNo", issudNo);
      json.put("licenseKey", licenseKey);
      json.put("hardwareType", hardwareType);
      json.put("hardwareTypeVal", hardwareTypeVal);
      Gson gson = new Gson();
      JSONObject resultJSON = null;
      OutputStream os = null;
      BufferedReader br = null;

      try {
         URL url = new URL(slmRestAPIDomain + "/spt/rs/slm/gn/mi/iDeActivation");
         HttpURLConnection conn = (HttpURLConnection)url.openConnection();
         conn.setDoOutput(true);
         String authString = smlRestAPIUSER + ":" + smlRestAPIPW;
         String authStringEnc = new String(Base64.encodeBase64(authString.getBytes()));
         conn.setRequestProperty("Authorization", "Basic " + authStringEnc);
         conn.setRequestMethod("POST");
         conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
         conn.setRequestProperty("Accept", "application/json;charset=utf-8");
         conn.setConnectTimeout(slmTimeOut);
         conn.setReadTimeout(slmReadTimeOut);
         os = conn.getOutputStream();
         os.write(gson.toJson(json).getBytes("UTF-8"));
         os.flush();
         br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
         StringBuffer response = new StringBuffer();
         if (conn.getResponseCode() != 200) {
            this.liceseMgr.setSlmLicenseHistoryForE2E("E01", hardwareTypeVal, licenseKey, issudNo, "requestDeActivation HTTP fail", true);
            throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
         } else {
            String output;
            while((output = br.readLine()) != null) {
               response.append(output);
               resultJSON = new JSONObject(response.toString());
            }

            conn.disconnect();
            return resultJSON;
         }
      } catch (Exception var24) {
         this.logger.error("Failed to call deActivation on" + hardwareTypeVal, var24);
         this.logger.error(var24.toString());
         throw var24;
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }
         } catch (Exception var23) {
            this.logger.error("During closing resource.");
         }

      }
   }

   public JSONObject requestModifyLocation_SLMDirect(String sysCd, String licenseKey, SiteInformation siteInformation, String location) throws Exception {
      Map json = new LinkedHashMap();
      json.put("sysCd", sysCd);
      json.put("licenseKey", licenseKey);
      json.put("companyNm", siteInformation.getCompanyName());
      json.put("deptNm", siteInformation.getDeptName());
      json.put("addr", siteInformation.getCompanyaddress());
      json.put("telNo", siteInformation.getTelephone());
      json.put("emailAddr", siteInformation.getEmailAddress());
      json.put("rmk", location);
      Gson gson = new Gson();
      JSONObject resultJSON = null;
      OutputStream os = null;
      BufferedReader br = null;

      try {
         URL url = new URL(slmRestAPIDomain + "/spt/rs/slm/gn/mi/iSaveSiteInfo");
         HttpURLConnection conn = (HttpURLConnection)url.openConnection();
         conn.setDoOutput(true);
         String authString = smlRestAPIUSER + ":" + smlRestAPIPW;
         String authStringEnc = new String(Base64.encodeBase64(authString.getBytes()));
         conn.setRequestProperty("Authorization", "Basic " + authStringEnc);
         conn.setRequestMethod("POST");
         conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
         conn.setRequestProperty("Accept", "application/json;charset=utf-8");
         conn.setConnectTimeout(slmTimeOut);
         conn.setReadTimeout(slmReadTimeOut);
         os = conn.getOutputStream();
         os.write(gson.toJson(json).getBytes("UTF-8"));
         os.flush();
         br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
         StringBuffer response = new StringBuffer();
         if (conn.getResponseCode() != 200) {
            this.liceseMgr.setSlmLicenseHistoryForE2E("E01", "", licenseKey, "", "requestModifyLocation HTTP fail", true);
            throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
         }

         String output;
         while((output = br.readLine()) != null) {
            response.append(output);
            resultJSON = new JSONObject(response.toString());
         }

         conn.disconnect();
      } catch (MalformedURLException var26) {
         this.logger.error("", var26);
      } catch (IOException var27) {
         this.logger.error("", var27);
      } finally {
         try {
            if (os != null) {
               os.close();
            }

            if (br != null) {
               br.close();
            }
         } catch (Exception var25) {
            this.logger.error("During closing resource.");
         }

      }

      return resultJSON;
   }
}
