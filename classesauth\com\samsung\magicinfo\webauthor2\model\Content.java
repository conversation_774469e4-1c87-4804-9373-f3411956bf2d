package com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.model.Audio;
import com.samsung.magicinfo.webauthor2.model.Document;
import com.samsung.magicinfo.webauthor2.model.Image;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.Video;
import java.sql.Timestamp;

public class Content {
  private MediaType type;
  
  private String name;
  
  private String id;
  
  private Integer versionId;
  
  private String thumbnailId;
  
  private String thumbnailName;
  
  private String fileId;
  
  private String fileName;
  
  private long size;
  
  private Video video;
  
  private Audio audio;
  
  private Image image;
  
  private Document document;
  
  private String createDate;
  
  private String lastModifiedDate;
  
  private long lastModifiedTimestamp;
  
  private Integer duration;
  
  private String playTime;
  
  private String resolution;
  
  private String refreshInterval;
  
  private String deviceType;
  
  private String deviceTypeVersion;
  
  private String htmlStartPage;
  
  public Content(MediaType type) {
    this.type = type;
  }
  
  public Content(MediaType type, String name, String id, Integer versionId, String thumbnailId, long size, String thumbnailName, String fileName, String fileId, Integer duration, String playTime, String createDate, String lastModifiedDate, String resolution, String refreshInterval, String deviceType, String deviceTypeVersion, String htmlStartPage) {
    this.type = type;
    this.name = name;
    this.id = id;
    this.versionId = versionId;
    this.thumbnailId = thumbnailId;
    this.size = size;
    this.thumbnailName = thumbnailName;
    this.fileName = fileName;
    this.fileId = fileId;
    this.duration = duration;
    this.playTime = playTime;
    this.createDate = createDate;
    this.lastModifiedDate = lastModifiedDate;
    this.lastModifiedTimestamp = convertToTimestampDiference(lastModifiedDate);
    this.resolution = resolution;
    this.refreshInterval = refreshInterval;
    this.deviceType = deviceType;
    this.deviceTypeVersion = deviceTypeVersion;
    this.htmlStartPage = htmlStartPage;
  }
  
  public MediaType getType() {
    return this.type;
  }
  
  public String getName() {
    return this.name;
  }
  
  public String getId() {
    return this.id;
  }
  
  public Integer getVersionId() {
    return this.versionId;
  }
  
  public String getThumbnailId() {
    return this.thumbnailId;
  }
  
  public long getSize() {
    return this.size;
  }
  
  public Video getVideo() {
    return this.video;
  }
  
  public Audio getAudio() {
    return this.audio;
  }
  
  public Image getImage() {
    return this.image;
  }
  
  public Document getDocument() {
    return this.document;
  }
  
  public void setVideo(Video video) {
    this.video = video;
  }
  
  public void setAudio(Audio audio) {
    this.audio = audio;
  }
  
  public void setImage(Image image) {
    this.image = image;
  }
  
  public void setDocument(Document document) {
    this.document = document;
  }
  
  public void setThumbnailName(String thumbnailName) {
    this.thumbnailName = thumbnailName;
  }
  
  public String getThumbnailName() {
    return this.thumbnailName;
  }
  
  public void setType(MediaType type) {
    this.type = type;
  }
  
  public void setId(String id) {
    this.id = id;
  }
  
  public void setVersionId(Integer versionId) {
    this.versionId = versionId;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public void setThumbnailId(String thumbnailId) {
    this.thumbnailId = thumbnailId;
  }
  
  public void setFileId(String fileId) {
    this.fileId = fileId;
  }
  
  public void setFileName(String fileName) {
    this.fileName = fileName;
  }
  
  public String getFileId() {
    return this.fileId;
  }
  
  public String getFileName() {
    return this.fileName;
  }
  
  public void setSize(long size) {
    this.size = size;
  }
  
  public String getCreateDate() {
    return this.createDate;
  }
  
  public void setCreateDate(String createDate) {
    this.createDate = createDate;
  }
  
  public String getPlayTime() {
    return this.playTime;
  }
  
  public void setPlayTime(String playTime) {
    this.playTime = playTime;
  }
  
  public String getLastModifiedDate() {
    return this.lastModifiedDate;
  }
  
  public void setLastModifiedDate(String lastModifiedDate) {
    this.lastModifiedDate = lastModifiedDate;
  }
  
  public long getLastModifiedTimestamp() {
    return this.lastModifiedTimestamp;
  }
  
  public void setLastModifiedTimestamp(long lastModifiedTimestamp) {
    this.lastModifiedTimestamp = lastModifiedTimestamp;
  }
  
  public Integer getDuration() {
    return this.duration;
  }
  
  public void setDuration(Integer duration) {
    this.duration = duration;
  }
  
  private long convertToTimestampDiference(String lastModifiedDate) {
    return Timestamp.valueOf(lastModifiedDate).getTime();
  }
  
  public String getResolution() {
    return this.resolution;
  }
  
  public String getRefreshInterval() {
    return this.refreshInterval;
  }
  
  public String getDeviceType() {
    return this.deviceType;
  }
  
  public String getDeviceTypeVersion() {
    return this.deviceTypeVersion;
  }
  
  public String getHtmlStartPage() {
    return this.htmlStartPage;
  }
  
  public void setHtmlStartPage(String startPage) {
    this.htmlStartPage = startPage;
  }
  
  public String toString() {
    return "Content{type=" + this.type + ", name=" + this.name + ", id=" + this.id + ", versionId=" + this.versionId + ", thumbnailId=" + this.thumbnailId + ", thumbnailName=" + this.thumbnailName + ", fileId=" + this.fileId + ", fileName=" + this.fileName + ", size=" + this.size + ", video=" + this.video + ", audio=" + this.audio + ", image=" + this.image + ", document=" + this.document + ", createDate=" + this.createDate + ", lastModifiedDate=" + this.lastModifiedDate + ", lastModifiedTimestamp=" + this.lastModifiedTimestamp + ", duration=" + this.duration + ", playTime=" + this.playTime + ", resolution=" + this.resolution + ", refreshInterval=" + this.refreshInterval + ", deviceType=" + this.deviceType + ", deviceTypeVersion=" + this.deviceTypeVersion + ", htmlStartPage=" + this.htmlStartPage + '}';
  }
}
