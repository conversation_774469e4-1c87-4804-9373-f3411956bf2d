package com.samsung.magicinfo.restapi.contents.service;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.hierynomus.msfscc.fileinformation.FileIdBothDirectoryInformation;
import com.hierynomus.smbj.SMBClient;
import com.hierynomus.smbj.SmbConfig;
import com.hierynomus.smbj.auth.AuthenticationContext;
import com.hierynomus.smbj.connection.Connection;
import com.hierynomus.smbj.session.Session;
import com.hierynomus.smbj.share.DiskShare;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.export.PdfBuilder;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.AISRUtils;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeleteContentUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.common.utils.FileUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.common.FileManager;
import com.samsung.magicinfo.framework.common.FileManagerImpl;
import com.samsung.magicinfo.framework.content.constants.ContentConstants;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.ContentLog;
import com.samsung.magicinfo.framework.content.entity.ContentPollingLog;
import com.samsung.magicinfo.framework.content.entity.ContentProductCodeHistoryEntity;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.entity.NotificationData;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistLog;
import com.samsung.magicinfo.framework.content.manager.ContentCodeInfo;
import com.samsung.magicinfo.framework.content.manager.ContentCodeInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentXmlManager;
import com.samsung.magicinfo.framework.content.manager.ConvertDataInfo;
import com.samsung.magicinfo.framework.content.manager.ConvertDataInfoImpl;
import com.samsung.magicinfo.framework.content.manager.LogInfo;
import com.samsung.magicinfo.framework.content.manager.LogInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.content.manager.SessionInfo;
import com.samsung.magicinfo.framework.content.manager.SessionInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ShareFolderInfo;
import com.samsung.magicinfo.framework.content.manager.ShareFolderInfoImpl;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistInterface;
import com.samsung.magicinfo.framework.role.manager.AbilityInfo;
import com.samsung.magicinfo.framework.role.manager.AbilityInfoImpl;
import com.samsung.magicinfo.framework.role.manager.RoleInfo;
import com.samsung.magicinfo.framework.role.manager.RoleInfoImpl;
import com.samsung.magicinfo.framework.ruleset.entity.RuleSet;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfo;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfoImpl;
import com.samsung.magicinfo.framework.ruleset.manager.RulesetUtils;
import com.samsung.magicinfo.framework.scheduler.dao.EventInfoDao;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.common.ScheduleInterface;
import com.samsung.magicinfo.framework.setup.dao.InsightIndexDao;
import com.samsung.magicinfo.framework.setup.entity.InsightIndexEntity;
import com.samsung.magicinfo.framework.setup.entity.TagEntity;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfo;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.TagInfo;
import com.samsung.magicinfo.framework.setup.manager.TagInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.msgqueue.monitor.BrokerMonitor;
import com.samsung.magicinfo.msgqueue.monitor.BrokerMonitorImpl;
import com.samsung.magicinfo.msgqueue.util.AMQUtil;
import com.samsung.magicinfo.openapi.auth.TokenRegistry;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.file.CifsContentScheduleJob;
import com.samsung.magicinfo.protocol.file.CifsFileDownloadThread;
import com.samsung.magicinfo.protocol.file.CifsFilesToDownload;
import com.samsung.magicinfo.protocol.file.FtpContentScheduleJob;
import com.samsung.magicinfo.protocol.file.FtpFileDownloadThread;
import com.samsung.magicinfo.protocol.file.UrlFilesToDownload;
import com.samsung.magicinfo.protocol.scheduler.ScheduleManager;
import com.samsung.magicinfo.protocol.util.MailUtil;
import com.samsung.magicinfo.protocol.util.TimeUtil;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonResultResource;
import com.samsung.magicinfo.restapi.contents.model.V2AdsContentPublisherInfo;
import com.samsung.magicinfo.restapi.contents.model.V2AdsContentSettingResource;
import com.samsung.magicinfo.restapi.contents.model.V2AdsContentSuggestionResource;
import com.samsung.magicinfo.restapi.contents.model.V2AdvertisementValueResource;
import com.samsung.magicinfo.restapi.contents.model.V2AuthorityToMoveContent;
import com.samsung.magicinfo.restapi.contents.model.V2CifsContentSettingResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentAdvertisementEditResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentAdvertisementMultiEditResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentAdvertisementResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentApproval;
import com.samsung.magicinfo.restapi.contents.model.V2ContentApprovals;
import com.samsung.magicinfo.restapi.contents.model.V2ContentCategoryResourceRequestWrapper;
import com.samsung.magicinfo.restapi.contents.model.V2ContentCheckResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentConvertibility;
import com.samsung.magicinfo.restapi.contents.model.V2ContentConvertibilityList;
import com.samsung.magicinfo.restapi.contents.model.V2ContentDeleteCheckResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentDeleteFail;
import com.samsung.magicinfo.restapi.contents.model.V2ContentDeleteParam;
import com.samsung.magicinfo.restapi.contents.model.V2ContentEdit;
import com.samsung.magicinfo.restapi.contents.model.V2ContentExpireDate;
import com.samsung.magicinfo.restapi.contents.model.V2ContentFileResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentFileSizeInterval;
import com.samsung.magicinfo.restapi.contents.model.V2ContentIds;
import com.samsung.magicinfo.restapi.contents.model.V2ContentListFilter;
import com.samsung.magicinfo.restapi.contents.model.V2ContentResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentSalesDataResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentShare;
import com.samsung.magicinfo.restapi.contents.model.V2ContentTagAssignment;
import com.samsung.magicinfo.restapi.contents.model.V2ContentThumbnailResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentUse;
import com.samsung.magicinfo.restapi.contents.model.V2ContentVersion;
import com.samsung.magicinfo.restapi.contents.model.V2ContentWebAuthorResource;
import com.samsung.magicinfo.restapi.contents.model.V2ExpiredContentListResource;
import com.samsung.magicinfo.restapi.contents.model.V2FtpContentSettingResource;
import com.samsung.magicinfo.restapi.contents.model.V2InsightIndexForContentResource;
import com.samsung.magicinfo.restapi.contents.model.V2InsightIndexValueForContentResource;
import com.samsung.magicinfo.restapi.contents.model.V2InsightIndexValueIdListResource;
import com.samsung.magicinfo.restapi.contents.model.V2PlaylistAddContentsResource;
import com.samsung.magicinfo.restapi.contents.model.V2PlaylistAddData;
import com.samsung.magicinfo.restapi.contents.model.V2PlaylistAddResource;
import com.samsung.magicinfo.restapi.contents.model.V2ProductCodeChangeHistory;
import com.samsung.magicinfo.restapi.contents.model.V2UrlContentSettingResource;
import com.samsung.magicinfo.restapi.exception.BaseRestException;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.setting.model.V2TagAssignment;
import com.samsung.magicinfo.restapi.setting.model.V2TagResource;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.service.statistics.DeviceStatisticsDownloadService;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.zip.ZipOutputStream;
import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.ftpserver.db.DownloadInfo;
import org.apache.ftpserver.db.DownloadInfoImpl;
import org.apache.logging.log4j.Logger;
import org.apache.xml.security.utils.Base64;
import org.json.JSONArray;
import org.quartz.JobDetail;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SimpleTrigger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import sun.awt.image.ToolkitImage;

@Service("V2ContentService")
@Transactional
public class V2ContentServiceImpl implements V2ContentService {
   protected Logger logger = LoggingManagerV2.getLogger(V2ContentServiceImpl.class);
   private static List imageFexts = Arrays.asList("gif", "jpeg", "jpg", "png", "bmp", "wmf", "emf", "tif", "tiff");
   private static List videoFexts = Arrays.asList("asf", "avi", "mpeg", "mpg", "ts", "trp", "m2v", "m2p", "mp4", "m1v", "m4v", "m4t", "vob", "m2t", "tsp", "mov", "asx", "wmv", "tp", "flv", "ra", "mkv", "rm", "ram", "rmvb", "3gp", "svi", "m2ts", "divx", "mts", "vro");
   private String CONTENTS_HOME = null;
   private String THUMBNAIL_HOME = null;
   private DeviceStatisticsDownloadService downloadService = null;
   @Autowired
   private InsightIndexDao insightIndexDao;

   public V2ContentServiceImpl() {
      super();
   }

   public void setDownloadService(DeviceStatisticsDownloadService downloadService) {
      this.downloadService = downloadService;
   }

   @PreAuthorize("hasAnyAuthority('Statistics Manage Authority', 'Content Read Authority', 'Content Write Authority', 'Content Manage Authority', 'Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Add Authority', 'Content Schedule Manage Authority')")
   public V2PageResource getContents(V2ContentListFilter filter) throws Exception {
      if (!StrUtils.nvl(filter.getGroupId()).equals("")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(filter.getGroupId()));
      }

      List resources = new ArrayList();
      Long MAX_CONTENT_FILE_SIZE = 102400L;
      String sort = "";
      String dir = "";
      int startIndex = filter.getStartIndex();
      int pageSize = filter.getPageSize();
      String group_type = StrUtils.nvl(filter.getGroupType()).equals("") ? "ALL" : filter.getGroupType();
      String media_type = StrUtils.nvl(filter.getMediaType()).equals("") ? "" : filter.getMediaType();
      String group_id = StrUtils.nvl(filter.getGroupId()).equals("") ? "0" : filter.getGroupId();
      String byUserOrganizationId = StrUtils.nvl(filter.getUserOrganizationId()).equals("") ? "" : filter.getUserOrganizationId();
      String share_folder_id = StrUtils.nvl(filter.getShareFolderId()).equals("") ? "0" : filter.getShareFolderId();
      String user_id = StrUtils.nvl(filter.getUserId()).equals("") ? "" : filter.getUserId();
      String search_text = StrUtils.nvl(filter.getSearchText()).equals("") ? "" : filter.getSearchText();
      String search_id = StrUtils.nvl(filter.getSearchId()).equals("") ? "-1" : filter.getSearchId();
      String my_content = filter.getIsMyContent() ? "TRUE" : "FALSE";
      String isSelectContent = "";
      String endDate = filter.getEndModifiedDate();
      String startDate = filter.getStartModifiedDate();
      String search_creator = filter.getSearchCreator();
      String deviceTypeParameter = filter.getDeviceType();
      String deviceTypeVersionParameter = filter.getDeviceTypeVersion() != null ? String.valueOf(filter.getDeviceTypeVersion()) : null;
      String is_main = filter.getIsMainPage() ? "true" : "false";
      String isVwlMode = filter.getIsVwlMode() ? "true" : "false";
      String source = StrUtils.nvl(filter.getSource()).equals("") ? "" : filter.getSource();
      if (filter.getSortColumn().equalsIgnoreCase("CONTENT_NAME")) {
         sort = "content_name";
      } else if (filter.getSortColumn().equalsIgnoreCase("CREATOR_ID")) {
         sort = "creator_id";
      } else if (filter.getSortColumn().equalsIgnoreCase("APPROVAL_STATUS")) {
         sort = "approval_status";
      } else if (filter.getSortColumn().equalsIgnoreCase("LAST_MODIFIED_DATE")) {
         sort = "last_modified_date";
      } else if (filter.getSortColumn().equalsIgnoreCase("EXPIRATION_DATE")) {
         sort = "expiration_date";
      } else {
         sort = "last_modified_date";
      }

      if (filter.getSortOrder().equalsIgnoreCase("DESC")) {
         dir = "desc";
      } else if (filter.getSortOrder().equalsIgnoreCase("ASC")) {
         dir = "asc";
      } else {
         dir = "desc";
      }

      String categoryIds = filter.getCategoryIds().isEmpty() ? "" : ConvertUtil.convertListToStringWithSeparator(filter.getCategoryIds(), ",");
      String deviceTypeFilter = "";
      if (filter.getDeviceTypes() != null && !filter.getDeviceTypes().isEmpty()) {
         if (filter.getDeviceTypes().contains("NONE")) {
            V2PageResource pageResource = V2PageResource.createPageResource(resources, 0);
            pageResource.setStartIndex(startIndex);
            pageResource.setPageSize(pageSize);
            pageResource.setRecordsTotal(0);
            return pageResource;
         }

         if (filter.getDeviceTypes().contains("ALL")) {
            filter.getDeviceTypes().clear();
         }

         deviceTypeFilter = ConvertUtil.convertListToStringWithSeparator(filter.getDeviceTypes(), ",");
      }

      String contentTypeFilter = "";
      if (filter.getContentTypes() != null && !filter.getContentTypes().isEmpty()) {
         if (filter.getContentTypes().contains("NONE")) {
            V2PageResource pageResource = V2PageResource.createPageResource(resources, 0);
            pageResource.setStartIndex(startIndex);
            pageResource.setPageSize(pageSize);
            pageResource.setRecordsTotal(0);
            return pageResource;
         }

         if (filter.getContentTypes().contains("ALL")) {
            filter.getContentTypes().clear();
         }

         contentTypeFilter = ConvertUtil.convertListToStringWithSeparator(filter.getContentTypes(), ",");
      }

      String creatorIdFilter = "";
      if (filter.getCreatorIds() != null && !filter.getCreatorIds().isEmpty()) {
         creatorIdFilter = ConvertUtil.convertListToStringWithSeparator(filter.getCreatorIds(), ",");
      }

      String approvalFilter = !StrUtils.nvl(filter.getApprovalStatus()).isEmpty() ? "" : filter.getApprovalStatus();
      if (filter.getApprovalStatus() != null && !filter.getApprovalStatus().isEmpty()) {
         if (filter.getApprovalStatus().equalsIgnoreCase("UNAPPROVED")) {
            approvalFilter = "unapproval_content";
         } else if (filter.getApprovalStatus().equalsIgnoreCase("APPROVED")) {
            approvalFilter = "approval_content";
         } else if (filter.getApprovalStatus().equalsIgnoreCase("ALL")) {
            approvalFilter = "device_status_view_all";
         } else {
            approvalFilter = "";
         }
      }

      String tagIds = "";
      if (filter.getTagIds() != null && !filter.getTagIds().isEmpty()) {
         tagIds = ConvertUtil.convertListToStringWithSeparator(filter.getTagIds(), ",");
      }

      List fileSizeIntervalFilter = null;
      if (filter.getFileSizes() != null && !filter.getFileSizes().isEmpty()) {
         fileSizeIntervalFilter = filter.getFileSizes();
      }

      String contentUsingStatusFilter = "";
      if (filter.getContentUsingStatus() != null && !filter.getContentUsingStatus().isEmpty()) {
         if (filter.getContentUsingStatus().equalsIgnoreCase("IN_USE")) {
            contentUsingStatusFilter = "used_content";
         } else if (filter.getContentUsingStatus().equalsIgnoreCase("UNUSED")) {
            contentUsingStatusFilter = "unused_content";
         } else if (filter.getContentUsingStatus().equalsIgnoreCase("ALL")) {
            contentUsingStatusFilter = "device_status_view_all";
         } else {
            contentUsingStatusFilter = "";
         }
      }

      String expirationStatusFilter = "";
      if (filter.getExpirationStatus() != null && !filter.getExpirationStatus().isEmpty()) {
         if (filter.getExpirationStatus().equalsIgnoreCase("ALL")) {
            expirationStatusFilter = "device_status_view_all";
         } else if (filter.getExpirationStatus().equalsIgnoreCase("EXPIRED")) {
            expirationStatusFilter = "expired_content";
         } else if (filter.getExpirationStatus().equalsIgnoreCase("VALID")) {
            expirationStatusFilter = "valid_content";
         } else {
            expirationStatusFilter = "";
         }
      }

      String media_type_filter = "";
      if (filter.getMediaTypeFilter() != null && !filter.getMediaTypeFilter().isEmpty()) {
         media_type_filter = ConvertUtil.convertListToStringWithSeparator(filter.getMediaTypeFilter(), ",");
      }

      String playlist_type = "";
      if (filter.getPlaylistType().equalsIgnoreCase("PREMIUM")) {
         playlist_type = "0";
      } else if (filter.getPlaylistType().equalsIgnoreCase("AMS")) {
         playlist_type = "1";
      } else if (filter.getPlaylistType().equalsIgnoreCase("VWL")) {
         playlist_type = "2";
      } else if (filter.getPlaylistType().equalsIgnoreCase("SYNCPLAY")) {
         playlist_type = "3";
      } else if (filter.getPlaylistType().equalsIgnoreCase("ADVERTISEMENT")) {
         playlist_type = "4";
      } else if (filter.getPlaylistType().equalsIgnoreCase("TAG")) {
         playlist_type = "5";
      } else if (filter.getPlaylistType().equalsIgnoreCase("LINKED")) {
         playlist_type = "6";
      } else {
         playlist_type = "0";
      }

      String content_type = StrUtils.nvl(filter.getContentType()).equals("") ? "CONTENT" : filter.getContentType();
      String orgName = SecurityUtils.getUserContainer().getUser().getOrganization();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      long orgId = userGroupInfo.getOrgGroupIdByName(orgName);
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
      boolean contentsApprovalEnable = false;
      if (infoMap != null && infoMap.get("CONTENTS_APPROVAL_ENABLE") != null) {
         contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
      }

      boolean myContentDelete = false;
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      if (my_content != null && my_content.equalsIgnoreCase("TRUE") && group_type.equalsIgnoreCase("DELETED")) {
         Long longGroupId = 0L;

         try {
            longGroupId = Long.valueOf(group_id);
         } catch (Exception var67) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"groupId"});
         }

         user_id = cmsDao.getGroupInfo(longGroupId).getCreator_id();
         myContentDelete = true;
      }

      if (my_content != null && my_content.equalsIgnoreCase("TRUE")) {
         group_type = "USER";
         user_id = this.getLoginUserId();
      }

      boolean canEditOthers = false;
      boolean canReadUnshared = false;
      cmsDao = ContentInfoImpl.getInstance();
      canEditOthers = cmsDao.getCanEditOthers(user_id, group_type, (HttpServletRequest)null);
      ListManager listMgr = new ListManager(cmsDao, "commonlist");
      listMgr.addSearchInfo("sortColumn", sort);
      listMgr.addSearchInfo("sortOrder", dir);
      search_text = search_text.replaceAll("]", "^]");
      search_text = search_text.replaceAll("%", "^%");
      listMgr.addSearchInfo("searchText", search_text);
      listMgr.addSearchInfo("isMain", is_main);
      if (expirationStatusFilter != null && !expirationStatusFilter.equals("")) {
         listMgr.addSearchInfo("expirationStatusFilter", expirationStatusFilter);
      }

      if (source != null && !source.equals("")) {
         listMgr.addSearchInfo("source", source);
         this.logger.info("RQ190703-00340 : " + source);
      }

      if (approvalFilter != null && !approvalFilter.equals("")) {
         listMgr.addSearchInfo("contentApprovalFilter", approvalFilter);
      }

      this.logger.error("Hennry #000 deviceTypeFilter : " + deviceTypeFilter);
      if (!deviceTypeFilter.equals("")) {
         listMgr.addSearchInfo("deviceFilter", deviceTypeFilter);
      }

      String[] categoryFilterList;
      if (creatorIdFilter != null && !creatorIdFilter.equals("")) {
         categoryFilterList = creatorIdFilter.split(",");
         listMgr.addSearchInfo("userFilter", categoryFilterList);
      }

      if (tagIds != null && !tagIds.equals("")) {
         categoryFilterList = tagIds.split(",");
         listMgr.addSearchInfo("tagFilter", categoryFilterList);
      }

      if (fileSizeIntervalFilter != null && !fileSizeIntervalFilter.isEmpty()) {
         List sizeFilterMapList = new ArrayList();

         HashMap sizeFilterMap;
         for(Iterator var51 = fileSizeIntervalFilter.iterator(); var51.hasNext(); sizeFilterMapList.add(sizeFilterMap)) {
            V2ContentFileSizeInterval interval = (V2ContentFileSizeInterval)var51.next();
            sizeFilterMap = new HashMap();
            Long[] sizeStringLong = new Long[2];

            try {
               if (interval.getMinFileSizeInMB() != null) {
                  sizeStringLong[0] = interval.getMinFileSizeInMB();
                  if (sizeStringLong[0] > MAX_CONTENT_FILE_SIZE) {
                     sizeStringLong[0] = MAX_CONTENT_FILE_SIZE;
                  }

                  sizeFilterMap.put("start", sizeStringLong[0] * 1024L * 1024L);
               }

               if (interval.getMaxFileSizeInMB() != null) {
                  sizeStringLong[1] = interval.getMaxFileSizeInMB();
                  if (sizeStringLong[1] > MAX_CONTENT_FILE_SIZE) {
                     sizeStringLong[1] = MAX_CONTENT_FILE_SIZE;
                  }

                  sizeFilterMap.put("end", sizeStringLong[1] * 1024L * 1024L);
               }
            } catch (Exception var68) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"sizeFilter"});
            }
         }

         listMgr.addSearchInfo("sizeFilter", sizeFilterMapList);
      }

      if (StringUtils.isNotBlank(contentUsingStatusFilter)) {
         listMgr.addSearchInfo("contentUsingStatusFilter", contentUsingStatusFilter);
      }

      if (contentTypeFilter != null && !contentTypeFilter.equals("")) {
         if (contentTypeFilter.contains("TLFD")) {
            listMgr.addSearchInfo("isTLFD", "Y");
         }

         categoryFilterList = contentTypeFilter.split(",");
         listMgr.addSearchInfo("contentFilter", categoryFilterList);
      }

      if (isVwlMode != null && isVwlMode.equals("true")) {
         listMgr.addSearchInfo("is_vwl_mode", "Y");
      }

      if (media_type.equalsIgnoreCase("PLAYLIST")) {
         listMgr.addSearchInfo("playlist_type", playlist_type);
         listMgr.addSearchInfo("mediaType", media_type);
      }

      listMgr.addSearchInfo("content_type", content_type);
      listMgr.addSearchInfo("isSelectContent", isSelectContent);
      if (media_type_filter != null && !media_type_filter.isEmpty()) {
         if (contentTypeFilter.contains("TLFD")) {
            listMgr.addSearchInfo("isTLFD", "Y");
         }

         listMgr.addSearchInfo("media_type_filter", media_type_filter);
      }

      if (deviceTypeParameter != null && deviceTypeVersionParameter != null) {
         listMgr.addSearchInfo("deviceType", deviceTypeParameter);
         listMgr.addSearchInfo("deviceTypeVersion", deviceTypeVersionParameter);
      }

      if (group_type.equalsIgnoreCase("USER") || group_type.equalsIgnoreCase("ORGAN")) {
         if (canEditOthers) {
            listMgr.addSearchInfo("viewRange", "all");
         } else {
            listMgr.addSearchInfo("viewRange", "shared");
         }
      }

      if (categoryIds != null && !categoryIds.equals("")) {
         categoryFilterList = categoryIds.split(",");
         listMgr.addSearchInfo("category", categoryIds);
         listMgr.addSearchInfo("categoryFilterList", categoryFilterList);
      }

      if (contentTypeFilter != null && !contentTypeFilter.contains("TLFD")) {
         if (myContentDelete) {
            listMgr.addSearchInfo("myContentDelete", myContentDelete);
            listMgr.addSearchInfo("creatorID", user_id);
         } else if (group_type.equalsIgnoreCase("USER")) {
            if (user_id.isEmpty()) {
               listMgr.addSearchInfo("creatorID", this.getLoginUserId());
            } else {
               listMgr.addSearchInfo("creatorID", user_id);
            }
         } else if (group_type.equalsIgnoreCase("GROUPED")) {
            Long longGroupId = 0L;

            try {
               longGroupId = Long.valueOf(group_id);
            } catch (Exception var66) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"groupId"});
            }

            if (cmsDao.getGroupInfo(longGroupId) != null) {
               user_id = cmsDao.getGroupInfo(longGroupId).getCreator_id();
            }

            listMgr.addSearchInfo("creatorID", user_id);
         } else {
            listMgr.addSearchInfo("creatorID", this.getLoginUserId());
         }
      }

      AbilityUtils abilityUtils = new AbilityUtils();
      AbilityInfo abilityInfo = AbilityInfoImpl.getInstance();
      List abilityList = abilityInfo.getAllAbilityListByUserId(this.getLoginUserId());
      Iterator it = abilityList.iterator();

      String loginUserRoleName;
      while(it.hasNext()) {
         Map abilityMap = (Map)it.next();
         loginUserRoleName = (String)abilityMap.get("ability_name");
         if (loginUserRoleName.equalsIgnoreCase("Content Manage Authority")) {
            canReadUnshared = true;
         }
      }

      if (user_id.equalsIgnoreCase(this.getLoginUserId())) {
         canReadUnshared = true;
      }

      if (is_main != null && is_main.equalsIgnoreCase("true")) {
         if (contentsApprovalEnable && !abilityUtils.isContentApprovalAuthority()) {
            listMgr.addSearchInfo("isContentApprove", true);
         }
      } else if (contentsApprovalEnable) {
         listMgr.addSearchInfo("approval_status", "APPROVED");
      }

      if (!share_folder_id.equals("0")) {
         group_type = "SHAREFOLDER";
      }

      listMgr.addSearchInfo("canReadUnshared", canReadUnshared);
      listMgr.addSearchInfo("searchID", search_id);
      listMgr.addSearchInfo("startDate", startDate);
      listMgr.addSearchInfo("endDate", endDate);
      listMgr.addSearchInfo("searchCreator", search_creator);
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      if (Long.parseLong(search_id) >= 0L) {
         listMgr.setSection("getSearchList");
      } else if (group_type.equalsIgnoreCase("GROUPED")) {
         listMgr.addSearchInfo("groupID", group_id);
         listMgr.addSearchInfo("listType", group_type);
         listMgr.setSection("getContentList");
      } else if (group_type.equalsIgnoreCase("SUBMITTED")) {
         listMgr.addSearchInfo("groupID", group_id);
         listMgr.addSearchInfo("listType", group_type);
         listMgr.setSection("getContentList");
      } else if (group_type.equalsIgnoreCase("SHAREFOLDER")) {
         listMgr.addSearchInfo("share_folder_id", Long.valueOf(share_folder_id));
         listMgr.addSearchInfo("listType", "SHAREFOLDER");
         listMgr.setSection("getContentList");
      } else {
         listMgr.addSearchInfo("listType", group_type);
         listMgr.setSection("getContentList");
      }

      loginUserRoleName = SecurityUtils.getUserContainer().getUser().getRole_name();
      if (loginUserRoleName.equals("Server Administrator") || SecurityUtils.getLoginUserOrganizationId() == 0L && abilityUtils.checkAuthority("Content Manage")) {
         listMgr.addSearchInfo("isServerAdmin", true);
      }

      if (byUserOrganizationId != null && !byUserOrganizationId.isEmpty()) {
         listMgr.addSearchInfo("byUserOrganizationId", byUserOrganizationId);
      }

      if (group_type.equalsIgnoreCase("SHAREFOLDER")) {
         listMgr.addSearchInfo("byUserOrganizationId", "");
         listMgr.addSearchInfo("creatorID", "");
      }

      List contentList = listMgr.V2dbexecute(startIndex, pageSize);
      PageManager pageManager = listMgr.getPageManager();
      int totalRowCount = pageManager.getTotalRowCount();
      if (contentList != null) {
         for(int i = 0; i < contentList.size(); ++i) {
            V2ContentResource resource = this.getContentDetail((Content)contentList.get(i));
            if (filter.getIsInsightRequested() != null && filter.getIsInsightRequested()) {
               this.getMoreInformationForInsight(resource);
            }

            if (resource != null) {
               resources.add(resource);
            } else {
               --totalRowCount;
               String logContentId = "contentId: " + StrUtils.nvl(((Content)contentList.get(i)).getContent_id()) + "\n";
               String logContentName = "contentName: " + StrUtils.nvl(((Content)contentList.get(i)).getContent_name()) + "\n";
               String logCreateDate = "createDate: " + TimeUtil.getTimeStr(((Content)contentList.get(i)).getCreate_date()) + "\n";
               String logMediaType = "mediaType: " + StrUtils.nvl(((Content)contentList.get(i)).getMedia_type()) + "\n";
               String logMainFileId = "mainFileId: " + StrUtils.nvl(((Content)contentList.get(i)).getMain_file_id());
               this.logger.error("[CONTENT NOT FOUND ERROR INFORMATION]\n" + logContentId + logContentName + logCreateDate + logMediaType + logMainFileId);
            }
         }

         pageManager.setTotalRowCount(totalRowCount);
      }

      V2PageResource pageResource = V2PageResource.createPageResource(resources, pageManager);
      return pageResource;
   }

   private void getMoreInformationForInsight(V2ContentResource resource) {
      ContentInfoImpl contentInfo = ContentInfoImpl.getInstance();

      try {
         V2AdvertisementValueResource advertisementValues = new V2AdvertisementValueResource();
         List indexValueList = contentInfo.getAssignedAdvertisementByContentId(resource.getContentId(), "INSIGHT_INDEX");
         List productCodeValueList = contentInfo.getAssignedAdvertisementByContentId(resource.getContentId(), "PRODUCT_CODE");
         advertisementValues.setInsightIndexValues(indexValueList);
         advertisementValues.setProductCodeValues(productCodeValueList);
         resource.setAdvertisementValues(advertisementValues);
      } catch (Exception var7) {
         this.logger.error("[Insight Requested][Content ID: " + resource.getContentId() + "] Get Assigned Insight Index Value Error. " + var7.getMessage());
      }

      try {
         String thumbnailId = StringUtils.isBlank(resource.getThumbFileId()) ? contentInfo.getThumbIdByMainFileId(resource.getMainFileId()) : resource.getThumbFileId();
         V2ContentThumbnailResource thumbnailResource = this.downloadThumbnail(thumbnailId, (Integer)null, (Integer)null, "MEDIUM", (String)null, (String)null);
         resource.setThumbnail(thumbnailResource);
      } catch (Exception var6) {
         this.logger.error("[Insight Requested][Content ID: " + resource.getContentId() + "] Get Thumbnail Error. " + var6.getMessage());
      }

   }

   private boolean isAplayerSupportMediaType(Content content) throws SQLException {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      List APLAYER_SUPPORT_MEDIA_TYPE_LIST = cInfo.getMediaTypeByDeviceType("APLAYER");
      String mediaType = content.getMedia_type();
      String deviceType = content.getDevice_type();
      String fileType = content.getMain_file_Extension();
      if (mediaType.equalsIgnoreCase("LFD")) {
         return deviceType.equalsIgnoreCase("APLAYER");
      } else if (mediaType.equalsIgnoreCase("DLK")) {
         return false;
      } else if (fileType.equalsIgnoreCase("DIVX")) {
         return false;
      } else {
         for(int i = 0; i < APLAYER_SUPPORT_MEDIA_TYPE_LIST.size(); ++i) {
            if (mediaType.equalsIgnoreCase((String)APLAYER_SUPPORT_MEDIA_TYPE_LIST.get(i))) {
               return true;
            }
         }

         return false;
      }
   }

   private boolean isWplayerSupportMediaType(Content content) throws SQLException {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      List WPLAYER_SUPPORT_FILE_TYPE_LIST = cInfo.getFileTypeByDeviceTypeAndVersion("WPLAYER", 1.0F);
      String fileType = content.getMain_file_Extension();
      String deviceType = content.getDevice_type();
      if (fileType != null && !fileType.equals("")) {
         if (fileType.equalsIgnoreCase("LFD") || fileType.equalsIgnoreCase("DLK") || fileType.equalsIgnoreCase("LFT")) {
            return deviceType.equalsIgnoreCase("WPLAYER");
         }

         if (WPLAYER_SUPPORT_FILE_TYPE_LIST.contains(fileType)) {
            return true;
         }
      } else {
         String mediaType = content.getMedia_type();
         if (mediaType.equalsIgnoreCase("TLFD") && deviceType.equalsIgnoreCase("WPLAYER")) {
            return true;
         }
      }

      return false;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority', 'Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Add Authority', 'Content Schedule Manage Authority')")
   public V2ContentResource getContent(String contentId) throws Exception {
      if (contentId != null) {
         V2ContentResource resource = this.getContentDetail(contentId);
         if (resource == null) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{contentId});
         } else {
            return resource;
         }
      } else {
         return null;
      }
   }

   private V2ContentResource getContentDetail(String contentId) throws SQLException {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      String orgName = SecurityUtils.getUserContainer().getUser().getOrganization();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      long orgId = userGroupInfo.getOrgGroupIdByName(orgName);
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      serverSetupDao.getServerInfoByOrgId(orgId);

      try {
         Content content = cInfo.getContentAndFileActiveVerInfo(contentId);
         String mediaType = cInfo.getMediaTypeByContentId(contentId);
         if (content == null) {
            if (mediaType.equalsIgnoreCase("TLFD")) {
               content = cInfo.getTLFDInfo(contentId);
            } else {
               ContentFile contentFile = cInfo.getMainFileInfo(contentId);
               if (contentFile == null) {
                  int allContentCount = cInfo.getAllContentCount();
                  ListManager listMgr = new ListManager(cInfo, "commonlist");
                  listMgr.addSearchInfo("selId", contentId);
                  listMgr.addSearchInfo("isSelectContent", "TRUE");
                  listMgr.addSearchInfo("listType", "ALL");
                  listMgr.setSection("getContentList");
                  List contentList = listMgr.V2dbexecute(1, allContentCount);
                  if (contentList != null && !contentList.isEmpty()) {
                     content = (Content)contentList.get(0);
                  }
               }
            }
         }

         if (content == null) {
            this.logger.error("[CONTENT NOT FOUND ERROR] Can not find content matched with contentId[" + contentId + "]. Need to check whether data row for FILE_ID is existing correctly or not.");
            return null;
         } else {
            V2ContentResource resource = this.getContentDetail(content);
            return resource;
         }
      } catch (Exception var16) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{contentId});
      }
   }

   private V2ContentResource getContentDetail(Content content) throws SQLException, ConfigException, UnsupportedEncodingException {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      String orgName = SecurityUtils.getUserContainer().getUser().getOrganization();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      long orgId = userGroupInfo.getOrgGroupIdByName(orgName);
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);

      try {
         if (content == null) {
            this.logger.error("[CONTENT NOT FOUND ERROR] Can not find content matched with contentId[]. (content is null)Need to check whether data row for FILE_ID is existing correctly or not.");
            return null;
         } else {
            boolean bRegLicLfd = false;
            boolean bool_reg_lic_soc = false;
            boolean bool_reg_lic_android = false;
            boolean bool_reg_lic_sinage = false;
            boolean bool_reg_lic_lite = false;
            boolean SlmLicenseCheck = false;
            boolean hasReadPermission = false;
            boolean hasWritePermission = false;
            if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
               bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
            }

            if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
               bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
            }

            if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
               bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
            }

            if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
               bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
            }

            if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
               bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
            }

            if (bool_reg_lic_soc || bRegLicLfd || bool_reg_lic_android || bool_reg_lic_sinage || bool_reg_lic_lite) {
               bRegLicLfd = true;
               SlmLicenseCheck = true;
            }

            AbilityUtils ability = new AbilityUtils();
            hasWritePermission = ability.checkAuthority("Content Write") && SlmLicenseCheck;
            if (SecurityUtils.getLoginUser().getRoot_group_id() != 0L && SecurityUtils.getLoginUser().getRoot_group_id() != content.getOrganization_id()) {
               hasWritePermission = false;
            }

            if (!SecurityUtils.checkReadPermissionWithOrgAndId("Content", content.getContent_id())) {
               hasReadPermission = false;
            } else {
               hasReadPermission = true;
            }

            V2ContentResource resource = new V2ContentResource();
            if (content != null) {
               resource.setContentId(content.getContent_id());
               resource.setVersionId(content.getVersion_id());
               resource.setContentName(content.getContent_name());
               resource.setMediaType(content.getMedia_type());
               resource.setCreatorId(content.getCreator_id());
               resource.setCreateDate(content.getCreate_date());
               resource.setLastModifiedDate(content.getLast_modified_date());
               resource.setTotalSize(content.getTotal_size());
               resource.setResolution(content.getResolution());
               resource.setIsDeleted(content.getIs_deleted());
               resource.setIsActive(content.getIs_active());
               resource.setIsHidden(content.getShare_flag() == 0);
               resource.setIsSupportAPlayer(this.isAplayerSupportMediaType(content));
               resource.setIsSupportWPlayer(this.isWplayerSupportMediaType(content));
               resource.setContentMetaData(content.getContent_meta_data());
               resource.setGroupId(content.getGroup_id());
               resource.setGroupName(content.getGroup_name());
               resource.setMainFileId(content.getMain_file_id());
               resource.setThumbFileId(content.getThumb_file_id());
               resource.setMainFileName(content.getMain_file_name());
               resource.setThumbFilePath("/servlet/ContentThumbnail?thumb_id=" + content.getThumb_file_id() + "&thumb_filename=" + URLEncoder.encode(content.getThumb_file_name(), "UTF-8") + "_MEDIUM.PNG");
               resource.setThumbFileName(URLEncoder.encode(content.getThumb_file_name(), "UTF-8") + "_MEDIUM.PNG");
               resource.setIsLinearVwl(content.getIs_linear_vwl());
               resource.setModelCountInfo(content.getModel_count_info());
               resource.setScreenCount(content.getScreen_count());
               resource.setXCount(content.getX_count());
               resource.setYCount(content.getY_count());
               resource.setXRange(content.getX_range());
               resource.setYRange(content.getY_range());
               resource.setOrganizationId(content.getOrganization_id());
               resource.setOrganizationName(content.getOrganization_name());
               resource.setOrgCreatorId(content.getOrg_creator_id());
               resource.setMainFileExtension(content.getMain_file_Extension());
               resource.setContentOrder(content.getContent_order());
               resource.setContentDuration(content.getContent_duration());
               resource.setVwlVersion(content.getVwl_version());
               resource.setIsMultiVwl(content.getMulti_vwl());
               resource.setApprovalStatus(content.getApproval_status());
               resource.setApprovalOpinion(content.getApproval_opinion());
               resource.setExpirationDate(content.getExpiration_date());
               resource.setIsExpirationDateActivated(!StrUtils.nvl(content.getExpiration_date()).equals("29991231"));
               resource.setStartPage(content.getHtml_start_page());
               resource.setHasReadPermission(hasReadPermission);
               resource.setHasWritePermission(hasWritePermission);
               resource.setUrlAddress(content.getUrl_address());
               resource.setIsInShareFolder(content.getIs_in_share_folder());
               if (content.getPlay_time() != null) {
                  String[] parsedArray = content.getPlay_time().split(":");
                  if (parsedArray.length > 1) {
                     resource.setPlayTimeInSeconds(Long.valueOf(parsedArray[0]) * 3600L + Long.valueOf(parsedArray[1]) * 60L + Long.valueOf(parsedArray[2]));
                     resource.setPlayTimeInString(content.getPlay_time());
                  }
               }

               String misUrl = CommonConfig.get("webauthor.web_url_public");
               String loadUrl = misUrl + "/servlet/GetFileLoader?paramPathConfName=CONTENTS_HOME&filepath=" + content.getMain_file_id() + "/" + content.getMain_file_name();
               resource.setMainFileUrl(loadUrl);
               if (content.getIs_streaming() != null) {
                  resource.setIsStreaming(content.getIs_streaming().equalsIgnoreCase("Y"));
               }

               String deviceType;
               float deviceTypeVersion;
               if (content.getDevice_type() != null && !content.getDevice_type().equals("")) {
                  deviceType = content.getDevice_type();
                  deviceTypeVersion = content.getDevice_type_version();
               } else {
                  Map typeMap = ContentUtils.getContentDeviceTypeAndVersion(content);
                  deviceType = (String)typeMap.get("deviceType");
                  deviceTypeVersion = Float.parseFloat(typeMap.get("deviceTypeVersion").toString());
               }

               resource.setDeviceType(deviceType);
               resource.setDeviceTypeVersion(deviceTypeVersion);
               if (content.getIs_used_template() != null) {
                  resource.setIsUsedTemplate(content.getIs_used_template().equalsIgnoreCase("Y"));
               }

               if (content.getTemplate_page_count() != null) {
                  resource.setTemplatePageCount(content.getTemplate_page_count());
               }

               if (infoMap != null && infoMap.get("CONTENTS_APPROVAL_ENABLE") != null) {
                  boolean contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
                  resource.setIsApprovalEnabled(contentsApprovalEnable);
               }

               Map cifsSettingInfo;
               List cifsSettingList;
               if (content.getMedia_type() != null && content.getMedia_type().equalsIgnoreCase("STRM")) {
                  cifsSettingList = cInfo.getUrlContentSettingByContentId(content.getContent_id());
                  if (cifsSettingList != null && cifsSettingList.size() > 0) {
                     cifsSettingInfo = (Map)cifsSettingList.get(0);
                     resource.setStreamingUrl(cifsSettingInfo.get("url").toString());
                  }
               }

               String playTimeOfLFT;
               String lftContentId;
               if ("FTP".equals(content.getMedia_type())) {
                  cifsSettingList = cInfo.getFtpContentSettingByContentId(content.getContent_id());
                  if (cifsSettingList != null && cifsSettingList.size() > 0) {
                     cifsSettingInfo = (Map)cifsSettingList.get(0);
                     playTimeOfLFT = StrUtils.nvl(cifsSettingInfo.get("can_refresh").toString());
                     lftContentId = StrUtils.nvl(cifsSettingInfo.get("can_login_retry").toString());
                     resource.setCanRefresh(!playTimeOfLFT.isEmpty() && playTimeOfLFT.equalsIgnoreCase("Y"));
                     resource.setRefreshInterval(StrUtils.nvl(cifsSettingInfo.get("refresh_interval").toString()));
                     resource.setCanLoginRetry(!lftContentId.isEmpty() && lftContentId.equalsIgnoreCase("Y"));
                     resource.setLoginRetryCount(Long.valueOf(cifsSettingInfo.get("login_retry_count").toString()));
                  }
               } else if ("CIFS".equals(content.getMedia_type())) {
                  cifsSettingList = cInfo.getCifsContentSettingByContentId(content.getContent_id());
                  if (cifsSettingList != null && cifsSettingList.size() > 0) {
                     cifsSettingInfo = (Map)cifsSettingList.get(0);
                     playTimeOfLFT = StrUtils.nvl(cifsSettingInfo.get("can_refresh").toString());
                     lftContentId = StrUtils.nvl(cifsSettingInfo.get("can_login_retry").toString());
                     resource.setCanRefresh(!playTimeOfLFT.isEmpty() && playTimeOfLFT.equalsIgnoreCase("Y"));
                     resource.setRefreshInterval(StrUtils.nvl(cifsSettingInfo.get("refresh_interval").toString()));
                     resource.setCanLoginRetry(!lftContentId.isEmpty() && lftContentId.equalsIgnoreCase("Y"));
                     resource.setLoginRetryCount(Long.valueOf(cifsSettingInfo.get("login_retry_count").toString()));
                  }
               } else if ("URL".equals(content.getMedia_type()) || "HTML".equals(content.getMedia_type()) || "SAPP".equals(content.getMedia_type())) {
                  String formattedRefreshInterval = content.getRefresh_interval();
                  resource.setRefreshInterval(formattedRefreshInterval);
               }

               if (content.getMedia_type() != null && (content.getMedia_type().equalsIgnoreCase("CIFS") || content.getMedia_type().equalsIgnoreCase("FTP"))) {
                  cifsSettingList = cInfo.getActiveFileList(content.getContent_id());
                  resource.setFtpCifsFiles(V2ContentFileResource.convert(cifsSettingList));
                  List pollingLogs = cInfo.getContentPollingHistories(content.getContent_id());
                  if (pollingLogs != null) {
                     List histories = new ArrayList();
                     Iterator var49 = pollingLogs.iterator();

                     while(var49.hasNext()) {
                        ContentPollingLog log = (ContentPollingLog)var49.next();
                        histories.add(log.createVO(SecurityUtils.getLocale()));
                     }

                     resource.setContentPollingHistories(histories);
                  }
               }

               if ("DLK".equals(content.getMedia_type())) {
                  ContentInfo contentInfo = ContentInfoImpl.getInstance();
                  String playTimeOfDLK = content.getPlay_time();
                  if (playTimeOfDLK == null || playTimeOfDLK != null && playTimeOfDLK.isEmpty()) {
                     try {
                        playTimeOfLFT = contentInfo.getPlayTimeOfLftByDlk(content.getContent_id());
                        if (playTimeOfLFT != null && !playTimeOfLFT.isEmpty()) {
                           String[] parsedArray = playTimeOfLFT.split(":");
                           if (parsedArray.length > 1) {
                              resource.setPlayTimeInSeconds(Long.valueOf(parsedArray[0]) * 3600L + Long.valueOf(parsedArray[1]) * 60L + Long.valueOf(parsedArray[2]));
                              resource.setPlayTimeInString(playTimeOfLFT);
                           }
                        }
                     } catch (Exception var30) {
                        this.logger.error(var30);
                     }
                  }

                  playTimeOfLFT = content.getResolution();
                  if (playTimeOfLFT == null || playTimeOfLFT != null && playTimeOfLFT.isEmpty()) {
                     try {
                        lftContentId = contentInfo.getLfdContentIdByDlkContentId(content.getContent_id());
                        if (lftContentId != null && !lftContentId.isEmpty()) {
                           Content lftContent = contentInfo.getContentActiveVerInfo(lftContentId);
                           if (lftContent != null) {
                              String resolution = lftContent.getResolution();
                              resource.setResolution(resolution);
                           }
                        }
                     } catch (Exception var29) {
                        this.logger.error(var29);
                     }
                  }
               }
            }

            TagInfo tagInfo = TagInfoImpl.getInstance();
            List tagEntities = tagInfo.getContentTagList(content.getContent_id());
            if (tagEntities != null && tagEntities.size() > 0) {
               List tagResources = new ArrayList();
               Iterator var37 = tagEntities.iterator();

               while(var37.hasNext()) {
                  TagEntity tagEntity = (TagEntity)var37.next();
                  int cnt = tagInfo.getCntTagCondition((long)tagEntity.getTag_id());
                  if (cnt > 0) {
                     List tagConditions = tagInfo.getContentTagConditionList(content.getContent_id(), (long)tagEntity.getTag_id());
                     tagResources.add(V2TagResource.convert(tagEntity, tagConditions));
                  } else {
                     tagResources.add(V2TagResource.convert(tagEntity, (List)null));
                  }
               }

               resource.setTags(tagResources);
            }

            CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
            List categoryList = categoryInfo.getCategoryWithContentId(content.getContent_id());
            if (categoryList != null && categoryList.size() > 0) {
               resource.setCategories(categoryList);
            }

            return resource;
         }
      } catch (Exception var31) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{content.getContent_id()});
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public V2ContentResource convertTemplate(String contentId, String groupId, String newTLFDName) throws Exception {
      RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.TLFD, Long.parseLong(groupId));
      V2ContentConvertibility contentConvertibility = this.checkConvertibility(contentId);
      if (!contentConvertibility.isConvertible()) {
         throw new BaseRestException(contentConvertibility.getReasonCode(), contentConvertibility.getReason());
      } else {
         String newTemplateId = this.convertToTLFD(contentId, newTLFDName, groupId);

         try {
            List notifications = new ArrayList();
            notifications.add(this.makeNotificationData(contentId));
            if (notifications.size() > 0) {
               MailUtil.sendContentEventMail(notifications, "Add content");
            }
         } catch (Exception var7) {
            this.logger.error(var7);
         }

         V2ContentResource resource = this.getContentDetail(newTemplateId);
         if (resource == null) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{contentId});
         } else {
            return resource;
         }
      }
   }

   public String convertToTLFD(String contentID, String newTLFDName, String groupId) throws ConfigException, Exception {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      UserInfo uInfo = UserInfoImpl.getInstance();
      Content oldLFDContent = cInfo.getContentActiveVerInfo(contentID);
      Content newTLFDContent = new Content();
      long orgId = uInfo.getRootGroupIdByUserId(this.getLoginUserId());
      String orgName = SecurityUtils.getUserContainer().getUser().getOrganization();
      String newTLFDContentId = UUID.randomUUID().toString().toUpperCase();
      if (newTLFDName == null || newTLFDName.equals("")) {
         newTLFDName = oldLFDContent.getContent_name() + " Template";
      }

      List tlfdGroups = cInfo.getTLFDGroupList(SecurityUtils.getLoginUserId());
      boolean isValidGroupId = false;
      if (tlfdGroups != null) {
         Iterator var15 = tlfdGroups.iterator();

         while(var15.hasNext()) {
            Group tlfdGroup = (Group)var15.next();
            if (tlfdGroup.getGroup_id() == Long.parseLong(groupId)) {
               isValidGroupId = true;
               break;
            }
         }
      }

      if (!isValidGroupId) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_INVALID_TLFD_GROUP_ID, new String[]{groupId});
      } else {
         newTLFDContent.setContent_id(newTLFDContentId);
         newTLFDContent.setVersion_id(1L);
         newTLFDContent.setContent_name(newTLFDName);
         newTLFDContent.setApproval_status("APPROVED");
         newTLFDContent.setGroup_id(Long.valueOf(groupId));
         newTLFDContent.setShare_flag(1);
         newTLFDContent.setContent_meta_data("");
         newTLFDContent.setCreator_id(this.getLoginUserId());
         newTLFDContent.setMedia_type("TLFD");
         newTLFDContent.setOrganization_id(orgId);
         newTLFDContent.setTotal_size(oldLFDContent.getTotal_size());
         newTLFDContent.setIs_active("Y");
         newTLFDContent.setOrg_creator_id(oldLFDContent.getCreator_id());
         newTLFDContent.setDevice_type(oldLFDContent.getDevice_type());
         newTLFDContent.setDevice_type_version(oldLFDContent.getDevice_type_version());
         newTLFDContent.setResolution(oldLFDContent.getResolution());
         ContentFile cmsThumbFile = cInfo.getThumbFileInfo(contentID);
         String thumbnailFileId = oldLFDContent.getThumb_file_id();
         String thumbnailFileName = oldLFDContent.getThumb_file_name();
         newTLFDContent.setThumb_file_id(thumbnailFileId);
         newTLFDContent.setThumb_file_name(thumbnailFileName);
         newTLFDContent.setMain_file_id(oldLFDContent.getMain_file_id());
         if (cmsThumbFile != null) {
            cmsThumbFile.setCreator_id(this.getLoginUserId());
         }

         List fileListToSave = cInfo.getFileList(contentID);
         if (cmsThumbFile != null) {
            fileListToSave.add(cmsThumbFile);
         }

         newTLFDContent.setArr_file_list(fileListToSave);
         cInfo.addContent(newTLFDContent);
         cInfo.setVersionId(newTLFDContentId, 0L, 1L);
         cInfo.setActiveVersion(newTLFDContentId, 1L, false);
         return newTLFDContentId;
      }
   }

   public String getLoginUserId() {
      return SecurityUtils.getUserContainer() == null ? "" : SecurityUtils.getUserContainer().getUser().getUser_id();
   }

   private List getReferredItemsByContentId(String contentId) throws SQLException {
      List deleteFails = new ArrayList();
      List referredPlaylistIds = this.setRefPlaylistList(contentId, "PREMIUM");
      if (!referredPlaylistIds.isEmpty()) {
         V2ContentDeleteFail deleteFail = new V2ContentDeleteFail();
         deleteFail.setId(contentId);
         deleteFail.setReason("Referred from playlist. [" + ConvertUtil.convertListToStringWithSeparator(referredPlaylistIds, ",") + "]");
         deleteFails.add(deleteFail);
      }

      List referredScheduleIds = this.setRefScheduleList(contentId, "PREMIUM");
      if (!referredScheduleIds.isEmpty()) {
         V2ContentDeleteFail deleteFail = new V2ContentDeleteFail();
         deleteFail.setId(contentId);
         deleteFail.setReason("Referred from schedule. [" + ConvertUtil.convertListToStringWithSeparator(referredScheduleIds, ",") + "]");
         deleteFails.add(deleteFail);
      }

      List referredEventScheduleIds = this.setRefEventList(contentId);
      if (!referredEventScheduleIds.isEmpty()) {
         V2ContentDeleteFail deleteFail = new V2ContentDeleteFail();
         deleteFail.setId(contentId);
         deleteFail.setReason("Referred from event-schedule. [" + ConvertUtil.convertListToStringWithSeparator(referredEventScheduleIds, ",") + "]");
         deleteFails.add(deleteFail);
      }

      return deleteFails;
   }

   public List setRefPlaylistList(String contentId, String productType) throws SQLException {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      List pList = null;
      List referredPlaylistIds = new ArrayList();
      if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         pList = cInfo.getPlaylistListUsingContent(contentId);
      }

      if (pList != null && pList.size() != 0) {
         for(int j = 0; j < pList.size(); ++j) {
            Map map = (Map)pList.get(j);
            String playlistId = (String)map.get("playlist_id");
            referredPlaylistIds.add(playlistId);
         }
      }

      return referredPlaylistIds;
   }

   public List setRefScheduleList(String contentId, String productType) throws SQLException {
      ScheduleInterface sInfo = DAOFactory.getScheduleInfoImpl(productType);
      List referredScheduleIds = new ArrayList();
      List programList = sInfo.getProgramByContentId(contentId);
      if (programList != null && programList.size() != 0) {
         for(int j = 0; j < programList.size(); ++j) {
            Map map = (Map)programList.get(j);
            String scheduleId = (String)map.get("program_id");
            referredScheduleIds.add(scheduleId);
         }
      }

      return referredScheduleIds;
   }

   public List setRefEventList(String contentId) throws SQLException {
      EventInfoDao eventInfoDao = new EventInfoDao();
      List referredEventScheduleIds = new ArrayList();
      List eventList = eventInfoDao.getEventIdListByContentId(contentId);
      if (eventList != null && eventList.size() != 0) {
         for(int j = 0; j < eventList.size(); ++j) {
            Map map = (Map)eventList.get(j);
            String eventId = (String)map.get("EVENT_ID");
            referredEventScheduleIds.add(eventId);
         }
      }

      return referredEventScheduleIds;
   }

   @PreAuthorize("hasAnyAuthority('Content Manage Authority')")
   public V2CommonResultResource deleteAll() throws Exception {
      LogInfo logInfo = LogInfoImpl.getInstance();
      new ContentLog();
      List notifications = new ArrayList();
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      V2CommonResultResource deleteResult = new V2CommonResultResource();
      List contentList = cInfo.getAllDeletedContentListWithoutCreatorId(this.getLoginUserId());
      if (contentList != null) {
         List deleteSuccessIds = new ArrayList();

         for(int i = 0; i < contentList.size(); ++i) {
            Map map = (Map)contentList.get(i);
            String contentId = (String)map.get("CONTENT_ID");
            deleteSuccessIds.add(contentId);

            try {
               notifications.add(this.makeNotificationData(contentId));
            } catch (Exception var21) {
               this.logger.error(var21);
            }

            boolean isFtpCifsContent = false;
            String CifsFileLoginId;
            String meta_file;
            String meta_folder;
            String creatorId;
            String CONTENTS_HOME;
            String cifsFileFolder;
            File cifsFilesFolder;
            String schedulerJobName;
            String schedulerJobGroup;
            if (!cInfo.getFtpUserIdByContentId(contentId).equals("")) {
               isFtpCifsContent = true;
               CifsFileLoginId = cInfo.getFtpUserIdByContentId(contentId);
               meta_file = cInfo.getFtpIpByContentId(contentId);
               meta_folder = cInfo.getFtpPathByContentId(contentId);
               creatorId = cInfo.getCreatorIdByContentId(contentId);
               CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
               cifsFileFolder = CONTENTS_HOME + File.separator + "FTP_" + meta_file.replace('.', '_') + '_' + CifsFileLoginId.replace('/', '_').replace('\\', '_').replace('.', '_').replace(' ', '_') + '_' + meta_folder.replace('/', '_').replace('\\', '_').replace('.', '_').replace(' ', '_') + creatorId + File.separator;
               this.logger.info("delete - ftpFileFolder : " + cifsFileFolder);
               cifsFilesFolder = SecurityUtils.getSafeFile(cifsFileFolder);
               if (this.deleteDirectoryRecursive(cifsFilesFolder)) {
                  this.logger.info("FtpFilesFolder and files are cleaned up successfully!");
               }

               schedulerJobName = "FTP_" + contentId;
               schedulerJobGroup = "UpdateFtpContentService";
               CommonUtils.deleteJob(schedulerJobName, schedulerJobGroup);
            }

            if (!cInfo.getCifsUserIdByContentId(contentId).equals("")) {
               isFtpCifsContent = true;
               CifsFileLoginId = cInfo.getCifsUserIdByContentId(contentId);
               meta_file = cInfo.getCifsIpByContentId(contentId);
               meta_folder = cInfo.getCifsPathByContentId(contentId);
               creatorId = cInfo.getCreatorIdByContentId(contentId);
               CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
               cifsFileFolder = ContentUtils.getCifsLocalPath(CONTENTS_HOME, meta_file, CifsFileLoginId, meta_folder, creatorId);
               this.logger.info("delete - cifsFileFolder : " + cifsFileFolder);
               cifsFilesFolder = SecurityUtils.getSafeFile(cifsFileFolder);
               if (this.deleteDirectoryRecursive(cifsFilesFolder)) {
                  this.logger.info("CifsFilesFolder and files are cleaned up successfully!");
               }

               schedulerJobName = "CIFS_" + contentId;
               schedulerJobGroup = "UpdateCifsContentService";
               CommonUtils.deleteJob(schedulerJobName, schedulerJobGroup);
            }

            List fileList = cInfo.getFileList(contentId);
            ContentUtils.deleteThumbFiles(contentId);

            for(int j = 0; j < fileList.size(); ++j) {
               ContentFile file = (ContentFile)fileList.get(j);
               if (cInfo.isDeletableFile(file.getFile_id(), contentId)) {
                  cInfo.deleteFile(file.getFile_id());
               }
            }

            if (isFtpCifsContent) {
               ContentFile mainMetaFile = cInfo.getMainFileInfo(contentId);
               if (mainMetaFile != null) {
                  if (cInfo.isDeletableFile(mainMetaFile.getFile_id(), contentId)) {
                     cInfo.deleteFile(mainMetaFile.getFile_id());
                  }
               } else {
                  this.logger.error("[MagicInfo_FTP][MagicInfo_CFIS] main meta file is null, content id : " + contentId);
               }

               cInfo.deletePollingFileInfo(contentId);
               cInfo.deletePollingInfo(contentId);
            }

            meta_file = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separator + "contents_home" + File.separator + "contents_meta" + File.separator + contentId + File.separator + "ContentsMetadata.CSD";
            meta_folder = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separator + "contents_home" + File.separator + "contents_meta" + File.separator + contentId + File.separator;
            File metaFile = SecurityUtils.getSafeFile(meta_file);
            File metaFolder = SecurityUtils.getSafeFile(meta_folder);
            metaFile.delete();
            metaFolder.delete();
            if (cInfo.deleteContentCompletely(contentId) <= 0) {
            }
         }

         if (notifications.size() <= 0) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_RECYCLE_BIN_EMPTY);
         }

         MailUtil.sendContentEventMail(notifications, "Delete content permanently");
         deleteResult.setSuccessList(deleteSuccessIds);
      }

      return deleteResult;
   }

   public boolean deleteDirectoryRecursive(File filePath) throws SQLException {
      if (filePath == null) {
         return false;
      } else {
         if (filePath.exists()) {
            File[] files = filePath.listFiles();
            if (files == null) {
               return false;
            }

            for(int i = 0; i < files.length; ++i) {
               if (files[i] == null) {
                  return false;
               }

               if (files[i].isDirectory()) {
                  this.deleteDirectoryRecursive(files[i]);
               } else {
                  files[i].delete();
               }
            }
         }

         return filePath.delete();
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Manage Authority')")
   public V2ContentResource approveContent(String contentId, V2ContentApproval approval) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      V2ContentResource resource = this.processContentApproval(approval.getContentId(), approval.getStatus(), approval.getOpinion());
      if (resource == null) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{contentId});
      } else {
         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Manage Authority')")
   public List approveContents(V2ContentApprovals approvals) throws SQLException, Exception {
      int size = approvals.getApprovals().size();
      boolean flag = false;

      for(int i = 0; i < size; ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, ((V2ContentApproval)approvals.getApprovals().get(i)).getContentId());
         } catch (Exception var8) {
            flag = false;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
      }

      List resources = new ArrayList();

      for(int i = 0; i < size; ++i) {
         V2ContentApproval approval = (V2ContentApproval)approvals.getApprovals().get(i);
         V2ContentResource resource = this.processContentApproval(approval.getContentId(), approval.getStatus(), approval.getOpinion());
         if (resource != null) {
            resources.add(resource);
         }
      }

      return resources;
   }

   private V2ContentResource processContentApproval(String contentId, String status, String opinion) throws SQLException {
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      ServerSetupInfo serverSetupInfo = ServerSetupInfoImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      RoleInfo roleInfo = RoleInfoImpl.getInstance();
      TagInfo tagInfo = TagInfoImpl.getInstance();
      AbilityUtils abilityUtils = new AbilityUtils();
      long orgId = userInfo.getRootGroupIdByUserId(this.getLoginUserId());
      Map serverSetupInfoMap = serverSetupInfo.getServerInfoByOrgId(orgId);
      String approvalType = "";
      if (serverSetupInfoMap.get("CONTENTS_APPROVAL_TYPE") != null) {
         approvalType = serverSetupInfoMap.get("CONTENTS_APPROVAL_TYPE").toString();
      }

      List playlistTriggerTagIds = new ArrayList();
      List notifications = new ArrayList();
      String userRoleName = roleInfo.getRoleByUserId(this.getLoginUserId()).getRole_name();
      if (status.equalsIgnoreCase("REJECTED")) {
         contentInfo.deleteContentApproverMapByContentId(contentId);
         contentInfo.setApprovalStatus(contentId, "REJECTED", opinion);
      } else {
         List tagList;
         Iterator var19;
         TagEntity tag;
         if (!userRoleName.equals("Server Administrator") && (orgId != 0L || !abilityUtils.checkAuthority("Content Manage")) && !approvalType.equalsIgnoreCase("part")) {
            contentInfo.deleteContentApproverMap(contentId, this.getLoginUserId());
            List approvers = contentInfo.getContentApproverListByContentId(contentId);
            if (approvers == null || approvers != null && approvers.size() == 0) {
               contentInfo.setApprovalStatus(contentId, "APPROVED", opinion);
               NotificationData n = this.writeContentApprovalLog(contentId);
               if (n != null) {
                  notifications.add(n);
               }
            }

            tagList = tagInfo.getContentTagList(contentId);
            if (tagList != null && tagList.size() > 0) {
               var19 = tagList.iterator();

               while(var19.hasNext()) {
                  tag = (TagEntity)var19.next();
                  if (!playlistTriggerTagIds.contains((long)tag.getTag_id())) {
                     playlistTriggerTagIds.add((long)tag.getTag_id());
                  }
               }
            }
         } else {
            contentInfo.deleteContentApproverMapByContentId(contentId);
            contentInfo.setApprovalStatus(contentId, "APPROVED", opinion);
            NotificationData n = this.writeContentApprovalLog(contentId);
            if (n != null) {
               notifications.add(n);
            }

            tagList = tagInfo.getContentTagList(contentId);
            if (tagList != null && tagList.size() > 0) {
               var19 = tagList.iterator();

               while(var19.hasNext()) {
                  tag = (TagEntity)var19.next();
                  if (!playlistTriggerTagIds.contains((long)tag.getTag_id())) {
                     playlistTriggerTagIds.add((long)tag.getTag_id());
                  }
               }
            }
         }

         if (playlistTriggerTagIds.size() > 0) {
            tagInfo.setPlaylistTrigger(playlistTriggerTagIds);
         }

         if (notifications.size() > 0) {
            MailUtil.sendContentEventMail(notifications, "Approve content");
         }
      }

      return this.getContentDetail(contentId);
   }

   private NotificationData writeContentApprovalLog(String contentId) {
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      Locale locale = SecurityUtils.getLocale();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      LogInfoImpl var6 = LogInfoImpl.getInstance();

      try {
         Content content = contentInfo.getContentActiveVerInfo(contentId);
         NotificationData n = new NotificationData();
         n.setName(content.getContent_name());
         n.setOrgId(content.getOrganization_id());
         n.setOrgName(userGroupInfo.getGroupNameByGroupId(content.getOrganization_id()));
         n.setUserName(this.getLoginUserId());
         return n;
      } catch (Exception var9) {
         this.logger.error(var9);
         return null;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Upload Authority', 'Content Manage Authority')")
   public V2CifsContentSettingResource createCifsContent(V2CifsContentSettingResource resource) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, SecurityUtils.getLoginUserId());
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      Long rootGroupId = cInfo.getRootId(this.getLoginUserId());
      String user_id = resource.getUserId();
      String group_id = resource.getGroupId();
      String cifsContentName = resource.getCifsContentName();
      String cifsIP = resource.getCifsIP();
      String cifsLoginId = resource.getCifsLoginId();
      String cifsPassword = resource.getCifsPassword();
      String cifsDirectory = resource.getCifsDirectory();
      if (cifsDirectory.charAt(0) != '/') {
         cifsDirectory = "/" + cifsDirectory;
      }

      if (cifsDirectory.charAt(cifsDirectory.length() - 1) != '/') {
         cifsDirectory = cifsDirectory + "/";
      }

      String cifsRefreshInterval = resource.getCifsRefreshInterval();
      String canRefresh = resource.getCanRefresh();
      String loginRetryMaxCount = resource.getLoginRetryMaxCount();
      String canLoginRetry = resource.getCanLoginRetry();
      String miUserId = "";
      String contentId = "";
      int maxSizeOfDirectory = 50;
      boolean isValid = true;
      boolean checkLogon = false;
      miUserId = user_id;
      contentId = UUID.randomUUID().toString();
      if (user_id == null || user_id != null && user_id.isEmpty()) {
         miUserId = this.getLoginUserId();
      }

      if (group_id.equals("0")) {
         group_id = rootGroupId.toString();
      }

      RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(group_id));
      String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
      if (cifsDirectory.length() > maxSizeOfDirectory) {
         cifsDirectory = cifsDirectory.substring(cifsDirectory.length() - maxSizeOfDirectory, cifsDirectory.length());
      }

      String localPathByIp = ContentUtils.getCifsLocalPath(CONTENTS_HOME, cifsIP, cifsLoginId, cifsDirectory, miUserId);
      boolean isFolderEmpty = true;
      if (!StringUtils.isEmpty(cifsContentName) && !StringUtils.isEmpty(cifsIP) && !StringUtils.isEmpty(cifsLoginId) && !StringUtils.isEmpty(cifsPassword) && !StringUtils.isEmpty(cifsDirectory) && !StringUtils.isEmpty(cifsRefreshInterval) && !StringUtils.isEmpty(canRefresh) && !StringUtils.isEmpty(loginRetryMaxCount) && !StringUtils.isEmpty(canLoginRetry)) {
         if (resource.getType().equalsIgnoreCase("CIFS") && SecurityUtils.getSafeFile(localPathByIp).isDirectory()) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SAME_CONTENT_EXIST);
         } else {
            if (isValid) {
               SMBClient client = new SMBClient();
               Connection connection = null;
               Session smbSession = null;
               DiskShare share = null;

               try {
                  cifsDirectory = CifsFilesToDownload.changePath(cifsDirectory);
                  String[] splitDomainAndUserId = CifsFilesToDownload.splitDomainAndUserId(cifsLoginId);
                  String domain = null;
                  String userId = null;
                  if (splitDomainAndUserId != null && splitDomainAndUserId.length > 0) {
                     if (splitDomainAndUserId.length > 1) {
                        domain = splitDomainAndUserId[0];
                        userId = splitDomainAndUserId[1];
                     } else {
                        userId = cifsLoginId;
                     }
                  } else {
                     userId = cifsLoginId;
                  }

                  try {
                     connection = client.connect(cifsIP);
                  } catch (IOException var49) {
                     isValid = false;
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_CONNECT_REMOTE_SERVER);
                  }

                  try {
                     AuthenticationContext ac = new AuthenticationContext(userId, cifsPassword.toCharArray(), domain);
                     smbSession = connection.authenticate(ac);
                  } catch (Exception var48) {
                     isValid = false;
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REMOTE_SERVER_LOGIN_FAIL);
                  }

                  if (isValid) {
                     String[] dirInfo = cifsDirectory.split("/");

                     try {
                        try {
                           share = (DiskShare)smbSession.connectShare(dirInfo[0]);
                        } catch (Exception var47) {
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_INVALID_PATH);
                        }

                        String checkingFolder = "";
                        if (dirInfo.length > 1) {
                           for(int i = 1; i < dirInfo.length; ++i) {
                              checkingFolder = checkingFolder + dirInfo[i];
                              if (i < dirInfo.length - 1) {
                                 checkingFolder = checkingFolder + "\\";
                              }
                           }

                           if (!share.folderExists(checkingFolder)) {
                              throw new RestServiceException(RestExceptionCode.BAD_REQUEST_INVALID_PATH);
                           }
                        }

                        if (!share.isConnected()) {
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_CONNECT_REMOTE_SERVER);
                        }

                        checkLogon = true;
                        Iterator var65 = share.list(checkingFolder).iterator();

                        while(var65.hasNext()) {
                           FileIdBothDirectoryInformation f = (FileIdBothDirectoryInformation)var65.next();
                           if (!f.getFileName().equals(".") && !f.getFileName().equals("..")) {
                              isFolderEmpty = false;
                              break;
                           }
                        }
                     } catch (RestServiceException var50) {
                        throw var50;
                     } catch (Exception var51) {
                        isValid = false;
                        this.logger.error("", var51);
                        throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CIFS_CONTENT_UPDATE);
                     }
                  }
               } catch (RestServiceException var52) {
                  throw var52;
               } catch (Exception var53) {
                  checkLogon = false;
                  this.logger.error("", var53);
                  throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CIFS_CONTENT_UPDATE);
               } finally {
                  if (share != null) {
                     share.close();
                  }

                  if (smbSession != null) {
                     smbSession.close();
                  }

                  if (connection != null) {
                     connection.close();
                  }

               }
            }

            if (isValid) {
               try {
                  if (isFolderEmpty) {
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NO_AVAILABLE_FILES);
                  }

                  if (isValid && checkLogon) {
                     if (AMQUtil.isEnabledRCDS()) {
                        BrokerMonitor broker = BrokerMonitorImpl.getInstance();
                        if (!broker.isAlive()) {
                           this.logger.error("[MagicInfo_CIFS] Message Queue Server is NOT alive... " + cifsContentName + "[" + contentId + "]");
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_CONNECT_REMOTE_SERVER);
                        }
                     }

                     if (isValid) {
                        this.logger.info("[MagicInfo_CIFS][CList] Start CIFS " + localPathByIp);
                        long nGroupId = Long.parseLong(group_id);
                        long nCifsRefreshInterval = Long.parseLong(cifsRefreshInterval);
                        boolean scheduledJob = false;
                        long nLoginRetryMaxCount = Long.parseLong(loginRetryMaxCount);
                        Runnable runCifs = new CifsFileDownloadThread(miUserId, nGroupId, contentId, cifsContentName, cifsIP, cifsLoginId, cifsPassword, localPathByIp, cifsDirectory, nCifsRefreshInterval, scheduledJob, canRefresh, nLoginRetryMaxCount, canLoginRetry);
                        Thread threadCifs = new Thread(runCifs);
                        threadCifs.start();
                     }
                  }
               } catch (Exception var46) {
                  this.logger.error("", var46);
                  throw var46;
               }
            }

            if (isValid && checkLogon) {
               try {
                  List notifications = new ArrayList();
                  notifications.add(this.makeNotificationData(contentId));
                  if (notifications.size() > 0) {
                     MailUtil.sendContentEventMail(notifications, "Add content");
                  }
               } catch (Exception var45) {
                  this.logger.error(var45);
               }

               V2CifsContentSettingResource newResource = new V2CifsContentSettingResource();
               newResource.setContentId(contentId);
               newResource.setType("CIFS");
               newResource.setUserId(miUserId);
               newResource.setCifsContentName(cifsContentName);
               newResource.setCifsIP(cifsIP);
               newResource.setCifsLoginId(cifsLoginId);
               newResource.setCifsPassword(cifsPassword);
               newResource.setCifsDirectory(cifsDirectory);
               newResource.setCifsRefreshInterval(cifsRefreshInterval);
               newResource.setCanRefresh(canRefresh);
               newResource.setLoginRetryMaxCount(loginRetryMaxCount);
               newResource.setCanLoginRetry(canLoginRetry);
               return newResource;
            } else {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REMOTE_SERVER_LOGIN_FAIL);
            }
         }
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_INVALID_PARAMS);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2CifsContentSettingResource getCifsContentSetting(String contentId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      V2CifsContentSettingResource resource = new V2CifsContentSettingResource();
      List settings = contentInfo.getCifsContentSettingByContentId(contentId);
      if (settings.size() > 0) {
         Map setting = (Map)settings.get(0);
         resource.setContentId(contentId);
         resource.setType((String)setting.get("media_type"));
         resource.setUserId((String)setting.get("creator_id"));
         resource.setCifsContentName((String)setting.get("content_name"));
         resource.setCifsIP((String)setting.get("server_ip"));
         resource.setCifsLoginId((String)setting.get("cifs_user_id"));
         resource.setCifsPassword(new String(Base64.decode((String)setting.get("cifs_user_password"))));
         resource.setCifsDirectory((String)setting.get("remote_dir"));
         resource.setCifsRefreshInterval(String.valueOf(setting.get("refresh_interval")));
         resource.setCanRefresh((String)setting.get("can_refresh"));
         resource.setLoginRetryMaxCount(String.valueOf(setting.get("login_retry_max_count")));
         resource.setCanLoginRetry((String)setting.get("can_login_retry"));
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Upload Authority', 'Content Manage Authority')")
   public V2CifsContentSettingResource updateCifsContentSetting(String contentId, V2CifsContentSettingResource resource) throws Exception {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, SecurityUtils.getLoginUserId());
      if (!StrUtils.nvl(resource.getGroupId()).equals("")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, resource.getGroupId());
      }

      V2CifsContentSettingResource newResource = new V2CifsContentSettingResource();
      String resultMessage = "Edit operation successful.";
      String newContentName = resource.getCifsContentName();
      String newServerIp = resource.getCifsIP();
      String newLoginId = resource.getCifsLoginId();
      String newLoginPw = resource.getCifsPassword();
      String newDirectory = resource.getCifsDirectory();
      String tmpRefreshInterval = resource.getCifsRefreshInterval();
      long newRefreshInterval = Long.parseLong(StringUtils.isEmpty(tmpRefreshInterval) ? "1" : tmpRefreshInterval);
      String newCanRefresh = resource.getCanRefresh();
      String tmpLoginRetryMaxCount = resource.getLoginRetryMaxCount();
      long newLoginRetryMaxCount = Long.parseLong(StringUtils.isEmpty(tmpLoginRetryMaxCount) ? "1" : tmpLoginRetryMaxCount);
      long newLoginRetryCount = 0L;
      String newCanLoginRetry = resource.getCanLoginRetry();
      List oldSettings = cInfo.getCifsContentSettingByContentId(contentId);
      if (oldSettings != null && oldSettings.size() > 0) {
         Map setting = (Map)oldSettings.get(0);
         newContentName = newContentName.isEmpty() ? setting.get("content_name").toString() : newContentName;
         newServerIp = newServerIp.isEmpty() ? setting.get("server_ip").toString() : newServerIp;
         newLoginId = newLoginId.isEmpty() ? setting.get("cifs_user_id").toString() : newLoginId;
         newLoginPw = newLoginPw.isEmpty() ? new String(Base64.decode((String)setting.get("cifs_user_password"))) : newLoginPw;
         newDirectory = newDirectory.isEmpty() ? setting.get("remote_dir").toString() : newDirectory;
         tmpRefreshInterval = resource.getCifsRefreshInterval().isEmpty() ? setting.get("refresh_interval").toString() : tmpRefreshInterval;
         newRefreshInterval = resource.getCifsRefreshInterval().isEmpty() ? Long.parseLong(setting.get("refresh_interval").toString()) : newRefreshInterval;
         newCanRefresh = newCanRefresh.isEmpty() ? setting.get("can_refresh").toString() : newCanRefresh;
         tmpLoginRetryMaxCount = resource.getLoginRetryMaxCount().isEmpty() ? setting.get("login_retry_max_count").toString() : tmpLoginRetryMaxCount;
         newLoginRetryMaxCount = resource.getLoginRetryMaxCount().isEmpty() ? Long.valueOf(setting.get("login_retry_max_count").toString()) : newLoginRetryMaxCount;
         newLoginRetryCount = Long.valueOf(setting.get("login_retry_count").toString());
         newCanLoginRetry = newCanLoginRetry.isEmpty() ? setting.get("can_login_retry").toString() : newCanLoginRetry;
      }

      if (!StringUtils.isEmpty(newContentName) && !StringUtils.isEmpty(newServerIp) && !StringUtils.isEmpty(newLoginId) && !StringUtils.isEmpty(newLoginPw) && !StringUtils.isEmpty(newDirectory) && !StringUtils.isEmpty(tmpRefreshInterval) && !StringUtils.isEmpty(newCanRefresh) && !StringUtils.isEmpty(tmpLoginRetryMaxCount) && !StringUtils.isEmpty(newCanLoginRetry)) {
         ContentInfo contentInfo = ContentInfoImpl.getInstance();
         String miUserId = contentInfo.getContentOrgCreatorId(contentId);
         int maxSizeOfDirectory = 50;
         if (miUserId == null || miUserId != null && miUserId.isEmpty()) {
            miUserId = "admin";
         }

         if (newDirectory.charAt(0) != '/') {
            newDirectory = "/" + newDirectory;
         }

         if (newDirectory.charAt(newDirectory.length() - 1) != '/') {
            newDirectory = newDirectory + "/";
         }

         if (newDirectory.length() > maxSizeOfDirectory) {
            newDirectory = newDirectory.substring(newDirectory.length() - maxSizeOfDirectory, newDirectory.length());
         }

         List cifsSettingList = contentInfo.getCifsContentSettingByContentId(contentId);
         if (cifsSettingList.size() == 0) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"content"});
         } else {
            Map cifsSettingInfo = (Map)cifsSettingList.get(0);
            String isDeleted = (String)cifsSettingInfo.get("is_deleted");
            String isReady = contentInfo.getIsReadyForNextCifsThread(contentId);
            if ("Y".equalsIgnoreCase(isDeleted)) {
               throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"content"});
            } else if ("N".equalsIgnoreCase(isReady)) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CONTENT_EDIT_FAIL_WHILE_DOWNLOADING);
            } else {
               contentInfo.setIsReadyForNextCifsThread("N", contentId);
               SMBClient client = null;
               Connection connection = null;
               Session smbSession = null;
               DiskShare share = null;

               try {
                  String oldServerIp;
                  try {
                     String oldContentName = (String)cifsSettingInfo.get("content_name");
                     oldServerIp = (String)cifsSettingInfo.get("server_ip");
                     String oldLoginId = (String)cifsSettingInfo.get("cifs_user_id");
                     String oldLoginPw = new String(Base64.decode((String)cifsSettingInfo.get("cifs_user_password")));
                     String oldDirectory = (String)cifsSettingInfo.get("remote_dir");
                     long oldRefreshInterval = Long.parseLong(cifsSettingInfo.get("refresh_interval").toString());
                     String oldCanRefresh = (String)cifsSettingInfo.get("can_refresh");
                     long oldLoginRetryMaxCount = Long.parseLong(cifsSettingInfo.get("login_retry_max_count").toString());
                     String oldCanLoginRetry = (String)cifsSettingInfo.get("can_login_retry");
                     newDirectory = CifsFilesToDownload.changePath(newDirectory);
                     if (oldContentName.equals(newContentName) && oldServerIp.equals(newServerIp) && oldLoginId.equals(newLoginId) && oldLoginPw.equals(newLoginPw) && oldDirectory.equals(newDirectory) && oldRefreshInterval == newRefreshInterval && oldCanRefresh.equals(newCanRefresh) && oldCanLoginRetry.equals(newCanLoginRetry) && oldLoginRetryMaxCount == newLoginRetryMaxCount) {
                        throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NO_CHANGES);
                     }

                     String encNewLoginPw;
                     String domain;
                     if (!oldServerIp.equals(newServerIp) || !oldLoginId.equals(newLoginId) || !oldLoginPw.equals(newLoginPw) || !oldDirectory.equals(newDirectory)) {
                        encNewLoginPw = "";
                        long timeout = 10L;

                        try {
                           encNewLoginPw = CommonConfig.get("cifs.connection.timeout");
                           if (StringUtils.isEmpty(encNewLoginPw)) {
                              encNewLoginPw = "10";
                           }

                           timeout = Long.valueOf(encNewLoginPw);
                        } catch (Exception var66) {
                           this.logger.error("", var66);
                        }

                        String[] splitDomainAndUserId = CifsFilesToDownload.splitDomainAndUserId(newLoginId);
                        domain = "";
                        String userId = "";
                        if (splitDomainAndUserId != null && splitDomainAndUserId.length > 0) {
                           if (splitDomainAndUserId.length > 1) {
                              domain = splitDomainAndUserId[0];
                              userId = splitDomainAndUserId[1];
                           } else {
                              userId = newLoginId;
                           }
                        } else {
                           userId = newLoginId;
                        }

                        SmbConfig config = SmbConfig.builder().withTimeout(timeout, TimeUnit.SECONDS).withSoTimeout(timeout, TimeUnit.SECONDS).build();
                        client = new SMBClient(config);

                        try {
                           connection = client.connect(newServerIp);
                        } catch (IOException var65) {
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_CONNECT_REMOTE_SERVER);
                        }

                        try {
                           AuthenticationContext ac = new AuthenticationContext(userId, newLoginPw.toCharArray(), domain);
                           smbSession = connection.authenticate(ac);
                        } catch (Exception var64) {
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REMOTE_SERVER_LOGIN_FAIL);
                        }

                        String[] dirInfo = newDirectory.split("/");
                        share = (DiskShare)smbSession.connectShare(dirInfo[0]);
                        String checkingFolder = "";
                        if (dirInfo.length > 1) {
                           for(int i = 1; i < dirInfo.length; ++i) {
                              checkingFolder = checkingFolder + dirInfo[i];
                              if (i < dirInfo.length - 1) {
                                 checkingFolder = checkingFolder + "\\";
                              }
                           }
                        }

                        if (!share.folderExists(checkingFolder)) {
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_INVALID_PATH);
                        }

                        if (!share.isConnected()) {
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_CONNECT_REMOTE_SERVER);
                        }
                     }

                     String schedulerJobGroup;
                     if (!oldServerIp.equals(newServerIp) || !oldLoginId.equals(newLoginId) || !oldDirectory.equals(newDirectory)) {
                        encNewLoginPw = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
                        String newLocalPathByIp = ContentUtils.getCifsLocalPath(encNewLoginPw, newServerIp, newLoginId, newDirectory, miUserId);
                        File newLocalPathByIpDir = SecurityUtils.getSafeFile(newLocalPathByIp);
                        schedulerJobGroup = ContentUtils.getCifsLocalPath(encNewLoginPw, oldServerIp, oldLoginId, oldDirectory, miUserId);
                        File oldLocalPathByIpDir = SecurityUtils.getSafeFile(schedulerJobGroup);
                        if (newLocalPathByIpDir.exists()) {
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SAME_CONTENT_EXIST);
                        }

                        if (oldLocalPathByIpDir.exists()) {
                           oldLocalPathByIpDir.renameTo(newLocalPathByIpDir);
                        }
                     }

                     if (oldRefreshInterval != newRefreshInterval || !oldCanRefresh.equals(newCanRefresh)) {
                        Scheduler scheduler = ScheduleManager.getSchedulerInstance();
                        int startingDelay = Integer.valueOf(String.valueOf(newRefreshInterval));
                        String schedulerJobName = "CIFS_" + contentId;
                        schedulerJobGroup = "UpdateCifsContentService";
                        CommonUtils.deleteJob(schedulerJobName, schedulerJobGroup);
                        if ("Y".equalsIgnoreCase(newCanRefresh) && newRefreshInterval > 0L) {
                           domain = null;
                           JobDetail jobdetail = CommonUtils.getJobDetail(schedulerJobName, schedulerJobGroup, CifsContentScheduleJob.class);
                           Calendar currTime = Calendar.getInstance();
                           currTime.add(12, startingDelay);
                           SimpleTrigger trigger = CommonUtils.getSimpleTrigger(schedulerJobName, schedulerJobGroup, currTime.getTime(), newRefreshInterval * 60L * 1000L);

                           try {
                              scheduler.scheduleJob(jobdetail, trigger);
                           } catch (SchedulerException var63) {
                              this.logger.error(var63);
                           }
                        }
                     }

                     encNewLoginPw = Base64.encode(newLoginPw.getBytes());
                     contentInfo.updateCifsSettingByContentId(contentId, newContentName, newServerIp, newLoginId, encNewLoginPw, newDirectory, newRefreshInterval, newCanRefresh, newLoginRetryMaxCount, newLoginRetryCount, newCanLoginRetry);
                     contentInfo.setContentInfo(contentId, newContentName, (String)null, -1);
                  } catch (Exception var67) {
                     contentInfo.setIsReadyForNextCifsThread(isReady, contentId);
                     oldServerIp = var67.getMessage();
                     this.logger.error("[MagicInfo_CIFS_Update] " + oldServerIp);
                     this.logger.error(var67);
                     throw var67;
                  }
               } finally {
                  if (share != null) {
                     share.close();
                  }

                  if (smbSession != null) {
                     smbSession.close();
                  }

                  if (connection != null) {
                     connection.close();
                  }

               }

               contentInfo.setIsReadyForNextCifsThread("Y", contentId);
               List settings = contentInfo.getCifsContentSettingByContentId(contentId);
               if (settings.size() > 0) {
                  Map setting = (Map)settings.get(0);
                  newResource.setContentId(contentId);
                  newResource.setType((String)setting.get("media_type"));
                  newResource.setUserId((String)setting.get("creator_id"));
                  newResource.setCifsContentName((String)setting.get("content_name"));
                  newResource.setCifsIP((String)setting.get("server_ip"));
                  newResource.setCifsLoginId((String)setting.get("cifs_user_id"));
                  newResource.setCifsPassword(new String(Base64.decode((String)setting.get("cifs_user_password"))));
                  newResource.setCifsDirectory((String)setting.get("remote_dir"));
                  newResource.setCifsRefreshInterval(String.valueOf(setting.get("refresh_interval")));
                  newResource.setCanRefresh((String)setting.get("can_refresh"));
                  newResource.setLoginRetryMaxCount(String.valueOf(setting.get("login_retry_max_count")));
                  newResource.setCanLoginRetry((String)setting.get("can_login_retry"));
               }

               try {
                  List notifications = new ArrayList();
                  notifications.add(this.makeNotificationData(contentId));
                  if (notifications.size() > 0) {
                     MailUtil.sendContentEventMail(notifications, "Edit content");
                  }
               } catch (Exception var62) {
                  this.logger.error(var62);
               }

               return newResource;
            }
         }
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REMOTE_CONTENT_PARAMETER_INVALID);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Upload Authority', 'Content Manage Authority')")
   public V2FtpContentSettingResource createFtpContent(V2FtpContentSettingResource resource) throws Exception {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      Locale locale = SecurityUtils.getLocale();
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, SecurityUtils.getLoginUserId());
      Long rootGroupId = cInfo.getRootId(this.getLoginUserId());
      String user_id = resource.getUserId();
      String group_id = resource.getGroupId();
      String ftpContentName = resource.getFtpContentName();
      String ftpIP = resource.getFtpIP();
      String ftpLoginId = resource.getFtpLoginId();
      String ftpPassword = resource.getFtpPassword();
      String ftpDirectory = resource.getFtpDirectory();
      String portStr = resource.getPortStr().isEmpty() ? "21" : resource.getPortStr();
      if (portStr != null && !portStr.isEmpty()) {
         long portNumber = Long.parseLong(portStr);
         int minPortNumber = 0;
         int maxPortNumber = '\uffff';
         if (portNumber < (long)minPortNumber || (long)maxPortNumber < portNumber) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_CONNECT_REMOTE_SERVER);
         }
      }

      int port = Integer.parseInt(portStr);
      String ftpRefreshInterval = resource.getFtpRefreshInterval().isEmpty() ? "1" : resource.getFtpRefreshInterval();
      String canRefresh = resource.getCanRefresh();
      String loginRetryMaxCount = resource.getLoginRetryMaxCount().isEmpty() ? "1" : resource.getLoginRetryMaxCount();
      String canLoginRetry = resource.getCanLoginRetry();
      String miUserId = "";
      String contentId = "";
      int maxSizeOfDirectory = 50;
      miUserId = user_id;
      contentId = UUID.randomUUID().toString();
      if (user_id == null || user_id != null && user_id.isEmpty()) {
         miUserId = this.getLoginUserId();
      }

      if (group_id.equals("0")) {
         group_id = rootGroupId.toString();
      }

      RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(group_id));
      String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
      if (ftpDirectory.length() > maxSizeOfDirectory) {
         ftpDirectory = ftpDirectory.substring(ftpDirectory.length() - maxSizeOfDirectory, ftpDirectory.length());
      }

      this.logger.debug("[CDV_FTP] " + ftpContentName + ftpIP + portStr + ftpLoginId + ftpDirectory + ftpRefreshInterval + " by " + miUserId + " in " + group_id);
      String tempIpAddress = "";
      if (ftpIP.contains(".")) {
         tempIpAddress = ftpIP.replace('.', '_');
      } else if (ftpIP.contains(":")) {
         tempIpAddress = ftpIP.replace(':', '_');
      }

      String localPathByIp = CONTENTS_HOME + File.separator + "FTP_" + tempIpAddress + '_' + ftpLoginId.replace('/', '_').replace('\\', '_').replace('.', '_').replace(' ', '_') + '_' + ftpDirectory.replace('/', '_').replace('\\', '_').replace('.', '_').replace(' ', '_') + miUserId;
      if (!StringUtils.isEmpty(ftpContentName) && !StringUtils.isEmpty(ftpIP) && !StringUtils.isEmpty(ftpLoginId) && !StringUtils.isEmpty(ftpPassword) && !StringUtils.isEmpty(ftpDirectory) && !StringUtils.isEmpty(ftpRefreshInterval) && !StringUtils.isEmpty(canRefresh) && !StringUtils.isEmpty(loginRetryMaxCount) && !StringUtils.isEmpty(canLoginRetry)) {
         if (resource.getType().equals("FTP") && SecurityUtils.getSafeFile(localPathByIp).isDirectory()) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SAME_CONTENT_EXIST);
         } else {
            FTPClient client = null;
            boolean var25 = false;

            try {
               try {
                  client = new FTPClient();
                  client.setControlEncoding("euc-kr");
                  client.setDefaultPort(port);
                  client.connect(ftpIP, port);
               } catch (Exception var48) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_CONNECT_REMOTE_SERVER);
               }

               int reply = client.getReplyCode();
               if (!FTPReply.isPositiveCompletion(reply)) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_CONNECT_REMOTE_SERVER);
               }

               if (!client.login(ftpLoginId, ftpPassword)) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REMOTE_SERVER_LOGIN_FAIL);
               }

               client.setFileType(2);
               client.enterLocalPassiveMode();
               if (!client.changeWorkingDirectory(ftpDirectory)) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_INVALID_PATH);
               }

               FTPFile[] ftpFiles = client.listFiles();

               try {
                  if (ftpFiles == null || ftpFiles.length <= 0) {
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NO_AVAILABLE_FILES);
                  }

                  int fileCount = 0;
                  FTPFile[] var28 = ftpFiles;
                  int var29 = ftpFiles.length;

                  for(int var30 = 0; var30 < var29; ++var30) {
                     FTPFile file = var28[var30];
                     String ftpFileName = file.getName();
                     if (file.isFile() && !file.isDirectory()) {
                        ++fileCount;
                        boolean validType = false;
                        String[] tempName = ftpFileName.split("[.]");
                        int sizeOfSplitedName = false;
                        if (tempName.length > 0) {
                           int sizeOfSplitedName = tempName.length - 1;
                           validType = cInfo.getCodeFile(tempName[sizeOfSplitedName].toUpperCase()).equalsIgnoreCase("");
                           this.logger.debug("[File checker] fileName validType " + ftpFileName + " " + validType);
                        }

                        if (validType) {
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NO_AVAILABLE_FILES);
                        }
                        break;
                     }
                  }

                  if (fileCount == 0) {
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NO_AVAILABLE_FILES);
                  }
               } catch (Exception var50) {
                  this.logger.error("", var50);
                  throw var50;
               }

               client.logout();
            } catch (Exception var51) {
               this.logger.error("", var51);
               throw var51;
            } finally {
               if (client != null && client.isConnected()) {
                  try {
                     client.disconnect();
                  } catch (IOException var46) {
                     System.out.println("Disconnected!");
                  }
               }

            }

            try {
               if (AMQUtil.isEnabledRCDS()) {
                  BrokerMonitor broker = BrokerMonitorImpl.getInstance();
                  if (!broker.isAlive()) {
                     this.logger.error("[MagicInfo_FTP] Message Queue Server is NOT alive... " + ftpContentName + "[" + contentId + "]");
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_CONNECT_REMOTE_SERVER);
                  }
               }

               this.logger.info("[CList] Start FTP " + localPathByIp);
               long nGroupId = Long.parseLong(group_id);
               long nFtpRefreshInterval = Long.parseLong(ftpRefreshInterval);
               boolean scheduledJob = false;
               long nLoginRetryMaxCount = Long.parseLong(loginRetryMaxCount);
               Runnable runFTP = new FtpFileDownloadThread(miUserId, nGroupId, contentId, ftpContentName, ftpIP, port, ftpLoginId, ftpPassword, localPathByIp, ftpDirectory, nFtpRefreshInterval, scheduledJob, canRefresh, nLoginRetryMaxCount, canLoginRetry);
               Thread threadFTP = new Thread(runFTP);
               threadFTP.start();
            } catch (Exception var49) {
               this.logger.error("", var49);
               throw var49;
            }

            try {
               List contentNotiDataList = new ArrayList();
               contentNotiDataList.add(this.makeNotificationData(contentId));
               if (contentNotiDataList.size() > 0) {
                  MailUtil.sendContentEventMail(contentNotiDataList, "Add content");
               }
            } catch (Exception var47) {
               this.logger.error(var47);
            }

            V2FtpContentSettingResource newResource = new V2FtpContentSettingResource();
            newResource.setContentId(contentId);
            newResource.setType("FTP");
            newResource.setUserId(miUserId);
            newResource.setFtpContentName(ftpContentName);
            newResource.setFtpIP(ftpIP);
            newResource.setPortStr(portStr);
            newResource.setFtpLoginId(ftpLoginId);
            newResource.setFtpPassword(ftpPassword);
            newResource.setFtpDirectory(ftpDirectory);
            newResource.setFtpRefreshInterval(ftpRefreshInterval);
            newResource.setCanRefresh(canRefresh);
            newResource.setLoginRetryMaxCount(loginRetryMaxCount);
            newResource.setCanLoginRetry(canLoginRetry);
            return newResource;
         }
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_INVALID_PARAMS);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2FtpContentSettingResource getFtpContent(String contentId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      Content content = contentInfo.getContentAndFileActiveVerInfo(contentId);
      V2FtpContentSettingResource resource = new V2FtpContentSettingResource();
      List settings = contentInfo.getFtpContentSettingByContentId(contentId);
      if (settings.size() > 0) {
         Map setting = (Map)settings.get(0);
         resource.setContentId(contentId);
         resource.setType((String)setting.get("media_type"));
         resource.setUserId((String)setting.get("creator_id"));
         resource.setFtpContentName(content.getContent_name());
         resource.setFtpIP((String)setting.get("server_ip"));
         resource.setPortStr((String)setting.get("server_port"));
         resource.setFtpLoginId((String)setting.get("ftp_user_id"));
         resource.setFtpPassword(new String(Base64.decode((String)setting.get("ftp_user_password"))));
         resource.setFtpDirectory((String)setting.get("ftp_path"));
         resource.setFtpRefreshInterval(String.valueOf(setting.get("refresh_interval")));
         resource.setCanRefresh((String)setting.get("can_refresh"));
         resource.setLoginRetryMaxCount(String.valueOf(setting.get("login_retry_max_count")));
         resource.setCanLoginRetry((String)setting.get("can_login_retry"));
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Upload Authority', 'Content Manage Authority')")
   public V2FtpContentSettingResource updateFtpContent(String contentId, V2FtpContentSettingResource resource) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, SecurityUtils.getLoginUserId());
      if (!StrUtils.nvl(resource.getGroupId()).equals("")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(resource.getGroupId()));
      }

      ContentInfo cInfo = ContentInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      new LinkedHashMap();
      String resultMessage = "Edit operation successful.";
      String newContentName = resource.getFtpContentName();
      String newServerIp = resource.getFtpIP();
      String portStr = resource.getPortStr().equals("") ? "21" : resource.getPortStr();
      String newLoginId = resource.getFtpLoginId();
      String newLoginPw = resource.getFtpPassword();
      String newDirectory = resource.getFtpDirectory();
      String tmpRefreshInterval = resource.getFtpRefreshInterval();
      long newRefreshInterval = Long.parseLong(StringUtils.isEmpty(tmpRefreshInterval) ? "1" : tmpRefreshInterval);
      String newCanRefresh = resource.getCanRefresh();
      String tmpLoginRetryMaxCount = resource.getLoginRetryMaxCount();
      long newLoginRetryMaxCount = Long.parseLong(StringUtils.isEmpty(tmpLoginRetryMaxCount) ? "1" : tmpLoginRetryMaxCount);
      long newLoginRetryCount = 0L;
      String newCanLoginRetry = resource.getCanLoginRetry();
      if (portStr != null && !portStr.isEmpty()) {
         long portNumber = Long.parseLong(portStr);
         int minPortNumber = 0;
         int maxPortNumber = '\uffff';
         if (portNumber < (long)minPortNumber || (long)maxPortNumber < portNumber) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_CONNECT_REMOTE_SERVER);
         }
      }

      int newServerPort = Integer.parseInt(portStr);
      List oldSettings = cInfo.getFtpContentSettingByContentId(contentId);
      if (oldSettings != null && oldSettings.size() > 0) {
         Map setting = (Map)oldSettings.get(0);
         newContentName = newContentName.isEmpty() ? setting.get("content_name").toString() : newContentName;
         newServerIp = newServerIp.isEmpty() ? setting.get("server_ip").toString() : newServerIp;
         portStr = resource.getPortStr().equals("") ? setting.get("server_port").toString() : resource.getPortStr();
         newLoginId = newLoginId.isEmpty() ? setting.get("ftp_user_id").toString() : newLoginId;
         newLoginPw = newLoginPw.isEmpty() ? new String(Base64.decode((String)setting.get("ftp_user_password"))) : newLoginPw;
         newDirectory = newDirectory.isEmpty() ? setting.get("ftp_path").toString() : newDirectory;
         tmpRefreshInterval = resource.getFtpRefreshInterval().isEmpty() ? setting.get("refresh_interval").toString() : tmpRefreshInterval;
         newRefreshInterval = resource.getFtpRefreshInterval().isEmpty() ? Long.parseLong(setting.get("refresh_interval").toString()) : newRefreshInterval;
         newCanRefresh = newCanRefresh.isEmpty() ? setting.get("can_refresh").toString() : newCanRefresh;
         tmpLoginRetryMaxCount = resource.getLoginRetryMaxCount().isEmpty() ? setting.get("login_retry_max_count").toString() : tmpLoginRetryMaxCount;
         newLoginRetryMaxCount = resource.getLoginRetryMaxCount().isEmpty() ? Long.valueOf(setting.get("login_retry_max_count").toString()) : newLoginRetryMaxCount;
         newLoginRetryCount = Long.valueOf(setting.get("login_retry_count").toString());
         newCanLoginRetry = newCanLoginRetry.isEmpty() ? setting.get("can_login_retry").toString() : newCanLoginRetry;
         if (portStr != null && !portStr.isEmpty()) {
            long portNumber = Long.parseLong(portStr);
            int minPortNumber = 0;
            int maxPortNumber = '\uffff';
            if (portNumber < (long)minPortNumber || (long)maxPortNumber < portNumber) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_CONNECT_REMOTE_SERVER);
            }
         }

         newServerPort = Integer.parseInt(portStr);
      }

      if (!StringUtils.isEmpty(newContentName) && !StringUtils.isEmpty(newServerIp) && !StringUtils.isEmpty(newLoginId) && !StringUtils.isEmpty(newLoginPw) && !StringUtils.isEmpty(newDirectory) && !StringUtils.isEmpty(tmpRefreshInterval) && !StringUtils.isEmpty(newCanRefresh) && !StringUtils.isEmpty(tmpLoginRetryMaxCount) && !StringUtils.isEmpty(newCanLoginRetry)) {
         ContentInfo contentInfo = ContentInfoImpl.getInstance();
         String miUserId = contentInfo.getContentOrgCreatorId(contentId);
         int maxSizeOfDirectory = 50;
         if (miUserId == null || miUserId != null && miUserId.isEmpty()) {
            miUserId = "admin";
         }

         if (newDirectory.length() > maxSizeOfDirectory) {
            newDirectory = newDirectory.substring(newDirectory.length() - maxSizeOfDirectory, newDirectory.length());
         }

         List ftpSettingList = contentInfo.getFtpContentSettingByContentId(contentId);
         if (ftpSettingList.size() == 0) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"content"});
         } else {
            Map ftpSettingInfo = (Map)ftpSettingList.get(0);
            String isDeleted = (String)ftpSettingInfo.get("is_deleted");
            String isReady = contentInfo.getIsReadyForNextFtpThread(contentId);
            if ("Y".equalsIgnoreCase(isDeleted)) {
               throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"content"});
            } else if ("N".equalsIgnoreCase(isReady)) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CONTENT_EDIT_FAIL_WHILE_DOWNLOADING);
            } else {
               contentInfo.setIsReadyForNextFtpThread("N", contentId);
               FTPClient client = null;

               try {
                  String oldServerIp;
                  try {
                     String oldContentName = (String)ftpSettingInfo.get("content_name");
                     oldServerIp = (String)ftpSettingInfo.get("server_ip");
                     int oldServerPort = Integer.parseInt(ftpSettingInfo.get("server_port").toString());
                     String oldLoginId = (String)ftpSettingInfo.get("ftp_user_id");
                     String oldLoginPw = new String(Base64.decode((String)ftpSettingInfo.get("ftp_user_password")));
                     String oldDirectory = (String)ftpSettingInfo.get("ftp_path");
                     long oldRefreshInterval = Long.parseLong(ftpSettingInfo.get("refresh_interval").toString());
                     String oldCanRefresh = (String)ftpSettingInfo.get("can_refresh");
                     long oldLoginRetryMaxCount = Long.parseLong(ftpSettingInfo.get("login_retry_max_count").toString());
                     String oldCanLoginRetry = (String)ftpSettingInfo.get("can_login_retry");
                     if (oldDirectory.length() > maxSizeOfDirectory) {
                        oldDirectory = oldDirectory.substring(oldDirectory.length() - maxSizeOfDirectory, oldDirectory.length());
                     }

                     if (oldContentName.equals(newContentName) && oldServerIp.equals(newServerIp) && oldServerPort == newServerPort && oldLoginId.equals(newLoginId) && oldLoginPw.equals(newLoginPw) && oldDirectory.equals(newDirectory) && oldRefreshInterval == newRefreshInterval && oldCanRefresh.equals(newCanRefresh) && oldCanLoginRetry.equals(newCanLoginRetry) && oldLoginRetryMaxCount == newLoginRetryMaxCount) {
                        throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NO_CHANGES);
                     }

                     String encNewLoginPw;
                     int startingDelay;
                     if (!oldServerIp.equals(newServerIp) || oldServerPort != newServerPort || !oldLoginId.equals(newLoginId) || !oldLoginPw.equals(newLoginPw) || !oldDirectory.equals(newDirectory)) {
                        encNewLoginPw = System.getProperty("file.encoding");
                        if (StringUtils.isEmpty(encNewLoginPw)) {
                           encNewLoginPw = "euckr";
                        }

                        try {
                           client = new FTPClient();
                           client.setControlEncoding(encNewLoginPw);
                           client.setAutodetectUTF8(true);
                           client.setDefaultPort(newServerPort);
                           client.connect(newServerIp, newServerPort);
                        } catch (Exception var65) {
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_CONNECT_REMOTE_SERVER);
                        }

                        startingDelay = client.getReplyCode();
                        if (!FTPReply.isPositiveCompletion(startingDelay)) {
                           client.disconnect();
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_CONNECT_REMOTE_SERVER);
                        }

                        if (!client.login(newLoginId, newLoginPw)) {
                           client.logout();
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REMOTE_SERVER_LOGIN_FAIL);
                        }

                        client.setFileType(2);
                        client.enterLocalPassiveMode();
                        if (!client.changeWorkingDirectory(newDirectory)) {
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_INVALID_PATH);
                        }
                     }

                     String schedulerJobGroup;
                     File jobdetail;
                     if (!oldServerIp.equals(newServerIp) || !oldLoginId.equals(newLoginId) || !oldDirectory.equals(newDirectory)) {
                        encNewLoginPw = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
                        String newLocalPathByIp = ContentUtils.getFtpLocalPath(encNewLoginPw, newServerIp, newLoginId, newDirectory, miUserId);
                        File newLocalPathByIpDir = SecurityUtils.getSafeFile(newLocalPathByIp);
                        schedulerJobGroup = ContentUtils.getFtpLocalPath(encNewLoginPw, oldServerIp, oldLoginId, oldDirectory, miUserId);
                        jobdetail = SecurityUtils.getSafeFile(schedulerJobGroup);
                        if (newLocalPathByIpDir.exists()) {
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SAME_ITEM_EXIST, new String[]{"Content List"});
                        }

                        if (jobdetail.exists()) {
                           jobdetail.renameTo(newLocalPathByIpDir);
                        }
                     }

                     if (oldRefreshInterval != newRefreshInterval || !oldCanRefresh.equals(newCanRefresh)) {
                        Scheduler scheduler = ScheduleManager.getSchedulerInstance();
                        startingDelay = Integer.valueOf(String.valueOf(newRefreshInterval));
                        String schedulerJobName = "FTP_" + contentId;
                        schedulerJobGroup = "UpdateFtpContentService";
                        CommonUtils.deleteJob(schedulerJobName, schedulerJobGroup);
                        if ("Y".equalsIgnoreCase(newCanRefresh) && newRefreshInterval > 0L) {
                           jobdetail = null;
                           JobDetail jobdetail = CommonUtils.getJobDetail(schedulerJobName, schedulerJobGroup, FtpContentScheduleJob.class);
                           Calendar currTime = Calendar.getInstance();
                           currTime.add(12, startingDelay);
                           SimpleTrigger trigger = CommonUtils.getSimpleTrigger(schedulerJobName, schedulerJobGroup, currTime.getTime(), newRefreshInterval * 60L * 1000L);

                           try {
                              scheduler.scheduleJob(jobdetail, trigger);
                           } catch (SchedulerException var64) {
                              this.logger.error(var64);
                           }
                        }
                     }

                     encNewLoginPw = Base64.encode(newLoginPw.getBytes());
                     contentInfo.updateFtpSettingByContentId(contentId, newContentName, newServerIp, newServerPort, newLoginId, encNewLoginPw, newDirectory, newRefreshInterval, newCanRefresh, newLoginRetryMaxCount, newLoginRetryCount, newCanLoginRetry);
                     contentInfo.setContentInfo(contentId, newContentName, (String)null, -1);
                  } catch (Exception var66) {
                     oldServerIp = var66.getMessage();
                     this.logger.error("[MagicInfo_FTP_Update] " + oldServerIp);
                     this.logger.error(var66);
                     throw var66;
                  }
               } finally {
                  if (client != null && client.isConnected()) {
                     try {
                        client.disconnect();
                     } catch (IOException var62) {
                        this.logger.error(var62);
                     }
                  }

                  contentInfo.setIsReadyForNextFtpThread("Y", contentId);
               }

               try {
                  List notifications = new ArrayList();
                  notifications.add(this.makeNotificationData(contentId));
                  if (notifications.size() > 0) {
                     MailUtil.sendContentEventMail(notifications, "Edit content");
                  }
               } catch (Exception var63) {
                  this.logger.error(var63);
               }

               List settings = contentInfo.getFtpContentSettingByContentId(contentId);
               if (settings != null && settings.size() > 0) {
                  V2FtpContentSettingResource newResource = new V2FtpContentSettingResource();
                  Map setting = (Map)settings.get(0);
                  newResource.setContentId(contentId);
                  newResource.setType((String)setting.get("media_type"));
                  newResource.setUserId((String)setting.get("creator_id"));
                  newResource.setFtpContentName((String)setting.get("content_name"));
                  newResource.setFtpIP((String)setting.get("server_ip"));
                  newResource.setPortStr((String)setting.get("server_port"));
                  newResource.setFtpLoginId((String)setting.get("ftp_user_id"));
                  newResource.setFtpPassword(new String(Base64.decode((String)setting.get("ftp_user_password"))));
                  newResource.setFtpDirectory((String)setting.get("ftp_path"));
                  newResource.setFtpRefreshInterval(String.valueOf(setting.get("refresh_interval")));
                  newResource.setCanRefresh((String)setting.get("can_refresh"));
                  newResource.setLoginRetryMaxCount(String.valueOf(setting.get("login_retry_max_count")));
                  newResource.setCanLoginRetry((String)setting.get("can_login_retry"));
                  return newResource;
               } else {
                  return null;
               }
            }
         }
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"Remote Content"});
      }
   }

   private boolean checkContentTypeSetCorrect(String contentTypeSet, String contentDuration) {
      if (!StringUtils.isBlank(contentTypeSet) && (contentTypeSet.equals("Y") || contentTypeSet.equals("N"))) {
         if (StringUtils.isBlank(contentDuration)) {
            return false;
         } else {
            try {
               Long nContentDuration = Long.parseLong(contentDuration);
               int minContentDuration = 5;
               return nContentDuration >= (long)minContentDuration;
            } catch (NumberFormatException var5) {
               return false;
            }
         }
      } else {
         return false;
      }
   }

   private boolean checkDefaultContentFile(String fileId) {
      if (StringUtils.isBlank(fileId)) {
         return false;
      } else {
         try {
            ContentInfo contentInfo = ContentInfoImpl.getInstance();
            if (!contentInfo.isExistFileByID(fileId)) {
               return false;
            } else {
               String fileName = contentInfo.getFileName(fileId);
               String fext = fileName.substring(fileName.lastIndexOf(".") + 1);
               return this.supportedImageType(fext) || this.supportedVideoType(fext);
            }
         } catch (Exception var5) {
            return false;
         }
      }
   }

   private void setSuggestionInfo(V2AdsContentSettingResource adsContentResource) {
      V2AdsContentPublisherInfo publisherInfo = new V2AdsContentPublisherInfo();
      publisherInfo.setPublisherId(adsContentResource.getPublisherId());
      publisherInfo.setPublisherName(adsContentResource.getPublisherName());
      publisherInfo.setApiKey(adsContentResource.getApiKey());
      publisherInfo.setApiKeySecret(adsContentResource.getApiSecret());
      String adUnitId = adsContentResource.getAdUnitId();
      String userId = SecurityUtils.getLoginUserId();
      ContentInfoImpl contentInfo = ContentInfoImpl.getInstance();

      try {
         V2AdsContentPublisherInfo pInfo = contentInfo.getPublisherInfoById(publisherInfo.getPublisherId(), userId);
         if (pInfo == null) {
            contentInfo.addAdsContentPublisherInfoSuggestion(publisherInfo, userId);
         } else if (!publisherInfo.getPublisherName().equals(pInfo.getPublisherName()) || !publisherInfo.getApiKey().equals(pInfo.getApiKey()) || !publisherInfo.getApiKeySecret().equals(pInfo.getApiKeySecret())) {
            contentInfo.updateAdsContentPublisherSuggestionInfo(publisherInfo, userId);
         }
      } catch (Exception var8) {
         this.logger.error(var8);
      }

      try {
         if (StringUtils.isNotEmpty(adUnitId) && contentInfo.existAdUnitIdById(adUnitId, userId) == 0) {
            contentInfo.addAdsContentAdUnitIdSuggestion(adUnitId, userId);
         }
      } catch (Exception var7) {
         this.logger.error(var7);
      }

   }

   private Map createAdsContentSettingMap(V2AdsContentSettingResource adsContentResource) {
      HashMap adsContentSettingData = new HashMap();

      try {
         String adUnitId = adsContentResource.getAdUnitId();
         adUnitId = StringUtils.isNotBlank(adUnitId) ? adUnitId : "";
         adsContentSettingData.put("ad_unit_id", adUnitId);
         adsContentSettingData.put("publisher_id", adsContentResource.getPublisherId());
         adsContentSettingData.put("publisher_name", adsContentResource.getPublisherName());
         adsContentSettingData.put("image_type_set", adsContentResource.getImageTypeSet());
         adsContentSettingData.put("video_type_set", adsContentResource.getVideoTypeSet());
         adsContentSettingData.put("video_duration", Long.parseLong(adsContentResource.getVideoDuration()));
         adsContentSettingData.put("image_duration", Long.parseLong(adsContentResource.getImageDuration()));
         adsContentSettingData.put("default_content", adsContentResource.getDefaultContentName());
         adsContentSettingData.put("default_content_file_id", adsContentResource.getDefaultContentFileId());
         adsContentSettingData.put("api_key", Base64.encode(adsContentResource.getApiKey().getBytes()));
         adsContentSettingData.put("api_key_secret", Base64.encode(adsContentResource.getApiSecret().getBytes()));
         return adsContentSettingData;
      } catch (Exception var4) {
         this.logger.error(var4);
         return null;
      }
   }

   private Map createAdsContentConfigMap(V2AdsContentSettingResource adsContentResource) {
      HashMap adsContentConfigData = new HashMap();

      try {
         boolean setImageDuration = adsContentResource.getImageTypeSet().equals("Y");
         boolean setVideoDuration = adsContentResource.getVideoTypeSet().equals("Y");
         Long nImageDuration = setImageDuration ? Long.parseLong(adsContentResource.getImageDuration()) : null;
         Long nVideoDuration = setVideoDuration ? Long.parseLong(adsContentResource.getVideoDuration()) : null;
         String adUnitId = adsContentResource.getAdUnitId();
         adUnitId = StringUtils.isNotBlank(adUnitId) ? adUnitId : null;
         ContentInfo cInfo = ContentInfoImpl.getInstance();
         String defaultContentFileName = cInfo.getFileName(adsContentResource.getDefaultContentFileId());
         adsContentConfigData.put("publisher_id", adsContentResource.getPublisherId());
         adsContentConfigData.put("publisher_name", adsContentResource.getPublisherName());
         adsContentConfigData.put("api_key", adsContentResource.getApiKey());
         adsContentConfigData.put("api_key_secret", adsContentResource.getApiSecret());
         adsContentConfigData.put("ad_unit_id", adUnitId);
         adsContentConfigData.put("video_duration", nVideoDuration);
         adsContentConfigData.put("image_duration", nImageDuration);
         adsContentConfigData.put("default_content", defaultContentFileName);
         return adsContentConfigData;
      } catch (Exception var10) {
         this.logger.error(var10);
         return null;
      }
   }

   private void createAdsContentUtil(V2AdsContentSettingResource resource, String contentId, boolean isUpdate) throws Exception {
      String CONTENTS_HOME = null;

      try {
         CONTENTS_HOME = ContentUtils.getContentsHome();
      } catch (ConfigException var11) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"CONTENTS_HOME"});
      }

      String lfdFileId = UUID.randomUUID().toString().toUpperCase();
      Map adsContentConfigData = this.createAdsContentConfigMap(resource);
      ContentFile adsContentConfigFile = ContentUtils.createAdsContentConfigFile(adsContentConfigData);
      if (adsContentConfigFile == null) {
         this.logger.error("[MagicInfo_createAdsContent] fail to create ADS Content config file.");
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONTENT_CREATE, new String[]{"ADS"});
      } else {
         Map adsContentSettingData = this.createAdsContentSettingMap(resource);
         if (adsContentSettingData == null) {
            this.logger.error("[MagicInfo_createAdsContent] fail to create ADS Content Settings.");
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONTENT_CREATE, new String[]{"ADS"});
         } else {
            adsContentSettingData.put("adsContentConfigFileId", adsContentConfigFile.getFile_id());
            Map data = new HashMap();
            data.put("adsSetting", adsContentSettingData);
            if (isUpdate) {
               data.put("mode", "UPDATE");
            }

            int code = ContentUtils.createWebContent(SecurityUtils.getLoginUserId(), "WPLAYER", 1.0F, CONTENTS_HOME + File.separator + lfdFileId, data, "ADS", contentId, resource.getAdsContentName(), (String)null);
            if (code != 1) {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONTENT_CREATE, new String[]{"ADS"});
            } else {
               this.setSuggestionInfo(resource);
            }
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public V2AdsContentSettingResource createAdsContent(V2AdsContentSettingResource resource) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, SecurityUtils.getLoginUserId());
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      String group_id = resource.getGroupId();
      Long rootGroupId = cInfo.getRootId(this.getLoginUserId());
      V2AdsContentSettingResource newResource = new V2AdsContentSettingResource();
      String adsContentName = resource.getAdsContentName();
      String apiKey = resource.getApiKey();
      String apiSecret = resource.getApiSecret();
      String publisherId = resource.getPublisherId();
      String publisherName = resource.getPublisherName();
      String adUnitId = resource.getAdUnitId();
      String videoTypeSet = resource.getVideoTypeSet();
      String imageTypeSet = resource.getImageTypeSet();
      String videoDuration = resource.getVideoDuration();
      String imageDuration = resource.getImageDuration();
      String defaultContentName = resource.getDefaultContentName();
      String defaultContentFileId = resource.getDefaultContentFileId();
      String contentId = UUID.randomUUID().toString().toUpperCase();
      if (!StringUtils.isBlank(adsContentName) && !StringUtils.isBlank(publisherId) && !StringUtils.isBlank(publisherName) && !StringUtils.isBlank(apiKey) && !StringUtils.isBlank(apiSecret) && this.checkContentTypeSetCorrect(imageTypeSet, imageDuration) && this.checkContentTypeSetCorrect(videoTypeSet, videoDuration) && this.checkDefaultContentFile(defaultContentFileId) && !StringUtils.isBlank(defaultContentName)) {
         if (group_id.equals("0")) {
            group_id = rootGroupId.toString();
         }

         RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(group_id));
         this.createAdsContentUtil(resource, contentId, false);

         try {
            List contentNotiDataList = new ArrayList();
            contentNotiDataList.add(this.makeNotificationData(contentId));
            if (contentNotiDataList.size() > 0) {
               MailUtil.sendContentEventMail(contentNotiDataList, "Add content");
            }
         } catch (Exception var20) {
            this.logger.error("[MagicInfo_createAdsContent] fail to send notification", var20);
         }

         Content content = cInfo.getContentAndFileActiveVerInfo(contentId);
         if (content != null) {
            newResource.setContentId(contentId);
            newResource.setType(content.getMedia_type());
            newResource.setGroupId(String.valueOf(content.getGroup_id()));
            newResource.setAdsContentName(adsContentName);
            newResource.setApiKey(apiKey);
            newResource.setApiSecret(apiSecret);
            newResource.setPublisherId(publisherId);
            newResource.setPublisherName(publisherName);
            newResource.setAdUnitId(adUnitId);
            newResource.setImageTypeSet(imageTypeSet);
            newResource.setImageDuration(imageDuration);
            newResource.setVideoTypeSet(videoTypeSet);
            newResource.setVideoDuration(videoDuration);
            newResource.setDefaultContentName(defaultContentName);
            newResource.setDefaultContentFileId(defaultContentFileId);
         }

         return newResource;
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_INVALID_PARAMS);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2AdsContentSettingResource updateAdsContent(String contentId, V2AdsContentSettingResource resource) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, SecurityUtils.getLoginUserId());
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      String group_id = resource.getGroupId();
      Long rootGroupId = cInfo.getRootId(this.getLoginUserId());
      V2AdsContentSettingResource newResource = new V2AdsContentSettingResource();
      String adsContentName = resource.getAdsContentName();
      String apiKey = resource.getApiKey();
      String apiSecret = resource.getApiSecret();
      String publisherId = resource.getPublisherId();
      String publisherName = resource.getPublisherName();
      String adUnitId = resource.getAdUnitId();
      String videoTypeSet = resource.getVideoTypeSet();
      String imageTypeSet = resource.getImageTypeSet();
      String videoDuration = resource.getVideoDuration();
      String imageDuration = resource.getImageDuration();
      String defaultContentName = resource.getDefaultContentName();
      String defaultContentFileId = resource.getDefaultContentFileId();
      if (!StringUtils.isBlank(adsContentName) && !StringUtils.isBlank(publisherId) && !StringUtils.isBlank(publisherName) && !StringUtils.isBlank(apiKey) && !StringUtils.isBlank(apiSecret) && this.checkContentTypeSetCorrect(imageTypeSet, imageDuration) && this.checkContentTypeSetCorrect(videoTypeSet, videoDuration) && this.checkDefaultContentFile(defaultContentFileId) && !StringUtils.isBlank(defaultContentName)) {
         Map oldSettings = cInfo.getAdsContentActiveVersionInfo(contentId);
         Content oldContentInfo = cInfo.getContentActiveVerInfo(contentId);
         if (oldSettings == null) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CONTENT_NOT_EXIST_FOR_UPDATE);
         } else {
            String oldAdsContentName = oldContentInfo.getContent_name();
            String oldApiKey = new String(Base64.decode((String)oldSettings.get("api_key")));
            String oldApiSecret = new String(Base64.decode((String)oldSettings.get("api_key_secret")));
            String oldPublisherId = (String)oldSettings.get("publisher_id");
            String oldPublisherName = (String)oldSettings.get("publisher_name");
            String oldAdUnitId = (String)oldSettings.get("ad_unit_id");
            String oldVideoTypeSet = (String)oldSettings.get("video_type_set");
            String oldImageTypeSet = (String)oldSettings.get("image_type_set");
            String oldVideoDuration = oldSettings.get("video_duration").toString();
            String oldImageDuration = oldSettings.get("image_duration").toString();
            String oldDefaultContentName = (String)oldSettings.get("default_content");
            String oldDefaultContentFileId = (String)oldSettings.get("default_content_file_id");
            if (oldAdsContentName.equals(adsContentName) && oldApiKey.equals(apiKey) && oldApiSecret.equals(apiSecret) && oldPublisherId.equals(publisherId) && oldPublisherName.equals(publisherName) && oldAdUnitId.equals(adUnitId) && oldVideoTypeSet.equals(videoTypeSet) && oldImageTypeSet.equals(imageTypeSet) && oldVideoDuration.equals(videoDuration) && oldImageDuration.equals(imageDuration) && oldDefaultContentName.equals(defaultContentName) && oldDefaultContentFileId.equals(defaultContentFileId)) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NO_CHANGES);
            } else {
               if (group_id.equals("0")) {
                  group_id = rootGroupId.toString();
               }

               RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(group_id));
               this.createAdsContentUtil(resource, contentId, true);

               try {
                  List contentNotiDataList = new ArrayList();
                  contentNotiDataList.add(this.makeNotificationData(contentId));
                  if (contentNotiDataList.size() > 0) {
                     MailUtil.sendContentEventMail(contentNotiDataList, "Edit content");
                  }
               } catch (Exception var34) {
                  this.logger.error("[MagicInfo_createAdsContent] fail to send notification", var34);
               }

               Content content = cInfo.getContentAndFileActiveVerInfo(contentId);
               if (content != null) {
                  newResource.setContentId(contentId);
                  newResource.setType(content.getMedia_type());
                  newResource.setGroupId(String.valueOf(content.getGroup_id()));
                  newResource.setAdsContentName(adsContentName);
                  newResource.setApiKey(apiKey);
                  newResource.setApiSecret(apiSecret);
                  newResource.setPublisherId(publisherId);
                  newResource.setPublisherName(publisherName);
                  newResource.setAdUnitId(adUnitId);
                  newResource.setImageTypeSet(imageTypeSet);
                  newResource.setImageDuration(imageDuration);
                  newResource.setVideoTypeSet(videoTypeSet);
                  newResource.setVideoDuration(videoDuration);
                  newResource.setDefaultContentName(defaultContentName);
                  newResource.setDefaultContentFileId(defaultContentFileId);
               }

               return newResource;
            }
         }
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_INVALID_PARAMS);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2AdsContentSettingResource getAdsContent(String contentId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      Content content = contentInfo.getContentAndFileActiveVerInfo(contentId);
      V2AdsContentSettingResource resource = new V2AdsContentSettingResource();
      Map adsSettings = contentInfo.getAdsContentActiveVersionInfo(contentId);
      if (content != null && adsSettings != null) {
         resource.setContentId(contentId);
         resource.setGroupId(Long.valueOf(content.getGroup_id()).toString());
         resource.setAdsContentName(content.getContent_name());
         resource.setPublisherId((String)adsSettings.get("publisher_id"));
         resource.setPublisherName((String)adsSettings.get("publisher_name"));
         resource.setApiKey(new String(Base64.decode((String)adsSettings.get("api_key"))));
         resource.setApiSecret(new String(Base64.decode((String)adsSettings.get("api_key_secret"))));
         resource.setAdUnitId((String)adsSettings.get("ad_unit_id"));
         resource.setImageTypeSet((String)adsSettings.get("image_type_set"));
         resource.setImageDuration(String.valueOf(adsSettings.get("image_duration")));
         resource.setVideoTypeSet((String)adsSettings.get("video_type_set"));
         resource.setVideoDuration(String.valueOf(adsSettings.get("video_duration")));
         resource.setDefaultContentName((String)adsSettings.get("default_content"));
         resource.setDefaultContentFileId((String)adsSettings.get("default_content_file_id"));
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2AdsContentSuggestionResource getAdsContentSuggestionInfo() throws Exception {
      V2AdsContentSuggestionResource newResource = new V2AdsContentSuggestionResource();
      String userId = SecurityUtils.getLoginUserId();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      List adUnitIdList = null;
      List publisherInfoList = null;

      try {
         adUnitIdList = contentInfo.getAdsContentAdUnitIdSuggestionListByUser(userId, 5);
         newResource.setAdUnitIdList(adUnitIdList);
      } catch (Exception var8) {
         this.logger.error(var8);
      }

      try {
         publisherInfoList = contentInfo.getAdsContentPublisherInfoSuggestionListByUser(userId, 5);
         newResource.setPublisherInfoList(publisherInfoList);
      } catch (Exception var7) {
         this.logger.error(var7);
      }

      if (adUnitIdList == null && publisherInfoList == null) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      } else {
         return newResource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2CommonResultResource deleteAdsContentSuggestionInfo(String suggestionType, String suggestionId) throws Exception {
      String userId = SecurityUtils.getLoginUserId();
      List deleteFailIds = new ArrayList();
      List deleteSuccessIds = new ArrayList();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      V2CommonResultResource commonResultResource = new V2CommonResultResource();

      try {
         if ("PUBLISHER_INFO_LIST".equals(suggestionType)) {
            if (contentInfo.deletePublisherInfoById(suggestionId, userId) > 0) {
               deleteSuccessIds.add(suggestionId);
            } else {
               deleteFailIds.add(suggestionId);
            }
         } else if ("AD_UNIT_ID_LIST".equals(suggestionType)) {
            if (contentInfo.deleteAdUnitIdInfoById(suggestionId, userId) > 0) {
               deleteSuccessIds.add(suggestionId);
            } else {
               deleteFailIds.add(suggestionId);
            }
         }

         commonResultResource.setFailList(deleteFailIds);
         commonResultResource.setSuccessList(deleteSuccessIds);
         return commonResultResource;
      } catch (Exception var9) {
         this.logger.error(var9);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2UrlContentSettingResource createUrlContent(V2UrlContentSettingResource resource) throws Exception {
      if (resource.getType().isEmpty()) {
         resource.setType("URL");
      }

      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, SecurityUtils.getLoginUserId());
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      V2UrlContentSettingResource newResource = new V2UrlContentSettingResource();
      Long rootGroupId = cInfo.getRootId(this.getLoginUserId());
      String webContentName = resource.getUrlContentName();
      String webContentUrl = resource.getUrlAddress();
      String refreshInterval = resource.getRefreshInterval();
      String group_id = resource.getGroupId();
      String contentId = UUID.randomUUID().toString().toUpperCase();
      String lfdFileId = UUID.randomUUID().toString().toUpperCase();
      if (!StringUtils.isEmpty(webContentUrl)) {
         webContentUrl = webContentUrl.replaceAll("&amp;", "&");
      }

      if (group_id.equals("0")) {
         group_id = rootGroupId.toString();
      }

      RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(group_id));
      Map data = new HashMap();
      data.put("webUrl", webContentUrl);
      data.put("lfdFileId", lfdFileId);
      data.put("refreshInterval", refreshInterval);
      String CONTENTS_HOME = null;

      try {
         CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
      } catch (ConfigException var20) {
         this.logger.error(var20);
      }

      File file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separator + lfdFileId, (String)null));
      if (!file.exists()) {
         file.mkdir();
      }

      String deviceType = "";
      float deviceTypeVersion = 0.0F;
      if (ContentUtils.createWebContent(SecurityUtils.getLoginUserId(), deviceType, deviceTypeVersion, CONTENTS_HOME + File.separator + lfdFileId, data, "URL", contentId, webContentName, (String)null) == 1) {
         try {
            List contentNotiDataList = new ArrayList();
            contentNotiDataList.add(this.makeNotificationData(contentId));
            if (contentNotiDataList.size() > 0) {
               MailUtil.sendContentEventMail(contentNotiDataList, "Add content");
            }
         } catch (Exception var19) {
            this.logger.error(var19);
         }

         Content content = cInfo.getContentAndFileActiveVerInfo(contentId);
         if (content != null) {
            newResource.setContentId(contentId);
            newResource.setType(content.getMedia_type());
            newResource.setGroupId(String.valueOf(content.getGroup_id()));
            newResource.setUserId(content.getCreator_id());
            newResource.setUrlContentName(content.getContent_name());
            newResource.setUrlAddress(content.getUrl_address());
            if (content.getMedia_type().equalsIgnoreCase("STRM")) {
               List settings = cInfo.getUrlContentSettingByContentId(contentId);
               if (settings.size() > 0) {
                  Map setting = (Map)settings.get(0);
                  newResource.setUrlAddress((String)setting.get("url"));
               }
            }
         }
      }

      return newResource;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2UrlContentSettingResource getUrlContent(String contentId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      V2UrlContentSettingResource resource = new V2UrlContentSettingResource();
      Content content = contentInfo.getContentAndFileActiveVerInfo(contentId);
      if (content != null) {
         resource.setContentId(contentId);
         resource.setType(content.getMedia_type());
         resource.setGroupId(String.valueOf(content.getGroup_id()));
         resource.setUserId(content.getCreator_id());
         resource.setUrlContentName(content.getContent_name());
         resource.setUrlAddress(content.getUrl_address());
         String deviceType;
         float deviceTypeVersion;
         if (content.getDevice_type() != null && !content.getDevice_type().equals("")) {
            deviceType = content.getDevice_type();
            deviceTypeVersion = content.getDevice_type_version();
         } else {
            Map typeMap = ContentUtils.getContentDeviceTypeAndVersion(content);
            deviceType = (String)typeMap.get("deviceType");
            deviceTypeVersion = Float.parseFloat(typeMap.get("deviceTypeVersion").toString());
         }

         resource.setDeviceType(deviceType);
         resource.setDeviceTypeVersion(deviceTypeVersion);
         resource.setRefreshInterval(content.getRefresh_interval());
         if (content.getMedia_type().equalsIgnoreCase("STRM")) {
            List settings = contentInfo.getUrlContentSettingByContentId(contentId);
            if (settings.size() > 0) {
               Map setting = (Map)settings.get(0);
               resource.setUrlAddress((String)setting.get("url"));
            }
         }
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Upload Authority', 'Content Manage Authority')")
   public V2UrlContentSettingResource updateUrlContent(String contentId, V2UrlContentSettingResource resource) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, SecurityUtils.getLoginUserId());
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      Content oldContent = cInfo.getContentAndFileActiveVerInfo(contentId);
      if (oldContent == null) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"contentId"});
      } else {
         Long rootGroupId = cInfo.getRootId(this.getLoginUserId());
         String webContentName = resource.getUrlContentName().isEmpty() ? oldContent.getContent_name() : resource.getUrlContentName();
         String webContentUrl = resource.getUrlAddress().isEmpty() ? oldContent.getUrl_address() : resource.getUrlAddress();
         String refreshInterval = resource.getRefreshInterval().isEmpty() ? oldContent.getRefresh_interval() : resource.getRefreshInterval();
         String group_id = resource.getGroupId();
         String lfdFileId = UUID.randomUUID().toString().toUpperCase();
         if (!StringUtils.isEmpty(webContentUrl)) {
            webContentUrl = webContentUrl.replaceAll("&amp;", "&");
         }

         if (group_id.equals("0")) {
            group_id = rootGroupId.toString();
         }

         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(group_id));
         Map data = new HashMap();
         data.put("mode", "UPDATE");
         data.put("webUrl", webContentUrl);
         data.put("lfdFileId", lfdFileId);
         data.put("refreshInterval", refreshInterval);
         String CONTENTS_HOME = null;

         try {
            CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
         } catch (ConfigException var21) {
            this.logger.error(var21);
         }

         File file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separator + lfdFileId, (String)null));
         if (!file.exists()) {
            file.mkdir();
         }

         String deviceType = "";
         float deviceTypeVersion = 0.0F;
         V2UrlContentSettingResource newResource = new V2UrlContentSettingResource();
         if (ContentUtils.createWebContent(SecurityUtils.getLoginUserId(), deviceType, deviceTypeVersion, CONTENTS_HOME + File.separator + lfdFileId, data, "URL", contentId, webContentName, (String)null) == 1) {
            try {
               List contentNotiDataList = new ArrayList();
               contentNotiDataList.add(this.makeNotificationData(contentId));
               if (contentNotiDataList.size() > 0) {
                  MailUtil.sendContentEventMail(contentNotiDataList, "Edit content");
               }
            } catch (Exception var20) {
               this.logger.error(var20);
            }

            Content newContent = cInfo.getContentActiveVerInfo(contentId);
            newResource.setContentId(contentId);
            newResource.setType(newContent.getMedia_type());
            newResource.setGroupId(String.valueOf(newContent.getGroup_id()));
            newResource.setUserId(newContent.getCreator_id());
            newResource.setUrlContentName(newContent.getContent_name());
            newResource.setUrlAddress(newContent.getUrl_address());
            newResource.setContentMetaData(newContent.getContent_meta_data());
            newResource.setRefreshInterval(newContent.getRefresh_interval());
            if (newContent.getMedia_type().equalsIgnoreCase("STRM")) {
               List settings = cInfo.getUrlContentSettingByContentId(contentId);
               if (settings.size() > 0) {
                  Map setting = (Map)settings.get(0);
                  newResource.setUrlAddress((String)setting.get("url"));
               }
            }
         }

         return newResource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Upload Authority', 'Content Manage Authority')")
   public V2UrlContentSettingResource createStrmContent(V2UrlContentSettingResource resource) throws Exception {
      if (resource.getType().isEmpty()) {
         resource.setType("STRM");
      }

      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, SecurityUtils.getLoginUserId());
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      Long rootGroupId = cInfo.getRootId(this.getLoginUserId());
      String urlContentName = resource.getUrlContentName();
      String urlAddress = resource.getUrlAddress();
      String user_id = resource.getUserId();
      String group_id = resource.getGroupId();
      String miUserId = "";
      String contentId = "";
      boolean isValid = true;
      miUserId = user_id;
      contentId = UUID.randomUUID().toString();
      if (user_id == null || user_id != null && user_id.isEmpty()) {
         miUserId = this.getLoginUserId();
      }

      if (group_id.equals("0")) {
         group_id = rootGroupId.toString();
      }

      RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(group_id));
      String successMessage = "The download operation has been started in background.";
      String defaultErrorMessage = "Failed to connect to the Remote Server.";
      String invalidParamsMessage = "One or more parameter(s) are invalid in Remote Content.";
      if (urlContentName.equalsIgnoreCase("") || urlAddress.equalsIgnoreCase("")) {
         isValid = false;
      }

      if (isValid) {
         UrlFilesToDownload urlFilesToDownload = UrlFilesToDownload.getInstance();
         urlFilesToDownload.getUrlFiles(miUserId, Long.parseLong(group_id), contentId, urlContentName, urlAddress, "");
      }

      try {
         List contentNotiDataList = new ArrayList();
         contentNotiDataList.add(this.makeNotificationData(contentId));
         if (contentNotiDataList.size() > 0) {
            MailUtil.sendContentEventMail(contentNotiDataList, "Add content");
         }
      } catch (Exception var20) {
         this.logger.error(var20);
      }

      V2UrlContentSettingResource newResource = new V2UrlContentSettingResource();
      Content newContent = cInfo.getContentActiveVerInfo(contentId);
      newResource.setContentId(contentId);
      newResource.setType(newContent.getMedia_type());
      newResource.setGroupId(String.valueOf(newContent.getGroup_id()));
      newResource.setUserId(newContent.getCreator_id());
      newResource.setUrlContentName(newContent.getContent_name());
      newResource.setUrlAddress(newContent.getUrl_address());
      newResource.setContentMetaData(newContent.getContent_meta_data());
      newResource.setRefreshInterval(newContent.getRefresh_interval());
      if (newContent.getMedia_type().equalsIgnoreCase("STRM")) {
         List settings = cInfo.getUrlContentSettingByContentId(contentId);
         if (settings.size() > 0) {
            Map setting = (Map)settings.get(0);
            newResource.setUrlAddress((String)setting.get("url"));
         }
      }

      return newResource;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2UrlContentSettingResource getStrmContent(String contentId) throws Exception {
      return this.getUrlContent(contentId);
   }

   @PreAuthorize("hasAnyAuthority('Content Upload Authority', 'Content Manage Authority')")
   public V2UrlContentSettingResource updateStrmContent(String contentId, V2UrlContentSettingResource resource) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, SecurityUtils.getLoginUserId());
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      String orgName = SecurityUtils.getUserContainer().getUser().getOrganization();
      long orgId = userGroupInfo.getOrgGroupIdByName(orgName);
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
      boolean contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
      Content tmpContent = cInfo.getContentActiveVerInfo(contentId);
      String contentName = resource.getUrlContentName();
      String contentMetaData = resource.getContentMetaData();
      String strGroupID = resource.getGroupId();
      String strVersionID = "";
      String content_type = resource.getType();
      String strmAddress = resource.getUrlAddress();
      String userId = resource.getUserId();
      if (userId == null || userId.isEmpty()) {
         userId = this.getLoginUserId();
      }

      String expirationDate = tmpContent.getExpiration_date();
      PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();
      LogInfo logInfo = LogInfoImpl.getInstance();
      ContentLog cLog = new ContentLog();
      cLog.setUser_id(this.getLoginUserId());
      PlaylistLog pLog = new PlaylistLog();
      pLog.setUser_id(this.getLoginUserId());
      Long groupID = 0L;
      int shareFlag = tmpContent.getShare_flag();
      if (!strGroupID.isEmpty()) {
         groupID = new Long(strGroupID);
      }

      Long versionID = cInfo.getVersionInfoByContentId(contentId);
      Long rootGroupId = cInfo.getRootId(this.getLoginUserId());
      if (groupID == 0L) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, rootGroupId);
      } else {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, groupID);
      }

      if (content_type.equalsIgnoreCase("STRM")) {
         cInfo.updateUrlSetting(contentId, contentName, strmAddress);
         UrlFilesToDownload urlFilesToDownload = UrlFilesToDownload.getInstance();
         urlFilesToDownload.getUrlFiles(userId, groupID, contentId, contentName, strmAddress, "");
      }

      if (contentId.length() > 0) {
         boolean ret = false;
         if ((contentName.length() > 0 || contentMetaData.length() > 0) && cInfo.setContentInfo(contentId, contentName, contentMetaData, shareFlag) > 0) {
            ret = true;
         }

         if (strGroupID.length() > 0 && cInfo.setContentGroup(contentId, groupID) > 0) {
            ret = true;
         }

         if (StrUtils.nvl(CommonConfig.get("contentApproval.enable")).equalsIgnoreCase("TRUE")) {
            if (cInfo.setActiveVersion(contentId, versionID, false) > 0) {
               ret = true;
            }
         } else if (cInfo.setActiveVersion(contentId, versionID, true) > 0) {
            ret = true;
         }

         Long oldVersion = 0L;
         if (!contentsApprovalEnable) {
            Content content = cInfo.getContentActiveVerInfo(contentId);
            String contentDuration = content.getPlay_time();
            oldVersion = cInfo.getContentNextVer(content.getContent_id()) - 1L;
            Content oldContent = cInfo.getContentVerInfo(content.getContent_id(), oldVersion);
            String oldContentDuration = "0";
            if (oldContent != null) {
               oldContentDuration = oldContent.getPlay_time();
            }

            boolean existPlaytime = true;
            if (contentDuration == null || contentDuration.equals("") || contentDuration.equals("-")) {
               if (oldContentDuration == null || oldContentDuration.equals("") || oldContentDuration.equals("-")) {
                  existPlaytime = false;
               }

               contentDuration = "00:00:30";
            }

            String[] tokens = contentDuration.split(":");
            int hours = Integer.parseInt(tokens[0]);
            int minutes = Integer.parseInt(tokens[1]);
            int seconds = Integer.parseInt(tokens[2]);
            int duration = 3600 * hours + 60 * minutes + seconds;
            if (existPlaytime) {
               pInfo.setContentDuraionByContentID(content.getContent_id(), (long)duration);
            }

            List pIDList = cInfo.getPlaylistListUsingContent(content.getContent_id());
            if (pIDList != null && pIDList.size() != 0) {
               String pId = null;
               Map map = null;
               List pList = null;
               Long playTime = null;
               String hms = null;

               for(int j = 0; j < pIDList.size(); ++j) {
                  map = (Map)pIDList.get(j);
                  pId = (String)map.get("PLAYLIST_ID");
                  pList = pInfo.getPlaylistAllVerInfo(pId);
                  if (pList != null) {
                     Iterator var47 = pList.iterator();

                     while(var47.hasNext()) {
                        Playlist p = (Playlist)var47.next();
                        playTime = pInfo.getSumOfContentDuration(p.getPlaylist_id(), p.getVersion_id());
                        if (playTime != null && existPlaytime) {
                           pInfo.setPlaytime(p.getPlaylist_id(), p.getVersion_id(), ContentUtils.getPlayTimeFormattedStr(playTime));
                        }
                     }
                  }
               }
            }

            contentDuration = content.getPlay_time();
            if (contentDuration == null || contentDuration.equals("") || contentDuration.equals("-")) {
               contentDuration = "00:00:05";
            }

            tokens = contentDuration.split(":");
            hours = Integer.parseInt(tokens[0]);
            minutes = Integer.parseInt(tokens[1]);
            seconds = Integer.parseInt(tokens[2]);
            duration = 3600 * hours + 60 * minutes + seconds;
         }
      }

      try {
         List contentNotiDataList = new ArrayList();
         contentNotiDataList.add(this.makeNotificationData(contentId));
         if (contentNotiDataList.size() > 0) {
            MailUtil.sendContentEventMail(contentNotiDataList, "Edit content");
         }
      } catch (Exception var49) {
         this.logger.error(var49);
      }

      if (expirationDate.length() == 8) {
         cInfo.setExpirationDate(contentId, expirationDate);
         V2UrlContentSettingResource newResource = new V2UrlContentSettingResource();
         Content newContent = cInfo.getContentActiveVerInfo(contentId);
         newResource.setContentId(contentId);
         newResource.setType(newContent.getMedia_type());
         newResource.setGroupId(String.valueOf(newContent.getGroup_id()));
         newResource.setUserId(newContent.getCreator_id());
         newResource.setUrlContentName(newContent.getContent_name());
         newResource.setUrlAddress(newContent.getUrl_address());
         newResource.setContentMetaData(newContent.getContent_meta_data());
         newResource.setRefreshInterval(newContent.getRefresh_interval());
         if (newContent.getMedia_type().equalsIgnoreCase("STRM")) {
            List settings = cInfo.getUrlContentSettingByContentId(contentId);
            if (settings.size() > 0) {
               Map setting = (Map)settings.get(0);
               newResource.setUrlAddress((String)setting.get("url"));
            }
         }

         return newResource;
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"contentId and expirationDate"});
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2CommonResultResource changeGroup(V2ContentIds body, String groupId) throws Exception {
      V2CommonResultResource resourceList = new V2CommonResultResource();
      if (!StrUtils.nvl(groupId).equals("")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(groupId));
      }

      boolean flag = false;
      List contentIds = body.getContentIds();

      for(int i = 0; i < contentIds.size(); ++i) {
         try {
            ContentInfo cInfo = ContentInfoImpl.getInstance();
            String mediaType = cInfo.getMediaTypeByContentId((String)contentIds.get(i));
            if (mediaType.equalsIgnoreCase("TLFD")) {
               RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.TLFD, (String)contentIds.get(i));
            } else {
               RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, (String)contentIds.get(i));
            }
         } catch (Exception var19) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
      }

      List successResourceList = new ArrayList();
      List failResourceList = new ArrayList();

      for(int i = 0; i < contentIds.size(); ++i) {
         try {
            ContentInfo cInfo = ContentInfoImpl.getInstance();
            SessionInfo sessionInfo = SessionInfoImpl.getInstance();
            LogInfo logInfo = LogInfoImpl.getInstance();
            ContentLog cLog = new ContentLog();
            sessionInfo.setUnlockBySessionID(UUID.randomUUID().toString());
            ShareFolderInfo shareFolderInfo = ShareFolderInfoImpl.getInstance();
            String mediaType = cInfo.getMediaTypeByContentId((String)contentIds.get(i));
            if (groupId != null && !groupId.isEmpty()) {
               shareFolderInfo.updatePositionInfoOfContentInShareFolderOrNot((String)contentIds.get(i), "N");
               boolean isMoveGroup;
               if (mediaType.equalsIgnoreCase("TLFD")) {
                  isMoveGroup = cInfo.setTLFDGroup((String)contentIds.get(i), new Long(groupId)) > 0;
               } else {
                  isMoveGroup = cInfo.setContentGroup((String)contentIds.get(i), new Long(groupId)) > 0;
               }

               V2ContentResource resource;
               if (isMoveGroup) {
                  try {
                     List notifications = new ArrayList();
                     notifications.add(this.makeNotificationData(cLog.getContent_id()));
                     if (notifications.size() > 0) {
                        MailUtil.sendContentEventMail(notifications, "Move content");
                     }
                  } catch (Exception var17) {
                     this.logger.error(var17);
                  }

                  resource = this.getContentDetail((String)contentIds.get(i));
                  if (resource != null) {
                     successResourceList.add(resource);
                  }
               } else {
                  resource = this.getContentDetail((String)contentIds.get(i));
                  if (resource != null) {
                     failResourceList.add(resource);
                  }
               }
            } else {
               V2ContentResource resource = this.getContentDetail((String)contentIds.get(i));
               if (resource != null) {
                  failResourceList.add(resource);
               }
            }
         } catch (Exception var18) {
            this.logger.error(var18);
            V2ContentResource resource = this.getContentDetail((String)contentIds.get(i));
            if (resource != null) {
               failResourceList.add(resource);
            }
         }
      }

      resourceList.setSuccessList(successResourceList);
      resourceList.setFailList(failResourceList);
      return resourceList;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2CommonResultResource contentRestore(V2ContentIds body) throws Exception {
      boolean flag = false;
      List contentIds = body.getContentIds();

      for(int i = 0; i < contentIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, (String)contentIds.get(i));
         } catch (Exception var16) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
      }

      ContentInfo cInfo = ContentInfoImpl.getInstance();
      LogInfo logInfo = LogInfoImpl.getInstance();
      V2CommonResultResource resource = new V2CommonResultResource();
      List successResourceList = new ArrayList();
      List failResourceList = new ArrayList();

      for(int i = 0; i < contentIds.size(); ++i) {
         ContentLog cLog = new ContentLog();
         if (cInfo.restoreContent((String)contentIds.get(i)) > 0) {
            try {
               List notifications = new ArrayList();
               notifications.add(this.makeNotificationData(cLog.getContent_id()));
               if (notifications.size() > 0) {
                  MailUtil.sendContentEventMail(notifications, "Restore content");
               }
            } catch (Exception var15) {
               this.logger.error(var15);
            }

            try {
               List adsList = cInfo.getAdsContentSettingByContentId((String)contentIds.get(i));
               List ftpList = cInfo.getFtpContentSettingByContentId((String)contentIds.get(i));
               List cifsList = cInfo.getCifsContentSettingByContentId((String)contentIds.get(i));
               if (adsList != null && adsList.size() > 0) {
                  cInfo.updateAdsSettingAsDeleted((String)contentIds.get(i), "N");
               }

               if (ftpList != null && ftpList.size() > 0) {
                  cInfo.updateFtpSettingAsDeleted((String)contentIds.get(i), "N");
               }

               if (cifsList != null && cifsList.size() > 0) {
                  cInfo.updateCifsSettingAsDeleted((String)contentIds.get(i), "N");
               }
            } catch (Exception var14) {
               this.logger.error(var14);
               failResourceList.add(contentIds.get(i));
            }

            successResourceList.add(contentIds.get(i));
         } else {
            failResourceList.add(contentIds.get(i));
         }
      }

      resource.setSuccessList(successResourceList);
      resource.setFailList(failResourceList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2CommonResultResource updateCategory(V2ContentCategoryResourceRequestWrapper categories) throws Exception {
      CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
      List successList = new ArrayList();
      List failList = new ArrayList();
      V2CommonResultResource resource = new V2CommonResultResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String organization = userContainer.getUser().getOrganization();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      userGroupInfo.getOrgGroupIdByName(organization);
      boolean isManualCategoryEnabled = categoryInfo.isManualCategoryEnabled(CommonDataConstants.ROOT_ID);
      boolean flag = false;
      Iterator var13 = categories.getContentIds().iterator();

      String contentId;
      while(var13.hasNext()) {
         contentId = (String)var13.next();
         if (!this.isContentInSharedFolder(contentId)) {
            try {
               RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
            } catch (Exception var20) {
               flag = true;
               break;
            }
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
      }

      flag = false;
      if (!isManualCategoryEnabled) {
         var13 = categories.getCategoryIds().iterator();

         while(var13.hasNext()) {
            contentId = (String)var13.next();

            try {
               RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CATEGORY, Long.valueOf(contentId));
            } catch (Exception var19) {
               flag = true;
               break;
            }
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CATEGORY);
      }

      var13 = categories.getContentIds().iterator();

      while(var13.hasNext()) {
         contentId = (String)var13.next();

         try {
            categoryInfo.deleteCategoryFromContentId(contentId);
            Iterator var15 = categories.getCategoryIds().iterator();

            while(var15.hasNext()) {
               String categoryId = (String)var15.next();
               categoryInfo.setCategoryFromContentId(categoryId, contentId);
            }

            try {
               List notifications = new ArrayList();
               notifications.add(this.makeNotificationData(contentId));
               if (notifications.size() > 0) {
                  MailUtil.sendContentEventMail(notifications, "Edit content");
               }
            } catch (Exception var17) {
               this.logger.error(var17);
            }

            ContentUtils.updateLastModifiedDate(contentId);
         } catch (Exception var18) {
            this.logger.error(var18);
            failList.add(contentId);
         }

         V2ContentResource contentResource = this.getContentDetail(contentId);
         if (contentResource != null) {
            successList.add(contentResource);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2PageResource updateCategory(V2ContentIds body, String categoryId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CATEGORY, Long.valueOf(categoryId));
      Iterator var3 = body.getContentIds().iterator();

      while(var3.hasNext()) {
         String contentId = (String)var3.next();
         if (!this.isContentInSharedFolder(contentId)) {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
         }
      }

      List notifications = new ArrayList();
      CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
      List resources = new ArrayList();
      Iterator var6 = body.getContentIds().iterator();

      while(var6.hasNext()) {
         String contentId = (String)var6.next();
         categoryInfo.deleteCategoryFromContentId(contentId);
         categoryInfo.setCategoryFromContentId(categoryId, contentId);
         V2ContentResource resource = this.getContentDetail(contentId);
         if (resource != null) {
            resources.add(resource);
         }

         try {
            notifications.add(this.makeNotificationData(contentId));
         } catch (Exception var10) {
            this.logger.error(var10);
         }
      }

      if (notifications.size() > 0) {
         MailUtil.sendContentEventMail(notifications, "Edit content");
      }

      return V2PageResource.createPageResource(resources, resources.size());
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public List checkAuthorityToMoveContent(V2ContentIds resource) throws Exception {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      List contentIds = resource.getContentIds();
      boolean flag = false;
      boolean canEditGroup = true;
      Iterator var6 = contentIds.iterator();

      while(var6.hasNext()) {
         String contentId = (String)var6.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
            Content content = cInfo.getContentActiveVerInfo(contentId);
            if (content != null && !content.getCreator_id().equalsIgnoreCase(this.getLoginUserId())) {
               canEditGroup = false;
               break;
            }
         } catch (Exception var11) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
      }

      if (!canEditGroup) {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_CANNOT_MOVE_GROUP_CONTENTS_OF_ANOTHER_USER);
      } else {
         List list = new ArrayList();

         for(int i = 0; i < contentIds.size(); ++i) {
            boolean hasAuthorityToMove = true;
            Content content = cInfo.getContentActiveVerInfo((String)contentIds.get(i));
            if (content != null && !content.getCreator_id().equalsIgnoreCase(this.getLoginUserId())) {
               hasAuthorityToMove = false;
            }

            V2AuthorityToMoveContent result = new V2AuthorityToMoveContent();
            result.setContentId((String)contentIds.get(i));
            result.setHasAuthorityToMove(hasAuthorityToMove);
            list.add(result);
         }

         return list;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2ContentConvertibility checkConvertibility(String contentId) throws Exception {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      Content content = cInfo.getContentActiveVerInfo(contentId);
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, content.getGroup_id());
      V2ContentConvertibility convertibility = new V2ContentConvertibility();
      RestExceptionCode error;
      if (content.getShare_flag() == 0) {
         error = RestExceptionCode.BAD_REQUEST_FILE_CHECK_FOR_TEMPLATES;
         convertibility.setConvertible(false);
         convertibility.setReason(error.generateFormattedMessages("shared"));
         convertibility.setReasonCode(error.getCode());
      } else if (!content.getMedia_type().equals("LFD")) {
         error = RestExceptionCode.BAD_REQUEST_CANNOT_CONVERT_NOT_LFD_TYPE;
         convertibility.setConvertible(false);
         convertibility.setReason(error.getMessage());
         convertibility.setReasonCode(error.getCode());
      } else if ((cInfo.getContentApproverListByContentId(contentId).size() == 0 || content.getApproval_status().equalsIgnoreCase("APPROVED")) && !StringUtils.equals(content.getApproval_status(), "REJECTED")) {
         convertibility.setConvertible(true);
         convertibility.setReason("Success");
      } else {
         error = RestExceptionCode.BAD_REQUEST_FILE_CHECK_FOR_NOT_APPROVED_CONTENT;
         convertibility.setConvertible(false);
         convertibility.setReason(error.generateFormattedMessages("approved"));
         convertibility.setReasonCode(error.getCode());
      }

      return convertibility;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2ContentConvertibilityList checkConvertibility(V2ContentConvertibilityList list) throws Exception {
      ContentInfo cInfo = ContentInfoImpl.getInstance();

      for(int i = 0; i < list.getConvertibilityList().size(); ++i) {
         String contentId = ((V2ContentConvertibility)list.getConvertibilityList().get(i)).getContentId();
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
         Content content = cInfo.getContentActiveVerInfo(contentId);
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, content.getGroup_id());
         boolean isConvertible = false;
         String reason = "";
         String reasonCode = "";
         RestExceptionCode error;
         if (content.getShare_flag() == 0) {
            error = RestExceptionCode.BAD_REQUEST_FILE_CHECK_FOR_TEMPLATES;
            isConvertible = false;
            reason = error.generateFormattedMessages("shared");
            reasonCode = error.getCode();
         } else if (!content.getMedia_type().equals("LFD")) {
            error = RestExceptionCode.BAD_REQUEST_CANNOT_CONVERT_NOT_LFD_TYPE;
            isConvertible = false;
            reason = error.getMessage();
            reasonCode = error.getCode();
         } else if (cInfo.getContentApproverListByContentId(contentId).size() != 0) {
            error = RestExceptionCode.BAD_REQUEST_FILE_CHECK_FOR_TEMPLATES;
            isConvertible = false;
            reason = error.generateFormattedMessages("approved");
            reasonCode = error.getCode();
         } else {
            isConvertible = true;
            reason = "Success";
         }

         ((V2ContentConvertibility)list.getConvertibilityList().get(i)).setConvertible(isConvertible);
         ((V2ContentConvertibility)list.getConvertibilityList().get(i)).setReason(reason);
         ((V2ContentConvertibility)list.getConvertibilityList().get(i)).setReasonCode(reasonCode);
      }

      return list;
   }

   @PreAuthorize("hasAnyAuthority('Content Upload Authority', 'Content Manage Authority')")
   public V2ContentFileResource uploadContentFile(MultipartFile[] files, String groupId, String updatedContentId, String contentType, String webContentName, String startPage, String refreshInterval, String mode, List categoryIds, HttpServletRequest request) throws Exception {
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      UserInfo uInfo = UserInfoImpl.getInstance();
      if (!StrUtils.nvl(updatedContentId).equals("")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, updatedContentId);
      }

      String userId = SecurityUtils.getLoginUserId();
      if (webContentName != null) {
         webContentName = URLDecoder.decode(webContentName, "UTF-8");
      }

      boolean contentUpdate = false;
      if (updatedContentId != null && !updatedContentId.equals("")) {
         contentUpdate = true;
      }

      long orgCreatorId;
      try {
         orgCreatorId = cmsDao.getRootId(userId);
      } catch (SQLException var169) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"user information"});
      }

      if (groupId == null || groupId.equals("") || groupId.equals("0") || groupId.equals("null")) {
         groupId = String.valueOf(orgCreatorId);
      }

      RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(groupId));
      if (!ServletFileUpload.isMultipartContent(request)) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"multipart content"});
      } else {
         String CONTENTS_HOME = null;
         String THUMBNAIL_HOME = null;

         try {
            CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
            THUMBNAIL_HOME = CommonConfig.get("THUMBNAIL_HOME").replace('/', File.separatorChar);
         } catch (ConfigException var168) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"CONTENTS_HOME or THUMBNAIL_HOME"});
         }

         File cmsHome = SecurityUtils.getSafeFile(CONTENTS_HOME);
         if (!cmsHome.exists()) {
            cmsHome.mkdir();
         }

         V2ContentFileResource resource = new V2ContentFileResource();
         ContentCodeInfo codeDao = ContentCodeInfoImpl.getInstance();
         FileManager fileManager = FileManagerImpl.getInstance();
         boolean bExistFile = false;
         boolean bMustAddContent = false;
         List doNotSupportContentList = new ArrayList();
         boolean contentsApprovalEnable = false;

         try {
            long orgId = uInfo.getRootGroupIdByUserId(userId);
            ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
            Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
            contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
         } catch (Exception var167) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"server setup info"});
         }

         try {
            boolean isStreaming = false;
            String fext;
            String contentName;
            String contentId;
            FileOutputStream os;
            String strmUrl;
            ContentFile cmsThumbFile;
            String fileName;
            File parentFile;
            if ("SAPP".equalsIgnoreCase(contentType)) {
               File wgtFile = null;
               File configFile = null;
               fileName = null;
               fext = null;
               contentName = null;
               int success = 0;
               Map data = new HashMap();
               if (files != null && files.length > 1) {
                  int code = 0;

                  while(true) {
                     if (code >= files.length) {
                        if (success != 2) {
                           throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_FILE_UPLOAD_FAIL, new String[]{"sssp content"});
                        }

                        code = 0;

                        try {
                           data.put("refreshInterval", refreshInterval);
                           data.put("fileSize", wgtFile.length());
                           data.put("configFilePath", configFile.getPath());
                           data.put("configFileTempField2", fext);
                           if (mode != null && mode.equalsIgnoreCase("update")) {
                              data.put("mode", "UPDATE");
                           }

                           if (contentUpdate) {
                              contentName = updatedContentId;
                           } else {
                              contentName = UUID.randomUUID().toString().toUpperCase();
                           }

                           code = ContentUtils.createWebContent(userId, "", 0.0F, wgtFile.getPath(), data, contentType, contentName, webContentName, fileName);
                           if (code != 1) {
                              throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONTENT_CREATE, new String[]{"SSSP Web App"});
                           }
                        } catch (Exception var164) {
                           this.logger.error("", var164);
                        }

                        resource.setSsspWgtFileId(fileName);
                        resource.setSsspConfigFileID(fext);
                        resource.setContentId(contentName);
                        resource.setFileSize(wgtFile.length());
                        resource.setIsUploadCompleted(code == 1);
                        resource.setIsStreaming(isStreaming);

                        try {
                           File parentFile;
                           if (wgtFile.exists()) {
                              wgtFile.delete();
                              parentFile = wgtFile.getParentFile();
                              parentFile.delete();
                           }

                           if (configFile.exists()) {
                              configFile.delete();
                              parentFile = configFile.getParentFile();
                              parentFile.delete();
                           }

                           this.logger.info("[MagicInfo_UploadSAppContent] delete temp Folder");
                        } catch (Exception var163) {
                           this.logger.error("[MagicInfo_UploadSAppContent] fail to delete temp Folder", var163);
                        }
                        break;
                     }

                     MultipartFile multipartFile = files[code];
                     synchronized(this) {
                        if (!multipartFile.isEmpty()) {
                           contentId = multipartFile.getOriginalFilename();
                           String fext = contentId.substring(contentId.lastIndexOf(".") + 1, contentId.length());
                           String contentName = contentId.substring(0, contentId.lastIndexOf("."));
                           os = null;
                           if (fext.equalsIgnoreCase("wgt") || contentId.toUpperCase().equals("SSSP_CONFIG.XML")) {
                              if (contentType == null || contentType.equals("") || !ContentUtils.supportContentType(fext)) {
                                 throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CONTENT_UPLOAD_NOT_SUPPORT_FILE);
                              }

                              long fileSize = multipartFile.getSize();
                              strmUrl = UUID.randomUUID().toString().toUpperCase();
                              parentFile = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separator + strmUrl + File.separator + contentId, (String)null));
                              parentFile.getParentFile().mkdir();
                              InputStream is = multipartFile.getInputStream();
                              FileOutputStream os = new FileOutputStream(parentFile.getAbsoluteFile());

                              try {
                                 IOUtils.copy(is, os);
                              } catch (Exception var165) {
                                 this.logger.error(var165);
                                 throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR);
                              } finally {
                                 os.close();
                                 is.close();
                              }

                              cmsThumbFile = null;
                              if (fext.equalsIgnoreCase("wgt")) {
                                 wgtFile = parentFile;
                                 fileName = strmUrl;
                                 ++success;
                              } else if (contentId.toUpperCase().equals("SSSP_CONFIG.XML")) {
                                 configFile = parentFile;
                                 fext = strmUrl;
                                 ++success;
                              }
                           }
                        }
                     }

                     ++code;
                  }
               }
            } else if (files != null && files.length > 0) {
               MultipartFile multipartFile = files[0];
               synchronized(this) {
                  if (!multipartFile.isEmpty()) {
                     fileName = multipartFile.getOriginalFilename();
                     fext = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
                     contentName = fileName.substring(0, fileName.lastIndexOf("."));
                     String mediaType = null;
                     boolean isEncrypted = false;
                     if (AISRUtils.isEncryptedFile(fileName)) {
                        fileName = fileName.substring(0, fileName.lastIndexOf("."));
                        fext = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
                        contentName = fileName.substring(0, fileName.lastIndexOf("."));
                        if (!AISRUtils.isAISRSupportedFile(fext)) {
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CONTENT_UPLOAD_NOT_SUPPORTED_BY_AISR, new String[]{"fext"});
                        }

                        isEncrypted = true;
                     }

                     if (contentType == null || !contentType.equals("HTML") && !ContentConstants.getMediaTypeForAuthor().contains(contentType)) {
                        mediaType = StrUtils.nvl(codeDao.getMediaTypeByFileType(fext.toUpperCase()));
                     } else {
                        mediaType = contentType;
                     }

                     if (mediaType == null || mediaType.equals("") || !ContentUtils.supportContentType(fext)) {
                        throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CONTENT_UPLOAD_NOT_SUPPORT_FILE);
                     }

                     long fileSize = multipartFile.getSize();
                     String fileID = UUID.randomUUID().toString().toUpperCase();
                     contentId = null;
                     if (contentUpdate) {
                        contentId = updatedContentId;
                     } else {
                        contentId = UUID.randomUUID().toString().toUpperCase();
                     }

                     File file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separator + fileID + File.separator + (isEncrypted ? multipartFile.getOriginalFilename() : fileName), (String)null));
                     file.getParentFile().mkdir();
                     InputStream is = multipartFile.getInputStream();
                     os = new FileOutputStream(file.getAbsoluteFile());

                     try {
                        IOUtils.copy(is, os);
                     } catch (Exception var161) {
                        this.logger.error(var161);
                        throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR);
                     } finally {
                        os.close();
                        is.close();
                     }

                     File thumbnailFileId;
                     if (isEncrypted) {
                        thumbnailFileId = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separator + fileID + File.separator + fileName, (String)null));
                        AISRUtils.aisrDecrypt(file, thumbnailFileId, contentType, true);
                        file = thumbnailFileId;
                     }

                     String thumbnailFileId = null;
                     if (contentType != null && !contentType.isEmpty() && contentType.equals("HTML")) {
                        if (fext.equalsIgnoreCase("zip")) {
                           Map data = new HashMap();
                           data.put("startPage", startPage);
                           data.put("refreshInterval", refreshInterval);
                           data.put("fileSize", fileSize);
                           if (mode != null && mode.equalsIgnoreCase("update")) {
                              data.put("mode", "UPDATE");
                           }

                           int code = ContentUtils.createWebContent(userId, "", 0.0F, file.getPath(), data, contentType, contentId, webContentName, fileID);
                           if (code != 1) {
                              throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONTENT_CREATE, new String[]{"WEB"});
                           }

                           file.delete();
                           parentFile = file.getParentFile();
                           parentFile.delete();
                           this.logger.info("[MagicInfo_UploadHtmlContent] delete temp Folder");
                           resource.setFileId(fileID);
                           resource.setContentId(contentId);
                           resource.setFileSize(fileSize);
                           resource.setIsUploadCompleted(code == 1);
                           resource.setIsStreaming(isStreaming);
                        }
                     } else {
                        String hashId = FileUtils.getHash(file);
                        strmUrl = null;
                        String play_time;
                        boolean fSuccess;
                        String thumb_url;
                        File thumbFile;
                        File thumb_File;
                        File thumb_smallFile;
                        if (contentUpdate) {
                           List fileListToSave = new ArrayList();
                           boolean createThumbnail = true;

                           try {
                              if (cmsDao.isExistFileByHash(fileName, fileSize, hashId)) {
                                 fileID = cmsDao.getFileIDByHash(fileName, fileSize, hashId);
                                 File f = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separatorChar + fileID + File.separatorChar + fileName, (String)null));
                                 if (!f.exists() && cmsDao.deleteFile(fileID) > 0) {
                                    this.logger.info("File information exists in DB, but no physical file exists.");
                                 }
                              }
                           } catch (Exception var160) {
                              this.logger.error(var160);
                           }

                           if (cmsDao.isExistFileByHash(fileName, fileSize, hashId)) {
                              bExistFile = true;
                           }

                           if (mediaType.equalsIgnoreCase("STRM")) {
                              BufferedReader br = null;

                              try {
                                 char[] c = new char[(int)file.length()];
                                 br = new BufferedReader(new FileReader(file));
                                 br.read(c);
                                 strmUrl = new String(c);
                              } catch (FileNotFoundException var157) {
                                 this.logger.error("", var157);
                                 throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"file"});
                              } catch (Exception var158) {
                                 this.logger.error("", var158);
                                 throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR);
                              } finally {
                                 try {
                                    br.close();
                                 } catch (IOException var151) {
                                    this.logger.error("", var151);
                                    throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR);
                                 }
                              }
                           }

                           if (bExistFile) {
                              file.delete();
                              fileID = cmsDao.getFileIDByHash(fileName, fileSize, hashId);
                              file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separatorChar + fileID + File.separatorChar + fileName, (String)null));
                           } else {
                              ContentFile cmsContentFile = new ContentFile();
                              if (!fext.equalsIgnoreCase("mpeg") && !fext.equalsIgnoreCase("mpg") && !fext.equalsIgnoreCase("wmv") && !fext.equalsIgnoreCase("avi") && !fext.equalsIgnoreCase("mov") && !fext.equalsIgnoreCase("mp4") && !fext.equalsIgnoreCase("asf")) {
                                 cmsContentFile.setIs_streaming("N");
                              } else {
                                 cmsContentFile.setIs_streaming("Y");
                                 isStreaming = true;
                              }

                              cmsContentFile.setFile_id(fileID);
                              cmsContentFile.setHash_code(hashId);
                              cmsContentFile.setFile_type("MAIN");
                              cmsContentFile.setFile_name(fileName);
                              cmsContentFile.setFile_size(fileSize);
                              cmsContentFile.setCreator_id(userId);
                              cmsContentFile.setFile_path(CONTENTS_HOME + File.separator + fileID);
                              cmsDao.addFile(cmsContentFile);
                              fileListToSave.add(cmsContentFile);
                           }

                           try {
                              long versionId = cmsDao.getMaxContentVersionId(contentId) + 1L;
                              long existGroupId = cmsDao.getGroupId(contentId);
                              Content content = new Content();
                              content.setMain_file_id(fileID);
                              content.setMain_file_Extension(fext.toUpperCase());
                              content.setContent_id(contentId);
                              content.setVersion_id(versionId);
                              content.setCreator_id(userId);
                              content.setMedia_type(mediaType);
                              content.setGroup_id(existGroupId);
                              content.setShare_flag(1);
                              content.setContent_meta_data("");
                              content.setOrganization_id(uInfo.getRootGroupIdByUserId(userId));
                              content.setTotal_size(fileSize);
                              content.setIs_active("N");
                              content.setOrg_creator_id(String.valueOf(orgCreatorId));
                              content.setIs_aisr(isEncrypted ? "Y" : "N");
                              play_time = null;
                              if (bExistFile) {
                                 play_time = cmsDao.getThumbIdByMainFileId(fileID);
                                 if (play_time != null) {
                                    ContentFile thumbFile = cmsDao.getFileInfo(play_time);
                                    content.setThumb_file_id(play_time);
                                    content.setThumb_file_name(thumbFile.getFile_name());
                                    createThumbnail = false;
                                 }
                              }

                              if (content.getMedia_type().equalsIgnoreCase("SOUND")) {
                                 content.setThumb_file_id("SOUND_THUMBNAIL");
                                 content.setThumb_file_name("SOUND_THUMBNAIL.PNG");
                                 String[] fileMeta;
                                 if (fext.equals("MP3")) {
                                    try {
                                       fileMeta = fileManager.getFileMeta(file);
                                       if (fileMeta[0] != null) {
                                          thumb_url = fileMeta[0];
                                          if (thumb_url.length() > 8) {
                                             content.setPlay_time(thumb_url.substring(0, 8));
                                             content.setPlay_time_milli(thumb_url.substring(9, thumb_url.length()));
                                          } else {
                                             content.setPlay_time(thumb_url);
                                          }
                                       } else {
                                          thumb_url = fileManager.getMP3PlayTime(file);
                                          if (thumb_url.length() > 8) {
                                             content.setPlay_time(thumb_url.substring(0, 8));
                                             content.setPlay_time_milli(thumb_url.substring(9, thumb_url.length()));
                                          } else {
                                             content.setPlay_time(thumb_url);
                                          }
                                       }
                                    } catch (Exception var156) {
                                       content.setPlay_time("");
                                    }
                                 } else {
                                    fileMeta = fileManager.getFileMeta(file);
                                    if (fileMeta[0] != null) {
                                       thumb_url = fileMeta[0];
                                       if (thumb_url.length() > 8) {
                                          content.setPlay_time(thumb_url.substring(0, 8));
                                          content.setPlay_time_milli(thumb_url.substring(9, thumb_url.length()));
                                       } else {
                                          content.setPlay_time(thumb_url);
                                       }
                                    } else {
                                       content.setPlay_time("");
                                    }
                                 }
                              } else if (!content.getMedia_type().equalsIgnoreCase("IMAGE") && !content.getMedia_type().equalsIgnoreCase("MOVIE")) {
                                 if (content.getMedia_type().equalsIgnoreCase("STRM")) {
                                    if (strmUrl != null) {
                                       cmsDao.updateUrlSetting(content.getContent_id(), content.getContent_name(), strmUrl);
                                    }

                                    content.setThumb_file_id("STRM_THUMBNAIL");
                                    content.setThumb_file_name("STRM_THUMBNAIL.PNG");
                                 } else if (content.getMedia_type().equalsIgnoreCase("OFFICE")) {
                                    content.setThumb_file_id("OFFICE_THUMBNAIL");
                                    content.setThumb_file_name("OFFICE_THUMBNAIL.PNG");
                                    thumbnailFileId = "OFFICE_THUMBNAIL";
                                 } else if (content.getMedia_type().equalsIgnoreCase("FLASH")) {
                                    content.setThumb_file_id("FLASH_THUMBNAIL");
                                    content.setThumb_file_name("FLASH_THUMBNAIL.PNG");
                                    thumbnailFileId = "FLASH_THUMBNAIL";
                                 } else if (content.getMedia_type().equalsIgnoreCase("PDF")) {
                                    content.setThumb_file_id("PDF_THUMBNAIL");
                                    content.setThumb_file_name("PDF_THUMBNAIL.PNG");
                                    thumbnailFileId = "PDF_THUMBNAIL";
                                 } else {
                                    content.setThumb_file_id("ETC_THUMBNAIL");
                                    content.setThumb_file_name("ETC_THUMBNAIL.PNG");
                                    thumbnailFileId = "ETC_THUMBNAIL";
                                 }
                              } else {
                                 fSuccess = true;
                                 ContentFile cmsThumbFile = new ContentFile();
                                 thumbnailFileId = null;
                                 if (createThumbnail) {
                                    thumbnailFileId = UUID.randomUUID().toString().toUpperCase();
                                 } else {
                                    thumbnailFileId = play_time;
                                 }

                                 Map thumbnailMap = fileManager.createThumbnailFile(file, thumbnailFileId, content);
                                 if (thumbnailMap != null && thumbnailMap.get("status") != null && thumbnailMap.get("status").equals("error")) {
                                    throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_THUMBNAIL_CREATE);
                                 }

                                 if (thumbnailMap != null && thumbnailMap.get("resolution") != null) {
                                    content.setResolution((String)thumbnailMap.get("resolution"));
                                 }

                                 if (thumbnailMap != null && thumbnailMap.get("playTime") != null) {
                                    String play_time = (String)thumbnailMap.get("playTime");
                                    if (play_time.length() > 8) {
                                       content.setPlay_time(play_time.substring(0, 8));
                                       content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                    } else {
                                       content.setPlay_time(play_time);
                                    }
                                 }

                                 thumbFile = null;
                                 if (thumbnailMap != null && thumbnailMap.get("file") != null) {
                                    thumbFile = (File)thumbnailMap.get("file");
                                 }

                                 String thmubHash = FileUtils.getHash(thumbFile);
                                 if (thmubHash == null) {
                                    content.setThumb_file_id("NOIMAGE_THUMBNAIL");
                                    content.setThumb_file_name("NOIMAGE_THUMBNAIL.PNG");
                                 } else {
                                    if (cmsDao.isExistFileByHash(fileName + ".png", thumbFile.length(), thmubHash)) {
                                       thumbnailFileId = cmsDao.getFileIDByHash(fileName + ".png", thumbFile.length(), thmubHash);
                                    }

                                    content.setThumb_file_id(thumbnailFileId);
                                    content.setThumb_file_name(fileName + ".png");
                                 }

                                 fSuccess = true;
                                 File fileCmsHome = SecurityUtils.getSafeFile(THUMBNAIL_HOME);
                                 File thumbnailFileChk = new File(SecurityUtils.directoryTraversalChecker(THUMBNAIL_HOME + "/" + thumbnailFileId, (String)null));
                                 if (thumbnailFileChk.exists()) {
                                    fSuccess = false;
                                 }

                                 if (fSuccess) {
                                    cmsThumbFile.setFile_id(thumbnailFileId);
                                    cmsThumbFile.setHash_code(thmubHash);
                                    cmsThumbFile.setFile_type("THUMBNAIL");
                                    cmsThumbFile.setFile_name(fileName + ".png");
                                    cmsThumbFile.setFile_size(thumbFile.length());
                                    cmsThumbFile.setCreator_id("SYSTEM");
                                    cmsThumbFile.setFile_path(CONTENTS_HOME + File.separator + thumbnailFileId);
                                    cmsDao.addFile(cmsThumbFile);
                                    if (cmsThumbFile != null) {
                                       fileListToSave.add(cmsThumbFile);
                                    }

                                    content.setArr_file_list(fileListToSave);
                                    if (thumbnailMap != null && thumbnailMap.get("resolution") != null) {
                                       content.setResolution((String)thumbnailMap.get("resolution"));
                                    }

                                    String filePath;
                                    if (thumbnailMap != null && thumbnailMap.get("playTime") != null) {
                                       filePath = (String)thumbnailMap.get("playTime");
                                       if (filePath.length() > 8) {
                                          content.setPlay_time(filePath.substring(0, 8));
                                          content.setPlay_time_milli(filePath.substring(9, filePath.length()));
                                       } else {
                                          content.setPlay_time(filePath);
                                       }
                                    }

                                    try {
                                       if (!fileCmsHome.exists()) {
                                          boolean fSuccess = fileCmsHome.mkdir();
                                          if (!fSuccess) {
                                             this.logger.error("mkdir Fail");
                                          }
                                       }

                                       filePath = THUMBNAIL_HOME + File.separator + content.getThumb_file_id();
                                       File fileCmsFile = SecurityUtils.getSafeFile(filePath);
                                       if (!fileCmsFile.exists()) {
                                          boolean fSuccess = fileCmsFile.mkdir();
                                          if (!fSuccess) {
                                             this.logger.error("mkdir Fail");
                                          }
                                       }

                                       String image_url = CONTENTS_HOME + "/" + content.getThumb_file_id() + "/" + content.getThumb_file_name();
                                       String thumb_url = THUMBNAIL_HOME + "/" + cmsThumbFile.getFile_id() + "/" + cmsThumbFile.getFile_name();
                                       thumb_File = SecurityUtils.getSafeFile(thumb_url);
                                       if (!thumb_File.exists()) {
                                          thumb_smallFile = SecurityUtils.getSafeFile(image_url);
                                          if (thumb_smallFile.exists()) {
                                             BufferedImage bufferedImage = ImageIO.read(thumb_smallFile);
                                             if (bufferedImage == null) {
                                                this.logger.error("[ContentFileUploadServlet] null thumbnail image : " + thumb_smallFile.getPath());
                                                throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_THUMBNAIL_LOAD_FAIL);
                                             }

                                             int orgWidth = bufferedImage.getWidth();
                                             int orgHeight = bufferedImage.getHeight();
                                             int smallWidth = 50;
                                             int smallHeight = 38;
                                             int mediumWidth = 165;
                                             int mediumHeight = 109;
                                             if ((long)(orgWidth / mediumWidth) > (long)(orgHeight / mediumHeight)) {
                                                mediumHeight = orgHeight * mediumWidth / orgWidth;
                                                if (mediumHeight % 2 != 0) {
                                                   ++mediumHeight;
                                                }
                                             } else {
                                                mediumWidth = orgWidth * mediumHeight / orgHeight;
                                                if (mediumWidth % 2 != 0) {
                                                   ++mediumWidth;
                                                }
                                             }

                                             if ((long)(orgWidth / smallWidth) > (long)(orgHeight / smallHeight)) {
                                                smallHeight = orgHeight * smallWidth / orgWidth;
                                                if (smallHeight % 2 != 0) {
                                                   ++smallHeight;
                                                }
                                             } else {
                                                smallWidth = orgWidth * smallHeight / orgHeight;
                                                if (smallWidth % 2 != 0) {
                                                   ++smallWidth;
                                                }
                                             }

                                             if (mediumWidth < 1) {
                                                mediumWidth = 1;
                                             }

                                             if (mediumHeight < 1) {
                                                mediumHeight = 1;
                                             }

                                             if (smallWidth < 1) {
                                                smallWidth = 1;
                                             }

                                             if (smallHeight < 1) {
                                                smallHeight = 1;
                                             }

                                             File thumb_File = SecurityUtils.getSafeFile(thumb_url);
                                             File thumb_smallFile = SecurityUtils.getSafeFile(thumb_url + "_SMALL.PNG");
                                             File thumb_mediumFile = SecurityUtils.getSafeFile(thumb_url + "_MEDIUM.PNG");
                                             ImageIO.write(fileManager.createResizedCopy(bufferedImage, orgWidth, orgHeight), "PNG", thumb_File);
                                             ImageIO.write(fileManager.createResizedCopy(bufferedImage, smallWidth, smallHeight), "PNG", thumb_smallFile);
                                             ImageIO.write(fileManager.createResizedCopy(bufferedImage, mediumWidth, mediumHeight), "PNG", thumb_mediumFile);
                                             Map hdThumbnailMap = fileManager.createThumbnailFile(file, THUMBNAIL_HOME, thumbnailFileId, content, 1280, 720, "_HD.PNG");
                                             if (hdThumbnailMap != null && hdThumbnailMap.get("status") != null && hdThumbnailMap.get("status").equals("error")) {
                                                this.logger.error("HD Thumbnail create error.");
                                             }
                                          }
                                       }
                                    } catch (Exception var155) {
                                       this.logger.error("[ContentFileUploadServlet] error create thumbnail");
                                       throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_THUMBNAIL_CREATE);
                                    }
                                 }
                              }

                              this.logger.info("[MagicInfo_ContentFileUpload] content update! contentId : " + contentId + " new version : " + versionId + " fileId : " + fileID);
                              cmsDao.addContentVersionInfo(content);
                              cmsDao.addMapContentFile(contentId, versionId, fileID);
                              if (thumbnailFileId == null) {
                                 thumbnailFileId = "ETC_THUMBNAIL";
                              }

                              if (content.getMedia_type().equalsIgnoreCase("IMAGE") || content.getMedia_type().equalsIgnoreCase("MOVIE")) {
                                 cmsDao.addMapContentFile(contentId, versionId, thumbnailFileId);
                              }

                              int code = cmsDao.setActiveVersionForUploader(contentId, versionId, true);
                              resource.setFileId(fileID);
                              resource.setContentId(contentId);
                              resource.setFileSize(fileSize);
                              resource.setIsUploadCompleted(code == 1);
                              resource.setIsStreaming(isStreaming);
                           } catch (Exception var171) {
                              this.logger.error("[MagicInfo_ContentFileUpload] fail update content error : " + var171.getMessage(), var171);
                           }
                        } else {
                           ContentFile cmsContentFile = new ContentFile();
                           if (!fext.equalsIgnoreCase("mpeg") && !fext.equalsIgnoreCase("mpg") && !fext.equalsIgnoreCase("wmv") && !fext.equalsIgnoreCase("avi") && !fext.equalsIgnoreCase("mov") && !fext.equalsIgnoreCase("mp4") && !fext.equalsIgnoreCase("asf")) {
                              cmsContentFile.setIs_streaming("N");
                           } else {
                              cmsContentFile.setIs_streaming("Y");
                              isStreaming = true;
                           }

                           try {
                              if (cmsDao.isExistFileByHash(fileName, fileSize, hashId)) {
                                 fileID = cmsDao.getFileIDByHash(fileName, fileSize, hashId);
                                 File f = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separatorChar + fileID + File.separatorChar + fileName, (String)null));
                                 if (!f.exists() && cmsDao.deleteFile(fileID) > 0) {
                                    this.logger.info("File information exists in DB, but no physical file exists.");
                                 }
                              }
                           } catch (Exception var154) {
                              this.logger.error(var154);
                           }

                           if (cmsDao.isExistFileByHash(fileName, fileSize, hashId)) {
                              bExistFile = true;
                           }

                           ContentFile cmsSFIContentFile;
                           if (bExistFile) {
                              cmsSFIContentFile = cmsDao.getMainFileInfo(contentId);
                              if (cmsSFIContentFile == null) {
                                 cmsSFIContentFile = cmsDao.getMainFileInfoOfTmpVer(contentId);
                              }

                              if (cmsSFIContentFile != null) {
                                 if (cmsSFIContentFile.getFile_name().equals(cmsContentFile.getFile_name()) && cmsSFIContentFile.getFile_size().equals(cmsContentFile.getFile_size()) && cmsSFIContentFile.getHash_code().equalsIgnoreCase(cmsContentFile.getHash_code())) {
                                    bMustAddContent = false;
                                 } else {
                                    bMustAddContent = true;
                                 }
                              }
                           } else {
                              bMustAddContent = true;
                           }

                           if (bExistFile) {
                              file.delete();
                              fileID = cmsDao.getFileIDByHash(fileName, fileSize, hashId);
                              file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separatorChar + fileID + File.separatorChar + fileName, (String)null));
                           }

                           cmsSFIContentFile = new ContentFile();
                           if (mediaType.equalsIgnoreCase("OFFICE") || mediaType.equalsIgnoreCase("FLASH")) {
                              Map data = new HashMap();
                              data.put("startPage", startPage);
                              data.put("fileSize", fileSize);
                              cmsSFIContentFile = ContentUtils.createSfiFile(data, userId, contentId, hashId, fileID, fileName);
                           }

                           cmsContentFile.setFile_type("MAIN");
                           cmsContentFile.setFile_id(fileID);
                           cmsContentFile.setHash_code(hashId);
                           cmsContentFile.setFile_name(fileName);
                           cmsContentFile.setFile_size(fileSize);
                           cmsContentFile.setCreator_id(userId);
                           cmsContentFile.setFile_path(CONTENTS_HOME + File.separator + fileID);
                           Content content = new Content();
                           content.setVersion_id(1L);
                           content.setContent_id(contentId);
                           content.setGroup_id(Long.valueOf(groupId));
                           content.setShare_flag(1);
                           content.setContent_meta_data("");
                           content.setCreator_id(userId);
                           content.setMedia_type(mediaType);
                           content.setOrganization_id(uInfo.getRootGroupIdByUserId(userId));
                           content.setTotal_size(fileSize);
                           content.setIs_aisr(isEncrypted ? "Y" : "N");
                           if (!cmsSFIContentFile.getFile_id().equals("")) {
                              content.setSfi_file_id(cmsSFIContentFile.getFile_id());
                           }

                           content.setIs_active("Y");
                           content.setOrg_creator_id(String.valueOf(orgCreatorId));
                           cmsThumbFile = null;
                           this.logger.error("[ContentFileUploadServlet] content name : " + content.getContent_name() + " content Media Type : " + content.getMedia_type());
                           if (content.getMedia_type().equalsIgnoreCase("LFD") || content.getMedia_type().equalsIgnoreCase("LFT") || content.getMedia_type().equalsIgnoreCase("VWL") || content.getMedia_type().equalsIgnoreCase("PROM")) {
                              throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CONTENT_UPLOAD_NOT_SUPPORT_FILE);
                           }

                           File fileCmsHome;
                           String filePath;
                           if (content.getMedia_type().equalsIgnoreCase("SOUND")) {
                              content.setThumb_file_id("SOUND_THUMBNAIL");
                              content.setThumb_file_name("SOUND_THUMBNAIL.PNG");
                              String[] fileMeta;
                              String play_time;
                              if (fext.equals("MP3")) {
                                 try {
                                    fileMeta = fileManager.getFileMeta(file);
                                    if (fileMeta[0] != null) {
                                       play_time = fileMeta[0];
                                       if (play_time.length() > 8) {
                                          content.setPlay_time(play_time.substring(0, 8));
                                          content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                       } else {
                                          content.setPlay_time(play_time);
                                       }
                                    } else {
                                       play_time = fileManager.getMP3PlayTime(file);
                                       if (play_time.length() > 8) {
                                          content.setPlay_time(play_time.substring(0, 8));
                                          content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                       } else {
                                          content.setPlay_time(play_time);
                                       }
                                    }
                                 } catch (Exception var153) {
                                    content.setPlay_time("");
                                 }
                              } else {
                                 fileMeta = fileManager.getFileMeta(file);
                                 if (fileMeta[0] != null) {
                                    play_time = fileMeta[0];
                                    if (play_time.length() > 8) {
                                       content.setPlay_time(play_time.substring(0, 8));
                                       content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                    } else {
                                       content.setPlay_time(play_time);
                                    }
                                 } else {
                                    content.setPlay_time("");
                                 }
                              }
                           } else if (!content.getMedia_type().equalsIgnoreCase("IMAGE") && !content.getMedia_type().equalsIgnoreCase("MOVIE")) {
                              if (content.getMedia_type().equalsIgnoreCase("OFFICE")) {
                                 content.setThumb_file_id("OFFICE_THUMBNAIL");
                                 content.setThumb_file_name("OFFICE_THUMBNAIL.PNG");
                              } else if (content.getMedia_type().equalsIgnoreCase("FLASH")) {
                                 content.setThumb_file_id("FLASH_THUMBNAIL");
                                 content.setThumb_file_name("FLASH_THUMBNAIL.PNG");
                              } else if (content.getMedia_type().equalsIgnoreCase("PDF")) {
                                 content.setThumb_file_id("PDF_THUMBNAIL");
                                 content.setThumb_file_name("PDF_THUMBNAIL.PNG");
                              } else {
                                 content.setThumb_file_id("ETC_THUMBNAIL");
                                 content.setThumb_file_name("ETC_THUMBNAIL.PNG");
                              }
                           } else {
                              cmsThumbFile = new ContentFile();
                              thumbnailFileId = UUID.randomUUID().toString().toUpperCase();
                              Map thumbnailMap = fileManager.createThumbnailFile(file, thumbnailFileId, content);
                              if (thumbnailMap != null && thumbnailMap.get("status") != null && thumbnailMap.get("status").equals("error")) {
                                 throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_THUMBNAIL_CREATE);
                              }

                              fileCmsHome = null;
                              if (thumbnailMap != null && thumbnailMap.get("file") != null) {
                                 fileCmsHome = (File)thumbnailMap.get("file");
                              }

                              filePath = FileUtils.getHash(fileCmsHome);
                              if (filePath == null) {
                                 content.setThumb_file_id("NOIMAGE_THUMBNAIL");
                                 content.setThumb_file_name("NOIMAGE_THUMBNAIL.PNG");
                              } else {
                                 if (cmsDao.isExistFileByHash(fileName + ".png", fileCmsHome.length(), filePath)) {
                                    thumbnailFileId = cmsDao.getFileIDByHash(fileName + ".png", fileCmsHome.length(), filePath);
                                 }

                                 content.setThumb_file_id(thumbnailFileId);
                                 content.setThumb_file_name(fileName + ".png");
                              }

                              cmsThumbFile.setFile_id(thumbnailFileId);
                              cmsThumbFile.setHash_code(filePath);
                              cmsThumbFile.setFile_type("THUMBNAIL");
                              cmsThumbFile.setFile_name(fileName + ".png");
                              cmsThumbFile.setFile_size(fileCmsHome.length());
                              cmsThumbFile.setCreator_id(userId);
                              cmsThumbFile.setFile_path(CONTENTS_HOME + File.separator + thumbnailFileId);
                              if (thumbnailMap != null && thumbnailMap.get("resolution") != null) {
                                 content.setResolution((String)thumbnailMap.get("resolution"));
                              }

                              if (thumbnailMap != null && thumbnailMap.get("playTime") != null) {
                                 play_time = (String)thumbnailMap.get("playTime");
                                 if (play_time.length() > 8) {
                                    content.setPlay_time(play_time.substring(0, 8));
                                    content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                 } else {
                                    content.setPlay_time(play_time);
                                 }
                              }
                           }

                           if (contentName.length() > 100) {
                              contentName = contentName.substring(0, 100);
                           }

                           content.setContent_name(contentName);
                           content.setMain_file_id(fileID);
                           content.setMain_file_Extension(fext.toUpperCase());
                           if (contentsApprovalEnable) {
                              if (content.getMain_file_Extension().equalsIgnoreCase("LFT")) {
                                 content.setApproval_status("APPROVED");
                              } else {
                                 AbilityUtils abilityUtils = new AbilityUtils();
                                 if (abilityUtils.isContentApprovalAuthority(userId)) {
                                    content.setApproval_status("APPROVED");
                                 } else {
                                    content.setApproval_status("UNAPPROVED");
                                 }
                              }
                           } else {
                              content.setApproval_status("APPROVED");
                           }

                           List fileListToSave = new ArrayList();
                           fileListToSave.add(cmsContentFile);
                           if (cmsThumbFile != null) {
                              fileListToSave.add(cmsThumbFile);
                           }

                           if (mediaType.equalsIgnoreCase("OFFICE") || mediaType.equalsIgnoreCase("FLASH")) {
                              fileListToSave.add(cmsSFIContentFile);
                           }

                           content.setArr_file_list(fileListToSave);
                           if (categoryIds != null) {
                              CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();

                              for(int i = 0; i < categoryIds.size(); ++i) {
                                 categoryInfo.setCategoryFromContentId((String)categoryIds.get(i), contentId);
                              }
                           }

                           cmsDao.addContent(content);

                           try {
                              fileCmsHome = SecurityUtils.getSafeFile(THUMBNAIL_HOME);
                              if (!fileCmsHome.exists()) {
                                 boolean fSuccess = fileCmsHome.mkdir();
                                 if (!fSuccess) {
                                    this.logger.error("mkdir Fail");
                                 }
                              }

                              filePath = THUMBNAIL_HOME + File.separator + content.getThumb_file_id();
                              File fileCmsFile = SecurityUtils.getSafeFile(filePath);
                              if (!fileCmsFile.exists()) {
                                 fSuccess = fileCmsFile.mkdir();
                                 if (!fSuccess) {
                                    this.logger.error("mkdir Fail");
                                 }
                              }

                              String image_url = CONTENTS_HOME + "/" + content.getThumb_file_id() + "/" + content.getThumb_file_name();
                              thumb_url = THUMBNAIL_HOME + "/" + content.getThumb_file_id() + "/" + content.getThumb_file_name();
                              File checkFile = SecurityUtils.getSafeFile(thumb_url);
                              if (!checkFile.exists()) {
                                 thumbFile = SecurityUtils.getSafeFile(image_url);
                                 if (thumbFile.exists()) {
                                    BufferedImage bufferedImage = ImageIO.read(thumbFile);
                                    if (bufferedImage == null) {
                                       this.logger.error("[ContentFileUploadServlet] null thumbnail image : " + thumbFile.getPath());
                                       throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_THUMBNAIL_LOAD_FAIL);
                                    }

                                    int orgWidth = bufferedImage.getWidth();
                                    int orgHeight = bufferedImage.getHeight();
                                    int smallWidth = 50;
                                    int smallHeight = 38;
                                    int mediumWidth = 165;
                                    int mediumHeight = 109;
                                    if ((long)(orgWidth / mediumWidth) > (long)(orgHeight / mediumHeight)) {
                                       mediumHeight = orgHeight * mediumWidth / orgWidth;
                                       if (mediumHeight % 2 != 0) {
                                          ++mediumHeight;
                                       }
                                    } else {
                                       mediumWidth = orgWidth * mediumHeight / orgHeight;
                                       if (mediumWidth % 2 != 0) {
                                          ++mediumWidth;
                                       }
                                    }

                                    if ((long)(orgWidth / smallWidth) > (long)(orgHeight / smallHeight)) {
                                       smallHeight = orgHeight * smallWidth / orgWidth;
                                       if (smallHeight % 2 != 0) {
                                          ++smallHeight;
                                       }
                                    } else {
                                       smallWidth = orgWidth * smallHeight / orgHeight;
                                       if (smallWidth % 2 != 0) {
                                          ++smallWidth;
                                       }
                                    }

                                    if (mediumWidth < 1) {
                                       mediumWidth = 1;
                                    }

                                    if (mediumHeight < 1) {
                                       mediumHeight = 1;
                                    }

                                    if (smallWidth < 1) {
                                       smallWidth = 1;
                                    }

                                    if (smallHeight < 1) {
                                       smallHeight = 1;
                                    }

                                    thumb_File = SecurityUtils.getSafeFile(thumb_url);
                                    thumb_smallFile = SecurityUtils.getSafeFile(thumb_url + "_SMALL.PNG");
                                    File thumb_mediumFile = SecurityUtils.getSafeFile(thumb_url + "_MEDIUM.PNG");
                                    ImageIO.write(fileManager.createResizedCopy(bufferedImage, orgWidth, orgHeight), "PNG", thumb_File);
                                    ImageIO.write(fileManager.createResizedCopy(bufferedImage, smallWidth, smallHeight), "PNG", thumb_smallFile);
                                    ImageIO.write(fileManager.createResizedCopy(bufferedImage, mediumWidth, mediumHeight), "PNG", thumb_mediumFile);
                                 }
                              }
                           } catch (Exception var172) {
                              this.logger.error("[ContentFileUploadServlet] error create thumbnail");
                              throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_THUMBNAIL_CREATE);
                           }

                           cmsDao.setActiveVersion(contentId, true);
                           cmsDao.setContentInfo(content.getContent_id(), content.getContent_name(), content.getContent_meta_data(), content.getShare_flag());
                           cmsDao.setContentGroup(content.getContent_id(), content.getGroup_id());
                           resource.setFileId(fileID);
                           resource.setContentId(contentId);
                           resource.setFileSize(fileSize);
                           resource.setIsUploadCompleted(true);
                           resource.setIsStreaming(isStreaming);
                        }
                     }
                  }
               }
            }

            if (doNotSupportContentList != null && doNotSupportContentList.size() > 0) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CONTENT_UPLOAD_NOT_SUPPORT_FILE);
            }
         } catch (Exception var174) {
            this.logger.error("[MagicInfo_ContentUpload][" + userId + "] fail file upload! message : " + var174.getMessage());
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_FILE_UPLOAD_FAIL, new String[]{"content"});
         }

         try {
            List notifications = new ArrayList();
            notifications.add(this.makeNotificationData(resource.getContentId()));
            if (notifications.size() > 0) {
               if (contentUpdate) {
                  MailUtil.sendContentEventMail(notifications, "Change content version");
               } else {
                  MailUtil.sendContentEventMail(notifications, "Add content");
               }
            }
         } catch (Exception var152) {
            this.logger.error(var152);
         }

         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority')")
   public List getVersionList(String contentId) throws Exception {
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      List contentAllVerInfo = contentInfo.getContentAllVerInfo(contentId);
      Locale locale = SecurityUtils.getLocale();
      ArrayList contentVersionResources = new ArrayList();
      if (contentAllVerInfo != null) {
         for(int i = 0; i < contentAllVerInfo.size(); ++i) {
            V2ContentVersion contentVersionResource = new V2ContentVersion();
            Content content = (Content)contentAllVerInfo.get(i);
            contentVersionResource.setVersionId(content.getVersion_id());
            contentVersionResource.setLastModified(content.getCreate_date());
            contentVersionResource.setIsActive(content.getIs_active());
            contentVersionResource.setThumbnailId(content.getThumb_file_id());
            contentVersionResource.setCreatedBy(content.getCreator_id());
            contentVersionResources.add(contentVersionResource);
         }
      }

      return contentVersionResources;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority')")
   public List getContentUseList(String contentId) throws Exception {
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      List contentUseList = new ArrayList();
      List playlistInfo = contentInfo.getContentUseInPlaylist(contentId);
      List scheduleInfo = contentInfo.getContentUseInSchedule(contentId);
      long playlistInfoLength = playlistInfo == null ? 0L : (long)playlistInfo.size();
      long scheduleInfoLength = scheduleInfo == null ? 0L : (long)scheduleInfo.size();

      int i;
      V2ContentUse res;
      for(i = 0; (long)i < playlistInfoLength; ++i) {
         res = new V2ContentUse();
         String playlistId = (String)((Map)playlistInfo.get(i)).get("playlist_id");
         String playlistVersion = this.getPlaylistVersionStr(playlistId, contentId);
         String playlistName = (String)((Map)playlistInfo.get(i)).get("playlist_name");
         res.setPlaylist(playlistName + "(" + playlistVersion + ")");
         res.setPlaylistOrganization((String)((Map)playlistInfo.get(i)).get("organization"));
         res.setPlaylistId(playlistId);
         contentUseList.add(res);
      }

      for(i = 0; (long)i < scheduleInfoLength; ++i) {
         res = new V2ContentUse();
         res.setSchedule((String)((Map)scheduleInfo.get(i)).get("program_name"));
         res.setScheduleOrganization((String)((Map)scheduleInfo.get(i)).get("organization"));
         res.setProgramId((String)((Map)scheduleInfo.get(i)).get("program_id"));
         contentUseList.add(res);
      }

      return contentUseList;
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public V2ContentResource updateContentVersion(String contentId, String versionId) throws Exception {
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      String orgName = SecurityUtils.getUserContainer().getUser().getOrganization();
      long orgId = userGroupInfo.getOrgGroupIdByName(orgName);
      Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
      boolean contentsApprovalEnable = false;
      if (infoMap != null && infoMap.get("CONTENTS_APPROVAL_ENABLE") != null) {
         contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
      }

      boolean ret = false;
      String mediaType;
      if (contentsApprovalEnable) {
         if (contentInfo.setActiveVersion(contentId, Long.parseLong(versionId), false) > 0) {
            mediaType = contentInfo.getMediaTypeByContentId(contentId);
            if (StringUtils.isNotBlank(mediaType) && mediaType.equals("ADS")) {
               contentInfo.setAdsContentActiveVersion(contentId, Long.parseLong(versionId));
            }

            ret = true;
         }
      } else if (contentInfo.setActiveVersion(contentId, Long.parseLong(versionId), true) > 0) {
         ret = true;
         mediaType = contentInfo.getMediaTypeByContentId(contentId);
         if (StringUtils.isNotBlank(mediaType) && mediaType.equals("ADS")) {
            contentInfo.setAdsContentActiveVersion(contentId, Long.parseLong(versionId));
         } else if (StringUtils.isNotBlank(mediaType) && mediaType.equalsIgnoreCase("LFT")) {
            String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
            List dlkContentList = null;
            dlkContentList = contentInfo.getDlkContentIdByTemplateId(contentId);
            if (dlkContentList != null && dlkContentList.size() > 0) {
               for(int i = 0; i < dlkContentList.size(); ++i) {
                  ContentXmlManager contentXmlManager = new ContentXmlManager();
                  Map dlkContent = (Map)dlkContentList.get(i);
                  String dlkContentId = dlkContent.get("DLK_CONTENT_ID").toString();
                  long nextVersion = contentInfo.getContentNextVer(dlkContentId);
                  String dlkCreatorUserId = contentInfo.getCreatorIdByContentId(dlkContentId);
                  String dlkMainFileId = contentInfo.getMainFileInfo(dlkContentId).getFile_id();
                  String newDlkMainFileId = UUID.randomUUID().toString().toUpperCase();
                  String templateMainFileId = contentInfo.getFileInfoByContentIdVersionId(contentId, versionId);
                  ContentFile dlkFile = contentInfo.getFileInfo(dlkMainFileId);
                  String dlkFilePath = dlkFile.getFile_path() + File.separator + dlkFile.getFile_name();
                  ContentFile templateFile = contentInfo.getFileInfo(templateMainFileId);
                  ContentFile contentDlkFile = new ContentFile();
                  String newDlkFilePath = CONTENTS_HOME + File.separator + newDlkMainFileId;
                  String newDlkFilePathFile = CONTENTS_HOME + File.separator + newDlkMainFileId + File.separator + dlkFile.getFile_name();
                  File fileCmsFile = SecurityUtils.getSafeFile(newDlkFilePath);
                  if (!fileCmsFile.exists()) {
                     boolean fSuccess = fileCmsFile.mkdir();
                     if (!fSuccess) {
                        this.logger.error("mkdir Fail");
                     }
                  }

                  contentXmlManager.modifyTemplateInfo(templateFile, dlkFilePath, newDlkFilePathFile, nextVersion, contentId, dlkContentId);
                  contentDlkFile.setFile_id(newDlkMainFileId);
                  contentDlkFile.setFile_path(newDlkFilePath);
                  contentDlkFile.setFile_name(dlkFile.getFile_name());
                  contentDlkFile.setCreator_id(dlkCreatorUserId);
                  contentDlkFile.setFile_type("DLK");
                  contentDlkFile.setFile_size(dlkFile.getFile_size());
                  String hashCode = "";
                  long fileSize = 0L;
                  hashCode = FileUtils.getHash(SecurityUtils.getSafeFile(newDlkFilePathFile));
                  fileSize = SecurityUtils.getSafeFile(newDlkFilePathFile).length();
                  contentInfo.addFile(contentDlkFile);
                  contentInfo.updateThumbnailIdOfDlkByContentId(contentId, versionId, dlkContentId);
                  contentInfo.updateHashCodeByMainFileId(newDlkMainFileId, fileSize, hashCode);
                  contentInfo.updateVersionAndMainFileIdInContentVersionInfo(nextVersion, newDlkMainFileId.toUpperCase(), dlkContentId.toUpperCase());
                  ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
                  schInfo.setScheduleTrigger(dlkContentId);
                  List pList = contentInfo.getPlaylistListUsingContent(dlkContentId);
                  EventInfo eInfo = EventInfoImpl.getInstance();
                  eInfo.setContentTrigger(dlkContentId);

                  for(int k = 0; k < pList.size(); ++k) {
                     Map map = (Map)pList.get(k);
                     String playlistId = (String)map.get("playlist_id");
                     eInfo.setPlaylistTrigger(playlistId);
                  }
               }
            }
         }
      }

      if (!ret) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      } else {
         try {
            List notifications = new ArrayList();
            notifications.add(this.makeNotificationData(contentId));
            if (notifications.size() > 0) {
               MailUtil.sendContentEventMail(notifications, "Change content version");
            }
         } catch (Exception var41) {
            this.logger.error(var41);
         }

         V2ContentResource resource = this.getContentDetail(contentId);
         if (resource == null) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{contentId});
         } else {
            return resource;
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public V2ContentResource updateContentDetail(String contentId, V2ContentEdit resource) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      if (!StrUtils.nvl(resource.getGroupId()).equals("")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(resource.getGroupId()));
      }

      ContentInfo cInfo = ContentInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      String orgName = SecurityUtils.getUserContainer().getUser().getOrganization();
      long orgId = userGroupInfo.getOrgGroupIdByName(orgName);
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
      boolean contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
      String contentName = resource.getContentName().trim();
      String contentMetaData = resource.getContentMetaData();
      String strShareFlag = resource.getIsHidden() ? ContentConstants.SHARE_FLAG_NO.toString() : ContentConstants.SHARE_FLAG_YES.toString();
      String strGroupId = resource.getGroupId();
      String strVersionId = resource.getVersionId();
      String content_type = cInfo.getMediaTypeByContentId(contentId);
      String strmAddress = resource.getStrmAddress().equals("") ? "CONTENT" : resource.getStrmAddress();
      String userId = this.getLoginUserId();
      String expirationDate = resource.getIsExpirationDateActivated() ? resource.getExpirationDate() : "29991231";
      PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();
      LogInfo logInfo = LogInfoImpl.getInstance();
      ContentLog cLog = new ContentLog();
      cLog.setUser_id(this.getLoginUserId());
      PlaylistLog pLog = new PlaylistLog();
      pLog.setUser_id(this.getLoginUserId());
      int shareFlag = 0;
      Long groupID = 0L;
      Long versionID = 0L;
      if (strShareFlag.length() > 0) {
         shareFlag = Integer.parseInt(strShareFlag);
      }

      if (strGroupId.length() > 0) {
         groupID = new Long(strGroupId);
      }

      if (strVersionId != null && strVersionId.length() > 0) {
         versionID = Long.parseLong(strVersionId);
      } else {
         versionID = cInfo.getVersionInfoByContentId(contentId);
      }

      if (content_type.equalsIgnoreCase("STRM")) {
         cInfo.updateUrlSetting(contentId, contentName, strmAddress);
         UrlFilesToDownload urlFilesToDownload = UrlFilesToDownload.getInstance();
         urlFilesToDownload.getUrlFiles(userId, groupID, contentId, contentName, strmAddress, "");
      }

      if (contentId.length() > 0) {
         boolean ret = false;
         if ((contentName.length() > 0 || contentMetaData.length() > 0 || strShareFlag.length() > 0) && cInfo.setContentInfo(contentId, contentName, contentMetaData, shareFlag) > 0) {
            ret = true;
         }

         if (strGroupId.length() > 0 && cInfo.setContentGroup(contentId, groupID) > 0) {
            ret = true;
         }

         if (strVersionId != null && strVersionId.length() > 0) {
            if (StrUtils.nvl(CommonConfig.get("contentApproval.enable")).equalsIgnoreCase("TRUE")) {
               if (cInfo.setActiveVersion(contentId, versionID, false) > 0) {
                  ret = true;
               }
            } else if (cInfo.setActiveVersion(contentId, versionID, true) > 0) {
               ret = true;
            }
         }

         Long oldVersion = 0L;
         Content content;
         String contentDuration;
         String pId;
         ContentFile contentDlkFile;
         if (!contentsApprovalEnable) {
            content = cInfo.getContentActiveVerInfo(contentId);
            contentDuration = content.getPlay_time();
            oldVersion = cInfo.getContentNextVer(content.getContent_id()) - 1L;
            Content oldContent = cInfo.getContentVerInfo(content.getContent_id(), oldVersion);
            String oldContentDuration = "0";
            if (oldContent != null) {
               oldContentDuration = oldContent.getPlay_time();
            }

            boolean existPlaytime = true;
            if (contentDuration == null || contentDuration.equals("") || contentDuration.equals("-")) {
               if (oldContentDuration == null || oldContentDuration.equals("") || oldContentDuration.equals("-")) {
                  existPlaytime = false;
               }

               contentDuration = "00:00:30";
            }

            String[] tokens = contentDuration.split(":");
            int hours = Integer.parseInt(tokens[0]);
            int minutes = Integer.parseInt(tokens[1]);
            int seconds = Integer.parseInt(tokens[2]);
            int duration = 3600 * hours + 60 * minutes + seconds;
            if (existPlaytime) {
               pInfo.setContentDuraionByContentID(content.getContent_id(), (long)duration);
            }

            List pIDList = cInfo.getPlaylistListUsingContent(content.getContent_id());
            if (pIDList != null && pIDList.size() != 0) {
               pId = null;
               Map map = null;
               List pList = null;
               Long playTime = null;
               contentDlkFile = null;

               for(int j = 0; j < pIDList.size(); ++j) {
                  map = (Map)pIDList.get(j);
                  pId = (String)map.get("PLAYLIST_ID");
                  pList = pInfo.getPlaylistAllVerInfo(pId);
                  if (pList != null) {
                     Iterator var46 = pList.iterator();

                     while(var46.hasNext()) {
                        Playlist p = (Playlist)var46.next();
                        playTime = pInfo.getSumOfContentDuration(p.getPlaylist_id(), p.getVersion_id());
                        if (playTime != null && existPlaytime) {
                           pInfo.setPlaytime(p.getPlaylist_id(), p.getVersion_id(), ContentUtils.getPlayTimeFormattedStr(playTime));
                        }
                     }
                  }
               }
            }

            contentDuration = content.getPlay_time();
            if (contentDuration == null || contentDuration.equals("") || contentDuration.equals("-")) {
               contentDuration = "00:00:05";
            }

            tokens = contentDuration.split(":");
            hours = Integer.parseInt(tokens[0]);
            minutes = Integer.parseInt(tokens[1]);
            seconds = Integer.parseInt(tokens[2]);
            duration = 3600 * hours + 60 * minutes + seconds;
         }

         content = null;
         contentDuration = cInfo.getMediaTypeByContentId(contentId);
         if (content_type.equals("TEMPLATE") || contentDuration.equals("LFT")) {
            List dlkContentList = cInfo.getDlkContentIdByTemplateId(contentId);
            if (dlkContentList != null && dlkContentList.size() > 0) {
               for(int i = 0; i < dlkContentList.size(); ++i) {
                  ContentXmlManager contentXmlManager = new ContentXmlManager();
                  Map dlkContent = (Map)dlkContentList.get(i);
                  String dlkContentId = dlkContent.get("DLK_CONTENT_ID").toString();
                  long nextVersion = cInfo.getContentNextVer(dlkContentId);
                  ContentFile contentFile = cInfo.getMainFileInfo(dlkContentId);
                  if (contentFile != null) {
                     String dlkMainFileId = contentFile.getFile_id();
                     String newDlkMainFileId = UUID.randomUUID().toString().toUpperCase();
                     pId = cInfo.getFileInfoByContentIdVersionId(contentId, String.valueOf(versionID));
                     ContentFile dlkFile = cInfo.getFileInfo(dlkMainFileId);
                     String dlkFilePath = dlkFile.getFile_path() + File.separator + dlkFile.getFile_name();
                     ContentFile templateFile = cInfo.getFileInfo(pId);
                     contentDlkFile = new ContentFile();
                     String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
                     String newDlkFilePath = CONTENTS_HOME + File.separator + newDlkMainFileId;
                     String newDlkFilePathFile = CONTENTS_HOME + File.separator + newDlkMainFileId + File.separator + dlkFile.getFile_name();
                     File fileCmsFile = SecurityUtils.getSafeFile(newDlkFilePath);
                     if (!fileCmsFile.exists()) {
                        fileCmsFile.mkdir();
                     }

                     contentXmlManager.modifyTemplateInfo(templateFile, dlkFilePath, newDlkFilePathFile, nextVersion, contentId, dlkContentId);
                     contentDlkFile.setFile_id(newDlkMainFileId);
                     contentDlkFile.setFile_path(newDlkFilePath);
                     contentDlkFile.setFile_name(dlkFile.getFile_name());
                     contentDlkFile.setCreator_id(this.getLoginUserId());
                     contentDlkFile.setFile_type("DLK");
                     contentDlkFile.setFile_size(dlkFile.getFile_size());
                     String hashCode = "";
                     long fileSize = 0L;
                     hashCode = FileUtils.getHash(SecurityUtils.getSafeFile(newDlkFilePathFile));
                     fileSize = SecurityUtils.getSafeFile(newDlkFilePathFile).length();
                     cInfo.addFile(contentDlkFile);
                     cInfo.updateThumbnailIdOfDlkByContentId(contentId, String.valueOf(versionID), dlkContentId);
                     cInfo.updateHashCodeByMainFileId(newDlkMainFileId, fileSize, hashCode);
                     cInfo.updateVersionAndMainFileIdInContentVersionInfo(nextVersion, newDlkMainFileId, dlkContentId);
                     ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
                     schInfo.setContentTrigger(dlkContentId);
                     EventInfo eInfo = EventInfoImpl.getInstance();
                     eInfo.setContentTrigger(dlkContentId);
                  }
               }
            }
         }
      }

      try {
         List notifications = new ArrayList();
         notifications.add(this.makeNotificationData(contentId));
         if (notifications.size() > 0) {
            MailUtil.sendContentEventMail(notifications, "Edit content");
         }
      } catch (Exception var55) {
         this.logger.error(var55);
      }

      try {
         if (expirationDate.length() == 8) {
            cInfo.setExpirationDate(contentId, expirationDate);
         } else {
            this.logger.error("[EDIT] expirationDate(" + expirationDate + ") contentId(" + contentId + ")");
         }

         this.rulesetUpdate(contentId);
      } catch (Exception var54) {
         this.logger.error("[EDIT] expirationDate(" + expirationDate + ") contentId(" + contentId + ") ", var54);
      }

      V2ContentResource contentResource = this.getContentDetail(contentId);
      if (contentResource == null) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{contentId});
      } else {
         return contentResource;
      }
   }

   private boolean rulesetUpdate(String contentId) {
      String[] contentIds = new String[]{contentId};
      return this.rulesetUpdate(contentIds);
   }

   private boolean rulesetUpdate(String[] contentIds) {
      try {
         RuleSetInfo rulesetInfo = RuleSetInfoImpl.getInstance();
         Map rulesets = new HashMap();
         String[] var4 = contentIds;
         int var5 = contentIds.length;

         List contents;
         RuleSet ruleset;
         for(int var6 = 0; var6 < var5; ++var6) {
            String contentId = var4[var6];
            contents = rulesetInfo.getRulesetUsingContents(contentId);
            Iterator var9 = contents.iterator();

            while(var9.hasNext()) {
               ruleset = (RuleSet)var9.next();
               if (!rulesets.containsKey(ruleset.getRuleset_id())) {
                  rulesets.put(ruleset.getRuleset_id(), ruleset);
               }
            }
         }

         Iterator var16 = rulesets.values().iterator();

         while(var16.hasNext()) {
            RuleSet ruleset = (RuleSet)var16.next();
            List conditions = rulesetInfo.getConditionsInRuleset(ruleset.getRuleset_id());
            List results = rulesetInfo.getResultsInRuleset(ruleset.getRuleset_id());
            contents = rulesetInfo.getContentsInRuleset(ruleset.getRuleset_id());
            new ArrayList();
            ruleset = null;
            Gson gson = (new GsonBuilder()).serializeNulls().create();
            JSONArray contentsJson = new JSONArray(gson.toJson(contents));
            JSONArray rulesJson = new JSONArray(ruleset.getRule_tree());
            ContentFile ruleMetaFile = RulesetUtils.makeRuleMetaFile(ruleset, conditions, results, contentsJson, rulesJson);
            if (ruleMetaFile != null) {
               ruleset.setFile_id(ruleMetaFile.getFile_id());
            }

            ruleset.setConditions(conditions);
            ruleset.setResults(results);
            RulesetUtils.editRulesetAndDeleteOldFile(true, ruleset);
            ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
            scheduleInfo.setContentTrigger(ruleset.getRuleset_id());
         }
      } catch (Exception var15) {
         this.logger.error(var15);
      }

      return false;
   }

   private boolean supportedImageType(String fext) {
      return imageFexts.contains(fext.toLowerCase());
   }

   private boolean supportedVideoType(String fext) {
      return videoFexts.contains(fext.toLowerCase());
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2CommonResultResource assignTagToContent(V2ContentTagAssignment body) throws Exception {
      List contentIds = body.getContentIds();
      List tags = body.getTags();
      boolean flag = false;
      Iterator var5 = contentIds.iterator();

      while(var5.hasNext()) {
         String contentId = (String)var5.next();
         if (!this.isContentInSharedFolder(contentId)) {
            try {
               RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
            } catch (Exception var25) {
               flag = true;
               break;
            }
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
      }

      TagInfo tagInfo = TagInfoImpl.getInstance();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      V2CommonResultResource resource = new V2CommonResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var10 = contentIds.iterator();

      label166:
      while(var10.hasNext()) {
         String contentId = (String)var10.next();
         ArrayList beforeDelTagIds = new ArrayList();

         try {
            List tagIds = tagInfo.getTagIdFromContentId(contentId);
            Content content = contentInfo.getContentActiveVerInfo(contentId);
            ContentLog log = new ContentLog();
            log.setContent_id(contentId);
            log.setContent_name(content.getContent_name());
            log.setVersion_id(content.getVersion_id());
            log.setMedia_type(content.getMedia_type());
            log.setThumb_file_id(content.getThumb_file_id());
            log.setThumb_file_name(content.getThumb_file_name());
            log.setCreator_id(content.getCreator_id());
            log.setUser_id(SecurityUtils.getUserContainer().getUser().getUser_id());
            if (tagIds != null && tagIds.size() > 0) {
               String arrTagIds = "";

               for(int i = 0; i < tags.size(); ++i) {
                  arrTagIds = arrTagIds + ((V2TagAssignment)tags.get(i)).getTagId();
                  if (i < tags.size() - 1) {
                     arrTagIds = arrTagIds + ",";
                  }
               }

               Iterator var30 = tagIds.iterator();

               while(var30.hasNext()) {
                  Map map = (Map)var30.next();
                  long tagId = (Long)map.get("tag_id");
                  if (!beforeDelTagIds.contains(tagId)) {
                     beforeDelTagIds.add(tagId);
                  }

                  if (!Arrays.asList(arrTagIds).contains(String.valueOf(tagId))) {
                     log.setTag_id(Long.valueOf(tagId));
                     log.setEvent_type("1");
                     tagInfo.addLogContentTag(log);
                  }
               }
            }

            tagInfo.deleteTagInfoFromContentId(contentId);
            Iterator var28 = tags.iterator();

            while(true) {
               V2TagAssignment tag;
               do {
                  if (!var28.hasNext()) {
                     for(int idx = 0; idx < beforeDelTagIds.size(); ++idx) {
                        long tagId = (Long)beforeDelTagIds.get(idx);
                        List thumbnailList = contentInfo.getContentListWithThumbnailFromTagId(tagId);

                        for(int idx2 = 0; idx2 < thumbnailList.size(); ++idx2) {
                           Map mapContentInfo = (Map)thumbnailList.get(idx2);
                           Content tcontent = contentInfo.getContentActiveVerInfo(mapContentInfo.get("content_id").toString());
                           if (tcontent != null && tcontent.getExpiration_date() != null && content.getExpiration_date().compareTo(DateUtils.getCurrentTime("yyyyMMdd")) < 0) {
                              tagInfo.deleteRelationOfTagAndContent(tagId, content.getContent_id());
                           }
                        }
                     }

                     tagInfo.setPlaylistTrigger(beforeDelTagIds);
                     V2ContentResource contentResource = this.getContentDetail(contentId);
                     if (contentResource != null) {
                        successList.add(contentResource);
                        ContentUtils.updateLastModifiedDate(contentId);
                     }
                     continue label166;
                  }

                  tag = (V2TagAssignment)var28.next();
               } while(tag.getTagId() == null);

               String tagId = String.valueOf(tag.getTagId());
               String tagType = String.valueOf(tag.getTagType());
               ArrayList tagConditions;
               if (tagType != null && tagType.equals("1")) {
                  tagConditions = tag.getTagConditions();
                  long conditionId = -4L;
                  if (tagConditions != null) {
                     String tagCondition = tagConditions.isEmpty() ? "" : (String)tagConditions.get(0);
                     tagInfo.deleteCondition(Long.parseLong(tagId));
                     conditionId = tagInfo.addCondition(Long.parseLong(tagId), tagCondition);
                     tagInfo.setContentTagMapping(contentId, tagId, String.valueOf(conditionId));
                  }
               } else {
                  Iterator var21;
                  String tagCondition;
                  if (tagType != null && tagType.equals("2")) {
                     tagConditions = tag.getTagConditions();
                     tagInfo.deleteBooleanCondition(Long.parseLong(tagId));
                     if (tagConditions == null || tagConditions.isEmpty()) {
                        ArrayList defaultTagConditions = new ArrayList();
                        defaultTagConditions.add("false");
                        tagConditions = defaultTagConditions;
                     }

                     var21 = tagConditions.iterator();
                     if (var21.hasNext()) {
                        tagCondition = (String)var21.next();
                        if (tagCondition != null && tagCondition.equals("true")) {
                           tagInfo.addBoolenCondition(Long.parseLong(tagId), -2L);
                           tagInfo.setContentTagMapping(contentId, tagId, "-2");
                        } else {
                           tagInfo.addBoolenCondition(Long.parseLong(tagId), -3L);
                           tagInfo.setContentTagMapping(contentId, tagId, "-3");
                        }
                     }
                  } else {
                     tagConditions = tag.getTagConditions();
                     if (tagConditions != null && !tagConditions.isEmpty()) {
                        var21 = tagConditions.iterator();

                        while(var21.hasNext()) {
                           tagCondition = (String)var21.next();
                           if (tagCondition != null && !tagCondition.equals("")) {
                              tagInfo.setContentTagMapping(contentId, tagId, tagCondition);
                           }
                        }
                     } else {
                        tagInfo.setContentTagMapping(contentId, tagId, "-1");
                     }
                  }
               }

               if (!beforeDelTagIds.contains(Long.valueOf(tagId))) {
                  beforeDelTagIds.add(Long.valueOf(tagId));
               }

               log.setTag_id(Long.valueOf(tagId));
               log.setEvent_type("0");
               tagInfo.addLogContentTag(log);
            }
         } catch (Exception var24) {
            this.logger.error(var24);
            failList.add(contentId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority','Content Read Authority', 'Content Manage Authority')")
   public V2CommonResultResource shareContents(V2ContentShare body) throws Exception {
      ShareFolderInfo sharedFolderInfo = ShareFolderInfoImpl.getInstance();
      V2CommonResultResource resourceList = new V2CommonResultResource();
      User user = SecurityUtils.getLoginUser();
      if (user.getRoot_group_id() != 0L) {
         long sharedFolderId = Long.parseLong(body.getSharedGroupId());
         long myRootOrgId = user.getRoot_group_id();
         List sharedOrgIds = sharedFolderInfo.getOrgListByShareFolderId(sharedFolderId);
         if (sharedOrgIds != null && !sharedOrgIds.contains(myRootOrgId)) {
            RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
         }
      }

      boolean flag = false;
      List contentIds = body.getContentIds();

      for(int i = 0; i < contentIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, (String)contentIds.get(i));
         } catch (Exception var15) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
      }

      String sharedGroupId = body.getSharedGroupId();
      List successResourceList = new ArrayList();
      List failResourceList = new ArrayList();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();

      for(int i = 0; i < contentIds.size(); ++i) {
         try {
            if (body.getSharedGroupId() != null && !body.getSharedGroupId().isEmpty()) {
               boolean ret = sharedFolderInfo.shareContent((String)contentIds.get(i), Long.valueOf(sharedGroupId));
               if (ret) {
                  contentInfo.setContentShare((String)contentIds.get(i), 1L);
                  V2ContentResource resource = this.getContentDetail((String)contentIds.get(i));
                  if (resource != null) {
                     successResourceList.add(resource);
                  } else {
                     failResourceList.add(contentIds.get(i));
                  }
               } else {
                  failResourceList.add(contentIds.get(i));
               }
            } else {
               failResourceList.add(contentIds.get(i));
            }
         } catch (Exception var14) {
            this.logger.error(var14);
            failResourceList.add(contentIds.get(i));
         }
      }

      resourceList.setSuccessList(successResourceList);
      resourceList.setFailList(failResourceList);
      return resourceList;
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority','Content Read Authority', 'Content Manage Authority')")
   public V2CommonResultResource releaseSharing(V2ContentShare body) throws Exception {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      List contentIds = body.getContentIds();
      User loginUser = SecurityUtils.getUserContainer().getUser();
      if (!this.checkUserAuthorityToUnshare(contentIds, cInfo)) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
      }

      V2CommonResultResource resourceList = new V2CommonResultResource();
      List successResourceList = new ArrayList();
      List failResourceList = new ArrayList();
      ShareFolderInfo sharedFolderInfo = ShareFolderInfoImpl.getInstance();

      for(int i = 0; i < contentIds.size(); ++i) {
         try {
            boolean ret = sharedFolderInfo.unshareContent((String)contentIds.get(i));
            if (ret) {
               V2ContentResource resource = this.getContentDetail((String)contentIds.get(i));
               if (resource != null) {
                  successResourceList.add(resource);
               } else {
                  failResourceList.add(contentIds.get(i));
               }
            } else {
               failResourceList.add(contentIds.get(i));
            }
         } catch (Exception var12) {
            this.logger.error(var12);
            failResourceList.add(contentIds.get(i));
         }
      }

      resourceList.setSuccessList(successResourceList);
      resourceList.setFailList(failResourceList);
      return resourceList;
   }

   private NotificationData makeNotificationData(String contentId) throws SQLException {
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      Content content = contentInfo.getContentActiveVerInfo(contentId);
      NotificationData notificationData = new NotificationData();
      notificationData.setName(content.getContent_name());
      notificationData.setOrgId(content.getOrganization_id());
      notificationData.setOrgName(userGroupInfo.getGroupNameByGroupId(content.getOrganization_id()));
      notificationData.setUserName(this.getLoginUserId());
      return notificationData;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Content Read Authority', 'Content Write Authority', 'Content Manage Authority','Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Add Authority', 'Content Schedule Manage Authority')")
   public V2ContentThumbnailResource downloadThumbnail(String thumbnailId, Integer width, Integer height, String resolution, String logoFilePath, String capturedFileName) throws SQLException, IOException {
      ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
      String thumb_file_id = "";
      String thumb_filename = "";
      String getWidth = width != null ? String.valueOf(width) : "";
      String getHeight = height != null ? String.valueOf(height) : "";
      int newWidth = 0;
      int newHeight = 0;
      boolean isCapturedImage = false;
      Path path;
      String THUMBNAIL_HOME;
      String filePath;
      if (thumbnailId.equals("CUSTOM_LOGO")) {
         if (logoFilePath == null | (logoFilePath != null && logoFilePath.isEmpty())) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_NOT_NULL_OR_EMPTY, new String[]{"logoFilePath"});
         }

         THUMBNAIL_HOME = "";

         try {
            THUMBNAIL_HOME = CommonConfig.get("UPLOAD_HOME").replace('/', File.separatorChar);
         } catch (ConfigException var36) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"CUSTOM_LOGO_HOME"});
         }

         filePath = SecurityUtils.directoryTraversalChecker(logoFilePath, "REST API v2.0");
         thumb_file_id = "CUSTOM_LOGO";
         thumb_filename = filePath.substring(filePath.lastIndexOf("\\") + 1);
         path = Paths.get(THUMBNAIL_HOME + File.separator + filePath);
      } else {
         if (thumbnailId.equals("CAPTURE")) {
            if (!DeviceUtils.isDeviceReadAuthorityPresent()) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_ACCESS_DENIED, new String[]{"User does not have device read authority"});
            }

            isCapturedImage = true;
            thumb_file_id = thumbnailId;
            thumb_filename = capturedFileName;
         } else {
            ContentInfo contentInfo = ContentInfoImpl.getInstance();
            Map thumbnailInfo = new HashMap();
            if (resolution.equalsIgnoreCase("HD")) {
               Map hdThumbnailInfo = contentInfo.getHDThumbnailInfo(thumbnailId);
               ((Map)thumbnailInfo).put("thumb_file_id", hdThumbnailInfo.get("thumbnailFileId"));
               ((Map)thumbnailInfo).put("thumb_file_name", hdThumbnailInfo.get("filename"));
            } else {
               thumbnailInfo = contentInfo.getThumbnailByThumbnailFileId(thumbnailId);
            }

            if (((Map)thumbnailInfo).isEmpty()) {
               throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"thumbnailId"});
            }

            thumb_file_id = (String)((Map)thumbnailInfo).get("thumb_file_id");
            thumb_filename = (String)((Map)thumbnailInfo).get("thumb_file_name");
            if (resolution != null && resolution.equalsIgnoreCase("SMALL")) {
               thumb_filename = thumb_filename + "_SMALL.PNG";
            } else if (resolution != null && resolution.equalsIgnoreCase("MEDIUM")) {
               thumb_filename = thumb_filename + "_MEDIUM.PNG";
            }
         }

         THUMBNAIL_HOME = "";

         try {
            if (isCapturedImage) {
               THUMBNAIL_HOME = CommonConfig.get("UPLOAD_HOME") + File.separator + CommonConfig.get("CAPTURE_DIR");
            } else {
               THUMBNAIL_HOME = CommonConfig.get("THUMBNAIL_HOME").replace('/', File.separatorChar);
            }
         } catch (ConfigException var35) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"THUMBNAIL_HOME"});
         }

         thumb_filename = SecurityUtils.directoryTraversalChecker(thumb_filename, "REST API v2.0");
         if (isCapturedImage) {
            thumb_filename = capturedFileName;
            path = Paths.get(THUMBNAIL_HOME + File.separator + capturedFileName);
         } else {
            path = Paths.get(THUMBNAIL_HOME + File.separator + thumb_file_id + File.separator + thumb_filename);
         }
      }

      if (Files.exists(path, new LinkOption[0])) {
         InputStream inputStream = Files.newInputStream(path);
         ImageIO.setUseCache(false);
         filePath = thumb_filename.substring(thumb_filename.lastIndexOf(46) + 1);
         Iterator readers = ImageIO.getImageReadersBySuffix(filePath);
         if (readers.hasNext()) {
            ImageReader imageReader = (ImageReader)readers.next();
            BufferedImage sourceImage = ImageIO.read(inputStream);
            int originalWidth = sourceImage.getWidth();
            int originalHeight = sourceImage.getHeight();
            int boundaryWidth = originalWidth;
            if (!getWidth.isEmpty()) {
               boundaryWidth = Integer.valueOf(getWidth);
            }

            int boundaryHeight = originalHeight;
            if (!getHeight.isEmpty()) {
               boundaryHeight = Integer.valueOf(getHeight);
            }

            newWidth = originalWidth;
            newHeight = originalHeight;
            if (originalWidth > boundaryWidth) {
               newWidth = boundaryWidth;
               newHeight = boundaryWidth * originalHeight / originalWidth;
            } else if (originalWidth < boundaryWidth) {
               newWidth = boundaryWidth;
            }

            if (newHeight > boundaryHeight) {
               newHeight = boundaryHeight;
               newWidth = boundaryHeight * originalWidth / originalHeight;
               if (newWidth > boundaryWidth) {
                  if (newWidth > boundaryHeight) {
                     newHeight = boundaryHeight / (newWidth / boundaryHeight);
                  } else {
                     newHeight = boundaryHeight / (boundaryHeight / newWidth);
                  }

                  newWidth = boundaryWidth;
               }
            } else if (originalHeight < boundaryHeight) {
               newHeight = boundaryHeight;
            }

            ToolkitImage image = (ToolkitImage)sourceImage.getScaledInstance(newWidth, newHeight, 4);
            image.getWidth();
            BufferedImage buffered = image.getBufferedImage();

            try {
               ImageIO.write(buffered, "png", outputStream);
            } catch (Exception var33) {
               this.logger.error(var33.toString(), var33);
            } finally {
               imageReader.dispose();
            }
         }

         if (inputStream != null) {
            inputStream.close();
         }

         if (outputStream != null) {
            outputStream.flush();
            outputStream.close();
         }

         String src = "data: image/png;base64," + java.util.Base64.getEncoder().encodeToString(outputStream.toByteArray());
         return new V2ContentThumbnailResource(src, thumb_file_id, thumb_filename, newWidth, newHeight);
      } else {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"file path"});
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Manage Authority')")
   public V2CommonResultResource gotoRecyclebin(V2ContentDeleteParam resource) throws Exception {
      boolean flag = false;
      List contentIds = resource.getContentIds();

      for(int i = 0; i < contentIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, (String)contentIds.get(i));
         } catch (Exception var29) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
      }

      LogInfo logInfo = LogInfoImpl.getInstance();
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      TagInfo tagInfo = TagInfoImpl.getInstance();
      CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
      V2CommonResultResource result = new V2CommonResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      String sessionId = UUID.randomUUID().toString();
      List notifications = new ArrayList();
      List triggerTagList = new ArrayList();

      for(int i = 0; i < contentIds.size(); ++i) {
         V2ContentDeleteFail obj;
         try {
            new ArrayList();
            if (!SecurityUtils.checkReadPermissionWithOrgAndId("Content", (String)contentIds.get(i))) {
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_NOT_HAVE, new String[]{"content read"});
            }

            Content curContent = cInfo.getContentAndFileActiveVerInfo((String)contentIds.get(i));
            if (curContent != null && SecurityUtils.getLoginUser().getRoot_group_id() != 0L && SecurityUtils.getLoginUser().getRoot_group_id() != curContent.getOrganization_id()) {
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
            }

            if (cInfo.isDeletableContent((String)contentIds.get(i), sessionId)) {
               if (cInfo.deleteContent((String)contentIds.get(i), cInfo.getContentOrgCreatorId((String)contentIds.get(i)), sessionId) <= 0) {
                  RestExceptionCode error = RestExceptionCode.INTERNAL_SERVER_ERROR_DELETE_FAIL;
                  V2ContentDeleteFail obj = new V2ContentDeleteFail();
                  obj.setId((String)contentIds.get(i));
                  obj.setReason(error.generateFormattedMessages("content"));
                  obj.setReasonCode(error.getCode());
                  failList.add(obj);
               } else {
                  List tagList = tagInfo.getContentTagList((String)contentIds.get(i));
                  if (tagList != null && tagList.size() > 0) {
                     Iterator var35 = tagList.iterator();

                     while(var35.hasNext()) {
                        TagEntity tag = (TagEntity)var35.next();
                        if (!triggerTagList.contains((long)tag.getTag_id())) {
                           triggerTagList.add((long)tag.getTag_id());
                        }
                     }

                     tagInfo.deleteTagInfoFromContentId((String)contentIds.get(i));
                  }

                  categoryInfo.deleteCategoryFromContentId((String)contentIds.get(i));

                  try {
                     notifications.add(this.makeNotificationData((String)contentIds.get(i)));
                  } catch (Exception var26) {
                     this.logger.error(var26);
                  }

                  List adsList = cInfo.getAdsContentSettingByContentId((String)contentIds.get(i));
                  List ftpList = cInfo.getFtpContentSettingByContentId((String)contentIds.get(i));
                  List cifsList = cInfo.getCifsContentSettingByContentId((String)contentIds.get(i));
                  if (adsList != null && adsList.size() > 0) {
                     cInfo.updateAdsSettingAsDeleted((String)contentIds.get(i), "Y");
                  }

                  if (ftpList != null && ftpList.size() > 0) {
                     cInfo.updateFtpSettingAsDeleted((String)contentIds.get(i), "Y");
                  }

                  if (cifsList != null && cifsList.size() > 0) {
                     cInfo.updateCifsSettingAsDeleted((String)contentIds.get(i), "Y");
                  }

                  successList.add(contentIds.get(i));
               }
            } else {
               obj = new V2ContentDeleteFail();
               V2ContentDeleteCheckResource checkResource = new V2ContentDeleteCheckResource();
               RestExceptionCode error = RestExceptionCode.BAD_REQUEST_USED_ITEM_NOT_DELETE;
               ArrayList refPlaylistList = new ArrayList();
               ArrayList refScheduleList = new ArrayList();
               ArrayList refEventList = new ArrayList();
               ArrayList refRulesetList = new ArrayList();
               ArrayList refConvertDataList = new ArrayList();
               String contentName = cInfo.getContentName((String)contentIds.get(i));
               this.setRefPlaylistList((String)contentIds.get(i), refPlaylistList, "PREMIUM");
               this.setRefScheduleList((String)contentIds.get(i), refScheduleList, "PREMIUM");
               this.setRefEventList((String)contentIds.get(i), refEventList);
               this.setRefRulesetList((String)contentIds.get(i), refRulesetList);
               this.setRefConvertDataList((String)contentIds.get(i), refConvertDataList);
               obj.setId((String)contentIds.get(i));
               obj.setReason(error.generateFormattedMessages("content"));
               obj.setReasonCode(error.getCode());
               obj.setContentName(contentName);
               checkResource.setRefEventList(refEventList);
               checkResource.setRefPlaylistList(refPlaylistList);
               checkResource.setRefScheduleList(refScheduleList);
               checkResource.setRefRulesetList(refRulesetList);
               checkResource.setRefConvertDataList(refConvertDataList);
               obj.setResource(checkResource);
               failList.add(obj);
            }
         } catch (RestServiceException var27) {
            this.logger.error(var27);
            V2ContentDeleteFail obj = new V2ContentDeleteFail();
            obj.setId((String)contentIds.get(i));
            obj.setReason(var27.getErrorMessage());
            obj.setReasonCode(var27.getErrorCode());
            failList.add(obj);
         } catch (Exception var28) {
            this.logger.error(var28);
            RestExceptionCode error = RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN;
            obj = new V2ContentDeleteFail();
            obj.setId((String)contentIds.get(i));
            obj.setReason(error.getMessage());
            obj.setReasonCode(error.getCode());
            failList.add(obj);
         }
      }

      if (triggerTagList.size() > 0) {
         tagInfo.setPlaylistTrigger(triggerTagList);
      }

      if (notifications.size() > 0) {
         MailUtil.sendContentEventMail(notifications, "Delete content");
      }

      result.setSuccessList(successList);
      result.setFailList(failList);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Content Manage Authority')")
   public V2CommonResultResource forceGotoRecyclebin(V2ContentDeleteParam resource) throws Exception {
      boolean flag = false;
      List contentIds = resource.getContentIds();

      for(int i = 0; i < contentIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, (String)contentIds.get(i));
         } catch (Exception var29) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
      }

      ContentInfo cInfo = ContentInfoImpl.getInstance();
      LogInfo logInfo = LogInfoImpl.getInstance();
      TagInfo tagInfo = TagInfoImpl.getInstance();
      CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
      PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();
      V2CommonResultResource result = new V2CommonResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      String sessionId = UUID.randomUUID().toString();
      List notifications = new ArrayList();
      List triggerTagList = new ArrayList();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String ipAddress = "mobile";

      for(int i = 0; i < contentIds.size(); ++i) {
         RestExceptionCode map;
         V2ContentDeleteFail playlistId;
         try {
            if (!cInfo.isForceDeletableContent((String)contentIds.get(i))) {
               failList.addAll(this.getReferredItemsByContentId((String)contentIds.get(i)));
            } else {
               List pList = pInfo.getActivePlaylistCountOne((String)contentIds.get(i));
               if (pList != null) {
                  map = null;
                  playlistId = null;
                  Long versionId = null;
                  String creatorId = null;
                  Long activeVersionId = null;

                  for(int j = 0; j < pList.size(); ++j) {
                     Map map = (Map)pList.get(j);
                     String playlistId = (String)map.get("playlist_id");
                     versionId = (Long)map.get("version_id");
                     activeVersionId = pInfo.getPlaylistActiveVersionId(playlistId);
                     if (versionId == activeVersionId) {
                        DeleteContentUtils.checkPlaylistFromSchedule(playlistId, ipAddress);
                     }

                     if (versionId == activeVersionId && pInfo.isExistMapPlaylistID(playlistId, activeVersionId)) {
                        creatorId = pInfo.getCreatorIdByPlaylistId(playlistId);
                        pInfo.deletePlaylistVersion(playlistId, Long.toString(activeVersionId), creatorId);
                        Long maxVersionId = pInfo.getPlaylistMaxVer(playlistId);
                        pInfo.setVersionPlaylistActive(playlistId, maxVersionId);
                     } else {
                        creatorId = pInfo.getCreatorIdByPlaylistId(playlistId);
                        pInfo.deletePlaylistVersion(playlistId, Long.toString(versionId), creatorId);
                     }
                  }
               }

               DeleteContentUtils.checkContentFromPlaylist((String)contentIds.get(i), ipAddress);
               DeleteContentUtils.checkContentFromSchedule((String)contentIds.get(i), ipAddress);
               DeleteContentUtils.checkContentFromConvertTable((String)contentIds.get(i));
               DeleteContentUtils.checkContentFromDLK((String)contentIds.get(i), ipAddress);
               DeleteContentUtils.checkContentFromEventCondition((String)contentIds.get(i));
               Content curContent = cInfo.getContentAndFileActiveVerInfo((String)contentIds.get(i));
               if (curContent == null) {
                  curContent = cInfo.getTLFDInfo((String)contentIds.get(i));
                  if (curContent == null) {
                     throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONTENT_MANAGEMENT_FAIL);
                  }
               }

               if (cInfo.deleteContent((String)contentIds.get(i), curContent.getCreator_id(), sessionId) <= 0) {
                  RestExceptionCode error = RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN;
                  V2ContentDeleteFail obj = new V2ContentDeleteFail();
                  obj.setId((String)contentIds.get(i));
                  obj.setReason(error.getMessage());
                  obj.setReasonCode(error.getCode());
                  failList.add(obj);
               } else {
                  List tagList = tagInfo.getContentTagList((String)contentIds.get(i));
                  if (tagList != null && tagList.size() > 0) {
                     Iterator var37 = tagList.iterator();

                     while(var37.hasNext()) {
                        TagEntity tag = (TagEntity)var37.next();
                        if (!triggerTagList.contains((long)tag.getTag_id())) {
                           triggerTagList.add((long)tag.getTag_id());
                        }
                     }

                     tagInfo.deleteTagInfoFromContentId((String)contentIds.get(i));
                  }

                  categoryInfo.deleteCategoryFromContentId((String)contentIds.get(i));

                  try {
                     notifications.add(this.makeNotificationData((String)contentIds.get(i)));
                  } catch (Exception var26) {
                     this.logger.error(var26);
                  }

                  successList.add(contentIds.get(i));
               }
            }
         } catch (RestServiceException var27) {
            this.logger.error(var27);
            V2ContentDeleteFail obj = new V2ContentDeleteFail();
            obj.setId((String)contentIds.get(i));
            obj.setReason(var27.getErrorMessage());
            obj.setReasonCode(var27.getErrorCode());
            failList.add(obj);
         } catch (Exception var28) {
            map = RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN;
            this.logger.error(var28);
            playlistId = new V2ContentDeleteFail();
            playlistId.setId((String)contentIds.get(i));
            playlistId.setReason(map.getMessage());
            playlistId.setReasonCode(map.getCode());
            failList.add(playlistId);
         }
      }

      if (triggerTagList.size() > 0) {
         tagInfo.setPlaylistTrigger(triggerTagList);
      }

      if (notifications.size() > 0) {
         MailUtil.sendContentEventMail(notifications, "Delete content");
      }

      result.setSuccessList(successList);
      result.setFailList(failList);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Content Manage Authority')")
   public V2CommonResultResource permanentlyDeletePlaylist(V2ContentDeleteParam resource) throws Exception {
      boolean flag = false;
      List contentIds = resource.getContentIds();

      for(int i = 0; i < contentIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, (String)contentIds.get(i));
         } catch (Exception var27) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
      }

      ContentInfo cInfo = ContentInfoImpl.getInstance();
      LogInfo logInfo = LogInfoImpl.getInstance();
      V2CommonResultResource result = new V2CommonResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      List notifications = new ArrayList();
      String sessionId = UUID.randomUUID().toString();

      for(int i = 0; i < contentIds.size(); ++i) {
         try {
            Content curContent = cInfo.getContentAndFileActiveVerInfo((String)contentIds.get(i));
            if (curContent == null) {
               curContent = cInfo.getTLFDInfo((String)contentIds.get(i));
               if (curContent == null) {
                  ContentFile contentFile = cInfo.getMainFileInfo((String)contentIds.get(i));
                  if (contentFile != null) {
                     throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONTENT_MANAGEMENT_FAIL);
                  }
               }
            }

            try {
               notifications.add(this.makeNotificationData((String)contentIds.get(i)));
            } catch (Exception var24) {
               this.logger.error(var24);
            }

            if (!cInfo.isDeletableContent((String)contentIds.get(i), sessionId)) {
               failList.addAll(this.getReferredItemsByContentId((String)contentIds.get(i)));
            } else {
               cInfo.deleteContentApproverMapByContentId((String)contentIds.get(i));
               boolean isFtpCifsContent = false;
               String CifsFileIP;
               String CifsFileDirectory;
               String meta_file;
               String meta_folder;
               String thumb_file_name;
               File metaFolder;
               String thumb_folder;
               String schedulerJobGroup;
               String CifsFileLoginId;
               if (!cInfo.getFtpUserIdByContentId((String)contentIds.get(i)).equals("")) {
                  isFtpCifsContent = true;
                  CifsFileLoginId = cInfo.getFtpUserIdByContentId((String)contentIds.get(i));
                  CifsFileIP = cInfo.getFtpIpByContentId((String)contentIds.get(i));
                  CifsFileDirectory = cInfo.getFtpPathByContentId((String)contentIds.get(i));
                  meta_file = cInfo.getCreatorIdByContentId((String)contentIds.get(i));
                  meta_folder = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
                  thumb_file_name = ContentUtils.getFtpLocalPath(meta_folder, CifsFileIP, CifsFileLoginId, CifsFileDirectory, meta_file);
                  this.logger.info("delete - ftpFileFolder : " + thumb_file_name);
                  metaFolder = SecurityUtils.getSafeFile(thumb_file_name);
                  if (this.deleteDirectoryRecursive(metaFolder)) {
                     this.logger.info("FtpFilesFolder and files are cleaned up successfully!");
                  }

                  thumb_folder = "FTP_" + (String)contentIds.get(i);
                  schedulerJobGroup = "UpdateFtpContentService";
                  CommonUtils.deleteJob(thumb_folder, schedulerJobGroup);
               }

               if (!cInfo.getCifsUserIdByContentId((String)contentIds.get(i)).equals("")) {
                  isFtpCifsContent = true;
                  CifsFileLoginId = cInfo.getCifsUserIdByContentId((String)contentIds.get(i));
                  CifsFileIP = cInfo.getCifsIpByContentId((String)contentIds.get(i));
                  CifsFileDirectory = cInfo.getCifsPathByContentId((String)contentIds.get(i));
                  meta_file = cInfo.getCreatorIdByContentId((String)contentIds.get(i));
                  meta_folder = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
                  thumb_file_name = ContentUtils.getCifsLocalPath(meta_folder, CifsFileIP, CifsFileLoginId, CifsFileDirectory, meta_file);
                  this.logger.info("delete - cifsFileFolder : " + thumb_file_name);
                  metaFolder = SecurityUtils.getSafeFile(thumb_file_name);
                  if (this.deleteDirectoryRecursive(metaFolder)) {
                     this.logger.info("CifsFilesFolder and files are cleaned up successfully!");
                  }

                  thumb_folder = "CIFS_" + (String)contentIds.get(i);
                  schedulerJobGroup = "UpdateCifsContentService";
                  CommonUtils.deleteJob(thumb_folder, schedulerJobGroup);
               }

               List fileList = cInfo.getFileList((String)contentIds.get(i));
               List movieThumblist = cInfo.getThumbMovieFileList((String)contentIds.get(i));
               if (movieThumblist != null && movieThumblist.size() > 0) {
                  for(int k = 0; k < movieThumblist.size(); ++k) {
                     Map map = (Map)movieThumblist.get(k);
                     meta_folder = (String)map.get("FILE_ID");
                     thumb_file_name = cInfo.getFileName((String)map.get("FILE_ID"));
                     String thumb_path = CommonConfig.get("THUMBNAIL_HOME").replace('/', File.separatorChar) + File.separator + meta_folder + File.separator;
                     thumb_folder = CommonConfig.get("THUMBNAIL_HOME").replace('/', File.separatorChar) + File.separator + meta_folder + File.separator;
                     File delFile = SecurityUtils.getSafeFile(thumb_path + thumb_file_name);
                     File delFolder = SecurityUtils.getSafeFile(thumb_folder);
                     delFile.delete();
                     delFolder.delete();
                  }

                  cInfo.deleteThumbMovieMap((String)contentIds.get(i));
               }

               ContentUtils.deleteThumbFiles((String)contentIds.get(i));
               DownloadInfo downloadDao = DownloadInfoImpl.getInstance();

               for(int j = 0; j < fileList.size(); ++j) {
                  ContentFile file = (ContentFile)fileList.get(j);
                  if (cInfo.isDeletableFile(file.getFile_id(), (String)contentIds.get(i))) {
                     cInfo.deleteFile(file.getFile_id());
                  }
               }

               if (isFtpCifsContent) {
                  ContentFile mainMetaFile = cInfo.getMainFileInfo((String)contentIds.get(i));
                  if (mainMetaFile != null) {
                     if (cInfo.isDeletableFile(mainMetaFile.getFile_id(), (String)contentIds.get(i))) {
                        cInfo.deleteFile(mainMetaFile.getFile_id());
                     }
                  } else {
                     this.logger.error("[MagicInfo_FTP][MagicInfo_CFIS] main meta file is null, content id : " + (String)contentIds.get(i));
                  }

                  cInfo.deletePollingFileInfo((String)contentIds.get(i));
                  cInfo.deletePollingInfo((String)contentIds.get(i));
               }

               meta_file = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separator + "contents_home" + File.separator + "contents_meta" + File.separator + (String)contentIds.get(i) + File.separator + "ContentsMetadata.CSD";
               meta_folder = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separator + "contents_home" + File.separator + "contents_meta" + File.separator + (String)contentIds.get(i) + File.separator;
               File metaFile = SecurityUtils.getSafeFile(meta_file);
               metaFolder = SecurityUtils.getSafeFile(meta_folder);
               metaFile.delete();
               metaFolder.delete();
               cInfo.deleteContentCompletely((String)contentIds.get(i));
               successList.add(contentIds.get(i));
            }
         } catch (RestServiceException var25) {
            this.logger.error(var25);
            V2ContentDeleteFail obj = new V2ContentDeleteFail();
            obj.setId((String)contentIds.get(i));
            obj.setReason(var25.getErrorMessage());
            obj.setReasonCode(var25.getErrorCode());
            failList.add(obj);
         } catch (Exception var26) {
            this.logger.error(var26);
            RestExceptionCode error = RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN;
            V2ContentDeleteFail obj = new V2ContentDeleteFail();
            obj.setId((String)contentIds.get(i));
            obj.setReason(error.getMessage());
            obj.setReasonCode(error.getCode());
            failList.add(obj);
         }
      }

      if (notifications.size() > 0) {
         MailUtil.sendContentEventMail(notifications, "Delete content permanently");
      }

      result.setSuccessList(successList);
      result.setFailList(failList);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2ContentVersion getContentVersion(String contentId, Long versionId) throws Exception {
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      V2ContentVersion contentVersionResource = new V2ContentVersion();
      if (contentId != null && versionId > 0L) {
         Content contentView = contentInfo.getContentVerInfo(contentId, versionId);
         if (contentView != null) {
            contentVersionResource.setVersionId(contentView.getVersion_id());
            contentVersionResource.setLastModified(contentView.getCreate_date());
            contentVersionResource.setIsActive(contentView.getIs_active());
            contentVersionResource.setThumbnailId(contentView.getThumb_file_id());
            return contentVersionResource;
         } else {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"content id and version id"});
         }
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"version id"});
      }
   }

   boolean checkUserAuthorityToUnshare(List contentIds, ContentInfo cInfo) throws SQLException {
      User loginUser = SecurityUtils.getUserContainer().getUser();
      if (loginUser != null && loginUser.getRole_name() != null) {
         String roleNameofLoginUser = loginUser.getRole_name();
         if (roleNameofLoginUser.equals("Server Administrator")) {
            return true;
         } else {
            AbilityUtils ability = new AbilityUtils();
            if (loginUser.getRoot_group_id() == 0L) {
               if (roleNameofLoginUser.equals("Administrator")) {
                  return true;
               }

               if (ability.checkAuthority("Content Manage")) {
                  return true;
               }
            }

            Iterator var6 = contentIds.iterator();

            while(var6.hasNext()) {
               String contentId = (String)var6.next();
               Content content = cInfo.getContentActiveVerInfo(contentId);
               if (roleNameofLoginUser.equals("Administrator")) {
                  if (loginUser.getRoot_group_id() != content.getOrganization_id()) {
                     return false;
                  }
               } else if (ability.checkAuthority("Content Manage")) {
                  if (loginUser.getRoot_group_id() != content.getOrganization_id()) {
                     return false;
                  }
               } else if (!content.getCreator_id().equals(loginUser.getUser_id())) {
                  return false;
               }
            }

            return true;
         }
      } else {
         return false;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2ContentCheckResource contentIdCheck(V2ContentIds body) throws Exception {
      boolean flag = false;
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      List contentIds = body.getContentIds();
      if (contentIds == null) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"contentIds"});
      } else {
         Iterator var5;
         String contentId;
         if (body.getAuthority() != null && body.getAuthority().equalsIgnoreCase("READ")) {
            var5 = contentIds.iterator();

            while(var5.hasNext()) {
               contentId = (String)var5.next();
               if (!SecurityUtils.checkReadPermissionWithOrgAndId("Content", contentId)) {
                  RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
               }
            }
         } else if (body.getAuthority() != null && body.getAuthority().equalsIgnoreCase("UNSHARED")) {
            if (!this.checkUserAuthorityToUnshare(contentIds, cInfo)) {
               RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
            }
         } else {
            var5 = contentIds.iterator();

            while(var5.hasNext()) {
               contentId = (String)var5.next();

               try {
                  RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
               } catch (Exception var17) {
                  flag = true;
                  break;
               }
            }

            if (flag) {
               RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
            }
         }

         V2ContentCheckResource results = new V2ContentCheckResource();
         List resource = new ArrayList();
         boolean result = false;
         int cnt = 0;

         for(int i = 0; contentIds != null && i < contentIds.size(); ++i) {
            Content content = cInfo.getContentActiveVerInfo((String)contentIds.get(i));
            if (content != null && content.getExpiration_date() != null && content.getExpiration_date().compareTo(DateUtils.getCurrentTime("yyyyMMdd")) < 0) {
               V2ExpiredContentListResource tmp = new V2ExpiredContentListResource();
               tmp.setContentId(content.getContent_id());
               tmp.setContentName(content.getContent_name());
               tmp.setExpirationDate(content.getExpiration_date());
               resource.add(tmp);
               results.setExpiredContentList(resource);
               ++cnt;
            }
         }

         if (cnt > 0) {
            results.setMessage("This content has expired");
            results.setStatus("non-addable");
            return results;
         } else {
            String deviceType = "";
            String deviceTypeVersion = "";
            Long contentPriority = null;

            for(int i = 0; i < contentIds.size(); ++i) {
               Content content = cInfo.getContentAndFileActiveVerInfo((String)contentIds.get(i));
               if (content == null || content.getMedia_type() == null || content.getMedia_type().equalsIgnoreCase("SOUND") || content.getMedia_type().equalsIgnoreCase("VWL") || content.getMedia_type().equalsIgnoreCase("LFT")) {
                  result = false;
                  break;
               }

               result = true;
               String tmpDeviceType = "";
               String tmpDeviceTypeVersion = "";
               if (StringUtils.isBlank(content.getDevice_type())) {
                  Map typeMap = ContentUtils.getContentDeviceTypeAndVersion(content);
                  tmpDeviceType = typeMap.get("deviceType").toString();
                  tmpDeviceTypeVersion = typeMap.get("deviceTypeVersion").toString();
               } else {
                  tmpDeviceType = content.getDevice_type();
                  tmpDeviceTypeVersion = content.getDevice_type_version() + "";
               }

               Long tmpContentPriority = CommonUtils.getMinPriorityByDeviceinfo(tmpDeviceType, tmpDeviceTypeVersion);
               if (tmpContentPriority < 0L) {
                  deviceType = tmpDeviceType;
                  deviceTypeVersion = tmpDeviceTypeVersion;
                  break;
               }

               if (contentPriority == null || contentPriority < tmpContentPriority) {
                  contentPriority = tmpContentPriority;
                  deviceType = tmpDeviceType;
                  deviceTypeVersion = tmpDeviceTypeVersion;
               }
            }

            results.setResult(result);
            if (result && StringUtils.isNotBlank(deviceType) && StringUtils.isNotBlank(deviceTypeVersion)) {
               results.setDeviceType(deviceType);
               results.setDeviceTypeVersion(deviceTypeVersion);
            }

            return results;
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public void contentDownload(V2ContentIds contents, HttpServletRequest request, HttpServletResponse response) throws Exception {
      List contentIds = contents.getContentIds();
      boolean flag = false;
      User currentUser = SecurityUtils.getUserContainer().getUser();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      if (contentIds == null) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"contentIds"});
      } else {
         Content downloadPath;
         for(int i = 0; i < contentIds.size(); ++i) {
            try {
               RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, (String)contentIds.get(i));
               downloadPath = contentInfo.getContentAndFileActiveVerInfo((String)contentIds.get(i));
               if (downloadPath.getApproval_status().equalsIgnoreCase("UNAPPROVED") && currentUser.getRole_name().equalsIgnoreCase("Content Uploader") && !downloadPath.getCreator_id().equalsIgnoreCase(currentUser.getUser_id())) {
                  flag = true;
               }
            } catch (Exception var97) {
               flag = true;
               break;
            }
         }

         if (flag) {
            RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
         }

         String userId = currentUser.getUser_id();
         downloadPath = null;
         String downloadPath = CommonConfig.get("UPLOAD_HOME") + File.separator + "contents_download";
         File downloadPahHome = SecurityUtils.getSafeFile(downloadPath);
         if (!downloadPahHome.exists()) {
            downloadPahHome.mkdir();
         }

         String zipFilePath = downloadPath + File.separator + "download.zip";
         FileOutputStream fos = null;
         ZipOutputStream zos = null;
         InputStream inputStream = null;
         InputStreamReader reader = null;

         String mime;
         try {
            fos = new FileOutputStream(zipFilePath);
            zos = new ZipOutputStream(fos);
            Iterator var16 = contentIds.iterator();

            while(var16.hasNext()) {
               mime = (String)var16.next();

               try {
                  Content content = contentInfo.getContentAndFileActiveVerInfo(mime);
                  List fileList = content.getArr_file_list();
                  String mainFileId;
                  HashMap supportFileItemsMap;
                  String fileId;
                  String realFullPath;
                  if (!StringUtils.isNoneBlank(new CharSequence[]{content.getMedia_type()}) || !content.getMedia_type().equalsIgnoreCase("LFD") && !content.getMedia_type().equalsIgnoreCase("SAPP")) {
                     if (StringUtils.isNoneBlank(new CharSequence[]{content.getMedia_type()}) && content.getMedia_type().equalsIgnoreCase("HTML")) {
                        mainFileId = content.getMain_file_id();
                        supportFileItemsMap = new HashMap();
                        List contentFileList = new ArrayList();

                        int i;
                        String filePath;
                        label1192:
                        for(i = 0; i < fileList.size(); ++i) {
                           if (mainFileId.equalsIgnoreCase(((ContentFile)fileList.get(i)).getFile_id())) {
                              inputStream = null;
                              reader = null;

                              try {
                                 XPath xpath = XPathFactory.newInstance().newXPath();
                                 filePath = ((ContentFile)fileList.get(i)).getFile_path() + File.separator + ((ContentFile)fileList.get(i)).getFile_name();
                                 File file = new File(filePath);
                                 inputStream = new FileInputStream(file);
                                 reader = new InputStreamReader(inputStream, "UTF-8");
                                 InputSource is = new InputSource(reader);
                                 Document document = DocumentBuilderFactory.newInstance().newDocumentBuilder().parse(is);
                                 NodeList cols = (NodeList)xpath.evaluate("/Content/SupportFileItems/FileItem", document, XPathConstants.NODESET);
                                 int j = 0;

                                 while(true) {
                                    if (j >= cols.getLength()) {
                                       break label1192;
                                    }

                                    Node node = cols.item(j);
                                    if (node instanceof Element) {
                                       Element element = (Element)node;
                                       fileId = element.getElementsByTagName("FileID").item(0).getTextContent();
                                       realFullPath = element.getElementsByTagName("RealFullPath").item(0).getTextContent();
                                       String pureFileItem = element.getElementsByTagName("PureFileItem").item(0).getTextContent();
                                       int startIndex = 0;
                                       int endIndex = realFullPath.lastIndexOf(pureFileItem);
                                       if (realFullPath.indexOf("./") == 0 || realFullPath.indexOf(".\\") == 0) {
                                          startIndex = 2;
                                       }

                                       if (startIndex <= endIndex - 1) {
                                          realFullPath = realFullPath.substring(startIndex, endIndex - 1);
                                          supportFileItemsMap.put(realFullPath, fileId);
                                          ContentFile contentFileTemp = new ContentFile();
                                          contentFileTemp.setFile_id(fileId);
                                          contentFileTemp.setFile_path(realFullPath);
                                          contentFileTemp.setFile_name(pureFileItem);
                                          contentFileList.add(contentFileTemp);
                                       }
                                    }

                                    ++j;
                                 }
                              } catch (Exception var92) {
                                 this.logger.error("[ContentFileDownloadServlet] fail file compress", var92);
                                 break;
                              } finally {
                                 if (reader != null) {
                                    reader.close();
                                 }

                                 if (inputStream != null) {
                                    inputStream.close();
                                 }

                              }
                           }
                        }

                        for(i = 0; i < fileList.size(); ++i) {
                           ContentFile contentFile = (ContentFile)fileList.get(i);
                           filePath = ((ContentFile)fileList.get(i)).getFile_path() + File.separator + ((ContentFile)fileList.get(i)).getFile_name();
                           boolean contain = false;

                           for(int j = 0; j < contentFileList.size(); ++j) {
                              if (contentFile.getFile_id().equals(((ContentFile)contentFileList.get(j)).getFile_id())) {
                                 CommonUtils.addToZipFile(filePath, ((ContentFile)contentFileList.get(j)).getFile_path(), zos);
                                 contain = true;
                              }
                           }

                           if (!contain) {
                              CommonUtils.addToZipFile(filePath, zos);
                           }
                        }
                     } else {
                        for(int i = 0; i < fileList.size(); ++i) {
                           String filePath = ((ContentFile)fileList.get(i)).getFile_path() + File.separator + ((ContentFile)fileList.get(i)).getFile_name();
                           CommonUtils.addToZipFile(filePath, zos);
                        }
                     }
                  } else {
                     mainFileId = content.getMain_file_id();
                     supportFileItemsMap = new HashMap();

                     int i;
                     String filePath;
                     label1159:
                     for(i = 0; i < fileList.size(); ++i) {
                        if (mainFileId.equalsIgnoreCase(((ContentFile)fileList.get(i)).getFile_id())) {
                           inputStream = null;
                           reader = null;

                           try {
                              XPath xpath = XPathFactory.newInstance().newXPath();
                              filePath = ((ContentFile)fileList.get(i)).getFile_path() + File.separator + ((ContentFile)fileList.get(i)).getFile_name();
                              File file = new File(filePath);
                              inputStream = new FileInputStream(file);
                              reader = new InputStreamReader(inputStream, "UTF-8");
                              InputSource is = new InputSource(reader);
                              Document document = DocumentUtils.getDocumentBuilderFactoryInstance().newDocumentBuilder().parse(is);
                              NodeList cols = (NodeList)xpath.evaluate("/Content/SupportFileItems/FileItem", document, XPathConstants.NODESET);
                              int j = 0;

                              while(true) {
                                 if (j >= cols.getLength()) {
                                    break label1159;
                                 }

                                 Node node = cols.item(j);
                                 if (node instanceof Element) {
                                    Element element = (Element)node;
                                    String fileId = element.getElementsByTagName("FileID").item(0).getTextContent();
                                    fileId = element.getElementsByTagName("RealFullPath").item(0).getTextContent();
                                    realFullPath = element.getElementsByTagName("PureFileItem").item(0).getTextContent();
                                    int startIndex = 0;
                                    int endIndex = fileId.lastIndexOf(realFullPath);
                                    if (fileId.indexOf("./") == 0 || fileId.indexOf(".\\") == 0) {
                                       startIndex = 2;
                                    }

                                    if (startIndex <= endIndex - 1) {
                                       fileId = fileId.substring(startIndex, endIndex - 1);
                                       supportFileItemsMap.put(fileId, fileId);
                                    }
                                 }

                                 ++j;
                              }
                           } catch (Exception var90) {
                              this.logger.error("[ContentFileDownloadServlet] fail file compress", var90);
                              break;
                           } finally {
                              if (reader != null) {
                                 reader.close();
                              }

                              if (inputStream != null) {
                                 inputStream.close();
                              }

                           }
                        }
                     }

                     for(i = 0; i < fileList.size(); ++i) {
                        ContentFile contentFile = (ContentFile)fileList.get(i);
                        filePath = ((ContentFile)fileList.get(i)).getFile_path() + File.separator + ((ContentFile)fileList.get(i)).getFile_name();
                        if (supportFileItemsMap.containsKey(contentFile.getFile_id())) {
                           CommonUtils.addToZipFile(filePath, (String)supportFileItemsMap.get(contentFile.getFile_id()), zos);
                        } else {
                           CommonUtils.addToZipFile(filePath, zos);
                        }
                     }
                  }
               } catch (SQLException var94) {
                  this.logger.error("[ContentFileDownloadServlet] fail file compress", var94);
               }
            }
         } catch (Exception var95) {
            this.logger.error(var95);
         } finally {
            if (zos != null) {
               try {
                  zos.close();
               } catch (Exception var89) {
                  this.logger.error(var89);
               }
            }

            if (fos != null) {
               try {
                  fos.close();
               } catch (Exception var88) {
                  this.logger.error(var88);
               }
            }

            if (reader != null) {
               try {
                  reader.close();
               } catch (Exception var87) {
                  this.logger.error(var87);
               }
            }

            if (inputStream != null) {
               try {
                  inputStream.close();
               } catch (Exception var86) {
                  this.logger.error(var86);
               }
            }

         }

         File file = new File(SecurityUtils.directoryTraversalChecker(zipFilePath, (String)null));
         mime = "application/zip";
         FileUtils.setFileToResponse(request, response, file, mime);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public void mediaContentDownload(String contentId, HttpServletRequest request, HttpServletResponse response) throws Exception {
      User currentUser = SecurityUtils.getUserContainer().getUser();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      if (contentId.isEmpty()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"contentId"});
      } else {
         Content downloadPath;
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
            downloadPath = contentInfo.getContentAndFileActiveVerInfo(contentId);
            if (downloadPath.getApproval_status().equalsIgnoreCase("UNAPPROVED") && currentUser.getRole_name().equalsIgnoreCase("Content Uploader") && !downloadPath.getCreator_id().equalsIgnoreCase(currentUser.getUser_id())) {
               RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
            }
         } catch (Exception var13) {
            RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
         }

         try {
            downloadPath = null;
            ContentFile contentFile = contentInfo.getMainFileInfo(contentId);
            String downloadPath = CommonConfig.get("UPLOAD_HOME") + File.separator + "contents_download";
            File downloadPathHome = SecurityUtils.getSafeFile(downloadPath);
            if (!downloadPathHome.exists()) {
               downloadPathHome.mkdir();
            }

            String destFilePath = downloadPath + File.separator + contentFile.getFile_name();
            if (!StringUtils.isNoneBlank(new CharSequence[]{contentFile.getMedia_type()}) || !contentFile.getMedia_type().equalsIgnoreCase("IMAGE") && !contentFile.getMedia_type().equalsIgnoreCase("MOVIE")) {
               this.logger.error("[ContentFileDownloadServlet] fail filetype is not Image/Movie");
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CONTENT_NOT_SUPPORTED_FOR_DOWNLOAD, new String[]{"contentIds"});
            } else {
               String srcFilePath = contentFile.getFile_path() + File.separator + contentFile.getFile_name();
               File destFile = new File(destFilePath);
               File srcFile = new File(srcFilePath);
               Files.copy(srcFile.toPath(), destFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
               response.setContentLength(contentFile.getFile_size().intValue());
               FileUtils.setFileToResponse(request, response, destFile, String.valueOf(MediaType.APPLICATION_OCTET_STREAM));
            }
         } catch (SQLException var14) {
            this.logger.error("[ContentFileDownloadServlet] fail file download SQLException", var14);
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CONTENT_NOT_SUPPORTED_FOR_DOWNLOAD, new String[]{"contentIds"});
         } catch (Exception var15) {
            this.logger.error("[ContentFileDownloadServlet] fail file download", var15);
            this.logger.error(var15);
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_FAIL_TO_DOWNLOAD_FILE, new String[]{"contentIds"});
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2ContentWebAuthorResource contentWebAuthor() throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      V2ContentWebAuthorResource resource = new V2ContentWebAuthorResource();
      String token = null;
      String loginId = userContainer.getUser().getUser_id();
      UserInfo userInfo = UserInfoImpl.getInstance();
      User user = userInfo.getUserByUserId(loginId);
      String locale = user.getLocale();
      if (locale == null || locale != null && locale.isEmpty()) {
         locale = "en";
      }

      TokenRegistry tr = TokenRegistry.getTokenRegistry();
      token = tr.issueToken(loginId, userContainer, "PREMIUM");
      resource.setUserId(loginId);
      resource.setToken(token);
      resource.setWebAuthorPath("/" + StrUtils.nvl(CommonConfig.get("webauthor.context")) + "/index.jsp");
      resource.setLanguage(locale);
      return resource;
   }

   public ModelAndView contentExport(V2ContentListFilter filter, String exportType, HttpServletResponse response, String localeData) throws Exception {
      if (!StrUtils.nvl(filter.getGroupId()).equals("")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(filter.getGroupId()));
      }

      String type = StrUtils.nvl(exportType).equals("") ? "EXCEL" : exportType;
      new ArrayList();
      String sort = "";
      String dir = "";
      int startIndex = filter.getStartIndex();
      int pageSize = filter.getPageSize();
      String group_type = StrUtils.nvl(filter.getGroupType()).equals("") ? "ALL" : filter.getGroupType();
      String media_type = StrUtils.nvl(filter.getMediaType()).equals("") ? "" : filter.getMediaType();
      String group_id = StrUtils.nvl(filter.getGroupId()).equals("") ? "0" : filter.getGroupId();
      String byUserOrganizationId = StrUtils.nvl(filter.getUserOrganizationId()).equals("") ? "" : filter.getUserOrganizationId();
      String share_folder_id = StrUtils.nvl(filter.getShareFolderId()).equals("") ? "0" : filter.getShareFolderId();
      String user_id = StrUtils.nvl(filter.getUserId()).equals("") ? "" : filter.getUserId();
      String search_text = StrUtils.nvl(filter.getSearchText()).equals("") ? "" : filter.getSearchText();
      String search_id = StrUtils.nvl(filter.getSearchId()).equals("") ? "-1" : filter.getSearchId();
      String my_content = filter.getIsMyContent() ? "TRUE" : "FALSE";
      String isSelectContent = "";
      String endDate = filter.getEndModifiedDate();
      String startDate = filter.getStartModifiedDate();
      String search_creator = filter.getSearchCreator();
      String deviceTypeParameter = filter.getDeviceType();
      String deviceTypeVersionParameter = filter.getDeviceTypeVersion() != null ? String.valueOf(filter.getDeviceTypeVersion()) : null;
      String is_main = filter.getIsMainPage() ? "true" : "false";
      String isVwlMode = filter.getIsVwlMode() ? "true" : "false";
      String source = StrUtils.nvl(filter.getSource()).equals("") ? "" : filter.getSource();
      if (filter.getSortColumn().equalsIgnoreCase("CONTENT_NAME")) {
         sort = "content_name";
      } else if (filter.getSortColumn().equalsIgnoreCase("CREATOR_ID")) {
         sort = "creator_id";
      } else if (filter.getSortColumn().equalsIgnoreCase("APPROVAL_STATUS")) {
         sort = "approval_status";
      } else if (filter.getSortColumn().equalsIgnoreCase("LAST_MODIFIED_DATE")) {
         sort = "last_modified_date";
      } else if (filter.getSortColumn().equalsIgnoreCase("EXPIRATION_DATE")) {
         sort = "expiration_date";
      } else {
         sort = "last_modified_date";
      }

      if (filter.getSortOrder().equalsIgnoreCase("DESC")) {
         dir = "desc";
      } else if (filter.getSortOrder().equalsIgnoreCase("ASC")) {
         dir = "asc";
      } else {
         dir = "desc";
      }

      String categoryIds = filter.getCategoryIds().isEmpty() ? "" : ConvertUtil.convertListToStringWithSeparator(filter.getCategoryIds(), ",");
      String deviceTypeFilter = "";
      if (filter.getDeviceTypes() != null && !filter.getDeviceTypes().isEmpty()) {
         if (filter.getDeviceTypes().contains("NONE")) {
            Map dataMap = this.makeExportFileData(exportType, localeData, (List)null);
            if (type.equalsIgnoreCase("PDF")) {
               PdfBuilder pdfView = new PdfBuilder();
               return new ModelAndView(pdfView, dataMap);
            }

            this.downloadService = new DeviceStatisticsDownloadService();
            this.downloadService.downloadExcelFile(dataMap, response);
            return null;
         }

         if (filter.getDeviceTypes().contains("ALL")) {
            filter.getDeviceTypes().clear();
         }

         deviceTypeFilter = ConvertUtil.convertListToStringWithSeparator(filter.getDeviceTypes(), ",");
      }

      String contentTypeFilter = "";
      if (filter.getContentTypes() != null && !filter.getContentTypes().isEmpty()) {
         if (filter.getContentTypes().contains("NONE")) {
            Map dataMap = this.makeExportFileData(exportType, localeData, (List)null);
            if (type.equalsIgnoreCase("PDF")) {
               PdfBuilder pdfView = new PdfBuilder();
               return new ModelAndView(pdfView, dataMap);
            }

            this.downloadService = new DeviceStatisticsDownloadService();
            this.downloadService.downloadExcelFile(dataMap, response);
            return null;
         }

         if (filter.getContentTypes().contains("ALL")) {
            filter.getContentTypes().clear();
         }

         contentTypeFilter = ConvertUtil.convertListToStringWithSeparator(filter.getContentTypes(), ",");
      }

      String creatorIdFilter = "";
      if (filter.getCreatorIds() != null && !filter.getCreatorIds().isEmpty()) {
         creatorIdFilter = ConvertUtil.convertListToStringWithSeparator(filter.getCreatorIds(), ",");
      }

      String approvalFilter = !StrUtils.nvl(filter.getApprovalStatus()).isEmpty() ? "" : filter.getApprovalStatus();
      if (filter.getApprovalStatus() != null && !filter.getApprovalStatus().isEmpty()) {
         if (filter.getApprovalStatus().equalsIgnoreCase("UNAPPROVED")) {
            approvalFilter = "unapproval_content";
         } else if (filter.getApprovalStatus().equalsIgnoreCase("APPROVED")) {
            approvalFilter = "approval_content";
         } else if (filter.getApprovalStatus().equalsIgnoreCase("ALL")) {
            approvalFilter = "device_status_view_all";
         } else {
            approvalFilter = "";
         }
      }

      String tagIds = "";
      if (filter.getTagIds() != null && !filter.getTagIds().isEmpty()) {
         tagIds = ConvertUtil.convertListToStringWithSeparator(filter.getTagIds(), ",");
      }

      List fileSizeIntervalFilter = null;
      if (filter.getFileSizes() != null && !filter.getFileSizes().isEmpty()) {
         fileSizeIntervalFilter = filter.getFileSizes();
      }

      String contentUsingStatusFilter = "";
      if (filter.getContentUsingStatus() != null && !filter.getContentUsingStatus().isEmpty()) {
         if (filter.getContentUsingStatus().equalsIgnoreCase("IN_USE")) {
            contentUsingStatusFilter = "used_content";
         } else if (filter.getContentUsingStatus().equalsIgnoreCase("UNUSED")) {
            contentUsingStatusFilter = "unused_content";
         } else if (filter.getContentUsingStatus().equalsIgnoreCase("ALL")) {
            contentUsingStatusFilter = "device_status_view_all";
         } else {
            contentUsingStatusFilter = "";
         }
      }

      String expirationStatusFilter = "";
      if (filter.getExpirationStatus() != null && !filter.getExpirationStatus().isEmpty()) {
         if (filter.getExpirationStatus().equalsIgnoreCase("ALL")) {
            expirationStatusFilter = "device_status_view_all";
         } else if (filter.getExpirationStatus().equalsIgnoreCase("EXPIRED")) {
            expirationStatusFilter = "expired_content";
         } else if (filter.getExpirationStatus().equalsIgnoreCase("VALID")) {
            expirationStatusFilter = "valid_content";
         } else {
            expirationStatusFilter = "";
         }
      }

      String media_type_filter = "";
      if (filter.getMediaTypeFilter() != null && !filter.getMediaTypeFilter().isEmpty()) {
         media_type_filter = ConvertUtil.convertListToStringWithSeparator(filter.getMediaTypeFilter(), ",");
      }

      String playlist_type = "";
      if (filter.getPlaylistType().equalsIgnoreCase("PREMIUM")) {
         playlist_type = "0";
      } else if (filter.getPlaylistType().equalsIgnoreCase("AMS")) {
         playlist_type = "1";
      } else if (filter.getPlaylistType().equalsIgnoreCase("VWL")) {
         playlist_type = "2";
      } else if (filter.getPlaylistType().equalsIgnoreCase("SYNCPLAY")) {
         playlist_type = "3";
      } else if (filter.getPlaylistType().equalsIgnoreCase("ADVERTISEMENT")) {
         playlist_type = "4";
      } else if (filter.getPlaylistType().equalsIgnoreCase("TAG")) {
         playlist_type = "5";
      } else if (filter.getPlaylistType().equalsIgnoreCase("LINKED")) {
         playlist_type = "6";
      } else {
         playlist_type = "0";
      }

      String content_type = StrUtils.nvl(filter.getContentType()).equals("") ? "CONTENT" : filter.getContentType();
      String orgName = SecurityUtils.getUserContainer().getUser().getOrganization();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      long orgId = userGroupInfo.getOrgGroupIdByName(orgName);
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
      boolean contentsApprovalEnable = false;
      if (infoMap != null && infoMap.get("CONTENTS_APPROVAL_ENABLE") != null) {
         contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
      }

      boolean myContentDelete = false;
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      if (my_content != null && my_content.equalsIgnoreCase("TRUE") && group_type.equalsIgnoreCase("DELETED")) {
         Long longGroupId = 0L;

         try {
            longGroupId = Long.valueOf(group_id);
         } catch (Exception var63) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"groupId"});
         }

         user_id = cmsDao.getGroupInfo(longGroupId).getCreator_id();
         myContentDelete = true;
      }

      if (my_content != null && my_content.equalsIgnoreCase("TRUE")) {
         group_type = "USER";
         user_id = this.getLoginUserId();
      }

      boolean canEditOthers = false;
      boolean canReadUnshared = false;
      cmsDao = ContentInfoImpl.getInstance();
      canEditOthers = cmsDao.getCanEditOthers(user_id, group_type, (HttpServletRequest)null);
      ListManager listMgr = new ListManager(cmsDao, "commonlist");
      listMgr.addSearchInfo("sortColumn", sort);
      listMgr.addSearchInfo("sortOrder", dir);
      search_text = search_text.replaceAll("]", "^]");
      search_text = search_text.replaceAll("%", "^%");
      listMgr.addSearchInfo("searchText", search_text);
      listMgr.addSearchInfo("isMain", is_main);
      if (expirationStatusFilter != null && !expirationStatusFilter.equals("")) {
         listMgr.addSearchInfo("expirationStatusFilter", expirationStatusFilter);
      }

      if (source != null && !source.equals("")) {
         listMgr.addSearchInfo("source", source);
         this.logger.info("RQ190703-00340 : " + source);
      }

      if (approvalFilter != null && !approvalFilter.equals("")) {
         listMgr.addSearchInfo("contentApprovalFilter", approvalFilter);
      }

      if (!deviceTypeFilter.equals("")) {
         listMgr.addSearchInfo("deviceFilter", deviceTypeFilter);
      }

      String[] categoryFilterList;
      if (creatorIdFilter != null && !creatorIdFilter.equals("")) {
         categoryFilterList = creatorIdFilter.split(",");
         listMgr.addSearchInfo("userFilter", categoryFilterList);
      }

      if (tagIds != null && !tagIds.equals("")) {
         categoryFilterList = tagIds.split(",");
         listMgr.addSearchInfo("tagFilter", categoryFilterList);
      }

      if (fileSizeIntervalFilter != null && !fileSizeIntervalFilter.isEmpty()) {
         List sizeFilterMapList = new ArrayList();

         HashMap sizeFilterMap;
         for(Iterator var54 = fileSizeIntervalFilter.iterator(); var54.hasNext(); sizeFilterMapList.add(sizeFilterMap)) {
            V2ContentFileSizeInterval interval = (V2ContentFileSizeInterval)var54.next();
            sizeFilterMap = new HashMap();
            Long[] sizeStringLong = new Long[2];

            try {
               if (interval.getMinFileSizeInMB() != null) {
                  sizeStringLong[0] = interval.getMinFileSizeInMB();
                  sizeFilterMap.put("start", sizeStringLong[0] * 1024L * 1024L);
               }

               if (interval.getMaxFileSizeInMB() != null) {
                  sizeStringLong[1] = interval.getMaxFileSizeInMB();
                  sizeFilterMap.put("end", sizeStringLong[1] * 1024L * 1024L);
               }
            } catch (Exception var64) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"sizeFilter"});
            }
         }

         listMgr.addSearchInfo("sizeFilter", sizeFilterMapList);
      }

      if (StringUtils.isNotBlank(contentUsingStatusFilter)) {
         listMgr.addSearchInfo("contentUsingStatusFilter", contentUsingStatusFilter);
      }

      if (contentTypeFilter != null && !contentTypeFilter.equals("")) {
         if (contentTypeFilter.contains("TLFD")) {
            listMgr.addSearchInfo("isTLFD", "Y");
         }

         categoryFilterList = contentTypeFilter.split(",");
         listMgr.addSearchInfo("contentFilter", categoryFilterList);
      }

      if (isVwlMode != null && isVwlMode.equals("true")) {
         listMgr.addSearchInfo("is_vwl_mode", "Y");
      }

      if (media_type.equalsIgnoreCase("PLAYLIST")) {
         listMgr.addSearchInfo("playlist_type", playlist_type);
         listMgr.addSearchInfo("mediaType", media_type);
      }

      listMgr.addSearchInfo("content_type", content_type);
      listMgr.addSearchInfo("isSelectContent", isSelectContent);
      if (media_type_filter != null && !media_type_filter.isEmpty()) {
         if (contentTypeFilter.contains("TLFD")) {
            listMgr.addSearchInfo("isTLFD", "Y");
         }

         listMgr.addSearchInfo("media_type_filter", media_type_filter);
      }

      if (deviceTypeParameter != null && deviceTypeVersionParameter != null) {
         listMgr.addSearchInfo("deviceType", deviceTypeParameter);
         listMgr.addSearchInfo("deviceTypeVersion", deviceTypeVersionParameter);
      }

      if (group_type.equalsIgnoreCase("USER") || group_type.equalsIgnoreCase("ORGAN")) {
         if (canEditOthers) {
            listMgr.addSearchInfo("viewRange", "all");
         } else {
            listMgr.addSearchInfo("viewRange", "shared");
         }
      }

      if (categoryIds != null && !categoryIds.equals("")) {
         categoryFilterList = categoryIds.split(",");
         listMgr.addSearchInfo("category", categoryIds);
         listMgr.addSearchInfo("categoryFilterList", categoryFilterList);
      }

      if (contentTypeFilter != null && !contentTypeFilter.contains("TLFD")) {
         if (myContentDelete) {
            listMgr.addSearchInfo("myContentDelete", myContentDelete);
            listMgr.addSearchInfo("creatorID", user_id);
         } else if (group_type.equalsIgnoreCase("USER")) {
            if (user_id.isEmpty()) {
               listMgr.addSearchInfo("creatorID", this.getLoginUserId());
            } else {
               listMgr.addSearchInfo("creatorID", user_id);
            }
         } else if (group_type.equalsIgnoreCase("GROUPED")) {
            Long longGroupId = 0L;

            try {
               longGroupId = Long.valueOf(group_id);
            } catch (Exception var62) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"groupId"});
            }

            if (cmsDao.getGroupInfo(longGroupId) != null) {
               user_id = cmsDao.getGroupInfo(longGroupId).getCreator_id();
            }

            listMgr.addSearchInfo("creatorID", user_id);
         } else {
            listMgr.addSearchInfo("creatorID", this.getLoginUserId());
         }
      }

      AbilityUtils abilityUtils = new AbilityUtils();
      AbilityInfo abilityInfo = AbilityInfoImpl.getInstance();
      List abilityList = abilityInfo.getAllAbilityListByUserId(this.getLoginUserId());
      Iterator it = abilityList.iterator();

      String loginUserRoleName;
      while(it.hasNext()) {
         Map abilityMap = (Map)it.next();
         loginUserRoleName = (String)abilityMap.get("ability_name");
         if (loginUserRoleName.equalsIgnoreCase("Content Manage Authority")) {
            canReadUnshared = true;
         }
      }

      if (user_id.equalsIgnoreCase(this.getLoginUserId())) {
         canReadUnshared = true;
      }

      if (is_main != null && is_main.equalsIgnoreCase("true")) {
         if (contentsApprovalEnable && !abilityUtils.isContentApprovalAuthority()) {
            listMgr.addSearchInfo("isContentApprove", true);
         }
      } else if (contentsApprovalEnable) {
         listMgr.addSearchInfo("approval_status", "APPROVED");
      }

      if (!share_folder_id.equals("0")) {
         group_type = "SHAREFOLDER";
      }

      listMgr.addSearchInfo("canReadUnshared", canReadUnshared);
      listMgr.addSearchInfo("searchID", search_id);
      listMgr.addSearchInfo("startDate", startDate);
      listMgr.addSearchInfo("endDate", endDate);
      listMgr.addSearchInfo("searchCreator", search_creator);
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      if (Long.parseLong(search_id) >= 0L) {
         listMgr.setSection("getSearchList");
      } else if (group_type.equalsIgnoreCase("GROUPED")) {
         listMgr.addSearchInfo("groupID", group_id);
         listMgr.addSearchInfo("listType", group_type);
         listMgr.setSection("getContentList");
      } else if (group_type.equalsIgnoreCase("SUBMITTED")) {
         listMgr.addSearchInfo("groupID", group_id);
         listMgr.addSearchInfo("listType", group_type);
         listMgr.setSection("getContentList");
      } else if (group_type.equalsIgnoreCase("SHAREFOLDER")) {
         listMgr.addSearchInfo("share_folder_id", Long.valueOf(share_folder_id));
         listMgr.addSearchInfo("listType", "SHAREFOLDER");
         listMgr.setSection("getContentList");
      } else {
         listMgr.addSearchInfo("listType", group_type);
         listMgr.setSection("getContentList");
      }

      loginUserRoleName = SecurityUtils.getUserContainer().getUser().getRole_name();
      if (loginUserRoleName.equals("Server Administrator") || SecurityUtils.getLoginUserOrganizationId() == 0L && abilityUtils.checkAuthority("Content Manage")) {
         listMgr.addSearchInfo("isServerAdmin", true);
      }

      if (byUserOrganizationId != null && !byUserOrganizationId.isEmpty()) {
         listMgr.addSearchInfo("byUserOrganizationId", byUserOrganizationId);
      }

      if (group_type.equalsIgnoreCase("SHAREFOLDER")) {
         listMgr.addSearchInfo("byUserOrganizationId", "");
         listMgr.addSearchInfo("creatorID", "");
      }

      List contentList = listMgr.V2dbexecute(startIndex, pageSize);
      Map dataMap = this.makeExportFileData(exportType, localeData, contentList);
      if (type.equalsIgnoreCase("PDF")) {
         PdfBuilder pdfView = new PdfBuilder();
         return new ModelAndView(pdfView, dataMap);
      } else {
         this.downloadService = new DeviceStatisticsDownloadService();
         this.downloadService.downloadExcelFile(dataMap, response);
         return null;
      }
   }

   private Map makeExportFileData(String exportType, String localeData, List contentList) throws ConfigException, SQLException {
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      String type = StrUtils.nvl(exportType).equals("") ? "EXCEL" : exportType;
      String fileExtension = "xls";
      if (type.equalsIgnoreCase("PDF")) {
         fileExtension = "pdf";
      }

      if (StrUtils.nvl(localeData).equals("")) {
         String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
         if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
            localeData = userLocale;
         } else {
            localeData = "en";
         }
      }

      Locale locale = new Locale(localeData);
      Map dataMap = new HashMap();
      String fileName = "ContentList." + fileExtension;
      String sheetName = "Content";
      String[] columnNames = null;
      String[] tmp;
      if (CommonConfig.get("yesco.ui.enable") != null && CommonConfig.get("yesco.ui.enable").equalsIgnoreCase("true")) {
         tmp = new String[]{"content_name", "creator_id", "media_type", "play_time", "total_size_str", "last_modified_date"};
         columnNames = tmp;
      } else {
         tmp = new String[]{"content_name", "device_type", "creator_id", "media_type", "play_time", "total_size_str", "last_modified_date"};
         columnNames = tmp;
      }

      String defaultMessage = rms.getMessage("TEXT_CONTENT_NAME_P", (Object[])null, new Locale("en"));
      String contentNameTitle = rms.getMessage("TEXT_CONTENT_NAME_P", (Object[])null, defaultMessage, locale);
      defaultMessage = rms.getMessage("TABLE_TYPE_P", (Object[])null, new Locale("en"));
      String MediaTypeTitle = rms.getMessage("TABLE_TYPE_P", (Object[])null, defaultMessage, locale);
      defaultMessage = rms.getMessage("TEXT_LAST_MODIFIED_DATE_P", (Object[])null, new Locale("en"));
      String modifiedDateTitle = rms.getMessage("TEXT_LAST_MODIFIED_DATE_P", (Object[])null, defaultMessage, locale);
      defaultMessage = rms.getMessage("COM_MAPP_SID_SUPPORTED_DEVICES", (Object[])null, new Locale("en"));
      String SupportedDeviceTypeTitle = rms.getMessage("COM_MAPP_SID_SUPPORTED_DEVICES", (Object[])null, defaultMessage, locale);
      defaultMessage = rms.getMessage("TEXT_CREATOR_P", (Object[])null, new Locale("en"));
      String CreatorIdTitle = rms.getMessage("TEXT_CREATOR_P", (Object[])null, defaultMessage, locale);
      defaultMessage = rms.getMessage("TEXT_PLAY_TIME_P", (Object[])null, new Locale("en"));
      String PlayTimeTitle = rms.getMessage("TEXT_PLAY_TIME_P", (Object[])null, defaultMessage, locale);
      defaultMessage = rms.getMessage("TEXT_TOTAL_SIZE_P", (Object[])null, new Locale("en"));
      String TotalSizeTitle = rms.getMessage("TEXT_TOTAL_SIZE_P", (Object[])null, defaultMessage, locale);
      String[] fieldNames = null;
      String[] tmp;
      if (CommonConfig.get("yesco.ui.enable") != null && CommonConfig.get("yesco.ui.enable").equalsIgnoreCase("true")) {
         tmp = new String[]{contentNameTitle, CreatorIdTitle, MediaTypeTitle, PlayTimeTitle, TotalSizeTitle, modifiedDateTitle};
         fieldNames = tmp;
      } else {
         tmp = new String[]{contentNameTitle, SupportedDeviceTypeTitle, CreatorIdTitle, MediaTypeTitle, PlayTimeTitle, TotalSizeTitle, modifiedDateTitle};
         fieldNames = tmp;
      }

      Object[] dataList = null;
      if (contentList != null) {
         int dataListSize = contentList.size();
         dataList = new Object[dataListSize];

         for(int index = 0; index < dataListSize; ++index) {
            Content cList = (Content)contentList.get(index);
            if (cList != null) {
               if (cList.getDevice_type() == null || cList.getDevice_type().equals("")) {
                  Map typeMap = ContentUtils.getContentDeviceTypeAndVersion(cList);
                  cList.setDevice_type((String)typeMap.get("deviceType"));
                  cList.setDevice_type_version(Float.parseFloat(typeMap.get("deviceTypeVersion").toString()));
               }

               if (cList.getDevice_type() != null && !cList.getDevice_type().equals("")) {
                  if (cList.getDevice_type().equals("SPLAYER") && cList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_2_0) {
                     cList.setDevice_type("S2PLAYER");
                  } else if (cList.getDevice_type().equals("SPLAYER") && cList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_3_0) {
                     cList.setDevice_type("S3PLAYER");
                  } else if (cList.getDevice_type().equals("SPLAYER") && cList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_4_0) {
                     cList.setDevice_type("S4PLAYER");
                  } else if (cList.getDevice_type().equals("SPLAYER") && cList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_5_0) {
                     cList.setDevice_type("S5PLAYER");
                  } else if (cList.getDevice_type().equals("SPLAYER") && cList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_6_0) {
                     cList.setDevice_type("S6PLAYER");
                  } else if (cList.getDevice_type().equals("SPLAYER") && cList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_7_0) {
                     cList.setDevice_type("S7PLAYER");
                  } else if (cList.getDevice_type().equals("SPLAYER") && cList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_9_0) {
                     cList.setDevice_type("S9PLAYER");
                  } else if (cList.getDevice_type().equals("SPLAYER") && cList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_10_0) {
                     cList.setDevice_type("S10PLAYER");
                  } else {
                     cList.setDevice_type(cList.getDevice_type());
                  }
               }
            }

            dataList[index] = contentList.get(index);
         }
      }

      dataMap.put("fileName", fileName);
      dataMap.put("sheetName", sheetName);
      dataMap.put("columnNames", columnNames);
      dataMap.put("fieldNames", fieldNames);
      dataMap.put("dataList", dataList);
      return dataMap;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2PageResource getContentsByFileId(String fileId) throws Exception {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      List contentIds = cInfo.getContentIdsByFileId(fileId);
      List resources = new ArrayList();
      Iterator var5 = contentIds.iterator();

      while(var5.hasNext()) {
         String id = (String)var5.next();
         V2ContentResource resource = this.getContentDetail(id);
         if (resource != null) {
            resources.add(resource);
         }
      }

      return V2PageResource.createPageResource(resources, resources.size());
   }

   public V2CommonResultResource expireDateToContent(V2ContentExpireDate body) throws Exception {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      String expirationDate = body.getExpirationDate();
      List contentIds = body.getContentIds();
      V2CommonResultResource resource = new V2CommonResultResource();
      List contentNotiDataList = new ArrayList();
      List successList = new ArrayList();
      List failList = new ArrayList();
      boolean flag = false;
      Iterator var10 = contentIds.iterator();

      String contentId;
      while(var10.hasNext()) {
         contentId = (String)var10.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
         } catch (Exception var19) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
      }

      var10 = contentIds.iterator();

      while(var10.hasNext()) {
         contentId = (String)var10.next();

         try {
            if (expirationDate.length() == 8) {
               cInfo.setExpirationDate(contentId, expirationDate);
               V2ContentResource contentResource = this.getContentDetail(contentId);
               if (contentResource != null) {
                  try {
                     Content content = cInfo.getContentActiveVerInfo(contentId);
                     UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
                     NotificationData notiData = new NotificationData();
                     notiData.setName(content.getContent_name());
                     notiData.setOrgId(content.getOrganization_id());
                     notiData.setOrgName(userGroupInfo.getGroupNameByGroupId(content.getOrganization_id()));
                     notiData.setUserName(this.getLoginUserId());
                     contentNotiDataList.add(notiData);
                  } catch (Exception var16) {
                     this.logger.error(var16);
                  }

                  successList.add(contentResource);
               } else {
                  failList.add(contentId);
               }
            } else {
               failList.add(contentId);
               this.logger.error("[EDIT] expirationDate(" + expirationDate + ") contentId(" + contentId + ")");
            }
         } catch (Exception var17) {
            failList.add(contentId);
            this.logger.error("[EDIT] expirationDate(" + expirationDate + ") contentId(" + contentId + ") ", var17);
         }
      }

      if (contentNotiDataList.size() > 0) {
         MailUtil.sendContentEventMail(contentNotiDataList, "Edit metadata");
      }

      try {
         RuleSetInfo rulesetInfo = RuleSetInfoImpl.getInstance();
         String[] contentIdArray = new String[successList.size()];

         for(int i = 0; i < successList.size(); ++i) {
            contentIdArray[i] = ((V2ContentResource)successList.get(i)).getContentId();
         }

         this.rulesetUpdate(contentIdArray);
      } catch (Exception var18) {
         this.logger.error(var18);
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2PlaylistAddResource playListAddContent(V2PlaylistAddData body) throws Exception {
      List contentList = body.getContentIds();
      if (contentList != null && contentList.size() > 0) {
         Iterator var3 = contentList.iterator();

         while(var3.hasNext()) {
            String s = (String)var3.next();
            if (!SecurityUtils.checkReadPermissionWithOrgAndId("Content", s)) {
               RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
            }
         }

         List result = new ArrayList();
         ContentInfo contentDao = ContentInfoImpl.getInstance();
         int startIndex = body.getStartIndex() - 1;
         int pageSize = body.getPageSize();
         String productType = body.getProductType();
         String firstContentName = "";
         String deviceType = "";
         float deviceTypeVersion = 1.0F;
         PlaylistInterface cmsDao = DAOFactory.getPlaylistInfoImpl(productType);
         new ArrayList();
         Set deviceTypeVersionSet = null;
         ContentInfo dao = ContentInfoImpl.getInstance();
         firstContentName = contentDao.getContentName((String)contentList.get(0));

         Content playlistList;
         String selectedDevice;
         String thumbFileId;
         for(int i = 0; i < contentList.size(); ++i) {
            playlistList = dao.getContentActiveVerInfo((String)contentList.get(i));
            String fileType;
            String[] results;
            String[] arr2;
            if (playlistList.getDevice_type() != null && !playlistList.getDevice_type().equals("")) {
               float tmpVersion = playlistList.getDevice_type_version();
               fileType = CommonUtils.convertDeviceTypeAndVerisonToDeviceType(playlistList.getDevice_type(), tmpVersion + "");
               String[] arr1 = new String[]{fileType};
               String[] arr2 = null;
               if (deviceTypeVersionSet != null) {
                  arr2 = new String[deviceTypeVersionSet.size()];
                  arr2 = (String[])deviceTypeVersionSet.toArray(arr2);
               }

               results = CommonUtils.getIntersectionDeviceType(arr1, arr2);
               if (deviceTypeVersionSet == null) {
                  deviceTypeVersionSet = new HashSet();
               } else {
                  deviceTypeVersionSet.clear();
               }

               arr2 = results;
               int var50 = results.length;

               for(int var53 = 0; var53 < var50; ++var53) {
                  thumbFileId = arr2[var53];
                  deviceTypeVersionSet.add(thumbFileId);
               }
            } else {
               selectedDevice = playlistList.getMedia_type();
               fileType = playlistList.getMain_file_Extension();
               List list = dao.getSupportedDeviceTypeByContentType(selectedDevice);
               Set contentDeviceTypeSet = new HashSet();
               Iterator var21 = list.iterator();

               while(var21.hasNext()) {
                  Map map = (Map)var21.next();
                  if (map.get("FILE_TYPE").equals(fileType)) {
                     String deviceTypeAndVersion = CommonUtils.convertDeviceTypeAndVerisonToDeviceType(map.get("DEVICE_TYPE").toString(), map.get("DEVICE_TYPE_VERSION").toString());
                     if (!contentDeviceTypeSet.contains(deviceTypeAndVersion)) {
                        contentDeviceTypeSet.add(deviceTypeAndVersion);
                     }
                  }
               }

               results = new String[contentDeviceTypeSet.size()];
               results = (String[])contentDeviceTypeSet.toArray(results);
               arr2 = null;
               if (deviceTypeVersionSet != null) {
                  arr2 = new String[deviceTypeVersionSet.size()];
                  arr2 = (String[])deviceTypeVersionSet.toArray(arr2);
               }

               String[] results = CommonUtils.getIntersectionDeviceType(results, arr2);
               if (deviceTypeVersionSet == null) {
                  deviceTypeVersionSet = new HashSet();
               } else {
                  deviceTypeVersionSet.clear();
               }

               String[] var24 = results;
               int var25 = results.length;

               for(int var26 = 0; var26 < var25; ++var26) {
                  String res = var24[var26];
                  deviceTypeVersionSet.add(res);
               }
            }
         }

         List selectedDeviceTypeList = new ArrayList();
         Iterator var34 = deviceTypeVersionSet.iterator();

         while(var34.hasNext()) {
            selectedDevice = (String)var34.next();
            Map map = new HashMap();
            switch(selectedDevice.toUpperCase().charAt(0)) {
            case 'A':
               map.put("deviceType", "APLAYER");
               map.put("deviceTypeVersion", 1.0F);
               break;
            case 'I':
               Map map2 = new HashMap();
               map2.put("deviceType", "iPLAYER");
               map2.put("deviceTypeVersion", 1.0F);
               selectedDeviceTypeList.add(map2);
               map.put("deviceType", "iPLAYER");
               map.put("deviceTypeVersion", 2.0F);
               break;
            case 'L':
               map.put("deviceType", "LPLAYER");
               map.put("deviceTypeVersion", 1.0F);
               break;
            case 'S':
               try {
                  selectedDevice = selectedDevice.substring(1, selectedDevice.indexOf("PLAYER"));
                  if (selectedDevice.length() == 0) {
                     selectedDevice = "1";
                  }

                  map.put("deviceType", "SPLAYER");
                  map.put("deviceTypeVersion", Float.parseFloat(selectedDevice));
               } catch (Exception var30) {
                  this.logger.error(var30);
                  map = null;
               }
               break;
            case 'W':
               map.put("deviceType", "WPLAYER");
               map.put("deviceTypeVersion", 1.0F);
               break;
            default:
               map = null;
            }

            if (map != null) {
               selectedDeviceTypeList.add(map);
            }
         }

         playlistList = null;
         int count = 0;
         int totalCount = 0;
         Object playlistList;
         if (deviceTypeVersionSet != null && deviceTypeVersionSet.size() > 0) {
            totalCount = cmsDao.getPlaylistListCountByDeviceType(this.getLoginUserId(), deviceType, deviceTypeVersion, selectedDeviceTypeList);
            playlistList = cmsDao.getPlaylistListByDeviceType(this.getLoginUserId(), startIndex, pageSize, deviceType, deviceTypeVersion, selectedDeviceTypeList);
         } else {
            playlistList = new ArrayList();
         }

         List contentNotiDataList = new ArrayList();

         for(int i = 0; i < ((List)playlistList).size(); ++i) {
            Playlist playlist = (Playlist)((List)playlistList).get(i);
            V2PlaylistAddContentsResource data = new V2PlaylistAddContentsResource();
            Content content = contentDao.getContentActiveVerInfo(playlist.getContent_id());
            playlist.setMainContent(content);
            String playlistName = "";
            thumbFileId = "";
            String thumbFileName = "";
            if (playlist.getPlaylist_name() != null) {
               playlistName = playlist.getPlaylist_name();
            }

            if (playlist.getMainContent() != null) {
               if (playlist.getMainContent().getThumb_file_id() != null) {
                  thumbFileId = playlist.getMainContent().getThumb_file_id();
               }

               if (playlist.getMainContent().getThumb_file_name() != null) {
                  thumbFileName = playlist.getMainContent().getThumb_file_name();
               }
            }

            data.setPlaylistId(playlist.getPlaylist_id());
            data.setDeviceType(playlist.getDevice_type());
            data.setPlaylistName(playlistName);
            data.setThumbFileId(thumbFileId);
            data.setThumbFileName(thumbFileName);
            data.setDeviceTypeVersion(playlist.getDevice_type_version());
            data.setContentCount(playlist.getContent_count());
            data.setAftercontentcount(playlist.getContent_count());
            data.setPlayTime(playlist.getPlay_time());
            data.setTotalSize(playlist.getTotal_size());
            data.setLastModifiedDate(playlist.getLast_modified_date());
            data.setShareFlag(playlist.getShare_flag());
            data.setGroupName(playlist.getGroup_name());
            data.setMetaData(StrUtils.makeTag(playlist.getPlaylist_meta_data()));
            data.setCreatorId(playlist.getCreator_id());
            data.setVersionId(playlist.getVersion_id());

            try {
               UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
               NotificationData notiData = new NotificationData();
               notiData.setName(playlist.getPlaylist_name());
               notiData.setOrgId(playlist.getOrganization_id());
               notiData.setOrgName(userGroupInfo.getGroupNameByGroupId(playlist.getOrganization_id()));
               notiData.setUserName(this.getLoginUserId());
               contentNotiDataList.add(notiData);
            } catch (Exception var29) {
               this.logger.error(var29);
            }

            ++count;
            result.add(data);
         }

         if (contentNotiDataList.size() > 0) {
            MailUtil.sendContentEventMail(contentNotiDataList, "Edit playlist");
         }

         V2PlaylistAddResource resource = new V2PlaylistAddResource();
         resource.setRecordsReturned(count);
         resource.setRecordsTotal(totalCount);
         resource.setFirstContentName(firstContentName);
         resource.setStartIndex(startIndex + 1);
         resource.setPageSize(count);
         resource.setSelectedContentCnt(contentList.size());
         resource.setPlaylist(result);
         return resource;
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"contentIds"});
      }
   }

   public void setRefRulesetList(String contentId, ArrayList refPlaylistList) {
      RuleSetInfo rulesetDao = RuleSetInfoImpl.getInstance();

      try {
         List rulesetList = rulesetDao.getRulesetUsingContents(contentId);
         if (rulesetList != null && rulesetList.size() > 0) {
            List list = new ArrayList();

            for(int i = 0; i < rulesetList.size(); ++i) {
               Map map = new HashMap();
               RuleSet ruleset = (RuleSet)rulesetList.get(i);
               map.put("rulesetId", ruleset.getRuleset_id());
               map.put("rulesetName", ruleset.getName());
               list.add(map);
            }

            refPlaylistList.add(list);
         } else {
            refPlaylistList.add((Object)null);
         }
      } catch (Exception var9) {
         this.logger.error("", var9);
      }

   }

   public void setRefPlaylistList(String contentId, ArrayList refPlaylistList, String productType) {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      List pList = null;

      try {
         if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
            pList = cInfo.getPlaylistListUsingContent(contentId);
         }

         if (pList != null && pList.size() != 0) {
            Map map = null;
            List playlistList = new ArrayList();
            String playlist_name = "";
            String playlist_id = "";
            String playlist_type = "";

            for(int j = 0; j < pList.size(); ++j) {
               Map tmpmap = new HashMap();
               map = (Map)pList.get(j);
               playlist_id = (String)map.get("playlist_id");
               playlist_name = (String)map.get("playlist_name");
               playlist_type = (String)map.get("playlist_type");
               String version = this.getPlaylistVersionStr(playlist_id, contentId);
               tmpmap.put("playlistId", playlist_id);
               tmpmap.put("playlistName", playlist_name + "(" + version + ")");
               tmpmap.put("playlistType", playlist_type);
               playlistList.add(tmpmap);
            }

            refPlaylistList.add(playlistList);
         } else {
            refPlaylistList.add((Object)null);
         }
      } catch (SQLException var14) {
         this.logger.error("", var14);
      }

   }

   public void setRefScheduleList(String contentId, ArrayList refScheduleList, String productType) {
      ScheduleInterface sInfo = DAOFactory.getScheduleInfoImpl(productType);

      try {
         List programList = sInfo.getProgramByContentId(contentId);
         if (programList != null && programList.size() != 0) {
            String programName = "";
            Map map = null;
            List scheduleList = new ArrayList();
            String scheduleId = "";
            String scheduleName = "";

            for(int j = 0; j < programList.size(); ++j) {
               Map tmpmap = new HashMap();
               map = (Map)programList.get(j);
               scheduleId = (String)map.get("program_id");
               scheduleName = (String)map.get("program_name");
               tmpmap.put("scheduleId", scheduleId);
               tmpmap.put("scheduleName", scheduleName);
               scheduleList.add(tmpmap);
            }

            refScheduleList.add(scheduleList);
         } else {
            refScheduleList.add((Object)null);
         }
      } catch (SQLException var13) {
         this.logger.error("", var13);
      }

   }

   public void setRefEventList(String contentId, ArrayList refEventList) {
      EventInfoDao eventInfoDao = new EventInfoDao();

      try {
         List eventList = eventInfoDao.getEventIdListByContentId(contentId);
         if (eventList != null && eventList.size() != 0) {
            String eventName = "";
            String eventId = "";
            Map map = null;
            List eventSchList = new ArrayList();

            for(int j = 0; j < eventList.size(); ++j) {
               Map tmpmap = new HashMap();
               eventId = (String)((Map)eventList.get(j)).get("EVENT_ID");
               eventName = eventInfoDao.getEvent(eventId).getEvent_name();
               tmpmap.put("eventId", eventId);
               tmpmap.put("eventName", eventName);
               eventSchList.add(tmpmap);
            }

            refEventList.add(eventSchList);
         } else {
            refEventList.add((Object)null);
         }
      } catch (SQLException var11) {
         this.logger.error("", var11);
      }

   }

   public void setRefConvertDataList(String contentId, ArrayList refConvertDataList) {
      ConvertDataInfo cInfo = ConvertDataInfoImpl.getInstance();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      List cList = null;

      try {
         cList = cInfo.getConvertDataNameByContentId(contentId);
         List dlkInfo = contentInfo.getDlkContentIdByIputDataContentId(contentId);
         List dlkListFromLft = contentInfo.getContentIdListByTemplateId(contentId);
         dlkInfo.addAll(dlkListFromLft);
         if ((cList == null || cList.size() == 0) && (dlkInfo == null || dlkInfo.size() == 0)) {
            refConvertDataList.add((Object)null);
         } else {
            Map map = null;
            List convertDataList = new ArrayList();
            String convertDataName = "";
            String dlkContentName = "";

            int j;
            HashMap tmpmap;
            for(j = 0; j < cList.size(); ++j) {
               tmpmap = new HashMap();
               map = (Map)cList.get(j);
               convertDataName = (String)map.get("convert_data_name");
               tmpmap.put("convertDataName", convertDataName);
               convertDataList.add(tmpmap);
            }

            for(j = 0; j < dlkInfo.size(); ++j) {
               tmpmap = new HashMap();
               map = (Map)dlkInfo.get(j);
               String dlkContentId = (String)map.get("dlk_content_id");
               dlkContentName = contentInfo.getContentName(dlkContentId);
               tmpmap.put("convertDataName", dlkContentName);
               convertDataList.add(tmpmap);
            }

            refConvertDataList.add(convertDataList);
         }
      } catch (SQLException var15) {
         this.logger.error("", var15);
      }

   }

   public String getPlaylistVersionStr(String playlistId, String contentId) throws SQLException {
      String result = "";
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      List pList = null;
      pList = cInfo.getPlaylistVersionListUsingContent(playlistId, contentId);
      Map map = null;
      if (pList != null) {
         for(int j = 0; j < pList.size(); ++j) {
            map = (Map)pList.get(j);
            if (j > 0) {
               result = result + ",";
            }

            result = result + map.get("version_id").toString();
         }
      }

      return result;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2CommonResultResource completeUploadContents(V2CommonIds resource) throws Exception {
      boolean flag = false;
      List contentIds = resource.getIds();

      for(int i = 0; i < contentIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, (String)contentIds.get(i));
         } catch (Exception var22) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT);
      }

      V2CommonResultResource resourceList = new V2CommonResultResource();
      List successResourceList = new ArrayList();
      ArrayList failResourceList = new ArrayList();

      try {
         ContentInfo contentInfo = ContentInfoImpl.getInstance();
         User loginUser = SecurityUtils.getLoginUser();
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         long loginUserOrgId = userGroupInfo.getOrgGroupIdByName(loginUser.getOrganization());
         ServerSetupInfo serverSetupInfo = ServerSetupInfoImpl.getInstance();
         Map serverInfo = serverSetupInfo.getServerInfoByOrgId(loginUserOrgId);
         Object notiEnableObj = serverInfo.get("DISCONNECT_ENABLE");
         if (notiEnableObj != null && notiEnableObj instanceof Boolean && (Boolean)notiEnableObj) {
            List contentNotiDataList = new ArrayList();
            Iterator var16 = contentIds.iterator();

            while(var16.hasNext()) {
               String contentId = (String)var16.next();
               Content content = contentInfo.getContentActiveVerInfo(contentId);

               try {
                  NotificationData notiData = new NotificationData();
                  notiData.setName(content.getContent_name());
                  notiData.setOrgId(content.getOrganization_id());
                  notiData.setOrgName(userGroupInfo.getGroupNameByGroupId(content.getOrganization_id()));
                  notiData.setUserName(this.getLoginUserId());
                  contentNotiDataList.add(notiData);
                  successResourceList.add(contentId);
               } catch (Exception var20) {
                  this.logger.error(var20);
                  failResourceList.add(contentId);
               }
            }

            if (contentNotiDataList != null && contentNotiDataList.size() > 0) {
               MailUtil.sendContentEventMail(contentNotiDataList, "Add content");
            }
         }
      } catch (Exception var21) {
         this.logger.error(var21);
         Iterator var8 = contentIds.iterator();

         while(var8.hasNext()) {
            String contentId = (String)var8.next();
            failResourceList.add(contentId);
         }
      }

      resourceList.setSuccessList(successResourceList);
      resourceList.setFailList(failResourceList);
      return resourceList;
   }

   private boolean isContentInSharedFolder(String contentId) throws SQLException {
      ShareFolderInfo info = ShareFolderInfoImpl.getInstance();
      int count = info.isExistContentByContentIdAndOrgGroupId(contentId, SecurityUtils.getLoginUserOrganizationId());
      return count > 0;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public V2ContentAdvertisementResource getAdvertisement(String contentId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      Long orgId = SecurityUtils.getLoginUserOrganizationId();
      List activatedIndexList = this.insightIndexDao.getAllActivatedInsightIndex(orgId == 0L ? null : orgId);
      List insightIndexList = new ArrayList();
      V2ContentSalesDataResource salesData = null;
      Iterator var7 = activatedIndexList.iterator();

      while(true) {
         while(var7.hasNext()) {
            InsightIndexEntity index = (InsightIndexEntity)var7.next();
            List valueList = this.insightIndexDao.getInsightIndexValueWithContentIdByIndexIdAndContentId(index.getIndex_id(), contentId);
            List v2ValueList = (List)valueList.stream().map((value) -> {
               V2InsightIndexValueForContentResource v2Value = new V2InsightIndexValueForContentResource();
               v2Value.setValueId(value.getValue_id());
               v2Value.setValue(value.getValue());
               v2Value.setSelected(StringUtils.isNotBlank(value.getContent_id()));
               return v2Value;
            }).collect(Collectors.toList());
            V2InsightIndexForContentResource v2Index = new V2InsightIndexForContentResource(index);
            v2Index.setOrganizationName(index.getOrganization_id() == -1L ? "Common" : userGroupInfo.getGroupNameByGroupId(index.getOrganization_id()));
            v2Index.setValues(v2ValueList);
            if (StringUtils.equals(index.getIndex_id(), "PRODUCT_CODE")) {
               ContentInfo contentInfo = ContentInfoImpl.getInstance();
               salesData = new V2ContentSalesDataResource(v2Index);
               List v2History = new ArrayList();
               List historyList = contentInfo.getContentProductCodeHistory(contentId);
               Iterator var15 = historyList.iterator();

               while(var15.hasNext()) {
                  ContentProductCodeHistoryEntity h = (ContentProductCodeHistoryEntity)var15.next();
                  v2History.add(new V2ProductCodeChangeHistory(h));
               }

               salesData.setHistory(v2History);
            } else {
               insightIndexList.add(v2Index);
            }
         }

         V2ContentAdvertisementResource result = new V2ContentAdvertisementResource();
         result.setInsightIndexes(insightIndexList);
         result.setSalesData(salesData);
         return result;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public V2CommonResultResource setAdvertisement(V2ContentAdvertisementEditResource resource) throws Exception {
      String contentId = resource.getContentId();
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, contentId);
      List successList = new ArrayList();
      List failList = new ArrayList();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      List list = resource.getInsightIndexValueIds();
      if (list == null) {
         list = new ArrayList();
      }

      if (resource.getSalesDataValueIds() != null) {
         V2InsightIndexValueIdListResource productCode = new V2InsightIndexValueIdListResource();
         productCode.setIndexId("PRODUCT_CODE");
         productCode.setValueIds(resource.getSalesDataValueIds());
         ((List)list).add(productCode);
      }

      Iterator var16 = ((List)list).iterator();

      while(var16.hasNext()) {
         V2InsightIndexValueIdListResource valueIdListResource = (V2InsightIndexValueIdListResource)var16.next();
         String indexId = valueIdListResource.getIndexId();
         List currentContentIndexMapList = contentInfo.getValueIdsByContentIdAndIndexId(contentId, indexId);
         Set currentValueSet = (Set)currentContentIndexMapList.stream().map((contentIndexMap) -> {
            return contentIndexMap.getValue_id();
         }).collect(Collectors.toSet());
         List newValues = new ArrayList();
         if (valueIdListResource.getValueIds() != null) {
            Iterator var13 = valueIdListResource.getValueIds().iterator();

            while(var13.hasNext()) {
               Long valueId = (Long)var13.next();
               if (currentValueSet.contains(valueId)) {
                  currentValueSet.remove(valueId);
               } else {
                  newValues.add(valueId);
               }
            }
         }

         List removeValues = (List)currentValueSet.stream().collect(Collectors.toList());
         if (contentInfo.setAdvertisement(contentId, indexId, newValues, removeValues)) {
            successList.add(indexId);
            ContentUtils.updateLastModifiedDate(contentId);
         } else {
            failList.add(indexId);
         }
      }

      V2CommonResultResource result = new V2CommonResultResource();
      result.setSuccessList(successList);
      result.setFailList(failList);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public V2CommonResultResource setAdvertisementMulti(V2ContentAdvertisementMultiEditResource resource) {
      List successList = new ArrayList();
      List failList = new ArrayList();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      String contentId = "";

      try {
         Iterator var6 = resource.getContentIds().iterator();

         label72:
         while(true) {
            while(true) {
               if (!var6.hasNext()) {
                  break label72;
               }

               String c = (String)var6.next();
               contentId = c;
               if (!contentInfo.isExistContentID(c)) {
                  failList.add(c);
               } else {
                  RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, c);
                  List list = resource.getInsightIndexValueIds();
                  if (list == null) {
                     list = new ArrayList();
                  }

                  if (resource.getSalesDataValueIds() != null) {
                     V2InsightIndexValueIdListResource productCode = new V2InsightIndexValueIdListResource();
                     productCode.setIndexId("PRODUCT_CODE");
                     productCode.setValueIds(resource.getSalesDataValueIds());
                     ((List)list).add(productCode);
                  }

                  boolean success = false;
                  if (((List)list).size() == 0) {
                     try {
                        contentInfo.removeAssignedAdvertisement(contentId);
                        success = true;
                     } catch (SQLException var18) {
                        this.logger.error(var18.getMessage());
                     }
                  } else {
                     Iterator var10 = ((List)list).iterator();

                     while(var10.hasNext()) {
                        V2InsightIndexValueIdListResource valueIdListResource = (V2InsightIndexValueIdListResource)var10.next();
                        String indexId = valueIdListResource.getIndexId();
                        List currentContentIndexMapList = contentInfo.getValueIdsByContentIdAndIndexId(contentId, indexId);
                        Set currentValueSet = (Set)currentContentIndexMapList.stream().map((contentIndexMap) -> {
                           return contentIndexMap.getValue_id();
                        }).collect(Collectors.toSet());
                        List newValues = new ArrayList();
                        if (valueIdListResource.getValueIds() != null) {
                           Iterator var16 = valueIdListResource.getValueIds().iterator();

                           while(var16.hasNext()) {
                              Long valueId = (Long)var16.next();
                              if (currentValueSet.contains(valueId)) {
                                 currentValueSet.remove(valueId);
                              } else {
                                 newValues.add(valueId);
                              }
                           }
                        }

                        List removeValues = (List)currentValueSet.stream().collect(Collectors.toList());
                        if (contentInfo.setAdvertisement(contentId, indexId, newValues, removeValues) && !success) {
                           success = true;
                        }
                     }
                  }

                  if (success) {
                     successList.add(contentId);
                     ContentUtils.updateLastModifiedDate(contentId);
                  } else {
                     failList.add(contentId);
                  }
               }
            }
         }
      } catch (Exception var19) {
         this.logger.error("Set Advertisement Error. Content ID : " + contentId + ", error msg : " + var19.getMessage());
         failList.add(contentId);
      }

      V2CommonResultResource result = new V2CommonResultResource();
      result.setSuccessList(successList);
      result.setFailList(failList);
      return result;
   }
}
