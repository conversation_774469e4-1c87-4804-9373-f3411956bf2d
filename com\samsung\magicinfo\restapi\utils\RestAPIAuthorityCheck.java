package com.samsung.magicinfo.restapi.utils;

import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.AccessDeniedException;

abstract class RestAPIAuthorityCheck {
   final ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
   final UserInfo userInfo = UserInfoImpl.getInstance();

   RestAPIAuthorityCheck() {
      super();
   }

   abstract void checkAuthorityById(UserContainer var1, String var2) throws Exception;

   abstract void checkAuthorityByGroupId(UserContainer var1, Long var2) throws Exception;

   abstract void checkPostAuthority(UserContainer var1, Long var2) throws Exception;

   void checkAuthority(UserContainer userContainer, Long targetGroupId, String targetId) throws Exception {
      if (targetId != null && targetId != "") {
         this.checkAuthorityById(userContainer, targetId);
      }

      if (targetGroupId != Long.MIN_VALUE) {
         this.checkAuthorityByGroupId(userContainer, targetGroupId);
      }

   }

   Long getOrganizationId(UserContainer userContainer) throws Exception {
      long organizationId = 0L;
      User user = userContainer.getUser();
      if (user.isMu()) {
         organizationId = this.userInfo.getCurMngOrgId(user.getUser_id());
      } else {
         organizationId = user.getRoot_group_id();
      }

      return organizationId;
   }

   void isAccessGroup(Long groupId, List groups) throws Exception {
      boolean isAccess = false;
      Iterator var4 = groups.iterator();

      while(var4.hasNext()) {
         Group group = (Group)var4.next();
         if (groupId == group.getGroup_id()) {
            isAccess = true;
            break;
         }
      }

      if (!isAccess) {
         this.throwAuthException();
      }

   }

   void isAccessGroup(Long organizationId, Long targetOrganizationId) throws Exception {
      if (targetOrganizationId == null || organizationId != targetOrganizationId) {
         this.throwAuthException();
      }

   }

   void isAccessOrgan(UserContainer userContainer, Long organId) throws Exception {
      Long userOrganId = this.getOrganizationId(userContainer);
      if (userOrganId != organId) {
         this.throwAuthException();
      }

   }

   void throwAuthException() throws Exception {
      Locale locale = SecurityUtils.getLocale();
      String notAuthorizedMessage = this.rms.getMessage("MIS_SID_20_THE_USER_IS_NOT_AUTHORIZED", (Object[])null, "The user is not authorized.", locale);
      throw new AccessDeniedException(notAuthorizedMessage);
   }
}
