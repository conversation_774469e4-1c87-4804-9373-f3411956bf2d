package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.FileInfo;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.service.FileService;
import java.io.FileNotFoundException;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/file"})
public class FileController {
  private FileService fileService;
  
  @Autowired
  public FileController(FileService fileService) {
    this.fileService = fileService;
  }
  
  @GetMapping({"/supportedFormats"})
  public HttpEntity<List<String>> getSupportedFormats(@RequestParam String mediaType, @RequestParam String deviceType) {
    return 
      (HttpEntity<List<String>>)ResponseEntity.ok(this.fileService.getFileTypeList(MediaType.valueOf(mediaType), DeviceType.valueOf(deviceType)));
  }
  
  @GetMapping({"/allSupportedFormats"})
  public HttpEntity<List<String>> getAllSupportedFormatsList(@RequestParam String deviceType) {
    return (HttpEntity<List<String>>)ResponseEntity.ok(this.fileService.getAllSupportedFileTypesForDevice(DeviceType.valueOf(deviceType)));
  }
  
  @GetMapping({"/info"})
  public HttpEntity<FileInfo> getFileInfo(@RequestParam String fileId) throws FileNotFoundException {
    return (HttpEntity<FileInfo>)ResponseEntity.ok(this.fileService.getFileInfo(fileId));
  }
  
  @PostMapping({"/info"})
  public HttpEntity<List<FileInfo>> getListOfFileInfos(@RequestBody List<String> fileIds) {
    return (HttpEntity<List<FileInfo>>)ResponseEntity.ok(this.fileService.getListOfFileInfos(fileIds));
  }
  
  @ExceptionHandler({IllegalArgumentException.class})
  public HttpEntity<String> illegalArgumentExceptionHandler(IllegalArgumentException ex) {
    return (HttpEntity<String>)ResponseEntity.badRequest().body(ex.getMessage());
  }
  
  @ExceptionHandler({FileNotFoundException.class})
  public HttpEntity<String> fileNotFoundExceptionHandler(FileNotFoundException ex) {
    return (HttpEntity<String>)ResponseEntity.status(HttpStatus.NOT_FOUND).body(ex.getMessage());
  }
}
