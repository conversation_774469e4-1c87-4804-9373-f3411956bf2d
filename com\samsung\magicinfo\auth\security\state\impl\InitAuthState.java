package com.samsung.magicinfo.auth.security.state.impl;

import com.samsung.magicinfo.auth.security.AuthResource;
import com.samsung.magicinfo.auth.security.otp.OTPAuthType;
import com.samsung.magicinfo.auth.security.state.AuthState;
import com.samsung.magicinfo.auth.security.strategies.AuthStrategyContext;
import com.samsung.magicinfo.auth.security.strategies.impl.HotpAuthStrategy;
import com.samsung.magicinfo.auth.security.strategies.impl.TotpAuthStrategy;
import com.samsung.magicinfo.auth.security.strategies.model.AuthModel;

public class InitAuthState implements AuthState {
   public InitAuthState() {
      super();
   }

   public OTPAuthType auth(AuthResource resource) {
      AuthModel authModel = new AuthModel();
      authModel.setUserId(resource.getUsername());
      AuthStrategyContext authContext = null;
      if (resource.getTotp() == null && resource.getHotp() == null) {
         authContext = new AuthStrategyContext(new TotpAuthStrategy());
         authModel = authContext.init(authModel);
         return authModel.getOtpAuthType();
      } else {
         authModel.setUserAuthDevice(resource.getUserAuthDevice());
         authContext = new AuthStrategyContext(new HotpAuthStrategy());
         authModel = authContext.init(authModel);
         return authModel.getOtpAuthType();
      }
   }
}
