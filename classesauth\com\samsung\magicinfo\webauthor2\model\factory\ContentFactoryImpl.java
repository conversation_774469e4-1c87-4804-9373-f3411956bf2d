package com.samsung.magicinfo.webauthor2.model.factory;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.model.Audio;
import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.ContentGroup;
import com.samsung.magicinfo.webauthor2.model.Document;
import com.samsung.magicinfo.webauthor2.model.Image;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.Video;
import com.samsung.magicinfo.webauthor2.model.factory.ContentFactory;
import com.samsung.magicinfo.webauthor2.repository.model.ContentData;
import com.samsung.magicinfo.webauthor2.repository.model.ContentGroupData;
import com.samsung.magicinfo.webauthor2.service.RemoteContentService;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ContentFactoryImpl implements ContentFactory {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.model.factory.ContentFactoryImpl.class);
  
  private RemoteContentService remoteContentService;
  
  @Autowired
  public ContentFactoryImpl(RemoteContentService remoteContentService) {
    this.remoteContentService = remoteContentService;
  }
  
  public Content fromData(ContentData contentData) throws IllegalArgumentException {
    String contentFileId, contentFileName;
    int fileSize;
    Content content = new Content(MediaType.valueOf(contentData.getMediaType()), contentData.getContentName(), contentData.getContentId(), contentData.getVersionId(), contentData.getThumbFileId(), contentData.getTotalSize().longValue(), contentData.getThumbFileName(), contentData.getMainFileName(), contentData.getMainFileId(), contentData.getContentDuration(), contentData.getPlayTime(), contentData.getCreateDate(), contentData.getLastModifiedDate(), contentData.getResolution(), contentData.getRefreshInterval(), contentData.getDeviceType(), contentData.getDeviceTypeVersion(), contentData.getHtmlStartPage());
    MediaType mediaType = content.getType();
    switch (null.$SwitchMap$com$samsung$magicinfo$webauthor2$model$MediaType[mediaType.ordinal()]) {
      case 1:
        content.setImage(Image.fromData(contentData));
      case 2:
        content.setVideo(Video.fromData(contentData));
      case 3:
      case 4:
        content.setDocument(Document.fromData(contentData));
      case 5:
        content.setAudio(Audio.fromData(contentData));
      case 6:
      case 7:
      case 8:
      case 9:
      case 10:
      case 11:
      case 12:
      case 13:
      case 14:
      case 15:
      case 16:
        return content;
      case 17:
      case 18:
        contentFileId = content.getFileId();
        contentFileName = content.getFileName();
        fileSize = 0;
        if (!Strings.isNullOrEmpty(contentFileId) && !Strings.isNullOrEmpty(contentFileName)) {
          byte[] contentFile = this.remoteContentService.getContentFileFromMagicInfoServer(contentFileId, contentFileName);
          fileSize = (null == contentFile) ? 0 : contentFile.length;
        } 
        content.setSize(fileSize);
        if (fileSize == 0)
          logger.error("Content.fromData, FileNotExist: " + contentFileId + ", " + contentFileName); 
    } 
    logger.error("Content.fromData, IllegalArgumentException : " + mediaType + " doesn't exist");
    throw new IllegalArgumentException(mediaType + " doesn't exist");
  }
  
  public ContentGroup fromGroupData(ContentGroupData contentGroupData) throws IllegalArgumentException {
    ContentGroup contentGroup = new ContentGroup(contentGroupData.getGroupId(), contentGroupData.getParentGroupId(), contentGroupData.getGroupDepth(), contentGroupData.getGroupName());
    return contentGroup;
  }
  
  public List<Content> fromData(List<ContentData> contentDataList) {
    List<Content> contents = new ArrayList<>();
    for (ContentData data : contentDataList)
      contents.add(fromData(data)); 
    return contents;
  }
  
  public List<ContentGroup> fromGroupData(List<ContentGroupData> contentGroupDataList) {
    List<ContentGroup> contentGroups = new ArrayList<>();
    for (ContentGroupData data : contentGroupDataList)
      contentGroups.add(fromGroupData(data)); 
    return contentGroups;
  }
}
