package com.samsung.magicinfo.webauthor2.model.weather;

import com.samsung.magicinfo.webauthor2.model.weather.CityData;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "Country")
@XmlAccessorType(XmlAccessType.FIELD)
public class CountryData {
  @XmlAttribute(name = "Name")
  String name;
  
  @XmlElement(name = "City")
  private List<CityData> cities = null;
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public List<CityData> getCities() {
    return this.cities;
  }
  
  public void setCities(List<CityData> cities) {
    this.cities = cities;
  }
}
