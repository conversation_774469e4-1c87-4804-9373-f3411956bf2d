package com.samsung.magicinfo.webauthor2.webapi.assembler;

import com.samsung.magicinfo.webauthor2.model.Tag;
import com.samsung.magicinfo.webauthor2.webapi.controller.TagQueryController;
import com.samsung.magicinfo.webauthor2.webapi.resource.TagResource;
import org.springframework.hateoas.ResourceSupport;
import org.springframework.hateoas.mvc.ResourceAssemblerSupport;
import org.springframework.stereotype.Component;

@Component
public class TagResourceAssembler extends ResourceAssemblerSupport<Tag, TagResource> {
  public TagResourceAssembler() {
    super(TagQueryController.class, TagResource.class);
  }
  
  public TagResource toResource(Tag tag) {
    TagResource tagResource = new TagResource(tag, new org.springframework.hateoas.Link[0]);
    return tagResource;
  }
}
