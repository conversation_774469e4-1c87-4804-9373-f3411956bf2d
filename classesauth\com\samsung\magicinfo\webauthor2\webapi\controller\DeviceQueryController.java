package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.model.Device;
import com.samsung.magicinfo.webauthor2.model.DeviceGroup;
import com.samsung.magicinfo.webauthor2.properties.MagicInfoProperties;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupLayout;
import com.samsung.magicinfo.webauthor2.service.DeviceService;
import java.util.List;
import javax.inject.Inject;
import org.springframework.hateoas.mvc.BasicLinkBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/deviceGroups"})
public class DeviceQueryController {
  private DeviceService service;
  
  private MagicInfoProperties magicInfoProperties;
  
  @Inject
  public DeviceQueryController(DeviceService deviceService, MagicInfoProperties magicInfoProperties) {
    this.service = deviceService;
    this.magicInfoProperties = magicInfoProperties;
  }
  
  @GetMapping
  public HttpEntity<List<DeviceGroup>> getDeviceGroupList() {
    List<DeviceGroup> deviceServices = this.service.getDeviceGroupList();
    if (deviceServices == null)
      return (HttpEntity<List<DeviceGroup>>)ResponseEntity.notFound().build(); 
    return (HttpEntity<List<DeviceGroup>>)ResponseEntity.ok(deviceServices);
  }
  
  @GetMapping({"/parentGroupId"})
  public HttpEntity<List<DeviceGroup>> getDeviceGroupInfo(@RequestParam(required = false) String parentGroupId) {
    List<DeviceGroup> deviceServices = this.service.getDeviceGroupInfo(parentGroupId);
    if (deviceServices == null)
      return (HttpEntity<List<DeviceGroup>>)ResponseEntity.notFound().build(); 
    return (HttpEntity<List<DeviceGroup>>)ResponseEntity.ok(deviceServices);
  }
  
  @GetMapping({"/groupId"})
  public HttpEntity<List<Device>> getDeviceList(@RequestParam(required = false) String groupId) {
    List<Device> deviceServices = this.service.getDeviceList(groupId);
    if (deviceServices == null)
      return (HttpEntity<List<Device>>)ResponseEntity.notFound().build(); 
    return (HttpEntity<List<Device>>)ResponseEntity.ok(deviceServices);
  }
  
  @GetMapping({"/VwLayoutByGroupId"})
  public HttpEntity<String> getVideowallLayoutUrl(@RequestParam(required = false) String groupId) {
    DeviceGroupLayout groupLayout = this.service.getVideowallLayoutPath(groupId);
    if (groupLayout.getVwtId().equals(""))
      return (HttpEntity<String>)ResponseEntity.ok(""); 
    String layoutUrl = ((BasicLinkBuilder)((BasicLinkBuilder)((BasicLinkBuilder)BasicLinkBuilder.linkToCurrentMapping().slash("vwt")).slash(groupLayout.getVwtId())).slash(groupLayout.getVwtFileName())).toString();
    return (HttpEntity<String>)ResponseEntity.ok(layoutUrl);
  }
  
  @GetMapping({"/LedLayoutByDeviceId"})
  public HttpEntity<String> getLedLayoutUrl(@RequestParam(required = false) String deviceId) {
    DeviceGroupLayout groupLayout = this.service.getVideowallLayoutPath(deviceId);
    if (groupLayout.getVwtId().equals(""))
      return (HttpEntity<String>)ResponseEntity.ok(""); 
    String layoutUrl = ((BasicLinkBuilder)((BasicLinkBuilder)((BasicLinkBuilder)BasicLinkBuilder.linkToCurrentMapping().slash("vwt")).slash(groupLayout.getVwtId())).slash(groupLayout.getVwtFileName())).toString();
    return (HttpEntity<String>)ResponseEntity.ok(layoutUrl);
  }
  
  @GetMapping({"/hasWPlayerDevice"})
  public HttpEntity<Boolean> hasAnyWPlayerDevice() {
    boolean showWPlayer = this.magicInfoProperties.getWebauthorDevelopment().equals("1");
    Boolean hasWPlayerDevice = this.service.hasWPlayerDevice();
    return (HttpEntity<Boolean>)ResponseEntity.ok(Boolean.valueOf((hasWPlayerDevice.booleanValue() || showWPlayer)));
  }
  
  @ExceptionHandler({IllegalArgumentException.class})
  ResponseEntity<String> handleIllegalArgumentException(IllegalArgumentException ex) {
    return ResponseEntity.badRequest().body(ex.getMessage());
  }
}
