package com.samsung.magicinfo.webauthor2.service.textImage;

import com.google.common.io.Files;
import com.samsung.magicinfo.webauthor2.model.svg.TextImageDescriptor;
import com.samsung.magicinfo.webauthor2.service.textImage.SVGgenerationService;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import org.apache.commons.io.FileUtils;
import org.apache.commons.net.util.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class SVGgenerationServiceImpl implements SVGgenerationService {
  private static final Logger LOGGER = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.textImage.SVGgenerationServiceImpl.class);
  
  public void createSVG(String svgPath, TextImageDescriptor textElement, String fontFilePath) {
    String svgString = createSVGstring(textElement, encodeFont(fontFilePath));
    try {
      FileUtils.writeStringToFile(new File(svgPath), svgString, StandardCharsets.UTF_8);
    } catch (IOException ex) {
      LOGGER.error("SVGfileWrite", ex.getMessage());
    } 
  }
  
  private String encodeFont(String fontFilePath) {
    byte[] encodedFont = new byte[0];
    try {
      encodedFont = Base64.encodeBase64(Files.toByteArray(new File(fontFilePath)));
    } catch (IOException ex) {
      LOGGER.error("Font encoding error", ex.toString());
    } 
    return new String(encodedFont, StandardCharsets.UTF_8);
  }
  
  public String createSVGstring(TextImageDescriptor textElement, String fontBase64) {
    StringBuilder data = new StringBuilder();
    Double angle = Double.valueOf(textElement.getShadowAngle() * Math.PI / 180.0D);
    long x = Math.round(Math.cos(angle.doubleValue()) * textElement.getShadowOffset());
    long y = Math.round(Math.sin(angle.doubleValue()) * textElement.getShadowOffset());
    int from = textElement.getOutlineWidth() / 2 * -1;
    int to = textElement.getOutlineWidth() / 2;
    String outline = "";
    String verticalAlignStyle = "";
    String dimensionsStyle = "";
    String styles = "";
    for (int i = from; i < to; i++) {
      for (int j = from; j < to; j++)
        outline = outline + (i / textElement.getFontSize1_24()) + "em " + (j / textElement.getFontSize1_24()) + "em 0 " + textElement.getOutlineColor() + ","; 
    } 
    outline = outline.substring(0, outline.length() - 1);
    String shadow = (x / textElement.getFontSize1_24()) + "em " + (y / textElement.getFontSize1_24()) + "em 0 " + textElement.getShadowColor().toString();
    switch (textElement.getTextVerticalAlign()) {
      case "top":
        verticalAlignStyle = "bottom: auto; top: 0;";
        dimensionsStyle = " width: " + textElement.getWidth() + "px; height: " + textElement.getHeight() + "px;";
        break;
      case "middle":
        verticalAlignStyle = "bottom: 0; top: 50%; transform: translateY(-50%);";
        break;
      case "bottom":
        verticalAlignStyle = "bottom: 0; top: auto;";
        break;
      default:
        verticalAlignStyle = "";
        dimensionsStyle = "";
        break;
    } 
    styles = "<style type=\"text/css\">@font-face {font-family:'" + textElement.getFontFamily() + "';src: url('data:application/x-font-ttf;base64," + fontBase64 + "') format('truetype');}</style>";
    data.append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"");
    data.append(textElement.getWidth());
    data.append("\" height=\"");
    data.append(textElement.getHeight());
    data.append("\">");
    data.append(styles);
    data.append("<foreignObject width=\"100%\" height=\"100%\">");
    data.append("<div xmlns=\"http://www.w3.org/1999/xhtml\" ");
    data.append("style=\"bottom: 0; left: 0; padding: 2px;  position: absolute; right: 0; top: 0; white-space: pre-wrap; word-wrap: break-word; width: ");
    data.append(textElement.getWidth());
    data.append("px; height: ");
    data.append(textElement.getHeight());
    data.append("px; background-color: ");
    data.append((textElement.getBackgroundColor() != null) ? textElement.getBackgroundColor().toString() : "transparent");
    data.append(";\"></div>");
    data.append("<div xmlns=\"http://www.w3.org/1999/xhtml\" ");
    data.append(" style=\"bottom: 0; left: 0; padding: 2px;  position: absolute; right: 0; top: 0; white-space: pre-wrap; word-wrap: break-word; width: ");
    data.append(textElement.getWidth());
    data.append("px;");
    data.append(dimensionsStyle);
    data.append("   color:  ");
    data.append(textElement.getColor().toString());
    data.append(" ; font-family: '");
    data.append(textElement.getFontFamily());
    data.append("'; font-size: ");
    data.append(textElement.getFontSize1_24());
    data.append("px; ");
    data.append("   font-weight: ");
    data.append(textElement.getFontWeight());
    data.append(";  ");
    data.append("   font-style: ");
    data.append(textElement.getFontStyle());
    data.append("; ");
    data.append("   text-decoration: ");
    data.append(textElement.getTextDecoration());
    data.append("; ");
    data.append("   word-wrap: break-word; white-space: normal; ");
    data.append("   text-align: ");
    data.append(textElement.getTextAlign());
    data.append(";");
    data.append("   overflow: hidden;");
    data.append(verticalAlignStyle);
    data.append("\">");
    data.append("    <div style=\"padding: 0 ");
    data.append(Math.floor(textElement.getFontSize1_24() / 5.0D));
    data.append("px; overflow:hidden;\">");
    data.append(textElement.getText().replaceAll("\\s\\s", "&nbsp;").replaceAll("\\n", "<br />"));
    data.append("   </div>");
    data.append("</div>");
    if (textElement.isShadow()) {
      data.append("<div xmlns=\"http://www.w3.org/1999/xhtml\" ");
      data.append(" style=\"bottom: 0; left: 0; padding: 2px;  position: absolute; right: 0; top: 0; white-space: pre-wrap; word-wrap: break-word; width: ");
      data.append(textElement.getWidth());
      data.append("px;");
      data.append("   color: ");
      data.append(textElement.getColor().toString());
      data.append("; font-family: '");
      data.append(textElement.getFontFamily());
      data.append("' ; font-size: ");
      data.append(textElement.getFontSize1_24());
      data.append("px;");
      data.append(dimensionsStyle);
      data.append("   font-weight: ");
      data.append(textElement.getFontWeight());
      data.append(";    font-style: ");
      data.append(textElement.getFontStyle());
      data.append(";    text-decoration: ");
      data.append(textElement.getTextDecoration());
      data.append(";    word-wrap: break-word; white-space: normal; ");
      data.append("   text-align: ");
      data.append(textElement.getTextAlign());
      data.append(";");
      data.append("   text-shadow: ");
      data.append(shadow);
      data.append(";    overflow: hidden;    opacity: ");
      data.append(textElement.getShadowOpacityDivided() / 100.0D);
      data.append(";");
      data.append(verticalAlignStyle);
      data.append("\">");
      data.append("   <div style=\"padding: 0 ");
      data.append(Math.floor(textElement.getFontSize1_24() / 5.0D));
      data.append("px; overflow:hidden;\">");
      data.append(textElement.getText().replaceAll("\\s\\s", "&nbsp;").replaceAll("\\n", "<br />"));
      data.append("   </div>");
      data.append("</div>");
    } 
    if (textElement.isOutline()) {
      data.append("<div xmlns=\"http://www.w3.org/1999/xhtml\" ");
      data.append(" style=\"bottom: 0; left: 0; padding: 2px;  position: absolute; right: 0; top: 0; white-space: pre-wrap; word-wrap: break-word; width: ");
      data.append(textElement.getWidth());
      data.append("px;");
      data.append("   color: ");
      data.append(textElement.getColor().toString());
      data.append("; font-family: '");
      data.append(textElement.getFontFamily());
      data.append("'; font-size: ");
      data.append(textElement.getFontSize1_24());
      data.append("px;");
      data.append(dimensionsStyle);
      data.append("   font-weight: ");
      data.append(textElement.getFontWeight());
      data.append(";    font-style: ");
      data.append(textElement.getFontStyle());
      data.append(";    text-decoration: ");
      data.append(textElement.getTextDecoration());
      data.append(";    word-wrap: break-word; white-space: normal; ");
      data.append("   text-align: ");
      data.append(textElement.getTextAlign());
      data.append(";");
      data.append("   text-shadow: ");
      data.append(outline);
      data.append(";    overflow: hidden;    opacity: ");
      data.append(textElement.getOutlineOpacity() / 100.0D);
      data.append(";");
      data.append(verticalAlignStyle);
      data.append("\">");
      data.append("   <div style=\"padding: 0 ");
      data.append(Math.floor(textElement.getFontSize1_24() / 5.0D));
      data.append("px; overflow:hidden;\">");
      data.append(textElement.getText().replaceAll("\\s\\s", "&nbsp;").replaceAll("\\n", "<br />"));
      data.append("   </div>");
      data.append("</div>");
    } 
    data.append("</foreignObject>");
    data.append("</svg>");
    return data.toString();
  }
}
