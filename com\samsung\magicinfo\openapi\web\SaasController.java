package com.samsung.magicinfo.openapi.web;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.scheduler.entity.MessageEntity;
import com.samsung.magicinfo.framework.scheduler.manager.MessageGroupInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageGroupInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.openapi.impl.OpenApiExceptionCode;
import com.samsung.magicinfo.openapi.impl.OpenApiParameter;
import com.samsung.magicinfo.openapi.impl.OpenApiParameterValidator;
import com.samsung.magicinfo.openapi.impl.OpenApiServiceException;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.IOException;
import java.io.StringReader;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

public class SaasController {
   static Logger logger = LoggingManagerV2.getLogger(SaasController.class);
   UserContainer uc;
   String serviceName;
   String methodName;
   String scope;
   OpenApiRequest oaReq;
   OpenApiParameter[] oaParams;
   static SaasLogicCheckMap[] mapIsScopeAll = new SaasLogicCheckMap[]{new SaasLogicCheckMap("isScopeAll", "LiteDeviceApproveService", "approveDevice", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "LiteDeviceApproveService", "getNonApprovedDeviceList", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "PremiumDeviceApproveService", "approveDevice", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "PremiumDeviceApproveService", "getNonApprovedDeviceList", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "CommonUserService", "addOrganization", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "CommonUserService", "changeUserOrganization", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "CommonUserService", "deleteOrganization", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "CommonUserService", "getOrganizationList", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "CommonUserService", "addUserScopeALL", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "CommonUserService", "addRoleScopeALL", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "CommonUserService", "getRoleListScopeALL", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "CommonUserService", "modifyUserRoleScopeALL", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "CommonUserService", "modifyOrganization", (String)null, (String)null, (String)null)};
   static SaasLogicCheckMap[] mapIsAdmin = new SaasLogicCheckMap[]{new SaasLogicCheckMap("isScopeAll", "CommonSettingService", "approveDevice", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "CommonSettingService", "getDeviceUpdateInterval", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "CommonSettingService", "getMonitoringInterval", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "CommonSettingService", "getSystemInfo", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "CommonSettingService", "setDeviceUpdateInterval", (String)null, (String)null, (String)null), new SaasLogicCheckMap("isScopeAll", "CommonSettingService", "setMonitoringInterval ", (String)null, (String)null, (String)null)};
   static SaasLogicCheckMap[] mapIsSameOrganization = new SaasLogicCheckMap[]{new SaasLogicCheckMap("checkUserById", "CommonSettingService", " withdrawUser ", "userId", "userId", (String)null), new SaasLogicCheckMap("checkUserById", "CommonUserService", "getUserInfo", "userId", "userId", (String)null), new SaasLogicCheckMap("checkUserById", "CommonUserService", "deleteUser", "userId", "userId", (String)null), new SaasLogicCheckMap("checkUserGroupById", "CommonUserService", "getUserList", "groupId", "groupId", (String)null), new SaasLogicCheckMap("checkUserGroupById", "CommonUserService", "getUserGroupList", "groupId", "groupId", (String)null), new SaasLogicCheckMap("checkUserGroupById", "CommonUserService", "modifyUserGroup", "groupId", "groupId", (String)null), new SaasLogicCheckMap("checkUserGroupById", "CommonUserService", "deleteUserGroup", "groupId", "groupId", (String)null), new SaasLogicCheckMap("checkUserById", "CommonUserService", "rejectUser", "userId", "userId", (String)null), new SaasLogicCheckMap("checkUserById", "CommonUserService", "acceptUser", "userId", "userId", (String)null), new SaasLogicCheckMap("checkUserGroupById", "CommonUserService", "changeUserGroup", "chGroupId", "chGroupId", (String)null), new SaasLogicCheckMap("checkUserById", "CommonUserService", "changeUserGroup", "userId", "userId", (String)null), new SaasLogicCheckMap("checkContentGroupById", "CommonContentService", "modifyContentGroup", "groupId", "groupId", (String)null), new SaasLogicCheckMap("checkContentGroupById", "CommonContentService", "deleteContentGroup", "groupId", "groupId", (String)null), new SaasLogicCheckMap("checkUserById", "CommonContentService", "getContentListByUser", "userId", "userId", (String)null), new SaasLogicCheckMap("checkUserById", "CommonContentService", "getContentList", "userId", "userId", (String)null), new SaasLogicCheckMap("checkUserById", "PremiumPlaylistService", "getPlaylistListByUser", "userId", "userId", (String)null), new SaasLogicCheckMap("checkUserById", "LitePlaylistService", "getPlaylistListByUser", "userId", "userId", (String)null), new SaasLogicCheckMap("checkLiteDevGroupByLiteDevId", "LiteDeviceService", "deleteDeviceInfo", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkLiteDevGroupByLiteDevId", "LiteDeviceService", "getDeviceBasicInfo", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkLiteDevGroupByLiteDevId", "LiteDeviceService", "getDeviceConnection", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkLiteDevGroupByLiteDevId", "LiteDeviceService", "getDeviceDisplayInfo", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkLiteDevGroupByLiteDevId", "LiteDeviceService", "getDeviceDisplayInfoByRealTime", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkLiteDevGroupByLiteDevId", "LiteDeviceService", "getDevicePlayingContent", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkLiteDevGroupByLiteDevId", "LiteDeviceService", "getDeviceSystemInfo", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkLiteDevGroupByLiteDevId", "LiteDeviceService", "getDeviceSystemInfoByRealTime", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkLiteDevGroupByLiteDevId", "LiteDeviceService", "getDeviceSystemSetupInfo", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkLiteDevGroupByLiteDevId", "LiteDeviceService", "getDeviceTimeInfo", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkLiteDevGroupByLiteDevId", "LiteDeviceService", "getDeviceTimeInfoByRealTime", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkLiteDevGroupByLiteDevId", "LiteDeviceService", "getMagicInfoServer", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevId", "PremiumDeviceService", "deleteDeviceInfo", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevId", "PremiumDeviceService", "getDeviceBasicInfo", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevId", "PremiumDeviceService", "getDeviceConnection", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevId", "PremiumDeviceService", "getDeviceDisplayInfo", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevId", "PremiumDeviceService", "getDeviceDisplayInfoByRealTime", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevId", "PremiumDeviceService", "getDevicePlayingContent", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevId", "PremiumDeviceService", "getDeviceSystemInfo", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevId", "PremiumDeviceService", "getDeviceSystemInfoByRealTime", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevId", "PremiumDeviceService", "getDeviceSystemSetupInfo", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevId", "PremiumDeviceService", "getDeviceTimeInfo", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevId", "PremiumDeviceService", "getDeviceTimeInfoByRealTime", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevId", "PremiumDeviceService", "getMagicInfoServer", "deviceId", "deviceId", (String)null), new SaasLogicCheckMap("checkProgramById", "PremiumScheduleService", "getProgramInfo", "programId", "programId", (String)null), new SaasLogicCheckMap("checkMessageById", "PremiumScheduleService", "getMessageInfo", "messageId", "messageId", (String)null), new SaasLogicCheckMap("checkProgramById", "PremiumScheduleService", "deleteProgram", "programId", "programId", (String)null), new SaasLogicCheckMap("checkMessageById", "PremiumScheduleService", "deleteMessage", "messageId", "messageId", (String)null), new SaasLogicCheckMap("checkMessageGroupById", "PremiumScheduleService", "deleteProgramGroup", "groupId", "groupId", (String)null), new SaasLogicCheckMap("checkMessageGroupById", "PremiumScheduleService", "deleteMessageGroup", "groupId", "groupId", (String)null), new SaasLogicCheckMap("checkProgramById", "PremiumScheduleService", "getFrameInfo", "programId", "programId", (String)null)};
   static SaasLogicCheckMap[] mapIsSameOrganizationXML = new SaasLogicCheckMap[]{new SaasLogicCheckMap("checkUserById", "CommonUserService", "modifyUser", "user_id", "user", "xml"), new SaasLogicCheckMap("checkSameOrganizationByOrganId", "CommonContentService", "addContentGroup", "organization_id", "group", "xml"), new SaasLogicCheckMap("checkLiteDevGroupByLiteDevId", "LiteDeviceService", "modifyDeviceDisplayInfo", "device_id", "deviceDisplayConf", "xml"), new SaasLogicCheckMap("checkLiteDevGroupByLiteDevId", "LiteDeviceService", "modifyDeviceSystemSetupInfo", "device_id", "DeviceSystemSetupConf", "xml"), new SaasLogicCheckMap("checkLiteDevGroupByLiteDevId", "LiteDeviceService", "modifyDeviceTimeInfo", "device_id", "deviceTimeConf", "xml"), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevId", "PremiumDeviceService", "modifyDeviceDisplayInfo", "device_id", "deviceDisplayConf", "xml"), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevId", "PremiumDeviceService", "modifyDeviceSystemSetupInfo", "device_id", "DeviceSystemSetupConf", "xml"), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevId", "PremiumDeviceService", "modifyDeviceTimeInfo", "device_id", "deviceTimeConf", "xml"), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevGroup", "PremiumDeviceService", "getDeviceList", "groupId", "condition", "xml"), new SaasLogicCheckMap("checkProgramGroupById", "PremiumScheduleService", "addProgramWithBasicInformation", "program_group_id", "ProgramEntity", "xml"), new SaasLogicCheckMap("checkMessageGroupById", "PremiumScheduleService", "addMessage", "message_group_id", "MessageEntity", "xml"), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevGroups", "PremiumScheduleService", "addProgramWithBasicInformation", "device_group_ids", "ProgramEntity", "xml"), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevGroups", "PremiumScheduleService", "modifyProgramWithBasicInformation", "device_group_ids", "ProgramEntity", "xml"), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevGroups", "PremiumScheduleService", "addMessage", "device_groups_ids", "MessageEntity", "xml"), new SaasLogicCheckMap("checkPremiumDevGroupByPremiumDevGroups", "PremiumScheduleService", "modifyMessage", "device_groups_ids", "MessageEntity", "xml"), new SaasLogicCheckMap("checkProgramById", "PremiumScheduleService", "modifyProgramWithBasicInformation", "program_id", "ProgramEntity", "xml"), new SaasLogicCheckMap("checkProgramGroupById", "PremiumScheduleService", "modifyProgramWithBasicInformation", "program_group_id", "ProgramEntity", "xml"), new SaasLogicCheckMap("checkMessageById", "PremiumScheduleService", "modifyMessage", "message_id", "MessageEntity", "xml"), new SaasLogicCheckMap("checkMessageGroupById", "PremiumScheduleService", "modifyMessage", "message_group_id", "MessageEntity", "xml"), new SaasLogicCheckMap("checkProgramGroupById", "PremiumScheduleService", "addProgramGroup", "p_group_id", "ProgramGroup", "xml"), new SaasLogicCheckMap("checkProgramGroupById", "PremiumScheduleService", "modifyProgramGroup", "group_id", "ProgramGroup", "xml"), new SaasLogicCheckMap("checkProgramGroupById", "PremiumScheduleService", "modifyProgramGroup", "p_group_id", "ProgramGroup", "xml"), new SaasLogicCheckMap("checkMessageGroupById", "PremiumScheduleService", "addMessageGroup", "p_group_id", "MessageGroup", "xml"), new SaasLogicCheckMap("checkMessageGroupById", "PremiumScheduleService", "modifyMessageGroup", "group_id", "MessageGroup", "xml"), new SaasLogicCheckMap("checkMessageGroupById", "PremiumScheduleService", "modifyMessageGroup", "p_group_id", "MessageGroup", "xml"), new SaasLogicCheckMap("checkProgramById", "PremiumScheduleService", "addFrame", "program_id", "FrameEntity", "xml"), new SaasLogicCheckMap("checkProgramById", "PremiumScheduleService", "addFrame", "program_id", "FrameEntity", "xml")};
   static SaasLogicCheckMap[] mapIsSameOrganizationList = new SaasLogicCheckMap[]{new SaasLogicCheckMap("isSameOrganizationList", "CommonUserService", "apiName", "userId", "userId", "list")};

   public SaasController(UserContainer uc, String serviceName, String methodName, String scope, OpenApiRequest oaReq, OpenApiParameter[] oaParams) {
      super();
      this.uc = uc;
      this.serviceName = serviceName;
      this.methodName = methodName;
      this.scope = scope;
      this.oaReq = oaReq;
      this.oaParams = oaParams;
   }

   private InputSource strToInputSource(String xmlString) {
      return new InputSource(new StringReader(xmlString));
   }

   private String getElementFromXml(String xmlString, String elementName) throws ParserConfigurationException, SAXException {
      DocumentBuilderFactory dbf = DocumentUtils.getDocumentBuilderFactoryInstance();
      Document doc = null;

      try {
         DocumentBuilder db = dbf.newDocumentBuilder();
         doc = db.parse(this.strToInputSource(xmlString));
      } catch (ParserConfigurationException var7) {
         logger.error("", var7);
      } catch (SAXException var8) {
         logger.error("", var8);
      } catch (IOException var9) {
         logger.error("", var9);
      }

      NodeList nodeList = doc.getDocumentElement().getElementsByTagName(elementName);
      return nodeList != null && nodeList.getLength() > 0 ? nodeList.item(0).getTextContent() : null;
   }

   private ArrayList getElement(SaasLogicCheckMap saasLogicCheckMap) {
      ArrayList elementList = new ArrayList();

      try {
         int i;
         if (saasLogicCheckMap.getParameterStyle() == null) {
            for(i = 0; i < this.oaReq.getParameterSize(); ++i) {
               if (saasLogicCheckMap.elementName.equals(this.oaParams[i].getParamName())) {
                  elementList.add(this.oaReq.getParameter().get(i).toString());
                  break;
               }
            }
         } else if (saasLogicCheckMap.getParameterStyle().equals("xml")) {
            for(i = 0; i < this.oaReq.getParameterSize(); ++i) {
               if (saasLogicCheckMap.parameterName.equals(this.oaParams[i].getParamName())) {
                  String elementValue = this.getElementFromXml(this.oaReq.getParameter().get(i).toString(), saasLogicCheckMap.elementName);
                  elementList.add(elementValue);
               }
            }
         } else if (saasLogicCheckMap.getParameterStyle().equals("list")) {
         }
      } catch (ParserConfigurationException var5) {
         logger.error(var5.toString());
      } catch (SAXException var6) {
         logger.error(var6.toString());
      } catch (Exception var7) {
         logger.error(var7.toString());
      }

      return elementList;
   }

   public boolean isValid() throws OpenApiServiceException {
      if (this.scope.equals("ALL")) {
         return this.checkSaasValidator(mapIsScopeAll);
      } else if (this.scope.equals("GROUP")) {
         if (this.checkSaasValidator(mapIsSameOrganization)) {
            return true;
         } else if (this.checkSaasValidator(mapIsSameOrganizationXML)) {
            return true;
         } else {
            return this.checkSaasValidator(mapIsSameOrganizationList);
         }
      } else {
         return false;
      }
   }

   public boolean checkSaasValidator(SaasLogicCheckMap[] saasLogicCheckMap) throws OpenApiServiceException {
      boolean result = false;

      for(int i = 0; i < saasLogicCheckMap.length; ++i) {
         if (saasLogicCheckMap[i].isExistMethod(this.serviceName, this.methodName)) {
            this.invokeSaasValidator(saasLogicCheckMap[i]);
            result = true;
         }
      }

      return result;
   }

   public void invokeSaasValidator(SaasLogicCheckMap saasLogicCheckMap) throws OpenApiServiceException {
      Object result = null;
      Class parameterTypes = SaasLogicCheckMap.class;

      try {
         Method invokeMethod = this.getClass().getMethod(saasLogicCheckMap.getInvokeMethodname(), parameterTypes);
         result = invokeMethod.invoke(this, saasLogicCheckMap);
      } catch (IllegalArgumentException var5) {
         logger.error("", var5);
      } catch (IllegalAccessException var6) {
         logger.error("", var6);
      } catch (InvocationTargetException var7) {
         logger.error("", var7);
      } catch (SecurityException var8) {
         logger.error("", var8);
      } catch (NoSuchMethodException var9) {
         logger.error("", var9);
      }

      if (result.equals(false)) {
         throw new OpenApiServiceException(OpenApiExceptionCode.U110[0], OpenApiExceptionCode.U110[1]);
      }
   }

   public boolean isScopeAll(SaasLogicCheckMap saasLogicCheckMap) {
      return this.scope.equals("ALL");
   }

   public boolean checkUserById(SaasLogicCheckMap saasLogicCheckMap) throws SQLException {
      ArrayList elementList = this.getElement(saasLogicCheckMap);
      UserInfo userInfo = UserInfoImpl.getInstance();
      long rootGroupId = userInfo.getRootGroupIdByUserId((String)elementList.get(0));
      return rootGroupId == this.uc.getUser().getRoot_group_id();
   }

   public boolean checkUserGroupById(SaasLogicCheckMap saasLogicCheckMap) throws NumberFormatException, SQLException {
      ArrayList elementList = this.getElement(saasLogicCheckMap);
      OpenApiParameterValidator openApiParameterValidator = new OpenApiParameterValidator();
      UserGroupInfo userGroupDao = UserGroupInfoImpl.getInstance();
      UserGroup userGroup = userGroupDao.getAllByUserGroupId(Long.valueOf((String)elementList.get(0)));
      return openApiParameterValidator.isUserAllManager(this.uc) || this.uc.getUser().getRoot_group_id().equals(userGroup.getRoot_group_id());
   }

   public boolean checkSameOrganizationByOrganId(SaasLogicCheckMap saasLogicCheckMap) throws NumberFormatException, SQLException {
      ArrayList elementList = this.getElement(saasLogicCheckMap);
      return this.uc.getUser().getRoot_group_id() == Long.valueOf((String)elementList.get(0));
   }

   public boolean checkContentGroupById(SaasLogicCheckMap saasLogicCheckMap) throws NumberFormatException, SQLException {
      ArrayList elementList = this.getElement(saasLogicCheckMap);
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      Group cGroup = cInfo.getGroupInfo(Long.valueOf((String)elementList.get(0)));
      return cInfo.isExistGroupName(cGroup.getGroup_name(), cGroup.getCreator_id(), this.uc.getUser().getRoot_group_id());
   }

   public boolean checkPremiumDevGroupByPremiumDevId(SaasLogicCheckMap saasLogicCheckMap) throws NumberFormatException, SQLException {
      ArrayList elementList = this.getElement(saasLogicCheckMap);
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      String devGroupId = deviceInfo.getDeviceGroupIdByDeviceId((String)elementList.get(0));
      String devOrganName = deviceGroupInfo.getDeviceGroupRoot(Integer.parseInt(devGroupId));
      return devOrganName.equals(this.uc.getUser().getOrganization());
   }

   public boolean checkPremiumDevGroupByPremiumDevGroup(SaasLogicCheckMap saasLogicCheckMap) throws NumberFormatException, SQLException {
      ArrayList elementList = this.getElement(saasLogicCheckMap);
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      String devGroupId = (String)elementList.get(0);
      if (devGroupId == null) {
         return true;
      } else if (devGroupId.equals(String.valueOf(0))) {
         return false;
      } else {
         String devOrganName = deviceGroupInfo.getDeviceGroupRoot(Integer.parseInt(devGroupId));
         return devOrganName.equals(this.uc.getUser().getOrganization());
      }
   }

   public boolean checkPremiumDevGroupByPremiumDevGroups(SaasLogicCheckMap saasLogicCheckMap) throws NumberFormatException, SQLException {
      ArrayList elementList = this.getElement(saasLogicCheckMap);
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      String devGroupIds = (String)elementList.get(0);
      String[] arrDevGroupIds = devGroupIds.split(",");
      int i = 0;

      for(int len = arrDevGroupIds.length; i < len; ++i) {
         if (!"".equals(arrDevGroupIds[i]) && arrDevGroupIds[i] != null) {
            String devGroupId = arrDevGroupIds[i];
            if (devGroupId.equals(String.valueOf(0))) {
               return false;
            }

            String devOrganName = deviceGroupInfo.getDeviceGroupRoot(Integer.parseInt(devGroupId));
            if (!devOrganName.equals(this.uc.getUser().getOrganization())) {
               return false;
            }
         }
      }

      return true;
   }

   public boolean checkProgramGroupById(SaasLogicCheckMap saasLogicCheckMap) throws NumberFormatException, SQLException {
      ArrayList elementList = this.getElement(saasLogicCheckMap);
      ProgramGroupInfo groupDao = ProgramGroupInfoImpl.getInstance();
      int targetGroupId = Integer.parseInt((String)elementList.get(0));
      int orgId = groupDao.getProgramOrgGroupId(targetGroupId);
      String pOrgName = groupDao.getGroup(orgId).getGroup_name();
      return pOrgName.equals(this.uc.getUser().getOrganization());
   }

   public boolean checkMessageGroupById(SaasLogicCheckMap saasLogicCheckMap) throws NumberFormatException, SQLException {
      ArrayList elementList = this.getElement(saasLogicCheckMap);
      MessageGroupInfo groupDao = MessageGroupInfoImpl.getInstance();
      int targetGroupId = Integer.parseInt((String)elementList.get(0));
      int pId = groupDao.getParentGroupId(targetGroupId);
      if (groupDao.getGroup(pId).getDescription().equals("root node")) {
         pId = targetGroupId;
      } else {
         while(!groupDao.getGroup(pId).getDescription().equals("Organization")) {
            pId = groupDao.getParentGroupId(pId);
         }
      }

      String pOrgName = groupDao.getGroup(pId).getGroup_name();
      return pOrgName.equals(this.uc.getUser().getOrganization());
   }

   public boolean checkProgramById(SaasLogicCheckMap saasLogicCheckMap) throws NumberFormatException, SQLException {
      ArrayList elementList = this.getElement(saasLogicCheckMap);
      ScheduleInfo programInfo = ScheduleInfoImpl.getInstance();
      ProgramGroupInfo groupDao = ProgramGroupInfoImpl.getInstance();
      Long targetGroupId = 0L;
      List list = programInfo.getProgramGroupIdAndName((String)elementList.get(0));
      if (list != null && list.size() != 0) {
         Map p = (Map)list.get(0);
         targetGroupId = (Long)p.get("group_id");
      }

      if (groupDao.getGroup(targetGroupId.intValue()).getDescription().equals("Organization")) {
         return true;
      } else {
         int orgId = groupDao.getProgramOrgGroupId(targetGroupId.intValue());
         String pOrgName = groupDao.getGroup(orgId).getGroup_name();
         return pOrgName.equals(this.uc.getUser().getOrganization());
      }
   }

   public boolean checkMessageById(SaasLogicCheckMap saasLogicCheckMap) throws NumberFormatException, SQLException {
      ArrayList elementList = this.getElement(saasLogicCheckMap);
      MessageInfo messageInfo = MessageInfoImpl.getInstance();
      long messageGroupId = 0L;

      try {
         MessageEntity message = messageInfo.getMessage((String)elementList.get(0), 0);
         messageGroupId = message.getMessage_group_id();
      } catch (ConfigException var9) {
         logger.error("", var9);
      }

      if (messageGroupId == this.uc.getUser().getRoot_group_id()) {
         return true;
      } else {
         MessageGroupInfo groupDao = MessageGroupInfoImpl.getInstance();

         int pId;
         for(pId = groupDao.getParentGroupId((int)messageGroupId); !groupDao.getGroup(pId).getDescription().equals("Organization"); pId = groupDao.getParentGroupId(pId)) {
         }

         String pOrgName = groupDao.getGroup(pId).getGroup_name();
         return pOrgName.equals(this.uc.getUser().getOrganization());
      }
   }
}
