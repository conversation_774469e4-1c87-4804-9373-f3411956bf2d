package com.samsung.magicinfo.webauthor2.repository.datalink.servers;

import com.samsung.magicinfo.webauthor2.repository.datalink.servers.DLKMethod;
import com.samsung.magicinfo.webauthor2.repository.model.dlk.DLKTableData;
import com.samsung.magicinfo.webauthor2.repository.model.dlk.GetDataTableListDLKResponseData;
import java.util.ArrayList;
import java.util.List;
import org.springframework.web.client.RestTemplate;

public class GetDataTableListDLKMethod extends DLKMethod<List<DLKTableData>, GetDataTableListDLKResponseData> {
  private static final String DLK_METHOD_NAME = "getDataTableList";
  
  public GetDataTableListDLKMethod(RestTemplate restTemplate, Boolean useSsl, String ipAddress, Integer port) {
    super(restTemplate, useSsl, ipAddress, port, null);
  }
  
  protected String getMethodName() {
    return "getDataTableList";
  }
  
  Class<GetDataTableListDLKResponseData> getResponseClass() {
    return GetDataTableListDLKResponseData.class;
  }
  
  List<DLKTableData> convertResponseData(GetDataTableListDLKResponseData responseData) {
    List<DLKTableData> dlkTableDataList;
    if (responseData.getTablelist() == null) {
      dlkTableDataList = new ArrayList<>();
    } else {
      dlkTableDataList = responseData.getTablelist();
    } 
    return dlkTableDataList;
  }
}
