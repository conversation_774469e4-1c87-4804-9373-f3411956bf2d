package com.samsung.magicinfo.rc.controller.client;

import com.samsung.magicinfo.rc.common.Device;
import com.samsung.magicinfo.rc.common.batch.CheckingServerAjaxTime;
import com.samsung.magicinfo.rc.common.exception.OpenApiServiceException;
import com.samsung.magicinfo.rc.common.memory.ServerAuthorityMemory;
import com.samsung.magicinfo.rc.common.memory.ServerCaptureImageMemory;
import com.samsung.magicinfo.rc.common.memory.ServerTokenMemory;
import com.samsung.magicinfo.rc.common.queue.ServerQueue;
import com.samsung.magicinfo.rc.model.remote.RemoteKey;
import com.samsung.magicinfo.rc.service.JwtServiceImpl;
import java.io.ByteArrayInputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class RemoteController {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.controller.client.RemoteController.class);
  
  @Autowired
  JwtServiceImpl jwtManagement;
  
  @Autowired
  RemoteKey remoteKey;
  
  @Autowired
  ServerCaptureImageMemory imageHashMap;
  
  @Autowired
  ServerQueue serverQueue;
  
  @Autowired
  CheckingServerAjaxTime checkingServerAjaxTime;
  
  @Autowired
  ServerAuthorityMemory serverAuthorityMemory;
  
  @Autowired
  ServerTokenMemory serverTokenMemory;
  
  @ResponseBody
  @GetMapping({"/remote/resource"})
  public ResponseEntity<InputStreamResource> getResource(@RequestParam("deviceId") String deviceId, @RequestParam("token") String token) {
    if (!this.serverTokenMemory.checkToken(deviceId, token)) {
      log.error("[FileLoaderServlet] error deviceId and token. deviceId : " + deviceId + " token : " + token);
    } else if (this.serverQueue.containsKey(deviceId)) {
      try {
        this.checkingServerAjaxTime.inputTimeQueue(deviceId);
        byte[] imageFile = this.imageHashMap.get(deviceId);
        if (imageFile != null) {
          ByteArrayInputStream iStream = new ByteArrayInputStream(imageFile);
          return ResponseEntity.ok()
            .contentLength(imageFile.length)
            .contentType(MediaType.parseMediaType("image/jpg"))
            .body(new InputStreamResource(iStream));
        } 
      } catch (OpenApiServiceException e) {
        log.error("", (Throwable)e);
      } 
    } 
    return ResponseEntity.noContent().build();
  }
  
  @PostMapping({"/remote/control"})
  public String control(@RequestParam("deviceId") String deviceId, @RequestParam(value = "keyCode", required = false) String keyCode, @RequestParam(value = "imageSize", required = false) String imageSize, @RequestParam(value = "x", required = false) String x, @RequestParam(value = "y", required = false) String y, @RequestParam(value = "width", required = false) String width, @RequestParam(value = "height", required = false) String height, @RequestParam(value = "text", required = false) String text, @RequestParam("token") String token) {
    if (this.serverQueue.getStatusQueue(deviceId).booleanValue() && 
      this.serverTokenMemory.checkToken(deviceId, token) && hasControlAuthority(deviceId).booleanValue()) {
      String mouse = "";
      if (!StringUtils.isEmpty(x) && !StringUtils.isEmpty(y)) {
        Device deviceIdWithToken = this.serverQueue.getDeviceInfo(deviceId);
        int oriWidth = Integer.valueOf(deviceIdWithToken.getWidth()).intValue();
        int oriHeight = Integer.valueOf(deviceIdWithToken.getHeight()).intValue();
        double doubleX = Double.valueOf(x).doubleValue();
        double doubleY = Double.valueOf(y).doubleValue();
        int posX = (int)doubleX;
        int posY = (int)doubleY;
        int screenWidth = (width == null) ? 1920 : Integer.valueOf(width).intValue();
        int screenHeight = (height == null) ? 1080 : Integer.valueOf(height).intValue();
        posX *= oriWidth / screenWidth;
        posY *= oriHeight / screenHeight;
        mouse = "setPosX:" + posX + ",setPosY:" + posY;
      } 
      keyCode = this.remoteKey.getRemoteCode(keyCode);
      if (imageSize == null)
        imageSize = "null"; 
      try {
        this.serverQueue.inputQueue(deviceId, keyCode, imageSize, text, mouse);
      } catch (OpenApiServiceException e) {
        e.printStackTrace();
      } 
    } 
    return "success";
  }
  
  @GetMapping({"/remote/status/image"})
  public String getImageStatus(@RequestParam("deviceId") String deviceId, @RequestParam("imageSize") String imageSize, @RequestParam("token") String token) {
    if (this.serverTokenMemory.checkToken(deviceId, token))
      try {
        this.serverQueue.inputScreenModeQueue(deviceId, imageSize);
      } catch (Exception e) {
        log.info("cmd : imageSize,  toke fail");
      }  
    return null;
  }
  
  @GetMapping({"/remote/status"})
  public String status(@RequestParam("deviceId") String deviceId) {
    if (this.serverQueue.getStatusQueue(deviceId).booleanValue()) {
      Device device = this.serverQueue.getDeviceInfo(deviceId);
      String rtn = "result|success";
      if (device != null) {
        rtn = rtn + "|supportKeyboard:" + device.isSupportKeyboard() + "|supportMouse:" + device.isSupportMouse();
        if (device.getResolution() != null && !device.getResolution().equals("") && device.getResolution().indexOf("*") > -1)
          rtn = rtn + "|width:" + device.getWidth() + "|height:" + device.getHeight(); 
      } 
      return rtn;
    } 
    return "result|fail";
  }
  
  @PostMapping({"/remote/stop"})
  public String stop(@RequestParam("deviceId") String deviceId) {
    if (this.serverQueue.getStatusQueue(deviceId).booleanValue()) {
      try {
        this.serverQueue.inputQueue(deviceId, "1");
        log.info("inputQueue from deviceIdWithToken - " + deviceId + " stop");
      } catch (Exception e) {
        log.error("", e);
      } 
    } else if (this.serverTokenMemory.containsKey(deviceId)) {
      log.info("inputQueue from deviceIdWithToken - " + deviceId + " stop");
      this.serverTokenMemory.deleteToken(deviceId);
      this.serverAuthorityMemory.deleteAuthority(deviceId);
    } 
    return "success";
  }
  
  @PostMapping({"/remote/update"})
  public String update(@RequestParam("deviceId") String deviceId, @RequestParam("imageSize") String screensize, @RequestParam(value = "text", required = false) String text, @RequestParam(value = "keyCode", required = false) String keyCode, @RequestParam(value = "x", required = false) String x, @RequestParam(value = "y", required = false) String y, @RequestParam(value = "width", required = false) String width, @RequestParam(value = "height", required = false) String height, @RequestParam("token") String token) {
    if (this.serverQueue.getStatusQueue(deviceId).booleanValue() && 
      this.serverTokenMemory.checkToken(deviceId, token) && hasControlAuthority(deviceId).booleanValue())
      try {
        String mouse = "";
        if (!StringUtils.isEmpty(x) && !StringUtils.isEmpty(y)) {
          Device deviceIdWithToken = this.serverQueue.getDeviceInfo(deviceId);
          int oriWidth = Integer.valueOf(deviceIdWithToken.getWidth()).intValue();
          int oriHeight = Integer.valueOf(deviceIdWithToken.getHeight()).intValue();
          double doubleX = Double.valueOf(x).doubleValue();
          double doubleY = Double.valueOf(y).doubleValue();
          int posX = (int)doubleX;
          int posY = (int)doubleY;
          int screenWidth = StringUtils.isEmpty(width) ? 1920 : Integer.valueOf(width).intValue();
          int screenHeight = StringUtils.isEmpty(height) ? 1080 : Integer.valueOf(height).intValue();
          posX *= oriWidth / screenWidth;
          posY *= oriHeight / screenHeight;
          mouse = "setPosX:" + posX + ",setPosY:" + posY;
        } 
        keyCode = this.remoteKey.getRemoteCode(keyCode);
        if (screensize == null)
          screensize = "null"; 
        this.serverQueue.inputQueue(deviceId, keyCode, screensize, text, mouse);
      } catch (Exception e) {
        log.error("", e);
      }  
    return "success";
  }
  
  private Boolean hasControlAuthority(String deviceId) {
    Boolean retV = Boolean.valueOf(false);
    String authority = (String)this.serverAuthorityMemory.get(deviceId);
    if (authority == null)
      return Boolean.valueOf(false); 
    if (authority.contains("DeviceControl") == true || authority.contains("DeviceWrite") == true)
      retV = Boolean.valueOf(true); 
    return retV;
  }
}
