package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.service.LogServiceImpl;
import java.io.File;
import java.util.Date;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.filefilter.IOFileFilter;

class null implements IOFileFilter {
  public boolean accept(File file) {
    return (FileUtils.isFileNewer(file, cutoffDate) && file.getName().contains("magicinfo-webauthor"));
  }
  
  public boolean accept(File dir, String name) {
    return false;
  }
}
