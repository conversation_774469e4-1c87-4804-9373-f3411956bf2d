package com.samsung.magicinfo.rc.common.exception;

import com.samsung.magicinfo.rc.common.exception.BaseRestException;
import com.samsung.magicinfo.rc.common.exception.RestExceptionCode;
import java.util.Map;

public class RestServiceException extends BaseRestException {
  private RestExceptionCode restExceptionCode;
  
  private String[] stringFields;
  
  private Map<String, Object> errorDetails;
  
  public RestServiceException(RestExceptionCode restExceptionCode) {
    super(restExceptionCode.getCode(), restExceptionCode.getMessage());
    this.restExceptionCode = restExceptionCode;
  }
  
  public RestServiceException(RestExceptionCode restExceptionCode, String... stringFields) {
    super(restExceptionCode.getCode(), restExceptionCode.getMessage());
    this.restExceptionCode = restExceptionCode;
    this.stringFields = stringFields;
  }
  
  public RestServiceException(RestExceptionCode restExceptionCode, Map<String, Object> details) {
    super(restExceptionCode.getCode(), restExceptionCode.getMessage());
    this.restExceptionCode = restExceptionCode;
    this.errorDetails = details;
  }
  
  public RestExceptionCode getRestExceptionCode() {
    return this.restExceptionCode;
  }
  
  public String[] getInvalidField() {
    return this.stringFields;
  }
  
  public Map<String, Object> getErrorDetails() {
    return this.errorDetails;
  }
}
