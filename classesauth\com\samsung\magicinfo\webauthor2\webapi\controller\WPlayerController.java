package com.samsung.magicinfo.webauthor2.webapi.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping({"/wplayer"})
public class WPlayerController {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.webapi.controller.WPlayerController.class);
  
  @Autowired
  public WPlayerController() {
    logger.info("WPlayerController");
  }
  
  @GetMapping
  public String getPreview() {
    logger.info("GET: getPreview()");
    return goToWPlayerMainPage();
  }
  
  @ExceptionHandler({Exception.class})
  public String WPlayerControllerErrors(Exception ex, Model model) {
    logger.error(ex.getMessage());
    return "common/error";
  }
  
  private String goToWPlayerMainPage() {
    logger.info("Accessed ExEventPreview page.");
    return "wplayer";
  }
}
