package com.samsung.magicinfo.rc.common.queue;

import com.samsung.magicinfo.rc.common.batch.CheckingServerAjaxTime;
import com.samsung.magicinfo.rc.common.exception.OpenApiServiceException;
import com.samsung.magicinfo.rc.common.memory.ServerAuthorityMemory;
import com.samsung.magicinfo.rc.common.memory.ServerTokenMemory;
import com.samsung.magicinfo.rc.common.queue.ServerQueue;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ServerTimeQueue extends HashMap<String, Timestamp> {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.common.queue.ServerTimeQueue.class);
  
  @Autowired
  ServerQueue serverQueue;
  
  @Autowired
  CheckingServerAjaxTime checkingServerAjaxTime;
  
  @Autowired
  ServerTokenMemory serverTokenMemory;
  
  @Autowired
  ServerAuthorityMemory serverAuthorityMemory;
  
  static com.samsung.magicinfo.rc.common.queue.ServerTimeQueue instance;
  
  @PostConstruct
  public void init() {
    instance = new com.samsung.magicinfo.rc.common.queue.ServerTimeQueue();
  }
  
  public synchronized void timeCheck(int second) throws OpenApiServiceException {
    Calendar oCalendar = Calendar.getInstance();
    Timestamp nowTime = new Timestamp(oCalendar.getTime().getTime());
    Iterator<String> it = instance.keySet().iterator();
    try {
      while (it.hasNext()) {
        String deviceId = it.next();
        if (deviceId != null) {
          Timestamp threadTime = (Timestamp)instance.get(deviceId);
          if (threadTime != null) {
            Timestamp time = new Timestamp(threadTime.getTime() + TimeUnit.SECONDS.toMillis(second));
            if (nowTime.after(time)) {
              log.error("[ServerAjaxTime] ServerThread delete! device : " + deviceId);
              if (this.serverQueue.containsKey(deviceId)) {
                try {
                  log.error("[ServerTimeQueue] ServerThread delete! device : " + deviceId);
                  this.serverQueue.inputQueue(deviceId, "1");
                  log.error("inputQueue from device - " + deviceId + " stop");
                } catch (Exception e) {
                  e.printStackTrace();
                } 
              } else {
                log.error("do not exist!");
              } 
              if (this.serverQueue.containsKey(deviceId))
                this.serverQueue.DestroyQueue(deviceId); 
              if (this.checkingServerAjaxTime.containsKey(deviceId))
                this.checkingServerAjaxTime.stop(deviceId); 
              if (this.serverTokenMemory.containsKey(deviceId)) {
                this.serverTokenMemory.deleteToken(deviceId);
                this.serverAuthorityMemory.deleteAuthority(deviceId);
              } 
              if (instance.containsKey(deviceId))
                instance.remove(deviceId); 
            } 
            continue;
          } 
          log.error("[ServerTimeQueue] timeCheck error in while threadTime is null :");
          continue;
        } 
        log.error("[ServerTimeQueue] timeCheck error in while deviceId is null :");
      } 
    } catch (Exception e) {
      e.printStackTrace();
      log.error("[ServerTimeQueue] timeCheck error in while e :" + e.getMessage());
    } 
  }
  
  public void inputTimeQueue(String DeviceId) throws OpenApiServiceException {
    Calendar oCalendar = Calendar.getInstance();
    Timestamp time = new Timestamp(oCalendar.getTime().getTime());
    instance.put(DeviceId, time);
  }
  
  public void stop(String DeviceId) throws OpenApiServiceException {
    if (instance.containsKey(DeviceId))
      instance.remove(DeviceId); 
  }
}
