package com.samsung.magicinfo.webauthor2.repository.model;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlElement;

public class FileInfoData implements Serializable {
  private static final long serialVersionUID = 1496159811800871377L;
  
  @XmlElement(name = "file_id")
  private String fileId;
  
  @XmlElement(name = "file_name")
  private String fileName;
  
  @XmlElement(name = "file_size")
  private long size;
  
  @XmlElement(name = "file_path")
  private String filePath;
  
  @XmlElement(name = "hash_code")
  private String fileHash;
  
  @XmlElement(name = "is_upload_completed")
  private String isUploadCompleted;
  
  @XmlElement(name = "file_type")
  private String fileType;
  
  @XmlElement(name = "creator_id")
  private String creatorId;
  
  @XmlElement(name = "create_date")
  private String createDate;
  
  @XmlElement(name = "is_streaming")
  private String isStreaming;
  
  public FileInfoData() {}
  
  public FileInfoData(String fileId, String fileName, long size, String filePath, String fileHash, String isUploadCompleted, String fileType, String creatorId, String createDate, String isStreaming) {
    this.fileId = fileId;
    this.fileName = fileName;
    this.size = size;
    this.filePath = filePath;
    this.fileHash = fileHash;
    this.isUploadCompleted = isUploadCompleted;
    this.fileType = fileType;
    this.creatorId = creatorId;
    this.createDate = createDate;
    this.isStreaming = isStreaming;
  }
  
  public String getFileId() {
    return this.fileId;
  }
  
  public String getFileName() {
    return this.fileName;
  }
  
  public long getSize() {
    return this.size;
  }
  
  public String getFilePath() {
    return this.filePath;
  }
  
  public String getFileHash() {
    return this.fileHash;
  }
  
  public String getIsUploadCompleted() {
    return this.isUploadCompleted;
  }
  
  public String getFileType() {
    return this.fileType;
  }
  
  public String getCreatorId() {
    return this.creatorId;
  }
  
  public String getCreateDate() {
    return this.createDate;
  }
  
  public String getIsStreaming() {
    return this.isStreaming;
  }
}
