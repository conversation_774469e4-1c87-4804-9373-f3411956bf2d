package com.samsung.magicinfo.rc.common.memory;

import java.util.HashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class ServerTokenMemory extends HashMap<String, String> {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.common.memory.ServerTokenMemory.class);
  
  private static final long serialVersionUID = 1L;
  
  static int SUCCESS = 0;
  
  static int DUPLICATION = 100;
  
  static int ERROR = 900;
  
  public boolean checkToken(String deviceId, String token) {
    boolean rtn = false;
    String temp_token = null;
    if (token == null || token.equals(""))
      return false; 
    if (containsKey(deviceId)) {
      temp_token = (String)get(deviceId);
      if (temp_token.equals(token)) {
        rtn = true;
      } else {
        rtn = false;
      } 
    } 
    return rtn;
  }
  
  public boolean deleteToken(String deviceId) {
    boolean rtn = false;
    if (containsKey(deviceId)) {
      remove(deviceId);
      rtn = true;
    } 
    return rtn;
  }
  
  public int startToken(String deviceId, String token) {
    int rtn = ERROR;
    try {
      log.error("[RC] input token! deviceId : " + deviceId + " token : " + token);
      put((K)deviceId, (V)token);
      rtn = SUCCESS;
    } catch (Exception e) {
      log.error("", e);
      log.error("[RC] input token! Exception!");
      rtn = ERROR;
    } 
    return rtn;
  }
}
