package com.samsung.magicinfo.webauthor2.exception.service;

import com.samsung.magicinfo.webauthor2.exception.WebAuthorAbstractException;

public final class WebContentValidationException extends WebAuthorAbstractException {
  public static final String NO_INDEX_HTML = "Zip file doesn't contain startup page";
  
  public static final String CONTAINS_EXECUTABLE = "Zip file contains executable file";
  
  public static final String EXCEED_SIZE_LIMITS = "Zip file size exceeds the limits";
  
  private static final long serialVersionUID = 9137349358385451981L;
  
  public WebContentValidationException(int errorCode, String message) {
    super(errorCode, message);
  }
}
