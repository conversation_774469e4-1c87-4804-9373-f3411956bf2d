package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.google.common.base.Optional;
import com.samsung.magicinfo.webauthor2.exception.WebAuthorAbstractException;
import com.samsung.magicinfo.webauthor2.exception.repository.ContentNotFoundException;
import com.samsung.magicinfo.webauthor2.exception.service.DownloaderException;
import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.CidMappingResponse;
import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.ContentSaveElements;
import com.samsung.magicinfo.webauthor2.model.FileItemsDescriptor;
import com.samsung.magicinfo.webauthor2.model.UploadResponse;
import com.samsung.magicinfo.webauthor2.model.VerificationResponse;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.service.FileHashService;
import com.samsung.magicinfo.webauthor2.service.PluginDownloadService;
import com.samsung.magicinfo.webauthor2.service.upload.ContentMIPUploadService;
import com.samsung.magicinfo.webauthor2.service.upload.PluginUploadService;
import com.samsung.magicinfo.webauthor2.webapi.assembler.ContentResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.resource.ContentResource;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.PagedResourcesAssembler;
import org.springframework.hateoas.PagedResources;
import org.springframework.hateoas.ResourceAssembler;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

@RestController
@Scope("session")
@RequestMapping({"/plugin"})
public class PluginController {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.webapi.controller.PluginController.class);
  
  private final PluginUploadService pluginUploadService;
  
  private FileHashService fileHashService;
  
  private ContentMIPUploadService uploadService;
  
  private ContentSaveElements contentSaveElements;
  
  private final PluginDownloadService pluginDownloadService;
  
  private final ContentResourceAssembler contentResourceAssembler;
  
  public static final String PREVIEW_RESOURCE_MAPPED_NAME = "/preview";
  
  @Autowired
  public PluginController(PluginUploadService pluginUploadService, FileHashService fileHashService, ContentMIPUploadService uploadService, ContentSaveElements contentSaveElements, PluginDownloadService pluginDownloadService, ContentResourceAssembler contentResourceAssembler) {
    this.pluginUploadService = pluginUploadService;
    this.fileHashService = fileHashService;
    this.uploadService = uploadService;
    this.contentSaveElements = contentSaveElements;
    this.pluginDownloadService = pluginDownloadService;
    this.contentResourceAssembler = contentResourceAssembler;
  }
  
  @GetMapping
  public HttpEntity<PagedResources<ContentResource>> getPluginContentsWithPaging(@PageableDefault(page = 0, size = 50) Pageable pageable, @RequestParam(name = "pluginType", required = false) String pluginType, PagedResourcesAssembler<Content> assembler) {
    Page<Content> page = this.pluginDownloadService.getPluginResources(pluginType, pageable);
    PagedResources<ContentResource> contentResources = assembler.toResource(page, (ResourceAssembler)this.contentResourceAssembler);
    return (HttpEntity<PagedResources<ContentResource>>)ResponseEntity.ok(contentResources);
  }
  
  @PostMapping({"/prerequisite"})
  public HttpEntity<CidMappingResponse> initializeUploadProcess(@RequestParam String contentName, @RequestParam String startupPage, @RequestParam String contentId, @RequestParam(value = "thumbnail", required = false) MultipartFile thumbnail) throws IOException {
    Optional<MultipartFile> thumbnailFile = Optional.fromNullable(thumbnail);
    return (HttpEntity<CidMappingResponse>)ResponseEntity.ok(new CidMappingResponse(this.pluginUploadService.initializeUploadProcess(thumbnailFile, contentName, startupPage, contentId)));
  }
  
  @PostMapping(value = {"/file"}, consumes = {"multipart/form-data"})
  public HttpEntity<MediaSource> uploadfile(@RequestParam(required = true) String contentId, @RequestParam(required = true) String path, @RequestParam("upload") MultipartFile file) throws IOException {
    return (HttpEntity<MediaSource>)ResponseEntity.ok(this.pluginUploadService.storeSupportFileItem(contentId, path, file));
  }
  
  @PostMapping({"/fileItems"})
  public HttpEntity<List<MediaSource>> requestMediaSource(@RequestBody FileItemsDescriptor fileItemsDescriptor, HttpServletRequest request) throws IOException {
    List<MediaSource> updatedMediaSources = this.pluginUploadService.getUpdatedMediaSources(fileItemsDescriptor);
    return (HttpEntity<List<MediaSource>>)ResponseEntity.ok(updatedMediaSources);
  }
  
  @PostMapping({"/submit"})
  public HttpEntity<UploadResponse> xmlUpload(@RequestParam String lfdXml) throws UploaderException {
    this.fileHashService.fileHashRefresh(lfdXml);
    String savedProjectContentId = this.uploadService.upload(this.contentSaveElements);
    UploadResponse response = new UploadResponse(200, savedProjectContentId);
    return (HttpEntity<UploadResponse>)ResponseEntity.ok(response);
  }
  
  @GetMapping({"/preview"})
  public HttpEntity<String> getPreviewUrl(@RequestParam(name = "contentId", required = true) String contentId, @RequestParam(name = "startupPage", required = true) String startupPage) throws DownloaderException {
    ServletUriComponentsBuilder builder = ServletUriComponentsBuilder.fromCurrentServletMapping();
    builder.path("/preview/" + contentId + "/" + startupPage);
    this.pluginDownloadService.downloadToRepository(contentId);
    HttpHeaders responseHeaders = new HttpHeaders();
    responseHeaders.setContentType(MediaType.TEXT_PLAIN);
    return (HttpEntity<String>)new ResponseEntity(builder.build().toString(), (MultiValueMap)responseHeaders, HttpStatus.OK);
  }
  
  @GetMapping({"/validateName"})
  public HttpEntity<VerificationResponse> isNameValid(@RequestParam(name = "name", required = true) String name, @RequestParam(name = "nameBeforeEdit", required = true) String nameBeforeEdit) {
    return (HttpEntity<VerificationResponse>)ResponseEntity.ok(this.pluginDownloadService.isNameValid(name, nameBeforeEdit));
  }
  
  @DeleteMapping
  public HttpEntity<String> deletePlugin(@RequestParam String contentId) {
    return (HttpEntity<String>)ResponseEntity.ok(this.pluginUploadService.deletePlugin(contentId));
  }
  
  @GetMapping({"/zip"})
  public void getCompressedPlugin(@RequestParam(name = "contentId") String contentId, HttpServletResponse response) throws IOException {
    Path pluginZip = this.pluginDownloadService.getPluginZip(contentId);
    writeZipFileToResponse(response, pluginZip);
  }
  
  private void writeZipFileToResponse(HttpServletResponse response, Path pluginZip) throws IOException {
    response.setContentType("application/zip");
    response.setHeader("Content-disposition", "attachment; filename=" + pluginZip.getFileName().toString());
    ServletOutputStream servletOutputStream = response.getOutputStream();
    try (InputStream inputStream = Files.newInputStream(pluginZip, new java.nio.file.OpenOption[0])) {
      IOUtils.copy(inputStream, (OutputStream)servletOutputStream);
      response.flushBuffer();
    } finally {
      deleteDirectoryWithZipFile(pluginZip);
    } 
  }
  
  private void deleteDirectoryWithZipFile(Path pluginZip) {
    Path parent = pluginZip.getParent();
    if (parent != null && Files.exists(parent, new java.nio.file.LinkOption[0]))
      FileUtils.deleteQuietly(parent.toFile()); 
  }
  
  @ExceptionHandler({WebAuthorAbstractException.class})
  public ResponseEntity<UploadResponse> webAuthorExceptionHandler(WebAuthorAbstractException ex) {
    logger.error(ex.getMessage(), (Throwable)ex);
    UploadResponse uploadResponse = new UploadResponse(ex.getErrorCode(), ex.getMessage());
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(uploadResponse);
  }
  
  @ExceptionHandler({ContentNotFoundException.class})
  public ResponseEntity<UploadResponse> contentNotFoundException(ContentNotFoundException ex) {
    logger.error(ex.getMessage());
    UploadResponse uploadResponse = new UploadResponse(404, ex.getMessage());
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(uploadResponse);
  }
  
  @ExceptionHandler({Exception.class})
  public ResponseEntity<UploadResponse> generalExceptionHandler(Exception ex) {
    logger.error(ex.getMessage(), ex);
    UploadResponse uploadResponse = new UploadResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), ex.getMessage());
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(uploadResponse);
  }
}
