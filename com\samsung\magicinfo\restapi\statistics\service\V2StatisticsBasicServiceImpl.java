package com.samsung.magicinfo.restapi.statistics.service;

import com.samsung.common.export.PdfBuilder;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.advertisement.dao.FaceAudienceBasedDao;
import com.samsung.magicinfo.framework.common.ListEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceConnHistoryInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceConnHistoryInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.monitoring.entity.CurrentPlayingEntity;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfo;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfoImpl;
import com.samsung.magicinfo.framework.statistics.dao.DeviceApproveStatisticsDao;
import com.samsung.magicinfo.framework.statistics.dao.ScheduleStatisticsDao;
import com.samsung.magicinfo.framework.statistics.dao.StatisticsDataDao;
import com.samsung.magicinfo.framework.statistics.entity.ApprovedDeviceTableEntity;
import com.samsung.magicinfo.framework.statistics.entity.DeviceConnHistoryEntity;
import com.samsung.magicinfo.framework.statistics.entity.PeriodApprovedDeviceEntity;
import com.samsung.magicinfo.framework.statistics.entity.connection.DeviceGroupConnectionEntity;
import com.samsung.magicinfo.framework.statistics.entity.content.ContentTypeEntity;
import com.samsung.magicinfo.framework.statistics.entity.schedule.ScheduleGroupEntity;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.statistics.model.V2StatisticsBasicParam;
import com.samsung.magicinfo.restapi.statistics.model.V2StatisticsDeviceResource;
import com.samsung.magicinfo.restapi.statistics.model.V2StatisticsSummaryResource;
import com.samsung.magicinfo.restapi.statistics.model.V2StatisticsSummaryStatusData;
import com.samsung.magicinfo.service.statistics.DeviceApproveStatisticsService;
import com.samsung.magicinfo.service.statistics.DeviceConnHistoryStatisticsService;
import com.samsung.magicinfo.service.statistics.DeviceConnectionStatisticsService;
import com.samsung.magicinfo.service.statistics.DeviceStatisticsDownloadService;
import com.samsung.magicinfo.service.statistics.ErrorOccurrenceStatisticsService;
import edu.emory.mathcs.backport.java.util.Collections;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.apache.logging.log4j.Logger;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.ModelAndView;

@Service("V2StatisticsBasicService")
@Transactional
public class V2StatisticsBasicServiceImpl implements V2StatisticsBasicService {
   protected Logger logger = LoggingManagerV2.getLogger(this.getClass());
   private DeviceConnectionStatisticsService device_connection_service = null;
   private DeviceStatisticsDownloadService downloadService = null;
   private DeviceConnHistoryStatisticsService device_conn_history_service = null;
   private ErrorOccurrenceStatisticsService device_error_service = null;
   private DeviceApproveStatisticsService device_approve_service = null;

   public V2StatisticsBasicServiceImpl() {
      super();
   }

   public void setDownloadService(DeviceStatisticsDownloadService downloadService) {
      this.downloadService = downloadService;
   }

   public void setDeviceConnectionService(DeviceConnectionStatisticsService device_connection_service) {
      this.device_connection_service = device_connection_service;
   }

   public void setDeviceConnHistoryStatisticsService(DeviceConnHistoryStatisticsService device_conn_history_service) {
      this.device_conn_history_service = device_conn_history_service;
   }

   public void setDeviceErrorService(ErrorOccurrenceStatisticsService device_error_service) {
      this.device_error_service = device_error_service;
   }

   public void setDeviceApproveService(DeviceApproveStatisticsService device_approve_service) {
      this.device_approve_service = device_approve_service;
   }

   @PreAuthorize("hasAnyAuthority('Statistics Manage Authority')")
   public List integratedStatisticsDevice(V2StatisticsBasicParam body) throws Exception {
      String data = body.getData();
      String startDate = body.getStartDate();
      String endDate = body.getEndDate();
      List groupIds = body.getGroupIds();
      data = StrUtils.nvl(data).equals("") ? "INIT" : data;
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      DeviceApproveStatisticsDao deviceDao = new DeviceApproveStatisticsDao();
      UserContainer container = SecurityUtils.getUserContainer();
      Long root_group_id = container.getUser().getRoot_group_id();
      int org_id = Integer.parseInt(root_group_id.toString());
      List records = new ArrayList();
      ListEntity list = new ListEntity();
      int panel_on_count;
      boolean panel_off_count;
      int total;
      List devList;
      V2StatisticsDeviceResource resource;
      MonitoringManager motMgr;
      boolean is_connected;
      int panel_off_count;
      if (data.equalsIgnoreCase("connectionStatus")) {
         int connected_count = 0;
         panel_on_count = 0;
         panel_off_count = false;
         total = 0;
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         String organName = userGroupInfo.getGroupNameByGroupId(root_group_id);
         devList = null;
         resource = new V2StatisticsDeviceResource();
         if (organName.equalsIgnoreCase("ROOT")) {
            devList = deviceGroupDao.getChildDeviceIdList(org_id, true);
         } else {
            devList = deviceGroupDao.getChildDeviceIdListByOrganName(organName);
         }

         panel_off_count = devList.size();
         motMgr = MonitoringManagerImpl.getInstance();

         for(int i = 0; i < panel_off_count; ++i) {
            is_connected = motMgr.isConnected((String)devList.get(i));
            boolean is_approved = motMgr.getApprovalStatus((String)devList.get(i));
            this.logger.debug("TEST " + is_approved);
            if (is_approved) {
               if (is_connected) {
                  ++connected_count;
               } else {
                  ++panel_on_count;
               }

               ++total;
            }
         }

         resource.setConnection(connected_count);
         resource.setDisconnection(panel_on_count);
         records.add(resource);
      } else {
         V2StatisticsDeviceResource resource;
         V2StatisticsSummaryStatusData pannelOn;
         if (data.equalsIgnoreCase("panelStatusTable")) {
            int connected_count = false;
            int panel_on_count = false;
            panel_off_count = false;
            int list_size = false;
            resource = new V2StatisticsDeviceResource();
            UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
            String organName = userGroupInfo.getGroupNameByGroupId(root_group_id);
            resource = null;
            List device_id_list;
            if (organName.equalsIgnoreCase("ROOT")) {
               device_id_list = deviceGroupDao.getChildDeviceIdList(org_id, true);
            } else {
               device_id_list = deviceGroupDao.getChildDeviceIdListByOrganName(organName);
            }

            total = device_id_list.size();
            motMgr = MonitoringManagerImpl.getInstance();
            panel_on_count = 0;
            panel_off_count = 0;

            for(int i = 0; i < total; ++i) {
               long is_panel_on = 0L;
               is_connected = motMgr.isConnected((String)device_id_list.get(i));
               if (is_connected) {
                  CurrentPlayingEntity temp = motMgr.getPlayingContent((String)device_id_list.get(i));
                  if (temp == null) {
                     ++panel_off_count;
                  } else {
                     is_panel_on = temp.getPanelStatus();
                     if (is_panel_on == 0L) {
                        ++panel_on_count;
                     } else {
                        ++panel_off_count;
                     }
                  }
               }
            }

            pannelOn = new V2StatisticsSummaryStatusData();
            V2StatisticsSummaryStatusData pannelOff = new V2StatisticsSummaryStatusData();
            V2StatisticsSummaryStatusData serverDisconnectedData = new V2StatisticsSummaryStatusData();
            pannelOn.setColor("#c51b8a");
            pannelOn.setHighlight("#f03b20");
            pannelOn.setValue(Integer.toString(panel_on_count));
            pannelOn.setLabel("Panel On Devices");
            resource.setPannelOn(pannelOn);
            pannelOff.setColor("#fa9fb5");
            pannelOff.setHighlight("#f03b20");
            pannelOff.setValue(Integer.toString(panel_off_count));
            pannelOff.setLabel("Panel Off Devices");
            resource.setPannelOff(pannelOff);
            serverDisconnectedData.setColor("#fde0dd");
            serverDisconnectedData.setHighlight("#f03b20");
            serverDisconnectedData.setValue(Integer.toString(total - panel_on_count - panel_off_count));
            serverDisconnectedData.setLabel("Disconnected");
            resource.setServerDisconnectedData(serverDisconnectedData);
            int var10000 = panel_on_count + panel_off_count;
            records.add(resource);
         } else {
            Long organ_device_list;
            Long groupId;
            int length;
            int i;
            V2StatisticsDeviceResource resource;
            if (data.equalsIgnoreCase("connectionHistoryTable")) {
               DeviceConnHistoryInfo device_dao = DeviceConnHistoryInfoImpl.getInstance();
               organ_device_list = container.getUser().getRoot_group_id();
               groupId = container.getUser().getGroup_id();
               List result_list = null;
               String groupIdsStr = "";
               if (groupIds != null && groupIds.size() > 0) {
                  for(length = 0; length < groupIds.size(); ++length) {
                     if (length != 0) {
                        groupIdsStr = groupIdsStr + ",";
                     }

                     groupIdsStr = groupIdsStr + ((Long)groupIds.get(length)).toString();
                  }
               }

               if (organ_device_list == 0L) {
                  if (groupIdsStr.equals("")) {
                     result_list = device_dao.getPeriodDeviceConnHistoryDataAllDevices(startDate, endDate);
                  } else {
                     result_list = device_dao.getPeriodDeviceConnHistoryData(startDate, endDate, this.getDeviceIdListbyGroupIds(groupIdsStr), 1000L);
                  }
               } else if (groupIdsStr.equals("")) {
                  DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
                  devList = null;
                  devList = deviceGroupInfo.getChildDeviceIdList(groupId.intValue(), true);
                  result_list = device_dao.getPeriodDeviceConnHistoryData(startDate, endDate, (String[])devList.toArray(new String[devList.size()]), 1000L);
               } else {
                  result_list = device_dao.getPeriodDeviceConnHistoryData(startDate, endDate, this.getDeviceIdListbyGroupIds(groupIdsStr), 1000L);
               }

               length = result_list.size();

               for(i = 0; i < length; ++i) {
                  String disconnected = StrUtils.nvl(((DeviceConnHistoryEntity)result_list.get(i)).getDisconnected_time_string()).equals("") ? "" : ((DeviceConnHistoryEntity)result_list.get(i)).getDisconnected_time_string();
                  resource = new V2StatisticsDeviceResource();
                  resource.setMacAdd(((DeviceConnHistoryEntity)result_list.get(i)).getDevice_id());
                  resource.setDeviceName(((DeviceConnHistoryEntity)result_list.get(i)).getDevice_name());
                  resource.setDeviceGroup(((DeviceConnHistoryEntity)result_list.get(i)).getGroup_name());
                  resource.setConnected(((DeviceConnHistoryEntity)result_list.get(i)).getConnected_time_string());
                  resource.setDisconnected(disconnected);
                  resource.setDuration(((DeviceConnHistoryEntity)result_list.get(i)).getDuration_string());
                  resource.setBootReason(((DeviceConnHistoryEntity)result_list.get(i)).getBoot_reason());
                  resource.setShutdownReason(((DeviceConnHistoryEntity)result_list.get(i)).getShutdown_reason());
                  resource.setBootTime(((DeviceConnHistoryEntity)result_list.get(i)).getBoot_time_string());
                  resource.setShutdownTime(((DeviceConnHistoryEntity)result_list.get(i)).getShutdown_time_string());
                  records.add(resource);
               }
            } else {
               int i;
               if (data.equalsIgnoreCase("approvalStatusTable")) {
                  if (!container.getUser().getRole_name().equals("Server Administrator")) {
                     throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
                  }

                  Long groupId = container.getUser().getGroup_id();
                  organ_device_list = null;
                  List organ_device_list = deviceGroupDao.V2GetChildDeviceIdList(groupId.intValue(), true);
                  new ArrayList();
                  new ApprovedDeviceTableEntity();
                  resource = new V2StatisticsDeviceResource();
                  length = organ_device_list.size();
                  i = deviceDao.getUnApprovedDeiveListCount();
                  i = length + i;
                  list.setTotalRecords(i);
                  resource.setConnection(i - i);
                  resource.setDisconnection(i);
                  V2StatisticsSummaryStatusData approvedData = new V2StatisticsSummaryStatusData();
                  pannelOn = new V2StatisticsSummaryStatusData();
                  approvedData.setColor("#78c679");
                  approvedData.setHighlight("#f03b20");
                  approvedData.setValue(Integer.toString(length));
                  approvedData.setLabel("Approved Devices");
                  resource.setApprovedData(approvedData);
                  pannelOn.setColor("#c2e699");
                  pannelOn.setHighlight("#f03b20");
                  pannelOn.setValue(Integer.toString(i));
                  pannelOn.setLabel("Unapproved Devices");
                  resource.setUnapprovedData(pannelOn);
                  records.add(resource);
               } else if (data.equalsIgnoreCase("requestDateTable")) {
                  String group_name = container.getUser().getGroup_name();
                  organ_device_list = container.getUser().getGroup_id();
                  groupId = null;
                  List result_list = deviceDao.getPeriodApprovedDeviceList(startDate, endDate, group_name, organ_device_list);
                  total = 0;
                  int length = result_list.size();
                  String[] createDates = new String[length];
                  int[] device_number = new int[length];

                  for(i = 0; i < length; ++i) {
                     resource = new V2StatisticsDeviceResource();
                     String tempDate = StrUtils.getDiffMin(Timestamp.valueOf(((PeriodApprovedDeviceEntity)result_list.get(i)).getCreate_date() + " 00:00:00"), false, (Locale)null);
                     tempDate = tempDate.replace(" 00:00", "");
                     resource.setDate(tempDate);
                     resource.setCount(Integer.toString(((PeriodApprovedDeviceEntity)result_list.get(i)).getDevice_number()));
                     total += ((PeriodApprovedDeviceEntity)result_list.get(i)).getDevice_number();
                     records.add(resource);
                  }

                  for(i = 0; i < length; ++i) {
                     String tempDate = "\"" + StrUtils.getDiffMin(Timestamp.valueOf(((PeriodApprovedDeviceEntity)result_list.get(i)).getCreate_date() + " 00:00:00"), false, (Locale)null) + "\"";
                     tempDate = tempDate.replace(" 00:00", "");
                     createDates[i] = tempDate;
                     device_number[i] = ((PeriodApprovedDeviceEntity)result_list.get(i)).getDevice_number();
                  }

                  resource = new V2StatisticsDeviceResource();
                  resource.setData(device_number);
                  resource.setLabel(createDates);
                  records.add(resource);
               }
            }
         }
      }

      return records;
   }

   @PreAuthorize("hasAnyAuthority('Statistics Manage Authority')")
   public V2StatisticsSummaryResource integratedStatisticsSummary(String data) throws SQLException {
      data = StrUtils.nvl(data).equals("") ? "INIT" : data;
      V2StatisticsSummaryResource resource = new V2StatisticsSummaryResource();
      UserContainer container = SecurityUtils.getUserContainer();
      Long root_group_id = container.getUser().getRoot_group_id();
      int org_id = Integer.parseInt(root_group_id.toString());
      MonitoringManagerInfo monitoringManager = MonitoringManagerInfoImpl.getInstance();
      String organization = container.getUser().getOrganization();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      boolean unapprovedDevice;
      boolean totalCount;
      boolean approvedDevice;
      List groupList;
      V2StatisticsSummaryStatusData disconnectedData;
      int connectedCount;
      int disconnectedCount;
      int totalCount;
      V2StatisticsSummaryStatusData connectedData;
      if (data.equalsIgnoreCase("connectionStatus")) {
         unapprovedDevice = false;
         totalCount = false;
         approvedDevice = false;

         try {
            groupList = DeviceUtils.getOrgByOrgManagerUserId(userContainer.getUser().getRoot_group_id(), userContainer.getUser().getUser_id());
            if (groupList == null) {
               groupList = Collections.singletonList(userContainer.getUser().getOrganization());
            }

            connectedCount = monitoringManager.getConnectionFromDBInfo(groupList, (String)null, (Float)null);
            totalCount = monitoringManager.getDeviceCountByOrgList(groupList);
            disconnectedCount = totalCount - connectedCount;
         } catch (Exception var21) {
            this.logger.error("[MagicInfo_StatisticsAjaxController] fail to get connectionInfo e : " + var21.getMessage());
            connectedCount = 0;
            disconnectedCount = 0;
            approvedDevice = false;
         }

         connectedData = new V2StatisticsSummaryStatusData();
         disconnectedData = new V2StatisticsSummaryStatusData();
         connectedData.setColor("#2b8cbe");
         connectedData.setHighlight("#f03b20");
         connectedData.setValue(Integer.toString(connectedCount));
         connectedData.setLabel("Connected");
         resource.setConnectedData(connectedData);
         disconnectedData.setColor("#a6bddb");
         disconnectedData.setHighlight("#f03b20");
         disconnectedData.setValue(Integer.toString(disconnectedCount));
         disconnectedData.setLabel("Disconnected");
         resource.setDisconnectedData(disconnectedData);
      } else if (data.equalsIgnoreCase("approvalStatus")) {
         unapprovedDevice = false;
         totalCount = false;
         approvedDevice = false;
         connectedCount = monitoringManager.getNonApprovalCount((String)null);
         disconnectedCount = monitoringManager.getDeviceCount(organization, (String)null) + connectedCount;
         totalCount = disconnectedCount - connectedCount;
         connectedData = new V2StatisticsSummaryStatusData();
         disconnectedData = new V2StatisticsSummaryStatusData();
         disconnectedData.setColor("#addd8e");
         disconnectedData.setHighlight("#f03b20");
         disconnectedData.setValue(Integer.toString(connectedCount));
         disconnectedData.setLabel("Unapproved Devices");
         resource.setUnapprovedData(disconnectedData);
         connectedData.setColor("#31a354");
         connectedData.setHighlight("#f03b20");
         connectedData.setValue(Integer.toString(totalCount));
         connectedData.setLabel("Approved Devices");
         resource.setApprovedData(connectedData);
      } else if (data.equalsIgnoreCase("registeredContents")) {
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         StatisticsDataDao statisticsDataDao = new StatisticsDataDao();
         List result_list = null;
         groupList = null;
         if ((long)org_id == 0L) {
            groupList = userGroupInfo.getUserManageGroupListByUserId(SecurityUtils.getLoginUserId());
            if (groupList != null && groupList.size() > 0) {
               result_list = statisticsDataDao.V2GetBeanListContentTypeCheckOrganId(groupList);
            } else {
               result_list = statisticsDataDao.V2GetBeanListContentType();
            }
         } else {
            List groupList = new ArrayList();
            groupList.add(userGroupInfo.getGroupById((long)org_id));
            result_list = statisticsDataDao.getBeanListContentTypeCheckOrganId(groupList);
         }

         if (result_list != null) {
            int total_count = 0;
            int size = result_list.size();
            String[] mediaType = new String[size];
            int[] mediaCount = new int[size];

            for(int index = 0; index < size; ++index) {
               String type = ((ContentTypeEntity)result_list.get(index)).getMedia_type();
               int count = ((ContentTypeEntity)result_list.get(index)).getType_count();
               String typeName = "";
               if (type.equals("STRM")) {
                  mediaType[index] = "Streaming";
               } else {
                  mediaType[index] = type;
               }

               if (count > 0) {
                  total_count += count;
                  mediaCount[index] = count;
               }
            }

            resource.setType(mediaType);
            resource.setCount(mediaCount);
         }
      }

      return resource;
   }

   String getDOW(int dow) {
      String dowToDay = "";
      switch(dow) {
      case 0:
         dowToDay = "SUN";
         break;
      case 1:
         dowToDay = "MON";
         break;
      case 2:
         dowToDay = "TUE";
         break;
      case 3:
         dowToDay = "WED";
         break;
      case 4:
         dowToDay = "THU";
         break;
      case 5:
         dowToDay = "FRI";
         break;
      case 6:
         dowToDay = "SAT";
      }

      return dowToDay;
   }

   public List returnAudienceEntity(String amsAudienceBasedShow, String unit, String groupIds, String type) throws Exception {
      FaceAudienceBasedDao FaceAudienceBasedDao = new FaceAudienceBasedDao();
      List audienceBasedEntity = null;
      if (amsAudienceBasedShow.equalsIgnoreCase("yesterday")) {
         audienceBasedEntity = FaceAudienceBasedDao.getYesterdayListBy(this.getDeviceIdListbyGroupIds(groupIds), unit, type);
      } else if (!amsAudienceBasedShow.equalsIgnoreCase("this_week") && !amsAudienceBasedShow.equalsIgnoreCase("last_week")) {
         if (amsAudienceBasedShow.equalsIgnoreCase("this_month")) {
            if (unit.equalsIgnoreCase("DAY")) {
               audienceBasedEntity = FaceAudienceBasedDao.getMonthListByDay(this.getDeviceIdListbyGroupIds(groupIds), true, type);
            } else if (unit.equalsIgnoreCase("HOUR")) {
               audienceBasedEntity = FaceAudienceBasedDao.getMonthListByHour(this.getDeviceIdListbyGroupIds(groupIds), true, type);
            } else if (unit.equalsIgnoreCase("WEEK")) {
               audienceBasedEntity = FaceAudienceBasedDao.getMonthListByDay(this.getDeviceIdListbyGroupIds(groupIds), true, type);
            } else {
               audienceBasedEntity = FaceAudienceBasedDao.getMonthListByMonth(this.getDeviceIdListbyGroupIds(groupIds), true, type);
            }
         } else if (amsAudienceBasedShow.equalsIgnoreCase("last_month")) {
            if (unit.equalsIgnoreCase("DAY")) {
               audienceBasedEntity = FaceAudienceBasedDao.getMonthListByDay(this.getDeviceIdListbyGroupIds(groupIds), false, type);
            } else if (unit.equalsIgnoreCase("HOUR")) {
               audienceBasedEntity = FaceAudienceBasedDao.getMonthListByHour(this.getDeviceIdListbyGroupIds(groupIds), false, type);
            } else if (unit.equalsIgnoreCase("WEEK")) {
               audienceBasedEntity = FaceAudienceBasedDao.getMonthListByDay(this.getDeviceIdListbyGroupIds(groupIds), false, type);
            } else {
               audienceBasedEntity = FaceAudienceBasedDao.getMonthListByMonth(this.getDeviceIdListbyGroupIds(groupIds), false, type);
            }
         }
      } else {
         audienceBasedEntity = FaceAudienceBasedDao.getWeekListBy(this.getDeviceIdListbyGroupIds(groupIds), unit, amsAudienceBasedShow, type);
      }

      return audienceBasedEntity;
   }

   private String[] getDeviceIdListbyGroupIds(String groupIds) throws Exception {
      String[] groupIdList = groupIds.split(",");
      List allDeviceIdList = new ArrayList();
      List tmpDeviceIdList = null;
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      int size = groupIdList.length;

      for(int i = 0; i < size; ++i) {
         try {
            tmpDeviceIdList = deviceInfo.getDeviceIdListByGroup(Integer.valueOf(groupIdList[i]));
         } catch (NumberFormatException var9) {
            this.logger.error("", var9);
         } catch (SQLException var10) {
            this.logger.error("", var10);
         }

         if (tmpDeviceIdList != null) {
            for(int j = 0; j < tmpDeviceIdList.size(); ++j) {
               allDeviceIdList.add((String)((Map)tmpDeviceIdList.get(j)).get("device_id"));
            }
         }
      }

      return (String[])allDeviceIdList.toArray(new String[allDeviceIdList.size()]);
   }

   public List getCurrentStatusData(HttpServletRequest request) {
      ScheduleStatisticsDao schedule_dao = new ScheduleStatisticsDao();
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      UserContainer container = SecurityUtils.getUserContainer();
      Long root_group_id = container.getUser().getRoot_group_id();
      int org_id = Integer.parseInt(root_group_id.toString());
      List result_list = new ArrayList();
      int cnt = 0;
      int schedule_assigned = 0;
      int schedule_unassigned = 0;

      try {
         List group_list = deviceGroupDao.getChildGroupIdList(org_id, true);
         int group_list_cnt = group_list.size();
         ScheduleGroupEntity element = new ScheduleGroupEntity();

         for(int index = 0; index < group_list_cnt; ++index) {
            int group_id = ((Long)group_list.get(index)).intValue();
            DeviceGroup temp = deviceGroupDao.getGroup(group_id);
            if (temp != null && temp.getP_group_id() != 0L) {
               List result = schedule_dao.getNonDefaultGroupScheduleData(group_id);
               if (result.size() == 0) {
                  ++schedule_unassigned;
               } else {
                  ++schedule_assigned;
               }

               ++cnt;
            }
         }

         element.setTotal_group_number(cnt);
         element.setSchedule_include(schedule_assigned);
         element.setSchedule_not_include(schedule_unassigned);
         result_list.add(0, element);
      } catch (Exception var18) {
         this.logger.error("", var18);
      }

      return result_list;
   }

   @PreAuthorize("hasAnyAuthority('Statistics Manage Authority')")
   public ModelAndView deviceExport(String exportType, @Valid V2StatisticsBasicParam body, HttpServletRequest request, HttpServletResponse response, String localeData) throws Exception {
      String data = body.getData();
      String startDate = body.getStartDate();
      String endDate = body.getEndDate();
      List groupIds = new ArrayList();
      if (body.getGroupIds() != null) {
         groupIds = body.getGroupIds();
      }

      String groupIdsStr = "";

      for(int i = 0; i < ((List)groupIds).size(); ++i) {
         if (i != 0) {
            groupIdsStr = groupIdsStr + ",";
         }

         groupIdsStr = groupIdsStr + ((Long)((List)groupIds).get(i)).toString();
      }

      if (StrUtils.nvl(localeData).equals("")) {
         String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
         if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
            localeData = userLocale;
         } else {
            localeData = "en";
         }
      }

      Locale locale = new Locale(localeData);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      String MessageKey1;
      String MessageKey2;
      String request_date;
      String device_number;
      String fileName;
      String sheetName;
      String device_id;
      String device_name;
      String group_name;
      String fileName;
      String sheetName;
      String[] columnNames;
      int index;
      if (data.equalsIgnoreCase("connectionStatus")) {
         MessageKey1 = "MESSAGE_STATISTICS_TABLE_COLUMN_CONNECTION_CONNECTED_DEVICE_NUMBER_P";
         MessageKey2 = "MESSAGE_STATISTICS_TABLE_COLUMN_CONNECTION_DISCONNECTED_DEVICE_NUMBER_P";
         request_date = "MESSAGE_STATISTICS_TABLE_COLUMN_CONNECTION_GROUP_NAME_P";
         device_number = "MESSAGE_STATISTICS_TABLE_COLUMN_APPROVE_TOTAL_DEVICE_NUMBER_P";
         fileName = rms.getMessage(request_date, (Object[])null, rms.getMessage(MessageKey1, (Object[])null, new Locale("en")), locale);
         sheetName = rms.getMessage(device_number, (Object[])null, rms.getMessage(MessageKey1, (Object[])null, new Locale("en")), locale);
         device_id = rms.getMessage(MessageKey1, (Object[])null, rms.getMessage(MessageKey1, (Object[])null, new Locale("en")), locale);
         device_name = rms.getMessage(MessageKey2, (Object[])null, rms.getMessage(MessageKey2, (Object[])null, new Locale("en")), locale);
         group_name = rms.getMessage("TABLE_ORGANIZATION_P", (Object[])null, locale);
         fileName = "GroupConnectionStatus.xls";
         if (exportType.toUpperCase().equals("PDF")) {
            fileName = "GroupConnectionStatus.pdf";
         }

         sheetName = "Device Group Connection";
         columnNames = new String[]{"organization_name", "group_name", "total_device_number", "connection_count", "disconnection_count"};
         String[] fieldNames = new String[]{group_name, fileName, sheetName, device_id, device_name};
         if (this.device_connection_service == null) {
            this.device_connection_service = new DeviceConnectionStatisticsService();
         }

         List resultList = this.device_connection_service.getDeviceGroupConnectionCurrentStatusData(request);
         DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
         if (resultList != null) {
            Iterator var28 = resultList.iterator();

            while(var28.hasNext()) {
               DeviceGroupConnectionEntity device = (DeviceGroupConnectionEntity)var28.next();
               device.setOrganization_name(groupDao.getOrgNameByGroupId(device.getGroup_id()));
            }
         }

         index = resultList.size();
         Object[] dataList = new Object[index];

         for(int index = 0; index < index; ++index) {
            dataList[index] = resultList.get(index);
         }

         Map dataMap = new HashMap();
         dataMap.put("fileName", fileName);
         dataMap.put("sheetName", sheetName);
         dataMap.put("columnNames", columnNames);
         dataMap.put("fieldNames", fieldNames);
         dataMap.put("dataList", dataList);
         if (exportType.toUpperCase().equals("PDF")) {
            PdfBuilder pdfView = new PdfBuilder();
            return new ModelAndView(pdfView, dataMap);
         }

         if (this.downloadService == null) {
            this.downloadService = new DeviceStatisticsDownloadService();
         }

         this.downloadService.downloadExcelFile(dataMap, response);
      } else if (data.equalsIgnoreCase("connectionHistoryTable")) {
         MessageKey1 = "TABLE_MAC_ADDR_P";
         MessageKey2 = "TABLE_DEVICE_NAME_P";
         request_date = "COM_TABLE_GROUP_NAME_P";
         device_number = "COM_MIS_TEXT_CONNECTED_P";
         fileName = "TEXT_DISCONNECTED_P";
         sheetName = "TABLE_DURATION_P";
         device_id = rms.getMessage(MessageKey1, (Object[])null, rms.getMessage(MessageKey1, (Object[])null, new Locale("en")), locale);
         device_name = rms.getMessage(MessageKey2, (Object[])null, rms.getMessage(MessageKey2, (Object[])null, new Locale("en")), locale);
         group_name = rms.getMessage(request_date, (Object[])null, rms.getMessage(request_date, (Object[])null, new Locale("en")), locale);
         fileName = rms.getMessage(device_number, (Object[])null, rms.getMessage(device_number, (Object[])null, new Locale("en")), locale);
         sheetName = rms.getMessage(fileName, (Object[])null, rms.getMessage(fileName, (Object[])null, new Locale("en")), locale);
         String duration = rms.getMessage(sheetName, (Object[])null, rms.getMessage(sheetName, (Object[])null, new Locale("en")), locale);
         String organization = rms.getMessage("TABLE_ORGANIZATION_P", (Object[])null, locale);
         String boot_reason = "BOOT REASON";
         String shutdown_reason = "SHUTDOWN REASON";
         String fileName = "DeviceConnectionHistoryStatus.xls";
         if (exportType.toUpperCase().equals("PDF")) {
            fileName = "DeviceConnectionHistoryStatus.pdf";
         }

         String sheetName = "Device Connection History Status";
         String[] columnNames = new String[]{"device_id", "device_name", "group_name", "connected_time_string", "disconnected_time_string", "duration_string", "organization_name", "boot_reason", "shutdown_reason"};
         String[] fieldNames = new String[]{device_id, device_name, group_name, fileName, sheetName, duration, organization, boot_reason, shutdown_reason};
         SimpleDateFormat sdfFrom = new SimpleDateFormat(SecurityUtils.getUserContainer().getUser().getDate_format());
         SimpleDateFormat sdfTo = new SimpleDateFormat("yyyy-MM-dd");
         Date start_date = null;
         Object var35 = null;

         try {
            sdfFrom.parse(startDate);
            sdfFrom.parse(endDate);
         } catch (Exception var44) {
            sdfTo.parse(startDate);
            sdfTo.parse(endDate);
         }

         if (this.device_conn_history_service == null) {
            this.device_conn_history_service = new DeviceConnHistoryStatisticsService();
         }

         List resultList = this.device_conn_history_service.getPeriodDeviceConnHistoryData(startDate, endDate, groupIdsStr, 65535L);
         DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
         if (resultList != null) {
            Iterator var38 = resultList.iterator();

            while(var38.hasNext()) {
               DeviceConnHistoryEntity deviceConnHistory = (DeviceConnHistoryEntity)var38.next();

               try {
                  deviceConnHistory.setOrganization_name(groupDao.getOrgNameByGroupId(Long.parseLong(deviceConnHistory.getGroup_id())));
               } catch (Exception var43) {
                  this.logger.error(var43);
               }
            }

            int dataListSize = resultList.size();
            Object[] dataList = new Object[dataListSize];

            for(int index = 0; index < dataListSize; ++index) {
               dataList[index] = resultList.get(index);
            }

            Map dataMap = new HashMap();
            dataMap.put("fileName", fileName);
            dataMap.put("sheetName", sheetName);
            dataMap.put("columnNames", columnNames);
            dataMap.put("fieldNames", fieldNames);
            dataMap.put("dataList", dataList);
            if (exportType.toUpperCase().equals("PDF")) {
               PdfBuilder pdfView = new PdfBuilder();
               return new ModelAndView(pdfView, dataMap);
            }

            if (this.downloadService == null) {
               this.downloadService = new DeviceStatisticsDownloadService();
            }

            this.downloadService.downloadExcelFile(dataMap, response);
         }
      } else if (data.equalsIgnoreCase("approvalStatusTable")) {
         MessageKey1 = "MESSAGE_STATISTICS_TABLE_COLUMN_APPROVE_REQUESTED_DATE_P";
         MessageKey2 = "MESSAGE_STATISTICS_TABLE_COLUMN_APPROVE_REQUESTED_DEVICE_NUMBER_P";
         request_date = rms.getMessage(MessageKey1, (Object[])null, rms.getMessage(MessageKey1, (Object[])null, new Locale("en")), locale);
         device_number = rms.getMessage(MessageKey2, (Object[])null, rms.getMessage(MessageKey2, (Object[])null, new Locale("en")), locale);
         fileName = "ApprovedDeviceList.xls";
         if (exportType.toUpperCase().equals("PDF")) {
            fileName = "ApprovedDeviceList.pdf";
         }

         sheetName = "Approved Device";
         String[] columnNames = new String[]{"create_date", "device_number"};
         String[] fieldNames = new String[]{request_date, device_number};
         SimpleDateFormat sdfFrom = new SimpleDateFormat(SecurityUtils.getUserContainer().getUser().getDate_format());
         SimpleDateFormat sdfTo = new SimpleDateFormat("yyyy-MM-dd");
         sheetName = null;
         columnNames = null;

         Date start_date;
         Date end_date;
         try {
            start_date = sdfFrom.parse(startDate);
            end_date = sdfFrom.parse(endDate);
         } catch (Exception var42) {
            start_date = sdfTo.parse(startDate);
            end_date = sdfTo.parse(endDate);
         }

         if (this.device_approve_service == null) {
            this.device_approve_service = new DeviceApproveStatisticsService();
         }

         List resultList = this.device_approve_service.getPeriodApprovedDeviceData(sdfTo.format(start_date), sdfTo.format(end_date), request);
         int dataListSize = resultList.size();
         Object[] dataList = new Object[dataListSize];

         for(index = 0; index < dataListSize; ++index) {
            dataList[index] = resultList.get(index);
         }

         Map dataMap = new HashMap();
         dataMap.put("fileName", fileName);
         dataMap.put("sheetName", sheetName);
         dataMap.put("columnNames", columnNames);
         dataMap.put("fieldNames", fieldNames);
         dataMap.put("dataList", dataList);
         if (exportType.toUpperCase().equals("PDF")) {
            PdfBuilder pdfView = new PdfBuilder();
            return new ModelAndView(pdfView, dataMap);
         }

         if (this.downloadService == null) {
            this.downloadService = new DeviceStatisticsDownloadService();
         }

         this.downloadService.downloadExcelFile(dataMap, response);
      }

      return null;
   }
}
