package com.samsung.magicinfo.webauthor2.webapi.resource;

import com.samsung.magicinfo.webauthor2.model.VWLContent;
import java.io.Serializable;
import org.springframework.hateoas.Link;
import org.springframework.hateoas.Resource;

public class VWLContentResource extends Resource<VWLContent> implements Serializable {
  private static final long serialVersionUID = 1L;
  
  public VWLContentResource(VWLContent content, Iterable<Link> links) {
    super(content, links);
  }
  
  public VWLContentResource(VWLContent content, Link... links) {
    super(content, links);
  }
}
