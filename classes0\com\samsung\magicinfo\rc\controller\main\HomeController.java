package com.samsung.magicinfo.rc.controller.main;

import com.samsung.magicinfo.rc.common.cache.OneTimeSessionCache;
import com.samsung.magicinfo.rc.model.api.UserSession;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

@Controller
public class HomeController {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.controller.main.HomeController.class);
  
  @Autowired
  OneTimeSessionCache oneTimeSessionCache;
  
  @Value("${spring.profiles.active}")
  private static String activeProfile;
  
  @PostMapping({"/remote"})
  public void index(@RequestParam(value = "token", required = false) String deviceToken, @RequestParam(value = "accessToken", required = false) String accessToken, @RequestParam(value = "from", required = false) String from, @RequestParam(value = "deviceIds", required = false) String device, @RequestParam(value = "locale", required = false, defaultValue = "en") String locale, @RequestParam(value = "sessionExpiry", required = false) Integer sessionExpiry, HttpServletRequest request, HttpServletResponse response) {
    List<String> deviceIds = null;
    if (!StringUtils.isEmpty(device))
      deviceIds = Arrays.asList(device.split(",")); 
    String sessionId = UUID.randomUUID().toString().toUpperCase();
    String magicInfoUrl = from;
    if (!StringUtils.isEmpty(from) && from.lastIndexOf("/MagicInfo") > 0)
      magicInfoUrl = from.substring(0, from.lastIndexOf("/MagicInfo") + 10); 
    log.error("[RC][HomeControl] deviceIds : " + deviceIds);
    UserSession userSession = UserSession.builder().id(sessionId).token(deviceToken).accessToken(accessToken).deviceIds(deviceIds).from(magicInfoUrl).locale(locale).sessionExpiry(sessionExpiry.intValue()).build();
    log.error("[RC][HomeControl] UserSession : " + userSession.toString());
    this.oneTimeSessionCache.put(sessionId, userSession);
    response.setHeader("Location", "/RMServer/remote/" + sessionId);
    response.setStatus(302);
  }
  
  @RequestMapping(value = {"/remocon.do"}, method = {RequestMethod.GET, RequestMethod.POST})
  public ModelAndView index(@RequestParam("deviceId") String deviceId, @RequestParam("token") String token, @RequestParam(value = "information", required = false) String information, @RequestParam(value = "type", required = false) String type, @RequestParam(value = "authority", required = false) String authority) {
    ModelAndView modelAndView = null;
    String deviceName = null;
    String scheduleName = null;
    String deviceModelName = null;
    String firmwareVersion = null;
    String ipAddress = null;
    String gateway = null;
    String triggeringInterval = null;
    if (token.indexOf(",") > -1)
      token = token.split(",")[0]; 
    if (information != null) {
      String[] playerInfo = information.split("\\|", 8);
      if (playerInfo.length == 1)
        deviceName = playerInfo[0]; 
      if (playerInfo.length == 2)
        scheduleName = playerInfo[1]; 
      if (playerInfo.length == 3)
        deviceModelName = playerInfo[2]; 
      if (playerInfo.length == 5)
        firmwareVersion = playerInfo[4]; 
      if (playerInfo.length == 6)
        ipAddress = playerInfo[5]; 
      if (playerInfo.length == 7)
        gateway = playerInfo[6]; 
      if (playerInfo.length == 8)
        triggeringInterval = playerInfo[7]; 
    } 
    if (type != null && type.equalsIgnoreCase("mobile")) {
      modelAndView = new ModelAndView("main/mobile/rm");
    } else {
      modelAndView = new ModelAndView("main/rm");
    } 
    modelAndView.addObject("token", token);
    modelAndView.addObject("deviceId", deviceId);
    if (authority == null) {
      modelAndView.addObject("cmd", "CONTROL");
    } else if (authority.contains("DeviceControl") == true || authority
      .contains("DeviceWrite") == true) {
      modelAndView.addObject("cmd", "CONTROL");
    } else {
      modelAndView.addObject("cmd", "ONLYVIEW");
    } 
    modelAndView.addObject("deviceName", deviceName);
    modelAndView.addObject("scheduleName", scheduleName);
    modelAndView.addObject("deviceModelName", deviceModelName);
    modelAndView.addObject("firmwareVersion", firmwareVersion);
    modelAndView.addObject("ipAddress", ipAddress);
    modelAndView.addObject("gateway", gateway);
    modelAndView.addObject("triggeringInterval", triggeringInterval);
    modelAndView.addObject("information", information);
    return modelAndView;
  }
}
