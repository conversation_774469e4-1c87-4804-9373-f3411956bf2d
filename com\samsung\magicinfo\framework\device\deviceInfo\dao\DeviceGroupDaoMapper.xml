<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceGroupDaoMapper">

	<insert id="addGroup">
		INSERT INTO MI_DMS_INFO_GROUP ( GROUP_ID, P_GROUP_ID, GROUP_DEPTH,
		GROUP_NAME,
		DESCRIPTION, IS_ROOT, CREATOR_ID, GROUP_TYPE, CREATE_DATE )
		VALUES (
		#{groupId}, #{deviceGroup.p_group_id},
		#{deviceGroup.group_depth}, #{deviceGroup.group_name},
		#{deviceGroup.description}, #{isRoot},
		#{deviceGroup.creator_id},
		#{deviceGroup.group_type},
		<include refid="utils.currentTimestamp" />)
	</insert>
	
	<insert id="addUserToGroup">
		INSERT INTO MI_DMS_MAP_GROUP_USER (GROUP_ID, USER_ID) 
		VALUES (#{groupId}, #{userId})
	</insert>

	<insert id="addGroupForOrg">
		INSERT INTO MI_DMS_INFO_GROUP ( GROUP_ID, P_GROUP_ID, GROUP_DEPTH,
		GROUP_NAME,
		DESCRIPTION, GROUP_TYPE, CREATOR_ID, CREATE_DATE )
		VALUES ( #{groupId}, #{p_groupId}, #{groupDepth} , #{groupName} ,
		#{description}, #{type}, #{creatorId} ,
		<include refid="utils.currentTimestamp" />)
	</insert>

	<select id="getGroup"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

		SELECT GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, DESCRIPTION, VWT_ID, IS_ROOT, GROUP_TYPE, CREATOR_ID,
		       CREATE_DATE, MIN_PRIORITY, ANALYSIS_STATUS
        FROM MI_DMS_INFO_GROUP
        WHERE GROUP_ID = #{groupId}
	</select>

	<select id="getDeviceTopGroup"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

		SELECT GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, DESCRIPTION, IS_ROOT,
		GROUP_TYPE, CREATOR_ID, CREATE_DATE
		FROM MI_DMS_INFO_GROUP WHERE
		GROUP_ID = #{groupId}
	</select>

	<select id="getDeviceGroupForUser" resultType="long">
		SELECT GROUP_ID
		FROM MI_DMS_INFO_GROUP WHERE GROUP_NAME = #{strOrg} AND P_GROUP_ID = 0 AND GROUP_DEPTH = 1
	</select>

	<select id="getChildGroupIdList" resultType="map">
		SELECT GROUP_ID FROM
		MI_DMS_INFO_GROUP WHERE P_GROUP_ID = #{groupId} AND GROUP_ID !=
		#{non_approval_group}
	</select>

	<select id="getChildDeviceList"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
		SELECT A.* FROM MI_DMS_INFO_DEVICE A,
		MI_DMS_MAP_GROUP_DEVICE B WHERE
		A.DEVICE_ID = B.DEVICE_ID AND
		B.GROUP_ID = #{groupId}
	</select>

	<select id="getTotalOrganizationDeviceCountByGroupId"
			resultType="long">
		SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE A, MI_DMS_INFO_GROUP B
		WHERE B.GROUP_NAME = A.ORGANIZATION AND B.GROUP_ID = #{groupId} AND A.DEVICE_ID NOT LIKE '%\_%'
	</select>

	<select id="getTotalOrganizationDeviceCountByGroupId"
			resultType="long" databaseId="mssql">
		SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE A, MI_DMS_INFO_GROUP B
		WHERE B.GROUP_NAME = A.ORGANIZATION AND B.GROUP_ID = #{groupId} AND CHARINDEX('_', A.DEVICE_ID) = 0
	</select>

	<select id="getTotalOrganizationDeviceCountByGroupIds" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		SELECT GROUPS.*, COUNT(DEVICE_ID) AS DEVICE_COUNT
		FROM MI_DMS_MAP_GROUP_DEVICE DEVICES
		LEFT JOIN MI_DMS_INFO_GROUP GROUPS ON DEVICES.ORGANIZATION = GROUPS.GROUP_NAME
		WHERE DEVICES.DEVICE_ID NOT LIKE '%\_%'
		<if test="deviceGroups != null and deviceGroups.size() > 0">
			AND
			<foreach collection="deviceGroups" item="deviceGroup" separator=" OR " open="(" close=")">
				ORGANIZATION = #{deviceGroup.group_name}
			</foreach>
		</if>
		GROUP BY GROUPS.GROUP_ID, GROUPS.P_GROUP_ID, GROUPS.GROUP_DEPTH, GROUPS.GROUP_NAME, GROUPS.DESCRIPTION, GROUPS.IS_ROOT, GROUPS.GROUP_TYPE, GROUPS.CREATOR_ID, GROUPS.DEFAULT_PROGRAM_ID, GROUPS.CREATE_DATE, GROUPS.IS_REDUNDANCY, GROUPS.VWT_ID, GROUPS.TOTAL_COUNT, GROUPS.MIN_PRIORITY
	</select>

	<select id="getTotalOrganizationDeviceCountByGroupIds" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup" databaseId="mssql">
		SELECT GROUPS.*, COUNT(DEVICE_ID) AS DEVICE_COUNT
		FROM MI_DMS_MAP_GROUP_DEVICE DEVICES
		LEFT JOIN MI_DMS_INFO_GROUP GROUPS ON DEVICES.ORGANIZATION = GROUPS.GROUP_NAME
		WHERE CHARINDEX('_', DEVICES.DEVICE_ID) = 0
		<if test="deviceGroups != null and deviceGroups.size() > 0">
			AND
			<foreach collection="deviceGroups" item="deviceGroup" separator=" OR " open="(" close=")">
				ORGANIZATION = #{deviceGroup.group_name}
			</foreach>
		</if>
		GROUP BY GROUPS.GROUP_ID, GROUPS.P_GROUP_ID, GROUPS.GROUP_DEPTH, GROUPS.GROUP_NAME, GROUPS.DESCRIPTION, GROUPS.IS_ROOT, GROUPS.GROUP_TYPE, GROUPS.CREATOR_ID, GROUPS.DEFAULT_PROGRAM_ID, GROUPS.CREATE_DATE, GROUPS.IS_REDUNDANCY, GROUPS.VWT_ID, GROUPS.TOTAL_COUNT, GROUPS.MIN_PRIORITY
	</select>

    <select id="getTotalApprovalDeviceCount" resultType="long">
        SELECT COUNT(GROUPS.DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE GROUPS
        LEFT JOIN MI_DMS_INFO_DEVICE DEVICES ON GROUPS.DEVICE_ID = DEVICES.DEVICE_ID
        WHERE GROUP_ID != 999999 AND DEVICES.IS_CHILD = <include refid="utils.false"/>
    </select>


	<select id="getDeviceCountByOrganization"
			resultType="map">
		SELECT  U.GROUP_ID AS ROOT_GROUP_ID, COUNT(D.DEVICE_ID) AS DEVICE_COUNT
		FROM MI_USER_INFO_GROUP U
		LEFT OUTER JOIN MI_DMS_MAP_GROUP_DEVICE D ON D.ORGANIZATION = U.GROUP_NAME
		WHERE U.P_GROUP_ID = 0
		GROUP BY U.GROUP_ID
		HAVING COUNT(D.DEVICE_ID) > 0
	</select>

	<delete id="deleteOrgGroup">
		DELETE FROM MI_DMS_INFO_GROUP WHERE GROUP_ID =
		#{groupId}
	</delete>

	<select id="getChildDeviceIdList" resultType="map">
		SELECT A.DEVICE_ID
		FROM MI_DMS_INFO_DEVICE A,
		MI_DMS_MAP_GROUP_DEVICE B
		WHERE A.DEVICE_ID = B.DEVICE_ID 
		      AND B.GROUP_ID = #{groupId}
		      AND A.IS_CHILD = <include refid="utils.false"/>
	</select>
	
	
	<select id="getChildDeviceIdListRecursive" resultType="map">
        WITH RECURSIVE DEVICE_GROUP AS (
		        SELECT 
		            GROUP_ID
		        FROM MI_DMS_INFO_GROUP A
		        WHERE GROUP_ID = #{groupId}
		    UNION ALL
		        SELECT G.GROUP_ID
		        FROM MI_DMS_INFO_GROUP G JOIN DEVICE_GROUP
		        ON G.P_GROUP_ID = DEVICE_GROUP.GROUP_ID
		)
		SELECT * FROM MI_DMS_MAP_GROUP_DEVICE 
		WHERE 
            GROUP_ID IN (SELECT GROUP_ID FROM DEVICE_GROUP)
            AND DEVICE_ID NOT LIKE '%\_%'
            AND GROUP_ID != 999999
	</select>
	
	<select id="getChildDeviceIdListRecursive" resultType="map" databaseId="mssql">
        WITH DEVICE_GROUP AS (
                SELECT 
                    GROUP_ID
                FROM MI_DMS_INFO_GROUP A
                WHERE GROUP_ID = #{groupId}
            UNION ALL
                SELECT G.GROUP_ID
                FROM MI_DMS_INFO_GROUP G JOIN DEVICE_GROUP
                ON G.P_GROUP_ID = DEVICE_GROUP.GROUP_ID
        )
        SELECT * FROM MI_DMS_MAP_GROUP_DEVICE 
        WHERE 
            GROUP_ID IN (SELECT GROUP_ID FROM DEVICE_GROUP)
            AND CHARINDEX('_', DEVICE_ID) = 0
            AND GROUP_ID != 999999
    </select>
	
	<select id="getChildDeviceIdListByOrganName" resultType="map">
		SELECT DEVICE_ID
		FROM MI_DMS_MAP_GROUP_DEVICE
		WHERE ORGANIZATION = #{organName}
	</select>

	<delete id="delGroup">
		DELETE FROM MI_DMS_INFO_GROUP WHERE GROUP_ID =
		#{groupId}
	</delete>

	<select id="getChildDeviceIdList2" resultType="map">
		SELECT A.DEVICE_ID
		FROM MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B
		WHERE A.DEVICE_ID = B.DEVICE_ID AND B.GROUP_ID = #{groupId} AND A.IS_CHILD = <include refid="utils.false"/>
	</select>

	<select id="getChildDeviceList2"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">

		SELECT A.* FROM MI_DMS_INFO_DEVICE A,
		MI_DMS_MAP_GROUP_DEVICE B
		WHERE A.DEVICE_ID = B.DEVICE_ID AND A.IS_CHILD = <include refid="utils.false"/> AND B.GROUP_ID = #{groupId}
	</select>

	<select id="getChildGroupList"
                     resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

		SELECT * FROM MI_DMS_INFO_GROUP WHERE P_GROUP_ID = #{groupId} AND GROUP_ID
		!= #{non_approval_group} ORDER BY GROUP_NAME ASC
	</select>

    <select id="getOrgListWithPermission"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

		SELECT * FROM MI_DMS_INFO_GROUP
		<if test="groupId != null and groupId != 0" >
			WHERE
				GROUP_ID = #{groupId}
		</if>
		<if test="groupId == 0" >
			WHERE
				P_GROUP_ID = #{groupId}
		</if>
		AND GROUP_ID != #{non_approval_group}
		ORDER BY GROUP_NAME ASC
	</select>

	<select id="getChildGroupListByGroupType"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

		SELECT * FROM MI_DMS_INFO_GROUP
		WHERE P_GROUP_ID =
		#{groupId} AND GROUP_ID != #{non_approval_group} AND
		(GROUP_TYPE != #{notGroupType} AND GROUP_TYPE != '3rdPartyPLAYER' OR GROUP_TYPE is null)
		ORDER BY GROUP_NAME ASC
	</select>

	<select id="getChildGroupListByGroupTypeForSchedule"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

		SELECT * FROM MI_DMS_INFO_GROUP
		WHERE P_GROUP_ID =
		#{groupId} AND GROUP_ID != #{non_approval_group} AND
		GROUP_TYPE =
		#{groupType}
		ORDER BY GROUP_NAME ASC
	</select>

	<select id="getDefaultProgramId" resultType="string">
		SELECT
		DEFAULT_PROGRAM_ID FROM MI_DMS_INFO_GROUP WHERE GROUP_ID =
		#{groupId}
	</select>

	<select id="getDeviceGroupRoot" resultType="map">
		SELECT
		GROUP_ID,GROUP_NAME,P_GROUP_ID FROM MI_DMS_INFO_GROUP WHERE
		GROUP_ID =
		#{groupId}
	</select>

	<select id="getDeviceOrgGroupId" resultType="map">
		SELECT GROUP_ID,
		P_GROUP_ID FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = #{groupId}
	</select>

	<select id="getGroupByDeviceId"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

		SELECT A.GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, CREATOR_ID, CREATE_DATE, A.DEFAULT_PROGRAM_ID
		FROM MI_DMS_INFO_GROUP A, MI_DMS_MAP_GROUP_DEVICE B
		WHERE A.GROUP_ID = B.GROUP_ID AND B.DEVICE_ID= #{deviceId}
	</select>

	<select id="getGroupIdByOrgBasic" resultType="long">
		SELECT A.GROUP_ID
		FROM MI_DMS_INFO_GROUP A JOIN MI_DMS_INFO_GROUP B ON A.P_GROUP_ID =
		B.GROUP_ID
		WHERE B.P_GROUP_ID = 0 AND B.GROUP_NAME = #{orgName} AND
		A.GROUP_NAME = #{childName}
	</select>

	<select id="getGroupList"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

		SELECT GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME FROM
		MI_DMS_INFO_GROUP
		WHERE GROUP_ID != #{rootGroup} AND GROUP_ID !=
		#{non_approval_group}
		ORDER BY P_GROUP_ID
	</select>
	
	<select id="getGroupListWithParams" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

		SELECT *
		FROM MI_DMS_INFO_GROUP
		<include refid="getGroupListWithParamsWhere"/>
		<if test="condition.sortName != null and condition.sortName != '' and condition.orderDir != null and condition.orderDir != ''">
            <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sortName)" />
            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.orderDir)" />
            ORDER BY ${safe_sortUpper} ${safe_sortOrder}
        </if>
        LIMIT #{pageSize} OFFSET #{startPos}
	</select>
	
	<select id="getGroupListWithParams" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup" databaseId="mssql">
		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
	SELECT * FROM (
		SELECT *
		<if test="condition.sortName != null and condition.sortName != '' and condition.orderDir != null and condition.orderDir != ''">
            <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sortName)" />
            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.orderDir)" />
            ,ROW_NUMBER() OVER(ORDER BY ${safe_sortUpper} ${safe_sortOrder} ) as RowNum
         </if>
		FROM MI_DMS_INFO_GROUP
		<include refid="getGroupListWithParamsWhere"/>
		
		) as SubQuery
        WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
        ORDER BY RowNum
	</select>
	
	<sql id="getGroupListWithParamsWhere">
        WHERE GROUP_ID != #{rootGroup} AND GROUP_ID !=	#{non_approval_group}
        <if test="condition.groupId != null and condition.groupId != -2" >
        	AND GROUP_ID = #{condition.groupId}
        </if>
        <if test="condition.parentGroupId != null and condition.parentGroupId != -2">
        	AND P_GROUP_ID = #{condition.parentGroupId}
        </if>
        <if test="condition.groupDepth != null and condition.groupDepth != -2">
        	AND GROUP_DEPTH = #{condition.groupDepth}
        </if>
        <if test="condition.groupName != null">
        	AND GROUP_NAME = #{condition.groupName}
        </if>
        <if test="condition.analysisStatus != null">
        	AND ANALYSIS_STATUS = #{condition.analysisStatus}
        </if>

	</sql>
	<select id="getGroupListCntWithParams" resultType="int" >
		SELECT COUNT(*)
		FROM MI_DMS_INFO_GROUP
		<include refid="getGroupListWithParamsWhere"/>
	</select>

	<select id="getGroupNameByDeviceId" resultType="map">
		SELECT GROUP_NAME, B.GROUP_ID
		FROM MI_DMS_MAP_GROUP_DEVICE A, MI_DMS_INFO_GROUP B
		WHERE
		A.GROUP_ID =
		B.GROUP_ID AND A.DEVICE_ID = #{deviceId}
	</select>

	<select id="getGroupsForOrg" resultType="map">
		<bind name="strOrgPattern" value="strOrg + '_basic'" />
		SELECT GROUP_ID FROM MI_DMS_INFO_GROUP WHERE lower(GROUP_NAME)
		IN(
		lower( #{strOrg} ), lower( #{strOrgPattern} ))
	</select>

	<select id="getMessageGroupRoot" resultType="map">
		SELECT
		GROUP_ID,GROUP_NAME,P_GROUP_ID FROM MI_CDS_INFO_MESSAGE_GROUP
		WHERE
		GROUP_ID = #{groupId}
	</select>

	<select id="getOrganGroupIdByName" resultType="map">
		SELECT GROUP_ID
		FROM MI_DMS_INFO_GROUP WHERE GROUP_NAME = #{orgGroupName} AND
		GROUP_DEPTH = 1
	</select>

	<select id="getOrgGroupId" resultType="map">
		SELECT GROUP_ID FROM
		MI_DMS_INFO_GROUP WHERE GROUP_NAME = #{groupName}
		LIMIT 1
	</select>
	
	<select id="getOrgGroupId" resultType="map" databaseId="mssql">
		SELECT TOP (1) GROUP_ID FROM
		MI_DMS_INFO_GROUP WHERE GROUP_NAME = #{groupName}
	</select>

	<select id="getOrgNameByGroupId" resultType="map">
		SELECT P_GROUP_ID,
		GROUP_DEPTH, GROUP_NAME FROM MI_DMS_INFO_GROUP WHERE GROUP_ID =
		#{groupId}
	</select>

	<select id="getOrgIdByGroupId" resultType="map">
		SELECT GROUP_ID,
		P_GROUP_ID, GROUP_DEPTH, GROUP_NAME FROM
		MI_DMS_INFO_GROUP WHERE
		GROUP_ID = #{groupId}
	</select>

	<select id="getParentGroupId" resultType="int">
		SELECT P_GROUP_ID FROM
		MI_DMS_INFO_GROUP WHERE GROUP_ID = #{groupId}
	</select>

	<select id="getProgramGroupRoot" resultType="map">
		SELECT
		GROUP_ID,GROUP_NAME,P_GROUP_ID FROM MI_CDS_INFO_PROGRAM_GROUP WHERE
		GROUP_ID = #{groupId}
	</select>

	<update id="moveGroup">
		UPDATE MI_DMS_INFO_GROUP SET P_GROUP_ID =
		#{new_parent_groupId}
		WHERE GROUP_ID = #{groupId}
	</update>

	<update id="changeGroupDepth">
		WITH RECURSIVE GROUP_IDS AS (
		SELECT *
		FROM MI_DMS_INFO_GROUP
		WHERE GROUP_ID = #{groupId}
		UNION ALL
		SELECT CHILD_GROUP.*
		FROM MI_DMS_INFO_GROUP CHILD_GROUP
		JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
		)
		UPDATE MI_DMS_INFO_GROUP GROUPS
		SET GROUP_DEPTH = GROUPS.GROUP_DEPTH +#{changeInGroupDepth}
		FROM GROUP_IDS
		WHERE GROUP_IDS.GROUP_ID = GROUPS.GROUP_ID;
	</update>
	<update id="changeGroupDepth" databaseId="mssql">
		WITH GROUP_IDS AS (
		SELECT *
		FROM MI_DMS_INFO_GROUP GROUPS
		WHERE GROUP_ID = #{groupId}
		UNION ALL
		SELECT CHILD_GROUP.*
		FROM MI_DMS_INFO_GROUP CHILD_GROUP
		JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
		)
		UPDATE MI_DMS_INFO_GROUP
		SET MI_DMS_INFO_GROUP.GROUP_DEPTH = MI_DMS_INFO_GROUP.GROUP_DEPTH + #{changeInGroupDepth}
		FROM GROUP_IDS
		WHERE GROUP_IDS.GROUP_ID = MI_DMS_INFO_GROUP.GROUP_ID;
	</update>

	<update id="setDefaultProgramId">
		UPDATE MI_DMS_INFO_GROUP SET DEFAULT_PROGRAM_ID =
		#{programId} WHERE GROUP_ID = #{groupId}
	</update>

	<update id="setGroup">
		UPDATE MI_DMS_INFO_GROUP SET P_GROUP_ID =
		#{deviceGroup.p_group_id},
		GROUP_DEPTH = #{deviceGroup.group_depth},
		GROUP_NAME =
		#{deviceGroup.group_name}, DESCRIPTION =
		#{deviceGroup.description},
		IS_ROOT = #{deviceGroup.is_root}, GROUP_TYPE =
		#{deviceGroup.group_type},
		CREATE_DATE =
		<include refid="utils.currentTimestamp" />
		WHERE GROUP_ID = #{deviceGroup.group_id}
	</update>

	<update id="setOrgName">
		UPDATE MI_DMS_INFO_GROUP SET GROUP_NAME =#{newName}
		WHERE P_GROUP_ID = 0 AND GROUP_NAME =
		#{originName}
	</update>

	<select id="getMaxDepth" resultType="long">
		SELECT MAX(GROUP_DEPTH) FROM
		MI_DMS_INFO_GROUP
	</select>

	<select id="getDefaultDeviceGroup"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

		SELECT * FROM MI_DMS_INFO_GROUP WHERE ( GROUP_DEPTH = 0 OR GROUP_DEPTH = 1 )
		AND GROUP_ID != 999999 AND GROUP_ID != 0
	</select>

	<select id="getSpecificDepthDeviceGroupList"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

		SELECT * FROM MI_DMS_INFO_GROUP WHERE GROUP_DEPTH = #{depth}
	</select>

	<select id="getDeviceGroupTreeFirstLevel"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

                <bind name="safe_tableName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(table)" />

		SELECT * FROM ${safe_tableName} WHERE GROUP_DEPTH = 1 AND GROUP_ID != 999999
		<if
			test="organization != null and organization != '' and organization != 'ROOT'">
			AND GROUP_NAME = #{organization}
		</if>
		<if test="skipId != null and skipId != ''">
			AND GROUP_ID != #{skipId}
		</if>

		ORDER BY GROUP_NAME

		<choose>
			<when test="sortType == null or sortType == ''">
				ASC
			</when>
			<otherwise>
				#{sortType}
			</otherwise>
		</choose>

	</select>

	<select id="getCntDeviceInDeviceGroup" resultType="int">
		SELECT
		COUNT(A.DEVICE_ID)
		FROM MI_DMS_MAP_GROUP_DEVICE A, MI_DMS_INFO_DEVICE B
		WHERE GROUP_ID = #{groupId}
		AND A.DEVICE_ID = B.DEVICE_ID AND B.IS_CHILD = <include refid="utils.false"/>
	</select>

	<select id="getCntDeviceInDeviceGroupExceptFor" resultType="int">
		SELECT
		COUNT(A.DEVICE_ID)
		FROM MI_DMS_MAP_GROUP_DEVICE A, MI_DMS_INFO_DEVICE B 
		WHERE B.DEVICE_ID = A.DEVICE_ID AND A.GROUP_ID =
		#{groupId}
		AND
		<foreach item="deviceType" collection="deviceTypeList"  separator="and" >
        	B.DEVICE_TYPE != #{deviceType}
        </foreach>
	</select>
	
	<select id="getCntDeviceInLiteDeviceGroup" resultType="int">
		SELECT
		COUNT(DEVICE_ID)
		FROM MI_DMS_MAP_LITE_GROUP_DEVICE
		WHERE GROUP_ID =
		#{groupId}
	</select>

	<select id="getDeviceGroupTreeSpecificLevel"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

		SELECT * FROM MI_DMS_INFO_GROUP WHERE P_GROUP_ID = #{p_group_id}
		<if test="skipId != null and skipId != ''">
			AND GROUP_ID != #{skipId}

			<if test="groupType != null and groupType != ''">
				AND GROUP_TYPE = #{groupType}
			</if>
		</if>

		ORDER BY GROUP_NAME

		<choose>
			<when test="sortType == null or sortType == ''">
				ASC
			</when>
			<otherwise>
				<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(sortType)" />
				${safe_sortOrder}
			</otherwise>
		</choose>
	</select>

	<select id="getCntDeviceInVwlConsoleDevice" resultType="int">
		SELECT
		COUNT(DEVICE_ID)
		FROM MI_DMS_MAP_VWL_CONSOLE_DEVICE
		WHERE CONSOLE_ID =
		#{consoleId}
	</select>

	<select id="getVWLGroupTreeSpecificLevel"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

		SELECT A.GROUP_ID AS P_GROUP_ID, 2 AS GROUP_DEPTH,
		GROUP_NAME, IS_ROOT, A.DEFAULT_PROGRAM_ID, B.DEVICE_ID AS DESCRIPTION,
		DEVICE_NAME AS GROUP_NAME, C.DEVICE_TYPE AS GROUP_TYPE
		FROM MI_DMS_INFO_VWL_GROUP A, MI_DMS_MAP_VWL_GROUP_CONSOLE B,
		MI_DMS_INFO_VWL_CONSOLE_DEVICE C
		WHERE A.GROUP_ID = B.GROUP_ID AND B.DEVICE_ID = C.DEVICE_ID AND A.GROUP_ID
		= #{p_group_id}
	</select>

	<select id="getGroupIdByName" resultType="map">
		SELECT GROUP_ID FROM
		MI_DMS_INFO_GROUP WHERE GROUP_NAME = #{groupName}
	</select>

	<update id="setDeviceGroupType">
		UPDATE MI_DMS_INFO_GROUP SET GROUP_TYPE =
		#{groupType} WHERE GROUP_ID = #{groupId}
	</update>

	<delete id="deleteChildGroupAndDevice">
		DELETE FROM MI_DMS_INFO_GROUP WHERE GROUP_ID =
		#{groupId}
	</delete>
	
	<delete id="delMapGroupUser">
		DELETE FROM MI_DMS_MAP_GROUP_USER WHERE GROUP_ID = #{groupId}
	</delete>
	
	<select id="getChildGroupListByGroupTypeForSchedule2"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		
		SELECT * FROM MI_DMS_INFO_GROUP
		WHERE P_GROUP_ID = #{groupId} AND GROUP_ID != #{non_approval_group}
		ORDER BY GROUP_NAME ASC
	</select>
	
	<select id="getAllVWLLayoutGroupList"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		
		SELECT * FROM MI_DMS_INFO_GROUP WHERE VWT_ID IS NOT NULL
	</select>
	
	<select id="getChildGroupListByGroupTypeForScheduleAuth"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		
		SELECT * FROM MI_DMS_INFO_GROUP A, MI_DMS_MAP_GROUP_USER B 
		WHERE A.P_GROUP_ID = #{groupId}
              AND A.GROUP_ID != #{non_approval_group}
              AND A.GROUP_TYPE = #{groupType}
              AND A.GROUP_ID = B.GROUP_ID
              AND B.USER_ID = #{userId}
		ORDER BY GROUP_NAME
	</select>
	
	<select id="getGroupNameByGroupId" resultType="string">
		SELECT GROUP_NAME
		FROM MI_DMS_INFO_GROUP
		WHERE GROUP_ID = #{groupId}
	</select>
	
	<select id="getDeviceGroupTreeFirstLevel2"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

                <bind name="safe_tableName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(table)" />

		SELECT * FROM ${safe_tableName} WHERE GROUP_DEPTH = 1 AND GROUP_ID != 999999
		<if
			test="organization != null and organization != '' and organization != 'ROOT'">
			AND GROUP_NAME = #{organization}
		</if>
		
		<if test="skipId != null and skipId != ''">
			AND GROUP_ID != #{skipId}
		</if>

		ORDER BY GROUP_NAME ASC
	</select>
	
	<select id="getAuthTreeList"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">

                <bind name="safe_tableName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(table)" />
		
		SELECT * FROM ${safe_tableName} A, MI_DMS_MAP_GROUP_USER B WHERE A.P_GROUP_ID = #{p_groupId} AND A.GROUP_ID = B.GROUP_ID AND B.USER_ID = #{userId}
		
		<if test="skipId != null and skipId != ''">
			AND A.GROUP_ID != #{skipId}
		</if>
		
		<if test="groupType != null and groupType != ''">
			AND A.GROUP_TYPE = #{groupType}
		</if>
		
		ORDER BY GROUP_NAME

		<choose>
			<when test="sortType == null or sortType == ''">
				ASC
			</when>
			<otherwise>
				#{sortType}
			</otherwise>
		</choose>
		
	</select>
	
	<select id="getDeviceGroupTreeSpecificLevel2"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
        <bind name="safe_tableName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(table)" />
		
		SELECT * FROM ${safe_tableName} WHERE P_GROUP_ID = #{p_group_id}
		
		<if test="skipId != null and skipId != ''">
			AND GROUP_ID != #{skipId}
		</if>
		
		<if test="groupType != null and groupType != ''">
			AND GROUP_TYPE = #{groupType}
		</if>
		
		ORDER BY GROUP_NAME ASC
		<!-- 
		<choose>
			<when test="sortType == null or sortType == ''">
				ASC
			</when>
			<otherwise>
				#{sortType}
			</otherwise>
		</choose>-->
	</select>
	
	<select id="getDeviceGroupVWLTreeSpecificLevel" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
                <bind name="safe_tableName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(table)" />
		SELECT * FROM ${safe_tableName} WHERE P_GROUP_ID = #{p_group_id}
	</select>
	
	<select id="getBooleanVwlGroupId" resultType="string">
		SELECT VWT_ID
		FROM MI_DMS_INFO_GROUP
		WHERE GROUP_ID = #{groupId}
	</select>
	
	<select id="getRedundancyGroups" resultType="map">
		SELECT GROUP_ID FROM MI_DMS_INFO_GROUP WHERE IS_REDUNDANCY = <include refid="utils.true"/>
	</select>
	
	<select id="isRedundancyGroup" resultType="boolean">
		SELECT IS_REDUNDANCY FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = #{groupId}
	</select>
	
	<select id="getRedundantDeviceIdbyGroupId" resultType="map">
		SELECT A.DEVICE_ID FROM MI_DMS_INFO_DEVICE A, MI_DMS_INFO_GROUP B, MI_DMS_MAP_GROUP_DEVICE C WHERE B.GROUP_ID = #{groupId}
		AND A.DEVICE_ID = C.DEVICE_ID AND B.GROUP_ID = C.GROUP_ID AND A.IS_REDUNDANCY = <include refid="utils.true"/>
	</select>
	
	<update id="setIsRedundancy">
		UPDATE MI_DMS_INFO_GROUP SET IS_REDUNDANCY = #{isRedundancy} WHERE GROUP_ID = #{groupId}
	</update>
	
	<select id="getVwlLayoutIdByGroupId" resultType="string">
		
		SELECT VWT_ID FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = #{groupId}
	</select>
	
	<select id="getVwlLayoutGroupId" resultType="string">
		SELECT GROUP_ID FROM MI_DMS_INFO_GROUP WHERE VWT_ID IS NOT NULL
	</select>
	
	<select id="getGroupNameByVwtId" resultType="string">
		SELECT GROUP_NAME FROM MI_DMS_INFO_GROUP WHERE VWT_ID = #{vwt_id}
	</select>
	
	<update id="setVwtId">
		UPDATE MI_DMS_INFO_GROUP SET VWT_ID = #{vwt_id} WHERE GROUP_ID = 
			(SELECT GROUP_ID FROM MI_DMS_MAP_GROUP_DEVICE WHERE DEVICE_ID= #{deviceId})
	</update>
	
	<select id="isVwlGroup" resultType="string">
		SELECT VWT_ID FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = #{groupId}
	</select>
	
	<select id="getGroupType" resultType="string">
		SELECT GROUP_TYPE FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = #{groupId}
	</select>
	
	<update id="cancelVwlGroup">
		UPDATE MI_DMS_INFO_GROUP SET VWT_ID = NULL WHERE GROUP_ID = #{groupId}
	</update>
	
	<select id="isVWLLayoutGroup" resultType="int">
		SELECT COUNT(VWT_ID) FROM MI_DMS_INFO_GROUP WHERE
        <foreach item="deviceGroupId" collection="deviceGroupList"  separator="OR" >
        	GROUP_ID = #{deviceGroupId}
        </foreach>
	</select>
	
	<select id="getMinimumPriority" resultType="long">
		SELECT MIN(MIN_PRIORITY) FROM MI_DMS_INFO_GROUP WHERE
		<foreach item="deviceGroupId" collection="deviceGroupList"  separator="OR" >
        	GROUP_ID = #{deviceGroupId}
        </foreach>
	</select>
	
	<update id="updateDeviceGroupPriority">
		UPDATE MI_DMS_INFO_GROUP SET MIN_PRIORITY = #{minPriority} WHERE GROUP_ID = #{groupId}
	</update>
	
	<select id="getDeviceTypeByMinimumPriority" resultType="string">
		SELECT DEVICE_TYPE FROM MI_DMS_INFO_DEVICE_PRIORITY WHERE PRIORITY = #{minPriority} LIMIT 1
	</select>
	
	<select id="getDeviceTypeByMinimumPriority" resultType="string" databaseId="mssql">
		SELECT TOP 1 DEVICE_TYPE FROM MI_DMS_INFO_DEVICE_PRIORITY WHERE PRIORITY = #{minPriority}
	</select>
	
	<select id="getDeviceTypeVersionByMinimumPriority" resultType="float">
		SELECT MAX(DEVICE_TYPE_VERSION) FROM MI_DMS_INFO_DEVICE_PRIORITY WHERE PRIORITY = #{minPriority}
	</select>
	
	<select id="getPriority" resultType="long">
		SELECT PRIORITY FROM MI_DMS_INFO_DEVICE_PRIORITY WHERE DEVICE_TYPE = #{deviceType} AND DEVICE_TYPE_VERSION = #{deviceTypeVersion}
	</select>
	
	<delete id="deleteFromMapGroupUser">
		DELETE FROM MI_DMS_MAP_GROUP_USER WHERE USER_ID = #{userId}
	</delete>
	
	<insert id="insertToMapGroupUser">
    	INSERT INTO MI_DMS_MAP_GROUP_USER ( USER_ID, GROUP_ID ) VALUES (#{userId}, #{groupId} ) 
	</insert>
	
	<select id="getAuthDeviceGroupList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		SELECT A.GROUP_ID FROM MI_DMS_MAP_GROUP_USER A, MI_DMS_INFO_GROUP B WHERE A.USER_ID = #{userId} AND A.GROUP_ID=B.GROUP_ID AND B.GROUP_DEPTH > 1
	</select>
	
	<select id="getPermissionsDeviceGroup" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		SELECT GROUP_ID FROM MI_DMS_MAP_GROUP_USER WHERE USER_ID = #{userId}
	</select>

	<select id="getOrgId" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		SELECT GROUP_ID FROM MI_DMS_INFO_GROUP WHERE GROUP_DEPTH = 1 AND GROUP_ID != 999999
		<if test="orgName != null and orgName != '' and orgName != 'ROOT'">
			AND GROUP_NAME = #{orgName}
		</if>
	</select>
	
	<select id="getOrganizationGroup" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		SELECT * FROM MI_DMS_INFO_GROUP WHERE GROUP_DEPTH = 1 AND GROUP_ID != 999999 AND P_GROUP_ID = 0
	</select>
	
	<select id="checkExistGroupId" resultType="int">
		SELECT COUNT(GROUP_ID) FROM MI_DMS_MAP_GROUP_USER WHERE GROUP_ID = #{groupId} AND USER_ID = #{userId}
	</select>
	
	<select id="checkExistUserId" resultType="int">
		SELECT COUNT(USER_ID) FROM MI_DMS_MAP_GROUP_USER WHERE USER_ID = #{userId}
	</select>
	
	<select id="checkChildPermissions1" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
                <bind name="safe_tableName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(table)" />
		SELECT * FROM ${safe_tableName} WHERE GROUP_ID = #{groupId}
	</select>
	
	<select id="checkChildPermissions2" resultType="int">
		SELECT COUNT(GROUP_ID) FROM MI_DMS_MAP_GROUP_USER WHERE GROUP_ID = #{groupId} AND USER_ID = #{userId}
	</select>
	
	<select id="getAuthDeviceGroupTreeSpecificLevel" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
                <bind name="safe_tableName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(table)" />
		SELECT * FROM ${safe_tableName} WHERE P_GROUP_ID = #{p_groupId} ORDER BY GROUP_ID ASC
	</select>
	
	<select id="getScheduleMappingDeviceGroupAuth" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		SELECT B.GROUP_ID, B.GROUP_NAME FROM MI_CDS_MAP_PROGRAM_DEVICE A, MI_DMS_INFO_GROUP B WHERE PROGRAM_ID = #{programId} AND A.DEVICE_GROUP_ID = B.GROUP_ID 
		<if test="userId != null">
		  AND A.DEVICE_GROUP_ID
		</if>
		<if test="include == false">
			NOT
		</if>
		<if test="userId != null">
		  IN (SELECT GROUP_ID FROM MI_DMS_MAP_GROUP_USER WHERE USER_ID = #{userId})
		</if>
	</select>
	
	<select id="getMessageMappingDeviceGroupAuth" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		SELECT B.GROUP_ID, B.GROUP_NAME FROM MI_CDS_MAP_MESSAGE_DEVICE A, MI_DMS_INFO_GROUP B WHERE MESSAGE_ID = #{programId} AND A.DEVICE_GROUP_ID = B.GROUP_ID
		<if test="userId != null">
		  AND A.DEVICE_GROUP_ID
		</if>
		<if test="include == false">
		  NOT
		</if>
		<if test="userId != null">
		  IN (SELECT GROUP_ID FROM MI_DMS_MAP_GROUP_USER WHERE USER_ID = #{userId})
		</if>
	</select>
	
	<select id="getEventMappingDeviceGroupAuth" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		SELECT B.GROUP_ID, B.GROUP_NAME FROM MI_EVENT_MAP_SCHEDULE_DEVICE A, MI_DMS_INFO_GROUP B WHERE SCHEDULE_ID = #{programId} AND A.DEVICE_GROUP_ID = B.GROUP_ID
		<if test="userId != null">
		  AND A.DEVICE_GROUP_ID
		</if>
		<if test="include == false">
		  NOT
		</if>
		<if test="userId != null">
		  IN (SELECT GROUP_ID FROM MI_DMS_MAP_GROUP_USER WHERE USER_ID = #{userId})
		</if>
	</select>
	
	<select id="getDeviceTypesMapGroup" resultType="String">
		SELECT DEVICE_TYPE FROM MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B 
		WHERE A.DEVICE_ID = B.DEVICE_ID AND B.GROUP_ID = #{groupId} AND A.DEVICE_TYPE != 'SIG_CHILD' AND A.DEVICE_TYPE != 'LED_CABINET' GROUP BY DEVICE_TYPE
	</select>
	
	<update id="setGroupTypeDefault">
		UPDATE MI_DMS_INFO_GROUP SET GROUP_TYPE = #{groupType} WHERE GROUP_ID = #{groupId}
	</update>
    
    <select id="selectDeviceTypeByGroupId" resultType="map">
        select
            device_type, device_type_version from mi_dms_info_device
        where
            device_id in (select device_id from mi_dms_map_group_device where group_id = #{groupId})
        group by device_type, device_type_version 
    </select>
    
    <select id="getAllGroupName" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
    	SELECT GROUP_ID, P_GROUP_ID, GROUP_NAME FROM MI_CDS_INFO_PROGRAM_GROUP WHERE P_GROUP_ID != -1
    </select>
    
     <select id="getGroupById" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
    	SELECT T.*, DEVICE_COUNT
		FROM
		(
		    SELECT GROUPS.*, 
		           (SELECT COUNT(*) 
		              FROM MI_DMS_MAP_GROUP_DEVICE DEVICES
		             WHERE GROUPS.GROUP_ID = DEVICES.GROUP_ID  AND DEVICES.DEVICE_ID NOT LIKE '%\\_%') AS DEVICE_COUNT 
		    FROM MI_DMS_INFO_GROUP GROUPS
		    WHERE GROUPS.P_GROUP_ID = #{id} AND GROUPS.GROUP_ID != 999999
		) AS T
		ORDER BY GROUP_NAME ASC
    </select>

	<select id="getGroupByIdWithPermission" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
    	SELECT T.*, DEVICE_COUNT
		FROM
		(
		    SELECT GROUPS.*,
		           (SELECT COUNT(*)
		              FROM MI_DMS_MAP_GROUP_DEVICE DEVICES
		             WHERE GROUPS.GROUP_ID = DEVICES.GROUP_ID  AND DEVICES.DEVICE_ID NOT LIKE '%\\_%') AS DEVICE_COUNT
		    FROM MI_DMS_INFO_GROUP GROUPS
			<if test="userId != null and userId != ''">
				, MI_DMS_MAP_GROUP_USER USER_MAP
			</if>
			WHERE
				GROUPS.P_GROUP_ID = #{id}
				AND GROUPS.GROUP_ID != 999999
				<if test="userId != null and userId != ''">
					AND GROUPS.GROUP_ID = USER_MAP.GROUP_ID
					AND USER_MAP.USER_ID = #{userId}
				</if>
		) AS T
		ORDER BY GROUP_NAME ASC
    </select>

	<select id="getGroupByUserGroup" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		SELECT T.*, DEVICE_COUNT
		FROM
		(
		    SELECT GROUPS.*,
		           (SELECT COUNT(*)
		              FROM MI_DMS_MAP_GROUP_DEVICE DEVICES
		             WHERE GROUPS.GROUP_ID = DEVICES.GROUP_ID  AND DEVICES.DEVICE_ID NOT LIKE '%\\_%') AS DEVICE_COUNT
		    FROM MI_DMS_INFO_GROUP GROUPS
		    WHERE GROUPS.GROUP_ID != 999999
			<if test="groupList != null and groupList.size() > 0">
				AND GROUPS.P_GROUP_ID = 0
				<foreach item="group" collection="groupList" open="AND (" separator=" OR " close=")">
					GROUPS.GROUP_NAME = #{group.group_name}
				</foreach>
			</if>

		) AS T
		ORDER BY GROUP_NAME ASC
	</select>
    
    <select id="getGroupById" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup" databaseId="mssql">
    	SELECT T.*, DEVICE_COUNT
		FROM
		(
		    SELECT GROUPS.*, 
		           (SELECT COUNT(*) 
		              FROM MI_DMS_MAP_GROUP_DEVICE DEVICES
		             WHERE GROUPS.GROUP_ID = DEVICES.GROUP_ID  AND DEVICES.DEVICE_ID NOT LIKE '%[_]%') AS DEVICE_COUNT 
		    FROM MI_DMS_INFO_GROUP GROUPS
		    WHERE GROUPS.P_GROUP_ID = #{id} AND GROUPS.GROUP_ID != 999999
		) AS T
		ORDER BY GROUP_NAME ASC
    </select>

	<select id="getGroupByUserGroup" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup" databaseId="mssql">
    	SELECT T.*, DEVICE_COUNT
		FROM
		(
		    SELECT GROUPS.*,
		           (SELECT COUNT(*)
		              FROM MI_DMS_MAP_GROUP_DEVICE DEVICES
		             WHERE GROUPS.GROUP_ID = DEVICES.GROUP_ID  AND DEVICES.DEVICE_ID NOT LIKE '%[_]%') AS DEVICE_COUNT
		    FROM MI_DMS_INFO_GROUP GROUPS
		    WHERE GROUPS.GROUP_ID != 999999
			<if test="groupList != null and groupList.size() > 0">
				AND GROUPS.P_GROUP_ID = 0
				<foreach item="group" collection="groupList" open="AND (" separator=" OR " close=")">
					GROUPS.GROUP_NAME = #{group.group_name}
				</foreach>
			</if>
		) AS T
		ORDER BY GROUP_NAME ASC
    </select>
    
    <select id="getAuthGroupById" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
    	SELECT *
		FROM MI_DMS_MAP_GROUP_USER USERS
		LEFT JOIN
		(
		    SELECT GROUPS.*, 
		           (SELECT COUNT(*) 
		              FROM MI_DMS_MAP_GROUP_DEVICE DEVICES
		             WHERE GROUPS.GROUP_ID = DEVICES.GROUP_ID  AND DEVICES.DEVICE_ID NOT LIKE '%\\_%') AS DEVICE_COUNT 
		    FROM MI_DMS_INFO_GROUP GROUPS
		
		) AS T
		ON USERS.GROUP_ID = T.GROUP_ID
		WHERE user_id = #{id}
		ORDER BY GROUP_NAME ASC
    </select>
    
    <select id="getAuthGroupById" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup" databaseId="mssql">
    	SELECT *
		FROM MI_DMS_MAP_GROUP_USER USERS
		LEFT JOIN
		(
		    SELECT GROUPS.*, 
		           (SELECT COUNT(*) 
		              FROM MI_DMS_MAP_GROUP_DEVICE DEVICES
		             WHERE GROUPS.GROUP_ID = DEVICES.GROUP_ID  AND DEVICES.DEVICE_ID NOT LIKE '%[_]%') AS DEVICE_COUNT 
		    FROM MI_DMS_INFO_GROUP GROUPS
		
		) AS T
		ON USERS.GROUP_ID = T.GROUP_ID
		WHERE user_id = #{id}
		ORDER BY GROUP_NAME ASC
    </select>
    
    <select id="getRootGroupById" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
    	SELECT MI_DMS_INFO_GROUP.GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, MIN_PRIORITY, CREATOR_ID, GROUP_TYPE, MI_DMS_MAP_GROUP_DEVICE.DEVICE_ID
    	FROM MI_DMS_INFO_GROUP
    	LEFT JOIN MI_DMS_MAP_GROUP_DEVICE ON MI_DMS_INFO_GROUP.GROUP_ID = MI_DMS_MAP_GROUP_DEVICE.GROUP_ID
    	WHERE MI_DMS_INFO_GROUP.GROUP_NAME = #{oranization} AND MI_DMS_INFO_GROUP.GROUP_ID != 999999 AND P_GROUP_ID = 0
    </select>
    
	<select id="getVwlGroupById" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.VwlGroup">
    	SELECT MI_DMS_INFO_VWL_GROUP.GROUP_ID, P_GROUP_ID, GROUP_DEPTH, MI_DMS_INFO_VWL_GROUP.CREATOR_ID, GROUP_TYPE, MI_DMS_MAP_VWL_GROUP_CONSOLE.DEVICE_ID AS CONSOLE_ID, MI_DMS_MAP_VWL_CONSOLE_DEVICE.DEVICE_ID, MI_DMS_INFO_VWL_CONSOLE_DEVICE.DEVICE_NAME AS GROUP_NAME, MI_DMS_INFO_VWL_CONSOLE_DEVICE.DEVICE_TYPE
    	FROM MI_DMS_INFO_VWL_GROUP
    	LEFT JOIN MI_DMS_MAP_VWL_GROUP_CONSOLE ON MI_DMS_INFO_VWL_GROUP.GROUP_ID = MI_DMS_MAP_VWL_GROUP_CONSOLE.GROUP_ID
		LEFT JOIN MI_DMS_MAP_VWL_CONSOLE_DEVICE ON MI_DMS_MAP_VWL_GROUP_CONSOLE.DEVICE_ID = MI_DMS_MAP_VWL_CONSOLE_DEVICE.CONSOLE_ID
		LEFT JOIN MI_DMS_INFO_VWL_CONSOLE_DEVICE ON MI_DMS_MAP_VWL_CONSOLE_DEVICE.CONSOLE_ID = MI_DMS_INFO_VWL_CONSOLE_DEVICE.DEVICE_ID
    	WHERE MI_DMS_INFO_VWL_GROUP.GROUP_ID = #{id} AND MI_DMS_INFO_VWL_GROUP.GROUP_ID != 999999 AND MI_DMS_INFO_VWL_CONSOLE_DEVICE.DEVICE_NAME != ' '
    </select>
    
    <select id="getVwlRootGroupById" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.VwlGroup">
    	SELECT DISTINCT ON (MI_DMS_INFO_VWL_GROUP.GROUP_ID) MI_DMS_INFO_VWL_GROUP.GROUP_ID, P_GROUP_ID, GROUP_DEPTH, MI_DMS_INFO_VWL_GROUP.CREATOR_ID, GROUP_TYPE, MI_DMS_MAP_VWL_GROUP_CONSOLE.GROUP_ID, group_name
    	FROM MI_DMS_INFO_VWL_GROUP
    	LEFT JOIN MI_DMS_MAP_VWL_GROUP_CONSOLE ON MI_DMS_INFO_VWL_GROUP.GROUP_ID = MI_DMS_MAP_VWL_GROUP_CONSOLE.GROUP_ID
		WHERE MI_DMS_INFO_VWL_GROUP.group_name =  #{oranization} AND MI_DMS_INFO_VWL_GROUP.GROUP_ID != 999999 AND P_GROUP_ID = 0
    </select>
    
    <select id="getVwlRootGroupById" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.VwlGroup" databaseId="mssql">
    	SELECT DISTINCT (MI_DMS_INFO_VWL_GROUP.GROUP_ID), MI_DMS_INFO_VWL_GROUP.GROUP_ID, P_GROUP_ID, GROUP_DEPTH, MI_DMS_INFO_VWL_GROUP.CREATOR_ID, GROUP_TYPE, MI_DMS_MAP_VWL_GROUP_CONSOLE.GROUP_ID, group_name
    	FROM MI_DMS_INFO_VWL_GROUP
    	LEFT JOIN MI_DMS_MAP_VWL_GROUP_CONSOLE ON MI_DMS_INFO_VWL_GROUP.GROUP_ID = MI_DMS_MAP_VWL_GROUP_CONSOLE.GROUP_ID
		WHERE MI_DMS_INFO_VWL_GROUP.group_name =  #{oranization} AND MI_DMS_INFO_VWL_GROUP.GROUP_ID != 999999 AND P_GROUP_ID = 0
    </select>
    
    <select id="getVwlRootGroupById" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.VwlGroup" databaseId="mysql">
    	SELECT DISTINCT MI_DMS_INFO_VWL_GROUP.GROUP_ID, MI_DMS_INFO_VWL_GROUP.GROUP_ID, P_GROUP_ID, GROUP_DEPTH, MI_DMS_INFO_VWL_GROUP.CREATOR_ID, GROUP_TYPE, MI_DMS_MAP_VWL_GROUP_CONSOLE.GROUP_ID, group_name
    	FROM MI_DMS_INFO_VWL_GROUP
    	LEFT JOIN MI_DMS_MAP_VWL_GROUP_CONSOLE ON MI_DMS_INFO_VWL_GROUP.GROUP_ID = MI_DMS_MAP_VWL_GROUP_CONSOLE.GROUP_ID
		WHERE MI_DMS_INFO_VWL_GROUP.group_name =  #{oranization} AND MI_DMS_INFO_VWL_GROUP.GROUP_ID != 999999 AND P_GROUP_ID = 0
    </select>
    
    <select id="getAdminVwlRootGroupById" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.VwlGroup">
    	SELECT DISTINCT ON (MI_DMS_INFO_VWL_GROUP.GROUP_ID) MI_DMS_INFO_VWL_GROUP.GROUP_ID, P_GROUP_ID, GROUP_DEPTH, MI_DMS_INFO_VWL_GROUP.CREATOR_ID, GROUP_TYPE, MI_DMS_MAP_VWL_GROUP_CONSOLE.GROUP_ID, group_name
    	FROM MI_DMS_INFO_VWL_GROUP
    	LEFT JOIN MI_DMS_MAP_VWL_GROUP_CONSOLE ON MI_DMS_INFO_VWL_GROUP.GROUP_ID = MI_DMS_MAP_VWL_GROUP_CONSOLE.GROUP_ID
    	WHERE MI_DMS_INFO_VWL_GROUP.P_GROUP_ID = 0 AND MI_DMS_INFO_VWL_GROUP.GROUP_ID != 999999
    </select>
    
        
    <select id="getAdminVwlRootGroupById" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.VwlGroup" databaseId="mssql">
    	SELECT DISTINCT (MI_DMS_INFO_VWL_GROUP.GROUP_ID), MI_DMS_INFO_VWL_GROUP.GROUP_ID, P_GROUP_ID, GROUP_DEPTH, MI_DMS_INFO_VWL_GROUP.CREATOR_ID, GROUP_TYPE, MI_DMS_MAP_VWL_GROUP_CONSOLE.GROUP_ID, group_name
    	FROM MI_DMS_INFO_VWL_GROUP
    	LEFT JOIN MI_DMS_MAP_VWL_GROUP_CONSOLE ON MI_DMS_INFO_VWL_GROUP.GROUP_ID = MI_DMS_MAP_VWL_GROUP_CONSOLE.GROUP_ID
    	WHERE MI_DMS_INFO_VWL_GROUP.P_GROUP_ID = 0 AND MI_DMS_INFO_VWL_GROUP.GROUP_ID != 999999
    </select>
    
        
    <select id="getAdminVwlRootGroupById" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.VwlGroup" databaseId="mysql">
    	SELECT DISTINCT MI_DMS_INFO_VWL_GROUP.GROUP_ID, MI_DMS_INFO_VWL_GROUP.GROUP_ID, P_GROUP_ID, GROUP_DEPTH, MI_DMS_INFO_VWL_GROUP.CREATOR_ID, GROUP_TYPE, MI_DMS_MAP_VWL_GROUP_CONSOLE.GROUP_ID, group_name
    	FROM MI_DMS_INFO_VWL_GROUP
    	LEFT JOIN MI_DMS_MAP_VWL_GROUP_CONSOLE ON MI_DMS_INFO_VWL_GROUP.GROUP_ID = MI_DMS_MAP_VWL_GROUP_CONSOLE.GROUP_ID
    	WHERE MI_DMS_INFO_VWL_GROUP.P_GROUP_ID = 0 AND MI_DMS_INFO_VWL_GROUP.GROUP_ID != 999999
    </select>
    
    <select id="getOrganizationCount" resultType="int">
    	<if test="table == 'PREMIUM'">
    		SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE WHERE ORGANIZATION IS NULL AND GROUP_ID != 999999
    	</if>
    	<if test="table == 'LITE'">
    		SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_LITE_GROUP_DEVICE WHERE ORGANIZATION IS NULL AND GROUP_ID != 999999
    	</if>
    	<if test="table == 'VIDEOWALL'">
    		SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_VWL_GROUP_CONSOLE WHERE ORGANIZATION IS NULL AND GROUP_ID != 999999
    	</if>
    </select>
    
    <select id="getOrganization" resultType="map">
    	<if test="table == 'PREMIUM'">
    		SELECT * FROM MI_DMS_MAP_GROUP_DEVICE WHERE ORGANIZATION IS NULL AND GROUP_ID != 999999
    	</if>
    	<if test="table == 'LITE'">
    		SELECT * FROM MI_DMS_MAP_LITE_GROUP_DEVICE WHERE ORGANIZATION IS NULL AND GROUP_ID != 999999
    	</if>
    	<if test="table == 'VIDEOWALL'">
    		SELECT * FROM MI_DMS_MAP_VWL_GROUP_CONSOLE WHERE ORGANIZATION IS NULL AND GROUP_ID != 999999
    	</if>
    </select>
    
    <update id="setOrganizationByDeviceId">
    	<if test="table == 'PREMIUM'">
    		UPDATE MI_DMS_MAP_GROUP_DEVICE SET
    		ORGANIZATION = #{organization}
    		WHERE DEVICE_ID = #{deviceId}
    	</if>
    	<if test="table == 'LITE'">
    		UPDATE MI_DMS_MAP_LITE_GROUP_DEVICE SET
    		ORGANIZATION = #{organization}
    		WHERE DEVICE_ID = #{deviceId}
    	</if>
    	<if test="table == 'VIDEOWALL'">
    		UPDATE MI_DMS_MAP_VWL_GROUP_CONSOLE SET
    		ORGANIZATION = #{organization}
    		WHERE DEVICE_ID = #{deviceId}
    	</if>
    </update>
    
    <select id="getChildGroupIdLists" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
    	SELECT T.*, DEVICE_COUNT
		FROM
		(
		    SELECT GROUPS.*, 
		           (SELECT COUNT(*) 
		              FROM MI_DMS_MAP_GROUP_DEVICE DEVICES
		             WHERE GROUPS.GROUP_ID = DEVICES.GROUP_ID) AS DEVICE_COUNT 
		    FROM MI_DMS_INFO_GROUP GROUPS
		    WHERE GROUPS.P_GROUP_ID = #{group_id} AND GROUPS.GROUP_ID != #{nonApprovalGroupId}
		) AS T
		ORDER BY GROUP_NAME ASC
    </select>
    
    <select id="getDynamicChildGroupIdList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
    	<choose>
			<when test="deviceType == 'ALL' or deviceType == 'all'">
				SELECT * FROM MI_DMS_INFO_GROUP WHERE P_GROUP_ID = #{group_id} AND GROUP_ID != #{nonApprovalGroupId} ORDER BY GROUP_NAME DESC
			</when>
			<otherwise>
				SELECT * FROM MI_DMS_INFO_GROUP WHERE P_GROUP_ID = #{group_id} AND GROUP_ID != #{nonApprovalGroupId} AND (GROUP_TYPE = '' OR GROUP_TYPE = #{deviceType}) ORDER BY GROUP_NAME DESC
			</otherwise>
		</choose>
    </select>
    
    <select id="getDeviceAuthor" resultType="Boolean">
		SELECT COUNT(GROUP_ID) FROM MI_DMS_MAP_GROUP_USER WHERE GROUP_ID = #{groupId} AND USER_ID = #{userId} 
	</select>
	
	<select id="checkIsSyncGroup" resultType="int">
		SELECT COUNT(A.DEVICE_GROUP_ID) FROM MI_CDS_MAP_PROGRAM_DEVICE A, MI_CDS_INFO_PROGRAM B WHERE A.PROGRAM_ID=B.PROGRAM_ID AND B.USE_SYNC_PLAY='Y' AND A.DEVICE_GROUP_ID = #{devGroupId} 
	</select>
    
    <select id="groupDepthCount" resultType="int">
		SELECT COUNT(GROUP_ID) FROM MI_DMS_INFO_GROUP WHERE GROUP_DEPTH = #{depth}
	</select>
	
	<select id="getDeviceTotalCountLists" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		SELECT * FROM MI_DMS_INFO_GROUP WHERE GROUP_DEPTH = #{depth} AND GROUP_ID != 999999 
		ORDER BY GROUP_ID ASC
		LIMIT #{pageSize} OFFSET #{startPos}
	</select>
	
	<select id="getDeviceTotalCountLists" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup" databaseId="mssql">
		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
		SELECT * FROM (
			SELECT *, ROW_NUMBER() OVER (ORDER BY GROUP_ID ASC) as rownum FROM MI_DMS_INFO_GROUP WHERE GROUP_DEPTH = #{depth} AND GROUP_ID != 999999 
		) as SubQuery
		WHERE rownum > ${startPos} and rownum &lt;= ${startPos + pageSize}
	    ORDER BY rownum	   
	</select>
	
	<select id="getChildrenGroupCount" resultType="int">
		SELECT COUNT(GROUP_ID) FROM MI_DMS_INFO_GROUP WHERE P_GROUP_ID = #{groupId}
	</select>
	
	<update id="updateDeviceTotalCount">
		UPDATE MI_DMS_INFO_GROUP SET TOTAL_COUNT = #{totalCount} WHERE GROUP_ID = #{groupId}		
	</update>
	
	<select id="getChildrenSum" resultType="map">
		SELECT SUM(TOTAL_COUNT) as sum FROM MI_DMS_INFO_GROUP WHERE P_GROUP_ID = #{groupId}
	</select>
	
	<select id="getDeviceModelName" resultType="map">
		SELECT DEVICE.DEVICE_MODEL_NAME
        FROM MI_DMS_MAP_GROUP_DEVICE AS DEVICE_GROUP
        LEFT JOIN MI_DMS_INFO_DEVICE AS DEVICE ON DEVICE_GROUP.DEVICE_ID = DEVICE.DEVICE_ID
        WHERE GROUP_ID = #{groupId}
	</select>
	
	<select id="getModelCountInfo" resultType="String">
		SELECT VWT.MODEL_COUNT_INFO
		FROM MI_DMS_INFO_GROUP GROUPS
		LEFT JOIN MI_DMS_INFO_VWT VWT ON GROUPS.VWT_ID = VWT.VWT_ID
		WHERE GROUP_ID = #{groupId}
	</select>
	
	<select id="getDiskSpaceRepository" resultType="long">
		SELECT DISK_SPACE_REPOSITORY 
		FROM MI_DMS_INFO_DEVICE DEVICE
		LEFT JOIN MI_DMS_MAP_GROUP_DEVICE GROUPS ON DEVICE.DEVICE_ID = GROUPS.DEVICE_ID
		WHERE
		GROUP_ID IN
        <foreach item="groupId" collection="groupIds" open="(" separator="," close=")">
            #{groupId}
        </foreach>
		ORDER BY DISK_SPACE_REPOSITORY ASC
		LIMIT 1
	</select>
	
	<select id="getDiskSpaceRepository" resultType="long" databaseId="mssql">
		SELECT TOP (1) DISK_SPACE_REPOSITORY 
		FROM MI_DMS_INFO_DEVICE DEVICE
		LEFT JOIN MI_DMS_MAP_GROUP_DEVICE GROUPS ON DEVICE.DEVICE_ID = GROUPS.DEVICE_ID
		WHERE
		GROUP_ID IN
        <foreach item="groupId" collection="groupIds" open="(" separator="," close=")">
            #{groupId}
        </foreach>
		ORDER BY DISK_SPACE_REPOSITORY DESC
	</select>
	
	<select  id="getAllDeviceGroups" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		<choose>
			<when test="groupId != null and groupId == 0">
				WITH RECURSIVE GROUP_IDS AS (
				SELECT *
				FROM MI_DMS_INFO_GROUP
				WHERE GROUP_ID > 0
				UNION ALL
				SELECT CHILD_GROUP.*
				FROM MI_DMS_INFO_GROUP CHILD_GROUP
				JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
				)
				SELECT DISTINCT *, (SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = GROUP_IDS.GROUP_ID) AS DEVICE_COUNT
				FROM GROUP_IDS WHERE GROUP_ID != 0 AND GROUP_ID != 999999
				<if test="groupName != null and !groupName.equals('')">
					AND GROUP_NAME LIKE '%'<include refid="utils.concatenate"/>#{groupName}<include refid="utils.concatenate"/>'%'
				</if>
				ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC
			</when>
			<otherwise>
				WITH RECURSIVE GROUP_IDS AS (
				SELECT *
				FROM MI_DMS_INFO_GROUP
				WHERE P_GROUP_ID = 0 AND GROUP_ID = #{groupId}
				UNION ALL
				SELECT CHILD_GROUP.*
				FROM MI_DMS_INFO_GROUP CHILD_GROUP
				JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
				)
				SELECT *, (SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = GROUP_IDS.GROUP_ID) AS DEVICE_COUNT
				<if test="groupName != null and !groupName.equals('')">
					WHERE GROUP_NAME LIKE '%'<include refid="utils.concatenate"/>#{groupName}<include refid="utils.concatenate"/>'%'
				</if>
				FROM GROUP_IDS
				ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC
			</otherwise>
		</choose>
	</select>

	<select  id="getAllDeviceGroupsWithPermission" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		<choose>
			<when test="groupId != null and groupId == 0">
				WITH RECURSIVE GROUP_IDS AS (
				SELECT *
				FROM MI_DMS_INFO_GROUP
				WHERE GROUP_ID > 0
				UNION ALL
				SELECT CHILD_GROUP.*
				FROM MI_DMS_INFO_GROUP CHILD_GROUP
				JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
				)
				SELECT DISTINCT *, (SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = GROUP_IDS.GROUP_ID) AS DEVICE_COUNT
				FROM GROUP_IDS WHERE GROUP_ID != 0 AND GROUP_ID != 999999
				ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC
			</when>
			<otherwise>
				WITH RECURSIVE GROUP_IDS AS (
				SELECT *
				FROM MI_DMS_INFO_GROUP
				WHERE P_GROUP_ID = 0 AND GROUP_ID = #{groupId}
				UNION ALL
				SELECT CHILD_GROUP.*
				FROM MI_DMS_INFO_GROUP CHILD_GROUP
				JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
				)
				SELECT GROUP_IDS.*, (SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = GROUP_IDS.GROUP_ID) AS DEVICE_COUNT
				FROM GROUP_IDS, MI_DMS_MAP_GROUP_USER USER_MAP
				WHERE
				GROUP_IDS.GROUP_ID = USER_MAP.GROUP_ID
				AND USER_MAP.USER_ID = #{userId}
				UNION ALL
				SELECT CHILD_GROUP.*, (SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = CHILD_GROUP.GROUP_ID) AS DEVICE_COUNT
				FROM GROUP_IDS CHILD_GROUP
				WHERE CHILD_GROUP.P_GROUP_ID = 0
				ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC
			</otherwise>
		</choose>
	</select>

	<select  id="getAllDeviceGroupsWithGroupIds" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		WITH RECURSIVE GROUP_IDS AS (
		SELECT *
		FROM MI_DMS_INFO_GROUP
		WHERE P_GROUP_ID = 0 AND
		<choose>
			<when test="groupIds != null and groupIds.size() > 0">
				<foreach item="groupId" collection="groupIds" open="(" separator=" OR " close=")">
					GROUP_ID = #{groupId}
				</foreach>
			</when>
			<otherwise>
				GROUP_ID = #{groupId}
			</otherwise>
		</choose>
		UNION ALL
		SELECT CHILD_GROUP.*
		FROM MI_DMS_INFO_GROUP CHILD_GROUP
		JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
		)
		SELECT *, (SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = GROUP_IDS.GROUP_ID) AS DEVICE_COUNT
		FROM GROUP_IDS
		<if test="groupName != null and !groupName.equals('')">
			WHERE GROUP_NAME LIKE '%'<include refid="utils.concatenate"/>#{str}<include refid="utils.concatenate"/>'%'
		</if>
		ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC
	</select>

	<select id="getAllDGroups" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		SELECT COUNT(DEVICES.DEVICE_ID) AS DEVICE_COUNT, GROUPS.* FROM MI_DMS_INFO_GROUP GROUPS
			LEFT JOIN (SELECT MAPS.GROUP_ID, MAPS.DEVICE_ID FROM MI_DMS_MAP_GROUP_DEVICE MAPS LEFT JOIN MI_DMS_INFO_DEVICE TEMP_DEVICES ON MAPS.DEVICE_ID = TEMP_DEVICES.DEVICE_ID WHERE TEMP_DEVICES.IS_CHILD = <include refid="utils.false"/>)
				AS DEVICES ON GROUPS.GROUP_ID = DEVICES.GROUP_ID
		WHERE GROUPS.GROUP_ID != 999999
		GROUP BY GROUPS.GROUP_ID, GROUPS.P_GROUP_ID, GROUPS.GROUP_DEPTH, GROUPS.GROUP_NAME, GROUPS.DESCRIPTION, GROUPS.IS_ROOT, GROUPS.GROUP_TYPE, GROUPS.CREATOR_ID, GROUPS.DEFAULT_PROGRAM_ID, GROUPS.CREATE_DATE, GROUPS.IS_REDUNDANCY, GROUPS.VWT_ID, GROUPS.TOTAL_COUNT, GROUPS.MIN_PRIORITY, GROUPS.NOC_UPDATE, GROUPS.ANALYSIS_STATUS
	</select>
	
	<select  id="getAllDeviceGroups" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup" databaseId="mssql">
		<choose>
			<when test="groupId != null and groupId == 0">
				WITH GROUP_IDS AS (
				SELECT *
				FROM MI_DMS_INFO_GROUP
				WHERE GROUP_ID > 0
				UNION ALL
				SELECT CHILD_GROUP.*
				FROM MI_DMS_INFO_GROUP CHILD_GROUP
				JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
				)
				SELECT DISTINCT *, (SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = GROUP_IDS.GROUP_ID) AS DEVICE_COUNT
				FROM GROUP_IDS WHERE GROUP_ID != 0 AND GROUP_ID != 999999
				ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC
			</when>
			<otherwise>
				WITH GROUP_IDS AS (
				SELECT *
				FROM MI_DMS_INFO_GROUP
				WHERE P_GROUP_ID = 0 AND GROUP_ID = #{groupId}
				UNION ALL
				SELECT CHILD_GROUP.*
				FROM MI_DMS_INFO_GROUP CHILD_GROUP
				JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
				)
				SELECT *, (SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = GROUP_IDS.GROUP_ID) AS DEVICE_COUNT
				FROM GROUP_IDS
				ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC
			</otherwise>
		</choose>
	</select>

	<select  id="getAllDeviceGroupsWithPermission" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup" databaseId="mssql">
		<choose>
			<when test="groupId != null and groupId == 0">
				WITH GROUP_IDS AS (
				SELECT *
				FROM MI_DMS_INFO_GROUP
				WHERE GROUP_ID > 0
				UNION ALL
				SELECT CHILD_GROUP.*
				FROM MI_DMS_INFO_GROUP CHILD_GROUP
				JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
				)
				SELECT DISTINCT *, (SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = GROUP_IDS.GROUP_ID) AS DEVICE_COUNT
				FROM GROUP_IDS WHERE GROUP_ID != 0 AND GROUP_ID != 999999
				ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC
			</when>
			<otherwise>
				WITH GROUP_IDS AS (
				SELECT *
				FROM MI_DMS_INFO_GROUP
				WHERE P_GROUP_ID = 0 AND GROUP_ID = #{groupId}
				UNION ALL
				SELECT CHILD_GROUP.*
				FROM MI_DMS_INFO_GROUP CHILD_GROUP
				JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
				)
				SELECT GROUP_IDS.*, (SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = GROUP_IDS.GROUP_ID) AS DEVICE_COUNT
				FROM GROUP_IDS, MI_DMS_MAP_GROUP_USER USER_MAP
				WHERE
				GROUP_IDS.GROUP_ID = USER_MAP.GROUP_ID
				AND USER_MAP.USER_ID = #{userId}
				UNION ALL
				SELECT CHILD_GROUP.*, (SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = CHILD_GROUP.GROUP_ID) AS DEVICE_COUNT
				FROM GROUP_IDS CHILD_GROUP
				WHERE CHILD_GROUP.P_GROUP_ID = 0
				ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC
			</otherwise>
		</choose>
	</select>

	<select  id="getAllDeviceGroupsWithGroupIds" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup" databaseId="mssql">

		WITH GROUP_IDS AS (
		SELECT *
		FROM MI_DMS_INFO_GROUP
		WHERE P_GROUP_ID = 0 AND
		<choose>
			<when test="groupIds != null and groupIds.size() > 0">
				<foreach item="groupId" collection="groupIds" open="(" separator=" OR " close=")">
					GROUP_ID = #{groupId}
				</foreach>
			</when>
			<otherwise>
				GROUP_ID = #{groupId}
			</otherwise>
		</choose>
		UNION ALL
		SELECT CHILD_GROUP.*
		FROM MI_DMS_INFO_GROUP CHILD_GROUP
		JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
		)
		SELECT *, (SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = GROUP_IDS.GROUP_ID) AS DEVICE_COUNT
		FROM GROUP_IDS
		<if test="groupName != null and !groupName.equals('')">
			WHERE GROUP_NAME LIKE '%'<include refid="utils.concatenate"/>#{groupName}<include refid="utils.concatenate"/>'%'
		</if>
		ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC

	</select>

	<select id="getUnapprovedDeviceCountByUser" resultType="int">
		SELECT COUNT(*) FROM MI_DMS_INFO_DEVICE WHERE IS_APPROVED = <include refid="utils.false"/> AND UNAPPROVED_GROUP_CODE != -1
		AND UNAPPROVED_GROUP_CODE IN
		<foreach item="groupId" collection="groupIds" open="(" separator="," close=")">
			#{groupId}
		</foreach>
	</select>

	<select  id="getAllAuthorityDeviceGroups" resultType="long" databaseId="mssql">
		SELECT USERS.GROUP_ID
		FROM MI_DMS_MAP_GROUP_USER USERS
		LEFT JOIN
		(
		    SELECT GROUPS.*, 
		           (SELECT COUNT(*) 
		              FROM MI_DMS_MAP_GROUP_DEVICE DEVICES
		             WHERE GROUPS.GROUP_ID = DEVICES.GROUP_ID  AND DEVICES.DEVICE_ID NOT LIKE '%[_]%') AS DEVICE_COUNT 
		    FROM MI_DMS_INFO_GROUP GROUPS
		) AS T
		ON USERS.GROUP_ID = T.GROUP_ID
		WHERE USER_ID = #{userId}
		ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC
	</select>
	
	<select  id="getAllAuthorityDeviceGroups" resultType="long" >
		SELECT USERS.GROUP_ID
		FROM MI_DMS_MAP_GROUP_USER USERS
		LEFT JOIN
		(
		    SELECT GROUPS.*, 
		           (SELECT COUNT(*) 
		              FROM MI_DMS_MAP_GROUP_DEVICE DEVICES
		             WHERE GROUPS.GROUP_ID = DEVICES.GROUP_ID  AND DEVICES.DEVICE_ID NOT LIKE '%\\_%') AS DEVICE_COUNT 
		    FROM MI_DMS_INFO_GROUP GROUPS
		) AS T
		ON USERS.GROUP_ID = T.GROUP_ID
		WHERE USER_ID = #{userId}
		ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC
	</select>
	
		<select id="getParentGroupNamePathByGroupId" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		 WITH RECURSIVE GROUP_IDS AS (
    		SELECT GROUPS.*
    		FROM MI_DMS_INFO_GROUP GROUPS
    		WHERE GROUP_ID = #{groupId}
    		UNION ALL
    		SELECT PARENT_GROUP.*
    		FROM MI_DMS_INFO_GROUP PARENT_GROUP
    		JOIN GROUP_IDS ON PARENT_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
    	)
    	SELECT GROUP_IDS.* FROM GROUP_IDS
		WHERE P_GROUP_ID > -1
		ORDER BY GROUP_DEPTH ASC
	</select>
	
	<select id="getParentGroupNamePathByGroupId" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup" databaseId="mssql">
        WITH GROUP_IDS AS (
    		SELECT GROUPS.*
    		FROM MI_DMS_INFO_GROUP GROUPS
    		WHERE GROUP_ID = #{groupId}
    		UNION ALL
    		SELECT PARENT_GROUP.*
    		FROM MI_DMS_INFO_GROUP PARENT_GROUP
    		JOIN GROUP_IDS ON PARENT_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
    	)
    	SELECT GROUP_IDS.* FROM GROUP_IDS
		WHERE P_GROUP_ID > -1
		ORDER BY GROUP_DEPTH ASC
	</select>

	<select id="getParentOrgNameByGroupId" resultType="string">
		WITH RECURSIVE GROUP_IDS AS (
		SELECT GROUPS.*
		FROM MI_DMS_INFO_GROUP GROUPS
		WHERE GROUP_ID = #{groupId}
		UNION ALL
		SELECT PARENT_GROUP.*
		FROM MI_DMS_INFO_GROUP PARENT_GROUP
		JOIN GROUP_IDS ON PARENT_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
		)
		SELECT GROUP_IDS.GROUP_NAME FROM GROUP_IDS
		WHERE P_GROUP_ID = 0
		ORDER BY GROUP_DEPTH ASC
	</select>

	<select id="getParentOrgNameByGroupId" resultType="string" databaseId="mssql">
		WITH GROUP_IDS AS (
		SELECT GROUPS.*
		FROM MI_DMS_INFO_GROUP GROUPS
		WHERE GROUP_ID = #{groupId}
		UNION ALL
		SELECT PARENT_GROUP.*
		FROM MI_DMS_INFO_GROUP PARENT_GROUP
		JOIN GROUP_IDS ON PARENT_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
		)
		SELECT GROUP_IDS.GROUP_NAME FROM GROUP_IDS
		WHERE P_GROUP_ID = 0
		ORDER BY GROUP_DEPTH ASC
	</select>

	<update id="changeDeviceOrgName">
		UPDATE MI_DMS_MAP_GROUP_DEVICE SET ORGANIZATION = #{name}
		WHERE ORGANIZATION = #{oldName}
	</update>
	
		<select id= "getCntAnalysisDeviceGroup" resultType="int">
		SELECT COUNT(GROUP_ID) FROM MI_DMS_INFO_GROUP WHERE ANALYSIS_STATUS = <include refid="utils.true"/> 
	</select>
	
	<update id="setAnalysisDeviceGroup">
		UPDATE MI_DMS_INFO_GROUP SET ANALYSIS_STATUS = #{value}
		WHERE GROUP_ID = #{groupId}
	</update>
	
	<select id="getDeviceTypeByGroupId" resultType="string">
		SELECT GROUP_TYPE
		FROM
			MI_DMS_INFO_GROUP
		WHERE
			GROUP_ID = #{groupId}
	</select>

	<update id="setTotalCountByGroupIdWithRecursive">
		WITH RECURSIVE GROUP_IDS AS (
			SELECT *
			FROM MI_DMS_INFO_GROUP
			WHERE GROUP_ID = (SELECT P_GROUP_ID FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = ${groupId})
			UNION ALL
			SELECT P_GROUP.*
			FROM MI_DMS_INFO_GROUP P_GROUP
				JOIN GROUP_IDS ON P_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
			WHERE P_GROUP.GROUP_ID != 0
		)
		UPDATE MI_DMS_INFO_GROUP GROUPS
		SET TOTAL_COUNT = GROUPS.TOTAL_COUNT <choose><when test="command != null and command.equals('INCREMENT')">+</when><when test="command != null and command.equals('DECREMENT')">-</when></choose> (SELECT TOTAL_COUNT FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = ${groupId})
		FROM GROUP_IDS
		WHERE GROUP_IDS.GROUP_ID = GROUPS.GROUP_ID
	</update>

	<update id="updateTotalDeviceCountOfParentGroupsRecursively">
		WITH RECURSIVE GROUP_IDS AS (
		SELECT *
		FROM MI_DMS_INFO_GROUP
		WHERE GROUP_ID = (SELECT P_GROUP_ID FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = ${groupId})
		UNION ALL
		SELECT P_GROUP.*
		FROM MI_DMS_INFO_GROUP P_GROUP
		JOIN GROUP_IDS ON P_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
		WHERE P_GROUP.GROUP_ID != 0
		)
		UPDATE MI_DMS_INFO_GROUP GROUPS
		SET TOTAL_COUNT = GROUPS.TOTAL_COUNT + ${count}
		FROM GROUP_IDS
		WHERE GROUP_IDS.GROUP_ID = GROUPS.GROUP_ID
	</update>

	<update id="setTotalCountByGroupIdWithRecursive" databaseId="mssql">
		WITH GROUP_IDS AS (
			SELECT *
			FROM MI_DMS_INFO_GROUP GROUPS
			WHERE GROUP_ID = (SELECT P_GROUP_ID FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = ${groupId})
			UNION ALL
			SELECT P_GROUP.*
			FROM MI_DMS_INFO_GROUP P_GROUP
			JOIN GROUP_IDS ON P_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
			WHERE P_GROUP.GROUP_ID != 0
		)
		UPDATE MI_DMS_INFO_GROUP
		SET MI_DMS_INFO_GROUP.TOTAL_COUNT = MI_DMS_INFO_GROUP.TOTAL_COUNT <choose><when test="command != null and command.equals('INCREMENT')">+</when><when test="command != null and command.equals('DECREMENT')">-</when></choose> (SELECT TOTAL_COUNT FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = ${groupId})
		FROM GROUP_IDS
		WHERE MI_DMS_INFO_GROUP.GROUP_ID = GROUP_IDS.GROUP_ID
	</update>
	<update id="updateTotalDeviceCountOfParentGroupsRecursively" databaseId="mssql">
		WITH GROUP_IDS AS (
		SELECT *
		FROM MI_DMS_INFO_GROUP GROUPS
		WHERE GROUP_ID = (SELECT P_GROUP_ID FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = ${groupId})
		UNION ALL
		SELECT P_GROUP.*
		FROM MI_DMS_INFO_GROUP P_GROUP
		JOIN GROUP_IDS ON P_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
		WHERE P_GROUP.GROUP_ID != 0
		)
		UPDATE MI_DMS_INFO_GROUP
		SET MI_DMS_INFO_GROUP.TOTAL_COUNT = MI_DMS_INFO_GROUP.TOTAL_COUNT + ${count}
		FROM GROUP_IDS
		WHERE MI_DMS_INFO_GROUP.GROUP_ID = GROUP_IDS.GROUP_ID
	</update>

	<update id="addTotalCountByGroupId">
		UPDATE MI_DMS_INFO_GROUP
		SET TOTAL_COUNT = ${count} + (SELECT TOTAL_COUNT FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = ${groupId})
		WHERE GROUP_ID = ${groupId}
	</update>

	<!-- [2019.11.21 KSY
	<update id="updateOrganizationByGroupId">
		UPDATE MI_DMS_MAP_GROUP_DEVICE
		SET ORGANIZATION = #{organization}
		WHERE GROUP_ID = #{groupId}
	</update>
	-->

	<update id="updateOrganizationByGroupId">
        WITH RECURSIVE DEVICE_GROUP AS (
		        SELECT
		            GROUP_ID
		        FROM MI_DMS_INFO_GROUP A
		        WHERE GROUP_ID = #{groupId}
		    UNION ALL
		        SELECT G.GROUP_ID
		        FROM MI_DMS_INFO_GROUP G JOIN DEVICE_GROUP
		        ON G.P_GROUP_ID = DEVICE_GROUP.GROUP_ID
		)
		update MI_DMS_MAP_GROUP_DEVICE
		set ORGANIZATION = #{organization}
		where GROUP_ID in (select group_id from DEVICE_GROUP )
	</update>

	<update id="updateOrganizationByGroupId" databaseId="mssql">
        WITH DEVICE_GROUP AS (
                SELECT
                    GROUP_ID
                FROM MI_DMS_INFO_GROUP A
                WHERE GROUP_ID = #{groupId}
            UNION ALL
                SELECT G.GROUP_ID
                FROM MI_DMS_INFO_GROUP G JOIN DEVICE_GROUP
                ON G.P_GROUP_ID = DEVICE_GROUP.GROUP_ID
        )
        update MI_DMS_MAP_GROUP_DEVICE
		set ORGANIZATION = #{organization}
		where GROUP_ID in (select group_id from DEVICE_GROUP )
	</update>

	<select id="getDeviceGroupList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		SELECT * FROM MI_DMS_INFO_GROUP
			WHERE GROUP_ID != 999999
			<if test="condition.parentGroupId != null">
				AND P_GROUP_ID = #{condition.parentGroupId}
			</if>
			<if test="skipIds != null and skipIds.size() > 0">
				AND GROUP_ID NOT IN
				<foreach item="skipId" index="index" collection="skipIds" open="(" separator="," close=")">
					#{skipId}
  				</foreach>
			</if>
	</select>	
	
	<insert id="insertAlarmDeviceGroup">
    	INSERT INTO MI_DMS_MAP_ALARM_GROUP ( ORGANIZATION_ID, organization, GROUP_ID ) VALUES (#{organizationId}, #{orgName}, #{groupId} ) 
	</insert>	
	
	<delete id="deleteAlarmDeviceGroup">
		DELETE FROM MI_DMS_MAP_ALARM_GROUP WHERE ORGANIZATION_ID = #{organizationId}
	</delete>			
	
	<select id="getAlarmDeviceGroupByName" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		SELECT ORGANIZATION_ID, ORGANIZATION, GROUP_ID FROM MI_DMS_MAP_ALARM_GROUP WHERE ORGANIZATION = #{orgName}
	</select>
	
	<select id="getAlarmDeviceGroup" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		SELECT ORGANIZATION_ID, ORGANIZATION, GROUP_ID FROM MI_DMS_MAP_ALARM_GROUP WHERE ORGANIZATION_ID = #{organizationId}
	</select>
	
	<select id="getDeviceOrgGroupByUserOrgId"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		SELECT GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, DESCRIPTION, IS_ROOT,
		GROUP_TYPE, CREATOR_ID, CREATE_DATE
		FROM MI_DMS_INFO_GROUP		
		WHERE GROUP_DEPTH = 1
		AND  GROUP_NAME =  (SELECT  GROUP_NAME
								        FROM MI_USER_INFO_GROUP        
								        WHERE GROUP_DEPTH = 1
								        AND    GROUP_ID = #{userOrgId}
									    )	
	</select>
	
	<select id="getGroupDeviceListByOrganName" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
		select   A.* , ORGANIZATION, B.DEVICE_ID
		FROM MI_DMS_INFO_GROUP A, MI_DMS_MAP_GROUP_DEVICE B 
		WHERE A.GROUP_ID = B.GROUP_ID 
		AND ORGANIZATION = #{organName}
	</select>
	
	<select id="getCountByUserIdAndGroupId" resultType="java.lang.Integer">
        SELECT COUNT(GROUP_ID)
        FROM MI_DMS_MAP_GROUP_USER
        WHERE GROUP_ID = #{groupId} AND USER_ID = #{userId}
    </select>
    
        <select id="V2GetChildDeviceIdListRecursive" resultType="map">
        WITH RECURSIVE DEVICE_GROUP AS (
                SELECT 
                    GROUP_ID
                FROM MI_DMS_INFO_GROUP A
                WHERE GROUP_ID = #{groupId}
            UNION ALL
                SELECT G.GROUP_ID
                FROM MI_DMS_INFO_GROUP G JOIN DEVICE_GROUP
                ON G.P_GROUP_ID = DEVICE_GROUP.GROUP_ID
        )
        SELECT * FROM MI_DMS_MAP_GROUP_DEVICE 
        WHERE 
            GROUP_ID IN (SELECT GROUP_ID FROM DEVICE_GROUP)
            AND DEVICE_ID NOT LIKE '%\_%' 
            AND GROUP_ID != 999999
    </select>

    <select id="V2GetChildDeviceIdListRecursive" resultType="map" databaseId="mssql">
        WITH DEVICE_GROUP AS (
                SELECT 
                    GROUP_ID
                FROM MI_DMS_INFO_GROUP A
                WHERE GROUP_ID = #{groupId}
            UNION ALL
                SELECT G.GROUP_ID
                FROM MI_DMS_INFO_GROUP G JOIN DEVICE_GROUP
                ON G.P_GROUP_ID = DEVICE_GROUP.GROUP_ID
        )
        SELECT * FROM MI_DMS_MAP_GROUP_DEVICE 
        WHERE 
            GROUP_ID IN (SELECT GROUP_ID FROM DEVICE_GROUP)
            AND CHARINDEX('_', DEVICE_ID) = 0
            AND GROUP_ID != 999999
    </select>

	<select id="getDeviceIdListByDeviceGroupPermission" resultType="Map">
		SELECT DEVICE_ID, USER_ID FROM MI_DMS_MAP_GROUP_USER U
		LEFT JOIN MI_DMS_MAP_GROUP_DEVICE D
		ON U.GROUP_ID = D.GROUP_ID
		WHERE DEVICE_ID IS NOT NULL AND U.USER_ID = #{userId}
	</select>


	<select id="getDeviceGroupTotalCount" resultType="int">
        SELECT COUNT(GROUP_ID) FROM MI_DMS_INFO_GROUP WHERE P_GROUP_ID > 0
	</select>

	<select id="getDeviceInDeviceGroup" resultType="String">
		SELECT A.DEVICE_ID
		FROM MI_DMS_MAP_GROUP_DEVICE A, MI_DMS_INFO_DEVICE B
		WHERE GROUP_ID = #{groupId}
		AND A.DEVICE_ID = B.DEVICE_ID AND B.IS_CHILD = <include refid="utils.false"/>
	</select>
	<select id="getCntDeviceInProgram" resultType="int">
		SELECT count(*) FROM MI_CDS_MAP_PROGRAM_DEVICE A
		JOIN MI_DMS_MAP_GROUP_DEVICE B on A.DEVICE_GROUP_ID = B.GROUP_ID
		WHERE PROGRAM_ID = #{programId}
	</select>
</mapper>
