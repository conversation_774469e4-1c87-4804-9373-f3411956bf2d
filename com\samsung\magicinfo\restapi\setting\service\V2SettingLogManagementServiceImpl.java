package com.samsung.magicinfo.restapi.setting.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.FileUtils;
import com.samsung.common.utils.RoleUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.framework.content.manager.LogInfo;
import com.samsung.magicinfo.framework.content.manager.LogInfoImpl;
import com.samsung.magicinfo.framework.scheduler.entity.SelectConditionScheduleAdmin;
import com.samsung.magicinfo.framework.setup.entity.NotificationHistoryEntity;
import com.samsung.magicinfo.framework.setup.entity.SystemLogEntity;
import com.samsung.magicinfo.framework.setup.manager.NotificationHistoryInfo;
import com.samsung.magicinfo.framework.setup.manager.NotificationHistoryInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.restapi.common.model.V2ListQueryFilter;
import com.samsung.magicinfo.restapi.setting.model.V2SettingLogFilter;
import com.samsung.magicinfo.restapi.utils.ServerSystemLogEventTypeUtils;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.service.statistics.DeviceStatisticsDownloadService;
import java.io.File;
import java.io.FileOutputStream;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.zip.ZipOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;
import org.springframework.context.NoSuchMessageException;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2SettingLogManagementService")
@Transactional
public class V2SettingLogManagementServiceImpl implements V2SettingLogManagementService {
   protected Logger logger = LoggingManagerV2.getLogger(V2SettingLogManagementServiceImpl.class);

   public V2SettingLogManagementServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public V2PageResource getServerLogs(V2SettingLogFilter filter) throws NumberFormatException, NoSuchMessageException, Exception {
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      Locale locale = SecurityUtils.getLocale();
      String filterType = filter.getFilterType();
      if (filterType.equalsIgnoreCase("none") || filterType.equalsIgnoreCase("")) {
         filterType = "ALL";
      }

      int results = filter.getPageSize();
      String searchText = null;
      String strRootGroup = "ROOT";
      User user = SecurityUtils.getUserContainer().getUser();
      if (!StrUtils.nvl(filter.getSearchText()).equals("")) {
         searchText = filter.getSearchText();
      }

      SelectConditionScheduleAdmin conditionList = new SelectConditionScheduleAdmin();
      conditionList.setNameLike(searchText);
      conditionList.setUserRootGroup(strRootGroup);
      LogInfo logInfo = LogInfoImpl.getInstance();
      ListManager listMgr = new ListManager(logInfo, "commonlist");
      listMgr.addSearchInfo("condition", conditionList);
      listMgr.addSearchInfo("userID", SecurityUtils.getLoginUserId());
      listMgr.addSearchInfo("sortColumn", filter.getSortColumn());
      listMgr.addSearchInfo("sortOrder", filter.getSortOrder());
      listMgr.addSearchInfo("startDate", filter.getStartDate());
      listMgr.addSearchInfo("endDate", filter.getEndDate());
      listMgr.addSearchInfo("endDate", filter.getEndDate());
      listMgr.addSearchInfo("searchText", searchText);
      if (!RoleUtils.isServerAdminRole(user)) {
         listMgr.addSearchInfo("organization_id", user.getRoot_group_id());
      }

      if (!filterType.equalsIgnoreCase("ALL")) {
         listMgr.addSearchInfo("filterType", filterType);
      }

      listMgr.setLstSize(Integer.valueOf(results));
      listMgr.setSection("getSystemLogList");
      PageManager pageMgr = null;
      List searchList = listMgr.V2dbexecute(filter.getStartIndex(), filter.getPageSize());
      pageMgr = listMgr.getPageManager();

      for(int i = 0; i < searchList.size(); ++i) {
         SystemLogEntity log = (SystemLogEntity)searchList.get(i);
         log.setEvent_type(ServerSystemLogEventTypeUtils.getEventTypeName(log.getMenu(), log.getEvent_type(), locale));
         searchList.set(i, log);
      }

      V2PageResource resouce = V2PageResource.createPageResource(searchList, pageMgr);
      return resouce;
   }

   public void exportServerLog(V2SettingLogFilter filter, HttpServletResponse response) throws Exception {
      DeviceStatisticsDownloadService downloadService = new DeviceStatisticsDownloadService();
      String fileName = "ServerLog.xls";
      String sheetName = "ServerLog";
      String[] columnNames = new String[]{"menu", "name", "event_type", "event_time", "user_id", "ip_address"};
      String[] fieldNames = new String[]{"MENU", "Name", "Event Type", "Time", "User ID", "IP Address"};
      V2PageResource pageResource = this.getServerLogs(filter);
      List resultList = pageResource.getList();
      int dataListSize = resultList.size();
      Object[] dataList = new Object[dataListSize];

      for(int index = 0; index < dataListSize; ++index) {
         dataList[index] = resultList.get(index);
      }

      Map dataMap = new HashMap();
      dataMap.put("fileName", fileName);
      dataMap.put("sheetName", sheetName);
      dataMap.put("columnNames", columnNames);
      dataMap.put("fieldNames", fieldNames);
      dataMap.put("dataList", dataList);
      downloadService.downloadExcelFile(dataMap, response);
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public V2PageResource getNotificationHistoryList(V2ListQueryFilter filter) throws Exception {
      NotificationHistoryInfo notificationHistoryInfo = NotificationHistoryInfoImpl.getInstance();
      ListManager listMgr = new ListManager(notificationHistoryInfo, "list");
      User user = SecurityUtils.getLoginUser();
      String search_text = filter.getSearchText();
      String orderDir = filter.getSortOrder();
      search_text = StrUtils.nvl(search_text).equals("") ? "" : search_text;
      listMgr.addSearchInfo("sortColumn", filter.getSortColumn());
      listMgr.addSearchInfo("sortOrder", filter.getSortOrder());
      listMgr.addSearchInfo("search_text", search_text.toUpperCase());
      listMgr.addSearchInfo("org_id", user.getRoot_group_id());
      PageManager pageMgr = null;
      List historyList = listMgr.V2dbexecute(filter.getStartIndex(), filter.getPageSize());

      for(int i = 0; i < historyList.size(); ++i) {
         boolean hasAttachedFile = false;
         if (notificationHistoryInfo.getAttachedFileFullPath(((NotificationHistoryEntity)historyList.get(i)).getId()).size() > 0) {
            ((NotificationHistoryEntity)historyList.get(i)).setHasAttachedFile(true);
         }
      }

      pageMgr = listMgr.getPageManager();
      pageMgr.setPageSize(filter.getPageSize());
      V2PageResource resouce = V2PageResource.createPageResource(historyList, pageMgr);
      return resouce;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public void exportAlarmMailHistory(String searchText, String sortColumn, String sortOrder, HttpServletResponse response) throws Exception {
      NotificationHistoryInfo notificationHistoryInfo = NotificationHistoryInfoImpl.getInstance();
      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = "id";
      }

      if (StrUtils.nvl(sortOrder).equals("")) {
         sortOrder = "desc";
      }

      if (StrUtils.nvl(searchText).equals("")) {
         searchText = "";
      }

      String fileName = "NotificationHistory.xls";
      String sheetName = "Notification History";
      String[] columnNames = new String[]{"organization_name", "title", "result_msg", "send_time"};
      String[] fieldNames = new String[]{"Organization", "Subject", "SMTP Access", "Date Sent"};
      ListManager listMgr = new ListManager(notificationHistoryInfo, "list");
      User user = SecurityUtils.getLoginUser();
      searchText = StrUtils.nvl(searchText).equals("") ? "" : searchText;
      listMgr.addSearchInfo("sortColumn", sortColumn);
      listMgr.addSearchInfo("sortOrder", sortOrder);
      listMgr.addSearchInfo("search_text", searchText.toUpperCase());
      listMgr.addSearchInfo("org_id", user.getRoot_group_id());
      int BIG_NUM = 1000000;
      List historyList = listMgr.V2dbexecute(1, 1000000);
      int dataListSize = historyList.size();
      Object[] dataList = new Object[dataListSize];

      for(int index = 0; index < dataListSize; ++index) {
         dataList[index] = historyList.get(index);
      }

      Map dataMap = new HashMap();
      dataMap.put("fileName", fileName);
      dataMap.put("sheetName", sheetName);
      dataMap.put("columnNames", columnNames);
      dataMap.put("fieldNames", fieldNames);
      dataMap.put("dataList", dataList);
      DeviceStatisticsDownloadService downloadService = new DeviceStatisticsDownloadService();
      downloadService.downloadExcelFile(dataMap, response);
   }

   public NotificationHistoryEntity getNotificationInformation(Long historyId) throws Exception {
      NotificationHistoryInfo notiHistoryInfo = NotificationHistoryInfoImpl.getInstance();
      NotificationHistoryEntity noti = notiHistoryInfo.getNotificationHistoryInfo(historyId);
      List receiverList = notiHistoryInfo.getReceiverIDList(historyId);
      List fileNameList = notiHistoryInfo.getAttachedFileNames(historyId);
      noti.setReceiverList(receiverList);
      noti.setFileNameList(fileNameList);
      return noti;
   }

   public void downloadMailAttachedFile(Long historyId, HttpServletRequest request, HttpServletResponse response) throws Exception {
      NotificationHistoryInfo notiInfo = NotificationHistoryInfoImpl.getInstance();
      List attachedFileFullPath = null;
      String zipFilePath = "";

      try {
         attachedFileFullPath = notiInfo.getAttachedFileFullPath(historyId);
         String downloadPath = CommonConfig.get("UPLOAD_HOME") + File.separator + "download";
         File downloadPahHome = SecurityUtils.getSafeFile(downloadPath);
         if (!downloadPahHome.exists()) {
            downloadPahHome.mkdir();
         }

         zipFilePath = downloadPath + File.separator + "download.zip";
      } catch (Exception var24) {
         this.logger.error(var24);
      }

      FileOutputStream fos = null;
      ZipOutputStream zos = null;

      String mime;
      try {
         fos = new FileOutputStream(zipFilePath);
         zos = new ZipOutputStream(fos);
         Iterator var9 = attachedFileFullPath.iterator();

         while(var9.hasNext()) {
            mime = (String)var9.next();
            CommonUtils.addToZipFile(mime, zos);
         }
      } catch (Exception var25) {
         this.logger.error("Fail to file compress", var25);
      } finally {
         if (zos != null) {
            try {
               zos.close();
            } catch (Exception var23) {
            }
         }

         if (fos != null) {
            try {
               fos.close();
            } catch (Exception var22) {
            }
         }

      }

      File file = new File(SecurityUtils.directoryTraversalChecker(zipFilePath, (String)null));
      mime = "application/zip";
      FileUtils.setFileToResponse(request, response, file, mime);
   }
}
