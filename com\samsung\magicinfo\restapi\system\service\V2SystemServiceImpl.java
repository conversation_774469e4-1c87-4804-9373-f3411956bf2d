package com.samsung.magicinfo.restapi.system.service;

import com.google.common.base.Enums;
import com.google.common.hash.Hashing;
import com.google.gson.Gson;
import com.samsung.common.cache.CacheFactory;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.ExternalSystemUtils;
import com.samsung.common.utils.RoleUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.auth.security.TokenUtils;
import com.samsung.magicinfo.framework.content.dao.ContentDao;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.role.dao.AbilityDao;
import com.samsung.magicinfo.framework.scheduler.dao.MessageGroupDao;
import com.samsung.magicinfo.framework.scheduler.entity.MessageGroup;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.dao.DbSchemeDao;
import com.samsung.magicinfo.framework.setup.dao.InsightServerDao;
import com.samsung.magicinfo.framework.setup.dao.LoginPageDao;
import com.samsung.magicinfo.framework.setup.entity.DbSchemeCheckEntity;
import com.samsung.magicinfo.framework.setup.entity.DbSchemeEntity;
import com.samsung.magicinfo.framework.setup.entity.InsightServerEntity;
import com.samsung.magicinfo.framework.setup.entity.LoginPageEntity;
import com.samsung.magicinfo.framework.setup.manager.EslServerInfo;
import com.samsung.magicinfo.framework.setup.manager.EslServerInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.system.dao.DatabaseManagerDao;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.openapi.auth.TokenRegistry;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.file.FileUploadCommonHelper;
import com.samsung.magicinfo.protocol.queue.ContentDownloadPool;
import com.samsung.magicinfo.protocol.util.TimeUtil;
import com.samsung.magicinfo.restapi.common.model.V2CommonKeyValueResource;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.setting.model.config.V2InsightServerSettings;
import com.samsung.magicinfo.restapi.system.model.V2ContentDownloadThrottling;
import com.samsung.magicinfo.restapi.system.model.V2DbSchemeResource;
import com.samsung.magicinfo.restapi.system.model.V2InsightSystemInfoResource;
import com.samsung.magicinfo.restapi.system.model.V2L1MenuResource;
import com.samsung.magicinfo.restapi.system.model.V2L2MenuResource;
import com.samsung.magicinfo.restapi.system.model.V2L3MenuResource;
import com.samsung.magicinfo.restapi.system.model.V2LoginConfigResource;
import com.samsung.magicinfo.restapi.system.model.V2MagicInfoConfigResource;
import com.samsung.magicinfo.restapi.system.model.V2MagicInfoVersionResource;
import com.samsung.magicinfo.restapi.system.model.V2RuleManagerSystemInfoResource;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import sun.awt.image.ToolkitImage;

@Service("V2SystemService")
@Transactional
public class V2SystemServiceImpl implements V2SystemService {
   @Autowired
   TokenUtils tokenUtils;
   protected final Log logger = LogFactory.getLog(this.getClass());

   public V2SystemServiceImpl() {
      super();
   }

   public V2L1MenuResource getMenu(String menuId, Boolean onlyMenu) throws Exception {
      boolean isRegisteredLicenseOfLfd = false;
      boolean isRegisteredLicenseOfTizen = false;
      boolean isRegisteredLicenseOfSoc = false;
      boolean isRegisteredLicenseOfAndroid = false;
      boolean isRegisteredLicenseOfSignage = false;
      boolean isRegisteredLicenseOfLite = false;
      boolean isSlmLicenseChecked = false;
      boolean isRegisteredLicenseOfRms = false;
      boolean isSlmMaintLicenseChecked = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         isRegisteredLicenseOfLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         isRegisteredLicenseOfSoc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         isRegisteredLicenseOfAndroid = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         isRegisteredLicenseOfSignage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         isRegisteredLicenseOfLite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_RMS") != null) {
         isRegisteredLicenseOfRms = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_RMS"));
      }

      if (isRegisteredLicenseOfSoc || isRegisteredLicenseOfLfd || isRegisteredLicenseOfTizen || isRegisteredLicenseOfAndroid || isRegisteredLicenseOfSignage || isRegisteredLicenseOfLite) {
         isRegisteredLicenseOfLfd = true;
         isSlmLicenseChecked = true;
      }

      SlmLicenseManager slmMgr = SlmLicenseManagerImpl.getInstance();
      if (slmMgr.chkValidationOfServerVersion() == 0L) {
         isSlmMaintLicenseChecked = true;
      } else {
         Date endOfService = new Date(slmMgr.chkValidationOfServerVersion());
         this.logger.error(new Date(slmMgr.chkValidationOfServerVersion()) + ", " + (new Date()).after(endOfService));
         if ((new Date()).after(endOfService)) {
            isSlmMaintLicenseChecked = false;
         } else {
            isSlmMaintLicenseChecked = true;
         }
      }

      AbilityUtils ability = new AbilityUtils();
      boolean canReadContent = (ability.checkAuthority("Content Read") || ability.checkAuthority("Content Log")) && isSlmLicenseChecked && isSlmMaintLicenseChecked;
      boolean canReadSchedule = ((ability.checkAuthority("Content Schedule Read") || ability.checkAuthority("Message Schedule Read") || ability.checkAuthority("Schedule Log")) && isRegisteredLicenseOfLfd) & isSlmLicenseChecked && isSlmMaintLicenseChecked;
      boolean canReadDevice = (ability.checkAuthority("Device Read") && isRegisteredLicenseOfLfd || ability.checkAuthority("Device Log Read")) && isSlmLicenseChecked && isSlmMaintLicenseChecked;
      boolean canReadUser = ability.checkAuthority("User Read") || ability.checkAuthority("Role Read");
      boolean canReadStatistics = ability.checkAuthority("Statistics Manage") && isSlmLicenseChecked && isSlmMaintLicenseChecked;
      if (!isSlmLicenseChecked && isSlmMaintLicenseChecked && isRegisteredLicenseOfRms && ability.checkAuthority("Device Read") || ability.checkAuthority("Device Log Read")) {
         canReadDevice = true;
      }

      if (!isSlmLicenseChecked && isSlmMaintLicenseChecked && isRegisteredLicenseOfRms && ability.checkAuthority("Statistics Manage")) {
         canReadStatistics = true;
      }

      if (menuId.equalsIgnoreCase("RULESET")) {
         if (canReadContent) {
            return new V2L1MenuResource("RULESET", canReadContent, this.makeRuleSetSubmenu(onlyMenu));
         } else {
            throw new AccessDeniedException("Access denied to get menu of RULESET");
         }
      } else if (menuId.equalsIgnoreCase("CONTENT")) {
         if (canReadContent) {
            return new V2L1MenuResource("CONTENT", canReadContent, this.makeContentSubmenu(onlyMenu));
         } else {
            throw new AccessDeniedException("Access denied to get menu of CONTENT");
         }
      } else if (menuId.equalsIgnoreCase("PLAYLIST")) {
         if (canReadContent) {
            return new V2L1MenuResource("PLAYLIST", canReadContent, this.makePlaylistSubmenu());
         } else {
            throw new AccessDeniedException("Access denied to get menu of PLAYLIST");
         }
      } else if (menuId.equalsIgnoreCase("SCHEDULE")) {
         if (canReadSchedule) {
            return new V2L1MenuResource("SCHEDULE", canReadSchedule, this.makeScheduleSubmenu(onlyMenu));
         } else {
            throw new AccessDeniedException("Access denied to get menu of SCHEDULE");
         }
      } else if (menuId.equalsIgnoreCase("DEVICE")) {
         if (canReadDevice) {
            return new V2L1MenuResource("DEVICE", canReadDevice, this.makeDeviceSubmenu(onlyMenu));
         } else {
            throw new AccessDeniedException("Access denied to get menu of DEVICE");
         }
      } else if (menuId.equalsIgnoreCase("STATISTICS")) {
         if (canReadStatistics) {
            return new V2L1MenuResource("STATISTICS", canReadStatistics, this.makeStatisticsSubmenu(isSlmLicenseChecked));
         } else {
            throw new AccessDeniedException("Access denied to get menu of STATISTICS");
         }
      } else if (menuId.equalsIgnoreCase("USER")) {
         if (canReadUser) {
            return new V2L1MenuResource("USER", canReadUser, this.makeUserSubmenu(onlyMenu));
         } else {
            throw new AccessDeniedException("Access denied to get menu of USER");
         }
      } else if (menuId.equalsIgnoreCase("SETTING")) {
         return new V2L1MenuResource("SETTING", true, this.makeSettingSubmenu());
      } else {
         return menuId.equalsIgnoreCase("INSIGHT") ? new V2L1MenuResource("INSIGHT", true, (List)null) : null;
      }
   }

   public List getMenus(Boolean onlyMenu) throws Exception {
      boolean isRegisteredLicenseOfLfd = false;
      boolean isRegisteredLicenseOfTizen = false;
      boolean isRegisteredLicenseOfSoc = false;
      boolean isRegisteredLicenseOfAndroid = false;
      boolean isRegisteredLicenseOfSignage = false;
      boolean isRegisteredLicenseOfLite = false;
      boolean isSlmLicenseChecked = false;
      boolean isRegisteredLicenseOfRms = false;
      boolean isSlmMaintLicenseChecked = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         isRegisteredLicenseOfLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         isRegisteredLicenseOfSoc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         isRegisteredLicenseOfAndroid = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         isRegisteredLicenseOfSignage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         isRegisteredLicenseOfLite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_RMS") != null) {
         isRegisteredLicenseOfRms = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_RMS"));
      }

      if (isRegisteredLicenseOfSoc || isRegisteredLicenseOfLfd || isRegisteredLicenseOfTizen || isRegisteredLicenseOfAndroid || isRegisteredLicenseOfSignage || isRegisteredLicenseOfLite) {
         isRegisteredLicenseOfLfd = true;
         isSlmLicenseChecked = true;
      }

      SlmLicenseManager slmMgr = SlmLicenseManagerImpl.getInstance();
      if (slmMgr.chkValidationOfServerVersion() == 0L) {
         isSlmMaintLicenseChecked = true;
      } else {
         Date endOfService = new Date(slmMgr.chkValidationOfServerVersion());
         this.logger.error(new Date(slmMgr.chkValidationOfServerVersion()) + ", " + (new Date()).after(endOfService));
         if ((new Date()).after(endOfService)) {
            isSlmMaintLicenseChecked = false;
         } else {
            isSlmMaintLicenseChecked = true;
         }
      }

      AbilityUtils ability = new AbilityUtils();
      boolean canReadContent = (ability.checkAuthority("Content Read") || ability.checkAuthority("Content Log")) && isSlmLicenseChecked && isSlmMaintLicenseChecked;
      boolean canReadSchedule = ((ability.checkAuthority("Content Schedule Read") || ability.checkAuthority("Message Schedule Read") || ability.checkAuthority("Schedule Log")) && isRegisteredLicenseOfLfd) & isSlmLicenseChecked && isSlmMaintLicenseChecked;
      boolean canReadDevice = (ability.checkAuthority("Device Read") && isRegisteredLicenseOfLfd || ability.checkAuthority("Device Log Read")) && isSlmLicenseChecked && isSlmMaintLicenseChecked;
      boolean canReadUser = ability.checkAuthority("User Read") || ability.checkAuthority("Role Read");
      boolean canReadStatistics = ability.checkAuthority("Statistics Manage") && isSlmLicenseChecked && isSlmMaintLicenseChecked;
      if (!isSlmLicenseChecked && isSlmMaintLicenseChecked && isRegisteredLicenseOfRms && ability.checkAuthority("Device Read") || ability.checkAuthority("Device Log Read")) {
         canReadDevice = true;
      }

      if (!isSlmLicenseChecked && isSlmMaintLicenseChecked && isRegisteredLicenseOfRms && ability.checkAuthority("Statistics Manage")) {
         canReadStatistics = true;
      }

      boolean isEnableInsight = false;
      boolean isActiveInsight = false;
      User loginUser = SecurityUtils.getUserContainer().getUser();
      AbilityDao abilityDao = new AbilityDao();
      int abilityCount = abilityDao.hasAbilityByUserId(loginUser.getUser_id(), "Insight Read Authority");
      if (abilityCount >= 1 && isSlmLicenseChecked && isSlmMaintLicenseChecked) {
         isEnableInsight = true;
      }

      InsightServerDao insightServerDao = new InsightServerDao();
      InsightServerEntity insightServerEntity = insightServerDao.getInsightServerInfo();
      if (null != insightServerEntity && insightServerEntity.isUse_server()) {
         isActiveInsight = true;
      }

      List menus = new ArrayList();
      menus.add(new V2L1MenuResource("CONTENT", canReadContent, this.makeContentSubmenu(onlyMenu)));
      menus.add(new V2L1MenuResource("PLAYLIST", canReadContent, this.makePlaylistSubmenu()));
      menus.add(new V2L1MenuResource("RULESET", canReadContent, this.makeRuleSetSubmenu(onlyMenu)));
      menus.add(new V2L1MenuResource("SCHEDULE", canReadSchedule, this.makeScheduleSubmenu(onlyMenu)));
      menus.add(new V2L1MenuResource("DEVICE", canReadDevice, this.makeDeviceSubmenu(onlyMenu)));
      menus.add(new V2L1MenuResource("STATISTICS", canReadStatistics, this.makeStatisticsSubmenu(isSlmLicenseChecked)));
      if (isEnableInsight) {
         menus.add(new V2L1MenuResource("INSIGHT", true, (List)null, isActiveInsight));
      }

      menus.add(new V2L1MenuResource("USER", canReadUser, this.makeUserSubmenu(onlyMenu)));
      menus.add(new V2L1MenuResource("SETTING", true, this.makeSettingSubmenu()));

      long orgId;
      try {
         UserGroupInfo userGroupDao = UserGroupInfoImpl.getInstance();
         EslServerInfo eslServerInfo = EslServerInfoImpl.getInstance();
         orgId = userGroupDao.getOrgGroupIdByName(SecurityUtils.getLoginUserOrganization());
         Boolean isEnabled = eslServerInfo.getEnableEslServerByOrgId(orgId);
         if (isEnabled != null && isEnabled) {
            menus.add(new V2L1MenuResource("ELECTRONIC_LABELS", true));
         }
      } catch (Exception var33) {
         this.logger.error(var33);
      }

      if (CommonConfig.get("rulemanager.enable") != null && CommonConfig.get("rulemanager.enable").equals("true")) {
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         orgId = userGroupInfo.getOrgGroupIdByName(SecurityUtils.getLoginUserOrganization());
         Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
         String rmEnable = infoMap.get("RM_ENABLE") != null ? infoMap.get("RM_ENABLE").toString() : "false";
         boolean orgRmEnable = Boolean.valueOf(rmEnable);
         if (orgRmEnable && (ability.checkAuthority("RuleManager HQ") || ability.checkAuthority("RuleManager Store"))) {
            menus.add(new V2L1MenuResource("RULE_MANAGER", true));
         }
      }

      return menus;
   }

   private List makeRuleSetSubmenu(Boolean onlyMenu) {
      List libraryManagerSubmenus = new ArrayList();
      libraryManagerSubmenus.add(new V2L3MenuResource("LIBRARY_MANAGER_CONDITION", true));
      libraryManagerSubmenus.add(new V2L3MenuResource("LIBRARY_MANAGER_PLAY", true));
      List submenus = new ArrayList();
      submenus.add(new V2L2MenuResource("ALL_RULESET", true));
      submenus.add(new V2L2MenuResource("RULESET_BY_GROUP", true));
      submenus.add(new V2L2MenuResource("LIBRARY_MANAGER", true, libraryManagerSubmenus));
      submenus.add(new V2L2MenuResource("RULESET_RECYCLE_BIN", true));
      return submenus;
   }

   private List makeContentSubmenu(Boolean onlyMenu) throws SQLException, ConfigException {
      AbilityUtils ability = new AbilityUtils();
      boolean isContentManager = ability.checkAuthority("Content Manage");
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      String orgName = SecurityUtils.getUserContainer().getUser().getOrganization();
      long orgId = userGroupInfo.getOrgGroupIdByName(orgName);
      Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
      boolean contentsApprovalEnable = false;
      if (infoMap != null && infoMap.get("CONTENTS_APPROVAL_ENABLE") != null) {
         contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
      }

      if (onlyMenu) {
         List submenus = new ArrayList();
         submenus.add(new V2L2MenuResource("ALL_CONTENT", true));
         submenus.add(new V2L2MenuResource("MY_CONTENT", true));
         submenus.add(new V2L2MenuResource("SHARED_CONTENT", true));
         submenus.add(new V2L2MenuResource("BY_USER", isContentManager));
         submenus.add(new V2L2MenuResource("UNAPPROVED_CONTENT", contentsApprovalEnable && ability.isContentApprovalAuthority()));
         submenus.add(new V2L2MenuResource("CONTENT_TEMPLATE", true, this.makeContentTemplateSubmenu()));
         submenus.add(new V2L2MenuResource("CONTENT_RECYCLE_BIN", true));
         return submenus;
      } else {
         String loginUserRoleName = SecurityUtils.getUserContainer().getUser().getRole_name();
         ContentDao contentdao = new ContentDao();
         String creatorId = SecurityUtils.getLoginUser().getUser_id();
         long groupIdOfLoginUser = SecurityUtils.getLoginUser().getGroup_id();
         Map condition = new HashMap();
         condition.put("content_type", "CONTENT");
         condition.put("listType", "SUBMITTED");
         condition.put("creatorID", creatorId);
         condition.put("canReadUnshared", true);
         condition.put("groupID", String.valueOf(groupIdOfLoginUser));
         condition.put("isMain", "true");
         condition.put("searchID", "-1");
         if (loginUserRoleName.equals("Server Administrator")) {
            condition.put("isServerAdmin", true);
         }

         int countOfUnapprovedContents = contentdao.getContentListCnt(condition);
         List submenus = new ArrayList();
         submenus.add(new V2L2MenuResource("ALL_CONTENT", true));
         submenus.add(new V2L2MenuResource("MY_CONTENT", true));
         submenus.add(new V2L2MenuResource("SHARED_CONTENT", true));
         submenus.add(new V2L2MenuResource("BY_USER", isContentManager));
         submenus.add(new V2L2MenuResource("UNAPPROVED_CONTENT", contentsApprovalEnable && ability.isContentApprovalAuthority(), countOfUnapprovedContents));
         submenus.add(new V2L2MenuResource("CONTENT_TEMPLATE", true, this.makeContentTemplateSubmenu()));
         submenus.add(new V2L2MenuResource("CONTENT_RECYCLE_BIN", true));
         return submenus;
      }
   }

   private List makeContentTemplateSubmenu() {
      List submenus = new ArrayList();
      submenus.add(new V2L3MenuResource("CONTENT_TEMPLATE_BY_FOLDER", true));
      return submenus;
   }

   private List makePlaylistSubmenu() {
      AbilityUtils ability = new AbilityUtils();
      boolean isContentManager = ability.checkAuthority("Content Manage");
      List submenus = new ArrayList();
      submenus.add(new V2L2MenuResource("ALL_PLAYLIST", true));
      submenus.add(new V2L2MenuResource("MY_PLAYLIST", true));
      submenus.add(new V2L2MenuResource("PLAYLIST_BY_USER", isContentManager));
      submenus.add(new V2L2MenuResource("PLAYLIST_RECYCLE_BIN", true));
      return submenus;
   }

   private List makeScheduleSubmenu(Boolean onlyMenu) throws Exception {
      List submenus = new ArrayList();
      submenus.add(new V2L2MenuResource("CONTENT", true, this.makeContentScheduleSubmenu(onlyMenu)));
      submenus.add(new V2L2MenuResource("MESSAGE", true, this.makeMessageScheduleSubmenu(onlyMenu)));
      submenus.add(new V2L2MenuResource("EVENT", true, this.makeEventScheduleSubmenu(onlyMenu)));
      return submenus;
   }

   private List makeContentScheduleSubmenu(Boolean onlyMenu) throws SQLException {
      if (onlyMenu) {
         List submenus = new ArrayList();
         submenus.add(new V2L3MenuResource("ALL_CONTENT_SCHEDULE", true));
         submenus.add(new V2L3MenuResource("CONTENT_SCHEDULE_BY_GROUP", true));
         submenus.add(new V2L3MenuResource("CONTENT_SCHEDULE_RECYCLE_BIN", true));
         return submenus;
      } else {
         int badgeCount = false;
         int badgeCount;
         if (SecurityUtils.getLoginUser().getGroup_id() < 1L) {
            ScheduleInfo info = ScheduleInfoImpl.getInstance();
            if (SecurityUtils.getLoginUser().isMu()) {
               String orgName = SecurityUtils.getLoginUser().getOrganization();
               badgeCount = (int)info.getAllScheduleCount(orgName);
            } else {
               badgeCount = CommonUtils.safeLongToInt(info.getAllScheduleCount());
            }
         } else {
            ProgramGroupInfo groupInfo = ProgramGroupInfoImpl.getInstance();
            int organizationId = groupInfo.getOrganizationIdByName(SecurityUtils.getLoginUser().getOrganization());
            badgeCount = CommonUtils.safeLongToInt(groupInfo.getTotalOrganizationProgramCountByGroupId(0L, (long)organizationId));
         }

         List submenus = new ArrayList();
         submenus.add(new V2L3MenuResource("ALL_CONTENT_SCHEDULE", true, badgeCount));
         submenus.add(new V2L3MenuResource("CONTENT_SCHEDULE_BY_GROUP", true));
         submenus.add(new V2L3MenuResource("CONTENT_SCHEDULE_RECYCLE_BIN", true));
         return submenus;
      }
   }

   private List makeMessageScheduleSubmenu(Boolean onlyMenu) throws SQLException {
      if (onlyMenu) {
         List submenus = new ArrayList();
         submenus.add(new V2L3MenuResource("ALL_MESSAGE_SCHEDULE", true));
         submenus.add(new V2L3MenuResource("MESSAGE_SCHEDULE_BY_GROUP", true));
         submenus.add(new V2L3MenuResource("MESSAGE_SCHEDULE_RECYCLE_BIN", true));
         return submenus;
      } else {
         MessageInfo messageInfo = MessageInfoImpl.getInstance();
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         int badgeCount = 0;
         if (RoleUtils.isServerAdminRole(SecurityUtils.getLoginUser())) {
            badgeCount = CommonUtils.safeLongToInt(messageInfo.getAllScheduleCount());
         } else {
            List lists = new ArrayList();
            MessageGroupDao messageGroupDao = new MessageGroupDao();
            if (SecurityUtils.getLoginUser().getRoot_group_id() == 0L) {
               List manageGroupList = userGroupInfo.getUserManageGroupListByUserId(SecurityUtils.getLoginUser().getUser_id());
               if (manageGroupList != null && manageGroupList.size() > 0) {
                  Iterator var8 = manageGroupList.iterator();

                  while(var8.hasNext()) {
                     UserGroup userGroup = (UserGroup)var8.next();
                     ((List)lists).addAll(messageGroupDao.getRootGroupById(userGroup.getGroup_name()));
                  }
               } else {
                  lists = messageGroupDao.getRootGroupById(SecurityUtils.getLoginUser().getOrganization());
               }
            } else {
               lists = messageGroupDao.getRootGroupById(SecurityUtils.getLoginUser().getOrganization());
            }

            List list;
            for(Iterator var12 = ((List)lists).iterator(); var12.hasNext(); badgeCount += list.size()) {
               MessageGroup group = (MessageGroup)var12.next();
               list = messageGroupDao.getChildMessageCount(group.getGroup_id().intValue(), true);
            }
         }

         List submenus = new ArrayList();
         submenus.add(new V2L3MenuResource("ALL_MESSAGE_SCHEDULE", true, badgeCount));
         submenus.add(new V2L3MenuResource("MESSAGE_SCHEDULE_BY_GROUP", true));
         submenus.add(new V2L3MenuResource("MESSAGE_SCHEDULE_RECYCLE_BIN", true));
         return submenus;
      }
   }

   private List makeEventScheduleSubmenu(Boolean onlyMenu) throws Exception {
      if (onlyMenu) {
         List submenus = new ArrayList();
         submenus.add(new V2L3MenuResource("ALL_EVENT_SCHEDULE", true));
         submenus.add(new V2L3MenuResource("EVENT_SCHEDULE_BY_GROUP", true));
         submenus.add(new V2L3MenuResource("EVENT_SCHEDULE_RECYCLE_BIN", true));
         submenus.add(new V2L3MenuResource("EVENT_SCHEDULE_MANAGER", true));
         return submenus;
      } else {
         EventInfo eventInfo = EventInfoImpl.getInstance();
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         int badgeCount = 0;
         if (RoleUtils.isServerAdminRole(SecurityUtils.getLoginUser())) {
            badgeCount = CommonUtils.safeLongToInt(eventInfo.getAllScheduleCount());
         } else if (SecurityUtils.getLoginUser().getRoot_group_id() == 0L) {
            List manageGroupList = userGroupInfo.getUserManageGroupListByUserId(SecurityUtils.getLoginUser().getUser_id());
            UserGroup userGroup;
            if (manageGroupList != null && manageGroupList.size() > 0) {
               for(Iterator var6 = manageGroupList.iterator(); var6.hasNext(); badgeCount += CommonUtils.safeLongToInt(eventInfo.getAllScheduleCount(userGroup.getGroup_name()))) {
                  userGroup = (UserGroup)var6.next();
               }
            } else {
               badgeCount = CommonUtils.safeLongToInt(eventInfo.getAllScheduleCount(SecurityUtils.getLoginUser().getOrganization()));
            }
         } else {
            badgeCount = CommonUtils.safeLongToInt(eventInfo.getAllScheduleCount(SecurityUtils.getLoginUser().getOrganization()));
         }

         List submenus = new ArrayList();
         submenus.add(new V2L3MenuResource("ALL_EVENT_SCHEDULE", true, badgeCount));
         submenus.add(new V2L3MenuResource("EVENT_SCHEDULE_BY_GROUP", true));
         submenus.add(new V2L3MenuResource("EVENT_SCHEDULE_RECYCLE_BIN", true));
         submenus.add(new V2L3MenuResource("EVENT_SCHEDULE_MANAGER", true));
         return submenus;
      }
   }

   private List makeDeviceSubmenu(Boolean onlyMenu) throws Exception {
      boolean isRmsMode = CommonConfig.get("RMS_MODE") != null && Boolean.parseBoolean(CommonConfig.get("RMS_MODE"));
      boolean isRootGroup = SecurityUtils.getLoginUser().getRoot_group_id() == 0L;
      List submenus = new ArrayList();
      submenus.add(new V2L2MenuResource("DEVICE", true, this.makeDeviceListSubmenu(onlyMenu)));
      submenus.add(new V2L2MenuResource("DEVICE_HEALTH", true, this.makeDeviceHealthSubmenu()));
      submenus.add(this.makeNotificationMenu(onlyMenu));
      submenus.add(this.makeSoftwareUpdateMenu());
      submenus.add(new V2L2MenuResource("PRESET_MANAGEMENT", true));
      submenus.add(this.makeCustomizeSubmenu());
      submenus.add(new V2L2MenuResource("I_PLAYER_REMOTE_JOB", !isRmsMode && isRootGroup));
      return submenus;
   }

   private int getUnapprovedDeviceCountByUser(User user) throws SQLException {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      if (!RoleUtils.isServerAdminRole(user) && !user.getOrganization().equals("ROOT")) {
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         int res = 0;
         DeviceGroupInfo deviceGroup = DeviceGroupInfoImpl.getInstance();
         long loginUserOrgId = SecurityUtils.getLoginUserOrganizationId();
         boolean hasDevicePermission = serverSetupDao.checkPermissionsDeviceByOrgId(loginUserOrgId);
         List permissionDeviceGroupList;
         if (hasDevicePermission) {
            permissionDeviceGroupList = deviceGroup.getAllAuthorityDeviceGroups(user.getUser_id());
            if (!permissionDeviceGroupList.isEmpty()) {
               res = deviceGroup.getUnapprovedDeviceCountByUser(permissionDeviceGroupList);
            }
         } else {
            permissionDeviceGroupList = DeviceUtils.getGroupIdsByOrgManagerUserId(loginUserOrgId, user.getUser_id());
            res = deviceGroup.getUnapprovedDeviceCountByUser(permissionDeviceGroupList);
         }

         return res;
      } else {
         return deviceDao.getNonApprovalDeviceCount((String)null);
      }
   }

   private List makeDeviceListSubmenu(Boolean onlyMenu) throws SQLException {
      boolean hasDeviceApprovalPermission = SecurityUtils.checkDeviceApprovalPermission();
      if (onlyMenu) {
         List submenus = new ArrayList();
         submenus.add(new V2L3MenuResource("ALL_DEVICE", true));
         submenus.add(new V2L3MenuResource("DEVICE_BY_GROUP", true));
         if (hasDeviceApprovalPermission) {
            submenus.add(new V2L3MenuResource("UNAPPROVED_DEVICE", true));
         } else {
            submenus.add(new V2L3MenuResource("UNAPPROVED_DEVICE", false));
         }

         return submenus;
      } else {
         DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
         DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
         User user = SecurityUtils.getUserContainer().getUser();
         String userId = user.getUser_id();
         String roleName = user.getRole_name();
         String orgName = user.getOrganization();
         Long groupId = deviceGroupInfo.getOrganGroupIdByName(orgName);
         boolean isDeviceGroupAuth = false;
         if (DeviceUtils.isDeviceGroupAuth(roleName, userId)) {
            isDeviceGroupAuth = true;
         }

         int badgeCountOfAllDevice = deviceDao.getCountDeviceAll(userId, groupId, isDeviceGroupAuth);
         List submenus = new ArrayList();
         submenus.add(new V2L3MenuResource("ALL_DEVICE", true, badgeCountOfAllDevice));
         submenus.add(new V2L3MenuResource("DEVICE_BY_GROUP", true));
         if (hasDeviceApprovalPermission) {
            int badgeCount = this.getUnapprovedDeviceCountByUser(user);
            submenus.add(new V2L3MenuResource("UNAPPROVED_DEVICE", true, badgeCount));
         } else {
            submenus.add(new V2L3MenuResource("UNAPPROVED_DEVICE", false));
         }

         return submenus;
      }
   }

   private List makeDeviceHealthSubmenu() {
      List submenus = new ArrayList();
      submenus.add(new V2L3MenuResource("DEVICE_HEALTH_ERROR", true));
      submenus.add(new V2L3MenuResource("DEVICE_HEALTH_WARNING", true));
      submenus.add(new V2L3MenuResource("DEVICE_HEALTH_RESOLVED", true));
      return submenus;
   }

   private V2L2MenuResource makeNotificationMenu(Boolean onlyMenu) throws Exception {
      boolean isRmsMode = CommonConfig.get("RMS_MODE") != null && Boolean.parseBoolean(CommonConfig.get("RMS_MODE"));
      if (onlyMenu) {
         List submenus = new ArrayList();
         submenus.add(new V2L3MenuResource("CONTENT_DOWNLOAD_INCOMPLETE", !isRmsMode));
         submenus.add(new V2L3MenuResource("SCHEDULE_NOT_PUBLISHED", !isRmsMode));
         submenus.add(new V2L3MenuResource("SCHEDULES_TO_EXPIRE", !isRmsMode));
         submenus.add(new V2L3MenuResource("PLAYLIST_TO_EXPIRE", !isRmsMode));
         submenus.add(new V2L3MenuResource("TIMEZONE_NOT_SET", true));
         submenus.add(new V2L3MenuResource("INSUFFICIENT_CAPACITY", true));
         return new V2L2MenuResource("NOTIFICATIONS", true, submenus);
      } else {
         DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
         DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         ScheduleInfo scheduleDao = ScheduleInfoImpl.getInstance();
         ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
         PlaylistInfo playListDao = PlaylistInfoImpl.getInstance();
         Calendar c = Calendar.getInstance();
         c.setTime(new Date());
         String stopDate = DateUtils.setDate(2);
         User user = SecurityUtils.getUserContainer().getUser();
         String userId = user.getUser_id();
         String roleName = user.getRole_name();
         String orgName = user.getOrganization();
         Long groupId = deviceGroupInfo.getOrganGroupIdByName(orgName);
         Long programGroupId = (long)programGroupInfo.getProgramGroupForOrg(orgName);
         boolean isDeviceGroupAuth = DeviceUtils.isDeviceGroupAuth(roleName, userId);
         int badgeCountOfUnsetTimezone = 0;
         int badgeCountOfNotEnoughMemory = 0;
         int badgeCountOfNotPublishedSchedule = 0;
         int badgeCountOfContentPlayingError = 0;
         int badgeCountOfScheduleToBeExpired = 0;
         int badgeCountOfPlaylistToBeExpired = 0;
         int badgeCountOfNotification = false;
         Map infoMap = serverSetupDao.getServerInfoByOrgId(0L);
         if (!infoMap.containsKey("NOTIFICATION_ENABLE") || infoMap.get("NOTIFICATION_ENABLE") == null || (Boolean)infoMap.get("NOTIFICATION_ENABLE")) {
            try {
               badgeCountOfUnsetTimezone = deviceDao.getCountTimezoneNotSet(userId, groupId, isDeviceGroupAuth);
               badgeCountOfNotEnoughMemory = deviceDao.getCountInsufficientCapacity(userId, groupId, isDeviceGroupAuth);
               badgeCountOfNotPublishedSchedule = deviceDao.getCountScheduleNotPublish(userId, groupId, isDeviceGroupAuth);
               badgeCountOfContentPlayingError = deviceDao.getCountContentError(userId, groupId, isDeviceGroupAuth);
               badgeCountOfScheduleToBeExpired = scheduleDao.getCountScheduleToExpire(userId, programGroupId, stopDate, (SelectCondition)null);
               badgeCountOfPlaylistToBeExpired = playListDao.getCountPlaylistToExpire(userId, programGroupId, stopDate, (SelectCondition)null);
            } catch (Exception var27) {
               this.logger.error("", var27);
            }
         }

         int badgeCountOfNotification;
         if (isRmsMode) {
            badgeCountOfNotification = badgeCountOfUnsetTimezone + badgeCountOfNotEnoughMemory;
         } else {
            badgeCountOfNotification = badgeCountOfUnsetTimezone + badgeCountOfNotEnoughMemory + badgeCountOfNotPublishedSchedule + badgeCountOfContentPlayingError + badgeCountOfScheduleToBeExpired + badgeCountOfPlaylistToBeExpired;
         }

         List submenus = new ArrayList();
         submenus.add(new V2L3MenuResource("CONTENT_DOWNLOAD_INCOMPLETE", !isRmsMode, badgeCountOfContentPlayingError));
         submenus.add(new V2L3MenuResource("SCHEDULE_NOT_PUBLISHED", !isRmsMode, badgeCountOfNotPublishedSchedule));
         submenus.add(new V2L3MenuResource("SCHEDULES_TO_EXPIRE", !isRmsMode, badgeCountOfScheduleToBeExpired));
         submenus.add(new V2L3MenuResource("PLAYLIST_TO_EXPIRE", !isRmsMode, badgeCountOfPlaylistToBeExpired));
         submenus.add(new V2L3MenuResource("TIMEZONE_NOT_SET", true, badgeCountOfUnsetTimezone));
         submenus.add(new V2L3MenuResource("INSUFFICIENT_CAPACITY", true, badgeCountOfNotEnoughMemory));
         return new V2L2MenuResource("NOTIFICATIONS", true, badgeCountOfNotification, submenus);
      }
   }

   private V2L2MenuResource makeSoftwareUpdateMenu() {
      AbilityUtils ability = new AbilityUtils();
      boolean hasDeviceManagerRole;
      if (SecurityUtils.getLoginUser() == null || !RoleUtils.isAdminRole(SecurityUtils.getLoginUser()) && !RoleUtils.isServerAdminRole(SecurityUtils.getLoginUser())) {
         hasDeviceManagerRole = ability.checkAuthority("Device Approval");
      } else {
         hasDeviceManagerRole = true;
      }

      boolean hasAuthority = hasDeviceManagerRole || ability.checkAuthority("Device Software Update");
      List submenus = new ArrayList();
      submenus.add(new V2L3MenuResource("SOFTWARE_REGISTER_PUBLISH", hasAuthority));
      submenus.add(new V2L3MenuResource("SOFTWARE_APPLIED_STATUS", hasAuthority));
      return new V2L2MenuResource("SOFTWARE_UPDATE", hasAuthority, submenus);
   }

   private V2L2MenuResource makeCustomizeSubmenu() {
      AbilityUtils ability = new AbilityUtils();
      boolean hasDeviceManagerRole;
      if (SecurityUtils.getLoginUser() == null || !RoleUtils.isAdminRole(SecurityUtils.getLoginUser()) && !RoleUtils.isServerAdminRole(SecurityUtils.getLoginUser())) {
         hasDeviceManagerRole = ability.checkAuthority("Device Approval");
      } else {
         hasDeviceManagerRole = true;
      }

      boolean hasAuthority = hasDeviceManagerRole || ability.checkAuthority("Device Customize");
      List submenus = new ArrayList();
      submenus.add(new V2L3MenuResource("CUSTOMIZE_REGISTER_PUBLISH", hasAuthority));
      submenus.add(new V2L3MenuResource("CUSTOMIZE_APPLIED_STATUS", hasAuthority));
      return new V2L2MenuResource("CUSTOMIZE", hasAuthority, submenus);
   }

   private List makeStatisticsSubmenu(boolean isSlmLicenseChecked) throws ConfigException {
      List submenus = new ArrayList();
      submenus.add(new V2L2MenuResource("SUMMARY", isSlmLicenseChecked));
      submenus.add(new V2L2MenuResource("DEVICE", true, this.makeStatisticsOfDeviceSubmenu()));
      submenus.add(this.makeStatisticsOfContentMenu());
      submenus.add(new V2L2MenuResource("EVENT_TRIGGER", isSlmLicenseChecked, this.makeStatisticsOfEventTriggerSubmenu()));
      return submenus;
   }

   private List makeStatisticsOfDeviceSubmenu() {
      User user = SecurityUtils.getUserContainer().getUser();
      boolean hasServerAdminRole = RoleUtils.isServerAdminRole(user.getRole_name());
      List submenus = new ArrayList();
      submenus.add(new V2L3MenuResource("CONNECTION_STATUS", true));
      submenus.add(new V2L3MenuResource("CONNECTION_HISTORY", true));
      submenus.add(new V2L3MenuResource("APPROVED_DEVICE", hasServerAdminRole));
      return submenus;
   }

   private V2L2MenuResource makeStatisticsOfContentMenu() throws ConfigException {
      boolean isRmsMode = CommonConfig.get("RMS_MODE") != null && Boolean.parseBoolean(CommonConfig.get("RMS_MODE"));
      boolean isMergeConfigDisabled = StrUtils.nvl(CommonConfig.get("pop.merge.enable")).equalsIgnoreCase("false");
      List submenus = new ArrayList();
      submenus.add(new V2L3MenuResource("CONTENT_TYPE", !isRmsMode));
      submenus.add(new V2L3MenuResource("PLAY_FREQUENCY", !isRmsMode));
      submenus.add(new V2L3MenuResource("DETAIL_REPORTS", !isRmsMode && !isMergeConfigDisabled));
      submenus.add(new V2L3MenuResource("STORAGE_SPACE_USAGE", !isRmsMode));
      submenus.add(new V2L3MenuResource("PROOF_OF_PLAY_FILE_HISTORY", !isRmsMode));
      return new V2L2MenuResource("CONTENT", !isRmsMode, submenus);
   }

   private List makeStatisticsOfEventTriggerSubmenu() {
      List submenus = new ArrayList();
      submenus.add(new V2L3MenuResource("EVENT_TRIGGER_DETAIL_REPORTS", true));
      return submenus;
   }

   private List makeUserSubmenu(Boolean onlyMenu) throws SQLException {
      UserInfo userInfo = UserInfoImpl.getInstance();
      ServerSetupInfo serverSetupInfo = ServerSetupInfoImpl.getInstance();
      User user = SecurityUtils.getUserContainer().getUser();
      String roleName = user.getRole_name();
      Map serverSetupInfoMap = serverSetupInfo.getServerInfoByOrgId(user.getRoot_group_id());
      Boolean ldapSyncEnabledValue = (Boolean)serverSetupInfoMap.get("LDAP_SYNC_ENABLE");
      boolean isLdapSyncEnabled = ldapSyncEnabledValue != null && ldapSyncEnabledValue;
      if (onlyMenu) {
         List submenus = new ArrayList();
         submenus.add(new V2L2MenuResource("ALL_USER", true));
         submenus.add(new V2L2MenuResource("USER_BY_GROUP", true));
         submenus.add(new V2L2MenuResource("UNAPPROVED", true));
         submenus.add(new V2L2MenuResource("WITHDRAWN_USERS", true));
         submenus.add(new V2L2MenuResource("ROLE", true));
         submenus.add(new V2L2MenuResource("ORGANIZATION_GROUP", roleName.equals("Server Administrator")));
         submenus.add(new V2L2MenuResource("SYNC_LDAP_ORGANIZATION", isLdapSyncEnabled));
         return submenus;
      } else {
         Map map = new HashMap();
         if (user.isMu()) {
            UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
            UserGroup mngOrg = userGroupInfo.getGroupById(user.getRoot_group_id());
            map.put("organization", mngOrg.getGroup_name());
         } else {
            map.put("organization", user.getOrganization());
         }

         int badgeCountOfUnapprovedUser = userInfo.getCountAllNonApprovedUser(map);
         int badgeCountOfWithdrawnUser = userInfo.getCountAllWithdrawalUser(map);
         List submenus = new ArrayList();
         submenus.add(new V2L2MenuResource("ALL_USER", true));
         submenus.add(new V2L2MenuResource("USER_BY_GROUP", true));
         submenus.add(new V2L2MenuResource("UNAPPROVED", true, badgeCountOfUnapprovedUser));
         submenus.add(new V2L2MenuResource("WITHDRAWN_USERS", true, badgeCountOfWithdrawnUser));
         submenus.add(new V2L2MenuResource("ROLE", true));
         submenus.add(new V2L2MenuResource("ORGANIZATION_GROUP", roleName.equals("Server Administrator")));
         submenus.add(new V2L2MenuResource("SYNC_LDAP_ORGANIZATION", isLdapSyncEnabled));
         return submenus;
      }
   }

   private List makeSettingSubmenu() throws ConfigException, SQLException {
      AbilityUtils ability = new AbilityUtils();
      String roleName = SecurityUtils.getUserContainer().getUser().getRole_name();
      boolean hasServerAdminRole = RoleUtils.isServerAdminRole(roleName);
      boolean hasAdminRole = RoleUtils.isAdminRole(roleName);
      boolean canServerSetting = ability.checkAuthority("Server Setup Manage");
      boolean canWriteContent = ability.checkAuthority("Content Write Authority") || ability.checkAuthority("Content Manage Authority");
      boolean canWriteSchedule = ability.checkAuthority("Content Schedule Add Authority") || ability.checkAuthority("Content Schedule Write Authority");
      boolean canWriteDevice = ability.checkAuthority("Device Write Authority") || ability.checkAuthority("Device Approval Authority");
      boolean isRmsMode = CommonConfig.get("RMS_MODE") != null && Boolean.parseBoolean(CommonConfig.get("RMS_MODE"));
      String e2eEnabledValue = CommonConfig.get("e2e.enable");
      boolean isE2EEnabled = e2eEnabledValue != null && e2eEnabledValue.equalsIgnoreCase("true");
      boolean isTagManagementActivated = canServerSetting || canWriteContent || canWriteSchedule || canWriteDevice;
      boolean isCategoryManagementActivated = !isRmsMode && (canWriteContent || hasAdminRole || hasServerAdminRole);
      boolean isLogManagementActivated = hasAdminRole || canServerSetting;
      boolean isInsightIndexManagementActivated = false;
      boolean isExternalLinkActivated = false;
      ServerSetupInfo serverSetupInfo = ServerSetupInfoImpl.getInstance();
      Map infoMap = serverSetupInfo.getServerInfoByOrgId(SecurityUtils.getLoginUserOrganizationId());
      if (infoMap != null) {
         isExternalLinkActivated = Boolean.parseBoolean(infoMap.getOrDefault("EXT_LINK_ENABLE", "false").toString());
      }

      InsightServerDao insightServerDao = new InsightServerDao();
      V2InsightServerSettings insightServerSettings = null;
      InsightServerEntity insightServerEntity = insightServerDao.getInsightServerInfo();
      if (insightServerEntity != null && insightServerEntity.isUse_server() != null && insightServerEntity.isUse_server()) {
         isInsightIndexManagementActivated = true;
      } else {
         isInsightIndexManagementActivated = false;
      }

      List submenus = new ArrayList();
      submenus.add(new V2L2MenuResource("MY_ACCOUNT", true, this.makeSettingMyAccountSubmenu()));
      if (hasServerAdminRole) {
         submenus.add(new V2L2MenuResource("SERVER_MANAGEMENT", true, this.makeSettingServerManagementSubmenu()));
         submenus.add(new V2L2MenuResource("EXTERNAL_SERVER_MANAGEMENT", true, this.makeSettingExternalServerManagementSubmenu()));
         submenus.add(new V2L2MenuResource("LICENSE_INFO", true));
         submenus.add(new V2L2MenuResource("E2E_LICENSE_INFO", isE2EEnabled));
      } else if (!hasAdminRole && !canServerSetting) {
         submenus.add(new V2L2MenuResource("SERVER_MANAGEMENT", false, this.makeSettingServerManagementSubmenu()));
         submenus.add(new V2L2MenuResource("EXTERNAL_SERVER_MANAGEMENT", false, this.makeSettingExternalServerManagementSubmenu()));
         submenus.add(new V2L2MenuResource("LICENSE_INFO", false));
         submenus.add(new V2L2MenuResource("E2E_LICENSE_INFO", false));
      } else {
         submenus.add(new V2L2MenuResource("SERVER_MANAGEMENT", true, this.makeSettingServerManagementSubmenu()));
         submenus.add(new V2L2MenuResource("EXTERNAL_SERVER_MANAGEMENT", false, this.makeSettingExternalServerManagementSubmenu()));
         submenus.add(new V2L2MenuResource("LICENSE_INFO", false));
         submenus.add(new V2L2MenuResource("E2E_LICENSE_INFO", false));
      }

      submenus.add(new V2L2MenuResource("TAG_MANAGEMENT", isTagManagementActivated, this.makeSettingTagManagementSubmenu(isTagManagementActivated)));
      submenus.add(new V2L2MenuResource("INSIGHT_INDEX_MANAGEMENT", isInsightIndexManagementActivated, this.makeSettingInsightIndexManagementSubmenu(isInsightIndexManagementActivated)));
      submenus.add(new V2L2MenuResource("CATEGORY_MANAGEMENT", isCategoryManagementActivated));
      submenus.add(new V2L2MenuResource("LOG_MANAGEMENT", isLogManagementActivated, this.makeSettingLogManagementSubmenu(isLogManagementActivated)));
      submenus.add(new V2L2MenuResource("EXTERNAL_LINK", isExternalLinkActivated));
      return submenus;
   }

   private List makeSettingMyAccountSubmenu() {
      List submenus = new ArrayList();
      submenus.add(new V2L3MenuResource("MY_INFORMATION", true));
      submenus.add(new V2L3MenuResource("WITHDRAW_MEMBERSHIP", true));
      return submenus;
   }

   private List makeSettingServerManagementSubmenu() {
      AbilityUtils ability = new AbilityUtils();
      String roleName = SecurityUtils.getUserContainer().getUser().getRole_name();
      boolean hasServerAdminRole = RoleUtils.isServerAdminRole(roleName);
      boolean hasAdminRole = RoleUtils.isServerAdminRole(roleName);
      boolean canServerSetting = ability.checkAuthority("Server Setup Manage");
      List submenus = new ArrayList();
      if (hasServerAdminRole) {
         submenus.add(new V2L3MenuResource("SERVER_SETTINGS", true));
         submenus.add(new V2L3MenuResource("DEVICE_SUMMARY", true));
         submenus.add(new V2L3MenuResource("SYSTEM_INFO", true));
         submenus.add(new V2L3MenuResource("SERVICE_HISTORY", true));
      } else if (!hasAdminRole && !canServerSetting) {
         submenus.add(new V2L3MenuResource("SERVER_SETTINGS", false));
         submenus.add(new V2L3MenuResource("DEVICE_SUMMARY", false));
         submenus.add(new V2L3MenuResource("SYSTEM_INFO", false));
      } else {
         submenus.add(new V2L3MenuResource("SERVER_SETTINGS", true));
         submenus.add(new V2L3MenuResource("DEVICE_SUMMARY", false));
         submenus.add(new V2L3MenuResource("SYSTEM_INFO", false));
      }

      return submenus;
   }

   private List makeSettingExternalServerManagementSubmenu() throws ConfigException {
      String roleName = SecurityUtils.getUserContainer().getUser().getRole_name();
      boolean hasServerAdminRole = RoleUtils.isServerAdminRole(roleName);
      List submenus = new ArrayList();
      if (hasServerAdminRole) {
         submenus.add(new V2L3MenuResource("DATALINK_SERVER", true));
         submenus.add(new V2L3MenuResource("EDGE_SERVER", true));
         submenus.add(new V2L3MenuResource("REMOTE_CONTROL_SERVER", true));
      } else {
         submenus.add(new V2L3MenuResource("DATALINK_SERVER", false));
         submenus.add(new V2L3MenuResource("EDGE_SERVER", false));
         submenus.add(new V2L3MenuResource("REMOTE_CONTROL_SERVER", false));
      }

      return submenus;
   }

   private List makeSettingTagManagementSubmenu(boolean isActivated) {
      List submenus = new ArrayList();
      submenus.add(new V2L3MenuResource("ALL_TAG", isActivated));
      submenus.add(new V2L3MenuResource("TAG_BY_GROUP", isActivated));
      return submenus;
   }

   private List makeSettingInsightIndexManagementSubmenu(boolean isActivated) {
      List submenus = new ArrayList();
      submenus.add(new V2L3MenuResource("ALL_INSIGHT_INDEX", isActivated));
      submenus.add(new V2L3MenuResource("INSIGHT_INDEX_BY_GROUP", isActivated));
      return submenus;
   }

   private List makeSettingLogManagementSubmenu(boolean isActivated) {
      List submenus = new ArrayList();
      submenus.add(new V2L3MenuResource("LOG", isActivated));
      submenus.add(new V2L3MenuResource("ALARM_MAIL_HISTORY", isActivated));
      return submenus;
   }

   @PreAuthorize("hasAnyAuthority('Insight Read Authority')")
   public V2InsightSystemInfoResource getInsightSystemInfo(String token, String cookieLocale) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String time = TimeUtil.getCurrentGMTTimeStr();
      User user = userContainer.getUser();
      User loginUser = SecurityUtils.getUserContainer().getUser();
      long userOrganizationId = loginUser.getRoot_group_id();
      long organizationId = 0L;
      String organization = user.getOrganization();

      try {
         DeviceGroupInfo groupInfo = DeviceGroupInfoImpl.getInstance();
         organizationId = (long)groupInfo.getDeviceGroupForUser(organization);
         organizationId = organizationId < 0L ? 0L : organizationId;
      } catch (Exception var30) {
         this.logger.error(var30);
      }

      AbilityDao abilityDao = new AbilityDao();
      boolean readAuthority = false;
      boolean manageAuthority = false;
      int abilityCount = abilityDao.hasAbilityByUserId(loginUser.getUser_id(), "Insight Read Authority");
      if (abilityCount >= 1) {
         readAuthority = true;
      }

      abilityCount = abilityDao.hasAbilityByUserId(loginUser.getUser_id(), "Insight Manage Authority");
      if (abilityCount >= 1) {
         manageAuthority = true;
      }

      if (user.getLocale() == null) {
         if (cookieLocale == null) {
            cookieLocale = "en";
         }
      } else {
         cookieLocale = user.getLocale();
      }

      if (cookieLocale == null) {
         cookieLocale = "en";
      }

      String locale = cookieLocale.toUpperCase();
      V2SystemServiceImpl.LanguageCodeForInsight localeCode = (V2SystemServiceImpl.LanguageCodeForInsight)Optional.ofNullable(Enums.getIfPresent(V2SystemServiceImpl.LanguageCodeForInsight.class, locale).orNull()).orElse(V2SystemServiceImpl.LanguageCodeForInsight.EN);
      String language = localeCode.getCode();
      InsightServerDao insightServerDao = new InsightServerDao();
      InsightServerEntity insightServerEntity = insightServerDao.getInsightServerInfo();
      V2InsightServerSettings insightServerSettings = new V2InsightServerSettings();
      insightServerSettings.setFromInsightServerEntity(insightServerEntity);
      String secretKey = insightServerSettings.getSecurity_key();
      String hashInputStr = secretKey + user.getUser_id() + time + organizationId + userOrganizationId + readAuthority + manageAuthority;
      String hash = Hashing.sha256().hashString(hashInputStr, StandardCharsets.UTF_8).toString();
      String lifetime = "43200";
      if (StrUtils.isNotEmpty(token)) {
         Date expiration = this.tokenUtils.getExpirationDateFromToken(token);
         Long current = System.currentTimeMillis();
         Long exp = expiration.getTime();
         lifetime = (exp - current) / 1000L + "";
      }

      String insightIP = insightServerSettings.getIp();
      String insightPort = String.valueOf(insightServerSettings.getHttp_port());
      String insightUrl = "http://" + insightIP;
      if (insightServerSettings.getHttp_port() == 0) {
         insightUrl = insightUrl.replace("http://", "https://");
         String httpsPort = String.valueOf(insightServerSettings.getHttps_port());
         if (!StrUtils.isEmpty(httpsPort)) {
            insightPort = httpsPort;
         }
      }

      insightUrl = insightUrl + ":" + insightPort;
      return V2InsightSystemInfoResource.V2InsightSystemInfoResourceBuilder.aV2InsightSystemInfoResource().userId(user.getUser_id()).time(time).hash(hash).lang(language).lifetime(lifetime).organizationId((int)organizationId).userOrganizationId((int)userOrganizationId).read(readAuthority).manage(manageAuthority).insightUrl(insightUrl).build();
   }

   public V2RuleManagerSystemInfoResource getRuleManagerSystemInfo() throws Exception {
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      TokenRegistry tokenRegistry = TokenRegistry.getTokenRegistry();
      String ruleManagerURL = CommonConfig.get("rulemanager.url");
      String ruleManagerAPIKey = CommonConfig.get("rulemanager.apiKey");
      String hwUniqueKey = LicenseMag.getHWUniqueKey();
      String userId = SecurityUtils.getUserContainer().getUser().getUser_id();
      Locale locale = SecurityUtils.getLocale();
      String token = tokenRegistry.issueTokenFor3rdParty(ExternalSystemUtils.SYSTEM_RULE_MANAGER, userId);
      Date currentTime = new Date();
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
      sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
      String time = sdf.format(currentTime);
      String beforeHash = hwUniqueKey + userId + time;
      String mHash = SecurityUtils.getHashSha(beforeHash, 2);
      if (!locale.equals(Locale.KOREAN) && !locale.equals(Locale.GERMAN) && !locale.equals(Locale.ENGLISH)) {
         locale = Locale.ENGLISH;
      }

      AbilityUtils ability = new AbilityUtils();
      String authcode;
      if (ability.checkAuthority("RuleManager HQ")) {
         authcode = "R0003";
      } else {
         authcode = "R0004";
      }

      return V2RuleManagerSystemInfoResource.V2RuleManagerSystemInfoResourceBuilder.aV2RuleManagerSystemInfoResource().ruleManagerURL(ruleManagerURL).ruleManagerAPIKey(ruleManagerAPIKey).mHash(mHash).userId(userId).time(time).authCode(authcode).token(token).langCode(locale.getLanguage()).build();
   }

   public V2MagicInfoVersionResource getMagicInfoVersion() throws Exception {
      String wsrmVersion = CommonConfig.get("wsrm.premiumVersion");
      if (wsrmVersion == null) {
         wsrmVersion = CommonConfig.get("wsrm.liteVersion");
      }

      String version = wsrmVersion.substring(wsrmVersion.lastIndexOf("-") + 1);
      return V2MagicInfoVersionResource.V2MagicInfoVersionResourceBuilder.aV2MagicInfoVersionResource().version(version).build();
   }

   public String getTimeOfMagicInfoServer() {
      return (new SimpleDateFormat("yyyy-MM-dd HH:mm:ssZ")).format(new Timestamp(System.currentTimeMillis()));
   }

   public V2MagicInfoConfigResource getConfig(String[] configIds) throws Exception {
      V2MagicInfoConfigResource resource = new V2MagicInfoConfigResource();
      List configList = resource.getConfigList();
      String[] var4 = configIds;
      int var5 = configIds.length;

      for(int var6 = 0; var6 < var5; ++var6) {
         String id = var4[var6];
         V2CommonKeyValueResource configResource = null;
         String var9 = id.toUpperCase();
         byte var10 = -1;
         switch(var9.hashCode()) {
         case -1195944144:
            if (var9.equals("SCHEDULE_MAX_CHANNEL_COUNT")) {
               var10 = 1;
            }
            break;
         case -855336228:
            if (var9.equals("SECURITY_FILTER_SRC")) {
               var10 = 2;
            }
            break;
         case 845079604:
            if (var9.equals("MAGICINFO_VERSION")) {
               var10 = 0;
            }
         }

         switch(var10) {
         case 0:
            configResource = new V2CommonKeyValueResource();
            configResource.setKey("MAGICINFO_VERSION");
            configResource.setValue(CommonConfig.get("wsrm.premiumVersion"));
            break;
         case 1:
            configResource = this.getMaxScheduleCount();
            break;
         case 2:
            configResource = this.getContentSecurityPolicy();
         }

         if (configList != null) {
            configList.add(configResource);
         }
      }

      return resource;
   }

   private V2CommonKeyValueResource getMaxScheduleCount() throws Exception {
      V2CommonKeyValueResource configResource = new V2CommonKeyValueResource();
      configResource.setKey("SCHEDULE_MAX_CHANNEL_COUNT");
      long maxChannelCount = 10L;

      try {
         maxChannelCount = Long.valueOf(CommonConfig.get("scheudle.max.channel.count"));
         if (maxChannelCount > 99L) {
            maxChannelCount = 99L;
         } else if (maxChannelCount < 10L) {
            maxChannelCount = 10L;
         }
      } catch (Exception var5) {
         maxChannelCount = 10L;
      }

      configResource.setValue(maxChannelCount);
      return configResource;
   }

   private V2CommonKeyValueResource getContentSecurityPolicy() {
      V2CommonKeyValueResource resource = new V2CommonKeyValueResource();
      resource.setKey("SECURITY_FILTER_SRC");
      resource.setValue("");

      try {
         String csp = CommonConfig.get("security.filter.src");
         if (!StringUtils.isEmpty(csp)) {
            csp = csp.replace(",", " ");
            resource.setValue(csp);
         }
      } catch (Exception var3) {
      }

      return resource;
   }

   public V2ContentDownloadThrottling getDownloadThrottlingStatus() throws Exception {
      ContentDownloadPool pool = ContentDownloadPool.getInstance();
      V2ContentDownloadThrottling item = new V2ContentDownloadThrottling();
      item.setIsEnabled(pool.isEnabled());
      item.setIsAvailable(pool.isAvailable());
      Map dataMap = pool.get();
      if (dataMap != null) {
         item.setStatus(new ArrayList(dataMap.values()));
      }

      return item;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public List getResultOfCheckingDbSchemeAndItemsFromJson() throws Exception {
      int DB_SCHEME_TEST_ID = true;
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (!RoleUtils.isServerAdminRole(userContainer.getUser())) {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
      } else {
         List dbSchemeResourceList = new ArrayList();
         HashMap serverHealthMap = (HashMap)CacheFactory.getCache().get("SERVER_HEALTH_MAP");
         V2DbSchemeResource resultCheckDbScheme = new V2DbSchemeResource();
         resultCheckDbScheme.setTestId(10000);
         if (null != serverHealthMap && null != serverHealthMap.get("db_integrity")) {
            String description = (new Gson()).toJson(serverHealthMap);
            resultCheckDbScheme.setCheckResult(false);
            resultCheckDbScheme.setDescription(description);
         } else {
            resultCheckDbScheme.setCheckResult(true);
         }

         dbSchemeResourceList.add(resultCheckDbScheme);
         DbSchemeDao dbSchemeDao = new DbSchemeDao();
         List dbSchemeCheckResult = dbSchemeDao.getDbSchemeCheckResult();
         if (null == dbSchemeCheckResult) {
            return dbSchemeResourceList;
         } else {
            Iterator var8 = dbSchemeCheckResult.iterator();

            while(var8.hasNext()) {
               DbSchemeEntity dbSchemeEntity = (DbSchemeEntity)var8.next();
               V2DbSchemeResource v2DbSchemeResource = new V2DbSchemeResource();
               v2DbSchemeResource.setFromDbSchemeEntity(dbSchemeEntity);
               dbSchemeResourceList.add(v2DbSchemeResource);
            }

            return dbSchemeResourceList;
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public List checkDbSchemeAndItemsFromJson() throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (!RoleUtils.isServerAdminRole(userContainer.getUser())) {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
      } else {
         ServerSetupInfo serverSetupInfo = ServerSetupInfoImpl.getInstance();
         serverSetupInfo.checkCheckingItemsFromJson();
         return this.getResultOfCheckingDbSchemeAndItemsFromJson();
      }
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public V2DbSchemeResource resolveDbSchemeIssue(int testId) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (!RoleUtils.isServerAdminRole(userContainer.getUser())) {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
      } else {
         DbSchemeDao dbSchemeDao = new DbSchemeDao();
         dbSchemeDao.deleteDbSchemeCheckResult();
         DatabaseManagerDao dao = new DatabaseManagerDao();
         ServerSetupInfo serverSetupInfo = ServerSetupInfoImpl.getInstance();
         V2DbSchemeResource v2DbSchemeResource = new V2DbSchemeResource();
         v2DbSchemeResource.setTestId(testId);
         List dbSchemeCheckItems = serverSetupInfo.loadDbSchemeCheckList();

         try {
            Iterator var8 = dbSchemeCheckItems.iterator();

            while(var8.hasNext()) {
               DbSchemeCheckEntity dbSchemeCheckEntity = (DbSchemeCheckEntity)var8.next();
               if (dbSchemeCheckEntity.getTestId() == testId) {
                  List resolveQuery = dbSchemeCheckEntity.getResolveQuery();
                  int retMsg = 0;

                  String query;
                  for(Iterator var12 = resolveQuery.iterator(); var12.hasNext(); this.logger.error("[resolveDbSchemeIssue] " + query + " -> " + retMsg)) {
                     query = (String)var12.next();
                     if (query.toLowerCase().contains("delete")) {
                        retMsg = dao.runDeleteQuery(query);
                     } else if (query.toLowerCase().contains("insert")) {
                        retMsg = dao.runInsertQuery(query);
                     }
                  }

                  v2DbSchemeResource.setCheckResult(true);
                  this.checkDbSchemeAndItemsFromJson();
                  break;
               }
            }
         } catch (Exception var14) {
            this.logger.error(var14.getMessage());
            v2DbSchemeResource.setCheckResult(false);
         }

         return v2DbSchemeResource;
      }
   }

   public V2LoginConfigResource getLoginConfig() throws Exception {
      V2LoginConfigResource resource = new V2LoginConfigResource();
      LoginPageDao loginPageDao = new LoginPageDao();
      LoginPageEntity loginPageEntity = loginPageDao.getLoginPageInfo();
      if (loginPageEntity != null && loginPageEntity.getUse_custom_config()) {
         resource.setFromLoginPageEntity(loginPageEntity);
         if (loginPageEntity.getUse_custom_login_page_image() && !StringUtils.isEmpty(loginPageEntity.getImg_file_name())) {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            Path path = Paths.get(CommonConfig.get("UPLOAD_HOME") + File.separator + "login" + File.separator + loginPageEntity.getImg_file_name());
            if (Files.exists(path, new LinkOption[0])) {
               InputStream inputStream = Files.newInputStream(path);
               ImageIO.setUseCache(false);
               Iterator readers = ImageIO.getImageReadersBySuffix("png");
               if (readers.hasNext()) {
                  ImageReader imageReader = (ImageReader)readers.next();
                  BufferedImage sourceImage = ImageIO.read(inputStream);
                  int originalWidth = sourceImage.getWidth() / 5;
                  int originalHeight = sourceImage.getHeight() / 5;
                  ToolkitImage image = (ToolkitImage)sourceImage.getScaledInstance(originalWidth, originalHeight, 4);
                  image.getWidth();
                  BufferedImage buffered = image.getBufferedImage();

                  try {
                     ImageIO.write(buffered, "png", outputStream);
                  } catch (Exception var18) {
                     this.logger.error(var18.toString(), var18);
                  } finally {
                     imageReader.dispose();
                  }
               }

               inputStream.close();
               outputStream.flush();
               outputStream.close();
               String loginImgSrc = "data:image/png;base64," + Base64.getEncoder().encodeToString(outputStream.toByteArray());
               resource.setLoginPageImageSrc(loginImgSrc);
               return resource;
            } else {
               throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"file path"});
            }
         } else {
            return resource;
         }
      } else {
         return new V2LoginConfigResource();
      }
   }

   public V2LoginConfigResource saveLoginPageImage(MultipartFile multipartFile, HttpServletRequest request) throws Exception {
      V2LoginConfigResource resource = new V2LoginConfigResource();

      try {
         HttpSession session = request.getSession();
         session.setMaxInactiveInterval(3600);
         String fileName = multipartFile.getOriginalFilename();
         int pos = fileName.lastIndexOf(".");
         String ext = fileName.substring(pos + 1);
         if (!ext.equalsIgnoreCase("png")) {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_LOGIN_PAGE_IMAGE_FILE_UPLOAD);
         } else {
            boolean isFileSuccess = FileUploadCommonHelper.saveFileToDisk(fileName, multipartFile, "login_page_image");
            if (!isFileSuccess) {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_LOGIN_PAGE_IMAGE_FILE_UPLOAD);
            } else {
               LoginPageDao loginPageDao = new LoginPageDao();
               LoginPageEntity loginPageEntity = loginPageDao.getLoginPageInfo();
               if (null == loginPageEntity) {
                  loginPageEntity = new LoginPageEntity();
               }

               resource.setFromLoginPageEntity(loginPageEntity);
               return resource;
            }
         }
      } catch (Exception var11) {
         this.logger.error("Fail to add new software", var11);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_LOGIN_PAGE_IMAGE_FILE_UPLOAD);
      }
   }

   static enum LanguageCodeForInsight {
      EN("en_US"),
      KO("ko_KR"),
      AR("ar_AE"),
      DE("de_DE"),
      ES("es_ES"),
      FA("fa_IR"),
      FR("fr_FR"),
      IT("it_IT"),
      JA("ja_JP"),
      PT("pt_PT"),
      RU("ru_RU"),
      SV("sv_SE"),
      TR("tr_TR"),
      ZH_CN("zh_CN"),
      ZH_TW("zh_TW"),
      ZHCN("zh_CN"),
      ZHTW("zh_TW"),
      PL("pl_PL"),
      VI("vi_VN"),
      FI("fi_FI");

      private String code;

      private LanguageCodeForInsight(String code) {
         this.code = code;
      }

      public String getCode() {
         return this.code;
      }
   }
}
