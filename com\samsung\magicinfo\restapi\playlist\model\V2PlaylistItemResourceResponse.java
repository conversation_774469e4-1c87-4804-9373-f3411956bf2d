package com.samsung.magicinfo.restapi.playlist.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Timestamp;
import java.util.List;

@JsonInclude(Include.NON_NULL)
@ApiModel(
   description = "(startDate, lastModifiedDate) are based on server local time.\r\n(expiredDate) is based on user local time."
)
public class V2PlaylistItemResourceResponse {
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000",
      dataType = "string",
      value = "Id of specific playlist , gets the Id of the playlist."
   )
   private String playlistId = "";
   @ApiModelProperty(
      example = "1",
      dataType = "long",
      value = "Id of specific version , displays the version Id of the playlist."
   )
   private Long versionId = 0L;
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000",
      dataType = "string",
      value = "ID of the content"
   )
   private String contentId = "";
   @ApiModelProperty(
      example = "Content",
      dataType = "string",
      value = "Name of the content"
   )
   private String contentName = "";
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000",
      dataType = "string",
      value = "ID of the thumbnail file to reference when displaying thumbnails of the playlist"
   )
   private String thumbFileId = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Name of the thumbnail file to reference when displaying thumbnails of the playlist"
   )
   private String thumbFileName = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Location of the thumbnail file to reference when displaying thumbnails of the playlist"
   )
   private String thumbFilePath;
   @ApiModelProperty(
      dataType = "string",
      value = "Resolution of the playlist"
   )
   private String resolution;
   @ApiModelProperty(
      example = "0",
      dataType = "string",
      value = "Playtime of the content calculated in seconds for movie files such as LFT,VWL,MOVIE, ..."
   )
   private String playTime = "";
   @ApiModelProperty(
      dataType = "boolean",
      value = "Check if you have play time"
   )
   private boolean hasDefaultPlayTime = false;
   @ApiModelProperty(
      example = "1",
      dataType = "long",
      value = "Not exactly known"
   )
   private Long contentOrder = 0L;
   @ApiModelProperty(
      example = "1",
      dataType = "long",
      value = "Duration of the content"
   )
   private Long contentDuration = 0L;
   @ApiModelProperty(
      example = "1",
      dataType = "string",
      value = "Not exactly known"
   )
   private String contentDurationMilli = "";
   @ApiModelProperty(
      example = "2016-01-01 00:00:00",
      dataType = "string",
      value = "Indicates the date of creation of the playlist."
   )
   private String startDate = null;
   @ApiModelProperty(
      example = "2016-01-01 00:00:00",
      dataType = "string",
      value = "Indicates the expiration date of the playlist."
   )
   private String expiredDate = null;
   @ApiModelProperty(
      allowableValues = "LFD,IMAGE,MOVIE,OFFICE,PDF,FLASH,SOUND,DLK,FTP,CIFS,STRM,VWL,HTML,URL,SAPP,DLKT",
      example = "IMAGE",
      dataType = "string",
      value = "media type of content , set media type of content"
   )
   private String mediaType;
   @ApiModelProperty(
      example = "1",
      dataType = "long",
      value = "Not exactly known"
   )
   private Long syncOrder;
   @ApiModelProperty(
      example = "5",
      dataType = "string",
      value = "Sync Indicates duration"
   )
   private String syncDuration;
   @ApiModelProperty(
      example = "Y",
      dataType = "string",
      value = "Sync Check if it is currently active."
   )
   private String syncStatus;
   @ApiModelProperty(
      example = "0",
      dataType = "string",
      value = "Get sync play ID"
   )
   private String syncPlayId;
   @ApiModelProperty(
      dataType = "string",
      value = "Not exactly known"
   )
   private String tagMatchType;
   @ApiModelProperty(
      example = "25,26",
      dataType = "string",
      value = "Indicates tag List.\r\nusage: [tagId],[tagId],..."
   )
   private String tagList;
   @ApiModelProperty(
      dataType = "boolean",
      value = "Check to see if you are referring to the current sub playlist."
   )
   private boolean isSubPlaylist = false;
   @ApiModelProperty(
      example = "TAG1,TAG2",
      dataType = "string",
      value = "Tag value of playlist.\r\nusage: [tagValue],[tagValue],..."
   )
   private String tagValue;
   @ApiModelProperty(
      dataType = "int",
      value = "Not exactly known"
   )
   private int randomCount = 0;
   @ApiModelProperty(
      dataType = "long",
      value = "Play weight value"
   )
   private long playWeight = 1L;
   @ApiModelProperty(
      dataType = "v2PlaylistContentEffectResource",
      value = "Indicates playlist effects."
   )
   V2PlaylistContentEffectResource effects = null;
   @ApiModelProperty(
      dataType = "V2PlaylistContentTagResource",
      value = "Indicates content Tag."
   )
   V2PlaylistContentTagResource contentTag = null;
   @ApiModelProperty(
      dataType = "V2PlaylistItemTagEffectResource",
      value = "Indicates playlist tagEffect."
   )
   V2PlaylistItemTagEffectResource tagEffect = null;
   @ApiModelProperty(
      dataType = "long",
      value = "File size of the content"
   )
   private Long totalSize;
   @ApiModelProperty(
      example = "2016-01-01 00:00:00",
      dataType = "timestamp",
      value = "Last modification date of the playlist"
   )
   private Timestamp lastModifiedDate = null;
   @ApiModelProperty(
      dataType = "string",
      example = "SPLAYER",
      value = "DeviceType, minimum supported deviceType"
   )
   private String deviceType;
   @ApiModelProperty(
      dataType = "string",
      value = "Device type version of the playlist"
   )
   private String deviceTypeVersion;
   @ApiModelProperty(
      dataType = "list",
      value = "Resource of content tag",
      reference = "V2TagResourceResponse"
   )
   private List tags;
   @ApiModelProperty(
      dataType = "list",
      value = "Resource of content files",
      reference = "V2ContentFileResourceResponse"
   )
   private List files;
   @ApiModelProperty(
      example = "creator",
      dataType = "string",
      value = "Content creator ID"
   )
   private String creator_id = "";
   @ApiModelProperty(
      example = "TAG1,TAG2",
      dataType = "string",
      value = "Tag value of playlist.\r\nusage: [tagValue],[tagValue],..."
   )
   private String contentTagValue;
   @ApiModelProperty(
      dataType = "long",
      value = "Playtime of the content calculated in seconds for movie files such as LFT,VWL,MOVIE, ..."
   )
   private Long playTimeInSeconds = null;

   public V2PlaylistItemResourceResponse() {
      super();
   }

   public String getPlaylistId() {
      return this.playlistId;
   }

   public void setPlaylistId(String playlistId) {
      this.playlistId = playlistId;
   }

   public Long getVersionId() {
      return this.versionId;
   }

   public void setVersionId(Long versionId) {
      this.versionId = versionId;
   }

   public String getContentId() {
      return this.contentId;
   }

   public void setContentId(String contentId) {
      this.contentId = contentId;
   }

   public String getContentName() {
      return this.contentName;
   }

   public void setContentName(String contentName) {
      this.contentName = contentName;
   }

   public String getThumbFileId() {
      return this.thumbFileId;
   }

   public void setThumbFileId(String thumbFileId) {
      this.thumbFileId = thumbFileId;
   }

   public String getThumbFileName() {
      return this.thumbFileName;
   }

   public void setThumbFileName(String thumbFileName) {
      this.thumbFileName = thumbFileName;
   }

   public String getThumbFilePath() {
      return this.thumbFilePath;
   }

   public void setThumbFilePath(String thumbFilePath) {
      this.thumbFilePath = thumbFilePath;
   }

   public String getResolution() {
      return this.resolution;
   }

   public void setResolution(String resolution) {
      this.resolution = resolution;
   }

   public String getPlayTime() {
      return this.playTime;
   }

   public void setPlayTime(String playTime) {
      this.playTime = playTime;
   }

   public boolean isHasDefaultPlayTime() {
      return this.hasDefaultPlayTime;
   }

   public void setHasDefaultPlayTime(boolean hasDefaultPlayTime) {
      this.hasDefaultPlayTime = hasDefaultPlayTime;
   }

   public Long getContentOrder() {
      return this.contentOrder;
   }

   public void setContentOrder(Long contentOrder) {
      this.contentOrder = contentOrder;
   }

   public Long getContentDuration() {
      return this.contentDuration;
   }

   public void setContentDuration(Long contentDuration) {
      this.contentDuration = contentDuration;
   }

   public String getContentDurationMilli() {
      return this.contentDurationMilli;
   }

   public void setContentDurationMilli(String contentDurationMilli) {
      this.contentDurationMilli = contentDurationMilli;
   }

   public String getStartDate() {
      return this.startDate;
   }

   public void setStartDate(String startDate) {
      this.startDate = startDate;
   }

   public String getExpiredDate() {
      return this.expiredDate;
   }

   public void setExpiredDate(String expiredDate) {
      this.expiredDate = expiredDate;
   }

   public String getMediaType() {
      return this.mediaType;
   }

   public void setMediaType(String mediaType) {
      this.mediaType = mediaType;
   }

   public V2PlaylistContentEffectResource getEffects() {
      return this.effects;
   }

   public void setEffects(V2PlaylistContentEffectResource effects) {
      this.effects = effects;
   }

   public V2PlaylistContentTagResource getContentTag() {
      return this.contentTag;
   }

   public void setContentTag(V2PlaylistContentTagResource contentTag) {
      this.contentTag = contentTag;
   }

   public V2PlaylistItemTagEffectResource getTagEffect() {
      return this.tagEffect;
   }

   public void setTagEffect(V2PlaylistItemTagEffectResource tagEffect) {
      this.tagEffect = tagEffect;
   }

   public Long getSyncOrder() {
      return this.syncOrder;
   }

   public void setSyncOrder(Long syncOrder) {
      this.syncOrder = syncOrder;
   }

   public String getSyncDuration() {
      return this.syncDuration;
   }

   public void setSyncDuration(String syncDuration) {
      this.syncDuration = syncDuration;
   }

   public String getSyncStatus() {
      return this.syncStatus;
   }

   public void setSyncStatus(String syncStatus) {
      this.syncStatus = syncStatus;
   }

   public String getSyncPlayId() {
      return this.syncPlayId;
   }

   public void setSyncPlayId(String syncPlayId) {
      this.syncPlayId = syncPlayId;
   }

   public boolean getIsSubPlaylist() {
      return this.isSubPlaylist;
   }

   public void setIsSubPlaylist(boolean isSubPlaylist) {
      this.isSubPlaylist = isSubPlaylist;
   }

   public String getTagMatchType() {
      return this.tagMatchType;
   }

   public void setTagMatchType(String tagMatchType) {
      this.tagMatchType = tagMatchType;
   }

   public String getTagList() {
      return this.tagList;
   }

   public void setTagList(String tagList) {
      this.tagList = tagList;
   }

   public String getTagValue() {
      return this.tagValue;
   }

   public void setTagValue(String tagValue) {
      this.tagValue = tagValue;
   }

   public int getRandomCount() {
      return this.randomCount;
   }

   public void setRandomCount(int randomCount) {
      this.randomCount = randomCount;
   }

   public long getPlayWeight() {
      return this.playWeight;
   }

   public void setPlayWeight(long playWeight) {
      this.playWeight = playWeight;
   }

   public Long getTotalSize() {
      return this.totalSize;
   }

   public void setTotalSize(Long totalSize) {
      this.totalSize = totalSize;
   }

   public Timestamp getLastModifiedDate() {
      return this.lastModifiedDate;
   }

   public void setLastModifiedDate(Timestamp lastModifiedDate) {
      this.lastModifiedDate = lastModifiedDate;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public void setDeviceType(String deviceType) {
      this.deviceType = deviceType;
   }

   public String getDeviceTypeVersion() {
      return this.deviceTypeVersion;
   }

   public void setDeviceTypeVersion(String deviceTypeVersion) {
      this.deviceTypeVersion = deviceTypeVersion;
   }

   public List getTags() {
      return this.tags;
   }

   public void setTags(List tags) {
      this.tags = tags;
   }

   public List getFiles() {
      return this.files;
   }

   public void setFiles(List files) {
      this.files = files;
   }

   public String getCreator_id() {
      return this.creator_id;
   }

   public void setCreator_id(String creator_id) {
      this.creator_id = creator_id;
   }

   public String getContentTagValue() {
      return this.contentTagValue;
   }

   public void setContentTagValue(String contentTagValue) {
      this.contentTagValue = contentTagValue;
   }

   public Long getPlayTimeInSeconds() {
      return this.playTimeInSeconds;
   }

   public void setPlayTimeInSeconds(Long playTimeInSeconds) {
      this.playTimeInSeconds = playTimeInSeconds;
   }
}
