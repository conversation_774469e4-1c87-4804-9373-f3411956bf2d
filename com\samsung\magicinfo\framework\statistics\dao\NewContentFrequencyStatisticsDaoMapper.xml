<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.samsung.magicinfo.framework.statistics.dao.NewContentFrequencyStatisticsDaoMapper">

<sql id="where_deviceIdList">
	<if test="deviceIdList != null and deviceIdList.length > 0">
		<foreach item="item"  index="index" collection="deviceIdList" open="AND (" separator="OR" close=")">	
		DEVICE_ID = #{item}	
		</foreach>		
	</if>
</sql>

<sql id="where_contentIdList">
	<if test="contentIdList != null and contentIdList.length > 0">
		<foreach item="item"  index="index" collection="contentIdList" open="AND (" separator="OR" close=")">	
		CONTENT_ID = #{item}	
		</foreach>		
	</if>
</sql>

<sql id="outerQueryClauses_begin">
	SELECT B.CONTENT_NAME, A.*, TO_CHAR((A.DURATION || ' second')::interval, 'HH24:MI:SS') AS DURATION_STRING FROM(
</sql>
<sql id="outerQueryClauses_begin" databaseId="mssql">	
	SELECT B.CONTENT_NAME, A.*, CONVERT(VARCHAR(8), DATEADD(SECOND, DURATION, '19000101'), 8) AS DURATION_STRING FROM(
</sql>
<sql id="outerQueryClauses_begin" databaseId="mysql">
	SELECT B.CONTENT_NAME, A.*, TIME_FORMAT(SEC_TO_TIME(A.DURATION),'%H:%i:%s') AS DURATION_STRING FROM(
</sql>

<sql id="outerQueryClauses_end">
	) A, MI_CMS_INFO_CONTENT B WHERE A.CONTENT_ID = B.CONTENT_ID
</sql>

<select id="getYesterdayListBy" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		<choose>
			<when test="unit  == 'DAY'">
			SELECT  CONTENT_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				FROM MI_STATISTICS_CONTENT_DAY 
			</when>
			<when test="unit  == 'HOUR'">
				SELECT CONTENT_ID, DATE_TRUNC('HOUR', START_TIME)::TIME AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				 FROM MI_STATISTICS_CONTENT_HOUR
			</when>
			<when test="unit  == 'SECOND'">
				SELECT CONTENT_ID, START_TIME::TIME AS TIME_STRING, DEVICE_ID, DURATION
				 FROM MI_STATISTICS_CONTENT_SECOND
			</when>
		</choose>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			AND START_TIME::DATE = DATE_TRUNC('DAY',CURRENT_DATE - interval '1days')::DATE
		</where>
		<choose>
			<when test="unit  == 'DAY'">
			 GROUP BY CONTENT_ID
			</when>
			<when test="unit  == 'HOUR'">
			 GROUP BY CONTENT_ID, START_TIME 
			 ORDER BY TIME_STRING, CONTENT_ID
			</when>
			<when test="unit  == 'SECOND'">
			  ORDER BY CONTENT_ID, TIME_STRING, DEVICE_ID
			</when>
		</choose>
  	<include refid="outerQueryClauses_end"/>
</select>

<select id="getYesterdayListBy" databaseId="mssql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		<choose>
			<when test="unit  == 'DAY'">
			SELECT  CONTENT_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				FROM MI_STATISTICS_CONTENT_DAY 
			</when>
			<when test="unit  == 'HOUR'">
				SELECT CONTENT_ID, CONVERT(VARCHAR(8), DATEADD(HOUR, DATEDIFF(HOUR, 0, START_TIME), 0), 108) AS TIME_STRING, 
				SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				 FROM MI_STATISTICS_CONTENT_HOUR
			</when>
			<when test="unit  == 'SECOND'">
				SELECT CONTENT_ID, CONVERT(VARCHAR(8), START_TIME, 108) AS TIME_STRING, DEVICE_ID, DURATION
				 FROM MI_STATISTICS_CONTENT_SECOND
			</when>
		</choose>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			AND CONVERT(VARCHAR(10), START_TIME, 120) = CONVERT(VARCHAR(10), DATEADD(dd, -1, DATEADD(DAY, DATEDIFF(DAY, 0, GETDATE()) , 0)), 120)
		</where>
		<choose>
			<when test="unit  == 'DAY'">
			 GROUP BY CONTENT_ID
			</when>
			<when test="unit  == 'HOUR'">
			 GROUP BY CONTENT_ID, START_TIME 
			</when>
		</choose>
  	<include refid="outerQueryClauses_end"/>
  	 <if test="unit  == 'HOUR'">
  		ORDER BY TIME_STRING, CONTENT_ID
  	</if>
  	<if test="unit  == 'SECOND'">
  		ORDER BY CONTENT_ID, TIME_STRING, DEVICE_ID
  	</if>
</select>

<select id="getYesterdayListBy" databaseId="mysql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		<choose>
			<when test="unit  == 'DAY'">
			SELECT  CONTENT_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				FROM MI_STATISTICS_CONTENT_DAY 
			</when>
			<when test="unit  == 'HOUR'">
				SELECT CONTENT_ID, DATE_FORMAT(START_TIME,'%H:00:00') AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				 FROM MI_STATISTICS_CONTENT_HOUR
			</when>
			<when test="unit  == 'SECOND'">
				SELECT CONTENT_ID, DATE_FORMAT(START_TIME,'%H:%i:%s') AS TIME_STRING, DEVICE_ID, DURATION
				 FROM MI_STATISTICS_CONTENT_SECOND
			</when>
		</choose>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			AND DATE_FORMAT(START_TIME, '%Y-%m-%d') = DATE_FORMAT(NOW() - INTERVAL 1 DAY, '%Y-%m-%d')
		</where>
		<choose>
			<when test="unit  == 'DAY'">
			 GROUP BY CONTENT_ID
			</when>
			<when test="unit  == 'HOUR'">
			 GROUP BY CONTENT_ID, START_TIME 
			 ORDER BY TIME_STRING, CONTENT_ID
			</when>
			<when test="unit  == 'SECOND'">
			  ORDER BY CONTENT_ID, TIME_STRING, DEVICE_ID
			</when>
		</choose>
  	<include refid="outerQueryClauses_end"/>
</select>


<sql id="conditionStartTimeEqToTruncCurrDateMonth">
	<choose>
		<when test="isThis">
			AND START_TIME = DATE_TRUNC('MONTH',CURRENT_DATE)
		</when>
		<otherwise>
			AND START_TIME = DATE_TRUNC('MONTH',CURRENT_DATE - interval '1 months')
		</otherwise>
	</choose>	
</sql>
<sql id="conditionStartTimeEqToTruncCurrDateMonth" databaseId="mssql">
	<choose>
		<when test="isThis">
			AND START_TIME = DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0)
		</when>
		<otherwise>
			AND START_TIME = DATEADD(mm, -1, DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()) , 0))
		</otherwise>
	</choose>
</sql>
<sql id="conditionStartTimeEqToTruncCurrDateMonth" databaseId="mysql">
	<choose>
		<when test="isThis">
			AND START_TIME = DATE_FORMAT(NOW(), '%Y-%m-01 %00:%00:%00')
		</when>
		<otherwise>
			AND START_TIME = DATE_FORMAT(NOW() - INTERVAL 1 MONTH, '%Y-%m-01 %00:%00:%00 ')
		</otherwise>
	</choose>	
</sql>

<select id="getMonthListByMonth" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT CONTENT_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="conditionStartTimeEqToTruncCurrDateMonth"/>
		</where>
		GROUP BY CONTENT_ID
  	<include refid="outerQueryClauses_end"/>
</select>

<sql id="conditionMonthStartTimeEqMonthCurrDate">
	<choose> 
		<when test="isThis">
			AND EXTRACT(MONTH FROM START_TIME) = EXTRACT(MONTH FROM CURRENT_DATE)
		</when>
		<otherwise>
			AND EXTRACT(MONTH FROM START_TIME) = EXTRACT(MONTH FROM CURRENT_DATE - interval '1 months')
		</otherwise>
	</choose>
</sql>
<sql id="conditionMonthStartTimeEqMonthCurrDate" databaseId="mssql">
	<choose> 
		<when test="isThis">
			AND MONTH(START_TIME) = MONTH(GETDATE())
		</when>
		<otherwise>
			AND MONTH(START_TIME) = MONTH(GETDATE()) - 1
		</otherwise>
	</choose>
</sql>
<sql id="conditionMonthStartTimeEqMonthCurrDate" databaseId="mysql">
	<choose> 
		<when test="isThis">
			AND MONTH(START_TIME) = MONTH(NOW())
		</when>
		<otherwise>
			AND MONTH(START_TIME) = MONTH(NOW()) -1 
		</otherwise>
	</choose>
</sql>
<select id="getMonthListByDow" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT PLAY_DOW, CONTENT_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_DAY
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="conditionMonthStartTimeEqMonthCurrDate"/>	
		</where>
		GROUP BY CONTENT_ID, PLAY_DOW
  	<include refid="outerQueryClauses_end"/>
  	ORDER BY PLAY_DOW, CONTENT_ID
</select>

<select id="getQuarterListByQuarter" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT CONTENT_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
				<choose>
				<when test="isThis">
					AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE) AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE)
				</when>
				<otherwise>
					 AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE - interval '3 months') AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE - interval '3 months')
				</otherwise>
			</choose>	
		</where>
		GROUP BY CONTENT_ID
  	<include refid="outerQueryClauses_end"/>
</select>

<select id="getQuarterListByQuarter" databaseId="mssql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT CONTENT_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<choose>
				<when test="isThis">
					AND YEAR(START_TIME) = YEAR(GETDATE()) AND LOG_QUARTER = DATENAME(Quarter, CAST(CONVERT(VARCHAR(8), DATEADD(month, 0 ,GETDATE())) AS DATETIME))
				</when>
				<otherwise>
					AND YEAR(START_TIME) = YEAR(DATEADD(month, -3 ,GETDATE())) AND LOG_QUARTER = DATENAME(Quarter, CAST(CONVERT(VARCHAR(8), DATEADD(month, -3 ,GETDATE())) AS DATETIME))
				</otherwise>
			</choose>	
		</where>
		GROUP BY CONTENT_ID
  	<include refid="outerQueryClauses_end"/>
</select>

<select id="getQuarterListByQuarter" databaseId="mysql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT CONTENT_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
				<choose>
				<when test="isThis">
					AND YEAR(START_TIME) = YEAR(NOW()) AND LOG_QUARTER = QUARTER(NOW())
				</when>
				<otherwise>
					 AND YEAR(START_TIME) = YEAR(NOW()) AND LOG_QUARTER = QUARTER(NOW()) -1
				</otherwise>
			</choose>	
		</where>
		GROUP BY CONTENT_ID
  	<include refid="outerQueryClauses_end"/>
</select>

<select id="getQuarterListByMonth" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT TO_CHAR(START_TIME,'Month') AS TIME_STRING, CONTENT_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
				<choose>
				<when test="isThis">
					AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE) AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE)
				</when>
				<otherwise>
					AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE - interval '3 months') AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE - interval '3 months')
				</otherwise>
			</choose>	
		</where>
		GROUP BY CONTENT_ID, TIME_STRING
		ORDER BY TIME_STRING, CONTENT_ID
  	<include refid="outerQueryClauses_end"/>
</select>

<select id="getQuarterListByMonth" databaseId="mssql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
	SELECT TIME_STRING, CONTENT_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION FROM (
		SELECT DateName( month , DateAdd( month , MONTH(START_TIME) , 0 ) - 1 ) AS TIME_STRING,
		CONTENT_ID, PLAY_COUNT, DURATION FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
				<choose>
				<when test="isThis">
					AND YEAR(START_TIME) = YEAR(GETDATE()) 
					AND LOG_QUARTER = DATENAME(Quarter, CAST(CONVERT(VARCHAR(8), DATEADD(month, 0 ,GETDATE())) AS DATETIME))
				</when>
				<otherwise>
					AND YEAR(START_TIME) = YEAR(DATEADD(month, -3 ,GETDATE()))
					AND LOG_QUARTER = DATENAME(Quarter, CAST(CONVERT(VARCHAR(8), DATEADD(month, -3 ,GETDATE())) AS DATETIME))
				</otherwise>
			</choose>	
		</where>
		) AS subquery
		GROUP BY CONTENT_ID, TIME_STRING
  	<include refid="outerQueryClauses_end"/>
  	ORDER BY TIME_STRING, CONTENT_ID
</select>

<select id="getQuarterListByMonth" databaseId="mysql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT MONTHNAME(START_TIME) AS TIME_STRING, CONTENT_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
				<choose>
				<when test="isThis">
					AND YEAR(START_TIME) = YEAR(NOW()) AND LOG_QUARTER = QUARTER(NOW())
				</when>
				<otherwise>
					AND YEAR(START_TIME) = YEAR(NOW()) AND LOG_QUARTER = QUARTER(NOW()) -1
				</otherwise>
			</choose>	
		</where>
		GROUP BY CONTENT_ID, TIME_STRING
		ORDER BY TIME_STRING, CONTENT_ID
  	<include refid="outerQueryClauses_end"/>
</select>

<sql id="truncYearCurrentDate">
	DATE_TRUNC('YEAR',CURRENT_DATE)
</sql>
<sql id="truncYearCurrentDate" databaseId="mssql">
	DATEADD(YEAR, DATEDIFF(YEAR, 0, GETDATE()), 0)
</sql>
<sql id="truncYearCurrentDate" databaseId="mysql">
	DATE_FORMAT(NOW(), '%Y-01-01 %00:%00:%00')
</sql>

<sql id="truncYearCurrentDateMinusOneYear">
	DATE_TRUNC('YEAR',CURRENT_DATE - interval '1 years')
</sql>
<sql id="truncYearCurrentDateMinusOneYear" databaseId="mssql">
	DATEADD(yy, -1, DATEADD(YEAR, DATEDIFF(YEAR, 0, GETDATE()) , 0))
</sql>
<sql id="truncYearCurrentDateMinusOneYear" databaseId="mysql">
	DATE_FORMAT(NOW() - INTERVAL 1 YEAR, '%Y-01-01 %00:%00:%00')
</sql>

<select id="getYearListByYear" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT CONTENT_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_YEAR
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
				<choose>
				<when test="isThis">
					 AND START_TIME = <include refid="truncYearCurrentDate"/>
				</when>
				<otherwise>
					AND START_TIME = <include refid="truncYearCurrentDateMinusOneYear"/>
				</otherwise>
			</choose>	
		</where>
		GROUP BY CONTENT_ID
  	<include refid="outerQueryClauses_end"/>
</select>

<sql id="conditionStartTimeYearEqCurrentYear">
	<choose>
		<when test="isThis">
			 AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE)
		</when>
		<otherwise>
			AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE - interval '1 years')
		</otherwise>
	</choose>
</sql>
<sql id="conditionStartTimeYearEqCurrentYear" databaseId="mssql">
	<choose>
		<when test="isThis">
			 AND YEAR(START_TIME) = YEAR(GETDATE())
		</when>
		<otherwise>
			AND YEAR(START_TIME) = YEAR(GETDATE()) - 1
		</otherwise>
	</choose>
</sql>
<sql id="conditionStartTimeYearEqCurrentYear" databaseId="mysql">
	<choose>
		<when test="isThis">
			 AND YEAR(START_TIME) = YEAR(NOW())
		</when>
		<otherwise>
			AND YEAR(START_TIME) = YEAR(NOW()) - 1
		</otherwise>
	</choose>
</sql>

<select id="getYearListByQuarter" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT LOG_QUARTER, CONTENT_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="conditionStartTimeYearEqCurrentYear"/>		
		</where>
		GROUP BY CONTENT_ID, LOG_QUARTER
  	<include refid="outerQueryClauses_end"/>
  	ORDER BY LOG_QUARTER, CONTENT_ID
</select>

<sql id="truncHourAndCastStartTime">
	DATE_TRUNC('HOUR', START_TIME)::TIME
</sql>
<sql id="truncHourAndCastStartTime" databaseId="mssql">
	CONVERT(VARCHAR(8), DATEADD(HOUR, DATEDIFF(HOUR, 0, START_TIME), 0), 108)
</sql>
<sql id="truncHourAndCastStartTime" databaseId="mysql">
	DATE_FORMAT(START_TIME,'%H:00:00')
</sql>

<select id="getWeekListByHour" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
	<include refid="outerQueryClauses_begin"/>
			SELECT CONTENT_ID, <include refid="truncHourAndCastStartTime"/> AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				 FROM MI_STATISTICS_CONTENT_HOUR
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}				
		</where>
		GROUP BY CONTENT_ID, START_TIME
	<include refid="outerQueryClauses_end"/>
</select>

<sql id="castStartTimeToDate">
	START_TIME::DATE
</sql>
<sql id="castStartTimeToDate" databaseId="mssql">
	CONVERT(VARCHAR(10), START_TIME, 120)
</sql>
<sql id="castStartTimeToDate" databaseId="mysql">
	DATE_FORMAT(START_TIME, '%Y-%m-%d')
</sql>

<select id="getWeekListByDay" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
	<include refid="outerQueryClauses_begin"/>
			SELECT CONTENT_ID, <include refid="castStartTimeToDate"/> AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				 FROM MI_STATISTICS_CONTENT_DAY
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}				
		</where>
		GROUP BY CONTENT_ID, START_TIME
	<include refid="outerQueryClauses_end"/>
</select>

<select id="getWeekListByWeek" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
	<include refid="outerQueryClauses_begin"/>
			SELECT CONTENT_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
			 FROM MI_STATISTICS_CONTENT_DAY
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}				
		</where>
		GROUP BY CONTENT_ID
	<include refid="outerQueryClauses_end"/>
</select>

<select id="getCustomList" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		<choose>
			<when test="unit  == 'DAY'">
			SELECT  CONTENT_ID, DATE_TRUNC('DAY', START_TIME)::DATE AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				FROM MI_STATISTICS_CONTENT_DAY 
			</when>
			<when test="unit  == 'HOUR'">
			SELECT CONTENT_ID, TO_CHAR(DATE_TRUNC('HOUR', START_TIME),'YYYY-MM-DD HH24:MI:SS') AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				 FROM MI_STATISTICS_CONTENT_HOUR
			</when>
			<when test="unit  == 'SECOND'">
				SELECT CONTENT_ID, TO_CHAR(START_TIME,'YYYY-MM-DD HH24:MI:SS') AS TIME_STRING, DEVICE_ID, DURATION
				 FROM MI_STATISTICS_CONTENT_SECOND
			</when>
		</choose>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}	
		</where>
		<choose>
			<when test="unit  == 'DAY'">
			GROUP BY CONTENT_ID, TIME_STRING
			</when>
			<when test="unit  == 'HOUR'">
			GROUP BY CONTENT_ID, START_TIME 
			ORDER BY TIME_STRING, CONTENT_ID
			</when>
			<when test="unit  == 'SECOND'">
			  ORDER BY CONTENT_ID, TIME_STRING, DEVICE_ID
			</when>
		</choose>
  	<include refid="outerQueryClauses_end"/>
</select>

<select id="getCustomList" databaseId="mssql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		<choose>
			<when test="unit  == 'DAY'">
		SELECT CONTENT_ID, TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION FROM (
			SELECT  CONTENT_ID, CONVERT(VARCHAR(10), DATEADD(DAY, DATEDIFF(DAY, 0, START_TIME), 0), 120) AS TIME_STRING, 
			PLAY_COUNT, DURATION FROM MI_STATISTICS_CONTENT_DAY 
			</when>
			<when test="unit  == 'HOUR'">
		SELECT START_TIME, CONTENT_ID, TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION FROM (	
			SELECT START_TIME, CONTENT_ID, CONVERT(VARCHAR(19), DATEADD(HOUR, DATEDIFF(HOUR, 0, START_TIME), 0), 120) AS TIME_STRING, 
			PLAY_COUNT, DURATION FROM MI_STATISTICS_CONTENT_HOUR
			</when>
			<when test="unit  == 'SECOND'">
		SELECT CONTENT_ID, TIME_STRING, DEVICE_ID, DURATION FROM (
				SELECT CONTENT_ID, CONVERT(VARCHAR(19), START_TIME, 120) AS TIME_STRING, DEVICE_ID, DURATION
				 FROM MI_STATISTICS_CONTENT_SECOND
			</when>
		</choose>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}	
		</where>
		) AS subquery
		<choose>
			<when test="unit  == 'DAY'">
			GROUP BY CONTENT_ID, TIME_STRING
			</when>
			<when test="unit  == 'HOUR'">
			GROUP BY CONTENT_ID, START_TIME, TIME_STRING
			</when>
		</choose>
  	<include refid="outerQueryClauses_end"/>
  	  <if test="unit  == 'HOUR'">
		ORDER BY TIME_STRING, CONTENT_ID
	</if>
	<if test="unit  == 'SECOND'">
		ORDER BY CONTENT_ID, TIME_STRING, DEVICE_ID
	</if>
</select>

<select id="getCustomList" databaseId="mysql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		<choose>
			<when test="unit  == 'DAY'">
			SELECT  CONTENT_ID, DATE_FORMAT(START_TIME, '%Y-%m-%d') AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				FROM MI_STATISTICS_CONTENT_DAY 
			</when>
			<when test="unit  == 'HOUR'">
			SELECT CONTENT_ID, DATE_FORMAT(START_TIME, '%Y-%m-%d %H:00:00') AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				 FROM MI_STATISTICS_CONTENT_HOUR
			</when>
			<when test="unit  == 'SECOND'">
				SELECT CONTENT_ID, DATE_FORMAT(START_TIME, '%Y-%m-%d %H:%i:%s') AS TIME_STRING, DEVICE_ID, DURATION
				 FROM MI_STATISTICS_CONTENT_SECOND
			</when>
		</choose>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}	
		</where>
		<choose>
			<when test="unit  == 'DAY'">
			GROUP BY CONTENT_ID, TIME_STRING
			</when>
			<when test="unit  == 'HOUR'">
			GROUP BY CONTENT_ID, START_TIME 
			ORDER BY TIME_STRING, CONTENT_ID
			</when>
			<when test="unit  == 'SECOND'">
			  ORDER BY CONTENT_ID, TIME_STRING, DEVICE_ID
			</when>
		</choose>
  	<include refid="outerQueryClauses_end"/>
</select>

<select id="getYesterdayDetailReport" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
	SELECT  B.DEVICE_NAME, A.*
	FROM(
		SELECT DEVICE_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_DAY
		<where>
		 	CONTENT_ID = #{contentId}
			<include refid="where_deviceIdList"/>
			AND START_TIME::DATE = DATE_TRUNC('DAY',CURRENT_DATE - interval '1days')::DATE
		</where>
		GROUP BY DEVICE_ID
	)A, MI_DMS_INFO_DEVICE B 
	WHERE A.DEVICE_ID = B.DEVICE_ID
</select>

<select id="getWeekListByWeekReport" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
	SELECT  B.DEVICE_NAME, A.*
	FROM(
		SELECT DEVICE_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_DAY
		<where>
		 	CONTENT_ID = #{contentId}
			<include refid="where_deviceIdList"/>
			AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}		
		</where>
		GROUP BY DEVICE_ID
	)A, MI_DMS_INFO_DEVICE B 
	WHERE A.DEVICE_ID = B.DEVICE_ID
</select>

<select id="getMonthListByMonthReport" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
	SELECT  B.DEVICE_NAME, A.*
	FROM(
		SELECT DEVICE_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
		 	CONTENT_ID = #{contentId}
			<include refid="where_deviceIdList"/>
			<include refid="conditionStartTimeEqToTruncCurrDateMonth"/>
		</where>
		GROUP BY DEVICE_ID
	)A, MI_DMS_INFO_DEVICE B 
	WHERE A.DEVICE_ID = B.DEVICE_ID
</select>

<select id="getQuarterListByQuarterReport" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
  	SELECT  B.DEVICE_NAME, A.*
	FROM(
		SELECT DEVICE_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
		 	CONTENT_ID = #{contentId}
			<include refid="where_deviceIdList"/>
			<choose>
				<when test="isThis">
					AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE) AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE)
				</when>
				<otherwise>
					 AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE - interval '3 months') AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE - interval '3 months')
				</otherwise>
			</choose>			
		</where>
		GROUP BY DEVICE_ID
	)A, MI_DMS_INFO_DEVICE B 
	WHERE A.DEVICE_ID = B.DEVICE_ID
</select>

<select id="getYearListByYearReport" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
  	SELECT  B.DEVICE_NAME, A.*
	FROM(
		SELECT DEVICE_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_YEAR
		<where>
		 	CONTENT_ID = #{contentId}
			<include refid="where_deviceIdList"/>
			<choose>
				<when test="isThis">
					 AND START_TIME = <include refid="truncYearCurrentDate"/>
				</when>
				<otherwise>
					AND START_TIME = <include refid="truncYearCurrentDateMinusOneYear"/>
				</otherwise>
			</choose>			
		</where>
		GROUP BY DEVICE_ID
	)A, MI_DMS_INFO_DEVICE B 
	WHERE A.DEVICE_ID = B.DEVICE_ID
</select>

<select id="getCustomListReport" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
	SELECT  B.DEVICE_NAME, A.*
	FROM(
		SELECT DEVICE_ID, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_DAY
		<where>
		 	CONTENT_ID = #{contentId}
			<include refid="where_deviceIdList"/>
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}				
		</where>
		GROUP BY DEVICE_ID
	)A, MI_DMS_INFO_DEVICE B 
	WHERE A.DEVICE_ID = B.DEVICE_ID
</select>

 <select id="getFirstPlayTime" resultType="java.sql.Timestamp">
    SELECT START_TIME
	FROM MI_STATISTICS_CONTENT_HOUR
	<where>
		CONTENT_ID = #{contentId}
		AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}				
	</where>
	ORDER BY START_TIME ASC
	LIMIT 1
 </select>
 
 <select id="getFirstPlayTime" resultType="java.sql.Timestamp" databaseId="mssql">
  	SELECT TOP 1 START_TIME
	FROM MI_STATISTICS_CONTENT_HOUR
	WHERE CONTENT_ID = #{contentId}
		AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}
	ORDER BY START_TIME ASC
</select>
 
  <select id="getLastPlayTime" resultType="java.sql.Timestamp">
    SELECT START_TIME
	FROM MI_STATISTICS_CONTENT_HOUR
	WHERE CONTENT_ID = #{contentId}
		AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}				
	ORDER BY START_TIME DESC
	LIMIT 1
 </select>
 
 <select id="getLastPlayTime" resultType="java.sql.Timestamp" databaseId="mssql">
  	SELECT TOP 1 START_TIME
	FROM MI_STATISTICS_CONTENT_HOUR
	WHERE CONTENT_ID = #{contentId}
	ORDER BY START_TIME DESC
</select>
 
</mapper>