package com.samsung.magicinfo.rc.common.exception;

import com.samsung.magicinfo.rc.common.exception.ErrorResponse;
import com.samsung.magicinfo.rc.common.exception.RestServiceException;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ResponseStatus;

@Order(1)
@ControllerAdvice({"com.samsung.magicinfo.rc.controller.common.CustomErrorController"})
public class GlobalControllerExceptionHandler {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.common.exception.GlobalControllerExceptionHandler.class);
  
  @ResponseStatus
  public ErrorResponse handle(HttpServletRequest req) {
    Throwable t = (Throwable)req.getAttribute("javax.servlet.error.exception");
    RestServiceException exception = (RestServiceException)t;
    return ErrorResponse.builder().code(500).errorMessage("test").build();
  }
}
