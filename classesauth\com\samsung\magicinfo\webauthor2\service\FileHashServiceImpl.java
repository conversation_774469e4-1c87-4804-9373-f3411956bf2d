package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.ContentSaveElements;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.repository.FileHashRefreshRepository;
import com.samsung.magicinfo.webauthor2.service.FileHashService;
import com.samsung.magicinfo.webauthor2.util.FileHashUtil;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FileHashServiceImpl implements FileHashService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.FileHashServiceImpl.class);
  
  private FileHashRefreshRepository fileHashRefreshRepository;
  
  private ContentSaveElements contentSaveElements;
  
  @Autowired
  public FileHashServiceImpl(FileHashRefreshRepository fileHashRefreshRepository, ContentSaveElements contentSaveElements) {
    this.fileHashRefreshRepository = fileHashRefreshRepository;
    this.contentSaveElements = contentSaveElements;
  }
  
  public String fileHashRefresh(String xml) {
    MediaSource mediaSourceXml = this.contentSaveElements.getMediaSources().get(0);
    this.contentSaveElements.setXml(xml);
    String contentId = mediaSourceXml.getContentId();
    String fileId = mediaSourceXml.getFileId();
    Path mediaSourcePath = Paths.get(mediaSourceXml.getPath(), new String[0]);
    String newFileHash = calculateNewFileHash(xml, mediaSourceXml, mediaSourcePath);
    mediaSourceXml.setFileHash(newFileHash);
    this.fileHashRefreshRepository.fileHashRefresh(contentId, fileId, newFileHash);
    return newFileHash;
  }
  
  private String calculateNewFileHash(String xml, MediaSource mediaSourceXml, Path mediaSourcePath) {
    try {
      if (Files.exists(mediaSourcePath, new java.nio.file.LinkOption[0]))
        FileUtils.deleteQuietly(mediaSourcePath.toFile()); 
      FileUtils.writeStringToFile(mediaSourcePath.toFile(), xml, "UTF-8");
      mediaSourceXml.setMediaSize(Files.size(mediaSourcePath));
      return FileHashUtil.getHash(mediaSourcePath.toFile());
    } catch (IOException e) {
      logger.debug(e.getMessage());
      return FileHashUtil.getHash(mediaSourcePath.toFile());
    } 
  }
}
