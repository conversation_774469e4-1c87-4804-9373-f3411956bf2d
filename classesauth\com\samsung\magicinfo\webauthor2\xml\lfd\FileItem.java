package com.samsung.magicinfo.webauthor2.xml.lfd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlValue;
import org.eclipse.persistence.oxm.annotations.XmlCDATA;

@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "FileItem")
public class FileItem {
  @XmlAttribute(name = "FileID")
  private String fileId;
  
  @XmlAttribute(name = "FileHashValue")
  private String fileHashValue;
  
  @XmlAttribute(name = "FileSize")
  private String fileSize;
  
  @XmlValue
  @XmlCDATA
  private String value;
  
  public String getFileId() {
    return this.fileId;
  }
  
  public void setFileId(String fileId) {
    this.fileId = fileId;
  }
  
  public String getFileHashValue() {
    return this.fileHashValue;
  }
  
  public void setFileHashValue(String fileHashValue) {
    this.fileHashValue = fileHashValue;
  }
  
  public String getFileSize() {
    return this.fileSize;
  }
  
  public void setFileSize(String fileSize) {
    this.fileSize = fileSize;
  }
  
  public String getValue() {
    return this.value;
  }
  
  public void setValue(String value) {
    this.value = value;
  }
  
  public String toString() {
    return "FileItem{fileId='" + this.fileId + '\'' + ", fileHashValue='" + this.fileHashValue + '\'' + ", fileSize='" + this.fileSize + '\'' + ", value='" + this.value + '\'' + '}';
  }
}
