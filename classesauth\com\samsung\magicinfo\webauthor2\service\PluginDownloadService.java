package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.exception.service.DownloaderException;
import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.VerificationResponse;
import java.nio.file.Path;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface PluginDownloadService {
  Path getPluginZip(String paramString) throws DownloaderException;
  
  void downloadToRepository(String paramString) throws DownloaderException;
  
  VerificationResponse isNameValid(String paramString1, String paramString2);
  
  Page<Content> getPluginResources(String paramString, Pageable paramPageable);
}
