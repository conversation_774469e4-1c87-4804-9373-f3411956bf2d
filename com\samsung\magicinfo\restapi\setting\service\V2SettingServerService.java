package com.samsung.magicinfo.restapi.setting.service;

import com.samsung.magicinfo.restapi.setting.model.V2CommonConfigResource;
import com.samsung.magicinfo.restapi.setting.model.V2PrivacyPolicyResource;
import com.samsung.magicinfo.restapi.setting.model.V2SettingCommonResource;
import com.samsung.magicinfo.restapi.setting.model.V2SettingOrganResource;
import com.samsung.magicinfo.restapi.setting.model.V2SettingServerResource;
import com.samsung.magicinfo.restapi.setting.model.config.V2LdapServerSettings;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface V2SettingServerService {
   V2CommonConfigResource getCommonConfig() throws Exception;

   V2SettingServerResource getServerSettings() throws Exception;

   V2SettingOrganResource getOrganSettings(Long var1) throws Exception;

   V2SettingServerResource updateServerSettings(V2SettingServerResource var1) throws Exception;

   V2SettingOrganResource updateOrganSettings(Long var1, V2SettingOrganResource var2) throws Exception;

   Boolean checkLdapConnection(V2LdapServerSettings var1) throws Exception;

   List getServerLogFileList(String var1) throws Exception;

   List getServerLogFileList(Integer var1, Integer var2) throws Exception;

   void downloadLogfile(String var1, String var2, HttpServletRequest var3, HttpServletResponse var4) throws Exception;

   void stopLogCollect() throws Exception;

   Map getDatabaseInformation() throws Exception;

   List getPrivacyPolicyList() throws Exception;

   V2PrivacyPolicyResource getPrivacyPolicy(String var1) throws Exception;

   V2SettingCommonResource getCommonSettings() throws Exception;

   Map getIcpInfo() throws Exception;
}
