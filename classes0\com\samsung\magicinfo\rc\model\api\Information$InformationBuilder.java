package com.samsung.magicinfo.rc.model.api;

import com.samsung.magicinfo.rc.model.api.Information;

public class InformationBuilder {
  private String token;
  
  private String accessToken;
  
  private String from;
  
  public InformationBuilder token(String token) {
    this.token = token;
    return this;
  }
  
  public InformationBuilder accessToken(String accessToken) {
    this.accessToken = accessToken;
    return this;
  }
  
  public InformationBuilder from(String from) {
    this.from = from;
    return this;
  }
  
  public Information build() {
    return new Information(this.token, this.accessToken, this.from);
  }
  
  public String toString() {
    return "Information.InformationBuilder(token=" + this.token + ", accessToken=" + this.accessToken + ", from=" + this.from + ")";
  }
}
