package com.samsung.magicinfo.restapi.playlist.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.export.PdfBuilder;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeleteContentUtils;
import com.samsung.common.utils.LinkedUtils;
import com.samsung.common.utils.PlaylistUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.common.LeftMenuGroupTreeDao;
import com.samsung.magicinfo.framework.content.constants.ContentConstants;
import com.samsung.magicinfo.framework.content.dao.PlaylistDao;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.entity.NotificationData;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.entity.PlaylistLog;
import com.samsung.magicinfo.framework.content.entity.PlaylistTag;
import com.samsung.magicinfo.framework.content.entity.PlaylistTagCondition;
import com.samsung.magicinfo.framework.content.entity.SyncPlaylist;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistInterface;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistLogInterface;
import com.samsung.magicinfo.framework.role.manager.AbilityInfo;
import com.samsung.magicinfo.framework.role.manager.AbilityInfoImpl;
import com.samsung.magicinfo.framework.ruleset.entity.RuleSet;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfo;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.common.ScheduleInterface;
import com.samsung.magicinfo.framework.setup.entity.ContentTagEntity;
import com.samsung.magicinfo.framework.setup.entity.TagEntity;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfo;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.TagInfo;
import com.samsung.magicinfo.framework.setup.manager.TagInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.protocol.util.MailUtil;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonResultResource;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.playlist.model.V2AmsInformationResource;
import com.samsung.magicinfo.restapi.playlist.model.V2ContentTagLogResource;
import com.samsung.magicinfo.restapi.playlist.model.V2DeletePlaylistRequestWrapper;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistAddContentsResponse;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistCategoryResourceRequestWrapper;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistCommonResource;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistContentEffectResource;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistContentResourceIdWrapper;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistContentResourceResponse;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistContentTagResource;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistCopyResource;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistDeleteAllRecycleResource;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistDeleteCheckResource;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistDeleteFail;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistFilter;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistItemResource;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistItemResourceResponse;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistListResourceResponse;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistResource;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistResourceResponse;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistTagsResource;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistTagsResourceResponse;
import com.samsung.magicinfo.restapi.playlist.model.V2TagContentResource;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.service.statistics.DeviceStatisticsDownloadService;
import java.io.Serializable;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.servlet.ModelAndView;

@Service("v2PlaylistService")
@Transactional
public class V2PlaylistServiceImpl implements V2PlaylistService {
   protected Logger logger = LoggingManagerV2.getLogger(V2PlaylistServiceImpl.class);
   PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
   PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
   ContentInfo cInfo = ContentInfoImpl.getInstance();
   PlaylistLog pLog = new PlaylistLog();
   PlaylistLogInterface logInfo = DAOFactory.getPlaylistLogInfoImpl("PREMIUM");
   LeftMenuGroupTreeDao treeDao = new LeftMenuGroupTreeDao();
   ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
   private DeviceStatisticsDownloadService downloadService = null;
   final int CONTENT_ID_INDEX = 0;
   final int CONTENT_ORDER_INDEX = 1;
   final int MATCH_TYPE_INDEX = 2;

   public V2PlaylistServiceImpl() {
      super();
   }

   public void setDownloadService(DeviceStatisticsDownloadService downloadService) {
      this.downloadService = downloadService;
   }

   private String getContentTagVal(String contentId) {
      TagInfo tagInfo = TagInfoImpl.getInstance();
      List tagEntities = tagInfo.getContentTagList(contentId);
      String contentTagVal = "";
      if (tagEntities != null && tagEntities.size() > 0) {
         int k = 0;

         for(Iterator var6 = tagEntities.iterator(); var6.hasNext(); ++k) {
            TagEntity tagEntity = (TagEntity)var6.next();
            if (k > 0) {
               contentTagVal = contentTagVal + ",";
            }

            contentTagVal = contentTagVal + tagEntity.getTag_value();
         }
      }

      return contentTagVal;
   }

   @PreAuthorize("hasAnyAuthority('Playlist Manage Authority')")
   public boolean canReadUnshared() {
      return true;
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority', 'Playlist Write Authority', 'Playlist Manage Authority')")
   public V2PlaylistResourceResponse createPlaylist(V2PlaylistResource resource) throws Exception {
      if (resource.getGroupId() != null) {
         RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST, resource.getGroupId());
      }

      String productType = resource.getProductType();
      String playlistId = UUID.randomUUID().toString();
      new ArrayList();
      new ArrayList();
      boolean addSubPlaylist = false;
      if (!"1".equals(resource.getPlaylistType())) {
         resource.setAmsMode("");
      }

      Long totalPlayTime = 0L;
      new HashMap();
      new HashMap();
      List contents;
      if (!ObjectUtils.isEmpty(resource.getContents())) {
         contents = this.checkContentExpireDate(resource);
         if (contents != null && !contents.isEmpty()) {
            V2PlaylistResourceResponse res = new V2PlaylistResourceResponse();
            res.setIncludeExpireContent(true);
            res.setContents(contents);
            return res;
         }
      }

      contents = resource.getContents();
      List tags = resource.getTags();
      if ("3".equals(resource.getPlaylistType())) {
         this.makeSyncPlaylistDurationMap(contents);
      }

      new V2PlaylistServiceImpl.PlaylistContentInformation();
      int inx = false;
      V2PlaylistServiceImpl.PlaylistContentInformation plci;
      if ("5".equals(resource.getPlaylistType())) {
         plci = this.tagPlaylistContentListBuilder(playlistId, 0L, tags, productType);
      } else {
         plci = this.playlistContentListBuilder(resource, playlistId, 0L, contents, productType, V2PlaylistServiceImpl.contentBuilderType.CREATE);
      }

      List contentList = plci.getContents();
      Map totalSizeMapByContentId = plci.getTotalSizeMapByContentId();
      ArrayList tagArrayList = plci.getTagArrayList();
      totalPlayTime = plci.getTotalPlayTime();
      addSubPlaylist = plci.getAddSubPlaylist();
      Playlist pl = this.playlistBuilder(resource, playlistId, contentList, tagArrayList, totalSizeMapByContentId, totalPlayTime, addSubPlaylist);
      if (this.pInfo.addPlaylist(pl) > 0) {
         String playlistType = resource.getPlaylistType();
         String eventType = "";
         if ("5".equals(playlistType)) {
            eventType = "Add tag playlist";
         } else if ("3".equals(playlistType)) {
            eventType = "Add sync playlist";
         } else {
            eventType = "Add playlist";
         }

         this.sendNoticeMail(pl.getPlaylist_id(), eventType);
      }

      this.setAmsLastMemory(resource.getLastMemories());
      return this.getActivePlaylistInfo(pl.getPlaylist_id());
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority', 'Playlist Write Authority', 'Playlist Manage Authority')")
   public V2PlaylistResourceResponse editPlaylist(String playlistId, V2PlaylistResource resource, HttpServletRequest request) throws Exception {
      String productType = resource.getProductType();
      String playlistType = resource.getPlaylistType();
      PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl(productType);
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      PlaylistLog pLog = new PlaylistLog();
      pLog.setUser_id(this.getLoginUserId());
      pLog.setIp_address(request.getRemoteAddr());
      Long oldTotalPlayTime = 0L;
      Long oldTotalSize = 0L;
      Long changeInTotalPlayTime = 0L;
      Long changeInTotalSize = 0L;
      boolean isNestedPlaylist = false;
      String sessionId = UUID.randomUUID().toString().toUpperCase();
      Long curActiveVersion = pInfo.getPlaylistActiveVersionId(playlistId);
      new ArrayList();
      new ArrayList();
      boolean addSubPlaylist = false;
      boolean isChanged = false;
      if (!"1".equals(resource.getPlaylistType())) {
         resource.setAmsMode("");
      }

      Playlist playlist = pInfo.getPlaylistVerInfo(playlistId, curActiveVersion);
      if (playlist == null) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"playlist information"});
      } else {
         if ("6".equals(resource.getPlaylistType())) {
            oldTotalSize = playlist.getTotal_size();
            oldTotalPlayTime = ContentUtils.getPlayTimeStr(playlist.getPlay_time());
            isNestedPlaylist = true;
         }

         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST, playlistId);
         if (!ObjectUtils.isEmpty(resource.getContents())) {
            List expiredContents = this.checkContentExpireDate(resource);
            if (expiredContents != null && !expiredContents.isEmpty()) {
               V2PlaylistResourceResponse res = new V2PlaylistResourceResponse();
               res.setIncludeExpireContent(true);
               res.setContents(expiredContents);
               return res;
            }
         }

         Long totalPlayTime = 0L;
         Long totalSize = 0L;
         new HashMap();
         List contents = resource.getContents();
         List tags = resource.getTags();
         String isShuffleFlagString = resource.getShuffleFlag() ? "Y" : "N";
         int inx = false;
         V2PlaylistServiceImpl.PlaylistContentInformation plci = new V2PlaylistServiceImpl.PlaylistContentInformation();
         if ("5".equals(playlistType)) {
            plci = this.tagPlaylistContentListBuilder(playlistId, curActiveVersion, tags, productType);
            isChanged = true;
         } else if (playlistId != null && resource.getContents() != null && !resource.getContents().isEmpty()) {
            plci = this.playlistContentListBuilder(resource, playlistId, curActiveVersion, contents, productType, V2PlaylistServiceImpl.contentBuilderType.UPDATE);
            isChanged = true;
         }

         List contentList = plci.getContents();
         Map totalSizeMapByContentId = plci.getTotalSizeMapByContentId();
         ArrayList tagArrayList = plci.getTagArrayList();
         totalPlayTime = plci.getTotalPlayTime();
         addSubPlaylist = plci.getAddSubPlaylist();
         if (isChanged) {
            Playlist pl = this.playlistBuilder(resource, playlistId, contentList, tagArrayList, totalSizeMapByContentId, totalPlayTime, addSubPlaylist);
            if (pInfo.addPlaylist(pl) > 0) {
               String eventType = "";
               if ("5".equals(playlistType)) {
                  eventType = "Edit tag playlist";
               } else if ("3".equals(playlistType)) {
                  eventType = "Edit Sync playlist";
               } else {
                  eventType = "Edit playlist";
               }

               changeInTotalPlayTime = ContentUtils.getPlayTimeStr(pl.getPlay_time()) - oldTotalPlayTime;
               changeInTotalSize = pl.getTotal_size() - oldTotalSize;
               this.sendNoticeMail(pl.getPlaylist_id(), eventType);
            }
         }

         boolean ret = false;
         if (playlistId != null) {
            if ((resource.getPlaylistName().length() > 0 || resource.getMetaData().length() > 0) && pInfo.setPlaylistInfo(playlistId, resource.getPlaylistName(), resource.getMetaData(), resource.getShareFlag(), resource.getIgnoreTag(), resource.getEvennessPlayback()) > 0) {
               ret = true;
            }

            if (pInfo.setPlaylistGroup(playlistId, resource.getGroupId()) > 0) {
               ret = true;
            }

            if (!isChanged) {
               boolean bVersionChanged = false;
               if (curActiveVersion != resource.getVersionId()) {
                  bVersionChanged = true;
               }

               if (resource.getVersionId() != null && bVersionChanged && playlistId != null && pInfo.setActiveVersion(playlistId, resource.getVersionId()) > 0) {
                  ret = true;
               }
            }
         }

         if (ret) {
            if (isNestedPlaylist) {
               PlaylistDao playlistDao = new PlaylistDao();
               playlistDao.updateParentPlaylists(playlistId, changeInTotalPlayTime, changeInTotalSize);
            }

            this.sendNoticeMail(playlistId, "Edit playlist");
         }

         ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
         scheduleInfo.setPlaylistTrigger(playlist.getPlaylist_id());
         EventInfo eInfo = EventInfoImpl.getInstance();
         eInfo.setPlaylistTrigger(playlist.getPlaylist_id());
         this.setAmsLastMemory(resource.getLastMemories());
         cmsDao.setContentUnlockBySessionID(sessionId);
         return this.getActivePlaylistInfo(playlistId);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority', 'Playlist Write Authority', 'Playlist Manage Authority')")
   public V2PlaylistResourceResponse playlistSaveAs(String playlistId, V2PlaylistResource resource) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST, playlistId);
      String productType = resource.getProductType();
      String deviceType = resource.getDeviceType();
      String playlistType = resource.getPlaylistType();
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      String sessionId = UUID.randomUUID().toString().toUpperCase();
      Long curActiveVersion = this.pInfo.getPlaylistActiveVersionId(playlistId);
      Playlist playlist = this.pInfo.getPlaylistVerInfo(playlistId, curActiveVersion);
      if (playlist == null) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"playlist information"});
      } else {
         new ArrayList();
         boolean addSubPlaylist = false;
         if (!"1".equals(resource.getPlaylistType())) {
            resource.setAmsMode("");
         }

         Long totalPlayTime = 0L;
         new HashMap();
         new HashMap();
         String isShuffleFlagString = resource.getShuffleFlag() ? "Y" : "N";
         List contentList;
         if (!"5".equals(playlistType) && !resource.getContents().isEmpty()) {
            contentList = this.checkContentExpireDate(resource);
            if (contentList != null && !contentList.isEmpty()) {
               V2PlaylistResourceResponse res = new V2PlaylistResourceResponse();
               res.setIncludeExpireContent(true);
               res.setContents(contentList);
               return res;
            }
         }

         new ArrayList();
         playlistId = UUID.randomUUID().toString();
         int inx = false;
         boolean isChanged = false;
         List contents = resource.getContents();
         List tags = resource.getTags();
         V2PlaylistServiceImpl.PlaylistContentInformation plci = new V2PlaylistServiceImpl.PlaylistContentInformation();
         if ("5".equals(playlistType)) {
            plci = this.tagPlaylistContentListBuilder(playlistId, curActiveVersion, tags, productType);
            isChanged = true;
         } else if (playlistId != null && !resource.getContents().isEmpty() || !playlist.getIs_shuffle().equalsIgnoreCase(isShuffleFlagString)) {
            plci = this.playlistContentListBuilder(resource, playlistId, curActiveVersion, contents, productType, V2PlaylistServiceImpl.contentBuilderType.UPDATE);
            isChanged = true;
         }

         contentList = plci.getContents();
         Map totalSizeMapByContentId = plci.getTotalSizeMapByContentId();
         ArrayList tagArrayList = plci.getTagArrayList();
         totalPlayTime = plci.getTotalPlayTime();
         addSubPlaylist = plci.getAddSubPlaylist();
         if (isChanged) {
            Playlist pl = this.playlistBuilder(resource, playlistId, contentList, tagArrayList, totalSizeMapByContentId, totalPlayTime, addSubPlaylist);
            if (this.pInfo.addPlaylist(pl) > 0) {
               String eventType = "";
               if ("5".equals(playlistType)) {
                  eventType = "Edit tag playlist";
               } else if ("3".equals(playlistType)) {
                  eventType = "Edit Sync playlist";
               } else {
                  eventType = "Edit playlist";
               }

               this.sendNoticeMail(pl.getPlaylist_id(), eventType);
            }
         }

         this.setAmsLastMemory(resource.getLastMemories());
         cmsDao.setContentUnlockBySessionID(sessionId);
         return this.getActivePlaylistInfo(playlistId);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority', 'Playlist Write Authority', 'Playlist Manage Authority')")
   public V2CommonResultResource recoveryRecycle(V2CommonIds resource) throws Exception {
      boolean flag = false;
      List playlistIds = resource.getIds();

      for(int i = 0; i < playlistIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST, (String)playlistIds.get(i));
         } catch (Exception var14) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST);
      }

      V2CommonResultResource resourceList = new V2CommonResultResource();
      List successResourceList = new ArrayList();
      List failResourceList = new ArrayList();
      List notiDataList = new ArrayList();

      for(int i = 0; i < playlistIds.size(); ++i) {
         try {
            if (!SecurityUtils.checkWritePermissionWithOrgAndId("Playlist", (String)playlistIds.get(i))) {
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
            }

            if (this.pInfo.restorePlaylist((String)playlistIds.get(i)) <= 0) {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
            }

            V2PlaylistResourceResponse res = this.getActivePlaylistInfo((String)playlistIds.get(i));
            successResourceList.add(res);
         } catch (RestServiceException var12) {
            this.logger.error(var12);
            V2PlaylistCommonResource obj = new V2PlaylistCommonResource();
            obj.setPlaylistId((String)playlistIds.get(i));
            obj.setReason(var12.getErrorMessage());
            obj.setReasonCode(var12.getErrorCode());
            failResourceList.add(obj);
         } catch (Exception var13) {
            this.logger.error(var13);
            RestExceptionCode error = RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN;
            V2PlaylistCommonResource obj = new V2PlaylistCommonResource();
            obj.setPlaylistId((String)playlistIds.get(i));
            obj.setReason(error.getMessage());
            obj.setReasonCode(error.getCode());
            failResourceList.add(obj);
         }
      }

      if (notiDataList != null && notiDataList.size() > 0) {
         MailUtil.sendPlaylistEventMail(notiDataList, "Restore playlist");
      }

      resourceList.setSuccessList(successResourceList);
      resourceList.setFailList(failResourceList);
      return resourceList;
   }

   @PreAuthorize("hasAnyAuthority('Content Manage Authority','Playlist Manage Authority')")
   public V2PlaylistDeleteAllRecycleResource deleteAllRecycle(HttpServletRequest request) throws Exception {
      String productType = "PREMIUM";
      ScheduleInterface sInfo = DAOFactory.getScheduleInfoImpl(productType);
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      String sessionId = UUID.randomUUID().toString().toUpperCase();
      List notiDataList = new ArrayList();
      V2PlaylistDeleteAllRecycleResource resource = new V2PlaylistDeleteAllRecycleResource();
      String playlist_list = "";
      String[] aval = null;
      int pageSize = 10000;
      V2PlaylistFilter filter = new V2PlaylistFilter();
      filter.setGroupType("DELETED");
      filter.setProductType("PREMIUM");
      filter.setPageSize(pageSize);
      filter.setStartIndex(1);
      V2PageResource tmpPlaylistSource = this.getPlaylistByFilter(filter);
      List pplist = new ArrayList();
      if (tmpPlaylistSource != null && tmpPlaylistSource.getList() != null) {
         pplist = tmpPlaylistSource.getList();
         resource.setPlaylistList((List)((List)pplist).stream().map((p) -> {
            Map map = new HashMap();
            map.put("playlistId", p.getPlaylistId());
            return map;
         }).collect(Collectors.toList()));
      }

      playlist_list = (String)((List)pplist).stream().map(V2PlaylistListResourceResponse::getPlaylistId).collect(Collectors.joining(","));
      if (playlist_list.length() > 0) {
         aval = playlist_list.split(",");
      }

      int nCannotDelPlaylist = 0;
      ArrayList cannotDelPlaylistList = new ArrayList();
      ArrayList refScheduleList = new ArrayList();
      if (aval != null && aval.length > 0) {
         for(int i = 0; aval != null && i < aval.length; ++i) {
            if (!this.pInfo.isDeletablePlaylist(aval[i], this.getLoginUserId(), sessionId)) {
               List list1 = sInfo.getProgramByPlaylistId(aval[i]);
               if (list1 != null && list1.size() > 0) {
                  cannotDelPlaylistList.add(nCannotDelPlaylist, StrUtils.cutCharLen(this.pInfo.getPlaylistName(aval[i]), 25));
                  String programName = "";
                  Map programMap = null;

                  for(int j = 0; j < list1.size(); ++j) {
                     programMap = (Map)list1.get(j);
                     programName = programName + this.getScheduleLink(request, programMap, productType);
                  }

                  refScheduleList.add(nCannotDelPlaylist, programName);
               } else {
                  refScheduleList.add(nCannotDelPlaylist, "");
               }

               ++nCannotDelPlaylist;
            } else {
               try {
                  NotificationData notiData = new NotificationData();
                  Playlist playlist = this.pInfo.getPlaylistActiveVerInfo(aval[i]);
                  notiData.setName(playlist.getPlaylist_name());
                  notiData.setOrgId(playlist.getOrganization_id());
                  notiData.setOrgName(userGroupInfo.getGroupNameByGroupId(playlist.getOrganization_id()));
                  notiData.setUserName(this.getLoginUserId());
                  notiDataList.add(notiData);
               } catch (Exception var22) {
                  this.logger.error(var22);
               }

               this.pInfo.deletePlaylist(aval[i], this.getLoginUserId(), sessionId);
               if (this.pInfo.deletePlaylistCompletely(aval[i]) <= 0) {
               }
            }
         }

         if (nCannotDelPlaylist > 0) {
            resource.setCannotDelPlaylistList(cannotDelPlaylistList);
            resource.setRefScheduleList(refScheduleList);
         }

         if (notiDataList != null && notiDataList.size() > 0) {
            MailUtil.sendPlaylistEventMail(notiDataList, "Delete playlist permanently");
         }

         return resource;
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_RECYCLE_BIN_EMPTY);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority','Playlist Read Authority', 'Playlist Write Authority', 'Playlist Manage Authority','Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Add Authority', 'Content Schedule Manage Authority')")
   public V2PlaylistResourceResponse getActivePlaylistInfo(String playlistId) throws Exception {
      return this.getActivePlaylistInfo(playlistId, "PREMIUM", 0L);
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority','Playlist Read Authority', 'Playlist Write Authority', 'Playlist Manage Authority','Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Add Authority', 'Content Schedule Manage Authority')")
   public V2PlaylistResourceResponse getActivePlaylistInfo(String playlistId, Long versionId) throws Exception {
      return this.getActivePlaylistInfo(playlistId, "PREMIUM", versionId);
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority', 'Playlist Write Authority', 'Playlist Manage Authority','Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Add Authority', 'Content Schedule Manage Authority')")
   public V2PlaylistResourceResponse getActivePlaylistInfo(String playlistId, String productType, Long versionId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST, playlistId);
      List contentResourceList = new ArrayList();
      V2PlaylistResourceResponse resource = new V2PlaylistResourceResponse();
      Playlist playlist = this.pInfo.getPlaylistActiveVerInfo(playlistId);
      if (playlist == null) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"playlist information"});
      } else {
         PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
         TagInfo tagInfo = TagInfoImpl.getInstance();
         Long searchVersionId = 0L;
         if (versionId != null && versionId > 0L) {
            searchVersionId = versionId;
         } else {
            searchVersionId = playlist.getVersion_id();
         }

         String plGroupName = this.pInfo.getGroupName(playlist.getGroup_id());
         resource.setPlaylistId(playlist.getPlaylist_id());
         resource.setPlaylistName(playlist.getPlaylist_name());
         resource.setPlaylistType(playlist.getPlaylist_type());
         resource.setDeviceType(playlist.getDevice_type());
         resource.setDeviceTypeVersion(playlist.getDevice_type_version());
         String[] convertToSeconds = playlist.getPlay_time().split(":");
         if (convertToSeconds.length == 1) {
            resource.setPlayTime(0L);
         } else {
            resource.setPlayTime(Long.valueOf(convertToSeconds[0]) * 3600L + Long.valueOf(convertToSeconds[1]) * 60L + Long.valueOf(convertToSeconds[2]));
         }

         resource.setLastModifiedDate(playlist.getLast_modified_date());
         resource.setVersionId(searchVersionId);
         resource.setShuffleFlag(playlist.getIs_shuffle());
         resource.setShareFlag(playlist.getShare_flag());
         resource.setGroupId(playlist.getGroup_id());
         resource.setOrganizationName(playlist.getOrganization_name());
         resource.setGroupName(plGroupName);
         resource.setMetaData(playlist.getPlaylist_meta_data());
         resource.setCreatorId(playlist.getCreator_id());
         resource.setTotalSize(playlist.getTotal_size());
         resource.setIgnoreTag(playlist.getIgnore_tag());
         resource.setEvennessPlayback(playlist.getEvenness_playback());
         if (versionId == null || versionId == 0L) {
            CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
            resource.setCategoryList(categoryInfo.getCategoryWithPlaylistId(playlistId));
         }

         List tagPlaylistContentList;
         List tagMap;
         List contentList;
         String contentTagValue;
         String tagList;
         if (playlist.getPlaylist_type().equals("5")) {
            ContentInfo contentDao = ContentInfoImpl.getInstance();
            contentList = this.pInfo.getTagContentListOfPlaylist(playlist.getPlaylist_id(), searchVersionId);
            List tagList = this.pInfo.getTagList(playlist.getPlaylist_id(), searchVersionId);
            if (tagList != null && !tagList.isEmpty()) {
               List trs = new ArrayList();

               V2PlaylistTagsResourceResponse vr;
               for(Iterator var16 = tagList.iterator(); var16.hasNext(); trs.add(vr)) {
                  PlaylistContent tag = (PlaylistContent)var16.next();
                  vr = new V2PlaylistTagsResourceResponse();
                  vr.setTagId(tag.getTag_id());
                  vr.setOrder(tag.getTag_order());
                  vr.setName(tag.getTag_name());
                  vr.setDuration(tag.getTag_duration());
                  vr.setStartDate(StrUtils.nvl(DateUtils.timestamp2StringDate(tag.getStart_date())));
                  vr.setExpiredDate(StrUtils.nvl(DateUtils.timestamp2StringDate(tag.getExpired_date())));
                  vr.setRepeatType(tag.getRepeat_type().replace(",", ";"));
                  List thumb;
                  List thumb;
                  if (tag.getTag_type() == 1L) {
                     ArrayList tagConditioniList = new ArrayList();
                     Map condition = new HashMap();
                     if (tag.getNumber_str() != null && !tag.getNumber_str().equals("") && !tag.getNumber_str().equals("null")) {
                        condition.put("tag_condition", tag.getNumber_str());
                     }

                     tagConditioniList.add(condition);
                     vr.setTagConditions(tagConditioniList);
                     contentTagValue = playlistInfo.getTagConditionIdWithTagNumber(playlistId, searchVersionId, tag.getTag_id(), tag.getNumber_str());
                     if (contentTagValue != null && !contentTagValue.equals("")) {
                        List tagCount = playlistInfo.getCntContentAtTagPlaylist(tag.getTag_id(), contentTagValue);
                        if (tagCount != null) {
                           vr.setTagCount(tagCount);
                        }

                        thumb = playlistInfo.getAllThumbContentAtTagPlaylist(tag.getTag_id(), contentTagValue);
                        if (thumb != null) {
                           thumb = this.convertMapToTagResources(thumb);
                           vr.setContents(thumb);
                        }
                     } else {
                        List tagCount = new ArrayList();
                        Map tempMap = new HashMap();
                        tempMap.put("total_size", 0);
                        tempMap.put("count", 0);
                        tagCount.add(tempMap);
                        vr.setTagCount(tagCount);
                     }
                  } else {
                     tagPlaylistContentList = playlistInfo.getPlaylistTagConditionList(playlistId, searchVersionId, tag.getTag_id());
                     if (tagPlaylistContentList != null && tagPlaylistContentList.size() > 0) {
                        List conditionIds = new ArrayList();
                        ArrayList tagConditioniList = new ArrayList();
                        Iterator var22 = tagPlaylistContentList.iterator();

                        while(var22.hasNext()) {
                           PlaylistTag tagCondition = (PlaylistTag)var22.next();
                           Map condition = new HashMap();
                           condition.put("tag_condition_id", tagCondition.getTag_condition_id());
                           condition.put("tag_condition", tagCondition.getTag_condition());
                           conditionIds.add(String.valueOf(tagCondition.getTag_condition_id()));
                           tagConditioniList.add(condition);
                        }

                        vr.setTagConditions(tagConditioniList);
                        tagList = conditionIds.toString().replace("[", "").replace("]", "");
                        thumb = playlistInfo.getCntContentAtTagPlaylist(tag.getTag_id(), tagList);
                        if (thumb != null) {
                           vr.setTagCount(thumb);
                        }

                        thumb = playlistInfo.getAllThumbContentAtTagPlaylist(tag.getTag_id(), tagList);
                        if (thumb != null) {
                           List contents = this.convertMapToTagResources(thumb);
                           vr.setContents(contents);
                        }
                     } else {
                        tagMap = contentDao.getContentListWithThumbnailFromTagId(tag.getTag_id());
                        if (tagMap != null && tagMap.size() > 0) {
                           List contents = this.convertMapToContentResources(tagMap);
                           vr.setContents(contents);
                        }
                     }
                  }
               }

               resource.setTags(trs);
            }
         } else {
            contentList = this.pInfo.getContentListOfPlaylist(playlist.getPlaylist_id(), searchVersionId);
         }

         if (contentList != null) {
            resource.setContentCount(contentList.size());
         } else {
            resource.setContentCount(0);
         }

         List playlistContentList = this.pInfo.getContentList(playlistId, searchVersionId);
         if (playlistContentList.size() != 0) {
            for(int i = 0; i < playlistContentList.size(); ++i) {
               PlaylistContent tmpPlaylistContent = (PlaylistContent)playlistContentList.get(i);
               Content content = this.cInfo.getContentAndFileActiveVerInfo(tmpPlaylistContent.getContent_id());
               Content tmpContent = (Content)contentList.get(i);
               V2PlaylistItemResourceResponse contentResource = new V2PlaylistItemResourceResponse();
               contentResource.setPlaylistId(tmpPlaylistContent.getPlaylist_id());
               if (content != null) {
                  if (content.getContent_id() != null) {
                     contentResource.setContentId(content.getContent_id());
                  }

                  if (tmpContent.getContent_duration() != null) {
                     contentResource.setContentDuration(tmpContent.getContent_duration());
                  }

                  if (content.getContent_name() != null) {
                     contentResource.setContentName(content.getContent_name());
                  }

                  if (content.getThumb_file_id() != null) {
                     contentResource.setThumbFileId(content.getThumb_file_id());
                  }

                  if (content.getThumb_file_name() != null) {
                     contentResource.setThumbFileName(content.getThumb_file_name());
                  }

                  contentResource.setThumbFilePath("/servlet/ContentThumbnail?thumb_id=" + content.getThumb_file_id() + "&thumb_filename=" + URLEncoder.encode(content.getThumb_file_name(), "UTF-8") + "_MEDIUM.PNG");
                  if (content.getMedia_type() != null) {
                     contentResource.setMediaType(content.getMedia_type());
                  }

                  if (!content.getMedia_type().equalsIgnoreCase("DLK")) {
                     contentResource.setPlayTime(String.valueOf(tmpContent.getContent_duration()));
                  }

                  contentResource.setContentOrder((long)tmpContent.getContent_order());
                  if (tmpContent.getPlay_time() != null && !tmpContent.getPlay_time().equalsIgnoreCase("")) {
                     contentResource.setHasDefaultPlayTime(true);
                  } else {
                     contentResource.setHasDefaultPlayTime(false);
                  }

                  contentResource.setStartDate(DateUtils.timestamp2StringDate(tmpPlaylistContent.getStart_date()));
                  contentResource.setExpiredDate(content.getExpiration_date());
                  contentResource.setCreator_id(content.getCreator_id());
                  contentResource.setPlayTime(tmpPlaylistContent.getPlay_time());
                  contentResource.setLastModifiedDate(content.getLast_modified_date());
               } else {
                  Playlist playlistContent = this.pInfo.getPlaylistActiveVerInfo(tmpPlaylistContent.getContent_id());
                  if (playlistContent != null) {
                     if (playlistContent.getPlaylist_id() != null) {
                        contentResource.setContentId(playlistContent.getPlaylist_id());
                     }

                     if (playlistContent.getPlaylist_name() != null) {
                        contentResource.setContentName(playlistContent.getPlaylist_name());
                     }

                     ContentFile thumbFile = playlistInfo.getThumbFileInfo(playlistContent.getPlaylist_id());
                     if (thumbFile != null) {
                        if (thumbFile.getFile_id() != null) {
                           contentResource.setThumbFileId(thumbFile.getFile_id());
                        }

                        if (thumbFile.getFile_name() != null) {
                           contentResource.setThumbFileName(thumbFile.getFile_name());
                        }

                        contentResource.setThumbFilePath("/servlet/ContentThumbnail?thumb_id=" + thumbFile.getFile_id() + "&thumb_filename=" + URLEncoder.encode(thumbFile.getFile_name(), "UTF-8") + "_MEDIUM.PNG");
                     }

                     if (playlistContent.getPlay_time() != null) {
                        contentResource.setPlayTime(playlistContent.getPlay_time());
                     }

                     contentResource.setIsSubPlaylist(true);
                     contentResource.setCreator_id(playlistContent.getCreator_id());
                     contentResource.setLastModifiedDate(playlistContent.getLast_modified_date());
                  }
               }

               tagPlaylistContentList = this.pInfo.getTagContentListOfPlaylist(playlist.getPlaylist_id(), searchVersionId);
               tagMap = this.pInfo.getContentTag(playlistId, searchVersionId, tmpContent.getContent_id(), Integer.parseInt(Long.toString((long)tmpContent.getContent_order())));
               if (tagMap != null && tagMap.size() > 0) {
                  contentTagValue = (String)((Map)tagMap.get(0)).get("match_type");
                  contentResource.setTagMatchType(contentTagValue);
                  tagList = "";
                  String tagVal = "";

                  for(int k = 0; k < tagMap.size(); ++k) {
                     if (k > 0) {
                        tagVal = tagVal + ",";
                        tagList = tagList + ",";
                     }

                     tagList = tagList + ((Map)tagMap.get(k)).get("tag_id");
                     tagVal = tagVal + ((Map)tagMap.get(k)).get("tag_value");
                  }

                  contentResource.setTagList(tagList);
                  contentResource.setTagValue(tagVal);
               }

               contentTagValue = this.getContentTagVal(tmpContent.getContent_id());
               contentResource.setContentTagValue(contentTagValue);
               contentResourceList.add(contentResource);
            }
         } else {
            V2PlaylistItemResourceResponse contentResource = new V2PlaylistItemResourceResponse();
            contentResource.setThumbFilePath("/servlet/ContentThumbnail?thumb_id=NOIMAGE_THUMBNAIL&thumb_filename=NOIMAGE_THUMBNAIL.PNG_MEDIUM.PNG");
            contentResourceList.add(contentResource);
         }

         if (versionId == null || versionId == 0L) {
            resource.setPlaylistVersions(V2PlaylistFactory.getPlaylistVersionsResponse(playlistId, productType));
         }

         resource.setContents(contentResourceList);
         return resource;
      }
   }

   private List convertMapToContentResources(List thumb) {
      List contents = new ArrayList();
      Iterator var3 = thumb.iterator();

      while(var3.hasNext()) {
         Map map = (Map)var3.next();
         V2TagContentResource content = new V2TagContentResource();
         content.setContentName((String)map.get("content_name"));
         content.setContentId((String)map.get("content_id"));
         content.setMediaType((String)map.get("media_type"));
         if (Optional.ofNullable(map.get("version_id")).isPresent()) {
            content.setVersionId((Long)map.get("version_id"));
         }

         content.setCreatorId((String)map.get("creator_id"));
         content.setResolution((String)map.get("resolution"));
         content.setCreateDate((Timestamp)map.get("create_date"));
         content.setThumbFileName((String)map.get("thumb_file_name"));
         content.setThumbFileId((String)map.get("thumb_file_id"));
         content.setPlayTime((String)map.get("play_time"));
         content.setExpiredDate((String)map.get("expiration_date"));
         content.setLastModifiedDate((Timestamp)map.get("last_modified_date"));
         String contentTagValue = this.getContentTagVal((String)map.get("content_id"));
         content.setContentTagValue(contentTagValue);
         contents.add(content);
      }

      return contents;
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority', 'Playlist Write Authority', 'Playlist Manage Authority')")
   public V2PlaylistResourceResponse copyPlaylist(HttpServletRequest request, String playlistId, V2PlaylistCopyResource resource) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST, playlistId);
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      new LinkedHashMap();
      String newPlaylistId = UUID.randomUUID().toString();

      try {
         Long curActiveVersion = this.pInfo.getPlaylistActiveVersionId(playlistId);
         List tagArrayList = this.pInfo.getPlaylistContentTagEntityList(playlistId, curActiveVersion);
         Playlist playlist = this.pInfo.getPlaylistVerInfo(playlistId, curActiveVersion);
         List pList = this.pInfo.getContentList(playlistId, curActiveVersion);
         boolean ret = false;
         User user = SecurityUtils.getLoginUser();
         playlist.setPlaylist_id(newPlaylistId);
         playlist.setPlaylist_name(resource.getPlaylistName());
         playlist.setGroup_id(resource.getGroupId());
         playlist.setTagList(tagArrayList);
         playlist.setPlaylist_meta_data(resource.getMetaData());
         playlist.setShare_flag(resource.getShareFlag());
         playlist.setVersion_id(1L);
         Long groupId = resource.getGroupId();
         if (groupId != null && groupId != 0L) {
            playlist.setGroup_id(new Long(groupId));
         } else if (user.isMu()) {
            playlist.setGroup_id(this.playlistInfo.getRootId(this.getLoginUserId(), user.getRoot_group_id()));
         } else {
            playlist.setGroup_id(this.pInfo.getRootId(this.getLoginUserId()));
         }

         playlist.setCreator_id(this.getLoginUserId());
         playlist.setArr_content_list(pList);
         playlist.setOrganization_id(user.getRoot_group_id());
         List content_list;
         if (playlist.getPlaylist_type().equals("3")) {
            content_list = this.pInfo.getSyncGroupInfo(playlistId, curActiveVersion);

            for(int i = 0; i < content_list.size(); ++i) {
               ((SyncPlaylist)content_list.get(i)).setPlaylist_id(newPlaylistId);
               ((SyncPlaylist)content_list.get(i)).setVersion_id(1L);
            }

            playlist.setSync_status_list(content_list);
         } else if (playlist.getPlaylist_type().equals("5")) {
            content_list = this.pInfo.getTagPlaylistTagList(playlistId, curActiveVersion);
            Iterator var15 = content_list.iterator();

            while(var15.hasNext()) {
               PlaylistContent tag = (PlaylistContent)var15.next();
               tag.setContent_id(String.valueOf(tag.getTag_id()));
            }

            playlist.setArr_content_list(content_list);
            List conditionList = this.pInfo.getTagPlaylistTagConditionList(playlistId, curActiveVersion);
            Iterator var22 = conditionList.iterator();

            while(var22.hasNext()) {
               PlaylistTagCondition condition = (PlaylistTagCondition)var22.next();
               this.pInfo.addTagConditionMapping(newPlaylistId, 1L, condition.getTag_id(), condition.getTag_condition_id());
            }
         }

         if (this.pInfo.addPlaylist(playlist) <= 0) {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN);
         }

         String eventType = "Add playlist";
         this.sendNoticeMail(playlist.getPlaylist_id(), eventType);
      } catch (Exception var18) {
         this.logger.error("", var18);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      }

      return this.getActivePlaylistInfo(newPlaylistId);
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority', 'Playlist Write Authority', 'Playlist Manage Authority')")
   public V2CommonResultResource forceGotoRecyclebin(V2DeletePlaylistRequestWrapper resource, HttpServletRequest request) throws Exception {
      boolean flag = false;
      List playlistIds = resource.getIds();

      for(int i = 0; i < playlistIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST, (String)playlistIds.get(i));
         } catch (Exception var18) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST);
      }

      String ipAddress = request.getRemoteAddr();
      String sessionId = UUID.randomUUID().toString().toUpperCase();
      V2CommonResultResource result = new V2CommonResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var10 = playlistIds.iterator();

      while(var10.hasNext()) {
         String playlistId = (String)var10.next();

         try {
            DeleteContentUtils.checkPlaylistFromSchedule(playlistId, ipAddress);
            if (this.pInfo.deletePlaylist(playlistId, this.getLoginUserId(), sessionId) <= 0) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PLAYLIST_NOT_DELETE, new String[]{playlistId});
            }

            successList.add(playlistId);

            try {
               this.sendNoticeMail((List)successList, "Delete playlist");
            } catch (Exception var15) {
               this.logger.error(var15);
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN);
            }
         } catch (RestServiceException var16) {
            this.logger.error(var16);
            V2PlaylistDeleteFail obj = new V2PlaylistDeleteFail();
            obj.setId(playlistId);
            obj.setReason(var16.getErrorMessage());
            obj.setReasonCode(var16.getErrorCode());
            failList.add(obj);
         } catch (Exception var17) {
            this.logger.error(var17);
            RestExceptionCode error = RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN;
            V2PlaylistDeleteFail obj = new V2PlaylistDeleteFail();
            obj.setId(playlistId);
            obj.setReason(error.getMessage());
            obj.setReasonCode(error.getCode());
            failList.add(obj);
         }
      }

      result.setSuccessList(successList);
      result.setFailList(failList);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority', 'Playlist Write Authority', 'Playlist Manage Authority')")
   public V2CommonResultResource gotoRecyclebin(V2DeletePlaylistRequestWrapper resource) throws Exception {
      boolean flag = false;
      List playlistIds = resource.getIds();

      for(int i = 0; i < playlistIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST, (String)playlistIds.get(i));
         } catch (Exception var19) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST);
      }

      String sessionId = UUID.randomUUID().toString().toUpperCase();
      V2CommonResultResource result = new V2CommonResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var8 = playlistIds.iterator();

      while(var8.hasNext()) {
         String playlistId = (String)var8.next();

         try {
            if (this.pInfo.isDeletablePlaylist(playlistId, this.getLoginUserId(), sessionId)) {
               if (this.pInfo.deletePlaylist(playlistId, this.getLoginUserId(), sessionId) <= 0) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PLAYLIST_NOT_DELETE, new String[]{playlistId});
               }

               successList.add(playlistId);

               try {
                  this.sendNoticeMail((List)successList, "Delete playlist");
               } catch (Exception var16) {
                  this.logger.error(var16);
                  throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN);
               }
            } else {
               V2PlaylistDeleteFail obj = new V2PlaylistDeleteFail();
               V2PlaylistDeleteCheckResource checkResource = new V2PlaylistDeleteCheckResource();
               ArrayList refScheduleList = new ArrayList();
               ArrayList refRulesetList = new ArrayList();
               RestExceptionCode error = RestExceptionCode.BAD_REQUEST_CANNOT_DELETE_CONTAINS_REFERENCED_ITEMS_IN_GROUP;
               String playlistName = this.playlistInfo.getPlaylistName(playlistId);
               this.setRefScheduleList(playlistId, refScheduleList, "PREMIUM");
               this.setRefRulesetList(playlistId, refRulesetList);
               checkResource.setRefScheduleList(refScheduleList);
               checkResource.setRefRulesetList(refRulesetList);
               obj.setId(playlistId);
               obj.setPlaylistName(playlistName);
               obj.setReason(error.generateFormattedMessages("playlists"));
               obj.setReasonCode(error.getCode());
               obj.setCheckResource(checkResource);
               failList.add(obj);
            }
         } catch (RestServiceException var17) {
            this.logger.error(var17);
            V2PlaylistDeleteFail obj = new V2PlaylistDeleteFail();
            obj.setId(playlistId);
            obj.setReason(var17.getErrorMessage());
            obj.setReasonCode(var17.getErrorCode());
            failList.add(obj);
         } catch (Exception var18) {
            RestExceptionCode error = RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN;
            this.logger.error(var18);
            V2PlaylistDeleteFail obj = new V2PlaylistDeleteFail();
            obj.setId(playlistId);
            obj.setReason(error.getMessage());
            obj.setReasonCode(error.getCode());
            failList.add(obj);
         }
      }

      result.setSuccessList(successList);
      result.setFailList(failList);
      return result;
   }

   public V2CommonResultResource permanentlyDeletePlaylist(V2DeletePlaylistRequestWrapper resource, HttpServletRequest request) throws Exception {
      boolean flag = false;
      List playlistIds = resource.getIds();

      for(int i = 0; i < playlistIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST, (String)playlistIds.get(i));
         } catch (Exception var19) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST);
      }

      String ipAddress = request.getRemoteAddr();
      String sessionId = UUID.randomUUID().toString().toUpperCase();
      V2CommonResultResource result = new V2CommonResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var10 = playlistIds.iterator();

      while(var10.hasNext()) {
         String playlistId = (String)var10.next();

         try {
            DeleteContentUtils.checkPlaylistFromSchedule(playlistId, ipAddress);
            if (this.pInfo.deletePlaylist(playlistId, this.getLoginUserId(), sessionId) <= 0) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PLAYLIST_NOT_DELETE, new String[]{playlistId});
            }

            successList.add(playlistId);

            try {
               this.sendNoticeMail(playlistId, "Delete playlist permanently");
            } catch (Exception var16) {
               this.logger.error(var16);
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN);
            }

            try {
               if (this.pInfo.deletePlaylistCompletely(playlistId) <= 0) {
               }
            } catch (Exception var15) {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
            }
         } catch (RestServiceException var17) {
            this.logger.error(var17);
            V2PlaylistDeleteFail obj = new V2PlaylistDeleteFail();
            obj.setId(playlistId);
            obj.setReason(var17.getErrorMessage());
            obj.setReasonCode(var17.getErrorCode());
            failList.add(obj);
         } catch (Exception var18) {
            RestExceptionCode error = RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN;
            this.logger.error(var18);
            V2PlaylistDeleteFail obj = new V2PlaylistDeleteFail();
            obj.setId(playlistId);
            obj.setReason(error.getMessage());
            obj.setReasonCode(error.getCode());
            failList.add(obj);
         }
      }

      result.setSuccessList(successList);
      result.setFailList(failList);
      return result;
   }

   public V2PlaylistAddContentsResponse addContentsToPlaylists(V2PlaylistContentResourceIdWrapper wrapper, HttpServletRequest request) throws Exception {
      UserGroupInfoImpl userGroupInfo = UserGroupInfoImpl.getInstance();
      String addedContents = (String)wrapper.getContentIds().stream().collect(Collectors.joining(":"));
      List notiDataList = new ArrayList();
      List failedPlaylist = new ArrayList();
      List successfulPlaylist = new ArrayList();
      if (wrapper.getContentIds() != null && !wrapper.getContentIds().isEmpty()) {
         Iterator var8 = wrapper.getPlaylistIds().iterator();

         while(var8.hasNext()) {
            String playlistId = (String)var8.next();
            Playlist thisPlaylist = this.pInfo.getPlaylistActiveVerInfo(playlistId);
            if (thisPlaylist.getContent_count() + wrapper.getContentIds().size() > 200) {
               failedPlaylist.add(thisPlaylist.getPlaylist_name());
            } else if (this.playlistInfo.addContentToPlaylist(playlistId, (String[])wrapper.getContentIds().toArray(new String[wrapper.getContentIds().size()]), this.getLoginUserId()) > 0) {
               successfulPlaylist.add(thisPlaylist.getPlaylist_name());

               try {
                  NotificationData notiData = new NotificationData();
                  notiData.setName(thisPlaylist.getPlaylist_name());
                  notiData.setOrgId(thisPlaylist.getOrganization_id());
                  notiData.setOrgName(userGroupInfo.getGroupNameByGroupId(thisPlaylist.getOrganization_id()));
                  notiData.setUserName(this.getLoginUserId());
                  notiDataList.add(notiData);
               } catch (Exception var13) {
                  throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
               }
            } else {
               failedPlaylist.add(thisPlaylist.getPlaylist_name());
            }

            ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
            scheduleInfo.setPlaylistTrigger(thisPlaylist.getPlaylist_id());
            EventInfo eInfo = EventInfoImpl.getInstance();
            eInfo.setPlaylistTrigger(thisPlaylist.getPlaylist_id());
         }

         if (notiDataList != null && notiDataList.size() > 0) {
            MailUtil.sendPlaylistEventMail(notiDataList, "Add to Playlist");
         }

         return V2PlaylistAddContentsResponse.V2PlaylistAddContentsResponseBuilder.aV2PlaylistAddContentsResponse().failedPlaylists(failedPlaylist).successfulPlaylists(successfulPlaylist).build();
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"The content id"});
      }
   }

   private Map getContentDurationByEffectList(String effectList) {
      Map map = new HashMap();
      if (effectList != null && !effectList.equalsIgnoreCase("")) {
         String[] arrEffectList = effectList.split(",");
         String[] var4 = arrEffectList;
         int var5 = arrEffectList.length;

         for(int var6 = 0; var6 < var5; ++var6) {
            String effect = var4[var6];
            String[] arrEffectItem = effect.split("↑");
            map.put(arrEffectItem[8], arrEffectItem[7]);
         }
      }

      return map;
   }

   private Long getDefaultContentDuration(String productType, String deviceType, Long contentDuration) {
      if (contentDuration == 0L && CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         if (deviceType.equalsIgnoreCase("iPLAYER")) {
            contentDuration = ContentConstants.CONTENT_DURATION;
         } else if (deviceType.equalsIgnoreCase("SPLAYER")) {
            contentDuration = ContentConstants.LITE_CONTENT_DURATION;
         } else if (!deviceType.equalsIgnoreCase("S2PLAYER") && !deviceType.equalsIgnoreCase("S3PLAYER")) {
            contentDuration = ContentConstants.LITE_CONTENT_DURATION;
         } else {
            contentDuration = ContentConstants.LITE_CONTENT_DURATION;
         }
      }

      return contentDuration;
   }

   public String getLoginUserId() {
      return SecurityUtils.getUserContainer() == null ? "" : SecurityUtils.getUserContainer().getUser().getUser_id();
   }

   public void setRefScheduleList(String playlistId, ArrayList refScheduleList, String productType) {
      ScheduleInterface sInfo = DAOFactory.getScheduleInfoImpl(productType);

      try {
         List programList = sInfo.getProgramByPlaylistId(playlistId);
         if (programList != null && programList.size() != 0) {
            String programName = "";
            Map map = null;
            List scheduleList = new ArrayList();
            String scheduleId = "";
            String scheduleName = "";

            for(int j = 0; j < programList.size(); ++j) {
               Map tmpmap = new HashMap();
               map = (Map)programList.get(j);
               scheduleId = (String)map.get("program_id");
               scheduleName = (String)map.get("program_name");
               tmpmap.put("scheduleId", scheduleId);
               tmpmap.put("scheduleName", scheduleName);
               scheduleList.add(tmpmap);
            }

            refScheduleList.add(scheduleList);
         } else {
            refScheduleList.add((Object)null);
         }

      } catch (SQLException var13) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      }
   }

   public String getScheduleLink(HttpServletRequest request, Map map, String productType) {
      String programId = (String)map.get("PROGRAM_ID");
      Map map_program = new HashMap();
      AbilityUtils ability = new AbilityUtils();
      boolean canEditSchedule = false;
      boolean canReadSchedule = false;
      if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         canEditSchedule = ability.checkAuthority("Content Schedule Write");
         canReadSchedule = ability.checkAuthority("Content Schedule Read");
      }

      String programUrl = "";
      String programSelectUrl = "";
      String isDeleted = "";
      String programSelectUrl_delete = "";
      String programName = "";
      map_program.put("contextPath", request.getContextPath());
      map_program.put("programId", programId);
      ScheduleInterface sInfo = DAOFactory.getScheduleInfoImpl(productType);

      try {
         isDeleted = sInfo.isDeleted(programId);
      } catch (SQLException var16) {
         this.logger.error("", var16);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      }

      if (canReadSchedule) {
         if (productType.equalsIgnoreCase("PREMIUM")) {
            map_program.put("isSelectMenu", "PREMIUM_TRASH");
         }

         map_program.put("groupType", "TRASH");
         programUrl = request.getContextPath() + "/schedule/program/programMain.htm?cmd=EDIT&progId=" + programId + "&group_type=ALL&group_name=ALL&productType=" + productType + "&isSelectMenu=" + productType + "";
         programSelectUrl = request.getContextPath() + "/schedule/scheduleAdmin.htm?cmd=QUERY&group_type=ALL&productType=" + productType + "&selId=" + programId + "&isSelect=TRUE&isSelectMenu=" + productType + "";
         programSelectUrl_delete = LinkedUtils.getLinkedURL(productType, "SCHEDULE", "SELECT", map_program);
         if (canEditSchedule) {
            if (isDeleted.equals("Y")) {
               programName = programName + "<a href='" + programSelectUrl_delete + "' target='content_ifrm' title='" + (String)map.get("PROGRAM_NAME") + "' ><img src='./images/common/button/btn5_default_02.gif' onClick='hidePlaylistMessagePanel()' border='0'></a>&nbsp;" + StrUtils.cutCharLen((String)map.get("PROGRAM_NAME"), 25) + "<br>";
            } else {
               programName = programName + "<a href='" + programSelectUrl + "' target='content_ifrm' title='" + (String)map.get("PROGRAM_NAME") + "' ><img src='./images/arrow/linked.png' onClick='hidePlaylistMessagePanel()'border='0'></a>&nbsp;<a href='" + programUrl + "' target='content_ifrm' onClick='hidePlaylistMessagePanel()'>" + StrUtils.cutCharLen((String)map.get("PROGRAM_NAME"), 25) + "</a><br>";
            }
         } else if (isDeleted.equals("Y")) {
            programName = programName + StrUtils.cutCharLen((String)map.get("PROGRAM_NAME"), 25) + "<br>";
         } else {
            programName = programName + "<a href='" + programSelectUrl + "' target='content_ifrm' title='" + (String)map.get("PROGRAM_NAME") + "' ><img src='./images/arrow/linked.png' onClick='hidePlaylistMessagePanel()' border='0'></a>&nbsp;" + StrUtils.cutCharLen((String)map.get("PROGRAM_NAME"), 25) + "<br>";
         }
      } else {
         programName = programName + StrUtils.cutCharLen((String)map.get("PROGRAM_NAME"), 25) + "<br>";
      }

      return programName;
   }

   private void setAmsLastMemory(List lastMemories) throws SQLException {
      if (lastMemories != null && !lastMemories.isEmpty()) {
         ContentInfo cmsDao = ContentInfoImpl.getInstance();
         Iterator var3 = lastMemories.iterator();

         while(var3.hasNext()) {
            V2AmsInformationResource ai = (V2AmsInformationResource)var3.next();
            String contentId = ai.getContentId();
            String gender = ai.getGender();
            String age = ai.getAge();
            if (!"".equals(StrUtils.nvl(gender)) && !"".equals(StrUtils.nvl(age))) {
               Map amsInfoMap = new HashMap();
               amsInfoMap.put("gender", gender);
               amsInfoMap.put("age", age);
               cmsDao.setAMSLastMemory(contentId, amsInfoMap);
            }
         }

      }
   }

   public List updatePlaylistCategories(V2PlaylistCategoryResourceRequestWrapper categories) throws Exception {
      List resource = new ArrayList();
      CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
      List playlistIds = categories.getPlaylistIds();
      List categoryIds = categories.getCategoryIds();
      boolean flag = false;
      Iterator var7 = playlistIds.iterator();

      String playlistId;
      while(var7.hasNext()) {
         playlistId = (String)var7.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST, playlistId);
         } catch (Exception var10) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST);
      }

      var7 = playlistIds.iterator();

      while(var7.hasNext()) {
         playlistId = (String)var7.next();
         categoryInfo.deleteCategoryFromPlaylistId(playlistId);
         categoryInfo.setCategoryFromPlaylistId(categoryIds, playlistId);
         PlaylistUtils.updateLastModifiedDate(playlistId);
         resource.add(this.getActivePlaylistInfo(playlistId));
      }

      this.noticeCategoryPlaylist(playlistIds);
      return resource;
   }

   public List getPlaylistContents(String playlistId) throws Exception {
      Playlist playlist = this.playlistInfo.getPlaylistActiveVerInfo(playlistId);
      List contentList = this.pInfo.getContentList(playlistId, playlist.getVersion_id());
      List res = new ArrayList();
      V2PlaylistContentResourceResponse pcrr = null;
      Iterator var6 = contentList.iterator();

      while(var6.hasNext()) {
         PlaylistContent pc = (PlaylistContent)var6.next();
         if (pc.getIs_sub_playlist()) {
            pcrr = V2PlaylistFactory.playlistContentResourceResponseBuilder(this.playlistInfo, pc);
         } else {
            Content content = this.cInfo.getContentAndFileActiveVerInfo(pc.getContent_id());
            pcrr = V2PlaylistFactory.playlistContentResourceResponseBuilder(this.cInfo, content);
         }

         if (pcrr != null) {
            res.add(pcrr);
         }
      }

      return res;
   }

   private void noticeCategoryPlaylist(List playlistIds) throws Exception {
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      List notiDataList = new ArrayList();
      Iterator var4 = playlistIds.iterator();

      while(var4.hasNext()) {
         String playlistId = (String)var4.next();

         try {
            NotificationData notiData = new NotificationData();
            Playlist playlist = this.pInfo.getPlaylistActiveVerInfo(playlistId);
            notiData.setName(playlist.getPlaylist_name());
            notiData.setOrgId(playlist.getOrganization_id());
            notiData.setOrgName(userGroupInfo.getGroupNameByGroupId(playlist.getOrganization_id()));
            notiData.setUserName(this.getLoginUserId());
            notiDataList.add(notiData);
         } catch (Exception var8) {
            this.logger.error(var8);
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
         }
      }

      if (notiDataList != null && notiDataList.size() > 0) {
         MailUtil.sendPlaylistEventMail(notiDataList, "Add to Playlist");
      }

   }

   private List checkPlaylistExpireDate(V2PlaylistResource resource) throws Exception {
      Locale locale = SecurityUtils.getLocale();
      int cnt = false;
      new ArrayList();
      List contents = resource.getContents();
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      List resContent = new ArrayList();
      Iterator var8 = contents.iterator();

      while(var8.hasNext()) {
         V2PlaylistItemResource item = (V2PlaylistItemResource)var8.next();
         Content content = cmsDao.getContentActiveVerInfo(item.getContentId());
         if (content != null && content.getExpiration_date() != null && content.getExpiration_date().compareTo(DateUtils.getCurrentTime("yyyyMMdd")) < 0) {
            V2PlaylistItemResourceResponse vr = new V2PlaylistItemResourceResponse();
            vr.setContentId(content.getContent_id());
            vr.setContentName(content.getContent_name());
            vr.setExpiredDate(content.getExpiration_date());
            resContent.add(vr);
         }
      }

      return resContent;
   }

   private void checkTagPlaylistExpireDate(V2PlaylistResource resource) throws Exception {
      Locale locale = SecurityUtils.getLocale();
      int cnt = false;
      new ArrayList();
      List contents = resource.getContents();
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      TagInfo tagInfo = TagInfoImpl.getInstance();
      Iterator var8 = contents.iterator();

      while(var8.hasNext()) {
         V2PlaylistItemResource item = (V2PlaylistItemResource)var8.next();
         List thumbnailList = cmsDao.getContentListWithThumbnailFromTagId(Long.valueOf(item.getContentId()));

         for(int j = 0; j < thumbnailList.size(); ++j) {
            Map mapContentInfo = (Map)thumbnailList.get(j);
            Content content = cmsDao.getContentActiveVerInfo(mapContentInfo.get("content_id").toString());
            if (content != null && content.getExpiration_date() != null && content.getExpiration_date().compareTo(DateUtils.getCurrentTime("yyyyMMdd")) < 0) {
               tagInfo.deleteRelationOfTagAndContent(Long.valueOf(item.getContentId()), content.getContent_id());
               this.logger.error("[INFO][MagicInfo_ExpiredContentJob] REMOVE Expired Content[" + content.getContent_id() + "] from Tag[" + Long.valueOf(item.getContentId()) + "]");
            }
         }
      }

   }

   private List checkContentExpireDate(V2PlaylistResource resource) throws Exception {
      List res = null;
      if ("5".equals(resource.getPlaylistType())) {
         this.checkTagPlaylistExpireDate(resource);
      } else {
         res = this.checkPlaylistExpireDate(resource);
      }

      return res;
   }

   private Map getContentDurationByEffectList(V2PlaylistResource resource) {
      Map map = new HashMap();
      List items = resource.getContents();
      Iterator var4 = items.iterator();

      while(var4.hasNext()) {
         V2PlaylistItemResource item = (V2PlaylistItemResource)var4.next();
         V2PlaylistContentEffectResource effects = item.getEffects();
         map.put(effects.getContentOrder(), effects.getContentDuration());
      }

      return map;
   }

   private Long getContentDuration(V2PlaylistResource resource, Content content, long beforeOrder, String productType, String deviceType, List contentList) {
      Long contentDuration = 0L;
      if (content != null && content.getPlay_time() != null && !content.getPlay_time().equals("-") && !content.getPlay_time().equals("")) {
         if (productType.equalsIgnoreCase("PREMIUM")) {
            contentDuration = ContentUtils.getPlayTimeStr(content.getPlay_time());
         }

         return contentDuration;
      } else {
         Map contentDurationMap = this.getContentDurationByEffectList(resource);
         if (contentDurationMap.containsKey(beforeOrder)) {
            contentDuration = (Long)contentDurationMap.get(beforeOrder);
            return contentDuration;
         } else {
            if (contentList != null) {
               Iterator var10 = contentList.iterator();

               while(var10.hasNext()) {
                  PlaylistContent playlistContent = (PlaylistContent)var10.next();
                  if (playlistContent.getContent_id().equalsIgnoreCase(content.getContent_id()) && playlistContent.getContent_order() == beforeOrder) {
                     contentDuration = playlistContent.getContent_duration();
                     return contentDuration;
                  }
               }
            }

            contentDuration = this.getDefaultContentDuration(productType, deviceType, content.getContent_duration());
            return contentDuration;
         }
      }
   }

   private Long getSyncContentDuration(V2PlaylistResource resource, Content content, long beforeOrder, String productType, String deviceType, List contentList) {
      Long contentDuration = 30L;
      Map contentDurationMap = this.getContentDurationByEffectList(resource);
      if (contentDurationMap.containsKey(beforeOrder)) {
         contentDuration = (Long)contentDurationMap.get(beforeOrder);
         return contentDuration;
      } else {
         if (contentList != null) {
            Iterator var10 = contentList.iterator();

            while(var10.hasNext()) {
               PlaylistContent playlistContent = (PlaylistContent)var10.next();
               if (playlistContent.getContent_id().equalsIgnoreCase(content.getContent_id()) && playlistContent.getContent_order() == beforeOrder) {
                  if (content.getPlay_time() != null && !content.getPlay_time().equals("")) {
                     contentDuration = ContentUtils.getPlayTimeStr(content.getPlay_time());
                  } else {
                     contentDuration = playlistContent.getContent_duration();
                  }

                  return contentDuration;
               }
            }
         }

         contentDuration = this.getDefaultContentDuration(productType, deviceType, ContentUtils.getPlayTimeStr(content.getPlay_time()));
         return contentDuration;
      }
   }

   private PlaylistContent setPlaylistContentEffects(String productType, String playlistType, PlaylistContent iContent, V2PlaylistContentEffectResource effects) {
      PlaylistContent pContent = new PlaylistContent();
      BeanUtils.copyProperties(iContent, pContent);
      pContent.setEffect_in_name(effects.getInName());
      pContent.setEffect_in_duration(effects.getInDuration());
      pContent.setEffect_in_direction(effects.getInDirection());
      pContent.setEffect_out_name(effects.getOutName());
      pContent.setEffect_out_duration(effects.getOutDuration());
      pContent.setEffect_out_direction(effects.getOutDirection());
      pContent.setContent_duration(effects.getContentDuration());
      if (effects.getContentDuration() <= 0L) {
         if ("4".equals(playlistType)) {
            pContent.setContent_duration((Long)null);
         } else if ("1".equals(playlistType)) {
            pContent.setContent_duration(5L);
         }
      }

      if (StrUtils.nvl(effects.getExpiredDate()).length() > 0) {
         pContent.setExpired_date(DateUtils.string2Timestamp(effects.getExpiredDate(), "yyyy-MM-dd"));
      } else {
         pContent.setExpired_date((Timestamp)null);
      }

      if (StrUtils.nvl(effects.getStartDate()).length() > 0) {
         pContent.setStart_date(DateUtils.string2Timestamp(effects.getStartDate(), "yyyy-MM-dd"));
      } else {
         pContent.setStart_date((Timestamp)null);
      }

      if ("2".equals(playlistType)) {
         pContent.setEffect_in_delay_duration(effects.getInDelayDuration());
         pContent.setEffect_in_delay_div(effects.getInDelayDiv());
         pContent.setEffect_out_delay_duration(effects.getOutDelayDuration());
         pContent.setEffect_out_delay_div(effects.getOutDelayDiv());
         pContent.setEffect_in_delay_direction(effects.getInDelayDirection());
         pContent.setEffect_out_delay_direction(effects.getOutDelayDirection());
      }

      if ("PREMIUM".equals(productType)) {
         if ("1".equals(playlistType)) {
            pContent.setGender(effects.getGender());
            pContent.setAge(effects.getAge());
            pContent.setAms_recog_type(effects.getAmsRecogType());
         }

         if (this.isIndependentPlayInAdvertisementPlaylist(playlistType, effects) && StringUtils.isBlank(effects.getStartTime())) {
            pContent.setStart_time("00:00:00");
         } else {
            pContent.setStart_time(effects.getStartTime());
         }

         if (this.isIndependentPlayInAdvertisementPlaylist(playlistType, effects) && StringUtils.isBlank(effects.getExpiredTime())) {
            pContent.setExpired_time("23:59:59");
         } else {
            pContent.setExpired_time(effects.getExpiredTime());
         }

         pContent.setRepeat_type(effects.getRepeatType());
         pContent.setIs_independent_play(effects.getIsIndependentPlay());
         pContent.setContiguous(effects.isContiguous());
      }

      return pContent;
   }

   private boolean isIndependentPlayInAdvertisementPlaylist(String playlistType, V2PlaylistContentEffectResource effects) {
      return "4".equals(playlistType) && StringUtils.equals(effects.getIsIndependentPlay(), "Y");
   }

   private ArrayList makeContentTagEntities(ArrayList iTagArrayList, V2PlaylistContentTagResource pcTag, Content content, int contentInx, int orderValue) {
      ArrayList tagArrayList = (ArrayList)iTagArrayList.stream().map((t) -> {
         ContentTagEntity ct = new ContentTagEntity();
         ct.setTag_id_list(t.getTag_id_list());
         ct.setTag_id(t.getTag_id());
         ct.setContent_id(t.getContent_id());
         ct.setMatch_type(t.getMatch_type());
         ct.setContent_order(t.getContent_order());
         return ct;
      }).collect(Collectors.toCollection(ArrayList::new));
      if (content.getContent_id().equals(pcTag.getContentId()) && (long)orderValue == pcTag.getContentOrder()) {
         ContentTagEntity tag = new ContentTagEntity();
         tag.setContent_id(pcTag.getContentId());
         tag.setContent_order(contentInx + 1);
         tag.setMatch_type(pcTag.getMatchType());
         List tagIdList = (List)pcTag.getTagIds().stream().map((s) -> {
            return (int)s;
         }).collect(Collectors.toList());
         tag.setTag_id_list(new ArrayList(tagIdList));
         tagArrayList.add(tag);
      }

      return tagArrayList;
   }

   private Playlist playlistBuilder(V2PlaylistResource resource, String playlistId, List contentList, ArrayList tagArrayList, Map totalSizeMap, Long totalPlayTime, boolean isAddSubPlaylist) throws Exception {
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      Long totalSize = 0L;
      if (resource.getPlaylistType().equals("5")) {
         Iterator it = totalSizeMap.entrySet().iterator();

         while(it.hasNext()) {
            Entry entry = (Entry)it.next();
            if (entry.getValue() != null) {
               totalSize = totalSize + (Long)entry.getValue();
            }
         }
      } else {
         for(int i = 0; i < contentList.size(); ++i) {
            totalSize = totalSize + (Long)totalSizeMap.get(((PlaylistContent)contentList.get(i)).getContent_id());
         }
      }

      String playlistName = StrUtils.nvl(resource.getPlaylistName()).equals("") ? "NO_TITLE" : resource.getPlaylistName();
      Long groupId = resource.getGroupId();
      int shareFlag = resource.getShareFlag();
      String metaData = StrUtils.nvl(resource.getMetaData()).equals("") ? "" : resource.getMetaData();
      User user = SecurityUtils.getLoginUser();
      Playlist pl = new Playlist();
      pl.setPlaylist_id(playlistId);
      pl.setPlaylist_name(playlistName);
      pl.setPlaylist_meta_data(metaData);
      pl.setDevice_type(resource.getDeviceType());
      pl.setDevice_type_version(Float.parseFloat(resource.getDeviceTypeVersion()));
      pl.setTotal_size(totalSize);
      pl.setPlay_time(ContentUtils.getPlayTimeFormattedStr(totalPlayTime));
      pl.setOrganization_id(user.getRoot_group_id());
      pl.setOrganization_id(user.getRoot_group_id());
      if (groupId != null && groupId != 0L) {
         pl.setGroup_id(new Long(groupId));
      } else if (user.isMu()) {
         pl.setGroup_id(this.playlistInfo.getRootId(this.getLoginUserId(), user.getRoot_group_id()));
      } else {
         pl.setGroup_id(this.pInfo.getRootId(this.getLoginUserId()));
      }

      pl.setCreator_id(this.getLoginUserId());
      pl.setShare_flag(shareFlag);
      if (resource.getShuffleFlag()) {
         pl.setIs_shuffle("Y");
      } else {
         pl.setIs_shuffle("N");
      }

      if (isAddSubPlaylist) {
         pl.setHas_sub_playlist(true);
      } else {
         pl.setHas_sub_playlist(false);
      }

      pl.setPlaylist_type(resource.getPlaylistType());
      pl.setAms_mode(resource.getAmsMode());
      pl.setAms_direct_play(resource.getAmsDirectPlay());
      if ("2".equals(resource.getPlaylistType())) {
         pl.setIs_vwl("Y");
      } else {
         pl.setIs_vwl("N");
      }

      pl.setDefault_content_duration(resource.getDefaultContentDuration());
      pl.setArr_content_list(contentList);
      if ("3".equals(resource.getPlaylistType())) {
         ArrayList syncPlaylistList = new ArrayList();
         List items = resource.getContents();
         Map tMap = (Map)items.stream().collect(Collectors.groupingBy(V2PlaylistItemResource::getSyncPlayId));
         tMap.entrySet().stream().forEach((entryx) -> {
            List list = (List)entryx.getValue();
            if (list != null && !list.isEmpty()) {
               V2PlaylistItemResource item = (V2PlaylistItemResource)list.get(0);
               SyncPlaylist sPlaylist = new SyncPlaylist();
               sPlaylist.setPlaylist_id(playlistId);
               sPlaylist.setSync_play_id(item.getSyncPlayId());
               sPlaylist.setIs_sync(item.getSyncStatus());
               syncPlaylistList.add(sPlaylist);
            }

         });
         pl.setSync_status_list(syncPlaylistList);
      } else if ("5".equals(resource.getPlaylistType())) {
         pl.setIgnore_tag(resource.getIgnoreTag());
         pl.setEvenness_playback(resource.getEvennessPlayback());
      }

      pl.setContent_count(contentList.size());
      if ("PREMIUM".equals(resource.getProductType())) {
         pl.setTagList(tagArrayList);
      }

      return pl;
   }

   private void sendNoticeMail(List playlistIds, String eventType) throws Exception {
      UserGroupInfoImpl userGroupInfo = UserGroupInfoImpl.getInstance();

      try {
         List notiDataList = new ArrayList();
         Iterator var5 = playlistIds.iterator();

         while(var5.hasNext()) {
            String playlistId = (String)var5.next();
            Playlist p = this.playlistInfo.getPlaylistActiveVerInfo(playlistId);
            NotificationData notiData = new NotificationData();
            notiData.setName(p.getPlaylist_name());
            notiData.setOrgId(p.getOrganization_id());
            notiData.setOrgName(userGroupInfo.getGroupNameByGroupId(p.getOrganization_id()));
            notiData.setUserName(this.getLoginUserId());
            notiDataList.add(notiData);
         }

         MailUtil.sendPlaylistEventMail(notiDataList, eventType);
      } catch (Exception var9) {
         this.logger.error(var9);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      }
   }

   private void sendNoticeMail(String playlistId, String eventType) throws Exception {
      List ids = new ArrayList();
      ids.add(playlistId);
      this.sendNoticeMail((List)ids, eventType);
   }

   private Map makeSyncPlaylistDurationMap(List items) {
      Map map = new HashMap();
      Iterator var3 = items.iterator();

      while(var3.hasNext()) {
         V2PlaylistItemResource item = (V2PlaylistItemResource)var3.next();
         if ("Y".equals(item.getSyncStatus())) {
            map.put(item.getSyncOrder(), item.getSyncDuration());
         }
      }

      return map;
   }

   private PlaylistContent subPlaylistContentBuilder(String playlistId, Long order) throws Exception {
      PlaylistContent subPlaylist = new PlaylistContent();
      subPlaylist.setIs_sub_playlist(true);
      subPlaylist.setContent_id(playlistId);
      subPlaylist.setSync_play_id("0");
      subPlaylist.setContent_order(order);
      subPlaylist.setContent_duration((Long)null);
      subPlaylist.setExpired_date((Timestamp)null);
      subPlaylist.setExpired_time((String)null);
      subPlaylist.setStart_date((Timestamp)null);
      subPlaylist.setStart_time((String)null);
      subPlaylist.setContiguous(false);
      return subPlaylist;
   }

   private long getLongestNoSyncTotalPlaytime(List items) {
      Map map = new HashMap();
      Iterator var3 = items.iterator();

      while(var3.hasNext()) {
         V2PlaylistItemResource item = (V2PlaylistItemResource)var3.next();
         if ("N".equals(item.getSyncStatus())) {
            Long totalPlaytime = (Long)map.get(item.getSyncPlayId());
            if (totalPlaytime == null) {
               totalPlaytime = 0L;
            }

            totalPlaytime = totalPlaytime + Long.valueOf(item.getSyncDuration());
            map.put(item.getSyncPlayId(), totalPlaytime);
         }
      }

      long max = (Long)((Entry)map.entrySet().stream().max(Comparator.comparing(Entry::getValue)).get()).getValue();
      return max;
   }

   private V2PlaylistServiceImpl.PlaylistContentInformation playlistContentListBuilder(V2PlaylistResource resource, String playlistId, Long curActiveVersion, List contents, String productType, V2PlaylistServiceImpl.contentBuilderType builderType) throws Exception {
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      Long nowVersion = curActiveVersion + 1L;
      String playlistType = resource.getPlaylistType();
      Map totalSizeMapByContentId = new HashMap();
      Long totalPlayTime = 0L;
      Map syncDurationMap = new HashMap();
      if ("3".equals(resource.getPlaylistType())) {
         syncDurationMap = this.makeSyncPlaylistDurationMap(contents);
      }

      if (!StringUtils.equals("3", resource.getPlaylistType()) && !StringUtils.equals("4", resource.getPlaylistType())) {
         contents.forEach((contentx) -> {
            if (contentx.getContentDuration() == null) {
               contentx.setContentDuration(0L);
            }

            if (!StringUtils.equals(contentx.getMediaType(), "CIFS") && !StringUtils.equals(contentx.getMediaType(), "FTP") && !StringUtils.equals(contentx.getMediaType(), "ADS") && !StringUtils.equals(contentx.getMediaType(), "MOVIE") && contentx.getContentDuration() < ContentConstants.MINIMUM_CONTENT_DURATION) {
               contentx.setContentDuration(ContentConstants.MINIMUM_CONTENT_DURATION);
               contentx.getEffects().setContentDuration(ContentConstants.MINIMUM_CONTENT_DURATION);
            }

         });
      }

      boolean addSubPlaylist = false;
      ArrayList tagArrayList = new ArrayList();
      int inx = 0;
      List contentList = new ArrayList();
      boolean foundSyncGroup = false;

      for(Iterator var18 = contents.iterator(); var18.hasNext(); ++inx) {
         V2PlaylistItemResource item = (V2PlaylistItemResource)var18.next();
         PlaylistContent pContent = null;
         Content content = cmsDao.getContentActiveVerInfo(item.getContentId());
         List pList = null;
         if (builderType == V2PlaylistServiceImpl.contentBuilderType.UPDATE) {
            pList = this.pInfo.getContentList(playlistId, curActiveVersion);
         }

         if (content == null) {
            Playlist subplaylist = this.playlistInfo.getPlaylistActiveVerInfo(item.getContentId());
            if (subplaylist != null) {
               totalPlayTime = totalPlayTime + Long.valueOf((long)DateUtils.changeFormatTimeToSecond(subplaylist.getPlay_time()));
               totalSizeMapByContentId.put(subplaylist.getPlaylist_id(), subplaylist.getTotal_size());
               PlaylistContent subPlaylistContent = this.subPlaylistContentBuilder(item.getContentId(), new Long((long)(inx + 1)));
               contentList.add(subPlaylistContent);
               addSubPlaylist = true;
            }
         } else {
            boolean foundContent = false;
            if (builderType == V2PlaylistServiceImpl.contentBuilderType.UPDATE) {
               Iterator var24 = pList.iterator();

               while(var24.hasNext()) {
                  PlaylistContent tempContent = (PlaylistContent)var24.next();
                  if (item.getContentId().equals(tempContent.getContent_id()) && item.getContentOrder() == tempContent.getContent_order()) {
                     foundContent = true;
                     pContent = tempContent;
                     content.setContent_duration(tempContent.getContent_duration());
                     break;
                  }
               }
            }

            if (!foundContent) {
               pContent = new PlaylistContent();
            }

            totalSizeMapByContentId.put(content.getContent_id(), content.getTotal_size());
            pContent.setContent_id(content.getContent_id());
            if (builderType == V2PlaylistServiceImpl.contentBuilderType.UPDATE && "1".equals(playlistType) && resource.getIsChangedAMSMode()) {
               pContent.setAms_recog_type("none");
            }

            pContent.setRandom_count(item.getRandomCount());
            pContent.setPlay_weight(item.getPlayWeight());
            String sync_play_id = "0";
            if ("3".equals(resource.getPlaylistType())) {
               sync_play_id = item.getSyncPlayId();
               pContent.setContent_order(item.getSyncOrder());
            } else {
               pContent.setContent_order(new Long((long)(inx + 1)));
            }

            pContent.setSync_play_id(sync_play_id);
            pContent.setPlaylist_id(playlistId);
            if (builderType == V2PlaylistServiceImpl.contentBuilderType.UPDATE) {
               content.setContent_order(inx + 1);
            }

            if (!"4".equalsIgnoreCase(resource.getPlaylistType())) {
               Long content_duration = 0L;
               if (builderType == V2PlaylistServiceImpl.contentBuilderType.UPDATE && playlistType.equals("3")) {
                  content_duration = this.getSyncContentDuration(resource, content, item.getContentOrder(), productType, resource.getDeviceType(), pList);
               } else {
                  content_duration = this.getContentDuration(resource, content, item.getContentOrder(), productType, resource.getDeviceType(), pList);
               }

               pContent.setContent_duration(content_duration);
               if (!"3".equals(resource.getPlaylistType())) {
                  pContent.setContent_duration_milli(content.getPlay_time_milli());
               }
            }

            V2PlaylistContentEffectResource effects = item.getEffects();
            if (effects.getContentId().equals(content.getContent_id()) && effects.getContentOrder() == item.getContentOrder()) {
               pContent = this.setPlaylistContentEffects(productType, resource.getPlaylistType(), pContent, effects);
            }

            if ("PREMIUM".equals(productType) && item.getContentTag() != null) {
               tagArrayList = this.makeContentTagEntities(tagArrayList, item.getContentTag(), content, inx, inx + 1);
            }

            if (pContent.getContent_duration() != null && !"3".equals(resource.getPlaylistType())) {
               totalPlayTime = totalPlayTime + pContent.getContent_duration();
            }

            if ("3".equals(resource.getPlaylistType())) {
               if ("Y".equals(item.getSyncStatus()) && !"".equals(item.getSyncDuration())) {
                  foundSyncGroup = true;
                  String time = (String)((Map)syncDurationMap).get(pContent.getContent_order());
                  String[] timeArr = time.split(":");
                  if (totalPlayTime == 0L) {
                     for(int d = 1; d <= ((Map)syncDurationMap).size(); ++d) {
                        totalPlayTime = totalPlayTime + Long.valueOf((String)((Map)syncDurationMap).get((long)d));
                     }
                  }

                  if (timeArr.length == 1) {
                     pContent.setContent_duration(Long.valueOf(timeArr[0]));
                     pContent.setContent_duration_milli("");
                  } else {
                     pContent.setContent_duration(Long.valueOf(timeArr[0]));
                     pContent.setContent_duration_milli(timeArr[1]);
                  }
               } else if ("N".equals(item.getSyncStatus()) && StringUtils.isNotBlank(item.getSyncDuration())) {
                  pContent.setContent_duration(Long.valueOf(item.getSyncDuration()));
               }
            }

            contentList.add(pContent);
         }
      }

      if ("3".equals(resource.getPlaylistType()) && !foundSyncGroup) {
         totalPlayTime = this.getLongestNoSyncTotalPlaytime(contents);
      }

      return (new V2PlaylistServiceImpl.PlaylistContentInformation()).contentsBuilder(contentList).totalPlayTimeBuilder(totalPlayTime).tagArrayListBuilder(tagArrayList).totalSizeMapByContentIdBuilder(totalSizeMapByContentId).addSubPlaylistBuilder(addSubPlaylist);
   }

   private V2PlaylistServiceImpl.PlaylistContentInformation tagPlaylistContentListBuilder(String playlistId, Long curActiveVersion, List contents, String productType) throws Exception {
      TagInfo tagInfo = TagInfoImpl.getInstance();
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      Long nowVersion = curActiveVersion + 1L;
      Map totalSizeMapByContentId = new HashMap();
      Long totalPlayTime = 0L;
      ArrayList tagArrayList = new ArrayList();
      int inx = 0;
      List contentList = new ArrayList();

      label138:
      for(Iterator var13 = contents.iterator(); var13.hasNext(); ++inx) {
         V2PlaylistTagsResource item = (V2PlaylistTagsResource)var13.next();
         TagEntity tag = tagInfo.getTag((int)((long)item.getTagId()));
         PlaylistContent pContent = new PlaylistContent();
         pContent.setPlaylist_id(playlistId);
         pContent.setContent_id(item.getTagId() + "");
         pContent.setTag_order((long)(inx + 1));
         pContent.setTag_duration(item.getDuration());
         if (!"".equals(StrUtils.nvl(item.getExpireDate()))) {
            pContent.setExpired_date(DateUtils.string2Timestamp(item.getExpireDate(), "yyyy-MM-dd"));
         }

         if (!"".equals(StrUtils.nvl(item.getStartDate()))) {
            pContent.setStart_date(DateUtils.string2Timestamp(item.getStartDate(), "yyyy-MM-dd"));
         }

         contentList.add(pContent);
         List mapList = new ArrayList();
         if (item.getTagConditions() != null && !item.getTagConditions().isEmpty()) {
            String condition;
            String conditionData;
            Iterator var34;
            if (tag != null && tag.getTag_type() == 1L) {
               var34 = item.getTagConditions().iterator();

               label104:
               while(true) {
                  String conditionData;
                  do {
                     while(true) {
                        if (!var34.hasNext()) {
                           break label104;
                        }

                        condition = (String)var34.next();
                        pContent.setNumber_str(condition);
                        if (!"".equals(StrUtils.nvl(condition))) {
                           List equals = new ArrayList();
                           List ups = new ArrayList();
                           List downs = new ArrayList();
                           String[] tempConditionList = condition.split(",");
                           String[] tagConditionEqual = tempConditionList;
                           int var46 = tempConditionList.length;

                           for(int var48 = 0; var48 < var46; ++var48) {
                              conditionData = tagConditionEqual[var48];
                              String tagConditionStr = conditionData.trim();
                              String regexInequality = "^([0-9]*)(-)([0-9]*)$";
                              String regexNumber = "^([0-9]*)$";
                              if (tagConditionStr.matches(regexInequality)) {
                                 String[] list = null;
                                 if (tagConditionStr.indexOf("-") > 0) {
                                    list = tagConditionStr.split("-");
                                    ups.add(list[1]);
                                    downs.add(list[0]);
                                 }
                              } else if (tagConditionStr.matches(regexNumber)) {
                                 equals.add(tagConditionStr);
                              }
                           }

                           tagConditionEqual = (String[])equals.toArray(new String[equals.size()]);
                           String[] tagConditionUp = (String[])ups.toArray(new String[ups.size()]);
                           String[] tagConditionDown = (String[])downs.toArray(new String[downs.size()]);
                           conditionData = this.playlistInfo.getConditionIdWithTagNumber(Long.valueOf((long)item.getTagId()), tagConditionEqual, tagConditionUp, tagConditionDown);
                           break;
                        }

                        conditionData = this.playlistInfo.getConditionIdWithTagNumber(Long.valueOf((long)tag.getTag_id()), (String[])null, (String[])null, (String[])null);
                        if (conditionData != null) {
                           String[] temp = conditionData.split(",");
                           String[] var22 = temp;
                           int var23 = temp.length;

                           for(int var24 = 0; var24 < var23; ++var24) {
                              String conditionId = var22[var24];
                              Long condition_id = Long.valueOf(conditionId.trim());
                              Map tagData = new HashMap();
                              tagData.put("tag_id", (long)tag.getTag_id());
                              tagData.put("tag_condition_id", condition_id);
                              mapList.add(tagData);
                           }
                        }
                     }
                  } while(conditionData == null);

                  String[] temp = conditionData.split(",");
                  String[] var52 = temp;
                  int var53 = temp.length;

                  for(int var54 = 0; var54 < var53; ++var54) {
                     String conditionId = var52[var54];
                     conditionId = conditionId.trim();
                     Map tagData = new HashMap();
                     tagData.put("tag_id", Long.valueOf((long)item.getTagId()));
                     tagData.put("tag_condition_id", Long.valueOf(conditionId));
                     mapList.add(tagData);
                  }
               }
            } else {
               var34 = item.getTagConditions().iterator();

               while(var34.hasNext()) {
                  condition = (String)var34.next();
                  conditionData = condition;
                  if (condition != null && condition.equals("NOT_ASSIGN")) {
                     conditionData = "-1";
                  }

                  this.pInfo.checkExistTagCondition(playlistId, nowVersion, (long)item.getTagId(), Long.valueOf(conditionData));
                  this.pInfo.addTagConditionMapping(playlistId, nowVersion, Long.valueOf((long)item.getTagId()), Long.valueOf(condition));
                  Map tagData = new HashMap();
                  tagData.put("tag_id", (long)item.getTagId());
                  tagData.put("tag_condition_id", Long.valueOf(conditionData));
                  mapList.add(tagData);
               }
            }
         } else {
            Map tagData = new HashMap();
            tagData.put("tag_id", (long)item.getTagId());
            mapList.add(tagData);
         }

         if (mapList != null && mapList.size() > 0) {
            List pList = this.playlistInfo.getContentListFromTagId(mapList);
            if (pList != null && pList.size() > 0) {
               Iterator var36 = pList.iterator();

               while(true) {
                  while(true) {
                     Content contentInfo;
                     do {
                        if (!var36.hasNext()) {
                           continue label138;
                        }

                        Map content = (Map)var36.next();
                        String contentId = (String)content.get("content_id");
                        contentInfo = cmsDao.getContentActiveVerInfo(contentId);
                     } while(contentInfo == null);

                     totalSizeMapByContentId.put(contentInfo.getContent_id(), contentInfo.getTotal_size());
                     if (contentInfo.getPlay_time() != null && !contentInfo.getPlay_time().equals("")) {
                        totalPlayTime = totalPlayTime + ContentUtils.getPlayTimeStr(contentInfo.getPlay_time());
                     } else {
                        totalPlayTime = totalPlayTime + pContent.getTag_duration();
                     }
                  }
               }
            }
         }
      }

      return (new V2PlaylistServiceImpl.PlaylistContentInformation()).contentsBuilder(contentList).totalPlayTimeBuilder(totalPlayTime).tagArrayListBuilder(tagArrayList).totalSizeMapByContentIdBuilder(totalSizeMapByContentId);
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public List getContentTagList(String playlistId) throws Exception {
      List resource = new ArrayList();
      PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
      TagInfo tagInfo = TagInfoImpl.getInstance();
      playlistId = StrUtils.nvl(playlistId).equals("") ? "" : playlistId;
      Playlist playlist = playlistInfo.getPlaylistActiveVerInfo(playlistId);
      if (playlist == null) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PLAYLIST_NOT_ACTIVE_INFO, new String[]{playlistId});
      } else {
         List list = playlistInfo.getTagList(playlist.getPlaylist_id(), playlist.getVersion_id());
         List logList = tagInfo.getlogContentTag(list);
         ObjectMapper mapper = new ObjectMapper();

         for(int i = 0; i < logList.size(); ++i) {
            new V2ContentTagLogResource();
            String jsonString = mapper.writeValueAsString(logList.get(i));
            Map map = (Map)mapper.readValue(jsonString, new TypeReference() {
            });
            Map map = ConvertUtil.convertMap(map);
            V2ContentTagLogResource tempResource = (V2ContentTagLogResource)mapper.convertValue(map, V2ContentTagLogResource.class);
            resource.add(tempResource);
         }

         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority', 'Playlist Read Authority', 'Playlist Write Authority', 'Playlist Manage Authority')")
   public List getPlaylistScheduleMappingInfo(List playlistIds) throws Exception {
      Iterator var2 = playlistIds.iterator();

      while(var2.hasNext()) {
         String playlistId = (String)var2.next();
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST, playlistId);
      }

      return this.playlistInfo.getPlaylistScheduleMapping(playlistIds);
   }

   public String convertString(List list) {
      String result = "";
      if (!list.isEmpty() && list.size() > 0) {
         StringBuffer strBuf = new StringBuffer();

         for(int i = 0; i < list.size(); ++i) {
            strBuf.append(list.get(i));
            if (i < list.size() - 1) {
               strBuf.append(",");
            }
         }

         result = strBuf.toString();
      }

      return result;
   }

   @PreAuthorize("hasAnyAuthority('Content Manage Authority','Playlist Manage Authority')")
   public V2CommonResultResource deletePlaylistByDeleteMethod(V2DeletePlaylistRequestWrapper wrapper, HttpServletRequest request) throws Exception {
      V2CommonResultResource resource = new V2CommonResultResource();
      if ("GO_TO_RECYCLEBIN".equalsIgnoreCase(wrapper.getDeleteMethod())) {
         resource = this.gotoRecyclebin(wrapper);
      } else if ("GO_TO_RECYCLEBIN_FORCE".equalsIgnoreCase(wrapper.getDeleteMethod())) {
         resource = this.forceGotoRecyclebin(wrapper, request);
      } else if ("DELETE_FORCE".equalsIgnoreCase(wrapper.getDeleteMethod())) {
         resource = this.permanentlyDeletePlaylist(wrapper, request);
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority', 'Playlist Write Authority', 'Playlist Manage Authority')")
   public V2CommonResultResource movePlaylist(V2CommonIds resource, String groupId) throws Exception {
      boolean flag = false;
      List playlistIds = resource.getIds();

      for(int i = 0; playlistIds != null && i < playlistIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST, Long.parseLong(groupId), (String)playlistIds.get(i));
         } catch (Exception var19) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST);
      }

      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      V2CommonResultResource resourceList = new V2CommonResultResource();
      new V2PlaylistResourceResponse();
      List successResourceList = new ArrayList();
      List failResourceList = new ArrayList();
      PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
      List notiDataList = new ArrayList();

      for(int i = 0; playlistIds != null && i < playlistIds.size(); ++i) {
         try {
            if (groupId == null || groupId.equals("")) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"groupId"});
            }

            if (pInfo.setPlaylistGroup((String)playlistIds.get(i), new Long(groupId)) <= 0) {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
            }

            try {
               NotificationData notiData = new NotificationData();
               Playlist playlist = pInfo.getPlaylistActiveVerInfo((String)playlistIds.get(i));
               notiData.setName(playlist.getPlaylist_name());
               notiData.setOrgId(playlist.getOrganization_id());
               notiData.setOrgName(userGroupInfo.getGroupNameByGroupId(playlist.getOrganization_id()));
               notiData.setUserName(SecurityUtils.getUserContainer().getUser().getUser_id());
               notiDataList.add(notiData);
            } catch (Exception var16) {
               this.logger.error(var16);
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
            }

            successResourceList.add(this.getActivePlaylistInfo((String)playlistIds.get(i)));
         } catch (RestServiceException var17) {
            this.logger.error(var17);
            V2PlaylistCommonResource obj = new V2PlaylistCommonResource();
            obj.setPlaylistId((String)playlistIds.get(i));
            obj.setGroupId(groupId);
            obj.setReason(var17.getErrorMessage());
            obj.setReasonCode(var17.getErrorCode());
            failResourceList.add(obj);
         } catch (Exception var18) {
            this.logger.error(var18);
            RestExceptionCode error = RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN;
            V2PlaylistCommonResource obj = new V2PlaylistCommonResource();
            obj.setPlaylistId((String)playlistIds.get(i));
            obj.setGroupId(groupId);
            obj.setReason(error.getMessage());
            obj.setReasonCode(error.getCode());
            failResourceList.add(obj);
         }
      }

      if (notiDataList != null && notiDataList.size() > 0) {
         MailUtil.sendPlaylistEventMail(notiDataList, "Edit playlist");
      }

      resourceList.setSuccessList(successResourceList);
      resourceList.setFailList(failResourceList);
      return resourceList;
   }

   public void setRefRulesetList(String contentId, ArrayList refPlaylistList) {
      RuleSetInfo rulesetDao = RuleSetInfoImpl.getInstance();

      try {
         List rulesetList = rulesetDao.getRulesetUsingContents(contentId);
         if (rulesetList != null && rulesetList.size() > 0) {
            List list = new ArrayList();

            for(int i = 0; i < rulesetList.size(); ++i) {
               Map map = new HashMap();
               RuleSet ruleset = (RuleSet)rulesetList.get(i);
               map.put("rulesetId", ruleset.getRuleset_id());
               map.put("rulesetName", ruleset.getName());
               list.add(map);
            }

            refPlaylistList.add(list);
         } else {
            refPlaylistList.add((Object)null);
         }

      } catch (Exception var9) {
         this.logger.error("", var9);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority', 'Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Add Authority', 'Content Schedule Manage Authority')")
   public V2PlaylistResourceResponse getPlaylistDetails(String playlistId, String productType) throws Exception {
      PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
      Playlist playlist = playlistInfo.getPlaylistActiveVerInfo(playlistId);
      if (playlist == null) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"playlist information"});
      } else {
         new ArrayList();
         V2PlaylistResourceResponse res = new V2PlaylistResourceResponse();
         String plGroupName = playlistInfo.getGroupName(playlist.getGroup_id());
         res.setPlaylistId(playlist.getPlaylist_id());
         res.setPlaylistName(playlist.getPlaylist_name());
         res.setPlaylistType(playlist.getPlaylist_type());
         res.setDeviceType(playlist.getDevice_type());
         res.setDeviceTypeVersion(playlist.getDevice_type_version());
         String[] convertToSeconds = playlist.getPlay_time().split(":");
         if (convertToSeconds.length == 1) {
            res.setPlayTime(0L);
         } else {
            res.setPlayTime(Long.valueOf(convertToSeconds[0]) * 3600L + Long.valueOf(convertToSeconds[1]) * 60L + Long.valueOf(convertToSeconds[2]));
         }

         res.setLastModifiedDate(playlist.getLast_modified_date());
         res.setVersionId(playlist.getVersion_id());
         res.setShuffleFlag(playlist.getIs_shuffle());
         res.setShareFlag(playlist.getShare_flag());
         res.setGroupId(playlist.getGroup_id());
         res.setGroupName(plGroupName);
         res.setMetaData(playlist.getPlaylist_meta_data());
         res.setCreatorId(playlist.getCreator_id());
         res.setTotalSize(playlist.getTotal_size());
         res.setDefaultContentDuration(playlist.getDefault_content_duration());
         CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
         res.setCategoryList(categoryInfo.getCategoryWithPlaylistId(playlistId));
         ContentInfo contentDao = ContentInfoImpl.getInstance();
         TagInfo tagInfo = TagInfoImpl.getInstance();
         if (playlist != null) {
            List contentList;
            ArrayList trs;
            if (playlist.getPlaylist_type().equals("5")) {
               contentList = playlistInfo.getTagList(playlistId, playlist.getVersion_id());
               trs = new ArrayList();
               Iterator var24 = contentList.iterator();

               while(true) {
                  if (!var24.hasNext()) {
                     res.setTags(trs);
                     res.setMetaData(playlist.getPlaylist_meta_data());
                     res.setPlaylistName(playlist.getPlaylist_name());
                     res.setDeviceType(playlist.getDevice_type());
                     res.setDeviceTypeVersion(playlist.getDevice_type_version());
                     res.setPlaylistType(playlist.getPlaylist_type());
                     res.setShareFlag(playlist.getShare_flag());
                     res.setGroupId(playlist.getGroup_id());
                     res.setGroupName(playlist.getGroup_name());
                     res.setHasSubPlaylist(playlist.getHas_sub_playlist());
                     res.setEvennessPlayback(playlist.getEvenness_playback());
                     res.setIgnoreTag(playlist.getIgnore_tag());
                     res.setVersionId(playlist.getVersion_id());
                     break;
                  }

                  PlaylistContent tag = (PlaylistContent)var24.next();
                  V2PlaylistTagsResourceResponse vr = new V2PlaylistTagsResourceResponse();
                  vr.setTagId(tag.getTag_id());
                  vr.setTagType(tag.getTag_type());
                  vr.setOrder(tag.getTag_order());
                  vr.setName(tag.getTag_name());
                  vr.setDuration(tag.getTag_duration());
                  vr.setStartDate(StrUtils.nvl(DateUtils.timestamp2StringDate(tag.getStart_date())));
                  vr.setExpiredDate(StrUtils.nvl(DateUtils.timestamp2StringDate(tag.getExpired_date())));
                  vr.setRepeatType(tag.getRepeat_type().replace(",", ";"));
                  List thumb;
                  List thumb;
                  if (tag.getTag_type() == 1L) {
                     ArrayList tagConditioniList = new ArrayList();
                     Map condition = new HashMap();
                     if (tag.getNumber_str() != null && !tag.getNumber_str().equals("") && !tag.getNumber_str().equals("null")) {
                        condition.put("tagCondition", tag.getNumber_str());
                     }

                     tagConditioniList.add(condition);
                     vr.setTagConditions(tagConditioniList);
                     String conditionIdStr = playlistInfo.getTagConditionIdWithTagNumber(playlistId, playlist.getVersion_id(), tag.getTag_id(), tag.getNumber_str());
                     if (conditionIdStr != null && !conditionIdStr.equals("")) {
                        List tagCount = playlistInfo.getCntContentAtTagPlaylist(tag.getTag_id(), conditionIdStr);
                        if (tagCount != null) {
                           vr.setTagCount(tagCount);
                        }

                        thumb = playlistInfo.getThumbContentAtTagPlaylist(tag.getTag_id(), conditionIdStr);
                        if (thumb != null) {
                           thumb = this.convertMapToTagResources(thumb);
                           vr.setContents(thumb);
                        }
                     } else {
                        List tagCount = new ArrayList();
                        Map tempMap = new HashMap();
                        tempMap.put("total_size", 0);
                        tempMap.put("count", 0);
                        tagCount.add(tempMap);
                        vr.setTagCount(tagCount);
                     }
                  } else {
                     List conditionList = playlistInfo.getPlaylistTagConditionList(playlistId, playlist.getVersion_id(), tag.getTag_id());
                     if (conditionList != null && conditionList.size() > 0) {
                        List conditionIds = new ArrayList();
                        ArrayList tagConditioniList = new ArrayList();
                        Iterator var20 = conditionList.iterator();

                        while(var20.hasNext()) {
                           PlaylistTag tagCondition = (PlaylistTag)var20.next();
                           Map condition = new HashMap();
                           condition.put("tagConditionId", tagCondition.getTag_condition_id());
                           condition.put("tagCondition", tagCondition.getTag_condition());
                           conditionIds.add(String.valueOf(tagCondition.getTag_condition_id()));
                           tagConditioniList.add(condition);
                        }

                        vr.setTagConditions(tagConditioniList);
                        String conditionIdStr = conditionIds.toString().replace("[", "").replace("]", "");
                        thumb = playlistInfo.getCntContentAtTagPlaylist(tag.getTag_id(), conditionIdStr);
                        if (thumb != null) {
                           vr.setTagCount(thumb);
                        }

                        thumb = playlistInfo.getThumbContentAtTagPlaylist(tag.getTag_id(), conditionIdStr);
                        if (thumb != null) {
                           List contents = this.convertMapToTagResources(thumb);
                           vr.setContents(contents);
                        }
                     } else {
                        List thumbnailList = contentDao.getContentListWithThumbnailFromTagId(tag.getTag_id());
                        if (thumbnailList != null && thumbnailList.size() > 0) {
                           List contents = this.convertMapToContentResources(thumbnailList);
                           vr.setContents(contents);
                        }
                     }
                  }

                  trs.add(vr);
               }
            } else {
               contentList = playlistInfo.getContentList(playlistId, playlist.getVersion_id());
               List items = V2PlaylistFactory.playlistItemResourceResponseListBuilder(playlistInfo, contentDao, playlist, contentList);
               res.setContents(items);
               if (playlist.getPlaylist_type().equalsIgnoreCase("3")) {
                  trs = new ArrayList();
                  List syncStatusInfo = playlistInfo.getSyncGroupInfo(playlist.getPlaylist_id(), playlist.getVersion_id());

                  for(int i = 0; i < syncStatusInfo.size(); ++i) {
                     trs.add(((SyncPlaylist)syncStatusInfo.get(i)).getIs_sync());
                  }

                  res.setSyncGroupStatus(trs);
               }

               res.setMetaData(playlist.getPlaylist_meta_data());
               res.setPlaylistName(playlist.getPlaylist_name());
               res.setDeviceType(playlist.getDevice_type());
               res.setDeviceTypeVersion(playlist.getDevice_type_version());
               res.setPlaylistType(playlist.getPlaylist_type());
               res.setSyncGroupCount((long)playlistInfo.getCountSyncGroup(playlistId, playlist.getVersion_id()));
               res.setAmsMode(playlist.getAms_mode());
               res.setAmsDirectPlay(playlist.getAms_direct_play());
               res.setShuffleFlag(playlist.getIs_shuffle());
               res.setShareFlag(playlist.getShare_flag());
               res.setGroupId(playlist.getGroup_id());
               res.setGroupName(playlist.getGroup_name());
               res.setHasSubPlaylist(playlist.getHas_sub_playlist());
               res.setVersionId(playlist.getVersion_id());
            }

            res.setPlaylistVersions(V2PlaylistFactory.getPlaylistVersionsResponse(playlistId, productType));
         }

         return res;
      }
   }

   private List convertMapToTagResources(List thumb) {
      List contents = new ArrayList();
      Iterator var3 = thumb.iterator();

      while(var3.hasNext()) {
         Map map = (Map)var3.next();
         V2TagContentResource content = new V2TagContentResource();
         content.setThumbFileName((String)map.get("file_name"));
         content.setThumbFileId((String)map.get("file_id"));
         content.setExpiredDate((String)map.get("expiration_date"));
         content.setContentId((String)map.get("content_id"));
         content.setContentName((String)map.get("content_name"));
         content.setCreatorId((String)map.get("creator_id"));
         content.setMediaType((String)map.get("media_type"));
         content.setPlayTime((String)map.get("play_time"));
         content.setLastModifiedDate((Timestamp)map.get("last_modified_date"));
         String contentTagValue = this.getContentTagVal((String)map.get("content_id"));
         content.setContentTagValue(contentTagValue);
         contents.add(content);
      }

      return contents;
   }

   private List convertMapToTagResourcesFromContent(List cont) {
      List contents = new ArrayList();
      Iterator var3 = cont.iterator();

      while(var3.hasNext()) {
         Content c = (Content)var3.next();
         V2TagContentResource content = new V2TagContentResource();
         content.setThumbFileName(c.getThumb_file_name());
         content.setThumbFileId(c.getThumb_file_id());
         contents.add(content);
      }

      return contents;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority', 'Content Schedule Read Authority', 'Content Schedule Write Authority', 'Content Schedule Add Authority', 'Content Schedule Manage Authority')")
   public V2PageResource getPlaylistByFilter(V2PlaylistFilter filter) throws Exception {
      String sort = filter.getSortColumn();
      String sortOrder = filter.getSortOrder();
      String cmd = StrUtils.nvl(filter.getSubMenu()).equals("") ? "INIT" : filter.getSubMenu();
      String groupType = StrUtils.nvl(filter.getGroupType()).equals("") ? "ALL" : filter.getGroupType();
      String groupId = StrUtils.nvl(filter.getGroupId()).equals("") ? "0" : filter.getGroupId();
      if (!groupId.equals("0")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.PLAYLIST, Long.parseLong(groupId));
      }

      String userId = StrUtils.nvl(filter.getUserId()).equals("") ? "" : filter.getUserId();
      List creatorIds = filter.getCreatorIds() != null && !filter.getCreatorIds().isEmpty() ? filter.getCreatorIds() : new ArrayList();
      String userOrganizationId = StrUtils.nvl(filter.getUserOrganizationId()).equals("") ? "" : filter.getUserOrganizationId();
      String searchText = StrUtils.nvl(filter.getSearchText()).equals("") ? "" : filter.getSearchText();
      String searchId = StrUtils.nvl(filter.getSearchId()).equals("") ? "-1" : filter.getSearchId();
      String deviceType = filter.getDeviceType();
      List playlist_type_list = filter.getPlaylistTypes();
      String playlist_type = null;
      if (playlist_type_list != null) {
         if (playlist_type_list.isEmpty()) {
            playlist_type = "NONE";
         } else if (playlist_type_list.contains("on")) {
            playlist_type = "0,1,2,3,4,5,6";
         } else {
            playlist_type = this.convertString(playlist_type_list);
         }
      }

      String productType = StrUtils.nvl(filter.getProductType()).equals("") ? "" : filter.getProductType();
      String startModifiedDate = StrUtils.nvl(filter.getStartModifiedDate()).equals("") ? "" : filter.getStartModifiedDate();
      String endModifiedDate = StrUtils.nvl(filter.getEndModifiedDate()).equals("") ? "" : filter.getEndModifiedDate();
      String searchCreator = StrUtils.nvl(filter.getSearchCreator()).equals("") ? "" : filter.getSearchCreator();
      String deviceTypeVersionParameter = filter.getDeviceTypeVersion();
      String adSchedule = filter.getAdSchedule();
      String isVwl = filter.getIsVwl();
      String isSync = filter.getIsSync();
      String isVwlMode = filter.getIsVwlMode();
      String category = "";
      List categoryList = filter.getCategoryIds();
      if (categoryList != null && categoryList.size() > 0) {
         category = this.convertString(categoryList);
      }

      Locale locale = SecurityUtils.getLocale();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      PlaylistInterface cmsDao = DAOFactory.getPlaylistInfoImpl(productType);
      int startIndex = true;
      int results = true;
      int startIndex = filter.getStartIndex();
      int results = filter.getPageSize();
      ContentInfo contentDao = ContentInfoImpl.getInstance();
      boolean canEditOthers = false;
      Boolean canReadUnshared = false;
      if (cmd.equalsIgnoreCase("byUser")) {
         groupType = "ALL";
      }

      canEditOthers = cmsDao.getCanEditOthers(userId, groupType, (HttpServletRequest)null);
      ListManager listMgr = new ListManager(cmsDao, "commonlist");
      if (deviceType != null) {
         listMgr.addSearchInfo("deviceType", deviceType);
         if (deviceTypeVersionParameter != null && !deviceTypeVersionParameter.equals("")) {
            float deviceTypeVersionFloat = Float.valueOf(deviceTypeVersionParameter);
            listMgr.addSearchInfo("deviceTypeVersion", deviceTypeVersionFloat);
         } else if (deviceType.equalsIgnoreCase("SPLAYER")) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_TOGETHER, new String[]{"SPLAYER", "DeviceTypeVersion"});
         }
      }

      if (isVwl != null && !isVwl.equals("")) {
         if (isVwl.equals("true")) {
            isVwl = "";
         } else {
            listMgr.addSearchInfo("isVwl", "N");
         }
      } else {
         isVwl = "";
      }

      if (isVwlMode != null && !isVwlMode.equals("") && isVwlMode.equals("true")) {
         listMgr.addSearchInfo("is_vwl_mode", "Y");
      }

      if (isSync != null) {
         if (isSync.equals("true")) {
            isSync = "";
         } else {
            listMgr.addSearchInfo("use_sync_play", "N");
         }
      } else {
         isSync = "";
      }

      if (adSchedule != null && !adSchedule.equals("")) {
         if (adSchedule.equals("true")) {
            playlist_type = "4";
         } else {
            playlist_type = "0,1,2,3,5,6";
         }
      }

      listMgr.addSearchInfo("sortColumn", sort);
      listMgr.addSearchInfo("sortOrder", sortOrder);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      listMgr.addSearchInfo("searchText", searchText);
      listMgr.addSearchInfo("isSelect", (Object)null);
      listMgr.addSearchInfo("selId", (Object)null);
      listMgr.addSearchInfo("searchCreator", searchCreator);
      if (category != null && !category.equals("")) {
         listMgr.addSearchInfo("category", category);
      }

      if (creatorIds != null && !((List)creatorIds).isEmpty()) {
         listMgr.addSearchInfo("userFilter", ((List)creatorIds).toArray(new String[((List)creatorIds).size()]));
      }

      if (playlist_type != null) {
         listMgr.addSearchInfo("playlist_type", playlist_type);
      }

      if (groupType.equalsIgnoreCase("USER") || groupType.equalsIgnoreCase("ORGAN")) {
         if (canEditOthers) {
            listMgr.addSearchInfo("viewRange", "all");
         } else {
            listMgr.addSearchInfo("viewRange", "shared");
         }
      }

      if (groupType.equalsIgnoreCase("USER")) {
         listMgr.addSearchInfo("creatorID", userId);
      } else if (groupType.equalsIgnoreCase("GROUPED")) {
         Group targetGroup = cmsDao.getGroupInfo(Long.valueOf(groupId));
         if (targetGroup != null && targetGroup.getCreator_id() != null) {
            userId = targetGroup.getCreator_id();
         }

         listMgr.addSearchInfo("creatorID", userId);
      } else {
         listMgr.addSearchInfo("creatorID", this.getLoginUserId());
      }

      AbilityInfo abilityInfo = AbilityInfoImpl.getInstance();
      List abilityList = abilityInfo.getAllAbilityListByUserId(userContainer.getUser().getUser_id());
      Iterator it = abilityList.iterator();

      while(it.hasNext()) {
         Map abilityMap = (Map)it.next();
         String abilityValue = (String)abilityMap.get("ability_name");
         if (productType.equalsIgnoreCase("PREMIUM")) {
            if (abilityValue.equalsIgnoreCase("Content Manage Authority")) {
               canReadUnshared = true;
            } else if (abilityValue.equalsIgnoreCase("Playlist Manage Authority")) {
               canReadUnshared = true;
            }
         } else if (abilityValue.equalsIgnoreCase("Lite Playlist Manage Authority")) {
            canReadUnshared = true;
         }
      }

      if (userId.equalsIgnoreCase(userContainer.getUser().getUser_id())) {
         canReadUnshared = true;
      }

      listMgr.addSearchInfo("canReadUnshared", canReadUnshared);
      listMgr.addSearchInfo("searchID", searchId);
      listMgr.setLstSize(Integer.valueOf(results));
      listMgr.addSearchInfo("startDate", startModifiedDate);
      listMgr.addSearchInfo("endDate", endModifiedDate);
      AbilityUtils ability = new AbilityUtils();
      boolean hasContentManage = ability.checkAuthority("Content Manage");
      listMgr.addSearchInfo("hasContentManage", hasContentManage);
      if (Long.parseLong(searchId) >= 0L) {
         listMgr.setSection("getSearchList");
      } else if (groupType.equalsIgnoreCase("GROUPED")) {
         listMgr.addSearchInfo("groupID", groupId);
         listMgr.addSearchInfo("listType", groupType);
         listMgr.setSection("getPlaylistList");
      } else {
         listMgr.addSearchInfo("listType", groupType);
         listMgr.setSection("getPlaylistList");
      }

      if (userOrganizationId != null && !userOrganizationId.isEmpty()) {
         listMgr.addSearchInfo("byUserOrganizationId", userOrganizationId);
      }

      PageManager pageMgr = null;
      List playlistList = listMgr.V2dbexecute(startIndex, results);
      pageMgr = listMgr.getPageManager();
      new PlaylistDao();
      new V2PageResource();

      try {
         new ArrayList();
         List resList = V2PlaylistFactory.playlistListResourceResponseBuilder(playlistList, productType);
         V2PageResource resource = V2PageResource.createPageResource(resList, pageMgr);
         resource.setStartIndex(startIndex);
         return resource;
      } catch (Exception var47) {
         this.logger.error("[MagicInfo_Playlist] fail playlist list e : " + var47.getMessage(), var47);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      }
   }

   public ModelAndView playlistExport(V2PlaylistFilter filter, String exportType, HttpServletResponse response, String localeData) throws Exception {
      this.rms.setBasename("resource/messages");
      String type = StrUtils.nvl(exportType).equals("") ? "EXCEL" : exportType;
      String fileExtension = "xls";
      if (type.equalsIgnoreCase("PDF")) {
         fileExtension = "pdf";
      }

      String fileName = "PlalylistList." + fileExtension;
      String sheetName = "Playlist";
      if (StrUtils.nvl(localeData).equals("")) {
         String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
         if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
            localeData = userLocale;
         } else {
            localeData = "en";
         }
      }

      Locale locale = new Locale(localeData);
      Map dataMap = new HashMap();
      String[] columnNames = null;
      String[] tmp;
      if (CommonConfig.get("yesco.ui.enable") != null && CommonConfig.get("yesco.ui.enable").equalsIgnoreCase("true")) {
         tmp = new String[]{"playlist_name", "content_count", "play_time", "total_size_str", "last_modified_date"};
         columnNames = tmp;
      } else {
         tmp = new String[]{"device_type", "playlist_name", "content_count", "play_time", "total_size_str", "last_modified_date"};
         columnNames = tmp;
      }

      String defaultMessage = this.rms.getMessage("TABLE_PLAYLIST_NAME_P", (Object[])null, new Locale("en"));
      String playlistNameTitle = this.rms.getMessage("TABLE_PLAYLIST_NAME_P", (Object[])null, defaultMessage, locale);
      defaultMessage = this.rms.getMessage("TABLE_CONTENT_COUNT_P", (Object[])null, new Locale("en"));
      String contentCountTitle = this.rms.getMessage("TABLE_CONTENT_COUNT_P", (Object[])null, defaultMessage, locale);
      defaultMessage = this.rms.getMessage("TEXT_LAST_MODIFIED_DATE_P", (Object[])null, new Locale("en"));
      String modifiedDateTitle = this.rms.getMessage("TEXT_LAST_MODIFIED_DATE_P", (Object[])null, defaultMessage, locale);
      defaultMessage = this.rms.getMessage("TEXT_PLAY_TIME_P", (Object[])null, new Locale("en"));
      String playTimeTitle = this.rms.getMessage("TEXT_PLAY_TIME_P", (Object[])null, defaultMessage, locale);
      defaultMessage = this.rms.getMessage("TEXT_TOTAL_SIZE_P", (Object[])null, new Locale("en"));
      String totalSize = this.rms.getMessage("TEXT_TOTAL_SIZE_P", (Object[])null, defaultMessage, locale);
      defaultMessage = this.rms.getMessage("COM_MAPP_SID_SUPPORTED_DEVICES", (Object[])null, new Locale("en"));
      String SupportedDeviceTypeTitle = this.rms.getMessage("COM_MAPP_SID_SUPPORTED_DEVICES", (Object[])null, defaultMessage, locale);
      String[] fieldNames = null;
      String[] tmp;
      if (CommonConfig.get("yesco.ui.enable") != null && CommonConfig.get("yesco.ui.enable").equalsIgnoreCase("true")) {
         tmp = new String[]{playlistNameTitle, contentCountTitle, playTimeTitle, totalSize, modifiedDateTitle};
         fieldNames = tmp;
      } else {
         tmp = new String[]{SupportedDeviceTypeTitle, playlistNameTitle, contentCountTitle, playTimeTitle, totalSize, modifiedDateTitle};
         fieldNames = tmp;
      }

      String sort = filter.getSortColumn();
      String sortOrder = filter.getSortOrder();
      String cmd = StrUtils.nvl(filter.getSubMenu()).equals("") ? "INIT" : filter.getSubMenu();
      String groupType = StrUtils.nvl(filter.getGroupType()).equals("") ? "ALL" : filter.getGroupType();
      String groupId = StrUtils.nvl(filter.getGroupId()).equals("") ? "0" : filter.getGroupId();
      String userId = StrUtils.nvl(filter.getUserId()).equals("") ? "" : filter.getUserId();
      List creatorIds = filter.getCreatorIds() != null && !filter.getCreatorIds().isEmpty() ? filter.getCreatorIds() : new ArrayList();
      String userOrganizationId = StrUtils.nvl(filter.getUserOrganizationId()).equals("") ? "" : filter.getUserOrganizationId();
      String searchText = StrUtils.nvl(filter.getSearchText()).equals("") ? "" : filter.getSearchText();
      String searchId = StrUtils.nvl(filter.getSearchId()).equals("") ? "-1" : filter.getSearchId();
      String deviceType = filter.getDeviceType();
      List playlist_type_list = filter.getPlaylistTypes();
      String playlist_type = null;
      if (playlist_type_list != null) {
         if (playlist_type_list.isEmpty()) {
            playlist_type = "NONE";
         } else if (playlist_type_list.contains("on")) {
            playlist_type = "0,1,2,3,4,5,6";
         } else {
            playlist_type = this.convertString(playlist_type_list);
         }
      }

      String productType = StrUtils.nvl(filter.getProductType()).equals("") ? "" : filter.getProductType();
      String startModifiedDate = StrUtils.nvl(filter.getStartModifiedDate()).equals("") ? "" : filter.getStartModifiedDate();
      String endModifiedDate = StrUtils.nvl(filter.getEndModifiedDate()).equals("") ? "" : filter.getEndModifiedDate();
      String searchCreator = StrUtils.nvl(filter.getSearchCreator()).equals("") ? "" : filter.getSearchCreator();
      String deviceTypeVersionParameter = filter.getDeviceTypeVersion();
      String adSchedule = filter.getAdSchedule();
      String isVwl = filter.getIsVwl();
      String isSync = StrUtils.nvl(filter.getIsSync()).equals("") ? null : filter.getIsSync();
      String isVwlMode = filter.getIsVwlMode();
      String category = "";
      List categoryList = filter.getCategoryIds();
      if (categoryList != null && categoryList.size() > 0) {
         category = this.convertString(categoryList);
      }

      UserContainer userContainer = SecurityUtils.getUserContainer();
      response.setContentType("application/json;charset=UTF-8");
      PlaylistInterface cmsDao = DAOFactory.getPlaylistInfoImpl(productType);
      int startIndex = true;
      int results = true;
      int startIndex = filter.getStartIndex();
      int results = filter.getPageSize();
      ContentInfo contentDao = ContentInfoImpl.getInstance();
      boolean canEditOthers = false;
      Boolean canReadUnshared = false;
      if (cmd.equalsIgnoreCase("byUser")) {
         groupType = "ALL";
      }

      canEditOthers = cmsDao.getCanEditOthers(userId, groupType, (HttpServletRequest)null);
      ListManager listMgr = new ListManager(cmsDao, "commonlist");
      listMgr.addSearchInfo("deviceType", deviceType);
      if (deviceTypeVersionParameter != null && !deviceTypeVersionParameter.equals("")) {
         float deviceTypeVersionFloat = Float.valueOf(deviceTypeVersionParameter);
         listMgr.addSearchInfo("deviceTypeVersion", deviceTypeVersionFloat);
      }

      if (isVwl != null && !isVwl.equals("")) {
         if (isVwl.equals("true")) {
            isVwl = "";
         } else {
            listMgr.addSearchInfo("isVwl", "N");
         }
      } else {
         isVwl = "";
      }

      if (isVwlMode != null && !isVwlMode.equals("") && isVwlMode.equals("true")) {
         listMgr.addSearchInfo("is_vwl_mode", "Y");
      }

      if (isSync != null) {
         if (isSync.equals("true")) {
            isSync = "";
         } else {
            listMgr.addSearchInfo("use_sync_play", "N");
         }
      } else {
         isSync = "";
      }

      if (adSchedule != null && !adSchedule.equals("")) {
         if (adSchedule.equals("true")) {
            playlist_type = "4";
         } else {
            playlist_type = "0,1,2,3,5,6";
         }
      }

      listMgr.addSearchInfo("sortColumn", sort);
      listMgr.addSearchInfo("sortOrder", sortOrder);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      listMgr.addSearchInfo("searchText", searchText);
      listMgr.addSearchInfo("isSelect", (Object)null);
      listMgr.addSearchInfo("selId", (Object)null);
      listMgr.addSearchInfo("searchCreator", searchCreator);
      if (category != null && !category.equals("")) {
         listMgr.addSearchInfo("category", category);
      }

      if (creatorIds != null && !((List)creatorIds).isEmpty()) {
         listMgr.addSearchInfo("userFilter", ((List)creatorIds).toArray(new String[((List)creatorIds).size()]));
      }

      if (playlist_type != null) {
         listMgr.addSearchInfo("playlist_type", playlist_type);
      }

      if (deviceType != null) {
         listMgr.addSearchInfo("device_type", deviceType);
      }

      if (groupType.equalsIgnoreCase("USER") || groupType.equalsIgnoreCase("ORGAN")) {
         if (canEditOthers) {
            listMgr.addSearchInfo("viewRange", "all");
         } else {
            listMgr.addSearchInfo("viewRange", "shared");
         }
      }

      if (groupType.equalsIgnoreCase("USER")) {
         listMgr.addSearchInfo("creatorID", userId);
      } else if (groupType.equalsIgnoreCase("GROUPED")) {
         Group targetGroup = cmsDao.getGroupInfo(Long.valueOf(groupId));
         if (targetGroup != null && targetGroup.getCreator_id() != null) {
            userId = targetGroup.getCreator_id();
         }

         listMgr.addSearchInfo("creatorID", userId);
      } else {
         listMgr.addSearchInfo("creatorID", this.getLoginUserId());
      }

      AbilityInfo abilityInfo = AbilityInfoImpl.getInstance();
      List abilityList = abilityInfo.getAllAbilityListByUserId(userContainer.getUser().getUser_id());
      Iterator it = abilityList.iterator();

      while(it.hasNext()) {
         Map abilityMap = (Map)it.next();
         String abilityValue = (String)abilityMap.get("ability_name");
         if (productType.equalsIgnoreCase("PREMIUM")) {
            if (abilityValue.equalsIgnoreCase("Content Manage Authority")) {
               canReadUnshared = true;
            } else if (abilityValue.equalsIgnoreCase("Playlist Manage Authority")) {
               canReadUnshared = true;
            }
         } else if (abilityValue.equalsIgnoreCase("Lite Playlist Manage Authority")) {
            canReadUnshared = true;
         }
      }

      if (userId.equalsIgnoreCase(userContainer.getUser().getUser_id())) {
         canReadUnshared = true;
      }

      listMgr.addSearchInfo("canReadUnshared", canReadUnshared);
      listMgr.addSearchInfo("searchID", searchId);
      listMgr.setLstSize(Integer.valueOf(results));
      listMgr.addSearchInfo("startDate", startModifiedDate);
      listMgr.addSearchInfo("endDate", endModifiedDate);
      if (Long.parseLong(searchId) >= 0L) {
         listMgr.setSection("getSearchList");
      } else if (groupType.equalsIgnoreCase("GROUPED")) {
         listMgr.addSearchInfo("groupID", groupId);
         listMgr.addSearchInfo("listType", groupType);
         listMgr.setSection("getPlaylistListByFilter");
      } else {
         listMgr.addSearchInfo("listType", groupType);
         listMgr.setSection("getPlaylistListByFilter");
      }

      if (userOrganizationId != null && !userOrganizationId.isEmpty()) {
         listMgr.addSearchInfo("byUserOrganizationId", userOrganizationId);
      }

      List playlistList = listMgr.V2dbexecute(startIndex, results);
      int dataListSize = false;
      Object[] dataList = null;
      if (playlistList != null) {
         int dataListSize = playlistList.size();
         dataList = new Object[dataListSize];

         for(int index = 0; index < dataListSize; ++index) {
            Playlist plList = (Playlist)playlistList.get(index);
            if (plList != null) {
               if (plList.getDevice_type().equals("SPLAYER") && plList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_2_0) {
                  plList.setDevice_type("S2PLAYER");
               } else if (plList.getDevice_type().equals("SPLAYER") && plList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_3_0) {
                  plList.setDevice_type("S3PLAYER");
               } else if (plList.getDevice_type().equals("SPLAYER") && plList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_4_0) {
                  plList.setDevice_type("S4PLAYER");
               } else if (plList.getDevice_type().equals("SPLAYER") && plList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_5_0) {
                  plList.setDevice_type("S5PLAYER");
               } else if (plList.getDevice_type().equals("SPLAYER") && plList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_6_0) {
                  plList.setDevice_type("S6PLAYER");
               } else if (plList.getDevice_type().equals("SPLAYER") && plList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_7_0) {
                  plList.setDevice_type("S7PLAYER");
               } else if (plList.getDevice_type().equals("SPLAYER") && plList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_9_0) {
                  plList.setDevice_type("S9PLAYER");
               } else if (plList.getDevice_type().equals("SPLAYER") && plList.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_10_0) {
                  plList.setDevice_type("S10PLAYER");
               }
            }

            dataList[index] = playlistList.get(index);
         }
      }

      dataMap.put("fileName", fileName);
      dataMap.put("sheetName", sheetName);
      dataMap.put("columnNames", columnNames);
      dataMap.put("fieldNames", fieldNames);
      dataMap.put("dataList", dataList);
      if (type.equalsIgnoreCase("PDF")) {
         PdfBuilder pdfView = new PdfBuilder();
         return new ModelAndView(pdfView, dataMap);
      } else {
         this.downloadService = new DeviceStatisticsDownloadService();
         this.downloadService.downloadExcelFile(dataMap, response);
         return null;
      }
   }

   private static enum contentBuilderType {
      CREATE,
      UPDATE;

      private contentBuilderType() {
      }
   }

   private class PlaylistContentInformation implements Serializable {
      private Long totalPlayTime = 0L;
      private Map totalSizeMapByContentId = new HashMap();
      private List contents = new ArrayList();
      private ArrayList tagArrayList = new ArrayList();
      private boolean addSubPlaylist = false;

      public PlaylistContentInformation() {
         super();
      }

      public Long getTotalPlayTime() {
         return this.totalPlayTime;
      }

      public Map getTotalSizeMapByContentId() {
         return this.totalSizeMapByContentId;
      }

      public List getContents() {
         return this.contents;
      }

      public ArrayList getTagArrayList() {
         return this.tagArrayList;
      }

      public boolean getAddSubPlaylist() {
         return this.addSubPlaylist;
      }

      public V2PlaylistServiceImpl.PlaylistContentInformation totalPlayTimeBuilder(Long totalPlayTime) {
         this.totalPlayTime = totalPlayTime;
         return this;
      }

      public V2PlaylistServiceImpl.PlaylistContentInformation totalSizeMapByContentIdBuilder(Map totalSizeMapByContentId) {
         this.totalSizeMapByContentId = totalSizeMapByContentId;
         return this;
      }

      public V2PlaylistServiceImpl.PlaylistContentInformation contentsBuilder(List contents) {
         this.contents = contents;
         return this;
      }

      public V2PlaylistServiceImpl.PlaylistContentInformation tagArrayListBuilder(ArrayList tagArrayList) {
         this.tagArrayList = tagArrayList;
         return this;
      }

      public V2PlaylistServiceImpl.PlaylistContentInformation addSubPlaylistBuilder(boolean addSubPlaylist) {
         this.addSubPlaylist = addSubPlaylist;
         return this;
      }
   }
}
