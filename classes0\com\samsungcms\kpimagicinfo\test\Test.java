package com.samsungcms.kpimagicinfo.test;

import com.samsungcms.kpimagicinfo.model.KpiPolicy;
import com.samsungcms.kpimagicinfo.service.Scheduler;
import com.samsungcms.kpimagicinfo.service.SchedulerService;
import com.samsungcms.kpimagicinfo.util.Utils;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.FileAttribute;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

@Component
public class Test {
  private static final Logger LOGGER = LogManager.getLogger(com.samsungcms.kpimagicinfo.test.Test.class);
  
  @Autowired
  Utils utils;
  
  @Autowired
  KpiPolicy kpiPolicy;
  
  @Autowired
  private SchedulerService schedulerService;
  
  @Autowired
  private Scheduler scheduler;
  
  public Resource getTestFile(int day) {
    String strDate = this.utils.getDateString(day);
    Path testFile = null;
    Path filePath = this.utils.getUserKPILogFolderPath();
    Path logFile = filePath.resolve("userkpi.log." + strDate + ".log.zip");
    if (logFile.toFile().exists())
      return null; 
    try {
      testFile = Files.createFile(logFile, (FileAttribute<?>[])new FileAttribute[0]);
      LOGGER.info("Creating and Uploading Test File: " + testFile);
      Files.write(testFile, "Hello World !!, This is a test file.".getBytes(), new java.nio.file.OpenOption[0]);
    } catch (Exception e) {
      e.printStackTrace();
    } 
    if (testFile == null)
      return null; 
    return (Resource)new FileSystemResource(testFile.toFile());
  }
  
  public void setCron(String cron) {
    this.scheduler.getKpiPolicy();
    LOGGER.info("scheduler cron = " + cron);
    this.schedulerService.changeCron(cron);
  }
}
