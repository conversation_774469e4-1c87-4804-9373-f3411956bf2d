package com.samsung.magicinfo.auth.security.strategies;

import com.samsung.magicinfo.auth.security.strategies.model.AuthType;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.stereotype.Component;

@Documented
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Component
public @interface Strategy {
   Class type();

   AuthType[] profiles() default {};
}
