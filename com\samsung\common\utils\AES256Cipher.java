package com.samsung.common.utils;

import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import org.apache.commons.codec.binary.Base64;

public class AES256Cipher {
   private String iv;
   private Key keySpec;

   public AES256Cipher(String key) throws UnsupportedEncodingException {
      super();
      this.init(key, (String)null);
   }

   public AES256Cipher(String key, String iv) throws UnsupportedEncodingException {
      super();
      this.init(key, iv);
   }

   private void init(String key, String iv) throws UnsupportedEncodingException {
      if (key.length() != 16 && key.length() != 32) {
         throw new IllegalArgumentException("'key' must be 128/256 bit.");
      } else if (iv != null && iv.length() != 16) {
         throw new IllegalArgumentException("'iv' must be 128 bit");
      } else {
         this.iv = iv;
         byte[] keyBytes = new byte[16];
         byte[] b = key.getBytes("UTF-8");
         int len = b.length;
         if (len > keyBytes.length) {
            len = keyBytes.length;
         }

         System.arraycopy(b, 0, keyBytes, 0, len);
         SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
         this.keySpec = keySpec;
      }
   }

   public String aesEncode(String str) throws UnsupportedEncodingException, NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException {
      Cipher c;
      if (this.iv == null) {
         c = Cipher.getInstance("AES/ECB/PKCS5Padding");
         c.init(1, this.keySpec);
      } else {
         c = Cipher.getInstance("AES/CBC/PKCS5Padding");
         c.init(1, this.keySpec, new IvParameterSpec(this.iv.getBytes("UTF-8")));
      }

      byte[] encrypted = c.doFinal(str.getBytes("UTF-8"));
      return new String(Base64.encodeBase64(encrypted));
   }

   public String aesDecode(String str) throws UnsupportedEncodingException, NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException {
      Cipher c;
      if (this.iv == null) {
         c = Cipher.getInstance("AES/ECB/PKCS5Padding");
         c.init(2, this.keySpec);
      } else {
         c = Cipher.getInstance("AES/CBC/PKCS5Padding");
         c.init(2, this.keySpec, new IvParameterSpec(this.iv.getBytes("UTF-8")));
      }

      byte[] byteStr = Base64.decodeBase64(str.getBytes());
      return new String(c.doFinal(byteStr), "UTF-8");
   }
}
