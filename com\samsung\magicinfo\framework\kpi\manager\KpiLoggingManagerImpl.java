package com.samsung.magicinfo.framework.kpi.manager;

import com.google.gson.Gson;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.auth.model.json.response.AuthenticationResponse;
import com.samsung.magicinfo.auth.security.TokenUtils;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.kpi.annotation.KPI;
import com.samsung.magicinfo.framework.kpi.annotation.LogProperty;
import com.samsung.magicinfo.framework.kpi.constants.KpiLoggingConstants;
import com.samsung.magicinfo.framework.kpi.entity.CategoryKPI;
import com.samsung.magicinfo.framework.kpi.entity.LoggingKPI;
import com.samsung.magicinfo.framework.ruleset.entity.RuleSet;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfo;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfoImpl;
import com.samsung.magicinfo.framework.scheduler.entity.CommonMessageEntity;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.common.MessageInterface;
import com.samsung.magicinfo.framework.scheduler.manager.common.ScheduleInterface;
import com.samsung.magicinfo.framework.setup.entity.SystemLogEntity;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.restapi.auth.model.V2AuthenticationResource;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import io.jsonwebtoken.Claims;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.servlet.http.HttpServletRequest;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

public class KpiLoggingManagerImpl implements KpiLoggingManager {
   protected static Logger logger = LoggingManagerV2.getLogger(KpiLoggingManagerImpl.class);
   protected static Logger kpiLogger = LoggingManagerV2.getLogger("KPI_LOGGER");
   private static volatile KpiLoggingManagerImpl instance = null;

   public KpiLoggingManagerImpl() {
      super();
   }

   public static KpiLoggingManagerImpl getInstance() {
      if (instance == null) {
         Class var0 = KpiLoggingManagerImpl.class;
         synchronized(KpiLoggingManagerImpl.class) {
            if (instance == null) {
               instance = new KpiLoggingManagerImpl();
            }
         }
      }

      return instance;
   }

   public String removeLastChar(String str, char x) {
      if (str.length() > 0 && str.charAt(str.length() - 1) == x) {
         str = str.substring(0, str.length() - 1);
      }

      return str;
   }

   public String getRuleSetName(String id) {
      RuleSetInfo dao = RuleSetInfoImpl.getInstance();
      String resultName = null;

      try {
         RuleSet ruleset = dao.getRuleset(id);
         resultName = ruleset.getName();
      } catch (Exception var5) {
         logger.error(var5);
      }

      return resultName;
   }

   public String getContentName(String id) {
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      String resultName = null;

      try {
         resultName = cmsDao.getContentName(id);
      } catch (SQLException var5) {
         logger.error(var5);
      }

      return resultName;
   }

   public String getPlaylistName(String id) {
      PlaylistInfo dao = PlaylistInfoImpl.getInstance();
      String resultName = null;

      try {
         resultName = dao.getPlaylistName(id);
      } catch (SQLException var5) {
         logger.error(var5);
      }

      return resultName;
   }

   public String getScheduleName(String menu, String id) {
      String resultName = null;

      try {
         if ("content-schedules".equalsIgnoreCase(menu)) {
            ScheduleInterface dao = DAOFactory.getScheduleInfoImpl("PREMIUM");
            resultName = dao.getProgramName(id);
         } else if ("message-schedules".equalsIgnoreCase(menu)) {
            MessageInterface dao = DAOFactory.getMessageInfoImpl("PREMIUM");
            CommonMessageEntity commonMessageEntity = dao.getMessage(id);
            resultName = commonMessageEntity.getMessage_name();
         } else if ("event-schedules".equalsIgnoreCase(menu)) {
            EventScheduleInfo dao = EventScheduleInfoImpl.getInstance();
            resultName = dao.getEventScheduleName(id);
         }
      } catch (Exception var6) {
         logger.error(var6.getMessage());
      }

      return resultName;
   }

   public String getDeviceName(String id) {
      DeviceInfo dao = DeviceInfoImpl.getInstance();
      String resultName = null;

      try {
         resultName = dao.getDeviceNameById(id);
      } catch (SQLException var5) {
         logger.error(var5);
      }

      return resultName;
   }

   public String getNameByMenu(Map menuMap, String id) {
      String menuFilter = (String)menuMap.get("MENU_FILTER");
      String menu = (String)menuMap.get("MENU");
      String name = "";
      switch(KpiLoggingConstants.LOG_MENU_FILTER.indexOf(menuFilter)) {
      case 0:
         name = this.getContentName(id);
         break;
      case 1:
         name = this.getPlaylistName(id);
         break;
      case 2:
         name = this.getScheduleName(menu, id);
         break;
      case 3:
         name = this.getDeviceName(id);
         break;
      case 4:
      case 5:
         name = id;
         break;
      case 6:
         name = this.getRuleSetName(id);
      }

      return name;
   }

   public List searchName(Field f, Map menuMap, Object obj) {
      ArrayList nameList = new ArrayList();

      try {
         Annotation[] annotationsFiled = f.getDeclaredAnnotations();
         Annotation[] var6 = annotationsFiled;
         int var7 = annotationsFiled.length;

         for(int var8 = 0; var8 < var7; ++var8) {
            Annotation aFiled = var6[var8];
            if (aFiled instanceof LogProperty) {
               LogProperty myAnnotation = (LogProperty)aFiled;
               f.setAccessible(true);
               if (f.get(obj) != null) {
                  if ("NAME".equals(myAnnotation.valueType())) {
                     nameList.add(f.get(obj).toString());
                  } else if ("ID".equals(myAnnotation.valueType())) {
                     if (f.get(obj) instanceof String) {
                        String name = this.getNameByMenu(menuMap, f.get(obj).toString());
                        if (name != null) {
                           nameList.add(name);
                        }
                     }
                  } else if ("ID_LIST".equals(myAnnotation.valueType()) && f.get(obj) instanceof List) {
                     ArrayList idList = (ArrayList)f.get(obj);
                     Iterator var12 = idList.iterator();

                     while(var12.hasNext()) {
                        Object id = var12.next();
                        if (id instanceof String) {
                           String name = this.getNameByMenu(menuMap, (String)id);
                           if (name != null) {
                              nameList.add(name);
                           }
                        }
                     }
                  }
               }
            }
         }
      } catch (Exception var15) {
         logger.info(var15);
      }

      return nameList;
   }

   public boolean searchKpiFieldAnnotation(Field f, Object obj) {
      try {
         Annotation[] annotationsFiled = f.getDeclaredAnnotations();
         Annotation[] var4 = annotationsFiled;
         int var5 = annotationsFiled.length;

         for(int var6 = 0; var6 < var5; ++var6) {
            Annotation aFiled = var4[var6];
            if (aFiled instanceof KPI) {
               f.setAccessible(true);
               if (f.get(obj) != null && !f.get(obj).toString().isEmpty()) {
                  return true;
               }
            }
         }
      } catch (Exception var8) {
         logger.info(var8);
      }

      return false;
   }

   public Map searchKpiField(Object obj) {
      HashMap result = new HashMap();

      try {
         Field[] field = obj.getClass().getDeclaredFields();
         Field[] var4 = field;
         int var5 = field.length;

         for(int var6 = 0; var6 < var5; ++var6) {
            Field f = var4[var6];
            if (this.isMagicInfoPackage(f.getType().getName())) {
               f.setAccessible(true);
               if (f.get(obj) == null || f.get(obj).getClass() == null) {
                  continue;
               }

               Field[] pField = f.get(obj).getClass().getDeclaredFields();
               Field[] var9 = pField;
               int var10 = pField.length;

               for(int var11 = 0; var11 < var10; ++var11) {
                  Field pF = var9[var11];
                  if (this.searchKpiFieldAnnotation(pF, f.get(obj))) {
                     result.put(pF.getName(), pF.get(obj));
                  }
               }
            } else if (f.getGenericType().getTypeName().indexOf("com.samsung.magicinfo.restapi") > -1) {
               f.setAccessible(true);
               if (f.get(obj) == null || f.get(obj).getClass() == null) {
                  continue;
               }

               if (f.get(obj) instanceof List) {
                  Map listMap = this.makeKpiValueListMap(obj, f);
                  result.putAll(listMap);
               }
            }

            if (this.searchKpiFieldAnnotation(f, obj)) {
               result.put(f.getName(), f.get(obj));
            }
         }
      } catch (Exception var13) {
         logger.error(var13);
      }

      return result;
   }

   private Map makeKpiValueListMap(Object obj, Field f) throws IllegalAccessException {
      Map tempMap = null;
      String nowValue = null;
      List nowList = null;
      Map listMap = new HashMap();
      Iterator var7 = ((List)f.get(obj)).iterator();

      while(true) {
         do {
            if (!var7.hasNext()) {
               return listMap;
            }

            Object item = var7.next();
            tempMap = this.searchKpiField(item);
         } while(tempMap.isEmpty());

         Set set = tempMap.keySet();
         Iterator var10 = set.iterator();

         while(var10.hasNext()) {
            String key = (String)var10.next();
            if (!listMap.containsKey(key)) {
               listMap.put(key, new ArrayList());
            }

            nowValue = (String)tempMap.get(key);
            nowList = (List)listMap.get(key);
            nowList.add(nowValue);
            listMap.put(key, nowList);
         }
      }
   }

   public List getNameList(Map menuMap, Object obj) {
      List nameList = new ArrayList();
      if (obj == null) {
         return nameList;
      } else {
         try {
            Field[] field = obj.getClass().getDeclaredFields();
            Field[] var5 = field;
            int var6 = field.length;

            for(int var7 = 0; var7 < var6; ++var7) {
               Field f = var5[var7];
               if (f.getType().getName().startsWith("com.samsung.magicinfo.restapi")) {
                  f.setAccessible(true);
                  if (f.get(obj) == null || f.get(obj).getClass() == null) {
                     continue;
                  }

                  Field[] pField = f.get(obj).getClass().getDeclaredFields();
                  Field[] var10 = pField;
                  int var11 = pField.length;

                  for(int var12 = 0; var12 < var11; ++var12) {
                     Field pF = var10[var12];
                     nameList.addAll(this.searchName(pF, menuMap, f.get(obj)));
                  }
               }

               nameList.addAll(this.searchName(f, menuMap, obj));
            }
         } catch (Exception var14) {
            logger.error(var14);
         }

         return nameList;
      }
   }

   public String getEventType(ProceedingJoinPoint joinPoint) {
      String eventType = null;
      MethodSignature signature = (MethodSignature)joinPoint.getSignature();
      Method method = signature.getMethod();
      Annotation[] methodAnnotations = method.getAnnotations();
      Annotation[] var6 = methodAnnotations;
      int var7 = methodAnnotations.length;

      for(int var8 = 0; var8 < var7; ++var8) {
         Annotation a = var6[var8];
         if (a instanceof LogProperty) {
            LogProperty aLogProperty = (LogProperty)a;
            eventType = aLogProperty.eventType();
            break;
         }
      }

      return eventType;
   }

   public boolean isMagicInfoPackage(Object obj) {
      Iterator var2 = KpiLoggingConstants.LIST_NAME_PACKAGE_MAGICINFO_RESTAPI_V1_V2.iterator();

      String pkg;
      do {
         if (!var2.hasNext()) {
            return false;
         }

         pkg = (String)var2.next();
      } while(!obj.getClass().getName().startsWith(pkg));

      return true;
   }

   private boolean isMagicInfoPackageV2(Object obj) {
      boolean result = false;
      if (obj.getClass().getName().startsWith("com.samsung.magicinfo.restapi") || obj.getClass().getName().startsWith("com.samsung.magicinfo.auth")) {
         result = true;
      }

      return result;
   }

   public Object getResponseBody(Object result) {
      Object objItems = null;
      if (result == null) {
         return null;
      } else if (!(result instanceof ResponseEntity)) {
         return null;
      } else if (((ResponseEntity)result).getBody() == null) {
         return null;
      } else if (AuthenticationResponse.class == ((ResponseEntity)result).getBody().getClass()) {
         return null;
      } else {
         if (((ResponseEntity)result).getBody().getClass() == ResponseBody.class) {
            objItems = ((ResponseBody)((ResponseEntity)result).getBody()).getItems();
         }

         return objItems;
      }
   }

   public String getUrl(ProceedingJoinPoint joinPoint, String url) {
      MethodSignature signature = (MethodSignature)joinPoint.getSignature();
      Method method = signature.getMethod();
      Annotation[][] annotations = method.getParameterAnnotations();
      String[] names = signature.getParameterNames();
      Object[] values = joinPoint.getArgs();
      Annotation[][] var8 = annotations;
      int var9 = annotations.length;

      for(int var10 = 0; var10 < var9; ++var10) {
         Annotation[] annotationList = var8[var10];
         Annotation[] var12 = annotationList;
         int var13 = annotationList.length;

         for(int var14 = 0; var14 < var13; ++var14) {
            Annotation annotation = var12[var14];
            if (annotation instanceof PathVariable) {
               PathVariable myAnnotation = (PathVariable)annotation;

               for(int i = 0; i < names.length; ++i) {
                  if (names[i] != null && !myAnnotation.value().isEmpty() && names[i].toString().equals(myAnnotation.value())) {
                     url = url.replace("/" + values[i].toString(), "/{" + names[i] + "}");
                  }
               }
            }
         }
      }

      return url;
   }

   public void searchKpi(ProceedingJoinPoint joinPoint, Object result) throws Exception {
      HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
      String url = request.getRequestURI();
      String action = request.getMethod();
      Map requestData = new HashMap();
      Map responseData = new HashMap();
      Object[] args = joinPoint.getArgs();
      Object objItems = null;

      try {
         if (args != null && args.length > 0) {
            for(int i = 0; i < args.length; ++i) {
               if (args[i] != null) {
                  if (this.isMagicInfoPackage(args[i])) {
                     requestData = this.searchKpiField(args[i]);
                  } else if (args[i] instanceof String) {
                     MethodSignature methodSignature = (MethodSignature)joinPoint.getStaticPart().getSignature();
                     Method method = methodSignature.getMethod();
                     Annotation[][] parameterAnnotations = method.getParameterAnnotations();
                     Annotation[] var14 = parameterAnnotations[i];
                     int var15 = var14.length;

                     for(int var16 = 0; var16 < var15; ++var16) {
                        Annotation annotation = var14[var16];
                        if (annotation instanceof KPI) {
                           ((Map)requestData).put(((KPI)annotation).value(), args[i]);
                        }
                     }
                  }
               }
            }
         }
      } catch (Exception var19) {
         logger.error(var19);
      }

      try {
         objItems = this.getResponseBody(result);
         if (objItems != null) {
            responseData = this.searchKpiField(objItems);
         }
      } catch (Exception var18) {
         logger.error(var18);
      }

      Map resultMap = new HashMap();
      if (requestData != null && ((Map)requestData).size() > 0) {
         resultMap.putAll((Map)requestData);
      }

      if (responseData != null && ((Map)responseData).size() > 0) {
         resultMap.putAll((Map)responseData);
      }

      url = this.getUrl(joinPoint, url);
      this.writeKpiLog(request, url, action, resultMap);
   }

   private void writeKpiLog(HttpServletRequest request, String url, String action, Map value) {
      try {
         LoggingKPI loggingKPI = new LoggingKPI();
         CategoryKPI categoryKPI = new CategoryKPI();
         categoryKPI.setType("API_RAW");
         categoryKPI.setAction(action);
         categoryKPI.setLabel(url);
         categoryKPI.setValue(value);
         String uid = this.getSessionUID(loggingKPI, request);
         categoryKPI.setUid(uid);
         loggingKPI.getCategory().add(categoryKPI);
         Gson gson = new Gson();
         kpiLogger.info(gson.toJson(loggingKPI));
      } catch (Exception var9) {
         logger.error(var9);
      }

   }

   public String getSessionUID(LoggingKPI loggingKPI, HttpServletRequest request) {
      String uid = loggingKPI.getUid();

      try {
         String token = request.getHeader(TokenUtils.tokenHeader);
         if (token != null && !token.isEmpty()) {
            TokenUtils tokenUtils = new TokenUtils();
            uid = (String)tokenUtils.getClaimFromToken(token, Claims::getId);
         }
      } catch (RestServiceException var6) {
         throw var6;
      } catch (Exception var7) {
         logger.error(var7);
      }

      return uid;
   }

   private String getClientIP() {
      HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
      String ip = request.getHeader("X-FORWARDED-FOR");
      if (ip == null || !StrUtils.isValidIpAddress(ip)) {
         ip = request.getRemoteAddr();
      }

      return ip;
   }

   public Map getMenu(HttpServletRequest request) {
      Map menuMap = new HashMap();
      String url = request.getRequestURI();
      String[] urlSplit = url.split("/");
      String menu = "";
      String menuFilter = "";
      if (urlSplit.length > 5) {
         menu = urlSplit[5].toUpperCase();
      } else if (urlSplit.length > 4) {
         menu = urlSplit[4].toUpperCase();
      } else if (urlSplit.length > 2) {
         menu = urlSplit[2].toUpperCase();
      }

      menuFilter = this.removeLastChar(menu, 'S');
      String[] menuStruct = menuFilter.split("-");
      if (menuStruct.length > 1) {
         menuFilter = menuStruct[menuStruct.length - 1];
      }

      menuMap.put("MENU", menu);
      menuMap.put("MENU_FILTER", menuFilter);
      return menuMap;
   }

   public void addSystemLog(String eventType, Map menuMap, Object[] args, Object objItems) {
      if (eventType != null) {
         String menuFilter = (String)menuMap.get("MENU_FILTER");
         if ("AUTH".equals(menuFilter)) {
            menuFilter = (String)KpiLoggingConstants.LOG_MENU_FILTER.get(5);
         }

         if (KpiLoggingConstants.LOG_MENU_FILTER.contains(menuFilter)) {
            List nameList = this.makeRequestNameList(menuMap, args);
            List responseNameList = this.getNameList(menuMap, objItems);
            if (!responseNameList.isEmpty()) {
               nameList = responseNameList;
            }

            this.makeSystemLog(eventType, menuFilter, nameList);
         }
      }
   }

   private List makeRequestNameList(Map menuMap, Object[] args) {
      List nameList = new ArrayList();
      if (args == null) {
         return nameList;
      } else if (args.length == 0) {
         return nameList;
      } else {
         for(int i = 0; i < args.length; ++i) {
            if (args[i] != null && this.isMagicInfoPackageV2(args[i])) {
               nameList.addAll(this.getNameList(menuMap, args[i]));
            }
         }

         return nameList;
      }
   }

   public void makeSystemLog(ProceedingJoinPoint joinPoint) {
      String username = null;
      if (joinPoint.getArgs().length >= 1) {
         if (joinPoint.getArgs()[0] instanceof V2AuthenticationResource) {
            V2AuthenticationResource v2AuthenticationResource = (V2AuthenticationResource)joinPoint.getArgs()[0];
            username = v2AuthenticationResource.getUsername();
         }

         List nameList = new ArrayList();
         nameList.add(username);
         this.makeSystemLog("Fail", (String)KpiLoggingConstants.LOG_MENU_FILTER.get(5), nameList);
      }
   }

   public void makeSystemLog(String eventType, String menuFilter, List nameList) {
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      UserContainer container = SecurityUtils.getUserContainer();
      if (!nameList.isEmpty()) {
         Iterator var6 = nameList.iterator();

         while(true) {
            String name;
            SystemLogEntity systemLogEntity;
            while(true) {
               do {
                  do {
                     if (!var6.hasNext()) {
                        return;
                     }

                     name = (String)var6.next();
                  } while(null == name);
               } while(name.isEmpty());

               systemLogEntity = new SystemLogEntity();
               if (container != null) {
                  User user = container.getUser();
                  systemLogEntity.setUser_id(user.getUser_id());
                  systemLogEntity.setOrganization_id(user.getRoot_group_id());
                  systemLogEntity.setEvent_type(eventType);
                  break;
               }

               if (((String)KpiLoggingConstants.LOG_MENU_FILTER.get(5)).equals(menuFilter)) {
                  UserInfo userInfo = UserInfoImpl.getInstance();
                  User user = null;

                  try {
                     user = userInfo.getUserInfo(name);
                  } catch (SQLException var13) {
                     logger.error(var13);
                  }

                  if (user != null) {
                     systemLogEntity.setEvent_type("Fail");
                     systemLogEntity.setUser_id(name);
                     break;
                  }
               }
            }

            systemLogEntity.setIp_address(this.getClientIP());
            systemLogEntity.setMenu(menuFilter);
            systemLogEntity.setName(name);

            try {
               serverSetupDao.addLogInfo(systemLogEntity);
            } catch (SQLException var12) {
               logger.error(var12);
            }
         }
      }
   }

   public void addServerIdToConfig() {
      try {
         String serverIdFromConfig = CommonConfig.get("privacy_policy.serverid");
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         Map kpiMap = serverSetupDao.getServerSetupKpi();
         String serverIdFromDB = null;
         if (null != kpiMap && null != kpiMap.get("SERVER_ID")) {
            serverIdFromDB = kpiMap.get("SERVER_ID").toString();
         }

         if (StrUtils.isEmpty(serverIdFromConfig) && StrUtils.isEmpty(serverIdFromDB)) {
            SlmLicenseManager licenseMag = SlmLicenseManagerImpl.getInstance();
            String hwkey = licenseMag.getHWUniqueKey();
            Map attribMap = new HashMap();
            attribMap.put("privacy_policy.serverid", hwkey);
            List attribKey = new ArrayList(attribMap.keySet());
            CommonConfig.saveOnlyModifiedConfigFile(attribMap, attribKey);
            Map serverIdMap = new HashMap();
            serverIdMap.put("SERVER_ID", hwkey);
            serverSetupDao.addServerSetupKpi(serverIdMap);
         } else {
            HashMap serverIdMap;
            ArrayList attribKey;
            if (StrUtils.isEmpty(serverIdFromConfig) && !StrUtils.isEmpty(serverIdFromDB)) {
               serverIdMap = new HashMap();
               serverIdMap.put("privacy_policy.serverid", serverIdFromDB);
               if (null != kpiMap.get("KPI_ENABLE")) {
                  serverIdMap.put("privacy_policy.enable", kpiMap.get("KPI_ENABLE").toString());
               }

               if (null != kpiMap.get("LOCATION")) {
                  serverIdMap.put("privacy_policy.location", kpiMap.get("LOCATION").toString());
               }

               attribKey = new ArrayList(serverIdMap.keySet());
               CommonConfig.saveOnlyModifiedConfigFile(serverIdMap, attribKey);
            } else if (!StrUtils.isEmpty(serverIdFromConfig) && StrUtils.isEmpty(serverIdFromDB)) {
               serverIdMap = new HashMap();
               serverIdMap.put("SERVER_ID", serverIdFromConfig);
               if (CommonConfig.get("privacy_policy.enable") != null) {
                  serverIdMap.put("KPI_ENABLE", Boolean.valueOf(CommonConfig.get("privacy_policy.enable")));
               }

               if (CommonConfig.get("privacy_policy.location") != null) {
                  serverIdMap.put("LOCATION", CommonConfig.get("privacy_policy.location"));
               }

               serverSetupDao.addServerSetupKpi(serverIdMap);
            } else if (!serverIdFromConfig.equalsIgnoreCase(serverIdFromDB)) {
               serverIdMap = new HashMap();
               serverIdMap.put("privacy_policy.serverid", serverIdFromDB);
               attribKey = new ArrayList(serverIdMap.keySet());
               CommonConfig.saveOnlyModifiedConfigFile(serverIdMap, attribKey);
            }
         }
      } catch (ConfigException var10) {
         logger.error(var10);
      } catch (Exception var11) {
         logger.error(var11);
      }

   }

   public String getServerIdFromConfig() {
      String serverId = null;

      try {
         serverId = CommonConfig.get("privacy_policy.serverid");
         if (StrUtils.isNotEmpty(serverId)) {
            return serverId;
         }

         this.addServerIdToConfig();
         serverId = CommonConfig.get("privacy_policy.serverid");
      } catch (ConfigException var3) {
         logger.error(var3);
      }

      return serverId;
   }
}
