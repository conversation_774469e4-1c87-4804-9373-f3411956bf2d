package com.samsung.magicinfo.webauthor2.service;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.repository.CidMappingRepository;
import com.samsung.magicinfo.webauthor2.service.CidMappingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CidMappingServiceImpl implements CidMappingService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.CidMappingServiceImpl.class);
  
  private CidMappingRepository cidMappingRepository;
  
  private static final int CONTENT_ID_LENGTH = 36;
  
  @Autowired
  public CidMappingServiceImpl(CidMappingRepository cidMappingRepository) {
    this.cidMappingRepository = cidMappingRepository;
  }
  
  public String cidMapping(String contentId) {
    if (Strings.isNullOrEmpty(contentId) || contentId.length() != 36)
      contentId = "00000000-0000-0000-0000-000000000000"; 
    return this.cidMappingRepository.cidMapping(contentId);
  }
  
  public String cidMapping() {
    return this.cidMappingRepository.cidMapping(null);
  }
}
