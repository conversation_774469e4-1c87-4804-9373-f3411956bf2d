package com.samsung.magicinfo.restapi.playlist.model;

import com.samsung.magicinfo.framework.kpi.annotation.LogProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;

@ApiModel(
   description = "(lastModifiedDate) is based on server local time."
)
public class V2PlaylistListResourceResponse implements Serializable {
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000",
      dataType = "string",
      value = "Id of specific playlist , gets the Id of the playlist."
   )
   private String playlistId;
   @ApiModelProperty(
      dataType = "string",
      example = "SPLAYER",
      value = "DeviceType, minimum supported deviceType"
   )
   private String deviceType;
   @ApiModelProperty(
      example = "2016-01-01 00:00:00",
      dataType = "timestamp",
      value = "Last modification date of the playlist"
   )
   private Timestamp lastModifiedDate;
   @ApiModelProperty(
      example = "1",
      dataType = "int",
      required = true,
      value = "If the group is not set in the playlist shareFlag sharing setting, it is taken to zero, if set to one."
   )
   @Min(0L)
   @Max(1L)
   private int shareFlag;
   @ApiModelProperty(
      example = "admin",
      required = true,
      value = "Creator of specific playlist, shows the author of a particular playlist.",
      dataType = "string"
   )
   @Size(
      max = 64,
      message = "[V2PlaylistResource][creatorId] max size is 64."
   )
   private String creatorId;
   @ApiModelProperty(
      example = "1",
      required = true,
      value = "The playlist evennessPlayback cannot be found because it does not have data. Only 0 and 1 are supposed to come.",
      dataType = "int"
   )
   private int evennessPlayback;
   @LogProperty(
      valueType = "NAME"
   )
   @ApiModelProperty(
      dataType = "string",
      value = "Name of specific playlist , shows the name of the playlist.",
      required = true
   )
   @Size(
      max = 60,
      message = "max size is 60."
   )
   private String playlistName;
   @ApiModelProperty(
      example = "-",
      dataType = "string",
      value = "Playlist description"
   )
   @Size(
      max = 200,
      message = "[V2PlaylistResource][metaData] max size is 200."
   )
   private String playlistMetaData;
   @ApiModelProperty(
      dataType = "string",
      value = "DeviceTypeVersion, version of minimum supported deviceType",
      required = true
   )
   private String deviceTypeVersion;
   @ApiModelProperty(
      dataType = "string",
      value = "GroupName, group name of specified playlist"
   )
   private String groupName;
   @ApiModelProperty(
      dataType = "long",
      value = "GroupId, group ID of specified playlist"
   )
   private Long groupId;
   @ApiModelProperty(
      example = "1",
      dataType = "long",
      value = "Id of specific version , displays the version Id of the playlist."
   )
   private Long versionId;
   @ApiModelProperty(
      dataType = "long",
      value = "File size of specific playlist, appears in the Details area of the list."
   )
   private Long totalSize;
   @ApiModelProperty(
      example = "0",
      dataType = "string",
      value = "0:PREMIUM, 1:AMS, 2:VWL, 3:SYNC, 4:ADV,5:TAG,6:LINKED"
   )
   private String playlistType;
   @ApiModelProperty(
      dataType = "string",
      value = "Bring the details playlist"
   )
   private String details;
   @ApiModelProperty(
      example = "0",
      dataType = "string",
      value = "Playtime for movie files such as LFT,VWL,MOVIE, shows play time for video files such as LFT,VWL,MOVIE in three digits, appears in the Details area of the list.",
      required = true
   )
   private String playTime;
   @ApiModelProperty(
      dataType = "string",
      value = "Thumbnail info to reference when displaying thumbnails of playlist"
   )
   private String thumbnailInfo;
   @ApiModelProperty(
      dataType = "string",
      value = "Video Well Cognitive Check"
   )
   private String isVwl;
   @ApiModelProperty(
      example = "0",
      dataType = "int",
      value = "Playlist contentCount represents the number of content the playlist contains in the edit."
   )
   private int contentCount;
   @ApiModelProperty(
      example = "1",
      value = "PlaylistignoreTag is missing data and cannot be found. Only 0 and 1 are supposed to come.",
      dataType = "int"
   )
   private int ignoreTag;
   @ApiModelProperty(
      dataType = "boolean",
      value = "Check if you have the current sub playlist"
   )
   private boolean hasSubPlaylist;
   @Valid
   @ApiModelProperty(
      dataType = "list",
      value = "List of content category data",
      reference = "CategoryEntity"
   )
   private List categories;

   public V2PlaylistListResourceResponse() {
      super();
   }

   public String getPlaylistId() {
      return this.playlistId;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public Timestamp getLastModifiedDate() {
      return this.lastModifiedDate;
   }

   public int getShareFlag() {
      return this.shareFlag;
   }

   public String getCreatorId() {
      return this.creatorId;
   }

   public int getEvennessPlayback() {
      return this.evennessPlayback;
   }

   public String getPlaylistName() {
      return this.playlistName;
   }

   public String getPlaylistMetaData() {
      return this.playlistMetaData;
   }

   public String getDeviceTypeVersion() {
      return this.deviceTypeVersion;
   }

   public String getGroupName() {
      return this.groupName;
   }

   public Long getVersionId() {
      return this.versionId;
   }

   public Long getTotalSize() {
      return this.totalSize;
   }

   public String getPlaylistType() {
      return this.playlistType;
   }

   public String getDetails() {
      return this.details;
   }

   public String getPlayTime() {
      return this.playTime;
   }

   public String getThumbnailInfo() {
      return this.thumbnailInfo;
   }

   public String getIsVwl() {
      return this.isVwl;
   }

   public int getContentCount() {
      return this.contentCount;
   }

   public int getIgnoreTag() {
      return this.ignoreTag;
   }

   public Long getGroupId() {
      return this.groupId;
   }

   public boolean isHasSubPlaylist() {
      return this.hasSubPlaylist;
   }

   public List getCategories() {
      return this.categories;
   }

   public void setCategories(List categories) {
      this.categories = categories;
   }

   public static final class V2PlaylistListResourceResponseBuilder {
      private String playlistId;
      private String deviceType;
      private Timestamp lastModifiedDate;
      private int shareFlag;
      private String creatorId;
      private int evennessPlayback;
      private String playlistName;
      private String playlistMetaData;
      private String deviceTypeVersion;
      private String groupName;
      private Long groupId;
      private Long versionId;
      private Long totalSize;
      private String playlistType;
      private String details;
      private String playTime;
      private String thumbnailInfo;
      private String isVwl;
      private int contentCount;
      private int ignoreTag;
      private boolean hasSubPlaylist;
      private List categories;

      private V2PlaylistListResourceResponseBuilder() {
         super();
      }

      public static V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder aV2PlaylistListResourceResponse() {
         return new V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder();
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder playlistId(String playlistId) {
         this.playlistId = playlistId;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder deviceType(String deviceType) {
         this.deviceType = deviceType;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder lastModifiedDate(Timestamp lastModifiedDate) {
         this.lastModifiedDate = lastModifiedDate;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder shareFlag(int shareFlag) {
         this.shareFlag = shareFlag;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder creatorId(String creatorId) {
         this.creatorId = creatorId;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder evennessPlayback(int evennessPlayback) {
         this.evennessPlayback = evennessPlayback;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder playlistName(String playlistName) {
         this.playlistName = playlistName;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder playlistMetaData(String playlistMetaData) {
         this.playlistMetaData = playlistMetaData;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder deviceTypeVersion(String deviceTypeVersion) {
         this.deviceTypeVersion = deviceTypeVersion;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder groupName(String groupName) {
         this.groupName = groupName;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder groupId(Long groupId) {
         this.groupId = groupId;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder versionId(Long versionId) {
         this.versionId = versionId;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder totalSize(Long totalSize) {
         this.totalSize = totalSize;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder playlistType(String playlistType) {
         this.playlistType = playlistType;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder details(String details) {
         this.details = details;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder playTime(String playTime) {
         this.playTime = playTime;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder thumbnailInfo(String thumbnailInfo) {
         this.thumbnailInfo = thumbnailInfo;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder isVwl(String isVwl) {
         this.isVwl = isVwl;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder contentCount(int contentCount) {
         this.contentCount = contentCount;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder ignoreTag(int ignoreTag) {
         this.ignoreTag = ignoreTag;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder hasSubPlaylist(boolean hasSubPlaylist) {
         this.hasSubPlaylist = hasSubPlaylist;
         return this;
      }

      public V2PlaylistListResourceResponse.V2PlaylistListResourceResponseBuilder categories(List categories) {
         this.categories = categories;
         return this;
      }

      public V2PlaylistListResourceResponse build() {
         V2PlaylistListResourceResponse v2PlaylistListResourceResponse = new V2PlaylistListResourceResponse();
         v2PlaylistListResourceResponse.playlistType = this.playlistType;
         v2PlaylistListResourceResponse.hasSubPlaylist = this.hasSubPlaylist;
         v2PlaylistListResourceResponse.ignoreTag = this.ignoreTag;
         v2PlaylistListResourceResponse.deviceType = this.deviceType;
         v2PlaylistListResourceResponse.deviceTypeVersion = this.deviceTypeVersion;
         v2PlaylistListResourceResponse.shareFlag = this.shareFlag;
         v2PlaylistListResourceResponse.isVwl = this.isVwl;
         v2PlaylistListResourceResponse.playlistMetaData = this.playlistMetaData;
         v2PlaylistListResourceResponse.creatorId = this.creatorId;
         v2PlaylistListResourceResponse.lastModifiedDate = this.lastModifiedDate;
         v2PlaylistListResourceResponse.groupName = this.groupName;
         v2PlaylistListResourceResponse.playlistName = this.playlistName;
         v2PlaylistListResourceResponse.thumbnailInfo = this.thumbnailInfo;
         v2PlaylistListResourceResponse.totalSize = this.totalSize;
         v2PlaylistListResourceResponse.details = this.details;
         v2PlaylistListResourceResponse.playlistId = this.playlistId;
         v2PlaylistListResourceResponse.versionId = this.versionId;
         v2PlaylistListResourceResponse.contentCount = this.contentCount;
         v2PlaylistListResourceResponse.evennessPlayback = this.evennessPlayback;
         v2PlaylistListResourceResponse.groupId = this.groupId;
         v2PlaylistListResourceResponse.playTime = this.playTime;
         v2PlaylistListResourceResponse.categories = this.categories;
         return v2PlaylistListResourceResponse;
      }
   }
}
