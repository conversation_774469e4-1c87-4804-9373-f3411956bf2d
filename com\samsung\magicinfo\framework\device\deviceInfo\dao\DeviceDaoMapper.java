package com.samsung.magicinfo.framework.device.deviceInfo.dao;

import com.samsung.magicinfo.framework.device.deviceInfo.custom.entity.DeviceGeneralSearch;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.BackupPlayEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceLoopOutEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMemo;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceModel;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DisasterAlertStatusEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.rms.model.DeviceFilter;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DeviceDaoMapper {
   boolean addDevice(@Param("device") Device var1) throws SQLException;

   boolean addDeviceList(@Param("deviceList") List var1) throws SQLException;

   boolean addDeviceGroupMapping(@Param("parentGroupId") int var1, @Param("deviceId") String var2) throws SQLException;

   boolean addDeviceGroupMappingList(@Param("parentGroupId") int var1, @Param("deviceIdList") List var2) throws SQLException;

   boolean addDeviceModel(@Param("deviceModel") DeviceModel var1) throws SQLException;

   boolean addDeviceOperationInfo(@Param("device") Device var1) throws SQLException;

   boolean setUnapprovedGroupCode(@Param("deviceId") String var1, @Param("unapprovedGroupCode") Long var2) throws SQLException;

   int addVwtInfo(@Param("map") Map var1, @Param("deviceId") String var2);

   boolean deleteDeviceGroupMappingByDeviceId(@Param("deviceId") String var1) throws SQLException;

   boolean deleteDevice(@Param("deviceId") String var1) throws SQLException;

   boolean deleteDeviceClock(@Param("deviceId") String var1) throws SQLException;

   boolean deleteDeviceHoliday(@Param("deviceId") String var1) throws SQLException;

   boolean deleteDeviceTimer(@Param("deviceId") String var1) throws SQLException;

   boolean deleteDeviceGroupMapping(@Param("deviceId") String var1, @Param("groupId") int var2) throws SQLException;

   boolean deleteDeviceModel(@Param("modelName") String var1) throws SQLException;

   boolean deleteDeviceModels(@Param("deviceModelNameList") String[] var1) throws SQLException;

   List deleteDeviceModelSoftware(@Param("modelName") String var1) throws SQLException;

   boolean deleteGroupDevices(@Param("deviceIdList") String[] var1) throws SQLException;

   boolean deleteDevices(@Param("deviceIdList") String[] var1) throws SQLException;

   List deleteModelAndRule(@Param("modelName") String var1) throws SQLException;

   int getApprovedDeviceListCnt(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("search") DeviceGeneralSearch var4, @Param("deviceGroupListStr") String var5, @Param("nonApprovalGroupId") int var6, @Param("isDeviceGroupAuth") boolean var7, @Param("userId") String var8, @Param("deviceExpirationDate") Integer var9, @Param("tagFilter") List var10, @Param("constants") Map var11) throws SQLException;

   List getApprovedDeviceList(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("search") DeviceGeneralSearch var4, @Param("deviceGroupListStr") String var5, @Param("nonApprovalGroupId") int var6, @Param("isDeviceGroupAuth") boolean var7, @Param("userId") String var8, @Param("deviceExpirationDate") Integer var9, @Param("tagFilter") List var10, @Param("constants") Map var11) throws SQLException;

   List getApprovedDeviceListWithFilter(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("search") DeviceGeneralSearch var4, @Param("deviceGroupListStr") String var5, @Param("nonApprovalGroupId") int var6, @Param("isDeviceGroupAuth") boolean var7, @Param("userId") String var8, @Param("deviceExpirationDate") Integer var9, @Param("tagFilter") List var10, @Param("constants") Map var11) throws SQLException;

   int getCntApprovedDeviceList(@Param("search") DeviceGeneralSearch var1, @Param("deviceGroupList") List var2, @Param("isRoot") boolean var3, @Param("groupId") Long var4) throws SQLException;

   List getApprovedDeviceFilterList(@Param("condition") SelectCondition var1, @Param("deviceGroupList") List var2, @Param("non_approval_group") int var3) throws SQLException;

   int getCntEqualDevName(@Param("deviceName") String var1, @Param("groupIdList") List var2) throws SQLException;

   int getCntModelByModelCode(@Param("deviceModelCode") String var1) throws SQLException;

   Device getDevice(@Param("deviceId") String var1) throws SQLException;

   Device getDeviceWithGroupId(String var1) throws SQLException;

   Map getDeviceAndModel(@Param("deviceId") String var1) throws SQLException;

   DeviceGeneralConf getDeviceGeneralConf(@Param("deviceId") String var1) throws SQLException;

   List getListDeviceGeneralConf(@Param("deviceIds") List var1) throws SQLException;

   int getDeviceListByGroupCnt(@Param("modelName") String var1, @Param("groupId") Long var2, @Param("deviceId") String var3) throws SQLException;

   List getDeviceListByGroupId(@Param("groupId") Long var1) throws SQLException;

   List getDeviceAndTagListByGroupId(@Param("groupId") Long var1) throws SQLException;

   Long getDeviceUnapprovedGroupCode(@Param("deviceId") String var1) throws SQLException;

   List getDeviceAndTagListByGroupIds(@Param("groupIds") Long[] var1) throws SQLException;

   List getDeviceAndTagListByDeviceIds(@Param("deviceIds") String[] var1, @Param("isVarTag") Boolean var2) throws SQLException;

   List getDeviceListByGroup(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("modelName") String var3, @Param("groupId") Long var4, @Param("deviceId") String var5) throws SQLException;

   List getDeviceListByModelNameAndGroup(@Param("modelName") String var1, @Param("groupId") int var2) throws SQLException;

   int getDeviceListCnt(@Param("modelName") String var1, @Param("modelCode") String var2, @Param("deviceId") String var3) throws SQLException;

   List getDeviceListPaged(@Param("modelName") String var1, @Param("modelCode") String var2, @Param("deviceId") String var3, @Param("startPos") int var4, @Param("pageSize") int var5) throws SQLException;

   List getDeviceList(@Param("modelName") String var1, @Param("modelCode") String var2, @Param("deviceId") String var3) throws SQLException;

   Device getDeviceMinInfo(@Param("deviceId") String var1) throws SQLException;

   DeviceModel getDeviceModel(@Param("deviceModelName") String var1) throws SQLException;

   int getDeviceModelListCnt(@Param("deviceModelCode") String var1) throws SQLException;

   List getDeviceModelList(@Param("deviceModelCode") String var1, @Param("startPos") Integer var2, @Param("pageSize") Integer var3) throws SQLException;

   int getDeviceMonitoringListCnt(@Param("condition") SelectCondition var1, @Param("deviceGroupList") List var2, @Param("isDeviceGroupAuth") boolean var3, @Param("userId") String var4, @Param("deviceExpirationDate") Integer var5, @Param("constants") Map var6) throws SQLException;

   List getExpiredDeviceList(@Param("deviceExpirationDate") int var1) throws SQLException;

   List getDeviceMonitoringList(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("deviceGroupList") List var4, @Param("isDeviceGroupAuth") boolean var5, @Param("userId") String var6, @Param("deviceExpirationDate") Integer var7, @Param("constants") Map var8) throws SQLException;

   List getDeviceMonitoringList(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") DeviceFilter var3, @Param("deviceGroupList") List var4, @Param("isDeviceGroupAuth") boolean var5, @Param("userId") String var6, @Param("deviceExpirationDate") Integer var7, @Param("constants") Map var8) throws SQLException;

   int getDeviceMonitoringListCnt(@Param("condition") DeviceFilter var1, @Param("deviceGroupList") List var2, @Param("isDeviceGroupAuth") boolean var3, @Param("userId") String var4, @Param("deviceExpirationDate") Integer var5, @Param("constants") Map var6) throws SQLException;

   List getDeviceMonitoringFilterList(@Param("condition") SelectCondition var1, @Param("deviceGroupList") List var2) throws SQLException;

   String getDeviceNameById(@Param("deviceId") String var1) throws SQLException;

   Device getDeviceOperationInfo(@Param("deviceId") String var1) throws SQLException;

   List getDMInfo(@Param("deviceId") String var1) throws SQLException;

   String getModelNameByDeviceId(@Param("deviceId") String var1) throws SQLException;

   List getModelNameListByDeviceId(@Param("deviceId") String var1) throws SQLException;

   List getModelNameListByModelCode(@Param("deviceModelCode") String var1) throws SQLException;

   List getMonitoringInfoByDeviceId(@Param("deviceId") String var1) throws SQLException;

   boolean getDeviceApprovalStatusByDeviceId(@Param("deviceId") String var1) throws SQLException;

   List getMonitoringInfoByDeviceIdList(@Param("deviceIdList") String[] var1) throws SQLException;

   List getNonApprovedDeviceListLimitByDeviceType(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") Map var3, @Param("deviceType") String var4, @Param("extraFlag") boolean var5) throws SQLException;

   int getNonApprovedDeviceListLimitByDeviceTypeCnt(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") Map var3, @Param("deviceType") String var4, @Param("extraFlag") boolean var5) throws SQLException;

   int getNonApprovedDeviceListCnt(@Param("map") Map var1) throws SQLException;

   int getNonApprovalDeviceCount(@Param("deviceType") String var1) throws SQLException;

   List getNonApprovedDeviceList(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("map") Map var3) throws SQLException;

   List getNonApprovedPremiumOnlyDeviceListLimit(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") Map var3, @Param("deviceTypeFilter") String[] var4) throws SQLException;

   int getNonApprovedPremiumOnlyDeviceListLimitCnt(@Param("condition") Map var1, @Param("deviceTypeFilter") String[] var2) throws SQLException;

   int getNonApprovedPremiumOnlyDeviceListLimitOpenAPISelect() throws SQLException;

   List getNonApprovedPremiumOnlyDeviceListLimitOpenAPI(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") Map var3, @Param("deviceTypeFilter") String[] var4, @Param("countOfExtraDisplay") Integer var5, @Param("constants") Map var6) throws SQLException;

   List getNonApprovedPremiumOnlyDeviceListLimitOpenAPIForMega(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") Map var3, @Param("countOfExtraDisplay") Integer var4, @Param("constants") Map var5) throws SQLException;

   List getAppVersionList() throws SQLException;

   List getRuleVersionList() throws SQLException;

   int moveDeviceUpdate(@Param("deviceId") String var1, @Param("currentParentGroupId") long var2, @Param("newParentGroupId") int var4) throws SQLException;

   int moveDeviceInsert(@Param("deviceId") String var1, @Param("newParentGroupId") int var2) throws SQLException;

   List moveDeviceSelect(@Param("deviceId") String var1) throws SQLException;

   List selAllApprovedDevice() throws SQLException;

   boolean setDevice(@Param("device") Device var1) throws SQLException;

   int setDeviceGroupId(@Param("map") Map var1) throws SQLException;

   int setChildDeviceGroupId(@Param("map") Map var1) throws SQLException;

   boolean setDeviceModel(@Param("deviceModel") DeviceModel var1) throws SQLException;

   boolean updateDiskSpaceChannel(@Param("deviceId") String var1, @Param("diskSpace") long var2, @Param("channel") String var4) throws SQLException;

   int setDeviceOperationInfo(@Param("device") Device var1) throws SQLException;

   int setIsApproved(@Param("map") Map var1) throws SQLException;

   int setNameDeviceAndModel(@Param("map") Map var1, @Param("calTimestamp") Timestamp var2) throws SQLException;

   List getConnectedDeviceLiteModelNameList() throws SQLException;

   List getConnectedDeviceModelNameList() throws SQLException;

   List getDeviceModelNameList() throws SQLException;

   List getDeviceLiteModelNameList() throws SQLException;

   List getDeviceModelTypeList() throws SQLException;

   boolean setDevicePostBootstrap(@Param("device") Device var1) throws SQLException;

   boolean setDeviceNameAndLocation(@Param("deviceId") String var1, @Param("deviceName") String var2, @Param("location") String var3, @Param("mapLocation") String var4, @Param("deviceModelName") String var5) throws SQLException;

   boolean setLastConnectionTime(@Param("deviceId") String var1) throws SQLException;

   boolean setShutDownConnectionTime(@Param("deviceId") String var1, @Param("currTime") Timestamp var2) throws SQLException;

   List getFirmwareVersionList() throws SQLException;

   List getOSImageVersionList() throws SQLException;

   List getDeviceResolutionList(@Param("deviceType") String var1) throws SQLException;

   boolean deleteBindingDevice(@Param("ipAddress") String var1) throws SQLException;

   Long addBindingDeviceSelect(@Param("map") Map var1) throws SQLException;

   boolean addBindingDeviceInsert(@Param("map") Map var1) throws SQLException;

   List getBindingDeviceList(@Param("deviceName") String var1) throws SQLException;

   List getBindingDeviceListPage(@Param("deviceName") String var1, @Param("startPos") int var2, @Param("pageSize") int var3) throws SQLException;

   int getBindingDeviceListCnt(@Param("deviceName") String var1) throws SQLException;

   int getAllDeviceCount(@Param("deviceType") String var1) throws SQLException;

   int getAllDeviceCountByOrganization(@Param("deviceType") String var1, @Param("organization") String var2) throws SQLException;

   int getApprovalDeviceCount(@Param("deviceType") String var1) throws SQLException;

   int getApprovalPremiumDeviceCount() throws SQLException;

   int getApprovalExtraDisplayDeviceCount() throws SQLException;

   Long addDeviceBindingInfoSelect(@Param("device") Device var1) throws SQLException;

   boolean addDeviceBindingInfoInsert(@Param("device") Device var1) throws SQLException;

   boolean deleteDeviceBindingInfoByDeviceId(@Param("deviceId") String var1) throws SQLException;

   boolean deleteDeviceBindingInfo() throws SQLException;

   String getDeviceModelCodeByDeviceId(@Param("deviceId") String var1) throws SQLException;

   String getDeviceGroupIdByDeviceId(@Param("deviceId") String var1) throws SQLException;

   List getDeviceIdGroupIdByDeviceName(@Param("deviceName") String var1) throws SQLException;

   String getProgramIdByDeviceId(@Param("deviceId") String var1) throws SQLException;

   String getEventScheduleIdByDeviceId(@Param("deviceId") String var1) throws SQLException;

   String getScheduleIdByProgramId(@Param("programId") String var1) throws SQLException;

   Long getVersionByProgramId(@Param("programId") String var1) throws SQLException;

   Map getApprovalByDeviceId(@Param("deviceId") String var1) throws SQLException;

   List getDeviceIdByDesc(@Param("deviceType") String var1) throws SQLException;

   List getDeviceIdByAsc(@Param("deviceType") String var1) throws SQLException;

   List getApprovalDeviceIdByAsc() throws SQLException;

   boolean isVwlConsole(@Param("deviceId") String var1) throws SQLException;

   int getCntDeviceByDeviceType(@Param("deviceType") String var1) throws SQLException;

   int refreshDeviceGroupType(@Param("groupId") int var1) throws SQLException;

   List getDeviceIdListByGroup(@Param("groupId") int var1) throws SQLException;

   List getNotChildDeviceIdListByGroup(@Param("groupId") int var1) throws SQLException;

   Long addDeviceDisplaySelect(@Param("deviceId") String var1) throws SQLException;

   boolean addDeviceDisplayInsert(@Param("deviceId") String var1) throws SQLException;

   int addStatRequestTimeSelect(@Param("deviceId") String var1) throws SQLException;

   boolean addStatRequestTimeInsert(@Param("deviceId") String var1, @Param("requestTime") Timestamp var2) throws SQLException;

   boolean addStatRequestTimeInsertCurrent(@Param("deviceId") String var1) throws SQLException;

   List getApprovalDeviceIdByDeviceTypeAsc(@Param("deviceType") String var1) throws SQLException;

   List getApprovalDeviceIdByDeviceTypeListAsc(@Param("deviceTypeList") List var1) throws SQLException;

   List getApprovalDeviceIdByDeviceTypeListInOrgAssignedLicenseAsc(@Param("deviceTypeList") List var1) throws SQLException;

   List getApprovalDeviceIdByDeviceTypeSocAsc(@Param("types") String[] var1) throws SQLException;

   Long getDeviceMonitoringInterval(@Param("deviceId") String var1) throws SQLException;

   List getAppVersionListByDeviceType(@Param("deviceType") String var1, @Param("premiumType") boolean var2) throws SQLException;

   List getAppVersionListBy(@Param("map") Map var1) throws SQLException;

   List getDeviceModelNameListBy(@Param("map") Map var1) throws SQLException;

   List getConnectedDeviceModelNameListTypeS(@Param("deviceType") String var1, @Param("organization") String var2) throws SQLException;

   Map getIsRedundancy(@Param("groupId") int var1) throws SQLException;

   String getVwtIdByDeviceId(@Param("deviceId") String var1) throws SQLException;

   Map getVwtFileName(@Param("vwtId") String var1) throws SQLException;

   int addDeviceTypeVersion(@Param("deviceId") String var1, @Param("deviceTypeVersion") BigDecimal var2) throws SQLException;

   int addDeviceInfoAtApprove(@Param("device") Device var1) throws SQLException;

   Long getPriorityByDeviceTypeAndVersion(@Param("deviceType") String var1, @Param("deviceTypeVersion") BigDecimal var2) throws SQLException;

   Float getMinimalDeviceTypeVersionByDeviceTypeAndGroupId(@Param("deviceType") String var1, @Param("groupId") long var2) throws SQLException;

   Long getDeviceGroupPriority(@Param("groupId") long var1) throws SQLException;

   List getDeviceListByModelNameAndType(@Param("modelName") String var1, @Param("modelCode") String var2, @Param("deviceId") String var3, @Param("deviceType") String var4, @Param("organization") String var5) throws SQLException;

   Long getDeviceListByModelNameAndTypeCount(@Param("modelName") String var1, @Param("modelCode") String var2, @Param("deviceId") String var3, @Param("deviceType") String var4) throws SQLException;

   int deleteVwtInfo(@Param("deviceId") String var1) throws SQLException;

   Map getDeviceModelNameByDeviceId(@Param("deviceId") String var1) throws SQLException;

   int addRedundancyStatus(@Param("deviceId") String var1, @Param("redundanctStatus") boolean var2) throws SQLException;

   Map isRedundancyDevice(@Param("deviceId") String var1) throws SQLException;

   int addDeviceWaitingMo(@Param("deviceId") String var1, @Param("serviceName") String var2, @Param("infoValue") String var3);

   String getDeviceWaitingMo(@Param("deviceId") String var1);

   int deleteWaitingMo(@Param("deviceId") String var1);

   int setDeviceWaitingMo(@Param("serviceName") String var1, @Param("infoValue") String var2, @Param("deviceId") String var3);

   String getDayLightSavingManual(@Param("deviceId") String var1);

   void setDeviceChildCount(@Param("deviceId") String var1, @Param("childCount") Long var2);

   Long getDeviceGroupId(@Param("deviceId") String var1) throws SQLException;

   boolean getExistCenterstage() throws SQLException;

   int insertDisasterAlertStatus(@Param("DisasterAlertStatus") DisasterAlertStatusEntity var1) throws SQLException;

   List selectDisasterAlertStatus(@Param("alert_id") String var1) throws SQLException;

   List selectDisasterAlertStatusDisconnected(@Param("device_id") String var1) throws SQLException;

   List selectDisasterAlertStatusByDeviceId(@Param("device_id") String var1) throws SQLException;

   List selectSimpleDisasterAlertStatus(@Param("alert_id") String var1) throws SQLException;

   List getDisconnectedDisasterAlertByDeviceIdAndAlertId(@Param("device_id") String var1, @Param("alert_id") String var2) throws SQLException;

   int updateDisasterAlertStatus(@Param("DisasterAlertStatus") DisasterAlertStatusEntity var1) throws SQLException;

   void deleteDisasterAlertStatus(@Param("alert_id") String var1) throws SQLException;

   void deleteDisconnectedDisasterAlertStatus(@Param("device_id") String var1, @Param("alert_id") String var2) throws SQLException;

   int insertExtDeviceInfo(@Param("deviceLoopOutInfo") DeviceLoopOutEntity var1) throws SQLException;

   List selectExtDeviceInfo(@Param("device_id") String var1, @Param("type") String var2) throws SQLException;

   int updateExtDeviceInfo(@Param("deviceLoopOutInfo") DeviceLoopOutEntity var1) throws SQLException;

   void deleteExtDeviceInfo(@Param("device_id") String var1) throws SQLException;

   Map getSoftwareUpdate(@Param("device_id") String var1) throws Exception;

   boolean setKeepAliveInfo(@Param("deviceId") String var1, @Param("diskSpace") Long var2, @Param("channel") String var3, @Param("curr_content_id") String var4, @Param("diskSpaceUsage") String var5, @Param("diskSpaceAvailable") String var6) throws SQLException;

   List getCheckDeviceListTimezone(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("deviceGroupList") List var4, @Param("search") DeviceGeneralSearch var5, @Param("deviceTypeFilter") String[] var6, @Param("constants") Map var7, @Param("isDeviceGroupAuth") boolean var8, @Param("userId") String var9) throws SQLException;

   int getCheckDeviceListCntTimezone(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("deviceGroupList") List var4, @Param("search") DeviceGeneralSearch var5, @Param("deviceTypeFilter") String[] var6, @Param("constants") Map var7, @Param("isDeviceGroupAuth") boolean var8, @Param("userId") String var9) throws SQLException;

   List getCheckDeviceList(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("deviceGroupList") List var4, @Param("search") DeviceGeneralSearch var5, @Param("deviceTypeFilter") String[] var6, @Param("constants") Map var7, @Param("isDeviceGroupAuth") boolean var8, @Param("userId") String var9) throws SQLException;

   int getCheckDeviceListCnt(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("deviceGroupList") List var4, @Param("search") DeviceGeneralSearch var5, @Param("deviceTypeFilter") String[] var6, @Param("constants") Map var7, @Param("isDeviceGroupAuth") boolean var8, @Param("userId") String var9) throws SQLException;

   List getCheckDeviceListSchedule(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("deviceGroupList") List var4, @Param("search") DeviceGeneralSearch var5, @Param("deviceTypeFilter") String[] var6, @Param("constants") Map var7, @Param("isDeviceGroupAuth") boolean var8, @Param("userId") String var9) throws SQLException;

   int getCheckDeviceListCntSchedule(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("deviceGroupList") List var4, @Param("search") DeviceGeneralSearch var5, @Param("deviceTypeFilter") String[] var6, @Param("constants") Map var7, @Param("isDeviceGroupAuth") boolean var8, @Param("userId") String var9) throws SQLException;

   List getCheckDeviceListScheduleFail(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("deviceGroupList") List var4, @Param("search") DeviceGeneralSearch var5, @Param("deviceTypeFilter") String[] var6, @Param("constants") Map var7, @Param("isDeviceGroupAuth") boolean var8, @Param("userId") String var9) throws SQLException;

   int getCheckDeviceListCntScheduleFail(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("deviceGroupList") List var4, @Param("search") DeviceGeneralSearch var5, @Param("deviceTypeFilter") String[] var6, @Param("constants") Map var7, @Param("isDeviceGroupAuth") boolean var8, @Param("userId") String var9) throws SQLException;

   List getCheckDeviceListReservationScheduleFail(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("deviceGroupList") List var4, @Param("search") DeviceGeneralSearch var5, @Param("deviceTypeFilter") String[] var6, @Param("constants") Map var7, @Param("isDeviceGroupAuth") boolean var8, @Param("userId") String var9) throws SQLException;

   int getCheckDeviceListCntReservationScheduleFail(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("deviceGroupList") List var4, @Param("search") DeviceGeneralSearch var5, @Param("deviceTypeFilter") String[] var6, @Param("constants") Map var7, @Param("isDeviceGroupAuth") boolean var8, @Param("userId") String var9) throws SQLException;

   List getCheckDeviceListContent(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("deviceGroupList") List var4, @Param("search") DeviceGeneralSearch var5, @Param("deviceTypeFilter") String[] var6, @Param("constants") Map var7, @Param("isDeviceGroupAuth") boolean var8, @Param("userId") String var9) throws SQLException;

   int getCheckDeviceListCntContent(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") SelectCondition var3, @Param("deviceGroupList") List var4, @Param("search") DeviceGeneralSearch var5, @Param("deviceTypeFilter") String[] var6, @Param("constants") Map var7, @Param("isDeviceGroupAuth") boolean var8, @Param("userId") String var9) throws SQLException;

   List getCheckUpcomingExpiryDateList(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") Map var3, @Param("stopDate") String var4) throws SQLException;

   int getCheckUpcomingExpiryDateCnt(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") Map var3, @Param("stopDate") String var4) throws SQLException;

   List getCheckUpcomingExpiryDatePlaylistList(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") Map var3, @Param("stopDate") String var4) throws SQLException;

   int getCheckUpcomingExpiryDatePlaylistCnt(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("condition") Map var3, @Param("stopDate") String var4) throws SQLException;

   List getChildDeviceIdList(@Param("parentDeviceId") String var1) throws SQLException;

   boolean deleteChildDevice(@Param("parentDeviceId") String var1) throws SQLException;

   boolean deleteChildDeviceDisplayConf(@Param("parentDeviceId") String var1) throws SQLException;

   boolean setChildCnt(@Param("parentDeviceId") String var1, @Param("childCnt") int var2) throws SQLException;

   boolean deleteChildDeviceGroupMapping(@Param("parentDeviceId") String var1) throws SQLException;

   boolean setConnectChildCnt(@Param("deviceId") String var1, @Param("connChildCnt") Long var2);

   DeviceMemo getDeviceMemo(@Param("deviceId") String var1) throws SQLException;

   boolean addDeviceMemo(@Param("memo") DeviceMemo var1) throws SQLException;

   boolean setDeviceMemo(@Param("memo") DeviceMemo var1) throws SQLException;

   boolean deleteDeviceMemo(@Param("memo") DeviceMemo var1) throws SQLException;

   List getDeviceReportList() throws SQLException;

   List getDeviceModelCount() throws SQLException;

   List getDeviceFirmwareCount() throws SQLException;

   boolean setDeviceAmsCam(@Param("isWebCam") boolean var1, @Param("deviceId") String var2) throws SQLException;

   boolean addBackupPlayer(@Param("backup") BackupPlayEntity var1) throws SQLException;

   boolean addBackupTargetPlayer(@Param("backup") BackupPlayEntity var1) throws SQLException;

   boolean deleteBackupPlayer(@Param("groupId") int var1) throws SQLException;

   boolean deleteBackupTargetPlayer(@Param("groupId") int var1) throws SQLException;

   boolean setBackupBusyLevel(@Param("busyLevel") int var1, @Param("backupDeviceId") String var2) throws SQLException;

   boolean setWaitingMoCount(@Param("waitingMoCount") int var1, @Param("backupDeviceId") String var2) throws SQLException;

   boolean setBackupDevice(@Param("backupDeviceId") String var1, @Param("deviceId") String var2) throws SQLException;

   List getBackupPlayers(@Param("groupId") Long var1) throws SQLException;

   List getBackupPlayerByWaitingMoCount(@Param("groupId") Long var1) throws SQLException;

   BackupPlayEntity getBackupPlayerByDeviceId(@Param("deviceId") String var1) throws SQLException;

   List getBackupTargetPlayers(@Param("groupId") Long var1) throws SQLException;

   DeviceMonitoring getProgramInfoByDeviceGroupId(@Param("groupId") Long var1) throws SQLException;

   List getDeviceAndGroupInfoByGroupId(@Param("groupId") Long var1) throws SQLException;

   int cntSyncPlayDevice(@Param("deviceId") String var1) throws SQLException;

   boolean updateLastModifiedTime(@Param("deviceId") String var1) throws SQLException;

   boolean updateLogFileName(@Param("deviceId") String var1, @Param("categoryScript") String var2, @Param("fileName") String var3) throws SQLException;

   List getNewAndModifiedDeviceList(@Param("startDate") Timestamp var1, @Param("endDate") Timestamp var2) throws SQLException;

   Map getStatisticsFileRequestTime(@Param("deviceId") String var1) throws SQLException;

   boolean addDeviceTotalCount(@Param("groupId") long var1, @Param("count") int var3) throws SQLException;

   List getPgorupIdLIsts(@Param("groupId") long var1) throws SQLException;

   long getDeviceTotalCount(@Param("groupId") long var1) throws SQLException;

   long setDeviceTotalCount(@Param("groupId") long var1, @Param("count") long var3) throws SQLException;

   int getDeviceCountBygroupId(@Param("groupId") long var1) throws SQLException;

   boolean removeDeviceTotalCount(@Param("groupId") long var1, @Param("count") int var3) throws SQLException;

   List getDeviceLogProcessInfo(@Param("deviceId") String var1) throws SQLException;

   int addDeviceLogProcessInfo(@Param("deviceId") String var1, @Param("type") String var2, @Param("categoryScript") String var3, @Param("status") String var4, @Param("startTime") Timestamp var5, @Param("duration") int var6, @Param("packetSize") int var7, @Param("token") String var8, @Param("smartDiagnosticVersion") int var9) throws SQLException;

   boolean updateDeviceLogProcessStatus(@Param("deviceId") String var1, @Param("type") String var2, @Param("categoryScript") String var3, @Param("status") String var4) throws SQLException;

   boolean deleteDeviceLogProcessInfoByDeviceId(@Param("deviceId") String var1, @Param("categoryScript") String var2) throws SQLException;

   int getLogProcessingDeviceCnt() throws SQLException;

   boolean updateDeviceLogInfo(@Param("deviceId") String var1, @Param("type") String var2, @Param("categoryScript") String var3, @Param("status") String var4, @Param("startTime") Timestamp var5, @Param("duration") int var6, @Param("packetSize") int var7, @Param("token") String var8, @Param("encryptionKey") String var9) throws SQLException;

   boolean getIsOverWriteDeviceName(@Param("deviceId") String var1) throws SQLException;

   List getAllDeviceLogProcess() throws SQLException;

   List getDeviceListByOrgName(@Param("orgName") String var1);

   int getDeviceCountForLicense(@Param("map") List var1) throws SQLException;

   List getDeviceListFromDeviceId(@Param("deviceIds") List var1) throws SQLException;

   List getFirstChildrenIDsOfSignageGroup(@Param("groupId") int var1) throws SQLException;

   List getDeviceGroupIdFromMapProgramByProgramId(@Param("programId") String var1) throws SQLException;

   int getProgramDeviceTypeByGroupId(@Param("groupId") long var1) throws SQLException;

   int checkFirstReceiveProgress(@Param("deviceId") String var1) throws SQLException;

   Integer getContentDownloadMode(@Param("deviceId") String var1) throws SQLException;

   List getTagFromDeviceId(@Param("deviceId") String var1, @Param("isVarTag") Boolean var2) throws SQLException;

   int getAllDeviceCountByDeviceTypeList(@Param("deviceTypeList") List var1) throws SQLException;

   String getOrganiationByDeviceId(@Param("deviceId") String var1) throws SQLException;

   boolean updateDeviceMapLocation(@Param("location") String var1, @Param("deviceIdList") String[] var2) throws SQLException;

   String getMapLocationByDeviceId(@Param("deviceId") String var1) throws SQLException;

   boolean updateDeviceMapLocationByLocation(@Param("location") String var1, @Param("locationList") String[] var2) throws SQLException;

   List getDeviceMinList(@Param("deviceIdList") String[] var1) throws SQLException;

   boolean addSboxVwtInfo(@Param("deviceId") String var1, @Param("sboxVwtId") String var2, @Param("sboxVwtFileName") String var3) throws SQLException;

   boolean deleteSboxVwtInfo(@Param("deviceId") String var1) throws SQLException;

   int setKeepaliveChangedStatus(@Param("keepaliveStatus") Boolean var1, @Param("deviceId") String var2) throws SQLException;

   int initKeepaliveChangedStatus() throws SQLException;

   List getDisconnectedDeviceIdList() throws SQLException;

   boolean setRecommendPlayByDeviceId(@Param("deviceId") String var1, @Param("value") boolean var2) throws SQLException;

   boolean getRecommendPlayByDeviceId(@Param("deviceId") String var1) throws SQLException;

   int getCntRecommendPlayDevice() throws SQLException;

   int getCountDeviceAll(@Param("groupList") List var1, @Param("userId") String var2, @Param("isDeviceGroupAuth") boolean var3) throws SQLException;

   int getCountTimezoneNotSet(@Param("groupList") List var1, @Param("userId") String var2, @Param("isDeviceGroupAuth") boolean var3, @Param("condition") SelectCondition var4) throws SQLException;

   List getListTimezoneNotSet(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("groupList") List var3, @Param("userId") String var4, @Param("isDeviceGroupAuth") boolean var5, @Param("condition") SelectCondition var6) throws SQLException;

   int getCountInsufficientCapacity(@Param("groupList") List var1, @Param("userId") String var2, @Param("isDeviceGroupAuth") boolean var3, @Param("condition") SelectCondition var4) throws SQLException;

   List getListInsufficientCapacity(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("groupList") List var3, @Param("userId") String var4, @Param("isDeviceGroupAuth") boolean var5, @Param("condition") SelectCondition var6) throws SQLException;

   int getCountScheduleNotPublish(@Param("groupList") List var1, @Param("userId") String var2, @Param("isDeviceGroupAuth") boolean var3, @Param("condition") SelectCondition var4) throws SQLException;

   List getListScheduleNotPublish(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("groupList") List var3, @Param("userId") String var4, @Param("isDeviceGroupAuth") boolean var5, @Param("condition") SelectCondition var6) throws SQLException;

   int getCountContentError(@Param("groupList") List var1, @Param("userId") String var2, @Param("isDeviceGroupAuth") boolean var3, @Param("condition") SelectCondition var4) throws SQLException;

   List getListContentError(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("groupList") List var3, @Param("userId") String var4, @Param("isDeviceGroupAuth") boolean var5, @Param("condition") SelectCondition var6) throws SQLException;

   List getRmMonitoringList(@Param("deviceIdList") String[] var1, @Param("errorStandardTime") Timestamp var2) throws SQLException;

   List getErrorList(@Param("deviceIdList") String[] var1, @Param("type") String var2, @Param("errorPeriod") Timestamp var3, @Param("status") Integer var4) throws SQLException;

   List getMaxDeviceTypeVersion() throws SQLException;

   List getDeviceIdListByCurrentContentIds(@Param("orgName") String var1, @Param("contentIdList") List var2) throws SQLException;

   String getCurrentContentIdByDeviceId(@Param("deviceId") String var1) throws SQLException;

   Boolean isDoneAtLast(@Param("deviceId") String var1) throws SQLException;

   List getAllNotDonePlayingDefaultContentHistory() throws SQLException;

   boolean addPlayingDefaultContentHistory(@Param("deviceId") String var1, @Param("orgName") String var2) throws SQLException;

   int setPlayingDefaultContentDone(@Param("deviceId") String var1) throws SQLException;

   boolean deletePlayingDefaultContentHistoryByDeviceId(@Param("deviceId") String var1) throws SQLException;

   boolean deletePlayingDefaultContentHistoryByOrganizationName(@Param("orgName") String var1) throws SQLException;

   int setOrganizationByDeviceId(@Param("map") Map var1) throws SQLException;

   List getPlayingDefaultContentHistoryList(@Param("orgName") String var1) throws SQLException;

   DeviceGeneralConf getDeviceTypeInfo(String var1);

   List getDeviceNameList(@Param("organizationName") String var1) throws SQLException;

   List getDevicesByGroupIds(@Param("groupIds") List var1) throws SQLException;

   List getDevicesByProgramId(@Param("programId") String var1) throws SQLException;

   List getDeviceCountByDeviceType() throws SQLException;

   List getDeviceSbox() throws SQLException;
}
