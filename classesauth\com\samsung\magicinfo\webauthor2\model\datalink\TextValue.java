package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.samsung.magicinfo.webauthor2.model.datalink.Value;
import com.samsung.magicinfo.webauthor2.model.datalink.ValueType;

public class TextValue extends Value {
  private final String value;
  
  @JsonCreator
  public TextValue(@JsonProperty("value") String value) {
    super(ValueType.TEXT);
    this.value = value;
  }
  
  public String getValue() {
    return this.value;
  }
}
