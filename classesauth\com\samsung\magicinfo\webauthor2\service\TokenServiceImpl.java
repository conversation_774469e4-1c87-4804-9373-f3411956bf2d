package com.samsung.magicinfo.webauthor2.service;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.service.ConnectionException;
import com.samsung.magicinfo.webauthor2.repository.model.TokenResponseData;
import com.samsung.magicinfo.webauthor2.service.TokenService;
import java.net.URI;
import java.util.Collections;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Service
public class TokenServiceImpl implements TokenService {
  private RestTemplate restTemplate;
  
  private static final Logger logger = LoggerFactory.getLogger(TokenService.class);
  
  @Autowired
  public TokenServiceImpl(RestTemplate restTemplate) {
    this.restTemplate = restTemplate;
  }
  
  public boolean inspireToken(String token) {
    URI uri = UriComponentsBuilder.newInstance().path("/openapi/auth").build().encode().toUri();
    HttpHeaders headers = new HttpHeaders();
    headers.setAccept(Collections.singletonList(MediaType.APPLICATION_XML));
    LinkedMultiValueMap linkedMultiValueMap = new LinkedMultiValueMap();
    linkedMultiValueMap.add("cmd", "inspireToken");
    linkedMultiValueMap.add("token", token);
    HttpEntity<MultiValueMap<String, String>> request = new HttpEntity(linkedMultiValueMap, (MultiValueMap)headers);
    logger.debug("REQUEST: " + request.toString());
    TokenResponseData tokenResponseData = (TokenResponseData)this.restTemplate.postForObject(uri, request, TokenResponseData.class);
    return (tokenResponseData != null && tokenResponseData.getCode().equals("0") && 
      !Strings.isNullOrEmpty(tokenResponseData.getToken()));
  }
  
  public String getAuthenticationToken(String userId, String password) throws ConnectionException {
    URI uri = UriComponentsBuilder.newInstance().path("/openapi/auth").queryParam("cmd", new Object[] { "getAuthToken" }).queryParam("id", new Object[] { userId }).queryParam("pw", new Object[] { password }).build().encode().toUri();
    HttpHeaders headers = new HttpHeaders();
    headers.setAccept(Collections.singletonList(MediaType.APPLICATION_XML));
    HttpEntity<Object> request = new HttpEntity((MultiValueMap)headers);
    logger.debug("REQUEST: " + request.toString());
    TokenResponseData tokenResponseData = (TokenResponseData)this.restTemplate.postForObject(uri, request, TokenResponseData.class);
    if (tokenResponseData == null || !tokenResponseData.getCode().equals("0"))
      throw new ConnectionException("Authentication error."); 
    return tokenResponseData.getToken();
  }
}
