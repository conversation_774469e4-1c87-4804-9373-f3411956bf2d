package com.samsung.magicinfo.restapi.user.service;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.RoleUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.mu.entity.OrganizationGroup;
import com.samsung.magicinfo.framework.mu.manager.OrganizationGroupInfo;
import com.samsung.magicinfo.framework.mu.manager.OrganizationGroupInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonDeleteFail;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonOrganizationData;
import com.samsung.magicinfo.restapi.exception.BaseRestException;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.user.controller.V2OrganizationGroupController;
import com.samsung.magicinfo.restapi.user.model.V2AdminInfoResource;
import com.samsung.magicinfo.restapi.user.model.V2DeleteCheckGroupInquiryResource;
import com.samsung.magicinfo.restapi.user.model.V2ListGroupInquiryFilter;
import com.samsung.magicinfo.restapi.user.model.V2OrganizationGroupResource;
import com.samsung.magicinfo.restapi.user.model.V2SaveGroupInquiryFilter;
import com.samsung.magicinfo.restapi.user.model.V2SaveGroupInquiryResource;
import com.samsung.magicinfo.restapi.user.model.V2UnDeletableOrgGroupData;
import com.samsung.magicinfo.restapi.user.utils.UserUtils;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import org.apache.logging.log4j.Logger;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2OrganizationGroupService")
@Transactional
public class V2OrganizationGroupServiceImpl implements V2OrganizationGroupService {
   protected Logger logger = LoggingManagerV2.getLogger(V2OrganizationGroupController.class);

   public V2OrganizationGroupServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2PageResource listGroupInquiry(V2ListGroupInquiryFilter filter) throws Exception {
      User loginUser = SecurityUtils.getUserContainer().getUser();
      if (!RoleUtils.isServerAdminRole(loginUser)) {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
      } else {
         String searchText = filter.getSearchText();
         int startIndex = filter.getStartIndex();
         int results = filter.getPageSize();
         String sort = filter.getSort();
         String dir = filter.getDir();
         searchText = StrUtils.nvl(searchText).equals("") ? "" : searchText;
         searchText = searchText.replaceAll("\\[", "^[");
         searchText = searchText.replaceAll("]", "^]");
         searchText = searchText.replaceAll("%", "^%");
         OrganizationGroupInfo organizationGroupInfo = OrganizationGroupInfoImpl.getInstance();
         ListManager listMgr = new ListManager(organizationGroupInfo, "list");
         listMgr.addSearchInfo("sortColumn", sort);
         listMgr.addSearchInfo("sortOrder", dir);
         listMgr.addSearchInfo("searchText", searchText);
         listMgr.addSearchInfo("scope", RoleUtils.SCOPE_GROUP);
         listMgr.setLstSize(Integer.valueOf(results));
         List organizationGroupList = listMgr.V2dbexecute(startIndex, results);
         PageManager pageMgr = listMgr.getPageManager();
         String mngOrgGroupId = "";
         List dataList = new ArrayList();
         String orgGroupNameList = "";
         V2OrganizationGroupResource data = new V2OrganizationGroupResource();
         List organizationList = new ArrayList();
         new V2CommonOrganizationData();

         int index;
         for(index = 0; index < organizationGroupList.size(); ++index) {
            OrganizationGroup organizationGroup = (OrganizationGroup)organizationGroupList.get(index);
            String tmpOrganizationGroupName = organizationGroup.getMng_org_group_name().replaceAll("<", "&lt");
            String tmpMngOrgGroupId = organizationGroup.getMng_org_group_id();
            String orgGroupName = organizationGroup.getOrg_group_name();
            String orgGroupId = organizationGroup.getOrg_group_id();
            V2CommonOrganizationData orgData;
            boolean orgCheck;
            int j;
            if (mngOrgGroupId.equals(tmpMngOrgGroupId)) {
               orgData = new V2CommonOrganizationData();
               data = new V2OrganizationGroupResource();
               orgData.setOrganizationId(Long.valueOf(orgGroupId));
               orgData.setOrganizationName(orgGroupName);
               organizationList.add(orgData);
               data.setOrganizationGroupName(tmpOrganizationGroupName);
               data.setOrganizationGroupId(Long.valueOf(tmpMngOrgGroupId));
               data.setOrganizationDataList(organizationList);
            } else {
               if (index > 0 && data.getOrganizationDataList().size() > 0) {
                  orgCheck = false;

                  for(j = 0; j < data.getOrganizationDataList().size(); ++j) {
                     if (((V2CommonOrganizationData)data.getOrganizationDataList().get(j)).getOrganizationName().toLowerCase().contains(searchText.toLowerCase())) {
                        orgCheck = true;
                     }
                  }

                  if (orgCheck) {
                     dataList.add(data);
                  }
               }

               data = new V2OrganizationGroupResource();
               orgData = new V2CommonOrganizationData();
               organizationList = new ArrayList();
               orgData.setOrganizationId(Long.valueOf(orgGroupId));
               orgData.setOrganizationName(orgGroupName);
               organizationList.add(orgData);
               orgGroupNameList = "";
               mngOrgGroupId = tmpMngOrgGroupId;
               data.setOrganizationGroupName(tmpOrganizationGroupName);
               data.setOrganizationGroupId(Long.valueOf(tmpMngOrgGroupId));
               data.setOrganizationDataList(organizationList);
            }

            if (index == organizationGroupList.size() - 1) {
               orgCheck = false;

               for(j = 0; j < data.getOrganizationDataList().size(); ++j) {
                  if (((V2CommonOrganizationData)data.getOrganizationDataList().get(j)).getOrganizationName().toLowerCase().contains(searchText.toLowerCase())) {
                     orgCheck = true;
                  }
               }

               if (orgCheck) {
                  dataList.add(data);
               }
            }
         }

         index = 1;
         List pDataList = new ArrayList();

         for(Iterator var27 = dataList.iterator(); var27.hasNext(); ++index) {
            V2OrganizationGroupResource dataResource = (V2OrganizationGroupResource)var27.next();
            if (startIndex <= index && startIndex + results > index) {
               pDataList.add(dataResource);
            }
         }

         V2PageResource resource = V2PageResource.createPageResource(pDataList, dataList.size());
         return resource;
      }
   }

   /** @deprecated */
   @Deprecated
   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public List additionGroupInquiry() throws SQLException {
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      userGroupInfo.getAllOrganizationGroup();
      List orgList = userGroupInfo.getAllOrganizationGroup();
      List tempList = ConvertUtil.convertList(orgList);
      return tempList;
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2SaveGroupInquiryResource saveGroupInquiry(V2SaveGroupInquiryFilter body, HttpServletRequest request) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String roleName = userContainer.getUser().getRole_name();
      if (!roleName.equalsIgnoreCase("Server Administrator")) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CONNECT_ONLY_SERVER_ADMIN);
      } else {
         V2SaveGroupInquiryResource newResource = new V2SaveGroupInquiryResource();
         String organizationGroupName = body.getOrganizationGroupName();
         ArrayList organizationIds = body.getOrganizationIds();
         List dataList = new ArrayList();
         dataList.add(organizationGroupName);
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         List list = userGroupInfo.getAllOrganizationGroup();

         for(int i = 0; i < organizationIds.size(); ++i) {
            boolean organIdCheck = false;

            for(int j = 0; j < list.size(); ++j) {
               Map orgInfoMap = (Map)list.get(j);
               new HashMap();
               if (((Long)organizationIds.get(i)).toString().equalsIgnoreCase(orgInfoMap.get("group_id").toString())) {
                  organIdCheck = true;
               }
            }

            if (!organIdCheck) {
               throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"organization Id"});
            }

            RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, (Long)organizationIds.get(i));
            dataList.add(((Long)organizationIds.get(i)).toString());
         }

         if (organizationGroupName.isEmpty()) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_ENTER_NAME);
         } else if (organizationIds.size() < 2) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SELECT_MORE_THAN_2_ORGANIZATIONS);
         } else {
            OrganizationGroupInfo organizationGroupInfo = OrganizationGroupInfoImpl.getInstance();
            Map paramsMap = new HashMap();
            List allOrgList = organizationGroupInfo.getAllOrganizationGroupList(paramsMap);
            Iterator var19 = allOrgList.iterator();

            OrganizationGroup organizationGroup;
            do {
               if (!var19.hasNext()) {
                  String organizationGroupId = organizationGroupInfo.addNewOrgGroup(dataList, request.getRemoteAddr());
                  if (organizationGroupId != null) {
                     newResource.setOrganizationGroupName(organizationGroupName);
                     newResource.setOrganizationIds(organizationIds);
                     newResource.setOrganizationGroupId(organizationGroupId);
                     return newResource;
                  }

                  throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
               }

               organizationGroup = (OrganizationGroup)var19.next();
            } while(!organizationGroup.getMng_org_group_name().equals(organizationGroupName));

            throw new BaseRestException("INVALID_PARAM", "DID_SCHEDULE_DUPLICATENAME");
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2OrganizationGroupResource editGroupInquiry(String organizationGroupId) throws Exception {
      V2OrganizationGroupResource resource = new V2OrganizationGroupResource();
      OrganizationGroupInfo organizationGroupInfo = OrganizationGroupInfoImpl.getInstance();
      String organizationGroupName = organizationGroupInfo.getOrganizationGroupNameByOrganizationGroupId(Long.valueOf(organizationGroupId));
      List orgList = organizationGroupInfo.getMngOrgListByOrganizationGroupId(Long.valueOf(organizationGroupId));
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      List allOrgList = userGroupInfo.getAllOrganizationGroup();
      ArrayList resultOrgList = new ArrayList();
      Iterator var9 = allOrgList.iterator();

      while(var9.hasNext()) {
         Map orgGroup = (Map)var9.next();
         V2CommonOrganizationData data = new V2CommonOrganizationData();
         if (orgList.contains(orgGroup.get("group_name"))) {
            data.setOrganizationName((String)orgGroup.get("group_name"));
            data.setOrganizationId((Long)orgGroup.get("group_id"));
            resultOrgList.add(data);
         }
      }

      resource.setOrganizationGroupId(Long.valueOf(organizationGroupId));
      resource.setOrganizationGroupName(organizationGroupName);
      resource.setOrganizationDataList(resultOrgList);
      return resource;
   }

   private V2SaveGroupInquiryResource checkMUUserResoruce(String organizationGroupId, String organizationGroupName, ArrayList organizationIds) throws Exception {
      OrganizationGroupInfo organizationGroupInfo = OrganizationGroupInfoImpl.getInstance();
      V2SaveGroupInquiryResource newResource = new V2SaveGroupInquiryResource();
      List orgList = organizationGroupInfo.getMngOrgListByOrganizationGroupId(Long.valueOf(organizationGroupId));
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      List allOrgList = userGroupInfo.getAllOrganizationGroup();
      List originOrgIds = (List)allOrgList.stream().filter((o) -> {
         return orgList.contains(o.get("group_name"));
      }).map((o) -> {
         return (Long)o.get("group_id");
      }).collect(Collectors.toList());
      List checkOrgIds = (List)originOrgIds.stream().filter((o) -> {
         return !organizationIds.contains(o);
      }).collect(Collectors.toList());
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
      List adminUsers = new ArrayList();
      List userIdList = organizationGroupInfo.getUserIdListByMngOrganizationGroupId(Long.valueOf(organizationGroupId));
      Iterator var15 = userIdList.iterator();

      label31:
      while(var15.hasNext()) {
         String userId = (String)var15.next();
         Iterator var17 = checkOrgIds.iterator();

         while(true) {
            Long orgId;
            long contentCnt;
            long playlistCnt;
            do {
               if (!var17.hasNext()) {
                  continue label31;
               }

               orgId = (Long)var17.next();
               contentCnt = contentInfo.getCntAllContents(userId, orgId);
               playlistCnt = playlistInfo.getCntAllPlaylists(userId, orgId);
            } while(contentCnt <= 0L && playlistCnt <= 0L);

            LinkedHashMap map = UserUtils.makeAdminInfo(orgId);
            V2AdminInfoResource admin = new V2AdminInfoResource();
            admin.setOrganizationName((String)map.get("organization"));
            admin.setUserName((String)map.get("name"));
            admin.setEmail((String)map.get("email"));
            adminUsers.add(admin);
         }
      }

      if (!adminUsers.isEmpty()) {
         newResource.setOrganizationGroupName(organizationGroupName);
         newResource.setOrganizationIds(organizationIds);
         newResource.setOrganizationGroupId(organizationGroupId);
         newResource.setAdminUsers(adminUsers);
         return newResource;
      } else {
         return newResource;
      }
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2SaveGroupInquiryResource saveEditGroupInquiry(String organizationGroupId, V2SaveGroupInquiryFilter filter, HttpServletRequest request) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String roleName = userContainer.getUser().getRole_name();
      if (!roleName.equalsIgnoreCase("Server Administrator")) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CONNECT_ONLY_SERVER_ADMIN);
      } else {
         V2SaveGroupInquiryResource newResource = new V2SaveGroupInquiryResource();
         String organizationGroupName = filter.getOrganizationGroupName();
         ArrayList organizationIds = filter.getOrganizationIds();
         boolean flag = false;

         for(int i = 0; i < organizationIds.size(); ++i) {
            try {
               RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, (Long)organizationIds.get(i));
            } catch (Exception var17) {
               flag = true;
               break;
            }
         }

         if (flag) {
            RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER);
         }

         OrganizationGroupInfo organizationGroupInfo = OrganizationGroupInfoImpl.getInstance();
         if (filter.getCheckHasResources() == null || filter.getCheckHasResources()) {
            V2SaveGroupInquiryResource tmpResource = this.checkMUUserResoruce(organizationGroupId, organizationGroupName, organizationIds);
            if (tmpResource.getAdminUsers() != null && !tmpResource.getAdminUsers().isEmpty()) {
               return tmpResource;
            }
         }

         List dataList = new ArrayList();
         dataList.add(organizationGroupId);
         dataList.add(organizationGroupName);
         UserInfo userInfo = UserInfoImpl.getInstance();
         String orgGroupName = organizationGroupInfo.getOrganizationGroupNameByOrganizationGroupId(Long.valueOf(organizationGroupId));
         if (orgGroupName == null) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"organizationGroupId"});
         } else {
            Iterator var14 = organizationIds.iterator();

            while(var14.hasNext()) {
               Long organizationId = (Long)var14.next();
               String organizationName = userInfo.getOrganNameByRootGroupId(organizationId);
               if (organizationName == null) {
                  throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"organizationId"});
               }

               dataList.add(String.valueOf(organizationId));
            }

            if (organizationGroupName.isEmpty()) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_ENTER_NAME, new String[]{"organizationGroupName"});
            } else if (dataList.size() < 4) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SELECT_MORE_THAN_2_ORGANIZATIONS);
            } else {
               Boolean ret = organizationGroupInfo.editOrganizationGroup(dataList, request.getRemoteAddr());
               if (ret) {
                  newResource.setOrganizationGroupName(organizationGroupName);
                  newResource.setOrganizationIds(organizationIds);
                  newResource.setOrganizationGroupId(organizationGroupId);
                  return newResource;
               } else {
                  throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SAVE_FAIL, new String[]{"organizationGroup"});
               }
            }
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2CommonBulkResultResource deleteGroupInquiry(V2CommonIds body, HttpServletRequest request) throws Exception {
      List organizationGroupIds = body.getIds();
      OrganizationGroupInfo organizationGroupInfo = OrganizationGroupInfoImpl.getInstance();
      Iterator var5 = organizationGroupIds.iterator();

      while(var5.hasNext()) {
         String organizationGroupId = (String)var5.next();
         List orgIdList = organizationGroupInfo.getMngOrgIdListByOrganizationGroupId(Long.valueOf(organizationGroupId));
         if (orgIdList.size() == 0) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"organizationGroupId"});
         }
      }

      Map paramsMap = new HashMap();
      List allOrgList = organizationGroupInfo.getAllOrganizationGroupList(paramsMap);
      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      List deletableOrgIdList = new ArrayList();

      for(int i = 0; i < organizationGroupIds.size(); ++i) {
         long count = 0L;
         boolean orgGroupIdCheck = false;
         String orgGroupId = (String)organizationGroupIds.get(i);

         V2CommonDeleteFail item;
         try {
            count = organizationGroupInfo.getCountUserByMngOrganizationGroupId(Long.valueOf((String)organizationGroupIds.get(i)));
            if (count > 0L) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_USED_ITEM_NOT_DELETE, new String[]{"organizationGroupId"});
            }

            Iterator var16 = allOrgList.iterator();

            while(var16.hasNext()) {
               OrganizationGroup organizationGroup = (OrganizationGroup)var16.next();
               if (organizationGroup.getMng_org_group_id().equals(orgGroupId)) {
                  orgGroupIdCheck = true;
               }
            }

            if (!orgGroupIdCheck) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"organizationGroupId"});
            }

            deletableOrgIdList.add(orgGroupId);
         } catch (RestServiceException var18) {
            item = new V2CommonDeleteFail();
            item.setId(orgGroupId);
            item.setReason(var18.getErrorMessage());
            item.setReasonCode(var18.getErrorCode());
            failList.add(item);
         } catch (Exception var19) {
            item = new V2CommonDeleteFail();
            item.setId(orgGroupId);
            item.setReason(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN.getMessage());
            item.setReasonCode(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN.getCode());
            failList.add(item);
         }
      }

      List mngOrgIdList = new ArrayList();
      Iterator var24 = deletableOrgIdList.iterator();

      while(var24.hasNext()) {
         String s = (String)var24.next();
         mngOrgIdList.add(Long.valueOf(s));
      }

      Boolean ret = organizationGroupInfo.deleteOrganizationGroupByOrganizationGroupId(mngOrgIdList, request.getRemoteAddr());
      if (ret) {
         successList.addAll(deletableOrgIdList);
      } else {
         Iterator var26 = deletableOrgIdList.iterator();

         while(var26.hasNext()) {
            String id = (String)var26.next();
            V2CommonDeleteFail item = new V2CommonDeleteFail();
            item.setId(id);
            item.setReason(RestExceptionCode.INTERNAL_SERVER_ERROR_DELETE_FAIL.getMessage());
            item.setReasonCode(RestExceptionCode.INTERNAL_SERVER_ERROR_DELETE_FAIL.getCode());
            failList.add(item);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2DeleteCheckGroupInquiryResource deleteCheckGroupInquiry(V2CommonIds body) throws Exception {
      V2DeleteCheckGroupInquiryResource resource = new V2DeleteCheckGroupInquiryResource();
      List UndeletableDatas = new ArrayList();
      List deletableOrgGroupIds = new ArrayList();
      long count = 0L;
      List organizationGroupIds = body.getIds();
      OrganizationGroupInfo organizationGroupInfo = OrganizationGroupInfoImpl.getInstance();
      Iterator var9 = organizationGroupIds.iterator();

      List users;
      do {
         String organizationGroupId;
         if (!var9.hasNext()) {
            var9 = organizationGroupIds.iterator();

            while(var9.hasNext()) {
               organizationGroupId = (String)var9.next();
               count = organizationGroupInfo.getCountUserByMngOrganizationGroupId(Long.valueOf(organizationGroupId));
               if (count > 0L) {
                  users = organizationGroupInfo.getUserIdListByMngOrganizationGroupId(Long.valueOf(organizationGroupId));
                  V2UnDeletableOrgGroupData data = new V2UnDeletableOrgGroupData();
                  data.setMuUserIds(users);
                  data.setUnDeletableOrgGroupId(organizationGroupId);
                  UndeletableDatas.add(data);
               } else {
                  deletableOrgGroupIds.add(organizationGroupId);
               }
            }

            resource.setDeletableOrgGroupIds(deletableOrgGroupIds);
            resource.setUndeletableData(UndeletableDatas);
            return resource;
         }

         organizationGroupId = (String)var9.next();
         users = organizationGroupInfo.getMngOrgIdListByOrganizationGroupId(Long.valueOf(organizationGroupId));
      } while(users.size() != 0);

      throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"organization Id"});
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public List getAdminInfo(V2CommonIds body) throws Exception {
      List dataList = new ArrayList();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      List userIds = body.getIds();
      List orgList = new ArrayList();
      boolean flag = false;

      for(int i = 0; i < userIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, (String)userIds.get(i));
         } catch (Exception var22) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER);
      }

      Iterator var23 = userIds.iterator();

      label71:
      while(true) {
         String userId;
         List list;
         do {
            label54:
            do {
               while(var23.hasNext()) {
                  userId = (String)var23.next();
                  User user = userInfo.getUserByUserId(userId);
                  if (user != null && user.isMu()) {
                     list = userGroupInfo.getMngGroupListByUserId(userId);
                     continue label54;
                  }

                  if (user != null) {
                     long contentCnt = contentInfo.getCntAllContents(userId, user.getRoot_group_id());
                     long playlistCnt = playlistInfo.getCntAllPlaylists(userId, user.getRoot_group_id());
                     if ((contentCnt > 0L || playlistCnt > 0L) && !orgList.contains(user.getRoot_group_id())) {
                        LinkedHashMap map = UserUtils.makeAdminInfo(user.getRoot_group_id());
                        V2AdminInfoResource data = new V2AdminInfoResource();
                        data.setOrganizationName((String)map.get("organization"));
                        data.setUserName((String)map.get("name"));
                        data.setEmail((String)map.get("email"));
                        dataList.add(data);
                        orgList.add(user.getRoot_group_id());
                     }
                  }
               }

               return dataList;
            } while(list == null);
         } while(list.size() <= 0);

         Iterator var14 = list.iterator();

         while(true) {
            long contentCnt;
            UserGroup userGroup;
            long playlistCnt;
            do {
               if (!var14.hasNext()) {
                  continue label71;
               }

               userGroup = (UserGroup)var14.next();
               contentCnt = contentInfo.getCntAllContents(userId, userGroup.getRoot_group_id());
               playlistCnt = playlistInfo.getCntAllPlaylists(userId, userGroup.getRoot_group_id());
            } while(contentCnt <= 0L && playlistCnt <= 0L);

            if (!orgList.contains(userGroup.getRoot_group_id())) {
               LinkedHashMap map = UserUtils.makeAdminInfo(userGroup.getRoot_group_id());
               V2AdminInfoResource data = new V2AdminInfoResource();
               data.setOrganizationName((String)map.get("organization"));
               data.setUserName((String)map.get("name"));
               data.setEmail((String)map.get("email"));
               dataList.add(data);
               orgList.add(userGroup.getRoot_group_id());
            }
         }
      }
   }

   public List convertList(List list) {
      List tempList = new ArrayList();
      String tempKey = "";
      Set keys = ((Map)list.get(0)).keySet();
      ConvertUtil convertUtil = new ConvertUtil();

      for(int i = 0; i < list.size(); ++i) {
         Map map = new HashMap();
         Iterator var8 = keys.iterator();

         while(var8.hasNext()) {
            String key = (String)var8.next();
            tempKey = ConvertUtil.camelCase(key);
            map.put(tempKey, String.valueOf(((Map)list.get(i)).get(key)));
         }

         tempList.add(map);
      }

      return tempList;
   }
}
