package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.GetServerSystemInfoOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.OpenAPISystemInfoRepository;
import com.samsung.magicinfo.webauthor2.repository.model.ServerSystemInfoData;
import com.samsung.magicinfo.webauthor2.util.UserData;
import javax.inject.Inject;
import org.springframework.stereotype.Repository;
import org.springframework.web.client.RestTemplate;

@Repository
public class OpenAPISystemInfoRepositoryImpl implements OpenAPISystemInfoRepository {
  private final RestTemplate restTemplate;
  
  private final UserData userData;
  
  @Inject
  public OpenAPISystemInfoRepositoryImpl(RestTemplate restTemplate, UserData userData) {
    this.restTemplate = restTemplate;
    this.userData = userData;
  }
  
  public ServerSystemInfoData getServerSystemInfo() {
    GetServerSystemInfoOpenApiMethod openApiMethod = new GetServerSystemInfoOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken());
    return (ServerSystemInfoData)openApiMethod.callMethod();
  }
}
