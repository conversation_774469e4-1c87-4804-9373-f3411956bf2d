package com.samsung.magicinfo.framework.content.dao;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.DeleteContentUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.content.constants.ContentConstants;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Effect;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.entity.PlaylistSearch;
import com.samsung.magicinfo.framework.content.entity.PlaylistTag;
import com.samsung.magicinfo.framework.content.entity.SyncPlaylist;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistSearchInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistSearchInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.role.manager.AbilityInfo;
import com.samsung.magicinfo.framework.role.manager.AbilityInfoImpl;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfo;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.entity.ContentTagEntity;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.Map.Entry;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class PlaylistDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(PlaylistDao.class);
   ContentInfo cmsDao = ContentInfoImpl.getInstance();

   public PlaylistDao() {
      super();
   }

   public Group getGroup(int groupId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getGroup(new Long((long)groupId));
   }

   public String getPlaylistName(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistName(playlistId);
   }

   public List getPlaylistAllVerInfo(String playlistId) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getPlaylistAllVerInfo(playlistId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public Playlist getPlaylistActiveVerInfo(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistActiveVerInfo(playlistId);
   }

   public List getPlaylistActiveVerInfoForSync(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistActiveVerInfoForSync(playlistId);
   }

   public Long getPlaylistActiveVersionId(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistActiveVersionId(playlistId);
   }

   public Playlist getPlaylistVerInfo(String playlistId, Long versionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistVerInfo(playlistId, versionId);
   }

   public List getContentListOfPlaylist(String playlistId, Long versionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getContentListOfPlaylist(playlistId, versionId);
   }

   public String getTagConditionIdWithTagNumber(String playlistId, Long versionId, Long tagId, String number_str) throws SQLException {
      List rtn = new ArrayList();
      if (number_str != null && !number_str.equals("")) {
         String[] tempConditionList = number_str.split(",");
         String[] var25 = tempConditionList;
         int var27 = tempConditionList.length;

         for(int var9 = 0; var9 < var27; ++var9) {
            String temp = var25[var9];
            List equals = new ArrayList();
            List ups = new ArrayList();
            List downs = new ArrayList();
            String tagCondition = temp.trim();
            String regexInequality = "^([0-9]*)(-)([0-9]*)$";
            String regexNumber = "^([0-9]*)$";
            String[] list;
            if (tagCondition.matches(regexInequality)) {
               list = null;
               if (tagCondition.indexOf("-") > 0) {
                  list = tagCondition.split("-");
                  ups.add(list[1]);
                  downs.add(list[0]);
               }
            } else if (tagCondition.matches(regexNumber)) {
               equals.add(tagCondition);
            }

            list = (String[])equals.toArray(new String[equals.size()]);
            String[] tagConditionUp = (String[])ups.toArray(new String[ups.size()]);
            String[] tagConditionDown = (String[])downs.toArray(new String[downs.size()]);
            List tagConditions = ((PlaylistDaoMapper)this.getMapper()).getConditionIdWithTagNumber(Long.valueOf(tagId), list, tagConditionUp, tagConditionDown);
            if (tagConditions != null) {
               Iterator var21 = tagConditions.iterator();

               while(var21.hasNext()) {
                  Map map = (Map)var21.next();
                  if (map != null && map.get("TAG_CONDITION_ID") != null && !rtn.contains(String.valueOf((Long)map.get("TAG_CONDITION_ID")))) {
                     rtn.add(String.valueOf((Long)map.get("TAG_CONDITION_ID")));
                  }
               }
            }
         }
      } else {
         List tagConditions = ((PlaylistDaoMapper)this.getMapper()).getConditionIdWithTagNumber(Long.valueOf(tagId), (String[])null, (String[])null, (String[])null);
         if (tagConditions != null) {
            Iterator var7 = tagConditions.iterator();

            while(var7.hasNext()) {
               Map map = (Map)var7.next();
               if (map != null && map.get("TAG_CONDITION_ID") != null && !rtn.contains(String.valueOf((Long)map.get("TAG_CONDITION_ID")))) {
                  rtn.add(String.valueOf((Long)map.get("TAG_CONDITION_ID")));
               }
            }
         }
      }

      String returnStr = "";
      if (rtn != null && rtn.size() > 0) {
         for(int i = 0; i < rtn.size(); ++i) {
            if (i > 0) {
               returnStr = returnStr + ",";
            }

            returnStr = returnStr + (String)rtn.get(i);
         }
      }

      return returnStr;
   }

   public List getTagConditionWithTagIdList(String playlistId, Long versionId, Long tagId, String number_str) throws SQLException {
      List tagConditionList = new ArrayList();
      List equals = new ArrayList();
      List ups = new ArrayList();
      List downs = new ArrayList();
      String[] conditionIdList;
      int var12;
      String tagCondition;
      if (number_str != null && !number_str.equals("")) {
         String[] tempConditionList = number_str.split(",");
         conditionIdList = tempConditionList;
         int var28 = tempConditionList.length;

         for(var12 = 0; var12 < var28; ++var12) {
            String temp = conditionIdList[var12];
            tagCondition = temp.trim();
            String regexInequality = "^([0-9]*)(-)([0-9]*)$";
            String regexNumber = "^([0-9]*)$";
            String[] list;
            if (tagCondition.matches(regexInequality)) {
               list = null;
               if (tagCondition.indexOf("-") > 0) {
                  list = tagCondition.split("-");
                  ups.add(list[1]);
                  downs.add(list[0]);
               }
            } else if (tagCondition.matches(regexNumber)) {
               equals.add(tagCondition);
            }

            list = (String[])equals.toArray(new String[equals.size()]);
            String[] tagConditionUp = (String[])ups.toArray(new String[ups.size()]);
            String[] tagConditionDown = (String[])downs.toArray(new String[downs.size()]);
            String str = this.getConditionIdWithTagNumber(Long.valueOf(tagId), list, tagConditionUp, tagConditionDown);
            if (str == null) {
               return null;
            }

            String[] conditionIdList = str.split(",");
            String[] var22 = conditionIdList;
            int var23 = conditionIdList.length;

            for(int var24 = 0; var24 < var23; ++var24) {
               String id = var22[var24];
               Map map = new HashMap();
               map.put("tag_id", tagId);
               id = id.trim();
               map.put("tag_condition_id", Long.valueOf(id));
               tagConditionList.add(map);
            }
         }
      } else {
         String str = this.getConditionIdWithTagNumber(Long.valueOf(tagId), (String[])null, (String[])null, (String[])null);
         if (str == null) {
            return null;
         }

         conditionIdList = str.split(",");
         String[] var11 = conditionIdList;
         var12 = conditionIdList.length;

         for(int var13 = 0; var13 < var12; ++var13) {
            tagCondition = var11[var13];
            Map map = new HashMap();
            map.put("tag_id", tagId);
            tagCondition = tagCondition.trim();
            map.put("tag_condition_id", Long.valueOf(tagCondition));
            tagConditionList.add(map);
         }
      }

      return ((PlaylistDaoMapper)this.getMapper()).getTagContentListOfPlaylist(tagConditionList);
   }

   public int getTagConditionWithTagIdListSize(String playlistId, Long versionId, Long tagId, String number_str) throws SQLException {
      List tagConditionList = new ArrayList();
      List equals = new ArrayList();
      List ups = new ArrayList();
      List downs = new ArrayList();
      String[] conditionIdList;
      int var12;
      String tagCondition;
      if (number_str != null && !number_str.equals("")) {
         String[] tempConditionList = number_str.split(",");
         conditionIdList = tempConditionList;
         int var28 = tempConditionList.length;

         for(var12 = 0; var12 < var28; ++var12) {
            String temp = conditionIdList[var12];
            tagCondition = temp.trim();
            String regexInequality = "^([0-9]*)(-)([0-9]*)$";
            String regexNumber = "^([0-9]*)$";
            String[] list;
            if (tagCondition.matches(regexInequality)) {
               list = null;
               if (tagCondition.indexOf("-") > 0) {
                  list = tagCondition.split("-");
                  ups.add(list[1]);
                  downs.add(list[0]);
               }
            } else if (tagCondition.matches(regexNumber)) {
               equals.add(tagCondition);
            }

            list = (String[])equals.toArray(new String[equals.size()]);
            String[] tagConditionUp = (String[])ups.toArray(new String[ups.size()]);
            String[] tagConditionDown = (String[])downs.toArray(new String[downs.size()]);
            String str = this.getConditionIdWithTagNumber(Long.valueOf(tagId), list, tagConditionUp, tagConditionDown);
            if (str == null) {
               return 0;
            }

            String[] conditionIdList = str.split(",");
            String[] var22 = conditionIdList;
            int var23 = conditionIdList.length;

            for(int var24 = 0; var24 < var23; ++var24) {
               String id = var22[var24];
               Map map = new HashMap();
               map.put("tag_id", tagId);
               id = id.trim();
               map.put("tag_condition_id", Long.valueOf(id));
               tagConditionList.add(map);
            }
         }
      } else {
         String str = this.getConditionIdWithTagNumber(Long.valueOf(tagId), (String[])null, (String[])null, (String[])null);
         if (str == null) {
            return 0;
         }

         conditionIdList = str.split(",");
         String[] var11 = conditionIdList;
         var12 = conditionIdList.length;

         for(int var13 = 0; var13 < var12; ++var13) {
            tagCondition = var11[var13];
            Map map = new HashMap();
            map.put("tag_id", tagId);
            tagCondition = tagCondition.trim();
            map.put("tag_condition_id", Long.valueOf(tagCondition));
            tagConditionList.add(map);
         }
      }

      return ((PlaylistDaoMapper)this.getMapper()).getTagContentListOfPlaylistSize(tagConditionList);
   }

   public List getContentListFromTagId(List parameters) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getContentListFromTagId(parameters);
   }

   public List getTagConditionWithTagIdList(String playlistId, Long versionId, Long tagId) throws SQLException {
      List tagConditionList = ((PlaylistDaoMapper)this.getMapper()).getTagConditionWithTagIdList(playlistId, versionId, tagId);
      List checkList = new ArrayList();
      Iterator itr = tagConditionList.iterator();

      Map map;
      while(itr.hasNext()) {
         map = (Map)itr.next();
         if (map.get("tag_condition_id") != null) {
            checkList.add((Long)map.get("tag_id"));
         }
      }

      itr = tagConditionList.iterator();

      while(itr.hasNext()) {
         map = (Map)itr.next();
         if (checkList == null || checkList.size() <= 0) {
            break;
         }

         Iterator var8 = checkList.iterator();

         while(var8.hasNext()) {
            Long tagIdTemp = (Long)var8.next();
            if ((Long)map.get("tag_id") == tagIdTemp && map.get("tag_condition_id") == null) {
               itr.remove();
               break;
            }
         }
      }

      return ((PlaylistDaoMapper)this.getMapper()).getTagContentListOfPlaylist(tagConditionList);
   }

   public int getTagConditionWithTagIdListSize(String playlistId, Long versionId, Long tagId) throws SQLException {
      List tagConditionList = ((PlaylistDaoMapper)this.getMapper()).getTagConditionWithTagIdList(playlistId, versionId, tagId);
      List checkList = new ArrayList();
      Iterator itr = tagConditionList.iterator();

      Map map;
      while(itr.hasNext()) {
         map = (Map)itr.next();
         if (map.get("tag_condition_id") != null) {
            checkList.add((Long)map.get("tag_id"));
         }
      }

      itr = tagConditionList.iterator();

      while(itr.hasNext()) {
         map = (Map)itr.next();
         if (checkList == null || checkList.size() <= 0) {
            break;
         }

         Iterator var8 = checkList.iterator();

         while(var8.hasNext()) {
            Long tagIdTemp = (Long)var8.next();
            if ((Long)map.get("tag_id") == tagIdTemp && map.get("tag_condition_id") == null) {
               itr.remove();
               break;
            }
         }
      }

      return ((PlaylistDaoMapper)this.getMapper()).getTagContentListOfPlaylistSize(tagConditionList);
   }

   public List getTagContentListOfPlaylist(String playlistId, Long versionId) throws SQLException {
      List tagConditionList = ((PlaylistDaoMapper)this.getMapper()).getTagConditionList(playlistId, versionId);
      List checkList = new ArrayList();
      List finalTagConditionList = new ArrayList();
      finalTagConditionList.addAll(tagConditionList);
      Iterator findMap = finalTagConditionList.iterator();

      while(true) {
         long tagId;
         String[] tagConditionIdList;
         do {
            do {
               String tagConditionIdStr;
               do {
                  Map map;
                  String numberStr;
                  do {
                     do {
                        label104:
                        do {
                           while(findMap.hasNext()) {
                              map = (Map)findMap.next();
                              tagId = (Long)map.get("tag_id");
                              if (map.get("tag_type") != null && (Long)map.get("tag_type") == 1L) {
                                 continue label104;
                              }

                              if (map.get("tag_condition_id") != null && !map.get("tag_condition_id").equals("") && !checkList.contains(tagId)) {
                                 checkList.add(tagId);
                              } else if (map.get("tag_condition_id") != null && map.get("tag_condition_id").equals("null") && !checkList.contains(tagId)) {
                                 checkList.add(tagId);
                              }
                           }

                           Iterator itr = tagConditionList.iterator();

                           label88:
                           while(itr.hasNext()) {
                              Map map = (Map)itr.next();
                              Iterator var9 = checkList.iterator();

                              while(true) {
                                 while(true) {
                                    Long tagId;
                                    do {
                                       if (!var9.hasNext()) {
                                          continue label88;
                                       }

                                       tagId = (Long)var9.next();
                                    } while((Long)map.get("tag_id") != tagId);

                                    if (map.get("tag_condition_id") == null) {
                                       itr.remove();
                                    } else if (map.get("tag_condition_id") != null && (map.get("tag_condition_id").equals("") || map.get("tag_condition_id").equals("null"))) {
                                       itr.remove();
                                    }
                                 }
                              }
                           }

                           if (tagConditionList != null && tagConditionList.size() >= 1) {
                              return ((PlaylistDaoMapper)this.getMapper()).getTagContentListOfPlaylist(tagConditionList);
                           }

                           return null;
                        } while((String)map.get("number_str") == null);

                        numberStr = (String)map.get("number_str");
                     } while(numberStr == null);
                  } while(numberStr.equals(""));

                  tagConditionList.remove(map);
                  List equals = new ArrayList();
                  List ups = new ArrayList();
                  List downs = new ArrayList();
                  String[] tempConditionList = numberStr.split(",");
                  String[] tagConditionEqual = tempConditionList;
                  int var16 = tempConditionList.length;

                  for(int var17 = 0; var17 < var16; ++var17) {
                     tagConditionIdStr = tagConditionEqual[var17];
                     String tagCondition = tagConditionIdStr.trim();
                     String regexInequality = "^([0-9]*)(-)([0-9]*)$";
                     String regexNumber = "^([0-9]*)$";
                     if (tagCondition.matches(regexInequality)) {
                        String[] list = null;
                        if (tagCondition.indexOf("-") > 0) {
                           list = tagCondition.split("-");
                           ups.add(list[1]);
                           downs.add(list[0]);
                        }
                     } else if (tagCondition.matches(regexNumber) && tagCondition != null && !tagCondition.equals("")) {
                        equals.add(tagCondition);
                     }
                  }

                  tagConditionEqual = (String[])equals.toArray(new String[equals.size()]);
                  String[] tagConditionUp = (String[])ups.toArray(new String[ups.size()]);
                  String[] tagConditionDown = (String[])downs.toArray(new String[downs.size()]);
                  tagConditionIdStr = this.getConditionIdWithTagNumber(tagId, tagConditionEqual, tagConditionUp, tagConditionDown);
               } while(tagConditionIdStr == null);

               tagConditionIdList = tagConditionIdStr.split(",");
            } while(tagConditionIdList == null);
         } while(tagConditionIdList.length <= 0);

         String[] var31 = tagConditionIdList;
         int var32 = tagConditionIdList.length;

         for(int var33 = 0; var33 < var32; ++var33) {
            String str = var31[var33];
            str = str.trim();
            Map tagInfo = new HashMap();
            tagInfo.put("tag_id", tagId);
            tagInfo.put("tag_condition_id", Long.valueOf(str));
            tagConditionList.add(tagInfo);
         }
      }
   }

   public int getTagContentListOfPlaylistSize(String playlistId, Long versionId) throws SQLException {
      List tagConditionList = ((PlaylistDaoMapper)this.getMapper()).getTagConditionList(playlistId, versionId);
      List checkList = new ArrayList();
      List finalTagConditionList = new ArrayList();
      finalTagConditionList.addAll(tagConditionList);
      Iterator findMap = finalTagConditionList.iterator();

      while(true) {
         long tagId;
         String[] tagConditionIdList;
         do {
            do {
               String tagConditionIdStr;
               do {
                  Map map;
                  String numberStr;
                  do {
                     do {
                        label102:
                        do {
                           while(findMap.hasNext()) {
                              map = (Map)findMap.next();
                              tagId = (Long)map.get("tag_id");
                              if ((Long)map.get("tag_type") == 1L) {
                                 continue label102;
                              }

                              if (map.get("tag_condition_id") != null && !map.get("tag_condition_id").equals("") && !checkList.contains(tagId)) {
                                 checkList.add(tagId);
                              } else if (map.get("tag_condition_id") != null && map.get("tag_condition_id").equals("null") && !checkList.contains(tagId)) {
                                 checkList.add(tagId);
                              }
                           }

                           Iterator itr = tagConditionList.iterator();

                           label87:
                           while(itr.hasNext()) {
                              Map map = (Map)itr.next();
                              Iterator var9 = checkList.iterator();

                              while(true) {
                                 while(true) {
                                    Long tagId;
                                    do {
                                       if (!var9.hasNext()) {
                                          continue label87;
                                       }

                                       tagId = (Long)var9.next();
                                    } while((Long)map.get("tag_id") != tagId);

                                    if (map.get("tag_condition_id") == null) {
                                       itr.remove();
                                    } else if (map.get("tag_condition_id") != null && (map.get("tag_condition_id").equals("") || map.get("tag_condition_id").equals("null"))) {
                                       itr.remove();
                                    }
                                 }
                              }
                           }

                           if (tagConditionList != null && tagConditionList.size() >= 1) {
                              return ((PlaylistDaoMapper)this.getMapper()).getTagContentListOfPlaylistSize(tagConditionList);
                           }

                           return 0;
                        } while((String)map.get("number_str") == null);

                        numberStr = (String)map.get("number_str");
                     } while(numberStr == null);
                  } while(numberStr.equals(""));

                  tagConditionList.remove(map);
                  List equals = new ArrayList();
                  List ups = new ArrayList();
                  List downs = new ArrayList();
                  String[] tempConditionList = numberStr.split(",");
                  String[] tagConditionEqual = tempConditionList;
                  int var16 = tempConditionList.length;

                  for(int var17 = 0; var17 < var16; ++var17) {
                     tagConditionIdStr = tagConditionEqual[var17];
                     String tagCondition = tagConditionIdStr.trim();
                     String regexInequality = "^([0-9]*)(-)([0-9]*)$";
                     String regexNumber = "^([0-9]*)$";
                     if (tagCondition.matches(regexInequality)) {
                        String[] list = null;
                        if (tagCondition.indexOf("-") > 0) {
                           list = tagCondition.split("-");
                           ups.add(list[1]);
                           downs.add(list[0]);
                        }
                     } else if (tagCondition.matches(regexNumber) && tagCondition != null && !tagCondition.equals("")) {
                        equals.add(tagCondition);
                     }
                  }

                  tagConditionEqual = (String[])equals.toArray(new String[equals.size()]);
                  String[] tagConditionUp = (String[])ups.toArray(new String[ups.size()]);
                  String[] tagConditionDown = (String[])downs.toArray(new String[downs.size()]);
                  tagConditionIdStr = this.getConditionIdWithTagNumber(tagId, tagConditionEqual, tagConditionUp, tagConditionDown);
               } while(tagConditionIdStr == null);

               tagConditionIdList = tagConditionIdStr.split(",");
            } while(tagConditionIdList == null);
         } while(tagConditionIdList.length <= 0);

         String[] var31 = tagConditionIdList;
         int var32 = tagConditionIdList.length;

         for(int var33 = 0; var33 < var32; ++var33) {
            String str = var31[var33];
            str = str.trim();
            Map tagInfo = new HashMap();
            tagInfo.put("tag_id", tagId);
            tagInfo.put("tag_condition_id", Long.valueOf(str));
            tagConditionList.add(tagInfo);
         }
      }
   }

   public List getContentListOfSyncGroup(String playlistId, Long versionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getContentListOfSyncGroup(playlistId, versionId);
   }

   public List getSearchList(Map map) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getSearchList(this.getContentListExtraParams(map));
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getSearchListPage(Map map, int startPos, int pageSize) throws SQLException {
      try {
         Map mapWithParams = this.getContentListExtraParams(map);
         --startPos;
         mapWithParams.put("startPos", startPos);
         mapWithParams.put("pageSize", pageSize);
         return ((PlaylistDaoMapper)this.getMapper()).getSearchListPage(mapWithParams);
      } catch (Exception var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public int getSearchListCnt(Map map) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getSearchListCnt(this.getContentListExtraParams(map));
      } catch (Exception var3) {
         this.logger.error(var3);
         return 0;
      }
   }

   private Map getOrganPlaylistWhereQuery(String creatorID) throws SQLException {
      Map resultMap = new HashMap();
      List lstArg = new ArrayList();
      StringBuffer query = new StringBuffer("");
      UserInfo uInfo = UserInfoImpl.getInstance();
      AbilityInfo abilityInfo = AbilityInfoImpl.getInstance();
      User user = uInfo.getAllByUserId(creatorID);
      String organization = user.getOrganization();
      List abilityList = abilityInfo.getAllAbilityListByUserId(creatorID);
      if (!organization.equalsIgnoreCase("ROOT")) {
         List userList = uInfo.getAllUserByRootGroupId(user.getRoot_group_id());
         if (userList.size() > 0) {
            query.append(" AND (");

            for(int i = 0; i < userList.size(); ++i) {
               Map userMap = (Map)userList.get(i);
               String userName = (String)userMap.get("user_id");
               query.append(" ( ");
               query.append(" A.CREATOR_ID = ? ");
               lstArg.add(userName);
               if (!abilityList.contains("Playlist Manage Authority") && !creatorID.equalsIgnoreCase(userName)) {
                  query.append(" AND A.SHARE_FLAG = ").append(ContentConstants.SHARE_FLAG_YES);
               }

               query.append(" ) ");
               if (i < userList.size() - 1) {
                  query.append(" OR ");
               }
            }

            query.append(") ");
         }
      }

      resultMap.put("query", query.toString());
      resultMap.put("args", lstArg);
      return resultMap;
   }

   public List getPlaylistList(Map map) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getPlaylistList(this.getContentListExtraParams(map));
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getPlaylistList(String userId, int startIndex, int resultsCount) {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getPlaylistListPaged(userId, startIndex, resultsCount);
      } catch (SQLException var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public List getPlaylistListByDeviceType(String userId, int startIndex, int resultsCount, String deviceType, float deviceTypeVersion) {
      return this.getPlaylistListByDeviceType(userId, startIndex, resultsCount, deviceType, deviceTypeVersion, (List)null);
   }

   public List getPlaylistListByDeviceType(String userId, int startIndex, int resultsCount, String deviceType, float deviceTypeVersion, List selectedDeviceType) {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getPlaylistListByDeviceType(userId, startIndex, resultsCount, deviceType, "iPLAYER", deviceTypeVersion, selectedDeviceType);
      } catch (SQLException var8) {
         this.logger.error(var8);
         return null;
      }
   }

   public int getPlaylistListCountByCountDeviceType(String userId, String deviceType, float deviceTypeVersion) {
      return this.getPlaylistListCountByCountDeviceType(userId, deviceType, deviceTypeVersion, (List)null);
   }

   public int getPlaylistListCountByCountDeviceType(String userId, String deviceType, float deviceTypeVersion, List selectedDeviceType) {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getPlaylistListCountByDeviceType(userId, deviceType, "iPLAYER", deviceTypeVersion, selectedDeviceType);
      } catch (SQLException var6) {
         this.logger.error(var6);
         return 0;
      }
   }

   public List getPlaylistListToDeleteContent(String userId, String[] contentIdList, int startIndex, int resultsCount) {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getPlaylistListToDeleteContent(userId, contentIdList, startIndex, resultsCount);
      } catch (SQLException var6) {
         this.logger.error(var6);
         return null;
      }
   }

   public List getPlaylistListPage(Map map, int startPos, int pageSize) throws SQLException {
      try {
         Map mapWithParams = this.getContentListExtraParams(map);
         if (startPos > 0) {
            --startPos;
            mapWithParams.put("startPos", startPos);
         }

         if (pageSize > 0) {
            mapWithParams.put("pageSize", pageSize);
         }

         return ((PlaylistDaoMapper)this.getMapper()).getPlaylistListPage(mapWithParams);
      } catch (Exception var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public int getPlaylistListCnt(Map map) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getPlaylistListCnt(this.getContentListExtraParams(map));
      } catch (Exception var3) {
         this.logger.error(var3);
         return 0;
      }
   }

   public List getAllDeletedPlaylistList(String creatorId) throws SQLException {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         return ((PlaylistDaoMapper)this.getMapper()).getAllDeletedPlaylistList(creatorId, uInfo.getRootGroupIdByUserId(creatorId));
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getAllPlaylistList(String creatorId) throws SQLException {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         return ((PlaylistDaoMapper)this.getMapper()).getAllPlaylistList(creatorId, uInfo.getRootGroupIdByUserId(creatorId));
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getPlaylistListByUser(String creatorId, boolean canReadUnsharedPlaylist) throws SQLException {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         long organization_id = uInfo.getRootGroupIdByUserId(creatorId);
         Long organizationId = organization_id != 0L ? organization_id : null;
         return ((PlaylistDaoMapper)this.getMapper()).getPlaylistListByUser(creatorId, canReadUnsharedPlaylist, organizationId, ContentConstants.SHARE_FLAG_DEFAULT);
      } catch (Exception var7) {
         this.logger.error(var7);
         return null;
      }
   }

   public List getAllPlaylistList(String creatorId, Long groupId) throws SQLException {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
         return ((PlaylistDaoMapper)this.getMapper()).getAllPlaylistListGroup(creatorId, organizationId, groupId);
      } catch (Exception var6) {
         this.logger.error(var6);
         return null;
      }
   }

   public List getContentList(String playlistId, Long versionId) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getContentList(playlistId, versionId);
      } catch (Exception var4) {
         this.logger.error(var4);
         return null;
      }
   }

   public List getTagList(String playlistId, Long versionId) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getTagList(playlistId, versionId);
      } catch (Exception var4) {
         this.logger.error(var4);
         return null;
      }
   }

   public List getTagListWithExpiredDate(String playlistId, Long versionId, String expiredDate) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getTagListWithExpiredDate(playlistId, versionId, expiredDate);
      } catch (Exception var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public int getContentCount(String playlistId, Long versionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getContentCount(playlistId, versionId);
   }

   public int getCountSyncGroup(String playlistId, Long versionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getCountSyncGroup(playlistId, versionId);
   }

   public int getMaxCountSyncContent(String playlistId, Long versionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getMaxCountSyncContent(playlistId, versionId);
   }

   public String getMainContentId(String playlistId) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getMainContentId(playlistId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getActiveVerContentList(String playlistId) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getActiveVerContentList(playlistId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getActiveVerContentListForDownloadCheck(String playlistID) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getActiveVerContentListForDownloadCheck(playlistID);
   }

   public PlaylistContent getContentEffectInfo(String playlistId, Long versionId, String contentId) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getContentEffectInfo(playlistId, versionId, contentId);
      } catch (Exception var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public PlaylistContent getContentEffectInfoByOrder(String playlistId, Long versionId, String contentOrder) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getContentEffectInfoByOrder(playlistId, versionId, Long.parseLong(contentOrder));
      } catch (Exception var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public Effect getEffectInfoByEffectName(String effectName) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getEffectInfoByEffectName(effectName);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public Effect getSocEffectInfoByEffectName(String effectName) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getSocEffectInfoByEffectName(effectName);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public Effect getVWLEffectInfoByEffectName(String effectName) throws SQLException {
      Object object = null;

      try {
         return ((PlaylistDaoMapper)this.getMapper()).getVWLEffectInfoByEffectName(effectName);
      } catch (Exception var4) {
         this.logger.error("", var4);
         return (Effect)object;
      }
   }

   public Boolean isExistPlaylistID(String playlistId) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).countPlaylistID(playlistId) > 0;
      } catch (Exception var3) {
         this.logger.error(var3);
         return false;
      }
   }

   public Boolean isExistPlaylistVersion(String playlistId, Long versionId) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).countPlaylistVersion(playlistId, versionId) > 0;
      } catch (Exception var4) {
         this.logger.error(var4);
         return false;
      }
   }

   public Boolean isUpdatablePlaylist(String playlistId) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).countUpdatablePlaylist(playlistId) > 0;
      } catch (Exception var3) {
         this.logger.error(var3);
         return false;
      }
   }

   public Boolean isDeletablePlaylist(String playlistId, String userId, String sessionId) throws SQLException {
      Boolean ret = false;
      ScheduleInfo sInfo = ScheduleInfoImpl.getInstance();
      RuleSetInfo rulesetDao = RuleSetInfoImpl.getInstance();
      if (rulesetDao.isUsedContentsForRuleset(playlistId)) {
         return false;
      } else {
         if (!this.isLockedPlaylist(playlistId, sessionId)) {
            List list = sInfo.getProgramByPlaylistId(playlistId);
            if (list == null || list.size() == 0) {
               ret = true;
            }
         }

         return ret;
      }
   }

   public Boolean isLockedPlaylist(String playlistId, String sessionId) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).countLockedPlaylist(playlistId, sessionId) > 0;
      } catch (Exception var4) {
         this.logger.error(var4);
         return false;
      }
   }

   public int addPlaylist(Playlist playlist) throws Exception {
      Long version = this.getPlaylistNextVer(playlist.getPlaylist_id());
      String content_version_limit_count = StrUtils.nvl(CommonConfig.get("content.version_limit_count"));
      if (content_version_limit_count.length() < 1) {
         content_version_limit_count = "3";
      }

      long versionCount = Long.parseLong(content_version_limit_count);
      long diffVersion = version - versionCount;
      int cntVersionInDB = this.getCountPlaylistVersionId(playlist.getPlaylist_id());
      if ((long)cntVersionInDB >= versionCount && diffVersion > 0L) {
         long versionId_i;
         if (playlist.getPlaylist_type().equals("5")) {
            for(versionId_i = 1L; versionId_i <= diffVersion; ++versionId_i) {
               this.deleteTagPlaylistVersion(playlist.getPlaylist_id(), Long.toString(versionId_i), playlist.getCreator_id(), playlist.getSession_id());
            }
         } else {
            for(versionId_i = 1L; versionId_i <= diffVersion; ++versionId_i) {
               this.deletePlaylistVersion(playlist.getPlaylist_id(), Long.toString(versionId_i), playlist.getCreator_id(), playlist.getSession_id());
            }
         }
      }

      playlist.setVersion_id(version);
      playlist.setIs_active("Y");
      boolean isExistPlaylistID = this.isExistPlaylistID(playlist.getPlaylist_id());
      boolean tagPlaylist = false;
      if (playlist.getPlaylist_type().equals("5")) {
         tagPlaylist = true;
      }

      List playlistContentToAddList = new ArrayList();
      if (playlist.getArr_content_list() != null) {
         for(int i = 0; i < playlist.getArr_content_list().size(); ++i) {
            PlaylistContent content = (PlaylistContent)playlist.getArr_content_list().get(i);
            content.setVersion_id(version);
            content.setPlaylist_id(playlist.getPlaylist_id());
            if (tagPlaylist || content.getIs_sub_playlist() || this.cmsDao.isExistContentID(content.getContent_id())) {
               playlistContentToAddList.add(content);
            }
         }
      }

      int loopCount = 0;

      boolean isTransationFail;
      do {
         isTransationFail = false;
         ++loopCount;
         SqlSession session = this.openNewSession(false);

         try {
            byte var29;
            if (!isExistPlaylistID && this.addPlaylistInfo(playlist, session) <= 0) {
               session.rollback();
               var29 = -1;
               return var29;
            }

            if (((PlaylistDaoMapper)this.getMapper(session)).addPlaylistVersionInfo(playlist) <= 0) {
               session.rollback();
               var29 = -1;
               return var29;
            }

            ((PlaylistDaoMapper)this.getMapper(session)).setOtherVersionInactive(playlist.getPlaylist_id(), playlist.getVersion_id());
            this.deleteMapGroupPlaylist(playlist.getPlaylist_id(), session);
            if (((PlaylistDaoMapper)this.getMapper(session)).addMapGroupPlaylist(playlist.getPlaylist_id(), playlist.getGroup_id()) <= 0) {
               session.rollback();
               var29 = -1;
               return var29;
            }

            Iterator var15 = playlistContentToAddList.iterator();

            while(var15.hasNext()) {
               PlaylistContent content = (PlaylistContent)var15.next();
               byte var17;
               if (tagPlaylist) {
                  if (this.addMapPlaylistTag(content, session) <= 0) {
                     session.rollback();
                     var17 = -1;
                     return var17;
                  }
               } else if (this.addMapPlaylistContent(content, session) <= 0) {
                  session.rollback();
                  var17 = -1;
                  return var17;
               }
            }

            if (playlist.getTagList() != null) {
               var15 = playlist.getTagList().iterator();

               while(var15.hasNext()) {
                  ContentTagEntity tag = (ContentTagEntity)var15.next();
                  ArrayList tagIdList = tag.getTag_id_list();
                  Iterator var18 = tagIdList.iterator();

                  while(var18.hasNext()) {
                     Integer tagId = (Integer)var18.next();
                     if (tagPlaylist) {
                        ((PlaylistDaoMapper)this.getMapper(session)).setPlaylistTag(playlist.getPlaylist_id(), playlist.getVersion_id(), Long.valueOf(tag.getContent_id()), tag.getContent_order(), tag.getMatch_type(), tagId);
                     } else {
                        this.setContentTag(playlist.getPlaylist_id(), playlist.getVersion_id(), tag.getContent_id(), tag.getContent_order(), tag.getMatch_type(), tagId, session);
                     }
                  }
               }
            }

            int updated = ((PlaylistDaoMapper)this.getMapper(session)).setPlaylistModifiedDate(playlist.getPlaylist_id());
            if (updated <= 0) {
               session.rollback();
               byte var33 = -1;
               return var33;
            }

            if (playlist.getPlaylist_type().equals("3") && playlist.getSync_status_list() != null && playlist.getSync_status_list().size() > 0) {
               Iterator var31 = playlist.getSync_status_list().iterator();

               while(var31.hasNext()) {
                  SyncPlaylist syncPlay = (SyncPlaylist)var31.next();
                  if (((PlaylistDaoMapper)this.getMapper(session)).addMapSyncGroupInfo(syncPlay.getPlaylist_id(), playlist.getVersion_id(), syncPlay.getSync_play_id(), syncPlay.getIs_sync()) <= 0) {
                     session.rollback();
                     byte var36 = -1;
                     return var36;
                  }
               }
            }

            session.commit();
            int var32 = updated;
            return var32;
         } catch (SQLException var23) {
            this.logger.warn(var23);
            this.logger.warn("Transaction failed. Retry transaction");
            isTransationFail = true;
         } finally {
            session.close();
         }
      } while(isTransationFail && loopCount <= 5);

      if (isTransationFail) {
         this.logger.error("Transaction FAILED!!! Retry transaction > 5!");
      }

      return 0;
   }

   public int deleteContentFromPlaylist(String playlistId, String[] contentIdList, String userId, String sessionId) throws SQLException, Exception {
      byte cnt = 0;

      try {
         Playlist playlist = this.getPlaylistActiveVerInfo(playlistId);
         if (playlist == null) {
            return cnt;
         } else {
            List contentList = this.getContentList(playlistId, playlist.getVersion_id());
            Long totalPlayTime = ContentUtils.getPlayTimeStr(playlist.getPlay_time());
            Long totalSize = playlist.getTotal_size();
            TreeMap orderedData = new TreeMap();

            for(int i = 0; i < contentIdList.length; ++i) {
               for(int contentListIndex = 0; contentListIndex < contentList.size(); ++contentListIndex) {
                  ContentInfo cInfo = ContentInfoImpl.getInstance();
                  String contentId = ((PlaylistContent)contentList.get(contentListIndex)).getContent_id();
                  if (contentId.equals(contentIdList[i])) {
                     Content content = cInfo.getContentActiveVerInfo(contentId);
                     totalPlayTime = totalPlayTime - content.getContent_duration();
                     totalSize = totalSize - content.getTotal_size();
                     contentList.remove(contentList.get(contentListIndex));
                     --contentListIndex;
                  } else if (i == contentIdList.length - 1) {
                     orderedData.put(((PlaylistContent)contentList.get(contentListIndex)).getContent_order(), ((PlaylistContent)contentList.get(contentListIndex)).getContent_id());
                  }
               }
            }

            Set orderKey = orderedData.entrySet();
            Iterator itOrderKey = orderKey.iterator();
            long order = 1L;

            while(true) {
               while(itOrderKey.hasNext()) {
                  Entry entry = (Entry)itOrderKey.next();
                  Iterator var16 = contentList.iterator();

                  while(var16.hasNext()) {
                     PlaylistContent playlistContent = (PlaylistContent)var16.next();
                     if (playlistContent.getContent_id().equalsIgnoreCase((String)entry.getValue())) {
                        playlistContent.setContent_order(order);
                        ++order;
                        break;
                     }
                  }
               }

               int cnt;
               if (contentList.size() == 0) {
                  cnt = this.deletePlaylist(playlistId, userId, sessionId);
               } else {
                  playlist.setTotal_size(totalSize);
                  playlist.setPlay_time(ContentUtils.getPlayTimeFormattedStr(totalPlayTime));
                  playlist.setArr_content_list(contentList);
                  playlist.setCreator_id(userId);
                  playlist.setContent_count(contentList.size());
                  cnt = this.addPlaylist(playlist);
               }

               return cnt;
            }
         }
      } catch (SQLException var18) {
         this.logger.error(var18);
         return 0;
      }
   }

   public int addContentToPlaylist(String playlistId, String[] contentIdList, String creatorId) throws SQLException, Exception {
      boolean var4 = false;

      try {
         ContentInfo cInfo = ContentInfoImpl.getInstance();
         Playlist playlist = this.getPlaylistActiveVerInfo(playlistId);
         List contentList = this.getContentList(playlistId, playlist.getVersion_id());
         Long contentOrder = this.getMaxContentOrder(playlistId, playlist.getVersion_id());
         Long totalPlayTime = ContentUtils.getPlayTimeStr(playlist.getPlay_time());
         Long totalSize = playlist.getTotal_size();
         long numofcon = contentOrder + (long)contentIdList.length;
         List contentTagEntityList = this.getPlaylistContentTagList(playlist.getPlaylist_id(), playlist.getVersion_id());
         if (numofcon > 200L) {
            return 0;
         } else {
            for(int i = 0; i < contentIdList.length; ++i) {
               PlaylistContent playlistContent = new PlaylistContent();
               Content content = cInfo.getContentActiveVerInfo(contentIdList[i]);
               if (ContentUtils.checkPlaylistContent(content.getMedia_type())) {
                  playlistContent.setContent_id(contentIdList[i]);
                  if (content.getPlay_time() != null && !content.getPlay_time().equals("") && !content.getPlay_time().equals("-")) {
                     playlistContent.setContent_duration(ContentUtils.getPlayTimeStr(content.getPlay_time()));
                  } else if (content.getContent_duration() == 0L) {
                     playlistContent.setContent_duration(ContentConstants.MINIMUM_CONTENT_DURATION);
                  }

                  contentOrder = contentOrder + 1L;
                  playlistContent.setContent_order(contentOrder);
                  playlistContent.setPlaylist_id(playlistId);
                  totalPlayTime = totalPlayTime + playlistContent.getContent_duration();
                  totalSize = totalSize + content.getTotal_size();
                  contentList.add(playlistContent);
               }
            }

            playlist.setTotal_size(totalSize);
            playlist.setPlay_time(ContentUtils.getPlayTimeFormattedStr(totalPlayTime));
            playlist.setArr_content_list(contentList);
            playlist.setCreator_id(creatorId);
            playlist.setContent_count(contentList.size());
            playlist.setTagList(contentTagEntityList);
            int cnt = this.addPlaylist(playlist);
            return cnt;
         }
      } catch (SQLException var19) {
         this.logger.error(var19);
         return 0;
      }
   }

   private Long getMaxContentOrder(String playlistId, Long versionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getMaxContentOrder(playlistId, versionId);
   }

   private int deleteMapGroupPlaylist(String playlistId, SqlSession session) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).deleteMapGroupPlaylist(playlistId);
   }

   public int setMaxVersionPlaylistActive(String playlistId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var4;
      try {
         Long versionId = ((PlaylistDaoMapper)this.getMapper()).getPlaylistMaxVer(playlistId);
         if (versionId > 0L) {
            if (((PlaylistDaoMapper)this.getMapper(session)).setVersionPlaylistActive(playlistId, versionId) <= 0) {
               session.rollback();
               var4 = -1;
               return var4;
            }

            ((PlaylistDaoMapper)this.getMapper(session)).setOtherVersionInactive(playlistId, versionId);
            session.commit();
            byte var10 = 1;
            return var10;
         }

         session.rollback();
         var4 = -1;
      } catch (SQLException var8) {
         this.logger.error(var8);
         session.rollback();
         var4 = -1;
         return var4;
      } finally {
         session.close();
      }

      return var4;
   }

   private Long getPlaylistNextVer(String playlistId) throws SQLException {
      Long returnValue = ((PlaylistDaoMapper)this.getMapper()).getPlaylistNextVer(playlistId);
      return returnValue != null && returnValue > 0L ? returnValue + 1L : 1L;
   }

   public int addPlaylistInfo(Playlist playlist) throws SQLException {
      SqlSession session = null;

      int var3;
      try {
         session = this.openNewSession(true);
         var3 = this.addPlaylistInfo(playlist, session);
      } catch (SQLException var7) {
         throw var7;
      } finally {
         if (session != null) {
            session.close();
         }

      }

      return var3;
   }

   private int addPlaylistInfo(Playlist playlist, SqlSession session) throws SQLException {
      UserInfo uInfo = UserInfoImpl.getInstance();
      float deviceTypeVersion = CommonDataConstants.TYPE_VERSION_1_0;
      if (playlist.getDevice_type().equals("iPLAYER")) {
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_2_0;
      } else {
         deviceTypeVersion = playlist.getDevice_type_version();
      }

      Map map = new HashMap();
      map.put("playlist_id", playlist.getPlaylist_id());
      map.put("playlist_name", playlist.getPlaylist_name());
      map.put("playlist_meta_data", playlist.getPlaylist_meta_data());
      map.put("share_flag", playlist.getShare_flag());
      map.put("device_type", playlist.getDevice_type());
      map.put("is_deleted", playlist.getIs_deleted());
      map.put("creator_id", playlist.getCreator_id());
      map.put("organization_id", playlist.getOrganization_id());
      map.put("is_vwl", playlist.getIs_vwl());
      map.put("playlist_type", playlist.getPlaylist_type());
      map.put("device_type_version", deviceTypeVersion);
      map.put("default_content_duration", playlist.getDefault_content_duration());
      map.put("ignore_tag", playlist.getIgnore_tag());
      map.put("evenness_playback", playlist.getEvenness_playback());
      return ((PlaylistDaoMapper)this.getMapper(session)).addPlaylistInfo(map);
   }

   public int addPlaylistVersionInfo(Playlist playlist) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).addPlaylistVersionInfo(playlist);
   }

   private int addMapPlaylistContent(PlaylistContent content, SqlSession session) throws SQLException {
      int result = false;
      int result = ((PlaylistDaoMapper)this.getMapper(session)).addMapPlaylistContentInsert(content);
      if (content.getExpired_date() != null || content.getStart_date() != null) {
         result = ((PlaylistDaoMapper)this.getMapper(session)).addMapPlaylistContentUpdate(content);
      }

      return result;
   }

   private int addMapPlaylistTag(PlaylistContent content, SqlSession session) throws SQLException {
      int result = false;
      PlaylistTag tag = new PlaylistTag();
      tag.setPlaylist_id(content.getPlaylist_id());
      tag.setTag_order(content.getTag_order());
      tag.setVersion_id(content.getVersion_id());
      tag.setTag_id(Long.valueOf(content.getContent_id()));
      tag.setContent_duration_milli(content.getContent_duration_milli());
      tag.setTag_duration(content.getTag_duration());
      tag.setExpired_time(content.getExpired_time());
      tag.setStart_time(content.getStart_time());
      tag.setExpired_date(content.getExpired_date());
      tag.setStart_date(content.getStart_date());
      tag.setNumber_str(content.getNumber_str());
      int result = ((PlaylistDaoMapper)this.getMapper(session)).addMapPlaylistTagInsert(tag);
      if (tag.getExpired_date() != null || tag.getStart_date() != null) {
         result = ((PlaylistDaoMapper)this.getMapper(session)).addMapPlaylistTagUpdate(tag);
      }

      return result;
   }

   public int addMapPlaylistContent(PlaylistContent content) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var4;
      try {
         int updated = this.addMapPlaylistContent(content, session);
         session.commit();
         int var10 = updated;
         return var10;
      } catch (SQLException var8) {
         session.rollback();
         var4 = 0;
      } finally {
         session.close();
      }

      return var4;
   }

   public int setPlaylistEffect(PlaylistContent content) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var11;
      try {
         if (((PlaylistDaoMapper)this.getMapper(session)).setPlaylistEffect(content) <= 0) {
            session.rollback();
            byte var10 = -1;
            return var10;
         }

         int updated = ((PlaylistDaoMapper)this.getMapper(session)).setPlaylistModifiedDate(content.getPlaylist_id());
         if (updated > 0) {
            session.commit();
            int var12 = updated;
            return var12;
         }

         session.rollback();
         var11 = -1;
      } catch (SQLException var8) {
         session.rollback();
         byte var4 = 0;
         return var4;
      } finally {
         session.close();
      }

      return var11;
   }

   public int addMapGroupPlaylist(String playlistId, Long groupId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).addMapGroupPlaylist(playlistId, groupId);
   }

   public int setPlaylistInfo(String playlistId, String playlistName, String playlistMetaData, int shareFlag, int ignoreTag, int evenessPlayback) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var8;
      try {
         if (((PlaylistDaoMapper)this.getMapper(session)).setPlaylistInfo(playlistId, playlistName, playlistMetaData, shareFlag, ignoreTag, evenessPlayback) > 0) {
            int updated = ((PlaylistDaoMapper)this.getMapper(session)).setPlaylistModifiedDate(playlistId);
            if (updated <= 0) {
               session.rollback();
               byte var17 = -1;
               return var17;
            }

            session.commit();
            int var16 = updated;
            return var16;
         }

         session.rollback();
         var8 = -1;
      } catch (SQLException var13) {
         session.rollback();
         byte var9 = 0;
         return var9;
      } finally {
         session.close();
      }

      return var8;
   }

   public int setActiveVersion(String playlistId, Long versionId) throws Exception {
      SqlSession session = this.openNewSession(false);

      byte var6;
      try {
         byte var12;
         if (((PlaylistDaoMapper)this.getMapper(session)).setVersionPlaylistActive(playlistId, versionId) <= 0) {
            session.rollback();
            var12 = -1;
            return var12;
         }

         ((PlaylistDaoMapper)this.getMapper(session)).setOtherVersionInactive(playlistId, versionId);
         if (((PlaylistDaoMapper)this.getMapper(session)).setPlaylistModifiedDate(playlistId) <= 0) {
            session.rollback();
            var12 = -1;
            return var12;
         }

         session.commit();
         ScheduleInfo sInfo = ScheduleInfoImpl.getInstance();
         sInfo.setPlaylistTrigger(playlistId);
         EventInfo eInfo = EventInfoImpl.getInstance();
         eInfo.setPlaylistTrigger(playlistId);
         var6 = 1;
      } catch (SQLException var10) {
         session.rollback();
         byte var5 = -1;
         return var5;
      } finally {
         session.close();
      }

      return var6;
   }

   public void updateParentPlaylists(String nestedPlaylistId, Long changeInTotalTime, Long changeInTotalSize) throws Exception {
      try {
         List parents = ((PlaylistDaoMapper)this.getMapper()).getPlaylistListBySubPlaylist(nestedPlaylistId);
         PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();
         Long totalTime = 0L;
         Long totalSize = 0L;

         for(int i = 0; i < parents.size(); ++i) {
            String playlistId = (String)((Map)parents.get(i)).get("PLAYLIST_ID");
            Long versionId = (Long)((Map)parents.get(i)).get("VERSION_ID");
            List nestedPlaylists = pInfo.getSubPlaylistContentOrderListOfPlaylist(playlistId, versionId);
            totalTime = ContentUtils.getPlayTimeStr(((Map)parents.get(i)).get("PLAY_TIME").toString());
            totalSize = (Long)((Map)parents.get(i)).get("TOTAL_SIZE");

            for(int j = 0; j < nestedPlaylists.size(); ++j) {
               if (((PlaylistContent)nestedPlaylists.get(j)).getContent_id().equals(nestedPlaylistId)) {
                  totalTime = totalTime + changeInTotalTime;
                  totalSize = totalSize + changeInTotalSize;
               }
            }

            pInfo.setTotalSize(playlistId, versionId, totalSize);
            pInfo.setPlaytime(playlistId, versionId, ContentUtils.getPlayTimeFormattedStr(totalTime));
         }

      } catch (SQLException var13) {
         this.logger.error("", var13);
      }
   }

   public int deletePlaylist(String playlistId, String userId, String sessionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var8;
      try {
         PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();
         Long curActiveVersion = pInfo.getPlaylistActiveVersionId(playlistId);
         Playlist playlist = pInfo.getPlaylistVerInfo(playlistId, curActiveVersion);
         if (((PlaylistDaoMapper)this.getMapper(session)).deletePlaylistChangeStatus(playlistId) > 0) {
            Long groupId = this.getRootId(userId);
            byte var21;
            if (((PlaylistDaoMapper)this.getMapper(session)).deletePlaylistChangeGroup(playlistId, groupId) <= 0) {
               session.rollback();
               var21 = -1;
               return var21;
            }

            if (((PlaylistDaoMapper)this.getMapper(session)).setPlaylistModifiedDate(playlistId) <= 0) {
               session.rollback();
               var21 = -1;
               return var21;
            }

            session.commit();
            this.setPlaylistUnlockBySessionID(sessionId);
            Long oldTotalPlayTime = 0L;
            Long oldTotalSize = 0L;
            if ("6".equals(playlist.getPlaylist_type())) {
               oldTotalSize = playlist.getTotal_size();
               oldTotalPlayTime = ContentUtils.getPlayTimeStr(playlist.getPlay_time());

               try {
                  this.updateParentPlaylists(playlistId, 0L - oldTotalPlayTime, 0L - oldTotalSize);
               } catch (Exception var16) {
                  this.logger.error(var16.getMessage());
               }
            }

            this.deleteSubPlaylistMap(playlistId);
            byte var11 = 1;
            return var11;
         }

         session.rollback();
         var8 = -1;
      } catch (SQLException var17) {
         session.rollback();
         byte var6 = -1;
         return var6;
      } finally {
         session.close();
      }

      return var8;
   }

   public int deleteSubPlaylistMap(String subPlaylistId) {
      try {
         List parents = ((PlaylistDaoMapper)this.getMapper()).getPlaylistListBySubPlaylist(subPlaylistId);
         if (parents != null && parents.size() > 0) {
            boolean result = ((PlaylistDaoMapper)this.getMapper()).deleteMapSubPlaylist(subPlaylistId);
            ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
            PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();

            for(int i = 0; i < parents.size(); ++i) {
               try {
                  Map parent = (Map)parents.get(i);
                  if (parent != null) {
                     String playlistId = (String)parent.get("PLAYLIST_ID");
                     Long versionId = (Long)parent.get("VERSION_ID");
                     List pList = pInfo.getContentListOfPlaylist(playlistId, versionId);
                     boolean hasSubPlaylistAtParent = this.hasSubPlaylist(playlistId);
                     if (!hasSubPlaylistAtParent) {
                        this.setHasSubPlaylist(playlistId, versionId, false);
                     }

                     int listSize = pList.size();
                     if (listSize <= 0 && !hasSubPlaylistAtParent) {
                        DeleteContentUtils.checkPlaylistFromSchedule(playlistId, "");
                        DeleteContentUtils.deletePlaylist(playlistId, "");
                     } else {
                        scheduleInfo.setPlaylistTrigger(playlistId);
                     }
                  }
               } catch (Exception var13) {
                  this.logger.error("", var13);
                  return -1;
               }
            }
         }

         return 1;
      } catch (SQLException var14) {
         return -1;
      }
   }

   public int restorePlaylist(String playlistId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var3;
      try {
         byte var10;
         if (((PlaylistDaoMapper)this.getMapper(session)).restorePlaylist(playlistId) <= 0) {
            session.rollback();
            var10 = -1;
            return var10;
         }

         if (((PlaylistDaoMapper)this.getMapper(session)).setPlaylistModifiedDate(playlistId) <= 0) {
            session.rollback();
            var10 = -1;
            return var10;
         }

         session.commit();
         var3 = 1;
      } catch (SQLException var8) {
         session.rollback();
         byte var4 = -1;
         return var4;
      } finally {
         session.close();
      }

      return var3;
   }

   public int deletePlaylistCompletely(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).deletePlaylistCompletely(playlistId);
   }

   public int setPlaylistLock(String playlistId, String sessionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).setPlaylistLock(playlistId, sessionId);
   }

   public int setPlaylistGroup(String playlistId, Long groupId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var4;
      try {
         byte var5;
         try {
            if (((PlaylistDaoMapper)this.getMapper(session)).setPlaylistGroup(playlistId, groupId) > 0) {
               int updated = ((PlaylistDaoMapper)this.getMapper(session)).setPlaylistModifiedDate(playlistId);
               if (updated <= 0) {
                  session.rollback();
                  var5 = -1;
                  return var5;
               }

               session.commit();
               int var12 = updated;
               return var12;
            }

            session.rollback();
            var4 = -1;
         } catch (SQLException var9) {
            session.rollback();
            var5 = -1;
            return var5;
         }
      } finally {
         session.close();
      }

      return var4;
   }

   public int setPlaylistShare(String playlistId, Long shareFlag) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var5;
      try {
         if (((PlaylistDaoMapper)this.getMapper()).setPlaylistShare(playlistId, shareFlag) <= 0) {
            session.rollback();
            byte var11 = -1;
            return var11;
         }

         int updated = ((PlaylistDaoMapper)this.getMapper(session)).setPlaylistModifiedDate(playlistId);
         if (updated > 0) {
            session.commit();
            int var13 = updated;
            return var13;
         }

         session.rollback();
         byte var12 = -1;
         return var12;
      } catch (SQLException var9) {
         session.rollback();
         var5 = 0;
      } finally {
         session.close();
      }

      return var5;
   }

   public int setPlaylistMetaData(String playlistId, String metaData) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var5;
      try {
         if (((PlaylistDaoMapper)this.getMapper()).setPlaylistMetaData(playlistId, metaData) <= 0) {
            session.rollback();
            byte var11 = -1;
            return var11;
         }

         int updated = ((PlaylistDaoMapper)this.getMapper(session)).setPlaylistModifiedDate(playlistId);
         if (updated > 0) {
            session.commit();
            int var13 = updated;
            return var13;
         }

         session.rollback();
         byte var12 = -1;
         return var12;
      } catch (SQLException var9) {
         session.rollback();
         var5 = 0;
      } finally {
         session.close();
      }

      return var5;
   }

   public Long getRootId(String userId) throws SQLException {
      UserInfo uInfo = UserInfoImpl.getInstance();
      long organizationId = uInfo.getRootGroupIdByUserId(userId);
      return ((PlaylistDaoMapper)this.getMapper()).getRootId(userId, organizationId, ContentConstants.PARENT_GROUP_OF_UNGROUPED);
   }

   public Long getRootId(String userId, long organizationId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getRootId(userId, organizationId, ContentConstants.PARENT_GROUP_OF_UNGROUPED);
   }

   public Boolean isExistGroupName(String groupName, String userId) throws SQLException {
      UserInfo uInfo = UserInfoImpl.getInstance();
      long organizationId = uInfo.getRootGroupIdByUserId(userId);

      try {
         return ((PlaylistDaoMapper)this.getMapper()).countExistGroupName(groupName, userId, organizationId) > 0;
      } catch (Exception var7) {
         this.logger.error(var7);
         return false;
      }
   }

   public Long getGroupId(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getGroupId(playlistId);
   }

   public String getGroupName(Long groupId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getGroupName(groupId);
   }

   public Group getGroupInfo(Long groupId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getGroupInfo(groupId);
   }

   public List getGroupList(String creatorId) throws SQLException {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
         return ((PlaylistDaoMapper)this.getMapper()).getGroupList(creatorId, organizationId);
      } catch (Exception var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public List getGroupList(String creatorId, long organizationId) throws SQLException {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         return ((PlaylistDaoMapper)this.getMapper()).getGroupList(creatorId, organizationId);
      } catch (Exception var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public List getGroupListPage(String creatorId, int startPos, int pageSize) throws SQLException {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
         PlaylistDaoMapper var10000 = (PlaylistDaoMapper)this.getMapper();
         Long var10002 = organizationId;
         --startPos;
         return var10000.getGroupListPage(creatorId, var10002, startPos, pageSize);
      } catch (Exception var7) {
         this.logger.error(var7);
         return null;
      }
   }

   public int getGroupListCnt(String creatorId) throws SQLException {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
         return ((PlaylistDaoMapper)this.getMapper()).getGroupListCnt(creatorId, organizationId);
      } catch (Exception var5) {
         this.logger.error(var5);
         return 0;
      }
   }

   public Long addGroup(Group group) throws SQLException {
      Group insertGroup = new Group();
      long groupId = (long)SequenceDB.getNextValue("MI_CMS_INFO_PLAYLIST_GROUP");
      if (groupId <= 0L) {
         return -1L;
      } else {
         insertGroup.setGroup_id(groupId);
         UserInfo uInfo = UserInfoImpl.getInstance();
         long organizationId = uInfo.getRootGroupIdByUserId(group.getCreator_id());
         insertGroup.setOrganization_id(organizationId);
         insertGroup.setP_group_id(group.getP_group_id());
         insertGroup.setGroup_depth(group.getGroup_depth());
         insertGroup.setGroup_name(group.getGroup_name());
         insertGroup.setCreator_id(group.getCreator_id());
         return ((PlaylistDaoMapper)this.getMapper()).addGroup(insertGroup) < 1L ? -1L : groupId;
      }
   }

   public Long addDefaultGroup(String userId) throws SQLException {
      long groupId = (long)SequenceDB.getNextValue("MI_CMS_INFO_PLAYLIST_GROUP");
      if (groupId <= 0L) {
         return -1L;
      } else {
         Long orgGroupId = this.getRootId(userId);
         if (orgGroupId == null) {
            Group insertGroup = new Group();
            insertGroup.setGroup_id(groupId);
            UserInfo uInfo = UserInfoImpl.getInstance();
            long organizationId = uInfo.getRootGroupIdByUserId(userId);
            insertGroup.setOrganization_id(organizationId);
            insertGroup.setP_group_id(ContentConstants.PARENT_GROUP_OF_UNGROUPED);
            insertGroup.setGroup_depth(ContentConstants.GROUP_DEPTH_OF_UNGROUPED);
            insertGroup.setGroup_name("default");
            insertGroup.setCreator_id(userId);
            return ((PlaylistDaoMapper)this.getMapper()).addGroup(insertGroup) < 1L ? -1L : groupId;
         } else {
            return orgGroupId;
         }
      }
   }

   public Long addDefaultGroup(String userId, long organizationId) throws SQLException {
      long groupId = (long)SequenceDB.getNextValue("MI_CMS_INFO_PLAYLIST_GROUP");
      if (((PlaylistDaoMapper)this.getMapper()).isExistDefaultGroup(userId, organizationId) <= 0 && groupId > 0L) {
         Long orgGroupId = this.getRootId(userId, organizationId);
         if (orgGroupId == null) {
            Group insertGroup = new Group();
            insertGroup.setGroup_id(groupId);
            UserInfo uInfo = UserInfoImpl.getInstance();
            insertGroup.setOrganization_id(organizationId);
            insertGroup.setP_group_id(ContentConstants.PARENT_GROUP_OF_UNGROUPED);
            insertGroup.setGroup_depth(ContentConstants.GROUP_DEPTH_OF_UNGROUPED);
            insertGroup.setGroup_name("default");
            insertGroup.setCreator_id(userId);
            return ((PlaylistDaoMapper)this.getMapper()).addGroup(insertGroup) < 1L ? -1L : groupId;
         } else {
            return orgGroupId;
         }
      } else {
         return -1L;
      }
   }

   public int setGroupInfo(Group group) throws SQLException {
      if (group.getGroup_id() <= 0L) {
         this.logger.fatal("Block to remove device group of root_group_id");
         return 0;
      } else {
         return ((PlaylistDaoMapper)this.getMapper()).setGroupInfo(group);
      }
   }

   public int deleteGroup(Long groupId) throws SQLException {
      if (groupId <= 0L) {
         this.logger.fatal("Block to remove device group of root_group_id");
         return 0;
      } else {
         SqlSession session = this.openNewSession(false);

         int var10;
         try {
            int deleted = ((PlaylistDaoMapper)this.getMapper(session)).deleteGroup(groupId);
            if (deleted <= 0) {
               session.rollback();
               byte var11 = -1;
               return var11;
            }

            session.commit();
            var10 = deleted;
         } catch (SQLException var8) {
            this.logger.error(var8);
            session.rollback();
            byte var4 = 0;
            return var4;
         } finally {
            session.close();
         }

         return var10;
      }
   }

   public Boolean isDeletableGroup(Long groupId) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).countDeletableGroup(groupId) == 0L;
      } catch (Exception var3) {
         this.logger.error(var3);
         return false;
      }
   }

   public List getGroupedPlaylistIdList(Long groupId) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getGroupedPlaylistIdList(groupId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getChildGroupList(Long groupId, boolean recursive, String creatorId) throws SQLException {
      List groupList = new ArrayList();
      List resList = null;
      UserInfo uInfo = UserInfoImpl.getInstance();
      long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
      resList = ((PlaylistDaoMapper)this.getMapper()).getChildGroupList(groupId, creatorId, organizationId);
      groupList.addAll(resList);
      if (recursive) {
         for(int i = 0; i < resList.size(); ++i) {
            Group group = (Group)resList.get(i);
            groupList.addAll(this.getChildGroupList(group.getGroup_id(), recursive, creatorId));
         }
      }

      return groupList;
   }

   public List getChildGroupList(Long groupId, boolean recursive, String creatorId, long organizationId) throws SQLException {
      List groupList = new ArrayList();
      List resList = null;
      UserInfo uInfo = UserInfoImpl.getInstance();
      resList = ((PlaylistDaoMapper)this.getMapper()).getChildGroupList(groupId, creatorId, organizationId);
      groupList.addAll(resList);
      if (recursive) {
         for(int i = 0; i < resList.size(); ++i) {
            Group group = (Group)resList.get(i);
            groupList.addAll(this.getChildGroupList(group.getGroup_id(), recursive, creatorId));
         }
      }

      return groupList;
   }

   public List getChildGroupIdList(Long groupId, boolean recursive) throws SQLException {
      List rtList = new ArrayList();
      List groupIdList = null;
      groupIdList = ((PlaylistDaoMapper)this.getMapper()).getChildGroupIdList(new Long(groupId), new Long(999999L));
      if (groupIdList != null) {
         for(int i = 0; i < groupIdList.size(); ++i) {
            if (recursive) {
               Long group = (Long)((Map)groupIdList.get(i)).get("group_id");
               rtList.add(group);
               List temp = this.getChildGroupIdList(group, recursive);
               if (temp != null && temp.size() != 0) {
                  rtList.addAll(temp);
               }
            } else {
               rtList.add((Long)((Map)groupIdList.get(i)).get("group_id"));
            }
         }
      }

      return rtList;
   }

   public int deletePlaylistVersion(String playlistId, String playlistVersionId, String userId, String sessionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var7;
      try {
         Long versionId = Long.parseLong(playlistVersionId);
         if (((PlaylistDaoMapper)this.getMapper(session)).deletePlaylistVersionMap(playlistId, versionId) <= 0) {
            session.rollback();
            var7 = -1;
            return var7;
         }

         if (((PlaylistDaoMapper)this.getMapper(session)).deletePlaylistVersion(playlistId, versionId, userId) > 0) {
            ((PlaylistDaoMapper)this.getMapper()).deleteMapSyncGroupInfo(playlistId, versionId);
            session.commit();
            this.setPlaylistUnlockBySessionID(sessionId);
            byte var13 = 1;
            return var13;
         }

         session.rollback();
         var7 = -1;
         return var7;
      } catch (Exception var11) {
         this.logger.error(var11);
         session.rollback();
         var7 = -1;
      } finally {
         session.close();
      }

      return var7;
   }

   public int deleteTagPlaylistVersion(String playlistId, String playlistVersionId, String userId, String sessionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var7;
      try {
         Long versionId = Long.parseLong(playlistVersionId);
         if (((PlaylistDaoMapper)this.getMapper(session)).deleteTagPlaylistVersionMap(playlistId, versionId) <= 0) {
            session.rollback();
            var7 = -1;
            return var7;
         }

         if (((PlaylistDaoMapper)this.getMapper(session)).deleteTagPlaylistCondition(playlistId, versionId) > 0) {
            if (((PlaylistDaoMapper)this.getMapper(session)).deletePlaylistVersion(playlistId, versionId, userId) <= 0) {
               session.rollback();
               var7 = -1;
               return var7;
            }

            ((PlaylistDaoMapper)this.getMapper()).deleteMapSyncGroupInfo(playlistId, versionId);
            session.commit();
            this.setPlaylistUnlockBySessionID(sessionId);
            byte var13 = 1;
            return var13;
         }

         session.rollback();
         var7 = -1;
      } catch (Exception var11) {
         this.logger.error(var11);
         session.rollback();
         var7 = -1;
         return var7;
      } finally {
         session.close();
      }

      return var7;
   }

   public List getPlaylistEffectList(String deviceType) throws SQLException {
      if (deviceType != null && !deviceType.equals("iPLAYER") && !deviceType.equals("SPLAYER")) {
         this.logger.error("deviceType Error");
      }

      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistEffectList(deviceType, "SPLAYER", "iPLAYER");
   }

   public int deleteContentTag(String playlistId, String contentId, int tagId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).deleteContentTag(playlistId, contentId, tagId);
   }

   public int setContentTag(String playlistId, Long versionId, String contentId, int contentOrder, String matchType, int tagId) throws SQLException {
      SqlSession session = null;

      int var8;
      try {
         session = this.openNewSession(true);
         var8 = this.setContentTag(playlistId, versionId, contentId, contentOrder, matchType, tagId, session);
      } catch (SQLException var12) {
         throw var12;
      } finally {
         if (session != null) {
            session.close();
         }

      }

      return var8;
   }

   private int setContentTag(String playlistId, Long versionId, String contentId, int contentOrder, String matchType, int tagId, SqlSession session) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper(session)).setContentTag(playlistId, versionId, contentId, contentOrder, matchType, tagId);
      } catch (Exception var9) {
         this.logger.error(var9);
         return 0;
      }
   }

   public List getContentTag(String playlistId, String contentId, Long versionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getContentTag(playlistId, contentId, versionId);
   }

   public List getContentTagInSchedule(String playlistId, Long versionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getContentTagInSchedule(playlistId, versionId);
   }

   public List getContentTag(String playlistId, Long versionId, String contentId, int contentOrder) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getContentTagOrder(playlistId, versionId, contentId, contentOrder);
   }

   public List getPlaylistTag(String playlistId, Long versionId, long playlistTagId, long tagOrder) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getTagPlaylistTagOrder(playlistId, versionId, playlistTagId, tagOrder);
   }

   public List getPlaylistContentTagList(String playlistId, Long versionId) throws SQLException {
      List list = ((PlaylistDaoMapper)this.getMapper()).getPlaylistContentTagList(playlistId, versionId);
      Map map = new HashMap();

      ContentTagEntity tag;
      ContentTagEntity contentTagEntity;
      for(Iterator var5 = list.iterator(); var5.hasNext(); contentTagEntity.getTag_id_list().add(tag.getTag_id())) {
         tag = (ContentTagEntity)var5.next();
         String map_key = tag.getContent_id() + "_" + tag.getContent_order();
         if (map.containsKey(map_key)) {
            contentTagEntity = (ContentTagEntity)map.get(map_key);
         } else {
            contentTagEntity = new ContentTagEntity();
            contentTagEntity.setContent_id(tag.getContent_id());
            contentTagEntity.setContent_order(tag.getContent_order());
            contentTagEntity.setMatch_type(tag.getMatch_type());
            contentTagEntity.setTag_id_list(new ArrayList());
            map.put(map_key, contentTagEntity);
         }
      }

      return new ArrayList(map.values());
   }

   public int setContentDuraionByContentID(String contentId, Long contentDuration) {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).setContentDuraionByContentID(contentId, contentDuration);
      } catch (Exception var4) {
         this.logger.error(var4);
         return 0;
      }
   }

   public int setContentDuraionMilliByContentID(String contentId, String milli) {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).setContentDuraionMilliByContentID(contentId, milli);
      } catch (Exception var4) {
         this.logger.error(var4);
         return 0;
      }
   }

   public List getPlaylistIDListByContentID(String contentId) {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getPlaylistIDListByContentID(contentId);
      } catch (SQLException var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public int setPlaytime(String playlistId, Long versionId, String playTime) {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).setPlaytime(playlistId, versionId, playTime);
      } catch (SQLException var5) {
         this.logger.error(var5);
         return 0;
      }
   }

   public Long getSumOfContentDuration(String playlistId, Long versionId) {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getSumOfContentDuration(playlistId, versionId);
      } catch (SQLException var4) {
         this.logger.error(var4);
         return 0L;
      }
   }

   public String isDelete(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).isDelete(playlistId);
   }

   public String getCreatorIdByPlaylistId(String playlistId) throws SQLException {
      String result = ((PlaylistDaoMapper)this.getMapper()).getCreatorIdByPlaylistId(playlistId);
      return result != null ? result : "";
   }

   public int unlockAllSession() throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).unlockAllSession();
   }

   public int setPlaylistUnlock(String playlistId, String sessionId) throws SQLException {
      int result = -1;
      int retry = 5;

      while(retry > 0) {
         try {
            result = ((PlaylistDaoMapper)this.getMapper()).setPlaylistUnlock(playlistId, sessionId);
            retry = 0;
         } catch (SQLException var8) {
            if (retry == 1) {
               throw var8;
            }

            try {
               --retry;
               Thread.sleep(1L);
            } catch (InterruptedException var7) {
               this.logger.error(var7.getMessage(), var7);
            }
         }
      }

      return result;
   }

   public int setPlaylistUnlockBySessionID(String sessionId) throws SQLException {
      int result = -1;
      int retry = 5;

      while(retry > 0) {
         try {
            result = ((PlaylistDaoMapper)this.getMapper()).setPlaylistUnlockBySessionID(sessionId);
            retry = 0;
         } catch (SQLException var7) {
            if (retry == 1) {
               throw var7;
            }

            try {
               --retry;
               Thread.sleep(1L);
            } catch (InterruptedException var6) {
               this.logger.error(var6.getMessage(), var6);
            }
         }
      }

      return result;
   }

   private void addWhereOrgan(Map map) throws SQLException {
      String creatorID = (String)map.get("creatorID");
      UserInfo uInfo = UserInfoImpl.getInstance();
      AbilityInfo abilityInfo = AbilityInfoImpl.getInstance();
      User user = uInfo.getAllByUserId(creatorID);
      String organization = user.getOrganization();
      List abilityList = abilityInfo.getAllAbilityListByUserId(creatorID);
      if (!organization.equalsIgnoreCase("ROOT")) {
         List userList = uInfo.getAllUserByRootGroupId(user.getRoot_group_id());
         if (userList.size() > 0) {
            Map organMap = new HashMap();
            Iterator var10 = userList.iterator();

            while(true) {
               while(var10.hasNext()) {
                  Map userMap = (Map)var10.next();
                  String userName = (String)userMap.get("user_id");
                  if (!abilityList.contains("Content Manage Authority") && !creatorID.equalsIgnoreCase(userName)) {
                     organMap.put(userName, ContentConstants.SHARE_FLAG_YES);
                  } else {
                     organMap.put(userName, (Object)null);
                  }
               }

               map.put("whereOrganMap", organMap);
               break;
            }
         }
      }

   }

   private Map getContentListExtraParams(Map orginalMap) throws SQLException {
      Map map = new HashMap(orginalMap);
      String listType = (String)map.get("listType");
      String searchText = (String)map.get("searchText");
      String searchCreator = (String)map.get("searchCreator");
      String searchId = (String)map.get("searchID");
      String sortColumn = (String)map.get("sortColumn");
      String sortOrder = (String)map.get("sortOrder");
      String groupID = (String)map.get("groupID");
      String creatorID = (String)map.get("creatorID");
      String device_type = (String)map.get("device_type");
      String playlist_type = (String)map.get("playlist_type");
      String search_start_date = (String)map.get("startDate");
      String search_end_date = (String)map.get("endDate");
      Boolean hasContentManage = (Boolean)map.get("hasContentManage");
      if (hasContentManage == null) {
         map.put("hasContentManage", false);
      } else {
         map.put("hasContentManage", hasContentManage);
      }

      if (map.get("adSchedule") != null) {
         map.put("adSchedule", (String)map.get("adSchedule"));
      }

      UserInfo uInfo = UserInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      String isVwl = (String)map.get("isVwl");
      String isSync = (String)map.get("isSync");
      String deviceType = null;
      if (map.get("deviceType") != null) {
         deviceType = (String)map.get("deviceType");
      }

      float deviceTypeVersion = 0.0F;
      if (map.get("deviceTypeVersion") != null) {
         if (map.get("deviceTypeVersion") instanceof String) {
            String deviceTypeVersionStr = (String)map.get("deviceTypeVersion");
            deviceTypeVersion = Float.valueOf(deviceTypeVersionStr);
         } else {
            deviceTypeVersion = (Float)map.get("deviceTypeVersion");
         }
      }

      if (deviceType != null) {
         map.put("deviceType", deviceType);
         map.put("deviceTypeVersion", deviceTypeVersion);
      }

      if (isVwl != null && !isVwl.equals("")) {
         map.put("isVwl", isVwl);
      }

      if (isSync != null && !isSync.equals("")) {
         map.put("isSync", isSync);
      }

      long organizationId = 0L;
      String userId;
      if (map.get("byUserOrganizationId") != null && !"".equals(map.get("byUserOrganizationId"))) {
         userId = (String)map.get("byUserOrganizationId");
         organizationId = Long.valueOf(userId);
      } else if (SecurityUtils.getUserContainer() != null) {
         organizationId = SecurityUtils.getUserContainer().getUser().getRoot_group_id();
      } else {
         User logged = uInfo.getUserInfo((String)map.get("creatorID"));
         if (logged.isMu()) {
            organizationId = uInfo.getCurMngOrgId((String)map.get("creatorID"));
         } else {
            organizationId = uInfo.getRootGroupIdByUserId((String)map.get("creatorID"));
         }
      }

      if (organizationId != 0L) {
         map.put("organizationId", organizationId);
      } else {
         userId = (String)map.get("creatorID");
         map.put("userManageGroupList", userGroupInfo.getUserManageGroupListByUserId(userId));
      }

      if (searchText != null && searchText.length() > 0) {
         map.put("searchText", searchText.toUpperCase().replaceAll("_", "^_"));
      }

      if (searchCreator != null && searchCreator.length() > 0) {
         map.put("searchCreator", searchCreator.toUpperCase().replaceAll("_", "^_"));
      }

      if ("UNGROUPED".equalsIgnoreCase(listType)) {
         map.put("groupRootId", this.getRootId(creatorID));
      }

      if (groupID != null && groupID.length() > 0) {
         String[] groupIdArray = groupID.split(",");
         List groupIds = new ArrayList();
         if (groupIdArray.length > 0) {
            if (groupIdArray.length == 1) {
               Long groupIDLong = new Long(groupIdArray[0]);
               map.put("groupIDLong", groupIDLong);
            } else if (groupIdArray.length > 1) {
               map.put("groupID", (Object)null);

               for(int idx = 0; idx < groupIdArray.length; ++idx) {
                  groupIds.add(Long.parseLong(groupIdArray[idx]));
               }

               map.put("groupIds", groupIds);
            }
         }
      }

      if ("ORGAN".equalsIgnoreCase(listType)) {
         this.addWhereOrgan(map);
      }

      Boolean canReadUnshared = (Boolean)map.get("canReadUnshared");
      if (canReadUnshared == null) {
         map.put("canReadUnshared", false);
      }

      String[] playlist_type_array;
      if (device_type != null && device_type.length() > 0) {
         playlist_type_array = device_type.split(",");
         if (playlist_type_array != null && playlist_type_array.length > 0) {
            map.put("deviceTypeArray", playlist_type_array);
         }
      }

      if (sortColumn != null && sortColumn.length() > 0) {
         map.put("sortColumn", ((String)map.get("sortColumn")).toUpperCase());
         if (sortOrder != null && sortOrder.length() > 0) {
            map.put("sortOrder", ((String)map.get("sortOrder")).toUpperCase());
         }
      }

      if (map.get("category") != null && !map.get("category").equals("")) {
         String category = (String)map.get("category");
         String[] categoryList = category.split(",");
         List contentIdList = ((PlaylistDaoMapper)this.getMapper()).getPlaylistIdfromCategory(categoryList);
         List playlistIds = new ArrayList();
         if (contentIdList != null && contentIdList.size() > 0) {
            Iterator var29 = contentIdList.iterator();

            while(var29.hasNext()) {
               Map content = (Map)var29.next();
               playlistIds.add((String)content.get("playlist_id"));
            }
         } else {
            playlistIds.add("null");
         }

         map.put("playlistIdList", playlistIds);
      }

      map.put("typeVersion", 2.0F);
      map.put("ConstSHARE_FLAG_DEFAULT", ContentConstants.SHARE_FLAG_DEFAULT);
      map.put("ConstGROUP_TYPE_UNGROUPED", "UNGROUPED");
      map.put("ConstGROUP_TYPE_GROUPED", "GROUPED");
      map.put("ConstGROUP_TYPE_USER", "USER");
      map.put("ConstGROUP_TYPE_ORGAN", "ORGAN");
      map.put("ConstGROUP_TYPE_SHARED", "SHARED");
      map.put("ConstGROUP_TYPE_ALL", "ALL");
      map.put("ConstGROUP_TYPE_DELETED", "DELETED");
      map.put("ConstTYPE_SOC", "SPLAYER");
      map.put("ConstTYPE_VERSION_1_0", CommonDataConstants.TYPE_VERSION_1_0);
      map.put("ConstTYPE_SOC2", "S2PLAYER");
      map.put("ConstTYPE_VERSION_2_0", CommonDataConstants.TYPE_VERSION_2_0);
      map.put("ConstTYPE_SOC3", "S3PLAYER");
      map.put("ConstTYPE_VERSION_3_0", CommonDataConstants.TYPE_VERSION_3_0);
      map.put("ConstTYPE_PREMIUM", "iPLAYER");
      map.put("ConstTYPE_APLAYER", "APLAYER");
      map.put("ConstTYPE_WPLAYER", "WPLAYER");
      map.put("ConstTYPE_LITE", "LPLAYER");
      map.put("ConstPLAYLIST_TYPE_PREMIUM", "0");
      map.put("ConstPLAYLIST_TYPE_SYNCPLAY", "3");
      map.put("ConstPLAYLIST_TYPE_AMS", "1");
      map.put("ConstPLAYLIST_TYPE_VWL", "2");
      map.put("ConstPLAYLIST_TYPE_VWL", "5");
      if (playlist_type != null && playlist_type.length() > 0) {
         playlist_type_array = playlist_type.split(",");
         if (playlist_type_array != null && playlist_type_array.length > 0) {
            map.put("playlist_type_filter", Arrays.asList(playlist_type_array));
         }
      }

      if (search_start_date != null && search_start_date.length() > 0) {
         map.put("searchStartDate", search_start_date + " 00:00:00");
      }

      if (search_end_date != null && search_end_date.length() > 0) {
         map.put("searchEndDate", search_end_date + " 23:59:59");
      }

      return map;
   }

   private PlaylistSearch getContentSearch(String searchId) throws NumberFormatException, SQLException {
      if (searchId != null && searchId.length() > 0) {
         PlaylistSearchInfo searchInfo = PlaylistSearchInfoImpl.getInstance();
         PlaylistSearch condition = searchInfo.getPlaylistSearchBySearchId(new Long(searchId));
         return condition;
      } else {
         return null;
      }
   }

   public void deleteContentFromPlaylist(String contentId) throws SQLException, Exception {
      ((PlaylistDaoMapper)this.getMapper()).deleteContentFromPlaylist(contentId);
   }

   public List getContentOrderListOfPlaylist(String playlistId, Long versionId) {
      return ((PlaylistDaoMapper)this.getMapper()).getContentOrderListOfPlaylist(playlistId, versionId);
   }

   public List getSubPlaylistContentOrderListOfPlaylist(String playlistId, Long versionId) {
      return ((PlaylistDaoMapper)this.getMapper()).getSubPlaylistContentOrderListOfPlaylist(playlistId, versionId);
   }

   public void updateContentOrder(int newContentOrder, String playlistId, Long versionId, Long oldContentOrder) throws SQLException, Exception {
      ((PlaylistDaoMapper)this.getMapper()).updateContentOrder(newContentOrder, playlistId, versionId, oldContentOrder);
   }

   public void updateSubPlaylistContentOrder(int newContentOrder, String playlistId, Long versionId, Long oldContentOrder) throws SQLException, Exception {
      ((PlaylistDaoMapper)this.getMapper()).updateSubPlaylistContentOrder(newContentOrder, playlistId, versionId, oldContentOrder);
   }

   public void updateContentCount(int contentCount, String playlistId, Long versionId) throws SQLException, Exception {
      ((PlaylistDaoMapper)this.getMapper()).updateContentCount(contentCount, playlistId, versionId);
   }

   public List getSyncGroupInfo(String playlistId, Long versionId) throws SQLException, Exception {
      return ((PlaylistDaoMapper)this.getMapper()).getSyncGroupInfo(playlistId, versionId);
   }

   public int getCountPlaylistVersionId(String playlistId) throws SQLException, Exception {
      return ((PlaylistDaoMapper)this.getMapper()).getCountPlaylistVersionId(playlistId);
   }

   public int getUsedPlaylistCount(long organizationId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getUsedPlaylistCount(organizationId);
   }

   public ContentFile getThumbFileInfo(String contentId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getThumbFileInfo(contentId);
   }

   public boolean addTagConditionMapping(String playlistId, long versionId, long tagId, long tagConditionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).addTagConditionMapping(playlistId, versionId, tagId, tagConditionId);
   }

   public List getPlaylistTagConditionList(String playlistId, long versionId, long tagId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistTagConditionList(playlistId, versionId, tagId);
   }

   public ContentFile getTagPlaylistThumbFileInfo(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getTagPlaylistThumbFileInfo(playlistId);
   }

   public int deleteTagPlaylistCondition(String playlistId, Long versionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).deleteTagPlaylistCondition(playlistId, versionId);
   }

   public int checkExistTagCondition(String playlistId, Long versionId, Long tagId, Long conditionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).checkExistTagCondition(playlistId, versionId, tagId, conditionId);
   }

   public int checkTagPlaylist(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).checkTagPlaylist(playlistId, "5");
   }

   public boolean deleteTagPlaylistPerm(String playlistId) throws SQLException {
      boolean rtn = false;
      SqlSession session = this.openNewSession(false);

      boolean var5;
      try {
         ((PlaylistDaoMapper)this.getMapper(session)).deleteTagPlaylistVersion(playlistId);
         ((PlaylistDaoMapper)this.getMapper(session)).deleteTagPlaylistConditionPerm(playlistId);
         session.commit();
         rtn = true;
         return rtn;
      } catch (SQLException var9) {
         this.logger.error(var9);
         session.rollback();
         var5 = false;
      } finally {
         session.close();
      }

      return var5;
   }

   public List getCntContentAtTagPlaylist(Long tagId, String conditionIds) throws SQLException {
      String[] conditionStr = null;
      if (conditionIds != null && !conditionIds.equals("")) {
         conditionStr = conditionIds.split(",");
      }

      return ((PlaylistDaoMapper)this.getMapper()).getCntContentAtTagPlaylist(tagId, conditionStr);
   }

   public List getCntContentAtTagPlaylist(Long tagId, String[] conditionIds) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getCntContentAtTagPlaylist(tagId, conditionIds);
   }

   public List getThumbContentAtTagPlaylistAll(Long tagId, String conditionIds) throws SQLException {
      String[] conditionStr = null;
      if (conditionIds != null && !conditionIds.equals("")) {
         conditionStr = conditionIds.split(",");
      }

      return ((PlaylistDaoMapper)this.getMapper()).getThumbContentAtTagPlaylistAll(tagId, conditionStr);
   }

   public List getThumbContentAtTagPlaylist(Long tagId, String conditionIds) throws SQLException {
      String[] conditionStr = null;
      if (conditionIds != null && !conditionIds.equals("")) {
         conditionStr = conditionIds.split(",");
      }

      return ((PlaylistDaoMapper)this.getMapper()).getThumbContentAtTagPlaylist(tagId, conditionStr);
   }

   public List getAllThumbContentAtTagPlaylist(Long tagId, String conditionIds) throws SQLException {
      String[] conditionStr = null;
      if (conditionIds != null && !conditionIds.equals("")) {
         conditionStr = conditionIds.split(",");
      }

      return ((PlaylistDaoMapper)this.getMapper()).getAllThumbContentAtTagPlaylist(tagId, conditionStr);
   }

   public List getTagPlaylistTagList(String playlistId, Long versionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getTagPlaylistTagList(playlistId, versionId);
   }

   public List getTagPlaylistTagConditionList(String playlistId, Long versionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getTagPlaylistTagConditionList(playlistId, versionId);
   }

   public String getConditionIdWithTagNumber(long tagId, String[] tagConditionEqual, String[] tagConditionUp, String[] tagConditionDown) throws SQLException {
      List rtn = ((PlaylistDaoMapper)this.getMapper()).getConditionIdWithTagNumber(tagId, tagConditionEqual, tagConditionUp, tagConditionDown);
      StringBuffer rtnStr = new StringBuffer();
      if (rtn != null && rtn.size() > 0) {
         for(int i = 0; i < rtn.size(); ++i) {
            Map map = (Map)rtn.get(i);
            if (map.get("tag_condition_id") != null) {
               if (i > 0) {
                  rtnStr.append(", ");
               }

               rtnStr.append(String.valueOf((Long)map.get("tag_condition_id")));
            }
         }

         return rtnStr.toString();
      } else {
         return null;
      }
   }

   public List getListLinkedPlaylistProgramId(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getListLinkedPlaylistProgramId(playlistId);
   }

   public String getPlaylistTypeFromPlaylistId(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistTypeFromPlaylistId(playlistId);
   }

   public List getListLinkedPlaylistPlaylistId(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getListLinkedPlaylistPlaylistId(playlistId);
   }

   public List getContentTagListWithPlaylistId(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getContentTagListWithPlaylistId(playlistId);
   }

   public List getContentListWithTag(String playlistId, String contentId, Long versionId, Long contentOrder, List tagList) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getContentListWithTag(playlistId, contentId, versionId, contentOrder, tagList);
   }

   public String getOrganizationByPlaylistId(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getOrganizationByPlaylistId(playlistId);
   }

   public boolean checkMappingSchedule(long groupId, String userId) throws SQLException {
      boolean rtn = false;
      List groups = ((PlaylistDaoMapper)this.getMapper()).getAllPlaylistGroups(groupId);
      if (groups != null && groups.size() > 0) {
         Iterator var6 = groups.iterator();

         while(var6.hasNext()) {
            Group group = (Group)var6.next();
            if (group != null && group.getGroup_id() > 0L) {
               List playlistIds = ((PlaylistDaoMapper)this.getMapper()).getGroupedPlaylistIdList(group.getGroup_id());
               if (playlistIds != null && playlistIds.size() > 0) {
                  Iterator var9 = playlistIds.iterator();

                  while(var9.hasNext()) {
                     Map map = (Map)var9.next();
                     String playlistId = (String)map.get("playlist_id");
                     if (playlistId != null && !playlistId.equals("") && !this.isDeletablePlaylist(playlistId, userId, (String)null)) {
                        rtn = true;
                        break;
                     }
                  }
               }
            }

            if (rtn) {
               break;
            }
         }
      }

      return rtn;
   }

   public boolean hasSubPlaylist(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getCountSubPlaylistOfPlaylist(playlistId) > 0;
   }

   public int setHasSubPlaylist(String playlistId, Long versionId, Boolean hasSubPlaylist) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).setHasSubPlaylist(playlistId, versionId, hasSubPlaylist);
   }

   public List getPlayListGroupByUserId(String userId) throws SQLException {
      UserGroupInfo userInfo = UserGroupInfoImpl.getInstance();
      return userInfo.getUserManageGroupListByUserId(userId);
   }

   public int getCountPlaylistToExpire(List orgGroupIds, String userId, String stopDate, SelectCondition condition) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getCountPlaylistToExpire(orgGroupIds, userId, stopDate, condition);
   }

   public List getListPlaylistToExpire(int startPos, int pageSize, List groupList, String stopDate, SelectCondition condition) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getListPlaylistToExpire(startPos, pageSize, groupList, stopDate, condition);
   }

   public int deleteContentTagFromPlaylist(String contentId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).deleteContentTagFromPlaylist(contentId);
   }

   public void updateContentOrderOfTag(String playlistId, Long versionId, String contentId, int newContentOrder) throws SQLException {
      ((PlaylistDaoMapper)this.getMapper()).updateContentOrderOfTag(playlistId, versionId, contentId, newContentOrder);
   }

   public long getDefaultPlaylistGroupId(String userId, long orgnizationId) throws SQLException {
      return this.getRootId(userId, orgnizationId);
   }

   public void changeGroupIdOf_MI_CMS_MAP_GROUP_PLAYLIST(Long groupId, String fromUserId, Long organizationId) throws SQLException {
      ((PlaylistDaoMapper)this.getMapper()).changeGroupIdOf_MI_CMS_MAP_GROUP_PLAYLIST(groupId, fromUserId, organizationId);
   }

   public void changeCreatorIdOf_MI_CMS_INFO_PLAYLIST(String fromUserId, String toUserId, Long organizationId) throws SQLException {
      ((PlaylistDaoMapper)this.getMapper()).changeCreatorIdOf_MI_CMS_INFO_PLAYLIST(fromUserId, toUserId, organizationId);
   }

   public void changeCreatorIdOf_MI_CMS_INFO_PLAYLIST_VERSION(String fromUserId, String toUserId, Long organizationId) throws SQLException {
      ((PlaylistDaoMapper)this.getMapper()).changeCreatorIdOf_MI_CMS_INFO_PLAYLIST_VERSION(fromUserId, toUserId, organizationId);
   }

   public void deleteGroupByCreatorId(String creatorId) throws SQLException {
      ((PlaylistDaoMapper)this.getMapper()).deleteGroupByCreatorId(creatorId);
   }

   public long getCntAllPlaylists(String creatorId, Long organizationId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getCntAllPlaylists(creatorId, organizationId);
   }

   public int deleteContentFromPlaylist(String playlistId, String contentId) throws Exception {
      int cnt = 0;
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      String playTime = "";

      try {
         Playlist playlist = this.getPlaylistActiveVerInfo(playlistId);
         if (playlist == null) {
            this.logger.error("[INFO][MagicInfo_ExpiredContentJob] Playlist is NULL : playlistId[" + playlistId + ", contentId[" + contentId + "]");
            return cnt;
         } else {
            playTime = playlist.getPlay_time();
            Long totalPlayTime = ContentUtils.getPlayTimeStr(playlist.getPlay_time());
            Long totalSize = playlist.getTotal_size();
            List contentTagEntityList = this.getPlaylistContentTagListAfterContentExpire(playlist.getPlaylist_id(), playlist.getVersion_id(), contentId);
            List oldContentList = this.getContentList(playlistId, playlist.getVersion_id());
            List newContentList = new ArrayList();
            Long contentOrder = 1L;
            if ("3".equals(playlist.getPlaylist_type())) {
               PlaylistContent playlistContent0 = (PlaylistContent)oldContentList.get(0);
               PlaylistContent playlistContentN = (PlaylistContent)oldContentList.get(oldContentList.size() - 1);
               int iGroupCount = Integer.valueOf(playlistContentN.getSync_play_id()) - Integer.valueOf(playlistContent0.getSync_play_id()) + 1;
               int iContentCount = oldContentList.size() / iGroupCount;
               String sProcessedContentList = "";

               for(int i = 0; i < iContentCount; ++i) {
                  List unduplicatedPlaylistContentList = new ArrayList();
                  boolean bIsSameContent = false;
                  String sContentList = "";
                  boolean bIsSetPlayTime = false;

                  int j;
                  PlaylistContent tmpPlaylistContent;
                  for(j = 0; j < iGroupCount; ++j) {
                     tmpPlaylistContent = (PlaylistContent)oldContentList.get(i + iContentCount * j);
                     if (!sContentList.contains(tmpPlaylistContent.getContent_id())) {
                        sContentList = sContentList + tmpPlaylistContent.getContent_id() + "|";
                        if (contentId.equals(tmpPlaylistContent.getContent_id())) {
                           bIsSameContent = true;
                        }

                        unduplicatedPlaylistContentList.add(tmpPlaylistContent);
                     }

                     if (bIsSameContent && !bIsSetPlayTime) {
                        totalPlayTime = totalPlayTime - tmpPlaylistContent.getContent_duration();
                        bIsSetPlayTime = true;
                     }
                  }

                  if (bIsSameContent) {
                     for(j = 0; j < unduplicatedPlaylistContentList.size(); ++j) {
                        tmpPlaylistContent = (PlaylistContent)unduplicatedPlaylistContentList.get(j);
                        Content content = contentInfo.getContentActiveVerInfo(tmpPlaylistContent.getContent_id());
                        if (content != null && !sProcessedContentList.contains(tmpPlaylistContent.getContent_id())) {
                           totalSize = totalSize - content.getTotal_size();
                        }

                        if (!sProcessedContentList.contains(tmpPlaylistContent.getContent_id())) {
                           sProcessedContentList = sProcessedContentList + tmpPlaylistContent.getContent_id() + "|";
                        }
                     }
                  } else {
                     for(j = 0; j < iGroupCount; ++j) {
                        tmpPlaylistContent = (PlaylistContent)oldContentList.get(i + iContentCount * j);
                        tmpPlaylistContent.setContent_order(contentOrder);
                        newContentList.add(tmpPlaylistContent);
                     }

                     contentOrder = contentOrder + 1L;
                  }
               }
            } else {
               String sContentList = "";

               for(int i = 0; i < oldContentList.size(); ++i) {
                  PlaylistContent playlistContent = (PlaylistContent)oldContentList.get(i);
                  if (playlistContent.getContent_id().equals(contentId)) {
                     Content content = contentInfo.getContentActiveVerInfo(contentId);
                     if (content != null) {
                        totalPlayTime = totalPlayTime - playlistContent.getContent_duration();
                        if (!sContentList.contains(content.getContent_id())) {
                           sContentList = sContentList + content.getContent_id() + "|";
                           totalSize = totalSize - content.getTotal_size();
                        }
                     }
                  } else {
                     Long var32 = contentOrder;
                     contentOrder = contentOrder + 1L;
                     playlistContent.setContent_order(var32);
                     newContentList.add(playlistContent);
                  }
               }
            }

            int cnt;
            if (newContentList.size() > 0) {
               playlist.setTotal_size(totalSize);
               playlist.setPlay_time(ContentUtils.getPlayTimeFormattedStr(totalPlayTime));
               playlist.setArr_content_list(newContentList);
               playlist.setContent_count(newContentList.size());
               playlist.setTagList(contentTagEntityList);
               cnt = this.addPlaylist(playlist);
            } else {
               cnt = -2;
            }

            return cnt;
         }
      } catch (Exception var26) {
         this.logger.error("[INFO][MagicInfo_ExpiredContentJob] PlayTime is NOT valid : playlistId[" + playlistId + "], contentId[" + contentId + "], playTime[" + playTime + "]");
         this.logger.error("[MagicInfo_ExpiredContentJob] ", var26);
         return 0;
      }
   }

   public List getPlaylistContentTagListAfterContentExpire(String playlistId, Long versionId, String contentId) throws SQLException {
      List list = ((PlaylistDaoMapper)this.getMapper()).getPlaylistContentTagList(playlistId, versionId);
      if (list != null && list.size() != 0) {
         List listOfContents = ((PlaylistDaoMapper)this.getMapper()).getContentOrderListOfPlaylist(playlistId, versionId);
         Set expiredContentOrdersSet = new HashSet();
         Map nContentsExpiredUptoOrder = new HashMap();
         int curExpiredOrders = 0;
         Iterator var9 = listOfContents.iterator();

         while(var9.hasNext()) {
            PlaylistContent content = (PlaylistContent)var9.next();
            nContentsExpiredUptoOrder.put(content.getContent_order(), curExpiredOrders);
            if (content.getContent_id().equals(contentId)) {
               expiredContentOrdersSet.add(content.getContent_order());
               ++curExpiredOrders;
            }
         }

         List map = new ArrayList();
         int index = -1;
         long content_order = 0L;
         Iterator var13 = list.iterator();

         while(var13.hasNext()) {
            ContentTagEntity tag = (ContentTagEntity)var13.next();
            if (!expiredContentOrdersSet.contains((long)tag.getContent_order())) {
               ContentTagEntity contentTagEntity;
               if ((long)tag.getContent_order() == content_order) {
                  contentTagEntity = (ContentTagEntity)map.get(index);
               } else {
                  ++index;
                  contentTagEntity = new ContentTagEntity();
                  contentTagEntity.setContent_id(tag.getContent_id());
                  int tagContentOrder = tag.getContent_order();
                  int nContentsExpiredTillNow = (Integer)nContentsExpiredUptoOrder.get((long)tagContentOrder);
                  int newTagContentOrder = tagContentOrder - nContentsExpiredTillNow;
                  contentTagEntity.setContent_order(newTagContentOrder);
                  content_order = (long)newTagContentOrder;
                  contentTagEntity.setMatch_type(tag.getMatch_type());
                  contentTagEntity.setTag_id_list(new ArrayList());
                  map.add(index, contentTagEntity);
               }

               contentTagEntity.getTag_id_list().add(tag.getTag_id());
            }
         }

         return map;
      } else {
         return null;
      }
   }

   public List getUpperPlaylist(String playlistId) throws Exception {
      return ((PlaylistDaoMapper)this.getMapper()).getUpperPlaylist(playlistId);
   }

   public int getContentCountInTagPlaylist(String playlistId) throws Exception {
      return ((PlaylistDaoMapper)this.getMapper()).getContentCountInTagPlaylist(playlistId);
   }

   public List getPlaylistInfoByContentId(String contentId) throws Exception {
      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistInfoByContentId(contentId);
   }

   public int setTotalSize(String playlistId, long versionId, long totalSize) throws Exception {
      return ((PlaylistDaoMapper)this.getMapper()).setTotalSize(playlistId, versionId, totalSize);
   }

   public int setContentDurationByVersionOfPlaylist(String playlistId, long versionId, String contentId, long contentDuration) throws Exception {
      return ((PlaylistDaoMapper)this.getMapper()).setContentDurationByVersionOfPlaylist(playlistId, versionId, contentId, contentDuration);
   }

   public int setContentDurationMilliByVersionOfPlaylist(String playlistId, long versionId, String contentId, String contentDurationMilli) throws Exception {
      return ((PlaylistDaoMapper)this.getMapper()).setContentDurationMilliByVersionOfPlaylist(playlistId, versionId, contentId, contentDurationMilli);
   }

   public List getGroupListByOrganizationId(Long organizationId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getGroupListByOrganizationId(organizationId);
   }

   public List getPlaylistIdListByPlaylistName(String[] playlistNameList) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistIdListByPlaylistName(playlistNameList);
   }

   public List getPlaylistIdListByRegex(String regex) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistIdListByRegex(regex);
   }

   public List getPlaylistScheduleMapping(List playlistIds) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistScheduleMapping(playlistIds);
   }

   public List getPlayListGroupBySearch(String searchText, long organizationId, String userId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlayListGroupBySearch(searchText, organizationId, userId);
   }

   public List getPlayListGroupBySearch(String searchText, long organizationId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlayListGroupBySearch(searchText, organizationId, (String)null);
   }

   public List getParentsGroupList(int pGroupId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getParentsGroupList(pGroupId);
   }

   public Long getOrganizationIdByGroupId(Long groupId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getOrganizationIdByGroupId(groupId);
   }

   public List getSubGroupList(Long groupId, boolean recursive, Long organizationId) throws SQLException {
      List groupList = new ArrayList();
      List resList = null;
      resList = ((PlaylistDaoMapper)this.getMapper()).getSubGroupList(groupId, organizationId);
      groupList.addAll(resList);
      if (recursive) {
         for(int i = 0; i < resList.size(); ++i) {
            Group group = (Group)resList.get(i);
            groupList.addAll(this.getSubGroupList(group.getGroup_id(), recursive, organizationId));
         }
      }

      return groupList;
   }

   public List getPlaylistListByFilter(Map map, int startPos, int pageSize) throws SQLException {
      try {
         Map mapWithParams = this.getContentListByFilter(map);
         --startPos;
         mapWithParams.put("startPos", startPos);
         mapWithParams.put("pageSize", pageSize);
         return ((PlaylistDaoMapper)this.getMapper()).getPlaylistListPage(mapWithParams);
      } catch (Exception var5) {
         this.logger.error(var5);
         return null;
      }
   }

   private Map getContentListByFilter(Map orginalMap) throws SQLException {
      Map map = new HashMap(orginalMap);
      String listType = (String)map.get("listType");
      String searchText = (String)map.get("searchText");
      String searchCreator = (String)map.get("searchCreator");
      String searchId = (String)map.get("searchID");
      String sortColumn = (String)map.get("sortColumn");
      String sortOrder = (String)map.get("sortOrder");
      String groupID = (String)map.get("groupID");
      String creatorID = (String)map.get("creatorID");
      String device_type = (String)map.get("device_type");
      String playlist_type = (String)map.get("playlist_type");
      String search_start_date = (String)map.get("startDate");
      String search_end_date = (String)map.get("endDate");
      if (map.get("adSchedule") != null) {
         map.put("adSchedule", (String)map.get("adSchedule"));
      }

      UserInfo uInfo = UserInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      String isVwl = (String)map.get("isVwl");
      String isSync = (String)map.get("isSync");
      String deviceType = null;
      if (map.get("deviceType") != null) {
         deviceType = (String)map.get("deviceType");
      }

      float deviceTypeVersion = 0.0F;
      if (map.get("deviceTypeVersion") != null) {
         deviceTypeVersion = (Float)map.get("deviceTypeVersion");
      }

      if (deviceType != null) {
         map.put("deviceType", deviceType);
         map.put("deviceTypeVersion", deviceTypeVersion);
      }

      if (isVwl != null && !isVwl.equals("")) {
         map.put("isVwl", isVwl);
      }

      if (isSync != null && !isSync.equals("")) {
         map.put("isSync", isSync);
      }

      long organizationId = 0L;
      String userId;
      if (map.get("byUserOrganizationId") != null && !"".equals(map.get("byUserOrganizationId"))) {
         userId = (String)map.get("byUserOrganizationId");
         organizationId = Long.valueOf(userId);
      } else if (SecurityUtils.getUserContainer() != null) {
         organizationId = SecurityUtils.getUserContainer().getUser().getRoot_group_id();
      } else {
         User logged = uInfo.getUserInfo((String)map.get("creatorID"));
         if (logged.isMu()) {
            organizationId = uInfo.getCurMngOrgId((String)map.get("creatorID"));
         } else {
            organizationId = uInfo.getRootGroupIdByUserId((String)map.get("creatorID"));
         }
      }

      if (organizationId != 0L) {
         map.put("organizationId", organizationId);
      } else {
         userId = (String)map.get("creatorID");
         map.put("userManageGroupList", userGroupInfo.getUserManageGroupListByUserId(userId));
      }

      if (searchText != null && searchText.length() > 0) {
         map.put("searchText", searchText.toUpperCase().replaceAll("_", "^_"));
      }

      if (searchCreator != null && searchCreator.length() > 0) {
         map.put("searchCreator", searchCreator.toUpperCase().replaceAll("_", "^_"));
      }

      if ("UNGROUPED".equalsIgnoreCase(listType)) {
         map.put("groupRootId", this.getRootId(creatorID));
      }

      if (groupID != null && groupID.length() > 0) {
         String[] groupIdArray = groupID.split(",");
         List groupIds = new ArrayList();
         if (groupIdArray.length > 0) {
            if (groupIdArray.length == 1) {
               Long groupIDLong = new Long(groupIdArray[0]);
               map.put("groupIDLong", groupIDLong);
            } else if (groupIdArray.length > 1) {
               map.put("groupID", (Object)null);

               for(int idx = 0; idx < groupIdArray.length; ++idx) {
                  groupIds.add(Long.parseLong(groupIdArray[idx]));
               }

               map.put("groupIds", groupIds);
            }
         }
      }

      if ("ORGAN".equalsIgnoreCase(listType)) {
         this.addWhereOrgan(map);
      }

      Boolean canReadUnshared = (Boolean)map.get("canReadUnshared");
      if (canReadUnshared == null) {
         map.put("canReadUnshared", false);
      }

      String[] playlist_type_array;
      if (device_type != null && device_type.length() > 0) {
         playlist_type_array = device_type.split(",");
         if (playlist_type_array != null && playlist_type_array.length > 0) {
            map.put("deviceTypeArray", playlist_type_array);
         }
      }

      if (sortColumn != null && sortColumn.length() > 0) {
         map.put("sortColumn", ((String)map.get("sortColumn")).toUpperCase());
         if (sortOrder != null && sortOrder.length() > 0) {
            map.put("sortOrder", ((String)map.get("sortOrder")).toUpperCase());
         }
      }

      if (map.get("category") != null && !map.get("category").equals("")) {
         String category = (String)map.get("category");
         String[] categoryList = category.split(",");
         List contentIdList = ((PlaylistDaoMapper)this.getMapper()).getPlaylistIdfromCategory(categoryList);
         List playlistIds = new ArrayList();
         if (contentIdList != null && contentIdList.size() > 0) {
            Iterator var28 = contentIdList.iterator();

            while(var28.hasNext()) {
               Map content = (Map)var28.next();
               playlistIds.add((String)content.get("playlist_id"));
            }
         } else {
            playlistIds.add("-1");
         }

         map.put("playlistIdList", playlistIds);
      }

      map.put("typeVersion", 2.0F);
      map.put("ConstSHARE_FLAG_DEFAULT", ContentConstants.SHARE_FLAG_DEFAULT);
      map.put("ConstGROUP_TYPE_UNGROUPED", "UNGROUPED");
      map.put("ConstGROUP_TYPE_GROUPED", "GROUPED");
      map.put("ConstGROUP_TYPE_USER", "USER");
      map.put("ConstGROUP_TYPE_ORGAN", "ORGAN");
      map.put("ConstGROUP_TYPE_SHARED", "SHARED");
      map.put("ConstGROUP_TYPE_ALL", "ALL");
      map.put("ConstGROUP_TYPE_DELETED", "DELETED");
      map.put("ConstTYPE_SOC", "SPLAYER");
      map.put("ConstTYPE_VERSION_1_0", CommonDataConstants.TYPE_VERSION_1_0);
      map.put("ConstTYPE_SOC2", "S2PLAYER");
      map.put("ConstTYPE_VERSION_2_0", CommonDataConstants.TYPE_VERSION_2_0);
      map.put("ConstTYPE_SOC3", "S3PLAYER");
      map.put("ConstTYPE_VERSION_3_0", CommonDataConstants.TYPE_VERSION_3_0);
      map.put("ConstTYPE_PREMIUM", "iPLAYER");
      map.put("ConstTYPE_APLAYER", "APLAYER");
      map.put("ConstTYPE_WPLAYER", "WPLAYER");
      map.put("ConstPLAYLIST_TYPE_PREMIUM", "0");
      map.put("ConstPLAYLIST_TYPE_SYNCPLAY", "3");
      map.put("ConstPLAYLIST_TYPE_AMS", "1");
      map.put("ConstPLAYLIST_TYPE_VWL", "2");
      map.put("ConstPLAYLIST_TYPE_VWL", "5");
      if (playlist_type != null && playlist_type.length() > 0) {
         playlist_type_array = playlist_type.split(",");
         if (playlist_type_array != null && playlist_type_array.length > 0) {
            map.put("playlist_type_filter", Arrays.asList(playlist_type_array));
         }
      }

      if (search_start_date != null && search_start_date.length() > 0) {
         map.put("searchStartDate", search_start_date + " 00:00:00");
      }

      if (search_end_date != null && search_end_date.length() > 0) {
         map.put("searchEndDate", search_end_date + " 23:59:59");
      }

      return map;
   }

   public int getPlaylistListByFilterCnt(Map map) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getPlaylistListCnt(this.getContentListByFilter(map));
      } catch (Exception var3) {
         this.logger.error(var3);
         return 0;
      }
   }

   public int deletePlaylistVersion(String playlistId, String playlistVersionId, String userId) throws SQLException {
      Long versionId = Long.parseLong(playlistVersionId);
      return ((PlaylistDaoMapper)this.getMapper()).deletePlaylistVersion(playlistId, versionId, userId);
   }

   public Long getPlaylistMaxVer(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistMaxVer(playlistId);
   }

   public int setVersionPlaylistActive(String playlistId, Long versionId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).setVersionPlaylistActive(playlistId, versionId);
   }

   public Boolean isExistMapPlaylistID(String playlistId, Long playlistVersionId) throws SQLException {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).isExistMapPlaylistID(playlistId, playlistVersionId) > 0;
      } catch (Exception var4) {
         this.logger.error(var4);
         return false;
      }
   }

   public List getActivePlaylistCountOne(String contentId) {
      try {
         return ((PlaylistDaoMapper)this.getMapper()).getActivePlaylistCountOne(contentId);
      } catch (SQLException var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public int setPlaylistModifiedDate(String playlistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).setPlaylistModifiedDate(playlistId);
   }

   public List getContentCountByPlaylist() throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getContentCountByPlaylist();
   }

   public List getContentTypeCountByPlaylist() throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getContentTypeCountByPlaylist();
   }

   public List getPlaylistCountByPlaylistType() throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistCountByPlaylistType();
   }

   public int getPlaylistGroupTotalCount() throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistGroupTotalCount();
   }

   public int getPlaylistCountBySubPlaylist(String subPlaylistId) throws SQLException {
      return ((PlaylistDaoMapper)this.getMapper()).getPlaylistCountBySubPlaylist(subPlaylistId);
   }
}
