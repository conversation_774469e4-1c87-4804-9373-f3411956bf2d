package com.samsung.magicinfo.webauthor2.service;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.service.UnauthorizedAccessException;
import com.samsung.magicinfo.webauthor2.service.LogService;
import com.samsung.magicinfo.webauthor2.service.UserService;
import com.samsung.magicinfo.webauthor2.util.LogLevelUtil;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.Date;
import java.util.Iterator;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.filefilter.FalseFileFilter;
import org.apache.commons.io.filefilter.IOFileFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
public class LogServiceImpl implements LogService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.LogServiceImpl.class);
  
  private static final String LOGS_PATTERN = "magicinfo-webauthor";
  
  private static final String MAGIC_INFO_PREMIUM_HOME_ENV = "MAGICINFO_PREMIUM_HOME";
  
  private static final String CATALINA_BASE_ENV = "CATALINA_BASE";
  
  private UserService userService;
  
  private ServletContext servletContext;
  
  private UserData userData;
  
  @Autowired
  public LogServiceImpl(UserService userService, ServletContext servletContext, UserData userData) {
    this.userService = userService;
    this.servletContext = servletContext;
    this.userData = userData;
  }
  
  public final Path getLogsZip(int lastDays) throws IOException {
    if (userIsNotAuthenticatedOrAuthorized())
      throw new UnauthorizedAccessException("Access forbidden."); 
    Assert.isTrue((lastDays >= 0), "Days can't be negative number.");
    Path logFile = getLogFilePath();
    try (ZipOutputStream zos = new ZipOutputStream(Files.newOutputStream(logFile, new java.nio.file.OpenOption[0]))) {
      addTomcatLogsDirectory(zos, lastDays);
      if (Files.notExists(logFile, new java.nio.file.LinkOption[0]))
        throw new FileNotFoundException("Can't find logs file."); 
      return logFile;
    } 
  }
  
  public final void setLogLevel(String level) {
    Assert.notNull(level, "Level can't be null!");
    if (userIsNotAuthenticatedOrAuthorized())
      throw new UnauthorizedAccessException("NOT ALLOWED!"); 
    if (level.toLowerCase().equals("default")) {
      LogLevelUtil.setDefaultLogLevel();
    } else {
      LogLevelUtil.setLogLevel(level);
    } 
  }
  
  private boolean userIsNotAuthenticatedOrAuthorized() {
    return (Strings.isNullOrEmpty(this.userData.getUserId()) || !this.userService.isUserAdmin(this.userData.getUserId()));
  }
  
  private Path getLogFilePath() throws IOException {
    Path logFileDir = Paths.get(this.servletContext.getRealPath("insertContents"), new String[] { this.userData.getUserId() + "_" + 
          Long.toString((new Date()).getTime()) });
    Files.createDirectories(logFileDir, (FileAttribute<?>[])new FileAttribute[0]);
    return Paths.get(logFileDir.toString(), new String[] { "log.zip" });
  }
  
  private void addTomcatLogsDirectory(ZipOutputStream zos, int lastDays) throws IOException {
    Iterator<File> files = getFilesFromTomcatToZip(lastDays);
    String tomcatDir = "tomcat/";
    ZipEntry tomcatDirEntry = new ZipEntry(tomcatDir);
    zos.putNextEntry(tomcatDirEntry);
    zos.closeEntry();
    while (files.hasNext()) {
      File file = files.next();
      ZipEntry fileEntry = new ZipEntry(tomcatDir + file.getName());
      zos.putNextEntry(fileEntry);
      try (InputStream in = new FileInputStream(file)) {
        IOUtils.copy(in, zos);
      } 
      zos.closeEntry();
    } 
  }
  
  private Iterator<File> getFilesFromTomcatToZip(int lastDays) throws IOException {
    Path tomcatLogsDir;
    String catalinaBase = System.getenv("CATALINA_BASE");
    if (!Strings.isNullOrEmpty(catalinaBase)) {
      tomcatLogsDir = Paths.get(catalinaBase, new String[] { "logs" });
    } else {
      String magicInfoPremiumHome = System.getenv("MAGICINFO_PREMIUM_HOME");
      if (!Strings.isNullOrEmpty(magicInfoPremiumHome)) {
        tomcatLogsDir = Paths.get(magicInfoPremiumHome, new String[] { "tomcat/logs" });
      } else {
        throw new FileNotFoundException("Can't find logs directory. No 'MAGICINFO_PREMIUM_HOME' or 'CATALINA_BASE' environment variables");
      } 
    } 
    logger.info("Getting logs from directory: {}", tomcatLogsDir);
    Date cutoffDate = getCutoffDate(lastDays);
    Object object = new Object(this, cutoffDate);
    return FileUtils.iterateFiles(tomcatLogsDir.toFile(), (IOFileFilter)object, FalseFileFilter.FALSE);
  }
  
  private Date getCutoffDate(int lastDays) {
    Date cutoffDate;
    if (lastDays == 0) {
      cutoffDate = new Date(0L);
    } else {
      long millisPerDay = 86400000L;
      cutoffDate = new Date(System.currentTimeMillis() - millisPerDay * lastDays);
    } 
    return cutoffDate;
  }
}
