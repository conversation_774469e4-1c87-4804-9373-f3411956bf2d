package com.samsung.magicinfo.webauthor.model;

import java.util.Arrays;
import java.util.List;

public enum MediaType {
  IMAGE, MOVIE, OFFICE, FLASH, SOUND, PDF, LFD, LFT, DLK, VWL, FTP, CIFS, STRM, TLFD, HTML, URL;
  
  public static List<com.samsung.magicinfo.webauthor.model.MediaType> getBasicMediaTypeList() {
    return Arrays.asList(new com.samsung.magicinfo.webauthor.model.MediaType[] { IMAGE, MOVIE, OFFICE, FLASH, SOUND, PDF, FTP, CIFS, HTML, URL });
  }
}
