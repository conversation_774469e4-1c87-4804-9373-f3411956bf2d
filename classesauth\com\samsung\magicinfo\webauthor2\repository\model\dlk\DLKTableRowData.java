package com.samsung.magicinfo.webauthor2.repository.model.dlk;

import com.samsung.magicinfo.webauthor2.repository.model.dlk.DLKTableColumnData;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
public class DLKTableRowData {
  @XmlElement(name = "entry")
  List<DLKTableColumnData> columns;
  
  public List<DLKTableColumnData> getColumns() {
    return this.columns;
  }
  
  public void setColumns(List<DLKTableColumnData> columns) {
    this.columns = columns;
  }
}
