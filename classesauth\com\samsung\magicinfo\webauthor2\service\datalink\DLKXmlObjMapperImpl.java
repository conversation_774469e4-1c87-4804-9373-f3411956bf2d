package com.samsung.magicinfo.webauthor2.service.datalink;

import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.model.datalink.ConvertTable;
import com.samsung.magicinfo.webauthor2.model.datalink.ConvertTableRow;
import com.samsung.magicinfo.webauthor2.model.datalink.DLKData;
import com.samsung.magicinfo.webauthor2.model.datalink.DLKDataType;
import com.samsung.magicinfo.webauthor2.model.datalink.DataLinkDescriptor;
import com.samsung.magicinfo.webauthor2.model.datalink.DynamicDLKData;
import com.samsung.magicinfo.webauthor2.model.datalink.Element;
import com.samsung.magicinfo.webauthor2.model.datalink.ElementType;
import com.samsung.magicinfo.webauthor2.model.datalink.FileValue;
import com.samsung.magicinfo.webauthor2.model.datalink.FolderValue;
import com.samsung.magicinfo.webauthor2.model.datalink.LFTContent;
import com.samsung.magicinfo.webauthor2.model.datalink.Page;
import com.samsung.magicinfo.webauthor2.model.datalink.SplitGroup;
import com.samsung.magicinfo.webauthor2.model.datalink.StaticDLKData;
import com.samsung.magicinfo.webauthor2.model.datalink.SyncGroup;
import com.samsung.magicinfo.webauthor2.model.datalink.TagList;
import com.samsung.magicinfo.webauthor2.model.datalink.TagListDynamicData;
import com.samsung.magicinfo.webauthor2.model.datalink.TagMatchType;
import com.samsung.magicinfo.webauthor2.model.datalink.TextValue;
import com.samsung.magicinfo.webauthor2.model.datalink.Value;
import com.samsung.magicinfo.webauthor2.model.datalink.ValueLocation;
import com.samsung.magicinfo.webauthor2.model.datalink.ValueLocationType;
import com.samsung.magicinfo.webauthor2.service.datalink.DLKXmlObjMapper;
import com.samsung.magicinfo.webauthor2.xml.datalink.ConvertTableRowType;
import com.samsung.magicinfo.webauthor2.xml.datalink.ConvertTableType;
import com.samsung.magicinfo.webauthor2.xml.datalink.DataLinkContentMetaType;
import com.samsung.magicinfo.webauthor2.xml.datalink.DataType;
import com.samsung.magicinfo.webauthor2.xml.datalink.ElementType;
import com.samsung.magicinfo.webauthor2.xml.datalink.ErrorType;
import com.samsung.magicinfo.webauthor2.xml.datalink.FileInfoType;
import com.samsung.magicinfo.webauthor2.xml.datalink.LFDContentType;
import com.samsung.magicinfo.webauthor2.xml.datalink.PageType;
import com.samsung.magicinfo.webauthor2.xml.datalink.SettingType;
import com.samsung.magicinfo.webauthor2.xml.datalink.SplitGroupType;
import com.samsung.magicinfo.webauthor2.xml.datalink.SyncGroupType;
import com.samsung.magicinfo.webauthor2.xml.datalink.TagListType;
import com.samsung.magicinfo.webauthor2.xml.datalink.ValueLocationType;
import com.samsung.magicinfo.webauthor2.xml.datalink.ValueType;
import com.samsung.magicinfo.webauthor2.xml.datalink.ValuesType;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Service;

@Service
public class DLKXmlObjMapperImpl implements DLKXmlObjMapper {
  public DataLinkContentMetaType toXmlObject(DataLinkDescriptor dataLinkDescriptor) {
    if (dataLinkDescriptor == null)
      throw new IllegalArgumentException("dataLinkDescriptor obj cannot be null"); 
    DataLinkContentMetaType dataLinkContentMetaType = new DataLinkContentMetaType();
    dataLinkContentMetaType.setLFDContent(mapLFDContent(dataLinkDescriptor.getLftContent()));
    dataLinkContentMetaType.setSetting(mapSettings(dataLinkDescriptor.getPollingInterval(), dataLinkDescriptor.getBackupForADay()));
    dataLinkContentMetaType.setVersion(Byte.valueOf("1"));
    List<PageType> xmlPages = dataLinkContentMetaType.getPage();
    int pageNumb = 0;
    for (Page page : dataLinkDescriptor.getPages()) {
      xmlPages.add(mapPage(page, pageNumb));
      pageNumb++;
    } 
    return dataLinkContentMetaType;
  }
  
  public DataLinkDescriptor fromXmlObject(DataLinkContentMetaType xmlObj) {
    if (xmlObj == null)
      throw new IllegalArgumentException("xmlObj cannot be null"); 
    List<Page> pages = new ArrayList<>();
    for (PageType pageType : xmlObj.getPage())
      pages.add(mapPageType(pageType)); 
    LFDContentType xmlLFTContent = xmlObj.getLFDContent();
    DataLinkDescriptor dataLinkDescriptor = new DataLinkDescriptor(mapSettingsTypePollingInterval(xmlObj.getSetting()), mapSettingsTypeBackupForADay(xmlObj.getSetting()), pages);
    dataLinkDescriptor.setLftContent(mapLFDContent(xmlLFTContent, dataLinkDescriptor));
    return dataLinkDescriptor;
  }
  
  private LFTContent mapLFDContent(LFDContentType lfdContentType, DataLinkDescriptor dataLinkDescriptor) {
    if (lfdContentType == null)
      return null; 
    dataLinkDescriptor.setLftContentId(lfdContentType.getId());
    return new LFTContent(lfdContentType.getId(), lfdContentType.getFileID(), lfdContentType
        .getFileName(), lfdContentType.getFileHashValue(), lfdContentType.getFileSize());
  }
  
  private LFDContentType mapLFDContent(LFTContent lfdContent) {
    if (lfdContent == null)
      return null; 
    LFDContentType lfdContentType = new LFDContentType();
    lfdContentType.setId(lfdContent.getContentId());
    lfdContentType.setFileHashValue(lfdContent.getFileHash());
    lfdContentType.setFileID(lfdContent.getFileId());
    lfdContentType.setFileName(lfdContent.getFileName());
    lfdContentType.setFileSize(lfdContent.getFileSize());
    return lfdContentType;
  }
  
  private SettingType mapSettings(long pollingInterval, long backupForADay) {
    SettingType settingType = new SettingType();
    settingType.setPollingInterval(pollingInterval);
    settingType.setBackupForADay(backupForADay);
    return settingType;
  }
  
  private long mapSettingsTypePollingInterval(SettingType settingType) {
    Validate.notNull(settingType);
    return settingType.getPollingInterval();
  }
  
  private long mapSettingsTypeBackupForADay(SettingType settingType) {
    Validate.notNull(settingType);
    return settingType.getBackupForADay();
  }
  
  private PageType createFirstXmlPage() {
    PageType defaultPage = new PageType();
    defaultPage.setNo(0);
    defaultPage.setName("Page for Global Elements");
    return defaultPage;
  }
  
  private PageType mapPage(Page page, int pageNumb) {
    PageType xmlPage = new PageType();
    xmlPage.setNo(pageNumb);
    xmlPage.setName(page.getName());
    List<SyncGroupType> xmlSyncGroups = new ArrayList<>();
    if (page.getSyncGroups() == null || page.getSyncGroups().size() == 0)
      return xmlPage; 
    for (SyncGroup syncGroup : page.getSyncGroups())
      xmlSyncGroups.add(mapSyncGroup(syncGroup)); 
    xmlPage.setSyncGroups(xmlSyncGroups);
    return xmlPage;
  }
  
  private Page mapPageType(PageType pageType) {
    List<SyncGroup> syncGroups = new ArrayList<>();
    if (pageType.getSyncGroups() == null)
      return new Page(pageType.getName(), null); 
    for (SyncGroupType xmlSyncGroup : pageType.getSyncGroups())
      syncGroups.add(mapSyncGroupType(xmlSyncGroup)); 
    return new Page(pageType.getName(), syncGroups);
  }
  
  private SplitGroupType mapSplitGroup(SplitGroup splitGroup) {
    SplitGroupType splitGroupType = new SplitGroupType();
    splitGroupType.setId(Integer.valueOf(splitGroup.getId()));
    splitGroupType.setName(splitGroup.getName());
    List<ElementType> xmlElements = splitGroupType.getElement();
    int elemNumb = 1;
    for (Element element : splitGroup.getElements()) {
      xmlElements.add(mapElement(element, elemNumb));
      elemNumb++;
    } 
    List<DataType> xmlDataList = splitGroupType.getData();
    if (splitGroup.getDataList() != null && !splitGroup.getDataList().isEmpty())
      for (DLKData dlkData : splitGroup.getDataList())
        xmlDataList.add(mapDLKData(dlkData));  
    return splitGroupType;
  }
  
  private SplitGroup mapSplitGroupType(SplitGroupType xmlSplitGroup) {
    List<Element> elements = new ArrayList<>();
    for (ElementType xmlElement : xmlSplitGroup.getElement())
      elements.add(mapElementType(xmlElement)); 
    List<DLKData> dlkDataList = new ArrayList<>();
    for (DataType dataType : xmlSplitGroup.getData())
      dlkDataList.add(mapDLKDataType(dataType)); 
    return new SplitGroup(xmlSplitGroup.getId().intValue(), xmlSplitGroup.getName(), elements, dlkDataList);
  }
  
  private SyncGroupType mapSyncGroup(SyncGroup syncGroup) {
    SyncGroupType syncGroupType = new SyncGroupType();
    syncGroupType.setId(Integer.valueOf(syncGroup.getId()));
    List<SplitGroupType> xmlSplitGroups = new ArrayList<>();
    for (SplitGroup splitGroup : syncGroup.getSplitGroups())
      xmlSplitGroups.add(mapSplitGroup(splitGroup)); 
    syncGroupType.setSplitGroups(xmlSplitGroups);
    return syncGroupType;
  }
  
  private SyncGroup mapSyncGroupType(SyncGroupType xmlSyncGroup) {
    List<SplitGroup> splitGroups = new ArrayList<>();
    for (SplitGroupType xmlSplitGroup : xmlSyncGroup.getSplitGroups())
      splitGroups.add(mapSplitGroupType(xmlSplitGroup)); 
    return new SyncGroup(xmlSyncGroup.getId().intValue(), splitGroups);
  }
  
  private ElementType mapElement(Element element, int number) {
    ElementType xmlElement = new ElementType();
    xmlElement.setName(element.getName());
    xmlElement.setNo(number);
    xmlElement.setType(element.getElementType().name());
    xmlElement.setPositionX(element.getPositionX());
    xmlElement.setPositionY(element.getPositionY());
    xmlElement.setWidth(element.getWidth());
    xmlElement.setHeight(element.getHeight());
    xmlElement.setChangeDuration(element.getChangeDuration());
    ErrorType errorType = new ErrorType();
    errorType.setKeepLastValue(element.isKeepLastValue());
    xmlElement.setError(errorType);
    List<DataType> xmlDataList = xmlElement.getData();
    if (element.getDataList() != null && !element.getDataList().isEmpty())
      for (DLKData dlkData : element.getDataList())
        xmlDataList.add(mapDLKData(dlkData));  
    return xmlElement;
  }
  
  private Element mapElementType(ElementType element) {
    boolean keepLastValue = true;
    if (element.getError() != null)
      keepLastValue = element.getError().getKeepLastValue(); 
    List<DLKData> dlkDataList = new ArrayList<>();
    List<DataType> xmlDataList = element.getData();
    for (DataType dataType : xmlDataList)
      dlkDataList.add(mapDLKDataType(dataType)); 
    ElementType modelElementType = ElementType.valueOf(element.getType());
    Element xmlElement = new Element(element.getName(), modelElementType, element.getChangeDuration(), element.getPositionX(), element.getPositionY(), element.getWidth(), element.getHeight(), keepLastValue, dlkDataList);
    return xmlElement;
  }
  
  private DataType mapDLKData(DLKData dlkData) {
    DataType dataType = new DataType();
    dataType.setType(dlkData.getType().name());
    if (dlkData.getType() == DLKDataType.Static) {
      StaticDLKData staticDLKData = (StaticDLKData)dlkData;
      dataType.setServerAddress("");
      dataType.setValueLocation(new ValueLocationType());
      dataType.setValues(new ValuesType(mapValue(staticDLKData.getValue())));
      dataType.setTagList(mapTagList(DLKDataType.Static, staticDLKData.getTagList()));
    } else {
      DynamicDLKData dynamicDLKData = (DynamicDLKData)dlkData;
      dataType.setServerAddress(dynamicDLKData.getServerName());
      dataType.setValueLocation(mapValueLocation(dynamicDLKData.getValueLocation()));
      dataType.setValues(new ValuesType());
      dataType.setTagList(mapTagList(DLKDataType.Dynamic, (TagList)dynamicDLKData.getTagList()));
      dataType.setConvertTable(mapConvertTable(dynamicDLKData.getConvertTable()));
    } 
    return dataType;
  }
  
  private DLKData mapDLKDataType(DataType xmlData) {
    DynamicDLKData dynamicDLKData;
    DLKDataType dlkDataType = DLKDataType.valueOf(xmlData.getType());
    if (dlkDataType == DLKDataType.Static) {
      Value staticValue = null;
      if (xmlData.getValues() != null)
        staticValue = mapValuesType(xmlData.getValues().getValue()); 
      StaticDLKData staticDLKData = new StaticDLKData(staticValue, mapTagListType(dlkDataType, xmlData.getTagList()));
    } else {
      String serverName = xmlData.getServerAddress();
      TagListDynamicData tagListDynamicData = (TagListDynamicData)mapTagListType(dlkDataType, xmlData.getTagList());
      dynamicDLKData = new DynamicDLKData(serverName, mapValueLocationType(xmlData.getValueLocation()), tagListDynamicData, mapConvertTableType(xmlData.getConvertTable()));
    } 
    return (DLKData)dynamicDLKData;
  }
  
  private ValueLocationType mapValueLocation(ValueLocation valueLocation) {
    if (valueLocation == null)
      return null; 
    ValueLocationType xmlValueLocationType = new ValueLocationType();
    xmlValueLocationType.setView(valueLocation.getValueLocationType().getShortName());
    xmlValueLocationType.setValue(valueLocation.getValue());
    return xmlValueLocationType;
  }
  
  private ValueLocation mapValueLocationType(ValueLocationType xmlValueLocation) {
    if (xmlValueLocation == null)
      return null; 
    String valueLocationValue = xmlValueLocation.getValue();
    ValueLocationType valueLocationType = ValueLocationType.getValueLocationTypeByShortName(xmlValueLocation.getView());
    return new ValueLocation(valueLocationValue, valueLocationType);
  }
  
  private TagListType mapTagList(DLKDataType dlkDataType, TagList tagList) {
    if (tagList == null)
      return null; 
    TagListType tagListType = new TagListType();
    tagListType.setMatchType(tagList.getTagMatchType().name());
    tagListType.setTagList(tagList.getTags());
    if (dlkDataType == DLKDataType.Dynamic) {
      TagListDynamicData tagListDynamicData = (TagListDynamicData)tagList;
      tagListType.setColumn(tagListDynamicData.getDataLinkTagColumn());
    } 
    return tagListType;
  }
  
  private TagList mapTagListType(DLKDataType dlkDataType, TagListType xmlTagList) {
    TagListDynamicData tagListDynamicData;
    if (xmlTagList == null)
      return null; 
    if (dlkDataType == DLKDataType.Static) {
      TagList tagList = new TagList(xmlTagList.getTagList(), TagMatchType.valueOf(xmlTagList.getMatchType()));
    } else {
      tagListDynamicData = new TagListDynamicData(xmlTagList.getTagList(), TagMatchType.valueOf(xmlTagList.getMatchType()), xmlTagList.getColumn());
    } 
    return (TagList)tagListDynamicData;
  }
  
  private ValueType mapValue(Value value) {
    TextValue textValue;
    FileValue fileValue;
    String fileIdAndName;
    FileInfoType fileInfoType;
    ValueType xmlValue = null;
    switch (null.$SwitchMap$com$samsung$magicinfo$webauthor2$model$datalink$ValueType[value.getType().ordinal()]) {
      case 1:
        textValue = (TextValue)value;
        xmlValue = new ValueType(textValue.getValue());
        break;
      case 2:
      case 3:
        fileValue = (FileValue)value;
        fileIdAndName = createFileRef(fileValue.getId(), fileValue.getName());
        fileInfoType = new FileInfoType();
        fileInfoType.setFileSize(fileValue.getFileSize());
        fileInfoType.setValue(fileIdAndName);
        xmlValue = new ValueType(fileIdAndName, fileInfoType);
        break;
    } 
    return xmlValue;
  }
  
  private Value mapValuesType(ValueType xmlValue) {
    FileValue fileValue;
    if (xmlValue.getFileInfo() == null) {
      TextValue textValue = new TextValue(xmlValue.getContent());
    } else {
      FileInfoType xmlFileInfo = xmlValue.getFileInfo();
      String fileName = FilenameUtils.getName(xmlFileInfo.getValue());
      String fileExt = FilenameUtils.getExtension(fileName);
      String id = FilenameUtils.getPath(xmlFileInfo.getValue()).replace("\\", "");
      long fileSize = xmlFileInfo.getFileSize();
      if (fileExt.equals("rlf")) {
        FolderValue folderValue = new FolderValue(fileName, id, fileSize);
      } else {
        fileValue = new FileValue(fileName, id, fileSize);
      } 
    } 
    return (Value)fileValue;
  }
  
  private ConvertTableType mapConvertTable(ConvertTable convertTable) {
    if (convertTable == null)
      return null; 
    ConvertTableType xmlConvertTable = new ConvertTableType();
    xmlConvertTable.setName(convertTable.getName());
    List<ConvertTableRowType> xmlConvertTableRows = new ArrayList<>();
    for (ConvertTableRow row : convertTable.getRows()) {
      ConvertTableRowType xmlRow = new ConvertTableRowType();
      xmlRow.setType(row.getType());
      xmlRow.setFrom(row.getFrom());
      xmlRow.setTo(mapValue(row.getToValue()));
      xmlConvertTableRows.add(xmlRow);
    } 
    xmlConvertTable.setRows(xmlConvertTableRows);
    return xmlConvertTable;
  }
  
  private ConvertTable mapConvertTableType(ConvertTableType xmlConvertTable) {
    if (xmlConvertTable == null)
      return null; 
    List<ConvertTableRow> convertTableRows = new ArrayList<>();
    for (ConvertTableRowType xmlRow : xmlConvertTable.getRows())
      convertTableRows.add(new ConvertTableRow(xmlRow.getType(), xmlRow.getFrom(), mapValuesType(xmlRow.getTo()))); 
    ConvertTable convertTable = new ConvertTable(convertTableRows);
    String xmlConvertTableName = xmlConvertTable.getName();
    if (!Strings.isNullOrEmpty(xmlConvertTableName))
      convertTable.setName(xmlConvertTableName); 
    return convertTable;
  }
  
  private String createFileRef(String fileId, String fileName) {
    return Joiner.on("\\").join(fileId, fileName, new Object[0]);
  }
}
