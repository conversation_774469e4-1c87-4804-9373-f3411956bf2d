package com.samsung.magicinfo.webauthor2.repository.model;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "response")
public class TokenResponseData implements Serializable {
  private static final long serialVersionUID = 1491216309423212182L;
  
  @XmlAttribute
  private String code;
  
  @XmlElement(name = "responseClass")
  private String token;
  
  public String getCode() {
    return this.code;
  }
  
  public String getToken() {
    return this.token;
  }
}
