package com.samsung.magicinfo.rc.common.exception;

import com.samsung.magicinfo.rc.common.exception.RestErrorInfo;
import com.samsung.magicinfo.rc.common.exception.RestServiceException;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
@Order(0)
public class GlobalRestExceptionHandler {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.common.exception.GlobalRestExceptionHandler.class);
  
  public static final int ORDER = 0;
  
  @ExceptionHandler({RestServiceException.class})
  public RestErrorInfo handle(HttpServletRequest req, RestServiceException e) {
    Throwable t = (Throwable)req.getAttribute("javax.servlet.error.exception");
    RestServiceException exception = (RestServiceException)t;
    if (exception != null)
      return new RestErrorInfo(null, exception.getErrorCode(), exception.getErrorMessage()); 
    return new RestErrorInfo(null, "", "");
  }
}
