package com.samsung.magicinfo.webauthor2.webapi.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping({"/preview"})
public class ExEventPreviewController {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.webapi.controller.ExEventPreviewController.class);
  
  @Autowired
  public ExEventPreviewController() {
    logger.info("ExEventPreviewController");
  }
  
  @GetMapping
  public String getPreview() {
    logger.info("GET: getPreview()");
    return goToExEventPreviewMainPage();
  }
  
  @ExceptionHandler({Exception.class})
  public String exEventPreviewControllerErrors(Exception ex, Model model) {
    logger.error(ex.getMessage());
    return "common/error";
  }
  
  private String goToExEventPreviewMainPage() {
    logger.info("Accessed ExEventPreview page.");
    return "preview";
  }
}
