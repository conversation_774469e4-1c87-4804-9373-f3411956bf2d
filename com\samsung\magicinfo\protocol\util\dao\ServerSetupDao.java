package com.samsung.magicinfo.protocol.util.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.setup.entity.ServerManagementEntity;
import com.samsung.magicinfo.framework.setup.entity.SystemLogEntity;
import com.samsung.magicinfo.protocol.util.BeanUtils;
import com.samsung.magicinfo.restapi.setting.model.config.MFA;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManager;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class ServerSetupDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(ServerSetupDao.class);

   public ServerSetupDao() {
      super();
   }

   public List getAllServerInfo() {
      List result = null;

      try {
         result = ((ServerSetupDaoMapper)this.getMapper()).getAllServerInfo();
      } catch (SQLException var3) {
         this.logger.error("", var3);
      }

      return result;
   }

   public Map getServerCommonInfo() {
      Map result = null;

      try {
         result = ((ServerSetupDaoMapper)this.getMapper()).getServerCommonInfo();
      } catch (SQLException var3) {
         this.logger.error("", var3);
      }

      return result;
   }

   public Boolean checkPermissionsDevice() throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).checkPermissionsDevice();
   }

   public Map getServerInfoByOrgId(Long orgId) {
      Map result = null;
      Map nullCheckMap = new HashMap();
      PIIDataManager piiDataManager = (PIIDataManager)BeanUtils.getBean("PIIDataManager");
      int flag = 0;
      Long defaultErrCnt = 3L;
      Boolean defaultExtServerEnable = false;
      Boolean defaultNotificationEnable = true;

      try {
         result = ((ServerSetupDaoMapper)this.getMapper()).getServerInfoByOrgId(orgId);
         if (result != null) {
            if (result.get("EXT_SERVER_ERR_CHK") == null) {
               nullCheckMap.put("EXT_SERVER_ERR_CHK", defaultErrCnt);
               ++flag;
            }

            if (result.get("EXT_SERVER_DN_MON_ENABLE") == null) {
               nullCheckMap.put("EXT_SERVER_DN_MON_ENABLE", defaultExtServerEnable);
               ++flag;
            }

            if (result.get("EXT_SERVER_DL_MON_ENABLE") == null) {
               nullCheckMap.put("EXT_SERVER_DL_MON_ENABLE", defaultExtServerEnable);
               ++flag;
            }

            if (result.get("EXT_SERVER_RM_MON_ENABLE") == null) {
               nullCheckMap.put("EXT_SERVER_RM_MON_ENABLE", defaultExtServerEnable);
               ++flag;
            }

            if (result.get("NOTIFICATION_ENABLE") == null) {
               nullCheckMap.put("NOTIFICATION_ENABLE", defaultNotificationEnable);
               ++flag;
            }

            if (flag > 0) {
               nullCheckMap.put("ORGANIZATION_ID", orgId);
               this.updateServerInfo(nullCheckMap);
               result = ((ServerSetupDaoMapper)this.getMapper()).getServerInfoByOrgId(orgId);
            }

            if (null != piiDataManager && result.get("SMTP_AUTH_ID") != null) {
               result.put("SMTP_AUTH_ID", piiDataManager.decryptData(((Comparable)result.get("SMTP_AUTH_ID")).toString()));
            }

            Map rootOrgInfo;
            String[] ldapInfoColumnNames;
            String[] var11;
            int var12;
            int var13;
            String ldapColumnName;
            if (orgId != 0L && !this.isSmtpSeparateSettings()) {
               rootOrgInfo = ((ServerSetupDaoMapper)this.getMapper()).getServerInfoByOrgId(0L);
               ldapInfoColumnNames = new String[]{"SMTP_ENABLE", "SMTP_ADDRESS", "SMTP_AUTH_ENABLE", "SMTP_AUTH_ID", "SMTP_AUTH_PWD", "SMTP_AUTH_PORT", "SMTP_AUTH_SSL_ENABLE"};
               var11 = ldapInfoColumnNames;
               var12 = ldapInfoColumnNames.length;

               for(var13 = 0; var13 < var12; ++var13) {
                  ldapColumnName = var11[var13];
                  if (rootOrgInfo.get(ldapColumnName) == null) {
                     result.remove(ldapColumnName);
                  } else {
                     result.put(ldapColumnName, rootOrgInfo.get(ldapColumnName));
                  }
               }
            }

            if (orgId != 0L && !this.isLdapSeparateSettings()) {
               rootOrgInfo = ((ServerSetupDaoMapper)this.getMapper()).getServerInfoByOrgId(0L);
               ldapInfoColumnNames = new String[]{"LDAP_ENABLE", "LDAP_USE_SERVER_SETTING", "LDAP_SYNC_ENABLE"};
               var11 = ldapInfoColumnNames;
               var12 = ldapInfoColumnNames.length;

               for(var13 = 0; var13 < var12; ++var13) {
                  ldapColumnName = var11[var13];
                  if (rootOrgInfo.get(ldapColumnName) == null) {
                     result.remove(ldapColumnName);
                  } else {
                     result.put(ldapColumnName, rootOrgInfo.get(ldapColumnName));
                  }
               }
            }
         }
      } catch (SQLException var15) {
         this.logger.info("MI_SYSTEM_INFO_SETUP sql error!");
      }

      return result;
   }

   public List getServerMFAInfo(String mfaType) {
      try {
         return ((ServerSetupDaoMapper)this.getMapper()).getServerMFAInfo(mfaType);
      } catch (SQLException var3) {
         this.logger.error(var3.getMessage());
         return null;
      }
   }

   public Integer updateServerMFAInfo(MFA mfa) {
      try {
         return ((ServerSetupDaoMapper)this.getMapper()).updateServerMFAInfo(mfa);
      } catch (SQLException var3) {
         this.logger.error(var3.getMessage());
         return null;
      }
   }

   public Boolean updateServerInfo(Map map) {
      boolean result = false;

      try {
         PIIDataManager piiDataManager = (PIIDataManager)BeanUtils.getBean("PIIDataManager");
         if (map.get("SMTP_AUTH_ID") != null) {
            map.put("SMTP_AUTH_ID", piiDataManager.encryptData(map.get("SMTP_AUTH_ID").toString(), "email"));
         }

         ((ServerSetupDaoMapper)this.getMapper()).updateServerInfo(map);
         String orgIdString = map.get("ORGANIZATION_ID").toString();
         if (Long.parseLong(orgIdString) == 0L) {
            Map smtpInfoMap = new HashMap();
            if (map.get("SMTP_ADDRESS") != null) {
               smtpInfoMap.put("SMTP_ADDRESS", map.get("SMTP_ADDRESS"));
            }

            if (map.get("SMTP_AUTH_ENABLE") != null) {
               smtpInfoMap.put("SMTP_AUTH_ENABLE", map.get("SMTP_AUTH_ENABLE"));
            }

            if (map.get("SMTP_AUTH_ID") != null) {
               smtpInfoMap.put("SMTP_AUTH_ID", map.get("SMTP_AUTH_ID"));
            }

            if (map.get("SMTP_AUTH_PWD") != null) {
               smtpInfoMap.put("SMTP_AUTH_PWD", map.get("SMTP_AUTH_PWD"));
            }

            if (map.get("SMTP_AUTH_PORT") != null) {
               smtpInfoMap.put("SMTP_AUTH_PORT", map.get("SMTP_AUTH_PORT"));
            }

            if (map.get("SMTP_AUTH_SSL_ENABLE") != null) {
               smtpInfoMap.put("SMTP_AUTH_SSL_ENABLE", map.get("SMTP_AUTH_SSL_ENABLE"));
            }

            if (smtpInfoMap.size() > 0) {
               smtpInfoMap.put("UPDATE_ALL_SMTP_USE_SERVER_SETTING", Boolean.TRUE);
               ((ServerSetupDaoMapper)this.getMapper()).updateServerInfo(smtpInfoMap);
            }
         }
      } catch (SQLException var6) {
         this.logger.error("", var6);
      }

      return result;
   }

   public Boolean checkPermissionsDeviceByOrgId(Long orgId) throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).checkPermissionsDeviceByOrgId(orgId);
   }

   public Boolean changePermissionsFunc(boolean check) throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).changePermissionsFunc(check) == 1;
   }

   public Integer getRedundancyEnabledCnt() {
      try {
         return ((ServerSetupDaoMapper)this.getMapper()).getRedundancyEnabledCnt();
      } catch (SQLException var2) {
         this.logger.error("", var2);
         return 0;
      }
   }

   public Boolean isRedundancyEnable() {
      return ((ServerSetupDaoMapper)this.getMapper()).isRedundancyEnable();
   }

   public List getDisconnectEmailAlarmEnabledOrg() {
      try {
         return ((ServerSetupDaoMapper)this.getMapper()).getDisconnectEmailAlarmEnabledOrg();
      } catch (SQLException var2) {
         return null;
      }
   }

   public Boolean addDefaultServerInfo(long orgId) {
      return ((ServerSetupDaoMapper)this.getMapper()).addDefaultServerInfo(orgId);
   }

   public Boolean resetExternalServerErrCount(String serverType, String ipAddress) {
      return ((ServerSetupDaoMapper)this.getMapper()).resetExternalServerErrCount(serverType, ipAddress);
   }

   public Boolean addExternalServerErrCount(String serverType, String ipAddress) {
      return ((ServerSetupDaoMapper)this.getMapper()).addExternalServerErrCount(serverType, ipAddress);
   }

   public Integer getExternalServerErrCount(String serverType, String ipAddress) {
      return ((ServerSetupDaoMapper)this.getMapper()).getExternalServerErrCount(serverType, ipAddress);
   }

   public Boolean addExternalServerForMonitoring(String serverType, String ipAddress, Integer defaultErrorCount) {
      return ((ServerSetupDaoMapper)this.getMapper()).addExternalServerForMonitoring(serverType, ipAddress, defaultErrorCount);
   }

   public Integer isExistExternalServer(String serverType, String ipAddress) {
      return ((ServerSetupDaoMapper)this.getMapper()).isExistExternalServer(serverType, ipAddress);
   }

   public Boolean deleteExternalServerForMonitoring(String serverType, String ipAddress) {
      return ((ServerSetupDaoMapper)this.getMapper()).deleteExternalServerForMonitoring(serverType, ipAddress);
   }

   public Boolean setLastErrTime(String serverType, String ipAddress) {
      return ((ServerSetupDaoMapper)this.getMapper()).setLastErrTime(serverType, ipAddress);
   }

   public Timestamp getLastErrTime(String serverType, String ipAddress) {
      return ((ServerSetupDaoMapper)this.getMapper()).getLastErrTime(serverType, ipAddress);
   }

   public Boolean isLdapEnable(Long orgId) throws SQLException {
      if (!this.isLdapSeparateSettings()) {
         orgId = 0L;
      }

      return ((ServerSetupDaoMapper)this.getMapper()).isLdapEnable(orgId);
   }

   public Boolean isLdapUseServerSetting(Long orgId) throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).isLdapUseServerSetting(orgId);
   }

   public Boolean hasApplyLdapServerSetting() throws SQLException {
      int result = ((ServerSetupDaoMapper)this.getMapper()).hasApplyLdapServerSetting();
      return result > 0;
   }

   public Boolean hasApplySmtpServerSetting() throws SQLException {
      int result = ((ServerSetupDaoMapper)this.getMapper()).hasApplySmtpServerSetting();
      return result > 0;
   }

   public Boolean isLdapSeparateSettings() throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).isLdapSeparateSettings();
   }

   public Boolean isSmtpSeparateSettings() throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).isSmtpSeparateSettings();
   }

   public String getSecretValue(int key_type) throws SQLException {
      Map secvalues = ((ServerSetupDaoMapper)this.getMapper()).getSecretValue();
      String ret = null;
      switch(key_type) {
      case 0:
         ret = (String)secvalues.get("SECRET_VALUE");
         break;
      case 1:
         ret = (String)secvalues.get("FTP_PASSWORD_KEY");
         break;
      case 2:
      default:
         this.logger.fatal("Invalid secret key type!! Fatal error" + key_type);
         break;
      case 3:
         ret = (String)secvalues.get("DEF_MAC");
         break;
      case 4:
         ret = (String)secvalues.get("DEF_CPUID");
         break;
      case 5:
         ret = (String)secvalues.get("DEF_BOARDID");
      }

      return ret;
   }

   public List getRuleMangerEnabledOrgID() throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).getRuleMangerEnabledOrgID();
   }

   public Boolean resetRuleMangerEnabledOrg() throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).resetRuleMangerEnabledOrg();
   }

   public Boolean updateRuleMangerEnabledOrg(Long orgId) throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).updateRuleMangerEnabledOrg(orgId);
   }

   public List getNotificationEnabledOrgList() throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).getNotificationEnabledOrgList();
   }

   public Boolean addLogInfo(SystemLogEntity systemLogEntity) {
      return ((ServerSetupDaoMapper)this.getMapper()).addLogInfo(systemLogEntity);
   }

   public Boolean addServerManagementInfo(ServerManagementEntity entity) throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).addServerManagementInfo(entity);
   }

   public ServerManagementEntity getServerManagementInfo(String managementId) throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).getServerManagementInfo(managementId);
   }

   public Boolean updateServerManagementInfo(ServerManagementEntity entity) throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).updateServerManagementInfo(entity);
   }

   public Boolean updateServerSetupKpi(Map map) throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).updateServerSetupKpi(map);
   }

   public Map getServerSetupKpi() throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).getServerSetupKpi();
   }

   public Boolean addServerSetupKpi(Map map) throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).addServerSetupKpi(map);
   }

   public Integer hideUserIdInLogs(SqlSession session, String userId) throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper(session)).hideUserIdInLogs(userId);
   }

   public String getDefaultPassword() throws SQLException {
      return ((ServerSetupDaoMapper)this.getMapper()).getDefaultPassword();
   }
}
