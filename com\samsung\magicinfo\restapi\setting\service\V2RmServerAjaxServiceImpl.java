package com.samsung.magicinfo.restapi.setting.service;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.framework.setup.entity.RmServerEntity;
import com.samsung.magicinfo.framework.setup.manager.RmServerImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.ConnectException;
import java.net.SocketException;
import java.net.URL;
import java.util.List;
import java.util.Map;
import javax.net.ssl.HttpsURLConnection;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXParseException;

@Service("V2RmServerAjaxService")
@Transactional
public class V2RmServerAjaxServiceImpl implements V2RmServerAjaxService {
   protected Logger logger = LoggingManagerV2.getLogger(V2RmServerAjaxServiceImpl.class);

   public V2RmServerAjaxServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority','Server Setup Manage Authority')")
   public ResponseBody remoteControlServerSearch(HttpServletRequest request, String sort, String dir, String orderCol, String orderDir, String search_text, String results, String startIndex, HttpServletResponse response) throws Exception {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      int intStartIndex = true;
      int intResults = true;
      int intResults = Integer.parseInt(results);
      int intStartIndex = Integer.parseInt(startIndex);
      response.setContentType("application/json; charset=UTF-8");
      StringBuffer resultBuf = new StringBuffer();
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map infoMap = serverSetupDao.getServerInfoByOrgId(0L);
      Boolean rmMonEnable = (Boolean)infoMap.get("EXT_SERVER_RM_MON_ENABLE");
      RmServerImpl rmsDao = RmServerImpl.getInstance();
      request.setAttribute("page", String.valueOf(intStartIndex / intResults + 1));
      ListManager listMgr = new ListManager(request, rmsDao, "commonlist");
      listMgr.addSearchInfo("sortColumn", sort);
      listMgr.addSearchInfo("sortOrder", orderDir);
      listMgr.addSearchInfo("searchText", search_text);
      listMgr.setLstSize(Integer.valueOf(results));
      listMgr.setSection("getRmServerList");
      PageManager pageMgr = null;
      List searchList = listMgr.dbexecute();
      pageMgr = listMgr.getPageManager();
      resultBuf.append("{\"recordsReturned\":" + searchList.size() + ",");
      resultBuf.append("\"totalRecords\":" + pageMgr.getTotalRowCount() + ",");
      resultBuf.append("\"recordsTotal\":" + pageMgr.getTotalRowCount() + ",");
      resultBuf.append("\"startIndex\":" + startIndex + ",");
      if (sort != null && !sort.equals("")) {
         resultBuf.append("\"sort\":\"" + sort + "\",");
         resultBuf.append("\"dir\":\"" + dir + "\",");
      }

      resultBuf.append("\"results\":" + pageMgr.getInfo().getPageSize() + ",");
      resultBuf.append("\"data\":[");

      for(int i = 0; i < searchList.size(); ++i) {
         RmServerEntity rmEntity = (RmServerEntity)searchList.get(i);
         String rmserver_status = null;
         if (i > 0) {
            resultBuf.append(",");
         }

         if (rmMonEnable != null && rmMonEnable) {
            rmserver_status = this.getRMStatusByJob(rmEntity);
         } else {
            rmserver_status = this.getRMStatusByRealTime(rmEntity);
         }

         resultBuf.append("{\"checkbox\":\"\",");
         resultBuf.append("\"server_name\":\"" + rmEntity.getServer_name() + "\",");
         resultBuf.append("\"public_ip\":\"" + rmEntity.getIp_address() + "\",");
         if (rmEntity.getPrivate_ip_address() != null) {
            resultBuf.append("\"private_ip\":\"" + rmEntity.getPrivate_ip_address() + "\",");
         } else {
            resultBuf.append("\"private_ip\":\"-\",");
         }

         if (rmEntity.getPrivate_port() != null) {
            resultBuf.append("\"private_port\":\"" + rmEntity.getPrivate_port() + "\",");
         } else {
            resultBuf.append("\"private_port\":\"\",");
         }

         resultBuf.append("\"private_mode\":\"" + rmEntity.getPrivate_mode() + "\",");
         resultBuf.append("\"port\":\"" + rmEntity.getPort() + "\",");
         resultBuf.append("\"use_ssl\":\"" + rmEntity.getUse_ssl() + "\",");
         resultBuf.append("\"link\":\"" + rmserver_status + "\"}");
      }

      resultBuf.append("]}");
      responseBody.setItems(resultBuf.toString());
      return responseBody;
   }

   private String getRMStatusByRealTime(RmServerEntity rmEntity) throws Exception {
      DocumentBuilderFactory factory = DocumentUtils.getDocumentBuilderFactoryInstance();
      DocumentBuilder builder = factory.newDocumentBuilder();
      HttpClient httpclient = new DefaultHttpClient();
      String rmserver_status = null;
      String return_code = null;
      String rmServer = null;
      if (rmEntity.getPrivate_mode()) {
         if (rmEntity.getPrivate_ssl()) {
            rmServer = "https://" + rmEntity.getPrivate_ip_address() + ":" + rmEntity.getPrivate_port() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
         } else {
            rmServer = "http://" + rmEntity.getPrivate_ip_address() + ":" + rmEntity.getPrivate_port() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
         }
      } else if (rmEntity.getUse_ssl()) {
         rmServer = "https://" + rmEntity.getIp_address() + ":" + rmEntity.getPort() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
      } else {
         rmServer = "http://" + rmEntity.getIp_address() + ":" + rmEntity.getPort() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
      }

      HttpPost httpget;
      BasicHttpParams httpParams;
      HttpResponse Rmserver_response;
      HttpEntity entity;
      String line;
      if (!rmEntity.getPrivate_mode() && rmEntity.getUse_ssl() || rmEntity.getPrivate_mode() && rmEntity.getPrivate_ssl()) {
         URL url = new URL(rmServer);
         SecurityUtils.trustAllCertificates();
         HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
         conn.setConnectTimeout(30000);
         httpget = null;
         httpParams = null;
         Rmserver_response = null;

         try {
            conn.connect();
            conn.setInstanceFollowRedirects(true);
            InputStream in = conn.getInputStream();
            InputStreamReader isr = new InputStreamReader(in);
            BufferedReader reader = new BufferedReader(isr);
            entity = null;

            String line;
            for(line = new String(); (line = reader.readLine()) != null; line = line + line) {
            }

            reader.close();
            Document doc = builder.parse(new InputSource(new StringReader(line)));
            doc.getDocumentElement().normalize();
            NodeList headNodeList = doc.getElementsByTagName("response");
            Element subItem = (Element)headNodeList.item(0);
            return_code = subItem.getAttribute("code");
            if (return_code.equals("0")) {
               rmserver_status = "ON";
            } else {
               rmserver_status = "OFF";
            }

            isr.close();
            in.close();
         } catch (Exception var29) {
            rmserver_status = "OFF";
            this.logger.error("SSL time out! e : " + var29.getMessage());
         }
      } else {
         BufferedReader rd = null;
         InputStreamReader isr = null;

         try {
            new URL(rmServer);
            httpget = new HttpPost(rmServer);
            httpParams = new BasicHttpParams();
            HttpConnectionParams.setConnectionTimeout(httpParams, 30000);
            httpclient = new DefaultHttpClient(httpParams);
            Rmserver_response = httpclient.execute(httpget);
            entity = Rmserver_response.getEntity();
            if (entity != null) {
               isr = new InputStreamReader(Rmserver_response.getEntity().getContent());
               rd = new BufferedReader(isr);
               line = null;

               String resultXml;
               for(resultXml = new String(); (line = rd.readLine()) != null; resultXml = resultXml + line) {
               }

               Document doc = builder.parse(new InputSource(new StringReader(resultXml)));
               doc.getDocumentElement().normalize();
               NodeList headNodeList = doc.getElementsByTagName("response");
               Element subItem = (Element)headNodeList.item(0);
               return_code = subItem.getAttribute("code");
               if (return_code.equals("0")) {
                  rmserver_status = "ON";
               } else {
                  rmserver_status = "OFF";
               }

               rd.close();
               rd = null;
               isr.close();
               isr = null;
            }

            httpget.abort();
            httpclient.getConnectionManager().shutdown();
         } catch (ClientProtocolException var30) {
            this.logger.error("[MagicInfo_RMServerAjaxController] ClientProtocolException");
            rmserver_status = "OFF";
         } catch (IllegalStateException var31) {
            this.logger.error("[MagicInfo_RMServerAjaxController] IllegalStateException");
            rmserver_status = "OFF";
         } catch (ConnectException var32) {
            this.logger.error("[MagicInfo_RMServerAjaxController] ConnectException");
            rmserver_status = "OFF";
         } catch (ConnectTimeoutException var33) {
            this.logger.error("[MagicInfo_RMServerAjaxController] ConnectTimeoutException");
            rmserver_status = "OFF";
         } catch (SocketException var34) {
            this.logger.error("[MagicInfo_RMServerAjaxController] SocketException");
            rmserver_status = "OFF";
         } catch (SAXParseException var35) {
            this.logger.error("[MagicInfo_RMServerAjaxController] SAXParseException");
            rmserver_status = "OFF";
         } catch (Exception var36) {
            this.logger.error("[MagicInfo_RMServerAjaxController] Exception");
            rmserver_status = "OFF";
         } finally {
            if (rd != null) {
               rd.close();
            }

            if (isr != null) {
               isr.close();
            }

            httpclient.getConnectionManager().shutdown();
         }
      }

      return rmserver_status;
   }

   private String getRMStatusByJob(RmServerEntity rmEntity) throws Exception {
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Integer errCnt = 0;
      Map infoMap = serverSetupDao.getServerInfoByOrgId(0L);
      Integer errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
      errCnt = serverSetupDao.getExternalServerErrCount("RM", rmEntity.getIp_address());
      if (errThresholdCnt != null && errCnt != null) {
         return errThresholdCnt <= errCnt ? "OFF" : "ON";
      } else {
         return "ERROR";
      }
   }
}
