package com.samsung.magicinfo.webauthor2.repository.model;

import com.samsung.magicinfo.webauthor2.repository.model.FileInfoData;
import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;

public class FileInfoListData implements Serializable {
  private static final long serialVersionUID = -4600996544163827769L;
  
  @XmlElement
  private Long totalCount;
  
  @XmlElementWrapper(name = "resultList")
  @XmlElement(name = "ContentFile")
  private List<FileInfoData> resultList;
  
  public Long getTotalCount() {
    return this.totalCount;
  }
  
  public List<FileInfoData> getResultList() {
    return this.resultList;
  }
}
