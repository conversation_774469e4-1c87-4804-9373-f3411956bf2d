package com.samsung.magicinfo.rc.common.security;

import com.samsung.magicinfo.rc.common.security.DeviceWithToken;

public class DeviceWithTokenBuilder {
  private String deviceId;
  
  private String token;
  
  public DeviceWithTokenBuilder deviceId(String deviceId) {
    this.deviceId = deviceId;
    return this;
  }
  
  public DeviceWithTokenBuilder token(String token) {
    this.token = token;
    return this;
  }
  
  public DeviceWithToken build() {
    return new DeviceWithToken(this.deviceId, this.token);
  }
  
  public String toString() {
    return "DeviceWithToken.DeviceWithTokenBuilder(deviceId=" + this.deviceId + ", token=" + this.token + ")";
  }
}
