<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.samsung.magicinfo.protocol.util.dao.ServerSetupDaoMapper">

	<!--  [2019.10 RC] KSY                                        -->  
	<!--  컬럼 추가 : ALARM_GROUPS_NOTIFY_TYPE,         -->
	<!--                PASSWORD_NOT_REUSE_ENABLED      -->
	<!--                PASSWORD_CHANGE_PERIOD            -->
	<!--                PASSWORD_LOGIN_CHANGE_ENABLED -->
	<!--                PASSWORD_NOT_REUSE_ENABLED      --> 
	
    <update id="updateServerInfo">
        UPDATE MI_SYSTEM_INFO_SETUP
        <set>
            <if test="map.NOTIFICATION_ENABLE != null">
                NOTIFICATION_ENABLE = #{map.NOTIFICATION_ENABLE},
            </if>
            <if test="map.DEVICE_DISCONNECTED_DURATION != null">
            	DEVICE_DISCONNECTED_DURATION = #{map.DEVICE_DISCONNECTED_DURATION},
            </if>
            <if test="map.DISCONNECT_START_TIME != null">
                DISCONNECT_START_TIME = #{map.DISCONNECT_START_TIME},
            </if>
            <if test="map.DISCONNECT_END_TIME != null">
                DISCONNECT_END_TIME = #{map.DISCONNECT_END_TIME},
            </if>
            <if test="map.DISCONNECT_PERIOD != null">
                DISCONNECT_PERIOD = #{map.DISCONNECT_PERIOD},
            </if>
            <if test="map.DISCONNECT_ENABLE != null">
                DISCONNECT_ENABLE = #{map.DISCONNECT_ENABLE},
            </if>
            <if test="map.DISCONNECT_ACTIVATED_DAYS != null">
                DISCONNECT_ACTIVATED_DAYS = #{map.DISCONNECT_ACTIVATED_DAYS},
            </if>              
            <if test="map.REDUNDANCY_ENABLE != null">
                REDUNDANCY_ENABLE = #{map.REDUNDANCY_ENABLE},
            </if>
            <if test="map.AUTO_TIME_ZONE_ENABLE != null">
                AUTO_TIME_ZONE_ENABLE = #{map.AUTO_TIME_ZONE_ENABLE},
            </if>
            <if test="map.AUTO_TIME_ZONE != null">
                AUTO_TIME_ZONE = #{map.AUTO_TIME_ZONE},
            </if>
            <if test="map.DAY_LIGHT_SAVING_ENABLE != null">
                DAY_LIGHT_SAVING_ENABLE = #{map.DAY_LIGHT_SAVING_ENABLE},
            </if>
            <if test="map.DAY_LIGHT_SAVING_MANUAL != null">
                DAY_LIGHT_SAVING_MANUAL = #{map.DAY_LIGHT_SAVING_MANUAL},
            </if>
            <if test="map.CONTENTS_APPROVAL_ENABLE != null">
                CONTENTS_APPROVAL_ENABLE = #{map.CONTENTS_APPROVAL_ENABLE},
            </if>
            <if test="map.DEVICE_PERMISSIONS != null">
                DEVICE_PERMISSIONS = #{map.DEVICE_PERMISSIONS},
            </if>
            <if test="map.DEVICE_TOTAL_COUNT != null">
                DEVICE_TOTAL_COUNT = #{map.DEVICE_TOTAL_COUNT},
            </if>
            <if test="map.DEVICE_POWER != null">
                DEVICE_POWER = #{map.DEVICE_POWER},
            </if>
            <if test="map.EXT_LINK_ENABLE != null">
                EXT_LINK_ENABLE = #{map.EXT_LINK_ENABLE},
            </if>
            <if test="map.SMTP_ENABLE != null">
                SMTP_ENABLE = #{map.SMTP_ENABLE},
            </if>
            <if test="map.SMTP_ADDRESS != null">
                SMTP_ADDRESS = #{map.SMTP_ADDRESS},
            </if>
            <if test="map.SMTP_AUTH_ENABLE != null">
                SMTP_AUTH_ENABLE = #{map.SMTP_AUTH_ENABLE},
            </if>
            <if test="map.SMTP_AUTH_ID != null">
                SMTP_AUTH_ID = #{map.SMTP_AUTH_ID},
            </if>
            <if test="map.SMTP_AUTH_PWD != null">
                SMTP_AUTH_PWD = #{map.SMTP_AUTH_PWD},
                SMTP_ENC_VERSION = 1,
            </if>
            SMTP_AUTH_PORT = #{map.SMTP_AUTH_PORT},
            <if test="map.SMTP_AUTH_SSL_ENABLE != null">
                SMTP_AUTH_SSL_ENABLE = #{map.SMTP_AUTH_SSL_ENABLE},
            </if>
            <if test="map.SMTP_USE_SERVER_SETTING != null">
            	SMTP_USE_SERVER_SETTING = #{map.SMTP_USE_SERVER_SETTING},
            </if>
            <if test="map.EXPIRE_SCHEDULE_DAY != null">
                EXPIRE_SCHEDULE_DAY = #{map.EXPIRE_SCHEDULE_DAY},
            </if>
            <if test="map.EXPIRE_PLAYLIST_DAY != null">
                EXPIRE_PLAYLIST_DAY = #{map.EXPIRE_PLAYLIST_DAY},
            </if>
            <if test="map.EXT_SERVER_MON_INTERVAL != null">
                EXT_SERVER_MON_INTERVAL = #{map.EXT_SERVER_MON_INTERVAL},
            </if>
            <if test="map.EXT_SERVER_ERR_CHK != null">
                EXT_SERVER_ERR_CHK = #{map.EXT_SERVER_ERR_CHK},
            </if>
            <if test="map.EXT_SERVER_DN_MON_ENABLE!= null">
                EXT_SERVER_DN_MON_ENABLE = #{map.EXT_SERVER_DN_MON_ENABLE},
            </if>
            <if test="map.EXT_SERVER_DL_MON_ENABLE != null">
                EXT_SERVER_DL_MON_ENABLE = #{map.EXT_SERVER_DL_MON_ENABLE},
            </if>
            <if test="map.EXT_SERVER_RM_MON_ENABLE != null">
                EXT_SERVER_RM_MON_ENABLE= #{map.EXT_SERVER_RM_MON_ENABLE},
            </if>
            <if test="map.LDAP_ENABLE != null">
            	LDAP_ENABLE = #{map.LDAP_ENABLE},
            </if>
            <if test="map.LDAP_USE_SERVER_SETTING != null">
            	LDAP_USE_SERVER_SETTING = #{map.LDAP_USE_SERVER_SETTING},
            </if>
            <if test="map.LDAP_SYNC_ENABLE != null">
            	LDAP_SYNC_ENABLE = #{map.LDAP_SYNC_ENABLE},
            </if>
            <if test="map.CONTENTS_APPROVAL_TYPE != null" >
            	CONTENTS_APPROVAL_TYPE = #{map.CONTENTS_APPROVAL_TYPE},
            </if>
            <if test="map.LDAP_SEPARATE_SETTINGS != null" >
            	LDAP_SEPARATE_SETTINGS = #{map.LDAP_SEPARATE_SETTINGS},
            </if>
            <if test="map.SMTP_SEPARATE_SETTINGS != null" >
            	SMTP_SEPARATE_SETTINGS = #{map.SMTP_SEPARATE_SETTINGS},
            </if>
            <if test="map.NOTIFICATION_HISTORY_RETENTION_PERIOD != null">
            	NOTIFICATION_HISTORY_RETENTION_PERIOD = #{map.NOTIFICATION_HISTORY_RETENTION_PERIOD},
            </if>
             <if test="map.INSUFFICIENT_CAPACITY != null" >
            	INSUFFICIENT_CAPACITY = #{map.INSUFFICIENT_CAPACITY},
            </if> 
            <if test="map.ALARM_GROUPS_NOTIFY_TYPE != null" >
            	ALARM_GROUPS_NOTIFY_TYPE = #{map.ALARM_GROUPS_NOTIFY_TYPE},
            </if>
            <if test="map.PASSWORD_CHANGE_PERIOD != null" >
            	PASSWORD_CHANGE_PERIOD = #{map.PASSWORD_CHANGE_PERIOD},
            </if>
            <if test="map.PASSWORD_LOGIN_CHANGE_ENABLED != null" >
            	PASSWORD_LOGIN_CHANGE_ENABLED = #{map.PASSWORD_LOGIN_CHANGE_ENABLED},
            </if>
            <if test="map.PASSWORD_NOT_REUSE_ENABLED != null" >
            	PASSWORD_NOT_REUSE_ENABLED = #{map.PASSWORD_NOT_REUSE_ENABLED},
            </if>
            <if test="map.MFA_ENABLE != null" >
                MFA_ENABLE = #{map.MFA_ENABLE},
            </if>
        </set>
        <choose>
        	<when test="map.UPDATE_ALL_SMTP_USE_SERVER_SETTING != null">
        		WHERE SMTP_USE_SERVER_SETTING = #{map.UPDATE_ALL_SMTP_USE_SERVER_SETTING}
        	</when>
        	<otherwise>
        		WHERE ORGANIZATION_ID = #{map.ORGANIZATION_ID}
        	</otherwise>
        </choose>
        
        
    </update>

    <update id="changePermissionsFunc">
        UPDATE MI_SYSTEM_INFO_SETUP
        SET DEVICE_PERMISSIONS = #{check}
    </update>

    <select id="getServerCommonInfo" resultType="java.util.Map">
        SELECT
            *
        FROM MI_SYSTEM_INFO_SETUP
        WHERE ORGANIZATION_ID = 0
    </select>

    <select id="getServerInfoByOrgId" resultType="java.util.Map">
        SELECT
            *
        FROM MI_SYSTEM_INFO_SETUP
        WHERE ORGANIZATION_ID = #{orgId}
    </select>

    <select id="getServerMFAInfo" resultType="com.samsung.magicinfo.restapi.setting.model.config.MFA">
        SELECT
            *
        FROM MI_SYSTEM_INFO_MFA
        <if test="mfaType != null">
        WHERE MFA_TYPE = #{mfaType}
        </if>
    </select>

    <update id="updateServerMFAInfo">
        UPDATE MI_SYSTEM_INFO_MFA
        <set>
            <if test="mfa.auth_enable != null">
                AUTH_ENABLE = #{mfa.auth_enable},
            </if>
            <if test="mfa.period != null">
                PERIOD = #{mfa.period},
            </if>
            <if test="mfa.use_period != null">
                USE_PERIOD = #{mfa.use_period},
            </if>
            MODIFY_DATE = <include refid="utils.currentTimestamp" />
        </set>
        WHERE MFA_TYPE = #{mfa.mfa_type}
    </update>





    <select id="getServerSetupKpi" resultType="java.util.Map">
        SELECT
        *
        FROM MI_SYSTEM_INFO_KPI
    </select>

    <update id="updateServerSetupKpi">
        UPDATE MI_SYSTEM_INFO_KPI
        <set>
            <if test="map.KPI_ENABLE != null">
                KPI_ENABLE = #{map.KPI_ENABLE},
            </if>
            <if test="map.LOCATION != null">
                LOCATION = #{map.LOCATION},
            </if>
            MODIFY_DATE = <include refid="utils.currentTimestamp" />
        </set>

    </update>


    <insert id= "addServerSetupKpi" >
        INSERT INTO MI_SYSTEM_INFO_KPI
        (
            SERVER_ID, KPI_ENABLE, LOCATION, MODIFY_DATE
        )
        VALUES
        (
            #{map.SERVER_ID},  #{map.KPI_ENABLE}, #{map.LOCATION}, <include refid="utils.currentTimestamp" />
        )
    </insert>


    <select id="checkPermissionsDeviceByOrgId" resultType="java.lang.Boolean">
        SELECT 
            DEVICE_PERMISSIONS
        FROM MI_SYSTEM_INFO_SETUP
        WHERE ORGANIZATION_ID = #{orgId}
    </select>
    
     <select id="getRedundancyEnabledCnt" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM MI_SYSTEM_INFO_SETUP
        WHERE REDUNDANCY_ENABLE = <include refid="utils.true"/>
    </select>
    
    <select id="isRedundancyEnable" resultType="java.lang.Boolean">
        SELECT 
            REDUNDANCY_ENABLE
        FROM MI_SYSTEM_INFO_SETUP
        WHERE ORGANIZATION_ID = 0
    </select>
    
    <select id="getDisconnectEmailAlarmEnabledOrg" resultType="map">
        SELECT ORGANIZATION_ID, DISCONNECT_START_TIME, DISCONNECT_END_TIME, DISCONNECT_ACTIVATED_DAYS, DISCONNECT_PERIOD
        FROM MI_SYSTEM_INFO_SETUP
        WHERE DISCONNECT_ENABLE = <include refid="utils.true"/> AND SMTP_ENABLE = <include refid="utils.true"/> 
    </select>
    
    <insert id= "addDefaultServerInfo" >
        INSERT INTO MI_SYSTEM_INFO_SETUP (ORGANIZATION_ID, DISCONNECT_START_TIME,DISCONNECT_END_TIME,DISCONNECT_PERIOD,DISCONNECT_ENABLE,DEVICE_PERMISSIONS,CONTENTS_APPROVAL_ENABLE,DEVICE_TOTAL_COUNT,DEVICE_POWER,EXT_LINK_ENABLE,SMTP_ENABLE,EXPIRE_SCHEDULE_DAY,EXPIRE_PLAYLIST_DAY) VALUES (#{orgId}, '2013-01-01 00:00:00', '2013-01-01 23:59:00',30, <include refid="utils.false"/>,<include refid="utils.false"/>,<include refid="utils.false"/>,<include refid="utils.false"/>,<include refid="utils.true"/>,<include refid="utils.false"/>,<include refid="utils.false"/>,2,2);
    </insert>
    
     <select id="checkPermissionsDevice" resultType="java.lang.Boolean">
        SELECT 
            DEVICE_PERMISSIONS
        FROM MI_SYSTEM_INFO_SETUP LIMIT 1
    </select>
    
     <select id="checkPermissionsDevice" resultType="java.lang.Boolean"  databaseId="mssql">
        SELECT  TOP 1 
            DEVICE_PERMISSIONS
        FROM MI_SYSTEM_INFO_SETUP
    </select>
    
     <select id="getAllServerInfo" resultType="map">
        SELECT
            A.*, B.GROUP_NAME
        FROM MI_SYSTEM_INFO_SETUP A, MI_USER_INFO_GROUP B WHERE A.ORGANIZATION_ID = B.GROUP_ID
    </select>
	
	 <update id="resetExternalServerErrCount">
        UPDATE MI_EXTERNAL_SERVER_STATUS
        SET
        	ERR_COUNT = 0, LAST_ERR_TIME = NULL
         WHERE SERVER_TYPE = #{serverType} AND IP_ADDRESS =  #{ipAddress}
	</update>
	
	<update id="addExternalServerErrCount">
        UPDATE MI_EXTERNAL_SERVER_STATUS
        SET
        	ERR_COUNT = ERR_COUNT +1  
         WHERE SERVER_TYPE = #{serverType} AND IP_ADDRESS =  #{ipAddress}
	</update>
	
	<select id="getExternalServer" resultType="java.lang.Integer">
        SELECT *
        FROM MI_EXTERNAL_SERVER_STATUS
        WHERE SERVER_TYPE  = #{serverType} AND IP_ADDRESS =  #{ipAddress} 
    </select>
	
	<select id="getExternalServerErrCount" resultType="java.lang.Integer">
        SELECT ERR_COUNT
        FROM MI_EXTERNAL_SERVER_STATUS
        WHERE SERVER_TYPE  = #{serverType} AND IP_ADDRESS =  #{ipAddress} 
    </select>
	
	<select id="isExistExternalServer" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM MI_EXTERNAL_SERVER_STATUS
        WHERE SERVER_TYPE  = #{serverType} AND IP_ADDRESS =  #{ipAddress} 
    </select>
    
    <insert id= "addExternalServerForMonitoring" >
        INSERT INTO MI_EXTERNAL_SERVER_STATUS (SERVER_TYPE, IP_ADDRESS, ERR_COUNT) 
		SELECT #{serverType}, #{ipAddress}, #{defaultErrorCount}
		WHERE NOT EXISTS(SELECT 1 FROM MI_EXTERNAL_SERVER_STATUS WHERE IP_ADDRESS = #{ipAddress} AND  SERVER_TYPE = #{serverType} )    
    </insert>
    
    <delete id="deleteExternalServerForMonitoring">
        DELETE FROM MI_EXTERNAL_SERVER_STATUS WHERE SERVER_TYPE  = #{serverType} AND IP_ADDRESS =  #{ipAddress}
    </delete>

	<update id="setLastErrTime">
        UPDATE MI_EXTERNAL_SERVER_STATUS
        <set>
        	LAST_ERR_TIME = CURRENT_TIMESTAMP 
        </set>
         WHERE SERVER_TYPE = #{serverType} AND IP_ADDRESS =  #{ipAddress}
	</update>
	
	<select id="getLastErrTime" resultType="java.sql.Timestamp">
        SELECT LAST_ERR_TIME
        FROM MI_EXTERNAL_SERVER_STATUS
        WHERE SERVER_TYPE  = #{serverType} AND IP_ADDRESS =  #{ipAddress} 
    </select>
    
    <select id="isLdapEnable" resultType="java.lang.Boolean">
		SELECT LDAP_ENABLE FROM MI_SYSTEM_INFO_SETUP WHERE ORGANIZATION_ID = #{org_id}     
    </select>
    
    <select id="isLdapUseServerSetting" resultType="java.lang.Boolean">
		SELECT LDAP_USE_SERVER_SETTING FROM MI_SYSTEM_INFO_SETUP WHERE ORGANIZATION_ID = #{org_id}     
    </select>
    
    <select id="hasApplyLdapServerSetting" resultType="java.lang.Integer">
    	SELECT COUNT(*) FROM MI_SYSTEM_INFO_SETUP WHERE LDAP_USE_SERVER_SETTING = 'true' AND ORGANIZATION_ID != 0
    </select>
    
    <select id="hasApplySmtpServerSetting" resultType="java.lang.Integer">
    	SELECT COUNT(*) FROM MI_SYSTEM_INFO_SETUP WHERE SMTP_USE_SERVER_SETTING = 'true' AND ORGANIZATION_ID != 0
    </select>
    
    <select id="isLdapSeparateSettings" resultType="java.lang.Boolean">
    	SELECT LDAP_SEPARATE_SETTINGS FROM MI_SYSTEM_INFO_SETUP WHERE ORGANIZATION_ID = 0
    </select>
    
    <select id="isSmtpSeparateSettings" resultType="java.lang.Boolean">
    	SELECT SMTP_SEPARATE_SETTINGS FROM MI_SYSTEM_INFO_SETUP WHERE ORGANIZATION_ID = 0
    </select>
    
    <select id="getSecretValue" resultType="java.util.Map">
    	SELECT SECRET_VALUE, FTP_PASSWORD_KEY, VNC_PASSWORD, DEF_MAC, DEF_CPUID, DEF_BOARDID FROM MI_SYSTEM_INFO_SETUP WHERE ORGANIZATION_ID = 0
    </select>
    
    <select id="getRuleMangerEnabledOrgID" resultType="java.util.Map">
    	SELECT A.ORGANIZATION_ID, B.GROUP_NAME FROM MI_SYSTEM_INFO_SETUP A, MI_USER_INFO_GROUP B WHERE A.RM_ENABLE = <include refid="utils.true"/> AND A.ORGANIZATION_ID = B. GROUP_ID AND A.ORGANIZATION_ID != 0
    </select>
    
    <update id="resetRuleMangerEnabledOrg">
        UPDATE MI_SYSTEM_INFO_SETUP
        SET
        	RM_ENABLE = <include refid="utils.false"/>
	</update>
	
	<update id="updateRuleMangerEnabledOrg">
        UPDATE MI_SYSTEM_INFO_SETUP
        SET	RM_ENABLE = <include refid="utils.true"/>
		WHERE ORGANIZATION_ID = #{org_id}     
	</update>
    
	<select id="getNotificationEnabledOrgList" resultType="java.util.Map">
    	SELECT
    		A.ORGANIZATION_ID AS ID, B.GROUP_NAME AS NAME
    	FROM
    		MI_SYSTEM_INFO_SETUP A, MI_USER_INFO_GROUP B
    	WHERE
    		A.ORGANIZATION_ID = B.GROUP_ID AND DISCONNECT_ENABLE = <include refid="utils.true"/>
    </select>
    
	<insert id="addLogInfo">
        INSERT INTO MI_SYSTEM_INFO_LOG (
            MENU, EVENT_TYPE, NAME, EVENT_DESC, EVENT_TIME, IP_ADDRESS, USER_ID, ORGANIZATION_ID )
        VALUES (
            #{systemLogEntity.menu}, #{systemLogEntity.event_type}, #{systemLogEntity.name}, #{systemLogEntity.event_desc}, <include refid="utils.currentTimestamp" />, #{systemLogEntity.ip_address}, #{systemLogEntity.user_id}, #{systemLogEntity.organization_id})
    </insert>

    <insert id="addServerManagementInfo">
        INSERT INTO MI_SYSTEM_INFO_SERVER_MANAGEMENT
        (MANAGEMENT_ID, MANAGEMENT_INFO, CREATE_DATE)
        VALUES
        (#{entity.management_id}, #{entity.management_info}, <include refid="utils.currentTimestamp"/>);
    </insert>

    <select id="getServerManagementInfo" resultType="com.samsung.magicinfo.framework.setup.entity.ServerManagementEntity">
        SELECT
        MANAGEMENT_ID, MANAGEMENT_INFO, CREATE_DATE
        FROM
        MI_SYSTEM_INFO_SERVER_MANAGEMENT
        <if test="management_id != null">
            WHERE
            MANAGEMENT_ID = #{management_id}
        </if>
        ORDER BY CREATE_DATE DESC
        LIMIT 1
    </select>

    <select id="getServerManagementInfo" resultType="com.samsung.magicinfo.framework.setup.entity.ServerManagementEntity" databaseId="mssql">
        SELECT
        TOP 1
        MANAGEMENT_ID, MANAGEMENT_INFO, CREATE_DATE
        FROM
        MI_SYSTEM_INFO_SERVER_MANAGEMENT
        <if test="management_id != null">
            WHERE
            MANAGEMENT_ID = #{management_id}
        </if>
        ORDER BY CREATE_DATE DESC
    </select>

    <update id="updateServerManagementInfo">
        UPDATE MI_SYSTEM_INFO_SERVER_MANAGEMENT
        SET MANAGEMENT_INFO = #{entity.management_info}
        WHERE MANAGEMENT_ID = #{entity.management_id}
	</update>

    <update id="hideUserIdInLogs">
        UPDATE MI_SYSTEM_INFO_LOG
        SET NAME = (CASE WHEN NAME = #{userId} THEN '******' ELSE NAME END), USER_ID = (CASE WHEN USER_ID = #{userId} THEN '******' ELSE USER_ID END)
        WHERE NAME = #{userId} OR USER_ID = #{userId}
    </update>
    <select id="getDefaultPassword" resultType="String">
        SELECT DEFAULT_PASSWORD
        FROM MI_SYSTEM_INFO_SETUP WHERE ORGANIZATION_ID = 0
    </select>
</mapper>
