package com.samsung.magicinfo.webauthor2.repository.command;

import org.springframework.util.Assert;

public class GetTagCommand {
  private String userId;
  
  private String token;
  
  public GetTagCommand(String userId, String token) {
    Assert.notNull(userId, "UserId can't be null!");
    Assert.notNull(token, "Token can't be null!");
    this.userId = userId;
    this.token = token;
  }
  
  public String getToken() {
    return this.token;
  }
  
  public String getUserId() {
    return this.userId;
  }
}
