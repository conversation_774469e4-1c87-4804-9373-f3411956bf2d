<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.samsung.magicinfo.framework.statistics.dao.NewContentFrequencyStatisticsForChartDaoMapper">

<sql id="where_deviceIdList">
	<if test="deviceIdList != null and deviceIdList.length > 0">
		<foreach item="deviceId"  index="indexDevices" collection="deviceIdList" open="AND (" separator="OR" close=")">	
		DEVICE_ID = #{deviceId}	
		</foreach>		
	</if>
</sql>

<sql id="where_contentIdList">
	<if test="contentIdList != null and contentIdList.length > 0">
		<foreach item="contentId"  index="indexContents" collection="contentIdList" open="AND (" separator="OR" close=")">	
		CONTENT_ID = #{contentId}	
		</foreach>		
	</if>
</sql>

<sql id="outerQueryClauses_begin">
	SELECT A.*, TO_CHAR((A.DURATION || ' second')::interval, 'HH24:MI:SS') AS DURATION_STRING FROM(
</sql>
<sql id="outerQueryClauses_begin" databaseId="mssql">	
	SELECT A.*, CONVERT(VARCHAR(8), DATEADD(SECOND, DURATION, '19000101'), 8) AS DURATION_STRING FROM(
</sql>
<sql id="outerQueryClauses_begin" databaseId="mysql">
	SELECT A.*, TIME_FORMAT(SEC_TO_TIME(A.DURATION),'%H:%i:%s') AS DURATION_STRING FROM(
</sql>

<sql id="outerQueryClauses_end">
	) A
</sql>

<select id="getYesterdayListBy" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		<choose>
			<when test="unit  == 'DAY'">
			SELECT  SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				FROM MI_STATISTICS_CONTENT_DAY 
			</when>
			<when test="unit  == 'HOUR'">
				SELECT DATE_TRUNC('HOUR', START_TIME)::TIME AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				 FROM MI_STATISTICS_CONTENT_HOUR
			</when>
			<when test="unit  == 'SECOND'">
				SELECT START_TIME::TIME AS TIME_STRING, DEVICE_ID, DURATION
				 FROM MI_STATISTICS_CONTENT_SECOND
			</when>
		</choose>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			AND START_TIME::DATE = DATE_TRUNC('DAY',CURRENT_DATE - interval '1days')::DATE
		</where>
		<choose>
			<when test="unit  == 'HOUR'">
			 GROUP BY  START_TIME 
			 ORDER BY TIME_STRING
			</when>
			<when test="unit  == 'SECOND'">
			  ORDER BY TIME_STRING, DEVICE_ID 
			</when>
		</choose>
  	<include refid="outerQueryClauses_end"/>
</select>

<select id="getYesterdayListBy" databaseId="mssql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		<choose>
			<when test="unit  == 'DAY'">
			SELECT  SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				FROM MI_STATISTICS_CONTENT_DAY 
			</when>
			<when test="unit  == 'HOUR'">
				SELECT CONVERT(VARCHAR(8), DATEADD(HOUR, DATEDIFF(HOUR, 0, START_TIME), 0), 108) AS TIME_STRING, 
				SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				FROM MI_STATISTICS_CONTENT_HOUR
			</when>
			<when test="unit  == 'SECOND'">
				SELECT CONVERT(VARCHAR(8), START_TIME, 108) AS TIME_STRING, DEVICE_ID, DURATION
				 FROM MI_STATISTICS_CONTENT_SECOND
			</when>
		</choose>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			AND CONVERT(VARCHAR(10), START_TIME, 120) = CONVERT(VARCHAR(10), DATEADD(dd, -1, DATEADD(DAY, DATEDIFF(DAY, 0, GETDATE()) , 0)), 120)
		</where>
		<choose>
			<when test="unit  == 'HOUR'">
			 GROUP BY START_TIME 
			</when>
		</choose>
  	<include refid="outerQueryClauses_end"/>
  	<if test="unit  == 'HOUR'">
  		ORDER BY TIME_STRING
  	</if>
  	<if test="unit  == 'SECOND'">
  		ORDER BY TIME_STRING, DEVICE_ID 
  	</if>
</select>

<select id="getYesterdayListBy" databaseId="mysql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		<choose>
			<when test="unit  == 'DAY'">
			SELECT  SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				FROM MI_STATISTICS_CONTENT_DAY 
			</when>
			<when test="unit  == 'HOUR'">
				SELECT DATE_FORMAT(START_TIME,'%H:00:00') AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				 FROM MI_STATISTICS_CONTENT_HOUR
			</when>
			<when test="unit  == 'SECOND'">
				SELECT DATE_FORMAT(START_TIME,'%H:%i:%s') AS TIME_STRING, DEVICE_ID, DURATION
				 FROM MI_STATISTICS_CONTENT_SECOND
			</when>
		</choose>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			AND DATE_FORMAT(START_TIME, '%Y-%m-%d') = DATE_FORMAT(NOW() - INTERVAL 1 DAY, '%Y-%m-%d')
		</where>
		<choose>
			<when test="unit  == 'HOUR'">
			 GROUP BY  START_TIME 
			 ORDER BY TIME_STRING
			</when>
			<when test="unit  == 'SECOND'">
			  ORDER BY TIME_STRING, DEVICE_ID 
			</when>
		</choose>
  	<include refid="outerQueryClauses_end"/>
</select>

<sql id="conditionStartTimeEqToTruncCurrDateMonth">
	<choose>
		<when test="isThis">
			AND START_TIME = DATE_TRUNC('MONTH',CURRENT_DATE)
		</when>
		<otherwise>
			AND START_TIME = DATE_TRUNC('MONTH',CURRENT_DATE - interval '1 months')
		</otherwise>
	</choose>	
</sql>

<sql id="conditionStartTimeEqToTruncCurrDateMonth" databaseId="mssql">
	<choose>
		<when test="isThis">
			AND START_TIME = DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0)
		</when>
		<otherwise>
			AND START_TIME = DATEADD(mm, -1, DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()) , 0))
		</otherwise>
	</choose>
</sql>
<sql id="conditionStartTimeEqToTruncCurrDateMonth" databaseId="mysql">
	<choose>
		<when test="isThis">
			AND START_TIME = DATE_FORMAT(NOW(), '%Y-%m-01 %00:%00:%00')
		</when>
		<otherwise>
			AND START_TIME = DATE_FORMAT(NOW() - INTERVAL 1 MONTH, '%Y-%m-01 %00:%00:%00')
		</otherwise>
	</choose>	
</sql>

<select id="getMonthListByMonth" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="conditionStartTimeEqToTruncCurrDateMonth"/>
		</where>
  	<include refid="outerQueryClauses_end"/>
</select>

<sql id="conditionMonthStartTimeEqMonthCurrDate">
	<choose> 
		<when test="isThis">
			AND EXTRACT(MONTH FROM START_TIME) = EXTRACT(MONTH FROM CURRENT_DATE)
		</when>
		<otherwise>
			AND EXTRACT(MONTH FROM START_TIME) = EXTRACT(MONTH FROM CURRENT_DATE - interval '1 months')
		</otherwise>
	</choose>
</sql>
<sql id="conditionMonthStartTimeEqMonthCurrDate" databaseId="mssql">
	<choose> 
		<when test="isThis">
			AND MONTH(START_TIME) = MONTH(GETDATE())
		</when>
		<otherwise>
			AND MONTH(START_TIME) = MONTH(GETDATE()) - 1
		</otherwise>
	</choose>
</sql>
<sql id="conditionMonthStartTimeEqMonthCurrDate" databaseId="mysql">
	<choose> 
		<when test="isThis">
			AND MONTH(START_TIME) = MONTH(NOW())
		</when>
		<otherwise>
			AND MONTH(START_TIME) = MONTH(NOW()) -1
		</otherwise>
	</choose>
</sql>

<select id="getMonthListByDow" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT PLAY_DOW, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_DAY
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="conditionMonthStartTimeEqMonthCurrDate"/>	
		</where>
		GROUP BY PLAY_DOW
  	<include refid="outerQueryClauses_end"/>
  	ORDER BY PLAY_DOW
</select>

<select id="getQuarterListByQuarter" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/> 
				<choose>
				<when test="isThis">
					AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE) AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE)
				</when>
				<otherwise>
					AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE - interval '3 months') AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE - interval '3 months')
				</otherwise>
			</choose>	
		</where>
  	<include refid="outerQueryClauses_end"/>
</select>

<select id="getQuarterListByQuarter" databaseId="mssql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/> 
				<choose>
				<when test="isThis">
					AND YEAR(START_TIME) = YEAR(GETDATE()) AND LOG_QUARTER = DATENAME(Quarter, CAST(CONVERT(VARCHAR(8), DATEADD(month, 0 ,GETDATE())) AS DATETIME))
				</when>
				<otherwise>
					AND YEAR(START_TIME) = YEAR(DATEADD(month, -3 ,GETDATE())) AND LOG_QUARTER = DATENAME(Quarter, CAST(CONVERT(VARCHAR(8), DATEADD(month, -3 ,GETDATE())) AS DATETIME))
				</otherwise>
			</choose>	
		</where>
  	<include refid="outerQueryClauses_end"/>
</select>

<select id="getQuarterListByQuarter" databaseId="mysql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/> 
				<choose>
				<when test="isThis">
					AND YEAR(START_TIME) = YEAR(NOW()) AND LOG_QUARTER = QUARTER(NOW())
				</when>
				<otherwise>
					AND YEAR(START_TIME) = YEAR(NOW()) AND LOG_QUARTER = QUARTER(NOW()) -1
				</otherwise>
			</choose>	
		</where>
  	<include refid="outerQueryClauses_end"/>
</select>


<select id="getQuarterListByMonth" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT extract(month from START_TIME) AS MONTH_ORDER, TO_CHAR(START_TIME,'Month') AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
				<choose>
				<when test="isThis">
					AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE) AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE)
				</when>
				<otherwise>
					AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE - interval '3 months') AND LOG_QUARTER = EXTRACT(QUARTER FROM CURRENT_DATE - interval '3 months')
				</otherwise>
			</choose>	
		</where>
		GROUP BY TIME_STRING, MONTH_ORDER
		ORDER BY MONTH_ORDER
  	<include refid="outerQueryClauses_end"/>
</select>

<select id="getQuarterListByMonth" databaseId="mssql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" />
	SELECT MONTH_ORDER, TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION FROM (
		SELECT MONTH(START_TIME) AS MONTH_ORDER, 
		DateName( month , DateAdd( month , MONTH(START_TIME) , 0 ) - 1 ) AS TIME_STRING, 
		PLAY_COUNT, DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
				<choose>
				<when test="isThis">
					AND YEAR(START_TIME) = YEAR(GETDATE()) 
					AND LOG_QUARTER = DATENAME(Quarter, CAST(CONVERT(VARCHAR(8), DATEADD(month, 0 ,GETDATE())) AS DATETIME))
				</when>
				<otherwise>
					AND YEAR(START_TIME) = YEAR(DATEADD(month, -3 ,GETDATE()))
					AND LOG_QUARTER = DATENAME(Quarter, CAST(CONVERT(VARCHAR(8), DATEADD(month, -3 ,GETDATE())) AS DATETIME))
				</otherwise>
			</choose>	
		</where>
	) AS subquery
	GROUP BY TIME_STRING, MONTH_ORDER
  	<include refid="outerQueryClauses_end"/>
  	ORDER BY MONTH_ORDER
</select>

<select id="getQuarterListByMonth" databaseId="mysql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT MONTH(START_TIME) AS MONTH_ORDER, MONTHNAME(START_TIME) AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
				<choose>
				<when test="isThis">
					AND YEAR(START_TIME) = YEAR(NOW()) AND LOG_QUARTER = QUARTER(NOW())
				</when>
				<otherwise>
					AND YEAR(START_TIME) = YEAR(NOW()) AND LOG_QUARTER = QUARTER(NOW()) -1
				</otherwise>
			</choose>	
		</where>
		GROUP BY TIME_STRING, MONTH_ORDER
		ORDER BY MONTH_ORDER
  	<include refid="outerQueryClauses_end"/>
</select>

<sql id="truncYearCurrentDate">
	DATE_TRUNC('YEAR',CURRENT_DATE)
</sql>
<sql id="truncYearCurrentDate" databaseId="mssql">
	DATEADD(YEAR, DATEDIFF(YEAR, 0, GETDATE()), 0)
</sql>
<sql id="truncYearCurrentDate" databaseId="mysql">
	DATE_FORMAT(NOW(), '%Y-01-01 %00:%00:%00')
</sql>

<sql id="truncYearCurrentDateMinusOneYear">
	DATE_TRUNC('YEAR',CURRENT_DATE - interval '1 years')
</sql>
<sql id="truncYearCurrentDateMinusOneYear" databaseId="mssql">
	DATEADD(yy, -1, DATEADD(YEAR, DATEDIFF(YEAR, 0, GETDATE()) , 0))
</sql>
<sql id="truncYearCurrentDateMinusOneYear" databaseId="mysql">
	DATE_FORMAT(NOW() - INTERVAL 1 YEAR, '%Y-01-01 %00:%00:%00')
</sql>

<select id="getYearListByYear" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_YEAR
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
				<choose>
				<when test="isThis">
					AND START_TIME = <include refid="truncYearCurrentDate"/>
				</when>
				<otherwise>
					AND START_TIME = <include refid="truncYearCurrentDateMinusOneYear"/>
				</otherwise>
			</choose>	
		</where>
  	<include refid="outerQueryClauses_end"/>
</select>

<sql id="conditionStartTimeYearEqCurrentYear">
	<choose>
		<when test="isThis">
			 AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE)
		</when>
		<otherwise>
			AND EXTRACT(YEAR FROM START_TIME) = EXTRACT(YEAR FROM CURRENT_DATE - interval '1 years')
		</otherwise>
	</choose>
</sql>
<sql id="conditionStartTimeYearEqCurrentYear" databaseId="mssql">
	<choose>
		<when test="isThis">
			 AND YEAR(START_TIME) = YEAR(GETDATE())
		</when>
		<otherwise>
			AND YEAR(START_TIME) = YEAR(GETDATE()) - 1
		</otherwise>
	</choose>
</sql>
<sql id="conditionStartTimeYearEqCurrentYear" databaseId="mysql">
	<choose>
		<when test="isThis">
			 AND YEAR(START_TIME) = YEAR(NOW())
		</when>
		<otherwise>
			AND YEAR(START_TIME) = YEAR(NOW()) - 1
		</otherwise>
	</choose>
</sql>

<select id="getYearListByQuarter" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		SELECT LOG_QUARTER, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
		FROM MI_STATISTICS_CONTENT_MONTH
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			<include refid="conditionStartTimeYearEqCurrentYear"/>	
		</where>
		GROUP BY LOG_QUARTER
  	<include refid="outerQueryClauses_end"/>
  	ORDER BY LOG_QUARTER
</select>

<sql id="truncHourAndCastStartTime">
	DATE_TRUNC('HOUR', START_TIME)::TIME
</sql>
<sql id="truncHourAndCastStartTime" databaseId="mssql">
	CONVERT(VARCHAR(8), DATEADD(HOUR, DATEDIFF(HOUR, 0, START_TIME), 0), 108)
</sql>
<sql id="truncHourAndCastStartTime" databaseId="mysql">
	DATE_FORMAT(START_TIME,'%H:00:00')
</sql>

<select id="getWeekListByHour" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
	<include refid="outerQueryClauses_begin"/>
			SELECT <include refid="truncHourAndCastStartTime"/> AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				 FROM MI_STATISTICS_CONTENT_HOUR
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}				
		</where>
		GROUP BY START_TIME
	<include refid="outerQueryClauses_end"/>
	ORDER BY TIME_STRING
</select>

<sql id="castStartTimeToDate">
	START_TIME::DATE
</sql>
<sql id="castStartTimeToDate" databaseId="mssql">
	CONVERT(VARCHAR(10), START_TIME, 120)
</sql>
<sql id="castStartTimeToDate" databaseId="mysql">
	DATE_FORMAT(START_TIME, '%Y-%m-%d')
</sql>

<select id="getWeekListByDay" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
	<include refid="outerQueryClauses_begin"/>
			SELECT <include refid="castStartTimeToDate"/> AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				 FROM MI_STATISTICS_CONTENT_DAY
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}				
		</where>
		GROUP BY START_TIME
	<include refid="outerQueryClauses_end"/>
	ORDER BY TIME_STRING
</select>

<select id="getWeekListByWeek" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
	<include refid="outerQueryClauses_begin"/>
			SELECT SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
			 FROM MI_STATISTICS_CONTENT_DAY
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}				
		</where>
	<include refid="outerQueryClauses_end"/>
</select>

<select id="getCustomList" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		<choose>
			<when test="unit  == 'DAY'">
			SELECT  DATE_TRUNC('DAY', START_TIME)::DATE AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				FROM MI_STATISTICS_CONTENT_DAY 
			</when>
			<when test="unit  == 'HOUR'">
			SELECT TO_CHAR(DATE_TRUNC('HOUR', START_TIME),'YYYY-MM-DD HH24:MI:SS') AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				 FROM MI_STATISTICS_CONTENT_HOUR
			</when>
			<when test="unit  == 'SECOND'">
				SELECT TO_CHAR(START_TIME,'YYYY-MM-DD HH24:MI:SS') AS TIME_STRING, DEVICE_ID, DURATION
				 FROM MI_STATISTICS_CONTENT_SECOND
			</when>
		</choose>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}	
		</where>
		<choose>
			<when test="unit  == 'DAY'">
			GROUP BY TIME_STRING
			</when>
			<when test="unit  == 'HOUR'">
			GROUP BY  START_TIME 
			ORDER BY TIME_STRING
			</when>
			<when test="unit  == 'SECOND'">
			  ORDER BY TIME_STRING, DEVICE_ID
			</when>
		</choose>
  	<include refid="outerQueryClauses_end"/>
</select>

<select id="getCustomList" databaseId="mssql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		<choose>
			<when test="unit  == 'DAY'">
		SELECT TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION FROM (
			SELECT CONVERT(VARCHAR(10), DATEADD(DAY, DATEDIFF(DAY, 0, START_TIME), 0), 120) AS TIME_STRING,
			 PLAY_COUNT, DURATION FROM MI_STATISTICS_CONTENT_DAY 
			</when>
			<when test="unit  == 'HOUR'">
		SELECT START_TIME, TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION FROM (
			SELECT START_TIME, CONVERT(VARCHAR(19), DATEADD(HOUR, DATEDIFF(HOUR, 0, START_TIME), 0), 120) AS TIME_STRING, 
			PLAY_COUNT, DURATION FROM MI_STATISTICS_CONTENT_HOUR
			</when>
			<when test="unit  == 'SECOND'">
		SELECT DEVICE_ID, TIME_STRING, DURATION FROM (
			SELECT CONVERT(VARCHAR(19), START_TIME, 120) AS TIME_STRING, DEVICE_ID, DURATION
				 FROM MI_STATISTICS_CONTENT_SECOND
			</when>
		</choose>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}	
		</where>
		) AS subquery
		<choose>
			<when test="unit  == 'DAY'">
			GROUP BY TIME_STRING
			</when>
			<when test="unit  == 'HOUR'">
			GROUP BY START_TIME, TIME_STRING
			</when>
		</choose>
  	<include refid="outerQueryClauses_end"/>
  	<if test="unit  == 'HOUR'">
		ORDER BY TIME_STRING
	</if>
	<if test="unit  == 'SECOND'">
		ORDER BY TIME_STRING, DEVICE_ID
	</if>
</select>

<select id="getCustomList" databaseId="mysql" resultType="com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity">
<include refid="outerQueryClauses_begin" /> 
		<choose>
			<when test="unit  == 'DAY'">
			SELECT  DATE_FORMAT(START_TIME, '%Y-%m-%d') AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				FROM MI_STATISTICS_CONTENT_DAY 
			</when>
			<when test="unit  == 'HOUR'">
			SELECT DATE_FORMAT(START_TIME, '%Y-%m-%d %H:00:00') AS TIME_STRING, SUM(PLAY_COUNT) AS PLAY_COUNT, SUM(DURATION) AS DURATION
				 FROM MI_STATISTICS_CONTENT_HOUR
			</when>
			<when test="unit  == 'SECOND'">
				SELECT DATE_FORMAT(START_TIME, '%Y-%m-%d %H:%i:%s') AS TIME_STRING, DEVICE_ID, DURATION
				 FROM MI_STATISTICS_CONTENT_SECOND
			</when>
		</choose>
		<where>
			<include refid="where_contentIdList"/> 
			<include refid="where_deviceIdList"/>
			 AND START_TIME &gt;= #{startDate} AND START_TIME &lt;= #{endDate}	
		</where>
		<choose>
			<when test="unit  == 'DAY'">
			GROUP BY TIME_STRING
			</when>
			<when test="unit  == 'HOUR'">
			GROUP BY  START_TIME 
			ORDER BY TIME_STRING
			</when>
			<when test="unit  == 'SECOND'">
			  ORDER BY TIME_STRING, DEVICE_ID
			</when>
		</choose>
  	<include refid="outerQueryClauses_end"/>
</select>
	
	<update id="updatePOPFileHistoryMappingInfo" parameterType="java.lang.String" >
		UPDATE MI_STATISTICS_CONTENT_FILE_MAP_TABLE SET TABLE_DATE= ${date} WHERE TABLE_NO = (SELECT TABLE_NO FROM MI_STATISTICS_CONTENT_FILE_MAP_TABLE ORDER BY TABLE_DATE ASC,TABLE_NO ASC LIMIT 1)
	</update>
	
	<update id="updatePOPFileHistoryMappingInfo" parameterType="java.lang.String" databaseId="mssql">
		UPDATE MI_STATISTICS_CONTENT_FILE_MAP_TABLE SET TABLE_DATE= ${date} WHERE TABLE_NO = (SELECT TOP 1 TABLE_NO FROM MI_STATISTICS_CONTENT_FILE_MAP_TABLE ORDER BY TABLE_DATE ASC,TABLE_NO ASC )
	</update>
	
	<select id="getPOPFileHistoryMappingInfoByDate"  resultType="java.lang.String">
		SELECT TABLE_NO FROM MI_STATISTICS_CONTENT_FILE_MAP_TABLE WHERE TABLE_DATE = #{date}
	</select>
	
	<select id="isExistPOPFileHistoryMappingInfo"  resultType="Boolean">
		SELECT CAST(COUNT(*) AS BIT) FROM MI_STATISTICS_CONTENT_FILE_MAP_TABLE WHERE TABLE_DATE = #{date}
	</select>
	
	<select id="isExistPOPFileHistoryTableByDeviceId"  resultType="Boolean">
		SELECT CAST(COUNT(*) AS BIT) FROM MI_STATISTICS_CONTENT_FILE_HISTORY_${tableNo} WHERE DEVICE_ID = #{deviceId}
	</select>
	
	<select id="cntPOPFileHistoryMappingInfo"  resultType="Integer">
		SELECT COUNT(*) FROM MI_STATISTICS_CONTENT_FILE_MAP_TABLE WHERE TABLE_DATE != 'INIT'
	</select>
	
	<update id="updatePOPFileHistory">
        UPDATE MI_STATISTICS_CONTENT_FILE_HISTORY_${tableNo}
        SET DAY${day} = #{status}
        WHERE DEVICE_ID = #{deviceId}
    </update>
	
	<insert id="initPOPFileHistoryTable">
		INSERT INTO MI_STATISTICS_CONTENT_FILE_HISTORY_${tableNo} (DEVICE_ID)  
		(SELECT DEVICE_ID FROM MI_DMS_INFO_DEVICE WHERE IS_CHILD = <include refid="utils.false"/> AND IS_APPROVED = <include refid="utils.true"/> AND
			(DEVICE_TYPE = #{constants.TYPE_PREMIUM} 
			OR DEVICE_TYPE = #{constants.TYPE_APLAYER} 
			OR DEVICE_TYPE = #{constants.TYPE_WPLAYER}
			OR (DEVICE_TYPE = #{constants.TYPE_SOC} AND DEVICE_TYPE_VERSION >= #{constants.TYPE_VERSION_2_0}) 
			OR DEVICE_TYPE = #{constants.TYPE_SIGNAGE}
			OR DEVICE_TYPE = #{constants.TYPE_LEDBOX}
			) 
		)
    </insert>
	
	<insert id="initPOPFileHistoryTableByDeviceId">
        INSERT INTO MI_STATISTICS_CONTENT_FILE_HISTORY_${tableNo}
        (DEVICE_ID) VALUES (#{deviceId})
    </insert>
	
	<select id="getCntNotReceviedByDate" parameterType="java.lang.String" resultType="Integer">
		SELECT COUNT(A.DEVICE_ID) FROM MI_STATISTICS_CONTENT_FILE_HISTORY_${tableNo} A LEFT JOIN MI_DMS_MAP_GROUP_DEVICE B ON A.DEVICE_ID = B.DEVICE_ID
		WHERE DAY${day} = 'NOT_RECEIVED' 
		<if test="orgName != null and orgName != '' and orgName != 'ROOT'">
            AND B.ORGANIZATION = #{orgName}
        </if>
	</select>
	
	<select id="getCntReceviedSuccessByDate" parameterType="java.lang.String" resultType="Integer">
		SELECT COUNT(A.DEVICE_ID) FROM MI_STATISTICS_CONTENT_FILE_HISTORY_${tableNo} A LEFT JOIN MI_DMS_MAP_GROUP_DEVICE B ON A.DEVICE_ID = B.DEVICE_ID
		WHERE DAY${day} != 'NOT_RECEIVED' 
		<if test="orgName != null and orgName != '' and orgName != 'ROOT'">
            AND B.ORGANIZATION = #{orgName}
        </if>
	</select>
	
	<select id="getOldestPOPFileHistoryMappingInfo" resultType="map">
		SELECT * FROM MI_STATISTICS_CONTENT_FILE_MAP_TABLE ORDER BY TABLE_DATE ASC LIMIT 1
	</select>
	
	<select id="getOldestPOPFileHistoryMappingInfo" resultType="map" databaseId="mssql">
		SELECT TOP 1 * FROM MI_STATISTICS_CONTENT_FILE_MAP_TABLE ORDER BY TABLE_DATE ASC
	</select>
	
	<update id = "deleteOldestPOPFileMappingInfo">
		UPDATE MI_STATISTICS_CONTENT_FILE_MAP_TABLE SET TABLE_DATE = 'INIT' WHERE TABLE_NO = #{tableNo} 
	</update>
	
	<delete id = "deleteOldestPOPFileHistoryTable">
		DELETE FROM MI_STATISTICS_CONTENT_FILE_HISTORY_${tableNo}
	</delete>
	
	<update id = "insertNewPOPFileMappingInfo">
		UPDATE MI_STATISTICS_CONTENT_FILE_MAP_TABLE SET TABLE_DATE = #{tableDate} WHERE TABLE_NO = #{tableNo} 
	</update>
	
	<select id="getNewPOPFileMappingInfo" resultType="String">
		SELECT TABLE_NO FROM MI_STATISTICS_CONTENT_FILE_MAP_TABLE WHERE TABLE_DATE = 'INIT' ORDER BY TABLE_NO ASC LIMIT 1
	</select>
	
	<select id="getNewPOPFileMappingInfo" resultType="String" databaseId="mssql">
		SELECT TOP 1 TABLE_NO FROM MI_STATISTICS_CONTENT_FILE_MAP_TABLE WHERE TABLE_DATE = 'INIT' ORDER BY TABLE_NO ASC
	</select>
	
	<select id="getMonthlyHistoryByTableNo" resultType="com.samsung.magicinfo.framework.statistics.entity.ProofOfPlayReceiveHistoryEntity">
		SELECT * FROM MI_STATISTICS_CONTENT_FILE_HISTORY_${tableNo}
	    WHERE 1 = 1
        <if test="orgName != null and orgName != '' and orgName != 'ROOT'">
        AND DEVICE_ID IN ( SELECT DEVICE_ID
        FROM MI_DMS_MAP_GROUP_DEVICE
        WHERE 1 = 1
            AND ORGANIZATION = #{orgName}
        )
        </if>

	</select>
	
	<select id="getReceviedSuccesDeviceList" parameterType="java.lang.String" resultType="map">
		SELECT A.DEVICE_ID,B.DEVICE_NAME, D.GROUP_NAME FROM MI_STATISTICS_CONTENT_FILE_HISTORY_${tableNo} A
		LEFT JOIN MI_DMS_INFO_DEVICE B ON A.DEVICE_ID = B.DEVICE_ID
		LEFT JOIN MI_DMS_MAP_GROUP_DEVICE C ON B.DEVICE_ID=C.DEVICE_ID
		LEFT JOIN MI_DMS_INFO_GROUP D ON C.GROUP_ID = D.GROUP_ID
		WHERE DAY${day} = 'RECEIVE_SUCCESS' AND A.DEVICE_ID = B.DEVICE_ID AND B.DEVICE_ID = C.DEVICE_ID AND C.GROUP_ID = D.GROUP_ID
		<if test="orgName != null and orgName != '' and orgName != 'ROOT'">
            AND C.ORGANIZATION = #{orgName}
        </if>
        LIMIT #{pageSize} OFFSET #{startPos}
	</select>
	
	<select id="getReceviedSuccesDeviceList" parameterType="java.lang.String" resultType="map" databaseId="mssql">
	<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
  	<bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
	  SELECT * FROM
	  (	
			SELECT A.DEVICE_ID, B.DEVICE_NAME, D.GROUP_NAME, ROW_NUMBER() 
			OVER(ORDER BY A.DEVICE_ID ASC) as RowNum 
			FROM MI_STATISTICS_CONTENT_FILE_HISTORY_${tableNo} AS A
			LEFT JOIN MI_DMS_INFO_DEVICE AS B ON A.DEVICE_ID = B.DEVICE_ID
			LEFT JOIN MI_DMS_MAP_GROUP_DEVICE AS C ON B.DEVICE_ID = C.DEVICE_ID
			LEFT JOIN MI_DMS_INFO_GROUP AS D ON C.GROUP_ID = D.GROUP_ID			
			WHERE DAY${day} = 'RECEIVE_SUCCESS' AND A.DEVICE_ID = B.DEVICE_ID AND B.DEVICE_ID = C.DEVICE_ID AND C.GROUP_ID = D.GROUP_ID
			<if test="orgName != null and orgName != '' and orgName != 'ROOT'">
				AND C.ORGANIZATION = #{orgName}
			</if>
	  ) as SubQuery
	  WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
	  ORDER BY RowNum
	</select>
	
	<select id="getAllCntReceviedSuccesDevice" parameterType="java.lang.String" resultType="int">
		SELECT COUNT(*) FROM MI_STATISTICS_CONTENT_FILE_HISTORY_${tableNo} A
		LEFT JOIN MI_DMS_INFO_DEVICE B ON A.DEVICE_ID = B.DEVICE_ID
		LEFT JOIN MI_DMS_MAP_GROUP_DEVICE C ON B.DEVICE_ID=C.DEVICE_ID
		LEFT JOIN MI_DMS_INFO_GROUP D ON C.GROUP_ID = D.GROUP_ID
		WHERE DAY${day} = 'RECEIVE_SUCCESS' AND A.DEVICE_ID = B.DEVICE_ID AND B.DEVICE_ID = C.DEVICE_ID AND C.GROUP_ID = D.GROUP_ID
		<if test="orgName != null and orgName != '' and orgName != 'ROOT'">
            AND C.ORGANIZATION = #{orgName}
        </if>
	</select>
	
	<select id="getNotReceviedDeviceList" parameterType="java.lang.String" resultType="map">
		SELECT A.DEVICE_ID, A.DEVICE_NAME, C.GROUP_NAME FROM MI_DMS_INFO_DEVICE A 
		LEFT JOIN MI_DMS_MAP_GROUP_DEVICE B ON A.DEVICE_ID=B.DEVICE_ID
		LEFT JOIN MI_DMS_INFO_GROUP C ON B.GROUP_ID = C.GROUP_ID
		WHERE A.CREATE_DATE <![CDATA[<=]]> #{approvalDate} AND C.GROUP_ID != 999999 AND A.IS_CHILD  = <include refid="utils.false"/>
 		AND A.DEVICE_ID NOT IN (SELECT DEVICE_ID FROM MI_STATISTICS_CONTENT_FILE_HISTORY_${tableNo} WHERE DAY${day} = 'RECEIVE_SUCCESS' )
 		<if test="orgName != null and orgName != '' and orgName != 'ROOT'">
            AND B.ORGANIZATION = #{orgName}
        </if>
        LIMIT #{pageSize} OFFSET #{startPos}
	</select>
	
	<select id="getNotReceviedDeviceList" parameterType="java.lang.String" resultType="map" databaseId="mssql">
	<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
    <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
	SELECT * FROM
	  (	
			SELECT A.DEVICE_ID, A.DEVICE_NAME, C.GROUP_NAME, ROW_NUMBER() 
			OVER(ORDER BY A.DEVICE_ID ASC) as RowNum 
			FROM MI_DMS_INFO_DEVICE AS A
			LEFT JOIN MI_DMS_MAP_GROUP_DEVICE AS B ON A.DEVICE_ID = B.DEVICE_ID
			LEFT JOIN MI_DMS_INFO_GROUP AS C ON B.GROUP_ID = C.GROUP_ID
			WHERE A.CREATE_DATE <![CDATA[<=]]> #{approvalDate} AND C.GROUP_ID != 999999 AND A.IS_CHILD  = <include refid="utils.false"/>
			AND A.DEVICE_ID NOT IN (SELECT DEVICE_ID FROM MI_STATISTICS_CONTENT_FILE_HISTORY_${tableNo} WHERE DAY${day} = 'RECEIVE_SUCCESS' )
			<if test="orgName != null and orgName != '' and orgName != 'ROOT'">
				AND B.ORGANIZATION = #{orgName}
			</if>
	  ) as SubQuery
	  WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
	  ORDER BY RowNum
	</select>
	
	<select id="getAllCntNotReceviedDevice" parameterType="java.lang.String" resultType="int">
		SELECT COUNT(*) FROM MI_DMS_INFO_DEVICE A 
		LEFT JOIN MI_DMS_MAP_GROUP_DEVICE B ON A.DEVICE_ID=B.DEVICE_ID
		LEFT JOIN MI_DMS_INFO_GROUP C ON B.GROUP_ID = C.GROUP_ID
		WHERE A.CREATE_DATE <![CDATA[<=]]> #{approvalDate} AND C.GROUP_ID != 999999 AND A.IS_CHILD  = <include refid="utils.false"/>
 		AND A.DEVICE_ID NOT IN (SELECT DEVICE_ID FROM MI_STATISTICS_CONTENT_FILE_HISTORY_${tableNo} WHERE DAY${day} = 'RECEIVE_SUCCESS' )
 		<if test="orgName != null and orgName != '' and orgName != 'ROOT'">
            AND B.ORGANIZATION = #{orgName}
        </if>
	</select>
	
	<select id = "getPOPFileHistoryTableList" resultType="map">
		SELECT * FROM MI_STATISTICS_CONTENT_FILE_MAP_TABLE WHERE TABLE_DATE != 'INIT' ORDER BY TABLE_DATE DESC
	</select>
</mapper>