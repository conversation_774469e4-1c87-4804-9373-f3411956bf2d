package com.samsung.magicinfo.framework.device.deviceInfo.dao;

import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.rms.model.DeviceGroupFilter;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

public interface DeviceGroupDaoMapper {
   Integer addGroup(@Param("groupId") int var1, @Param("deviceGroup") DeviceGroup var2, @Param("isRoot") boolean var3) throws SQLException;

   Integer addUserToGroup(@Param("groupId") int var1, @Param("userId") String var2) throws SQLException;

   Integer addGroupForOrg(@Param("groupName") String var1, @Param("creatorId") String var2, @Param("groupId") Long var3, @Param("p_groupId") Long var4, @Param("groupDepth") Long var5, @Param("description") String var6, @Param("type") String var7) throws SQLException;

   DeviceGroup getGroup(@Param("groupId") int var1) throws SQLException;

   DeviceGroup getDeviceTopGroup(@Param("groupId") int var1) throws SQLException;

   Long getDeviceGroupForUser(@Param("strOrg") String var1) throws SQLException;

   List getChildGroupIdList(@Param("groupId") Long var1, @Param("non_approval_group") Long var2) throws SQLException;

   long getTotalOrganizationDeviceCountByGroupId(@Param("groupId") long var1) throws SQLException;

   List getTotalOrganizationDeviceCountByGroupIds(@Param("deviceGroups") List var1);

   List getChildDeviceList(@Param("groupId") int var1) throws SQLException;

   Integer deleteOrgGroup(@Param("groupId") Long var1) throws SQLException;

   List getChildDeviceIdListRecursive(@Param("groupId") int var1) throws SQLException;

   List getChildDeviceIdList(@Param("groupId") int var1) throws SQLException;

   List getChildDeviceIdListByOrganName(@Param("organName") String var1) throws SQLException;

   Integer delGroup(@Param("groupId") int var1) throws SQLException;

   List getChildDeviceIdList2(@Param("groupId") int var1) throws SQLException;

   List getChildDeviceList2(@Param("groupId") int var1) throws SQLException;

   List getChildGroupList(@Param("groupId") Long var1, @Param("non_approval_group") Long var2) throws SQLException;

   List getOrgListWithPermission(@Param("groupId") Long var1, @Param("non_approval_group") Long var2) throws SQLException;

   List getChildGroupListByGroupType(@Param("groupId") int var1, @Param("non_approval_group") int var2, @Param("notGroupType") String var3) throws SQLException;

   List getChildGroupListByGroupTypeForSchedule(@Param("groupId") int var1, @Param("non_approval_group") int var2, @Param("groupType") String var3) throws SQLException;

   String getDefaultProgramId(@Param("groupId") long var1) throws SQLException;

   Map getDeviceGroupRoot(@Param("groupId") int var1) throws SQLException;

   Map getDeviceOrgGroupId(@Param("groupId") int var1) throws SQLException;

   DeviceGroup getGroupByDeviceId(@Param("deviceId") String var1) throws SQLException;

   Long getGroupIdByOrgBasic(@Param("orgName") String var1, @Param("childName") String var2) throws SQLException;

   List getGroupList(@Param("rootGroup") int var1, @Param("non_approval_group") int var2) throws SQLException;

   List getGroupListWithParams(@Param("rootGroup") int var1, @Param("non_approval_group") int var2, @Param("startPos") int var3, @Param("pageSize") int var4, @Param("condition") DeviceGroupFilter var5) throws SQLException;

   int getGroupListCntWithParams(@Param("rootGroup") int var1, @Param("non_approval_group") int var2, @Param("startPos") int var3, @Param("pageSize") int var4, @Param("condition") DeviceGroupFilter var5) throws SQLException;

   Map getGroupNameByDeviceId(@Param("deviceId") String var1) throws SQLException;

   List getGroupsForOrg(@Param("strOrg") String var1) throws SQLException;

   Map getMessageGroupRoot(@Param("groupId") int var1) throws SQLException;

   Map getOrganGroupIdByName(@Param("orgGroupName") String var1) throws SQLException;

   Map getOrgGroupId(@Param("groupName") String var1) throws SQLException;

   Map getOrgNameByGroupId(@Param("groupId") long var1) throws SQLException;

   Map getOrgIdByGroupId(@Param("groupId") long var1) throws SQLException;

   int getParentGroupId(@Param("groupId") int var1) throws SQLException;

   Map getProgramGroupRoot(@Param("groupId") int var1) throws SQLException;

   boolean moveGroup(@Param("groupId") Long var1, @Param("new_parent_groupId") Long var2) throws SQLException;

   boolean changeGroupDepth(@Param("groupId") Long var1, @Param("changeInGroupDepth") Long var2) throws SQLException;

   boolean setDefaultProgramId(@Param("groupId") long var1, @Param("programId") String var3) throws SQLException;

   boolean setGroup(@Param("deviceGroup") DeviceGroup var1) throws SQLException;

   boolean setOrgName(@Param("originName") String var1, @Param("newName") String var2) throws SQLException;

   Long getMaxDepth() throws SQLException;

   List getDefaultDeviceGroup() throws SQLException;

   List getSpecificDepthDeviceGroupList(@Param("depth") int var1) throws SQLException;

   List getDeviceGroupTreeFirstLevel(@Param("table") String var1, @Param("organization") String var2, @Param("skipId") String var3, @Param("sortType") String var4) throws SQLException;

   Integer getCntDeviceInDeviceGroup(@Param("groupId") int var1) throws SQLException;

   Integer getCntDeviceInLiteDeviceGroup(@Param("groupId") int var1) throws SQLException;

   List getDeviceGroupTreeSpecificLevel(@Param("p_group_id") Long var1, @Param("skipId") String var2, @Param("sortType") String var3) throws SQLException;

   Integer getCntDeviceInVwlConsoleDevice(@Param("consoleId") String var1) throws SQLException;

   List getVWLGroupTreeSpecificLevel(@Param("p_group_id") Long var1) throws SQLException;

   List getGroupIdByName(@Param("groupName") String var1) throws SQLException;

   boolean setDeviceGroupType(@Param("groupId") int var1, @Param("groupType") String var2) throws SQLException;

   Integer deleteChildGroupAndDevice(@Param("groupId") long var1) throws SQLException;

   Integer delMapGroupUser(@Param("groupId") Long var1) throws SQLException;

   List getChildGroupListByGroupTypeForSchedule2(@Param("groupId") int var1, @Param("non_approval_group") int var2) throws SQLException;

   List getAllVWLLayoutGroupList() throws SQLException;

   List getChildGroupListByGroupTypeForScheduleAuth(@Param("groupId") int var1, @Param("non_approval_group") int var2, @Param("groupType") String var3, @Param("userId") String var4) throws SQLException;

   String getGroupNameByGroupId(@Param("groupId") long var1) throws SQLException;

   List getDeviceGroupTreeFirstLevel2(@Param("table") String var1, @Param("organization") String var2, @Param("skipId") String var3, @Param("sortType") String var4) throws SQLException;

   List getAuthTreeList(@Param("table") String var1, @Param("p_groupId") long var2, @Param("userId") String var4, @Param("groupType") String var5, @Param("sortType") String var6, @Param("skipId") String var7) throws SQLException;

   List getDeviceGroupTreeSpecificLevel2(@Param("table") String var1, @Param("p_group_id") Long var2, @Param("skipId") String var3, @Param("groupType") String var4, @Param("sortType") String var5) throws SQLException;

   List getDeviceGroupVWLTreeSpecificLevel(@Param("table") String var1, @Param("p_group_id") Long var2, @Param("skipId") String var3, @Param("groupType") String var4, @Param("sortType") String var5) throws SQLException;

   String getBooleanVwlGroupId(long var1) throws SQLException;

   List getRedundancyGroups() throws SQLException;

   boolean isRedundancyGroup(@Param("groupId") int var1) throws SQLException;

   List getRedundantDeviceIdbyGroupId(@Param("groupId") int var1) throws SQLException;

   boolean setIsRedundancy(@Param("groupId") Long var1, @Param("isRedundancy") boolean var2) throws SQLException;

   String getVwlLayoutIdByGroupId(@Param("groupId") Long var1) throws SQLException;

   List getVwlLayoutGroupId() throws SQLException;

   String getGroupNameByVwtId(@Param("vwt_id") String var1) throws SQLException;

   boolean setVwtId(@Param("deviceId") String var1, @Param("vwt_id") String var2) throws SQLException;

   String isVwlGroup(@Param("groupId") Long var1) throws SQLException;

   String getGroupType(@Param("groupId") Long var1) throws SQLException;

   boolean cancelVwlGroup(@Param("groupId") Long var1) throws SQLException;

   int isVWLLayoutGroup(@Param("deviceGroupList") List var1) throws SQLException;

   Long getMinimumPriority(@Param("deviceGroupList") List var1) throws SQLException;

   boolean updateDeviceGroupPriority(@Param("minPriority") Long var1, @Param("groupId") Long var2) throws SQLException;

   String getDeviceTypeByMinimumPriority(@Param("minPriority") Long var1) throws SQLException;

   Float getDeviceTypeVersionByMinimumPriority(@Param("minPriority") Long var1) throws SQLException;

   Long getPriority(@Param("deviceType") String var1, @Param("deviceTypeVersion") Float var2) throws SQLException;

   int deleteFromMapGroupUser(@Param("userId") String var1) throws SQLException;

   int insertToMapGroupUser(@Param("userId") String var1, @Param("groupId") Long var2) throws SQLException;

   List getAuthDeviceGroupList(@Param("userId") String var1) throws SQLException;

   List getPermissionsDeviceGroup(@Param("userId") String var1) throws SQLException;

   List getOrgId(@Param("orgName") String var1) throws SQLException;

   int checkExistGroupId(@Param("groupId") Long var1, @Param("userId") String var2) throws SQLException;

   int checkExistUserId(@Param("userId") String var1) throws SQLException;

   List checkChildPermissions1(@Param("table") String var1, @Param("groupId") Long var2) throws SQLException;

   int checkChildPermissions2(@Param("userId") String var1, @Param("groupId") Long var2) throws SQLException;

   List getAuthDeviceGroupTreeSpecificLevel(@Param("table") String var1, @Param("p_groupId") Long var2) throws SQLException;

   List getScheduleMappingDeviceGroupAuth(@Param("programId") String var1, @Param("userId") String var2, @Param("include") boolean var3) throws SQLException;

   List getMessageMappingDeviceGroupAuth(@Param("programId") String var1, @Param("userId") String var2, @Param("include") boolean var3) throws SQLException;

   List getEventMappingDeviceGroupAuth(@Param("programId") String var1, @Param("userId") String var2, @Param("include") boolean var3) throws SQLException;

   List getDeviceTypesMapGroup(@Param("groupId") Long var1) throws SQLException;

   boolean setGroupTypeDefault(@Param("groupType") String var1, @Param("groupId") Long var2) throws SQLException;

   List selectDeviceTypeByGroupId(@Param("groupId") int var1);

   List getAllGroupName() throws SQLException;

   List getGroupById(@Param("cmd") String var1, @Param("id") long var2) throws SQLException;

   List getGroupByIdWithPermission(@Param("cmd") String var1, @Param("id") long var2, @Param("userId") String var4) throws SQLException;

   List getGroupByUserGroup(@Param("groupList") List var1) throws SQLException;

   List getRootGroupById(@Param("cmd") String var1, @Param("oranization") String var2) throws SQLException;

   List getVwlGroupById(@Param("cmd") String var1, @Param("id") long var2) throws SQLException;

   List getAdminVwlRootGroupById(@Param("cmd") String var1, @Param("id") long var2) throws SQLException;

   List getVwlRootGroupById(@Param("cmd") String var1, @Param("oranization") String var2) throws SQLException;

   int getOrganizationCount(@Param("table") String var1) throws SQLException;

   List getOrganization(@Param("table") String var1) throws SQLException;

   boolean setOrganizationByDeviceId(@Param("table") String var1, @Param("organization") String var2, @Param("deviceId") String var3) throws SQLException;

   List getChildGroupIdLists(@Param("group_id") int var1, @Param("device_type") String var2, @Param("nonApprovalGroupId") int var3) throws SQLException;

   List getDynamicChildGroupIdList(@Param("group_id") int var1, @Param("deviceType") String var2, @Param("nonApprovalGroupId") int var3) throws SQLException;

   List getAuthGroupById(@Param("cmd") String var1, @Param("id") long var2) throws SQLException;

   boolean getDeviceAuthor(@Param("groupId") long var1, @Param("userId") String var3) throws SQLException;

   int checkIsSyncGroup(@Param("devGroupId") int var1) throws SQLException;

   int groupDepthCount(@Param("depth") int var1) throws SQLException;

   List getDeviceTotalCountLists(@Param("depth") int var1, @Param("startPos") int var2, @Param("pageSize") int var3) throws SQLException;

   int getChildrenGroupCount(@Param("groupId") long var1) throws SQLException;

   boolean updateDeviceTotalCount(@Param("totalCount") long var1, @Param("groupId") long var3) throws SQLException;

   Map getChildrenSum(@Param("groupId") long var1) throws SQLException;

   List getDeviceModelName(@Param("groupId") long var1) throws SQLException;

   Integer getCntDeviceInDeviceGroupExceptFor(@Param("groupId") int var1, @Param("deviceTypeList") List var2) throws SQLException;

   String getModelCountInfo(@Param("groupId") long var1) throws SQLException;

   long getDiskSpaceRepository(@Param("groupIds") List var1) throws SQLException;

   List getOrganizationGroup() throws SQLException;

   List getAllDGroups() throws SQLException;

   List getAllDeviceGroups(@Param("groupId") long var1, @Param("groupName") String var3) throws SQLException;

   List getAllDeviceGroupsWithPermission(@Param("groupId") long var1, @Param("userId") String var3) throws SQLException;

   List getAllDeviceGroupsWithGroupIds(@Param("groupIds") List var1, @Param("groupName") String var2) throws SQLException;

   List getAllAuthorityDeviceGroups(@Param("userId") String var1) throws SQLException;

   int getUnapprovedDeviceCountByUser(@Param("groupIds") List var1) throws SQLException;

   long getTotalApprovalDeviceCount() throws SQLException;

   List getDeviceCountByOrganization() throws SQLException;

   List getParentGroupNamePathByGroupId(@Param("groupId") Long var1);

   String getParentOrgNameByGroupId(@Param("groupId") Long var1);

   boolean changeDeviceOrgName(@Param("name") String var1, @Param("oldName") String var2) throws SQLException;

   int getCntAnalysisDeviceGroup() throws SQLException;

   boolean setAnalysisDeviceGroup(@Param("groupId") long var1, @Param("value") boolean var3) throws SQLException;

   String getDeviceTypeByGroupId(@Param("groupId") Long var1) throws SQLException;

   int setTotalCountByGroupIdWithRecursive(@Param("groupId") Long var1, @Param("command") String var2);

   int updateTotalDeviceCountOfParentGroupsRecursively(@Param("groupId") Long var1, @Param("count") Long var2);

   boolean addTotalCountByGroupId(@Param("groupId") Long var1, @Param("count") Long var2);

   boolean updateOrganizationByGroupId(@Param("groupId") long var1, @Param("organization") String var3) throws SQLException;

   List getDeviceGroupList(@Param("condition") DeviceGroupFilter var1, @Param("skipIds") List var2) throws SQLException;

   int deleteAlarmDeviceGroup(@Param("organizationId") Long var1) throws SQLException;

   int insertAlarmDeviceGroup(@Param("organizationId") Long var1, @Param("orgName") String var2, @Param("groupId") Long var3) throws SQLException;

   List getGroupDeviceListByOrganName(@Param("organName") String var1) throws SQLException;

   DeviceGroup getDeviceOrgGroupByUserOrgId(@Param("userOrgId") long var1) throws SQLException;

   List getAlarmDeviceGroup(@Param("organizationId") Long var1) throws SQLException;

   List getAlarmDeviceGroupByName(@Param("orgName") String var1) throws SQLException;

   Integer getCountByUserIdAndGroupId(@Param("userId") String var1, @Param("groupId") Long var2) throws SQLException;

   List V2GetChildDeviceIdListRecursive(@Param("groupId") int var1);

   List getDeviceIdListByDeviceGroupPermission(@Param("userId") String var1);

   int getDeviceGroupTotalCount();

   int getCntDeviceInProgram(String var1);

   List getDeviceInDeviceGroup(@Param("groupId") int var1) throws SQLException;
}
