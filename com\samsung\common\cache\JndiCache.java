package com.samsung.common.cache;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.protocol.queue.RequestContext;
import java.util.Hashtable;
import java.util.LinkedList;
import java.util.List;
import java.util.ListIterator;
import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NameNotFoundException;
import javax.naming.NamingException;
import net.spy.memcached.CASResponse;
import net.spy.memcached.CASValue;
import org.apache.logging.log4j.Logger;

public class JndiCache implements BasicCache {
   static Logger logger = LoggingManagerV2.getLogger(JndiCache.class);
   public static volatile Context ctx = null;
   private static JndiCache instance = null;
   private static final Object LOCK = new Object();

   private JndiCache() {
      super();
      if (ctx == null) {
         try {
            Hashtable env = new Hashtable();
            boolean jndiFlag = false;
            String strInitialContextClass = CommonConfig.get("java.naming.factory.initial");
            String strJndiUrl = CommonConfig.get("java.naming.provider.url");
            String strUrlPkgPrefixes = CommonConfig.get("java.naming.factory.url.pkgs");
            String strSecurityPrincipal = CommonConfig.get("java.naming.security.principal");
            String strSecurityCredentials = CommonConfig.get("java.naming.security.credentials");
            if (CommonConfig.get("java.naming.useJNDI") != null && CommonConfig.get("java.naming.useJNDI").equalsIgnoreCase("Y")) {
               jndiFlag = true;
            }

            logger.info("########## JNDI ########## ");
            logger.info(strInitialContextClass);
            logger.info(strJndiUrl);
            logger.info(strUrlPkgPrefixes);
            logger.info(strSecurityPrincipal);
            logger.info(strInitialContextClass);
            Class var8;
            if (jndiFlag) {
               if (strInitialContextClass != null && !strInitialContextClass.trim().equals("")) {
                  env.put("java.naming.factory.initial", strInitialContextClass);
               }

               if (strJndiUrl != null && !strJndiUrl.trim().equals("")) {
                  env.put("java.naming.provider.url", strJndiUrl);
               }

               if (strUrlPkgPrefixes != null && !strUrlPkgPrefixes.trim().equals("")) {
                  env.put("java.naming.factory.url.pkgs", strUrlPkgPrefixes);
               }

               if (strSecurityPrincipal != null && !strSecurityPrincipal.trim().equals("")) {
                  env.put("java.naming.security.principal", strSecurityPrincipal);
               }

               if (strSecurityCredentials != null && !strSecurityCredentials.trim().equals("")) {
                  env.put("java.naming.security.credentials", strSecurityCredentials);
               }

               var8 = JndiCache.class;
               synchronized(JndiCache.class) {
                  ctx = new InitialContext(env);
               }
            } else {
               var8 = JndiCache.class;
               synchronized(JndiCache.class) {
                  ctx = new InitialContext();
               }
            }
         } catch (Exception var13) {
            logger.error("", var13);
         }
      }

   }

   public static synchronized JndiCache getInstance() {
      logger.info("Instance: " + instance);
      if (instance == null) {
         logger.info("Creating a new instance");
         instance = new JndiCache();
      }

      return instance;
   }

   public void set(String key, Object o) throws Exception {
      try {
         ctx.rebind(key, o);
      } catch (NamingException var6) {
         logger.error("failed to rebind", var6);

         try {
            ctx.bind(key, o);
         } catch (NamingException var5) {
            logger.error("failed to bind", var5);
         }
      }

   }

   public void set(String key, int timeToLive, Object o) throws Exception {
   }

   public CASResponse cas(String key, long casId, Object o) throws Exception {
      this.set(key, o);
      return CASResponse.OK;
   }

   public Object get(String key) throws Exception {
      Object obj = null;

      try {
         obj = ctx.lookup(key);
      } catch (NamingException var4) {
      }

      return obj;
   }

   public CASValue gets(String key, Object autoInitObj) throws Exception {
      Object obj = this.get(key);
      if (obj == null) {
         if (autoInitObj == null) {
            return null;
         }

         ctx.bind(key, autoInitObj);
         obj = this.get(key);
      }

      return new CASValue(0L, obj);
   }

   public void delete(String key) throws Exception {
      try {
         ctx.unbind(key);
      } catch (NameNotFoundException var3) {
      } catch (NamingException var4) {
         logger.error("failed to unbind", var4);
      }

   }

   public void clean() throws Exception {
   }

   public boolean isEmpty(String key) throws Exception {
      Object o = this.get(key);
      return o == null;
   }

   public Object cas(String key, Object item, MutatorOperation operation) {
      try {
         synchronized(LOCK) {
            Object object = this.get(key);
            Object newValue;
            if (object == null) {
               newValue = operation.initialValue(item);
               this.set(key, newValue);
               return newValue;
            } else {
               newValue = operation.mutate(object, item);
               this.set(key, newValue);
               return newValue;
            }
         }
      } catch (Exception var9) {
         throw new CacheException(var9);
      }
   }

   public boolean putMapCache(String cacaheKey, String hashKey, Object o) {
      return false;
   }

   public boolean enQueue(String cacheKey, Object o) {
      return false;
   }

   public Object deQueue(String cacheKey) {
      return null;
   }

   public List readAllQueue(String cacheKey) {
      return null;
   }

   public Object getMapCache(String cacheKey, String hashKey, int command) {
      return null;
   }

   public void removeMapCache(String cacheKey, String hashKey) {
   }

   public List readAllMap(String cacheKey) {
      return null;
   }

   public int getSizeMap(String cacheKey) {
      return 0;
   }

   public Object getQueue(String cacheKey) {
      return null;
   }

   public boolean isExistServiceInQueue(String cacheKey, String service) throws Exception {
      try {
         LinkedList queue = (LinkedList)this.get(cacheKey);
         if (queue != null) {
            ListIterator iterator = (ListIterator)queue.iterator();

            while(iterator.hasNext()) {
               RequestContext request = (RequestContext)iterator.next();
               if (request != null && request.getWebServiceContext().getServiceID().equals(service)) {
                  return true;
               }
            }
         }

         return false;
      } catch (Exception var6) {
         logger.error("[JndiCache] fail isExistServiceInQueue e : " + var6.getMessage());
         throw var6;
      }
   }
}
