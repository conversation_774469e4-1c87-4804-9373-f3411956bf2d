package com.samsung.magicinfo.webauthor2.xml.datalink;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.XmlValue;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ValueLocationType", propOrder = {"value"})
public class ValueLocationType {
  @XmlValue
  protected String value;
  
  @XmlAttribute(name = "view")
  protected String view;
  
  public String getValue() {
    return this.value;
  }
  
  public void setValue(String value) {
    this.value = value;
  }
  
  public String getView() {
    return this.view;
  }
  
  public void setView(String value) {
    this.view = value;
  }
}
