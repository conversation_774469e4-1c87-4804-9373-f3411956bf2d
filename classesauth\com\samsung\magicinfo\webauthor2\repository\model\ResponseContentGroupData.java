package com.samsung.magicinfo.webauthor2.repository.model;

import com.samsung.magicinfo.webauthor2.repository.model.ResultContentGroupListData;
import java.io.Serializable;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "response")
public class ResponseContentGroupData implements Serializable {
  private static final long serialVersionUID = 1L;
  
  @XmlAttribute
  private String code;
  
  @XmlElement
  private String errorMessage;
  
  @XmlElement
  private ResultContentGroupListData responseClass;
  
  public String getCode() {
    return this.code;
  }
  
  public String getErrorMessage() {
    return this.errorMessage;
  }
  
  public ResultContentGroupListData getResponseClass() {
    return this.responseClass;
  }
}
