package com.samsung.common.utils;

import com.samsung.magicinfo.framework.user.entity.User;

public class RoleUtils {
   public static String SCOPE_ALL = "ALL";
   public static String SCOPE_GROUP = "GROUP";
   public static final String SUPER_ADMIN_ID = "admin";
   public static final String DEFAULT_ROLE_NAME_SERVER_ADMINISTRATOR = "Server Administrator";
   public static final String DEFAULT_ROLE_NAME_ADMINISTRATOR = "Administrator";
   public static final String DEFAULT_ROLE_NAME_CONTENT_MANAGER = "Content Manager";
   public static final String DEFAULT_ROLE_NAME_SCHEDULE_MANAGER = "Schedule Manager";
   public static final String DEFAULT_ROLE_NAME_DEVICE_MANAGER = "Device Manager";
   public static final String DEFAULT_ROLE_NAME_USER_MANAGER = "User Manager";
   public static final String DEFAULT_ROLE_NAME_CONTENT_UPLOADER = "Content Uploader";
   public static final String DEFAULT_ROLE_NAME_SCHEDULE_EDITOR = "Schedule Editor";
   public static final String DEFAULT_ROLE_NAME_CONTENT_SCHEDULE_MANAGER = "Content Schedule Manager";
   public static final int SUPER_ADMIN_ROLE_ID = 1;

   public RoleUtils() {
      super();
   }

   public static String getUserScope(User user) {
      return user != null && user.getRoot_group_id() != null && user.getRoot_group_id() == 0L ? SCOPE_ALL : SCOPE_GROUP;
   }

   public static boolean isServerAdminRole(String userRoleName) {
      return userRoleName.equals("Server Administrator");
   }

   public static boolean isServerAdminRole(User user) {
      return isServerAdminRole(user.getRole_name());
   }

   public static boolean isAdminRole(String userRoleName) {
      return userRoleName.equals("Administrator");
   }

   public static boolean isAdminRole(User user) {
      return isAdminRole(user.getRole_name());
   }
}
