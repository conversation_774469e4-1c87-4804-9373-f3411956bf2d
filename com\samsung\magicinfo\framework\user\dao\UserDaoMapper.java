package com.samsung.magicinfo.framework.user.dao;

import com.samsung.magicinfo.auth.security.otp.UserAuthDevice;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.entity.UserSearch;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

public interface UserDaoMapper {
   User getAllByUserId(@Param("userId") String var1) throws SQLException;

   int getCountByUserId(@Param("userId") String var1) throws SQLException;

   int getCountByUserIdForCheck(@Param("userId") String var1) throws SQLException;

   List getAllUser(@Param("map") Map var1, @Param("condition") UserSearch var2) throws SQLException;

   int getCountAllUser(@Param("map") Map var1, @Param("condition") UserSearch var2) throws SQLException;

   List getGroupedUser(@Param("map") Map var1, @Param("condition") UserSearch var2) throws SQLException;

   int getCountGroupedUser(@Param("map") Map var1, @Param("condition") UserSearch var2) throws SQLException;

   List getAllNonApprovedUser(@Param("map") Map var1) throws SQLException;

   int getCountAllNonApprovedUser(@Param("map") Map var1) throws SQLException;

   List getAllWithdrawalUser(@Param("map") Map var1) throws SQLException;

   int getCountAllWithdrawalUser(@Param("map") Map var1) throws SQLException;

   boolean setShortcut(@Param("userId") String var1, @Param("shortcut") String var2) throws SQLException;

   String getShortcut(@Param("userId") String var1) throws SQLException;

   Group getOrganGroupInfo(@Param("groupID") Long var1) throws SQLException;

   String[] getAllUserListByEmailSendingOptions(@Param("organization_id") Long var1, @Param("get_fault") String var2, @Param("get_alarm") String var3, @Param("rootGroupId") int var4) throws SQLException;

   boolean setEmailSendingOptions(@Param("user_id") String var1, @Param("get_fault") String var2, @Param("get_alarm") String var3) throws SQLException;

   boolean setEmailSendingDisconnectAlarm(@Param("user_id") String var1, @Param("disconnect_alarm") String var2) throws SQLException;

   User getUserInfo(@Param("userID") String var1) throws SQLException;

   String getIsResetPwdByUserId(@Param("userId") String var1) throws SQLException;

   String getOsTypeByUserId(@Param("userId") String var1) throws SQLException;

   String getImeiByUserId(@Param("userId") String var1) throws SQLException;

   List getAllUserListBySearch(@Param("search_id") Long var1, @Param("condition") UserSearch var2) throws SQLException;

   List getSMSList(@Param("userIdList") List var1) throws SQLException;

   List getMailList(@Param("userIdList") List var1) throws SQLException;

   String getOrganNameByUserId(@Param("userId") String var1) throws SQLException;

   List getAllUserList(@Param("organization_id") Long var1) throws SQLException;

   List getAllUserListToMigrate() throws SQLException;

   boolean migrateUser(@Param("user") User var1) throws SQLException;

   List getAllUserListNonApproved(@Param("map") Map var1, @Param("condition") UserSearch var2) throws SQLException;

   List getAllUserListApproved(@Param("map") Map var1, @Param("condition") UserSearch var2) throws SQLException;

   Map getDeleteInfoByUserId(@Param("userId") String var1) throws SQLException;

   Map getIsRejectByUserId(@Param("userId") String var1) throws SQLException;

   List getAllRootUserList() throws SQLException;

   List getAllUserListByRootGroupId(@Param("organization_id") Long var1, @Param("root_group_id") Integer var2) throws SQLException;

   List getAllUserByRootGroupId(@Param("rootGroupId") Long var1) throws SQLException;

   List getAllApprovalUserByRootGroupIdAll(@Param("rootGroupId") Long var1) throws SQLException;

   List getAllApprovalUserByRootGroupId(@Param("rootGroupId") Long var1) throws SQLException;

   String getIsDeletedByUserId(@Param("userId") String var1) throws SQLException;

   String getIsApprovedByUserId(@Param("userId") String var1) throws SQLException;

   int deleteUser(@Param("userId") String var1) throws SQLException;

   User getUserByUserId(@Param("userId") String var1) throws SQLException;

   Map getRootGroupIdByUserId(@Param("userId") String var1) throws SQLException;

   String getNameByUserId(@Param("userId") String var1) throws SQLException;

   int addUser(@Param("user") User var1, @Param("ldap_info") String var2) throws SQLException;

   Boolean setUser(@Param("user") User var1) throws SQLException;

   Boolean setIsApprovedByUserId(@Param("userId") String var1, @Param("approved") String var2) throws SQLException;

   Boolean setLoginDateByUserId(@Param("userId") String var1) throws SQLException;

   Boolean setIsDeletedByUserId(@Param("userId") String var1, @Param("reason") String var2, @Param("delete_type") String var3) throws SQLException;

   boolean setOrganizationByUserIdList(@Param("userIdList") List var1, @Param("organization") String var2) throws SQLException;

   boolean setRejectUser(@Param("userId") String var1, @Param("rejectReason") String var2) throws SQLException;

   int deleteMappingInfoByUserID_GroupUser(@Param("userId") String var1) throws SQLException;

   int deleteMappingInfoByUserID_RoleUser(@Param("userId") String var1) throws SQLException;

   int deleteMappingInfoByUserID_MenuUser(@Param("userId") String var1) throws SQLException;

   int getCountByUserIdIsDeleted(@Param("userId") String var1) throws SQLException;

   int getCountByLDAPUserFullIdForCheck(@Param("ldapFullId") String var1) throws SQLException;

   int getCountByLDAPUserIdForCheck(@Param("ldapUserId") String var1) throws SQLException;

   List getLdapUserInfo(@Param("ldapUserId") String var1) throws SQLException;

   int deleteLDAPUser(@Param("orgId") Long var1) throws SQLException;

   int removeLdapInfomation(@Param("orgId") Long var1) throws SQLException;

   List getNewAndModifiedUserByDate(@Param("startDate") Timestamp var1, @Param("endDate") Timestamp var2) throws SQLException;

   List getDashboard(@Param("userId") String var1) throws SQLException;

   List getDashboardOrderByDashboardId(@Param("userId") String var1) throws SQLException;

   List getOrganization() throws SQLException;

   List getDisconnectAlarmUserList(@Param("orgId") Long var1) throws SQLException;

   boolean addDashboardWithUserId(@Param("userId") String var1, @Param("dashboardId") int var2, @Param("priority") int var3) throws SQLException;

   List getContentManagerUserListByOrgId(@Param("orgId") Long var1) throws SQLException;

   boolean setContentApprover(@Param("userId") String var1, @Param("contentApprover") String var2) throws SQLException;

   boolean setAllContentApprover(@Param("contentApprover") String var1) throws SQLException;

   List getContentApproverListByGroupId(@Param("rootGroupId") Long var1) throws SQLException;

   List getContentApprover() throws SQLException;

   boolean setLocale(@Param("userId") String var1, @Param("locale") String var2) throws SQLException;

   List getEmailNotificationUserListByOrgId(@Param("orgId") Long var1, @Param("includeRoot") boolean var2) throws SQLException;

   boolean addEmailNotificationOption(@Param("orgId") Long var1, @Param("userId") String var2, @Param("type") String var3) throws SQLException;

   boolean deleteEmailNotificationOption(@Param("orgId") Long var1, @Param("userId") String var2, @Param("type") String var3) throws SQLException;

   int deleteEmailNotificationByOrdIdAndUserId(@Param("orgId") Long var1, @Param("userId") String var2) throws SQLException;

   List getAlarmUserListByOrgIdAndType(@Param("orgId") Long var1, @Param("type") String var2);

   String getOrganNameByRootGroupId(@Param("rootGroupId") long var1);

   Integer deleteDashboardUserInfoByUserId(@Param("userId") String var1);

   Boolean setIsFirstLoginByUserId(@Param("userId") String var1);

   boolean setResetPwToken(@Param("encryptionToken") String var1, @Param("passwordResetExpirationInSec") long var2, @Param("userId") String var4);

   Boolean checkResetValidByTokenAndId(@Param("userId") String var1, @Param("encryptionToken") String var2);

   List getAllUserWithEncryptionToken();

   long getCurMngOrgId(@Param("userId") String var1) throws SQLException;

   boolean setCurMngOrgId(@Param("userId") String var1, @Param("mngOrg") long var2) throws SQLException;

   List getMUInfoByMngOrgId(@Param("mngOrg") long var1) throws SQLException;

   boolean setMUByUserId(@Param("userId") String var1, @Param("flag") String var2) throws SQLException;

   User getAdminOfOrganizationByRootGroupId(@Param("roleId") long var1, @Param("rootGroupId") long var3) throws SQLException;

   User getAdminOfOrganizationNotInDeleteUsers(@Param("roleId") long var1, @Param("rootGroupId") long var3, @Param("deleteUsers") String[] var5) throws SQLException;

   int setIsResetPasswordBatch(@Param("passwordChangeDate") Timestamp var1) throws SQLException;

   List getfilterExport(@Param("map") Map var1) throws SQLException;

   List getfilterExportUnApporved(@Param("map") Map var1) throws SQLException;

   int addAuthDeviceInfo(@Param("userAuthDevice") UserAuthDevice var1);

   List getUserAuthDevice(@Param("userId") String var1) throws SQLException;

   List getUserStoredDevice(@Param("userId") String var1) throws SQLException;

   int setUserSecretKey(@Param("userId") String var1, @Param("secretKey") String var2) throws SQLException;

   int deleteUserDevice(@Param("authDeviceId") int var1) throws SQLException;

   int deleteUserDeviceByUserId(@Param("userId") String var1) throws SQLException;

   int deleteExpiredUserDevice() throws SQLException;

   int updateUserDeviceExpiredDate(@Param("day") int var1) throws SQLException;

   int updateUserDeviceByUserId(@Param("userId") String var1) throws SQLException;

   int deleteAllMfaDevice() throws SQLException;

   String unapprovedUser(@Param("userId") String var1) throws SQLException;
}
