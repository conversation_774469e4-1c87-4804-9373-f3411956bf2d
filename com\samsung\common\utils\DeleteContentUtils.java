package com.samsung.common.utils;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.content.dao.PlaylistDao;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.entity.TemplateContentSetting;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentXmlEditor;
import com.samsung.magicinfo.framework.content.manager.ContentXmlEditorInfo;
import com.samsung.magicinfo.framework.content.manager.ConvertDataInfo;
import com.samsung.magicinfo.framework.content.manager.ConvertDataInfoImpl;
import com.samsung.magicinfo.framework.content.manager.LogInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.content.manager.SaveTemplateContentXmlThread;
import com.samsung.magicinfo.framework.content.manager.XPathContentEditorImpl;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistInterface;
import com.samsung.magicinfo.framework.scheduler.dao.EventInfoDao;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAO;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleAdminInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleAdminInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.common.ScheduleInterface;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.util.StringUtils;
import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import org.apache.logging.log4j.Logger;
import org.springframework.context.support.ResourceBundleMessageSource;

public class DeleteContentUtils {
   static UserContainer userContainer = null;
   static Logger logger = LoggingManagerV2.getLogger(DeleteContentUtils.class);
   static ResourceBundleMessageSource rms = new ResourceBundleMessageSource();

   public DeleteContentUtils() {
      super();
   }

   public static boolean changePlaylistOrder(String playlistId, Long versionId) {
      boolean result = true;
      PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();
      List pList = pInfo.getContentOrderListOfPlaylist(playlistId, versionId);
      List subPlaylistContentList = pInfo.getSubPlaylistContentOrderListOfPlaylist(playlistId, versionId);
      pList.addAll(subPlaylistContentList);
      Collections.sort(pList, new Comparator() {
         public int compare(PlaylistContent c1, PlaylistContent c2) {
            return Long.compare(c1.getContent_order(), c2.getContent_order());
         }
      });
      int listSize = pList.size();
      if (listSize > 0) {
         for(int i = 1; i <= listSize; ++i) {
            if (((PlaylistContent)pList.get(i - 1)).getContent_order() != (long)i) {
               if (((PlaylistContent)pList.get(i - 1)).getIs_sub_playlist()) {
                  pInfo.updateSubPlaylistContentOrder(i, playlistId, versionId, ((PlaylistContent)pList.get(i - 1)).getContent_order());
               } else {
                  pInfo.updateContentOrder(i, playlistId, versionId, ((PlaylistContent)pList.get(i - 1)).getContent_order());
               }

               pInfo.updateContentOrderOfTag(playlistId, versionId, ((PlaylistContent)pList.get(i - 1)).getContent_id(), i);
            }
         }

         pInfo.updateContentCount(listSize, playlistId, versionId);
      } else {
         result = false;
      }

      return result;
   }

   public static boolean isUsablePlaylist(String playlistId) {
      boolean result = true;
      int contentSize = 0;
      PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();

      try {
         List pList = pInfo.getPlaylistAllVerInfo(playlistId);
         if (pList != null) {
            Playlist p;
            for(Iterator var5 = pList.iterator(); var5.hasNext(); contentSize += p.getArr_content_list().size()) {
               p = (Playlist)var5.next();
            }

            if (contentSize == 0) {
               result = false;
            }
         }
      } catch (SQLException var7) {
         logger.error("", var7);
      }

      return result;
   }

   public static void checkContentFromPlaylist(String contentId, String ipAddress) {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      ScheduleInfo sInfo = ScheduleInfoImpl.getInstance();
      EventInfo eInfo = EventInfoImpl.getInstance();
      List pList = null;
      List nestedPlaylists = new ArrayList();
      Set playlistIdList = new HashSet();
      ArrayList playlistIdListVersion = new ArrayList();
      HashSet playlistDeleteIdList = new HashSet();

      try {
         pList = cInfo.getPlaylistInfoUsingContent(contentId);
         if (pList != null && pList.size() > 0) {
            deleteContentFromPlaylist(contentId, ipAddress);
            Long sizeOfDeletedContent = cInfo.getContentActiveVerInfo(contentId).getTotal_size();
            Map map = null;
            String playlistId = null;
            Long versionId = null;
            String playTime = null;
            Long contentDuration = null;
            Long totalSize = null;
            String timeOfDeletedContent = null;
            PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();
            PlaylistDao dao = new PlaylistDao();

            int countPlaylistIdListVersion;
            String tPlaylistId;
            for(countPlaylistIdListVersion = 0; countPlaylistIdListVersion < pList.size(); ++countPlaylistIdListVersion) {
               map = (Map)pList.get(countPlaylistIdListVersion);
               playlistId = (String)map.get("playlist_id");
               versionId = (Long)map.get("version_id");
               playTime = (String)map.get("play_time");
               contentDuration = (Long)((Long)map.get("content_duration"));
               totalSize = (Long)((Long)map.get("total_size"));
               boolean result = changePlaylistOrder(playlistId, versionId);
               timeOfDeletedContent = DateUtils.changeSecondToFormatTime(Math.toIntExact(contentDuration));
               playTime = DateUtils.subtractStringTime(playTime, timeOfDeletedContent);
               pInfo.setPlaytime(playlistId, versionId, playTime);
               totalSize = totalSize - sizeOfDeletedContent;
               pInfo.setTotalSize(playlistId, versionId, totalSize);
               tPlaylistId = dao.getPlaylistTypeFromPlaylistId(playlistId);
               if (tPlaylistId.equals("6")) {
                  map.put("time_of_deleted_contents", timeOfDeletedContent);
                  map.put("size_of_deleted_contents", sizeOfDeletedContent);
                  nestedPlaylists.add(map);
               }

               if (result) {
                  playlistIdList.add(playlistId);
                  playlistIdListVersion.add(versionId);
               } else {
                  playlistDeleteIdList.add(playlistId);
               }
            }

            for(countPlaylistIdListVersion = 0; countPlaylistIdListVersion < nestedPlaylists.size(); ++countPlaylistIdListVersion) {
               map = (Map)nestedPlaylists.get(countPlaylistIdListVersion);
               timeOfDeletedContent = (String)map.get("time_of_deleted_contents");
               sizeOfDeletedContent = (Long)((Long)map.get("size_of_deleted_contents"));
               dao.updateParentPlaylists(playlistId, 0L - ContentUtils.getPlayTimeStr(timeOfDeletedContent), 0L - sizeOfDeletedContent);
            }

            countPlaylistIdListVersion = 0;
            Iterator var27 = playlistIdList.iterator();

            while(var27.hasNext()) {
               tPlaylistId = (String)var27.next();
               Long playlistActiveVersionId = pInfo.getPlaylistActiveVersionId(tPlaylistId);
               Long tPlaylistVersionId = (Long)playlistIdListVersion.get(countPlaylistIdListVersion);
               if (playlistActiveVersionId == tPlaylistVersionId) {
                  sInfo.setPlaylistTrigger(tPlaylistId);
                  eInfo.setPlaylistTrigger(tPlaylistId);
               }

               ++countPlaylistIdListVersion;
               playlistDeleteIdList.remove(tPlaylistId);
            }

            var27 = playlistDeleteIdList.iterator();

            while(var27.hasNext()) {
               tPlaylistId = (String)var27.next();
               checkPlaylistFromSchedule(tPlaylistId, ipAddress);
               deletePlaylist(tPlaylistId, ipAddress);
            }
         }
      } catch (SQLException var25) {
         logger.error("", var25);
      } catch (Exception var26) {
         logger.error("", var26);
      }

   }

   public static void checkContentFromSchedule(String contentId, String ipAddress) {
      ScheduleInterface sInfo = DAOFactory.getScheduleInfoImpl("PREMIUM");
      ScheduleInfoDAO dao = new ScheduleInfoDAO();
      rms.setBasename("resource/messages");
      (new StringBuilder()).append(rms.getMessage("TEXT_UNKNOWN_P", (Object[])null, new Locale("en"))).append(rms.getMessage("TEXT_TITLE_USER_P", (Object[])null, new Locale("en"))).toString();

      try {
         List list1 = sInfo.getProgramByContentId(contentId);
         if (list1 != null && list1.size() > 0) {
            deleteContentFromSchedule(contentId, ipAddress);
            Map map = null;

            for(int j = 0; j < list1.size(); ++j) {
               map = (Map)list1.get(j);
               String programId = (String)map.get("PROGRAM_ID");
               dao.programVersionUp(programId);
               ProgramEntity program = dao.getProgram(programId);
               if (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals("")) {
                  sInfo.reserveSchedule(program);
                  UserContainer var10 = null;

                  try {
                     var10 = SecurityUtils.getUserContainer();
                  } catch (Exception var12) {
                     logger.error("[MagicInfo_deleteContentUtils_checkContentFromSchedule] fail get UserContainer. e : " + var12.getMessage());
                  }
               }
            }
         }
      } catch (Exception var13) {
         logger.error("", var13);
      }

   }

   public static void checkPlaylistFromSchedule(String playlistId, String ipAddress) {
      ScheduleInterface sInfo = DAOFactory.getScheduleInfoImpl("PREMIUM");
      ScheduleInfoDAO dao = new ScheduleInfoDAO();
      rms.setBasename("resource/messages");
      (new StringBuilder()).append(rms.getMessage("TEXT_UNKNOWN_P", (Object[])null, new Locale("en"))).append(rms.getMessage("TEXT_TITLE_USER_P", (Object[])null, new Locale("en"))).toString();
      UserContainer var5 = null;

      try {
         var5 = SecurityUtils.getUserContainer();
      } catch (Exception var11) {
         logger.error("[MagicInfo_deleteContentUtils_checkPlaylist] fail get UserContainer. e : " + var11.getMessage());
      }

      try {
         List list1 = sInfo.getProgramByPlaylistId(playlistId);
         if (list1 != null && list1.size() > 0) {
            deletePlaylistFromSchedule(playlistId, ipAddress);
            Map map = null;

            for(int j = 0; j < list1.size(); ++j) {
               map = (Map)list1.get(j);
               String programId = (String)map.get("PROGRAM_ID");
               dao.programVersionUp(programId);
               ProgramEntity program = dao.getProgram(programId);
               if (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals("")) {
                  sInfo.reserveSchedule(program);
               }
            }
         }
      } catch (Exception var12) {
         logger.error("", var12);
      }

   }

   public static void checkContentFromConvertTable(String contentId) {
      ConvertDataInfo convertDataDao = ConvertDataInfoImpl.getInstance();
      List list = null;

      try {
         list = convertDataDao.getConvertDataNameByContentId(contentId);
         if (list != null && list.size() > 0) {
            deleteContentFromConvertTable(contentId);

            for(int j = 0; j < list.size(); ++j) {
               Map map = (Map)list.get(j);
               modifyConvertTableFromDLK(map, contentId);
            }
         }
      } catch (SQLException var5) {
         logger.error("", var5);
      }

   }

   public static void checkContentFromDLK(String contentId, String ipAddress) {
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      List dlkContentList = null;

      try {
         dlkContentList = contentInfo.getDlkContentIdByIputDataContentId(contentId);
         if (dlkContentList != null && dlkContentList.size() > 0) {
            deleteContentFromDLK(contentId);

            for(int i = 0; i < dlkContentList.size(); ++i) {
               Map dlkContentMap = (Map)dlkContentList.get(i);
               String dlkContentId = dlkContentMap.get("DLK_CONTENT_ID").toString();
               Content dlkContent = contentInfo.getContentActiveVerInfo(dlkContentId);
               String lfdContentId = contentInfo.getLfdContentIdByDlkContentId(dlkContentId);
               Content lfdActiveContent = contentInfo.getContentActiveVerInfo(lfdContentId);
               String dlkDataList = contentInfo.getTemplateElementDataListString(dlkContentId, dlkContent.getVersion_id());
               dlkDataList = StringUtils.replaceStrHTML(dlkDataList);
               if (dlkDataList.equals("")) {
                  checkContentFromPlaylist(dlkContentId, ipAddress);
                  checkContentFromSchedule(dlkContentId, ipAddress);
                  deleteContent(dlkContentId);
               } else {
                  List contentList = contentInfo.getContentIdListByDlkContentId(dlkContentId, dlkContent.getVersion_id());
                  String newDlkContentFileId = UUID.randomUUID().toString().toUpperCase();
                  String fromLfdFile = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separator + "contents_home" + File.separator + lfdActiveContent.getMain_file_id() + File.separator + lfdActiveContent.getMain_file_name();
                  String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
                  String filePath = CONTENTS_HOME + File.separator + newDlkContentFileId;
                  File fileCmsFile = SecurityUtils.getSafeFile(filePath);
                  if (!fileCmsFile.exists()) {
                     fileCmsFile.mkdir();
                  }

                  String toDlkFile = filePath + File.separator + dlkContent.getMain_file_name();
                  Long dlkVersion = contentInfo.getContentNextVer(dlkContentId);
                  ContentFile contentFile = contentInfo.getMainFileInfo(dlkContentId);
                  contentFile.setFile_id(newDlkContentFileId);
                  contentFile.setFile_size(0L);
                  String newDlkFilePath = CONTENTS_HOME + File.separator + newDlkContentFileId;
                  contentFile.setFile_path(newDlkFilePath);
                  dlkContent.setMain_file_id(newDlkContentFileId);
                  List fileList = new ArrayList();
                  fileList.add(contentFile);
                  dlkContent.setArr_file_list(fileList);
                  dlkContent.setOrg_creator_id(dlkContent.getCreator_id());
                  contentInfo.addContent(dlkContent);
                  contentInfo.setVersionId(dlkContentId, 0L, dlkVersion);
                  contentInfo.addTemplateElementData(lfdContentId, lfdActiveContent.getVersion_id(), dlkContentId, dlkVersion, dlkDataList);
                  TemplateContentSetting templateContentInfo = new TemplateContentSetting();
                  templateContentInfo.setContentLftId(lfdContentId);
                  templateContentInfo.setContentDlkId(dlkContentId);
                  templateContentInfo.setContentDlkFileId(newDlkContentFileId);
                  templateContentInfo.setTemplateLftFile(contentInfo.getMainFileInfo(lfdContentId));
                  templateContentInfo.setFromLftFile(fromLfdFile);
                  templateContentInfo.setToDlkFile(toDlkFile);
                  templateContentInfo.setContentData(dlkDataList);
                  templateContentInfo.setDlkVersionId(dlkVersion);
                  templateContentInfo.setPollingInterval(dlkContent.getPolling_interval());
                  StringBuffer content_id_list = new StringBuffer("");

                  for(int k = 0; k < contentList.size(); ++k) {
                     if (k != 0) {
                        content_id_list.append(",");
                     }

                     content_id_list.append(((Map)contentList.get(k)).get("CONTENT_ID"));
                  }

                  contentInfo.addTemplateContent(dlkContentId, dlkVersion, "LFT", lfdContentId);
                  contentInfo.addContentIdListForDlkContent(dlkContentId, dlkVersion, content_id_list.toString());
                  SaveTemplateContentXmlThread saveTemplateContentXmlThread = new SaveTemplateContentXmlThread(templateContentInfo);
                  saveTemplateContentXmlThread.start();
               }
            }
         }
      } catch (SQLException var25) {
         logger.error("", var25);
      } catch (ConfigException var26) {
         logger.error("", var26);
      } catch (Exception var27) {
         logger.error("", var27);
      }

   }

   public static void checkContentFromEventCondition(String contentId) {
      EventInfoDao eventInfoDao = new EventInfoDao();
      EventInfo eventInfo = EventInfoImpl.getInstance();
      List eventList = null;

      try {
         eventList = eventInfoDao.getEventIdListByContentId(contentId);
         if (eventList != null && eventList.size() > 0) {
            deleteContentFromEventManager(contentId);
         }
      } catch (SQLException var5) {
         logger.error("", var5);
      } catch (Exception var6) {
         logger.error("", var6);
      }

   }

   public static void deleteContentFromSchedule(String contentId, String ipAddress) {
      ScheduleAdminInfo infoDao = ScheduleAdminInfoImpl.getInstance();
      ScheduleInfoDAO dao = new ScheduleInfoDAO();

      try {
         infoDao.deleteContentFromSchedule(contentId);
         infoDao.deleteContentFromScheduleReserved(contentId);
         infoDao.deleteContentFromScheduleTemp(contentId);
         dao.deleteBgmContentInProgram(contentId);
      } catch (Exception var5) {
         logger.error("", var5);
      }

   }

   public static void deleteContentFromPlaylist(String contentId, String ipAddress) {
      PlaylistInfo pinfo = PlaylistInfoImpl.getInstance();
      LogInfoImpl var3 = LogInfoImpl.getInstance();

      try {
         pinfo.deleteContentFromPlaylist(contentId);
         pinfo.deleteContentTagFromPlaylist(contentId);
      } catch (SQLException var5) {
         logger.error("", var5);
      }

   }

   public static void deletePlaylistFromSchedule(String playlistId, String ipAddress) {
      ScheduleAdminInfo infoDao = ScheduleAdminInfoImpl.getInstance();
      LogInfoImpl var3 = LogInfoImpl.getInstance();

      try {
         infoDao.deleteContentFromSchedule(playlistId);
      } catch (Exception var5) {
         logger.error("", var5);
      }

   }

   public static void deleteContentFromDLK(String contentId) {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      LogInfoImpl var2 = LogInfoImpl.getInstance();

      try {
         cInfo.deleteContentFromDLK(contentId);
         cInfo.deleteContentIdFromDlkContentIdMap(contentId);
      } catch (SQLException var4) {
         logger.error("", var4);
      }

   }

   public static void deleteContentFromConvertTable(String contentId) {
      ConvertDataInfoImpl convertDataDao = ConvertDataInfoImpl.getInstance();

      try {
         convertDataDao.deleteConvertDataContent(contentId);
      } catch (SQLException var3) {
         logger.error("", var3);
      }

   }

   public static void deleteContentFromEventManager(String contentId) {
      EventInfoDao eventInfoDao = new EventInfoDao();
      EventInfo eventInfo = EventInfoImpl.getInstance();
      LogInfoImpl var3 = LogInfoImpl.getInstance();

      try {
         List eventList = eventInfoDao.getEventIdListByContentId(contentId);
         if (eventList != null && eventList.size() > 0) {
            eventInfoDao.deleteContentIdFromEventCondition(contentId);

            for(int i = 0; i < eventList.size(); ++i) {
               String eventId = (String)((Map)eventList.get(i)).get("event_id");
               eventInfo.setEventTrigger(eventId);
            }
         }
      } catch (Exception var7) {
         logger.error("", var7);
      }

   }

   public static void modifyConvertTableFromDLK(Map convertMap, String contentId) {
      ConvertDataInfo convertDataDao = ConvertDataInfoImpl.getInstance();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      List templateElementDataList = null;

      try {
         templateElementDataList = convertDataDao.getTemplateElementDataByConvertTable((String)convertMap.get("CONVERT_DATA_NAME"));
         int templateElementDataListSize = templateElementDataList.size();

         for(int i = 0; i < templateElementDataListSize; ++i) {
            Map map = (Map)templateElementDataList.get(i);
            String dlkContentId = (String)map.get("DLK_CONTENT_ID");
            Content dlkContent = contentInfo.getContentAndFileActiveVerInfo(dlkContentId);
            String oldFileName = FileUtils.getPathFile(dlkContent.getMain_file_id(), dlkContent.getMain_file_name());
            String newContentFileId = UUID.randomUUID().toString().toUpperCase();
            String newFileFolder = FileUtils.getPathFolder(newContentFileId);
            String newFileName = FileUtils.getPathFile(newContentFileId, dlkContent.getMain_file_name());
            FileUtils.makeFolder(newFileFolder);
            ContentXmlEditorInfo xPathContentEditor = new XPathContentEditorImpl();
            ContentXmlEditor contentXmlEditor = new ContentXmlEditor(xPathContentEditor);
            contentInfo.getMainFileInfo(contentId);
            String[] data = new String[]{(String)convertMap.get("FROM_DATA")};
            contentXmlEditor.deleteConvertData(oldFileName, newFileName, (Map)templateElementDataList.get(i), data);
            dlkContent = ContentUtils.addNewContent(newFileName, dlkContentId, newContentFileId, dlkContent, (UserContainer)null);
            contentInfo.modifyDlkContentByConvertData(dlkContent);
         }
      } catch (ConfigException var19) {
         logger.error("", var19);
      } catch (SQLException var20) {
         logger.error("", var20);
      }

   }

   public static void deleteContent(String contentId) {
      ContentInfoImpl cInfo = ContentInfoImpl.getInstance();

      try {
         if (cInfo.deleteContentCompletely(contentId) <= 0) {
         }
      } catch (SQLException var3) {
         logger.error("", var3);
      }

   }

   public static void deletePlaylist(String playlistId, String ipAddress) {
      String productType = "PREMIUM";
      PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl(productType);

      try {
         PlaylistInfo info = PlaylistInfoImpl.getInstance();
         String creatorId = info.getCreatorIdByPlaylistId(playlistId);
         pInfo.deletePlaylist(playlistId, creatorId, (String)null);
         if (pInfo.deletePlaylistCompletely(playlistId) <= 0) {
         }
      } catch (SQLException | ConfigException var6) {
         logger.error("", var6);
      }

   }

   public static void deletContentByGroupId(Long groupId, String ipAddress) throws SQLException {
      ContentInfo cinfo = ContentInfoImpl.getInstance();
      List contentList = cinfo.getGroupedContentIdList(groupId);
      if (contentList != null && contentList.size() > 0) {
         for(int i = 0; i < contentList.size(); ++i) {
            Map map = (Map)contentList.get(i);
            String tmpContentId = (String)map.get("content_id");
            checkContentFromPlaylist(tmpContentId, ipAddress);
            checkContentFromSchedule(tmpContentId, ipAddress);
            deleteContent(tmpContentId);
         }
      }

   }

   public static void deleteTLFDByGroupId(Long groupId) throws SQLException {
      ContentInfo cinfo = ContentInfoImpl.getInstance();
      List contentList = cinfo.getTLFDListByGroupId(groupId);
      if (contentList != null && contentList.size() > 0) {
         for(int i = 0; i < contentList.size(); ++i) {
            Map map = (Map)contentList.get(i);
            String tmpContentId = (String)map.get("content_id");
            deleteTLFDContent(tmpContentId);
         }
      }

   }

   public static void deleteTLFDContent(String contentId) {
      ContentInfoImpl cInfo = ContentInfoImpl.getInstance();

      try {
         if (cInfo.deleteContentCompletely(contentId) <= 0) {
         }
      } catch (SQLException var3) {
         logger.error("", var3);
      }

   }
}
