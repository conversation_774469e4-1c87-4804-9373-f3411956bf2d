package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.DataLinkServer;
import com.samsung.magicinfo.webauthor2.model.DataLinkTable;
import com.samsung.magicinfo.webauthor2.model.DataLinkTableRow;
import com.samsung.magicinfo.webauthor2.model.datalink.DataLinkDescriptor;
import java.util.List;

public interface DataLinkService {
  List<DataLinkServer> getDataLinkServerList();
  
  List<DataLinkServer> getDataLinkServerListCached();
  
  List<DataLinkTable> getDataLinkServerTables(String paramString1, String paramString2);
  
  List<DataLinkTable> getDataLinkServerTablesForService(String paramString1, String paramString2, String paramString3);
  
  List<String> getDataLinkServerServices(String paramString1, String paramString2);
  
  List<DataLinkTableRow> getDataLinkServerTableRows(String paramString1, String paramString2);
  
  DataLinkDescriptor getDataLinkDescriptor(String paramString);
}
