package com.samsung.magicinfo.rc.common.http;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import org.apache.http.HttpEntity;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.config.Lookup;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.BasicHttpClientConnectionManager;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
public class CustomHttpExecutor {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.common.http.CustomHttpExecutor.class);
  
  private final CloseableHttpClient httpClient;
  
  public CustomHttpExecutor(RequestConfig requestConfig) {
    this.httpClient = createDefaultHttpClient(requestConfig, defaultConnectionManager());
  }
  
  private static CloseableHttpClient createDefaultHttpClient(RequestConfig requestConfig, PoolingHttpClientConnectionManager connectionManager1) {
    SSLConnectionSocketFactory csf = null;
    TrustStrategy acceptingTrustStrategy = (cert, authType) -> true;
    try {
      SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(null, acceptingTrustStrategy).build();
      csf = new SSLConnectionSocketFactory(sslContext, (HostnameVerifier)NoopHostnameVerifier.INSTANCE);
      Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.create().register("https", csf).register("http", new PlainConnectionSocketFactory()).build();
      BasicHttpClientConnectionManager connectionManager = new BasicHttpClientConnectionManager((Lookup)socketFactoryRegistry);
      CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory((LayeredConnectionSocketFactory)csf).setConnectionManager((HttpClientConnectionManager)connectionManager).build();
      return httpClient;
    } catch (NoSuchAlgorithmException e) {
      e.printStackTrace();
    } catch (KeyManagementException e) {
      e.printStackTrace();
    } catch (KeyStoreException e) {
      e.printStackTrace();
    } 
    return null;
  }
  
  private PoolingHttpClientConnectionManager defaultConnectionManager() {
    PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
    connectionManager.setMaxTotal(50);
    connectionManager.setDefaultMaxPerRoute(10);
    return connectionManager;
  }
  
  public String getForString(String url, String accessToken) throws IOException {
    HttpGet request = new HttpGet(url);
    request.setHeader("api_key", accessToken);
    request.setHeader("Content-Type", "application/json");
    return getString(url, this.httpClient.execute((HttpUriRequest)request));
  }
  
  public String postForString(String url, String accessToken, ByteArrayEntity entity) throws IOException {
    HttpPost request = new HttpPost(url);
    request.setHeader("api_key", accessToken);
    request.setHeader("Content-Type", "application/json");
    request.setEntity((HttpEntity)entity);
    return getString(url, this.httpClient.execute((HttpUriRequest)request));
  }
  
  public RestTemplate getRestTemplate() {
    SSLConnectionSocketFactory csf = null;
    TrustStrategy acceptingTrustStrategy = (chain, authType) -> true;
    try {
      SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(null, acceptingTrustStrategy).build();
      csf = new SSLConnectionSocketFactory(sslContext);
    } catch (NoSuchAlgorithmException e) {
      e.printStackTrace();
    } catch (KeyManagementException e) {
      e.printStackTrace();
    } catch (KeyStoreException e) {
      e.printStackTrace();
    } 
    CloseableHttpClient httpClient = HttpClientBuilder.create().setSSLSocketFactory((LayeredConnectionSocketFactory)csf).setConnectionManager((HttpClientConnectionManager)defaultConnectionManager()).setDefaultRequestConfig(RequestConfig.custom().setConnectionRequestTimeout(5000).setConnectTimeout(5000).setSocketTimeout(5000).setCookieSpec("ignoreCookies").build()).build();
    HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
    PoolingHttpClientConnectionManager poolingHttpClientConnectionManager = new PoolingHttpClientConnectionManager();
    poolingHttpClientConnectionManager.setDefaultMaxPerRoute(50);
    poolingHttpClientConnectionManager.setMaxTotal(100);
    factory.setHttpClient((HttpClient)httpClient);
    return new RestTemplate((ClientHttpRequestFactory)factory);
  }
  
  private String getString(String url, CloseableHttpResponse execute) throws IOException {
    try (CloseableHttpResponse response = execute) {
      int httpStatus = response.getStatusLine().getStatusCode();
      String responseBody = EntityUtils.toString(response.getEntity());
      log.debug("Http is success. : url({}), httpStatus({}, {}), responseBody({})", new Object[] { url, response.getStatusLine().getReasonPhrase(), Integer.valueOf(httpStatus), responseBody });
      response.close();
      return responseBody;
    } catch (IOException e) {
      log.error("Http get is failed. : url({})", url);
      throw e;
    } 
  }
}
