package com.samsung.magicinfo.webauthor2.xml.datalink;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.XmlValue;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "FileInfoType", propOrder = {"fileSize", "value"})
public class FileInfoType {
  @XmlAttribute(name = "filesize")
  private long fileSize;
  
  @XmlValue
  private String value;
  
  public long getFileSize() {
    return this.fileSize;
  }
  
  public void setFileSize(long fileSize) {
    this.fileSize = fileSize;
  }
  
  public String getValue() {
    return this.value;
  }
  
  public void setValue(String value) {
    this.value = value;
  }
}
