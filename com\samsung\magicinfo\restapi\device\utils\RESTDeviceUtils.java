package com.samsung.magicinfo.restapi.device.utils;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.DBCacheUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.RoleUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.common.manager.EncryptionManager;
import com.samsung.magicinfo.framework.common.manager.EncryptionManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManagerImpl;
import com.samsung.magicinfo.framework.device.job.manager.JobManager;
import com.samsung.magicinfo.framework.device.job.manager.JobManagerImpl;
import com.samsung.magicinfo.framework.device.log.entity.ServerLogEntity;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManager;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManagerImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfo;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfoImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManager;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManagerImpl;
import com.samsung.magicinfo.framework.device.preconfig.manager.DevicePreconfigInfo;
import com.samsung.magicinfo.framework.device.preconfig.manager.DevicePreconfigInfoImpl;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.monitoring.entity.ScheduleInfoEntity;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.entity.TagEntity;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.setup.manager.TagInfo;
import com.samsung.magicinfo.framework.setup.manager.TagInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.interfaces.WSCall;
import com.samsung.magicinfo.protocol.scheduler.ScheduleManager;
import com.samsung.magicinfo.protocol.util.ExpirationDeviceJob;
import com.samsung.magicinfo.protocol.util.dao.ServerSetupDao;
import com.samsung.magicinfo.restapi.common.model.V2CommonGroupIds;
import com.samsung.magicinfo.restapi.setting.model.V2TagResource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.apache.ftpserver.db.DownloadInfo;
import org.apache.ftpserver.db.DownloadInfoImpl;
import org.apache.ftpserver.ftplet.UserManager;
import org.apache.ftpserver.usermanager.DbUserManagerFactory;
import org.apache.ftpserver.usermanager.impl.BaseUser;
import org.apache.logging.log4j.Logger;
import org.quartz.JobDetail;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SimpleTrigger;

public class RESTDeviceUtils {
   static Logger logger = LoggingManagerV2.getLogger(DeviceUtils.class);
   static MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
   public static final String[] INPUTSOURCE_NAME = new String[]{"PC", "BNC", "DVI", "AV", "S-Video", "Component", "MagicInfo", "MagicInfo-Lite/S", "Plug In Module", "HDMI1", "HDMI2", "HDMI3", "HDMI4", "HDMI1_PC", "HDMI2_PC", "HDMI3_PC", "HDMI4_PC", "Display_Port", "Display_Port2", "ATV", "DTV", "DVI_VIDEO", "AV2", "Ext", "HDBaseT", "WiDi", "WebBrowser", "URL Launcher", "KIOSK"};
   public static final String[] INPUTSOURCE_CODE = new String[]{"20", "30", "24", "12", "4", "8", "32", "96", "80", "33", "35", "49", "51", "34", "36", "50", "52", "37", "38", "48", "64", "31", "13", "14", "85", "97", "101", "99", "103"};

   public RESTDeviceUtils() {
      super();
   }

   public static List getInputSourceList(String[] codeArr, String[] textArr) {
      List inputSourceList = new ArrayList();

      for(int i = 0; i < codeArr.length; ++i) {
         HashMap inputSource = new HashMap();
         inputSource.put("code", codeArr[i]);
         inputSource.put("text", textArr[i]);
         inputSourceList.add(inputSource);
      }

      return inputSourceList;
   }

   public static List getInputSourceList() {
      return getInputSourceList(INPUTSOURCE_CODE, INPUTSOURCE_NAME);
   }

   public static String approveDevice(Long groupId, String deviceName, String location, String selDeviceId, Locale locale, String sessionId, String userId, String calDate, String organization, String ipAddress, boolean flag) throws Exception {
      String deviceId = selDeviceId;
      String message = null;
      DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
      SlmLicenseManager licenseMgr = SlmLicenseManagerImpl.getInstance();
      new ServerLogEntity();
      String deviceModelName = null;
      if (locale == null || locale.toString().equals("")) {
         locale = Locale.US;
      }

      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      groupDao.getOrgIdByGroupId(groupId);
      String eventType = "TEXT_TITLE_NON_APPROVAL_DEVICE_APPROVAL_P";
      String strMenu = "MENU";
      String strMenuName = "Unapproved";
      int cntRemainLicenseByDeviceType = false;
      int nowCntApproval = 0;
      String deviceType = null;
      Float deviceTypeVersion = 1.0F;
      List approvedList = new ArrayList();
      int nowCntApproval = nowCntApproval + 1;
      Device tmpDevice = deviceInfo.getDevice(selDeviceId);
      deviceType = tmpDevice.getDevice_type();
      deviceTypeVersion = tmpDevice.getDevice_type_version();
      User loginUser = SecurityUtils.getUserContainer().getUser();
      int cntRemainLicenseByDeviceType;
      if (RoleUtils.isServerAdminRole(loginUser)) {
         cntRemainLicenseByDeviceType = licenseMgr.getRemainLicenseCountByDeviceType(deviceType);
      } else {
         cntRemainLicenseByDeviceType = licenseMgr.getRemainLicenseCountByDeviceTypeWithOrg(deviceType, organization);
      }

      logger.error("[MagicInfo_DeviceUtils][APPROVAL] devices approval modelName:" + deviceModelName + ", deviceType:" + deviceType + ", cntRemainLicenseByDeviceType:" + cntRemainLicenseByDeviceType + ", nowCount : " + nowCntApproval);
      if (licenseMgr.hasMigrationLicense() >= 0L) {
         message = "device_approval_fail_by_migration_license";
      } else if (cntRemainLicenseByDeviceType < nowCntApproval) {
         message = "device_approval_max_connection_over";
      } else if (selDeviceId != null && !selDeviceId.equals("")) {
         Device device = deviceInfo.getDevice(selDeviceId);
         if (calDate.equals("∞")) {
            calDate = "";
         }

         Map param = new HashMap();
         param.put("device_id", selDeviceId);
         param.put("device_name", deviceName);
         param.put("device_type", device.getDevice_type());
         param.put("group_id", groupId);
         param.put("current_group_id", 999999);
         param.put("device_model_name", deviceModelName);
         param.put("location", location);
         param.put("is_approved", true);
         param.put("calDate", calDate);
         param.put("organization", organization);
         if (flag) {
            int seq = deviceInfo.setApprovalWithSeq(param);
            deviceName = deviceName + "_" + seq;
            if (seq > -1) {
               approvedList.add(selDeviceId);
            }
         }

         boolean result = deviceInfo.setDeviceForApproval(param);
         if (!result) {
            message = "device_approval_fail";
         } else {
            approvedList.add(selDeviceId);
            DeviceGeneralConf deviceTemp = deviceInfo.getDeviceGeneralConf(selDeviceId, true);
            DBCacheUtils.setDeviceGeneralConf(deviceTemp, selDeviceId);
            DownloadInfo downloadDao = DownloadInfoImpl.getInstance();
            logger.error("[MagicInfo_DeviceUtils][APPROVAL][START FTP REGISTERING]DeviceType is : " + deviceType);
            ServerSetupDao serverSetupDao = new ServerSetupDao();
            String encryPass = serverSetupDao.getDefaultPassword();
            EncryptionManager encMgr = EncryptionManagerImpl.getInstance();
            String decryptPass = encMgr.getDecryptionPassword("", encryPass);
            if (downloadDao.getUserByName(selDeviceId) == null) {
               BaseUser user = new BaseUser();
               user.setName(selDeviceId);
               user.setPassword(decryptPass);
               UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
               userMgr.save(user);
               logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING]DeviceID : " + selDeviceId);
               logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING]realPassword : MagicInfo");
            }

            logger.error("[MagicInfo_DeviceUtils][APPROVAL][END FTP REGISTERING]=====================================");
            motMgr.connectionReload(selDeviceId, 1);
            motMgr.scheduleReload(selDeviceId, 1);
            ScheduleInfoEntity schEntity = motMgr.getScheduleStatus(selDeviceId);
            if (!StrUtils.nvl(CommonConfig.get("saas.eu.enable")).equalsIgnoreCase("TRUE") && schEntity != null) {
               try {
                  ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
                  schInfo.deploySchedule(deviceId, schEntity.getScheduleId(), schEntity.getScheduleVersion());
               } catch (Exception var45) {
                  logger.fatal("[MagicInfo_DeviceUtils][APPROVAL] Error of mapping content schedule while approving device " + selDeviceId);
               }

               try {
                  MessageInfo msgInfo = MessageInfoImpl.getInstance();
                  msgInfo.deployMessage(deviceId, schEntity.getMessageId(), schEntity.getMessageVersion());
               } catch (Exception var44) {
                  logger.fatal("[MagicInfo_DeviceUtils][APPROVAL] Error of mapping message mschedule while approving device " + selDeviceId);
               }
            }

            JobManager jobMgr = JobManagerImpl.getInstance();
            jobMgr.deployJobSchedule("", device);
            WSCall.setPlayerRequest(selDeviceId, "device approval");
            if (motMgr.isConnected(selDeviceId)) {
               DeviceGeneralConf info = new DeviceGeneralConf();
               info.setDevice_id(selDeviceId);
               info.setDevice_name(deviceName);
               DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
               confManager.reqSetGeneralToDevice(info, sessionId);
            }

            makeExpirationJob(selDeviceId, calDate);
            message = "device_approval_success";

            try {
               Float deviceTypeVerseion = device.getDevice_type_version();
               boolean isSupportStatistics = deviceType.equalsIgnoreCase("iPLAYER") || deviceType.equalsIgnoreCase("SPLAYER") && deviceTypeVerseion >= CommonDataConstants.ALL_DEVICE_TYPE_VERSION_ARRAY[2];
               if (isSupportStatistics && !deviceInfo.isRequestTimeExist(deviceId) && deviceInfo.addStatRequestTimeInsertCurrent(deviceId)) {
                  logger.info("[MagicInfo_DeviceUtils][APPROVAL] Statistics Requesttiem is Inserted deivce: " + deviceId);
               }
            } catch (Exception var46) {
               logger.error("", var46);
            }
         }

         try {
            DevicePreconfigInfo preconfigInfo = DevicePreconfigInfoImpl.getInstance();
            preconfigInfo.deployToDevice(device);
         } catch (Exception var43) {
            logger.error("Failed to send preconfig. " + device.getDevice_id(), var43);
         }

         if (message != null && message.equalsIgnoreCase("device_approval_success") && approvedList != null && approvedList.size() > 0 && isSupportNOC()) {
            DeviceNocInfo nocDao = DeviceNocInfoImpl.getInstance();
            boolean nocGroup = nocDao.isNocSupportGroup(groupId);
            if (nocGroup) {
               DeviceNocManager nocService = DeviceNocManagerImpl.getInstance();
               nocService.thingworxCreateDevice(approvedList);
            }
         }
      } else {
         message = "device_approval_fail";
      }

      return message;
   }

   public static void makeExpirationJob(String deviceId, String calDate) {
      Scheduler scheduler = ScheduleManager.getSchedulerInstance();
      String schedulerJobName = "ExpirationDevice_" + deviceId;
      String schedulerJobGroup = "ExpirationDevice";

      try {
         CommonUtils.deleteJob(schedulerJobName, schedulerJobGroup);
         if (calDate != null && !calDate.equals("")) {
            JobDetail jobdetail = CommonUtils.getJobDetail(schedulerJobName, schedulerJobGroup, ExpirationDeviceJob.class);
            SimpleTrigger trigger = CommonUtils.getSimpleTrigger(schedulerJobName, schedulerJobGroup, DateUtils.string2Timestamp(calDate, "yyyy-MM-dd"), (Date)null, 0, 1L);
            scheduler.scheduleJob(jobdetail, trigger);
         }
      } catch (SchedulerException var7) {
         logger.error(var7);
      }

   }

   public static Boolean isSupportNOC() {
      try {
         if (CommonConfig.get("thingworx.update.ui") != null && CommonConfig.get("thingworx.update.enable") != null && Boolean.parseBoolean(CommonConfig.get("thingworx.update.ui")) && Boolean.parseBoolean(CommonConfig.get("thingworx.update.enable"))) {
            return true;
         }
      } catch (ConfigException var1) {
         return false;
      }

      return false;
   }

   public static List getDeviceTagsByGroupIds(V2CommonGroupIds ids) throws SQLException {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceSystemSetupConfManager SystemSetupDao = DeviceSystemSetupConfManagerImpl.getInstance("PREMIUM");
      TagInfo tagDao = TagInfoImpl.getInstance();
      List resources = new ArrayList();
      Iterator var5 = ids.getIds().iterator();

      while(true) {
         List deviceList;
         do {
            if (!var5.hasNext()) {
               return resources;
            }

            long groupId = (Long)var5.next();
            deviceList = deviceDao.getDeviceAndGroupInfoByGroupId(groupId);
         } while(deviceList == null);

         for(int j = 0; j < deviceList.size(); ++j) {
            String deviceId = ((Map)deviceList.get(j)).get("device_id").toString();
            List tmp = SystemSetupDao.getDeviceTag(deviceId);
            if (tmp != null && tmp.size() > 0) {
               int tagId = Integer.parseInt(((Map)tmp.get(0)).get("tag_id").toString());
               TagEntity tagEntity = tagDao.getTag(tagId);
               V2TagResource resource = V2TagResource.convert(tagEntity, (List)null);
               resources.add(resource);
            }
         }
      }
   }
}
