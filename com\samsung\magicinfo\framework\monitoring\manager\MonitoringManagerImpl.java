package com.samsung.magicinfo.framework.monitoring.manager;

import com.samsung.common.cache.CacheFactory;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.MemcacheFailoverUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceConnHistoryInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceConnHistoryInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.monitoring.entity.ClientFaultEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ConnectionInfoEntity;
import com.samsung.magicinfo.framework.monitoring.entity.CurrentPlayingEntity;
import com.samsung.magicinfo.framework.monitoring.entity.DashboardEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ScheduleInfoEntity;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import edu.emory.mathcs.backport.java.util.Collections;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import net.spy.memcached.CASResponse;
import net.spy.memcached.CASValue;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class MonitoringManagerImpl implements MonitoringManager {
   static Logger logger = LoggingManagerV2.getLogger(MonitoringManagerImpl.class);
   private static final String CONNECTIONINFO_REPOSITORY_LOOKUP_ID = "CONNECTIONINFO_MEMORY_MAP";
   private static final String CURRENTPLAYING_REPOSITORY_LOOKUP_ID = "CURRENTPLAYING_MEMORY_MAP";
   private static final String SCHEDULEINFO_REPOSITORY_LOOKUP_ID = "SCHEDULEINFO_MEMORY_MAP";
   private static final String CLIENTFAULT_REPOSITORY_LOOKUP_ID = "CLIENTFAULT_MEMORY_MAP";
   private static final String STAT_TIMER_REPOSITORY_LOOKUP_ID = "STAT_TIMER_REPOSITORY_LOOK_ID";
   ArrayList contentUploadList = new ArrayList();
   private static volatile MonitoringManager instance = null;
   private static boolean checkLastConnectionTime;
   private static long lastconnectionTime;

   private MonitoringManagerImpl() {
      super();
      checkLastConnectionTime = false;
      lastconnectionTime = 0L;

      try {
         checkLastConnectionTime = Boolean.parseBoolean(StrUtils.nvl(CommonConfig.get("keepalive.lastconnection.enable")));
         lastconnectionTime = Long.parseLong(StrUtils.nvl(CommonConfig.get("keepalive.lastconnection.checktime")));
      } catch (Exception var2) {
         checkLastConnectionTime = false;
      }

      logger.error("[MagicInfo_Keepalive] Keepalive pass logic init");
      logger.error("[MagicInfo_Keepalive] Keepalive enable " + checkLastConnectionTime);
      logger.error("[MagicInfo_Keepalive] Keepalive set time " + lastconnectionTime);
   }

   public static MonitoringManager getInstance() {
      if (instance == null) {
         Class var0 = MonitoringManagerImpl.class;
         synchronized(MonitoringManagerImpl.class) {
            if (instance == null) {
               instance = new MonitoringManagerImpl();
            }
         }
      }

      return instance;
   }

   public void init() {
      String popMode = "";
      Map statTimerRepository = new HashMap();
      Object obj = null;

      try {
         popMode = StrUtils.nvl(CommonConfig.get("pop.server_mode"));
         if (popMode.equalsIgnoreCase("DB")) {
            CacheFactory.getCache().set("STAT_TIMER_REPOSITORY_LOOK_ID", statTimerRepository);
         } else {
            obj = CacheFactory.getCache().get("STAT_TIMER_REPOSITORY_LOOK_ID");
            if (obj == null) {
               CacheFactory.getCache().set("STAT_TIMER_REPOSITORY_LOOK_ID", statTimerRepository);
            }
         }

         logger.info("### created STAT_TIMER_REPOSITORY_LOOKUP_ID ###");
      } catch (Exception var5) {
         logger.error(var5);
      }

   }

   public void setUploadContentId(String contentId) {
      this.contentUploadList.add(contentId);
   }

   public String getUploadContentId() {
      int num = this.contentUploadList.size();
      String str = "";

      for(int cnt = 0; cnt < num; ++cnt) {
         str = str + (String)this.contentUploadList.get(cnt);
         if (cnt < num - 1) {
            str = str + ',';
         }
      }

      return str;
   }

   public boolean getContentUploadStatus(String contentId) {
      int num = this.contentUploadList.size();

      for(int cnt = 0; cnt < num; ++cnt) {
         if (contentId.equalsIgnoreCase((String)this.contentUploadList.get(cnt))) {
            return true;
         }
      }

      return false;
   }

   public void removeUploadContentId() {
      this.contentUploadList.clear();
   }

   public void printConnectionMap() {
      MonitoringManagerInfo dao = MonitoringManagerInfoImpl.getInstance("PREMIUM");

      try {
         List connectionInfo = dao.getConnectionInfo();

         for(int i = 0; i < connectionInfo.size(); ++i) {
            Map connectionInfoMap = (Map)connectionInfo.get(i);
            String deviceId = (String)connectionInfoMap.get("device_id");
            ConnectionInfoEntity conn = (ConnectionInfoEntity)CacheFactory.getCache().get("CONNECTIONINFO_MEMORY_MAP" + deviceId);
            if (conn == null) {
               logger.error("[MagicInfo_Keepalive] connectionMap None in memoryMap. deviceId=" + deviceId);
            } else {
               logger.error("[MagicInfo_Keepalive][" + deviceId + "] connectionMap Exist in memoryMap. monitoring interval : " + conn.getMonitoringInterval());
            }
         }
      } catch (Exception var7) {
         logger.error("", var7);
      }

   }

   public void connectionReload(String deviceId, int flag) {
      logger.warn("[MagicInfo_Keepalive][" + deviceId + "][START] Device Connection reload flag : " + flag);
      if (1 == flag) {
         MonitoringManagerInfo dao = MonitoringManagerInfoImpl.getInstance("PREMIUM");
         DeviceGroupInfo dGroupInfo = DeviceGroupInfoImpl.getInstance();

         try {
            CASResponse casResponse = null;
            int retryCnt = 0;
            boolean isInit = false;
            Long monInterval = 0L;

            do {
               CASValue casObj = CacheFactory.getCache().gets("CONNECTIONINFO_MEMORY_MAP" + deviceId, (Object)null);
               boolean powerOnBefore = false;
               if (casObj != null) {
                  ConnectionInfoEntity oldConnInfo = (ConnectionInfoEntity)casObj.getValue();
                  if (oldConnInfo != null) {
                     boolean isConn = System.currentTimeMillis() - oldConnInfo.getLastConnectionTime() - 60000L < oldConnInfo.getMonitoringInterval() * 60000L;
                     powerOnBefore = isConn;
                     monInterval = oldConnInfo.getMonitoringInterval();
                     logger.error("[MagicInfo_Keepalive][" + deviceId + "] oldConnInfo oldConnInfo monInterval =" + monInterval);
                  }
               } else {
                  isInit = true;
               }

               logger.error("[MagicInfo_Keepalive][" + deviceId + "] Reload Connection powerOnBefore=" + powerOnBefore);
               List connList = dao.getConnectionInfo(deviceId);
               if (connList == null) {
                  logger.error("[MagicInfo_Keepalive][" + deviceId + "] Not found connection DB");
                  return;
               }

               Map conn = (Map)connList.get(0);
               if (monInterval == 0L) {
                  monInterval = (Long)conn.get("monitoring_interval");
                  logger.info("[MagicInfo_Keepalive][connectionReload - DB value] DB value - monInterval: " + monInterval);
               }

               Timestamp connTime = (Timestamp)conn.get("last_connection_time");
               Long groupId = (Long)conn.get("group_id");
               Long devOrgId = dGroupInfo.getOrgIdByGroupId(groupId);
               if (connTime == null) {
                  connTime = new Timestamp(0L);
               }

               ConnectionInfoEntity cEntity = new ConnectionInfoEntity(monInterval, connTime.getTime(), groupId, devOrgId, powerOnBefore, new Timestamp(1L));
               if (isInit) {
                  CacheFactory.getCache().set("CONNECTIONINFO_MEMORY_MAP" + deviceId, cEntity);
                  casResponse = CASResponse.OK;
               } else {
                  casResponse = CacheFactory.getCache().cas("CONNECTIONINFO_MEMORY_MAP" + deviceId, casObj.getCas(), cEntity);
               }
            } while(!MemcacheFailoverUtils.isOKCASResponse(casResponse, retryCnt++));
         } catch (Exception var17) {
            logger.error("", var17);
         }
      }

      logger.warn("[MagicInfo_Keepalive][" + deviceId + "][END] Device Connection reload");
   }

   public boolean getApprovalStatus(String deviceId) {
      boolean nonApproval = false;

      try {
         if (deviceId != null && deviceId.contains("_")) {
            deviceId = deviceId.split("_")[0];
         }

         Object nonApprovalObj = CacheFactory.getCache().get("CONNECTIONINFO_MEMORY_MAP" + deviceId);
         if (nonApprovalObj != null) {
            nonApproval = true;
         } else {
            DeviceInfo devMgr = DeviceInfoImpl.getInstance();
            String strApp = devMgr.getApprovalByDeviceId(deviceId);
            if (strApp != null && strApp.equals("true")) {
               nonApproval = true;
            }
         }
      } catch (Exception var6) {
         logger.error("", var6);
      }

      return nonApproval;
   }

   public String getTimeOfLastConnectionEntry() throws SQLException {
      MonitoringManagerInfo dao = MonitoringManagerInfoImpl.getInstance();
      return dao.getTimeOfLastConnectionEntry();
   }

   public void dashboardValuesReloadFromCache() {
   }

   public DashboardEntity getDashboardStatus() {
      return this.getDashboardStatus("ROOT", (String)null, (Float)null);
   }

   public DashboardEntity getDashboardStatus(String organization, String deviceType, Float deviceTypeVersion) {
      long connCnt = 0L;
      long disconnCnt = 0L;
      Integer nonApprovalCnt = null;
      long faultCnt = 0L;
      long alarmCnt = 0L;
      MonitoringManagerInfo monitoringManagerInfo = MonitoringManagerInfoImpl.getInstance("PREMIUM");

      try {
         List organizations = Collections.singletonList(organization);
         User user;
         if (null != SecurityUtils.getUserContainer()) {
            user = SecurityUtils.getUserContainer().getUser();
            ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
            Map settingsMap = serverSetupDao.getServerInfoByOrgId(user.getRoot_group_id());
            boolean isDevicePermission = (Boolean)settingsMap.get("device_permissions");
            if (isDevicePermission && DeviceUtils.isDeviceGroupAuth(user)) {
               connCnt = (long)monitoringManagerInfo.getConnectionFromDBInfoGroupPermission(user.getUser_id(), deviceType, deviceTypeVersion);
               disconnCnt = (long)monitoringManagerInfo.getDeviceCountGroupPermission(user.getUser_id(), deviceType) - connCnt;
            } else {
               connCnt = (long)monitoringManagerInfo.getConnectionFromDBInfo(organizations, deviceType, deviceTypeVersion);
               disconnCnt = (long)monitoringManagerInfo.getDeviceCount(organization, deviceType) - connCnt;
            }
         } else {
            connCnt = (long)monitoringManagerInfo.getConnectionFromDBInfo(organizations, deviceType, deviceTypeVersion);
            disconnCnt = (long)monitoringManagerInfo.getDeviceCount(organization, deviceType) - connCnt;
         }

         if (SecurityUtils.checkDeviceApprovalPermission()) {
            user = SecurityUtils.getUserContainer().getUser();
            nonApprovalCnt = DeviceUtils.getUnapprovedDeviceCountByUser(user);
         }

         faultCnt = monitoringManagerInfo.getFaultStatusCount(organization);
         alarmCnt = monitoringManagerInfo.getAlarmStatusCount(organization);
      } catch (Exception var19) {
         logger.error("", var19);
      }

      DashboardEntity entity = new DashboardEntity(connCnt, disconnCnt, nonApprovalCnt, faultCnt, alarmCnt);
      return entity;
   }

   public CurrentPlayingEntity getPlayingContent(String deviceId) {
      try {
         CurrentPlayingEntity cPlayingEntity = (CurrentPlayingEntity)CacheFactory.getCache().get("CURRENTPLAYING_MEMORY_MAP" + deviceId);
         return cPlayingEntity;
      } catch (Exception var3) {
         logger.error("", var3);
         return null;
      }
   }

   public ScheduleInfoEntity getScheduleStatus(String deviceId) {
      ScheduleInfoEntity rt = null;

      try {
         rt = (ScheduleInfoEntity)CacheFactory.getCache().get("SCHEDULEINFO_MEMORY_MAP" + deviceId);
         if (rt == null) {
            this.scheduleReload(deviceId, 1);
            rt = (ScheduleInfoEntity)CacheFactory.getCache().get("SCHEDULEINFO_MEMORY_MAP" + deviceId);
         }
      } catch (Exception var4) {
         logger.error("", var4);
      }

      return rt;
   }

   public boolean isConnected(String deviceId) {
      try {
         CurrentPlayingEntity playingContent = this.getPlayingContent(deviceId);
         if (playingContent != null && playingContent.getInputSource() == 1000) {
            return false;
         }

         ConnectionInfoEntity connInfo = (ConnectionInfoEntity)CacheFactory.getCache().get("CONNECTIONINFO_MEMORY_MAP" + deviceId);
         if (connInfo != null && System.currentTimeMillis() - connInfo.getLastConnectionTime() - 60000L < connInfo.getMonitoringInterval() * 60000L) {
            return true;
         }
      } catch (Exception var4) {
         logger.error("", var4);
      }

      return false;
   }

   public int getChangeInputSorceStatus(String deviceId) {
      return this.getChangeInputSorceStatus(deviceId, 0);
   }

   private int getChangeInputSorceStatus(String deviceId, int retryCnt) {
      try {
         CASValue casObj = CacheFactory.getCache().gets("CURRENTPLAYING_MEMORY_MAP" + deviceId, (Object)null);
         if (casObj == null) {
            return -1;
         }

         CurrentPlayingEntity cPlayingEntity = (CurrentPlayingEntity)casObj.getValue();
         CASResponse casResponse = null;
         if (cPlayingEntity != null) {
            logger.info("[MagicInfo_Keepalive] getChangeInputSrcStatus : " + cPlayingEntity.getInputSourceBefore() + " => " + cPlayingEntity.getInputSource() + ". deviceId=" + deviceId);
            if (cPlayingEntity.getInputSource() != -1 && cPlayingEntity.getInputSource() != cPlayingEntity.getInputSourceBefore()) {
               cPlayingEntity.setInputSourceBefore(cPlayingEntity.getInputSource());
               casResponse = CacheFactory.getCache().cas("CURRENTPLAYING_MEMORY_MAP" + deviceId, casObj.getCas(), cPlayingEntity);
               if (!MemcacheFailoverUtils.isOKCASResponse(casResponse, retryCnt)) {
                  return this.getChangeInputSorceStatus(deviceId, retryCnt + 1);
               }

               return cPlayingEntity.getInputSource();
            }

            return -1;
         }
      } catch (Exception var6) {
         logger.error("", var6);
      }

      return -1;
   }

   public int getChangeConnStatus(String deviceId) {
      return this.getChangeConnStatus(deviceId, 0);
   }

   private int getChangeConnStatus(String deviceId, int retryCnt) {
      try {
         CASValue casObj = CacheFactory.getCache().gets("CONNECTIONINFO_MEMORY_MAP" + deviceId, (Object)null);
         if (casObj == null) {
            return -1;
         }

         ConnectionInfoEntity connInfo = (ConnectionInfoEntity)casObj.getValue();
         if (connInfo != null) {
            boolean isConn = System.currentTimeMillis() - connInfo.getLastConnectionTime() - 60000L < connInfo.getMonitoringInterval() * 60000L;
            logger.info("[MagicInfo_Keepalive] REDUNDANCY getChangeConnStatus : " + connInfo.getPowerOnBefore() + " => " + isConn + ". deviceId=" + deviceId);
            if (isConn != connInfo.getPowerOnBefore()) {
               CASResponse casResponse = null;
               connInfo.setPowerOnBefore(isConn);
               casResponse = CacheFactory.getCache().cas("CONNECTIONINFO_MEMORY_MAP" + deviceId, casObj.getCas(), connInfo);
               if (!MemcacheFailoverUtils.isOKCASResponse(casResponse, retryCnt)) {
                  return this.getChangeConnStatus(deviceId, retryCnt + 1);
               }

               if (isConn) {
                  return 1;
               }

               return 0;
            }

            if (!isConn) {
               return -2;
            }

            return -1;
         }
      } catch (Exception var7) {
         logger.error("", var7);
      }

      return -1;
   }

   public void scheduleReload(String deviceId, int flag) {
      this.scheduleReload(deviceId, flag, (SqlSession)null);
   }

   public void scheduleReload(String deviceId, int flag, SqlSession session) {
      logger.warn("[MagicInfo_Keepalive][" + deviceId + "][START] Schedule reload flag : " + flag);
      if (1 == flag) {
         try {
            MonitoringManagerInfo dao = MonitoringManagerInfoImpl.getInstance("PREMIUM", session);
            DeviceInfo deviceDao = DeviceInfoImpl.getInstance(session);
            ScheduleInfoEntity sInfo = new ScheduleInfoEntity();
            int groupId = Integer.valueOf(deviceDao.getDeviceGroupIdByDeviceId(deviceId));
            List programInfo = dao.getProgramIdbyDeviceGroup(groupId);
            if (programInfo != null && programInfo.size() != 0) {
               for(int i = 0; i < programInfo.size(); ++i) {
                  Map programInfoMap = (Map)programInfo.get(i);
                  sInfo.setScheduleId((String)programInfoMap.get("program_id"));
                  sInfo.setScheduleVersion((Long)programInfoMap.get("version"));
               }
            } else {
               sInfo.setScheduleId("");
               sInfo.setScheduleVersion(-1L);
               logger.error("[MagicInfo_ReloadSchedule][" + deviceId + "] SCH MEMORY Add-empty, programId=, version=-1");
            }

            List messageInfo = dao.getMessageIdbyDeviceGroup(groupId);
            if (messageInfo != null && messageInfo.size() != 0) {
               for(int i = 0; i < messageInfo.size(); ++i) {
                  Map messageInfoMap = (Map)messageInfo.get(i);
                  sInfo.setMessageId((String)messageInfoMap.get("message_id"));
                  sInfo.setMessageVersion((Long)messageInfoMap.get("version"));
               }
            } else {
               sInfo.setMessageId("00000000-0000-0000-0000-000000000000");
               sInfo.setMessageVersion(-1L);
            }

            List jobInfo = dao.getJobIdbyDeviceGroup(deviceId, groupId);
            if (jobInfo != null && jobInfo.size() != 0) {
               for(int i = 0; i < jobInfo.size(); ++i) {
                  Map jobInfoMap = (Map)jobInfo.get(i);
                  sInfo.setLastModifyDate(((Timestamp)jobInfoMap.get("last_modify_date")).getTime());
               }
            } else {
               sInfo.setLastModifyDate(0L);
            }

            EventScheduleInfo esInfo = EventScheduleInfoImpl.getInstance(session);
            String esId = esInfo.getEventScheduleIdByDeviceId(deviceId);
            long esVersion = esInfo.getVersionByEventScheduleId(esId);
            if (StringUtils.isEmpty(esId)) {
               sInfo.setEventId("00000000-0000-0000-0000-000000000000");
               sInfo.setEventVersion(-1L);
            } else {
               sInfo.setEventId(esId);
               sInfo.setEventVersion(esVersion);
            }

            CacheFactory.getCache().set("SCHEDULEINFO_MEMORY_MAP" + deviceId, sInfo);
         } catch (Exception var16) {
            logger.error("", var16);
         }
      } else {
         try {
            if (CacheFactory.getCache().get("SCHEDULEINFO_MEMORY_MAP" + deviceId) != null) {
               CacheFactory.getCache().delete("SCHEDULEINFO_MEMORY_MAP" + deviceId);
               logger.error("[MagicInfo_Keepalive][" + deviceId + "] SCH MEMORY delete");
            }
         } catch (Exception var15) {
            logger.error("", var15);
         }
      }

      logger.warn("[MagicInfo_Keepalive][" + deviceId + "][END] Schedule reload");
   }

   public boolean setDisconnected(String deviceId) throws SQLException {
      return this.setDisconnected(deviceId, (String)null, (Date)null);
   }

   public boolean setDisconnected(String deviceId, String shutdownReason, Date shutdownTime) throws SQLException {
      try {
         CASResponse casResponse = null;
         int var5 = 0;

         do {
            CASValue casObj = CacheFactory.getCache().gets("CONNECTIONINFO_MEMORY_MAP" + deviceId, (Object)null);
            if (casObj == null) {
               return false;
            }

            ConnectionInfoEntity connInfo = (ConnectionInfoEntity)casObj.getValue();
            if (connInfo == null) {
               return false;
            }

            connInfo.setLastConnectionTime(0L);
            casResponse = CacheFactory.getCache().cas("CONNECTIONINFO_MEMORY_MAP" + deviceId, casObj.getCas(), connInfo);
            logger.error("[MagicInfo_Keepalive] setDisconnected - addDisconnectedTime deviceId :" + deviceId);
         } while(!MemcacheFailoverUtils.isOKCASResponse(casResponse, var5++));

         long currTime = System.currentTimeMillis();
         DeviceConnHistoryInfo deviceConnHistoryInfo = DeviceConnHistoryInfoImpl.getInstance();
         Timestamp shutdownTimestamp = shutdownTime == null ? null : new Timestamp(shutdownTime.getTime());
         deviceConnHistoryInfo.addDisconnectedTime(deviceId, new Timestamp(currTime), shutdownReason, shutdownTimestamp, false);
         DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
         return deviceMgr.setShutDownConnectionTime(deviceId);
      } catch (Exception var11) {
         logger.error("", var11);
         return false;
      }
   }

   public boolean setPlayingContent(String deviceId, CurrentPlayingEntity playingEntity) {
      try {
         CacheFactory.getCache().set("CURRENTPLAYING_MEMORY_MAP" + deviceId, playingEntity);
         return true;
      } catch (Exception var4) {
         logger.error("", var4);
         return false;
      }
   }

   public boolean setPanelStatus(String deviceId, Long panelStatus) {
      try {
         CASResponse casResponse = null;
         int var4 = 0;

         do {
            CASValue casObj = CacheFactory.getCache().gets("CURRENTPLAYING_MEMORY_MAP" + deviceId, new CurrentPlayingEntity());
            CurrentPlayingEntity cPlayingEntity = (CurrentPlayingEntity)casObj.getValue();
            if (cPlayingEntity == null) {
               cPlayingEntity = new CurrentPlayingEntity();
            }

            cPlayingEntity.setPanelStatus(panelStatus);
            casResponse = CacheFactory.getCache().cas("CURRENTPLAYING_MEMORY_MAP" + deviceId, casObj.getCas(), cPlayingEntity);
         } while(!MemcacheFailoverUtils.isOKCASResponse(casResponse, var4++));
      } catch (Exception var7) {
         logger.error("", var7);
      }

      return false;
   }

   public boolean setConnectionInfo(String deviceId, long monitoringInterval) {
      if (this.getApprovalStatus(deviceId)) {
         try {
            int var6 = 0;

            CASResponse casResponse;
            do {
               CASValue casObj = CacheFactory.getCache().gets("CONNECTIONINFO_MEMORY_MAP" + deviceId, (Object)null);
               if (casObj == null) {
                  return false;
               }

               ConnectionInfoEntity connInfo = (ConnectionInfoEntity)casObj.getValue();
               if (connInfo == null) {
                  return false;
               }

               connInfo.setLastConnectionTime(System.currentTimeMillis());
               connInfo.setLast_abnormal_shutdown_time(new Timestamp(0L));
               connInfo.setMonitoringInterval(monitoringInterval);
               casResponse = CacheFactory.getCache().cas("CONNECTIONINFO_MEMORY_MAP" + deviceId, casObj.getCas(), connInfo);
            } while(!MemcacheFailoverUtils.isOKCASResponse(casResponse, var6++));

            return true;
         } catch (Exception var8) {
            logger.error("", var8);
         }
      }

      return false;
   }

   public boolean setConnectionNow(Device device) {
      return this.setConnectionNow(device, 0);
   }

   public boolean setConnectionNow(Device device, int retryCnt) {
      if (device == null) {
         return false;
      } else {
         String deviceId = device.getDevice_id();
         if (device.getIs_approved() || this.getApprovalStatus(deviceId)) {
            try {
               ConnectionInfoEntity connInfo = null;
               CASValue casObj = CacheFactory.getCache().gets("CONNECTIONINFO_MEMORY_MAP" + deviceId, (Object)null);
               DeviceGroupInfo dGroupInfo;
               long groupId;
               long devOrgId;
               if (casObj != null && (ConnectionInfoEntity)casObj.getValue() != null) {
                  connInfo = (ConnectionInfoEntity)casObj.getValue();
                  if (connInfo != null) {
                     connInfo.setLastConnectionTime(System.currentTimeMillis());
                     connInfo.setLast_abnormal_shutdown_time(new Timestamp(0L));
                  } else {
                     try {
                        dGroupInfo = DeviceGroupInfoImpl.getInstance();
                        groupId = dGroupInfo.getGroupByDeviceId(deviceId).getGroup_id();
                        devOrgId = dGroupInfo.getOrgIdByGroupId(groupId);
                        connInfo = new ConnectionInfoEntity(device.getMonitoring_interval(), System.currentTimeMillis(), groupId, devOrgId, false);
                     } catch (SQLException var11) {
                        logger.error(var11);
                     }
                  }
               } else {
                  try {
                     dGroupInfo = DeviceGroupInfoImpl.getInstance();
                     groupId = dGroupInfo.getGroupByDeviceId(deviceId).getGroup_id();
                     devOrgId = dGroupInfo.getOrgIdByGroupId(groupId);
                     connInfo = new ConnectionInfoEntity(device.getMonitoring_interval(), System.currentTimeMillis(), groupId, devOrgId, false);
                  } catch (SQLException var12) {
                     logger.error(var12);
                  }
               }

               if (casObj == null) {
                  CacheFactory.getCache().set("CONNECTIONINFO_MEMORY_MAP" + deviceId, connInfo);
               } else {
                  CASResponse casResponse = CacheFactory.getCache().cas("CONNECTIONINFO_MEMORY_MAP" + deviceId, casObj.getCas(), connInfo);
                  if (!MemcacheFailoverUtils.isOKCASResponse(casResponse, retryCnt)) {
                     return this.setConnectionNow(device, retryCnt + 1);
                  }
               }

               return true;
            } catch (Exception var13) {
               logger.error("", var13);
            }
         }

         return false;
      }
   }

   public void deleteConnectionInfo(String deviceId) {
      try {
         CASValue casObj = CacheFactory.getCache().gets("CONNECTIONINFO_MEMORY_MAP" + deviceId, (Object)null);
         if (casObj != null) {
            CacheFactory.getCache().delete("CONNECTIONINFO_MEMORY_MAP" + deviceId);
         }
      } catch (Exception var3) {
         logger.error("", var3);
      }

   }

   public boolean isRunningStatJob() {
      try {
         Object obj = CacheFactory.getCache().get("STAT_TIMER_REPOSITORY_LOOK_ID");
         HashMap statJobTimerRepository;
         if (obj == null) {
            statJobTimerRepository = new HashMap();
            CacheFactory.getCache().set("STAT_TIMER_REPOSITORY_LOOK_ID", statJobTimerRepository);
            logger.error("### Created  STAT_TIMER_REPOSITORY_LOOKUP_ID on isStartStatJob() ###");
            obj = CacheFactory.getCache().get("STAT_TIMER_REPOSITORY_LOOK_ID");
         }

         if (obj != null) {
            statJobTimerRepository = (HashMap)obj;
            Long startTime = (Long)statJobTimerRepository.get("stat_start_timer");
            Long endTime = (Long)statJobTimerRepository.get("stat_end_timer");
            if (startTime == null && endTime == null) {
               return false;
            } else if (startTime != null && endTime == null) {
               return true;
            } else if (startTime == null && endTime != null) {
               return false;
            } else {
               Long startEndTimeDiff = endTime - startTime;
               if (startEndTimeDiff >= 0L) {
                  return false;
               } else {
                  long currTime = System.currentTimeMillis();
                  if (currTime - startTime >= 3600000L) {
                     logger.error("[MagicInfo_Keepalive] (Over Max waiting time.(60min))");
                     return false;
                  } else {
                     logger.error("[MagicInfo_Keepalive] (StatJob Running now) : startTime=" + startTime + ",endTime=" + endTime);
                     return true;
                  }
               }
            }
         } else {
            logger.error("[MagicInfo_Keepalive] isRunningStatJob get(STAT_TIMER_REPOSITORY_LOOKUP_ID) is null.");
            return false;
         }
      } catch (Exception var8) {
         var8.printStackTrace();
         return false;
      }
   }

   public boolean setStatJobTimer(String keyString) {
      try {
         CASResponse casResponse = null;
         int var3 = 0;

         do {
            CASValue casObj = CacheFactory.getCache().gets("STAT_TIMER_REPOSITORY_LOOK_ID", new HashMap());
            Object obj = casObj.getValue();
            Map statJobTimerRepository = (HashMap)obj;
            long currTime = System.currentTimeMillis();
            statJobTimerRepository.put(keyString, currTime);
            casResponse = CacheFactory.getCache().cas("STAT_TIMER_REPOSITORY_LOOK_ID", casObj.getCas(), statJobTimerRepository);
         } while(!MemcacheFailoverUtils.isOKCASResponse(casResponse, var3++));

         return true;
      } catch (Exception var9) {
         logger.error("", var9);
         return false;
      }
   }

   public boolean setClientFaultStatus(String deviceId, String faultValue) throws Exception {
      Map map = new HashMap();
      String[] faultArr = null;
      boolean isExistKey = false;
      int listSize = 0;
      if (faultValue == null) {
         logger.error("[MagicInfo_Keepalive] setClientFaultStatus fault string is null\n");
         return false;
      } else {
         String code = null;
         ArrayList mapList = new ArrayList();
         ClientFaultEntity faultEntity = (ClientFaultEntity)CacheFactory.getCache().get("CLIENTFAULT_MEMORY_MAP" + deviceId);
         String currTime = DateUtils.getCurrentTime("yyyy-MM-dd HH:mm:ss");
         if (faultEntity == null) {
            faultEntity = new ClientFaultEntity();
         } else {
            mapList = faultEntity.getFaultMapList();
            if (mapList != null) {
               listSize = mapList.size();
            }
         }

         faultArr = faultValue.split(";");
         code = faultArr[1];

         for(int i = 0; i < listSize; ++i) {
            if (((Map)mapList.get(i)).containsKey(faultArr[0])) {
               if (code.equalsIgnoreCase("0")) {
                  mapList.remove(i);
                  break;
               }

               ((Map)mapList.get(i)).put(faultArr[0], faultValue + ";" + currTime);
               isExistKey = true;
            }
         }

         map.put(faultArr[0], faultValue + ";" + currTime);
         if (!isExistKey && !code.equalsIgnoreCase("0")) {
            mapList.add(map);
         }

         faultEntity.setFaultMapList(mapList);
         CacheFactory.getCache().set("CLIENTFAULT_MEMORY_MAP" + deviceId, faultEntity);
         logger.error("[MagicInfo_Keepalive][" + deviceId + "] setClientFaultStatus " + faultValue + ";" + currTime);
         return true;
      }
   }

   public ClientFaultEntity getClientFaultStatus(String deviceId) throws Exception {
      ClientFaultEntity faultEntity = new ClientFaultEntity();
      if (deviceId != null) {
         faultEntity = (ClientFaultEntity)CacheFactory.getCache().get("CLIENTFAULT_MEMORY_MAP" + deviceId);
      } else {
         logger.error("[MagicInfo_Keepalive] getClientFaultStatus device Id is null\n");
      }

      return faultEntity;
   }

   public boolean checkKeepAliveLastConnection(String deviceId) {
      boolean rtn = false;
      if (checkLastConnectionTime) {
         CurrentPlayingEntity connInfo = null;

         try {
            connInfo = (CurrentPlayingEntity)CacheFactory.getCache().get("CURRENTPLAYING_MEMORY_MAP" + deviceId);
            if (connInfo == null) {
               return true;
            }

            if (connInfo.getKeepaliveTime() != null) {
               long deviceLastConnectionTime = connInfo.getKeepaliveTime().getTime() + 1000L * lastconnectionTime;
               long currentTime = System.currentTimeMillis();
               if (currentTime > deviceLastConnectionTime) {
                  rtn = true;
               }
            }
         } catch (Exception var8) {
            rtn = true;
            logger.error("[MagicInfo_Keepalive] getConnection fail! deviceId : " + deviceId);
         }
      } else {
         rtn = true;
      }

      return rtn;
   }

   public List getDisconnectedDevicesByLastConnectionTime() {
      List disconnectedDevices = new ArrayList();
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      List disconnectedDeviceList = null;

      try {
         disconnectedDeviceList = deviceInfo.getDisconnectedDeviceIdList();
         if (disconnectedDeviceList != null && disconnectedDeviceList.size() > 0) {
            Iterator var4 = disconnectedDeviceList.iterator();

            while(var4.hasNext()) {
               String deviceId = (String)var4.next();
               ConnectionInfoEntity connInfo = null;

               try {
                  connInfo = (ConnectionInfoEntity)CacheFactory.getCache().get("CONNECTIONINFO_MEMORY_MAP" + deviceId);
               } catch (Exception var8) {
                  logger.error("[MagicInfo_Monitoring][" + deviceId + "] getDisconnectedDevicesByLastConnectionTime error");
               }

               if (connInfo != null && connInfo.getLastConnectionTime() != 0L) {
                  disconnectedDevices.add(deviceId);
               }
            }
         }
      } catch (SQLException var9) {
         logger.error("[MagicInfo_Monitoring] fail getDisconnectedList error : " + var9.getMessage());
      }

      return disconnectedDevices;
   }

   public void setRMToken(String deviceId, String token) {
      try {
         CASResponse casResponse = null;
         int var4 = 0;

         do {
            CASValue casObj = CacheFactory.getCache().gets("CURRENTPLAYING_MEMORY_MAP" + deviceId, new ScheduleInfoEntity());
            CurrentPlayingEntity sInfo = (CurrentPlayingEntity)casObj.getValue();
            sInfo.setRmServerToken(token);
            casResponse = CacheFactory.getCache().cas("CURRENTPLAYING_MEMORY_MAP" + deviceId, casObj.getCas(), sInfo);
         } while(!MemcacheFailoverUtils.isOKCASResponse(casResponse, var4++));
      } catch (Exception var7) {
         logger.error("", var7);
      }

   }

   public String getRMToken(String deviceId) {
      String token = null;
      CurrentPlayingEntity deviceInfo = null;

      try {
         deviceInfo = (CurrentPlayingEntity)CacheFactory.getCache().get("CURRENTPLAYING_MEMORY_MAP" + deviceId);
         if (deviceInfo != null) {
            token = deviceInfo.getRmServerToken();
         }
      } catch (Exception var5) {
         token = null;
      }

      return token;
   }

   public int addStatisticsDeviceConnect(Timestamp time, long connect, long error, long warning, long organization_id) throws SQLException {
      MonitoringManagerInfo dao = MonitoringManagerInfoImpl.getInstance();
      return dao.addStatisticsDeviceConnect(time, connect, error, warning, organization_id);
   }

   public List getStatisticsDeviceConnect(Timestamp date, long root_group_id) throws SQLException {
      MonitoringManagerInfo dao = MonitoringManagerInfoImpl.getInstance();
      return dao.getStatisticsDeviceConnect(date, root_group_id);
   }

   public int deleteOldStatisticsDeviceData(Timestamp time) throws SQLException {
      MonitoringManagerInfo dao = MonitoringManagerInfoImpl.getInstance();
      return dao.deleteOldStatisticsDeviceData(time);
   }
}
