package com.samsung.magicinfo.webauthor2.repository.datalink.servers;

import com.samsung.magicinfo.webauthor2.repository.datalink.servers.DLKMethod;
import com.samsung.magicinfo.webauthor2.repository.model.dlk.DLKTableRowData;
import com.samsung.magicinfo.webauthor2.repository.model.dlk.GetDataTableInfoDLKResponseData;
import java.util.ArrayList;
import java.util.List;
import org.springframework.web.client.RestTemplate;

public class GetDataTableInfoDLKMethod extends DLKMethod<List<DLKTableRowData>, GetDataTableInfoDLKResponseData> {
  private static final String DLK_METHOD_NAME = "getTableInfo";
  
  public GetDataTableInfoDLKMethod(RestTemplate restTemplate, Boolean useSsl, String ipAddress, Integer port, String dynaName) {
    super(restTemplate, useSsl, ipAddress, port, createPathsVariable(dynaName));
  }
  
  protected String getMethodName() {
    return "getTableInfo";
  }
  
  Class<GetDataTableInfoDLKResponseData> getResponseClass() {
    return GetDataTableInfoDLKResponseData.class;
  }
  
  List<DLKTableRowData> convertResponseData(GetDataTableInfoDLKResponseData responseData) {
    if (responseData != null && responseData.getRows() != null)
      return responseData.getRows(); 
    return new ArrayList<>();
  }
  
  private static String createPathsVariable(String dynaName) {
    StringBuilder sb = new StringBuilder("/");
    sb.append(dynaName);
    return sb.toString();
  }
}
