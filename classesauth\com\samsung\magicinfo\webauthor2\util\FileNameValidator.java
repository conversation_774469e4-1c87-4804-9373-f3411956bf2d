package com.samsung.magicinfo.webauthor2.util;

import com.samsung.magicinfo.webauthor2.util.SupportedFormatUtils;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;

@Component
public class FileNameValidator {
  private static final String SPECIAL_CHARACTERS_REGEX = "^[^\\\\{}&^%#$,\"|:'']+$";
  
  private static final String MOVE_TO_UPPER_FOLDER = "../";
  
  private static final List<String> INVALID_EXTENSIONS = Arrays.asList(new String[] { "lft", "lfd", "dlk", "vwl" });
  
  private static final List<String> EXECUTABLE_EXTENSIONS = Arrays.asList(new String[] { 
        "exe", "bat", "sh", "jsp", "jspx", "asp", "php", "mht", "ps1", "vbs", 
        "dll", "php5", "pht", "phtml", "shtml", "asa", "asax", "swf", "xap", "cmd", 
        "bin", "com", "cpl", "gadget", "inf1", "ins", "inx", "isu", "job", "jse", 
        "lnk", "msc", "msi", "msp", "mst", "paf", "pif", "reg", "rgs", "scr", 
        "sct", "shb", "shs", "u3p", "vb", "vbe", "vbscript", "ws", "wsf", "wsh" });
  
  private static final List<String> VALID_THUMBNAIL_EXTENSIONS = Arrays.asList(new String[] { "jpg", "jpeg", "png", "gif" });
  
  public boolean filenameContainsSpecialCharacters(String filename) {
    if (!Pattern.matches("^[^\\\\{}&^%#$,\"|:'']+$", filename))
      return true; 
    if (filename.contains("../"))
      return true; 
    return false;
  }
  
  public boolean filenameContainsSpecialCharactersAndDot(String filename) {
    return (!Pattern.matches("^[^\\\\{}&^%#$,\"|:'']+$", filename) || filename.contains("."));
  }
  
  public boolean filenameHasInvalidType(String filename) {
    String extension = FilenameUtils.getExtension(filename).toLowerCase();
    return (!SupportedFormatUtils.isSupportedFormat(extension) || INVALID_EXTENSIONS.contains(extension));
  }
  
  public boolean filenameHasExecutableType(String filename) {
    String extension = FilenameUtils.getExtension(filename).toLowerCase();
    return EXECUTABLE_EXTENSIONS.contains(extension);
  }
  
  public boolean filenameStartsWithDotOrEndsWithDotOrDoesntHaveAny(String filename) {
    int lastIndexOfDot = filename.lastIndexOf('.');
    return (lastIndexOfDot <= 0 || lastIndexOfDot == filename.length() - 1);
  }
  
  public boolean filenameStartsWithDotOrDoesntHaveAny(String filename) {
    int lastIndexOfDot = filename.lastIndexOf('.');
    return (lastIndexOfDot <= 0);
  }
  
  public boolean filenameHasValidThumbnailType(String filename) {
    String extension = FilenameUtils.getExtension(filename).toLowerCase();
    return VALID_THUMBNAIL_EXTENSIONS.contains(extension);
  }
}
