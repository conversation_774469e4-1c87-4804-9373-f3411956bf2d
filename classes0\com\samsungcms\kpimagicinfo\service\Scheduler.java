package com.samsungcms.kpimagicinfo.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.github.fge.jackson.JsonLoader;
import com.github.fge.jsonschema.core.report.ProcessingReport;
import com.github.fge.jsonschema.main.JsonSchema;
import com.github.fge.jsonschema.main.JsonSchemaFactory;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;
import com.samsungcms.kpimagicinfo.model.KpiPolicy;
import com.samsungcms.kpimagicinfo.service.SchedulerService;
import com.samsungcms.kpimagicinfo.util.FileSplitter;
import com.samsungcms.kpimagicinfo.util.RestClient;
import com.samsungcms.kpimagicinfo.util.Utils;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.FilenameFilter;
import java.io.IOException;
import java.io.Writer;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.NoSuchFileException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResourceAccessException;

@Component
public class Scheduler {
  private static final Logger LOGGER = LogManager.getLogger(com.samsungcms.kpimagicinfo.service.Scheduler.class);
  
  @Autowired
  KpiPolicy kpiPolicy;
  
  @Autowired
  Utils utils;
  
  @Autowired
  private SchedulerService schedulerService;
  
  private KpiPolicy newKpiPolicy;
  
  @Value("${debug.saveFile}")
  private boolean debugSaveFile;
  
  @Value("${policy.send.count}")
  private int sendPastFileCount;
  
  @Value("${policy.send.protocol.https.only}")
  private boolean httpsOnly;
  
  @Value("${policy.file.capacity}")
  private int capacity;
  
  @Autowired
  RestClient restClient;
  
  public static final String CRON_FORMAT = "0 %d %d */%d * *";
  
  public void loadCron() {
    try {
      getKpiPolicy();
      String[] timeHourMin = this.kpiPolicy.getTransfer().getTime().split(":");
      String cron = String.format("0 %d %d */%d * *", new Object[] { Integer.valueOf(Integer.parseInt(timeHourMin[1])), Integer.valueOf(Integer.parseInt(timeHourMin[0])), Integer.valueOf(this.kpiPolicy.getTransfer().getPeriod()) });
      LOGGER.info("scheduler cron = " + cron);
      this.schedulerService.changeCron(cron);
    } catch (Exception e) {
      LOGGER.error(e);
    } 
  }
  
  public String getURL(String type) {
    String url = null;
    url = this.kpiPolicy.getServer().getUrl() + type;
    url = url.replace("/" + type, type);
    if (this.httpsOnly && url != null && !url.startsWith("https://")) {
      LOGGER.error("url protocol is not HTTPS : " + url);
      return null;
    } 
    return url;
  }
  
  public void setCron() {
    try {
      setKpiPolicy();
      String[] timeHourMin = this.kpiPolicy.getTransfer().getTime().split(":");
      String cron = String.format("0 %d %d */%d * *", new Object[] { Integer.valueOf(Integer.parseInt(timeHourMin[1])), Integer.valueOf(Integer.parseInt(timeHourMin[0])), Integer.valueOf(this.kpiPolicy.getTransfer().getPeriod()) });
      LOGGER.info("scheduler cron = " + cron);
      this.schedulerService.changeCron(cron);
    } catch (Exception e) {
      LOGGER.error(e);
    } 
  }
  
  public void getPolicy() {
    try {
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(new MediaType("application", "json", StandardCharsets.UTF_8));
      headers.set("SID", this.utils.getServerID());
      LinkedMultiValueMap linkedMultiValueMap = new LinkedMultiValueMap();
      HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity(linkedMultiValueMap, (MultiValueMap)headers);
      String url = getURL("/policy");
      if (url == null)
        return; 
      LOGGER.info("request url: " + url);
      ResponseEntity<String> response = this.restClient.get(url, requestEntity);
      LOGGER.info("Response code: " + response.getStatusCode());
      if (response.getStatusCode() == HttpStatus.OK) {
        for (int i = 0; i < this.sendPastFileCount; i++)
          uploadFile(i); 
        String responseBody = (String)response.getBody();
        if (!validateKpiPolicy(responseBody))
          return; 
        Gson gsonObj = new Gson();
        this.newKpiPolicy = (KpiPolicy)gsonObj.fromJson(responseBody, KpiPolicy.class);
        checkKpiPolicyVersion();
      } else {
        LOGGER.error("response code : " + response.getStatusCode());
      } 
    } catch (ResourceAccessException e) {
      LOGGER.error(e.getMessage());
    } catch (HttpClientErrorException e) {
      LOGGER.error(e.getMessage());
    } catch (Exception e) {
      LOGGER.error(e);
    } 
  }
  
  private boolean validateKpiPolicy(String responseBody) {
    if (responseBody == null)
      return false; 
    try {
      String jsonSchema = "/static/kpiPolicySchema.json";
      JsonNode schemaNode = JsonLoader.fromResource(jsonSchema);
      JsonNode dataNode = JsonLoader.fromString(responseBody);
      JsonSchemaFactory factory = JsonSchemaFactory.byDefault();
      JsonSchema schema = factory.getJsonSchema(schemaNode);
      ProcessingReport report = schema.validate(dataNode);
      if (report.isSuccess())
        return true; 
      LOGGER.error(report.toString());
    } catch (Exception e) {
      LOGGER.error(e);
    } 
    return false;
  }
  
  public String zipFiles(List<File> fileList, int cnt) {
    String zipFileName = null;
    try {
      String date = this.utils.getDateString(0);
      zipFileName = this.utils.getUserKPILogFolderPath().toString() + File.separatorChar + this.utils.getServerID() + "." + date + "." + cnt + ".zip";
      LOGGER.info("zip filename : " + zipFileName);
      File zipFile = new File(zipFileName);
      if (zipFile.exists())
        zipFile.delete(); 
      FileOutputStream fos = new FileOutputStream(zipFileName);
      ZipOutputStream zipOut = new ZipOutputStream(fos);
      for (File file : fileList) {
        FileInputStream fis = new FileInputStream(file);
        ZipEntry zipEntry = new ZipEntry(file.getName());
        zipOut.putNextEntry(zipEntry);
        byte[] bytes = new byte[1024];
        int length;
        while ((length = fis.read(bytes)) >= 0)
          zipOut.write(bytes, 0, length); 
        fis.close();
      } 
      zipOut.close();
      fos.close();
    } catch (Exception e) {
      LOGGER.error(e);
    } 
    return zipFileName;
  }
  
  public void uploadFile(int cnt) {
    try {
      int period = this.kpiPolicy.getTransfer().getPeriod();
      List<File> kpiFileList = new ArrayList<>();
      try {
        List<File> fileList = null;
        fileList = getFilteredFiles(period, "CS_SPLITTED");
        if (fileList != null && fileList.size() > 0) {
          kpiFileList.addAll(fileList);
        } else {
          fileList = getFilteredFiles(period, "CS_LOG");
          kpiFileList.addAll(fileList);
        } 
        fileList = getFilteredFiles(period, "WEBAUTHOR");
        kpiFileList.addAll(fileList);
      } catch (Exception e) {
        LOGGER.error(e);
      } 
      LOGGER.info(cnt + ") [upload file] kpi file list size : " + kpiFileList.size());
      if (kpiFileList.size() == 0)
        return; 
      LinkedMultiValueMap linkedMultiValueMap = new LinkedMultiValueMap();
      String zipFileResult = zipFiles(kpiFileList, cnt);
      File sendFile = new File(zipFileResult);
      if (sendFile.exists()) {
        FileSystemResource fileSystemResource = new FileSystemResource(sendFile);
        linkedMultiValueMap.add("file", fileSystemResource);
      } else {
        return;
      } 
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.MULTIPART_FORM_DATA);
      headers.set("SID", this.utils.getServerID());
      HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity(linkedMultiValueMap, (MultiValueMap)headers);
      String url = getURL("/upload");
      if (url == null)
        return; 
      LOGGER.info("request url: " + url);
      ResponseEntity<String> response = this.restClient.post(url, requestEntity);
      LOGGER.info("Response code: " + response.getStatusCode());
      if (response.getStatusCode() == HttpStatus.OK) {
        deleteFile(sendFile, kpiFileList);
      } else {
        LOGGER.error("response code : " + response.getStatusCode());
      } 
    } catch (ResourceAccessException e) {
      LOGGER.error(e.getMessage());
    } catch (HttpClientErrorException e) {
      LOGGER.error(e.getMessage());
    } catch (Exception e) {
      LOGGER.error(e);
    } 
  }
  
  public void deleteFile(File sendFile, List<File> kpiFileList) {
    try {
      if (!this.debugSaveFile) {
        LOGGER.info("delete file : " + sendFile.getName());
        sendFile.delete();
        for (File logFile : kpiFileList) {
          LOGGER.info("delete file : " + logFile.getName());
          logFile.delete();
        } 
      } 
    } catch (Exception e) {
      LOGGER.error(e);
    } 
  }
  
  public void checkKpiPolicyVersion() {
    try {
      if (this.kpiPolicy.getVersion() < this.newKpiPolicy.getVersion()) {
        this.kpiPolicy = this.newKpiPolicy;
        setCron();
        LOGGER.info("policy updated. version : " + this.kpiPolicy.getVersion());
      } 
    } catch (Exception e) {
      LOGGER.error(e);
    } 
  }
  
  public void setKpiPolicy() {
    try {
      String filePath = this.utils.getKPIPolicyFilePath();
      LOGGER.info("set kpiPolicy filePath : " + filePath);
      Gson gson = (new GsonBuilder()).setPrettyPrinting().create();
      Writer writer = new FileWriter(filePath);
      gson.toJson(this.kpiPolicy, writer);
      writer.flush();
      writer.close();
    } catch (IOException e) {
      LOGGER.error(e);
    } 
  }
  
  public void backupFile(Path path) {
    try {
      String date = this.utils.getDateString(0);
      File newFile = new File(path.toAbsolutePath() + ".backup." + date);
      Files.move(path, newFile.toPath(), new java.nio.file.CopyOption[0]);
    } catch (IOException e) {
      LOGGER.error(e);
    } 
  }
  
  public void getKpiPolicy() {
    String filePath = null;
    try {
      filePath = this.utils.getKPIPolicyFilePath();
      LOGGER.info("kpiPolicy filePath : " + filePath);
      Path path = Paths.get(filePath, new String[0]);
      BufferedReader reader = Files.newBufferedReader(path);
      StringBuffer stringBuffer = new StringBuffer();
      String readLine = null;
      while ((readLine = reader.readLine()) != null)
        stringBuffer.append(readLine); 
      if (!validateKpiPolicy(stringBuffer.toString())) {
        LOGGER.info("kpipolicy.json validation check error");
        LOGGER.info("backup file - kpipolicy.json.backup");
        backupFile(Paths.get(filePath, new String[0]));
        LOGGER.info("create new file - kpipolicy.json");
        setKpiPolicy();
        return;
      } 
      Gson gsonObj = new Gson();
      this.kpiPolicy = (KpiPolicy)gsonObj.fromJson(stringBuffer.toString(), KpiPolicy.class);
      LOGGER.info("load kpiPolicy : " + filePath);
    } catch (JsonSyntaxException e) {
      LOGGER.error(e);
      LOGGER.info("backup file - kpipolicy.json.backup");
      Path path = Paths.get(filePath, new String[0]);
      backupFile(path);
      LOGGER.info("create new file - kpipolicy.json");
      setKpiPolicy();
    } catch (NoSuchFileException e) {
      LOGGER.error(e);
      LOGGER.info("create new file - kpipolicy.json");
      setKpiPolicy();
    } catch (Exception e) {
      LOGGER.error(e);
    } 
  }
  
  public List<File> splitFile(File file, String destDirPath) {
    List<File> result = new ArrayList<>();
    try {
      String srcFilePath = file.getAbsolutePath();
      String splittedFileNameFormat = file.getName().replace(".json", ".%d.json");
      String header = null;
      ByteBuffer buffer = ByteBuffer.allocate(this.capacity * 1048576);
      result = FileSplitter.splitFileIntoDir(srcFilePath, destDirPath, splittedFileNameFormat, header, buffer);
      file.delete();
    } catch (Exception e) {
      e.printStackTrace();
    } 
    return result;
  }
  
  public List<File> getFilteredFiles(int period, String type) {
    List<File> fileList = new ArrayList<>();
    try {
      String userKpiForlderPath = this.utils.getUserKPILogFolderPath().toString();
      File dir = new File(userKpiForlderPath);
      File[] files = dir.listFiles((FilenameFilter)new Object(this, type));
      if (files != null && files.length > 0) {
        Arrays.sort(files, Collections.reverseOrder());
        int fileCount = (files.length < period) ? files.length : period;
        for (int i = 0; i < fileCount; i++) {
          files[i] = this.utils.renameFile(files[i]);
          if (this.utils.validFileName(files[i].getName(), "SPLITTED")) {
            fileList.add(files[i]);
            LOGGER.info("files add - " + files[i].getName());
          } else {
            List<File> result = splitFile(files[i], userKpiForlderPath);
            for (File file : result) {
              fileList.add(file);
              LOGGER.info("files add - " + file.getName());
              i++;
              if (i >= fileCount)
                break; 
            } 
          } 
        } 
      } 
    } catch (Exception e) {
      LOGGER.error(e.getMessage());
    } 
    return fileList;
  }
}
