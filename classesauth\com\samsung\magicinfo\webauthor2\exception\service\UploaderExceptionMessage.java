package com.samsung.magicinfo.webauthor2.exception.service;

public final class UploaderExceptionMessage {
  public static final String UPLOAD_ALL_NOT_SUCCESS = "UploadAllNotSuccessError";
  
  public static final String UPLOAD_LARGE_FILE = "UploadLargeFileError";
  
  public static final String INVALID_FILE_PATH = "InvalidFilePathError";
  
  public static final String INVALID_CONTENT_ID = "InvalidContentIdError";
  
  public static final String UPLOAD_FILE_CORRUPT = "UploadFileCorruptError";
  
  public static final String PARSE_LFD_ERROR = "LFDParseError";
  
  public static final String PARSE_LFD_ERROR_1003 = "LFDParseError1003";
  
  public static final String PARSE_LFD_ERROR_1004 = "LFDParseError1004";
  
  public static final String PARSE_LFD_ERROR_1005 = "LFDParseError1005";
  
  public static final String PARSE_LFD_ERROR_1006 = "LFDParseError1006";
  
  public static final String PARSE_LFD_ERROR_1007 = "LFDParseError1007";
  
  public static final String PARSE_LFD_ERROR_1008 = "LFDParseError1008";
  
  public static final String PARSE_LFD_ERROR_1009 = "LFDParseError1009";
  
  public static final String PARSE_LFD_ERROR_1010 = "LFDParseError1010";
  
  public static final String PARSE_LFD_ERROR_1014 = "LFDParseError1014";
  
  public static final String PARSE_LFD_ERROR_1049 = "LFDParseError1049";
  
  public static final String PARSE_LFD_ERROR_1065 = "LFDParseError1065";
  
  public static final String PARSE_LFD_ERROR_3026 = "LFDParseError3026";
  
  public static final String PARSE_VWL_ERROR = "Server Parse VWL Internal Error";
  
  public static final String FILE_EXCEED_LIMIT_ERROR = "fileExceededLimit";
  
  public static final String TIME_OUT = "connectionTimeout";
  
  public static final String HTTP_ERROR = "httpError";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR = "ServerInternalUploadError";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR_1000 = "ServerInternalUploadError1000";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR_1001 = "ServerInternalUploadError1001";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR_1002 = "ServerInternalUploadError1002";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR_1032 = "UploaderException(1032)";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR_1033 = "UploaderException(1033)";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR_1035 = "UploaderException(1035)";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR_1038 = "ServerInternalErrorInterruptedError1038";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR_1045 = "ServerInternalError1045";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR_1046 = "ServerInternalErrorInterruptedError1046";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR_1047 = "ServerInternalError1047";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR_1057 = "ServerInternalErrorInterruptedError1057";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR_1058 = "ServerInternalErrorInterruptedError1058";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR_1064 = "ServerInternalErrorInterruptedError1064";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR_1084 = "ServerInternalUploadError1084";
  
  public static final String KEY_CANT_BE_NULL = "Config Exception : key can not be null";
  
  public static final String U001 = "U001";
  
  public static final String FTP_CONNECTION_ERROR = "421";
  
  public static final String LOGIN_FAILED_601 = "Login Failed";
  
  public static final String CONTENT_ID_IS_NULL_602 = "Content ID is null.";
  
  public static final String SERVER_INTERNAL_UPLOAD_ERROR_603 = "Internal Server Error.";
  
  public static final String FILE_ID_IS_NULL_604 = "File ID is null.";
  
  public static final String HASH_VALUE_IS_NULL_605 = "Hash value is null.";
  
  public static final String FILE_DOESNT_EXIST_606 = "File doesn't exist on server.";
  
  public static final String FILE_ALREADY_EXISTS_607 = "File already exists";
  
  public static final String FILE_PATH_IS_NULL_608 = "File path is null.";
  
  public static final String CLIENT_ABORTED_DOWNLOADING_FILE_609 = "Client aborted downloading file.";
  
  public static final String USER_NO_AUTHORIZATION_READ_610 = "The user is not authorized to read content.";
  
  public static final String USER_NO_AUTHORIZATION_WRITE_611 = "The user is not authorized to write content.";
  
  public static final String FAILED_TO_GET_FILE_INFORMATION_612 = "Failed to get file information.";
  
  public static final String FAILED_TO_GET_FILE_613 = "Failed to get file.";
  
  public static final String CANNOT_READ_CSD_FILE_614 = "Cannot read CSD file.";
  
  public static final String CANNOT_READ_CSD_FILE_615 = "Cannot read CSD file.";
  
  public static final String DB_CONNECTION_ERROR_620 = "DB Connection Error";
  
  public static final String FAILED_TO_UPLOAD_FILE_UNKNOWN_699 = "Fail upload file (Unknow Exception).";
  
  public static final String CANT_LOAD_CONFIG_FILE = "Can't load config file";
  
  public static final String UNKNOWN_HTTP_CODE_FROM_MIS = "Unknown http status code response from MagicINFO Server";
  
  public static final String INITIALIZATION_ERROR = "Error during file structure initialization";
  
  public static final String CLEAN_UP_ERROR = "Error during clean up after file upload";
  
  public static final String WEB_CONTENT_UPLOAD_ERROR = "Failed to upload WebContent";
  
  public static final String THUMBNAIL_GENERATION_ERROR = "Error during thumbnail generation";
  
  public static final String FONT_UPLOAD_ERROR = "Failed to upload Font";
  
  public static final String ERROR_NONE_OR_MULTIPLE_LFD = "Lfd is not exist or multiple";
}
