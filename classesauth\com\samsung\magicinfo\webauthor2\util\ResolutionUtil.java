package com.samsung.magicinfo.webauthor2.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

public final class ResolutionUtil {
  public static int[] parseResolution(String resolution) {
    Assert.hasLength(resolution, "Resolution can't be empty");
    String[] res = StringUtils.split(resolution, "x");
    Assert.notEmpty((Object[])res, "Resolution can't be empty");
    int[] pair = new int[2];
    pair[0] = Integer.parseInt(res[0].trim());
    pair[1] = Integer.parseInt(res[1].trim());
    return pair;
  }
}
