package com.samsung.magicinfo.rc.common.queue;

import com.samsung.magicinfo.rc.common.Device;
import com.samsung.magicinfo.rc.common.exception.OpenApiExceptionCode;
import com.samsung.magicinfo.rc.common.exception.OpenApiServiceException;
import com.samsung.magicinfo.rc.framework.queue.entity.ClientEntity;
import java.util.HashMap;
import java.util.Queue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class ServerQueue extends HashMap<String, Device> {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.common.queue.ServerQueue.class);
  
  public static int SUCCESS = 0;
  
  private static final long serialVersionUID = 1L;
  
  public void startQueue(Device device) throws OpenApiServiceException {
    if (!containsKey(device.getDeviceId())) {
      put((K)device.getDeviceId(), (V)device);
    } else {
      log.error("startQueue error - already start service");
    } 
  }
  
  public void putQueue(String DeviceID, ClientEntity client) throws OpenApiServiceException {
    if (containsKey(DeviceID)) {
      ((Device)get(DeviceID)).getQueue().add(client);
    } else {
      log.error("putQueue error - containKey");
      throw new OpenApiServiceException(OpenApiExceptionCode.DONT_HAVE_DEVICE_ID_IN_QUEEUE[0], OpenApiExceptionCode.DONT_HAVE_DEVICE_ID_IN_QUEEUE[1]);
    } 
  }
  
  public Device getDeviceInfo(String deviceId) {
    return (Device)get(deviceId);
  }
  
  public Object[] getQueue(String deviceId) throws OpenApiServiceException {
    Object[] rtn = new Object[2];
    ClientEntity client = new ClientEntity();
    Queue<ClientEntity> queue = null;
    if (containsKey(deviceId)) {
      rtn[0] = Integer.valueOf(SUCCESS);
      queue = ((Device)get(deviceId)).getQueue();
      if (queue.isEmpty()) {
        client.setKeyCode(null);
        rtn[1] = client;
      } else {
        rtn[1] = queue.poll();
      } 
      ((Device)get(deviceId)).setQueue(queue);
    } else {
      throw new OpenApiServiceException(OpenApiExceptionCode.DONT_HAVE_DEVICE_ID_IN_QUEEUE[0], OpenApiExceptionCode.DONT_HAVE_DEVICE_ID_IN_QUEEUE[1]);
    } 
    return rtn;
  }
  
  public Boolean getStatusQueue(String deviceId) {
    Boolean rtn = Boolean.valueOf(false);
    if (containsKey(deviceId))
      rtn = Boolean.valueOf(true); 
    return rtn;
  }
  
  public ClientEntity inputQueue(String KeyCode, String setPanel, String reqScreenMode, String reqOpMode) {
    ClientEntity client = new ClientEntity();
    client.setKeyCode(KeyCode);
    client.setSetPanel(setPanel);
    client.setReqScreenMode(reqScreenMode);
    client.setReqOpMode(reqOpMode);
    return client;
  }
  
  public void inputQueue(String deviceId, String KeyCode, String screenMode, String text, String mouse) throws OpenApiServiceException {
    ClientEntity client = new ClientEntity();
    client.setKeyCode(KeyCode);
    client.setReqOpMode("null");
    client.setSetPanel("null");
    client.setReqScreenMode(screenMode);
    client.setText(text);
    client.setMouse(mouse);
    putQueue(deviceId, client);
  }
  
  public void inputScreenModeQueue(String deviceId, String screenMode) throws OpenApiServiceException {
    ClientEntity client = new ClientEntity();
    client.setKeyCode("null");
    client.setReqOpMode("null");
    client.setSetPanel("null");
    client.setReqScreenMode(screenMode);
    putQueue(deviceId, client);
  }
  
  public void inputQueue(String deviceId, String reqOpMode) throws OpenApiServiceException {
    ClientEntity client = new ClientEntity();
    client.setKeyCode("null");
    client.setReqOpMode(reqOpMode);
    putQueue(deviceId, client);
  }
  
  public void DestroyQueue(String DeviceId) throws OpenApiServiceException {
    if (containsKey(DeviceId)) {
      remove(DeviceId);
    } else {
      throw new OpenApiServiceException(OpenApiExceptionCode.ALREADY_DISCONNECTION[0], OpenApiExceptionCode.ALREADY_DISCONNECTION[1]);
    } 
  }
  
  public HashMap getQueue() {
    HashMap lists = null;
    lists = this;
    return lists;
  }
  
  public Object[] getConnection() {
    Object[] rtn = new Object[2];
    rtn[0] = Integer.valueOf(SUCCESS);
    rtn[1] = Integer.valueOf(size());
    return rtn;
  }
}
