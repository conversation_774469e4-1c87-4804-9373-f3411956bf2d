package com.samsung.magicinfo.protocol.util.dao;

import com.samsung.magicinfo.framework.setup.entity.ServerManagementEntity;
import com.samsung.magicinfo.framework.setup.entity.SystemLogEntity;
import com.samsung.magicinfo.restapi.setting.model.config.MFA;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

public interface ServerSetupDaoMapper {
   Map getServerCommonInfo() throws SQLException;

   Map getServerInfoByOrgId(@Param("orgId") Long var1) throws SQLException;

   Integer updateServerInfo(@Param("map") Map var1) throws SQLException;

   List getServerMFAInfo(@Param("mfaType") String var1) throws SQLException;

   Integer updateServerMFAInfo(@Param("mfa") MFA var1) throws SQLException;

   Boolean checkPermissionsDeviceByOrgId(@Param("orgId") Long var1) throws SQLException;

   Integer changePermissionsFunc(@Param("check") Boolean var1) throws SQLException;

   Integer getRedundancyEnabledCnt() throws SQLException;

   Boolean isRedundancyEnable();

   List getDisconnectEmailAlarmEnabledOrg() throws SQLException;

   Boolean addDefaultServerInfo(long var1);

   Boolean checkPermissionsDevice() throws SQLException;

   List getAllServerInfo() throws SQLException;

   Boolean resetExternalServerErrCount(@Param("serverType") String var1, @Param("ipAddress") String var2);

   Boolean addExternalServerErrCount(@Param("serverType") String var1, @Param("ipAddress") String var2);

   Integer getExternalServerErrCount(@Param("serverType") String var1, @Param("ipAddress") String var2);

   Boolean addExternalServerForMonitoring(@Param("serverType") String var1, @Param("ipAddress") String var2, @Param("defaultErrorCount") Integer var3);

   Integer isExistExternalServer(@Param("serverType") String var1, @Param("ipAddress") String var2);

   Boolean deleteExternalServerForMonitoring(@Param("serverType") String var1, @Param("ipAddress") String var2);

   Boolean setLastErrTime(@Param("serverType") String var1, @Param("ipAddress") String var2);

   Timestamp getLastErrTime(@Param("serverType") String var1, @Param("ipAddress") String var2);

   Boolean isLdapEnable(@Param("org_id") Long var1) throws SQLException;

   Boolean isLdapUseServerSetting(@Param("org_id") Long var1) throws SQLException;

   Integer hasApplyLdapServerSetting();

   Integer hasApplySmtpServerSetting() throws SQLException;

   Boolean isLdapSeparateSettings() throws SQLException;

   Boolean isSmtpSeparateSettings() throws SQLException;

   Map getSecretValue() throws SQLException;

   List getRuleMangerEnabledOrgID() throws SQLException;

   Boolean resetRuleMangerEnabledOrg() throws SQLException;

   Boolean updateRuleMangerEnabledOrg(@Param("org_id") Long var1) throws SQLException;

   List getNotificationEnabledOrgList() throws SQLException;

   Boolean addLogInfo(@Param("systemLogEntity") SystemLogEntity var1);

   Boolean addServerManagementInfo(@Param("entity") ServerManagementEntity var1) throws SQLException;

   ServerManagementEntity getServerManagementInfo(@Param("management_id") String var1) throws SQLException;

   Boolean updateServerManagementInfo(@Param("entity") ServerManagementEntity var1) throws SQLException;

   Boolean updateServerSetupKpi(@Param("map") Map var1) throws SQLException;

   Map getServerSetupKpi() throws SQLException;

   Boolean addServerSetupKpi(@Param("map") Map var1) throws SQLException;

   Integer hideUserIdInLogs(@Param("userId") String var1) throws SQLException;

   String getDefaultPassword() throws SQLException;
}
