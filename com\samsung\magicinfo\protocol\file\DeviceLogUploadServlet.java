package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.DiagnosisConstants;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.device.constants.DeviceConstants;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceLogCollectEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FilenameFilter;
import java.io.IOException;
import java.io.InputStream;
import java.nio.channels.FileChannel;
import java.sql.SQLException;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import javax.servlet.ServletException;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;

public class DeviceLogUploadServlet extends HttpServlet {
   private static final long serialVersionUID = 7240807302591007139L;
   final String TYPE_PLATFORM = "platform";
   final String TYPE_W_PLAYER = "wplayer";
   final String TYPE_THIRD_APPLICATION = "third_application";
   int nServletCalledCount = 0;
   int MAX_SERVLET_CALLED_COUNT = 20;
   private static long maxLogFileSize = CommonUtils.getConfigLongNumber("device.log_collect.max_size", 10485760L, 419430400L);
   Logger logger = LoggingManagerV2.getLogger(DeviceLogUploadServlet.class);

   public DeviceLogUploadServlet() {
      super();
   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/plain; charset=UTF-8");
      this.doPost(request, response);
   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/plain; charset=UTF-8");
      System.out.println("setCharacterEncoding : " + request.getContentType());
      String deviceId = request.getHeader("deviceId");
      String deviceToken = request.getHeader("token");
      String deviceStatus = request.getHeader("status");
      String encryptionKey = request.getHeader("encryptionKey");
      String diagnosticEncryptionKey = request.getHeader("diagnosticEncryptionKey");
      String deviceFilename = request.getHeader("filename");
      FileOutputStream outputStream = null;
      FileChannel fileChannel = null;
      ServletInputStream inputStream = null;

      try {
         try {
            this.logger.error("[DeviceLogUploadServlet] DeviceId : " + deviceId + ", status : " + deviceStatus);
            DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
            List deviceLogCollectEntityList = deviceDao.getDeviceLogProcessInfo(deviceId);
            DeviceLogCollectEntity logInfo = null;
            Iterator var16 = deviceLogCollectEntityList.iterator();

            while(true) {
               String type;
               while(var16.hasNext()) {
                  DeviceLogCollectEntity logCollectEntity = (DeviceLogCollectEntity)var16.next();
                  if (logCollectEntity.getStatus().equalsIgnoreCase("END") && deviceToken.equalsIgnoreCase(logCollectEntity.getToken())) {
                     this.logger.error("[DeviceLogUploadServlet][ERROR] This device's collecting is stopped by user. (deviceid : " + deviceId + ")");
                     if (this.nServletCalledCount > this.MAX_SERVLET_CALLED_COUNT) {
                        DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
                        type = UUID.randomUUID().toString();
                        confMgr.setDeviceLogProcessing(deviceId, logCollectEntity.getCategory_script(), type);
                        this.nServletCalledCount = 0;
                     }

                     ++this.nServletCalledCount;
                  } else if (!logCollectEntity.getStatus().equalsIgnoreCase("END")) {
                     logInfo = logCollectEntity;
                  }
               }

               String responseCode;
               if (null != logInfo) {
                  String DEVICE_LOG_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "device_log";
                  String logFileFolderPath = DEVICE_LOG_HOME + File.separator + deviceId;
                  String fileName = deviceId + "_" + deviceToken + "_" + (deviceFilename != null ? deviceFilename : "deviceLog.txt");
                  fileName = SecurityUtils.directoryTraversalChecker(fileName, (String)null);
                  this.logger.info("[DeviceLogUploadServlet] creating file (deviceid : " + deviceId + ") fileName=" + fileName);
                  type = logInfo.getType();
                  String categoryScript = logInfo.getCategory_script();
                  if ("platform".equalsIgnoreCase(type)) {
                     deviceFilename = "";
                  }

                  File logHome = SecurityUtils.getSafeFile(DEVICE_LOG_HOME);
                  File logFileFolder = SecurityUtils.getSafeFile(logFileFolderPath);
                  String token = logInfo.getToken();
                  if (!deviceToken.equalsIgnoreCase(token)) {
                     responseCode = DeviceConstants.DEVICE_LOG_WRONG_TOKEN[0];
                     response.setStatus(Integer.valueOf(responseCode));
                     deviceDao.updateDeviceLogProcessStatus(deviceId, type, categoryScript, "END");
                     this.logger.error("[DeviceLogUploadServlet][ERROR] wrong token. (deviceid : " + deviceId + ") deviceToken=" + deviceToken + ", token=" + token);
                     return;
                  }

                  if (deviceStatus != null && deviceStatus.equalsIgnoreCase("END")) {
                     deviceDao.updateDeviceLogProcessStatus(deviceId, type, categoryScript, "END");
                     this.logger.error("[DeviceLogUploadServlet] Log Collecting is stopped by device. (deviceid : " + deviceId + ")");
                     return;
                  }

                  if (deviceStatus != null && deviceStatus.equalsIgnoreCase("START")) {
                     this.nServletCalledCount = 0;
                     if (logFileFolder != null && logFileFolder.exists()) {
                        if (DeviceUtils.deleteLogFile(logFileFolder, type, logInfo.getFile_name())) {
                           this.logger.info("[DeviceLogUploadServlet] LogFilesFolder by type and files are cleaned up successfully! (deviceId : " + deviceId + ")");
                        } else {
                           this.logger.info("[DeviceLogUploadServlet] Failed to clean up the logfilefolder by type. (deviceId : " + deviceId + ")");
                        }
                     }

                     boolean fSuccess;
                     if (logHome != null && !logHome.exists()) {
                        fSuccess = logHome.mkdir();
                        if (fSuccess) {
                           this.logger.info("[DeviceLogUploadServlet] make device log home folder successfully.");
                        } else {
                           this.logger.info("[DeviceLogUploadServlet] failed to make device log home folder.");
                        }
                     }

                     if (logFileFolder != null && !logFileFolder.exists()) {
                        fSuccess = logFileFolder.mkdir();
                        if (fSuccess) {
                           this.logger.info("[DeviceLogUploadServlet] make device log folder successfully. (deviceId : " + deviceId + ")");
                        } else {
                           this.logger.info("[DeviceLogUploadServlet] failed to make device log folder . (deviceId : " + deviceId + ")");
                        }
                     }

                     if (logFileFolder != null && !logFileFolder.exists()) {
                        fSuccess = logFileFolder.mkdir();
                        if (fSuccess) {
                           this.logger.info("[DeviceLogUploadServlet] make device log folder by type successfully. (deviceId : " + deviceId + ")");
                        } else {
                           this.logger.info("[DeviceLogUploadServlet] failed to make device log folder by type. (deviceId : " + deviceId + ")");
                        }
                     }

                     if (encryptionKey != null && !encryptionKey.equalsIgnoreCase("") && encryptionKey.length() > 0) {
                        new String(Base64.decodeBase64(encryptionKey.getBytes()));
                        FileOutputStream fos = null;

                        try {
                           this.logger.info("[DeviceLogUploadServlet] encryptionKey file is created. for device id = " + deviceId);
                           File keyfile = SecurityUtils.getSafeFile(logFileFolder + File.separator + DiagnosisConstants.key);
                           fos = new FileOutputStream(keyfile);
                           fos.write(Base64.decodeBase64(encryptionKey.getBytes()));
                        } catch (IOException var117) {
                           this.logger.error("", var117);
                        } finally {
                           try {
                              if (fos != null) {
                                 fos.close();
                              }
                           } catch (IOException var116) {
                              this.logger.error("Unable to close file output stream ", var116);
                           }

                        }
                     } else {
                        this.logger.error("[DeviceLogUploadServlet] encryptionKey is not exist. Empty!! (deviceid : " + deviceId + ")");
                     }

                     if (StringUtils.isNotEmpty(diagnosticEncryptionKey)) {
                        FileOutputStream fos = null;

                        try {
                           this.logger.info("[DeviceLogUploadServlet] diagnosticEncryptionKey file is created. for device id = " + deviceId);
                           File keyfile = SecurityUtils.getSafeFile(logFileFolder + File.separator + DiagnosisConstants.diagnosticKey);
                           fos = new FileOutputStream(keyfile);
                           fos.write(Base64.decodeBase64(diagnosticEncryptionKey.getBytes()));
                        } catch (IOException var114) {
                           this.logger.error("", var114);
                        } finally {
                           try {
                              if (fos != null) {
                                 fos.close();
                              }
                           } catch (IOException var113) {
                              this.logger.error("Unable to close file output stream ", var113);
                           }

                        }
                     } else {
                        this.logger.error("[DeviceLogUploadServlet] diagnosticEncryptionKey is not exist. Empty!! (deviceid : " + deviceId + ")");
                     }

                     this.logger.error("[DeviceLogUploadServlet] Server ready to get log from device successfully.  (deviceid : " + deviceId + ")");
                     return;
                  }

                  if (deviceStatus != null && deviceStatus.equalsIgnoreCase("PROCESSING")) {
                     this.logger.error("[DeviceLogUploadServlet] Request type PROCESSING");
                     int contentSize = request.getContentLength();
                     inputStream = request.getInputStream();
                     String result = this.messageStreamToString(inputStream);
                     if (result.length() != contentSize) {
                        responseCode = DeviceConstants.DEVICE_LOG_WRONG_SIZE[0];
                        response.setStatus(Integer.valueOf(responseCode));
                        this.logger.error("[DeviceLogUploadServlet] content size and body size are different.  (deviceid : " + deviceId + ")");
                        return;
                     } else {
                        boolean storageCheck = false;
                        String homePath = CommonConfig.get("CONTENTS_HOME");
                        String homeDir = "C:\\";
                        if (homePath != null) {
                           homeDir = homePath.split(":")[0] + ":\\";
                        }

                        if (CommonConfig.get("e2e.enable") != null && CommonConfig.get("e2e.enable").equalsIgnoreCase("true")) {
                           storageCheck = true;
                        }

                        File[] roots;
                        if (!storageCheck) {
                           roots = File.listRoots();

                           for(int i = 0; roots != null && i < roots.length; ++i) {
                              if (roots[i].getPath().equalsIgnoreCase(homeDir) && roots[i].getTotalSpace() > 0L) {
                                 long freeSizeLong = roots[i].getFreeSpace();
                                 if (freeSizeLong > maxLogFileSize) {
                                    storageCheck = true;
                                    break;
                                 }
                              }
                           }
                        }

                        if (storageCheck) {
                           deviceDao.updateDeviceLogProcessStatus(deviceId, type, categoryScript, "PROCESSING");
                           roots = null;
                           String logFilePath = null;
                           int numOfFiles = 0;
                           File logFile;
                           String encryption;
                           if (StrUtils.isEmpty(deviceFilename)) {
                              int index = fileName.indexOf(46);
                              if (index > 0) {
                                 numOfFiles = this.getFileCount(logFileFolder, fileName.substring(0, index));
                                 if (numOfFiles > 1) {
                                    String extension = fileName.substring(index + 1);
                                    encryption = fileName.substring(0, index);
                                    fileName = encryption + "." + (numOfFiles - 1) + "." + extension;
                                 }
                              }

                              logFilePath = logFileFolder + File.separator + fileName;
                              logFile = SecurityUtils.getSafeFile(logFilePath);
                              this.logger.error("[DeviceLogUploadServlet] File is empty (deviceFilename : " + deviceFilename + "  numOfFiles : " + numOfFiles + " fileName : " + fileName + " logFilePath: " + logFilePath + " logFile: " + logFile + ")");
                           } else {
                              logFilePath = logFileFolder + File.separator + fileName;
                              logFile = SecurityUtils.getSafeFile(logFilePath);
                              if (!logFile.exists()) {
                                 this.logger.error("[DeviceLogUploadServlet] File exist so delete it (logFilePath : " + logFilePath + "  logFile : " + logFile + ")");
                                 logFile.delete();
                              }
                           }

                           try {
                              if (!logFile.exists() && !logFile.createNewFile()) {
                                 this.logger.error("[DeviceLogUploadServlet] failed to make device log file . (fileName : " + logFilePath + ")");
                              }

                              if (StrUtils.isEmpty(deviceFilename)) {
                                 long logFileSize = logFile.length();
                                 this.logger.error("[DeviceLogUploadServlet] deviceFilename not empty fo check file size : " + deviceFilename + "  size : " + logFileSize + ")");
                                 if (logFileSize > maxLogFileSize && logFileFolder.exists()) {
                                    int index = fileName.indexOf(46);
                                    if (index > 0) {
                                       String extension = fileName.substring(fileName.lastIndexOf(46) + 1);
                                       String fileNameWithoutExtension = fileName.substring(0, index);
                                       fileName = fileNameWithoutExtension + "." + numOfFiles + "." + extension;
                                       logFilePath = logFileFolder + File.separator + fileName;
                                       logFile = SecurityUtils.getSafeFile(logFilePath);
                                       logFile.createNewFile();
                                       this.logger.info("========= new log file is added. fileName : " + fileName);
                                    }
                                 }
                              }
                           } catch (IOException var121) {
                              this.logger.error(" Log file Create Issue  " + var121);
                           }

                           StringBuffer sb = new StringBuffer("");
                           sb.append(result);
                           encryption = StrUtils.nvl(CommonConfig.get("device.log_collect.encryption"));
                           byte[] byteLog;
                           if (encryption != null && encryption.equalsIgnoreCase("true")) {
                              byteLog = Base64.decodeBase64(sb.toString().getBytes());
                           } else {
                              byteLog = sb.toString().getBytes();
                           }

                           try {
                              FileOutputStream fos = new FileOutputStream(logFile, true);
                              Throwable var143 = null;

                              try {
                                 fos.write(byteLog);
                              } catch (Throwable var120) {
                                 var143 = var120;
                                 throw var120;
                              } finally {
                                 if (fos != null) {
                                    if (var143 != null) {
                                       try {
                                          fos.close();
                                       } catch (Throwable var119) {
                                          var143.addSuppressed(var119);
                                       }
                                    } else {
                                       fos.close();
                                    }
                                 }

                              }
                           } catch (IOException var123) {
                              this.logger.error("[DeviceLogUploadServlet] Error occur during writing log file.");
                              this.logger.error(var123.getMessage());
                           }

                           if (StringUtils.isNotEmpty(fileName)) {
                              deviceDao.updateLogFileName(deviceId, logInfo.getCategory_script(), fileName);
                           }

                           this.logger.info("========= [DeviceLogUploadServlet] device log file is made successfully .");
                           this.logger.info("========= FileName : " + (StringUtils.isNotEmpty(deviceFilename) ? deviceFilename : fileName));
                           this.logger.info("========= FileSize : " + logFile.length());
                        } else {
                           responseCode = DeviceConstants.DEVICE_LOG_NO_STORAGE[0];
                           response.setStatus(Integer.valueOf(responseCode));
                           deviceDao.updateDeviceLogProcessStatus(deviceId, type, categoryScript, "END");
                           this.logger.error("[DeviceLogUploadServlet][ERROR] No Storage in server. (deviceid : " + deviceId + ")");
                        }

                        return;
                     }
                  }

                  return;
               }

               responseCode = DeviceConstants.DEVICE_LOG_ERROR_INVALID[0];
               response.setStatus(Integer.valueOf(responseCode));
               return;
            }
         } catch (SQLException | ConfigException var124) {
            this.logger.error(var124.getMessage(), var124);
            response.setStatus(Integer.valueOf(DeviceConstants.DEVICE_LOG_UNKNOWN_ERROR[0]));
         } catch (Exception var125) {
            this.logger.error("[DeviceLogUploadServlet] Caught Exception" + var125.getMessage());
            this.logger.error(var125.getMessage());
         }

      } finally {
         if (outputStream != null) {
            ((FileOutputStream)outputStream).close();
         }

         if (fileChannel != null) {
            ((FileChannel)fileChannel).close();
         }

         if (inputStream != null) {
            inputStream.close();
         }

      }
   }

   private int getFileCount(File path, final String startString) {
      File[] files = path.listFiles(new FilenameFilter() {
         public boolean accept(File dir, String name) {
            return name.toLowerCase().startsWith(startString.toLowerCase());
         }
      });
      return files == null ? 0 : files.length;
   }

   private String messageStreamToString(InputStream in) throws ConfigException, FileNotFoundException {
      StringBuffer message = new StringBuffer();

      try {
         while(true) {
            int i = in.read();
            if (i == -1) {
               break;
            }

            message.append((char)i);
         }
      } catch (IOException var4) {
         this.logger.error("", var4);
      }

      return message.toString();
   }

   public static String stringToHex(String s) {
      String result = "";

      for(int i = 0; i < s.length(); ++i) {
         result = result + String.format("%02X ", Integer.valueOf(s.charAt(i)));
      }

      return result;
   }

   public static byte[] base64Enc(byte[] buffer) {
      return Base64.encodeBase64(buffer);
   }
}
