package com.samsung.magicinfo.webauthor2.repository.model.device;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
public class DeviceData {
  @XmlElement(name = "device_id")
  private String id;
  
  @XmlElement(name = "group_id")
  private String groupId;
  
  @XmlElement(name = "device_model_code")
  private Integer modelCode;
  
  @XmlElement(name = "device_model_name")
  private String modelName;
  
  @XmlElement(name = "device_name")
  private String name;
  
  @XmlElement(name = "screen_size")
  private Integer screenSize;
  
  @XmlElement(name = "location")
  private String location;
  
  @XmlElement(name = "resolution")
  private String resolution;
  
  @XmlElement(name = "device_type")
  private String deviceType;
  
  @XmlElement(name = "device_type_version")
  private String deviceTypeVersion;
  
  public String getId() {
    return this.id;
  }
  
  public void setId(String id) {
    this.id = id;
  }
  
  public String getGroupId() {
    return this.groupId;
  }
  
  public void setGroupId(String groupId) {
    this.groupId = groupId;
  }
  
  public Integer getModelCode() {
    return this.modelCode;
  }
  
  public void setModelCode(Integer modelCode) {
    this.modelCode = modelCode;
  }
  
  public String getModelName() {
    return this.modelName;
  }
  
  public void setModelName(String modelName) {
    this.modelName = modelName;
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public Integer getScreenSize() {
    return this.screenSize;
  }
  
  public void setScreenSize(Integer screenSize) {
    this.screenSize = screenSize;
  }
  
  public String getLocation() {
    return this.location;
  }
  
  public void setLocation(String location) {
    this.location = location;
  }
  
  public String getResolution() {
    return this.resolution;
  }
  
  public void setResolution(String resolution) {
    this.resolution = resolution;
  }
  
  public String getDeviceType() {
    return this.deviceType;
  }
  
  public void setDeviceType(String deviceType) {
    this.deviceType = deviceType;
  }
  
  public String getDeviceTypeVersion() {
    return this.deviceTypeVersion;
  }
  
  public void setDeviceTypeVersion(String deviceTypeVersion) {
    this.deviceTypeVersion = deviceTypeVersion;
  }
  
  public DeviceData(String id, Integer modelCode, String modelName, String name, Integer screenSize, String location, String resolution, String deviceType, String deviceTypeVersion, String groupId) {
    this.id = id;
    this.modelCode = modelCode;
    this.modelName = modelName;
    this.name = name;
    this.screenSize = screenSize;
    this.location = location;
    this.resolution = resolution;
    this.deviceType = deviceType;
    this.deviceTypeVersion = deviceTypeVersion;
    this.groupId = groupId;
  }
  
  public DeviceData() {
    this.modelCode = Integer.valueOf(0);
    this.screenSize = Integer.valueOf(0);
  }
}
