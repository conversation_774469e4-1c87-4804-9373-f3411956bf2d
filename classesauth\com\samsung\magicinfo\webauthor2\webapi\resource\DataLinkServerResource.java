package com.samsung.magicinfo.webauthor2.webapi.resource;

import com.samsung.magicinfo.webauthor2.model.DataLinkServer;
import java.io.Serializable;
import org.springframework.hateoas.Link;
import org.springframework.hateoas.Resource;

public class DataLinkServerResource extends Resource<DataLinkServer> implements Serializable {
  private static final long serialVersionUID = -3130530452548428888L;
  
  public DataLinkServerResource(DataLinkServer content, Link... links) {
    super(content, links);
  }
  
  public DataLinkServerResource(DataLinkServer content, Iterable<Link> links) {
    super(content, links);
  }
}
