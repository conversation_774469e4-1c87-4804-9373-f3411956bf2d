package com.samsung.magicinfo.webauthor2.service.upload;

import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.repository.JobStateRepository;
import com.samsung.magicinfo.webauthor2.repository.model.JobStateResponse;
import com.samsung.magicinfo.webauthor2.service.upload.JobStateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class JobStateServiceImpl implements JobStateService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.upload.JobStateServiceImpl.class);
  
  private JobStateRepository jobStateRepository;
  
  @Autowired
  public JobStateServiceImpl(JobStateRepository jobStateRepository) {
    this.jobStateRepository = jobStateRepository;
  }
  
  public void jobStateSuccess(String userId, String token, String contentId, String versionId, boolean isDuplicate) throws UploaderException {
    JobStateResponse response = this.jobStateRepository.jobStateSuccess(userId, token, contentId, versionId, isDuplicate);
    checkResponse(response);
  }
  
  public void jobStateFail(String userId, String token, String contentId, String versionId) throws UploaderException {
    JobStateResponse response = this.jobStateRepository.jobStateFail(userId, token, contentId, versionId);
    checkResponse(response);
  }
  
  private void checkResponse(JobStateResponse response) throws UploaderException {
    if (response.getStatusCode() == HttpStatus.OK) {
      logger.debug("Response from MIP:{}, url:{}", Integer.valueOf(response.getStatusCode().value()), response.getRedirectUrl());
    } else {
      logger.error("Response from MIP:{}", Integer.valueOf(response.getStatusCode().value()));
      throw new UploaderException(response.getStatusCode().value(), "Fail upload file (Unknow Exception).");
    } 
  }
}
