package com.samsung.magicinfo.framework.setup.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.setup.entity.DatalinkServerEntity;
import com.samsung.magicinfo.framework.setup.entity.DatalinkServerTableEntity;
import com.samsung.magicinfo.restapi.setting.model.V2DatalinkServerTableEntity;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class DatalinkServerDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(DatalinkServerDao.class);

   public DatalinkServerDao() {
      super();
   }

   public Integer addDatalinkServer(DatalinkServerEntity dlsEntity) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).addDatalinkServer(dlsEntity);
   }

   public List getDatalinkTableInfo(String datalinkName, String datalinkServiceName, String datalinkDynaName, String datalinkTableName) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).getDatalinkTableInfo(datalinkName, datalinkServiceName, datalinkDynaName, datalinkTableName);
   }

   public List getDatalinkTableToOrgInfo(String orgId, String datalinkName, String datalinkServiceName, String datalinkDynaName, String datalinkTableName) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).getDatalinkTableToOrgInfo(orgId, datalinkName, datalinkServiceName, datalinkDynaName, datalinkTableName);
   }

   public DatalinkServerTableEntity getTableInfoByDynaNameAndServerName(String dynaName, String serverName) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).getTableInfoByDynaNameAndServerName(dynaName, serverName);
   }

   public Integer addDatalinkTableInfo(DatalinkServerTableEntity dlktEntity) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).addDatalinkTableInfo(dlktEntity);
   }

   public boolean updateDatalinkTableInfo(DatalinkServerTableEntity dlktEntity) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).updateDatalinkTableInfo(dlktEntity);
   }

   public Integer addDatalinkTableToOrg(int orgId, DatalinkServerTableEntity dlktEntity) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).addDatalinkTableToOrg(orgId, dlktEntity);
   }

   public Integer addV2DatalinkTableToOrg(int orgId, V2DatalinkServerTableEntity dlktEntity) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).addV2DatalinkTableToOrg(orgId, dlktEntity);
   }

   public List getSelectedDatalinkServerTableByOrgId(long orgId) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).getSelectedDatalinkServerTableByOrgId(orgId);
   }

   public List getDatalinkServerTableByOrgIdAndServerName(long orgId, String serverName) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).getSelectedDatalinkServerTableByOrgIdAndServerName(orgId, serverName);
   }

   public List getAllSelectedDatalinkServerTableByServer(String serverName) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).getAllSelectedDatalinkServerTableByServer(serverName);
   }

   public List getSelectedDatalinkServerTableByOrgIdAndServer(long orgId, String serverName) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).getSelectedDatalinkServerTableByOrgIdAndServer(orgId, serverName);
   }

   public List getSelectedV2DatalinkServerTableByOrgIdAndServer(long orgId, String serverName) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).getSelectedV2DatalinkServerTableByOrgIdAndServer(orgId, serverName);
   }

   public int updateDatalinkTableToOrgInfo() throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).updateDatalinkTableToOrgInfo();
   }

   public int updateDatalinkServer(DatalinkServerEntity dlsEntity) throws SQLException {
      int retCnt = 0;
      if (dlsEntity != null && dlsEntity.getServer_name() != null) {
         SqlSession session = this.openNewSession(false);

         try {
            retCnt = ((DatalinkServerDaoMapper)this.getMapper(session)).updateDatalinkServer(dlsEntity, dlsEntity.getServer_name());
            if (retCnt <= 0) {
               session.rollback();
               byte var4 = -1;
               return var4;
            }

            session.commit();
         } catch (SQLException var8) {
            this.logger.error("", var8);
         } finally {
            session.close();
         }

         return retCnt;
      } else {
         return retCnt;
      }
   }

   public int deleteDatalinkServer(DatalinkServerEntity dlsEntity) throws SQLException {
      int retDeleteDatalinkServer = false;
      int retDeleteDatalinkServerFromDatalinkMapOrgTable = false;
      int retDeleteDatalinkServerFromDatalinkInfoServerTable = false;
      if (dlsEntity != null && dlsEntity.getServer_name() != null) {
         SqlSession session = this.openNewSession(false);

         byte var6;
         try {
            int retDeleteDatalinkServer = ((DatalinkServerDaoMapper)this.getMapper()).deleteDatalinkServer(dlsEntity);
            int retDeleteDatalinkServerFromDatalinkMapOrgTable = ((DatalinkServerDaoMapper)this.getMapper()).deleteDatalinkServerFromDatalinkMapOrgTable(dlsEntity);
            int retDeleteDatalinkServerFromDatalinkInfoServerTable = ((DatalinkServerDaoMapper)this.getMapper()).deleteDatalinkServerFromDatalinkInfoServerTable(dlsEntity);
            if (retDeleteDatalinkServer < 2) {
               session.commit();
               int var15 = retDeleteDatalinkServerFromDatalinkInfoServerTable + retDeleteDatalinkServerFromDatalinkMapOrgTable + retDeleteDatalinkServer;
               return var15;
            }

            session.rollback();
            var6 = -1;
         } catch (SQLException var10) {
            session.rollback();
            return 0;
         } finally {
            session.close();
         }

         return var6;
      } else {
         return 0;
      }
   }

   public int deleteAllDatalinkInfoServerTable() throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).deleteAllDatalinkInfoServerTable();
   }

   public int deleteAllDatalinkMapOrgTable() throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).deleteAllDatalinkMapOrgTable();
   }

   public int deleteDatalinkInfoServerTableByServer(String serverName) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).deleteDatalinkInfoServerTableByServer(serverName);
   }

   public int deleteDatalinkInfoServerTableByDynaName(String DynaName) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).deleteDatalinkInfoServerTableByDynaName(DynaName);
   }

   public int deleteDatalinkMapOrgTableByServer(String serverName) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).deleteDatalinkMapOrgTableByServer(serverName);
   }

   public int deleteDatalinkMapOrgTableByDynaName(String DynaName) throws SQLException {
      return ((DatalinkServerDaoMapper)this.getMapper()).deleteDatalinkMapOrgTableByDynaName(DynaName);
   }

   public List getAllDatalinkServerTableInfo(String serverName) throws SQLException {
      List object = null;
      object = ((DatalinkServerDaoMapper)this.getMapper()).getAllDatalinkServerTableInfo(serverName);
      return object;
   }

   public List getAllV2DatalinkServerTableInfo(String serverName) throws SQLException {
      List object = null;
      object = ((DatalinkServerDaoMapper)this.getMapper()).getAllV2DatalinkServerTableInfo(serverName);
      return object;
   }

   public List getDatalinkServerListPage(Map map, int startPos, int pageSize) throws SQLException {
      List object = null;
      String searchText = null;
      String sortColumn = null;
      String sortOrder = null;
      --startPos;
      if (map != null) {
         searchText = (String)map.get("searchText");
         sortColumn = map.get("sortColumn") != null ? ((String)map.get("sortColumn")).toUpperCase() : null;
         sortOrder = map.get("sortOrder") != null ? ((String)map.get("sortOrder")).toUpperCase() : null;
      }

      try {
         object = ((DatalinkServerDaoMapper)this.getMapper()).getDatalinkServerListPage(searchText, sortColumn, sortOrder, startPos, pageSize);
      } catch (Exception var9) {
         this.logger.error("", var9);
      }

      return object;
   }

   public int getDatalinkServerListCnt(Map map) throws SQLException {
      int retCnt = -1;
      String searchText = null;
      if (map != null) {
         searchText = (String)map.get("searchText");
      }

      try {
         retCnt = ((DatalinkServerDaoMapper)this.getMapper()).getDatalinkServerListCnt(searchText);
      } catch (Exception var5) {
         this.logger.error("", var5);
      }

      return retCnt;
   }

   public DatalinkServerEntity getDatalinkServerInfo(String serverName) throws SQLException {
      DatalinkServerEntity object = null;

      try {
         object = ((DatalinkServerDaoMapper)this.getMapper()).getDatalinkServerInfo(serverName);
      } catch (Exception var4) {
         this.logger.error("", var4);
      }

      return object;
   }
}
