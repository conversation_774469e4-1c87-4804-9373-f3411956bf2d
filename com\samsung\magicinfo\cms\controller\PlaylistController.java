package com.samsung.magicinfo.cms.controller;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.CommonUtils;
import com.samsung.magicinfo.cms.model.PlaylistFilter;
import com.samsung.magicinfo.cms.model.PlaylistResource;
import com.samsung.magicinfo.cms.model.SyncPlaylistResource;
import com.samsung.magicinfo.cms.service.PlaylistService;
import com.samsung.magicinfo.framework.kpi.annotation.KPI;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import java.sql.SQLException;
import javax.validation.Valid;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(
   value = "Playlist Management System",
   description = "Operations pertaining to Playlist in Playlist Management System",
   tags = {"Playlist API Group"}
)
@RestController
@RequestMapping({"/restapi/v1.0/cms/playlists"})
public class PlaylistController {
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   @Autowired
   private PlaylistService playlistService;

   public PlaylistController() {
      super();
   }

   @ApiOperation(
      value = "get all playlist list",
      notes = "get all playlist list.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {""},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity listAllPlaylist(@RequestParam(value = "startIndex",required = true) int startIndex, @RequestParam(value = "pageSize",required = true) int pageSize) throws Exception {
      this.logger.info("[REST][PLAYLIST][listAllPlaylist] get all playlist list");
      ResponseBody responsebody = new ResponseBody();
      if (!CommonUtils.checkAvailable("playlist")) {
         return new ResponseEntity(responsebody, HttpStatus.METHOD_NOT_ALLOWED);
      } else {
         try {
            PlaylistFilter filter = new PlaylistFilter();
            filter.setStartIndex(startIndex);
            filter.setPageSize(pageSize);
            responsebody = this.playlistService.listPlaylist(filter);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][PLAYLIST][listAllPlaylist] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][PLAYLIST][listAllPlaylist] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.NO_CONTENT);
            }
         } catch (AccessDeniedException var5) {
            this.logger.error("[REST][PLAYLIST][listAllPlaylist] access denied.");
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var5.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var6) {
            this.logger.error("[REST][PLAYLIST][listAllPlaylist] Exception is occured. " + var6.toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @KPI
   @ApiOperation(
      value = "get playlist list by filter",
      notes = "get playlist list by filter.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/filter"},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity listPlaylist(@Valid @RequestBody PlaylistFilter filter, BindingResult result) throws Exception {
      this.logger.info("[REST][PLAYLIST][listPlaylist] get playlist list by filter");
      ResponseBody responsebody = new ResponseBody();
      if (!CommonUtils.checkAvailable("playlist")) {
         return new ResponseEntity(responsebody, HttpStatus.METHOD_NOT_ALLOWED);
      } else if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      } else {
         try {
            responsebody = this.playlistService.listPlaylist(filter);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][PLAYLIST][listPlaylist] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][PLAYLIST][listPlaylist] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.NO_CONTENT);
            }
         } catch (AccessDeniedException var5) {
            this.logger.error("[REST][PLAYLIST][listPlaylist] access denied.");
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var5.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var6) {
            this.logger.error("[REST][PLAYLIST][listPlaylist] Exception is occured. " + var6.toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "GET PLAYLIST INFO",
      notes = "GET PLAYLIST INFO.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{playlistId}"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity getActivePlaylistInfo(@PathVariable String playlistId) throws SQLException {
      this.logger.info("[REST][PLAYLIST][getActivePlaylistInfo][" + playlistId + "] get playlist detail information by playlistId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.playlistService.getActivePlaylistInfo(playlistId);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][PLAYLIST][getActivePlaylistInfo][" + playlistId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][PLAYLIST][getActivePlaylistInfo][" + playlistId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][PLAYLIST][getActivePlaylistInfo][" + playlistId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][PLAYLIST][getActivePlaylistInfo][" + playlistId + "] Exception is occured. " + var5.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @KPI
   @ApiOperation(
      value = "create playlist",
      notes = "create new playlist.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {""},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity createPlaylist(@Valid @RequestBody PlaylistResource params, BindingResult result) throws SQLException {
      this.logger.info("[REST][PLAYLIST][createPlaylist] create the playlist");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      } else {
         try {
            responsebody = this.playlistService.createPlaylist(params);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][PLAYLIST][createPlaylist] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.CREATED);
            } else {
               this.logger.error("[REST][PLAYLIST][createPlaylist] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var5) {
            this.logger.error("[REST][PLAYLIST][createPlaylist] access denied.");
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var5.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var6) {
            this.logger.error("[REST][PLAYLIST][createPlaylist] Exception is occured. " + var6.toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @KPI
   @ApiOperation(
      value = "edit playlist",
      notes = "edit selected playlist.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{playlistId}"},
      method = {RequestMethod.PUT},
      produces = {"application/json"}
   )
   public ResponseEntity editPlaylist(@PathVariable("playlistId") String playlistId, @Valid @RequestBody PlaylistResource params, BindingResult result) throws SQLException {
      this.logger.info("[REST][PLAYLIST][editPlaylist][" + playlistId + "] edit the playlist by playlistId");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      } else {
         try {
            responsebody = this.playlistService.editPlaylist(playlistId, params);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][PLAYLIST][editPlaylist][" + playlistId + "] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][PLAYLIST][editPlaylist][" + playlistId + "] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var6) {
            this.logger.error("[REST][PLAYLIST][editPlaylist][" + playlistId + "] access denied.");
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var7) {
            this.logger.error("[REST][PLAYLIST][editPlaylist][" + playlistId + "] Exception is occured. " + var7.toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var7.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "copy playlist",
      notes = "copy selected playlist.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{playlistId}/copy"},
      method = {RequestMethod.PUT},
      produces = {"application/json"}
   )
   public ResponseEntity copyPlaylist(@PathVariable String playlistId, @Valid @RequestBody PlaylistResource params, BindingResult result) throws SQLException {
      this.logger.info("[REST][PLAYLIST][copyPlaylist][" + playlistId + "] copy the playlist by playlistId");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      } else {
         try {
            responsebody = this.playlistService.copyPlaylist(playlistId, params);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][PLAYLIST][copyPlaylist][" + playlistId + "] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.CREATED);
            } else {
               this.logger.error("[REST][PLAYLIST][copyPlaylist][" + playlistId + "] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var6) {
            this.logger.error("[REST][PLAYLIST][copyPlaylist][" + playlistId + "] access denied.");
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var7) {
            this.logger.error("[REST][PLAYLIST][copyPlaylist][" + playlistId + "] Exception is occured. " + var7.toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var7.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "get playlist information for dashboard",
      notes = "get playlist information for dashboard(totalCount, usedCount).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/dashboard"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity getDashboardPlaylistInfo() throws Exception {
      this.logger.info("[REST][PLAYLIST][getDashboardPlaylistInfo] get playlist information used in the dashboard");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.playlistService.getDashboardPlaylistInfo();
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][PLAYLIST][getDashboardPlaylistInfo] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][PLAYLIST][getDashboardPlaylistInfo] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         this.logger.error("[REST][PLAYLIST][getDashboardPlaylistInfo] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var3.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         this.logger.error("[REST][PLAYLIST][getDashboardPlaylistInfo] Exception is occured. " + var4.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "delete playlist",
      notes = "delete selected playlist.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{playlistId}"},
      method = {RequestMethod.DELETE},
      produces = {"application/json"}
   )
   public ResponseEntity deletePlaylist(@PathVariable String playlistId, @RequestParam(value = "permanently",required = false) Boolean permanently, @RequestParam(value = "force",required = false) Boolean force) throws SQLException {
      this.logger.info("[REST][PLAYLIST][deletePlaylist][" + playlistId + "] delete playlist by playlistId");
      ResponseBody responsebody = new ResponseBody();

      try {
         if (permanently != null && permanently) {
            responsebody = this.playlistService.permanentlyDeletePlaylist(playlistId, force);
         } else {
            responsebody = this.playlistService.deletePlaylist(playlistId, force);
         }

         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][PLAYLIST][deletePlaylist][" + playlistId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][PLAYLIST][deletePlaylist][" + playlistId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var6) {
         this.logger.error("[REST][PLAYLIST][deletePlaylist][" + playlistId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var6.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var7) {
         this.logger.error("[REST][PLAYLIST][deletePlaylist][" + playlistId + "] Exception is occured. " + var7.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var7.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "create syncplaylist",
      notes = "create syncplaylist.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/syncplay"},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity createSyncPlaylist(@Valid @RequestBody SyncPlaylistResource params, BindingResult result) throws SQLException {
      this.logger.info("[REST][PLAYLIST][createSyncPlaylist] create the playlist");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      } else {
         try {
            responsebody = this.playlistService.createSyncPlaylist(params);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][PLAYLIST][createSyncPlaylist] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.CREATED);
            } else {
               this.logger.error("[REST][PLAYLIST][createSyncPlaylist] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var5) {
            this.logger.error("[REST][PLAYLIST][createSyncPlaylist] access denied.");
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var5.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var6) {
            this.logger.error("[REST][PLAYLIST][createSyncPlaylist] Exception is occured. " + var6.toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "update syncplaylist",
      notes = "update syncplaylist.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/syncplay/{playlistId}"},
      method = {RequestMethod.PUT},
      produces = {"application/json"}
   )
   public ResponseEntity updateSyncPlaylist(@PathVariable String playlistId, @Valid @RequestBody SyncPlaylistResource params, BindingResult result) throws SQLException {
      this.logger.info("[REST][PLAYLIST][editSyncPlaylist] create the playlist");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      } else {
         try {
            responsebody = this.playlistService.updateSyncPlaylist(playlistId, params);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][PLAYLIST][editSyncPlaylist] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.CREATED);
            } else {
               this.logger.error("[REST][PLAYLIST][editSyncPlaylist] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var6) {
            this.logger.error("[REST][PLAYLIST][editSyncPlaylist] access denied.");
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var7) {
            this.logger.error("[REST][PLAYLIST][editSyncPlaylist] Exception is occured. " + var7.toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var7.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }
}
