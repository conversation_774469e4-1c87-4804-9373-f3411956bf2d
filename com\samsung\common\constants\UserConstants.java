package com.samsung.common.constants;

public class UserConstants {
   public static final String USER_APPROVAL_TYPE_NEW = "TEXT_NEW_P";
   public static final String USER_APPROVAL_TYPE_MOVE_ORGAN = "TEXT_MOVE_ORGAN_P";
   public static final String USER_APPROVAL_TYPE_MOVE_RE_SIGN = "TEXT_RE_SIGN_P";
   public static final String RESET_PASSWORD_HASH_VALUE = "RESET_PASSWORD_HASH_VALUE";
   public static final String RESET_PASSWORD_SETTING_TIME = "RESET_PASSWORD_SETTING_TIME";
   public static final String RESET_PASSWORD_AUTHENTICATED_FLAG = "RESET_PASSWORD_AUTHENTICATED_FLAG";
   public static final String RESET_PASSWORD_MAP = "RESET_PASSWORD_MAP";
   public static final String RESET_OTP_MAP = "RESET_OTP_MAP";
   public static final String USER_ID = "userId";
   public static final String DEVICE_READ_AUTHORITY = "Device Read Authority";
   public static final String RESET_OTP = "RESET_OTP";
   public static final String RESET_PASSWORD = "RESET_PASSWORD";
   public static final String _MAP = "_MAP";
   public static final String OTP = "OTP";
   public static final String _HASH_VALUE = "_HASH_VALUE";
   public static final String _SETTING_TIME = "_SETTING_TIME";
   public static final String _AUTHENTICATED_FLAG = "_AUTHENTICATED_FLAG";
   public static final String INIT_TOTP = "INIT_TOTP";
   public static final String INIT_HOTP = "INIT_HOTP";
   public static final String AUTH_CODE = "AUTH_CODE";

   public UserConstants() {
      super();
   }

   public static String getSID(String approvalType) {
      byte var3 = -1;
      switch(approvalType.hashCode()) {
      case -2138037018:
         if (approvalType.equals("TEXT_MOVE_ORGAN_P")) {
            var3 = 1;
         }
         break;
      case -704240705:
         if (approvalType.equals("TEXT_NEW_P")) {
            var3 = 0;
         }
         break;
      case 1044933960:
         if (approvalType.equals("TEXT_RE_SIGN_P")) {
            var3 = 2;
         }
      }

      String sid;
      switch(var3) {
      case 0:
         sid = "TEXT_NEW_P";
         break;
      case 1:
         sid = "TEXT_MOVE_ORGAN_P";
         break;
      case 2:
         sid = "TEXT_RE_SIGN_P";
         break;
      default:
         sid = "";
      }

      return sid;
   }
}
