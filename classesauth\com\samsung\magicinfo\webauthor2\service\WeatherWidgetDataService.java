package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.weather.CityData;
import com.samsung.magicinfo.webauthor2.model.weather.Country;
import com.samsung.magicinfo.webauthor2.model.weather.Language;
import java.util.List;

public interface WeatherWidgetDataService {
  List<Language> getLanguageList();
  
  List<Country> getCountryList(String paramString);
  
  List<CityData> getCityList(int paramInt, String paramString);
}
