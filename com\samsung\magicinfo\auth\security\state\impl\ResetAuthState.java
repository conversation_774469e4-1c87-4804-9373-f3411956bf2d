package com.samsung.magicinfo.auth.security.state.impl;

import com.samsung.magicinfo.auth.security.AuthResource;
import com.samsung.magicinfo.auth.security.otp.OTPAuthType;
import com.samsung.magicinfo.auth.security.state.AuthState;

public class ResetAuthState implements AuthState {
   public ResetAuthState() {
      super();
   }

   public OTPAuthType auth(AuthResource resource) {
      return null;
   }
}
