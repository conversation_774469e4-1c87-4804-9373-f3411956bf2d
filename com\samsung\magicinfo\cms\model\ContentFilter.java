package com.samsung.magicinfo.cms.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@JsonInclude(Include.NON_NULL)
public class ContentFilter {
   @ApiModelProperty(
      example = "1"
   )
   private int startIndex = 1;
   @ApiModelProperty(
      example = "10"
   )
   private int pageSize = 10;
   @ApiModelProperty(
      example = "last_modified_date"
   )
   @Pattern(
      regexp = "last_modified_date",
      message = "[ContentFilter][sortColumn] Only last_modified_date are available."
   )
   private String sortColumn = "last_modified_date";
   @ApiModelProperty(
      example = "desc"
   )
   @Pattern(
      regexp = "desc|asc",
      message = "[ContentFilter][sortOrder] Only asc, desc are available."
   )
   private String sortOrder = "desc";
   @ApiModelProperty(
      example = "ALL"
   )
   private String groupType = "ALL";
   @ApiModelProperty(
      example = "IMAGE"
   )
   private String mediaType = "";
   @ApiModelProperty(
      example = "0"
   )
   private String groupId = "0";
   @ApiModelProperty(
      example = "test"
   )
   @Size(
      max = 20,
      message = "[ContentFilter][searchText] max size is 20."
   )
   private String searchText = "";
   @ApiModelProperty(
      example = "32FA85B4-2389-476C-845A-0FC6F1D10D81"
   )
   @Pattern(
      regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$",
      message = "[ContentFilter][selectId] Not UUID pattern."
   )
   private String selectId;
   @ApiModelProperty(
      example = "IMAGE"
   )
   private String mediaTypeFilter = "";
   @ApiModelProperty(
      example = "2000-01-01"
   )
   private String endDate = "";
   @ApiModelProperty(
      example = "2000-01-01"
   )
   private String startDate = "";
   @ApiModelProperty(
      example = "admin"
   )
   @Size(
      max = 64,
      message = "[ContentFilter][userId] max size is 64."
   )
   private String userId = "admin";
   @ApiModelProperty(
      example = "iPLAYER"
   )
   private String deviceType;
   @ApiModelProperty(
      example = "3.0"
   )
   private String deviceTypeVersion;
   @ApiModelProperty(
      example = "0"
   )
   private String category;
   @ApiModelProperty(
      example = "ID or NAME"
   )
   private String tagInputType;
   private String tag;
   private String isMain;
   @ApiModelProperty(
      example = "true or false"
   )
   private String isUsedContent;
   @ApiModelProperty(
      example = "true or false"
   )
   private String isThumbnail;
   @ApiModelProperty(
      example = "2000-01-01"
   )
   private String createdDateFrom = "";
   @ApiModelProperty(
      example = "2000-01-01"
   )
   private String createdDateTo = "";

   public String getCreatedDateFrom() {
      return this.createdDateFrom;
   }

   public void setCreatedDateFrom(String createdDateFrom) {
      this.createdDateFrom = createdDateFrom;
   }

   public String getCreatedDateTo() {
      return this.createdDateTo;
   }

   public void setCreatedDateTo(String createdDateTo) {
      this.createdDateTo = createdDateTo;
   }

   public ContentFilter() {
      super();
   }

   public String getTagInputType() {
      return this.tagInputType;
   }

   public void setTagInputType(String tagInputType) {
      this.tagInputType = tagInputType;
   }

   public String getTag() {
      return this.tag;
   }

   public void setTag(String tag) {
      this.tag = tag;
   }

   public String getCategory() {
      return this.category;
   }

   public void setCategory(String category) {
      this.category = category;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public void setDeviceType(String deviceType) {
      this.deviceType = deviceType;
   }

   public String getDeviceTypeVersion() {
      return this.deviceTypeVersion;
   }

   public void setDeviceTypeVersion(String deviceTypeVersion) {
      this.deviceTypeVersion = deviceTypeVersion;
   }

   public int getStartIndex() {
      return this.startIndex;
   }

   public void setStartIndex(int startIndex) {
      this.startIndex = startIndex;
   }

   public int getPageSize() {
      return this.pageSize;
   }

   public void setPageSize(int pageSize) {
      this.pageSize = pageSize;
   }

   public String getSortColumn() {
      return this.sortColumn;
   }

   public void setSortColumn(String sortColumn) {
      this.sortColumn = sortColumn;
   }

   public String getSortOrder() {
      return this.sortOrder;
   }

   public void setSortOrder(String sortOrder) {
      this.sortOrder = sortOrder;
   }

   public String getGroupType() {
      return this.groupType;
   }

   public void setGroupType(String groupType) {
      this.groupType = groupType;
   }

   public String getMediaType() {
      return this.mediaType;
   }

   public void setMediaType(String mediaType) {
      this.mediaType = mediaType;
   }

   public String getGroupId() {
      return this.groupId;
   }

   public void setGroupId(String groupId) {
      this.groupId = groupId;
   }

   public String getSearchText() {
      return this.searchText;
   }

   public void setSearchText(String searchText) {
      this.searchText = searchText;
   }

   public String getSelectId() {
      return this.selectId;
   }

   public void setSelectId(String selectId) {
      this.selectId = selectId;
   }

   public String getMediaTypeFilter() {
      return this.mediaTypeFilter;
   }

   public void setMediaTypeFilter(String mediaTypeFilter) {
      this.mediaTypeFilter = mediaTypeFilter;
   }

   public String getEndDate() {
      return this.endDate;
   }

   public void setEndDate(String endDate) {
      this.endDate = endDate;
   }

   public String getStartDate() {
      return this.startDate;
   }

   public void setStartDate(String startDate) {
      this.startDate = startDate;
   }

   public String getUserId() {
      return this.userId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }

   public String getIsMain() {
      return this.isMain;
   }

   public void setIsMain(String isMain) {
      this.isMain = isMain;
   }

   public String getIsUsedContent() {
      return this.isUsedContent;
   }

   public void setIsUsedContent(String isUsedContent) {
      this.isUsedContent = isUsedContent;
   }

   public String getIsThumbnail() {
      return this.isThumbnail;
   }

   public void setIsThumbnail(String isThumbnail) {
      this.isThumbnail = isThumbnail;
   }
}
