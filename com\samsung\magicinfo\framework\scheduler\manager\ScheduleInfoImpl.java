package com.samsung.magicinfo.framework.scheduler.manager;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.RequestUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.monitoring.manager.DownloadStatusInfo;
import com.samsung.magicinfo.framework.monitoring.manager.DownloadStatusInfoImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistInterface;
import com.samsung.magicinfo.framework.ruleset.entity.RuleSet;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfo;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfoImpl;
import com.samsung.magicinfo.framework.ruleset.manager.RulesetUtils;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAO;
import com.samsung.magicinfo.framework.scheduler.entity.ChannelEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.DetailDownloadContentEntity;
import com.samsung.magicinfo.framework.scheduler.entity.DynamicTagEntity;
import com.samsung.magicinfo.framework.scheduler.entity.FrameEntity;
import com.samsung.magicinfo.framework.scheduler.entity.FrameTemplateEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramFile;
import com.samsung.magicinfo.framework.scheduler.entity.ScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.SyncSchedule;
import com.samsung.magicinfo.framework.setup.manager.TagInfo;
import com.samsung.magicinfo.framework.setup.manager.TagInfoImpl;
import com.samsung.magicinfo.openapi.impl.OpenApiExceptionCode;
import com.samsung.magicinfo.openapi.impl.OpenApiServiceException;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.reservation.ReservationManager;
import com.samsung.magicinfo.protocol.reservation.ReservationManagerImpl;
import com.samsung.magicinfo.protocol.reservation.ScheduleReservation;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;
import org.quartz.CronScheduleBuilder;
import org.quartz.CronTrigger;
import org.quartz.JobDetail;
import org.quartz.Scheduler;
import org.quartz.TriggerBuilder;
import org.springframework.context.support.ResourceBundleMessageSource;

public class ScheduleInfoImpl implements ScheduleInfo {
   Logger logger = LoggingManagerV2.getLogger(ScheduleInfoImpl.class);
   static ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
   private static volatile ScheduleInfo sInfo = null;
   private ScheduleInfoDAO dao;

   private ScheduleInfoImpl(SqlSession session) {
      super();
      if (this.dao == null) {
         this.dao = new ScheduleInfoDAO(session);
      }

   }

   public static ScheduleInfo getInstance() {
      if (sInfo == null) {
         Class var0 = ScheduleInfoImpl.class;
         synchronized(ScheduleInfoImpl.class) {
            if (sInfo == null) {
               sInfo = new ScheduleInfoImpl((SqlSession)null);
            }
         }
      }

      return sInfo;
   }

   public static ScheduleInfo getInstance(SqlSession session) {
      return new ScheduleInfoImpl(session);
   }

   public boolean addFrame(FrameEntity frame) throws SQLException {
      return this.dao.addFrame(frame);
   }

   public boolean addDeviceGroupMappedInProgram(String program_id, String device_group_id) throws SQLException {
      return this.dao.addDeviceGroupMappedInProgram(program_id, device_group_id);
   }

   public boolean addDeviceGroupMappedInProgramTemp(String program_id, String device_group_id, String device_groups, String default_content, String bgm_content_id) throws SQLException {
      return this.dao.addDeviceGroupMappedInProgramTemp(program_id, device_group_id, device_groups, default_content, bgm_content_id);
   }

   public boolean deleteDeviceGroupMappedInProgramTempByProgramId(String program_id) throws SQLException {
      return this.dao.deleteDeviceGroupMappedInProgramTempByProgramId(program_id);
   }

   public boolean deleteDeviceGroupMappedInProgramByProgramId(String program_id) throws SQLException {
      return this.dao.deleteDeviceGroupMappedInProgramByProgramId(program_id);
   }

   public boolean setDefaultProgramId(long groupId, String programId) throws SQLException {
      return this.dao.setDefaultProgramId(groupId, programId);
   }

   public List getDeviceGroupMappedInProgramTemp(String program_id) throws SQLException {
      return this.dao.getDeviceGroupMappedInProgramTemp(program_id);
   }

   public List getDeviceGroupMappedInProgram(String program_id) throws SQLException {
      return this.dao.getDeviceGroupMappedInProgram(program_id);
   }

   public List getDevicesMappedInProgram(String program_id) throws SQLException {
      return this.dao.getDevicesMappedInProgram(program_id);
   }

   public boolean addProgram(ProgramEntity program, String sessionId) throws ConfigException, SQLException {
      return this.dao.addProgram(program, sessionId);
   }

   public boolean addNewVersionProgram(ProgramEntity program, String sessionId, String ipAddress) throws ConfigException, SQLException {
      return this.dao.addNewVersionProgram(program, sessionId, ipAddress);
   }

   public List getAuthorityFromFrame(String programId, String frameId) {
      return this.dao.getAuthorityFromFrame(programId, frameId);
   }

   public boolean addNewADProgram(ProgramEntity program, String sessionId, String ipAddress) throws ConfigException, SQLException {
      return this.dao.addNewADProgram(program, sessionId, ipAddress);
   }

   public boolean updateProgram(ProgramEntity program, String sessionId) throws SQLException, ConfigException {
      return this.dao.updateProgram(program, sessionId);
   }

   public boolean updateProgramView(ProgramEntity program) throws SQLException {
      return this.dao.updateProgramView(program);
   }

   public boolean updateNewVersionProgram(ProgramEntity program, String sessionId, String ipAddress) throws SQLException {
      return this.dao.updateNewVersionProgram(program, sessionId, ipAddress);
   }

   public boolean updateADNewVersionProgram(ProgramEntity program, String sessionId, String ipAddress) throws SQLException {
      return this.dao.updateADNewVersionProgram(program, sessionId, ipAddress);
   }

   public boolean addContentSchedule(ContentsScheduleEntity schedule) throws ConfigException, SQLException {
      return this.dao.addContentSchedule(schedule);
   }

   public boolean addSyncMatchInfoTemp(SyncSchedule syncschedule) throws SQLException {
      return this.dao.addSyncMatchInfoTemp(syncschedule);
   }

   public int countSyncScheduleMatchInfoTemp(String scheduleId) throws SQLException {
      return this.dao.countSyncScheduleMatchInfoTemp(scheduleId);
   }

   public int countSyncScheduleMatchInfo(String scheduleId) throws SQLException {
      return this.dao.countSyncScheduleMatchInfo(scheduleId);
   }

   public boolean deleteSyncScheduleMatchInfoTemp(String scheduleId) throws SQLException {
      return this.dao.deleteSyncScheduleMatchInfoTemp(scheduleId);
   }

   public boolean deleteSyncScheduleMatchInfo(String scheduleId) throws SQLException {
      return this.dao.deleteSyncScheduleMatchInfo(scheduleId);
   }

   public List getSyncScheduleMatchInfoTemp(String scheduleId) throws SQLException {
      return this.dao.getSyncScheduleMatchInfoTemp(scheduleId);
   }

   public List getSyncScheduleMatchInfo(String scheduleId) throws SQLException {
      return this.dao.getSyncScheduleMatchInfo(scheduleId);
   }

   public boolean addSyncMatchInfo(String scheduleId) throws SQLException {
      return this.dao.addSyncMatchInfo(scheduleId);
   }

   public List getSyncGroupListPerSchedule(String scheduleId) throws SQLException {
      return this.dao.getSyncGroupListPerSchedule(scheduleId);
   }

   public List getSyncDeviceIdListPerSchedule(String scheduleId, String playlistId, String syncPlayId) throws SQLException {
      return this.dao.getSyncDeviceIdListPerSchedule(scheduleId, playlistId, syncPlayId);
   }

   public boolean updateContentSchedule(ContentsScheduleEntity schedule) throws SQLException, ConfigException {
      return this.dao.updateContentSchedule(schedule);
   }

   public boolean addPanelOrZeroFrameSchedule(ScheduleEntity schedule) throws ConfigException, SQLException {
      return this.dao.addPanelOrZeroFrameSchedule(schedule);
   }

   public boolean updatePanelOrZeroFrameSchedule(ScheduleEntity schedule) throws ConfigException, SQLException {
      return this.dao.updatePanelOrZeroFrameSchedule(schedule);
   }

   public boolean deleteTempSchedule(String programId, String scheduleId) throws ConfigException, SQLException {
      return this.dao.deleteTempSchedule(programId, scheduleId);
   }

   public boolean deleteTempScheduleByChannelNo(String programId, String sessionId, int channelNo) throws ConfigException, SQLException {
      return this.dao.deleteTempScheduleByChannelNo(programId, sessionId, channelNo);
   }

   public boolean deleteProgram(String programId) throws SQLException {
      return this.dao.deleteProgram(programId);
   }

   public boolean deleteSchedule(String sessionId, String scheduleId) throws SQLException {
      return this.dao.deleteSchedule(sessionId, scheduleId);
   }

   public boolean deleteTempFrame(String programId, String session_id) throws SQLException, ConfigException {
      return this.dao.deleteTempFrame(programId, session_id);
   }

   public boolean deleteTempFrameByChannelNo(String programId, String session_id, int channelNo) throws SQLException, ConfigException {
      return this.dao.deleteTempFrameByChannelNo(programId, session_id, channelNo);
   }

   public boolean deleteTempScheduleForFrameIndex(String programId, String sessionId, int channel_no, int frameIndex) throws SQLException {
      return this.dao.deleteTempScheduleForFrameIndex(programId, sessionId, channel_no, frameIndex);
   }

   public boolean deleteProgramTempData(String programId, String sessionId) throws SQLException {
      return this.dao.deleteProgramTempData(programId, sessionId);
   }

   public boolean deleteProgramData(String programId) throws SQLException {
      return this.dao.deleteProgramData(programId);
   }

   public boolean deleteProgramTempDataWithSession(String sessionId) throws SQLException {
      return this.dao.deleteProgramTempDataWithSession(sessionId);
   }

   public boolean deleteProgramTempDataWithId(String programId) throws SQLException {
      return this.dao.deleteProgramTempDataWithId(programId);
   }

   public boolean deleteAllProgramTempData() throws SQLException {
      return this.dao.deleteAllProgramTempData();
   }

   public List getFrames(String programId, int channelNo, int screenIndex) throws SQLException {
      return this.dao.getFrames(programId, channelNo, screenIndex);
   }

   public List getFrames(String programId, int channelNo) throws SQLException {
      return this.dao.getFrames(programId, channelNo);
   }

   public List getFramesInfo(String programId, int screenIndex) throws SQLException {
      return this.dao.getFramesInfo(programId, screenIndex);
   }

   public List getTempFrames(String programId, int channelNo, String session_id) throws SQLException {
      return this.dao.getTempFrames(programId, channelNo, session_id);
   }

   public FrameEntity getFrameData(String programId, int frame_index) throws SQLException {
      return this.dao.getFrameData(programId, frame_index);
   }

   public List getFrameCount(String programId) throws SQLException {
      return this.dao.getFrameCount(programId);
   }

   public FrameEntity getFrameData(String programId, int frame_index, int channelNo) throws SQLException {
      return this.dao.getFrameData(programId, frame_index, channelNo);
   }

   public String getBGMContentName(String programId) throws SQLException {
      return this.dao.getBGMContentName(programId);
   }

   public Map getDeviceGroupIdsAndName(String programId) throws SQLException {
      return this.dao.getDeviceGroupIdsAndName(programId);
   }

   public List getProgramGroupIdAndName(String programId) throws SQLException {
      return this.dao.getProgramGroupIdAndName(programId);
   }

   public long getTempFrameCount(String programId, String session_id) throws SQLException {
      return this.dao.getTempFrameCount(programId, session_id);
   }

   public long getTempFrameCount(String programId, int channelNo, String session_id) throws SQLException {
      return this.dao.getTempFrameCount(programId, channelNo, session_id);
   }

   public long getTempFrameCount(String programId) throws SQLException {
      return this.dao.getTempFrameCount(programId);
   }

   public long getTempFrameCountForSession(String session_id) throws SQLException {
      return this.dao.getTempFrameCountForSession(session_id);
   }

   public ProgramEntity getProgramWithGroupIdAndNameByProgramId(String programId) throws SQLException {
      return this.dao.getProgramWithGroupIdAndNameByProgramId(programId);
   }

   public ProgramEntity getProgram(String programId) throws SQLException {
      ScheduleInfo scheduleInfo = getInstance();
      ProgramEntity programEntity = this.dao.getProgram(programId);
      if (programEntity == null) {
         return null;
      } else {
         List list = scheduleInfo.getProgramGroupIdAndName(programId);
         Long program_group_id = 0L;
         String program_group_name = "";
         if (list != null && list.size() != 0) {
            Map p = (Map)list.get(0);
            program_group_id = (Long)p.get("group_id");
            program_group_name = (String)p.get("group_name");
         }

         programEntity.setProgram_group_id(program_group_id);
         programEntity.setProgram_group_name(program_group_name);
         return programEntity;
      }
   }

   public String getProgramName(String programId) throws SQLException {
      return this.dao.getProgramName(programId);
   }

   public List getContentSchedules(String programId, int channelNo, int screenIndex, int frameIndex) throws SQLException {
      return this.dao.getContentSchedules(programId, channelNo, screenIndex, frameIndex);
   }

   public List getContentListFromProgramidandChannel(String programId, int channelNo, int screenIndex, int frameIndex) throws SQLException {
      return this.dao.getContentListFromProgramidandChannel(programId, channelNo, screenIndex, frameIndex);
   }

   public List getContentSchedules(String programId, int channelNo) throws SQLException {
      return this.dao.getContentSchedules(programId, channelNo);
   }

   public List getContentSchedules(String programId) throws SQLException {
      return this.dao.getContentSchedules(programId);
   }

   public List getPanelOrZeroFrameSchedules(String programId, String scheduleType) throws SQLException {
      return this.dao.getPanelOrZeroFrameSchedules(programId, scheduleType);
   }

   public List getPanelOrZeroFrameTempSchedules(String programId, String scheduleType) throws SQLException {
      return this.dao.getPanelOrZeroFrameTempSchedules(programId, scheduleType);
   }

   public List selAllScheduleByMonth(Map pmap) throws SQLException {
      return this.dao.selAllScheduleByMonth(pmap);
   }

   public List selAllScheduleByWeek(Map pmap) throws SQLException {
      return this.dao.selAllScheduleByWeek(pmap);
   }

   public List selAllScheduleByDay(Map pmap) throws SQLException {
      return this.dao.selAllScheduleByDay(pmap);
   }

   public ContentsScheduleEntity getScheduleData(Map pmap) throws SQLException, ConfigException {
      return this.dao.getScheduleData(pmap);
   }

   public boolean setProgram(String sessionId, ProgramEntity program) throws ConfigException, SQLException {
      return this.dao.setProgram(sessionId, program);
   }

   public boolean transferProgramDataToMain(String programId, String sessionId, String userId) throws SQLException, ConfigException {
      return this.dao.transferProgramDataToMain(programId, sessionId, userId);
   }

   public boolean transferProgramDataToTemp(String programId, String sessionId) throws SQLException, ConfigException {
      return this.dao.transferProgramDataToTemp(programId, sessionId);
   }

   public boolean transferSyncScheduleMatchInfoToTemp(String scheduleId, String sessionId) throws SQLException {
      return this.dao.transferSyncScheduleMatchInfoToTemp(scheduleId, sessionId);
   }

   public boolean transferScheduleDataToTempWithNewId(String newProgramId, String programId, String sessionId) throws SQLException, ConfigException {
      return this.dao.transferScheduleDataToTempWithNewId(newProgramId, programId, sessionId);
   }

   public boolean transferScheduleDataToTempWithNewIdForImport(String programId, String schedule_id, String sessionId, String frame_index, int channel_no) throws SQLException, ConfigException {
      return this.dao.transferScheduleDataToTempWithNewIdForImport(programId, schedule_id, sessionId, frame_index, channel_no);
   }

   public long getProgramVersion(String program_id) throws SQLException {
      return this.dao.getProgramVersion(program_id);
   }

   public PagedListInfo getPagedList(int startPos, int pageSize, Map condition, String section) throws Exception {
      PagedListInfo pageListInfo = null;
      if (section != null && section.equals("PROGRAM_LIST")) {
         pageListInfo = this.dao.getProgramList(condition, startPos, pageSize);
      } else if (section != null && section.equals("getScheduleSearchList")) {
         int totCount = this.dao.getScheduleListCnt(condition);
         List retList = this.dao.getScheduleListPage(condition, startPos, pageSize);
         pageListInfo = new PagedListInfo(retList, totCount);
      } else if (section != null && section.equals("getDownloadContentListForContentSchedule")) {
         pageListInfo = this.dao.getDownloadContentPagedListForContentSchedule(startPos, pageSize, condition);
      } else if (section != null && section.equals("getDownloadStatus")) {
         pageListInfo = this.dao.getDownloadStatus(startPos, pageSize, condition);
      } else if (section != null && section.equals("getDownloadContentListForEventSchedule")) {
         pageListInfo = this.dao.getDownloadContentPagedListForEventSchedule(startPos, pageSize, condition);
      } else if (section != null && section.equals("getEventList")) {
         pageListInfo = this.dao.getEventList(startPos, pageSize, condition);
      } else if (section != null && section.equals("getDownloadContentList")) {
         pageListInfo = this.dao.getDownloadContentList(startPos, pageSize, condition);
      }

      return pageListInfo;
   }

   public boolean checkDeviceMapping(String program_id, String device_group_ids) throws Exception {
      String[] dGroupIdsArray = device_group_ids.split(",");
      List prev_dev_map_list = this.dao.getDeviceProgramMapList(program_id);
      if (prev_dev_map_list != null && prev_dev_map_list.size() > 0) {
         for(int i = 0; i < prev_dev_map_list.size(); ++i) {
            Map dev_map = (Map)prev_dev_map_list.get(i);
            boolean match = false;
            String prev_dev_id = "" + dev_map.get("device_group_id");
            if (!device_group_ids.equals("")) {
               for(int count = 0; count < dGroupIdsArray.length; ++count) {
                  if (dGroupIdsArray[count].equals(prev_dev_id)) {
                     match = true;
                  }
               }
            }

            if (!match) {
               this.dao.mapDeviceGroupWithDefault((Long)dev_map.get("device_group_id"), (String)dev_map.get("default_program_id"));
               ProgramEntity program = this.getProgram((String)dev_map.get("default_program_id"));
               Map m = this.getDeviceGroupIdsAndName((String)dev_map.get("default_program_id"));
               program.setDevice_group_ids((String)m.get("device_group_ids"));
               this.reserveSchedule(program);
            }
         }
      }

      return true;
   }

   public boolean changeDefaultProgramName(String dev_group_name, String programId) throws SQLException {
      String program_name = dev_group_name + "_default";
      return this.dao.updateProgramName(program_name, programId);
   }

   public String createDefaultProgram(String dev_group_name, String dev_group_id, String user_id) throws SQLException, ConfigException, Exception {
      boolean ret = false;
      ProgramEntity program = new ProgramEntity();
      String program_id = UUID.randomUUID().toString();
      String program_name = dev_group_name + "_default";
      program.setProgram_id(program_id);
      program.setVersion(1L);
      program.setSynchronization("0");
      program.setResume("0");
      program.setProgram_name(program_name);
      program.setDeploy_time("null");
      program.setSchedult_limit(0L);
      program.setIs_default("Y");
      program.setIs_media_merchant("N");
      program.setScreen_count(1L);
      program.setIs_bgm_with_content("N");
      program.setDescription("Default Schedule");
      program.setAspect_ratio("16:9");
      program.setResolution("1280*720");
      program.setFrame_layout_type("fixed");
      program.setProgram_type("LFD");
      program.setDeleted("N");
      program.setDevice_group_ids(dev_group_id);
      program.setDevice_groups(dev_group_name);
      program.setUser_id(user_id);
      ChannelEntity ce = new ChannelEntity();
      ce.setChannel_no(1);
      ce.setProgram_id(program_id);
      FrameEntity fe = new FrameEntity();
      fe.setProgram_id(program_id);
      fe.setChannel_no(1);
      fe.setScreen_index(0);
      fe.setFrame_id(UUID.randomUUID().toString());
      fe.setFrame_index(0);
      fe.setIs_main_frame("Y");
      fe.setX(0.0D);
      fe.setY(0.0D);
      fe.setWidth(100.0D);
      fe.setHeight(100.0D);
      fe.setLine_data("ZeroFrameOnly");
      fe.setVersion(1L);
      fe.setFrame_name("Frame 0");
      ret = this.dao.addDefaultProgram(program, ce, fe, RequestUtils.getIpAddress());
      if (!ret) {
         return null;
      } else {
         this.reserveSchedule(program);
         return program_id;
      }
   }

   private boolean isReservedSchedule(String deployTime) {
      return deployTime != null && !"null".equalsIgnoreCase(deployTime) && !deployTime.equals("");
   }

   public boolean reserveSchedule(ProgramEntity program) throws Exception {
      return this.reserveSchedule(program, (SqlSession)null);
   }

   public boolean reserveSchedule(ProgramEntity program, SqlSession session) throws Exception {
      return this.reserveSchedule(program, (String)null, (String)null, session);
   }

   public boolean reserveSchedule(ProgramEntity program, String deviceId, String schedulerType, SqlSession session) throws Exception {
      if (program != null && !"Y".endsWith(program.getIs_default())) {
         String cronExpression = null;
         Date startTime = null;
         Date endTime = null;
         boolean isRepublishSchedule = false;
         CronTrigger trigger;
         if (this.isReservedSchedule(program.getDeploy_time())) {
            String[] timeStr = program.getDeploy_time().split(":");
            String[] startEndStr;
            Calendar myCal;
            if (program.getReservation_start_date() != null && !program.getReservation_start_date().equals("")) {
               startEndStr = program.getReservation_start_date().split("-");
               myCal = Calendar.getInstance();
               myCal.set(1, Integer.valueOf(startEndStr[0]));
               myCal.set(2, Integer.valueOf(startEndStr[1]) - 1);
               myCal.set(5, Integer.valueOf(startEndStr[2]));
               myCal.set(11, 0);
               myCal.set(12, 0);
               myCal.set(13, 0);
               startTime = myCal.getTime();
            }

            if (program.getReservation_end_date() != null && !program.getReservation_end_date().equals("")) {
               startEndStr = program.getReservation_end_date().split("-");
               myCal = Calendar.getInstance();
               myCal.set(1, Integer.valueOf(startEndStr[0]));
               myCal.set(2, Integer.valueOf(startEndStr[1]) - 1);
               myCal.set(5, Integer.valueOf(startEndStr[2]));
               myCal.set(11, 23);
               myCal.set(12, 59);
               myCal.set(13, 59);
               endTime = myCal.getTime();
            }

            if (program.getReservation_repeat_type().equals("daily")) {
               cronExpression = "0 " + timeStr[1] + " " + timeStr[0] + " * * ?";
            } else if (program.getReservation_repeat_type().equals("weekly")) {
               cronExpression = "0 " + timeStr[1] + " " + timeStr[0] + " ? * " + program.getReservation_weekly();
            } else if (program.getReservation_repeat_type().equals("monthly")) {
               cronExpression = "0 " + timeStr[1] + " " + timeStr[0] + " " + program.getReservation_monthly() + " * ?";
            }

            Scheduler scheduler = com.samsung.magicinfo.protocol.scheduler.ScheduleManager.getSchedulerInstance();
            String jobName = program.getProgram_id();
            String jobGroupName = "ScheduleJobGroup";
            CommonUtils.deleteJob(jobName, jobGroupName);
            trigger = (CronTrigger)TriggerBuilder.newTrigger().withIdentity(jobName, jobGroupName).withSchedule(CronScheduleBuilder.cronSchedule(cronExpression)).startAt(startTime).endAt(endTime).build();
            JobDetail jobdetail = CommonUtils.getJobDetail(jobName, jobGroupName, ReservedScheduleJob.class);
            scheduler.scheduleJob(jobdetail, trigger);
         } else {
            try {
               String jobName = program.getProgram_id();
               String jobGroupName = "ScheduleJobGroup";
               CommonUtils.deleteJob(jobName, jobGroupName);
            } catch (Exception var22) {
               this.logger.error("exception " + var22);
            }

            ProgramFileManager programFileManager = ProgramFileManagerImpl.getInstance();
            ProgramFile programFile = programFileManager.create(program);
            DownloadStatusInfo downloadInfo = DownloadStatusInfoImpl.getInstacne();
            DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
            trigger = null;
            if (deviceId == null) {
               downloadInfo.deleteDownloadStatus(program.getProgram_id());
            } else {
               downloadInfo.deleteDownloadStatusByDeviceId(deviceId);
            }

            this.dao.setActiveProgramVersion(program.getProgram_id(), this.dao.getProgramVersion(program.getProgram_id()));
            Device device;
            Object devices;
            if (deviceId == null) {
               devices = deviceInfo.getDevicesByProgramId(program.getProgram_id());
               Iterator var33 = ((List)devices).iterator();

               while(var33.hasNext()) {
                  device = (Device)var33.next();
                  downloadInfo.deleteDownloadStatusByDeviceId(device.getDevice_id());
               }
            } else {
               DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
               device = deviceInfo.getDevice(deviceId);
               DeviceGroup deviceGroup = deviceGroupInfo.getGroupByDeviceId(deviceId);
               device.setGroup_id(deviceGroup.getGroup_id());
               downloadInfo.deleteDownloadStatusByDeviceId(deviceId);
               devices = new ArrayList();
               ((List)devices).add(device);
               isRepublishSchedule = true;
            }

            boolean initScheduleStatus = devices != null && ((List)devices).size() > 0;
            if (initScheduleStatus) {
               this.dao.setProgramDeployTime(program.getProgram_id());
            }

            if (programFile.getContentIds() != null && programFile.getContentIds().size() > 0 && initScheduleStatus) {
               downloadInfo.initDownloadStatus(program.getProgram_id(), (List)devices, programFile.getContentIds(), false, "content", isRepublishSchedule ? "INIT_DOWNLOAD_FROM_RE_PUBLISH" : "INIT_DOWNLOAD_FROM_NEW_OR_EDIT_SCHEDULE");
            }

            downloadInfo.addScheduleDeployStatus(program.getProgram_id(), (List)devices);
            int index = 0;
            MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
            if (CollectionUtils.isNotEmpty((Collection)devices)) {
               Iterator var17 = ((List)devices).iterator();

               while(var17.hasNext()) {
                  Device device = (Device)var17.next();
                  HashMap params = new HashMap();
                  params.put("program_id", program.getProgram_id());
                  params.put("program_version", program.getVersion());
                  params.put("device", device);
                  ScheduleReservation schReservation = new ScheduleReservation();
                  schReservation.setService_id("DEPLOY_CONTENT_SCHEDULE");
                  schReservation.setDevice_id(device.getDevice_id());
                  schReservation.setDevice_model_name(device.getDevice_model_name());
                  schReservation.setService_params(params);
                  schReservation.setScheduler_type(schedulerType);
                  schReservation.setService_start_date(startTime);
                  schReservation.setService_end_date(endTime);
                  schReservation.setExpression(cronExpression);
                  ReservationManager reserveManager = ReservationManagerImpl.getInstance();
                  reserveManager.insert(schReservation);
                  monMgr.scheduleReload(device.getDevice_id(), 1, session);
                  downloadInfo.deleteDownloadStatusInCache(device.getDevice_id(), programFile.getContentIds());
                  ++index;
                  if (index % 500 == 0) {
                     Thread.sleep(1000L);
                  }
               }
            }
         }

         return true;
      } else {
         return this.deployDefaultSchedule(program, deviceId, schedulerType);
      }
   }

   public boolean deployDefaultSchedule(ProgramEntity program, String deviceId, String schedulerType) {
      try {
         String programId = program != null && program.getProgram_id() != null ? program.getProgram_id() : null;
         Long version = program != null ? program.getVersion() : -1L;
         DeviceInfo devInfo = DeviceInfoImpl.getInstance();
         DownloadStatusInfo downloadInfo = DownloadStatusInfoImpl.getInstacne();
         DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
         List devices = null;
         if (StringUtils.isNotEmpty(deviceId)) {
            Device device = devInfo.getDeviceMinInfo(deviceId);
            devices = new ArrayList();
            ((List)devices).add(device);
         }

         if (StringUtils.isNotEmpty(programId)) {
            ProgramFileManager programFileManager = ProgramFileManagerImpl.getInstance();
            programFileManager.create(program);
            downloadInfo.deleteDownloadStatus(programId);
            if (StringUtils.isNotEmpty(deviceId)) {
               downloadInfo.deleteDownloadStatusByDeviceId(deviceId);
            } else {
               devices = deviceInfo.getDevicesByProgramId(programId);
            }
         }

         if (StringUtils.isNotEmpty(programId) && version > 0L) {
            ScheduleInfoDAO scheduleInfoDAO = new ScheduleInfoDAO();
            scheduleInfoDAO.setActiveProgramVersion(programId, version);
         }

         Iterator var19 = ((List)devices).iterator();

         while(var19.hasNext()) {
            Device device = (Device)var19.next();
            HashMap params = new HashMap();
            params.put("program_id", programId);
            params.put("program_version", version);
            params.put("device", device);
            ScheduleReservation schReservation = new ScheduleReservation();
            schReservation.setService_id("DEPLOY_CONTENT_SCHEDULE");
            schReservation.setDevice_id(device.getDevice_id());
            schReservation.setDevice_model_name(device.getDevice_model_name());
            schReservation.setService_params(params);
            schReservation.setScheduler_type(schedulerType);
            ReservationManager reserveManager = ReservationManagerImpl.getInstance();
            reserveManager.insert(schReservation);
            MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
            monMgr.scheduleReload(device.getDevice_id(), 1);
         }

         return true;
      } catch (Exception var16) {
         this.logger.error("[MagicInfo_deploySchedule] fail deploySchedule! e : " + var16.getMessage());
         return false;
      }
   }

   public void deploySchedule(String deviceId, String programId, long version) throws Exception {
      if ("".equals(programId) && version == -1L) {
         DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
         DeviceGroup deviceGroup = deviceGroupInfo.getGroupByDeviceId(deviceId);
         programId = deviceGroup.getDefault_program_id();
         ScheduleInfo scheduleInfo = getInstance();
         ProgramEntity programEntity = scheduleInfo.getProgram(programId);
         version = programEntity.getVersion();
      }

      this.deploySchedule(deviceId, programId, version, "");
   }

   public void deploySchedule(String deviceId, String programId, long version, String schedulerType) throws Exception {
      try {
         ScheduleInfo scheduleInfo = getInstance();
         ProgramEntity program = scheduleInfo.getProgram(programId);
         this.reserveSchedule(program, deviceId, schedulerType, (SqlSession)null);
      } catch (Exception var8) {
         this.logger.error("[MagicInfo_deploySchedule] fail deploySchedule! e : " + var8.getMessage());
      }

   }

   public List getProgramByContentId(String contentId) throws SQLException {
      return this.dao.getProgramByContentId(contentId);
   }

   public List getProgramByPlaylistId(String playlistId) throws SQLException {
      return this.dao.getProgramByPlaylistId(playlistId);
   }

   public void setContentTrigger(String contentId) throws Exception {
      SchedulePublishThread thread = new SchedulePublishThread(1, contentId);
      Thread t = new Thread(thread);
      t.start();
   }

   public void setPlaylistTrigger(String playlistId) throws Exception {
      SchedulePublishThread thread = new SchedulePublishThread(2, playlistId);
      Thread t = new Thread(thread);
      t.start();
   }

   public void setPlaylistTrigger(String playlistId, boolean leaveFileLog) throws Exception {
      PlaylistInfo playlistDao = PlaylistInfoImpl.getInstance();
      Playlist playlist = playlistDao.getPlaylistActiveVerInfo(playlistId);
      UserContainer userContainer = null;
      rms.setBasename("resource/messages");
      (new StringBuilder()).append(rms.getMessage("TEXT_UNKNOWN_P", (Object[])null, new Locale("en"))).append(rms.getMessage("TEXT_TITLE_USER_P", (Object[])null, new Locale("en"))).toString();

      try {
         userContainer = SecurityUtils.getUserContainer();
      } catch (Exception var12) {
         this.logger.error("[MagicInfo_setPlaylistTrigger] fail get UserContainer. e : " + var12.getMessage());
      }

      List programList;
      String programId;
      ProgramEntity program;
      String logMsg;
      if (playlist.getPlaylist_type() != null && playlist.getPlaylist_type().equals("6")) {
         programList = playlistDao.getListLinkedPlaylistProgramId(playlistId);
         Iterator var13 = programList.iterator();

         while(var13.hasNext()) {
            programId = (String)var13.next();
            this.dao.programVersionUp(programId);
            program = this.dao.getProgram(programId);
            if (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals("")) {
               this.reserveSchedule(program);
            }

            if (leaveFileLog) {
               logMsg = "playlistName[" + playlist.getPlaylist_name() + "]";
               logMsg = logMsg + ",playlistId[" + playlistId + "]";
               logMsg = logMsg + ",programName[" + program.getProgram_name() + "]";
               logMsg = logMsg + ",programId[" + programId + "]";
               logMsg = logMsg + ",programType[" + program.getProgram_type() + "]";
               this.logger.error("[INFO][MagicInfo_ExpiredContentJob] will DEPLOY PROGRAM = " + logMsg);
            }
         }

         List playlistList = playlistDao.getListLinkedPlaylistPlaylistId(playlistId);
         Iterator var15 = playlistList.iterator();

         while(var15.hasNext()) {
            String pId = (String)var15.next();
            this.setRulesetTrigger(pId, "playlist");
         }
      } else {
         programList = this.dao.getProgramByPlaylistId(playlistId);

         for(int i = 0; i < programList.size(); ++i) {
            programId = (String)((Map)programList.get(i)).get("program_id");
            this.dao.programVersionUp(programId);
            program = this.dao.getProgram(programId);
            if (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals("")) {
               this.reserveSchedule(program);
            }

            if (leaveFileLog) {
               logMsg = "playlistName[" + playlist.getPlaylist_name() + "]";
               logMsg = logMsg + ",playlistId[" + playlistId + "]";
               logMsg = logMsg + ",programName[" + program.getProgram_name() + "]";
               logMsg = logMsg + ",programId[" + programId + "]";
               logMsg = logMsg + ",programType[" + program.getProgram_type() + "]";
               this.logger.error("[INFO][MagicInfo_ExpiredContentJob] will DEPLOY PROGRAM = " + logMsg);
            }
         }

         this.setRulesetTrigger(playlistId, "playlist");
      }

   }

   public void setScheduleTrigger(String contentId) throws Exception {
      SchedulePublishThread thread = new SchedulePublishThread(3, contentId);
      Thread t = new Thread(thread);
      t.start();
   }

   private void setRulesetTrigger(String contentId, String type) throws Exception {
      type = type.toLowerCase();
      new HashSet();
      RuleSetInfo rulesetInfo = RuleSetInfoImpl.getInstance();
      List rulesetList = rulesetInfo.getRulesetUsingContents(contentId);

      String rulesetId;
      for(Iterator var6 = rulesetList.iterator(); var6.hasNext(); this.setContentTrigger(rulesetId)) {
         RuleSet ruleset = (RuleSet)var6.next();
         rulesetId = ruleset.getRuleset_id();
         ContentFile ruleMetaFile = RulesetUtils.updateContentInRuleset(rulesetId, contentId, type);
         String oldFileId = ruleset.getFile_id();
         if (rulesetInfo.updateRulesetMetaFile(rulesetId, ruleMetaFile.getFile_id())) {
            try {
               if (StringUtils.isNotBlank(oldFileId)) {
                  ContentInfo contentInfo = ContentInfoImpl.getInstance();
                  contentInfo.deleteFile(oldFileId);
               }
            } catch (Exception var12) {
               this.logger.error(var12);
            }
         }
      }

   }

   public boolean isProgramNameUnique(String program_name, String program_id, int prog_group_id) throws SQLException {
      return this.dao.isProgramNameUnique(program_name, program_id, prog_group_id);
   }

   public List getDownloadContentList(String program_id, String device_id) throws SQLException {
      return this.dao.getDownloadContentList(program_id, device_id);
   }

   public List getEventList(String scheduleId) throws SQLException {
      return this.dao.getEventList(scheduleId);
   }

   public long getContentScheduleCntToday() throws SQLException {
      return this.dao.getContentScheduleCntToday();
   }

   public long getContentScheduleCntToday(String orgName) throws SQLException {
      long cnt = 0L;
      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      List groupIdList = this.dao.getContentScheduleTodayGroupId();
      if (groupIdList != null) {
         Iterator var6 = groupIdList.iterator();

         while(var6.hasNext()) {
            Map groupId = (Map)var6.next();
            String programOrganName = programGroupInfo.getOrgNameByGroupId((Long)groupId.get("group_id"));
            if (programOrganName != null && programOrganName.equalsIgnoreCase(orgName)) {
               ++cnt;
            }
         }
      }

      return cnt;
   }

   public long getContentScheduleCntThisWeek() throws SQLException {
      return this.dao.getContentScheduleCntThisWeek();
   }

   public long getAllScheduleCount() throws SQLException {
      return this.dao.getAllScheduleCount();
   }

   public long getAllScheduleCount(String orgName) throws SQLException {
      long cnt = 0L;
      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      List groupIdList = this.dao.getAllScheduleGroupId();
      if (groupIdList != null) {
         Iterator var6 = groupIdList.iterator();

         while(var6.hasNext()) {
            Map groupId = (Map)var6.next();
            String programOrganName = programGroupInfo.getOrgNameByGroupId((Long)groupId.get("group_id"));
            if (programOrganName != null && programOrganName.equalsIgnoreCase(orgName)) {
               ++cnt;
            }
         }
      }

      return cnt;
   }

   public long getMapedScheduleCount() throws SQLException {
      return this.dao.getMapedScheduleCount();
   }

   public long getMapedScheduleCount(String orgName) throws SQLException {
      long cnt = 0L;
      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      List groupIdList = this.dao.getMapedScheduleGroupId();
      if (groupIdList != null) {
         Iterator var6 = groupIdList.iterator();

         while(var6.hasNext()) {
            Map groupId = (Map)var6.next();
            String programOrganName = programGroupInfo.getOrgNameByGroupId((Long)groupId.get("group_id"));
            if (programOrganName != null && programOrganName.equalsIgnoreCase(orgName)) {
               ++cnt;
            }
         }
      }

      return cnt;
   }

   public long getNotMapedScheduleCount() throws SQLException {
      return this.dao.getNotMapedScheduleCount();
   }

   public long getNotMapedScheduleCount(String orgName) throws SQLException {
      long cnt = 0L;
      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      List groupIdList = this.dao.getNotMapedScheduleGroupId();
      if (groupIdList != null) {
         Iterator var6 = groupIdList.iterator();

         while(var6.hasNext()) {
            Map groupId = (Map)var6.next();
            String programOrganName = programGroupInfo.getOrgNameByGroupId((Long)groupId.get("group_id"));
            if (programOrganName != null && programOrganName.equalsIgnoreCase(orgName)) {
               ++cnt;
            }
         }
      }

      return cnt;
   }

   public boolean onProgramLayoutChange(String program_id, String session_id, int channelNo, String layout_type, double resolution_x, double resolution_y) throws SQLException {
      return this.dao.onProgramLayoutChange(program_id, session_id, channelNo, layout_type, resolution_x, resolution_y);
   }

   public List getFrameTemplates(String template_type, String resolution, String organization) throws SQLException {
      return this.dao.getFrameTemplates(template_type, resolution, organization);
   }

   public boolean saveFrameTemplate(FrameTemplateEntity fte) throws SQLException {
      return this.dao.saveFrameTemplate(fte);
   }

   public long createFrameTemplate(FrameTemplateEntity fte) throws SQLException {
      return this.dao.createFrameTemplate(fte);
   }

   public boolean deleteFrameTemplate(Long template_id) throws SQLException {
      return this.dao.deleteFrameTemplate(template_id);
   }

   public long checkAvailableDiskSpace(String program_id, String session_id, String curr_date, boolean temp) throws SQLException {
      return this.dao.checkAvailableDiskSpace(program_id, session_id, curr_date, temp);
   }

   public boolean updateDeviceGroupMappedInProgramAsDefault(String group_id) throws SQLException {
      return this.dao.updateDeviceGroupMappedInProgramAsDefault(group_id);
   }

   public boolean updateDeviceGroupMappedInProgramAsDefaultByPid(String program_id) throws SQLException {
      return this.dao.updateDeviceGroupMappedInProgramAsDefaultByPid(program_id);
   }

   public List getDeviceProgramMapList(String programId) throws SQLException {
      return this.dao.getDeviceProgramMapList(programId);
   }

   public boolean mapDeviceGroupWithDefault(Long devGrp, String defaultProgramId) throws SQLException {
      return this.dao.mapDeviceGroupWithDefault(devGrp, defaultProgramId);
   }

   public String getProgramIdByProgramName(String programName) throws SQLException {
      return this.dao.getProgramIdByProgramName(programName);
   }

   public boolean deleteFrameByFrameId(String frameId) throws SQLException {
      return this.dao.deleteFrameByFrameId(frameId);
   }

   public boolean deleteContentScheduleByScheduleId(String scheduleId) throws SQLException {
      boolean retVal = false;
      String program_id = this.dao.getProgramIdByByScheduleId(scheduleId);
      retVal = this.dao.deleteContentScheduleByScheduleId(scheduleId);
      if (retVal && program_id != null) {
         this.dao.programVersionUp(program_id);
         ProgramEntity program = this.getProgram(program_id);
         if (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals("")) {
            try {
               this.reserveSchedule(program);
            } catch (Exception var6) {
               retVal = false;
               this.logger.error("", var6);
            }
         }
      }

      return retVal;
   }

   public boolean deleteContentScheduleByScheduleId(String scheduleId, boolean programVersionUp, boolean deployProgram) throws SQLException {
      boolean retVal = false;
      String program_id = this.dao.getProgramIdByByScheduleId(scheduleId);
      retVal = this.dao.deleteContentScheduleByScheduleId(scheduleId);
      if (retVal && program_id != null) {
         if (programVersionUp) {
            this.dao.programVersionUp(program_id);
         }

         ProgramEntity program = this.getProgram(program_id);
         if (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals("")) {
            try {
               if (deployProgram) {
                  this.reserveSchedule(program);
               }
            } catch (Exception var8) {
               retVal = false;
               this.logger.error("", var8);
            }
         }
      }

      return retVal;
   }

   public List getContentScheduleIdByFrameId(String frameId) throws SQLException {
      return this.dao.getContentScheduleIdByFrameId(frameId);
   }

   public boolean addProgramWithFrameAndHWControlAndContent(ProgramEntity program, FrameEntity frame, ContentsScheduleEntity schedule, String sessionId) throws SQLException, ConfigException {
      return this.dao.addProgramWithFrameAndHWControlAndContent(program, frame, schedule, sessionId);
   }

   public boolean addProgramWithBasicInformation(ProgramEntity program, String sessionId) throws SQLException, ConfigException {
      boolean retVal = false;
      String program_id = null;
      if (program.getProgram_id() == null || program.getProgram_id() == "") {
         program_id = UUID.randomUUID().toString();
         program.setProgram_id(program_id);
      }

      retVal = this.dao.addProgramWithBasicInformation(program, sessionId);
      if (retVal && (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals(""))) {
         try {
            this.reserveSchedule(program);
         } catch (Exception var6) {
            retVal = false;
            this.logger.error("", var6);
         }
      }

      return retVal;
   }

   public boolean modifyProgramWithFrameAndHWControlAndContent(ProgramEntity program, FrameEntity frame, ContentsScheduleEntity schedule, String sessionId) throws SQLException, ConfigException {
      return this.dao.modifyProgramWithFrameAndHWControlAndContent(program, frame, schedule, sessionId);
   }

   public boolean modifyProgramWithBasicInformation(ProgramEntity program, String sessionId) throws SQLException, ConfigException {
      boolean retVal = false;
      retVal = this.dao.modifyProgramWithBasicInformation(program, sessionId);
      if (retVal && (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals(""))) {
         try {
            this.reserveSchedule(program);
         } catch (Exception var5) {
            retVal = false;
            this.logger.error("", var5);
         }
      }

      return retVal;
   }

   public boolean addContentScheduleWithoutTemp(ContentsScheduleEntity schedule) throws SQLException, ConfigException, OpenApiServiceException {
      boolean retVal = false;
      schedule.setSchedule_id(UUID.randomUUID().toString());
      retVal = this.dao.addContentScheduleWithoutTemp(schedule);
      if (retVal) {
         String program_id = schedule.getProgram_id();
         this.dao.programVersionUp(program_id);
         ProgramEntity program = this.getProgram(program_id);
         if (program == null) {
            throw new OpenApiServiceException(OpenApiExceptionCode.S009[0], OpenApiExceptionCode.S009[1]);
         }

         if (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals("")) {
            try {
               this.reserveSchedule(program);
            } catch (Exception var6) {
               retVal = false;
               this.logger.error("", var6);
            }
         }
      }

      return retVal;
   }

   public boolean addContentScheduleWithoutTemp(ContentsScheduleEntity schedule, boolean programVersionUp, boolean deployProgram) throws SQLException, ConfigException, OpenApiServiceException {
      boolean retVal = false;
      if (!StrUtils.nvl(CommonConfig.get("saas.eu.enable")).equalsIgnoreCase("TRUE")) {
         schedule.setSchedule_id(UUID.randomUUID().toString());
      }

      retVal = this.dao.addContentScheduleWithoutTemp(schedule);
      if (retVal) {
         String program_id = schedule.getProgram_id();
         if (programVersionUp) {
            this.dao.programVersionUp(program_id);
         }

         ProgramEntity program = this.getProgram(program_id);
         if (program == null) {
            throw new OpenApiServiceException(OpenApiExceptionCode.S009[0], OpenApiExceptionCode.S009[1]);
         }

         if (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals("")) {
            try {
               if (deployProgram) {
                  this.reserveSchedule(program);
               }
            } catch (Exception var8) {
               retVal = false;
               this.logger.error("", var8);
            }
         }
      }

      return retVal;
   }

   public boolean addHWConstraint(ContentsScheduleEntity schedule) throws SQLException, ConfigException {
      boolean retVal = false;
      schedule.setSchedule_id(UUID.randomUUID().toString());
      if (retVal) {
         String program_id = schedule.getProgram_id();
         this.dao.programVersionUp(program_id);
         ProgramEntity program = this.getProgram(program_id);
         if (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals("")) {
            try {
               this.reserveSchedule(program);
            } catch (Exception var6) {
               retVal = false;
               this.logger.error("", var6);
            }
         }
      }

      return retVal;
   }

   public boolean modifyContentScheduleWithoutTemp(ContentsScheduleEntity schedule) throws SQLException, ConfigException, OpenApiServiceException {
      boolean retVal = false;
      retVal = this.dao.modifyContentScheduleWithoutTemp(schedule);
      if (retVal) {
         String program_id = schedule.getProgram_id();
         this.dao.programVersionUp(program_id);
         ProgramEntity program = this.getProgram(program_id);
         if (program == null) {
            throw new OpenApiServiceException(OpenApiExceptionCode.S009[0], OpenApiExceptionCode.S009[1]);
         }

         if (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals("")) {
            try {
               this.reserveSchedule(program);
            } catch (Exception var6) {
               retVal = false;
               this.logger.error("", var6);
            }
         }
      }

      return retVal;
   }

   public boolean modifyContentScheduleWithoutTemp(ContentsScheduleEntity schedule, boolean programVersionUp, boolean deployProgram) throws SQLException, ConfigException, OpenApiServiceException {
      boolean retVal = false;
      retVal = this.dao.modifyContentScheduleWithoutTemp(schedule);
      if (retVal) {
         String program_id = schedule.getProgram_id();
         if (programVersionUp) {
            this.dao.programVersionUp(program_id);
         }

         ProgramEntity program = this.getProgram(program_id);
         if (program == null) {
            throw new OpenApiServiceException(OpenApiExceptionCode.S009[0], OpenApiExceptionCode.S009[1]);
         }

         if (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals("")) {
            try {
               if (deployProgram) {
                  this.reserveSchedule(program);
               }
            } catch (Exception var8) {
               retVal = false;
               this.logger.error("", var8);
            }
         }
      }

      return retVal;
   }

   public boolean modifyHWConstraint(ContentsScheduleEntity schedule) throws SQLException, ConfigException {
      boolean retVal = false;
      retVal = this.dao.modifyHWConstraint(schedule);
      if (retVal) {
         String program_id = schedule.getProgram_id();
         this.dao.programVersionUp(program_id);
         ProgramEntity program = this.getProgram(program_id);
         if (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals("")) {
            try {
               this.reserveSchedule(program);
            } catch (Exception var6) {
               retVal = false;
               this.logger.error("", var6);
            }
         }
      }

      return retVal;
   }

   public String getDeviceTypeByProgramId(String programId) throws SQLException {
      return this.dao.getDeviceTypeByProgramId(programId);
   }

   public Float getDeviceTypeVersionByProgramId(String programId) throws SQLException {
      return this.dao.getDeviceTypeVersionByProgramId(programId);
   }

   public boolean addFrameWithoutTemp(FrameEntity frame) throws SQLException {
      return this.dao.addFrameWithoutTemp(frame);
   }

   public int getFrameIndexByFrameId(String frameId) throws SQLException {
      return this.dao.getFrameIndexByFrameId(frameId);
   }

   public int setLinedataByProgramId(String programId, int channelNo, String lineData) throws SQLException {
      return this.dao.setLinedataByProgramId(programId, channelNo, lineData);
   }

   public int setLinedataToZeroFrame(String programId, int channelNo) throws SQLException {
      return this.dao.setLinedataToZeroFrame(programId, channelNo);
   }

   public String getProgramIdByFrameId(String frameId) throws SQLException {
      return this.dao.getProgramIdByFrameId(frameId);
   }

   public boolean updateDefaultProgramDeviceType(Long device_group_id, String device_type) throws SQLException {
      return this.dao.updateDefaultProgramDeviceType(device_group_id, device_type);
   }

   public boolean updateDefaultProgramDeviceType(Long device_group_id, String device_type, SqlSession session) throws SQLException {
      return this.dao.updateDefaultProgramDeviceType(device_group_id, device_type, session);
   }

   public String isDeleted(String programId) throws SQLException {
      return this.dao.isDelete(programId);
   }

   public String getCreatorIdByProgramId(String programId) throws SQLException {
      return this.dao.getCreatorIdByProgramId(programId);
   }

   public boolean deleteFrame(String sessionId, String programId, int screenIndex, int frameIndex) throws SQLException {
      return false;
   }

   public List getProgramListBySchOrgId(int schOrgId) throws SQLException {
      return this.dao.getProgramListBySchOrgId(schOrgId);
   }

   public List getChannelListByProgramId(String program_id) throws SQLException {
      return this.dao.getChannelListByProgramId(program_id);
   }

   public boolean deleteFrameByChannelNo(String programId, int channelNo) throws SQLException, ConfigException {
      return this.dao.deleteFrameByChannelNo(programId, channelNo);
   }

   public boolean deleteScheduleByChannelNo(String programId, int channelNo) throws SQLException {
      return this.dao.deleteScheduleByChannelNo(programId, channelNo);
   }

   public boolean modifyProgramDeviceTypeAndVersion(String programId, String deviceType, float deviceTypeVersion) throws SQLException {
      return this.dao.modifyProgramDeviceTypeAndVersion(programId, deviceType, deviceTypeVersion);
   }

   public List getScheduleDetailPublishStatusList(String programId, long device_group_id) {
      return this.dao.getScheduleDetailPublishStatusList(programId, device_group_id);
   }

   public List getContentListInSchedule(String programId) {
      return this.dao.getContentListInSchedule(programId);
   }

   public List getContentListInScheduleWithStopDate(String programId, String stopDate) throws Exception {
      return this.dao.getContentListInScheduleWithStopDate(programId, stopDate);
   }

   public List getContentListInADScheduleWithStopDate(String programId, String stopDate) throws Exception {
      return this.dao.getContentListInADScheduleWithStopDate(programId, stopDate);
   }

   public void addContentPublishData(DetailDownloadContentEntity detailDownloadContentEntity) throws SQLException {
      this.dao.addContentPublishData(detailDownloadContentEntity);
   }

   public void deleteContentPublishData(String programId) throws SQLException {
      this.dao.deleteContentPublishData(programId);
   }

   public boolean addDynaminTagInfo(List tagList) throws SQLException {
      return this.dao.addDynaminTagInfoList(tagList);
   }

   public int addDynaminTagInfoTemp(DynamicTagEntity entity) throws SQLException {
      return this.dao.addDynaminTagInfoTemp(entity);
   }

   public boolean deleteDynaminTagInfo(String scheduleId) throws SQLException {
      return this.dao.deleteDynaminTagInfo(scheduleId);
   }

   public boolean deleteDynaminTagInfoTemp(String scheduleId) throws SQLException {
      return this.dao.deleteDynaminTagInfoTemp(scheduleId);
   }

   public List getDynaminTagInfo(String scheduleId) throws SQLException {
      return this.dao.getDynaminTagInfo(scheduleId);
   }

   public List getDynaminTagInfoTemp(String sessionId, String scheduleId) throws SQLException {
      return this.dao.getDynaminTagInfoTemp(sessionId, scheduleId);
   }

   public boolean deleteAllProgramData(String programId) throws ConfigException, SQLException {
      return this.dao.deleteAllProgramData(programId);
   }

   public boolean programVersionUp(String programId) throws Exception {
      boolean ret = this.dao.programVersionUp(programId);
      return ret;
   }

   public List getDeletedProgramIdList(String organization) throws Exception {
      return this.dao.getDeletedProgramIdList(organization);
   }

   public List getScheduleMappedContentTotalSize(String programId) throws Exception {
      return this.dao.getScheduleMappedContentTotalSize(programId);
   }

   public FrameTemplateEntity getTemplateEntity(long templateId) throws Exception {
      return this.dao.getTemplateEntity(templateId);
   }

   public List getSlotListFromProgramIdWithFrameId(String programId, String frameId) throws Exception {
      return this.dao.getAdSlotList(programId, frameId);
   }

   public List getSlotListFromProgramId(String programId) throws Exception {
      return this.dao.getAdSlotList(programId);
   }

   public List getAdScheduleListFromProgramIdWithSlotId(String programId, String slotId) throws Exception {
      return this.dao.getAdScheduleList(programId, slotId);
   }

   public boolean updateTemplate(FrameTemplateEntity template) throws Exception {
      return this.dao.updateTemplate(template);
   }

   public List getReserveSchedule() throws Exception {
      return this.dao.getReserveScheduleList();
   }

   public int getCountProgramIdByGroupId(long groupId) throws Exception {
      return this.dao.getCountProgramIdByGroupId(groupId);
   }

   public List getTagPlaylistIdVersion(String programid) throws Exception {
      return this.dao.getTagPlaylistIdVersion(programid);
   }

   public boolean chkOrganizationByProgramId(String organization, String programId) throws Exception {
      String scheduleOrganization = "";
      List list = this.dao.getProgramGroupIdAndName(programId);
      long program_group_id = 0L;
      if (list != null && list.size() != 0) {
         Map p = (Map)list.get(0);
         program_group_id = (Long)p.get("group_id");
      }

      long p_group_id = this.dao.getProgramGroupRoot((int)program_group_id);
      scheduleOrganization = this.dao.getGroupNameByGroupId(p_group_id);
      if (organization.equals(scheduleOrganization)) {
         return true;
      } else {
         scheduleOrganization = this.dao.getOrganiationByProgramId(programId);
         return organization.equals(scheduleOrganization);
      }
   }

   public List getDynamicTagByScheduleIdIdAndPlaylistId(String programId, String playlistId) throws Exception {
      return this.dao.getDynamicTagByScheduleIdIdAndPlaylistId(programId, playlistId);
   }

   public boolean existsProgramId(String programId) throws Exception {
      return this.dao.existsProgramId(programId) > 0L;
   }

   public int getCountScheduleToExpire(String userId, Long groupId, String stopDate, SelectCondition condition) throws SQLException {
      ProgramGroupInfo groupInfo = ProgramGroupInfoImpl.getInstance();
      List groupList = groupInfo.getGroupIdsByOrgManagerUserId(userId, groupId);
      return this.dao.getCountScheduleToExpire(groupList, userId, stopDate, condition);
   }

   public int getDeviceCountByScheduleToExpire(String userId, Long groupId, String stopDate, SelectCondition condition) throws SQLException {
      ProgramGroupInfo groupInfo = ProgramGroupInfoImpl.getInstance();
      List groupList = groupInfo.getGroupIdsByOrgManagerUserId(userId, groupId);
      return this.dao.getDeviceCountByScheduleToExpire(groupList, userId, stopDate, condition);
   }

   public List getListScheduleToExpire(int startPos, int pageSize, String userId, Long groupId, String stopDate, SelectCondition condition) throws SQLException {
      ProgramGroupInfo groupInfo = ProgramGroupInfoImpl.getInstance();
      List groupList = groupInfo.getGroupIdsByOrgManagerUserId(userId, groupId);
      return this.dao.getListScheduleToExpire(startPos, pageSize, groupList, userId, stopDate, condition);
   }

   public List getDeviceListByScheduleToExpire(int startPos, int pageSize, String userId, Long groupId, String stopDate, SelectCondition condition) throws SQLException {
      ProgramGroupInfo groupInfo = ProgramGroupInfoImpl.getInstance();
      List groupList = groupInfo.getGroupIdsByOrgManagerUserId(userId, groupId);
      return this.dao.getDeviceListByScheduleToExpire(startPos, pageSize, groupList, userId, stopDate, condition);
   }

   public Long getMaxPriorityByProgramId(String programId) throws SQLException {
      return this.dao.getMaxPriorityByProgramId(programId);
   }

   public Long getMinPriorityByProgramId(String programId) throws SQLException {
      return this.dao.getMinPriorityByProgramId(programId);
   }

   public Long getPriorityByScheduleId(String programId, String scheduleId) throws SQLException {
      return this.dao.getPriorityByScheduleId(programId, scheduleId);
   }

   public List getScheduleIdAndPriorityByProgramId(String programId) throws SQLException {
      return this.dao.getScheduleIdAndPriorityByProgramId(programId);
   }

   public boolean updateSchedulePriorityByProgramId(String programId, List scheduleMapList) throws SQLException {
      return this.dao.updateSchedulePriorityByProgramId(programId, scheduleMapList);
   }

   public void setExpiredContentTrigger(String contentId, String contentName) throws Exception {
      List programList = this.dao.getProgramByExpiredContentId(contentId);

      for(int i = 0; i < programList.size(); ++i) {
         Map programInfo = (Map)programList.get(i);
         String type = programInfo.get("type").toString();
         String programId = programInfo.get("program_id").toString();
         String programName = programInfo.get("program_name").toString();
         int channelNo = Integer.parseInt(programInfo.get("channel_no").toString());
         int screenIndex = Integer.parseInt(programInfo.get("screen_index").toString());
         String frameId = programInfo.get("frame_id").toString();
         String scheduleId = programInfo.get("schedule_id").toString();
         String logMsg;
         if ("program".equals(type)) {
            logMsg = "contentName[" + contentName + "]";
            logMsg = logMsg + ",contentId[" + contentId + "]";
            logMsg = logMsg + ",type[" + type + "]";
            logMsg = logMsg + ",programId[" + programId + "]";
            logMsg = logMsg + ",programName[" + programName + "]";
            this.logger.error("[INFO][MagicInfo_ExpiredContentJob] DELETE IN PROGRAM = " + logMsg);
            this.dao.deleteExpiredContentInProgram(programId, contentId);
         } else if ("frame".equals(type)) {
            logMsg = "contentName[" + contentName + "]";
            logMsg = logMsg + ",contentId[" + contentId + "]";
            logMsg = logMsg + ",type[" + type + "]";
            logMsg = logMsg + ",programId[" + programId + "]";
            logMsg = logMsg + ",programName[" + programName + "]";
            logMsg = logMsg + ",channelNo[" + channelNo + "]";
            logMsg = logMsg + ",screenIndex[" + screenIndex + "]";
            logMsg = logMsg + ",frameId[" + frameId + "]";
            this.logger.error("[INFO][MagicInfo_ExpiredContentJob] DELETE IN FRAME = " + logMsg);
            this.dao.deleteExpiredContentInFrame(programId, channelNo, screenIndex, frameId, contentId);
         } else {
            if (!"schedule".equals(type)) {
               logMsg = "contentName[" + contentName + "]";
               logMsg = logMsg + ",contentId[" + contentId + "]";
               logMsg = logMsg + ",type[" + type + "]";
               this.logger.error("[MagicInfo_ExpiredContentJob] DELETE - Type Error = " + logMsg);
               continue;
            }

            logMsg = "contentName[" + contentName + "]";
            logMsg = logMsg + ",contentId[" + contentId + "]";
            logMsg = logMsg + ",type[" + type + "]";
            logMsg = logMsg + ",programId[" + programId + "]";
            logMsg = logMsg + ",programName[" + programName + "]";
            logMsg = logMsg + ",scheduleId[" + scheduleId + "]";
            this.logger.error("[INFO][MagicInfo_ExpiredContentJob] DELETE IN SCHEDULE = " + logMsg);
            this.dao.deleteExpiredContentInSchedule(scheduleId, contentId);
         }

         if (i + 1 == programList.size() || !programId.equals(((Map)programList.get(i + 1)).get("program_id").toString())) {
            this.dao.programVersionUp(programId);
            ProgramEntity program = this.dao.getProgram(programId);
            if (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals("")) {
               this.reserveSchedule(program);
            }

            String logMsg = "contentName[" + contentName + "]";
            logMsg = logMsg + ",contentId[" + contentId + "]";
            logMsg = logMsg + ",type[" + type + "]";
            logMsg = logMsg + ",programId[" + programId + "]";
            logMsg = logMsg + ",programName[" + programName + "]";
            this.logger.error("[INFO][MagicInfo_ExpiredContentJob] will DEPLOY PROGRAM = " + logMsg);
         }
      }

   }

   public void setPlaylistWithExpiredContentTrigger(String contentId, String contentName) throws Exception {
      List playlistList = this.dao.getPlaylistByExpiredContentId(contentId);

      for(int i = 0; i < playlistList.size(); ++i) {
         Map mapPlaylistInfo = (Map)playlistList.get(i);
         String playlistId = mapPlaylistInfo.get("playlist_id").toString();
         this.setPlaylistTrigger(playlistId, contentId, contentName);
      }

   }

   private void setPlaylistTrigger(String playlistId, String contentId, String contentName) throws Exception {
      PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
      TagInfo tagInfo = TagInfoImpl.getInstance();
      Long curActiveVersionId = playlistInfo.getPlaylistActiveVersionId(playlistId);
      Playlist playlist = playlistInfo.getPlaylistVerInfo(playlistId, curActiveVersionId);
      int cnt = 0;
      if (playlist != null) {
         String logMsg;
         if ("5".equals(playlist.getPlaylist_type())) {
            logMsg = "contentName[" + contentName + "]";
            logMsg = logMsg + ",contentId[" + contentId + "]";
            logMsg = logMsg + ",playlistName[" + playlist.getPlaylist_name() + "]";
            logMsg = logMsg + ",playlistId[" + playlistId + "]";
            logMsg = logMsg + ",playlistType[" + playlist.getPlaylist_type() + "]";
            this.logger.error("[INFO][MagicInfo_ExpiredContentJob] DELETE IN TAG-PLAYLIST = " + logMsg);
            tagInfo.deleteTagInfoFromContentId(contentId);
            if (playlistInfo.getContentCountInTagPlaylist(playlistId) <= 0) {
               cnt = -2;
            }
         } else {
            logMsg = "contentName[" + contentName + "]";
            logMsg = logMsg + ",contentId[" + contentId + "]";
            logMsg = logMsg + ",playlistName[" + playlist.getPlaylist_name() + "]";
            logMsg = logMsg + ",playlistId[" + playlistId + "]";
            logMsg = logMsg + ",playlistType[" + playlist.getPlaylist_type() + "]";
            this.logger.error("[INFO][MagicInfo_ExpiredContentJob] DELETE IN NON-TAG-PLAYLIST = " + logMsg);
            cnt = playlistInfo.deleteContentFromPlaylist(playlistId, contentId);
         }

         if (cnt == -2) {
            List programList = this.dao.getScheduleByPlaylistId(playlistId);

            String userId;
            String upperPlaylistId;
            for(int j = 0; j < programList.size(); ++j) {
               Map programInfo = (Map)programList.get(j);
               userId = programInfo.get("program_id").toString();
               upperPlaylistId = programInfo.get("program_name").toString();
               String scheduleId = programInfo.get("schedule_id").toString();
               String programType = programInfo.get("program_type").toString();
               this.dao.deletePlaylistInSchedule(programType, scheduleId, playlistId);
               if (j + 1 == programList.size() || !userId.equals(((Map)programList.get(j + 1)).get("program_id").toString())) {
                  this.dao.programVersionUp(userId);
                  ProgramEntity program = this.dao.getProgram(userId);
                  if (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals("")) {
                     this.reserveSchedule(program);
                  }

                  String logMsg = "contentName[" + contentName + "]";
                  logMsg = logMsg + "contentId[" + contentId + "]";
                  logMsg = logMsg + ",playlistName[" + playlist.getPlaylist_name() + "]";
                  logMsg = logMsg + ",playlistId[" + playlistId + "]";
                  logMsg = logMsg + ",programName[" + upperPlaylistId + "]";
                  logMsg = logMsg + ",programId[" + userId + "]";
                  logMsg = logMsg + ",scheduleId[" + scheduleId + "]";
                  logMsg = logMsg + ",programType[" + programType + "]";
                  this.logger.error("[INFO][MagicInfo_ExpiredContentJob] will DEPLOY PROGRAM = " + logMsg);
               }
            }

            if ("6".equals(playlist.getPlaylist_type())) {
               List upperPlaylistList = playlistInfo.getUpperPlaylist(playlistId);

               for(int i = 0; i < upperPlaylistList.size(); ++i) {
                  Map UpperPlaylistInfo = (Map)upperPlaylistList.get(i);
                  upperPlaylistId = UpperPlaylistInfo.get("upper_playlist_id").toString();
                  this.setPlaylistTrigger(upperPlaylistId, playlistId, playlist.getPlaylist_name());
               }
            }

            try {
               PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
               String sessionId = UUID.randomUUID().toString();
               userId = "admin";
               if (pInfo.isDeletablePlaylist(playlistId, userId, sessionId)) {
                  pInfo.deletePlaylist(playlistId, userId, sessionId);
                  pInfo.deletePlaylistCompletely(playlistId);
               } else {
                  this.logger.error("[INFO][MagicInfo_ExpiredContentJob] Playlist: " + playlistId + " will not be deleted as it is in use somewhere else");
               }
            } catch (Exception var18) {
               this.logger.error("[INFO][MagicInfo_ExpiredContentJob] Could not delete the playlist: " + playlistId + ". Exception:- " + var18);
            }
         } else {
            this.setPlaylistTrigger(playlistId, true);
         }

      }
   }

   public List getContentListByProgramId(String programId) throws SQLException {
      return this.dao.getContentListByProgramId(programId);
   }

   public List getFrameContentList(String programId) throws Exception {
      return this.dao.getFrameContentsByProgramId(programId);
   }

   public List getScheduleGroupBySearchText(String searchText, String organizationName, String table) throws SQLException {
      return this.dao.getScheduleGroupBySearchText(searchText, organizationName, table);
   }

   public List getParentsGroupList(int pGroupId, String table) throws SQLException {
      return this.dao.getParentsGroupList(pGroupId, table);
   }

   public List getProgramCountByProgramType() throws SQLException {
      return this.dao.getProgramCountByProgramType();
   }
}
