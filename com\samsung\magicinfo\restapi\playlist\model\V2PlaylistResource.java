package com.samsung.magicinfo.restapi.playlist.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.samsung.magicinfo.framework.kpi.annotation.KPI;
import com.samsung.magicinfo.framework.kpi.annotation.LogProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@JsonInclude(Include.NON_NULL)
@ApiModel(
   description = "(lastModifiedDate) is based on server local time."
)
public class V2PlaylistResource {
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000",
      dataType = "string",
      value = "Id of specific playlist , gets the Id of the playlist."
   )
   @Pattern(
      regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$",
      message = "[V2PlaylistResource][playlistId] Not UUID pattern."
   )
   private String playlistId;
   @ApiModelProperty(
      dataType = "string",
      example = "SPLAYER",
      value = "DeviceType, minimum supported deviceType",
      required = true,
      allowableValues = "SPLAYER,iPLAYER,APLAYER,LPLAYER,WPLAYER"
   )
   private String deviceType = "";
   @ApiModelProperty(
      example = "3.0",
      dataType = "string",
      value = "DeviceTypeVersion, version of minimum supported deviceType",
      required = true,
      allowableValues = "1.0,2.0,3.0,4.0"
   )
   private String deviceTypeVersion = "1.0";
   @LogProperty(
      valueType = "NAME"
   )
   @ApiModelProperty(
      dataType = "string",
      example = "New Playlist",
      value = "Name of specific playlist , shows the name of the playlist.",
      required = true
   )
   @Size(
      max = 60,
      message = "max size is 60."
   )
   private String playlistName = "NO_TITLE";
   @ApiModelProperty(
      dataType = "long",
      example = "1",
      value = "GroupId, group ID of specified playlist",
      required = true
   )
   private Long groupId = 0L;
   @ApiModelProperty(
      dataType = "string",
      example = "default",
      value = "GroupName, group name of specified playlist",
      required = true
   )
   private String groupName = "";
   @ApiModelProperty(
      example = "1",
      dataType = "int",
      required = true,
      value = "If the group is not set in the playlist sharing setting, it is taken to zero, if set to one."
   )
   @Min(0L)
   @Max(1L)
   private int shareFlag = 1;
   @ApiModelProperty(
      example = "-",
      required = true,
      dataType = "string",
      value = "Playlist description"
   )
   @Size(
      max = 200,
      message = "[V2PlaylistResource][metaData] max size is 200."
   )
   private String metaData = "-";
   @ApiModelProperty(
      example = "false",
      required = true,
      dataType = "boolean",
      value = "Shuffle operation status, \"on\" if true and \"off\" if false."
   )
   private boolean shuffleFlag = false;
   @ApiModelProperty(
      example = "0",
      required = true,
      dataType = "int",
      value = "Number of contents in the playlist"
   )
   private int contentCount = 0;
   @ApiModelProperty(
      example = "0",
      dataType = "long",
      value = "Playtime of the playlist",
      required = true
   )
   private Long playTime = 0L;
   @ApiModelProperty(
      dataType = "long",
      value = "The total size of the contents that make up the playlist (Unit : Bytes)"
   )
   private Long totalSize = 0L;
   @ApiModelProperty(
      example = "2016-01-01 00:00:00",
      dataType = "timestamp",
      value = "Last modification date",
      required = true
   )
   private String lastModifiedDate = "";
   @ApiModelProperty(
      example = "admin",
      required = true,
      value = "Creator Id of specific playlist, shows the author of a particular playlist.",
      dataType = "string"
   )
   @Size(
      max = 64,
      message = "[V2PlaylistResource][creatorId] max size is 64."
   )
   private String creatorId = "";
   @KPI
   @ApiModelProperty(
      example = "0",
      dataType = "string",
      required = true,
      value = "Playlist type information.\r\n0:PREMIUM, 1:AMS, 2:VWL, 3 : SYNC, 4 : ADV, 5 : TAG, 6 : LINKED",
      allowableValues = "0, 1, 2, 3 , 4, 5, 6"
   )
   private String playlistType = "0";
   @ApiModelProperty(
      example = "1",
      dataType = "long",
      value = "Id of specific version , displays the version Id of the playlist.",
      required = true
   )
   private Long versionId = 1L;
   @Valid
   @ApiModelProperty(
      dataType = "list",
      value = "Resource of the content that composes the playlist",
      reference = "V2PlaylistItemResource"
   )
   private List contents;
   @Valid
   @ApiModelProperty(
      dataType = "list",
      value = "Resource of playlist tag",
      reference = "V2PlaylistTagsResource"
   )
   private List tags;
   @ApiModelProperty(
      dataType = "string",
      value = "Thumbnail file ID to reference when displaying thumbnails of playlist. Refer to GET .../cms/playlists/{playlistId}/details"
   )
   private String thumbFileId = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Thumbnail file path to reference when displaying thumbnails of playlist. Refer to GET .../cms/playlists/{playlistId}/details"
   )
   private String thumbFilePath = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Thumbnail file name to reference when displaying thumbnails of playlist. Refer to GET .../cms/playlists/{playlistId}/details"
   )
   private String thumbFileName = "";
   @ApiModelProperty(
      dataType = "list",
      value = "Resource of category. Refer to GET .../cms/playlists/{playlistId}/details"
   )
   private List categoryList;
   @ApiModelProperty(
      example = "PREMIUM",
      required = true,
      value = "Playlist productType (cannot be found other than PREMIUM)",
      dataType = "string"
   )
   private String productType = "PREMIUM";
   @Valid
   @ApiModelProperty(
      dataType = "list",
      value = "Resource of last memories content",
      reference = "V2AmsInformationResource"
   )
   private List lastMemories;
   @ApiModelProperty(
      example = "1",
      value = "The \"ignoreTag\" is missing data and cannot be found. Only 0 and 1 are supposed to come.",
      dataType = "int"
   )
   private int ignoreTag = 0;
   @ApiModelProperty(
      example = "1",
      required = true,
      value = "The \"evennessPlayback\" cannot be found because it does not have data. Only 0 and 1 are supposed to come.",
      dataType = "int"
   )
   private int evennessPlayback = 0;
   @ApiModelProperty(
      example = "5",
      dataType = "long",
      value = "Default duration of content set for content added to the playlist",
      required = true
   )
   private long defaultContentDuration = 5L;
   @ApiModelProperty(
      example = "false",
      dataType = "boolean",
      value = "Playlist isChangedAMSMode checks the conversion to nested type."
   )
   private boolean isChangedAMSMode = false;
   @ApiModelProperty(
      dataType = "string",
      value = "Playlist amsMode represents an overlay type."
   )
   private String amsMode = "";
   @ApiModelProperty(
      dataType = "boolean",
      value = "Playlist Ams Direct Play is not exactly available.",
      allowableValues = "true, false"
   )
   private boolean amsDirectPlay = true;

   public V2PlaylistResource() {
      super();
   }

   public String getPlaylistId() {
      return this.playlistId;
   }

   public void setPlaylistId(String playlistId) {
      this.playlistId = playlistId;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public void setDeviceType(String deviceType) {
      this.deviceType = deviceType;
   }

   public String getDeviceTypeVersion() {
      return this.deviceTypeVersion;
   }

   public void setDeviceTypeVersion(String deviceTypeVersion) {
      this.deviceTypeVersion = deviceTypeVersion;
   }

   public String getPlaylistName() {
      return this.playlistName;
   }

   public void setPlaylistName(String playlistName) {
      this.playlistName = playlistName;
   }

   public Long getGroupId() {
      return this.groupId;
   }

   public void setGroupId(Long groupId) {
      this.groupId = groupId;
   }

   public String getGroupName() {
      return this.groupName;
   }

   public void setGroupName(String groupName) {
      this.groupName = groupName;
   }

   public int getShareFlag() {
      return this.shareFlag;
   }

   public void setShareFlag(int shareFlag) {
      this.shareFlag = shareFlag;
   }

   public String getMetaData() {
      return this.metaData;
   }

   public void setMetaData(String metaData) {
      this.metaData = metaData;
   }

   public boolean getShuffleFlag() {
      return this.shuffleFlag;
   }

   public void setShuffleFlag(boolean shuffleFlag) {
      this.shuffleFlag = shuffleFlag;
   }

   public int getContentCount() {
      return this.contentCount;
   }

   public void setContentCount(int contentCount) {
      this.contentCount = contentCount;
   }

   public Long getPlayTime() {
      return this.playTime;
   }

   public void setPlayTime(Long playTime) {
      this.playTime = playTime;
   }

   public Long getTotalSize() {
      return this.totalSize;
   }

   public void setTotalSize(Long totalSize) {
      this.totalSize = totalSize;
   }

   public String getLastModifiedDate() {
      return this.lastModifiedDate;
   }

   public void setLastModifiedDate(String lastModifiedDate) {
      this.lastModifiedDate = lastModifiedDate;
   }

   public String getCreatorId() {
      return this.creatorId;
   }

   public void setCreatorId(String creatorId) {
      this.creatorId = creatorId;
   }

   public String getPlaylistType() {
      return this.playlistType;
   }

   public void setPlaylistType(String playlistType) {
      this.playlistType = playlistType;
   }

   public Long getVersionId() {
      return this.versionId;
   }

   public void setVersionId(Long versionId) {
      this.versionId = versionId;
   }

   public List getContents() {
      return this.contents;
   }

   public void setContentList(List contents) {
      this.contents = contents;
   }

   public String getThumbFileId() {
      return this.thumbFileId;
   }

   public void setThumbFileId(String thumbFileId) {
      this.thumbFileId = thumbFileId;
   }

   public String getThumbFilePath() {
      return this.thumbFilePath;
   }

   public void setThumbFilePath(String thumbFilePath) {
      this.thumbFilePath = thumbFilePath;
   }

   public String getThumbFileName() {
      return this.thumbFileName;
   }

   public void setThumbFileName(String thumbFileName) {
      this.thumbFileName = thumbFileName;
   }

   public List getCategoryList() {
      return this.categoryList;
   }

   public void setCategoryList(List categoryList) {
      this.categoryList = categoryList;
   }

   public String getProductType() {
      return this.productType;
   }

   public void setProductType(String productType) {
      this.productType = productType;
   }

   public List getLastMemories() {
      return this.lastMemories;
   }

   public void setLastMemories(List lastMemories) {
      this.lastMemories = lastMemories;
   }

   public int getIgnoreTag() {
      return this.ignoreTag;
   }

   public void setIgnoreTag(int ignoreTag) {
      this.ignoreTag = ignoreTag;
   }

   public int getEvennessPlayback() {
      return this.evennessPlayback;
   }

   public void setEvennessPlayback(int evennessPlayback) {
      this.evennessPlayback = evennessPlayback;
   }

   public long getDefaultContentDuration() {
      return this.defaultContentDuration;
   }

   public void setDefaultContentDuration(long defaultContentDuration) {
      this.defaultContentDuration = defaultContentDuration;
   }

   public boolean getIsChangedAMSMode() {
      return this.isChangedAMSMode;
   }

   public void setIsChangedAMSMode(boolean isChangedAMSMode) {
      this.isChangedAMSMode = isChangedAMSMode;
   }

   public String getAmsMode() {
      return this.amsMode;
   }

   public void setAmsMode(String amsMode) {
      this.amsMode = amsMode;
   }

   public boolean getAmsDirectPlay() {
      return this.amsDirectPlay;
   }

   public void setAmsDirectPlay(boolean amsDirectPlay) {
      this.amsDirectPlay = amsDirectPlay;
   }

   public List getTags() {
      return this.tags;
   }

   public void setTags(List tags) {
      this.tags = tags;
   }
}
