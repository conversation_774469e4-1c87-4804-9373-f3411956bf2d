package com.samsung.magicinfo.rc.common.batch;

import com.samsung.magicinfo.rc.common.batch.CheckingServerAjaxTime;
import com.samsung.magicinfo.rc.common.exception.OpenApiServiceException;
import com.samsung.magicinfo.rc.common.queue.ServerTimeQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
public class RCScheduler {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.common.batch.RCScheduler.class);
  
  @Autowired
  ServerTimeQueue serverTimeQueue;
  
  @Autowired
  CheckingServerAjaxTime checkingServerAjaxTime;
  
  @Autowired
  CacheManager cacheManager;
  
  @Scheduled(cron = "*/60 * * * * *")
  public void job() {
    log.info("[RC][job] check threads");
    try {
      this.serverTimeQueue.timeCheck(90);
    } catch (OpenApiServiceException e) {
      e.printStackTrace();
    } 
    try {
      this.checkingServerAjaxTime.timeCheck(90);
    } catch (OpenApiServiceException e) {
      e.printStackTrace();
    } 
  }
}
