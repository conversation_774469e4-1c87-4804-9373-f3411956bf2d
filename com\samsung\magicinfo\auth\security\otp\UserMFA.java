package com.samsung.magicinfo.auth.security.otp;

import java.sql.Timestamp;

public class UserMFA {
   private String userId;
   private String mfaType;
   private int loginFailCount;
   private boolean authEnable;
   private Timestamp expiredDate;

   public UserMFA() {
      super();
   }

   public String getUserId() {
      return this.userId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }

   public String getMfaType() {
      return this.mfaType;
   }

   public void setMfaType(String mfaType) {
      this.mfaType = mfaType;
   }

   public int getLoginFailCount() {
      return this.loginFailCount;
   }

   public void setLoginFailCount(int loginFailCount) {
      this.loginFailCount = loginFailCount;
   }

   public boolean isAuthEnable() {
      return this.authEnable;
   }

   public void setAuthEnable(boolean authEnable) {
      this.authEnable = authEnable;
   }

   public Timestamp getExpiredDate() {
      return this.expiredDate;
   }

   public void setExpiredDate(Timestamp expiredDate) {
      this.expiredDate = expiredDate;
   }
}
