package com.samsung.magicinfo.framework.scheduler.dao;

import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.db.TypeFilterPagedListInfo;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DaoTools;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.scheduler.entity.DeviceGroup;
import com.samsung.magicinfo.framework.scheduler.entity.MessageAdminEntity;
import com.samsung.magicinfo.framework.scheduler.entity.MessageEntity;
import com.samsung.magicinfo.framework.scheduler.entity.MessageGroup;
import com.samsung.magicinfo.framework.scheduler.entity.MessageSearch;
import com.samsung.magicinfo.framework.scheduler.entity.SelectConditionMessageAdmin;
import com.samsung.magicinfo.framework.scheduler.manager.MesgSearchInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MesgSearchInfoImpl;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import java.lang.reflect.InvocationTargetException;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class MessageInfoDAO extends SqlSessionBaseDao {
   private static final Logger LOGGER = LoggingManagerV2.getLogger(MessageInfoDAO.class);

   public MessageInfoDAO() {
      super();
   }

   public MessageInfoDAO(SqlSession sqlSession) {
      super(sqlSession);
   }

   public boolean checkMesgGroupChange(String strMsgIds, String strNewGroup) throws SQLException {
      String newRootGroup = this.getMessageGroupRoot(Integer.valueOf(strNewGroup));
      if (strMsgIds != null && !"".equals(strMsgIds)) {
         String[] ids = strMsgIds.split(",");
         int len = ids.length;

         for(int i = 0; i < len; ++i) {
            String strMessage = ids[i];
            MessageEntity message = this.getMessage(strMessage, 0);
            int iOldGroup = Long.valueOf(message.getMessage_group_id()).intValue();
            String oldRootGroup = this.getMessageGroupRoot(iOldGroup);
            if (!oldRootGroup.equalsIgnoreCase(newRootGroup)) {
               return false;
            }

            ++i;
         }
      }

      return true;
   }

   public boolean addMessage(MessageEntity message) throws SQLException {
      SqlSession sqlSession = this.openNewSession(false);

      boolean var4;
      try {
         MessageInfoDAOMapper mapper = (MessageInfoDAOMapper)this.getMapper(sqlSession);
         mapper.createMessage(message, this.getStartTimeFromMessage(message));
         mapper.deleteMessageGroup(message.getMessage_id());
         mapper.createMessageGroup(message.getMessage_id(), message.getMessage_group_id());

         for(int i = 0; i < message.getDevice_groups().size(); ++i) {
            long deviceGroupId = ((DeviceGroup)message.getDevice_groups().get(i)).getDevice_group_id();
            mapper.deleteMessageDeviceByDeviceId(deviceGroupId);
            int cnt = mapper.createMessageDevice(message.getMessage_id(), ((DeviceGroup)message.getDevice_groups().get(i)).getDevice_group_id());
            if (cnt != 1) {
               sqlSession.rollback();
               boolean var8 = false;
               return var8;
            }
         }

         sqlSession.commit();
         boolean var15 = true;
         return var15;
      } catch (SQLException var12) {
         sqlSession.rollback();
         LOGGER.error("An unexpected error occurred during message add.", var12);
         var4 = false;
      } finally {
         sqlSession.close();
      }

      return var4;
   }

   private Timestamp getStartTimeFromMessage(MessageEntity message) {
      return Timestamp.valueOf(message.getStart_time());
   }

   public List getMessageList(String messageId) throws SQLException {
      List mList = ((MessageInfoDAOMapper)this.getMapper()).getMessageListByMessageId(messageId);
      Map message_group = ((MessageInfoDAOMapper)this.getMapper()).getMessageGroup(messageId);
      List messageList = new ArrayList();

      MessageEntity mEntity;
      for(Iterator it = mList.iterator(); it.hasNext(); messageList.add(mEntity)) {
         mEntity = (MessageEntity)it.next();
         if (message_group != null) {
            mEntity.setMessage_group_id((Long)message_group.get("group_id"));
            mEntity.setMessage_group_name((String)message_group.get("group_name"));
         }

         if (mEntity.getStart_time() != null) {
            mEntity.setStart_time(mEntity.getStart_time().split(" ", 2)[1]);
         }

         if (mEntity.getMessage_text() != null) {
            String text = mEntity.getMessage_text();
            mEntity.setMessage_text(text.replace("\\", ""));
         }
      }

      return messageList;
   }

   public MessageEntity getMessage(String messageId, int idx) throws SQLException {
      MessageEntity message = ((MessageInfoDAOMapper)this.getMapper()).getMessage(messageId, idx);
      Map message_group = ((MessageInfoDAOMapper)this.getMapper()).getMessageGroup(messageId);
      if (message_group != null) {
         message.setMessage_group_id((Long)message_group.get("group_id"));
         message.setMessage_group_name((String)message_group.get("group_name"));
      }

      if (message != null && message.getMessage_text() != null) {
         message.setMessage_text(message.getMessage_text().replace("\\", ""));
      }

      return message;
   }

   public List getMessageListByMessageIds(String messageIds) throws SQLException {
      if (messageIds != null && !"".equals(messageIds)) {
         MessageInfoDAOMapper mapper = (MessageInfoDAOMapper)this.getMapper();
         List msgList = new ArrayList();
         String[] ids = messageIds.split(":");

         for(int i = 0; i < ids.length; ++i) {
            if (ids[i] != null && !ids[i].equals("")) {
               MessageEntity message = mapper.getMessageByMessageId(ids[i]);
               if (message != null) {
                  message.setMessage_text(message.getMessage_text().replace("\\", ""));
                  msgList.add(message);
               }
            }
         }

         return msgList;
      } else {
         return null;
      }
   }

   public Map getDeviceGroupIdsAndName(String messageId) throws SQLException {
      List result = ((MessageInfoDAOMapper)this.getMapper()).getMessageDeviceGroup(messageId);
      StringBuilder group_ids = new StringBuilder();
      StringBuilder group_names = new StringBuilder();

      for(int count = 0; count < result.size(); ++count) {
         Map map = (Map)result.get(count);
         if (count > 0) {
            group_ids.append(",");
            group_names.append(",");
         }

         group_ids.append(String.valueOf(map.get("DEVICE_GROUP_ID")));
         group_names.append((String)map.get("DEVICE_GROUP_NAME"));
      }

      Map rt = new HashMap();
      rt.put("device_group_ids", group_ids.toString());
      rt.put("group_names", group_names.toString());
      return rt;
   }

   public boolean setMessage(MessageEntity message) throws SQLException {
      SqlSession sqlSession = null;

      boolean var5;
      try {
         int cnt = false;
         sqlSession = this.openNewSession(false);
         MessageInfoDAOMapper mapper = (MessageInfoDAOMapper)this.getMapper(sqlSession);
         mapper.deleteMessageGroup(message.getMessage_id());
         mapper.deleteMessageDevice(message.getMessage_id());
         int cnt = mapper.createMessage(message, this.getStartTimeFromMessage(message));
         if (cnt == 1) {
            mapper.createMessageGroup(message.getMessage_id(), message.getMessage_group_id());

            for(int i = 0; i < message.getDevice_groups().size(); ++i) {
               long deviceGroupId = ((DeviceGroup)message.getDevice_groups().get(i)).getDevice_group_id();
               mapper.deleteMessageDeviceByDeviceId(deviceGroupId);
               mapper.createMessageDevice(message.getMessage_id(), deviceGroupId);
            }

            sqlSession.commit();
            boolean var16 = true;
            return var16;
         }

         sqlSession.rollback();
         var5 = false;
      } catch (SQLException var11) {
         if (sqlSession != null) {
            sqlSession.rollback();
         }

         boolean var4 = false;
         return var4;
      } finally {
         if (sqlSession != null) {
            sqlSession.close();
         }

      }

      return var5;
   }

   public boolean deleteMessage(String messageId) throws SQLException {
      SqlSession sqlSession = null;

      try {
         int cnt = false;
         if (messageId != null && !"".equals(messageId)) {
            sqlSession = this.openNewSession(false);
            MessageInfoDAOMapper mapper = (MessageInfoDAOMapper)this.getMapper(sqlSession);
            String[] ids = messageId.split(":");
            String[] var6 = ids;
            int var7 = ids.length;

            for(int var8 = 0; var8 < var7; ++var8) {
               String id = var6[var8];
               if (!"".equals(id)) {
                  mapper.deleteMessageGroup(id);
                  mapper.deleteMessageDevice(id);
                  int cnt = mapper.deleteMessage(id);
                  if (cnt < 1) {
                     sqlSession.rollback();
                     boolean var10 = false;
                     return var10;
                  }
               }
            }

            sqlSession.commit();
         }

         boolean var17 = true;
         return var17;
      } catch (SQLException var14) {
         if (sqlSession != null) {
            sqlSession.rollback();
         }

         throw var14;
      } finally {
         if (sqlSession != null) {
            sqlSession.close();
         }

      }
   }

   public PagedListInfo getMessageList(Map map, int startPos, int pageSize) throws SQLException {
      SqlSession sqlSession = this.openNewSession(false);

      try {
         MessageInfoDAOMapper mapper = (MessageInfoDAOMapper)this.getMapper(sqlSession);
         SelectConditionMessageAdmin condition = copy((SelectConditionMessageAdmin)map.get("condition"));
         String sortCol = condition.getSort_name();
         String order = condition.getOrder_dir();
         String groupType = condition.getGroupType();
         String strSearchId = condition.getSearch_id();
         String device_type = condition.getDevice_type();
         List deviceTypes = null;
         List childGroups = null;
         MessageSearch messageSearch = null;
         if (condition.getNameLike() != null && !condition.getNameLike().equals("")) {
            condition.setNameLike(condition.getNameLike().replaceAll("_", "^_"));
         }

         if (condition.getSelect_devgroup_ids() != null && !condition.getSelect_devgroup_ids().equals("")) {
            String[] devGroupArr = condition.getSelect_devgroup_ids().split(",");
            if (devGroupArr != null && devGroupArr.length > 0) {
               List devGroupIdsArr = new ArrayList();

               for(int i = 0; i < devGroupArr.length; ++i) {
                  devGroupIdsArr.add(new Integer(devGroupArr[i]));
               }

               condition.setSelect_devgroup_list(devGroupIdsArr);
            }
         }

         if (condition.getStart_modified_date() != null && !condition.getStart_modified_date().equals("")) {
            condition.setStart_modified_date(condition.getStart_modified_date() + " 00:00:00");
         }

         if (condition.getEnd_modified_date() != null && !condition.getEnd_modified_date().equals("")) {
            condition.setEnd_modified_date(condition.getEnd_modified_date() + " 23:59:59");
         }

         if (device_type != null && !device_type.equals("")) {
            deviceTypes = Arrays.asList(device_type.split(","));
         }

         MessageGroupDao messageGroupDao;
         Iterator var19;
         UserGroup userGroup;
         int messageOrgId;
         int group_id;
         UserGroupInfoImpl userGroupInfo;
         List manageGroupList;
         if ("all".equalsIgnoreCase(groupType)) {
            group_id = this.getMessageGroupForUser(condition.getUserRootGroup());
            if (group_id != -1) {
               if ((long)group_id == 0L) {
                  userGroupInfo = UserGroupInfoImpl.getInstance();
                  manageGroupList = userGroupInfo.getUserManageGroupListByUserId(SecurityUtils.getLoginUserId());
                  messageGroupDao = new MessageGroupDao();
                  if (manageGroupList != null && manageGroupList.size() > 0) {
                     childGroups = new ArrayList();
                     var19 = manageGroupList.iterator();

                     while(var19.hasNext()) {
                        userGroup = (UserGroup)var19.next();
                        messageOrgId = messageGroupDao.getMessageGroupForOrg(userGroup.getGroup_name());
                        ((List)childGroups).addAll(this.getChildGroupIdList(messageOrgId, true));
                     }
                  } else {
                     childGroups = this.getChildGroupIdList(group_id, true);
                  }
               } else {
                  childGroups = this.getChildGroupIdList(group_id, true);
               }
            }
         } else if (!"trash".equalsIgnoreCase(groupType)) {
            MessageGroupDao grpDao = new MessageGroupDao();
            MessageGroup messageGroup = grpDao.getGroup(Integer.parseInt(groupType));
            if (messageGroup.getP_group_id() == 0L) {
               childGroups = this.getChildGroupIdList(messageGroup.getGroup_id().intValue(), true);
            }
         } else if (groupType.equalsIgnoreCase("trash")) {
            group_id = this.getMessageGroupForUser(condition.getUserRootGroup());
            if (group_id != -1) {
               childGroups = new ArrayList();
               if ((long)group_id == 0L) {
                  userGroupInfo = UserGroupInfoImpl.getInstance();
                  manageGroupList = userGroupInfo.getUserManageGroupListByUserId(SecurityUtils.getLoginUserId());
                  messageGroupDao = new MessageGroupDao();
                  if (manageGroupList != null && manageGroupList.size() > 0) {
                     var19 = manageGroupList.iterator();

                     while(var19.hasNext()) {
                        userGroup = (UserGroup)var19.next();
                        messageOrgId = messageGroupDao.getMessageGroupForOrg(userGroup.getGroup_name());
                        ((List)childGroups).add((long)messageOrgId);
                     }
                  } else {
                     childGroups = this.getChildGroupIdList(group_id, true);
                  }
               } else {
                  ((List)childGroups).add((long)group_id);
               }
            }
         }

         if (!strSearchId.equals("-1")) {
            messageSearch = this.getWhereClauseByCustomSearch(Long.parseLong(strSearchId));
         }

         Map commonsMap = new HashMap();
         commonsMap.put("TYPE_APLAYER", "APLAYER");
         commonsMap.put("TYPE_WPLAYER", "WPLAYER");
         commonsMap.put("TYPE_SOC", "SPLAYER");
         commonsMap.put("TYPE_SOC2", "S2PLAYER");
         commonsMap.put("TYPE_SOC3", "S3PLAYER");
         commonsMap.put("TYPE_PREMIUM", "iPLAYER");
         commonsMap.put("TYPE_VERSION_1_0", CommonDataConstants.TYPE_VERSION_1_0);
         commonsMap.put("TYPE_VERSION_2_0", CommonDataConstants.TYPE_VERSION_2_0);
         commonsMap.put("TYPE_VERSION_3_0", CommonDataConstants.TYPE_VERSION_3_0);
         long totalCnt = mapper.getMessageCount(condition, deviceTypes, (List)childGroups, messageSearch, commonsMap);
         List typeFilterMapList = mapper.getTypeFilterMap(condition, deviceTypes, (List)childGroups, messageSearch, commonsMap);
         List pgList = mapper.getMessageListMap(condition, deviceTypes, (List)childGroups, messageSearch, sortCol, order, pageSize, DaoTools.offsetStartPost(startPos), commonsMap);
         List newList = new ArrayList();
         Iterator it = pgList.iterator();

         while(it.hasNext()) {
            Map tmp = (Map)it.next();
            String messageId = (String)tmp.get("message_id");
            List groups = mapper.getMessageListGroup(messageId);
            Iterator gIt = groups.iterator();
            StringBuffer groupStr = new StringBuffer();
            StringBuffer groupIdStr = new StringBuffer();
            Map tmpDeviceGroup = null;

            while(gIt.hasNext()) {
               tmpDeviceGroup = (Map)gIt.next();
               if (groupStr.length() == 0) {
                  groupStr.append(tmpDeviceGroup.get("group_name"));
                  groupIdStr.append(tmpDeviceGroup.get("group_id"));
               } else {
                  groupStr.append(", ").append(tmpDeviceGroup.get("group_name"));
                  groupIdStr.append(",").append(tmpDeviceGroup.get("group_id"));
               }
            }

            tmp.put("device_group", groupStr.toString());
            tmp.put("device_group_id", groupIdStr.toString());
            String text = (String)tmp.get("message_text");
            tmp.put("message_text", text.replace("\\", ""));
            newList.add(tmp);
         }

         TypeFilterPagedListInfo pgInfo = new TypeFilterPagedListInfo(newList, (int)totalCnt, typeFilterMapList);
         TypeFilterPagedListInfo var45 = pgInfo;
         return var45;
      } finally {
         sqlSession.close();
      }
   }

   public List getMessageListByCondition(SelectConditionMessageAdmin condition, int startPos, int pageSize) throws SQLException {
      SqlSession sqlSession = this.openNewSession(false);

      try {
         MessageInfoDAOMapper mapper = (MessageInfoDAOMapper)this.getMapper(sqlSession);
         String sortCol = condition.getSort_name();
         String order = condition.getOrder_dir();
         String groupType = condition.getGroupType();
         String strSearchId = condition.getSearch_id();
         String device_type = condition.getDevice_type();
         List deviceTypes = null;
         List childGroups = new ArrayList();
         MessageSearch messageSearch = null;
         if (condition.getNameLike() != null && !condition.getNameLike().equals("")) {
            condition.setNameLike(condition.getNameLike().replaceAll("_", "^_"));
         }

         String[] tempGroupIdArr;
         if (condition.getSelect_devgroup_ids() != null && !condition.getSelect_devgroup_ids().equals("")) {
            tempGroupIdArr = condition.getSelect_devgroup_ids().split(",");
            if (tempGroupIdArr != null && tempGroupIdArr.length > 0) {
               List devGroupIdsArr = new ArrayList();

               for(int i = 0; i < tempGroupIdArr.length; ++i) {
                  devGroupIdsArr.add(new Integer(tempGroupIdArr[i]));
               }

               condition.setSelect_devgroup_list(devGroupIdsArr);
            }
         }

         if (condition.getStart_modified_date() != null && !condition.getStart_modified_date().equals("")) {
            condition.setStart_modified_date(condition.getStart_modified_date() + " 00:00:00");
         }

         if (condition.getEnd_modified_date() != null && !condition.getEnd_modified_date().equals("")) {
            condition.setEnd_modified_date(condition.getEnd_modified_date() + " 23:59:59");
         }

         if (device_type != null && !device_type.equals("")) {
            deviceTypes = Arrays.asList(device_type.split(","));
         }

         if ("all".equalsIgnoreCase(groupType)) {
            int group_id = this.getMessageGroupForUser(condition.getUserRootGroup());
            if (group_id != -1) {
               childGroups = this.getChildGroupIdList(group_id, true);
            }
         } else if (!"trash".equalsIgnoreCase(groupType)) {
            if (groupType.indexOf("c") != -1) {
               tempGroupIdArr = groupType.split(",");

               for(int i = 0; i < tempGroupIdArr.length; ++i) {
                  String tempGroupId = tempGroupIdArr[i];
                  if (tempGroupId.length() > 1) {
                     tempGroupId = tempGroupId.substring(0, tempGroupId.length() - 1);
                     List tempChildGroups = this.getChildGroupIdList(Integer.parseInt(tempGroupId), true);
                     ((List)childGroups).add(Long.valueOf(tempGroupId));
                     if (tempChildGroups.size() > 0) {
                        ((List)childGroups).addAll(tempChildGroups);
                     }
                  }
               }
            } else {
               MessageGroupDao grpDao = new MessageGroupDao();
               MessageGroup messageGroup = grpDao.getGroup(Integer.parseInt(groupType));
               if (messageGroup.getP_group_id() == 0L) {
                  childGroups = this.getChildGroupIdList(messageGroup.getGroup_id().intValue(), true);
               }
            }
         }

         if (!strSearchId.equals("-1")) {
            messageSearch = this.getWhereClauseByCustomSearch(Long.parseLong(strSearchId));
         }

         Map commonsMap = new HashMap();
         commonsMap.put("TYPE_APLAYER", "APLAYER");
         commonsMap.put("TYPE_WPLAYER", "WPLAYER");
         commonsMap.put("TYPE_SOC", "SPLAYER");
         commonsMap.put("TYPE_SOC2", "S2PLAYER");
         commonsMap.put("TYPE_SOC3", "S3PLAYER");
         commonsMap.put("TYPE_PREMIUM", "iPLAYER");
         commonsMap.put("TYPE_VERSION_1_0", CommonDataConstants.TYPE_VERSION_1_0);
         commonsMap.put("TYPE_VERSION_2_0", CommonDataConstants.TYPE_VERSION_2_0);
         commonsMap.put("TYPE_VERSION_3_0", CommonDataConstants.TYPE_VERSION_3_0);
         mapper.getMessageCount(condition, deviceTypes, (List)childGroups, messageSearch, commonsMap);
         mapper.getTypeFilterMap(condition, deviceTypes, (List)childGroups, messageSearch, commonsMap);
         List pgList = mapper.getMessageListMap(condition, deviceTypes, (List)childGroups, messageSearch, sortCol, order, pageSize, DaoTools.offsetStartPost(startPos), commonsMap);
         List newList = new ArrayList();
         Iterator it = pgList.iterator();

         while(it.hasNext()) {
            Map tmp = (Map)it.next();
            String messageId = (String)tmp.get("message_id");
            List groups = mapper.getMessageListGroup(messageId);
            Iterator gIt = groups.iterator();
            StringBuffer groupStr = new StringBuffer();
            StringBuffer groupIdStr = new StringBuffer();
            Map tmpDeviceGroup = null;

            while(gIt.hasNext()) {
               tmpDeviceGroup = (Map)gIt.next();
               if (groupStr.length() == 0) {
                  groupStr.append(tmpDeviceGroup.get("group_name"));
                  groupIdStr.append(tmpDeviceGroup.get("group_id"));
               } else {
                  groupStr.append(", ").append(tmpDeviceGroup.get("group_name"));
                  groupIdStr.append(",").append(tmpDeviceGroup.get("group_id"));
               }
            }

            tmp.put("device_group", groupStr.toString());
            tmp.put("device_group_id", groupIdStr.toString());
            String text = (String)tmp.get("message_text");
            tmp.put("message_text", text.replace("\\", ""));
            newList.add(tmp);
         }

         ArrayList var38 = newList;
         return var38;
      } finally {
         sqlSession.close();
      }
   }

   public Long getMessageListCntByCondition(SelectConditionMessageAdmin condition, int startPos, int pageSize) throws SQLException {
      SqlSession sqlSession = this.openNewSession(false);

      Long var28;
      try {
         MessageInfoDAOMapper mapper = (MessageInfoDAOMapper)this.getMapper(sqlSession);
         String sortCol = condition.getSort_name();
         String order = condition.getOrder_dir();
         String groupType = condition.getGroupType();
         String strSearchId = condition.getSearch_id();
         String device_type = condition.getDevice_type();
         List deviceTypes = null;
         List childGroups = new ArrayList();
         MessageSearch messageSearch = null;
         if (condition.getNameLike() != null && !condition.getNameLike().equals("")) {
            condition.setNameLike(condition.getNameLike().replaceAll("_", "^_"));
         }

         String[] tempGroupIdArr;
         if (condition.getSelect_devgroup_ids() != null && !condition.getSelect_devgroup_ids().equals("")) {
            tempGroupIdArr = condition.getSelect_devgroup_ids().split(",");
            if (tempGroupIdArr != null && tempGroupIdArr.length > 0) {
               List devGroupIdsArr = new ArrayList();

               for(int i = 0; i < tempGroupIdArr.length; ++i) {
                  devGroupIdsArr.add(new Integer(tempGroupIdArr[i]));
               }

               condition.setSelect_devgroup_list(devGroupIdsArr);
            }
         }

         if (condition.getStart_modified_date() != null && !condition.getStart_modified_date().equals("")) {
            condition.setStart_modified_date(condition.getStart_modified_date() + " 00:00:00");
         }

         if (condition.getEnd_modified_date() != null && !condition.getEnd_modified_date().equals("")) {
            condition.setEnd_modified_date(condition.getEnd_modified_date() + " 23:59:59");
         }

         if (device_type != null && !device_type.equals("")) {
            deviceTypes = Arrays.asList(device_type.split(","));
         }

         if ("all".equalsIgnoreCase(groupType)) {
            int group_id = this.getMessageGroupForUser(condition.getUserRootGroup());
            if (group_id != -1) {
               childGroups = this.getChildGroupIdList(group_id, true);
            }
         } else if (!"trash".equalsIgnoreCase(groupType)) {
            if (groupType.indexOf("c") != -1) {
               tempGroupIdArr = groupType.split(",");

               for(int i = 0; i < tempGroupIdArr.length; ++i) {
                  String tempGroupId = tempGroupIdArr[i];
                  if (tempGroupId.length() > 1) {
                     tempGroupId = tempGroupId.substring(0, tempGroupId.length() - 1);
                     List tempChildGroups = this.getChildGroupIdList(Integer.parseInt(tempGroupId), true);
                     ((List)childGroups).add(Long.valueOf(tempGroupId));
                     if (tempChildGroups.size() > 0) {
                        ((List)childGroups).addAll(tempChildGroups);
                     }
                  }
               }
            } else {
               MessageGroupDao grpDao = new MessageGroupDao();
               MessageGroup messageGroup = grpDao.getGroup(Integer.parseInt(groupType));
               if (messageGroup.getP_group_id() == 0L) {
                  childGroups = this.getChildGroupIdList(messageGroup.getGroup_id().intValue(), true);
               }
            }
         }

         if (!strSearchId.equals("-1")) {
            messageSearch = this.getWhereClauseByCustomSearch(Long.parseLong(strSearchId));
         }

         Map commonsMap = new HashMap();
         commonsMap.put("TYPE_APLAYER", "APLAYER");
         commonsMap.put("TYPE_WPLAYER", "WPLAYER");
         commonsMap.put("TYPE_SOC", "SPLAYER");
         commonsMap.put("TYPE_SOC2", "S2PLAYER");
         commonsMap.put("TYPE_SOC3", "S3PLAYER");
         commonsMap.put("TYPE_PREMIUM", "iPLAYER");
         commonsMap.put("TYPE_VERSION_1_0", CommonDataConstants.TYPE_VERSION_1_0);
         commonsMap.put("TYPE_VERSION_2_0", CommonDataConstants.TYPE_VERSION_2_0);
         commonsMap.put("TYPE_VERSION_3_0", CommonDataConstants.TYPE_VERSION_3_0);
         long totalCnt = mapper.getMessageCount(condition, deviceTypes, (List)childGroups, messageSearch, commonsMap);
         var28 = totalCnt;
      } finally {
         sqlSession.close();
      }

      return var28;
   }

   public List getMessageList(SelectConditionMessageAdmin condObj) throws SQLException {
      SqlSession sqlSession = this.openNewSession(false);

      try {
         MessageInfoDAOMapper mapper = (MessageInfoDAOMapper)this.getMapper(sqlSession);
         SelectConditionMessageAdmin condition = copy(condObj);
         String sortCol = condition.getSort_name();
         String order = condition.getOrder_dir();
         String groupType = condition.getGroupType();
         String strSearchId = condition.getSearch_id();
         String device_type = condition.getDevice_type();
         List deviceTypes = null;
         List childGroups = null;
         MessageSearch messageSearch = null;
         if (condition.getNameLike() != null && !condition.getNameLike().equals("")) {
            condition.setNameLike(condition.getNameLike().replaceAll("_", "^_"));
         }

         if (device_type != null && !device_type.equals("")) {
            deviceTypes = Arrays.asList(device_type.split(","));
         }

         if ("all".equalsIgnoreCase(groupType)) {
            int group_id = this.getMessageGroupForUser(condition.getUserRootGroup());
            if (group_id != -1) {
               childGroups = this.getChildGroupIdList(group_id, true);
            }
         } else if (!"trash".equalsIgnoreCase(groupType)) {
            MessageGroupDao grpDao = new MessageGroupDao();
            MessageGroup messageGroup = grpDao.getGroup(Integer.parseInt(groupType));
            if (messageGroup.getP_group_id() == 0L) {
               childGroups = this.getChildGroupIdList(messageGroup.getGroup_id().intValue(), true);
            }
         }

         if (strSearchId != null && !strSearchId.equals("-1")) {
            messageSearch = this.getWhereClauseByCustomSearch(Long.parseLong(strSearchId));
         }

         List pgList = mapper.getMessageList(condition, deviceTypes, childGroups, messageSearch, sortCol, order);
         List newList = new ArrayList();
         Iterator it = pgList.iterator();

         while(it.hasNext()) {
            MessageAdminEntity tmp = (MessageAdminEntity)it.next();
            String messageId = tmp.getMessage_id();
            List groups = mapper.getMessageListGroup(messageId);
            Iterator gIt = groups.iterator();
            StringBuffer groupStr = new StringBuffer();
            StringBuffer groupIdStr = new StringBuffer();
            Map tmpDeviceGroup = null;

            while(gIt.hasNext()) {
               tmpDeviceGroup = (Map)gIt.next();
               if (groupStr.length() == 0) {
                  groupStr.append(tmpDeviceGroup.get("group_name"));
                  groupIdStr.append(tmpDeviceGroup.get("group_id"));
               } else {
                  groupStr.append(", ").append(tmpDeviceGroup.get("group_name"));
                  groupIdStr.append(",").append(tmpDeviceGroup.get("group_id"));
               }
            }

            tmp.setDevice_group_name(groupStr.toString());
            tmp.setDevice_group_id(groupIdStr.toString());
            String text = null;
            text = tmp.getMessage_text();
            tmp.setMessage_text(text.replace("\\", ""));
            newList.add(tmp);
         }

         ArrayList var30 = newList;
         return var30;
      } finally {
         sqlSession.close();
      }
   }

   private MessageSearch getWhereClauseByCustomSearch(Long searchId) throws SQLException {
      MesgSearchInfo searchInfo = MesgSearchInfoImpl.getInstance();
      MessageSearch messageSearch = searchInfo.getMesgSearchBySearchId(searchId);
      if (messageSearch.getMesg_name() != null && !messageSearch.getMesg_name().equals("")) {
         messageSearch.setMesg_name(messageSearch.getMesg_name().replaceAll("_", "^_"));
      }

      if (messageSearch.getUser_name() != null && !messageSearch.getUser_name().equals("")) {
         messageSearch.setUser_name(messageSearch.getUser_name().replaceAll("_", "^_"));
      }

      return messageSearch;
   }

   public List getDevcieByMessageId(String messageId) throws SQLException {
      return ((MessageInfoDAOMapper)this.getMapper()).getDevicesByMessageId(messageId);
   }

   public boolean updateMessageGroup(String groupId, String strMesgId) throws SQLException {
      int cnt = false;
      SqlSession sqlSession = this.openNewSession(false);

      try {
         if (strMesgId == null || "".equals(strMesgId)) {
            boolean var18 = false;
            return var18;
         } else {
            MessageInfoDAOMapper mapper = (MessageInfoDAOMapper)this.getMapper(sqlSession);
            String[] ids = strMesgId.split(",");
            int len = ids.length;

            for(int i = 0; i < len; ++i) {
               String messageId = ids[i];
               if (!"".equals(messageId)) {
                  boolean compatible = this.checkMesgGroupChange(messageId, groupId);
                  int cnt = mapper.updateMessageGroup(Long.valueOf(groupId), messageId);
                  if (cnt <= 0) {
                     sqlSession.rollback();
                     boolean var11 = false;
                     return var11;
                  }

                  if (!compatible) {
                     mapper.deleteMessageDevice(messageId);
                  }
               }
            }

            sqlSession.commit();
            boolean var20 = true;
            return var20;
         }
      } catch (Exception var15) {
         LOGGER.error("An unexpected error occurred during the persistence operation.", var15);
         sqlSession.rollback();
         boolean var6 = false;
         return var6;
      } finally {
         sqlSession.close();
      }
   }

   public boolean setDeleteMessage(String strMesgId) throws SQLException {
      int cnt = false;
      SqlSession sqlSession = null;

      boolean var5;
      try {
         if (strMesgId != null && !"".equals(strMesgId)) {
            sqlSession = this.openNewSession(false);
            MessageInfoDAOMapper mapper = (MessageInfoDAOMapper)this.getMapper(sqlSession);
            String[] ids = strMesgId.split(":");
            int len = ids.length;

            for(int i = 0; i < len; ++i) {
               String messageId = ids[i];
               if (!"".equals(messageId)) {
                  int cnt = mapper.markMessageDeleted(messageId);
                  if (cnt <= 0) {
                     sqlSession.rollback();
                     boolean var21 = false;
                     return var21;
                  }

                  Long groupId = mapper.getMessageGroupId(messageId);
                  MessageGroupDao grpDao = new MessageGroupDao();
                  int orgGrpId = grpDao.getMessageOrgGroupId(groupId.intValue());
                  mapper.updateMessageGroup((long)orgGrpId, messageId);
                  mapper.resetMessageId(messageId);
               }
            }

            sqlSession.commit();
            boolean var20 = true;
            return var20;
         }

         boolean var4 = false;
         return var4;
      } catch (SQLException var15) {
         LOGGER.error("An unexpected error occurred during the persistence operation.", var15);
         if (sqlSession != null) {
            sqlSession.rollback();
         }

         var5 = false;
      } finally {
         if (sqlSession != null) {
            sqlSession.close();
         }

      }

      return var5;
   }

   public boolean unmappMessageId(long groupId) throws SQLException {
      String messageId = "00000000-0000-0000-0000-000000000000";
      int cnt = ((MessageInfoDAOMapper)this.getMapper()).unmappMessageId(messageId, groupId);
      return cnt == 1;
   }

   public String getMessageIdx(String messageId) throws SQLException {
      String result = "";
      List idxList = ((MessageInfoDAOMapper)this.getMapper()).getMessageIdx(messageId);
      int loopSize = idxList.size();

      for(int i = 0; i < loopSize; ++i) {
         if (i == 0) {
            result = (String)idxList.get(i);
         } else {
            result = result + "," + (String)idxList.get(i);
         }
      }

      return result;
   }

   public int getMessageGroupForUser(String strOrg) throws SQLException {
      Integer group = ((MessageInfoDAOMapper)this.getMapper()).getMessageGroupForUser(strOrg);
      return group == null ? -1 : group;
   }

   public List getChildGroupIdList(int group_id, boolean recursive) throws SQLException {
      List rtList = new ArrayList();
      List groupIdList = ((MessageInfoDAOMapper)this.getMapper()).getChildGroupIdList(group_id, 999999);
      if (groupIdList != null) {
         Iterator var5 = groupIdList.iterator();

         while(var5.hasNext()) {
            Long aGroupIdList = (Long)var5.next();
            if (!recursive) {
               rtList.add(aGroupIdList);
            } else {
               rtList.add(aGroupIdList);
               List temp = this.getChildGroupIdList(aGroupIdList.intValue(), recursive);
               if (temp != null && temp.size() != 0) {
                  rtList.addAll(temp);
               }
            }
         }
      }

      return rtList;
   }

   public String getMessageGroupRoot(int groupId) throws SQLException {
      Map info = ((MessageInfoDAOMapper)this.getMapper()).getMessageGroupRoot(groupId);
      int newGroupParentId = ((Long)info.get("P_GROUP_ID")).intValue();
      return newGroupParentId != 0 ? this.getMessageGroupRoot(newGroupParentId) : (String)info.get("GROUP_NAME");
   }

   public Long getMessageGroupRootId(int groupId) throws SQLException {
      Map info = ((MessageInfoDAOMapper)this.getMapper()).getMessageGroupRoot(groupId);
      int newGroupParentId = ((Long)info.get("P_GROUP_ID")).intValue();
      return newGroupParentId != 0 ? this.getMessageGroupRootId(newGroupParentId) : (Long)info.get("GROUP_ID");
   }

   public String getDeviceGroupRoot(int groupId) throws SQLException {
      Map info = ((MessageInfoDAOMapper)this.getMapper()).getDeviceGroupRoot(groupId);
      int newGroupParentId = ((Long)info.get("P_GROUP_ID")).intValue();
      return newGroupParentId != 0 ? this.getDeviceGroupRoot(newGroupParentId) : (String)info.get("GROUP_NAME");
   }

   public boolean isMessageNameUnique(String message_name, String message_id, int messGrpId) throws SQLException {
      List childGroups = null;

      try {
         long root_group_id = this.getMessageGroupRootId(messGrpId);
         if (root_group_id != -1L) {
            childGroups = this.getChildGroupIdList((int)root_group_id, true);
            childGroups.add(root_group_id);
         }

         return ((MessageInfoDAOMapper)this.getMapper()).isMessageNameUnique(message_id, message_name, childGroups) == 0;
      } catch (SQLException var7) {
         throw var7;
      }
   }

   public boolean recoverSchedule(String strMessageId, String messageGrpId) throws SQLException {
      SqlSession sqlSession = this.openNewSession(false);

      boolean var6;
      try {
         MessageInfoDAOMapper mapper = (MessageInfoDAOMapper)this.getMapper(sqlSession);
         int cnt = mapper.unmarkMessageDeleted(strMessageId);
         if (cnt > 0) {
            mapper.deleteMessageDevice(strMessageId);
            cnt = mapper.updateMessageGroup(Long.valueOf(messageGrpId), strMessageId);
            if (cnt <= 0) {
               sqlSession.rollback();
               var6 = false;
               return var6;
            }

            sqlSession.commit();
            var6 = true;
            return var6;
         }

         sqlSession.rollback();
         var6 = false;
         return var6;
      } catch (Exception var10) {
         LOGGER.error("An unexpected error occurred during the persistence operation.", var10);
         sqlSession.rollback();
         var6 = false;
      } finally {
         sqlSession.close();
      }

      return var6;
   }

   public List getDeviceMessageMapList(String message_id) throws SQLException {
      return ((MessageInfoDAOMapper)this.getMapper()).getDeviceMessageMapList(message_id);
   }

   public boolean mapDeviceGroupWithDefaultMessage(Long dev_grp_id, String message_id) throws SQLException {
      return ((MessageInfoDAOMapper)this.getMapper()).createMessageDevice(message_id, dev_grp_id) == 1;
   }

   public List getMappedMessageIdByGroupId(String group_id) throws SQLException {
      return ((MessageInfoDAOMapper)this.getMapper()).getMappedMessageIdByGroupId(Long.parseLong(group_id));
   }

   public String getIsInstantByMessageId(String message_id) throws SQLException {
      return ((MessageInfoDAOMapper)this.getMapper()).isInstant(message_id);
   }

   private static SelectConditionMessageAdmin copy(SelectConditionMessageAdmin condition) {
      try {
         SelectConditionMessageAdmin copy = new SelectConditionMessageAdmin();
         BeanUtils.copyProperties(copy, condition);
         return copy;
      } catch (IllegalAccessException var2) {
         throw new RuntimeException("Could not copy the properties of the predicate object.");
      } catch (InvocationTargetException var3) {
         throw new RuntimeException("Could not copy the properties of the predicate object.");
      }
   }

   public String getDeviceTypeByMessageId(String message_id) throws SQLException {
      String returnValue = "SPLAYER";
      String result = "";

      try {
         result = ((MessageInfoDAOMapper)this.getMapper()).getDeviceTypeByMessageId(message_id);
         if (result != null && result.length() > 0) {
            returnValue = result;
         }
      } catch (Exception var5) {
         LOGGER.error("An unexpected error occurred during the persistence operation.", var5);
      }

      return returnValue;
   }

   public Float getDeviceTypeVersionByMessageId(String message_id) throws SQLException {
      Float returnValue = CommonDataConstants.TYPE_VERSION_1_0;
      Float result = 0.0F;

      try {
         result = ((MessageInfoDAOMapper)this.getMapper()).getDeviceTypeVersionByMessageId(message_id);
         if (result != null && result > 0.0F) {
            returnValue = result;
         }
      } catch (Exception var5) {
         LOGGER.error("An unexpected error occurred during the persistence operation.", var5);
      }

      return returnValue;
   }

   public boolean updateDeviceGroup(String strGroupId, String strProgId, String userId) throws SQLException {
      int cnt = false;
      if (strProgId != null && !"".equals(strProgId)) {
         try {
            int cnt = ((MessageInfoDAOMapper)this.getMapper()).deleteMessageDevice(strProgId);
            String[] ids = strGroupId.split(",");
            int len = ids.length;

            for(int i = 0; i < len; ++i) {
               String strGroup = ids[i];
               if (!strGroup.equals("")) {
                  ((MessageInfoDAOMapper)this.getMapper()).deleteMessageDeviceByDeviceId(Long.valueOf(strGroup));
                  cnt = ((MessageInfoDAOMapper)this.getMapper()).createMessageDevice(strProgId, Long.valueOf(strGroup));
                  if (cnt <= 0) {
                     return false;
                  }
               }
            }

            return true;
         } catch (Exception var9) {
            return false;
         }
      } else {
         return false;
      }
   }

   public boolean deleteMessageWithIdx(String message_id, int idx) throws SQLException {
      try {
         MessageInfoDAOMapper mapper = (MessageInfoDAOMapper)this.getMapper();
         long idxCnt = mapper.countMessagesById(message_id);
         if (idxCnt == 1L) {
            return false;
         } else {
            int cnt = mapper.deleteMessageWithIdx(message_id, idx);
            if (cnt == 0) {
               return false;
            } else {
               for(int i = idx + 1; (long)i < idxCnt; ++i) {
                  mapper.decreaseMessageIdx(message_id, i);
               }

               return true;
            }
         }
      } catch (SQLException var8) {
         throw var8;
      }
   }

   public boolean setMessageWithIdx(MessageEntity message) throws SQLException {
      String start_time_str = message.getStart_date() + " " + message.getStart_time();
      Timestamp startTime = Timestamp.valueOf(start_time_str);
      ((MessageInfoDAOMapper)this.getMapper()).deleteMessageGroup(message.getMessage_id());
      ((MessageInfoDAOMapper)this.getMapper()).deleteMessageDevice(message.getMessage_id());
      ((MessageInfoDAOMapper)this.getMapper()).createMessageGroup(message.getMessage_id(), message.getMessage_group_id());

      for(int i = 0; i < message.getDevice_groups().size(); ++i) {
         long deviceGroupId = ((DeviceGroup)message.getDevice_groups().get(i)).getDevice_group_id();
         ((MessageInfoDAOMapper)this.getMapper()).deleteMessageDeviceByDeviceId(deviceGroupId);
         ((MessageInfoDAOMapper)this.getMapper()).createMessageDevice(message.getMessage_id(), deviceGroupId);
      }

      ((MessageInfoDAOMapper)this.getMapper()).setMessageWithIdx(message, startTime);
      return true;
   }

   public long getAllScheduleCount() throws SQLException {
      return ((MessageInfoDAOMapper)this.getMapper()).getAllScheduleCount();
   }

   public List getAllScheduleGroupId() throws SQLException {
      return ((MessageInfoDAOMapper)this.getMapper()).getAllScheduleGroupId();
   }

   public List getDeletedMessageIdList() throws SQLException {
      return ((MessageInfoDAOMapper)this.getMapper()).getDeletedMessageIdList();
   }

   public boolean updateMessageView(MessageEntity message) {
      SqlSession sqlSession = this.openNewSession(false);

      boolean var4;
      try {
         ((MessageInfoDAOMapper)this.getMapper(sqlSession)).updateMessageView(message);
         ((MessageInfoDAOMapper)this.getMapper(sqlSession)).updateMessagesGroupId(message.getMessage_id(), message.getMessage_group_id());
         sqlSession.commit();
         return true;
      } catch (Exception var8) {
         sqlSession.rollback();
         LOGGER.error("An unexpected error occurred during message update .", var8);
         var4 = false;
      } finally {
         sqlSession.close();
      }

      return var4;
   }

   public boolean updateMessageModifiedDate(String messageId) throws SQLException {
      return ((MessageInfoDAOMapper)this.getMapper()).updateMessageModifiedDate(messageId);
   }

   public String getOrganiationByMessageId(String messageId) throws SQLException {
      return ((MessageInfoDAOMapper)this.getMapper()).getOrganiationByMessageId(messageId);
   }

   public String getCreatorIdByMessageId(String messageId) throws SQLException {
      String retVal = "";
      String result = ((MessageInfoDAOMapper)this.getMapper()).getCreatorIdByMessageId(messageId);
      if (result != null) {
         retVal = result;
      }

      return retVal;
   }

   public long getGroupIdByMessageId(String messageId) throws SQLException {
      return ((MessageInfoDAOMapper)this.getMapper()).getGroupIdByMessageId(messageId);
   }

   public boolean deleteMessageByDeviceGroupId(SqlSession sqlSession, Long groupId) throws SQLException {
      if (sqlSession == null) {
         sqlSession = this.openNewSession(false);
      }

      ((MessageInfoDAOMapper)this.getMapper(sqlSession)).deleteMessageDeviceByDeviceId(groupId);
      return true;
   }
}
