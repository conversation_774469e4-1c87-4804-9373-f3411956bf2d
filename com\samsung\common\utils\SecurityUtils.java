package com.samsung.common.utils;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.manager.DesEncryptionManagerImpl;
import com.samsung.magicinfo.framework.common.manager.EncryptionManager;
import com.samsung.magicinfo.framework.common.manager.EncryptionManagerImpl;
import com.samsung.magicinfo.framework.common.manager.ExtendedPasswordEncoder;
import com.samsung.magicinfo.framework.common.manager.ExtendedPasswordEncoderImpl;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.security.manager.SecurityInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.openapi.auth.TokenRegistry;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.util.TimeUtil;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.Socket;
import java.net.URI;
import java.security.KeyManagementException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Locale;
import java.util.Properties;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.KeyManager;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509ExtendedTrustManager;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;
import org.owasp.esapi.errors.ValidationException;
import org.springframework.security.core.context.SecurityContextHolder;

public class SecurityUtils {
   static Logger logger = LoggingManagerV2.getLogger(SecurityUtils.class);
   public static final String ANONYMOUS_USER = "anonymousUser";
   public static final int KEY_TYPE_SECRET_STRING = 0;
   public static final int KEY_TYPE_FTP_PASSWORD = 1;
   public static final int KEY_TYPE_VNC_PASSWORD = 2;
   public static final int KEY_TYPE_DEF_MAC = 3;
   public static final int KEY_TYPE_DEF_CPUID = 4;
   public static final int KEY_TYPE_DEF_BOARDID = 5;
   private static final String DEFAULT_SESSION_TIMEOUT = "1800";
   private static ExtendedPasswordEncoder passwordEncoder;
   private static TokenRegistry tokenRegistry;
   private static String policySrc;
   public static String FTP_SECRET_KEY;
   public static String FTP_SECRET_KEY_V7;
   public static String VNC_DEFAULT_PASSWORD;
   public static String CIPHER_AES;
   public static String CIPHER_AES_BIT;

   public SecurityUtils() {
      super();
   }

   public static UserContainer getUserContainer() {
      UserContainer userContainer = null;

      try {
         if (SecurityContextHolder.getContext().getAuthentication() != null && !SecurityContextHolder.getContext().getAuthentication().getPrincipal().equals("anonymousUser")) {
            userContainer = (UserContainer)SecurityContextHolder.getContext().getAuthentication().getPrincipal();
         }
      } catch (Exception var2) {
         logger.error("", var2);
      }

      return userContainer;
   }

   public static boolean isValidToken(String token) {
      return tokenRegistry.isValidToken(token);
   }

   public static String getUserDateFormat() {
      String df = null;
      User currentUser = null;
      if (SecurityContextHolder.getContext().getAuthentication() != null && !SecurityContextHolder.getContext().getAuthentication().getPrincipal().equals("anonymousUser")) {
         currentUser = ((UserContainer)SecurityContextHolder.getContext().getAuthentication().getPrincipal()).getUser();
      }

      if (currentUser != null && currentUser.getDate_format() != null && !currentUser.getDate_format().equals("")) {
         df = currentUser.getDate_format();
      } else {
         df = "YYYY-MM-dd";
      }

      return df;
   }

   public static String getUserTimeFormat() {
      String tf = null;
      User currentUser = null;
      if (SecurityContextHolder.getContext().getAuthentication() != null && !SecurityContextHolder.getContext().getAuthentication().getPrincipal().equals("anonymousUser")) {
         currentUser = ((UserContainer)SecurityContextHolder.getContext().getAuthentication().getPrincipal()).getUser();
      }

      if (currentUser != null && currentUser.getTime_format() != null) {
         String var2 = currentUser.getTime_format();
         byte var3 = -1;
         switch(var2.hashCode()) {
         case 2998057:
            if (var2.equals("ampm")) {
               var3 = 0;
            }
            break;
         case 1482689318:
            if (var2.equals("24hour")) {
               var3 = 1;
            }
         }

         switch(var3) {
         case 0:
            tf = "hh:mm a";
            break;
         case 1:
            tf = "HH:mm";
            break;
         default:
            tf = "HH:mm";
         }
      } else {
         tf = "HH:mm";
      }

      return tf;
   }

   public static int getMaxInactiveInterval() {
      try {
         return Integer.valueOf("3600");
      } catch (NumberFormatException var1) {
         return Integer.valueOf("3600");
      }
   }

   public static void encryptUserPasswordIfNecessary(User user) {
      user.setPassword(getEncryptedPassword(user.getPassword()));
   }

   public static String getEncryptedPassword(String password) {
      return !passwordEncoder.isEncrypted(password) ? passwordEncoder.encode(password) : password;
   }

   public static boolean matchPassword(String rawPassword, String encodedPassword) {
      return passwordEncoder.matches(rawPassword, encodedPassword);
   }

   public static String directoryTraversalChecker(String str, String clientIP) {
      String retStr = null;
      if (str != null) {
         str = str.replace("/", File.separator);
         retStr = str.replace("..\\", "");
         if (!str.equalsIgnoreCase(retStr)) {
            if (clientIP != null) {
               logger.fatal("invalid input string for security - str :" + str + ", retStr :" + retStr + ", client IP :" + clientIP);
            } else {
               logger.fatal("invalid input string for security - str :" + str + ", retStr :" + retStr);
            }
         }
      }

      int strCount = StringUtils.countMatches(str, File.separator);
      int retStrCount = StringUtils.countMatches(retStr, File.separator);
      if (strCount != retStrCount) {
         if (clientIP != null) {
            logger.fatal("Critical invalid input string for security - str :" + str + ", retStr :" + retStr + ", client IP :" + clientIP);
         } else {
            logger.fatal("Critical invalid input string for security - str :" + str + ", retStr :" + retStr);
         }

         retStr = str.replace("..", "");
      }

      logger.debug("checked by directoryTraversalChecker - str :" + str + ", retStr :" + retStr);
      logger.debug("checked by directoryTraversalChecker - strCount :" + strCount + ", retStrCount :" + retStrCount);
      return retStr;
   }

   public static File getSafeFile(String path) {
      try {
         return new SafeFile(path);
      } catch (ValidationException var2) {
         logger.error(var2.getLogMessage());
      } catch (Exception var3) {
         logger.error("Error during SafeFile generation - path : " + path);
      }

      return new File(path);
   }

   public static File getSafeFile(URI uri) {
      try {
         return new SafeFile(uri);
      } catch (ValidationException var2) {
         logger.error(var2.getLogMessage());
      } catch (Exception var3) {
         if (uri != null) {
            logger.fatal("Error during SafeFile generation - uri : " + uri.toString(), var3);
            return new File(uri);
         }

         logger.fatal("Error during SafeFile generation - uri : null", var3);
      }

      return null;
   }

   public static File getSafeFile(File parent, String child) {
      try {
         return new SafeFile(parent, child);
      } catch (ValidationException var3) {
         logger.error(var3.getLogMessage());
      } catch (Exception var4) {
         if (parent == null) {
            logger.fatal("Error during SafeFile generation - parent : null , child : " + child, var4);
         } else {
            logger.fatal("Error during SafeFile generation - parent : " + parent.toString() + " , child : " + child, var4);
         }
      }

      String filePath = directoryTraversalChecker(parent + File.separator + child, (String)null);
      return new File(filePath);
   }

   public static File getSafeFile(String parent, String child) {
      try {
         return new SafeFile(parent, child);
      } catch (ValidationException var3) {
         logger.error(var3.getLogMessage());
      } catch (Exception var4) {
         logger.fatal("Error during SafeFile generation - parent : " + parent + " , child : " + child, var4);
      }

      String filePath = directoryTraversalChecker(parent + File.separator + child, (String)null);
      return new File(filePath);
   }

   public static String getLoginUserId() {
      return getUserContainer() == null ? "" : getUserContainer().getUser().getUser_id();
   }

   public static User getLoginUser() {
      return getUserContainer() == null ? null : getUserContainer().getUser();
   }

   public static String getLoginUserOrganization() {
      return getUserContainer() == null ? "" : getUserContainer().getUser().getOrganization();
   }

   public static Long getLoginUserOrganizationId() throws SQLException {
      if (getUserContainer() == null) {
         return null;
      } else {
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         Long orgId = userGroupInfo.getOrgGroupIdByName(getLoginUser().getOrganization());
         return orgId;
      }
   }

   public static String issueToken() throws ConfigException {
      return StrUtils.nvl(CommonConfig.get("saas.no_token.enable")).equalsIgnoreCase("TRUE") ? tokenRegistry.onloadUserContainer(getUserContainer()) : tokenRegistry.issueToken(getUserContainer());
   }

   public static Locale getLocale() {
      Locale locale = null;
      if (getUserContainer() != null && getUserContainer().getUser().getLocale() != null && !getUserContainer().getUser().getLocale().equals("")) {
         String localeStr = getUserContainer().getUser().getLocale();
         String[] languages = new String[]{"de", "en", "es", "fr", "it", "ja", "ko", "pt", "ru", "sv", "tr", "zh", "ar", "fa", "pl", "vi", "fi"};
         String[] var3 = languages;
         int var4 = languages.length;

         for(int var5 = 0; var5 < var4; ++var5) {
            String compareStr = var3[var5];
            if (localeStr.startsWith(compareStr)) {
               if ((localeStr.equals("zh_CN") || localeStr.equals("zh_cn") || localeStr.equals("zh_TW") || localeStr.equals("zh_tw")) && localeStr.indexOf("_") > 0) {
                  String[] zhLocale = localeStr.split("_");
                  locale = new Locale("zh", zhLocale[1]);
               } else {
                  locale = new Locale(compareStr);
               }
            }
         }
      } else {
         locale = new Locale("en");
      }

      return locale;
   }

   public static boolean checkReadPermissionWithOrgAndId(String type, String id) throws Exception {
      if (getUserContainer() == null) {
         return false;
      } else {
         String organizationFromUser = null;
         User loginUser = getUserContainer().getUser();
         if (loginUser != null && loginUser.getOrganization() != null) {
            organizationFromUser = loginUser.getOrganization();
            if (organizationFromUser.equals("ROOT")) {
               UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
               userGroupInfo.getUserManageGroupListByUserId(loginUser.getUser_id());
               return true;
            } else {
               try {
                  AbilityUtils ability = new AbilityUtils();
                  byte var6 = -1;
                  switch(type.hashCode()) {
                  case -1678783399:
                     if (type.equals("Content")) {
                        var6 = 0;
                     }
                     break;
                  case -1675388953:
                     if (type.equals("Message")) {
                        var6 = 3;
                     }
                     break;
                  case -644372944:
                     if (type.equals("Setting")) {
                        var6 = 8;
                     }
                     break;
                  case -633276745:
                     if (type.equals("Schedule")) {
                        var6 = 2;
                     }
                     break;
                  case 2645995:
                     if (type.equals("User")) {
                        var6 = 6;
                     }
                     break;
                  case 67338874:
                     if (type.equals("Event")) {
                        var6 = 4;
                     }
                     break;
                  case 1898876227:
                     if (type.equals("Statistics")) {
                        var6 = 7;
                     }
                     break;
                  case 1944118770:
                     if (type.equals("Playlist")) {
                        var6 = 1;
                     }
                     break;
                  case 2043677302:
                     if (type.equals("Device")) {
                        var6 = 5;
                     }
                  }

                  switch(var6) {
                  case 0:
                     ContentInfo contentInfo = ContentInfoImpl.getInstance();
                     if (ability.checkAuthority("Content Read") && contentInfo.chkOrganizationByContentId(organizationFromUser, id)) {
                        return true;
                     }
                     break;
                  case 1:
                     PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
                     if (ability.checkAuthority("Content Read") && playlistInfo.chkOrganizationByPlaylistId(organizationFromUser, id)) {
                        return true;
                     }
                     break;
                  case 2:
                     ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
                     if (ability.checkAuthority("Content Schedule Read") && scheduleInfo.chkOrganizationByProgramId(organizationFromUser, id)) {
                        return true;
                     }
                     break;
                  case 3:
                     MessageInfo messageInfo = MessageInfoImpl.getInstance();
                     if (ability.checkAuthority("Content Schedule Read") && messageInfo.chkOrganizationByMessageId(organizationFromUser, id)) {
                        return true;
                     }
                     break;
                  case 4:
                     EventInfo eventInfo = EventInfoImpl.getInstance();
                     if (ability.checkAuthority("Content Schedule Read") && eventInfo.chkOrganizationByEventScheduleId(organizationFromUser, id)) {
                        return true;
                     }
                     break;
                  case 5:
                     DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
                     if (ability.checkAuthority("Device Read") && deviceInfo.chkOrganizationByDeviceId(organizationFromUser, id)) {
                        return true;
                     }
                     break;
                  case 6:
                     UserInfo userInfo = UserInfoImpl.getInstance();
                     if (ability.checkAuthority("User Read") && userInfo.chkOrganizationByUserId(organizationFromUser, id)) {
                        return true;
                     }
                  case 7:
                  case 8:
                  }
               } catch (Exception var14) {
               }

               return false;
            }
         } else {
            return false;
         }
      }
   }

   public static boolean checkWritePermissionWithOrgAndId(String type, String id) throws Exception {
      if (getUserContainer() == null) {
         return false;
      } else {
         String organizationFromUser = null;
         User loginUser = getUserContainer().getUser();
         if (StringUtils.equalsIgnoreCase(loginUser.getIs_admin(), "Y")) {
            return true;
         } else if (loginUser != null && loginUser.getOrganization() != null) {
            organizationFromUser = loginUser.getOrganization();
            if (organizationFromUser.equals("ROOT")) {
               UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
               userGroupInfo.getUserManageGroupListByUserId(loginUser.getUser_id());
               return true;
            } else {
               try {
                  AbilityUtils ability = new AbilityUtils();
                  byte var6 = -1;
                  switch(type.hashCode()) {
                  case -1678783399:
                     if (type.equals("Content")) {
                        var6 = 0;
                     }
                     break;
                  case 2645995:
                     if (type.equals("User")) {
                        var6 = 2;
                     }
                     break;
                  case 1944118770:
                     if (type.equals("Playlist")) {
                        var6 = 1;
                     }
                  }

                  switch(var6) {
                  case 0:
                     ContentInfo contentInfo = ContentInfoImpl.getInstance();
                     if ((ability.checkAuthority("Content Write") || ability.checkAuthority("Content Manage")) && contentInfo.chkOrganizationByContentId(organizationFromUser, id)) {
                        return true;
                     }
                     break;
                  case 1:
                     PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
                     if ((ability.checkAuthority("Content Write") || ability.checkAuthority("Content Manage") || ability.checkAuthority("Playlist Write") || ability.checkAuthority("Playlist Manage")) && playlistInfo.chkOrganizationByPlaylistId(organizationFromUser, id)) {
                        return true;
                     }
                     break;
                  case 2:
                     UserInfo userInfo = UserInfoImpl.getInstance();
                     if (ability.checkAuthority("User Write") && userInfo.chkOrganizationByUserId(organizationFromUser, id)) {
                        return true;
                     }
                  }
               } catch (Exception var10) {
               }

               return false;
            }
         } else {
            return false;
         }
      }
   }

   public static boolean checkCMDInjection(String[] cmdArr) {
      boolean isValid = false;
      String[] cmdPath = cmdArr[0].split("\\\\");
      String cmd = cmdPath[cmdPath.length - 1];
      if (cmd.equalsIgnoreCase("copyBat.bat")) {
         isValid = true;
      } else if (cmd.equalsIgnoreCase("ffmpeg.exe")) {
         isValid = true;
      } else if (cmd.equalsIgnoreCase("MIDiagTool.exe")) {
         isValid = true;
      }

      return isValid;
   }

   public static String getHashSha(String origin, int ver) {
      String result = "";

      try {
         String algorithm = null;
         switch(ver) {
         case 2:
            algorithm = "SHA-256";
            break;
         case 5:
            algorithm = "SHA-512";
            break;
         default:
            algorithm = "SHA-512";
         }

         MessageDigest messageDigest = MessageDigest.getInstance(algorithm);
         messageDigest.update(origin.getBytes());
         byte[] mb = messageDigest.digest();

         for(int i = 0; i < mb.length; ++i) {
            byte temp = mb[i];

            String s;
            for(s = Integer.toHexString(new Byte(temp)); s.length() < 2; s = "0" + s) {
            }

            s = s.substring(s.length() - 2);
            result = result + s;
         }

         return result;
      } catch (Exception var9) {
         return null;
      }
   }

   public static String getHashSha(String origin, int length, int ver) {
      String result = "";

      try {
         String algorithm = null;
         switch(ver) {
         case 2:
            algorithm = "SHA-256";
            break;
         case 5:
            algorithm = "SHA-512";
            break;
         default:
            algorithm = "SHA-512";
         }

         MessageDigest messageDigest = MessageDigest.getInstance(algorithm);
         messageDigest.update(origin.getBytes());
         byte[] mb = messageDigest.digest();

         for(int i = 0; i < mb.length; ++i) {
            byte temp = mb[i];

            String s;
            for(s = Integer.toHexString(new Byte(temp)); s.length() < 2; s = "0" + s) {
            }

            s = s.substring(s.length() - 2);
            result = result + s;
         }

         return result.length() > length ? result.substring(0, length) : result;
      } catch (Exception var10) {
         return null;
      }
   }

   public static String getPincodeEncryptionKey(String deviceId) {
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      String hashedEncKey = null;

      try {
         Timestamp bootstrap_time = deviceInfo.getDevice(deviceId).getBootstrap_time();
         String encKey = TimeUtil.getTimeStr(bootstrap_time) + deviceId + getFtpSecretKeyV7();
         hashedEncKey = getHashSha(encKey, 16, 2);
      } catch (SQLException var5) {
         logger.error("", var5);
      }

      logger.info(deviceId + " Enc Key : " + hashedEncKey);
      return hashedEncKey;
   }

   public static String getServerEncryptionKey(String prefix) throws Exception {
      String hashedEncKey = null;
      SecurityInfoImpl securityInfo = SecurityInfoImpl.getInstance();

      try {
         hashedEncKey = getHashSha(prefix + securityInfo.getSecretKey() + getFtpSecretKeyV7(), 16, 2);
      } catch (Exception var4) {
         logger.error("", var4);
      }

      return hashedEncKey;
   }

   public static void trustAllCertificates() {
      try {
         TrustManager[] trustAllCerts = new TrustManager[]{new X509ExtendedTrustManager() {
            public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
            }

            public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
            }

            public X509Certificate[] getAcceptedIssuers() {
               return null;
            }

            public void checkClientTrusted(X509Certificate[] x509Certificates, String s, Socket socket) throws CertificateException {
            }

            public void checkServerTrusted(X509Certificate[] x509Certificates, String s, Socket socket) throws CertificateException {
            }

            public void checkClientTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) throws CertificateException {
            }

            public void checkServerTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) throws CertificateException {
            }
         }};
         SSLContext sslContext = SSLContext.getInstance("SSL");
         sslContext.init((KeyManager[])null, trustAllCerts, new SecureRandom());
         HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
      } catch (KeyManagementException var2) {
         logger.error("[MagicInfo_SecurityUtils] KeyManagementException e : " + var2.getMessage());
      } catch (NoSuchAlgorithmException var3) {
         logger.error("[MagicInfo_SecurityUtils] NoSuchAlgorithmException e : " + var3.getMessage());
      }

   }

   public static boolean checkDeviceApprovalPermission() {
      try {
         User loginUser = getLoginUser();
         if (loginUser == null) {
            return false;
         }

         AbilityUtils ability = new AbilityUtils();
         if (ability.checkAuthority("Device Only Approval")) {
            return true;
         }
      } catch (Exception var2) {
         logger.error("[MagicInfo_SecurityUtils] " + var2.getMessage());
      }

      return false;
   }

   public static boolean checkContentApprovalPermission() {
      try {
         User loginUser = getLoginUser();
         if (loginUser == null) {
            return false;
         }

         AbilityUtils ability = new AbilityUtils();
         if (ability.checkAuthority("Content Manage")) {
            return true;
         }
      } catch (Exception var2) {
         logger.error("[MagicInfo_SecurityUtils] " + var2.getMessage());
      }

      return false;
   }

   public static boolean checkUserApprovalPermission() {
      try {
         User loginUser = getLoginUser();
         if (loginUser == null) {
            return false;
         }

         AbilityUtils ability = new AbilityUtils();
         if (ability.checkAuthority("User Approval")) {
            return true;
         }
      } catch (Exception var2) {
         logger.error("[MagicInfo_SecurityUtils] " + var2.getMessage());
      }

      return false;
   }

   public static boolean checkContentReadPermission() {
      try {
         User loginUser = getLoginUser();
         if (loginUser == null) {
            return false;
         }

         AbilityUtils ability = new AbilityUtils();
         if (ability.checkAuthority("Content Read")) {
            return true;
         }
      } catch (Exception var2) {
         logger.error("[MagicInfo_SecurityUtils] " + var2.getMessage());
      }

      return false;
   }

   public static boolean checkScheduleReadPermission() {
      try {
         User loginUser = getLoginUser();
         if (loginUser == null) {
            return false;
         }

         AbilityUtils ability = new AbilityUtils();
         if (ability.checkAuthority("Content Schedule Read")) {
            return true;
         }
      } catch (Exception var2) {
         logger.error("[MagicInfo_SecurityUtils] " + var2.getMessage());
      }

      return false;
   }

   public static String getSecurityPolicySrc() {
      policySrc = "";

      try {
         if (CommonConfig.get("security.filter.src") != null) {
            String src = CommonConfig.get("security.filter.src").trim();
            policySrc = src.replaceAll(",", " ");
         }
      } catch (ConfigException var1) {
         logger.error("", var1);
      }

      return policySrc;
   }

   public static String getFtpSecretKey() {
      if (FTP_SECRET_KEY == null) {
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();

         try {
            FTP_SECRET_KEY = serverSetupDao.getSecretValue(1);
         } catch (SQLException var2) {
            FTP_SECRET_KEY = null;
            logger.fatal("FTP Secret key is null", var2);
         }
      }

      return FTP_SECRET_KEY;
   }

   public static String getFtpSecretKeyV7() {
      if (FTP_SECRET_KEY_V7 == null) {
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();

         try {
            FTP_SECRET_KEY_V7 = serverSetupDao.getSecretValue(0);
         } catch (SQLException var2) {
            FTP_SECRET_KEY_V7 = null;
            logger.fatal("V7 FTP Secret key is null", var2);
         }
      }

      return FTP_SECRET_KEY_V7;
   }

   public static String getVncDefaultPassword() {
      if (VNC_DEFAULT_PASSWORD == null) {
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();

         try {
            VNC_DEFAULT_PASSWORD = serverSetupDao.getSecretValue(2);
         } catch (SQLException var2) {
            VNC_DEFAULT_PASSWORD = null;
            logger.fatal("VNC Default password is null", var2);
         }
      }

      return VNC_DEFAULT_PASSWORD;
   }

   public static boolean getDbEncryptEnable() throws IOException {
      String[] ENCRYPTION_PROPERTY_KEYS = new String[]{"wsrm.username", "wsrm.password", "org.quartz.dataSource.databaseDS.user", "org.quartz.dataSource.databaseDS.password"};
      boolean isEnc = false;
      String[] propValues = new String[ENCRYPTION_PROPERTY_KEYS.length];
      Properties props = new Properties();
      String confFilePath = CommonConfig.getConfigFilePath();
      String configFileName = CommonConfig.getConfigFileName();
      FileInputStream in = null;

      try {
         in = new FileInputStream(directoryTraversalChecker(confFilePath + File.separator + configFileName, (String)null));
      } catch (Exception var9) {
         if (in != null) {
            in.close();
         }
      }

      props.load(new BufferedInputStream(in));
      if (in != null) {
         try {
            in.close();
         } catch (Exception var8) {
         }
      }

      for(int i = 0; i < ENCRYPTION_PROPERTY_KEYS.length; ++i) {
         propValues[i] = StrUtils.nvl(props.getProperty(ENCRYPTION_PROPERTY_KEYS[i])).trim();
         if (!isEnc && propValues[i].indexOf("ENC(") == 0) {
            isEnc = true;
         }
      }

      return isEnc;
   }

   public static String getUserIdFromRequest(HttpServletRequest request) {
      String userId = StrUtils.nvl(request.getParameter("id"));
      if (StringUtils.isEmpty(userId)) {
         userId = StrUtils.nvl(request.getParameter("userId"));
      }

      if (StringUtils.isEmpty(userId)) {
         userId = StrUtils.nvl(request.getHeader("userId"));
      }

      return userId;
   }

   public static String getTokenFromRequest(HttpServletRequest request) {
      String password = StrUtils.nvl(request.getHeader("passwd"));
      if (StringUtils.isEmpty(password)) {
         password = StrUtils.nvl(request.getParameter("passwd"));
      }

      if (StringUtils.isEmpty(password)) {
         password = StrUtils.nvl(request.getHeader("token"));
      }

      return password;
   }

   public static String getDecryptionPassword(String encPassword, int encVersion) {
      EncryptionManager encMgr = null;
      if (1 == encVersion) {
         encMgr = EncryptionManagerImpl.getInstance();
      } else if (0 == encVersion) {
         encMgr = DesEncryptionManagerImpl.getInstance();
      }

      return encMgr.getDecryptionPassword("", encPassword);
   }

   public static String getDecryptionPassword(String encPassword, String encVersion) {
      int version = 0;
      if (StrUtils.isNumeric(encVersion)) {
         version = Integer.parseInt(encVersion);
      }

      return getDecryptionPassword(encPassword, version);
   }

   static {
      passwordEncoder = ExtendedPasswordEncoderImpl.INSTANCE;
      tokenRegistry = TokenRegistry.getTokenRegistry();
      policySrc = null;
      FTP_SECRET_KEY = null;
      FTP_SECRET_KEY_V7 = null;
      VNC_DEFAULT_PASSWORD = null;
      CIPHER_AES = "AES";
      CIPHER_AES_BIT = "http://www.w3.org/2001/04/xmlenc#aes256-cbc";
   }
}
