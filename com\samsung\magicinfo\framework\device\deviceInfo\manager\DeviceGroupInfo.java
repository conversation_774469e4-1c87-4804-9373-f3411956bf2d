package com.samsung.magicinfo.framework.device.deviceInfo.manager;

import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.rms.model.DeviceGroupFilter;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.apache.ibatis.session.SqlSession;

public interface DeviceGroupInfo {
   void updateCacheDeviceGroup();

   void updateCacheDeviceGroup(Long var1, DeviceGroup var2);

   int addGroup(DeviceGroup var1) throws SQLException, ConfigException;

   boolean addGroupForOrg(String var1, String var2) throws SQLException, ConfigException;

   boolean canDeleteOrgGroups(String var1) throws SQLException;

   boolean canDeleteDeviceGroup(int var1) throws SQLException;

   boolean deleteChildGroupAndDevice(Long var1, String var2, HttpServletRequest var3) throws SQLException, ConfigException;

   boolean deleteOrgGroups(String var1, String var2, HttpServletRequest var3) throws SQLException, ConfigException;

   boolean delGroup(int var1) throws SQLException;

   List getChildDeviceIdList(int var1, boolean var2) throws SQLException;

   long getTotalOrganizationDeviceCountByGroupId(long var1) throws SQLException;

   List getChildDeviceIdList(int var1) throws SQLException;

   List getChildDeviceIdListByOrganName(String var1) throws SQLException;

   List getChildDeviceList(int var1, boolean var2) throws SQLException;

   List getChildDeviceList(int var1) throws SQLException;

   List getChildGroupIdList(int var1, boolean var2) throws SQLException;

   List getChildGroupList(int var1, boolean var2) throws SQLException;

   List getChildGroupListWithPermission(int var1, String var2, boolean var3) throws SQLException;

   List getChildGroupListByGroupType(int var1, boolean var2, String var3) throws SQLException;

   List getChildGroupListByGroupTypeForSchedule(int var1, boolean var2, String var3, ArrayList var4, String var5, Long var6) throws SQLException;

   String getDefaultProgramId(long var1) throws SQLException;

   int getDeviceGroupForUser(String var1) throws Exception;

   String getDeviceGroupRoot(int var1) throws SQLException;

   int getDeviceOrgGroupId(int var1) throws SQLException;

   DeviceGroup getGroup(int var1) throws SQLException;

   List getGroupType(int var1) throws SQLException;

   DeviceGroup getGroupByDeviceId(String var1) throws SQLException;

   long getGroupIdByOrgBasic(String var1, String var2) throws SQLException;

   List getGroupList() throws SQLException;

   List getGroupList(DeviceGroupFilter var1) throws SQLException;

   int getGroupListCnt(DeviceGroupFilter var1) throws SQLException;

   Map getGroupNameByDeviceId(String var1) throws SQLException;

   List getGroupsForOrg(String var1) throws SQLException;

   Long getOrgIdByGroupId(long var1) throws SQLException;

   String getMessageGroupRoot(int var1) throws SQLException;

   long getOrganGroupIdByName(String var1) throws SQLException;

   Map getOrgGroupId(String var1) throws SQLException;

   String getOrgNameByGroupId(long var1) throws SQLException;

   int getParentGroupId(int var1) throws SQLException;

   String getProgramGroupRoot(int var1) throws SQLException;

   boolean moveGroup(int var1, int var2, Long var3) throws SQLException;

   boolean setDefaultProgramId(long var1, String var3) throws SQLException;

   boolean setGroup(DeviceGroup var1) throws SQLException, ConfigException;

   boolean setOrgName(String var1, String var2) throws SQLException;

   List getGroupIdByName(String var1) throws SQLException;

   DeviceGroup getDeviceTopGroup(int var1) throws SQLException;

   boolean setDeviceGroupType(int var1, String var2) throws SQLException;

   boolean setDeviceGroupType(int var1, String var2, SqlSession var3) throws SQLException;

   boolean setAllDeviceGroupType(int var1, String var2) throws SQLException;

   int getCntDeviceInDeviceGroup(int var1) throws SQLException;

   int getCntDeviceInLiteDeviceGroup(int var1) throws SQLException;

   int getCntDeviceInProgram(String var1) throws SQLException;

   int getCntDeviceInVwlConsoleDevice(String var1) throws SQLException;

   String getParentGroupType(int var1) throws SQLException;

   List getScheduleMappingDeviceGroupAuth(String var1, String var2, boolean var3);

   List getRedundancyGroups() throws SQLException;

   boolean isRedundancyGroup(int var1) throws SQLException;

   List getRedundantDeviceIdbyGroupId(int var1) throws SQLException;

   boolean setIsRedundancy(long var1, boolean var3) throws SQLException;

   String getVwlLayoutIdByGroupId(String var1) throws SQLException;

   List getVwlLayoutGroupId() throws SQLException;

   List getAllVWLLayoutGroupList() throws SQLException;

   boolean setVwtId(String var1, String var2) throws SQLException;

   boolean isVwlGroup(String var1) throws SQLException;

   String getGroupNameByVwtId(String var1) throws SQLException;

   boolean isVWLLayoutGroup(String var1) throws SQLException;

   Long getPriority(String var1, Float var2) throws SQLException;

   Long getMinimumPriority(String var1) throws SQLException;

   boolean cancelVwlGroup(String var1) throws SQLException;

   boolean updateDeviceGroupPriority(Long var1, long var2) throws SQLException;

   String getDeviceTypeByMinimumPriority(Long var1) throws SQLException;

   Float getDeviceTypeVersionByMinimumPriority(Long var1) throws SQLException;

   String getGroupNameByGroupId(long var1) throws SQLException;

   String getPermissionsDeviceGroup(String var1) throws SQLException;

   String getOrgId(String var1) throws SQLException;

   List getAuthDeviceGroupList(String var1) throws SQLException;

   List getDeviceTypesMapGroup(Long var1) throws SQLException;

   boolean setGroupTypeDefault(Long var1) throws SQLException;

   List getAllGroupName() throws SQLException;

   String getModelCountInfo(long var1) throws SQLException;

   List getGroupById(String var1, String var2, long var3, boolean var5) throws SQLException;

   List getGroupByIdWithPermission(String var1, String var2, long var3, String var5, boolean var6) throws SQLException;

   List getGroupById(String var1, List var2) throws SQLException;

   List getRootGroupById(String var1, String var2, String var3) throws SQLException;

   List getVwlGroupById(String var1, String var2, long var3) throws SQLException;

   List getVwlRootGroupById(String var1, String var2, String var3) throws SQLException;

   List getAdminVwlRootGroupById(String var1, String var2, long var3) throws SQLException;

   boolean getDeviceAuthor(long var1, String var3) throws SQLException;

   boolean isSyncPlayGroup(int var1) throws SQLException;

   int checkChildPermissions2(String var1, Long var2) throws SQLException;

   List getDeviceModelName(long var1) throws SQLException;

   int getCntDeviceInDeviceGroupExceptFor(int var1, List var2) throws SQLException;

   long getDiskSpaceRepository(String var1) throws SQLException;

   List getOrganizationGroup() throws SQLException;

   boolean updateDeviceGroupPriority(Long var1, long var2, SqlSession var4) throws SQLException;

   List getAllDeviceGroupsByGroupName(long var1, String var3) throws SQLException;

   List getAllDeviceGroupsByGroupName(List var1, String var2) throws SQLException;

   List getAllDeviceGroups(long var1) throws SQLException;

   List getAllDeviceGroups(List var1) throws SQLException;

   List getAllAuthorityDeviceGroups(String var1) throws SQLException;

   int getUnapprovedDeviceCountByUser(List var1) throws SQLException;

   List getMessageMappingDeviceGroupAuth(String var1, String var2, boolean var3);

   List getEventMappingDeviceGroupAuth(String var1, String var2, boolean var3);

   List getPermissionsDeviceGroupList(String var1) throws SQLException;

   long getTotalApprovalDeviceCount() throws SQLException;

   List getDeviceCountByOrganization() throws SQLException;

   List getParentGroupNamePathByGroupId(Map var1, Long var2) throws SQLException;

   List getParentGroupNamePathByGroupId(Long var1) throws SQLException;

   String getParentOrgNameByGroupId(Long var1) throws SQLException;

   boolean changeDeviceOrgName(String var1, String var2) throws SQLException;

   int getCntAnalysisDeviceGroup() throws SQLException;

   boolean setAnalysisDeviceGroup(long var1, boolean var3) throws SQLException;

   Map getDeviceOrganizationByGroupId(int var1) throws SQLException;

   String getDeviceTypeByGroupId(Long var1) throws SQLException;

   Map getCacheDeviceGroup();

   void addGroupTotalCount(Long var1, Long var2) throws SQLException;

   void updateOrgNameByGroupId(String var1, long var2, long var4) throws SQLException;

   List getAlarmDeviceGroupList(long var1) throws SQLException;

   List getAlarmDeviceGroupListByName(String var1) throws SQLException;

   boolean addAlarmDeviceGroup(long var1, String var3, String var4) throws SQLException;

   DeviceGroup getDeviceOrgGroupByUserOrgId(Long var1) throws SQLException;

   List getGroupDeviceListByOrganName(String var1) throws SQLException;

   Integer getCountByUserIdAndGroupId(String var1, Long var2) throws SQLException;

   List V2GetChildDeviceIdList(int var1, boolean var2) throws SQLException;

   int getDeviceGroupTotalCount() throws SQLException;

   List getDeviceInDeviceGroup(int var1) throws SQLException;
}
