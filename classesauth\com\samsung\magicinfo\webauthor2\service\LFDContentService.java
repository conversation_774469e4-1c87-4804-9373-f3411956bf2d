package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.LFDContent;
import com.samsung.magicinfo.webauthor2.model.VerificationResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface LFDContentService {
  LFDContent getLFDContent(String paramString);
  
  Page<Content> getLFDContentList(Pageable paramPageable, DeviceType paramDeviceType);
  
  VerificationResponse verifyLFD(LFDContent paramLFDContent);
}
