package com.samsung.magicinfo.rc;

import java.util.Arrays;
import org.apache.catalina.connector.Connector;
import org.apache.coyote.AbstractProtocol;
import org.apache.coyote.ProtocolHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.embedded.ConfigurableEmbeddedServletContainer;
import org.springframework.boot.context.embedded.EmbeddedServletContainerCustomizer;
import org.springframework.boot.context.embedded.tomcat.TomcatConnectorCustomizer;
import org.springframework.boot.context.embedded.tomcat.TomcatEmbeddedServletContainerFactory;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCache;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableScheduling
@EnableCaching
public class WebApplication extends SpringBootServletInitializer {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.WebApplication.class);
  
  public static void main(String[] args) {
    SpringApplication.run(com.samsung.magicinfo.rc.WebApplication.class, args);
  }
  
  @Bean
  public EmbeddedServletContainerCustomizer containerCustomizer() {
    return container -> {
        if (container instanceof TomcatEmbeddedServletContainerFactory) {
          TomcatEmbeddedServletContainerFactory tomcat = (TomcatEmbeddedServletContainerFactory)container;
          tomcat.addConnectorCustomizers(new TomcatConnectorCustomizer[] { () });
        } 
      };
  }
  
  @Bean
  public CacheManager cacheManager() {
    SimpleCacheManager cacheManager = new SimpleCacheManager();
    cacheManager.setCaches(
        Arrays.asList(new ConcurrentMapCache[] { new ConcurrentMapCache("tokens") }));
    return (CacheManager)cacheManager;
  }
}
