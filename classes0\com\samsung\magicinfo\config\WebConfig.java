package com.samsung.magicinfo.config;

import com.samsung.magicinfo.config.AppConfig;
import com.samsung.magicinfo.config.MvcConfig;
import java.nio.charset.StandardCharsets;
import javax.servlet.Filter;
import org.springframework.web.filter.CharacterEncodingFilter;
import org.springframework.web.servlet.support.AbstractAnnotationConfigDispatcherServletInitializer;

public class WebConfig extends AbstractAnnotationConfigDispatcherServletInitializer {
  protected Class<?>[] getRootConfigClasses() {
    return new Class[] { AppConfig.class };
  }
  
  protected Class<?>[] getServletConfigClasses() {
    return new Class[] { MvcConfig.class };
  }
  
  protected String[] getServletMappings() {
    return new String[] { "/" };
  }
  
  protected Filter[] getServletFilters() {
    CharacterEncodingFilter characterEncodingFilter = new CharacterEncodingFilter(StandardCharsets.UTF_8.toString(), true);
    return new Filter[] { (Filter)characterEncodingFilter };
  }
}
