package com.samsung.magicinfo.webauthor.ftp;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPSClient;

public class FtpClientConfiguration {
  private FTPClient client;
  
  private FtpClientConfiguration(ConfigurationBuilder configuration) throws IOException, NoSuchAlgorithmException {
    if (ConfigurationBuilder.access$000(configuration)) {
      String protocol = "TLS";
      this.client = (FTPClient)new FTPSClient(protocol, true);
    } else {
      this.client = new FTPClient();
    } 
    this.client.setDefaultPort(ConfigurationBuilder.access$100(configuration));
    this.client.setControlEncoding(ConfigurationBuilder.access$200(configuration));
    this.client.setDataTimeout(ConfigurationBuilder.access$300(configuration));
    this.client.setRemoteVerificationEnabled(ConfigurationBuilder.access$400(configuration));
    this.client.enterLocalPassiveMode();
    this.client.setBufferSize(ConfigurationBuilder.access$500(configuration));
  }
  
  public FTPClient getClient() {
    return this.client;
  }
}
