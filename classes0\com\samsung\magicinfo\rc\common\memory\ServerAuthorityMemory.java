package com.samsung.magicinfo.rc.common.memory;

import java.util.HashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class ServerAuthorityMemory extends HashMap<String, String> {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.common.memory.ServerAuthorityMemory.class);
  
  private static final long serialVersionUID = 1L;
  
  static int SUCCESS = 0;
  
  static int DUPLICATION = 100;
  
  static int ERROR = 900;
  
  public boolean checkAuthority(String deviceId, String authority) {
    boolean rtn = false;
    String temp_authority = null;
    if (deviceId == null || deviceId.equals("") || authority == null || authority.equals(""))
      return false; 
    if (containsKey(deviceId)) {
      temp_authority = (String)get(deviceId);
      if (temp_authority.equals(deviceId)) {
        rtn = true;
      } else {
        rtn = false;
      } 
    } 
    return rtn;
  }
  
  public boolean deleteAuthority(String diviceId) {
    boolean rtn = false;
    if (containsKey(diviceId)) {
      remove(diviceId);
      rtn = true;
    } 
    return rtn;
  }
  
  public int startAuthority(String deviceId, String authority) {
    int rtn = ERROR;
    try {
      log.info("[RC] input deviceId : " + deviceId + " authority : " + authority);
      put((K)deviceId, (V)authority);
      rtn = SUCCESS;
    } catch (Exception e) {
      log.error("", e);
      log.error("[RC] input! Exception!");
      rtn = ERROR;
    } 
    return rtn;
  }
}
