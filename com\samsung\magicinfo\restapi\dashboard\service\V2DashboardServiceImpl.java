package com.samsung.magicinfo.restapi.dashboard.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.RoleUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.content.dao.ContentDao;
import com.samsung.magicinfo.framework.content.dao.PlaylistDao;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.dashboard.dao.DashboardDao;
import com.samsung.magicinfo.framework.dashboard.entity.UserDashboardEntity;
import com.samsung.magicinfo.framework.dashboard.manager.DashboardManager;
import com.samsung.magicinfo.framework.dashboard.manager.DashboardManagerImpl;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManager;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManagerImpl;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManager;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManagerImpl;
import com.samsung.magicinfo.framework.edge.db.EdgeServerDao;
import com.samsung.magicinfo.framework.monitoring.entity.DashboardEntity;
import com.samsung.magicinfo.framework.monitoring.entity.DeviceErrorWarningStaticEntity;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.notice.dao.NoticeDao;
import com.samsung.magicinfo.framework.notice.entity.NoticeEntity;
import com.samsung.magicinfo.framework.role.manager.AbilityInfo;
import com.samsung.magicinfo.framework.role.manager.AbilityInfoImpl;
import com.samsung.magicinfo.framework.ruleset.entity.SelectConditionRuleset;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfo;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.dao.InsightServerDao;
import com.samsung.magicinfo.framework.setup.dao.SlmLicenseDao;
import com.samsung.magicinfo.framework.setup.entity.DatalinkServerEntity;
import com.samsung.magicinfo.framework.setup.entity.InsightServerEntity;
import com.samsung.magicinfo.framework.setup.entity.RmServerEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity;
import com.samsung.magicinfo.framework.setup.manager.DatalinkServerImpl;
import com.samsung.magicinfo.framework.setup.manager.RmServerImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.openapi.custom.domain.notice.NoticeIF;
import com.samsung.magicinfo.openapi.custom.domain.notice.NoticeImpl;
import com.samsung.magicinfo.restapi.common.model.V2ListQueryFilter;
import com.samsung.magicinfo.restapi.dashboard.model.V2ActiveWidgetResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2AddDashboardResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2BaseDashboard;
import com.samsung.magicinfo.restapi.dashboard.model.V2ConnectedDevicesResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2DashboardDeviceStatusResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2DashboardListResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2DashboardNoticeDataResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2DashboardStorageInfoResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2DeleteDashboardResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2DeviceCategoryErrorWarningResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2DeviceHealthByDateResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2DeviceHealthResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2DeviceSummaryResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2EnvironmentNoticeResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2EnvironmentPriorityResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2GetDeviceConnectResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2GetDeviceDayConnectResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2ListDashboardLoginInfoResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2ListDashboardNoticeInfoResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2NoticeResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2OrderedDashboardResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2ServerListResource;
import com.samsung.magicinfo.restapi.dashboard.model.V2ServerListinfo;
import com.samsung.magicinfo.restapi.dashboard.model.V2SoftwareDeployInfo;
import com.samsung.magicinfo.restapi.dashboard.model.V2UnapprovedResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceStatusResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTypeResource;
import com.samsung.magicinfo.restapi.device.service.V2DeviceService;
import com.samsung.magicinfo.restapi.edge.model.EdgeServer;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManager;
import com.samsung.magicinfo.restapi.user.model.V2DashboardUserInfoResource;
import com.samsung.magicinfo.restapi.user.service.V2UserService;
import java.io.File;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;
import org.apache.ftpserver.db.DownloadInfo;
import org.apache.ftpserver.db.DownloadInfoImpl;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2DashboardService")
@Transactional
public class V2DashboardServiceImpl implements V2DashboardService {
   protected Logger logger = LoggingManagerV2.getLogger(V2DashboardServiceImpl.class);
   @Autowired
   private PIIDataManager piiDataManager;
   @Autowired
   private V2DeviceService v2DeviceService;
   public static final String DASHBOARD_NAME = "dashboardName";
   public static final String PRIORITY = "priority";
   public static final String SIGN_DASHBOARD = "signInfoDashboard";
   public static final String NOTICE_DASHBOARD = "noticeInfoDashboard";
   public static final String STORAGE_DASHBOARD = "storageInfoDashboard";
   public static final String CONTENT_DASHBOARD = "contentInfoDashboard";
   public static final String PLAYLIST_DASHBOARD = "playlistInfoDashboard";
   public static final String SCHEDULE_DASHBOARD = "scheduleInfoDashboard";
   public static final String DEVICE_DASHBOARD = "deviceInfoDashboard";
   public static final String USER_DASHBOARD = "userInfoDashboard";
   public static final String DN_SERVER_DASHBOARD = "downloadServerDashboard";
   public static final String RM_SERVER_DASHBOARD = "rmServerDashboard";
   public static final String DL_SERVER_DASHBOARD = "datalinkServerDashboard";
   public static final String DEVICE_WARMING_DASHBOARD = "deviceErrorWarning";
   public static final String DEVICE_CONNET_DASHBOARD = "deviceConnect";
   public static final String DEVICE_Day_DASHBOARD = "deviceDayConnect";
   public static final String SOFTWARE_DEPLOY_STATUS_DASHBOARD = "softwareDeployStatus";
   public static final String ROOT_PATH = "rootPath";
   public static final String FREE_SIZE = "freeSize";
   public static final String USED_SIZE = "usedSize";
   public static final String TOTAL_SIZE = "totalSize";
   public static final String PERCENT = "percent";
   public static final String STORAGE_LIST = "storageList";
   public static final String GIGA_BYTE = "GB";
   public static final String NOTICE_START_ID = "1";
   public static final String TOTAL_RECORD = "totalRecord";
   public static final String NOTICE_TITLE = "noticeTitle";
   public static final String NOTICE_ID = "noticeId";
   public static final String NOTICE_USER_ID = "noticeUserId";
   public static final String NOTICE_WRITE_DATE = "noticeWriteDate";
   public static final String NOTICE_IMPORTANT = "noticeImportant";
   public static final String NOTICE_LIST = "noticeList";
   public static final String NOTICE = "notice";
   public static final String START_DATE = "startDate";
   public static final String END_DATE = "endDate";
   public static final String DATE_FORMAT_YY_MM_DD = "yyyy-MM-dd";
   public static final String USER_ID = "userId";
   public static final String USER_NAME = "userName";
   public static final String ROLE = "role";
   public static final String LAST_SIGN_IN = "lastSignIn";
   public static final String DESC = "desc";
   public static final String REG_DATE = "reg_date";
   public static final String SORT = "sort";
   public static final String ORDER = "order";
   public static final String START_INDEX = "startIndex";
   public static final String RESULTS = "results";
   public static final String LICENSE_KEY = "licenseKey";
   public static final String PRODUCT_CODE = "productCode";
   public static final String LICENSE_TYPE = "licenseType";
   public static final String CHARGED = "charged";
   public static final String FREE_OF_CHARGED = "freeOfCharged";
   public static final String FREE = "free";
   public static final String MAX_CLIENTS = "maxClients";
   public static final String LICENSE_LIST = "licenseList";
   public static final String DATE_FORMAT_HH_MM_SS = "HH-mm-ss";
   public static final String MODE = "mode";
   public static final String WRITE = "write";
   public static final String VIEW = "view";
   private static final String CONTENT = "CONTENT";
   private static final String PLAYLIST = "PLAYLIST";
   private static final String RULESET = "RULESET";
   private static final String SCHEDULE = "SCHEDULE";
   private final Integer storageInfoDashboard = 3;
   private final Integer userInfoDashboard = 8;
   private final Integer softwareDeployStatus = 15;
   private final Integer externalServersInfoDashboard = 16;
   @Autowired
   private V2UserService v2UserService;

   public V2DashboardServiceImpl() {
      super();
   }

   public ResponseBody listDashboardInfo() {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      DashboardManager dashboardInfo = DashboardManagerImpl.getInstance();
      LinkedHashMap data = new LinkedHashMap();

      try {
         List list = dashboardInfo.getDashboard(userId);
         Iterator var7 = list.iterator();

         while(var7.hasNext()) {
            UserDashboardEntity entity = (UserDashboardEntity)var7.next();
            data.put(entity.getDashboard_name(), true);
         }

         responseBody.setItems(data);
         responseBody.setStatus("Success");
      } catch (Exception var9) {
         this.logger.error("", var9);
         responseBody.setErrorMessage(var9.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public V2AddDashboardResource addDashboard(String dashboardName) throws SQLException, Exception {
      V2AddDashboardResource resource = new V2AddDashboardResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      DashboardManager dashboardInfo = DashboardManagerImpl.getInstance();
      int id = dashboardInfo.getDashboardWidgetId(dashboardName);
      int priority = dashboardInfo.addDashboardWidget(userId, id);
      if (priority > 0) {
         resource.setDashboardName(dashboardName);
         resource.setPriority(priority);
         return resource;
      } else {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_DASHBOARD_WRONG_PRIORITY);
      }
   }

   public V2DeleteDashboardResource deleteDashboard(String dashboardName) throws SQLException {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      DashboardManager dashboardInfo = DashboardManagerImpl.getInstance();
      dashboardInfo.removeDashboardWidget(userId, dashboardName);
      V2DeleteDashboardResource resource = new V2DeleteDashboardResource();
      resource.setUserId(userId);
      resource.setDashboardName(dashboardName);
      return resource;
   }

   public List updateDashboardPriority(V2EnvironmentPriorityResource resource) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      DashboardManager dashboardInfo = DashboardManagerImpl.getInstance();
      List dashboards = new ArrayList();
      List dashboardLists = new ArrayList();
      String combineData = "";
      if (resource.getSignInfoDashboard() > 0) {
         combineData = combineData + "signInfoDashboard:" + resource.getSignInfoDashboard() + ":1,";
      }

      if (resource.getNoticeInfoDashboard() > 0) {
         combineData = combineData + "noticeInfoDashboard:" + resource.getNoticeInfoDashboard() + ":2,";
      }

      if (resource.getStorageInfoDashboard() > 0) {
         combineData = combineData + "storageInfoDashboard:" + resource.getStorageInfoDashboard() + ":3,";
      }

      if (resource.getContentInfoDashboard() > 0) {
         combineData = combineData + "contentInfoDashboard:" + resource.getContentInfoDashboard() + ":4,";
      }

      if (resource.getPlaylistInfoDashboard() > 0) {
         combineData = combineData + "playlistInfoDashboard:" + resource.getPlaylistInfoDashboard() + ":5,";
      }

      if (resource.getScheduleInfoDashboard() > 0) {
         combineData = combineData + "scheduleInfoDashboard:" + resource.getScheduleInfoDashboard() + ":6,";
      }

      if (resource.getDeviceInfoDashboard() > 0) {
         combineData = combineData + "deviceInfoDashboard:" + resource.getDeviceInfoDashboard() + ":7,";
      }

      if (resource.getUserInfoDashboard() > 0) {
         combineData = combineData + "userInfoDashboard:" + resource.getUserInfoDashboard() + ":8,";
      }

      if (resource.getDownloadServerDashboard() > 0) {
         combineData = combineData + "downloadServerDashboard:" + resource.getDownloadServerDashboard() + ":9,";
      }

      if (resource.getRmServerDashboard() > 0) {
         combineData = combineData + "rmServerDashboard:" + resource.getRmServerDashboard() + ":10,";
      }

      if (resource.getDatalinkServerDashboard() > 0) {
         combineData = combineData + "datalinkServerDashboard:" + resource.getDatalinkServerDashboard() + ":11,";
      }

      if (resource.getDeviceConnect() > 0) {
         combineData = combineData + "deviceConnect:" + resource.getDeviceConnect() + ":12,";
      }

      if (resource.getDeviceDayConnect() > 0) {
         combineData = combineData + "deviceDayConnect:" + resource.getDeviceDayConnect() + ":13,";
      }

      if (resource.getDeviceErrorWarning() > 0) {
         combineData = combineData + "deviceErrorWarning:" + resource.getDeviceErrorWarning() + ":14,";
      }

      if (resource.getSoftwareDeployStatus() > 0) {
         combineData = combineData + "softwareDeployStatus:" + resource.getSoftwareDeployStatus() + ":15,";
      }

      String[] dashboardList = combineData.split(",");
      String[] var9 = dashboardList;
      int var10 = dashboardList.length;

      for(int var11 = 0; var11 < var10; ++var11) {
         String dashboardTemp = var9[var11];
         String[] dashboard = dashboardTemp.split(":", 3);
         if (dashboard.length == 3) {
            UserDashboardEntity entity = new UserDashboardEntity();
            entity.setDashboard_name(dashboard[0]);
            entity.setPriority(Integer.valueOf(dashboard[1]));
            entity.setUser_id(userId);
            dashboardLists.add(entity);
            V2OrderedDashboardResource orderdashboard = new V2OrderedDashboardResource();
            orderdashboard.setDashboardName(dashboard[0]);
            orderdashboard.setPriority(Integer.valueOf(dashboard[1]));
            orderdashboard.setDashboardId(Integer.valueOf(dashboard[2]));
            orderdashboard.setUserId(userId);
            dashboards.add(orderdashboard);
         }
      }

      dashboardInfo.updateDashboardPriority(dashboardLists);
      return dashboards;
   }

   public List listDashboardStorageInfo() {
      List storageList = new ArrayList();
      File[] roots = File.listRoots();

      for(int i = 0; roots != null && i < roots.length; ++i) {
         if (roots[i].getTotalSpace() > 0L) {
            V2DashboardStorageInfoResource storage = new V2DashboardStorageInfoResource();
            long totalSizeLong = roots[i].getTotalSpace();
            long freeSizeLong = roots[i].getFreeSpace();
            long usedSizeLong = totalSizeLong - freeSizeLong;
            String freeSize = formatSizeGB(freeSizeLong);
            String usedSize = formatSizeGB(usedSizeLong);
            String totalSize = formatSizeGB(totalSizeLong);
            String percent = String.valueOf((int)((double)usedSizeLong / (double)totalSizeLong * 100.0D));
            storage.setRootPath(roots[i].getPath());
            storage.setFreeSize(freeSize);
            storage.setUsedSize(usedSize);
            storage.setTotalSize(totalSize);
            storage.setPercent(percent);
            storageList.add(storage);
         }
      }

      return storageList;
   }

   public static String formatSizeGB(Object obj) {
      long bytes = -1L;
      if (obj instanceof Long) {
         bytes = (Long)obj;
      } else if (obj instanceof Integer) {
         bytes = (long)(Integer)obj;
      }

      long gbytes = bytes / 1073741824L;
      return gbytes + "GB";
   }

   public V2ListDashboardNoticeInfoResource listDashboardNoticeInfo(V2ListQueryFilter filter) throws Exception {
      V2ListDashboardNoticeInfoResource resource = new V2ListDashboardNoticeInfoResource();
      List dataList = new ArrayList();
      NoticeIF noticeImp = NoticeImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      NoticeDao noticeDao = new NoticeDao();
      List noticeResult = null;
      noticeResult = noticeImp.getPagedNoticeList(userId, filter.getStartIndex(), filter.getPageSize());
      Long rootGroupId = userInfo.getRootGroupIdByUserId(userId);
      User user = userInfo.getUserByUserId(userId);
      int total_record;
      if (rootGroupId.intValue() != 0 && !user.isMu()) {
         total_record = noticeDao.getNoticeListTotalRecordScopeGroup(userId);
      } else {
         total_record = noticeDao.getNoticeListTotalRecordScopeAll(userId);
      }

      resource.setTotalRecord(total_record);
      Iterator var13 = noticeResult.iterator();

      while(var13.hasNext()) {
         NoticeEntity notice = (NoticeEntity)var13.next();
         V2DashboardNoticeDataResource noticeData = new V2DashboardNoticeDataResource();
         noticeData.setNoticeTitle(notice.getNotice_title());
         noticeData.setNoticeId(notice.getNotice_id());
         noticeData.setNoticeUserId(notice.getUser_id());
         noticeData.setNoticeWriteDate(StrUtils.getDiffMin(notice.getWrite_date()));
         noticeData.setNoticeImportant(notice.getPriority());
         noticeData.setNoticeContent(notice.getNotice_subject());
         dataList.add(noticeData);
      }

      resource.setNoticeList(dataList);
      return resource;
   }

   public V2NoticeResource listEditNoticeInfo(String noticeId) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      V2NoticeResource resource = new V2NoticeResource();
      NoticeDao noticeDao = new NoticeDao();
      NoticeEntity notice = noticeDao.getNoticeData(noticeId);
      if (notice.getUser_id().equals(userId)) {
         resource.setMode("write");
      } else {
         resource.setMode("view");
      }

      resource.setId(notice.getNotice_id());
      resource.setTitle(notice.getNotice_title());
      resource.setSubject(notice.getNotice_subject());
      resource.setPriority(notice.getPriority());
      resource.setStartDate(StrUtils.getDiffMin(notice.getStart_date()));
      resource.setEndDate(StrUtils.getDiffMin(notice.getEnd_date()));
      return resource;
   }

   public V2NoticeResource createNoticeInfo(V2EnvironmentNoticeResource resource, boolean create) throws Exception {
      new V2NoticeResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      NoticeDao noticeDao = new NoticeDao();
      NoticeEntity notice = new NoticeEntity();
      if (!create) {
         notice.setNotice_id(Integer.valueOf(resource.getNoticeId()));
      }

      DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
      Date enddate = dateFormat.parse(resource.getNoticeEndDate() + " 23-59-59");
      Date startdate = dateFormat.parse(resource.getNoticeStartDate() + " 00-00-00");
      long endTime = enddate.getTime();
      long startTime = startdate.getTime();
      notice.setNotice_title(resource.getNoticeTitle());
      notice.setNotice_subject(resource.getNoticeSubject());
      notice.setStart_date(new Timestamp(startTime));
      notice.setEnd_date(new Timestamp(endTime));
      if (resource.isImportant()) {
         notice.setPriority(0);
      } else {
         notice.setPriority(1);
      }

      notice.setShow_range(0);
      notice.setHas_limit(false);
      notice.setUser_id(userId);
      if (create) {
         int noticeId = noticeDao.insertNotice(notice);
         notice.setNotice_id(noticeId);
      } else {
         noticeDao.updateNotice(notice);
      }

      V2NoticeResource result = this.listEditNoticeInfo(String.valueOf(notice.getNotice_id()));
      return result;
   }

   public V2NoticeResource deleteNoticeInfo(String noticeId) throws Exception {
      V2NoticeResource resource = new V2NoticeResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      User user = userContainer.getUser();
      String userId = user.getUser_id();
      String[] arr_notice_id = new String[]{noticeId};
      NoticeDao noticeDao = new NoticeDao();
      if (!RoleUtils.isServerAdminRole(user) && !RoleUtils.isAdminRole(user)) {
         NoticeEntity noticeData = noticeDao.getNoticeData(noticeId);
         if (!noticeData.getUser_id().equals(userId)) {
            throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
         }

         if (!noticeDao.deleteNotice(userId, noticeId)) {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_DELETE_FAIL, new String[]{"notice"});
         }

         resource.setId(Integer.parseInt(noticeId));
      } else {
         noticeDao.deleteNoticeList(arr_notice_id);
      }

      resource.setId(Integer.parseInt(noticeId));
      return resource;
   }

   public V2ListDashboardLoginInfoResource listDashboardLoginInfo() {
      V2ListDashboardLoginInfoResource resource = new V2ListDashboardLoginInfoResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      Locale locales = null;
      User user = userContainer.getUser();
      resource.setUserId(user.getUser_id());
      resource.setUserName(this.piiDataManager.decryptData(user.getUser_name()));
      resource.setRole(user.getRole_name());
      resource.setLastSignIn(StrUtils.getDiffMin(user.getLast_login_date(), false, (Locale)locales));
      resource.setLastSignInDate(user.getLast_login_date());
      return resource;
   }

   public ResponseBody listLicense() {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      String order_dir = "desc";
      String sort_name = "reg_date";
      String createTime = "";
      int startIndex = 1;
      int results = 20;
      new ArrayList();
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
      SlmLicenseDao licenseDao = new SlmLicenseDao();
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      LinkedHashMap data = new LinkedHashMap();
      List dataList = new ArrayList();
      PagedListInfo listInfo = null;
      Map condition = new HashMap();
      condition.put("sort", sort_name);
      condition.put("order", order_dir);
      List integrateList = new ArrayList();
      List premiumIList = new ArrayList();
      List liteList = new ArrayList();
      List mobileList = new ArrayList();
      List premiumSList = new ArrayList();
      List tizenList = new ArrayList();
      List signageList = new ArrayList();
      List androidList = new ArrayList();
      List rmsList = new ArrayList();
      int integrateCnt = 0;
      int premiumICnt = 0;
      int liteCnt = 0;
      int mobileCnt = 0;
      int premiumSCnt = 0;
      int tizenCnt = 0;
      int signageCnt = 0;
      int androidCnt = 0;
      int rmsCnt = 0;

      try {
         listInfo = licenseDao.getLicenseList(startIndex, results, condition);
         List list = listInfo.getPagedResultList();
         data.put("totalRecord", listInfo.getTotalRowCount());
         data.put("startIndex", Integer.valueOf(startIndex));
         data.put("sort", sort_name);
         data.put("order", order_dir);
         data.put("results", listInfo.getTotalRowCount());

         for(int i = 0; i < list.size(); ++i) {
            LinkedHashMap licenseDetailInfo = new LinkedHashMap();
            SlmLicenseEntity info = (SlmLicenseEntity)list.get(i);
            licenseDetailInfo.put("licenseKey", info.getLicense_key());
            licenseDetailInfo.put("productCode", LicenseMag.getProductCode(info.getProduct_code()));
            if (info.getLicense_type().equals("11")) {
               licenseDetailInfo.put("licenseType", "charged");
            } else if (info.getLicense_type().equals("12")) {
               licenseDetailInfo.put("licenseType", "freeOfCharged");
            } else if (info.getLicense_type().equals("13")) {
               licenseDetailInfo.put("licenseType", "free");
            }

            licenseDetailInfo.put("maxClients", info.getMax_clients());
            if (info.getStart_date() != null) {
               createTime = this.cutDate(sdf.format(info.getStart_date()));
            }

            licenseDetailInfo.put("startDate", createTime);
            if (info.getEnd_date() != null) {
               if (Math.abs(info.getStart_date().getYear() - info.getEnd_date().getYear()) < 100) {
                  createTime = this.cutDate(sdf.format(info.getEnd_date()));
               } else {
                  createTime = "-";
               }
            }

            licenseDetailInfo.put("endDate", createTime);
            if (info.getReg_date() != null) {
               createTime = this.cutDate(sdf.format(info.getReg_date()));
            }

            licenseDetailInfo.put("reg_date", createTime);
            String tempProductCode = info.getProduct_code();
            Long tempCnt = info.getMax_clients();
            if (tempProductCode.equals("01014A")) {
               integrateList.add(licenseDetailInfo);
               integrateCnt = (int)((long)integrateCnt + tempCnt);
            }

            if (tempProductCode.equals("010120")) {
               premiumIList.add(licenseDetailInfo);
               premiumICnt = (int)((long)premiumICnt + tempCnt);
            } else if (tempProductCode.equals("010311")) {
               liteList.add(licenseDetailInfo);
               liteCnt = (int)((long)liteCnt + tempCnt);
            } else if (tempProductCode.equals("010121")) {
               premiumSList.add(licenseDetailInfo);
               premiumSCnt = (int)((long)premiumSCnt + tempCnt);
            } else if (tempProductCode.equals("010121")) {
               tizenList.add(licenseDetailInfo);
               tizenCnt = (int)((long)tizenCnt + tempCnt);
            } else if (tempProductCode.equals("010V31")) {
               signageList.add(licenseDetailInfo);
               signageCnt = (int)((long)signageCnt + tempCnt);
            } else if (tempProductCode.equals("01011N")) {
               androidList.add(licenseDetailInfo);
               androidCnt = (int)((long)androidCnt + tempCnt);
            } else if (tempProductCode.equals("01064A")) {
               rmsList.add(licenseDetailInfo);
               rmsCnt = (int)((long)rmsCnt + tempCnt);
            }
         }

         LinkedHashMap tempData;
         if (integrateCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "I, S, Signage, LED Player");
            tempData.put("maxClients", integrateCnt);
            tempData.put("licenseList", integrateList);
            dataList.add(dataList.size(), tempData);
         }

         if (premiumICnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "I Player");
            tempData.put("maxClients", premiumICnt);
            tempData.put("licenseList", premiumIList);
            dataList.add(dataList.size(), tempData);
         }

         if (liteCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "Lite Player");
            tempData.put("maxClients", liteCnt);
            tempData.put("licenseList", liteList);
            dataList.add(dataList.size(), tempData);
         }

         if (mobileCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "Mobile Player");
            tempData.put("maxClients", Integer.valueOf(mobileCnt));
            tempData.put("licenseList", mobileList);
            dataList.add(dataList.size(), tempData);
         }

         if (premiumSCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "S Player");
            tempData.put("maxClients", premiumSCnt);
            tempData.put("licenseList", premiumSList);
            dataList.add(dataList.size(), tempData);
         }

         if (tizenCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "Tizen");
            tempData.put("maxClients", tizenCnt);
            tempData.put("licenseList", tizenList);
            dataList.add(dataList.size(), tempData);
         }

         if (signageCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "Signage Player");
            tempData.put("maxClients", signageCnt);
            tempData.put("licenseList", signageList);
            dataList.add(dataList.size(), tempData);
         }

         if (androidCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "Android Player");
            tempData.put("maxClients", androidCnt);
            tempData.put("licenseList", androidList);
            dataList.add(dataList.size(), tempData);
         }

         if (rmsCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "RM Player");
            tempData.put("maxClients", rmsCnt);
            tempData.put("licenseList", rmsList);
            dataList.add(dataList.size(), tempData);
         }

         data.put("licenseList", dataList);
         responseBody.setItems(data);
         responseBody.setStatus("Success");
      } catch (Exception var38) {
         this.logger.error("", var38);
         responseBody.setErrorMessage(var38.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public ResponseBody listMenu() throws Exception {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      ArrayList menuList = new ArrayList();

      try {
         if (CommonUtils.checkAvailable("content")) {
            menuList.add("content");
         }

         if (CommonUtils.checkAvailable("playlist")) {
            menuList.add("playlist");
         }

         if (CommonUtils.checkAvailable("schedule")) {
            menuList.add("schedule");
         }

         if (CommonUtils.checkAvailable("device")) {
            menuList.add("device");
         }

         responseBody.setItems(menuList);
         responseBody.setStatus("Success");
      } catch (Exception var4) {
         responseBody.setErrorMessage(var4.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public String cutDate(String date) {
      String[] rtn = null;
      rtn = date.split(" ");
      return rtn[0];
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceHealthByDateResource getDeviceErrorWarning(String deviceType, Float deviceTypeVersion, boolean getResolvedCnt) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String organization = userContainer.getUser().getOrganization();
      V2DeviceHealthByDateResource resource = new V2DeviceHealthByDateResource();
      AlarmManager alarmDao = AlarmManagerImpl.getInstance();
      Map condition = new HashMap();
      if (!"ROOT".equals(organization)) {
         condition.put("organization", organization);
      }

      condition.put("isApproved", "true");
      condition.put("inProgress", "true");
      if (null != deviceType) {
         condition.put("deviceType", deviceType);
      }

      if (null != deviceTypeVersion) {
         condition.put("deviceTypeVersion", deviceTypeVersion);
      }

      condition.put("type", "DEVICEERROR");
      int error = alarmDao.getCntErrorWarning(condition);
      condition.put("type", "ALLWARNING");
      int warning = alarmDao.getCntErrorWarning(condition);
      if (getResolvedCnt) {
         condition.put("type", "DEVICERESOLVED");
         condition.put("inProgress", "false");
         int resolved = alarmDao.getCntErrorWarning(condition);
         resource.setResolved(resolved);
      }

      resource.setError(error);
      resource.setWarning(warning);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2GetDeviceConnectResource getDeviceConnectionStatus() throws Exception {
      return this.getDeviceConnectingInfo((String)null, (Float)null);
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2GetDeviceDayConnectResource getDeviceDayConnect() throws Exception {
      V2GetDeviceDayConnectResource resource = new V2GetDeviceDayConnectResource();
      MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      List listToday = null;
      List listYesterday = null;
      Calendar c = Calendar.getInstance();
      listToday = motMgr.getStatisticsDeviceConnect(new Timestamp(c.getTimeInMillis()), userContainer.getUser().getRoot_group_id());
      if (listToday != null) {
         resource.setToday(listToday);
      }

      c.add(5, -1);
      listYesterday = motMgr.getStatisticsDeviceConnect(new Timestamp(c.getTimeInMillis()), userContainer.getUser().getRoot_group_id());
      if (listYesterday != null) {
         resource.setYesterday(listYesterday);
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2GetDeviceConnectResource getDeviceConnectingInfo(String deviceType, Float deviceTypeVersion) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      V2GetDeviceConnectResource resource = new V2GetDeviceConnectResource();
      String organization = userContainer.getUser().getOrganization();
      MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
      DashboardEntity entity = motMgr.getDashboardStatus(organization, deviceType, deviceTypeVersion);
      long connectionCount = entity.getConnectionCount();
      long disconnectionCount = entity.getDisconnectionCount();
      Integer nonApprovalCount = entity.getNonApprovalCount();
      resource.setConnectionCount(connectionCount);
      resource.setDisconnectionCount(disconnectionCount);
      if (SecurityUtils.checkDeviceApprovalPermission()) {
         resource.setNonApprovalCount(nonApprovalCount);
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceSummaryResource getDeviceSummaryInfo(Boolean byDeviceTypeVersion) throws Exception {
      V2DeviceSummaryResource deviceSummaryResource = new V2DeviceSummaryResource();
      deviceSummaryResource.setStatus(this.getDeviceStatusResource());
      deviceSummaryResource.setConnectedDevices(this.getConnectedDevicesResources(byDeviceTypeVersion != null && byDeviceTypeVersion));
      deviceSummaryResource.setDeviceHealth(this.getDeviceHealthResource(byDeviceTypeVersion != null && byDeviceTypeVersion));
      deviceSummaryResource.setDeviceHealthByHW(this.getHealthCategoryStaticInfo());
      deviceSummaryResource.setDeviceHealthByDate(this.getErrorWarningDailyStatics());
      return deviceSummaryResource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DashboardDeviceStatusResource getDeviceConnectionAndHealthStatus() throws Exception {
      return this.getDeviceStatusResource();
   }

   private V2DashboardDeviceStatusResource getDeviceStatusResource() throws Exception {
      V2DashboardDeviceStatusResource dashboardDeviceStatusResource = new V2DashboardDeviceStatusResource();
      V2GetDeviceConnectResource deviceConnectingInfo = this.getDeviceConnectingInfo((String)null, (Float)null);
      dashboardDeviceStatusResource.setConnected(deviceConnectingInfo.getConnectionCount().intValue());
      dashboardDeviceStatusResource.setDisConnected(deviceConnectingInfo.getDisconnectionCount().intValue());
      V2DeviceHealthByDateResource deviceErrorWarning = this.getDeviceErrorWarning((String)null, (Float)null, false);
      dashboardDeviceStatusResource.setWarning(deviceErrorWarning.getWarning());
      dashboardDeviceStatusResource.setError(deviceErrorWarning.getError());
      return dashboardDeviceStatusResource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public List getConnectedDevicesByDeviceType(boolean byDeviceTypeVersion) throws Exception {
      return this.getConnectedDevicesResources(byDeviceTypeVersion);
   }

   private List getConnectedDevicesResources(boolean byDeviceTypeVersion) throws Exception {
      List connectedDevicesResources = new ArrayList();
      V2DeviceStatusResource deviceStatusResource = this.v2DeviceService.getDeviceListByUserId();
      List deviceList = deviceStatusResource.getDeviceList();
      if (!byDeviceTypeVersion) {
         List deviceTypes = new ArrayList();
         Iterator var6 = deviceList.iterator();

         while(var6.hasNext()) {
            V2DeviceTypeResource deviceTypeResource = (V2DeviceTypeResource)var6.next();
            String deviceType = deviceTypeResource.getDeviceType();
            if (!deviceTypes.contains(deviceType)) {
               deviceTypes.add(deviceType);
            }
         }

         var6 = deviceTypes.iterator();

         while(var6.hasNext()) {
            String deviceType = (String)var6.next();
            V2GetDeviceConnectResource deviceConnectingInfo = this.getDeviceConnectingInfo(deviceType, (Float)null);
            V2ConnectedDevicesResource connectedDevicesResource = new V2ConnectedDevicesResource();
            connectedDevicesResource.setDeviceType(deviceType);
            connectedDevicesResource.setConnected(deviceConnectingInfo.getConnectionCount().intValue());
            connectedDevicesResource.setDisconnected(deviceConnectingInfo.getDisconnectionCount().intValue());
            connectedDevicesResources.add(connectedDevicesResource);
         }
      } else {
         Iterator var10 = deviceList.iterator();

         while(var10.hasNext()) {
            V2DeviceTypeResource deviceTypeResource = (V2DeviceTypeResource)var10.next();
            V2GetDeviceConnectResource deviceConnectingInfo = this.getDeviceConnectingInfo(deviceTypeResource.getDeviceType(), deviceTypeResource.getDeviceTypeVersion());
            V2ConnectedDevicesResource connectedDevicesResource = new V2ConnectedDevicesResource();
            connectedDevicesResource.setDeviceType(deviceTypeResource.getDeviceType());
            connectedDevicesResource.setDeviceTypeVersion(deviceTypeResource.getDeviceTypeVersion());
            connectedDevicesResource.setConnected(deviceConnectingInfo.getConnectionCount().intValue());
            connectedDevicesResource.setDisconnected(deviceConnectingInfo.getDisconnectionCount().intValue());
            connectedDevicesResources.add(connectedDevicesResource);
         }
      }

      return connectedDevicesResources;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public List getDeviceHealthResourceByDeviceType(boolean byDeviceTypeVersion) throws Exception {
      return this.getDeviceHealthResource(byDeviceTypeVersion);
   }

   private List getDeviceHealthResource(boolean byDeviceTypeVersion) throws Exception {
      List deviceHealthResources = new ArrayList();
      V2DeviceStatusResource deviceStatusResource = this.v2DeviceService.getDeviceListByUserId();
      List deviceList = deviceStatusResource.getDeviceList();
      if (!byDeviceTypeVersion) {
         List deviceTypes = new ArrayList();
         Iterator var6 = deviceList.iterator();

         while(var6.hasNext()) {
            V2DeviceTypeResource deviceTypeResource = (V2DeviceTypeResource)var6.next();
            String deviceType = deviceTypeResource.getDeviceType();
            if (!deviceTypes.contains(deviceType)) {
               deviceTypes.add(deviceType);
            }
         }

         var6 = deviceTypes.iterator();

         while(var6.hasNext()) {
            String deviceType = (String)var6.next();
            V2DeviceHealthByDateResource deviceErrorWarning = this.getDeviceErrorWarning(deviceType, (Float)null, false);
            V2DeviceHealthResource connectedDevicesResource = new V2DeviceHealthResource();
            connectedDevicesResource.setDeviceType(deviceType);
            connectedDevicesResource.setError(deviceErrorWarning.getError());
            connectedDevicesResource.setWarning(deviceErrorWarning.getWarning());
            deviceHealthResources.add(connectedDevicesResource);
         }
      } else {
         Iterator var10 = deviceList.iterator();

         while(var10.hasNext()) {
            V2DeviceTypeResource deviceTypeResource = (V2DeviceTypeResource)var10.next();
            V2DeviceHealthByDateResource deviceErrorWarning = this.getDeviceErrorWarning(deviceTypeResource.getDeviceType(), deviceTypeResource.getDeviceTypeVersion(), false);
            V2DeviceHealthResource connectedDevicesResource = new V2DeviceHealthResource();
            connectedDevicesResource.setDeviceType(deviceTypeResource.getDeviceType());
            connectedDevicesResource.setDeviceTypeVersion(deviceTypeResource.getDeviceTypeVersion());
            connectedDevicesResource.setError(deviceErrorWarning.getError());
            connectedDevicesResource.setWarning(deviceErrorWarning.getWarning());
            deviceHealthResources.add(connectedDevicesResource);
         }
      }

      return deviceHealthResources;
   }

   public List getAvailableDashboardList() throws Exception {
      DashboardDao dashboardDao = new DashboardDao();
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      String orgName = SecurityUtils.getUserContainer().getUser().getOrganization();
      long orgId = userGroupInfo.getOrgGroupIdByName(orgName);
      List dashboardAll = dashboardDao.getDashboardAll();
      List removedList = new ArrayList();
      Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
      AbilityUtils ability = new AbilityUtils();
      boolean content = ability.checkAuthority("Content Read") || ability.checkAuthority("Content Write") || ability.checkAuthority("Content Manage");
      boolean schedule = ability.checkAuthority("Content Schedule Write") || ability.checkAuthority("Content Schedule Write") || ability.checkAuthority("Content Schedule Manage");
      boolean deviceAbility = ability.checkAuthority("Device Write") || ability.checkAuthority("Device Read");
      boolean userAbility = ability.checkAuthority("User Write") || ability.checkAuthority("User Read");
      boolean isServerAdmin = false;
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (RoleUtils.isServerAdminRole(userContainer.getUser().getRole_name())) {
         isServerAdmin = true;
      }

      boolean rmsMode = Boolean.valueOf(CommonConfig.get("RMS_MODE"));
      SlmLicenseManager slm = SlmLicenseManagerImpl.getInstance();
      boolean isNoLicense = slm.getAllSlmLicense().size() == 0;
      Iterator var20 = dashboardAll.iterator();

      while(true) {
         while(var20.hasNext()) {
            UserDashboardEntity dashboard = (UserDashboardEntity)var20.next();
            String dashboardName = dashboard.getDashboard_name();
            if ("contentInfoDashboard".equals(dashboardName) && (!isServerAdmin && !content || rmsMode || isNoLicense)) {
               removedList.add(dashboard);
            } else if ("playlistInfoDashboard".equals(dashboardName) && (!isServerAdmin && !content || rmsMode || isNoLicense)) {
               removedList.add(dashboard);
            } else if ("deviceInfoDashboard".equals(dashboardName) && (!isServerAdmin && !deviceAbility || isNoLicense)) {
               removedList.add(dashboard);
            } else if (!"scheduleInfoDashboard".equals(dashboardName) || (isServerAdmin || schedule) && !rmsMode && !isNoLicense) {
               if ("userInfoDashboard".equals(dashboardName) && !isServerAdmin && !userAbility) {
                  removedList.add(dashboard);
               } else if ("downloadServerDashboard".equals(dashboardName) && (!isServerAdmin || !(Boolean)infoMap.get("EXT_SERVER_DN_MON_ENABLE"))) {
                  removedList.add(dashboard);
               } else if (!"rmServerDashboard".equals(dashboardName) || isServerAdmin && (Boolean)infoMap.get("EXT_SERVER_RM_MON_ENABLE")) {
                  if (!"datalinkServerDashboard".equals(dashboardName) || isServerAdmin && (Boolean)infoMap.get("EXT_SERVER_DL_MON_ENABLE")) {
                     if (!"deviceConnect".equals(dashboardName) || (isServerAdmin || deviceAbility) && !isNoLicense) {
                        if ("deviceDayConnect".equals(dashboardName) && (!this.isAvailableDeviceDayConnect() || isNoLicense)) {
                           removedList.add(dashboard);
                        } else if ("deviceErrorWarning".equals(dashboardName) && (!isServerAdmin && !deviceAbility || isNoLicense)) {
                           removedList.add(dashboard);
                        } else if ("softwareDeployStatus".equals(dashboardName) && (!isServerAdmin && !deviceAbility || isNoLicense)) {
                           removedList.add(dashboard);
                        }
                     } else {
                        removedList.add(dashboard);
                     }
                  } else {
                     removedList.add(dashboard);
                  }
               } else {
                  removedList.add(dashboard);
               }
            } else {
               removedList.add(dashboard);
            }
         }

         dashboardAll.removeAll(removedList);
         List dashboards = new ArrayList();
         Iterator var25 = dashboardAll.iterator();

         while(var25.hasNext()) {
            UserDashboardEntity entity = (UserDashboardEntity)var25.next();
            V2BaseDashboard dashboard = new V2BaseDashboard();
            dashboard.setDashboardId(entity.getDashboard_id());
            dashboard.setDashboardName(entity.getDashboard_name());
            dashboards.add(dashboard);
         }

         return dashboards;
      }
   }

   private boolean isAvailableDeviceDayConnect() {
      String orgName = SecurityUtils.getUserContainer().getUser().getOrganization();
      User userName = SecurityUtils.getUserContainer().getUser();
      AbilityUtils ability = new AbilityUtils();
      boolean deviceAbility = ability.checkAuthority("Device Write") || ability.checkAuthority("Device Read");
      return deviceAbility && "ROOT".equals(orgName) && !userName.isMu();
   }

   public List getMyDashboardList() throws Exception {
      DashboardManager dashboardInfo = DashboardManagerImpl.getInstance();
      String userId = SecurityUtils.getUserContainer().getUser().getUser_id();
      List entities = dashboardInfo.getDashboardOrderByDashboardId(userId);
      if (entities != null && entities.size() != 0 && this.deleteRepeatedDashboardEntity(entities, dashboardInfo)) {
         this.reassignDashboardPriority(userId, dashboardInfo);
      }

      entities = dashboardInfo.getDashboard(userId);
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map infoMap = serverSetupDao.getServerInfoByOrgId(0L);
      List dashboards = new ArrayList();
      Iterator var7 = entities.iterator();

      while(true) {
         UserDashboardEntity entity;
         String dashboardName;
         do {
            do {
               do {
                  do {
                     if (!var7.hasNext()) {
                        return dashboards;
                     }

                     entity = (UserDashboardEntity)var7.next();
                     dashboardName = entity.getDashboard_name();
                  } while(dashboardName.equals("downloadServerDashboard") && !(Boolean)infoMap.get("EXT_SERVER_DN_MON_ENABLE"));
               } while(dashboardName.equals("rmServerDashboard") && !(Boolean)infoMap.get("EXT_SERVER_RM_MON_ENABLE"));
            } while(dashboardName.equals("datalinkServerDashboard") && !(Boolean)infoMap.get("EXT_SERVER_DL_MON_ENABLE"));
         } while(dashboardName.equals("deviceDayConnect") && !this.isAvailableDeviceDayConnect());

         V2OrderedDashboardResource dashboard = new V2OrderedDashboardResource();
         dashboard.setDashboardId(entity.getDashboard_id());
         dashboard.setDashboardName(entity.getDashboard_name());
         dashboard.setPriority(entity.getPriority());
         dashboard.setUserId(userId);
         dashboards.add(dashboard);
      }
   }

   private void reassignDashboardPriority(String userId, DashboardManager dashboardInfo) {
      try {
         List list = dashboardInfo.getDashboardOrderByDashboardId(userId);

         for(int idx = 0; idx < list.size(); ++idx) {
            dashboardInfo.setDashboardPriority(((UserDashboardEntity)list.get(idx)).getUser_id(), idx + 1, ((UserDashboardEntity)list.get(idx)).getDashboard_id());
         }
      } catch (SQLException var5) {
         this.logger.error("DashboardController Error" + var5.getMessage());
      }

   }

   private Boolean deleteRepeatedDashboardEntity(List list, DashboardManager dashboardInfo) {
      Boolean isRepeated = false;
      if (list.size() == 0) {
         return isRepeated;
      } else {
         UserDashboardEntity temp = (UserDashboardEntity)list.get(0);
         int i = 1;

         while(i < list.size()) {
            if (temp.getDashboard_id() == ((UserDashboardEntity)list.get(i)).getDashboard_id()) {
               try {
                  dashboardInfo.removeDashboardEntity(((UserDashboardEntity)list.get(i)).getUser_id(), ((UserDashboardEntity)list.get(i)).getPriority(), ((UserDashboardEntity)list.get(i)).getDashboard_id());
                  isRepeated = true;
               } catch (SQLException var7) {
                  this.logger.error("DashboardController Error! " + var7.getMessage());
               }

               list.remove(i);
            } else {
               temp = (UserDashboardEntity)list.get(i);
               ++i;
            }
         }

         return isRepeated;
      }
   }

   public List getExternalServersInfo() throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (!RoleUtils.isServerAdminRole(userContainer.getUser())) {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
      } else {
         ArrayList serverListResources = new ArrayList();

         try {
            serverListResources.add(this.getDashboardEdgeServerInfo());
         } catch (Exception var7) {
         }

         try {
            serverListResources.add(this.getDashboardRMServerInfo());
         } catch (Exception var6) {
         }

         try {
            serverListResources.add(this.getDashboardDatalinkServerInfo());
         } catch (Exception var5) {
         }

         try {
            serverListResources.add(this.getDashboardInsightServerInfo());
         } catch (Exception var4) {
         }

         return serverListResources;
      }
   }

   public V2ServerListResource getDashboardRMServerInfo() throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (!RoleUtils.isServerAdminRole(userContainer.getUser())) {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
      } else {
         Integer errCnt = 0;
         int totalCnt = false;
         int runningCnt = 0;
         V2ServerListResource data = new V2ServerListResource();
         data.setId("remoteControl");
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         RmServerImpl rmDao = RmServerImpl.getInstance();
         Map infoMap = serverSetupDao.getServerInfoByOrgId(0L);
         Integer errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
         List rmServerEntityList = rmDao.getRmServerList();
         if (rmServerEntityList != null && rmServerEntityList.size() > 0) {
            int totalCnt = rmServerEntityList.size();
            List serverinfo = new ArrayList();

            for(int i = 0; i < rmServerEntityList.size(); ++i) {
               errCnt = serverSetupDao.getExternalServerErrCount("RM", ((RmServerEntity)rmServerEntityList.get(i)).getIp_address());
               errCnt = errCnt == null ? errThresholdCnt + 1 : errCnt;
               Timestamp lastErrTime = serverSetupDao.getLastErrTime("RM", ((RmServerEntity)rmServerEntityList.get(i)).getIp_address());
               V2ServerListinfo server;
               if (errThresholdCnt <= errCnt) {
                  server = new V2ServerListinfo();
                  server.setIsServerChk("ERR");
                  server.setServerName(((RmServerEntity)rmServerEntityList.get(i)).getServer_name());
                  server.setIpAddress(((RmServerEntity)rmServerEntityList.get(i)).getIp_address());
                  server.setCurrentDate(lastErrTime);
                  serverinfo.add(server);
               } else {
                  server = new V2ServerListinfo();
                  server.setIsServerChk("NORMAL");
                  server.setServerName(((RmServerEntity)rmServerEntityList.get(i)).getServer_name());
                  server.setIpAddress(((RmServerEntity)rmServerEntityList.get(i)).getIp_address());
                  server.setCurrentDate(lastErrTime);
                  serverinfo.add(server);
                  ++runningCnt;
               }
            }

            data.setSeverInfolist(serverinfo);
            data.setRunningCnt(runningCnt);
            data.setTotalServerCnt(totalCnt);
            return data;
         } else {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"RM Server list"});
         }
      }
   }

   public V2ServerListResource getDashboardDatalinkServerInfo() throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (!RoleUtils.isServerAdminRole(userContainer.getUser())) {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
      } else {
         Integer errCnt = 0;
         int totalCnt = false;
         int runningCnt = 0;
         Timestamp lastErrTime = null;
         V2ServerListResource data = new V2ServerListResource();
         data.setId("datalink");
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         DatalinkServerImpl datalinkDao = DatalinkServerImpl.getInstance();
         Map infoMap = serverSetupDao.getServerInfoByOrgId(0L);
         Integer errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
         List datalinkServerEntityList = datalinkDao.getDatalinkServerList();
         if (datalinkServerEntityList != null && datalinkServerEntityList.size() > 0) {
            int totalCnt = datalinkServerEntityList.size();
            List serverinfo = new ArrayList();

            for(int i = 0; i < datalinkServerEntityList.size(); ++i) {
               errCnt = serverSetupDao.getExternalServerErrCount("DATALINK", ((DatalinkServerEntity)datalinkServerEntityList.get(i)).getIp_address());
               errCnt = errCnt == null ? errThresholdCnt + 1 : errCnt;
               lastErrTime = serverSetupDao.getLastErrTime("DATALINK", ((DatalinkServerEntity)datalinkServerEntityList.get(i)).getIp_address());
               V2ServerListinfo server;
               if (errThresholdCnt <= errCnt) {
                  server = new V2ServerListinfo();
                  server.setIsServerChk("ERR");
                  server.setServerName(((DatalinkServerEntity)datalinkServerEntityList.get(i)).getServer_name());
                  server.setIpAddress(((DatalinkServerEntity)datalinkServerEntityList.get(i)).getIp_address());
                  server.setCurrentDate(lastErrTime);
                  serverinfo.add(server);
               } else {
                  server = new V2ServerListinfo();
                  server.setIsServerChk("NORMAL");
                  server.setServerName(((DatalinkServerEntity)datalinkServerEntityList.get(i)).getServer_name());
                  server.setIpAddress(((DatalinkServerEntity)datalinkServerEntityList.get(i)).getIp_address());
                  server.setCurrentDate(lastErrTime);
                  serverinfo.add(server);
                  ++runningCnt;
               }
            }

            data.setSeverInfolist(serverinfo);
            data.setRunningCnt(runningCnt);
            data.setTotalServerCnt(totalCnt);
            return data;
         } else {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"Datalink Server list"});
         }
      }
   }

   public V2ServerListResource getDashboardEdgeServerInfo() throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (!RoleUtils.isServerAdminRole(userContainer.getUser())) {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
      } else {
         Integer errCnt = 0;
         int totalCnt = false;
         int runningCnt = 0;
         Timestamp lastErrTime = null;
         V2ServerListResource data = new V2ServerListResource();
         data.setId("edge");
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         DownloadInfo download = DownloadInfoImpl.getInstance();
         Map infoMap = serverSetupDao.getServerInfoByOrgId(0L);
         Integer errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
         errThresholdCnt = errThresholdCnt == null ? 0 : errThresholdCnt;
         EdgeServerDao edgeServerDao = new EdgeServerDao();
         List edgeServerList = edgeServerDao.selectAllEdge();
         if (edgeServerList != null && edgeServerList.size() > 0) {
            int totalCnt = edgeServerList.size();
            List serverinfo = new ArrayList();

            for(int i = 0; i < edgeServerList.size(); ++i) {
               errCnt = serverSetupDao.getExternalServerErrCount("DOWNLOAD", ((EdgeServer)edgeServerList.get(i)).getIpAddress());
               errCnt = errCnt == null ? errThresholdCnt + 1 : errCnt;
               lastErrTime = serverSetupDao.getLastErrTime("DOWNLOAD", ((EdgeServer)edgeServerList.get(i)).getIpAddress());
               V2ServerListinfo server;
               if (errThresholdCnt <= errCnt) {
                  server = new V2ServerListinfo();
                  server.setIsServerChk("ERR");
                  server.setServerName(((EdgeServer)edgeServerList.get(i)).getHostName());
                  server.setIpAddress(((EdgeServer)edgeServerList.get(i)).getIpAddress());
                  server.setCurrentDate(lastErrTime);
                  serverinfo.add(server);
               } else {
                  server = new V2ServerListinfo();
                  server.setIsServerChk("NORMAL");
                  server.setServerName(((EdgeServer)edgeServerList.get(i)).getHostName());
                  server.setIpAddress(((EdgeServer)edgeServerList.get(i)).getIpAddress());
                  server.setCurrentDate(lastErrTime);
                  serverinfo.add(server);
                  ++runningCnt;
               }
            }

            data.setSeverInfolist(serverinfo);
            data.setRunningCnt(runningCnt);
            data.setTotalServerCnt(totalCnt);
            return data;
         } else {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"Download Server list"});
         }
      }
   }

   public V2ServerListResource getDashboardInsightServerInfo() throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (!RoleUtils.isServerAdminRole(userContainer.getUser())) {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
      } else {
         int runningCnt = 0;
         Timestamp lastErrTime = null;
         V2ServerListResource data = new V2ServerListResource();
         data.setId("insight");
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         InsightServerDao insightServerDao = new InsightServerDao();
         InsightServerEntity insightServerEntity = insightServerDao.getInsightServerInfo();
         Map infoMap = serverSetupDao.getServerInfoByOrgId(0L);
         Integer errThresholdCnt = Integer.parseInt(infoMap.get("ext_server_err_chk").toString());
         if (null != insightServerEntity && insightServerEntity.isUse_server()) {
            int totalCnt = insightServerEntity.isUse_server() ? 1 : 0;
            List serverinfo = new ArrayList();
            Integer errCnt = serverSetupDao.getExternalServerErrCount("INSIGHT", insightServerEntity.getIp());
            errCnt = errCnt == null ? errThresholdCnt + 1 : errCnt;
            lastErrTime = serverSetupDao.getLastErrTime("INSIGHT", insightServerEntity.getIp());
            V2ServerListinfo server = new V2ServerListinfo();
            server.setServerName("Insight Server");
            server.setIpAddress(insightServerEntity.getIp());
            server.setCurrentDate(lastErrTime);
            if (errThresholdCnt <= errCnt) {
               server.setIsServerChk("ERR");
            } else {
               server.setIsServerChk("NORMAL");
               ++runningCnt;
            }

            serverinfo.add(server);
            data.setSeverInfolist(serverinfo);
            data.setRunningCnt(runningCnt);
            data.setTotalServerCnt(totalCnt);
            return data;
         } else {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"Insight Server"});
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority')")
   public V2DashboardListResource getDashboardPlaylistInfo() throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      V2DashboardListResource data = new V2DashboardListResource();
      data.setResourceType("PLAYLIST");
      PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
      PlaylistDao playlistDao = new PlaylistDao();
      Map condition = new HashMap();
      boolean canReadUnshared = false;
      AbilityInfo abilityInfo = AbilityInfoImpl.getInstance();
      List abilityList = abilityInfo.getAllAbilityListByUserId(userContainer.getUser().getUser_id());
      Iterator it = abilityList.iterator();

      while(it.hasNext()) {
         Map abilityMap = (Map)it.next();
         String abilityValue = (String)abilityMap.get("ability_name");
         if (abilityValue.equalsIgnoreCase("Content Manage Authority")) {
            canReadUnshared = true;
         } else if (abilityValue.equalsIgnoreCase("Playlist Manage Authority")) {
            canReadUnshared = true;
         }
      }

      String playlist_type = "0,1,2,3,4,5,6";
      condition.put("canReadUnshared", canReadUnshared);
      condition.put("listType", "ALL");
      condition.put("creatorID", SecurityUtils.getLoginUserId());
      condition.put("playlist_type", playlist_type);
      int totalCount = playlistDao.getPlaylistListCnt(condition);
      if (userContainer.getUser() != null) {
         int usedCount = playlistInfo.getUsedPlaylistCount(userContainer.getUser().getRoot_group_id());
         data.setAllListCnt(totalCount);
         data.setRunningListCnt(usedCount);
         return data;
      } else {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority')")
   public V2DashboardListResource getDashboardRuleSetInfo() throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      V2DashboardListResource data = new V2DashboardListResource();
      data.setResourceType("RULESET");
      RuleSetInfo ruleSetInfo = RuleSetInfoImpl.getInstance();
      boolean canReadUnshared = false;
      AbilityInfo abilityInfo = AbilityInfoImpl.getInstance();
      List abilityList = abilityInfo.getAllAbilityListByUserId(userContainer.getUser().getUser_id());
      Iterator it = abilityList.iterator();

      String abilityValue;
      while(it.hasNext()) {
         Map abilityMap = (Map)it.next();
         abilityValue = (String)abilityMap.get("ability_name");
         if (abilityValue.equalsIgnoreCase("Content Manage Authority")) {
            canReadUnshared = true;
         } else if (abilityValue.equalsIgnoreCase("Playlist Manage Authority")) {
            canReadUnshared = true;
         }
      }

      if (userContainer.getUser() != null) {
         SelectConditionRuleset selectConditionRuleset = new SelectConditionRuleset();
         selectConditionRuleset.setIncludeTemp(true);
         abilityValue = userContainer.getUser().getOrganization();
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         long orgId = userGroupInfo.getOrgGroupIdByName(abilityValue);
         if (0L != userContainer.getUser().getRoot_group_id()) {
            selectConditionRuleset.setOrgId(orgId);
         }

         selectConditionRuleset.setGroupId(userContainer.getUser().getRoot_group_id());
         int totalCount = ruleSetInfo.getRuleSetListTotalCount(selectConditionRuleset);
         selectConditionRuleset.setIsUsed(true);
         int usedCount = ruleSetInfo.getRuleSetListTotalCount(selectConditionRuleset);
         data.setAllListCnt(totalCount);
         data.setRunningListCnt(usedCount);
         return data;
      } else {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority')")
   public V2DashboardListResource getDashboardScheduleInfo() throws Exception {
      V2DashboardListResource data = new V2DashboardListResource();
      data.setResourceType("SCHEDULE");
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
      String orgName = userContainer.getUser().getOrganization();
      DashboardManager dashboardInfo = DashboardManagerImpl.getInstance();
      long runningCount = 0L;
      long totalCount = 0L;
      long reservedCount = 0L;
      long notSetDeviceCount = 0L;
      List list = dashboardInfo.getDashboard(userId);
      if (list != null) {
         if ("ROOT".equals(orgName)) {
            runningCount = schInfo.getMapedScheduleCount();
            totalCount = schInfo.getAllScheduleCount();
            reservedCount = schInfo.getContentScheduleCntToday();
            notSetDeviceCount = schInfo.getNotMapedScheduleCount();
         } else {
            runningCount = schInfo.getMapedScheduleCount(orgName);
            totalCount = schInfo.getAllScheduleCount(orgName);
            reservedCount = schInfo.getContentScheduleCntToday(orgName);
            notSetDeviceCount = schInfo.getNotMapedScheduleCount(orgName);
         }

         data.setAllListCnt((int)totalCount);
         data.setRunningListCnt((int)runningCount);
         data.setReservedCnt((int)reservedCount);
         data.setNotSetDeviceCnt((int)notSetDeviceCount);
         return data;
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"userId"});
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority')")
   public V2DashboardListResource getDashboardContentInfo() throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = SecurityUtils.getLoginUserId();
      V2DashboardListResource data = new V2DashboardListResource();
      data.setResourceType("CONTENT");
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      ContentDao contentDao = new ContentDao();
      DashboardManager dashboardInfo = DashboardManagerImpl.getInstance();
      List list = dashboardInfo.getDashboard(userId);
      if (list == null) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"userId"});
      } else {
         Map condition = new HashMap();
         condition.put("listType", "ALL");
         condition.put("creatorID", userId);
         condition.put("content_type", "CONTENT");
         condition.put("isMain", "true");
         String orgName = SecurityUtils.getUserContainer().getUser().getOrganization();
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         long orgId = userGroupInfo.getOrgGroupIdByName(orgName);
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
         boolean contentsApprovalEnable = false;
         if (infoMap != null && infoMap.get("CONTENTS_APPROVAL_ENABLE") != null) {
            contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
         }

         AbilityUtils abilityUtils = new AbilityUtils();
         boolean canReadUnshared = false;
         AbilityInfo abilityInfo = AbilityInfoImpl.getInstance();
         List abilityList = abilityInfo.getAllAbilityListByUserId(userContainer.getUser().getUser_id());
         Iterator it = abilityList.iterator();

         while(it.hasNext()) {
            Map abilityMap = (Map)it.next();
            String abilityValue = (String)abilityMap.get("ability_name");
            if (abilityValue.equalsIgnoreCase("Content Manage Authority")) {
               canReadUnshared = true;
            }
         }

         condition.put("canReadUnshared", canReadUnshared);
         if (contentsApprovalEnable && !abilityUtils.isContentApprovalAuthority()) {
            condition.put("isContentApprove", true);
         }

         int totalCount = contentDao.getContentListCnt(condition);
         condition.put("contentUsingStatusFilter", "used_content");
         int usedCount = contentDao.getContentListCnt(condition);
         data.setAllListCnt(totalCount);
         data.setRunningListCnt(usedCount);
         if (infoMap != null && infoMap.get("CONTENTS_APPROVAL_ENABLE") != null) {
            contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
         }

         if (contentsApprovalEnable && SecurityUtils.checkContentApprovalPermission()) {
            int unapprovedCount = false;
            int unapprovedCount;
            if (!RoleUtils.isServerAdminRole(userContainer.getUser()) && (RoleUtils.isServerAdminRole(userContainer.getUser()) || userContainer.getUser().getRoot_group_id() != 0L)) {
               unapprovedCount = this.getUnApprovedContentCountOfLoginUser();
            } else {
               unapprovedCount = contentInfo.V2GetUnapprovedContentCnt();
            }

            int rejectCount = contentInfo.getRejectCnt(userContainer.getUser().getRoot_group_id());
            data.setUnApprovedCnt(unapprovedCount);
            data.setRejectCnt(rejectCount);
         }

         return data;
      }
   }

   private int getUnApprovedContentCountOfLoginUser() throws SQLException {
      User loginUser = SecurityUtils.getLoginUser();
      ContentDao contentdao = new ContentDao();
      Map condition = new HashMap();
      condition.put("content_type", "CONTENT");
      condition.put("listType", "SUBMITTED");
      condition.put("creatorID", loginUser.getUser_id());
      condition.put("canReadUnshared", true);
      condition.put("groupID", String.valueOf(loginUser.getGroup_id()));
      condition.put("isMain", "true");
      condition.put("searchID", "-1");
      if (loginUser.getRole_name().equals("Server Administrator")) {
         condition.put("isServerAdmin", true);
      }

      int countOfUnapprovedContents = contentdao.getContentListCnt(condition);
      return countOfUnapprovedContents;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2SoftwareDeployInfo getSoftwareDeployInfo() throws Exception {
      User currentUser = SecurityUtils.getUserContainer().getUser();
      String organization = currentUser.getOrganization();
      SoftwareManager softwareManager = SoftwareManagerImpl.getInstance();
      V2SoftwareDeployInfo sofwareInfo = new V2SoftwareDeployInfo();
      Map condition = new HashMap();
      condition.put("day", 7);
      if (organization != null) {
         condition.put("organization", organization);
         Map status = softwareManager.getSoftwareDeployStatus(condition);
         Number upgradeSuccess = (Number)status.get("upgrade_success");
         Number upgradeFail = (Number)status.get("upgrade_fail");
         Number downloading = (Number)status.get("downloading");
         Number standBy = (Number)status.get("stand_by");
         Number downloadFinished = (Number)status.get("download_finished");
         sofwareInfo.setUpgradeSuccess(Long.valueOf(upgradeSuccess.longValue()));
         sofwareInfo.setUpgradeFail(Long.valueOf(upgradeFail.longValue()));
         sofwareInfo.setDownloading(Long.valueOf(downloading.longValue()));
         sofwareInfo.setStandBy(Long.valueOf(standBy.longValue()));
         sofwareInfo.setDownloadFinished(Long.valueOf(downloadFinished.longValue()));
         return sofwareInfo;
      } else {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"organization information"});
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public List getErrorWarningDailyStatics() throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String organization = userContainer.getUser().getOrganization();
      AlarmManager alarmManager = AlarmManagerImpl.getInstance();
      if ("ROOT".equals(organization)) {
         organization = null;
      }

      List deviceErrorWarningResources = new ArrayList();
      Calendar currentCalendar = Calendar.getInstance();

      for(int i = 0; i < 7; ++i) {
         V2DeviceHealthByDateResource resource = new V2DeviceHealthByDateResource();
         SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
         dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT0"));
         String timeStamp = dateFormat.format(currentCalendar.getTime());
         Date parsedDate = dateFormat.parse(timeStamp);
         resource.setDate(new Timestamp(parsedDate.getTime()));
         deviceErrorWarningResources.add(resource);
         currentCalendar.add(5, -1);
      }

      List errorWarningStatics = alarmManager.getErrorWarningDailyStatics(organization, new Timestamp(currentCalendar.getTime().getTime()));
      Iterator var14 = errorWarningStatics.iterator();

      label44:
      while(var14.hasNext()) {
         DeviceErrorWarningStaticEntity errorWarningStatic = (DeviceErrorWarningStaticEntity)var14.next();
         Iterator var16 = deviceErrorWarningResources.iterator();

         while(true) {
            V2DeviceHealthByDateResource deviceErrorWarningResource;
            do {
               String reportDate;
               do {
                  if (!var16.hasNext()) {
                     continue label44;
                  }

                  deviceErrorWarningResource = (V2DeviceHealthByDateResource)var16.next();
                  Date date = new Date();
                  date.setTime(deviceErrorWarningResource.getDate().getTime());
                  reportDate = (new SimpleDateFormat("yyyy-MM-dd")).format(date);
               } while(!errorWarningStatic.getEvent_date().equals(reportDate));

               if ("E".equals(errorWarningStatic.getEWCategory())) {
                  deviceErrorWarningResource.setError(errorWarningStatic.getCount());
               }
            } while(!"W".equals(errorWarningStatic.getEWCategory()) && !"SW".equals(errorWarningStatic.getEWCategory()));

            deviceErrorWarningResource.setWarning(deviceErrorWarningResource.getWarning() + errorWarningStatic.getCount());
         }
      }

      return deviceErrorWarningResources;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public List getHealthCategoryStaticInfo() throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String organization = userContainer.getUser().getOrganization();
      AlarmManager alarmManager = AlarmManagerImpl.getInstance();
      if ("ROOT".equals(organization)) {
         organization = null;
      }

      List deviceCategoryErrorWarningResources = new ArrayList();
      List errorWarningCategoryStatics = alarmManager.getErrorWarningCategoryStatics(organization);
      Iterator var6 = errorWarningCategoryStatics.iterator();

      while(var6.hasNext()) {
         DeviceErrorWarningStaticEntity errorWarningCategoryStatic = (DeviceErrorWarningStaticEntity)var6.next();
         String category = errorWarningCategoryStatic.getCategory();
         boolean find = false;
         V2DeviceCategoryErrorWarningResource deviceCategoryErrorWarningResource;
         if ("SW".equals(category)) {
            deviceCategoryErrorWarningResource = new V2DeviceCategoryErrorWarningResource();
            deviceCategoryErrorWarningResource.setCategory("SW");
            deviceCategoryErrorWarningResource.setWarning(errorWarningCategoryStatic.getCount());
            deviceCategoryErrorWarningResources.add(deviceCategoryErrorWarningResource);
            find = true;
         } else {
            Iterator var10 = deviceCategoryErrorWarningResources.iterator();

            while(var10.hasNext()) {
               V2DeviceCategoryErrorWarningResource deviceCategoryErrorWarningResource = (V2DeviceCategoryErrorWarningResource)var10.next();
               if (deviceCategoryErrorWarningResource.getCategory().equals(category)) {
                  if ("E".equals(errorWarningCategoryStatic.getEWCategory())) {
                     deviceCategoryErrorWarningResource.setError(errorWarningCategoryStatic.getCount());
                  }

                  if ("W".equals(errorWarningCategoryStatic.getEWCategory())) {
                     deviceCategoryErrorWarningResource.setWarning(errorWarningCategoryStatic.getCount());
                  }

                  find = true;
               }
            }
         }

         if (!find) {
            deviceCategoryErrorWarningResource = new V2DeviceCategoryErrorWarningResource();
            deviceCategoryErrorWarningResource.setCategory(errorWarningCategoryStatic.getCategory());
            if ("E".equals(errorWarningCategoryStatic.getEWCategory())) {
               deviceCategoryErrorWarningResource.setError(errorWarningCategoryStatic.getCount());
            }

            if ("W".equals(errorWarningCategoryStatic.getEWCategory())) {
               deviceCategoryErrorWarningResource.setWarning(errorWarningCategoryStatic.getCount());
            }

            deviceCategoryErrorWarningResources.add(deviceCategoryErrorWarningResource);
         }
      }

      return deviceCategoryErrorWarningResources;
   }

   @PreAuthorize("hasAnyAuthority('Content Manage Authority', 'Device Only Approval Authority', 'User Approval Authority')")
   public List getUnapprovedResources() throws Exception {
      List unapprovedResources = new ArrayList();
      if (SecurityUtils.checkDeviceApprovalPermission()) {
         V2GetDeviceConnectResource v2GetDeviceConnectResource = this.getDeviceConnectingInfo((String)null, (Float)null);
         V2UnapprovedResource v2UnapprovedResourceDevice = new V2UnapprovedResource();
         v2UnapprovedResourceDevice.setId("device");
         v2UnapprovedResourceDevice.setCount(v2GetDeviceConnectResource.getNonApprovalCount());
         unapprovedResources.add(v2UnapprovedResourceDevice);
      }

      V2UnapprovedResource unapprovedResourceUser;
      if (SecurityUtils.checkContentApprovalPermission() && null != this.getDashboardContentInfo().getUnApprovedCnt()) {
         unapprovedResourceUser = new V2UnapprovedResource();
         unapprovedResourceUser.setId("content");
         unapprovedResourceUser.setCount(this.getDashboardContentInfo().getUnApprovedCnt());
         unapprovedResources.add(unapprovedResourceUser);
      }

      if (SecurityUtils.checkUserApprovalPermission()) {
         unapprovedResourceUser = new V2UnapprovedResource();
         unapprovedResourceUser.setId("user");
         V2DashboardUserInfoResource v2DashboardUserInfoResource = this.v2UserService.listDashboardUserInfo();
         unapprovedResourceUser.setCount(v2DashboardUserInfoResource.getUnapprovedCount());
         unapprovedResources.add(unapprovedResourceUser);
      }

      return unapprovedResources;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Schedule Read Authority')")
   public List getDashboardContentsInfo() throws Exception {
      List resourceList = new ArrayList();
      if (SecurityUtils.checkContentReadPermission()) {
         resourceList.add(this.getDashboardContentInfo());
         resourceList.add(this.getDashboardPlaylistInfo());
         resourceList.add(this.getDashboardRuleSetInfo());
      }

      if (SecurityUtils.checkScheduleReadPermission()) {
         resourceList.add(this.getDashboardScheduleInfo());
      }

      return resourceList;
   }

   public List getWidgetActiveStatus() {
      List activeWidgetResources = new ArrayList();
      AbilityUtils ability = new AbilityUtils();
      List selectableWidgetList = new ArrayList();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (RoleUtils.isServerAdminRole(userContainer.getUser())) {
         selectableWidgetList.add(this.storageInfoDashboard);
         selectableWidgetList.add(this.externalServersInfoDashboard);
      }

      if (ability.checkAuthority("User Write")) {
         selectableWidgetList.add(this.userInfoDashboard);
      }

      if (ability.checkAuthority("Device Write")) {
         selectableWidgetList.add(this.softwareDeployStatus);
      }

      try {
         DashboardManager dashboardManager = DashboardManagerImpl.getInstance();
         String userId = SecurityUtils.getLoginUserId();
         List list = dashboardManager.getDashboard(userId);
         Iterator var8 = list.iterator();

         V2ActiveWidgetResource activeWidgetResource;
         while(var8.hasNext()) {
            UserDashboardEntity entity = (UserDashboardEntity)var8.next();
            if (selectableWidgetList.contains(entity.getDashboard_id())) {
               activeWidgetResource = new V2ActiveWidgetResource();
               activeWidgetResource.setActive(true);
               activeWidgetResource.setWidgetId(entity.getDashboard_id());
               activeWidgetResources.add(activeWidgetResource);
               selectableWidgetList.remove(entity.getDashboard_id());
            }
         }

         var8 = selectableWidgetList.iterator();

         while(var8.hasNext()) {
            Integer widgetId = (Integer)var8.next();
            activeWidgetResource = new V2ActiveWidgetResource();
            activeWidgetResource.setActive(false);
            activeWidgetResource.setWidgetId(widgetId);
            activeWidgetResources.add(activeWidgetResource);
         }

         return activeWidgetResources;
      } catch (Exception var11) {
         this.logger.error(var11.getMessage());
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      }
   }

   public void setWidgetActiveStatus(List activeWidgetResources) throws Exception {
      List selectableWidgetList = new ArrayList();
      selectableWidgetList.add(this.storageInfoDashboard);
      selectableWidgetList.add(this.userInfoDashboard);
      selectableWidgetList.add(this.softwareDeployStatus);
      selectableWidgetList.add(this.externalServersInfoDashboard);

      try {
         DashboardManager dashboardManager = DashboardManagerImpl.getInstance();
         String userId = SecurityUtils.getLoginUserId();
         dashboardManager.removeAllDashboardWidget(userId);
         Iterator var5 = activeWidgetResources.iterator();

         while(var5.hasNext()) {
            V2ActiveWidgetResource activeWidgetResource = (V2ActiveWidgetResource)var5.next();
            if (activeWidgetResource.getActive()) {
               if (!selectableWidgetList.contains(activeWidgetResource.getWidgetId())) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
               }

               dashboardManager.addDashboardWidget(userId, activeWidgetResource.getWidgetId());
            }
         }

      } catch (Exception var7) {
         this.logger.error(var7.getMessage());
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      }
   }
}
