package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.LFDContent;
import com.samsung.magicinfo.webauthor2.model.VerificationResponse;
import com.samsung.magicinfo.webauthor2.service.LFDContentService;
import com.samsung.magicinfo.webauthor2.webapi.assembler.ContentResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.assembler.LFDContentResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.resource.ContentResource;
import com.samsung.magicinfo.webauthor2.webapi.resource.LFDContentResource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.PagedResourcesAssembler;
import org.springframework.hateoas.PagedResources;
import org.springframework.hateoas.ResourceAssembler;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/LFDContents"})
public class LFDContentQueryController {
  private LFDContentService lfdContentService;
  
  private LFDContentResourceAssembler lfdContentResourceAssembler;
  
  private ContentResourceAssembler contentResourceAssembler;
  
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.webapi.controller.LFDContentQueryController.class);
  
  @Autowired
  public LFDContentQueryController(LFDContentService lfdContentService, LFDContentResourceAssembler lfdContentResourceAssembler, ContentResourceAssembler contentResourceAssembler) {
    this.lfdContentService = lfdContentService;
    this.lfdContentResourceAssembler = lfdContentResourceAssembler;
    this.contentResourceAssembler = contentResourceAssembler;
  }
  
  @GetMapping
  public HttpEntity<PagedResources<ContentResource>> getContentsWithLFDMediaType(@PageableDefault(size = 20, page = 0) Pageable pageable, @RequestParam String playerType, PagedResourcesAssembler<Content> assembler) {
    Page<Content> page = this.lfdContentService.getLFDContentList(pageable, DeviceType.valueOf(playerType));
    PagedResources<ContentResource> contentResources = assembler.toResource(page, (ResourceAssembler)this.contentResourceAssembler);
    return (HttpEntity<PagedResources<ContentResource>>)ResponseEntity.ok(contentResources);
  }
  
  @RequestMapping(value = {"/{lfdContentId}"}, method = {RequestMethod.GET})
  public HttpEntity<LFDContentResource> getLFDContent(@PathVariable String lfdContentId) {
    LFDContent content = this.lfdContentService.getLFDContent(lfdContentId);
    if (content == null)
      return (HttpEntity<LFDContentResource>)ResponseEntity.notFound().build(); 
    LFDContentResource contentResources = this.lfdContentResourceAssembler.toResource(content);
    return (HttpEntity<LFDContentResource>)ResponseEntity.ok(contentResources);
  }
  
  @GetMapping({"/Validate"})
  public HttpEntity<VerificationResponse> validateLFD(@RequestParam String contentId) {
    LFDContent lfdContent = this.lfdContentService.getLFDContent(contentId);
    if (lfdContent == null)
      return (HttpEntity<VerificationResponse>)ResponseEntity.notFound().build(); 
    VerificationResponse response = this.lfdContentService.verifyLFD(lfdContent);
    if (!response.isValid())
      logger.warn("LFD file " + lfdContent.getFileName() + " validation error: " + response.getMessage()); 
    boolean waitReviewForRelease = true;
    if (waitReviewForRelease)
      return (HttpEntity<VerificationResponse>)ResponseEntity.ok(new VerificationResponse(true, "Valid")); 
    return (HttpEntity<VerificationResponse>)ResponseEntity.ok(response);
  }
  
  @GetMapping({"/ValidateTest"})
  public HttpEntity<VerificationResponse> validateLfdForTests(@RequestParam String contentId) {
    LFDContent lfdContent = this.lfdContentService.getLFDContent(contentId);
    if (lfdContent == null)
      return (HttpEntity<VerificationResponse>)ResponseEntity.notFound().build(); 
    VerificationResponse response = this.lfdContentService.verifyLFD(lfdContent);
    logger.warn("LFD file " + lfdContent.getFileName() + " validation error: " + response.getMessage());
    return (HttpEntity<VerificationResponse>)ResponseEntity.ok(response);
  }
}
