package com.samsung.magicinfo.rc.model.api;

import com.samsung.magicinfo.rc.model.api.VncItems;

public class VncResponse {
  String apiVersion;
  
  Long errorCode;
  
  String errorMessage;
  
  String status;
  
  VncItems items;
  
  public void setApiVersion(String apiVersion) {
    this.apiVersion = apiVersion;
  }
  
  public void setErrorCode(Long errorCode) {
    this.errorCode = errorCode;
  }
  
  public void setErrorMessage(String errorMessage) {
    this.errorMessage = errorMessage;
  }
  
  public void setStatus(String status) {
    this.status = status;
  }
  
  public void setItems(VncItems items) {
    this.items = items;
  }
  
  public String getApiVersion() {
    return this.apiVersion;
  }
  
  public Long getErrorCode() {
    return this.errorCode;
  }
  
  public String getErrorMessage() {
    return this.errorMessage;
  }
  
  public String getStatus() {
    return this.status;
  }
  
  public VncItems getItems() {
    return this.items;
  }
}
