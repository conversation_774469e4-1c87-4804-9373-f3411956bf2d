package com.samsung.magicinfo.webauthor.ftp;

import com.samsung.magicinfo.webauthor.ftp.FtpClientConfiguration;
import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FtpClientConnection {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor.ftp.FtpClientConnection.class);
  
  private final String ftpAddress;
  
  private final int ftpPort;
  
  public FtpClientConnection(String ftpAddress, int ftpPort) {
    this.ftpAddress = ftpAddress;
    this.ftpPort = ftpPort;
  }
  
  public FTPClient getConnection(String user, String password, boolean sslEnabled) throws IOException, UploaderException {
    FtpClientConfiguration configuration;
    try {
      configuration = (new FtpClientConfiguration.ConfigurationBuilder(this.ftpPort)).sslEnabled(sslEnabled).getFtpClient();
    } catch (NoSuchAlgorithmException e) {
      logger.error("cryptographic algorithm is requested but is not available in the environment: {}", e.getMessage());
      throw new UploaderException(699, "ServerInternalUploadError " + e
          .getMessage());
    } 
    FTPClient client = configuration.getClient();
    client.connect(this.ftpAddress);
    int replyCode = client.getReplyCode();
    if (!FTPReply.isPositiveCompletion(replyCode)) {
      logger.error("Failed while connecting to FTP server: url {} port {} ", this.ftpAddress, Integer.valueOf(client.getDefaultPort()));
      throw new UploaderException(699, "ServerInternalUploadError fail_access_ftp");
    } 
    boolean success = client.login(user, password);
    if (!success) {
      logger.error("Failed while logging to FTP server: url {} port {} ", this.ftpAddress, Integer.valueOf(client.getDefaultPort()));
      throw new UploaderException(699, "ServerInternalUploadError fail_access_ftp");
    } 
    return client;
  }
  
  public void closeConnection(FTPClient client) {
    if (client != null && client.isConnected())
      try {
        client.disconnect();
      } catch (IOException e) {
        logger.error("Cannot close FTP connection {}", e);
      }  
  }
}
