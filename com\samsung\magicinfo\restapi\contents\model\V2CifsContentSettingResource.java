package com.samsung.magicinfo.restapi.contents.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Size;

@JsonInclude(Include.NON_EMPTY)
@ApiModel(
   value = "V2CifsContentSettingResource",
   description = "CIFS (Common Internet File System) is a protocol that allows programs to make requests for files or services on remote computers over the Internet."
)
public class V2CifsContentSettingResource {
   @ApiModelProperty(
      dataType = "string",
      example = "00000000-0000-0000-0000-000000000000",
      allowEmptyValue = true,
      value = "ID of the content"
   )
   private String contentId = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Media type of the content",
      example = "CIFS",
      allowableValues = "CIFS"
   )
   private String type = "CIFS";
   @ApiModelProperty(
      dataType = "string",
      value = "Login user ID",
      example = "admin"
   )
   @Size(
      max = 64,
      message = "[ContentFilter][userId] max size is 64."
   )
   private String userId = "";
   @ApiModelProperty(
      dataType = "string",
      example = "0",
      value = "ID of the group to which the content belongs"
   )
   private String groupId = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Name of the CIFS Content"
   )
   private String cifsContentName = "";
   @ApiModelProperty(
      dataType = "string",
      example = "***************",
      value = "IP address of the CIFS content"
   )
   private String cifsIP = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Login ID of the CIFS",
      example = "magicinfo"
   )
   private String cifsLoginId = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Password of the CIFS"
   )
   private String cifsPassword = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Directory path of content in the CIFS",
      example = "Shared/CIFS"
   )
   private String cifsDirectory = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Refresh interval of the CIFS"
   )
   private String cifsRefreshInterval = "";
   @ApiModelProperty(
      dataType = "boolean",
      value = "Whether polling is activated",
      allowableValues = "Y,N"
   )
   private String canRefresh = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Number of login retries. maximum value is 6"
   )
   private String loginRetryMaxCount = "";
   @ApiModelProperty(
      dataType = "string",
      value = "Whether to enable login retry ('Y' or 'N')"
   )
   private String canLoginRetry = "";

   public V2CifsContentSettingResource() {
      super();
   }

   public String getContentId() {
      return this.contentId;
   }

   public void setContentId(String contentId) {
      this.contentId = contentId;
   }

   public String getType() {
      return this.type;
   }

   public void setType(String type) {
      this.type = type;
   }

   public String getCifsContentName() {
      return this.cifsContentName;
   }

   public void setCifsContentName(String cifsContentName) {
      this.cifsContentName = cifsContentName;
   }

   public String getCifsIP() {
      return this.cifsIP;
   }

   public void setCifsIP(String cifsIP) {
      this.cifsIP = cifsIP;
   }

   public String getCifsLoginId() {
      return this.cifsLoginId;
   }

   public void setCifsLoginId(String cifsLoginId) {
      this.cifsLoginId = cifsLoginId;
   }

   public String getCifsPassword() {
      return this.cifsPassword;
   }

   public void setCifsPassword(String cifsPassword) {
      this.cifsPassword = cifsPassword;
   }

   public String getCifsDirectory() {
      return this.cifsDirectory;
   }

   public void setCifsDirectory(String cifsDirectory) {
      this.cifsDirectory = cifsDirectory;
   }

   public String getCifsRefreshInterval() {
      return this.cifsRefreshInterval;
   }

   public void setCifsRefreshInterval(String cifsRefreshInterval) {
      this.cifsRefreshInterval = cifsRefreshInterval;
   }

   public String getCanRefresh() {
      return this.canRefresh;
   }

   public void setCanRefresh(String canRefresh) {
      this.canRefresh = canRefresh;
   }

   public String getLoginRetryMaxCount() {
      return this.loginRetryMaxCount;
   }

   public void setLoginRetryMaxCount(String loginRetryMaxCount) {
      this.loginRetryMaxCount = loginRetryMaxCount;
   }

   public String getCanLoginRetry() {
      return this.canLoginRetry;
   }

   public void setCanLoginRetry(String canLoginRetry) {
      this.canLoginRetry = canLoginRetry;
   }

   public String getGroupId() {
      return this.groupId;
   }

   public void setGroupId(String groupId) {
      this.groupId = groupId;
   }

   public String getUserId() {
      return this.userId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }
}
