package com.samsung.magicinfo.framework.scheduler.manager;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAO;
import com.samsung.magicinfo.framework.scheduler.entity.AdScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.AdSlotEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ChannelEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.FrameEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramFile;
import com.samsung.magicinfo.framework.scheduler.entity.ScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.exception.ScheduleException;
import com.samsung.magicinfo.framework.setup.entity.TagEntity;
import com.samsung.magicinfo.framework.setup.manager.TagInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.schemas.schedule_3_0.AmsModeDocument;
import com.samsung.magicinfo.schemas.schedule_3_0.ContentDocument;
import com.samsung.magicinfo.schemas.schedule_3_0.EffectType;
import com.samsung.magicinfo.schemas.schedule_3_0.FileDocument;
import com.samsung.magicinfo.schemas.schedule_3_0.InputSourceTypeDocument;
import com.samsung.magicinfo.schemas.schedule_3_0.ProgramDocument;
import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.OpenOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Document;

public class ScheduleFileManager3_0 implements ScheduleFileInterface {
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   static boolean amsEnable;

   public ScheduleFileManager3_0() {
      super();
   }

   public ProgramFile createProgram(String programId, SqlSession sqlSession) throws ScheduleException {
      return this.createProgram(programId, sqlSession);
   }

   long getProgramVersionFromDisk(String programId) {
      DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();

      try {
         String schedule_home = CommonConfig.get("SCHEDULE_HOME");
         schedule_home = schedule_home.replace('/', File.separatorChar);
         String sch_dir = schedule_home + File.separator + programId + ".sch";
         File inputFile = new File(sch_dir);
         DocumentBuilder builder = factory.newDocumentBuilder();
         Document programFile = builder.parse(inputFile);
         return Long.parseLong(programFile.getElementsByTagName("version").item(0).getTextContent());
      } catch (Exception var8) {
         this.logger.error("Cannot Read program file from disk " + programId);
         this.logger.error(var8.getMessage());
         return -1L;
      }
   }

   public ProgramFile createProgram(String programId) throws ScheduleException {
      ProgramFile programFile = new ProgramFile();
      ArrayList contentIds = new ArrayList();

      try {
         ContentInfo contentInfo = ContentInfoImpl.getInstance();
         ScheduleInfoDAO dao = new ScheduleInfoDAO();
         ProgramEntity pEntity = dao.getProgram(programId);
         ProgramDocument programDoc = ProgramDocument.Factory.newInstance();
         ProgramDocument.Program program = programDoc.addNewProgram();
         program.setProgramId(pEntity.getProgram_id());
         program.setProgramName(pEntity.getProgram_name());
         program.setVersion(pEntity.getVersion());
         String syncVal = pEntity.getSynchronization();

         try {
            if (syncVal != null && !syncVal.equals("")) {
               program.setSynchronization(syncVal);
            } else {
               program.setSynchronization("0");
            }
         } catch (Exception var62) {
            program.setSynchronization("0");
         }

         try {
            if (StringUtils.isBlank(pEntity.getResume())) {
               program.setResume("0");
            } else {
               program.setResume(pEntity.getResume());
            }
         } catch (Exception var61) {
            program.setResume("0");
         }

         List pHwControlScheduleList;
         if (pEntity.getBgm_content_id() != null && !pEntity.getBgm_content_id().equals("")) {
            ProgramDocument.Program.BackgroundMusic bgMusic = program.addNewBackgroundMusic();
            bgMusic.setWithContent(pEntity.getIs_bgm_with_content().equals("Y"));
            FileDocument.File file = bgMusic.addNewFile();
            pHwControlScheduleList = contentInfo.getActiveFileList(pEntity.getBgm_content_id());
            ContentFile cfile = (ContentFile)pHwControlScheduleList.get(0);
            file.setId(cfile.getFile_id());
            file.setName(cfile.getFile_name());
            file.setSize(cfile.getFile_size());
            file.setHash(cfile.getHash_code());
         }

         List channelList = dao.getChannels(programId);
         Iterator var68 = channelList.iterator();

         int channelNo;
         while(var68.hasNext()) {
            ChannelEntity channelEntity = (ChannelEntity)var68.next();
            ProgramDocument.Program.Channel channel = program.addNewChannel();
            channelNo = channelEntity.getChannel_no();
            channel.setChannelNo(channelEntity.getChannel_no());
            channel.setChannelName(channelEntity.getChannel_name());
            channel.setChannelDescription(channelEntity.getChannel_description());

            for(int i = 0; (long)i < pEntity.getScreen_count(); ++i) {
               ProgramDocument.Program.Channel.Screen screen = channel.addNewScreen();
               screen.setScreenIndex(i);
               List frameList = dao.getFrames(programId, channelNo, i);

               for(int j = 0; j < frameList.size(); ++j) {
                  ProgramDocument.Program.Channel.Screen.Frame frame = screen.addNewFrame();
                  FrameEntity fEntity = (FrameEntity)frameList.get(j);
                  frame.setIsMainFrame(!fEntity.getIs_main_frame().equals("N"));
                  frame.setFrameIndex(fEntity.getFrame_index());
                  frame.setFrameName(fEntity.getFrame_name());
                  frame.setVersion(fEntity.getVersion());
                  if (fEntity.getDefault_content_id() != null && !fEntity.getDefault_content_id().equals("")) {
                     ProgramDocument.Program.Channel.Screen.Frame.DefaultContent dContent = frame.addNewDefaultContent();
                     dContent.setContentId(fEntity.getDefault_content_id());
                     ContentFile cfile = contentInfo.getMainFileInfo(fEntity.getDefault_content_id());
                     Content c = contentInfo.getThumbInfoOfActiveVersion(fEntity.getDefault_content_id());
                     if (cfile != null) {
                        FileDocument.File file = dContent.addNewFile();
                        file.setId(cfile.getFile_id());
                        file.setName(cfile.getFile_name());
                        file.setSize(cfile.getFile_size());
                        file.setHash(cfile.getHash_code());
                        file.setThumbId(c.getThumb_file_id());
                     }
                  }

                  List scheduleList;
                  int k;
                  Calendar start_date2;
                  List contentList;
                  String syncTagList;
                  String contentId;
                  Calendar start_date2;
                  Calendar expired_date;
                  String str_expired_date;
                  String[] expiredStr;
                  if (pEntity.getUse_ad_schedule() != null && pEntity.getUse_ad_schedule().equals("Y")) {
                     scheduleList = dao.getAdSlotList(programId, fEntity.getFrame_id());

                     for(k = 0; k < scheduleList.size(); ++k) {
                        AdSlotEntity adSlotEntity = (AdSlotEntity)scheduleList.get(k);
                        List scheduleList = dao.getAdScheduleList(programId, adSlotEntity.getSlot_id());
                        if (scheduleList != null) {
                           Iterator var110 = scheduleList.iterator();

                           while(var110.hasNext()) {
                              AdScheduleEntity adSchedule = (AdScheduleEntity)var110.next();
                              ProgramDocument.Program.Channel.Screen.Frame.FrameSchedule fSchedule = frame.addNewFrameSchedule();
                              fSchedule.setSlot(adSlotEntity.getSlot_index());
                              fSchedule.setSlotPlayDuration(adSlotEntity.getDuration());
                              String[] startStr = adSchedule.getStart_date().split("-");
                              Calendar start_date = Calendar.getInstance();
                              start_date.clear();
                              start_date.set(1, Integer.parseInt(startStr[0]));
                              start_date.set(2, Integer.parseInt(startStr[1]) - 1);
                              start_date.set(5, Integer.parseInt(startStr[2]));
                              fSchedule.setStartDate(start_date);
                              String[] stopStr = adSchedule.getStop_date().split("-");
                              Calendar stop_date = Calendar.getInstance();
                              stop_date.clear();
                              stop_date.set(1, Integer.parseInt(stopStr[0]));
                              stop_date.set(2, Integer.parseInt(stopStr[1]) - 1);
                              stop_date.set(5, Integer.parseInt(stopStr[2]));
                              fSchedule.setStopDate(stop_date);
                              ProgramDocument.Program.Channel.Screen.Frame.FrameSchedule.Repeat repeat = fSchedule.addNewRepeat();
                              ProgramDocument.Program.Channel.Screen.Frame.FrameSchedule.Repeat.Type.Enum repeatType = ProgramDocument.Program.Channel.Screen.Frame.FrameSchedule.Repeat.Type.Enum.forString("period");
                              repeat.setType(repeatType);
                              fSchedule.setRepeat(repeat);
                              String[] timeStr = adSchedule.getStart_time().split(":");
                              Calendar start_time = Calendar.getInstance();
                              start_time.clear();
                              start_time.set(11, Integer.parseInt(timeStr[0]));
                              start_time.set(12, Integer.parseInt(timeStr[1]));
                              start_time.set(13, Integer.parseInt(timeStr[2]));
                              fSchedule.setStartTime(start_time);
                              String[] endTimeStr = adSchedule.getStop_time().split(":");
                              start_date2 = Calendar.getInstance();
                              start_date2.clear();
                              start_date2.set(11, Integer.parseInt(endTimeStr[0]));
                              start_date2.set(12, Integer.parseInt(endTimeStr[1]));
                              start_date2.set(13, Integer.parseInt(endTimeStr[2]));
                              fSchedule.setStopTime(start_date2);
                              fSchedule.setPriority(1L);
                              fSchedule.setPlayerMode("single");
                              ProgramDocument.Program.Channel.Screen.Frame.FrameSchedule.Playlist playlist = fSchedule.addNewPlaylist();
                              PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();
                              Playlist plist = pInfo.getPlaylistActiveVerInfo(adSchedule.getContent_id());
                              if (plist != null) {
                                 playlist.setPlaylistName(plist.getPlaylist_name());
                                 if (plist.getIs_shuffle().equalsIgnoreCase("Y")) {
                                    playlist.setShuffle(true);
                                 } else {
                                    playlist.setShuffle(false);
                                 }
                              }

                              contentList = pInfo.getActiveVerContentList(adSchedule.getContent_id());

                              for(int x = 0; x < contentList.size(); ++x) {
                                 ContentDocument.Content content = playlist.addNewContent();
                                 PlaylistContent pContent = (PlaylistContent)contentList.get(x);
                                 content.setContentId(pContent.getContent_id());
                                 contentIds.add(pContent.getContent_id());
                                 if (pContent.getContent_duration() != null && pContent.getContent_duration() != 0L) {
                                    contentId = pContent.getContent_duration().toString();
                                    content.setUseSlotDuration(false);
                                    content.setDuration(contentId);
                                 } else {
                                    content.setUseSlotDuration(true);
                                    content.setDuration("");
                                 }

                                 content.setContiguous(pContent.getContiguous());
                                 Calendar start_date2 = Calendar.getInstance();
                                 start_date2.clear();
                                 String[] startTimeStr2;
                                 if (pContent.getStart_date() != null) {
                                    syncTagList = DateUtils.timestamp2StringDate(pContent.getStart_date());
                                    startTimeStr2 = syncTagList.split("-");
                                    start_date2.set(1, Integer.parseInt(startTimeStr2[0]));
                                    start_date2.set(2, Integer.parseInt(startTimeStr2[1]) - 1);
                                    start_date2.set(5, Integer.parseInt(startTimeStr2[2]));
                                 }

                                 content.setStartDate(start_date2);
                                 start_date2 = Calendar.getInstance();
                                 start_date2.clear();

                                 try {
                                    if (pContent.getStart_time() != null) {
                                       startTimeStr2 = pContent.getStart_time().split(":");
                                       start_date2.set(11, Integer.parseInt(startTimeStr2[0]));
                                       start_date2.set(12, Integer.parseInt(startTimeStr2[1]));
                                       start_date2.set(13, Integer.parseInt(startTimeStr2[2]));
                                    }
                                 } catch (Exception var60) {
                                    start_date2.clear();
                                 }

                                 content.setStartTime(start_date2);
                                 expired_date = Calendar.getInstance();
                                 expired_date.clear();
                                 if (pContent.getExpired_date() != null) {
                                    str_expired_date = DateUtils.timestamp2StringDate(pContent.getExpired_date());
                                    expiredStr = str_expired_date.split("-");
                                    expired_date.set(1, Integer.parseInt(expiredStr[0]));
                                    expired_date.set(2, Integer.parseInt(expiredStr[1]) - 1);
                                    expired_date.set(5, Integer.parseInt(expiredStr[2]));
                                 }

                                 content.setExpiredDate(expired_date);
                                 Calendar expired_time = Calendar.getInstance();
                                 expired_time.clear();

                                 try {
                                    if (pContent.getExpired_time() != null) {
                                       expiredStr = pContent.getExpired_time().split(":");
                                       expired_time.set(11, Integer.parseInt(expiredStr[0]));
                                       expired_time.set(12, Integer.parseInt(expiredStr[1]));
                                       expired_time.set(13, Integer.parseInt(expiredStr[2]));
                                    }
                                 } catch (Exception var59) {
                                    expired_time.clear();
                                 }

                                 content.setExpiredTime(expired_time);
                                 ContentDocument.Content.Repeat playlistRepeat = content.addNewRepeat();
                                 ContentDocument.Content.Repeat.Type.Enum playlistRepeatType = ContentDocument.Content.Repeat.Type.Enum.forString("day_of_week");
                                 playlistRepeat.setType(playlistRepeatType);
                                 if (pContent.getRepeat_type() != null && !pContent.getRepeat_type().equals("")) {
                                    playlistRepeat.setWeekday(pContent.getRepeat_type());
                                 } else {
                                    playlistRepeat.setWeekday("Sun,Mon,Tue,Wed,Thu,Fri,Sat");
                                 }

                                 content.setRepeat(playlistRepeat);
                                 content.setWeight(pContent.getPlay_weight());
                                 ContentFile cfile = contentInfo.getSfiFileInfo(pContent.getContent_id());
                                 if (cfile == null) {
                                    cfile = contentInfo.getMainFileInfo(pContent.getContent_id());
                                 }

                                 Content c = contentInfo.getThumbInfoOfActiveVersion(pContent.getContent_id());
                                 if (cfile != null) {
                                    FileDocument.File file = content.addNewFile();
                                    file.setId(cfile.getFile_id());
                                    file.setName(cfile.getFile_name());
                                    file.setSize(cfile.getFile_size());
                                    file.setHash(cfile.getHash_code());
                                    file.setThumbId(c.getThumb_file_id());
                                 }

                                 ContentDocument.Content.Taglist tagList = content.addNewTaglist();
                                 List tagListMap = null;
                                 TagInfoImpl tagDao = TagInfoImpl.getInstance();
                                 if (plist != null) {
                                    tagListMap = pInfo.getContentTag(plist.getPlaylist_id(), plist.getVersion_id(), pContent.getContent_id(), pContent.getContent_order().intValue());
                                 }

                                 if (tagList != null && tagListMap != null && tagListMap.size() > 0) {
                                    for(int tagIdx = 0; tagIdx < tagListMap.size(); ++tagIdx) {
                                       int tagId = Integer.parseInt(((Map)tagListMap.get(tagIdx)).get("tag_id").toString());
                                       TagEntity tagEntity = tagDao.getTag(tagId);
                                       if (tagEntity != null && tagEntity.getTag_value() != null) {
                                          tagList.addTag(tagEntity.getTag_value());
                                       }

                                       tagList.setMatchType(ContentDocument.Content.Taglist.MatchType.Enum.forString(((Map)tagListMap.get(tagIdx)).get("match_type").toString()));
                                    }
                                 }

                                 content.setTaglist(tagList);
                              }
                           }
                        }
                     }
                  } else {
                     scheduleList = dao.getContentSchedules(programId, channelNo, i, fEntity.getFrame_index());

                     label1044:
                     for(k = 0; k < scheduleList.size(); ++k) {
                        ProgramDocument.Program.Channel.Screen.Frame.FrameSchedule fSchedule = frame.addNewFrameSchedule();
                        ContentsScheduleEntity sEntity = (ContentsScheduleEntity)scheduleList.get(k);
                        String[] startStr = sEntity.getStart_date().split("-");
                        Calendar start_date = Calendar.getInstance();
                        start_date.clear();
                        start_date.set(1, Integer.parseInt(startStr[0]));
                        start_date.set(2, Integer.parseInt(startStr[1]) - 1);
                        start_date.set(5, Integer.parseInt(startStr[2]));
                        fSchedule.setStartDate(start_date);
                        fSchedule.setPlayerMode(sEntity.getPlayer_mode());
                        fSchedule.setSafetyLock(sEntity.getSafetyLock());
                        String[] timeStr;
                        Calendar start_time;
                        if (sEntity.getStop_date() != null && !sEntity.getStop_date().equals("")) {
                           timeStr = sEntity.getStop_date().split("-");
                           start_time = Calendar.getInstance();
                           start_time.clear();
                           start_time.set(1, Integer.parseInt(timeStr[0]));
                           start_time.set(2, Integer.parseInt(timeStr[1]) - 1);
                           start_time.set(5, Integer.parseInt(timeStr[2]));
                           fSchedule.setStopDate(start_time);
                        }

                        ProgramDocument.Program.Channel.Screen.Frame.FrameSchedule.Repeat repeat;
                        ProgramDocument.Program.Channel.Screen.Frame.FrameSchedule.Repeat.Type.Enum repeatType;
                        if (sEntity.getRepeat_type() != null && sEntity.getRepeat_type().equals("daily")) {
                           repeat = fSchedule.addNewRepeat();
                           repeatType = ProgramDocument.Program.Channel.Screen.Frame.FrameSchedule.Repeat.Type.Enum.forString("daily");
                           repeat.setType(repeatType);
                        } else if (sEntity.getRepeat_type() != null && sEntity.getRepeat_type().equals("day_of_week")) {
                           repeat = fSchedule.addNewRepeat();
                           repeatType = ProgramDocument.Program.Channel.Screen.Frame.FrameSchedule.Repeat.Type.Enum.forString("day_of_week");
                           repeat.setType(repeatType);
                           repeat.setWeekday(sEntity.getWeekdays());
                        } else if (sEntity.getRepeat_type() != null && sEntity.getRepeat_type().equals("day_of_month")) {
                           repeat = fSchedule.addNewRepeat();
                           repeatType = ProgramDocument.Program.Channel.Screen.Frame.FrameSchedule.Repeat.Type.Enum.forString("day_of_month");
                           repeat.setType(repeatType);
                           repeat.setMonthday(sEntity.getMonthdays());
                        }

                        timeStr = sEntity.getStart_time().split(":");
                        start_time = Calendar.getInstance();
                        start_time.clear();
                        start_time.set(11, Integer.parseInt(timeStr[0]));
                        start_time.set(12, Integer.parseInt(timeStr[1]));
                        start_time.set(13, Integer.parseInt(timeStr[2]));
                        fSchedule.setStartTime(start_time);
                        fSchedule.setDuration(sEntity.getDuration());
                        fSchedule.setPriority(sEntity.getPriority());
                        if (sEntity.getContent_type() != null && sEntity.getContent_type().equals("HW_IS")) {
                           ProgramDocument.Program.Channel.Screen.Frame.FrameSchedule.InputSourceControl inputSource = fSchedule.addNewInputSourceControl();
                           String hw_input_source = ScheduleUtility.getInputSourceByContentId(sEntity.getContent_id());
                           if (hw_input_source.equalsIgnoreCase("PANELOFF")) {
                              inputSource.addNewPaneloff();
                           } else {
                              InputSourceTypeDocument.InputSourceType.Type.Enum inputType = InputSourceTypeDocument.InputSourceType.Type.Enum.forString(hw_input_source);
                              inputSource.addNewInputSourceType().setType(inputType);
                           }
                        } else {
                           PlaylistInfo pInfo;
                           if (sEntity.getContent_type() != null && !sEntity.getContent_type().equals("PLAYLIST")) {
                              ContentDocument.Content content = fSchedule.addNewContent();
                              content.setContentId(sEntity.getContent_id());
                              contentIds.add(sEntity.getContent_id());
                              content.setIsStreaming(sEntity.getIs_streaming().equals("Y"));
                              content.setDuration(Integer.toString(sEntity.getDuration()));
                              content.setSlideTransitionTime(sEntity.getSlide_transition_time() * 1000);
                              pInfo = null;
                              ContentFile cfile;
                              if (sEntity.getContent_type().equalsIgnoreCase("RULESET")) {
                                 cfile = contentInfo.getRulesetFileInfo(sEntity.getContent_id());
                              } else {
                                 cfile = contentInfo.getSfiFileInfo(sEntity.getContent_id());
                                 if (cfile == null) {
                                    cfile = contentInfo.getMainFileInfo(sEntity.getContent_id());
                                 }
                              }

                              Content c = contentInfo.getThumbInfoOfActiveVersion(sEntity.getContent_id());
                              if (cfile != null) {
                                 FileDocument.File file = content.addNewFile();
                                 file.setId(cfile.getFile_id());
                                 file.setName(cfile.getFile_name());
                                 file.setSize(cfile.getFile_size());
                                 file.setHash(cfile.getHash_code());
                                 if (c != null && c.getThumb_file_id() != null) {
                                    file.setThumbId(c.getThumb_file_id());
                                 }
                              }

                              if (sEntity.getRepeat_time() != 0) {
                                 content.setRepeatTime(sEntity.getRepeat_time());
                              }

                              EffectType outEffect;
                              if (sEntity.getIn_effect_type() != null && !sEntity.getIn_effect_type().equals("")) {
                                 outEffect = content.addNewEffectIn();
                                 outEffect.setName(sEntity.getIn_effect_type());
                                 if (sEntity.getIn_effect_direction() != null && !sEntity.getIn_effect_direction().equals("")) {
                                    outEffect.setDirection(sEntity.getIn_effect_direction());
                                 } else {
                                    outEffect.setDirection("-1");
                                 }

                                 outEffect.setDuration(sEntity.getIn_effect_duration());
                              }

                              if (sEntity.getOut_effect_type() != null && !sEntity.getOut_effect_type().equals("")) {
                                 outEffect = content.addNewEffectOut();
                                 outEffect.setName(sEntity.getOut_effect_type());
                                 if (sEntity.getOut_effect_direction() != null && !sEntity.getOut_effect_direction().equals("")) {
                                    outEffect.setDirection(sEntity.getOut_effect_direction());
                                 } else {
                                    outEffect.setDirection("-1");
                                 }

                                 outEffect.setDuration(sEntity.getOut_effect_duration());
                              }
                           } else if (sEntity.getContent_type() != null && sEntity.getContent_type().equals("PLAYLIST")) {
                              ProgramDocument.Program.Channel.Screen.Frame.FrameSchedule.Playlist playlist = fSchedule.addNewPlaylist();
                              pInfo = PlaylistInfoImpl.getInstance();
                              Playlist plist = pInfo.getPlaylistActiveVerInfo(sEntity.getContent_id());
                              List syncTagList;
                              if (plist != null) {
                                 playlist.setPlaylistName(plist.getPlaylist_name());
                                 if (plist.getIs_shuffle().equalsIgnoreCase("Y")) {
                                    playlist.setShuffle(true);
                                 } else {
                                    playlist.setShuffle(false);
                                 }

                                 if (plist.getPlaylist_type().equals("1")) {
                                    AmsModeDocument.AmsMode amsmode = playlist.addNewAmsMode();
                                    amsmode.setAmsRecog(plist.getAms_mode());
                                    amsmode.setAmsDirect(plist.getAms_direct_play());
                                 } else if (plist.getPlaylist_type().equals("3")) {
                                    syncTagList = dao.getTagListWithIsSync(sEntity.getSchedule_id(), plist.getPlaylist_id(), plist.getVersion_id());
                                    String str = "";
                                    if (syncTagList != null) {
                                       TagInfoImpl tagDao = TagInfoImpl.getInstance();

                                       for(int idx = 0; idx < syncTagList.size(); ++idx) {
                                          if (idx > 0) {
                                             str = str + ",";
                                          }

                                          int tmptagId = (Integer)syncTagList.get(idx);
                                          TagEntity tagEntity = tagDao.getTag(tmptagId);
                                          if (tagEntity != null && tagEntity.getTag_value() != null) {
                                             str = str + tagEntity.getTag_value();
                                          }
                                       }
                                    }

                                    playlist.setSyncplay(str);
                                 }
                              }

                              FileDocument.File file;
                              List tagListMap;
                              List subContentList;
                              Content c;
                              if (plist != null && plist.getPlaylist_type() != null && plist.getPlaylist_type().equals("5")) {
                                 String playlistId = plist.getPlaylist_id();
                                 long versionId = plist.getVersion_id();
                                 subContentList = pInfo.getTagList(playlistId, versionId);
                                 boolean ignoreTag = false;
                                 boolean evennessPlayBack = false;
                                 int evennessPlayBackMax = 0;
                                 if (plist.getIgnore_tag() > 0) {
                                    ignoreTag = true;
                                 }

                                 PlaylistContent playlistContent;
                                 Iterator var166;
                                 int index;
                                 if (plist.getEvenness_playback() > 0) {
                                    evennessPlayBack = true;
                                    var166 = subContentList.iterator();

                                    while(var166.hasNext()) {
                                       playlistContent = (PlaylistContent)var166.next();
                                       int contentSize = false;
                                       if (playlistContent.getTag_type() == 1L) {
                                          index = pInfo.getTagConditionWithTagIdListSize(playlistId, versionId, playlistContent.getTag_id(), playlistContent.getNumber_str());
                                       } else {
                                          index = pInfo.getTagConditionWithTagIdListSize(playlistId, versionId, playlistContent.getTag_id());
                                       }

                                       if (evennessPlayBackMax <= index) {
                                          evennessPlayBackMax = index;
                                       }
                                    }
                                 }

                                 ContentDocument.Content content;
                                 String str_start_date;
                                 String[] startStr2;
                                 ContentFile cfile;
                                 Content c;
                                 FileDocument.File file;
                                 ContentDocument.Content.Taglist playlisttagList;
                                 if (evennessPlayBack) {
                                    ArrayList evennessPlayBackList = new ArrayList();

                                    PlaylistContent playlistContent;
                                    for(Iterator var161 = subContentList.iterator(); var161.hasNext(); evennessPlayBackList.add(playlistContent)) {
                                       playlistContent = (PlaylistContent)var161.next();
                                       tagListMap = null;
                                       if (playlistContent.getTag_type() == 1L) {
                                          tagListMap = pInfo.getTagConditionWithTagIdList(playlistId, versionId, playlistContent.getTag_id(), playlistContent.getNumber_str());
                                       } else {
                                          tagListMap = pInfo.getTagConditionWithTagIdList(playlistId, versionId, playlistContent.getTag_id());
                                       }

                                       if (tagListMap != null) {
                                          List newContentList = new ArrayList();
                                          int contnetIndex = 0;
                                          Iterator it = tagListMap.iterator();

                                          label871:
                                          while(true) {
                                             while(true) {
                                                do {
                                                   if (!it.hasNext()) {
                                                      break label871;
                                                   }

                                                   Content contentTemp = (Content)it.next();
                                                   newContentList.add(contentTemp);
                                                   ++contnetIndex;
                                                } while(!evennessPlayBack);

                                                if (!it.hasNext() && contnetIndex < evennessPlayBackMax) {
                                                   it = tagListMap.iterator();
                                                } else if (contnetIndex > evennessPlayBackMax - 1) {
                                                   break label871;
                                                }
                                             }
                                          }

                                          playlistContent.setEvennessPlaybackContentList(newContentList);
                                       }
                                    }

                                    if (evennessPlayBackList != null && evennessPlayBackList.size() > 0) {
                                       for(int evennessPlayBackIndex = 0; evennessPlayBackIndex < evennessPlayBackMax; ++evennessPlayBackIndex) {
                                          for(index = 0; index < evennessPlayBackList.size(); ++index) {
                                             PlaylistContent evennessPlay = (PlaylistContent)evennessPlayBackList.get(index);
                                             if (evennessPlay.getEvennessPlaybackContentList() != null && evennessPlay.getEvennessPlaybackContentList().size() > 0 && evennessPlay.getEvennessPlaybackContentList().size() >= evennessPlayBackIndex) {
                                                c = (Content)evennessPlay.getEvennessPlaybackContentList().get(evennessPlayBackIndex);
                                                content = playlist.addNewContent();
                                                contentId = c.getContent_id();
                                                content.setContentId(contentId);
                                                contentIds.add(contentId);
                                                if (c.getPlay_time() != null && !c.getPlay_time().equals("")) {
                                                   syncTagList = String.valueOf(ContentUtils.getPlayTimeStr(c.getPlay_time()));
                                                   if (c.getPlay_time_milli() != null && !c.getPlay_time_milli().equals("")) {
                                                      syncTagList = syncTagList + "." + c.getPlay_time_milli();
                                                   }

                                                   content.setDuration(syncTagList);
                                                } else {
                                                   content.setDuration(String.valueOf(evennessPlay.getTag_duration()));
                                                }

                                                start_date2 = Calendar.getInstance();
                                                start_date2.clear();
                                                if (evennessPlay.getStart_date() != null) {
                                                   str_start_date = DateUtils.timestamp2StringDate(evennessPlay.getStart_date());
                                                   startStr2 = str_start_date.split("-");
                                                   start_date2.set(1, Integer.parseInt(startStr2[0]));
                                                   start_date2.set(2, Integer.parseInt(startStr2[1]) - 1);
                                                   start_date2.set(5, Integer.parseInt(startStr2[2]));
                                                }

                                                content.setStartDate(start_date2);
                                                expired_date = Calendar.getInstance();
                                                expired_date.clear();
                                                if (evennessPlay.getExpired_date() != null) {
                                                   str_expired_date = DateUtils.timestamp2StringDate(evennessPlay.getExpired_date());
                                                   expiredStr = str_expired_date.split("-");
                                                   expired_date.set(1, Integer.parseInt(expiredStr[0]));
                                                   expired_date.set(2, Integer.parseInt(expiredStr[1]) - 1);
                                                   expired_date.set(5, Integer.parseInt(expiredStr[2]));
                                                }

                                                content.setExpiredDate(expired_date);
                                                cfile = contentInfo.getSfiFileInfo(contentId);
                                                if (cfile == null) {
                                                   cfile = contentInfo.getMainFileInfo(contentId);
                                                }

                                                c = contentInfo.getThumbInfoOfActiveVersion(contentId);
                                                if (cfile != null) {
                                                   file = content.addNewFile();
                                                   file.setId(cfile.getFile_id());
                                                   file.setName(cfile.getFile_name());
                                                   file.setSize(cfile.getFile_size());
                                                   file.setHash(cfile.getHash_code());
                                                   file.setThumbId(c.getThumb_file_id());
                                                }

                                                if (!ignoreTag) {
                                                   playlisttagList = content.addNewTaglist();
                                                   playlisttagList.addTag(evennessPlay.getTag_name());
                                                   playlisttagList.setMatchType(ContentDocument.Content.Taglist.MatchType.Enum.forString("or"));
                                                }
                                             }
                                          }
                                       }
                                    }
                                 } else {
                                    var166 = subContentList.iterator();

                                    while(true) {
                                       do {
                                          if (!var166.hasNext()) {
                                             continue label1044;
                                          }

                                          playlistContent = (PlaylistContent)var166.next();
                                          file = null;
                                          if (playlistContent.getTag_type() == 1L) {
                                             contentList = pInfo.getTagConditionWithTagIdList(playlistId, versionId, playlistContent.getTag_id(), playlistContent.getNumber_str());
                                          } else {
                                             contentList = pInfo.getTagConditionWithTagIdList(playlistId, versionId, playlistContent.getTag_id());
                                          }
                                       } while(contentList == null);

                                       Iterator var184 = contentList.iterator();

                                       while(var184.hasNext()) {
                                          c = (Content)var184.next();
                                          content = playlist.addNewContent();
                                          contentId = c.getContent_id();
                                          content.setContentId(contentId);
                                          contentIds.add(contentId);
                                          if (c.getPlay_time() != null && !c.getPlay_time().equals("")) {
                                             syncTagList = String.valueOf(ContentUtils.getPlayTimeStr(c.getPlay_time()));
                                             if (c.getPlay_time_milli() != null && !c.getPlay_time_milli().equals("")) {
                                                syncTagList = syncTagList + "." + c.getPlay_time_milli();
                                             }

                                             content.setDuration(syncTagList);
                                          } else {
                                             content.setDuration(String.valueOf(playlistContent.getTag_duration()));
                                          }

                                          start_date2 = Calendar.getInstance();
                                          start_date2.clear();
                                          if (playlistContent.getStart_date() != null) {
                                             str_start_date = DateUtils.timestamp2StringDate(playlistContent.getStart_date());
                                             startStr2 = str_start_date.split("-");
                                             start_date2.set(1, Integer.parseInt(startStr2[0]));
                                             start_date2.set(2, Integer.parseInt(startStr2[1]) - 1);
                                             start_date2.set(5, Integer.parseInt(startStr2[2]));
                                          }

                                          content.setStartDate(start_date2);
                                          expired_date = Calendar.getInstance();
                                          expired_date.clear();
                                          if (playlistContent.getExpired_date() != null) {
                                             str_expired_date = DateUtils.timestamp2StringDate(playlistContent.getExpired_date());
                                             expiredStr = str_expired_date.split("-");
                                             expired_date.set(1, Integer.parseInt(expiredStr[0]));
                                             expired_date.set(2, Integer.parseInt(expiredStr[1]) - 1);
                                             expired_date.set(5, Integer.parseInt(expiredStr[2]));
                                          }

                                          content.setExpiredDate(expired_date);
                                          cfile = contentInfo.getSfiFileInfo(contentId);
                                          if (cfile == null) {
                                             cfile = contentInfo.getMainFileInfo(contentId);
                                          }

                                          c = contentInfo.getThumbInfoOfActiveVersion(contentId);
                                          if (cfile != null) {
                                             file = content.addNewFile();
                                             file.setId(cfile.getFile_id());
                                             file.setName(cfile.getFile_name());
                                             file.setSize(cfile.getFile_size());
                                             file.setHash(cfile.getHash_code());
                                             file.setThumbId(c.getThumb_file_id());
                                          }

                                          if (!ignoreTag) {
                                             playlisttagList = content.addNewTaglist();
                                             playlisttagList.addTag(playlistContent.getTag_name());
                                             playlisttagList.setMatchType(ContentDocument.Content.Taglist.MatchType.Enum.forString("or"));
                                          }
                                       }
                                    }
                                 }
                              } else {
                                 syncTagList = pInfo.getActiveVerContentList(sEntity.getContent_id());

                                 for(int x = 0; x < syncTagList.size(); ++x) {
                                    PlaylistContent pContent = (PlaylistContent)syncTagList.get(x);
                                    String ageString;
                                    if (Boolean.TRUE.equals(pContent.getIs_sub_playlist())) {
                                       subContentList = pInfo.getActiveVerContentList(pContent.getContent_id());
                                       if (subContentList != null && subContentList.size() > 0) {
                                          Iterator var145 = subContentList.iterator();

                                          while(var145.hasNext()) {
                                             PlaylistContent subContent = (PlaylistContent)var145.next();
                                             ContentDocument.Content content = playlist.addNewContent();
                                             content.setContentId(subContent.getContent_id());
                                             contentIds.add(subContent.getContent_id());
                                             ageString = subContent.getContent_duration().toString();
                                             if (subContent.getContent_duration_milli() != null && !subContent.getContent_duration_milli().equals("")) {
                                                ageString = ageString + "." + subContent.getContent_duration_milli();
                                             }

                                             content.setDuration(ageString);
                                             Calendar start_date2 = Calendar.getInstance();
                                             start_date2.clear();
                                             if (subContent.getStart_date() != null) {
                                                String str_start_date = DateUtils.timestamp2StringDate(subContent.getStart_date());
                                                String[] startStr2 = str_start_date.split("-");
                                                start_date2.set(1, Integer.parseInt(startStr2[0]));
                                                start_date2.set(2, Integer.parseInt(startStr2[1]) - 1);
                                                start_date2.set(5, Integer.parseInt(startStr2[2]));
                                             }

                                             content.setStartDate(start_date2);
                                             Calendar expired_date = Calendar.getInstance();
                                             expired_date.clear();
                                             String ageString;
                                             if (subContent.getExpired_date() != null) {
                                                ageString = DateUtils.timestamp2StringDate(subContent.getExpired_date());
                                                String[] expiredStr = ageString.split("-");
                                                expired_date.set(1, Integer.parseInt(expiredStr[0]));
                                                expired_date.set(2, Integer.parseInt(expiredStr[1]) - 1);
                                                expired_date.set(5, Integer.parseInt(expiredStr[2]));
                                             }

                                             content.setExpiredDate(expired_date);
                                             if (amsEnable) {
                                                ageString = subContent.getAge();
                                                if (ageString != null) {
                                                   ageString = subContent.getAge().replace("_", ", ");
                                                   ageString = ageString.trim();
                                                   content.setAge(ageString);
                                                } else {
                                                   content.setAge("");
                                                }
                                             }

                                             ContentFile cfile = contentInfo.getSfiFileInfo(subContent.getContent_id());
                                             if (cfile == null) {
                                                cfile = contentInfo.getMainFileInfo(subContent.getContent_id());
                                             }

                                             c = contentInfo.getThumbInfoOfActiveVersion(subContent.getContent_id());
                                             if (cfile != null) {
                                                FileDocument.File file = content.addNewFile();
                                                file.setId(cfile.getFile_id());
                                                file.setName(cfile.getFile_name());
                                                file.setSize(cfile.getFile_size());
                                                file.setHash(cfile.getHash_code());
                                                file.setThumbId(c.getThumb_file_id());
                                             }

                                             EffectType outEffect;
                                             if (subContent.getEffect_in_name() != null && !subContent.getEffect_in_name().equals("")) {
                                                outEffect = content.addNewEffectIn();
                                                outEffect.setName(subContent.getEffect_in_name());
                                                if (subContent.getEffect_in_direction() != null && !subContent.getEffect_in_direction().equals("")) {
                                                   outEffect.setDirection(subContent.getEffect_in_direction());
                                                } else {
                                                   outEffect.setDirection("-1");
                                                }

                                                outEffect.setDuration(subContent.getEffect_in_duration().intValue());
                                                if (sEntity.getPlayer_mode().equals("vwl")) {
                                                   outEffect.setDelayDirection(subContent.getEffect_in_delay_direction());
                                                   outEffect.setDelayDuration(subContent.getEffect_in_delay_duration().intValue());
                                                   outEffect.setDivision(subContent.getEffect_in_delay_div().intValue());
                                                }
                                             }

                                             if (subContent.getEffect_out_name() != null && !subContent.getEffect_out_name().equals("")) {
                                                outEffect = content.addNewEffectOut();
                                                outEffect.setName(subContent.getEffect_out_name());
                                                if (subContent.getEffect_out_direction() != null && !subContent.getEffect_out_direction().equals("")) {
                                                   outEffect.setDirection(subContent.getEffect_out_direction());
                                                } else {
                                                   outEffect.setDirection("-1");
                                                }

                                                outEffect.setDuration(subContent.getEffect_out_duration().intValue());
                                                if (sEntity.getPlayer_mode().equals("vwl")) {
                                                   outEffect.setDelayDirection(subContent.getEffect_out_delay_direction());
                                                   outEffect.setDelayDuration(subContent.getEffect_out_delay_duration().intValue());
                                                   outEffect.setDivision(subContent.getEffect_out_delay_div().intValue());
                                                }
                                             }

                                             ContentDocument.Content.Taglist tagList = content.addNewTaglist();
                                             List tagListMap = null;
                                             syncTagList = null;
                                             TagInfoImpl tagDao = TagInfoImpl.getInstance();
                                             if (pContent != null) {
                                                tagListMap = pInfo.getContentTag(subContent.getPlaylist_id(), subContent.getVersion_id(), subContent.getContent_id(), subContent.getContent_order().intValue());
                                             }

                                             int randomCnt;
                                             if (tagList != null && tagListMap != null && tagListMap.size() > 0) {
                                                for(randomCnt = 0; randomCnt < tagListMap.size(); ++randomCnt) {
                                                   int tagId = Integer.parseInt(((Map)tagListMap.get(randomCnt)).get("tag_id").toString());
                                                   TagEntity tagEntity = tagDao.getTag(tagId);
                                                   if (tagEntity != null && tagEntity.getTag_value() != null) {
                                                      tagList.addTag(tagEntity.getTag_value());
                                                   }

                                                   tagList.setMatchType(ContentDocument.Content.Taglist.MatchType.Enum.forString(((Map)tagListMap.get(randomCnt)).get("match_type").toString()));
                                                }
                                             }

                                             content.setTaglist(tagList);
                                             if (plist.getPlaylist_type().equals("0")) {
                                                randomCnt = subContent.getRandom_count();
                                                if (randomCnt > 0) {
                                                   content.setRandomPlay(randomCnt);
                                                }
                                             }
                                          }
                                       }
                                    } else {
                                       ContentDocument.Content content = playlist.addNewContent();
                                       content.setContentId(pContent.getContent_id());
                                       contentIds.add(pContent.getContent_id());
                                       String duration = pContent.getContent_duration().toString();
                                       if (pContent.getContent_duration_milli() != null && !pContent.getContent_duration_milli().equals("")) {
                                          duration = duration + "." + pContent.getContent_duration_milli();
                                       }

                                       content.setDuration(duration);
                                       start_date2 = Calendar.getInstance();
                                       start_date2.clear();
                                       if (pContent.getStart_date() != null) {
                                          String str_start_date = DateUtils.timestamp2StringDate(pContent.getStart_date());
                                          String[] startStr2 = str_start_date.split("-");
                                          start_date2.set(1, Integer.parseInt(startStr2[0]));
                                          start_date2.set(2, Integer.parseInt(startStr2[1]) - 1);
                                          start_date2.set(5, Integer.parseInt(startStr2[2]));
                                       }

                                       content.setStartDate(start_date2);
                                       Calendar expired_date = Calendar.getInstance();
                                       expired_date.clear();
                                       if (pContent.getExpired_date() != null) {
                                          ageString = DateUtils.timestamp2StringDate(pContent.getExpired_date());
                                          String[] expiredStr = ageString.split("-");
                                          expired_date.set(1, Integer.parseInt(expiredStr[0]));
                                          expired_date.set(2, Integer.parseInt(expiredStr[1]) - 1);
                                          expired_date.set(5, Integer.parseInt(expiredStr[2]));
                                       }

                                       content.setExpiredDate(expired_date);
                                       if (amsEnable) {
                                          ageString = pContent.getAge();
                                          if (ageString != null) {
                                             ageString = pContent.getAge().replace("_", ", ");
                                             ageString = ageString.trim();
                                             content.setAge(ageString);
                                          } else {
                                             content.setAge("");
                                          }
                                       }

                                       ContentFile cfile = contentInfo.getSfiFileInfo(pContent.getContent_id());
                                       if (cfile == null) {
                                          cfile = contentInfo.getMainFileInfo(pContent.getContent_id());
                                       }

                                       Content c = contentInfo.getThumbInfoOfActiveVersion(pContent.getContent_id());
                                       if (cfile != null) {
                                          file = content.addNewFile();
                                          file.setId(cfile.getFile_id());
                                          file.setName(cfile.getFile_name());
                                          file.setSize(cfile.getFile_size());
                                          file.setHash(cfile.getHash_code());
                                          file.setThumbId(c.getThumb_file_id());
                                       }

                                       EffectType outEffect;
                                       if (pContent.getEffect_in_name() != null && !pContent.getEffect_in_name().equals("")) {
                                          outEffect = content.addNewEffectIn();
                                          outEffect.setName(pContent.getEffect_in_name());
                                          if (pContent.getEffect_in_direction() != null && !pContent.getEffect_in_direction().equals("")) {
                                             outEffect.setDirection(pContent.getEffect_in_direction());
                                          } else {
                                             outEffect.setDirection("-1");
                                          }

                                          outEffect.setDuration(pContent.getEffect_in_duration().intValue());
                                          if (sEntity.getPlayer_mode().equals("vwl")) {
                                             outEffect.setDelayDirection(pContent.getEffect_in_delay_direction());
                                             outEffect.setDelayDuration(pContent.getEffect_in_delay_duration().intValue());
                                             outEffect.setDivision(pContent.getEffect_in_delay_div().intValue());
                                          }
                                       }

                                       if (pContent.getEffect_out_name() != null && !pContent.getEffect_out_name().equals("")) {
                                          outEffect = content.addNewEffectOut();
                                          outEffect.setName(pContent.getEffect_out_name());
                                          if (pContent.getEffect_out_direction() != null && !pContent.getEffect_out_direction().equals("")) {
                                             outEffect.setDirection(pContent.getEffect_out_direction());
                                          } else {
                                             outEffect.setDirection("-1");
                                          }

                                          outEffect.setDuration(pContent.getEffect_out_duration().intValue());
                                          if (sEntity.getPlayer_mode().equals("vwl")) {
                                             outEffect.setDelayDirection(pContent.getEffect_out_delay_direction());
                                             outEffect.setDelayDuration(pContent.getEffect_out_delay_duration().intValue());
                                             outEffect.setDivision(pContent.getEffect_out_delay_div().intValue());
                                          }
                                       }

                                       ContentDocument.Content.Taglist tagList = content.addNewTaglist();
                                       tagListMap = null;
                                       List syncTagList = null;
                                       TagInfoImpl tagDao = TagInfoImpl.getInstance();
                                       int tagIdx;
                                       int tagId;
                                       TagEntity tagEntity;
                                       if (plist != null && plist.getPlaylist_type().equals("3")) {
                                          syncTagList = dao.getTagListForContent(sEntity.getSchedule_id(), plist.getPlaylist_id(), pContent.getSync_play_id());
                                          if (syncTagList != null) {
                                             for(tagIdx = 0; tagIdx < syncTagList.size(); ++tagIdx) {
                                                tagId = (Integer)syncTagList.get(tagIdx);
                                                tagEntity = tagDao.getTag(tagId);
                                                if (tagEntity != null && tagEntity.getTag_value() != null) {
                                                   tagList.addTag(tagEntity.getTag_value());
                                                }

                                                tagList.setMatchType(ContentDocument.Content.Taglist.MatchType.Enum.forString("or"));
                                             }
                                          }
                                       } else {
                                          if (plist != null) {
                                             tagListMap = pInfo.getContentTag(plist.getPlaylist_id(), plist.getVersion_id(), pContent.getContent_id(), pContent.getContent_order().intValue());
                                          }

                                          if (tagList != null && tagListMap != null && tagListMap.size() > 0) {
                                             for(tagIdx = 0; tagIdx < tagListMap.size(); ++tagIdx) {
                                                tagId = Integer.parseInt(((Map)tagListMap.get(tagIdx)).get("tag_id").toString());
                                                tagEntity = tagDao.getTag(tagId);
                                                if (tagEntity != null && tagEntity.getTag_value() != null) {
                                                   tagList.addTag(tagEntity.getTag_value());
                                                }

                                                tagList.setMatchType(ContentDocument.Content.Taglist.MatchType.Enum.forString(((Map)tagListMap.get(tagIdx)).get("match_type").toString()));
                                             }
                                          }
                                       }

                                       content.setTaglist(tagList);
                                       if (plist.getPlaylist_type().equals("1")) {
                                          if (!pContent.getAms_recog_type().equals("") && !pContent.getAms_recog_type().equals("none")) {
                                             content.setTargeted("true");
                                             if (plist.getAms_mode().equals("target")) {
                                                content.setGender(pContent.getGender());
                                             }
                                          } else {
                                             content.setTargeted("false");
                                          }
                                       } else if (plist.getPlaylist_type().equals("0")) {
                                          tagIdx = pContent.getRandom_count();
                                          if (tagIdx > 0) {
                                             content.setRandomPlay(tagIdx);
                                          }
                                       }
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }

                  if (fEntity.getDefault_content_id() != null && fEntity.getLine_data().equalsIgnoreCase("CustomLayout")) {
                     String[] resol = pEntity.getResolution().split("\\*");
                     double xRange = Double.parseDouble(resol[0]);
                     double yRange = Double.parseDouble(resol[1]);
                     if (fEntity.getX() != -1.0D) {
                        frame.setX(fEntity.getX() / xRange * 100.0D);
                     }

                     if (fEntity.getY() != -1.0D) {
                        frame.setY(fEntity.getY() / yRange * 100.0D);
                     }

                     if (fEntity.getWidth() != -1.0D) {
                        frame.setWidth(fEntity.getWidth() / xRange * 100.0D);
                     }

                     if (fEntity.getHeight() != -1.0D) {
                        frame.setHeight(fEntity.getHeight() / yRange * 100.0D);
                     }
                  } else {
                     if (fEntity.getX() != -1.0D) {
                        frame.setX(fEntity.getX());
                     }

                     if (fEntity.getY() != -1.0D) {
                        frame.setY(fEntity.getY());
                     }

                     if (fEntity.getWidth() != -1.0D) {
                        frame.setWidth(fEntity.getWidth());
                     }

                     if (fEntity.getHeight() != -1.0D) {
                        frame.setHeight(fEntity.getHeight());
                     }
                  }
               }
            }
         }

         List pOffScheduleList = dao.getPanelOrZeroFrameSchedules(programId, "01");

         for(int i = 0; i < pOffScheduleList.size(); ++i) {
            ScheduleEntity sEntity = (ScheduleEntity)pOffScheduleList.get(i);
            ProgramDocument.Program.PanelOffSchedule pOffSchedule = program.addNewPanelOffSchedule();
            pOffSchedule.setWeekday(sEntity.getWeekdays());
            String[] timeStr = sEntity.getStart_time().split(":");
            Calendar start_time = Calendar.getInstance();
            start_time.clear();
            start_time.set(11, Integer.parseInt(timeStr[0]));
            start_time.set(12, Integer.parseInt(timeStr[1]));
            start_time.set(13, Integer.parseInt(timeStr[2]));
            pOffSchedule.setStartTime(start_time);
            pOffSchedule.setDuration(sEntity.getDuration());
         }

         pHwControlScheduleList = dao.getPanelOrZeroFrameSchedules(programId, "03");

         String streaming_port;
         String schedule_home;
         for(int i = 0; i < pHwControlScheduleList.size(); ++i) {
            ScheduleEntity sEntity = (ScheduleEntity)pHwControlScheduleList.get(i);
            ProgramDocument.Program.HardwareControl pHwControlSchedule = program.addNewHardwareControl();
            pHwControlSchedule.setWeekday(sEntity.getWeekdays());
            String[] timeStr = sEntity.getStart_time().split(":");
            Calendar start_time = Calendar.getInstance();
            start_time.clear();
            start_time.set(11, Integer.parseInt(timeStr[0]));
            start_time.set(12, Integer.parseInt(timeStr[1]));
            start_time.set(13, Integer.parseInt(timeStr[2]));
            pHwControlSchedule.setStartTime(start_time);
            pHwControlSchedule.setDuration(sEntity.getDuration());
            streaming_port = "";
            if (sEntity.getHw_input_source().equals("20")) {
               streaming_port = "PC";
            } else if (sEntity.getHw_input_source().equals("30")) {
               streaming_port = "BNC";
            } else if (sEntity.getHw_input_source().equals("24")) {
               streaming_port = "DVI";
            } else if (sEntity.getHw_input_source().equals("12")) {
               streaming_port = "AV";
            } else if (sEntity.getHw_input_source().equals("4")) {
               streaming_port = "S-Video";
            } else if (sEntity.getHw_input_source().equals("8")) {
               streaming_port = "Component";
            } else if (sEntity.getHw_input_source().equals("33")) {
               streaming_port = "HDMI1";
            } else if (sEntity.getHw_input_source().equals("35")) {
               streaming_port = "HDMI2";
            } else if (sEntity.getHw_input_source().equals("37")) {
               streaming_port = "DisplayPort";
            } else if (sEntity.getHw_input_source().equals("38")) {
               streaming_port = "DisplayPort2";
            } else if (sEntity.getHw_input_source().equals("48")) {
               streaming_port = "TV";
            } else if (sEntity.getHw_input_source().equals("64")) {
               streaming_port = "DTV";
            } else if (sEntity.getHw_input_source().equals("96")) {
               streaming_port = "MagicInfo-Lite";
            } else if (sEntity.getHw_input_source().equals("80")) {
               streaming_port = "PlugInModule";
            } else if (sEntity.getHw_input_source().equals("49")) {
               streaming_port = "HDMI3";
            } else if (sEntity.getHw_input_source().equals("51")) {
               streaming_port = "HDMI4";
            } else if (sEntity.getHw_input_source().equals("34")) {
               streaming_port = "HDMI1_PC";
            } else if (sEntity.getHw_input_source().equals("36")) {
               streaming_port = "HDMI2_PC";
            } else if (sEntity.getHw_input_source().equals("50")) {
               streaming_port = "HDMI3_PC";
            } else if (sEntity.getHw_input_source().equals("52")) {
               streaming_port = "HDMI4_PC";
            } else if (sEntity.getHw_input_source().equals("31")) {
               streaming_port = "DVI_VIDEO";
            } else if (sEntity.getHw_input_source().equals("13")) {
               streaming_port = "AV2";
            } else if (sEntity.getHw_input_source().equals("14")) {
               streaming_port = "Ext";
            } else if (sEntity.getHw_input_source().equals("85")) {
               streaming_port = "HDBaseT";
            } else if (sEntity.getHw_input_source().equals("97")) {
               streaming_port = "WiDi";
            } else if (sEntity.getHw_input_source().equals("101")) {
               streaming_port = "WebBrowser";
            } else if (sEntity.getHw_input_source().equals("102")) {
               streaming_port = "SamsungWorkspace";
            } else {
               streaming_port = "MagicInfo";
            }

            ProgramDocument.Program.HardwareControl.ControlItem controlItem = pHwControlSchedule.addNewControlItem();
            ProgramDocument.Program.HardwareControl.ControlItem.InputSource inputSource = controlItem.addNewInputSource();
            ProgramDocument.Program.HardwareControl.ControlItem.InputSource.Type.Enum inputType = ProgramDocument.Program.HardwareControl.ControlItem.InputSource.Type.Enum.forString(streaming_port);
            inputSource.setType(inputType);
            if (streaming_port == "TV") {
               ProgramDocument.Program.HardwareControl.ControlItem.InputSource.TV tv = inputSource.addNewTV();
               tv.setAtvDtv(false);
               if (sEntity.getHw_AtvDtv().equals("1")) {
                  tv.setAtvDtv(true);
               }

               tv.setAirCable(false);
               if (sEntity.getHw_AirCable().equals("1")) {
                  tv.setAirCable(true);
               }

               schedule_home = "0";
               if (sEntity.getHw_MajorCH() != null || sEntity.getHw_MajorCH() != "") {
                  schedule_home = sEntity.getHw_MajorCH();
               }

               tv.setMajorChannel(Integer.parseInt(schedule_home));
            }

            if (streaming_port == "DTV") {
               ProgramDocument.Program.HardwareControl.ControlItem.InputSource.DTV dtv = inputSource.addNewDTV();
               dtv.setAtvDtv(false);
               if (sEntity.getHw_AtvDtv().equals("1")) {
                  dtv.setAtvDtv(true);
               }

               dtv.setAirCable(false);
               if (sEntity.getHw_AirCable().equals("1")) {
                  dtv.setAirCable(true);
               }

               schedule_home = "0";
               if (sEntity.getHw_MajorCH() != null || sEntity.getHw_MajorCH() != "") {
                  schedule_home = sEntity.getHw_MajorCH();
               }

               dtv.setMajorChannel(Integer.parseInt(schedule_home));
               dtv.setSelMinor(false);
               if (Integer.parseInt(sEntity.getHw_MinorCH()) > 0) {
                  dtv.setSelMinor(true);
               }

               String minorCh = "0";
               if (sEntity.getHw_MinorCH() != null || sEntity.getHw_MinorCH() != "") {
                  minorCh = sEntity.getHw_MinorCH();
               }

               dtv.setMinorChannel(Integer.parseInt(minorCh));
            }

            controlItem.setVolume(Integer.parseInt(sEntity.getHw_Volume()));
         }

         List schChannelScheduleList = dao.getPanelOrZeroFrameSchedules(programId, "04");

         for(channelNo = 0; channelNo < schChannelScheduleList.size(); ++channelNo) {
            ScheduleEntity sEntity = (ScheduleEntity)schChannelScheduleList.get(channelNo);
            ProgramDocument.Program.ChangeScheduleChannelNo schChannelSchedule = program.addNewChangeScheduleChannelNo();
            schChannelSchedule.setWeekday(sEntity.getWeekdays());
            String[] timeStr = sEntity.getStart_time().split(":");
            Calendar start_time = Calendar.getInstance();
            start_time.clear();
            start_time.set(11, Integer.parseInt(timeStr[0]));
            start_time.set(12, Integer.parseInt(timeStr[1]));
            start_time.set(13, Integer.parseInt(timeStr[2]));
            schChannelSchedule.setStartTime(start_time);
            schChannelSchedule.setDuration(sEntity.getDuration());
            schChannelSchedule.setChannelNo(Integer.parseInt(sEntity.getHw_sch_ch()));
         }

         String ip_address = CommonConfig.get("download.server.ip");
         String web_port = CommonConfig.get("download.server.web.port");
         String ftp_port = CommonConfig.get("download.server.ftp.port");
         String streaming_address = CommonConfig.get("daemon_bind_addr");
         streaming_port = CommonConfig.get("daemon_bind_port");
         if (ip_address == null) {
            ip_address = InetAddress.getLocalHost().getHostAddress();
         }

         if (streaming_address == null) {
            streaming_address = InetAddress.getLocalHost().getHostAddress();
         }

         if (web_port == null) {
            web_port = "80";
         }

         if (ftp_port == null) {
            ftp_port = "21";
         }

         if (streaming_port == null) {
            streaming_port = "7000";
         }

         ProgramDocument.Program.DownloadServer downServer = program.addNewDownloadServer();
         ProgramDocument.Program.DownloadServer.Http http = downServer.addNewHttp();
         http.setUrl(ip_address);
         http.setPort(Integer.parseInt(web_port));
         ProgramDocument.Program.DownloadServer.Ftp ftp = downServer.addNewFtp();
         ftp.setUrl(ip_address);
         ftp.setPort(Integer.parseInt(ftp_port));
         ProgramDocument.Program.DownloadServer.Streaming streaming = downServer.addNewStreaming();
         streaming.setUrl(streaming_address);
         streaming.setPort(Integer.parseInt(streaming_port));
         schedule_home = CommonConfig.get("SCHEDULE_HOME");
         schedule_home = schedule_home.replace('/', File.separatorChar);
         Path scheduleHomeFile = Paths.get(schedule_home);
         if (!Files.exists(scheduleHomeFile, new LinkOption[0])) {
            Files.createDirectories(scheduleHomeFile);
         }

         String sch_dir = schedule_home + File.separator;
         Path scheduleFilePath = Paths.get(sch_dir + pEntity.getProgram_id() + ".sch");
         byte[] scheduleFileByte = (programDoc.toString() + System.lineSeparator()).getBytes(Charset.forName("UTF-8"));
         if (this.getProgramVersionFromDisk(programId) != pEntity.getVersion()) {
            Files.write(scheduleFilePath, scheduleFileByte, new OpenOption[0]);
         }

         programFile.setProgramFile(scheduleFilePath.toFile());
         programFile.setContentIds(contentIds);
      } catch (SQLException var63) {
      } catch (UnknownHostException var64) {
         var64.printStackTrace();
      } catch (IOException var65) {
         var65.printStackTrace();
      } catch (ConfigException var66) {
         var66.printStackTrace();
      }

      return programFile;
   }

   static {
      try {
         amsEnable = "TRUE".equalsIgnoreCase(CommonConfig.get("ams.enable"));
      } catch (ConfigException var1) {
         amsEnable = false;
      }

   }
}
