package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.repository.model.JobStateResponse;

public interface JobStateRepository {
  JobStateResponse jobStateSuccess(String paramString1, String paramString2, String paramString3, String paramString4, boolean paramBoolean) throws UploaderException;
  
  JobStateResponse jobStateFail(String paramString1, String paramString2, String paramString3, String paramString4) throws UploaderException;
}
