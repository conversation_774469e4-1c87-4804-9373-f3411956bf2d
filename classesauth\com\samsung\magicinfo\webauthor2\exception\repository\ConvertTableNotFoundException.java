package com.samsung.magicinfo.webauthor2.exception.repository;

public class ConvertTableNotFoundException extends RuntimeException {
  private static final long serialVersionUID = 4870200553299253457L;
  
  private String code;
  
  private String message;
  
  public ConvertTableNotFoundException(String code, String message) {
    this.code = code;
    this.message = message;
  }
  
  public String getCode() {
    return this.code;
  }
  
  public String getMessage() {
    return this.message;
  }
}
