package com.samsung.magicinfo.framework.monitoring.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManagerImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfo;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfoImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManager;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManagerImpl;
import com.samsung.magicinfo.framework.monitoring.entity.ContentList;
import com.samsung.magicinfo.framework.monitoring.entity.CurrentPlayingEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ScheduleInfoEntity;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.scheduler.entity.FrameEntity;
import com.samsung.magicinfo.framework.scheduler.entity.MessageEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleUtility;
import com.samsung.magicinfo.protocol.exception.ServiceException;
import com.samsung.magicinfo.protocol.rmql.RMQL;
import com.samsung.magicinfo.protocol.rmql.RMQLDriver;
import com.samsung.magicinfo.protocol.rmql.ResultSet;
import com.samsung.magicinfo.protocol.servicemanager.ServiceOpActivity;
import com.samsung.magicinfo.protocol.util.RMQLDriverUtil;
import com.samsung.magicinfo.protocol.util.RMQLInstanceCreator;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;

public class MonitoringServiceActivity extends ServiceOpActivity {
   private Logger logger = LoggingManagerV2.getLogger(MonitoringServiceActivity.class);
   private static List DEFAULT_CONTENT_ID_LIST = new ArrayList();
   private static final long PROGRAM = 1L;
   private static final long MESSAGE = 2L;
   private static final long EVENT = 3L;
   private static int deployScheduleRetryCount;
   private static int deployScheduleCheckTime;
   private static String schedulerType;
   private DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
   private ResultSet rs;

   public MonitoringServiceActivity() {
      super();
   }

   public static List getDefaultContentIdList() {
      return DEFAULT_CONTENT_ID_LIST;
   }

   public Object process(HashMap params) throws ServiceException {
      String eventPath;
      RMQLDriver device;
      try {
         this.rs = (ResultSet)params.get("resultset");
         String deviceId = this.rs.getAttribute("DEVICE_ID");
         eventPath = this.rs.getAttribute("MO_EVENT");
         String eventID = this.rs.getAttribute("EVENT_ID");
         if (deviceId == null) {
            throw new ServiceException("No device_sn found");
         } else if (eventPath == null) {
            throw new ServiceException("No mo_event found");
         } else if (eventID == null) {
            throw new ServiceException("No event_id found");
         } else {
            DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
            MonitoringManager mgr = MonitoringManagerImpl.getInstance();
            device = null;
            boolean supportSchedule = true;

            Device device;
            try {
               device = deviceDao.getDevice(deviceId);
               supportSchedule = DeviceUtils.isSupportPlayingSchedule(device.getDevice_type(), device.getDevice_type_version());
               this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] Recv Keepalive.");
            } catch (Exception var46) {
               this.logger.info(var46.toString());
               throw var46;
            }

            if (device.getIs_approved() && mgr.checkKeepAliveLastConnection(deviceId)) {
               if (eventPath != null && eventPath.equals(".MO.MONITORING_INFO.CURRENT_SCHEDULE")) {
                  CurrentPlayingEntity curEntity = null;
                  mgr.setConnectionNow(device);
                  curEntity = mgr.getPlayingContent(deviceId);
                  if (curEntity == null) {
                     curEntity = new CurrentPlayingEntity();
                  }

                  int inputSource = -1;
                  if (this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.INPUT_SOURCE") != null && !this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.INPUT_SOURCE").equals("")) {
                     inputSource = this.rs.getInt(".MO.MONITORING_INFO.CURRENT_SCHEDULE.INPUT_SOURCE");
                  }

                  curEntity.setInputSource(inputSource);
                  if (inputSource != curEntity.getInputSourceBefore()) {
                     curEntity.setInputSourceBefore(inputSource);
                     DeviceDisplayConf info = new DeviceDisplayConf();
                     info.setBasic_source((long)inputSource);
                     info.setDevice_id(deviceId);
                     DeviceDisplayConfManager displayDao = DeviceDisplayConfManagerImpl.getInstance();
                     displayDao.setDeviceDisplayConf(info);
                  }

                  if (inputSource != 1000) {
                     ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
                     String programId = null;
                     String programName = "";
                     long programVersion = 0L;
                     long preProgramVersion = curEntity.getVersion();
                     if (this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.CONTENT.PROGRAM_ID") != null) {
                        programId = this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.CONTENT.PROGRAM_ID");
                     }

                     if (this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.CONTENT.VERSION") != null && !this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.CONTENT.VERSION").equals("FAIL")) {
                        programVersion = this.rs.getLong(".MO.MONITORING_INFO.CURRENT_SCHEDULE.CONTENT.VERSION");
                     }

                     curEntity.setVersion(programVersion);

                     try {
                        programName = schInfo.getProgramName(programId);
                     } catch (Exception var45) {
                        this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] Get Program Name Exception " + var45.toString());
                     }

                     if (StringUtils.isNotEmpty(programId) && !StringUtils.equals(curEntity.getProgramId(), programId) || StringUtils.isNotEmpty(programName) && !StringUtils.equals(curEntity.getProgramName(), programName)) {
                        curEntity.setProgramId(programId);
                        curEntity.setProgramName(programName);
                     }

                     String contentStr = null;
                     boolean supportEventSchedule = true;
                     if (this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.ACTIVE_TYPE") != null && !this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.ACTIVE_TYPE").equals("")) {
                        curEntity.setActiveType(this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.ACTIVE_TYPE"));
                     } else {
                        curEntity.setActiveType((String)null);
                        supportEventSchedule = false;
                     }

                     if (this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.CONTENT.CONTENT_ID") != null) {
                        contentStr = this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.CONTENT.CONTENT_ID");
                     }

                     if (!this.compareContentId(contentStr, curEntity, 1L) || preProgramVersion != programVersion) {
                        this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] not compare! LFD : " + contentStr);
                        List contentList = this.setContentInfo(programId, contentStr, curEntity);
                        curEntity.setContentChannel(curEntity.getContentChannel());
                        curEntity.setContentLists(contentList);
                     }

                     String currentContentId = null;
                     if (!StringUtils.isEmpty(contentStr)) {
                        currentContentId = this.getCurrentContentId(contentStr);
                     }

                     String eventScheduleId;
                     String contentStr2;
                     String messageScheduleName;
                     try {
                        Long diskSpaceRepository = null;

                        try {
                           diskSpaceRepository = Long.parseLong(this.rs.getString(".MO.MONITOR_OPERATION.BOOTSTRAP.DISK_SPACE_REPOSITORY"));
                        } catch (Exception var43) {
                           this.logger.error("[MagicInfo_Keepalive][" + deviceId + "] invalid DISK_SPACE_REPOSITORY : " + this.rs.getString(".MO.MONITOR_OPERATION.BOOTSTRAP.DISK_SPACE_REPOSITORY"));
                        }

                        eventScheduleId = null;
                        if (this.rs.getString(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL") != null) {
                           eventScheduleId = this.rs.getString(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL");
                        }

                        this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] Disk space repository : " + diskSpaceRepository + ", direct channel : " + eventScheduleId);
                        contentStr2 = this.rs.getString(".MO.DEVICE_CONF.SYSTEM_INFO.DISK_SPACE_USAGE");
                        messageScheduleName = this.rs.getString(".MO.DEVICE_CONF.SYSTEM_INFO.DISK_SPACE_AVAILABLE");
                        if (contentStr2 == null) {
                           this.logger.debug("[MagicInfo_Keepalive][" + deviceId + "] : No Disk Space Usage");
                        }

                        if (messageScheduleName == null) {
                           this.logger.debug("[MagicInfo_Keepalive][" + deviceId + "] : No Disk Space Available");
                        }

                        try {
                           deviceDao.setKeepAliveInfo(deviceId, diskSpaceRepository, eventScheduleId, currentContentId, contentStr2, messageScheduleName);
                        } catch (Exception var42) {
                           this.logger.error("[MagicInfo_Keepalive][" + deviceId + "] Set Keepalive error " + var42.toString());
                        }
                     } catch (Exception var44) {
                        this.logger.fatal("[MagicInfo_Keepalive][" + deviceId + "] Last access time has not been updated - " + var44.toString());
                     }

                     if (this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.CONTENT.PLAYER_MODE") != null && !this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.CONTENT.PLAYER_MODE").equals("")) {
                        curEntity.setPlayerMode(this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.CONTENT.PLAYER_MODE"));
                     }

                     if (this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.PANEL_STATUS") != null && !this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.PANEL_STATUS").equals("")) {
                        curEntity.setPanelStatus((long)this.rs.getInt(".MO.MONITORING_INFO.CURRENT_SCHEDULE.PANEL_STATUS"));
                     }

                     if (this.rs.getString(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL") != null && !this.rs.getString(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL").equals("")) {
                        curEntity.setDirectChannel(this.rs.getString(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL"));
                     }

                     curEntity.setKeepaliveTime(new Timestamp(System.currentTimeMillis()));
                     if (supportEventSchedule) {
                        EventScheduleInfo esInfo = EventScheduleInfoImpl.getInstance();
                        eventScheduleId = null;
                        if (this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.EVENT.SCHEDULE_ID") != null) {
                           eventScheduleId = this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.EVENT.SCHEDULE_ID");
                        }

                        if (StringUtils.isNotEmpty(eventScheduleId) && !StringUtils.equals(eventScheduleId, curEntity.getEventScheduleId())) {
                           curEntity.setEventScheduleId(eventScheduleId);
                           contentStr2 = null;

                           try {
                              contentStr2 = esInfo.getEventScheduleName(eventScheduleId);
                           } catch (Exception var41) {
                              this.logger.info(var41.toString());
                              contentStr2 = "";
                           }

                           curEntity.setEventScheduleName(contentStr2);
                        }

                        if (this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.EVENT.VERSION") != null && !this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.EVENT.VERSION").equals("FAIL")) {
                           curEntity.setEventScheduleVersion(this.rs.getLong(".MO.MONITORING_INFO.CURRENT_SCHEDULE.EVENT.VERSION"));
                        }

                        contentStr2 = this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.EVENT.CONTENT_ID");
                        if (!this.compareContentId(contentStr2, curEntity, 3L)) {
                           List contentList = this.setEventInfo(eventScheduleId, contentStr2, curEntity);
                           curEntity.setEventScheduleContentLists(contentList);
                        }
                     }

                     try {
                        if (DeviceUtils.isSupportNOC()) {
                           DeviceNocInfo nocDao = DeviceNocInfoImpl.getInstance();
                           eventScheduleId = deviceDao.getDeviceGroupIdByDeviceId(deviceId);
                           boolean nocGroup = nocDao.isNocSupportGroup(Long.valueOf(eventScheduleId));
                           if (nocGroup) {
                              DeviceNocManager nocService = DeviceNocManagerImpl.getInstance();
                              nocService.thingworxUpdateDiagnosticsRealTime(curEntity, deviceId);
                           }
                        }
                     } catch (Exception var40) {
                        this.logger.error("[MagicInfo_Keepalive][" + deviceId + "] NOC diagnostic error Exception " + var40.toString());
                     }

                     String messageId = this.rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.MESSAGE.MESSAGE_ID");
                     long messageVersion = this.rs.getLong(".MO.MONITORING_INFO.CURRENT_SCHEDULE.MESSAGE.VERSION");
                     messageScheduleName = "";

                     try {
                        if (!StringUtils.isNotEmpty(messageId)) {
                           messageId = "00000000-0000-0000-0000-000000000000";
                        }

                        if (!StringUtils.equals(curEntity.getMessageScheduleId(), messageId)) {
                           curEntity.setMessageScheduleId(messageId);
                           MessageInfo msgInfo = MessageInfoImpl.getInstance();
                           if (!messageId.equals("00000000-0000-0000-0000-000000000000")) {
                              messageScheduleName = "";
                              MessageEntity messageEntity = msgInfo.getMessage(messageId, 0);
                              if (messageEntity != null) {
                                 messageScheduleName = messageEntity.getMessage_name();
                              }
                           }

                           curEntity.setMessageScheduleName(messageScheduleName);
                           curEntity.setMessageScheduleVersion(messageVersion);
                        }
                     } catch (Exception var39) {
                        this.logger.error("[MagicInfo_Keepalive][" + deviceId + "] Error while updating current message info in cache " + messageId + ", " + messageVersion + ", " + messageScheduleName);
                     }

                     ScheduleInfoEntity schEntity = mgr.getScheduleStatus(deviceId);
                     if (schEntity == null) {
                        this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] SchEntity is null");
                        schEntity = new ScheduleInfoEntity();
                     }

                     DeviceInfo devInfo = DeviceInfoImpl.getInstance();
                     boolean isInvaledMO = false;
                     boolean needReloadCache = false;
                     if (supportSchedule) {
                        if (!this.compareDeviceAndCache(deviceId, curEntity, schEntity, 1L)) {
                           String pId = devInfo.getProgramIdByDeviceId(deviceId);
                           if (!StringUtils.isEmpty(pId)) {
                              long version = 0L;
                              ProgramEntity pEntity = schInfo.getProgramWithGroupIdAndNameByProgramId(pId);
                              if (pEntity != null) {
                                 version = pEntity.getVersion();
                              } else {
                                 version = devInfo.getVersionByProgramId(pId);
                              }

                              this.logger.warn("[MagicInfo_Keepalive][" + deviceId + "] Different Content Schedule Version or ProgramId. Deploy Content Schedule programId : " + pId + " version : " + version);
                              curEntity.setPlayerProgramId(schEntity.getScheduleId());
                              curEntity.setPlayerVersion(schEntity.getScheduleVersion());
                              curEntity.setProgramLastDeployTime(new Timestamp(System.currentTimeMillis()));
                              ScheduleUtility.deploySchedule(deviceId, pId, version, schEntity.getScheduleVersion(), schedulerType);
                           }
                        }

                        if (!this.compareDeviceAndCache(deviceId, curEntity, schEntity, 2L)) {
                           MessageInfo msgInfo = MessageInfoImpl.getInstance();
                           boolean isDeployed = true;
                           String gId = "";

                           try {
                              gId = deviceDao.getDeviceGroupIdByDeviceId(deviceId);
                              if (!StringUtils.isEmpty(gId)) {
                                 MessageInfo minfo = MessageInfoImpl.getInstance();
                                 List mlist = minfo.getMappedMessageIdByGroupId(gId);
                                 if (mlist != null && mlist.size() > 0) {
                                    for(int i = 0; i < mlist.size(); ++i) {
                                       Map temp = (Map)mlist.get(i);
                                       if (temp.get("message_id").equals("00000000-0000-0000-0000-000000000000")) {
                                          isDeployed = false;
                                          needReloadCache = true;
                                          break;
                                       }
                                    }
                                 } else {
                                    isDeployed = false;
                                 }
                              } else {
                                 isDeployed = false;
                              }
                           } catch (Exception var47) {
                              this.logger.error("[MagicInfo_Keepalive][" + deviceId + "] No gId by deviceId", var47);
                              isDeployed = false;
                           }

                           if (isDeployed) {
                              try {
                                 curEntity.setPlayerMessageScheduleId(schEntity.getMessageId());
                                 curEntity.setPlayerMessgaeVersion(schEntity.getMessageVersion());
                                 curEntity.setMessageLastDeployTime(new Timestamp(System.currentTimeMillis()));
                                 this.logger.warn("[MagicInfo_Keepalive][" + deviceId + "] Different Message Version or MessageId. Message Schedule is Deployed messageId : " + schEntity.getMessageId());
                                 msgInfo.deployMessage(deviceId, schEntity.getMessageId(), schEntity.getMessageVersion(), schedulerType);
                              } catch (Exception var38) {
                                 this.logger.warn("[MagicInfo_Keepalive][" + deviceId + "] fail to deploy message schedule messageId : " + schEntity.getMessageId());
                              }
                           } else {
                              this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] Deploy Msg: Not Deployed");
                           }
                        }

                        if (supportEventSchedule && !this.compareDeviceAndCache(deviceId, curEntity, schEntity, 3L)) {
                           EventScheduleInfo esInfo = EventScheduleInfoImpl.getInstance();
                           String esId = "";
                           long esVersion = 1L;

                           try {
                              esId = esInfo.getEventScheduleIdByDeviceId(deviceId);
                              if (!StringUtils.isEmpty(esId)) {
                                 esVersion = esInfo.getVersionByEventScheduleId(esId);
                              }
                           } catch (Exception var37) {
                              this.logger.fatal("[MagicInfo_Keepalive][" + deviceId + "] Exception - get mapped eventschedule-device info");
                              isInvaledMO = true;
                           }

                           if (curEntity.getEventScheduleId() == null || esId.equals(curEntity.getEventScheduleId()) && (curEntity.getEventScheduleId().equals("00000000-0000-0000-0000-000000000000") || esVersion == curEntity.getEventScheduleVersion())) {
                              needReloadCache = true;
                           } else if (!isInvaledMO && !curEntity.getEventScheduleId().equals("") && esId != null && !esId.equals("") && curEntity.getEventScheduleId() != null && !curEntity.getEventScheduleId().equals("")) {
                              curEntity.setPlayerEventScheduleId(schEntity.getEventId());
                              curEntity.setPlayerEventVersion(schEntity.getEventVersion());
                              curEntity.setEventLastDeployTime(new Timestamp(System.currentTimeMillis()));
                              this.logger.warn("[MagicInfo_Keepalive][" + deviceId + "] Different Event Schedule Version or eventId. eventId : " + esId + " version : " + esVersion);
                              this.deployEventSchedule(deviceId, esId, esVersion);
                           } else {
                              this.logger.fatal("[MagicInfo_Keepalive][" + deviceId + "] EVENT_SCHEDULE DEPLOY - INVALID KEEP ALIVE - PID " + deviceId + isInvaledMO + curEntity.getEventScheduleId());
                              this.logger.warn("[MagicInfo_Keepalive][" + deviceId + "] - esId " + esId);
                              this.logger.warn("[MagicInfo_Keepalive][" + deviceId + "] - curEntity.getEventScheduleId() " + curEntity.getEventScheduleId());
                              this.logger.warn("[MagicInfo_Keepalive][" + deviceId + "] - esVersion " + esVersion);
                              this.logger.warn("[MagicInfo_Keepalive][" + deviceId + "] - curEntity.getEventScheduleVersion() " + curEntity.getEventScheduleVersion());
                           }
                        }

                        if (needReloadCache) {
                           this.logger.warn("[MagicInfo_Keepalive][" + deviceId + "] needReloadCache!");
                           MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
                           monMgr.scheduleReload(deviceId, 1);
                        }
                     }
                  } else {
                     this.logger.info("[MagicInfo_Keepalive][" + deviceId + "][Network Standby mode] Skipping deploying schedule .. " + deviceId);
                  }

                  mgr.setPlayingContent(deviceId, curEntity);
               }
            } else {
               this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] skip.. ");
            }

            RMQL rmql = RMQLInstanceCreator.getInstance(device, "NOTIFY", (Long)null);
            rmql.addParam("RESULT", "SUCCESS");
            RMQLDriver driver = RMQLDriverUtil.getWSRMQLDriver();
            return driver.createAppBOForResponse(rmql);
         }
      } catch (Exception var48) {
         this.logger.info("[MagicInfo_Keepalive][Mon] Exception");
         this.logger.error("", var48);

         try {
            eventPath = this.rs.getAttribute("DEVICE_ID");
            DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
            Device device = deviceDao.getDevice(eventPath);
            RMQL rmql = RMQLInstanceCreator.getInstance(device, "NOTIFY", (Long)null);
            rmql.addParam("RESULT", "FAIL");
            device = RMQLDriverUtil.getWSRMQLDriver();
            return device.createAppBOForResponse(rmql);
         } catch (Exception var36) {
            throw new ServiceException(var36);
         }
      }
   }

   private void deployEventSchedule(String deviceId, String esId, long esVersion) {
      try {
         EventScheduleInfo esInfo = EventScheduleInfoImpl.getInstance();
         esInfo.deployEventSchedule(deviceId, esId, esVersion, schedulerType);
      } catch (Exception var6) {
         this.logger.error("", var6);
      }

   }

   private List setContentInfo(String programId, String contentStr, CurrentPlayingEntity playingEntity) {
      ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
      ContentInfo conInfo = ContentInfoImpl.getInstance();
      List frameList = null;
      String contentChannel = null;
      ArrayList contentList = new ArrayList();

      try {
         frameList = schInfo.getFramesInfo(programId, 0);
         if (contentStr != null && !contentStr.equals("")) {
            String[] contents = contentStr.split(";");

            for(int i = 0; i < contents.length; ++i) {
               if (contents[i] != null && !contents[i].equals("")) {
                  ContentList content = new ContentList();
                  String contentId = null;
                  String frameIndex = null;
                  String[] contentIdArr;
                  if (contents[i].split(":").length == 4) {
                     contentIdArr = contents[i].split(":", 4);
                     contentChannel = contentIdArr[0];
                     frameIndex = contentIdArr[2];
                     contentId = contentIdArr[3];
                  } else {
                     contentIdArr = contents[i].split(":", 3);
                     frameIndex = contentIdArr[1];
                     contentId = contentIdArr[2];
                  }

                  if (DEFAULT_CONTENT_ID_LIST.contains(contentId)) {
                     content.setFrameIndex(frameIndex);
                     content.setContentId(contentId);
                     if (frameList != null) {
                        for(int j = 0; j < frameList.size(); ++j) {
                           if (((FrameEntity)frameList.get(j)).getFrame_index() == Integer.parseInt(frameIndex)) {
                              content.setFrameName(((FrameEntity)frameList.get(j)).getFrame_name());
                              if (((FrameEntity)frameList.get(j)).getIs_main_frame().equals("Y")) {
                                 content.setMainFrame(true);
                              } else {
                                 content.setMainFrame(false);
                              }
                              break;
                           }
                        }
                     }
                  } else {
                     Content contentInfo = conInfo.getThumbInfoOfActiveVersion(contentId);
                     content.setFrameIndex(frameIndex);
                     content.setContentId(contentId);
                     if (contentInfo != null) {
                        content.setContentName(contentInfo.getContent_name());
                        content.setThumbnailFileId(contentInfo.getThumb_file_id());
                        content.setThumbnailFileName(contentInfo.getThumb_file_name());
                     }

                     if (frameList != null) {
                        for(int j = 0; j < frameList.size(); ++j) {
                           if (((FrameEntity)frameList.get(j)).getFrame_index() == Integer.parseInt(frameIndex)) {
                              content.setFrameName(((FrameEntity)frameList.get(j)).getFrame_name());
                              if (((FrameEntity)frameList.get(j)).getIs_main_frame().equals("Y")) {
                                 content.setMainFrame(true);
                              } else {
                                 content.setMainFrame(false);
                              }
                              break;
                           }
                        }
                     }
                  }

                  contentList.add(content);
               }
            }

            playingEntity.setContentChannel(contentChannel);
         }
      } catch (Exception var16) {
         contentList = null;
      }

      return contentList;
   }

   private List setEventInfo(String scheduleId, String contentStr, CurrentPlayingEntity playingEntity) {
      ContentInfo conInfo = ContentInfoImpl.getInstance();
      ArrayList contentList = new ArrayList();

      try {
         if (!StringUtils.isEmpty(contentStr)) {
            String[] contents = contentStr.split(";");

            for(int i = 0; i < contents.length; ++i) {
               if (contents[i] != null && !contents[i].equals("")) {
                  ContentList content = new ContentList();
                  String[] contentIdArr = contents[i].split(":", 4);
                  String contentId = contentIdArr[3];
                  String frameIndex = contentIdArr[2];
                  if (!DEFAULT_CONTENT_ID_LIST.contains(contentId)) {
                     Content contentInfo = conInfo.getThumbInfoOfActiveVersion(contentId);
                     content.setFrameIndex(frameIndex);
                     content.setContentId(contentId);
                     if (contentInfo != null) {
                        content.setContentName(contentInfo.getContent_name());
                        content.setThumbnailFileId(contentInfo.getThumb_file_id());
                        content.setThumbnailFileName(contentInfo.getThumb_file_name());
                     }
                  }

                  contentList.add(content);
               }
            }
         }
      } catch (Exception var13) {
         contentList = null;
      }

      return contentList;
   }

   private boolean compareContentId(String contentStr, CurrentPlayingEntity playingEntity, long type) {
      List contentList = null;
      if (type == 1L) {
         contentList = playingEntity.getContentLists();
      } else if (type == 3L) {
         contentList = playingEntity.getEventScheduleContentLists();
      }

      if (!StringUtils.isEmpty(contentStr) && playingEntity != null && contentList != null) {
         String[] contents = contentStr.split(";");

         for(int i = 0; i < contents.length; ++i) {
            if (!StringUtils.isEmpty(contents[i])) {
               String contentId = null;
               String frameIndex = null;
               String[] contentIdArr;
               if (!StringUtils.isEmpty(contents[i]) && contents[i].split(":").length == 4) {
                  contentIdArr = contents[i].split(":", 4);
                  frameIndex = contentIdArr[2];
                  contentId = contentIdArr[3];
               } else {
                  contentIdArr = contents[i].split(":", 3);
                  frameIndex = contentIdArr[1];
                  contentId = contentIdArr[2];
               }

               if (!this.containsContent(contentList, contentId, frameIndex)) {
                  return false;
               }
            }
         }

         return true;
      } else {
         return false;
      }
   }

   private boolean containsContent(List contentlist, String contentId, String frameIndex) {
      Iterator var4 = contentlist.iterator();

      ContentList o;
      do {
         if (!var4.hasNext()) {
            return false;
         }

         o = (ContentList)var4.next();
      } while(o == null || o.getContentId() == null || !o.getContentId().equals(contentId) || o.getFrameIndex() == null || !o.getFrameIndex().equals(frameIndex));

      return true;
   }

   private boolean compareDeviceAndCache(String deviceId, CurrentPlayingEntity currentEntity, ScheduleInfoEntity schEntity, long type) {
      if (currentEntity != null && schEntity != null) {
         try {
            if (type == 1L) {
               if (currentEntity.getProgramId().equals(schEntity.getScheduleId()) && currentEntity.getVersion() == schEntity.getScheduleVersion()) {
                  return true;
               }

               this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] Program MEM SchContent " + schEntity.getScheduleId() + " " + schEntity.getScheduleVersion());
               this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] Program LFD SchContent " + currentEntity.getProgramId() + " " + currentEntity.getVersion());
               this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] before keepalive Program LFD SchContent " + currentEntity.getPlayerProgramId() + " " + currentEntity.getPlayerVersion());
               if ("".equals(schEntity.getScheduleId()) && schEntity.getScheduleVersion() == -1L) {
                  this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] deploy schedule for cache memory");
                  return false;
               }

               if (currentEntity.getPlayerProgramId().equals(schEntity.getScheduleId()) && currentEntity.getPlayerVersion() == schEntity.getScheduleVersion()) {
                  if (currentEntity.getProgramCount() > (long)deployScheduleRetryCount) {
                     if (currentEntity.getProgramLastDeployTime().getTime() + (long)deployScheduleCheckTime > System.currentTimeMillis()) {
                        this.logger.warn("[MagicInfo_Keepalive][" + deviceId + "] skip deploySchedule before deployed time : " + this.dateFormat.format(currentEntity.getProgramLastDeployTime()) + " programId : " + schEntity.getScheduleId() + " version : " + schEntity.getScheduleVersion());
                        return true;
                     }

                     currentEntity.setProgramCount(0L);
                  } else {
                     currentEntity.setProgramCount(currentEntity.getProgramCount() + 1L);
                     this.logger.warn("[MagicInfo_Keepalive][" + deviceId + "] deployed program count : " + currentEntity.getProgramCount() + " programId : " + currentEntity.getProgramId() + " version : " + currentEntity.getVersion());
                  }
               }
            } else if (type == 2L) {
               if (currentEntity.getMessageScheduleId().equals(schEntity.getMessageId())) {
                  return true;
               }

               this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] Message MEM SchContent " + schEntity.getMessageId() + " " + schEntity.getMessageVersion());
               this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] Message LFD SchContent " + currentEntity.getMessageScheduleId() + " " + currentEntity.getMessageScheduleVersion());
               this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] before keepalive Message LFD Schedule " + currentEntity.getPlayerMessageScheduleId() + " " + currentEntity.getPlayerMessgaeVersion());
               if (currentEntity.getPlayerMessageScheduleId().equals(schEntity.getMessageId()) && currentEntity.getPlayerMessgaeVersion() == schEntity.getMessageVersion()) {
                  if (currentEntity.getMessageCount() > (long)deployScheduleRetryCount) {
                     if (currentEntity.getMessageLastDeployTime().getTime() + (long)deployScheduleCheckTime > System.currentTimeMillis()) {
                        this.logger.warn("[MagicInfo_Keepalive][" + deviceId + "] skip deployMessage before deployed time : " + this.dateFormat.format(currentEntity.getMessageLastDeployTime()) + " messageId : " + schEntity.getMessageId() + " version : " + schEntity.getMessageVersion());
                        return true;
                     }

                     currentEntity.setMessageCount(0L);
                  } else {
                     currentEntity.setMessageCount(currentEntity.getMessageCount() + 1L);
                     this.logger.warn("[MagicInfo_Keepalive][" + deviceId + "] deployed message count : " + currentEntity.getMessageCount() + " messageId : " + currentEntity.getMessageScheduleId() + " version : " + currentEntity.getMessageScheduleVersion());
                  }
               }
            } else if (type == 3L) {
               if (currentEntity.getEventScheduleId().equals(schEntity.getEventId()) && schEntity.getEventId().equals("00000000-0000-0000-0000-000000000000")) {
                  return true;
               }

               if (currentEntity.getEventScheduleId().equals(schEntity.getEventId()) && currentEntity.getEventScheduleVersion() == schEntity.getEventVersion()) {
                  return true;
               }

               this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] Event MEM SchContent " + schEntity.getEventId() + " " + schEntity.getEventVersion());
               this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] Event LFD SchContent " + currentEntity.getEventScheduleId() + " " + currentEntity.getEventScheduleVersion());
               this.logger.info("[MagicInfo_Keepalive][" + deviceId + "] before keepalive Event LFD SchContent " + currentEntity.getPlayerEventScheduleId() + " " + currentEntity.getPlayerEventVersion());
               if (currentEntity.getPlayerEventScheduleId().equals(schEntity.getEventId()) && currentEntity.getPlayerEventVersion() == schEntity.getEventVersion()) {
                  if (currentEntity.getEventCount() > (long)deployScheduleRetryCount) {
                     if (currentEntity.getEventLastDeployTime().getTime() + (long)deployScheduleCheckTime > System.currentTimeMillis()) {
                        this.logger.warn("[MagicInfo_Keepalive][" + deviceId + "] skip deployMessage before deployed time : " + this.dateFormat.format(currentEntity.getEventLastDeployTime()) + " eventId : " + schEntity.getEventId() + " version : " + schEntity.getEventVersion());
                        return true;
                     }

                     currentEntity.setEventCount(0L);
                  } else {
                     currentEntity.setEventCount(currentEntity.getEventCount() + 1L);
                     this.logger.warn("[MagicInfo_Keepalive][" + deviceId + "] deployed event count : " + currentEntity.getEventCount() + " eventId : " + currentEntity.getEventScheduleId() + " version : " + currentEntity.getEventScheduleVersion());
                  }
               }
            }
         } catch (Exception var7) {
            this.logger.error("[MagicInfo_MonitoringServiceActivity] fail compareDeployProgram e : " + var7.getMessage());
         }

         return false;
      } else {
         return false;
      }
   }

   private String getCurrentContentId(String contentStr) {
      String contentId = null;
      String[] contents = contentStr.split(";");

      for(int i = 0; i < contents.length; ++i) {
         if (contents[i] != null && !contents[i].equals("")) {
            String[] contentIdArr;
            if (contents[i].split(":").length == 4) {
               contentIdArr = contents[i].split(":", 4);
               if (contentIdArr.length > 3) {
                  contentId = contentIdArr[3];
               }
            } else {
               contentIdArr = contents[i].split(":", 3);
               if (contentIdArr.length > 2) {
                  contentId = contentIdArr[2];
               }
            }
         }

         if (contentId != null) {
            return contentId;
         }
      }

      return contentId;
   }

   static {
      DEFAULT_CONTENT_ID_LIST.add("00000000-0000-0000-0000-000000000000");
      DEFAULT_CONTENT_ID_LIST.add("C0C336FC-0EAA-416C-AC92-697C4A103EDF");
      DEFAULT_CONTENT_ID_LIST.add("DEFAULT_CONTENT_ID");

      try {
         deployScheduleRetryCount = Integer.valueOf(CommonConfig.get("keepalive.deploy_schedule.retry_count"));
      } catch (Exception var3) {
         deployScheduleRetryCount = 5;
      }

      try {
         deployScheduleCheckTime = Integer.valueOf(CommonConfig.get("keepalive.deploy_schedule.checkTime")) * '\uea60';
      } catch (Exception var2) {
         deployScheduleCheckTime = 600000;
      }

      try {
         schedulerType = CommonConfig.get("keepalive.quartz.scheduler.type");
      } catch (Exception var1) {
         schedulerType = "";
      }

   }
}
