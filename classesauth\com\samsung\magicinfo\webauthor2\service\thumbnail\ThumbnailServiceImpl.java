package com.samsung.magicinfo.webauthor2.service.thumbnail;

import com.google.common.base.Joiner;
import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.service.thumbnail.ImageThumbnailService;
import com.samsung.magicinfo.webauthor2.service.thumbnail.MovieThumbnailService;
import com.samsung.magicinfo.webauthor2.service.thumbnail.ThumbnailService;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.Date;
import javax.imageio.ImageIO;
import javax.servlet.ServletContext;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ThumbnailServiceImpl implements ThumbnailService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.thumbnail.ThumbnailServiceImpl.class);
  
  private ImageThumbnailService imageThumbnailService;
  
  private MovieThumbnailService movieThumbnailService;
  
  private ServletContext servletContext;
  
  private UserData userData;
  
  @Autowired
  public ThumbnailServiceImpl(ImageThumbnailService imageThumbnailService, MovieThumbnailService movieThumbnailService, ServletContext servletContext, UserData userData) {
    this.imageThumbnailService = imageThumbnailService;
    this.movieThumbnailService = movieThumbnailService;
    this.servletContext = servletContext;
    this.userData = userData;
  }
  
  public Path createProjectThumbnail(String byteCode, String projectName, Boolean isTemplate, Boolean isVwl) throws UploaderException {
    try {
      Path uploadDir = initilizeDirectoryStructure();
      Path projectThumbnailPath = Paths.get(uploadDir.toString(), new String[] { projectName + "-projectThumbnail.png" });
      Path projectThumbnailPathParent = projectThumbnailPath.getParent();
      if (projectThumbnailPathParent != null && Files.notExists(projectThumbnailPathParent, new java.nio.file.LinkOption[0]))
        Files.createDirectories(projectThumbnailPathParent, (FileAttribute<?>[])new FileAttribute[0]); 
      writeThumbnail(byteCode, projectThumbnailPath);
      return projectThumbnailPath;
    } catch (IOException e) {
      logger.error(e.getMessage());
      throw new UploaderException("Error during thumbnail generation");
    } 
  }
  
  public Path createContentThumbnail(MediaSource mediaSource, Path targetDirectory) throws UploaderException {
    MediaType mediaType = mediaSource.getMediaType();
    switch (null.$SwitchMap$com$samsung$magicinfo$webauthor2$model$MediaType[mediaType.ordinal()]) {
      case 1:
        thumbnailPath = Paths.get(targetDirectory.toString(), new String[] { mediaSource.getFileName() + ".png" });
        this.movieThumbnailService.createMovieThumbnail(mediaSource, thumbnailPath);
        return thumbnailPath;
      case 2:
        thumbnailPath = Paths.get(targetDirectory.toString(), new String[] { mediaSource.getFileName() + ".png" });
        this.imageThumbnailService.createImageThumbnail(mediaSource, thumbnailPath);
        return thumbnailPath;
    } 
    Path thumbnailPath = Paths.get("", new String[0]);
    return thumbnailPath;
  }
  
  private Path initilizeDirectoryStructure() throws IOException {
    String userDirectory = Joiner.on("_").join(this.userData.getUserId(), Long.toString((new Date()).getTime()), new Object[0]);
    Path uploadDir = Paths.get(this.servletContext.getRealPath("insertContents"), new String[] { userDirectory });
    if (Files.notExists(uploadDir, new java.nio.file.LinkOption[0]))
      Files.createDirectories(uploadDir, (FileAttribute<?>[])new FileAttribute[0]); 
    return uploadDir;
  }
  
  private void writeThumbnail(String bytecode, Path path) throws IOException {
    Files.createFile(path, (FileAttribute<?>[])new FileAttribute[0]);
    BufferedImage newImg = decodeToImage(bytecode);
    ImageIO.write(newImg, "png", path.toFile());
  }
  
  private BufferedImage decodeToImage(String imageString) throws IOException {
    Base64 base64decoder = new Base64();
    try (ByteArrayInputStream bis = new ByteArrayInputStream(base64decoder.decode(imageString))) {
      return ImageIO.read(bis);
    } 
  }
}
