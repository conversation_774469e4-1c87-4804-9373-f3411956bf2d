package com.samsung.magicinfo.framework.content.manager;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.TemplateContentSetting;
import com.samsung.magicinfo.protocol.util.StringUtils;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.CDATASection;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.Text;
import org.xml.sax.SAXException;

public class DOMContentWriterImpl implements ContentXmlWriterInfo {
   Logger logger = LoggingManagerV2.getLogger(DOMContentWriterImpl.class);
   String dataListString = null;
   String lfdFileId = null;
   String lfdFileName = null;
   String lfdContentId = null;
   ArrayList arrConvertDataList = new ArrayList();
   ArrayList arrTextFormatList = new ArrayList();
   ArrayList arrContentFormatList = new ArrayList();

   public DOMContentWriterImpl() {
      super();
      this.arrTextFormatList.add("TEXT");
      this.arrTextFormatList.add("WORDART");
      this.arrTextFormatList.add("TABLE");
      this.arrContentFormatList.add("IMAGE");
      this.arrContentFormatList.add("VIDEO");
      this.arrContentFormatList.add("MEDIASLIDE");
      this.arrContentFormatList.add("SOUND");
      this.arrContentFormatList.add("FLASH");
      this.arrContentFormatList.add("AIRPORT");
   }

   private Element writeDLKContentInfo(Document doc, String strLfdContentIdByLfdFile, ContentFile templateLfdFile) {
      Element lfdContentElement = doc.createElement("LFDContent");
      lfdContentElement.setAttribute("id", strLfdContentIdByLfdFile);
      Element fileIdElement = doc.createElement("FileID");
      Text fileIdText = doc.createTextNode(templateLfdFile.getFile_id());
      fileIdElement.appendChild(fileIdText);
      Element fileNameElement = doc.createElement("FileName");
      Text fileNameText = doc.createTextNode(templateLfdFile.getFile_name());
      fileNameElement.appendChild(fileNameText);
      Element fileHashValueElement = doc.createElement("FileHashValue");
      Text fileHashValueText = doc.createTextNode(templateLfdFile.getHash_code());
      fileHashValueElement.appendChild(fileHashValueText);
      Element fileSizeElement = doc.createElement("FileSize");
      Text fileSizeText = doc.createTextNode(Long.toString(templateLfdFile.getFile_size()));
      fileSizeElement.appendChild(fileSizeText);
      lfdContentElement.appendChild(fileIdElement);
      lfdContentElement.appendChild(fileNameElement);
      lfdContentElement.appendChild(fileHashValueElement);
      lfdContentElement.appendChild(fileSizeElement);
      return lfdContentElement;
   }

   private Element getSettingElement(Document doc, TemplateContentSetting templateContentInfo) {
      String pollingIntervalValue = templateContentInfo.getPollingInterval() + "";
      Element settingElement = doc.createElement("Setting");
      Element pollingIntervalElement = doc.createElement("PollingInterval");
      Text pollingIntervalText = doc.createTextNode(pollingIntervalValue);
      pollingIntervalElement.appendChild(pollingIntervalText);
      settingElement.appendChild(pollingIntervalElement);
      return settingElement;
   }

   private ArrayList getContentDataItem(String[] arrContentData, int pageNo, String elementName, int itemNo) {
      ArrayList resultContentDataList = new ArrayList();

      for(int data_i = 0; data_i < arrContentData.length; ++data_i) {
         String[] arrContentDataItem = arrContentData[data_i].split("[|]");
         if (arrContentDataItem.length >= 20) {
            int dataPageNo = Integer.parseInt(arrContentDataItem[0].replace("p", ""));
            String dataElementName = arrContentDataItem[4];
            int dataItemNo = Integer.parseInt(arrContentDataItem[2].replace("i", ""));
            String isInnerDatalink = arrContentDataItem[9];
            if (elementName != null) {
               if (pageNo == dataPageNo && elementName.equals(dataElementName) && isInnerDatalink.equalsIgnoreCase("FALSE")) {
                  resultContentDataList.add(arrContentDataItem);
               } else if (pageNo == dataPageNo && elementName.equals(dataElementName) && isInnerDatalink.equalsIgnoreCase("TRUE") && itemNo == dataItemNo) {
                  resultContentDataList.add(arrContentDataItem);
               }
            }
         }
      }

      return resultContentDataList;
   }

   private Node searchTemplateElementList(Document doc, Node node, String contentData) {
      this.logger.info(contentData);
      String[] arrContentData = contentData.split("\\^");
      NodeList pageNodeList = node.getChildNodes();
      if (pageNodeList.getLength() >= 1) {
         int totalPageNodeSize = pageNodeList.getLength();

         for(int page_i = 0; page_i < totalPageNodeSize; ++page_i) {
            if (pageNodeList.item(page_i).getNodeName().equalsIgnoreCase("PAGE")) {
               int pageNo = Integer.parseInt(((Element)pageNodeList.item(page_i)).getAttributeNode("no").getTextContent());
               this.logger.info("pageNo = " + pageNo);
               NodeList syncGroupNodeList = pageNodeList.item(page_i).getChildNodes();
               int syncGroupNodeListSize = syncGroupNodeList.getLength();

               for(int syncGroup_i = 0; syncGroup_i < syncGroupNodeListSize; ++syncGroup_i) {
                  if (syncGroupNodeList.item(syncGroup_i).getNodeName().equalsIgnoreCase("SYNCGROUP")) {
                     this.logger.info("syncGroup = " + syncGroup_i);
                     int syncGroupId = Integer.parseInt(((Element)syncGroupNodeList.item(syncGroup_i)).getAttributeNode("id").getTextContent());
                     this.logger.info("syncGroupId = " + syncGroupId);
                     NodeList splitGroupNodeList = syncGroupNodeList.item(syncGroup_i).getChildNodes();

                     for(int splitGroup_i = 0; splitGroup_i < splitGroupNodeList.getLength(); ++splitGroup_i) {
                        if (splitGroupNodeList.item(splitGroup_i).getNodeName().equalsIgnoreCase("SPLITGROUP")) {
                           this.logger.info("splitGroup = " + splitGroup_i);
                           int splitGroupId = Integer.parseInt(((Element)splitGroupNodeList.item(splitGroup_i)).getAttributeNode("id").getTextContent());
                           this.logger.info("splitGroupId = " + splitGroupId);
                           NodeList elementNodeList = splitGroupNodeList.item(splitGroup_i).getChildNodes();

                           for(int element_i = 0; element_i < elementNodeList.getLength(); ++element_i) {
                              if (elementNodeList.item(element_i).getNodeName().equalsIgnoreCase("ELEMENT")) {
                                 this.logger.info("element = " + element_i);
                                 NodeList elementChildNodeList = elementNodeList.item(element_i).getChildNodes();
                                 String elementName = null;
                                 boolean isInnerDatalink = false;
                                 Node innerDatalinkNode = null;

                                 for(int elementChild_i = 0; elementChild_i < elementChildNodeList.getLength(); ++elementChild_i) {
                                    if (elementChildNodeList.item(elementChild_i).getNodeName().equalsIgnoreCase("NAME")) {
                                       elementName = elementChildNodeList.item(elementChild_i).getTextContent();
                                       this.logger.info("elementName = " + elementName);
                                    } else if (elementChildNodeList.item(elementChild_i).getNodeName().equalsIgnoreCase("InnerDataLink")) {
                                       isInnerDatalink = true;
                                       innerDatalinkNode = elementChildNodeList.item(elementChild_i);
                                    }
                                 }

                                 int elementChild_i;
                                 if (!isInnerDatalink) {
                                    ArrayList arrContentDataItemList;
                                    String[] arrContentDataItem;
                                    Element dataElement;
                                    if (splitGroupId == 0) {
                                       arrContentDataItemList = this.getContentDataItem(arrContentData, pageNo, elementName, 0);

                                       for(elementChild_i = 0; elementChild_i < arrContentDataItemList.size(); ++elementChild_i) {
                                          arrContentDataItem = (String[])arrContentDataItemList.get(elementChild_i);
                                          dataElement = this.makeDataElement(doc, arrContentDataItem);
                                          elementNodeList.item(element_i).appendChild(dataElement);
                                       }
                                    } else if (splitGroupId > 0) {
                                       arrContentDataItemList = this.getContentDataItem(arrContentData, pageNo, elementName, 0);

                                       for(elementChild_i = 0; elementChild_i < arrContentDataItemList.size(); ++elementChild_i) {
                                          arrContentDataItem = (String[])arrContentDataItemList.get(elementChild_i);
                                          dataElement = this.makeDataElement(doc, arrContentDataItem);
                                          splitGroupNodeList.item(splitGroup_i).appendChild(dataElement);
                                       }
                                    }
                                 } else {
                                    NodeList innerDatalinkNodeList = innerDatalinkNode.getChildNodes();

                                    for(elementChild_i = 0; elementChild_i < innerDatalinkNodeList.getLength(); ++elementChild_i) {
                                       if (innerDatalinkNodeList.item(elementChild_i).getNodeName().equalsIgnoreCase("Item")) {
                                          int itemNo = Integer.parseInt(((Element)innerDatalinkNodeList.item(elementChild_i)).getAttributeNode("no").getTextContent());
                                          ArrayList arrContentDataItemList = this.getContentDataItem(arrContentData, pageNo, elementName, itemNo);

                                          for(int dataItemI = 0; dataItemI < arrContentDataItemList.size(); ++dataItemI) {
                                             String[] arrContentDataItem = (String[])arrContentDataItemList.get(dataItemI);
                                             Element dataElement = this.makeDataElement(doc, arrContentDataItem);
                                             innerDatalinkNodeList.item(elementChild_i).appendChild(dataElement);
                                          }
                                       }
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            }
         }
      }

      return node;
   }

   public void writeDLKFile(TemplateContentSetting templateContentInfo) {
      this.logger.info("fromLFDFile=" + templateContentInfo.getFromLftFile());
      Element dataLinkContentMetaElement = null;
      FileInputStream inputStreamTemplate = null;

      try {
         DocumentBuilderFactory docFctory = DocumentUtils.getDocumentBuilderFactoryInstance();
         DocumentBuilder builder = docFctory.newDocumentBuilder();
         inputStreamTemplate = new FileInputStream(templateContentInfo.getFromLftFile());
         Document doc = builder.parse(inputStreamTemplate);
         Element root = doc.getDocumentElement();
         String strLfdContentIdByLfdFile = root.getAttributeNode("id").getTextContent();
         this.logger.info("strLfdContentIdByLfdFile = " + strLfdContentIdByLfdFile);
         NodeList contentChildNodeList = root.getChildNodes();
         int contentChildNodeListSize = contentChildNodeList.getLength();

         for(int contentChild_i = 0; contentChild_i < contentChildNodeListSize; ++contentChild_i) {
            if (contentChildNodeList.item(contentChild_i).getNodeName().equalsIgnoreCase("DATALINKCONTENTMETA")) {
               Node dataNode = this.searchTemplateElementList(doc, contentChildNodeList.item(contentChild_i), templateContentInfo.getContentData());
               dataLinkContentMetaElement = (Element)dataNode;
               break;
            }
         }

         if (dataLinkContentMetaElement != null) {
            dataLinkContentMetaElement.removeAttribute("virtual");
            dataLinkContentMetaElement.setAttribute("version", templateContentInfo.getDlkVersionId().toString());
            dataLinkContentMetaElement.appendChild(this.writeDLKContentInfo(doc, strLfdContentIdByLfdFile, templateContentInfo.getTemplateLftFile()));
            dataLinkContentMetaElement.appendChild(this.getSettingElement(doc, templateContentInfo));
            doc.replaceChild(dataLinkContentMetaElement, doc.getChildNodes().item(0));
         }

         TransformerFactory factory2 = DocumentUtils.getTransformerFactoryInstance();
         Transformer trans = factory2.newTransformer();
         trans.setOutputProperty("method", "xml");
         trans.setOutputProperty("encoding", "UTF-8");
         trans.setOutputProperty("indent", "yes");
         trans.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "10");
         File output = SecurityUtils.getSafeFile(templateContentInfo.getToDlkFile());
         trans.transform(new DOMSource(doc), new StreamResult(output));
      } catch (ParserConfigurationException var30) {
         this.logger.error(var30.toString());
      } catch (SAXException var31) {
         this.logger.error(var31.toString());
      } catch (IOException var32) {
         this.logger.error(var32.toString());
      } catch (TransformerConfigurationException var33) {
         this.logger.error(var33.toString());
      } catch (TransformerException var34) {
         this.logger.error(var34.toString());
      } finally {
         try {
            if (inputStreamTemplate != null) {
               inputStreamTemplate.close();
               inputStreamTemplate = null;
            }
         } catch (Exception var29) {
            this.logger.error("", var29);
         }

      }

   }

   private Element makeDataElement(Document doc, String[] arrContentDataItem) {
      Element dataElement = doc.createElement("Data");
      String dataType = null;
      Element serverAddressElement = doc.createElement("ServerAddress");
      Element valueLocationElement = doc.createElement("ValueLocation");
      Element values = doc.createElement("Values");
      Element value = doc.createElement("Value");
      String contentType = "";
      if (arrContentDataItem[9].equalsIgnoreCase("TRUE")) {
         contentType = arrContentDataItem[7];
      } else {
         contentType = arrContentDataItem[6];
      }

      Text valueText;
      Element convertTableElement;
      Text tagText;
      if (arrContentDataItem[5].equalsIgnoreCase("DIRECTINPUT")) {
         dataType = "Static";
         dataElement.appendChild(serverAddressElement);
         dataElement.appendChild(valueLocationElement);
         String directInputContentId;
         if (this.arrTextFormatList.contains(contentType.toUpperCase())) {
            directInputContentId = arrContentDataItem[10];
            directInputContentId = StringUtils.replaceStrHTML(directInputContentId);
            directInputContentId = directInputContentId.replace("<br>", "\n");
            CDATASection cdataSection = doc.createCDATASection(directInputContentId);
            value.appendChild(cdataSection);
            values.appendChild(value);
         } else if (this.arrContentFormatList.contains(contentType.toUpperCase())) {
            directInputContentId = arrContentDataItem[13];
            String filesize = "";
            String contentFileName = "";
            String contentFileid = "";
            ContentInfoImpl contentInfo = ContentInfoImpl.getInstance();

            try {
               ContentFile contentFile = contentInfo.getMainFileInfo(directInputContentId);
               filesize = contentFile.getFile_size().toString();
               contentFileName = contentFile.getFile_name();
               contentFileid = contentFile.getFile_id();
            } catch (SQLException var33) {
               this.logger.error("", var33);
            }

            String valueString = contentFileid + "\\" + contentFileName;
            valueText = null;
            if (contentType.equalsIgnoreCase("MEDIASLIDE")) {
               valueText = doc.createTextNode(contentFileid);
            } else {
               valueText = doc.createTextNode(valueString);
            }

            value.appendChild(valueText);
            Element fileInfoElement = doc.createElement("FileInfo");
            fileInfoElement.setAttribute("filesize", filesize);
            Text fileInfoText = doc.createTextNode(valueString);
            fileInfoElement.appendChild(fileInfoText);
            value.appendChild(fileInfoElement);
            values.appendChild(value);
         }

         dataElement.appendChild(values);
         if (arrContentDataItem[15].length() > 0) {
            Element tagListElement = doc.createElement("TagList");
            tagListElement.setAttribute("matchType", arrContentDataItem[16]);
            if (arrContentDataItem[15].length() > 0) {
               String[] arrMainTagList = arrContentDataItem[15].split(",");

               for(int i = 0; i < arrMainTagList.length; ++i) {
                  convertTableElement = doc.createElement("Tag");
                  tagText = doc.createTextNode(arrMainTagList[i]);
                  convertTableElement.appendChild(tagText);
                  tagListElement.appendChild(convertTableElement);
               }
            }

            dataElement.appendChild(tagListElement);
         }
      } else if (arrContentDataItem[5].equalsIgnoreCase("DATALINK")) {
         dataType = "Dynamic";
         Text serverAddressText = doc.createTextNode(arrContentDataItem[18]);
         serverAddressElement.appendChild(serverAddressText);
         dataElement.appendChild(serverAddressElement);
         Text valueLocationText = doc.createTextNode(arrContentDataItem[11]);
         valueLocationElement.appendChild(valueLocationText);
         valueLocationElement.setAttribute("view", arrContentDataItem[17]);
         dataElement.appendChild(valueLocationElement);
         dataElement.appendChild(values);
         if (arrContentDataItem[15].length() > 0 || arrContentDataItem[19].length() > 0) {
            Element tagListElement = doc.createElement("TagList");
            tagListElement.setAttribute("matchType", arrContentDataItem[16]);
            if (arrContentDataItem[15].length() > 0) {
               String[] arrMainTagList = arrContentDataItem[15].split(",");

               for(int i = 0; i < arrMainTagList.length; ++i) {
                  Element tagElement = doc.createElement("Tag");
                  valueText = doc.createTextNode(arrMainTagList[i]);
                  tagElement.appendChild(valueText);
                  tagListElement.appendChild(tagElement);
               }
            }

            if (arrContentDataItem[19].length() > 0) {
               convertTableElement = doc.createElement("Column");
               tagText = doc.createTextNode(arrContentDataItem[19]);
               convertTableElement.appendChild(tagText);
               tagListElement.appendChild(convertTableElement);
            }

            dataElement.appendChild(tagListElement);
         }

         if (arrContentDataItem[20].length() > 0) {
            ConvertDataInfo convertDataInfo = ConvertDataInfoImpl.getInstance();
            convertTableElement = doc.createElement("ConvertTable");
            String convertTableName = arrContentDataItem[20];
            String[] arrConvertTableName = convertTableName.split(",");

            for(int arrConvert_i = 0; arrConvert_i < arrConvertTableName.length; ++arrConvert_i) {
               String convertName = arrConvertTableName[arrConvert_i];
               if (!this.arrConvertDataList.contains(convertName)) {
                  this.arrConvertDataList.add(convertName);
               }

               try {
                  List fromToDataList = convertDataInfo.getConvertDataFromToByName(convertName);
                  Iterator fromToDataListIterator = fromToDataList.iterator();

                  while(fromToDataListIterator.hasNext()) {
                     Element rowElement = doc.createElement("Row");
                     Element fromElement = doc.createElement("From");
                     Element toElement = doc.createElement("To");
                     Map fromToData = (Map)fromToDataListIterator.next();
                     String from_data = (String)fromToData.get("from_data");
                     String to_data = (String)fromToData.get("to_data");
                     Text fromText = null;
                     Text toText = null;
                     if (from_data != null) {
                        fromText = doc.createTextNode(from_data);
                        fromElement.appendChild(fromText);
                     }

                     if (to_data != null) {
                        if (this.arrTextFormatList.contains(contentType.toUpperCase())) {
                           toText = doc.createTextNode(to_data);
                           toElement.appendChild(toText);
                        } else if (this.arrContentFormatList.contains(contentType.toUpperCase())) {
                           ContentInfo contentInfo = ContentInfoImpl.getInstance();
                           ContentFile contentFile = contentInfo.getMainFileInfo(to_data);
                           Element fileInfoElement = doc.createElement("FileInfo");
                           String fileName = contentFile.getFile_id() + "\\" + contentFile.getFile_name();
                           if (contentType.equalsIgnoreCase("MEDIASLIDE")) {
                              toText = doc.createTextNode(contentFile.getFile_id());
                           } else {
                              toText = doc.createTextNode(fileName);
                           }

                           toElement.appendChild(toText);
                           fileInfoElement.setAttribute("filesize", Long.toString(contentFile.getFile_size()));
                           Text fileInfoText = doc.createTextNode(fileName);
                           fileInfoElement.appendChild(fileInfoText);
                           toElement.appendChild(fileInfoElement);
                        }
                     }

                     rowElement.appendChild(fromElement);
                     rowElement.appendChild(toElement);
                     convertTableElement.appendChild(rowElement);
                  }
               } catch (SQLException var34) {
                  this.logger.error("", var34);
               }
            }

            dataElement.appendChild(convertTableElement);
         }
      }

      dataElement.setAttribute("type", dataType);
      return dataElement;
   }

   public ArrayList getConvertDataList() {
      return this.arrConvertDataList;
   }
}
