package com.samsung.magicinfo.rc.model.client;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.samsung.magicinfo.rc.model.client.Parameters;

public class Service {
  @JacksonXmlProperty(localName = "id")
  String id;
  
  @JacksonXmlProperty(localName = "token")
  String token;
  
  @JacksonXmlProperty(localName = "parameters")
  Parameters parameters;
  
  public String getId() {
    return this.id;
  }
  
  public void setId(String id) {
    this.id = id;
  }
  
  public String getToken() {
    return this.token;
  }
  
  public void setToken(String token) {
    this.token = token;
  }
  
  public Parameters getParameters() {
    return this.parameters;
  }
  
  public void setParameters(Parameters parameters) {
    this.parameters = parameters;
  }
}
