package com.samsung.magicinfo.restapi.system.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.DiagnosisConstants;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.file.FileLoaderServlet;
import com.samsung.magicinfo.protocol.util.TimeUtil;
import com.samsung.magicinfo.restapi.contents.service.V2ContentServiceImpl;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.InetAddress;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import javax.annotation.Nullable;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.DefaultValue;
import jodd.util.StringUtil;
import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import org.apache.ftpserver.db.DownloadInfo;
import org.apache.ftpserver.db.DownloadInfoImpl;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2FileService")
@Transactional
public class V2FileDownloadServiceImpl implements V2FileDownloadService {
   protected Logger logger = LoggingManagerV2.getLogger(V2ContentServiceImpl.class);

   public V2FileDownloadServiceImpl() {
      super();
   }

   public void downloadFile(@NotNull @NotEmpty String homeType, @NotNull @NotEmpty String filePath, @Nullable String deviceId, @Nullable Long fileOffset, @DefaultValue("false") boolean isSaveAs, HttpServletRequest request, HttpServletResponse response) throws IOException {
      FileInputStream fileIS = null;
      BufferedOutputStream os = null;
      FileChannel fileChannel = null;
      String device_id = StrUtils.nvl(deviceId);
      String file_id = null;
      DownloadInfo var13 = DownloadInfoImpl.getInstance();

      try {
         String filepath;
         try {
            String topPath = this.getRootDirectoryPath(homeType);
            filepath = filePath;
            boolean onS3Storage = false;
            if (homeType.equals("JOBS_RESULT_HOME")) {
               topPath = topPath + File.separatorChar + device_id;
            }

            if (homeType.equals("COLLECTED_LOG")) {
               filepath = this.makeProtectedZipFiles(device_id, StrUtils.nvl(filePath));
            }

            if (homeType.equals("CONTENT")) {
               onS3Storage = true;
            }

            File m_file = null;
            String file_tm0 = null;
            String file_tm1 = null;
            String file_tm2 = null;
            String fullpath = null;
            String realname = null;
            String[] tmp = null;
            if (filepath != null) {
               filepath = filepath.replace("\\", "/");
               if (isSaveAs) {
                  file_tm0 = URLDecoder.decode(filepath, "8859_1");
                  file_tm1 = URLDecoder.decode(filepath, "EUC-KR");
                  file_tm2 = filepath;
                  filepath = file_tm0;
               } else {
                  file_tm1 = filepath;
               }

               if (filepath.contains("/")) {
                  tmp = filepath.split("/");
               } else if (filepath.contains("\\")) {
                  tmp = filepath.split("\\");
               }

               if (!onS3Storage) {
                  topPath = topPath.replace("\\", "/");
               }

               realname = file_tm1;
               if (tmp != null && tmp.length > 1) {
                  String var10000 = tmp[tmp.length - 2];
                  realname = tmp[tmp.length - 1];
               }

               URLEncoder.encode(realname, "UTF-8");
               response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(realname, "UTF-8") + ";");
               response.setHeader("Content-Type", "application/octet-stream; charset=UTF-8");
               this.logger.debug("[MagicInfo_HTTP] TEST real name is " + realname);
               int dotIdx = realname.lastIndexOf(".");
               int binaryRead;
               String hostName;
               if (dotIdx > 0) {
                  binaryRead = realname.length();
                  hostName = realname.substring(dotIdx + 1, binaryRead);
                  String contentType = FileLoaderServlet.getContentType(hostName.toLowerCase());
                  response.setContentType(contentType);
               } else {
                  response.setContentType("application/unknown");
               }

               os = new BufferedOutputStream(response.getOutputStream());
               if (!onS3Storage) {
                  fullpath = topPath + "/" + file_tm1;
                  File file = SecurityUtils.getSafeFile(fullpath);
                  if (!file.exists()) {
                     fullpath = topPath + "/" + file_tm1;
                     file = SecurityUtils.getSafeFile(fullpath);
                  }

                  if (!file.exists()) {
                     fullpath = topPath + "/" + file_tm2;
                     file = SecurityUtils.getSafeFile(fullpath);
                  }

                  fullpath = SecurityUtils.directoryTraversalChecker(fullpath, request.getRemoteAddr());
               }

               hostName = CommonConfig.get("download.server.node.name");
               if (hostName == null) {
                  hostName = InetAddress.getLocalHost().getHostName();
               }

               if (!onS3Storage) {
                  m_file = SecurityUtils.getSafeFile(fullpath);
                  fileIS = new FileInputStream(m_file);
                  fileChannel = fileIS.getChannel();
                  long fileOffsetLong = fileOffset == null ? 0L : fileOffset;
                  if (m_file.length() > 0L) {
                     for(ByteBuffer buf = ByteBuffer.allocate(1024); (binaryRead = fileChannel.read(buf, fileOffsetLong)) != -1; fileOffsetLong += (long)binaryRead) {
                        buf.flip();
                        os.write(buf.array(), 0, binaryRead);
                        buf.clear();
                     }
                  }

                  os.close();
                  fileChannel.close();
                  fileIS.close();
               }
            }
         } catch (FileNotFoundException var34) {
            filepath = request.getParameter("id");
            if (!StringUtil.isEmpty(filepath)) {
               ContentInfo contentInfo = ContentInfoImpl.getInstance();
               contentInfo.deleteFileInfoIfNoExistFile(filepath);
            }

            this.logger.error(var34);
            response.sendError(404, var34.getMessage());
         } catch (Exception var35) {
            this.logger.error(var35);
            response.sendError(500, var35.getMessage());
         }
      } finally {
         if (os != null) {
            os.close();
         }

         if (fileChannel != null) {
            fileChannel.close();
         }

         if (fileIS != null) {
            fileIS.close();
         }

      }

   }

   private String getRootDirectoryPath(String home) throws ConfigException {
      String SLASH1 = File.separator;
      char SLASH2 = File.separatorChar;
      String REPORT_DIR = "report";
      String CONTENTS_DIR = "contents_home";
      String JNLP_DIR = "jnlp";
      String JOBS_HOME_DIR = "jobs_home";
      String RESULT = "result";
      String DIR_DIAG_LFD = "diagnosis_lfd";
      String DIR_DIAG_SERVER = "diagnosis_server";
      String rootDirPath = "";
      if (home != null && home.length() > 0) {
         byte var22 = -1;
         switch(home.hashCode()) {
         case -2124122866:
            if (home.equals("COLLECTED_LOG")) {
               var22 = 13;
            }
            break;
         case -1977776172:
            if (home.equals("CAPTURE_DIR")) {
               var22 = 4;
            }
            break;
         case -1483528231:
            if (home.equals("LOGO_DIR")) {
               var22 = 20;
            }
            break;
         case -1325086607:
            if (home.equals("DIAGNOSIS_SERVER")) {
               var22 = 15;
            }
            break;
         case -1149020840:
            if (home.equals("JOBS_RESULT_HOME")) {
               var22 = 12;
            }
            break;
         case -1139644205:
            if (home.equals("USER_HOME")) {
               var22 = 2;
            }
            break;
         case -1139276458:
            if (home.equals("FACE_REPORT")) {
               var22 = 7;
            }
            break;
         case -1078008062:
            if (home.equals("POP_REPORT")) {
               var22 = 6;
            }
            break;
         case -1019788169:
            if (home.equals("SOFTWARE_HOME")) {
               var22 = 18;
            }
            break;
         case -996298506:
            if (home.equals("JNLP_HOME")) {
               var22 = 11;
            }
            break;
         case -933923249:
            if (home.equals("PRODUCT_HOME")) {
               var22 = 0;
            }
            break;
         case -928480925:
            if (home.equals("IMAGE_HOME")) {
               var22 = 3;
            }
            break;
         case -579061020:
            if (home.equals("CONTENTS_HOME")) {
               var22 = 9;
            }
            break;
         case -30326884:
            if (home.equals("DIAGNOSIS_LFD")) {
               var22 = 14;
            }
            break;
         case 528296327:
            if (home.equals("SCHEDULE_HOME")) {
               var22 = 8;
            }
            break;
         case 1202405359:
            if (home.equals("ADMIN_HOME")) {
               var22 = 19;
            }
            break;
         case 1268736152:
            if (home.equals("REMOTE_JOB_RESULT")) {
               var22 = 5;
            }
            break;
         case 1499406822:
            if (home.equals("NOTICE_HOME")) {
               var22 = 1;
            }
            break;
         case 1669513305:
            if (home.equals("CONTENT")) {
               var22 = 10;
            }
            break;
         case 1956048875:
            if (home.equals("VWT_HOME")) {
               var22 = 16;
            }
            break;
         case 2145929140:
            if (home.equals("ALARM_RULE_HOME")) {
               var22 = 17;
            }
         }

         String UPLOAD_HOME;
         String CONTENTS_HOME;
         String JOBS_DIR;
         switch(var22) {
         case 0:
            UPLOAD_HOME = CommonConfig.get("UPLOAD_HOME");
            rootDirPath = UPLOAD_HOME + SLASH1 + "Product" + SLASH1;
            break;
         case 1:
            UPLOAD_HOME = CommonConfig.get("UPLOAD_HOME");
            rootDirPath = UPLOAD_HOME + SLASH1 + "notice" + SLASH1;
            break;
         case 2:
            UPLOAD_HOME = CommonConfig.get("UPLOAD_HOME");
            rootDirPath = UPLOAD_HOME + SLASH1 + "User" + SLASH1;
            break;
         case 3:
            String IMAGE_HOME = CommonConfig.get("IMAGE_HOME");
            rootDirPath = IMAGE_HOME + SLASH1;
            break;
         case 4:
            UPLOAD_HOME = CommonConfig.get("UPLOAD_HOME");
            String CAPTURE_DIR = CommonConfig.get("CAPTURE_DIR");
            rootDirPath = UPLOAD_HOME + SLASH1 + CAPTURE_DIR;
            break;
         case 5:
            CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME");
            JOBS_DIR = CommonConfig.get("JOBS_DIR");
            rootDirPath = CONTENTS_HOME + SLASH1 + JOBS_DIR + SLASH1 + "result";
            break;
         case 6:
            UPLOAD_HOME = CommonConfig.get("UPLOAD_HOME");
            String POP_LOG_DIR = CommonConfig.get("POP_LOG_DIR");
            rootDirPath = UPLOAD_HOME + SLASH1 + POP_LOG_DIR + SLASH1 + "report";
            break;
         case 7:
            UPLOAD_HOME = CommonConfig.get("UPLOAD_HOME");
            String FACE_LOG_DIR = CommonConfig.get("FACE_LOG_DIR");
            rootDirPath = UPLOAD_HOME + SLASH1 + FACE_LOG_DIR + SLASH1 + "report";
            break;
         case 8:
            String SCHEDULE_HOME = CommonConfig.get("SCHEDULE_HOME");
            rootDirPath = SCHEDULE_HOME;
            break;
         case 9:
         case 10:
            CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME");
            rootDirPath = CONTENTS_HOME + SLASH1 + "contents_home";
            break;
         case 11:
            CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME");
            rootDirPath = CONTENTS_HOME + SLASH1 + "jnlp";
            break;
         case 12:
            CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME");
            rootDirPath = CONTENTS_HOME + SLASH2 + "jobs_home" + SLASH2 + "result" + SLASH2;
            break;
         case 13:
            CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME");
            rootDirPath = CONTENTS_HOME.replace('/', SLASH2) + SLASH2 + "device_log" + SLASH2 + "downloads";
            break;
         case 14:
            CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME");
            JOBS_DIR = CommonConfig.get("JOBS_DIR");
            rootDirPath = CONTENTS_HOME + SLASH1 + JOBS_DIR + SLASH1 + "result" + SLASH1 + "diagnosis_lfd";
            break;
         case 15:
            CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME");
            JOBS_DIR = CommonConfig.get("JOBS_DIR");
            rootDirPath = CONTENTS_HOME + SLASH1 + JOBS_DIR + SLASH1 + "result" + SLASH1 + "diagnosis_server";
            break;
         case 16:
            String VWT_HOME = CommonConfig.get("VWT_HOME");
            rootDirPath = VWT_HOME;
            break;
         case 17:
         case 18:
         case 19:
         case 20:
         default:
            UPLOAD_HOME = CommonConfig.get("UPLOAD_HOME");
            rootDirPath = UPLOAD_HOME;
         }
      }

      return rootDirPath;
   }

   private String makeProtectedZipFiles(String deviceId, String fileNames) throws Exception {
      String downloadPath = null;
      String DeviceLogPath = null;
      String zipFilename = "";

      try {
         this.logger.error("[MagicInfo_HTTP] Log Collect - make zip fils");
         DeviceLogPath = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "device_log";
         downloadPath = DeviceLogPath + File.separatorChar + "downloads";
         File downloadPahHome = SecurityUtils.getSafeFile(downloadPath);
         if (!downloadPahHome.exists()) {
            downloadPahHome.mkdir();
         }

         Timestamp startTime = DateUtils.dateTime2TimeStamp(TimeUtil.getCurrentGMTTime());
         SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
         String token = sdf.format(startTime);
         zipFilename = deviceId + "_" + token + ".zip";
         String zipFilePath = downloadPath + File.separator + zipFilename;
         ZipFile zipFile = new ZipFile(zipFilePath);
         ArrayList filesToAdd = new ArrayList();
         String[] logFileList = fileNames.split(",");
         String[] var14 = logFileList;
         int var15 = logFileList.length;

         String diagnosticFilePath;
         for(int var16 = 0; var16 < var15; ++var16) {
            diagnosticFilePath = var14[var16];
            String filePath = DeviceLogPath + File.separatorChar + deviceId + File.separatorChar + diagnosticFilePath;
            filesToAdd.add(new File(filePath));
         }

         String encryption = StrUtils.nvl(CommonConfig.get("device.log_collect.encryption"));
         if (encryption.equalsIgnoreCase("true")) {
            String filePath = DeviceLogPath + File.separatorChar + deviceId + File.separatorChar + DiagnosisConstants.key;
            File keyFile = SecurityUtils.getSafeFile(filePath);
            if (keyFile.exists()) {
               filesToAdd.add(keyFile);
            }

            diagnosticFilePath = DeviceLogPath + File.separatorChar + deviceId + File.separatorChar + DiagnosisConstants.diagnosticKey;
            File diagnosticKeyFile = SecurityUtils.getSafeFile(diagnosticFilePath);
            if (diagnosticKeyFile.exists()) {
               filesToAdd.add(diagnosticKeyFile);
            }
         }

         ZipParameters parameters = new ZipParameters();
         parameters.setCompressionMethod(8);
         parameters.setCompressionLevel(5);
         parameters.setEncryptFiles(true);
         parameters.setEncryptionMethod(99);
         parameters.setAesKeyStrength(3);
         String password = token.substring(8, 14);
         this.logger.error("[MagicInfo_HTTP] zipFilename : " + zipFilename + " , password : " + password);
         parameters.setPassword(password);
         zipFile.addFiles(filesToAdd, parameters);
      } catch (ConfigException var19) {
         this.logger.error("", var19);
      }

      return zipFilename;
   }
}
