package com.samsung.magicinfo.framework.ruleset.manager;

import com.google.gson.Gson;
import com.google.gson.JsonParser;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.common.utils.FileUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.ruleset.entity.Condition;
import com.samsung.magicinfo.framework.ruleset.entity.Result;
import com.samsung.magicinfo.framework.ruleset.entity.RuleSet;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.File;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.Map.Entry;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

public class RulesetUtils {
   private static Logger logger = LoggingManagerV2.getLogger(RulesetUtils.class);

   public RulesetUtils() {
      super();
   }

   public static Map castConditionToFormat(Condition condition) {
      Map conditionMap = CommonUtils.ConverObjectToMap(condition);
      if (condition.getType() != null && condition.getType().equalsIgnoreCase("datalink")) {
         Map valueLocationMap = new HashMap();
         valueLocationMap.put("view", "v");
         valueLocationMap.put("text", condition.getValue_location());
         Map tagListMap = new HashMap();
         tagListMap.put("match_type", condition.getTag_match_type());
         tagListMap.put("column", condition.getTag_match_column());
         if (condition.getTags() != null && condition.getTags().length() > 0) {
            tagListMap.put("tags", condition.getTags().split(","));
         }

         Map configMap = new HashMap();
         configMap.put("server_address", condition.getServer_address());
         configMap.put("value_location", valueLocationMap);
         configMap.put("polling_interval", condition.getDatalink_polling_interval());
         configMap.put("tag_list", tagListMap);
         conditionMap.put("config", configMap);
      }

      String valueString = condition.getValue();
      if (valueString != null) {
         List values = new ArrayList();
         String[] valueArray = valueString.split(";");
         String[] var5 = valueArray;
         int var6 = valueArray.length;

         for(int var7 = 0; var7 < var6; ++var7) {
            String value = var5[var7];
            Map valueMap = new HashMap();
            if (value.indexOf("-") > -1) {
               Map range = new HashMap();
               String[] split = value.split("-");
               range.put("start", split[0]);
               range.put("end", split[1]);
               valueMap.put("value", range);
            } else {
               valueMap.put("value", value);
            }

            values.add(valueMap);
         }

         conditionMap.put("values", values);
      }

      return conditionMap;
   }

   public static List getConditionMapListInRuleset(String rulesetId) throws SQLException {
      RuleSetInfo rulesetInfo = RuleSetInfoImpl.getInstance();
      List conditions = rulesetInfo.getConditionsInRuleset(rulesetId);
      List conditionsMap = null;
      if (conditions != null && !conditions.isEmpty()) {
         conditionsMap = new ArrayList();
         Iterator var4 = conditions.iterator();

         while(var4.hasNext()) {
            Condition condition = (Condition)var4.next();
            conditionsMap.add(castConditionToFormat(condition));
         }
      }

      return conditionsMap;
   }

   public static List getResultMapListInRuleset(String rulesetId) throws SQLException {
      RuleSetInfo rulesetInfo = RuleSetInfoImpl.getInstance();
      List results = rulesetInfo.getResultsInRuleset(rulesetId);
      List resultsMap = null;
      if (results != null && !results.isEmpty()) {
         resultsMap = new ArrayList();
         Iterator var4 = results.iterator();

         while(var4.hasNext()) {
            Result result = (Result)var4.next();
            resultsMap.add(castResultToFormat(result));
         }
      }

      return resultsMap;
   }

   public static List getXmlChildrenElement(Element element, String tagName) {
      List resultList = new ArrayList();
      NodeList nodeList = element.getChildNodes();

      for(int i = 0; i < nodeList.getLength(); ++i) {
         Node node = nodeList.item(i);
         if (node instanceof Element) {
            Element e = (Element)node;
            if (e.getTagName().equals(tagName)) {
               resultList.add(e);
            }
         }
      }

      return resultList;
   }

   public static Map castResultToFormat(Result result) {
      Map resultMap = CommonUtils.ConverObjectToMap(result);
      String resultName = result.getResult_name();
      String contentsType = result.getContents_type();
      String resultType = result.getType();
      resultMap.put("contents_type", contentsType.toLowerCase());
      Map defaultDuration = new HashMap();
      if (result.getDefault_duration() != null) {
         defaultDuration.put("default_duration", result.getDefault_duration().toString());
      }

      resultMap.put("option", defaultDuration);
      resultMap.put("description", result.getDescription());
      resultMap.put("device_type", result.getDevice_type());
      resultMap.put("device_type_version", result.getDevice_type_version());

      try {
         ArrayList filterValueList;
         HashMap content;
         if (resultType != null && resultType.equalsIgnoreCase("static")) {
            List contentsIds = result.getContentsIDList();
            if (contentsIds != null) {
               filterValueList = new ArrayList();

               for(int i = 0; i < contentsIds.size(); ++i) {
                  content = new HashMap();
                  if (contentsType.equalsIgnoreCase("content")) {
                     ContentInfo contentDao = ContentInfoImpl.getInstance();
                     content.put("content_id", contentsIds.get(i));
                     content.put("name", contentDao.getContentName((String)contentsIds.get(i)));
                  } else if (contentsType.equalsIgnoreCase("playlist")) {
                     PlaylistInfo playlistDao = PlaylistInfoImpl.getInstance();
                     content.put("playlist_id", contentsIds.get(i));
                     content.put("name", playlistDao.getPlaylistName((String)contentsIds.get(i)));
                  }

                  filterValueList.add(content);
               }

               resultMap.put("contents", filterValueList);
            }
         } else if (resultType != null && resultType.equalsIgnoreCase("dynamic")) {
            Map filter = new HashMap();
            filterValueList = new ArrayList();
            Map filterValueItem1 = new HashMap();
            content = new HashMap();
            Map filterValueItem3 = new HashMap();
            Map filterValueItem4 = new HashMap();
            String filterValue1 = result.getFilter_value_1();
            String filterValue2 = result.getFilter_value_2();
            String filterValue3 = result.getFilter_value_3();
            String filterValue4 = result.getFilter_value_4();
            if (filterValue1 != null) {
               filterValueItem1.put("value", filterValue1);
               filterValueItem1.put("order", "0");
               filterValueList.add(filterValueItem1);
            }

            if (filterValue2 != null) {
               content.put("value", filterValue2);
               content.put("order", "1");
               filterValueList.add(content);
            }

            if (filterValue3 != null) {
               filterValueItem3.put("value", filterValue3);
               filterValueItem3.put("order", "2");
               filterValueList.add(filterValueItem3);
            }

            if (filterValue4 != null) {
               filterValueItem4.put("value", filterValue4);
               filterValueItem4.put("order", "3");
               filterValueList.add(filterValueItem4);
            }

            filter.put("sign", result.getFilter_sign());
            filter.put("expression", result.getFilter_expression());
            filter.put("dst_type", result.getFilter_dst_type());
            filter.put("src_type", result.getFilter_src_type());
            filter.put("separator", result.getFilter_separator());
            filter.put("expression_type", result.getFilter_expression_type());
            filter.put("filter_value_list", filterValueList);
            resultMap.put("filter", filter);
         }
      } catch (SQLException var16) {
         logger.error("", var16);
      }

      return resultMap;
   }

   public static ContentFile makeRuleMetaFile(RuleSet rulesetObject, List conditionList, List resultList, JSONArray contentsJson, JSONArray rulesJson) throws SQLException, ParserConfigurationException, TransformerException {
      DocumentBuilderFactory docFactory = DocumentUtils.getDocumentBuilderFactoryInstance();
      DocumentBuilder docBuilder = docFactory.newDocumentBuilder();
      Document doc = docBuilder.newDocument();
      doc.setXmlStandalone(true);
      Element rulesetEl = doc.createElement("ruleset");
      rulesetEl.setAttribute("id", rulesetObject.getRuleset_id());
      rulesetEl.setAttribute("version", rulesetObject.getVersion() != null ? rulesetObject.getVersion().toString() : "");
      doc.appendChild(rulesetEl);
      makeOptionTagInRulesetXML(doc, rulesetObject, rulesetEl);
      makeConditionsTagInRulesetXML(conditionList, doc, rulesetEl);
      ArrayList contentsIDList = new ArrayList();
      ArrayList playlistIDList = new ArrayList();
      makeResultsTaginRulesetXML(resultList, doc, rulesetEl, contentsIDList, playlistIDList);
      makeContentsTagInRulesetXML(contentsJson, doc, rulesetEl, contentsIDList, playlistIDList);
      makeDefaultPlayTagInRulesetXML(doc, rulesetObject, rulesetEl);
      makeRulesTaginRulesetXML(rulesJson, doc, rulesetEl);
      TransformerFactory transformerFactory = DocumentUtils.getTransformerFactoryInstance();
      Transformer transformer = transformerFactory.newTransformer();
      transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
      transformer.setOutputProperty("encoding", "UTF-8");
      transformer.setOutputProperty("indent", "yes");
      transformer.setOutputProperty("doctype-public", "yes");
      StringWriter writer = new StringWriter();
      transformer.transform(new DOMSource(doc), new StreamResult(writer));
      String output = writer.getBuffer().toString();

      try {
         ContentFile ruleMetaFile = createRuleFile(output, rulesetObject.getRuleset_id());
         return ruleMetaFile;
      } catch (Exception var17) {
         logger.error("[createRuleFolder] failed to create Rule Meta file." + var17.getMessage());
         return null;
      }
   }

   private static void makeDefaultPlayTagInRulesetXML(Document doc, RuleSet rulesetObject, Element rulesetEl) throws SQLException {
      ContentInfo contentDao = ContentInfoImpl.getInstance();
      String defaultContentID = rulesetObject.getDefault_play();
      Element defaultPlayEl = doc.createElement("default_play");
      if (defaultContentID != null && !defaultContentID.equals("")) {
         Content content = contentDao.getContentActiveVerInfo(defaultContentID);
         Content contentThumb = contentDao.getThumbInfoOfActiveVersion(defaultContentID);
         ContentFile contentFile = contentDao.getSfiFileInfo(defaultContentID);
         if (contentFile == null) {
            contentFile = contentDao.getMainFileInfo(defaultContentID);
         }

         Element contentEl = doc.createElement("content");
         defaultPlayEl.appendChild(contentEl);
         Element defaultContentIdEl = doc.createElement("content_id");
         contentEl.appendChild(defaultContentIdEl);
         defaultContentIdEl.appendChild(doc.createTextNode(defaultContentID));
         Element defaultContentDurationEl = doc.createElement("duration");
         Element defaultContentStartDateEl = doc.createElement("start_date");
         Element defaultContentExpiredDateEl = doc.createElement("expired_date");
         if (content.getPlay_time() != null && !content.getPlay_time().equals("")) {
            String duration = String.valueOf(ContentUtils.getPlayTimeStr(content.getPlay_time()));
            if (content.getPlay_time_milli() != null && !content.getPlay_time_milli().equals("")) {
               duration = duration + "." + content.getPlay_time_milli();
            }

            defaultContentDurationEl.appendChild(doc.createTextNode(duration));
         }

         contentEl.appendChild(defaultContentDurationEl);
         contentEl.appendChild(defaultContentStartDateEl);
         contentEl.appendChild(defaultContentExpiredDateEl);
         Element fileEl = doc.createElement("file");
         Element fileIdEl = doc.createElement("id");
         fileIdEl.appendChild(doc.createTextNode(contentFile.getFile_id()));
         Element fileNameEl = doc.createElement("name");
         fileNameEl.appendChild(doc.createTextNode(contentFile.getFile_name()));
         Element fileSizeEl = doc.createElement("size");
         fileSizeEl.appendChild(doc.createTextNode(contentFile.getFile_size().toString()));
         Element fileHashEl = doc.createElement("hash");
         fileHashEl.appendChild(doc.createTextNode(contentFile.getHash_code()));
         Element fileThumbIdEl = doc.createElement("thumb_id");
         if (contentThumb != null && contentThumb.getThumb_file_id() != null) {
            fileThumbIdEl.appendChild(doc.createTextNode(contentThumb.getThumb_file_id()));
         }

         fileEl.appendChild(fileIdEl);
         fileEl.appendChild(fileNameEl);
         fileEl.appendChild(fileSizeEl);
         fileEl.appendChild(fileHashEl);
         fileEl.appendChild(fileThumbIdEl);
         contentEl.appendChild(fileEl);
      }

      rulesetEl.appendChild(defaultPlayEl);
   }

   private static ContentInfo makeContentsTagInRulesetXML(JSONArray contentsJson, Document doc, Element rulesetEl, ArrayList contentsIDList, ArrayList playlistIDList) throws SQLException {
      ContentInfo contentDao = ContentInfoImpl.getInstance();
      if (contentsJson != null) {
         for(int i = 0; i < contentsJson.length(); ++i) {
            JSONObject contentItem = contentsJson.getJSONObject(i);
            if (contentItem.getString("contents_type").equalsIgnoreCase("content")) {
               contentsIDList.add(contentItem.getString("contents_id"));
            } else if (contentItem.getString("contents_type").equalsIgnoreCase("playlist")) {
               playlistIDList.add(contentItem.getString("contents_id"));
            }
         }
      }

      HashSet contentHash = new HashSet(contentsIDList);
      HashSet playlistHash = new HashSet(playlistIDList);
      contentsIDList.clear();
      playlistIDList.clear();
      contentsIDList = new ArrayList(contentHash);
      playlistIDList = new ArrayList(playlistHash);
      Element contentsEl = doc.createElement("contents");

      int i;
      String playlistID;
      Element playlistEl;
      Element contentIdEl;
      for(i = 0; i < contentsIDList.size(); ++i) {
         playlistID = (String)contentsIDList.get(i);
         Content content = contentDao.getContentActiveVerInfo(playlistID);
         Content contentThumb = contentDao.getThumbInfoOfActiveVersion(playlistID);
         ContentFile contentFile = contentDao.getSfiFileInfo(playlistID);
         if (contentFile == null) {
            contentFile = contentDao.getMainFileInfo(playlistID);
         }

         if (contentFile != null && content != null) {
            playlistEl = doc.createElement("content");
            playlistEl.setAttribute("id", playlistID);
            playlistEl.setAttribute("content_name", content.getContent_name());
            contentIdEl = doc.createElement("content_id");
            contentIdEl.appendChild(doc.createTextNode(playlistID));
            Element contentDurationEl = doc.createElement("duration");
            if (content.getPlay_time() != null && !content.getPlay_time().equals("")) {
               String duration = String.valueOf(ContentUtils.getPlayTimeStr(content.getPlay_time()));
               if (content.getPlay_time_milli() != null && !content.getPlay_time_milli().equals("")) {
                  duration = duration + "." + content.getPlay_time_milli();
               }

               contentDurationEl.appendChild(doc.createTextNode(duration));
            }

            Element startDateEl = doc.createElement("start_date");
            Element expiredDateEl = doc.createElement("expired_date");
            Element fileEl = doc.createElement("file");
            playlistEl.appendChild(contentIdEl);
            playlistEl.appendChild(contentDurationEl);
            playlistEl.appendChild(startDateEl);
            playlistEl.appendChild(expiredDateEl);
            Element fileIdEl = doc.createElement("id");
            fileIdEl.appendChild(doc.createTextNode(contentFile.getFile_id()));
            Element fileNameEl = doc.createElement("name");
            fileNameEl.appendChild(doc.createTextNode(contentFile.getFile_name()));
            Element fileSizeEl = doc.createElement("size");
            fileSizeEl.appendChild(doc.createTextNode(contentFile.getFile_size().toString()));
            Element fileHashEl = doc.createElement("hash");
            fileHashEl.appendChild(doc.createTextNode(contentFile.getHash_code()));
            Element fileThumbIdEl = doc.createElement("thumb_id");
            if (contentThumb != null && contentThumb.getThumb_file_id() != null) {
               fileThumbIdEl.appendChild(doc.createTextNode(contentThumb.getThumb_file_id()));
            }

            fileEl.appendChild(fileIdEl);
            fileEl.appendChild(fileNameEl);
            fileEl.appendChild(fileSizeEl);
            fileEl.appendChild(fileHashEl);
            fileEl.appendChild(fileThumbIdEl);
            playlistEl.appendChild(fileEl);
            contentsEl.appendChild(playlistEl);
         }
      }

      for(i = 0; i < playlistIDList.size(); ++i) {
         playlistID = (String)playlistIDList.get(i);
         PlaylistInfo playlistDao = PlaylistInfoImpl.getInstance();
         Playlist playlist = playlistDao.getPlaylistActiveVerInfo(playlistID);
         List contentList = playlistDao.getActiveVerContentList(playlistID);
         if (playlist != null) {
            playlistEl = doc.createElement("playlist");
            playlistEl.setAttribute("id", playlistID);
            playlistEl.setAttribute("playlist_name", playlist.getPlaylist_name());
            contentIdEl = doc.createElement("shuffle");
            contentIdEl.appendChild(doc.createTextNode(playlist.getIs_shuffle()));
            playlistEl.appendChild(contentIdEl);

            for(int j = 0; j < contentList.size(); ++j) {
               PlaylistContent pContent = (PlaylistContent)contentList.get(j);
               if (pContent.getIs_sub_playlist()) {
                  List subContentList = playlistDao.getActiveVerContentList(pContent.getContent_id());
                  if (subContentList != null && subContentList.size() > 0) {
                     Iterator var34 = subContentList.iterator();

                     while(var34.hasNext()) {
                        PlaylistContent subContent = (PlaylistContent)var34.next();
                        makePlaylistContentXml(subContent, doc, playlistEl);
                     }
                  }
               } else {
                  makePlaylistContentXml(pContent, doc, playlistEl);
               }
            }

            contentsEl.appendChild(playlistEl);
         }
      }

      rulesetEl.appendChild(contentsEl);
      return contentDao;
   }

   private static void makeRulesTaginRulesetXML(JSONArray rulesJson, Document doc, Element rulesetEl) {
      Element rulesElement = doc.createElement("rules");
      rulesetEl.appendChild(rulesElement);

      for(int i = 0; i < rulesJson.length(); ++i) {
         JSONObject ruleJSON = rulesJson.getJSONObject(i);
         Element ruleElement = doc.createElement("rule");
         Long ruleId = 0L;
         Boolean multiResult = true;
         ruleId = ruleJSON.getLong("rule_id");
         ruleElement.setAttribute("rule_id", ruleId.toString());
         multiResult = ruleJSON.getBoolean("multi_result");
         ruleElement.setAttribute("rule_name", ruleJSON.getString("rule_name"));
         ruleElement.setAttribute("multi_result", multiResult.toString());
         rulesElement.appendChild(ruleElement);
         JSONArray ruleTreeArray = ruleJSON.getJSONArray("rule_tree");
         ruleTreeTraversal(doc, ruleElement, ruleTreeArray);
      }

   }

   private static void makeResultsTaginRulesetXML(List resultList, Document doc, Element rulesetEl, ArrayList contentsIDList, ArrayList playlistIDList) throws SQLException {
      Element resultsEl = doc.createElement("results");
      Iterator var6 = resultList.iterator();

      while(true) {
         Result result;
         do {
            if (!var6.hasNext()) {
               rulesetEl.appendChild(resultsEl);
               return;
            }

            result = (Result)var6.next();
         } while(result.getIs_public());

         Element resultEl = doc.createElement("result");
         resultEl.setAttribute("id", result.getResult_id());
         resultEl.setAttribute("type", result.getContents_type().toLowerCase());
         resultEl.setAttribute("source", result.getType());
         Element resultOptionEl = doc.createElement("option");
         Element defaultDurationEl = doc.createElement("default_duration");
         if (result.getContents_type().equalsIgnoreCase("content")) {
            defaultDurationEl.appendChild(doc.createTextNode(result.getDefault_duration().toString()));
         }

         resultOptionEl.appendChild(defaultDurationEl);
         resultEl.appendChild(resultOptionEl);
         Element filterEl;
         Element bundleEl;
         if (result.getType().equalsIgnoreCase("static")) {
            filterEl = doc.createElement("contents");

            for(int i = 0; i < result.getContentsIDList().size(); ++i) {
               List contentIDList = result.getContentsIDList();
               String contentID = (String)contentIDList.get(i);
               bundleEl = doc.createElement("id");
               bundleEl.appendChild(doc.createTextNode(contentID));
               filterEl.appendChild(bundleEl);
            }

            resultEl.appendChild(filterEl);
         } else if (result.getType().equalsIgnoreCase("dynamic")) {
            filterEl = doc.createElement("filter");
            filterEl.setAttribute("value_type", result.getFilter_expression_type());
            filterEl.setAttribute("sign", result.getFilter_sign());
            filterEl.setAttribute("separator", result.getFilter_separator());
            filterEl.setAttribute("src_type", result.getFilter_src_type());
            filterEl.setAttribute("dst_type", result.getFilter_dst_type());
            List bundleList = new ArrayList();
            List bundleMapList = new ArrayList();
            LinkedHashMap filterMap;
            String values;
            String[] valueArray;
            if (result.getFilter_value_1() != "" && !result.getFilter_value_1().isEmpty()) {
               filterMap = new LinkedHashMap();
               filterMap.put("order", 0);
               values = result.getFilter_value_1();
               valueArray = values.split(";");
               filterMap.put("value", valueArray);
               bundleMapList.add(filterMap);
               bundleList.add(values);
            }

            if (result.getFilter_value_2() != "" && !result.getFilter_value_2().isEmpty()) {
               filterMap = new LinkedHashMap();
               filterMap.put("order", 1);
               values = result.getFilter_value_2();
               valueArray = values.split(";");
               filterMap.put("value", valueArray);
               bundleMapList.add(filterMap);
               bundleList.add(values);
            }

            if (result.getFilter_value_3() != "" && !result.getFilter_value_3().isEmpty()) {
               filterMap = new LinkedHashMap();
               filterMap.put("order", 2);
               values = result.getFilter_value_3();
               valueArray = values.split(";");
               filterMap.put("value", valueArray);
               bundleMapList.add(filterMap);
               bundleList.add(values);
            }

            if (result.getFilter_value_4() != "" && !result.getFilter_value_4().isEmpty()) {
               filterMap = new LinkedHashMap();
               filterMap.put("order", 3);
               values = result.getFilter_value_4();
               valueArray = values.split(";");
               filterMap.put("value", valueArray);
               bundleMapList.add(filterMap);
               bundleList.add(values);
            }

            for(int i = 0; i < bundleList.size(); ++i) {
               bundleEl = doc.createElement("bundle");
               bundleEl.setAttribute("index", Integer.toString(i));
               bundleEl.appendChild(doc.createTextNode((String)bundleList.get(i)));
               filterEl.appendChild(bundleEl);
            }

            Element valueEl = doc.createElement("value");
            valueEl.appendChild(doc.createTextNode(result.getFilter_expression()));
            filterEl.appendChild(valueEl);
            resultEl.appendChild(filterEl);
            if (result.getType().equalsIgnoreCase("content")) {
               contentsIDList.addAll(getDynamicResultContentUsingRegex(result.getType(), result.getFilter_expression(), bundleMapList));
            } else if (result.getType().equalsIgnoreCase("playlist")) {
               playlistIDList.addAll(getDynamicResultContentUsingRegex(result.getType(), result.getFilter_expression(), bundleMapList));
            }
         }

         resultsEl.appendChild(resultEl);
      }
   }

   private static void makeConditionsTagInRulesetXML(List conditionList, Document doc, Element rulesetEl) {
      Element conditionsEl = doc.createElement("conditions");
      rulesetEl.appendChild(conditionsEl);
      Iterator var4 = conditionList.iterator();

      while(true) {
         Condition condition;
         do {
            if (!var4.hasNext()) {
               rulesetEl.appendChild(conditionsEl);
               return;
            }

            condition = (Condition)var4.next();
         } while(condition.getIs_public());

         Element conditionEl = doc.createElement("condition");
         conditionEl.setAttribute("id", condition.getCondition_id());
         String source = condition.getSource();
         conditionEl.setAttribute("source", source);
         String type = condition.getType();
         conditionEl.setAttribute("type", type);
         conditionEl.setAttribute("value_type", condition.getValue_type());
         conditionEl.setAttribute("sign", condition.getSign());
         if (condition.getIs_invert() != null) {
            conditionEl.setAttribute("invert", condition.getIs_invert().toString());
         }

         if (type != null && type.equalsIgnoreCase("curr_date")) {
            conditionEl.setAttribute("repeat", condition.getRepeat_type());
         }

         String[] valueList;
         if (source != null && source.equalsIgnoreCase("external")) {
            Element configEl = doc.createElement("config");
            valueList = condition.getServer_address().split("/");
            Element serverAddressEl = doc.createElement("server_address");
            serverAddressEl.appendChild(doc.createTextNode(valueList[valueList.length - 1]));
            Element valueLocationEl = doc.createElement("value_location");
            valueLocationEl.setAttribute("view", condition.getDatalink_view());
            valueLocationEl.appendChild(doc.createTextNode(condition.getValue_location()));
            Element pollingInterval = doc.createElement("polling_interval");
            pollingInterval.appendChild(doc.createTextNode(condition.getDatalink_polling_interval().toString()));
            Element tagListEl = doc.createElement("tag_list");
            tagListEl.setAttribute("match_type", condition.getTag_match_type());
            if (condition.getTag_match_column() != null) {
               tagListEl.setAttribute("column", condition.getTag_match_column());
            }

            if (condition.getTags() != null) {
               String tags = condition.getTags();
               String[] tagList = tags.split(",");
               String[] var17 = tagList;
               int var18 = tagList.length;

               for(int var19 = 0; var19 < var18; ++var19) {
                  String tag = var17[var19];
                  Element tagEl = doc.createElement("tag");
                  tagEl.appendChild(doc.createTextNode(tag));
                  tagListEl.appendChild(tagEl);
               }
            }

            configEl.appendChild(serverAddressEl);
            configEl.appendChild(valueLocationEl);
            configEl.appendChild(pollingInterval);
            configEl.appendChild(tagListEl);
            conditionEl.appendChild(configEl);
         }

         String values = condition.getValue();
         valueList = values.split(";");
         String[] var23 = valueList;
         int var24 = valueList.length;

         for(int var25 = 0; var25 < var24; ++var25) {
            String value = var23[var25];
            Element valueEl = doc.createElement("value");
            if (condition.getSign().equalsIgnoreCase("rg")) {
               if (value.indexOf("-") > -1) {
                  String start = value.split("-")[0];
                  String end = value.split("-")[1];
                  if (type.equalsIgnoreCase("curr_date")) {
                     start = start.replace(".", "-");
                     end = end.replace(".", "-");
                  }

                  Element startEl = doc.createElement("start");
                  startEl.appendChild(doc.createTextNode(start));
                  Element endEl = doc.createElement("end");
                  endEl.appendChild(doc.createTextNode(end));
                  valueEl.appendChild(startEl);
                  valueEl.appendChild(endEl);
               }
            } else {
               valueEl.appendChild(doc.createTextNode(value));
            }

            conditionEl.appendChild(valueEl);
         }

         conditionsEl.appendChild(conditionEl);
      }
   }

   private static void makeOptionTagInRulesetXML(Document doc, RuleSet rulesetObject, Element rulesetEl) {
      Element optionEl = doc.createElement("option");
      Element multiRuleEl = doc.createElement("multi_rule");
      multiRuleEl.appendChild(doc.createTextNode(rulesetObject.getMulti_rule().toString()));
      if (rulesetObject.getSync_duration() != null) {
         Element syncEl = doc.createElement("sync");
         Element durationEl = doc.createElement("duration");
         durationEl.appendChild(doc.createTextNode(rulesetObject.getSync_duration().toString()));
         syncEl.appendChild(durationEl);
         optionEl.appendChild(syncEl);
      }

      optionEl.appendChild(multiRuleEl);
      rulesetEl.appendChild(optionEl);
   }

   public static RuleSet createNewRulesetAndFile(String ruleset, String conditions, String contents, String rules, String results) throws Exception {
      JSONArray conditionsJson = new JSONArray(conditions);
      JSONArray contentsJson = new JSONArray(contents);
      JSONArray rulesJson = new JSONArray(rules);
      JSONArray resultsJson = new JSONArray(results);
      JSONObject rulesetJson = new JSONObject(ruleset);
      RuleSet rulesetObject = castJSONtoRuleset(rulesetJson);
      List conditionList = castJSONtoConditions(conditionsJson);
      List resultList = castJSONtoResults(resultsJson);
      ContentFile ruleMetaFile = makeRuleMetaFile(rulesetObject, conditionList, resultList, contentsJson, rulesJson);
      if (ruleMetaFile != null) {
         try {
            rulesetObject.setConditions(conditionList);
            rulesetObject.setResults(resultList);
            rulesetObject.setFile_id(ruleMetaFile.getFile_id());
            rulesetObject.setRule_tree(rules);
            return rulesetObject;
         } catch (Exception var16) {
            ContentInfo contentInfo = ContentInfoImpl.getInstance();
            contentInfo.deleteFile(ruleMetaFile.getFile_id());
            logger.error(var16);
            return null;
         }
      } else {
         return null;
      }
   }

   private static RuleSet castJSONtoRuleset(JSONObject rulesetJson) {
      RuleSet ruleset = new RuleSet();
      RuleSetInfo rulesetInfo = RuleSetInfoImpl.getInstance();

      try {
         JSONObject rulesetOptionJson = rulesetJson.getJSONObject("option");
         if (rulesetOptionJson != null) {
            String multiRule = rulesetOptionJson.get("multi_rule").toString();
            if (multiRule.equalsIgnoreCase("true")) {
               ruleset.setMulti_rule(true);
            } else {
               ruleset.setMulti_rule(false);
            }

            if (rulesetOptionJson.has("sync")) {
               JSONObject rulesetSyncJson = rulesetOptionJson.getJSONObject("sync");
               String duration = rulesetSyncJson.get("duration").toString();
               ruleset.setSync_enable(true);
               ruleset.setSync_duration(Long.valueOf(duration));
            } else {
               ruleset.setSync_enable(false);
            }
         }

         ruleset.setRuleset_id(rulesetJson.getString("ruleset_id"));
         ruleset.setName(rulesetJson.getString("name"));
         ruleset.setGroup_id(rulesetJson.getLong("group_id"));
         ruleset.setCreator(SecurityUtils.getLoginUserId());
         ruleset.setDefault_play(rulesetJson.getString("default_content"));
         ruleset.setDevice_type(rulesetJson.getString("device_type"));
         ruleset.setDevice_type_version((float)rulesetJson.getInt("device_type_version"));
         Long organizationId = rulesetInfo.getOrgGroupIdByName(SecurityUtils.getLoginUserOrganization());
         ruleset.setOrganization_id(organizationId);
      } catch (Exception var7) {
         logger.error("[castJSONtoRuleset] failed to parse Ruleset JSON.");
         ruleset = null;
      }

      return ruleset;
   }

   public static void ruleTreeTraversal(Document doc, Element rootElement, JSONArray childrenList) {
      for(int i = 0; i < childrenList.length(); ++i) {
         JSONObject children = (JSONObject)childrenList.get(i);
         Element conditionEl;
         if (children.has("children") && children.getJSONArray("children").length() > 0) {
            conditionEl = doc.createElement("condition");
            if (children.getString("id").equalsIgnoreCase("default")) {
               conditionEl.setAttribute("ref", "default");
            } else {
               conditionEl.setAttribute("ref", children.getString("id"));
            }

            rootElement.appendChild(conditionEl);
            ruleTreeTraversal(doc, conditionEl, children.getJSONArray("children"));
         } else {
            conditionEl = doc.createElement("result");
            rootElement.appendChild(conditionEl);
            conditionEl.setAttribute("ref", children.getString("id"));
            if (i == children.length() - 1) {
               return;
            }
         }
      }

   }

   private static ContentFile createRuleFile(String str, String rulesetId) {
      String CONTENTS_HOME;
      try {
         CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
      } catch (ConfigException var15) {
         logger.error(var15);
         return null;
      }

      String newRuleFileId = UUID.randomUUID().toString().toUpperCase();
      String newRuleFilePath = CONTENTS_HOME + File.separator + newRuleFileId + File.separator + "RuleMetadata.RULE";
      File ruleFolder = SecurityUtils.getSafeFile(CONTENTS_HOME + File.separator + newRuleFileId);
      if (!ruleFolder.exists()) {
         ruleFolder.mkdir();
         logger.info("[MagicInfo_RuleBasedSchedule] createRuleFolder " + newRuleFileId);
      }

      File ruleFile = SecurityUtils.getSafeFile(CONTENTS_HOME + File.separator + newRuleFileId + File.separator + "RuleMetadata.RULE");

      try {
         if (!ruleFile.exists()) {
            ruleFile.createNewFile();
         }

         PrintWriter print = new PrintWriter(ruleFile, "UTF-8");
         print.write(str);
         print.close();
      } catch (Exception var14) {
         logger.error(var14);

         try {
            File delFile = SecurityUtils.getSafeFile(CONTENTS_HOME + File.separator + newRuleFileId + File.separator + "RuleMetadata.RULE");
            if (delFile != null && delFile.exists()) {
               delFile.delete();
            }
         } catch (Exception var13) {
            logger.error(var13);
         }
      }

      try {
         String ruleFileHashCode = FileUtils.getHash(ruleFile);
         ContentFile contentRuleFile = new ContentFile();
         contentRuleFile.setFile_id(newRuleFileId);
         contentRuleFile.setFile_path(newRuleFilePath);
         contentRuleFile.setFile_name("RuleMetadata.RULE");
         contentRuleFile.setCreator_id(SecurityUtils.getLoginUserId());
         contentRuleFile.setHash_code(ruleFileHashCode);
         contentRuleFile.setFile_type("RULE");
         contentRuleFile.setFile_size(SecurityUtils.getSafeFile(newRuleFilePath).length());

         try {
            ContentInfo contentDao = ContentInfoImpl.getInstance();
            contentDao.addFile(contentRuleFile);
            return contentRuleFile;
         } catch (Exception var11) {
            logger.error(var11);
            return null;
         }
      } catch (Exception var12) {
         logger.error(var12);

         try {
            File delFile = SecurityUtils.getSafeFile(CONTENTS_HOME + File.separator + newRuleFileId + File.separator + "RuleMetadata.RULE");
            if (delFile != null && delFile.exists()) {
               delFile.delete();
            }
         } catch (Exception var10) {
            logger.error(var10);
         }

         return null;
      }
   }

   public static List castJSONtoConditions(JSONArray conditionsJSON) {
      RuleSetInfo rulesetInfo = RuleSetInfoImpl.getInstance();
      if (conditionsJSON != null && conditionsJSON.length() != 0) {
         Gson gson = new Gson();
         ArrayList conditions = new ArrayList();

         try {
            Long organizationId = rulesetInfo.getOrgGroupIdByName(SecurityUtils.getLoginUserOrganization());

            for(int i = 0; i < conditionsJSON.length(); ++i) {
               JSONObject conditionJSON = conditionsJSON.getJSONObject(i);
               Condition condition = (Condition)gson.fromJson(conditionJSON.toString(), Condition.class);
               condition.setOrganization_id(organizationId);
               condition.setCreator(SecurityUtils.getLoginUserId());
               condition.setValue(condition.getValues());
               if (condition.getType().equalsIgnoreCase("datalink")) {
                  condition.setDatalinkConfig();
               }

               conditions.add(condition);
            }
         } catch (Exception var8) {
            logger.error("", var8);
            conditions = null;
         }

         return conditions;
      } else {
         return null;
      }
   }

   public static List getDynamicResultContentUsingRegex(String contentsType, String expression, List filterValueList) {
      List contentsIDList = new ArrayList();
      String[] values = new String[4];

      int i;
      for(i = 0; i < filterValueList.size(); ++i) {
         String[] value = null;
         int index = 0;
         if (((Map)filterValueList.get(i)).get("order").toString().equalsIgnoreCase("0")) {
            value = (String[])((String[])((Map)filterValueList.get(i)).get("value"));
            index = 0;
         } else if (((Map)filterValueList.get(i)).get("order").toString().equalsIgnoreCase("1")) {
            value = (String[])((String[])((Map)filterValueList.get(i)).get("value"));
            index = 1;
         } else if (((Map)filterValueList.get(i)).get("order").toString().equalsIgnoreCase("2")) {
            value = (String[])((String[])((Map)filterValueList.get(i)).get("value"));
            index = 2;
         } else if (((Map)filterValueList.get(i)).get("order").toString().equalsIgnoreCase("3")) {
            value = (String[])((String[])((Map)filterValueList.get(i)).get("value"));
            index = 3;
         }

         if (value != null && value.length > 0) {
            values[index] = String.join("|", value);
         }
      }

      for(i = 0; i < values.length; ++i) {
         expression = expression.replace("%{" + i + "}", "(" + values[i] + ")");
      }

      String regex = "";
      if (expression.charAt(expression.length() - 1) == '*') {
         if (expression.charAt(0) == '*') {
            regex = expression.substring(1, expression.length() - 1);
         } else {
            regex = "^" + expression.substring(0, expression.length() - 1);
         }
      } else {
         regex = "^" + expression + "$";
      }

      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();

      try {
         if (contentsType.equalsIgnoreCase("content_name")) {
            contentsIDList = contentInfo.getContentIdListByRegex(regex);
         } else if (contentsType.equalsIgnoreCase("playlist_name")) {
            contentsIDList = playlistInfo.getPlaylistIdListByRegex(regex);
         }
      } catch (SQLException var9) {
         logger.error("", var9);
      }

      return (List)contentsIDList;
   }

   public static List castJSONtoResults(JSONArray resultsJSON) throws SQLException {
      RuleSetInfo rulesetInfo = RuleSetInfoImpl.getInstance();
      if (resultsJSON != null && resultsJSON.length() != 0) {
         ArrayList results = new ArrayList();

         try {
            Long organizationId = rulesetInfo.getOrgGroupIdByName(SecurityUtils.getLoginUserOrganization());

            for(int i = 0; i < resultsJSON.length(); ++i) {
               JSONObject resultJSON = resultsJSON.getJSONObject(i);
               Result result = new Result();
               String contentsType = resultJSON.get("contents_type").toString();
               String type = resultJSON.getString("type").toString();
               result.setResult_id(resultJSON.get("result_id").toString());
               result.setContents_type(contentsType.toUpperCase());
               result.setResult_name(resultJSON.get("result_name").toString());
               result.setDevice_type(resultJSON.get("device_type").toString());
               result.setDevice_type_version((float)resultJSON.getInt("device_type_version"));
               result.setOrganization_id(organizationId);
               result.setCreator(SecurityUtils.getLoginUserId());
               result.setType(type);
               result.setIs_public(resultJSON.getBoolean("is_public"));
               if (resultJSON.has("description") && !resultJSON.isNull("description")) {
                  result.setDescription(resultJSON.getString("description"));
               }

               JSONObject filterJson;
               if (resultJSON.has("option")) {
                  filterJson = resultJSON.getJSONObject("option");
                  if (filterJson.has("default_duration")) {
                     Long defaultDuration = filterJson.getLong("default_duration");
                     result.setDefault_duration(defaultDuration);
                  }
               }

               int j;
               JSONObject filterValueItem;
               if (!type.equalsIgnoreCase("static")) {
                  filterJson = resultJSON.getJSONObject("filter");
                  result.setFilter_sign(filterJson.getString("sign"));
                  result.setFilter_separator(filterJson.getString("separator"));
                  result.setFilter_expression(filterJson.getString("expression"));
                  result.setFilter_expression_type(filterJson.getString("expression_type"));
                  result.setFilter_src_type(filterJson.getString("src_type"));
                  result.setFilter_dst_type(filterJson.getString("dst_type"));
                  JSONArray filterValueList = filterJson.getJSONArray("filter_value_list");

                  for(j = 0; j < filterValueList.length(); ++j) {
                     filterValueItem = (JSONObject)filterValueList.get(j);
                     if (filterValueItem.getInt("order") == 0) {
                        result.setFilter_value_1(filterValueItem.getString("value"));
                     } else if (filterValueItem.getInt("order") == 1) {
                        result.setFilter_value_2(filterValueItem.getString("value"));
                     } else if (filterValueItem.getInt("order") == 2) {
                        result.setFilter_value_3(filterValueItem.getString("value"));
                     } else if (filterValueItem.getInt("order") == 3) {
                        result.setFilter_value_4(filterValueItem.getString("value"));
                     }
                  }
               } else {
                  JSONArray contentsList = resultJSON.getJSONArray("contents");
                  ArrayList contentsIdList = new ArrayList();

                  for(j = 0; j < contentsList.length(); ++j) {
                     filterValueItem = (JSONObject)contentsList.get(j);
                     if (contentsType.equalsIgnoreCase("content")) {
                        contentsIdList.add(filterValueItem.getString("content_id"));
                     } else if (contentsType.equalsIgnoreCase("playlist")) {
                        contentsIdList.add(filterValueItem.getString("playlist_id"));
                     }
                  }

                  result.setContentsIDList(contentsIdList);
               }

               results.add(result);
            }
         } catch (Exception var13) {
         }

         return results;
      } else {
         return null;
      }
   }

   public static String rulesetObjectToJsonString(RuleSet ruleset) {
      Gson gson = new Gson();
      String jsonString = gson.toJson(ruleset);
      new JsonParser();
      Map map = (Map)gson.fromJson(jsonString, Map.class);
      Map option = new LinkedHashMap();
      option.put("multi_rule", map.get("multi_rule"));
      map.put("option", option);
      map.put("default_content", map.get("default_play"));
      return gson.toJson(map);
   }

   public static JSONObject getJsonObjectFromMap(Map map) {
      JSONObject jsonObject = new JSONObject();
      Iterator var2 = map.entrySet().iterator();

      while(var2.hasNext()) {
         Entry entry = (Entry)var2.next();
         String key = (String)entry.getKey();
         Object value = entry.getValue();
         jsonObject.put(key, value);
      }

      return jsonObject;
   }

   private static StringBuffer makePlaylistContentXml(PlaylistContent pContent, Document doc, Element playlistEl) {
      StringBuffer stringBuffer = new StringBuffer("");

      try {
         ContentInfo contentDao = ContentInfoImpl.getInstance();
         Element contentEl = doc.createElement("content");
         Element contentIdEl = doc.createElement("content_id");
         contentIdEl.appendChild(doc.createTextNode(pContent.getContent_id()));
         contentEl.appendChild(contentIdEl);
         String duration = pContent.getContent_duration().toString();
         Element durationEl = doc.createElement("duration");
         if (pContent.getContent_duration_milli() != null && !pContent.getContent_duration_milli().equals("")) {
            duration = duration + "." + pContent.getContent_duration_milli();
         }

         durationEl.appendChild(doc.createTextNode(duration));
         contentEl.appendChild(durationEl);
         Element startDateEl = doc.createElement("start_date");
         if (pContent.getStart_date() != null) {
            String str_start_date = DateUtils.timestamp2StringDate(pContent.getStart_date());
            startDateEl.appendChild(doc.createTextNode(str_start_date));
         }

         contentEl.appendChild(startDateEl);
         Element expiredDateEl = doc.createElement("expired_date");
         if (pContent.getExpired_date() != null) {
            String str_expired_date = DateUtils.timestamp2StringDate(pContent.getExpired_date());
            expiredDateEl.appendChild(doc.createTextNode(str_expired_date));
         }

         contentEl.appendChild(expiredDateEl);
         ContentFile cfile = contentDao.getSfiFileInfo(pContent.getContent_id());
         if (cfile == null) {
            cfile = contentDao.getMainFileInfo(pContent.getContent_id());
         }

         Content c = contentDao.getThumbInfoOfActiveVersion(pContent.getContent_id());
         if (cfile != null) {
            Element fileEl = doc.createElement("file");
            Element fileIdEl = doc.createElement("id");
            fileIdEl.appendChild(doc.createTextNode(cfile.getFile_id()));
            Element fileNameEl = doc.createElement("name");
            fileNameEl.appendChild(doc.createTextNode(cfile.getFile_name()));
            Element fileSizeEl = doc.createElement("size");
            fileSizeEl.appendChild(doc.createTextNode(cfile.getFile_size().toString()));
            Element fileHashEl = doc.createElement("hash");
            fileHashEl.appendChild(doc.createTextNode(cfile.getHash_code()));
            Element fileThumbIdEl = doc.createElement("thumb_id");
            if (c != null && c.getThumb_file_id() != null) {
               fileThumbIdEl.appendChild(doc.createTextNode(c.getThumb_file_id()));
            }

            fileEl.appendChild(fileIdEl);
            fileEl.appendChild(fileNameEl);
            fileEl.appendChild(fileSizeEl);
            fileEl.appendChild(fileHashEl);
            fileEl.appendChild(fileThumbIdEl);
            contentEl.appendChild(fileEl);
         }

         playlistEl.appendChild(contentEl);
      } catch (Exception var19) {
         logger.error("", var19);
      }

      return stringBuffer;
   }

   public static ContentFile updateContentInRuleset(String rulesetId, String contentId, String type) {
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      RuleSetInfo rulesetInfo = RuleSetInfoImpl.getInstance();
      type = type.toLowerCase();

      try {
         RuleSet ruleset = rulesetInfo.getRuleset(rulesetId);
         String rulesetFilePath = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home" + File.separator + ruleset.getFile_id() + File.separator + "RuleMetadata.RULE";
         DocumentBuilderFactory factory = DocumentUtils.getDocumentBuilderFactoryInstance();
         factory.setIgnoringElementContentWhitespace(true);
         DocumentBuilder builder = factory.newDocumentBuilder();
         Document document = builder.parse(rulesetFilePath);
         XPath xPath = XPathFactory.newInstance().newXPath();
         String expression = "/ruleset/contents/" + type + "[@id='" + contentId + "']";
         Node contentNode = (Node)xPath.compile(expression).evaluate(document, XPathConstants.NODE);
         Node contentsNode = contentNode.getParentNode();
         contentsNode.removeChild(contentNode);
         Element playlistEl;
         Element contentIdEl;
         if (type.equals("content")) {
            Content content = contentInfo.getContentActiveVerInfo(contentId);
            Content contentThumb = contentInfo.getThumbInfoOfActiveVersion(contentId);
            ContentFile contentFile = contentInfo.getSfiFileInfo(contentId);
            if (contentFile == null) {
               contentFile = contentInfo.getMainFileInfo(contentId);
            }

            if (contentFile != null && content != null) {
               playlistEl = document.createElement("content");
               playlistEl.setAttribute("id", contentId);
               playlistEl.setAttribute("content_name", content.getContent_name());
               contentIdEl = document.createElement("content_id");
               contentIdEl.appendChild(document.createTextNode(contentId));
               Element contentDurationEl = document.createElement("duration");
               if (content.getPlay_time() != null && !content.getPlay_time().equals("")) {
                  String duration = String.valueOf(ContentUtils.getPlayTimeStr(content.getPlay_time()));
                  if (content.getPlay_time_milli() != null && !content.getPlay_time_milli().equals("")) {
                     duration = duration + "." + content.getPlay_time_milli();
                  }

                  contentDurationEl.appendChild(document.createTextNode(duration));
               }

               Element startDateEl = document.createElement("start_date");
               Element expiredDateEl = document.createElement("expired_date");
               Element fileEl = document.createElement("file");
               playlistEl.appendChild(contentIdEl);
               playlistEl.appendChild(contentDurationEl);
               playlistEl.appendChild(startDateEl);
               playlistEl.appendChild(expiredDateEl);
               Element fileIdEl = document.createElement("id");
               fileIdEl.appendChild(document.createTextNode(contentFile.getFile_id()));
               Element fileNameEl = document.createElement("name");
               fileNameEl.appendChild(document.createTextNode(contentFile.getFile_name()));
               Element fileSizeEl = document.createElement("size");
               fileSizeEl.appendChild(document.createTextNode(contentFile.getFile_size().toString()));
               Element fileHashEl = document.createElement("hash");
               fileHashEl.appendChild(document.createTextNode(contentFile.getHash_code()));
               Element fileThumbIdEl = document.createElement("thumb_id");
               if (contentThumb != null && contentThumb.getThumb_file_id() != null) {
                  fileThumbIdEl.appendChild(document.createTextNode(contentThumb.getThumb_file_id()));
               }

               fileEl.appendChild(fileIdEl);
               fileEl.appendChild(fileNameEl);
               fileEl.appendChild(fileSizeEl);
               fileEl.appendChild(fileHashEl);
               fileEl.appendChild(fileThumbIdEl);
               playlistEl.appendChild(fileEl);
               contentsNode.appendChild(playlistEl);
            }
         } else if (type.equals("playlist")) {
            PlaylistInfo playlistDao = PlaylistInfoImpl.getInstance();
            Playlist playlist = playlistDao.getPlaylistActiveVerInfo(contentId);
            List contentList = playlistDao.getActiveVerContentList(contentId);
            if (playlist != null) {
               playlistEl = document.createElement("playlist");
               playlistEl.setAttribute("id", contentId);
               playlistEl.setAttribute("playlist_name", playlist.getPlaylist_name());
               contentIdEl = document.createElement("shuffle");
               contentIdEl.appendChild(document.createTextNode(playlist.getIs_shuffle()));

               for(int j = 0; j < contentList.size(); ++j) {
                  PlaylistContent pContent = (PlaylistContent)contentList.get(j);
                  if (pContent.getIs_sub_playlist()) {
                     List subContentList = playlistDao.getActiveVerContentList(pContent.getContent_id());
                     if (subContentList != null && subContentList.size() > 0) {
                        Iterator var40 = subContentList.iterator();

                        while(var40.hasNext()) {
                           PlaylistContent subContent = (PlaylistContent)var40.next();
                           makePlaylistContentXml(subContent, document, playlistEl);
                        }
                     }
                  } else {
                     makePlaylistContentXml(pContent, document, playlistEl);
                  }
               }

               contentsNode.appendChild(playlistEl);
            }
         }

         TransformerFactory tf = DocumentUtils.getTransformerFactoryInstance();
         Transformer transformer = tf.newTransformer();
         transformer.setOutputProperty("omit-xml-declaration", "yes");
         StringWriter writer = new StringWriter();
         transformer.transform(new DOMSource(document), new StreamResult(writer));
         String output = writer.getBuffer().toString();
         return createRuleFile(output, rulesetId);
      } catch (Exception var28) {
         logger.error(var28);
         return null;
      }
   }

   public static String editRulesetByJsonString(String ruleset, String conditions, String contents, String rules, String results, boolean isTriggering) throws Exception {
      RuleSet rulesetObject = createNewRulesetAndFile(ruleset, conditions, contents, rules, results);

      try {
         if (editRulesetAndDeleteOldFile(isTriggering, rulesetObject)) {
            return rulesetObject.getRuleset_id();
         }
      } catch (Exception var8) {
         logger.error("", var8);
      }

      return "";
   }

   public static Boolean editRulesetAndDeleteOldFile(boolean isTriggering, RuleSet rulesetObject) {
      RuleSetInfo rulesetInfo = RuleSetInfoImpl.getInstance();
      if (rulesetObject != null) {
         String oldFileId = "";

         ContentInfoImpl contentInfo;
         try {
            RuleSet originalRuleset = rulesetInfo.getRuleset(rulesetObject.getRuleset_id());
            if (isTriggering) {
               rulesetObject.setCreator(originalRuleset.getCreator());
            }

            oldFileId = originalRuleset.getFile_id();
            if (rulesetInfo.editRuleset(rulesetObject)) {
               try {
                  if (StringUtils.isNotBlank(oldFileId)) {
                     contentInfo = ContentInfoImpl.getInstance();
                     contentInfo.deleteFile(oldFileId);
                  }
               } catch (Exception var7) {
                  logger.error(var7);
               }

               return true;
            }
         } catch (Exception var8) {
            try {
               if (StringUtils.isNotBlank(rulesetObject.getFile_id())) {
                  contentInfo = ContentInfoImpl.getInstance();
                  contentInfo.deleteFile(rulesetObject.getFile_id());
               }
            } catch (Exception var6) {
               logger.error(var6);
            }
         }
      }

      return false;
   }

   public static Long getContentSizeByResultId(Result result) {
      Long contentTotalSize = 0L;

      try {
         RuleSetInfo rulesetInfo = RuleSetInfoImpl.getInstance();
         List contentIdList = rulesetInfo.getContentIdsInResult(result.getResult_id());
         Iterator var5;
         String contentId;
         if (result.getContents_type().equalsIgnoreCase("content")) {
            ContentInfo contentInfo = ContentInfoImpl.getInstance();

            ContentFile contentFile;
            for(var5 = contentIdList.iterator(); var5.hasNext(); contentTotalSize = contentTotalSize + contentFile.getFile_size()) {
               contentId = (String)var5.next();
               contentFile = contentInfo.getSfiFileInfo(contentId);
               if (contentFile == null) {
                  contentFile = contentInfo.getMainFileInfo(contentId);
               }
            }
         } else if (result.getContents_type().equalsIgnoreCase("playlist")) {
            PlaylistInfo playlistDao = PlaylistInfoImpl.getInstance();

            Playlist playlist;
            for(var5 = contentIdList.iterator(); var5.hasNext(); contentTotalSize = contentTotalSize + playlist.getTotal_size()) {
               contentId = (String)var5.next();
               playlist = playlistDao.getPlaylistActiveVerInfo(contentId);
            }
         }
      } catch (SQLException var8) {
         logger.error("", var8);
      }

      return contentTotalSize;
   }

   public static List makeRulesetMapList(List rulesetList) {
      List records = new ArrayList();

      for(int i = 0; i < rulesetList.size(); ++i) {
         HashMap data = new HashMap();
         RuleSet ruleset = (RuleSet)rulesetList.get(i);
         data.put("check", "");
         data.put("ruleset_id", ruleset.getRuleset_id());
         data.put("name", ruleset.getName());
         data.put("creator", ruleset.getCreator());
         data.put("device_type", ruleset.getDevice_type());
         data.put("device_type_version", ruleset.getDevice_type_version());
         data.put("modify_date", ruleset.getModify_date());
         records.add(data);
      }

      return records;
   }
}
