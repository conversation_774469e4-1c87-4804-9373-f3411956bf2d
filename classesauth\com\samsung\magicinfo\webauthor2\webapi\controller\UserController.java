package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.repository.model.UserInfo;
import com.samsung.magicinfo.webauthor2.service.UserService;
import com.samsung.magicinfo.webauthor2.util.UserData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class UserController {
  private UserService userService;
  
  private UserData userData;
  
  @Autowired
  public UserController(UserService userService, UserData userData) {
    this.userService = userService;
    this.userData = userData;
  }
  
  @GetMapping({"/admin"})
  public boolean isUserAdmin() {
    return this.userService.isUserAdmin(this.userData.getUserId());
  }
  
  @GetMapping({"/user/info"})
  public HttpEntity<UserInfo> getUserInfo() {
    UserInfo response = this.userService.getUserInfo(this.userData.getUserId());
    if (response == null)
      return (HttpEntity<UserInfo>)ResponseEntity.notFound().build(); 
    return (HttpEntity<UserInfo>)ResponseEntity.ok(response);
  }
  
  @ExceptionHandler({Exception.class})
  public boolean generalExceptionHandler() {
    return false;
  }
}
