package com.samsung.magicinfo.webauthor2.repository.model.device;

import com.samsung.magicinfo.webauthor2.model.DeviceType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
public class DeviceGroupData {
  @XmlElement(name = "group_id")
  private String id;
  
  @XmlElement(name = "p_group_id")
  private String parentId;
  
  @XmlElement(name = "group_depth")
  private Integer depth = Integer.valueOf(0);
  
  @XmlElement(name = "group_name")
  private String name;
  
  @XmlElement(name = "description")
  private String description;
  
  @XmlElement(name = "group_type")
  private String playerType;
  
  @XmlElement(name = "device_count")
  private Integer deviceCount = Integer.valueOf(0);
  
  @XmlElement(name = "total_count")
  private Integer totalDeviceCount = Integer.valueOf(0);
  
  @XmlElement(name = "vwt_id")
  private String videowallLayoutId = "";
  
  public String getId() {
    return this.id;
  }
  
  public void setId(String id) {
    this.id = id;
  }
  
  public String getParentId() {
    return this.parentId;
  }
  
  public void setParentId(String parentId) {
    this.parentId = parentId;
  }
  
  public Integer getDepth() {
    return this.depth;
  }
  
  public void setDepthId(Integer depth) {
    this.depth = depth;
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public String getDescription() {
    return this.description;
  }
  
  public void setDescription(String description) {
    this.description = description;
  }
  
  public String getPlayerType() {
    return DeviceType.getCompatiblePlayerType(this.playerType);
  }
  
  public void setPlayerType(String playerType) {
    this.playerType = playerType;
  }
  
  public Integer getTotalDeviceCount() {
    return this.totalDeviceCount;
  }
  
  public void setTotalDeviceCount(Integer totalDeviceCount) {
    this.totalDeviceCount = totalDeviceCount;
  }
  
  public Integer getDeviceCount() {
    return this.deviceCount;
  }
  
  public void setDeviceCount(Integer deviceCount) {
    this.deviceCount = deviceCount;
  }
  
  public String getVideowallLayoutId() {
    return this.videowallLayoutId;
  }
  
  public void setVideowallLayoutId(String vwtId) {
    this.videowallLayoutId = vwtId;
  }
  
  public DeviceGroupData(String Id, String parentId, Integer depth, String name, String description, String playerType, Integer deviceCount, Integer totalDeviceCount, String videowallLayoutId) {
    this.id = Id;
    this.parentId = parentId;
    this.depth = depth;
    this.name = name;
    this.description = description;
    this.playerType = playerType;
    this.deviceCount = deviceCount;
    this.totalDeviceCount = totalDeviceCount;
    this.videowallLayoutId = videowallLayoutId;
  }
  
  public DeviceGroupData() {}
}
