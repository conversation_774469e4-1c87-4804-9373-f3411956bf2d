package com.samsung.magicinfo.framework.setup.dao;

import com.samsung.magicinfo.framework.setup.entity.NotificationHistoryEntity;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

public interface NotificationHistoryDaoMapper {
   boolean addHistory(@Param("history") NotificationHistoryEntity var1) throws SQLException;

   boolean addAttachedFile(@Param("fileId") String var1, @Param("fileName") String var2, @Param("filePath") String var3) throws SQLException;

   boolean addHistoryMapIssueId(@Param("historyId") Long var1, @Param("issueId") String var2, @Param("issueName") String var3) throws SQLException;

   boolean addHistoryMapUserId(@Param("historyId") Long var1, @Param("userId") String var2, @Param("email") String var3) throws SQLException;

   List getAllNotificationUserHistory() throws SQLException;

   boolean setUserNotificationHistory(@Param("historyId") Long var1, @Param("userId") String var2, @Param("email") String var3) throws SQLException;

   boolean addHistoryMapAttachedFileId(@Param("historyId") Long var1, @Param("fileId") String var2) throws SQLException;

   int getCount(@Param("condition") Map var1) throws SQLException;

   List getAllNotificationHistoryList(@Param("organization") String var1) throws SQLException;

   List getPagedNotificationHistoryList(@Param("condition") Map var1, @Param("offset") int var2, @Param("pageSize") int var3) throws SQLException;

   List getAttachedFileList(@Param("historyId") Long var1) throws SQLException;

   List getAttachedFileListByTime(@Param("retentionTime") Timestamp var1) throws SQLException;

   int deleteAttachedFile(@Param("retentionTime") Timestamp var1) throws SQLException;

   int deleteHistory(@Param("retentionTime") Timestamp var1) throws SQLException;

   List getGarbageFileList() throws SQLException;

   int deleteGarbageFiles() throws SQLException;

   List getReceiverList(@Param("historyId") Long var1) throws SQLException;

   List getReceiverIDList(@Param("historyId") Long var1) throws SQLException;

   NotificationHistoryEntity getNotificationHistoryInfo(Long var1) throws SQLException;
}
