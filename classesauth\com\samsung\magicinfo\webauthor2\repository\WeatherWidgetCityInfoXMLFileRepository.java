package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.model.weather.CityData;
import com.samsung.magicinfo.webauthor2.model.weather.Country;
import com.samsung.magicinfo.webauthor2.model.weather.CountryData;
import java.util.List;

public interface WeatherWidgetCityInfoXMLFileRepository {
  List<Country> getCountryList();
  
  List<CountryData> getCountryDataList();
  
  List<CityData> getCitiesForCountryIndex(int paramInt);
}
