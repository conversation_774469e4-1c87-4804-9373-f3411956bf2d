package com.samsung.magicinfo.framework.content.manager;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.XPathQueryBuilder;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.ConvertData;
import com.samsung.magicinfo.framework.content.entity.ConvertTable;
import com.samsung.magicinfo.framework.content.entity.ConvertTableMap;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.Text;
import org.xml.sax.SAXException;

public class XPathContentEditorImpl implements ContentXmlEditorInfo {
   Logger logger = LoggingManagerV2.getLogger(XPathContentEditorImpl.class);

   public XPathContentEditorImpl() {
      super();
   }

   private boolean writeFile(String fileName, Document doc) {
      Element datalinkElement = doc.getDocumentElement();
      String newVersion = Integer.parseInt(datalinkElement.getAttribute("version")) + 1 + "";
      datalinkElement.setAttribute("version", newVersion);
      TransformerFactory factory2 = DocumentUtils.getTransformerFactoryInstance();

      try {
         Transformer trans = factory2.newTransformer();
         trans.setOutputProperty("method", "xml");
         trans.setOutputProperty("encoding", "UTF-8");
         trans.setOutputProperty("indent", "yes");
         trans.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "10");
         File output = SecurityUtils.getSafeFile(fileName);
         FileOutputStream fileStream = new FileOutputStream(output);
         OutputStreamWriter writer = new OutputStreamWriter(fileStream, "UTF-8");
         trans.transform(new DOMSource(doc), new StreamResult(writer));
      } catch (TransformerConfigurationException var10) {
         this.logger.error("", var10);
      } catch (TransformerException var11) {
         this.logger.error("", var11);
      } catch (Exception var12) {
         this.logger.error("", var12);
      }

      return true;
   }

   private Document getDocumentByFile(String fileName) {
      Document doc = null;
      FileInputStream inputStreamTemplate = null;

      try {
         DocumentBuilderFactory docFctory = DocumentUtils.getDocumentBuilderFactoryInstance();
         DocumentBuilder builder = docFctory.newDocumentBuilder();
         inputStreamTemplate = new FileInputStream(SecurityUtils.directoryTraversalChecker(fileName, (String)null));
         doc = builder.parse(inputStreamTemplate);
      } catch (FileNotFoundException var20) {
         this.logger.error("", var20);
      } catch (SAXException var21) {
         this.logger.error("", var21);
      } catch (IOException var22) {
         this.logger.error("", var22);
      } catch (ParserConfigurationException var23) {
         this.logger.error("", var23);
      } finally {
         try {
            if (inputStreamTemplate != null) {
               inputStreamTemplate.close();
               inputStreamTemplate = null;
            }
         } catch (Exception var19) {
            this.logger.error("", var19);
         }

      }

      return doc;
   }

   public boolean deleteConvertTable(String oldFileName, String newFileName, Map convertDataMap) {
      boolean bResult = false;

      try {
         Document doc = this.getDocumentByFile(oldFileName);
         XPathFactory factory = XPathFactory.newInstance();
         XPath xpath = factory.newXPath();
         XPathExpression expr = null;
         Object result = null;
         convertDataMap.get("DLK_CONTENT_ID");
         convertDataMap.get("DLK_VERSION_ID");
         long pageNo = (Long)convertDataMap.get("PAGE_NO");
         String elementName = (String)convertDataMap.get("ELEMENT_NAME");
         long splitGroupId = (Long)convertDataMap.get("SPLIT_GROUP_ID");
         String splitGroupName = (String)convertDataMap.get("SPLIT_GROUP_NAME");
         convertDataMap.get("CONVERT_TABLE");
         if (splitGroupId == 0L) {
            expr = xpath.compile((new XPathQueryBuilder("//DataLinkContentMeta/Page[@no=:PAGE_NO]//SplitGroup[@id=:SPLIT_GROUP_ID]//Element[Name=:ELEMENT_NAME]//Data/ConvertTable")).setLiteral("PAGE_NO", pageNo).setLiteral("SPLIT_GROUP_ID", splitGroupId).setLiteral("ELEMENT_NAME", elementName).build());
         } else if (splitGroupId > 0L) {
            expr = xpath.compile((new XPathQueryBuilder("//DataLinkContentMeta/Page[@no=:PAGE_NO]//SplitGroup[@id=:SPLIT_GROUP_ID and Name=:SPLIT_GROUP_NAME]//Data/ConvertTable")).setLiteral("PAGE_NO", pageNo).setLiteral("SPLIT_GROUP_ID", splitGroupId).setLiteral("SPLIT_GROUP_NAME", splitGroupName).build());
         }

         if (expr != null) {
            result = expr.evaluate(doc, XPathConstants.NODESET);
         }

         NodeList convertTableList = (NodeList)result;
         if (convertTableList != null) {
            for(int convertTableListSize = convertTableList.getLength(); convertTableListSize > 0; --convertTableListSize) {
               Node parent = convertTableList.item(0).getParentNode();
               if (parent != null) {
                  parent.removeChild(convertTableList.item(0));
               }
            }
         }

         bResult = this.writeFile(newFileName, doc);
      } catch (XPathExpressionException var19) {
         this.logger.error("", var19);
      }

      return bResult;
   }

   public boolean deleteConvertTable(String oldFileName, String newFileName, ConvertTableMap convertTableMap) {
      boolean bResult = false;

      try {
         Document doc = this.getDocumentByFile(oldFileName);
         XPathFactory factory = XPathFactory.newInstance();
         XPath xpath = factory.newXPath();
         XPathExpression expr = null;
         Object result = null;
         if (convertTableMap.getSplit_group_id() == 0) {
            expr = xpath.compile((new XPathQueryBuilder("//DataLinkContentMeta/Page[@no=:PAGE_NO]//SplitGroup[@id=:SPLIT_GROUP_ID]//Element[Name=:ELEMENT_NAME]//Data[:DATA_INDEX]/ConvertTable")).setLiteral("PAGE_NO", convertTableMap.getPage_no()).setLiteral("SPLIT_GROUP_ID", convertTableMap.getSplit_group_id()).setLiteral("ELEMENT_NAME", convertTableMap.getElement_name()).setLiteral("DATA_INDEX", convertTableMap.getData_index()).build());
         } else if (convertTableMap.getSplit_group_id() > 0) {
            expr = xpath.compile((new XPathQueryBuilder("//DataLinkContentMeta/Page[@no=:PAGE_NO]//SplitGroup[@id=:SPLIT_GROUP_ID and Name=:SPLIT_GROUP_NAME]//Data[:DATA_INDEX]/ConvertTable")).setLiteral("PAGE_NO", convertTableMap.getPage_no()).setLiteral("SPLIT_GROUP_ID", convertTableMap.getSplit_group_id()).setLiteral("SPLIT_GROUP_NAME", convertTableMap.getSplit_group_name()).setLiteral("DATA_INDEX", convertTableMap.getData_index()).build());
         }

         if (expr != null) {
            result = expr.evaluate(doc, XPathConstants.NODESET);
         }

         NodeList convertTableList = (NodeList)result;
         if (convertTableList != null) {
            for(int convertTableListSize = convertTableList.getLength(); convertTableListSize > 0; --convertTableListSize) {
               Node parent = convertTableList.item(0).getParentNode();
               if (parent != null) {
                  parent.removeChild(convertTableList.item(0));
               }
            }
         }

         bResult = this.writeFile(newFileName, doc);
      } catch (XPathExpressionException var13) {
         this.logger.error("", var13);
      }

      return bResult;
   }

   public boolean deleteConvertData(String oldFileName, String newFileName, Map convertDataMap, String[] fromData) {
      boolean bResult = false;

      try {
         Document doc = this.getDocumentByFile(oldFileName);
         XPathFactory factory = XPathFactory.newInstance();
         XPath xpath = factory.newXPath();
         XPathExpression expr = null;
         Object result = null;
         long pageNo = (Long)convertDataMap.get("PAGE_NO");
         String elementName = (String)convertDataMap.get("ELEMENT_NAME");
         long splitGroupId = (Long)convertDataMap.get("SPLIT_GROUP_ID");
         String splitGroupName = (String)convertDataMap.get("SPLIT_GROUP_NAME");
         Map queryParams = new HashMap();
         int fromDataSize = fromData.length;
         StringBuffer fromListQueryFragment = new StringBuffer();

         for(int fromI = 0; fromI < fromDataSize; ++fromI) {
            String placeholderName = "FROM_LIST_ITEM_" + fromI;
            queryParams.put(placeholderName, fromData[fromI]);
            fromListQueryFragment.append("From=:").append(placeholderName);
            if (fromI < fromDataSize - 1) {
               fromListQueryFragment.append(" or ");
            }
         }

         Iterator var22;
         Entry entry;
         XPathQueryBuilder queryBuilder;
         String query;
         if (splitGroupId == 0L) {
            query = "//DataLinkContentMeta/Page[@no=:PAGE_NO]//SplitGroup[@id=:SPLIT_GROUP_ID]//Element[Name=:ELEMENT_NAME]/Data/ConvertTable/Row[" + fromListQueryFragment.toString() + "]";
            queryBuilder = (new XPathQueryBuilder(query)).setLiteral("PAGE_NO", pageNo).setLiteral("SPLIT_GROUP_ID", splitGroupId).setLiteral("ELEMENT_NAME", elementName);
            var22 = queryParams.entrySet().iterator();

            while(var22.hasNext()) {
               entry = (Entry)var22.next();
               queryBuilder.setLiteral((String)entry.getKey(), (String)entry.getValue());
            }

            expr = xpath.compile(queryBuilder.build());
         } else if (splitGroupId > 0L) {
            query = "//DataLinkContentMeta/Page[@no=:PAGE_NO]//SplitGroup[@id=:SPLIT_GROUP_ID and Name=:SPLIT_GROUP_NAME]//Data/ConvertTable/Row[" + fromListQueryFragment.toString() + "]";
            queryBuilder = (new XPathQueryBuilder(query)).setLiteral("PAGE_NO", pageNo).setLiteral("SPLIT_GROUP_ID", splitGroupId).setLiteral("SPLIT_GROUP_NAME", splitGroupName);
            var22 = queryParams.entrySet().iterator();

            while(var22.hasNext()) {
               entry = (Entry)var22.next();
               queryBuilder.setLiteral((String)entry.getKey(), (String)entry.getValue());
            }

            expr = xpath.compile(queryBuilder.build());
         }

         if (expr != null) {
            result = expr.evaluate(doc, XPathConstants.NODESET);
         }

         NodeList nodeList = (NodeList)result;
         if (nodeList != null) {
            int nodeListSize = nodeList.getLength();

            for(var22 = null; nodeListSize > 0; --nodeListSize) {
               Node parent = nodeList.item(0).getParentNode();
               if (parent != null) {
                  parent.removeChild(nodeList.item(0));
               }
            }
         }

         bResult = this.writeFile(newFileName, doc);
      } catch (XPathExpressionException var24) {
         this.logger.error("", var24);
      }

      return bResult;
   }

   public boolean modifyConvertData(String oldFileName, String newFileName, Map convertDataMap, String oldFromData, String newFromData, String newToData, String listType) {
      boolean bResult = false;

      try {
         Document doc = this.getDocumentByFile(oldFileName);
         XPathFactory factory = XPathFactory.newInstance();
         XPath xpath = factory.newXPath();
         XPathExpression expr = null;
         Object result = null;
         long pageNo = (Long)convertDataMap.get("PAGE_NO");
         String elementName = (String)convertDataMap.get("ELEMENT_NAME");
         long splitGroupId = (Long)convertDataMap.get("SPLIT_GROUP_ID");
         String splitGroupName = (String)convertDataMap.get("SPLIT_GROUP_NAME");
         if (splitGroupId == 0L) {
            expr = xpath.compile((new XPathQueryBuilder("//DataLinkContentMeta/Page[@no=:PAGE_NO]//SplitGroup[@id=:SPLIT_GROUP_ID]/Element[Name=:ELEMENT_NAME]/Data/ConvertTable/Row[From=:OLD_FROM_DATA]")).setLiteral("PAGE_NO", pageNo).setLiteral("SPLIT_GROUP_ID", splitGroupId).setLiteral("ELEMENT_NAME", elementName).setLiteral("OLD_FROM_DATA", oldFromData).build());
         } else if (splitGroupId > 0L) {
            expr = xpath.compile((new XPathQueryBuilder("//DataLinkContentMeta/Page[@no=:PAGE_NO]//SplitGroup[@id=:SPLIT_GROUP_ID and Name=:SPLIT_GROUP_NAME]//Data/ConvertTable/Row[From=:OLD_FROM_DATA]")).setLiteral("PAGE_NO", pageNo).setLiteral("SPLIT_GROUP_ID", splitGroupId).setLiteral("SPLIT_GROUP_NAME", splitGroupName).setLiteral("OLD_FROM_DATA", oldFromData).build());
         }

         if (expr != null) {
            result = expr.evaluate(doc, XPathConstants.NODESET);
         }

         NodeList nodeList = (NodeList)result;
         if (nodeList != null) {
            for(int nodeI = 0; nodeI < nodeList.getLength(); ++nodeI) {
               Node rowNode = nodeList.item(nodeI);
               NodeList rowChildList = rowNode.getChildNodes();

               for(int rowChildListSize = rowChildList.getLength(); rowChildListSize > 0; --rowChildListSize) {
                  rowNode.removeChild(rowChildList.item(0));
               }

               Element rowElement = (Element)rowNode;
               Element fromElement = doc.createElement("From");
               Text fromText = doc.createTextNode(newFromData);
               fromElement.appendChild(fromText);
               Element toElement = doc.createElement("To");
               if (listType.equalsIgnoreCase("TEXT")) {
                  Text toText = doc.createTextNode(newToData);
                  toElement.appendChild(toText);
               } else {
                  ContentInfo contentInfo = ContentInfoImpl.getInstance();
                  ContentFile contentFile = contentInfo.getMainFileInfo(newToData);
                  Element fileInfoElement = doc.createElement("FileInfo");
                  String fileName = contentFile.getFile_id() + "\\" + contentFile.getFile_name();
                  Text toText = doc.createTextNode(fileName);
                  toElement.appendChild(toText);
                  fileInfoElement.setAttribute("filesize", Long.toString(contentFile.getFile_size()));
                  Text fileInfoText = doc.createTextNode(fileName);
                  fileInfoElement.appendChild(fileInfoText);
                  toElement.appendChild(fileInfoElement);
               }

               rowElement.appendChild(fromElement);
               rowElement.appendChild(toElement);
            }
         }

         bResult = this.writeFile(newFileName, doc);
      } catch (XPathExpressionException var35) {
         this.logger.error("", var35);
      } catch (SQLException var36) {
         this.logger.error("", var36);
      }

      return bResult;
   }

   public boolean modifyConvertData(String oldFileName, String newFileName, ConvertTableMap convertTableMap, ConvertTable oldConvertTable, ConvertTable newConvertTable) {
      boolean bResult = false;

      try {
         Document doc = this.getDocumentByFile(oldFileName);
         XPathFactory factory = XPathFactory.newInstance();
         XPath xpath = factory.newXPath();
         XPathExpression expr = null;
         Object result = null;
         if (convertTableMap.getSplit_group_id() == -1) {
            expr = xpath.compile((new XPathQueryBuilder("//DataLinkContentMeta/Page[@no=:PAGE_NO]//Element[Name=:ELEMENT_NAME]/Data[:DATA_INDEX]/ConvertTable")).setLiteral("PAGE_NO", convertTableMap.getPage_no()).setLiteral("ELEMENT_NAME", convertTableMap.getElement_name()).setLiteral("DATA_INDEX", convertTableMap.getData_index()).build());
         } else if (convertTableMap.getSplit_group_id() == 0) {
            expr = xpath.compile((new XPathQueryBuilder("//DataLinkContentMeta/Page[@no=:PAGE_NO]//SplitGroup[@id=:SPLIT_GROUP_ID]/Element[Name=:ELEMENT_NAME]/Data[:DATA_INDEX]/ConvertTable")).setLiteral("PAGE_NO", convertTableMap.getPage_no()).setLiteral("SPLIT_GROUP_ID", convertTableMap.getSplit_group_id()).setLiteral("ELEMENT_NAME", convertTableMap.getElement_name()).setLiteral("DATA_INDEX", convertTableMap.getData_index()).build());
         } else if (convertTableMap.getSplit_group_id() > 0) {
            expr = xpath.compile((new XPathQueryBuilder("//DataLinkContentMeta/Page[@no=:PAGE_NO]//SplitGroup[@id=:SPLIT_GROUP_ID and Name=:SPLIT_GROUP_NAME]//Data[:DATA_INDEX]/ConvertTable")).setLiteral("PAGE_NO", convertTableMap.getPage_no()).setLiteral("SPLIT_GROUP_ID", convertTableMap.getSplit_group_id()).setLiteral("SPLIT_GROUP_NAME", convertTableMap.getSplit_group_name()).setLiteral("DATA_INDEX", convertTableMap.getData_index()).build());
         }

         if (expr != null) {
            result = expr.evaluate(doc, XPathConstants.NODESET);
         }

         NodeList nodeList = (NodeList)result;
         if (nodeList != null) {
            Node convertTableNode = nodeList.item(0);
            NodeList rowList = convertTableNode.getChildNodes();

            for(int rowListSize = rowList.getLength(); rowListSize > 0; --rowListSize) {
               convertTableNode.removeChild(rowList.item(0));
            }

            Iterator var16 = newConvertTable.getConvertDataList().iterator();

            while(var16.hasNext()) {
               ConvertData convertData = (ConvertData)var16.next();
               Element rowElement = doc.createElement("Row");
               Element fromElement = doc.createElement("From");
               Text fromText = doc.createTextNode(convertData.getFrom_data());
               fromElement.appendChild(fromText);
               Element toElement = doc.createElement("To");
               if ("TEXT".equalsIgnoreCase(newConvertTable.getConvert_type())) {
                  Text toText = doc.createTextNode(convertData.getTo_data());
                  toElement.appendChild(toText);
               } else {
                  ContentInfo contentInfo = ContentInfoImpl.getInstance();
                  ContentFile contentFile = contentInfo.getMainFileInfo(convertData.getTo_data());
                  Element fileInfoElement = doc.createElement("FileInfo");
                  String fileName = contentFile.getFile_id() + "\\" + contentFile.getFile_name();
                  Text toText = doc.createTextNode(fileName);
                  toElement.appendChild(toText);
                  fileInfoElement.setAttribute("filesize", Long.toString(contentFile.getFile_size()));
                  Text fileInfoText = doc.createTextNode(fileName);
                  fileInfoElement.appendChild(fileInfoText);
                  toElement.appendChild(fileInfoElement);
                  if (convertData.getConvert_type() != null && convertData.getConvert_type().equalsIgnoreCase("MEDIASLIDE")) {
                     String mediaType = "";
                     if (contentFile.getMedia_type() != null) {
                        mediaType = contentFile.getMedia_type();
                     }

                     if (mediaType.equalsIgnoreCase("MOVIE")) {
                        mediaType = "video";
                     }

                     rowElement.setAttribute("type", mediaType.toLowerCase());
                  }
               }

               rowElement.appendChild(fromElement);
               rowElement.appendChild(toElement);
               convertTableNode.appendChild(rowElement);
            }
         }

         bResult = this.writeFile(newFileName, doc);
      } catch (XPathExpressionException var29) {
         this.logger.error("", var29);
      } catch (SQLException var30) {
         this.logger.error("", var30);
      }

      return bResult;
   }

   public boolean addConvertData(String oldFileName, String newFileName, Map convertDataMap, String newFromData, String newToData, String listType) {
      boolean bResult = false;
      this.logger.info("oldFileName = " + oldFileName);
      this.logger.info("newFileName = " + newFileName);
      this.logger.info("newFromData = " + newFromData);
      this.logger.info("newToData = " + newToData);

      try {
         Document doc = this.getDocumentByFile(oldFileName);
         XPathFactory factory = XPathFactory.newInstance();
         XPath xpath = factory.newXPath();
         XPathExpression expr = null;
         Object result = null;
         long pageNo = (Long)convertDataMap.get("PAGE_NO");
         String elementName = (String)convertDataMap.get("ELEMENT_NAME");
         long splitGroupId = (Long)convertDataMap.get("SPLIT_GROUP_ID");
         String splitGroupName = (String)convertDataMap.get("SPLIT_GROUP_NAME");
         if (splitGroupId == 0L) {
            expr = xpath.compile((new XPathQueryBuilder("//DataLinkContentMeta/Page[@no=:PAGE_NO]//SplitGroup[@id=:SPLIT_GROUP_ID]/Element[Name=:ELEMENT_NAME]/Data/ConvertTable")).setLiteral("PAGE_NO", pageNo).setLiteral("SPLIT_GROUP_ID", splitGroupId).setLiteral("ELEMENT_NAME", elementName).build());
         } else if (splitGroupId > 0L) {
            expr = xpath.compile((new XPathQueryBuilder("//DataLinkContentMeta/Page[@no=:PAGE_NO]//SplitGroup[@id=:SPLIT_GROUP_ID and Name=:SPLIT_GROUP_NAME]//Data/ConvertTable")).setLiteral("PAGE_NO", pageNo).setLiteral("SPLIT_GROUP_ID", splitGroupId).setLiteral("SPLIT_GROUP_NAME", splitGroupName).build());
         }

         if (expr != null) {
            result = expr.evaluate(doc, XPathConstants.NODESET);
         }

         NodeList nodeList = (NodeList)result;
         if (nodeList != null) {
            int convertNodeListSize = nodeList.getLength();
            this.logger.info("convertNode size = " + convertNodeListSize);

            for(int nodeI = 0; nodeI < convertNodeListSize; ++nodeI) {
               Node convertTableNode = nodeList.item(nodeI);
               Element rowElement = doc.createElement("Row");
               Element fromElement = doc.createElement("From");
               Text fromText = doc.createTextNode(newFromData);
               fromElement.appendChild(fromText);
               Element toElement = doc.createElement("To");
               if (listType.equalsIgnoreCase("TEXT")) {
                  Text toText = doc.createTextNode(newToData);
                  toElement.appendChild(toText);
               } else {
                  ContentInfo contentInfo = ContentInfoImpl.getInstance();
                  ContentFile contentFile = contentInfo.getMainFileInfo(newToData);
                  Element fileInfoElement = doc.createElement("FileInfo");
                  String fileName = contentFile.getFile_id() + "\\" + contentFile.getFile_name();
                  Text toText = doc.createTextNode(fileName);
                  toElement.appendChild(toText);
                  fileInfoElement.setAttribute("filesize", Long.toString(contentFile.getFile_size()));
                  Text fileInfoText = doc.createTextNode(fileName);
                  fileInfoElement.appendChild(fileInfoText);
                  toElement.appendChild(fileInfoElement);
               }

               rowElement.appendChild(fromElement);
               rowElement.appendChild(toElement);
               convertTableNode.appendChild(rowElement);
            }
         }

         bResult = this.writeFile(newFileName, doc);
      } catch (XPathExpressionException var33) {
         this.logger.error("", var33);
      } catch (SQLException var34) {
         this.logger.error("", var34);
      }

      return bResult;
   }
}
