package com.samsung.magicinfo.openapi.custom.service;

import com.hierynomus.smbj.SMBClient;
import com.hierynomus.smbj.auth.AuthenticationContext;
import com.hierynomus.smbj.connection.Connection;
import com.hierynomus.smbj.session.Session;
import com.hierynomus.smbj.share.DiskShare;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.common.utils.FileUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.FileManagerImpl;
import com.samsung.magicinfo.framework.content.constants.ContentConstants;
import com.samsung.magicinfo.framework.content.dao.ConvertDataDao;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.ContentLog;
import com.samsung.magicinfo.framework.content.entity.ContentReference;
import com.samsung.magicinfo.framework.content.entity.ContentSearch;
import com.samsung.magicinfo.framework.content.entity.ConvertData;
import com.samsung.magicinfo.framework.content.entity.ConvertTable;
import com.samsung.magicinfo.framework.content.entity.ConvertTableMap;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.entity.TemplateElementData;
import com.samsung.magicinfo.framework.content.manager.ContentCodeInfo;
import com.samsung.magicinfo.framework.content.manager.ContentCodeInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentSearchInfo;
import com.samsung.magicinfo.framework.content.manager.ContentSearchInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentXmlEditor;
import com.samsung.magicinfo.framework.content.manager.ContentXmlEditorInfo;
import com.samsung.magicinfo.framework.content.manager.XPathContentEditorImpl;
import com.samsung.magicinfo.framework.monitoring.dao.DownloadStatusDAO;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfo;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.TagInfo;
import com.samsung.magicinfo.framework.setup.manager.TagInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.openapi.auth.TokenRegistry;
import com.samsung.magicinfo.openapi.custom.domain.log.LogIF;
import com.samsung.magicinfo.openapi.custom.domain.log.LogImpl;
import com.samsung.magicinfo.openapi.custom.openEntity.ResultList;
import com.samsung.magicinfo.openapi.custom.openEntity.log.SelectConditionLog;
import com.samsung.magicinfo.openapi.custom.service.builder.ContentDownloaderJnlpStringBuilder;
import com.samsung.magicinfo.openapi.custom.service.builder.ContentEditorJnlpStringBuilder;
import com.samsung.magicinfo.openapi.custom.service.builder.ContentUploaderJnlpStringBuilder;
import com.samsung.magicinfo.openapi.impl.OpenApiExceptionCode;
import com.samsung.magicinfo.openapi.impl.OpenApiParameterValidator;
import com.samsung.magicinfo.openapi.impl.OpenApiServiceException;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.file.CifsFileDownloadThread;
import com.samsung.magicinfo.protocol.file.CifsFilesToDownload;
import com.samsung.magicinfo.protocol.file.FtpFileDownloadThread;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URL;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.logging.log4j.Logger;
import org.apache.xml.security.utils.Base64;
import org.springframework.context.annotation.Scope;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

@Service("openApiCommonContentService")
@Scope("prototype")
public class CommonContentService {
   static Logger logger = LoggingManagerV2.getLogger(CommonContentService.class);
   String token = null;
   private UserContainer user = null;
   private TokenRegistry tokenRegistry = TokenRegistry.getTokenRegistry();

   public CommonContentService() {
      super();
   }

   public String getToken() {
      return this.token;
   }

   public void setToken(String token) {
      this.token = token;
      this.user = (UserContainer)this.tokenRegistry.getUserObject(token);
   }

   @PreAuthorize("hasAnyRole('Content Read Authority', 'Content Schedule Write Authority', 'Content Schedule Add Authority', 'Lite Content Schedule Write Authority', 'Lite Content Schedule Add Authority', 'Videowall Schedule Write Authority', 'Videowall Schedule Add Authority', 'Lite Playlist Write Authority', 'Lite Playlist Manage Authority', 'Playlist Write Authority', 'Playlist Manage Authority')")
   public ResultList getContentList(String userId, ContentSearch condition, String deviceType) throws OpenApiServiceException {
      try {
         UserInfo userInfo = UserInfoImpl.getInstance();
         if (userInfo.getCountByUserId(userId) == 0) {
            throw new OpenApiServiceException(OpenApiExceptionCode.U001[0], OpenApiExceptionCode.U001[1]);
         }
      } catch (SQLException var16) {
         logger.error(var16);
      }

      String deviceTypeVersion = CommonDataConstants.TYPE_VERSION_1_0 + "";
      if (deviceType.equalsIgnoreCase("ALL")) {
         deviceType = "";
      }

      if (deviceType.equalsIgnoreCase("S2PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_2_0 + "";
      } else if (deviceType.equalsIgnoreCase("S3PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_3_0 + "";
      } else if (deviceType.equalsIgnoreCase("S4PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_4_0 + "";
      } else if (deviceType.equalsIgnoreCase("S5PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_5_0 + "";
      } else if (deviceType.equalsIgnoreCase("S6PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_6_0 + "";
      } else if (deviceType.equalsIgnoreCase("S7PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_7_0 + "";
      } else if (deviceType.equalsIgnoreCase("S9PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_9_0 + "";
      } else if (deviceType.equalsIgnoreCase("S10PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_10_0 + "";
      }

      ContentSearchInfo mgr = ContentSearchInfoImpl.getInstance();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      ResultList resultList = new ResultList();
      PagedListInfo pagedList = null;
      ArrayList openContentList = new ArrayList();

      try {
         pagedList = mgr.getContentList(userId, deviceType, deviceTypeVersion, condition);
         List contentList = pagedList.getPagedResultList();
         String tmpStr = null;
         String fileId = null;
         ContentInfo mgr2 = ContentInfoImpl.getInstance();

         for(int i = 0; i < contentList.size(); ++i) {
            Content content = (Content)contentList.get(i);
            tmpStr = content.getContent_name();
            if (tmpStr != null) {
               tmpStr = tmpStr.replace("<", "&lt;").replace(">", "&gt;");
            }

            content.setContent_name(tmpStr);
            content.setGroup_id(contentInfo.getGroupId(content.getContent_id()));
            fileId = content.getMain_file_id();
            content.setMain_file_name(mgr2.getFileName(fileId));
            if (content.getPlay_time() != null && !content.getPlay_time().equals("")) {
               if (content.getPlay_time_milli() != null && !content.getPlay_time_milli().equals("")) {
                  content.setPlay_time(content.getPlay_time() + "." + content.getPlay_time_milli());
               } else {
                  content.setPlay_time(content.getPlay_time() + ".0");
               }
            }

            openContentList.add(content);
         }

         resultList.setResultList(openContentList);
         resultList.setTotalCount(pagedList.getTotalRowCount());
      } catch (Exception var17) {
         logger.error(var17);
      }

      return resultList;
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public ResultList getContentListByUser(String userId) throws OpenApiServiceException {
      ContentInfo mgr = ContentInfoImpl.getInstance();
      List contentList = null;
      ResultList resultList = new ResultList();

      try {
         UserInfo userInfo = UserInfoImpl.getInstance();
         if (userInfo.getCountByUserId(userId) == 0) {
            throw new OpenApiServiceException(OpenApiExceptionCode.U001[0], OpenApiExceptionCode.U001[1]);
         }

         if (userId.equals(this.user.getUser().getUser_id())) {
            contentList = mgr.getAllContentListByUser(userId, true, this.user.getUser().getRoot_group_id());
         } else {
            contentList = mgr.getAllContentListByUser(userId, this.user.checkAuthority("Content Manage"), this.user.getUser().getRoot_group_id());
         }

         new Content();
         String fileId = null;

         for(int i = 0; i < contentList.size(); ++i) {
            Content content = (Content)contentList.get(i);
            fileId = content.getMain_file_id();
            content.setMain_file_name(mgr.getFileName(fileId));
            fileId = content.getThumb_file_id();
            content.setThumb_file_name(mgr.getFileName(fileId));
         }

         resultList.setTotalCount(contentList.size());
         resultList.setResultList(contentList);
      } catch (SQLException var9) {
         logger.error(var9);
      }

      return resultList;
   }

   @PreAuthorize("hasAnyRole('Content Read Authority', 'Content Schedule Write Authority', 'Content Schedule Add Authority', 'Lite Content Schedule Write Authority', 'Lite Content Schedule Add Authority', 'Videowall Schedule Write Authority', 'Videowall Schedule Add Authority', 'Lite Playlist Write Authority', 'Lite Playlist Manage Authority', 'Playlist Write Authority', 'Playlist Manage Authority')")
   public ResultList getContentListByType(String contentType, boolean byAll) throws OpenApiServiceException {
      boolean bContentType = false;

      List contentList;
      String fileId;
      try {
         if (ContentConstants.getMediaTypeForAuthor().contains(contentType)) {
            bContentType = true;
         } else {
            ContentCodeInfo contentCodeInfo = ContentCodeInfoImpl.getInstance();
            contentList = contentCodeInfo.getMediaTypeList();

            for(int i = 0; i < contentList.size(); ++i) {
               Map map = (Map)contentList.get(i);
               fileId = (String)map.get("media_type");
               if (fileId.equals(contentType)) {
                  bContentType = true;
                  break;
               }
            }
         }
      } catch (SQLException var11) {
         logger.error(var11);
      }

      if (!bContentType) {
         throw new OpenApiServiceException(OpenApiExceptionCode.C102[0], OpenApiExceptionCode.C102[1]);
      } else {
         ContentInfo mgr = ContentInfoImpl.getInstance();
         contentList = null;
         ResultList resultList = new ResultList();

         try {
            contentList = mgr.getAllContentListByType(byAll, contentType, this.user.checkAuthority("Content Manage"), this.user.getUser().getUser_id(), this.user.getUser().getRoot_group_id());
            new Content();
            fileId = null;

            for(int i = 0; i < contentList.size(); ++i) {
               Content content = (Content)contentList.get(i);
               fileId = content.getMain_file_id();
               content.setMain_file_name(mgr.getFileName(fileId));
               fileId = content.getThumb_file_id();
               content.setThumb_file_name(mgr.getFileName(fileId));
            }

            resultList.setTotalCount(contentList.size());
            resultList.setResultList(contentList);
         } catch (Exception var10) {
            logger.error(var10);
         }

         return resultList;
      }
   }

   @PreAuthorize("hasRole('Content Read Authority')")
   public ResultList getContentGroupList() throws OpenApiServiceException {
      String userId = this.user.getUser().getUser_id();
      ContentInfo contentMgr = ContentInfoImpl.getInstance();
      ResultList resultList = new ResultList();
      List groupList = new ArrayList();
      Long groupId = -1L;
      Boolean recursive = true;

      try {
         UserInfo userInfo = UserInfoImpl.getInstance();
         if (userInfo.getCountByUserId(userId) == 0) {
            throw new OpenApiServiceException(OpenApiExceptionCode.U001[0], OpenApiExceptionCode.U001[1]);
         }

         List orgGroupList = null;
         Group sourceEntity;
         if (groupId.intValue() == -1) {
            groupId = contentMgr.getRootId(userId);
            sourceEntity = contentMgr.getGroupInfo(groupId);
            groupList.add(sourceEntity);
         }

         if (recursive != null) {
            orgGroupList = contentMgr.getChildGroupList(groupId, recursive, userId);
         } else {
            orgGroupList = contentMgr.getChildGroupList(groupId, false, userId);
         }

         if (orgGroupList != null) {
            sourceEntity = null;

            for(int i = 0; i < orgGroupList.size(); ++i) {
               sourceEntity = (Group)orgGroupList.get(i);
               groupList.add(sourceEntity);
            }

            resultList.setResultList(groupList);
            resultList.setTotalCount(groupList.size());
         }
      } catch (SQLException var11) {
         logger.error(var11);
      }

      return resultList;
   }

   @PreAuthorize("hasRole('Content Read Authority')")
   public Content getContentInfo(String contentId) throws OpenApiServiceException {
      Content result = null;

      try {
         ContentInfo mgr = ContentInfoImpl.getInstance();
         if (!mgr.isExistContentID(contentId)) {
            throw new OpenApiServiceException(OpenApiExceptionCode.C101[0], OpenApiExceptionCode.C101[1]);
         }

         String mediaType = mgr.getMediaTypeByContentId(contentId);
         if (mediaType.equals("TLFD")) {
            result = mgr.getTLFDInfo(contentId);
         } else {
            result = mgr.getContentActiveVerInfo(contentId);
         }

         if (result.getPlay_time() != null && !result.getPlay_time().equals("")) {
            if (result.getPlay_time_milli() != null && !result.getPlay_time_milli().equals("")) {
               result.setPlay_time(result.getPlay_time() + "." + result.getPlay_time_milli());
            } else {
               result.setPlay_time(result.getPlay_time() + ".0");
            }
         }

         String tmpStr = null;
         tmpStr = result.getContent_name();
         if (tmpStr != null) {
            tmpStr = tmpStr.replace("<", "&lt;").replace(">", "&gt;");
         }

         result.setContent_name(tmpStr);
      } catch (SQLException var6) {
         logger.error(var6);
      }

      return result;
   }

   @PreAuthorize("hasRole('Content Read Authority')")
   public Content getTLFDInfo(String contentId) throws OpenApiServiceException {
      Content result = null;

      try {
         ContentInfo mgr = ContentInfoImpl.getInstance();
         if (!mgr.isExistContentID(contentId)) {
            throw new OpenApiServiceException(OpenApiExceptionCode.C101[0], OpenApiExceptionCode.C101[1]);
         }

         result = mgr.getTLFDInfo(contentId);
         if (result.getPlay_time() != null && !result.getPlay_time().equals("")) {
            if (result.getPlay_time_milli() != null && !result.getPlay_time_milli().equals("")) {
               result.setPlay_time(result.getPlay_time() + "." + result.getPlay_time_milli());
            } else {
               result.setPlay_time(result.getPlay_time() + ".0");
            }
         }

         String tmpStr = null;
         tmpStr = result.getContent_name();
         if (tmpStr != null) {
            tmpStr = tmpStr.replace("<", "&lt;").replace(">", "&gt;");
         }

         result.setContent_name(tmpStr);
      } catch (SQLException var5) {
         logger.error(var5);
      }

      return result;
   }

   @PreAuthorize("hasAnyRole('Content Read Authority', 'Content Schedule Write Authority', 'Content Schedule Add Authority', 'Lite Content Schedule Write Authority', 'Lite Content Schedule Add Authority', 'Videowall Schedule Write Authority', 'Videowall Schedule Add Authority', 'Lite Playlist Write Authority', 'Lite Playlist Manage Authority', 'Playlist Write Authority', 'Playlist Manage Authority')")
   public ResultList getMediaTypeList(String deviceType) throws OpenApiServiceException {
      ResultList resultList = new ResultList();
      List mediaTypeList = new ArrayList();
      Float deviceTypeVersion = CommonDataConstants.TYPE_VERSION_1_0;
      if (deviceType.equals("iPLAYER")) {
         deviceType = "iPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_1_0;
      } else if (deviceType.equals("SPLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_1_0;
      } else if (deviceType.equalsIgnoreCase("S2PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_2_0;
      } else if (deviceType.equalsIgnoreCase("S3PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_3_0;
      } else if (deviceType.equalsIgnoreCase("S4PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_4_0;
      } else if (deviceType.equalsIgnoreCase("S5PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_5_0;
      } else if (deviceType.equalsIgnoreCase("S6PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_6_0;
      } else if (deviceType.equalsIgnoreCase("S7PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_7_0;
      } else if (deviceType.equalsIgnoreCase("S9PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_9_0;
      } else if (deviceType.equalsIgnoreCase("S10PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_10_0;
      }

      OpenApiParameterValidator openApiParameterValidator = new OpenApiParameterValidator();
      if (openApiParameterValidator.isLiteVersion()) {
         mediaTypeList.add("IMAGE");
         mediaTypeList.add("MOVIE");
         mediaTypeList.add("OFFICE");
         mediaTypeList.add("FLASH");
      } else {
         ContentInfoImpl contentInfo = ContentInfoImpl.getInstance();

         try {
            List tmpList = contentInfo.getMediaTypeByDeviceTypeAndVersion(deviceType, deviceTypeVersion);

            for(int i = 0; i < tmpList.size(); ++i) {
               String mediaType = (String)tmpList.get(i);
               mediaTypeList.add(mediaType);
            }

            mediaTypeList.remove("PROM");
         } catch (SQLException var10) {
            logger.error(var10);
         }
      }

      resultList.setResultList(mediaTypeList);
      resultList.setTotalCount(mediaTypeList.size());
      return resultList;
   }

   @PreAuthorize("hasAnyRole('Content Read Authority', 'Content Schedule Write Authority', 'Content Schedule Add Authority', 'Lite Content Schedule Write Authority', 'Lite Content Schedule Add Authority', 'Videowall Schedule Write Authority', 'Videowall Schedule Add Authority', 'Lite Playlist Write Authority', 'Lite Playlist Manage Authority', 'Playlist Write Authority', 'Playlist Manage Authority')")
   public ResultList getFileTypeList(String mediaType, String deviceType) throws OpenApiServiceException {
      ResultList resultList = new ResultList();
      List fileTypeList = new ArrayList();
      ContentCodeInfo mgr = ContentCodeInfoImpl.getInstance();
      Float deviceTypeVersion = CommonDataConstants.TYPE_VERSION_1_0;
      if (deviceType.equals("iPLAYER")) {
         deviceType = "iPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_1_0;
      } else if (deviceType.equals("SPLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_1_0;
      } else if (deviceType.equals("S2PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_2_0;
      } else if (deviceType.equalsIgnoreCase("S3PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_3_0;
      } else if (deviceType.equalsIgnoreCase("S4PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_4_0;
      } else if (deviceType.equalsIgnoreCase("S5PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_5_0;
      } else if (deviceType.equalsIgnoreCase("S6PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_6_0;
      } else if (deviceType.equalsIgnoreCase("S7PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_7_0;
      } else if (deviceType.equalsIgnoreCase("S9PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_9_0;
      } else if (deviceType.equalsIgnoreCase("S10PLAYER")) {
         deviceType = "SPLAYER";
         deviceTypeVersion = CommonDataConstants.TYPE_VERSION_10_0;
      }

      try {
         List tmpList = mgr.getFileTypeListByDeviceTypeAndVersion(mediaType, deviceType, deviceTypeVersion);

         for(int i = 0; i < tmpList.size(); ++i) {
            Map map = (Map)tmpList.get(i);
            String fileType = (String)map.get("file_type");
            fileTypeList.add(fileType);
         }
      } catch (SQLException var11) {
         logger.error(var11);
      }

      resultList.setResultList(fileTypeList);
      resultList.setTotalCount(fileTypeList.size());
      return resultList;
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public Boolean addContentGroup(Group group_info) throws OpenApiServiceException {
      boolean ret = false;
      ContentInfo cinfo = ContentInfoImpl.getInstance();
      String user_id = group_info.getCreator_id();
      if (user_id == null || user_id.trim().equals("")) {
         user_id = this.user.getUser().getUser_id();
         group_info.setCreator_id(user_id);
      }

      Long this_group_index = group_info.getIndex();
      Long this_group_id = -9L;
      boolean bExistGroupName = false;

      try {
         bExistGroupName = cinfo.isExistGroupName(group_info.getGroup_name(), user_id);
      } catch (SQLException var10) {
         logger.error(var10);
      }

      if (bExistGroupName) {
         throw new OpenApiServiceException(OpenApiExceptionCode.V003[0], OpenApiExceptionCode.V003[1] + "- group_name");
      } else {
         try {
            logger.debug("Update group name as " + group_info.getGroup_name());
            Group pGroup = cinfo.getGroupInfo(group_info.getP_group_id());
            group_info.setGroup_depth(pGroup.getGroup_depth() + 1L);
            group_info.setOrganization_id(pGroup.getOrganization_id());
            this_group_id = cinfo.addGroup(group_info);
         } catch (Exception var9) {
            logger.error(var9);
         }

         if (this_group_id == 0L) {
            throw new OpenApiServiceException(OpenApiExceptionCode.V003[0], OpenApiExceptionCode.V003[1] + "- group_name");
         } else {
            ret = true;
            Long temp_p_group_id = group_info.getP_group_id();
            logger.debug("Temp group parent id : " + temp_p_group_id);
            if (this_group_index.equals(temp_p_group_id)) {
               logger.debug("Change Parent group id");
               group_info.setP_group_id(this_group_id);
            }

            return ret;
         }
      }
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public Boolean modifyContentGroup(Long groupId, String newGroupName) throws OpenApiServiceException {
      OpenApiParameterValidator openApiParameterValidator = new OpenApiParameterValidator();
      if (!openApiParameterValidator.checkGroupName(newGroupName)) {
         throw new OpenApiServiceException(OpenApiExceptionCode.G123[0], OpenApiExceptionCode.G123[1]);
      } else {
         boolean ret = false;

         try {
            ContentInfo cinfo = ContentInfoImpl.getInstance();
            Group group_info = cinfo.getGroupInfo(groupId);
            if (group_info == null) {
               throw new OpenApiServiceException(OpenApiExceptionCode.G103[0], OpenApiExceptionCode.G103[1]);
            }

            if (group_info.getGroup_name().equals("default")) {
               throw new OpenApiServiceException(OpenApiExceptionCode.G116[0], OpenApiExceptionCode.G116[1]);
            }

            group_info.setGroup_name(newGroupName);
            logger.debug("Update group name as " + group_info.getGroup_name());
            if (cinfo.setGroupInfo(group_info) > 0) {
               ret = true;
            }
         } catch (SQLException var7) {
            logger.error(var7);
         }

         return ret;
      }
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public Boolean deleteContentGroup(Long group_id) throws OpenApiServiceException {
      boolean ret = false;

      try {
         String target_table = "MI_CMS_INFO_CONTENT_GROUP";
         logger.debug("Delete group of which name is " + group_id + " from " + target_table + " table ");
         ContentInfo cinfo = ContentInfoImpl.getInstance();
         Group group = cinfo.getGroupInfo(group_id);
         if (group == null) {
            throw new OpenApiServiceException(OpenApiExceptionCode.G103[0], OpenApiExceptionCode.G103[1]);
         }

         if (group.getGroup_name().equals("default")) {
            throw new OpenApiServiceException(OpenApiExceptionCode.G116[0], OpenApiExceptionCode.G116[1]);
         }

         if (!cinfo.isDeletableGroup(group_id)) {
            throw new OpenApiServiceException(OpenApiExceptionCode.G117[0], OpenApiExceptionCode.G117[1]);
         }

         List content_list = cinfo.getGroupedContentIdList(group_id);
         ContentLog cLog = new ContentLog();
         if (content_list != null) {
            int j;
            Map map;
            String content_id;
            for(j = 0; j < content_list.size(); ++j) {
               map = (Map)content_list.get(j);
               content_id = (String)map.get("content_id");
               if (!cinfo.isDeletableContent(content_id, this.getToken())) {
                  throw new OpenApiServiceException(OpenApiExceptionCode.G118[0], OpenApiExceptionCode.G118[1]);
               }
            }

            for(j = 0; j < content_list.size(); ++j) {
               map = (Map)content_list.get(j);
               content_id = (String)map.get("content_id");
               if (cinfo.deleteContentCompletely(content_id) <= 0) {
                  throw new OpenApiServiceException(OpenApiExceptionCode.C103[0], OpenApiExceptionCode.C103[1]);
               }

               cLog.setEvent_type("Delete content");
               cLog.setContent_id(content_id);
            }
         }

         if (cinfo.deleteGroup(group_id) <= 0) {
            throw new OpenApiServiceException(OpenApiExceptionCode.G105[0], OpenApiExceptionCode.G105[1]);
         }

         ret = true;
      } catch (SQLException var11) {
         logger.error(var11);
      }

      return ret;
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public ResultList getContentVersionList(String contentId) throws OpenApiServiceException {
      ResultList resultList = new ResultList();
      List cList = null;

      try {
         ContentInfo cInfo = ContentInfoImpl.getInstance();
         if (!cInfo.isExistContentID(contentId)) {
            throw new OpenApiServiceException(OpenApiExceptionCode.C101[0], OpenApiExceptionCode.C101[1]);
         }

         cList = cInfo.getContentAllVerInfo(contentId);
         new Content();
         String tmpStr = null;

         for(int i = 0; i < cList.size(); ++i) {
            Content content_tmp = (Content)cList.get(i);
            tmpStr = content_tmp.getContent_name();
            if (tmpStr != null) {
               tmpStr = tmpStr.replace("<", "&lt;").replace(">", "&gt;");
            }

            content_tmp.setContent_name(tmpStr);
            content_tmp.setGroup_id(cInfo.getGroupId(content_tmp.getContent_id()));
            cList.set(i, content_tmp);
         }

         resultList.setTotalCount(cList.size());
         resultList.setResultList(cList);
      } catch (SQLException var8) {
         logger.error(var8);
      }

      return resultList;
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public Boolean setContentActiveVersion(String contentID, Long versionID) throws OpenApiServiceException {
      boolean ret = false;
      ContentInfoImpl cInfo = ContentInfoImpl.getInstance();

      try {
         if (!cInfo.isExistContentID(contentID)) {
            throw new OpenApiServiceException(OpenApiExceptionCode.C101[0], OpenApiExceptionCode.C101[1]);
         }
      } catch (SQLException var7) {
         logger.error(var7);
      }

      try {
         if (cInfo.setActiveVersion(contentID, versionID, true) > 0) {
            ret = true;
         }
      } catch (Exception var6) {
         logger.error(var6);
      }

      return ret;
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public boolean addCifsContent(String cifsContentName, String cifsAddress, String cifsDirectory, String group_id, String loginId, String password, String refreshInterval) throws Exception {
      String user_id = this.user.getUser().getUser_id();
      String contentId = UUID.randomUUID().toString();
      int maxSizeOfDirectory = 50;
      boolean isValid = true;
      boolean checkLogon = false;
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      Long rootGroupId = cInfo.getRootId(user_id);
      if (group_id.equals("0")) {
         group_id = rootGroupId.toString();
      }

      String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
      if (cifsDirectory.length() > maxSizeOfDirectory) {
         cifsDirectory = cifsDirectory.substring(cifsDirectory.length() - maxSizeOfDirectory, cifsDirectory.length());
      }

      String localPathByIp = ContentUtils.getCifsLocalPath(CONTENTS_HOME, cifsAddress, loginId, cifsDirectory, user_id);
      logger.info("[CDV_CIFS] " + contentId + cifsContentName + ContentUtils.getFolderIp(cifsAddress) + loginId + cifsDirectory + refreshInterval + " by " + user_id + " in " + group_id);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      String defaultErrorMessage = rms.getMessage("MIS_TEXT_CIFS_SERVER_ACCESS_FAILURE_P", (Object[])null, "Failed to connect to the Remote Server.", Locale.US);
      rms.getMessage("MIS_TEXT_FTP_INVALID_FILES_P", (Object[])null, defaultErrorMessage, Locale.US);
      String loginFailureMessage = rms.getMessage("MIS_TEXT_CIFS_SERVER_LOGIN_FAILURE_P", (Object[])null, defaultErrorMessage, Locale.US);
      String sameContentMessage = rms.getMessage("MIS_TEXT_FTP_SAME_CONTENT_P", (Object[])null, defaultErrorMessage, Locale.US);
      rms.getMessage("MIS_TEXT_FTP_INVALID_PATH_P", (Object[])null, defaultErrorMessage, Locale.US);
      if (SecurityUtils.getSafeFile(localPathByIp).isDirectory()) {
         isValid = false;
         throw new Exception(sameContentMessage);
      } else {
         String domain;
         if (isValid) {
            SMBClient client = new SMBClient();
            Connection connection = null;
            Session smbSession = null;
            DiskShare share = null;

            try {
               cifsDirectory = CifsFilesToDownload.changePath(cifsDirectory);
               String[] splitDomainAndUserId = CifsFilesToDownload.splitDomainAndUserId(loginId);
               domain = null;
               String userId = null;
               if (splitDomainAndUserId != null && splitDomainAndUserId.length > 0) {
                  if (splitDomainAndUserId.length > 1) {
                     domain = splitDomainAndUserId[0];
                     userId = splitDomainAndUserId[1];
                  } else {
                     userId = loginId;
                  }
               } else {
                  userId = loginId;
               }

               connection = client.connect(cifsAddress);
               AuthenticationContext ac = new AuthenticationContext(userId, password.toCharArray(), domain);
               smbSession = connection.authenticate(ac);
               share = (DiskShare)smbSession.connectShare(cifsDirectory);
               if (share.isConnected()) {
                  checkLogon = true;
               }
            } catch (Exception var38) {
               throw new Exception(loginFailureMessage);
            } finally {
               if (share != null) {
                  share.close();
               }

               if (smbSession != null) {
                  smbSession.close();
               }

               if (connection != null) {
                  connection.close();
               }

            }
         }

         try {
            if (isValid && checkLogon) {
               logger.info("[CList] Start CIFS " + localPathByIp);
               long nGroupId = Long.parseLong(group_id);
               long nRefreshInterval = Long.parseLong(refreshInterval);
               boolean scheduledJob = false;
               domain = "Y";
               long nLoginRetryMaxCount = 1L;
               String canLoginRetry = "Y";
               Runnable runCifs = new CifsFileDownloadThread(user_id, nGroupId, contentId, cifsContentName, cifsAddress, loginId, password, localPathByIp, cifsDirectory, nRefreshInterval, scheduledJob, domain, nLoginRetryMaxCount, canLoginRetry);
               Thread threadCifs = new Thread(runCifs);
               threadCifs.start();
            }

            return true;
         } catch (Exception var37) {
            logger.error("", var37);
            throw new Exception(defaultErrorMessage);
         }
      }
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public boolean addFtpContent(String ftpContentName, String ftpAddress, String ftpDirectory, String ftpPort, String group_id, String loginId, String password, String refreshInterval) throws Exception {
      String miUserId = "";
      String contentId = "";
      int maxSizeOfDirectory = 50;
      boolean isValid = true;
      boolean checkLogon = false;
      int port = Integer.parseInt(ftpPort);
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      miUserId = this.user.getUser().getUser_id();
      Long rootGroupId = cInfo.getRootId(miUserId);
      contentId = UUID.randomUUID().toString();
      if (group_id.equals("0")) {
         group_id = rootGroupId.toString();
      }

      String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
      if (ftpDirectory.length() > maxSizeOfDirectory) {
         ftpDirectory = ftpDirectory.substring(ftpDirectory.length() - maxSizeOfDirectory, ftpDirectory.length());
      }

      logger.info("[CDV_FTP] " + ftpContentName + ContentUtils.getFolderIp(ftpAddress) + ftpPort + loginId + ftpDirectory + refreshInterval + " by " + miUserId + " in " + group_id);
      String localPathByIp = ContentUtils.getFtpLocalPath(CONTENTS_HOME, ftpAddress, loginId, ftpDirectory, miUserId);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      String defaultErrorMessage = rms.getMessage("MIS_TEXT_CIFS_SERVER_ACCESS_FAILURE_P", (Object[])null, "Failed to connect to the Remote Server.", Locale.US);
      String invalidFilesMessage = rms.getMessage("MIS_TEXT_FTP_INVALID_FILES_P", (Object[])null, defaultErrorMessage, Locale.US);
      String invalidParamsMessage = rms.getMessage("MIS_TEXT_FTP_PARAM_INVALID_P", (Object[])null, defaultErrorMessage, Locale.US);
      String loginFailureMessage = rms.getMessage("MESSAGE_UPLOADER_FAIL_LOGIN_FTP_P", (Object[])null, defaultErrorMessage, Locale.US);
      String sameContentMessage = rms.getMessage("MIS_TEXT_FTP_SAME_CONTENT_P", (Object[])null, defaultErrorMessage, Locale.US);
      String invalidPathMessage = rms.getMessage("MIS_TEXT_FTP_INVALID_PATH_P", (Object[])null, defaultErrorMessage, Locale.US);
      if (!ftpContentName.equalsIgnoreCase("") && !ftpAddress.equalsIgnoreCase("") && !loginId.equalsIgnoreCase("") && !password.equalsIgnoreCase("") && !ftpDirectory.equalsIgnoreCase("") && !refreshInterval.equalsIgnoreCase("")) {
         if (SecurityUtils.getSafeFile(localPathByIp).isDirectory()) {
            isValid = false;
            throw new Exception(sameContentMessage);
         } else {
            if (isValid) {
               FTPClient client = null;
               boolean var27 = false;

               try {
                  client = new FTPClient();
                  client.setControlEncoding("euc-kr");
                  client.setDefaultPort(port);
                  client.connect(ftpAddress, port);
                  int reply = client.getReplyCode();
                  if (!FTPReply.isPositiveCompletion(reply)) {
                     throw new Exception(defaultErrorMessage);
                  }

                  checkLogon = true;
                  if (!client.login(loginId, password)) {
                     throw new Exception(loginFailureMessage);
                  }

                  checkLogon = true;
                  client.setFileType(2);
                  client.enterLocalPassiveMode();
                  if (!client.changeWorkingDirectory(ftpDirectory)) {
                     isValid = false;
                     throw new Exception(invalidPathMessage);
                  }

                  if (checkLogon && isValid) {
                     FTPFile[] ftpFiles = client.listFiles();

                     try {
                        if (ftpFiles == null || ftpFiles.length <= 0) {
                           isValid = false;
                           throw new Exception(invalidFilesMessage);
                        }

                        int var30 = ftpFiles.length;
                        byte var31 = 0;
                        if (var31 < var30) {
                           FTPFile file = ftpFiles[var31];
                           String ftpFileName = file.getName();
                           boolean validType = false;
                           String[] tempName = ftpFileName.split("[.]");
                           int sizeOfSplitedName = false;
                           if (tempName.length > 0) {
                              int sizeOfSplitedName = tempName.length - 1;
                              validType = cInfo.getCodeFile(tempName[sizeOfSplitedName].toUpperCase()).equalsIgnoreCase("");
                              System.out.println("[File checker] fileName validType " + ftpFileName + " " + validType);
                           }

                           if (validType) {
                              isValid = false;
                              throw new Exception(invalidFilesMessage);
                           }

                           isValid = true;
                        }
                     } catch (Exception var47) {
                        isValid = false;
                        logger.error("", var47);
                        throw new Exception(invalidPathMessage);
                     }

                     client.logout();
                  }
               } catch (Exception var48) {
                  checkLogon = false;
                  logger.error("", var48);
                  throw new Exception(defaultErrorMessage);
               } finally {
                  if (client != null && client.isConnected()) {
                     try {
                        client.disconnect();
                     } catch (IOException var45) {
                        System.out.println("Disconnected!");
                     }
                  }

               }
            }

            try {
               if (isValid && checkLogon) {
                  logger.info("[CList] Start FTP " + localPathByIp);
                  long nGroupId = Long.parseLong(group_id);
                  long nRefreshInterval = Long.parseLong(refreshInterval);
                  boolean scheduledJob = false;
                  String canRefresh = "Y";
                  long nLoginRetryMaxCount = 1L;
                  String canLoginRetry = "Y";
                  Runnable runFTP = new FtpFileDownloadThread(miUserId, nGroupId, contentId, ftpContentName, ftpAddress, port, loginId, password, localPathByIp, ftpDirectory, nRefreshInterval, scheduledJob, canRefresh, nLoginRetryMaxCount, canLoginRetry);
                  Thread threadFTP = new Thread(runFTP);
                  threadFTP.start();
               }

               return true;
            } catch (Exception var46) {
               logger.error("", var46);
               throw new Exception(defaultErrorMessage);
            }
         }
      } else {
         isValid = false;
         throw new Exception(invalidParamsMessage);
      }
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public String addContent(Boolean isSecure, String locale) throws OpenApiServiceException {
      OpenApiParameterValidator openApiParameterValidator = new OpenApiParameterValidator();
      if (!openApiParameterValidator.isLocale(locale)) {
         throw new OpenApiServiceException(OpenApiExceptionCode.C104[0], OpenApiExceptionCode.C104[1]);
      } else {
         String ret = "";

         try {
            String user_id = this.user.getUser().getUser_id();
            String jnlpFileName = "uploader_" + user_id + "_" + DateUtils.date2String(this.user.getUser().getCreate_date(), "yyyyMMddHHmmss") + ".jnlp";
            String sessionId = this.getToken();
            URL urlValue = Thread.currentThread().getContextClassLoader().getResource("/");
            if (urlValue == null) {
               return null;
            }

            String topPath = urlValue.getPath();
            topPath = topPath.replaceAll("%20", " ");
            topPath = topPath + "../../uploader/jnlp/";
            logger.debug("addContent = " + topPath + jnlpFileName);
            SecurityUtils.getSafeFile(topPath).mkdirs();
            File jnlpFile = SecurityUtils.getSafeFile(topPath + jnlpFileName);
            jnlpFile.createNewFile();
            String ftpPort = CommonConfig.get("download.server.ftp.port");
            String url = CommonConfig.get("web_url");
            if (isSecure) {
               url = url.replace("http://", "https://");
            }

            String newToken = this.issueToken();
            String newTokenEncode = Base64.encode(newToken.getBytes());
            ContentUploaderJnlpStringBuilder uploaderJnlpBuilder = (new ContentUploaderJnlpStringBuilder()).addServerUrl(url).addUrlToken(newTokenEncode).addJnlpFilename(jnlpFileName).addLocale(locale).addSessionId(sessionId).addUserId(user_id).addToken(newToken).addFtpPort(Integer.valueOf(ftpPort)).addUserGroupId(this.user.getUser().getGroup_id()).addIsUserScopeGrouped(this.user.getScope().equals("GROUPED"));
            FileWriter writer = new FileWriter(jnlpFile);
            PrintWriter fout = new PrintWriter(writer);
            fout.write(uploaderJnlpBuilder.build());
            fout.close();
            writer.close();
            ret = url + "/uploader/" + newTokenEncode + "/jnlp/" + jnlpFileName;
         } catch (IOException var18) {
            logger.error(var18);
         } catch (ConfigException var19) {
            logger.error(var19);
         }

         return ret;
      }
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public Boolean addContentForMega(Content content) throws ConfigException, SQLException, Exception {
      String contentId = content.getContent_id();
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      if (cmsDao.isExistContentID(contentId)) {
         throw new OpenApiServiceException(OpenApiExceptionCode.C106[0], OpenApiExceptionCode.C106[1]);
      } else {
         int retVal = cmsDao.addContentForMega(content);
         if (retVal > 0) {
            cmsDao.setTemplateContentParsed(contentId, content.getVersion_id());
         }

         return retVal > 0 ? true : false;
      }
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public Boolean modifyContent(Content content) throws OpenApiServiceException {
      boolean ret = false;
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      String contentID = content.getContent_id();

      try {
         if (!contentInfo.isExistContentID(contentID)) {
            throw new OpenApiServiceException(OpenApiExceptionCode.C101[0], OpenApiExceptionCode.C101[1]);
         }

         String contentName = content.getContent_name();
         String contentMetaData = content.getContent_meta_data();
         int iResult = contentInfo.setContentInfo(contentID, contentName, contentMetaData, content.getShare_flag());
         if (iResult > 0) {
            ret = true;
         }

         if (ret && contentInfo.setContentGroup(contentID, content.getGroup_id()) > 0) {
            ret = true;
         } else {
            ret = false;
         }
      } catch (SQLException var9) {
         logger.error(var9);
      } catch (ConfigException var10) {
         logger.error(var10);
      }

      try {
         if (ret && contentInfo.setActiveVersion(contentID, content.getVersion_id(), true) > 0) {
            ret = true;
         } else {
            ret = false;
         }
      } catch (Exception var8) {
         logger.error(var8);
      }

      return ret;
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public Boolean modifyContentForMega(Content content) throws ConfigException, SQLException, Exception {
      String contentId = content.getContent_id();
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      Long contentVer = cmsDao.getContentActiveVer(contentId);
      if (contentVer == 0L) {
         throw new OpenApiServiceException(OpenApiExceptionCode.C101[0], OpenApiExceptionCode.C101[1]);
      } else {
         int retVal = cmsDao.addContentForMega(content);
         if (retVal > 0) {
            ScheduleInfo sInfo = ScheduleInfoImpl.getInstance();
            sInfo.setScheduleTrigger(contentId);
            return true;
         } else {
            throw new OpenApiServiceException(OpenApiExceptionCode.C105[0], OpenApiExceptionCode.C105[1]);
         }
      }
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public ResultList deleteContent(String contentID) throws OpenApiServiceException {
      String sessionID = this.getToken();
      ResultList resultList = null;
      ArrayList result_list = null;
      ArrayList refPlaylistList = null;
      ArrayList refScheduleList = null;

      try {
         ContentInfo cInfo = ContentInfoImpl.getInstance();
         ContentLog cLog = new ContentLog();
         resultList = new ResultList();
         result_list = new ArrayList();
         refPlaylistList = new ArrayList();
         refScheduleList = new ArrayList();
         if (!cInfo.isExistContentID(contentID)) {
            throw new OpenApiServiceException(OpenApiExceptionCode.C101[0], OpenApiExceptionCode.C101[1]);
         }

         ContentReference contentReference = new ContentReference();
         contentReference.setContent_id(contentID);
         String tmpStr = cInfo.getContentName(contentID);
         if (tmpStr != null) {
            tmpStr = tmpStr.replace("<", "&lt;").replace(">", "&gt;");
         }

         contentReference.setContent_name(tmpStr);
         if (cInfo.isDeletableContent(contentID, sessionID)) {
            if (cInfo.deleteContent(contentID, this.user.getUser().getUser_id(), sessionID) > 0) {
               cLog.setEvent_type("Delete content");
               cLog.setContent_id(contentID);
               contentReference.setDelete_result(true);
            }
         } else {
            contentReference.setDelete_result(false);
            ScheduleInfo sInfo = ScheduleInfoImpl.getInstance();
            List pList = cInfo.getPlaylistListUsingContent(contentID);
            if (pList != null && pList.size() != 0) {
               for(int j = 0; j < pList.size(); ++j) {
                  Map map = (Map)pList.get(j);
                  tmpStr = (String)map.get("PLAYLIST_NAME");
                  if (tmpStr != null) {
                     tmpStr = tmpStr.replace("<", "&lt;").replace(">", "&gt;");
                  }

                  refPlaylistList.add(tmpStr);
               }
            }

            List list1 = sInfo.getProgramByContentId(contentID);
            if (list1 != null && list1.size() != 0) {
               for(int j = 0; j < list1.size(); ++j) {
                  Map map = (Map)list1.get(j);
                  tmpStr = (String)map.get("PROGRAM_NAME");
                  if (tmpStr != null) {
                     tmpStr = tmpStr.replace("<", "&lt;").replace(">", "&gt;");
                  }

                  refScheduleList.add(tmpStr);
               }
            }

            if (refPlaylistList != null && refPlaylistList.size() != 0) {
               contentReference.setRefPlaylistList(refPlaylistList);
            }

            if (refScheduleList != null && refScheduleList.size() != 0) {
               contentReference.setRefScheduleList(refScheduleList);
            }
         }

         result_list.add(contentReference);
      } catch (SQLException var16) {
         logger.error(var16);
      } catch (ConfigException var17) {
         logger.error(var17);
      }

      resultList.setTotalCount(result_list.size());
      resultList.setResultList(result_list);
      return resultList;
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public String removeContent(String contentID) throws OpenApiServiceException, SQLException, ConfigException {
      String retval = "false";
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      Content content = cInfo.getContentActiveVerInfo(contentID);
      if (!content.getIs_deleted().equalsIgnoreCase("Y")) {
         throw new OpenApiServiceException(OpenApiExceptionCode.U123[0], OpenApiExceptionCode.U123[1]);
      } else {
         if (cInfo.deleteContentCompletely(contentID) > 0) {
            retval = "true";
         }

         return retval;
      }
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public String restoreContent(String contentID) throws OpenApiServiceException, SQLException, ConfigException {
      String retval = "false";
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      Content content = cInfo.getContentActiveVerInfo(contentID);
      if (!content.getIs_deleted().equalsIgnoreCase("Y")) {
         throw new OpenApiServiceException(OpenApiExceptionCode.U124[0], OpenApiExceptionCode.U124[1]);
      } else {
         if (cInfo.restoreContent(contentID) > 0) {
            retval = "true";
         }

         return retval;
      }
   }

   @PreAuthorize("hasRole('Content Read Authority')")
   public String downloadContent(String content_id_list, Boolean isSecure, String locale) throws OpenApiServiceException {
      OpenApiParameterValidator openApiParameterValidator = new OpenApiParameterValidator();
      if (!openApiParameterValidator.isLocale(locale)) {
         throw new OpenApiServiceException(OpenApiExceptionCode.C104[0], OpenApiExceptionCode.C104[1]);
      } else {
         String ret = "";

         try {
            String user_id = this.user.getUser().getUser_id();
            String jnlpFileName = "downloader_" + user_id + "_" + DateUtils.date2String(this.user.getUser().getCreate_date(), "yyyyMMddHHmmss") + ".jnlp";
            URL urlValue = Thread.currentThread().getContextClassLoader().getResource("/");
            if (urlValue == null) {
               return null;
            }

            String topPath = urlValue.getPath();
            topPath = topPath.replaceAll("%20", " ");
            topPath = topPath + "../../uploader/jnlp/";
            logger.debug("downloadContent = " + topPath + jnlpFileName);
            boolean ret2 = SecurityUtils.getSafeFile(topPath).mkdirs();
            if (!ret2) {
               logger.error("returned fail");
            }

            File jnlpFile = SecurityUtils.getSafeFile(topPath + jnlpFileName);
            jnlpFile.createNewFile();
            String ftpPort = CommonConfig.get("download.server.ftp.port");
            String url = CommonConfig.get("web_url");
            if (isSecure) {
               url = url.replace("http://", "https://");
            }

            String newToken = this.issueToken();
            String newTokenEncode = Base64.encode(newToken.getBytes());
            ContentDownloaderJnlpStringBuilder downloaderJnlpBuilder = (new ContentDownloaderJnlpStringBuilder()).addServerUrl(url).addUrlToken(newTokenEncode).addJnlpFilename(jnlpFileName).addLocale(locale).addUserId(user_id).addToken(newToken).addFtpPort(Integer.valueOf(ftpPort)).addContentIdListAsString(content_id_list);
            FileWriter writer = new FileWriter(jnlpFile);
            PrintWriter fout = new PrintWriter(writer);
            fout.write(downloaderJnlpBuilder.build());
            fout.close();
            writer.close();
            ret = url + "/uploader/" + newTokenEncode + "/jnlp/" + jnlpFileName;
         } catch (ConfigException var19) {
            logger.error(var19);
         } catch (IOException var20) {
            logger.error(var20);
         }

         return ret;
      }
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public String modifyContentFile(String contentId, Boolean isSecure, String locale) throws OpenApiServiceException {
      OpenApiParameterValidator openApiParameterValidator = new OpenApiParameterValidator();
      if (!openApiParameterValidator.isLocale(locale)) {
         throw new OpenApiServiceException(OpenApiExceptionCode.C104[0], OpenApiExceptionCode.C104[1]);
      } else {
         String ret = "";

         try {
            ContentInfo contentInfo = ContentInfoImpl.getInstance();
            if (!contentInfo.isExistContentID(contentId)) {
               throw new OpenApiServiceException(OpenApiExceptionCode.C101[0], OpenApiExceptionCode.C101[1]);
            }

            String user_id = this.user.getUser().getUser_id();
            String jnlpFileName = "editor_" + user_id + "_" + DateUtils.date2String(this.user.getUser().getCreate_date(), "yyyyMMddHHmmss") + ".jnlp";
            String sessionId = this.getToken();
            URL urlValue = Thread.currentThread().getContextClassLoader().getResource("/");
            if (urlValue == null) {
               return null;
            }

            String topPath = urlValue.getPath();
            topPath = topPath.replaceAll("%20", " ");
            topPath = topPath + "../../uploader/jnlp/";
            logger.debug("modifyContentFile = " + topPath + jnlpFileName);
            SecurityUtils.getSafeFile(topPath).mkdirs();
            File jnlpFile = SecurityUtils.getSafeFile(topPath + jnlpFileName);
            jnlpFile.createNewFile();
            String ftpPort = CommonConfig.get("download.server.ftp.port");
            String org_creator_id = contentInfo.getContentOrgCreatorId(contentId);
            String url = CommonConfig.get("web_url");
            if (isSecure) {
               url = url.replace("http://", "https://");
            }

            String newToken = this.issueToken();
            String newTokenEncode = Base64.encode(newToken.getBytes());
            ContentEditorJnlpStringBuilder editorJnlpBuilder = (new ContentEditorJnlpStringBuilder()).addServerUrl(url).addUrlToken(newTokenEncode).addJnlpFilename(jnlpFileName).addLocale(locale).addUserId(user_id).addToken(newToken).addFtpPort(Integer.valueOf(ftpPort)).addSessionId(sessionId).addContentId(contentId).addOrgCreatorId(org_creator_id);
            FileWriter writer = new FileWriter(jnlpFile);
            PrintWriter fout = new PrintWriter(writer);
            fout.write(editorJnlpBuilder.build());
            fout.close();
            writer.close();
            ret = url + "/uploader/" + newTokenEncode + "/jnlp/" + jnlpFileName;
         } catch (SQLException var21) {
            logger.error(var21);
         } catch (IOException var22) {
            logger.error(var22);
         } catch (ConfigException var23) {
            logger.error(var23);
         }

         return ret;
      }
   }

   public ResultList getContentLogList(SelectConditionLog condition) throws Exception {
      LogIF mgr = LogImpl.getInstance();
      ResultList resultList = new ResultList();
      String userId = this.user.getUser().getUser_id();
      String section = "getContentLogList";
      int startPos = condition.getStart_index();
      int pageSize = condition.getPage_size();
      condition.setUserID(userId);
      if (condition.getSortOrder() == null) {
         condition.setSortOrder("asc");
      }

      if (condition.getStartDate() == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.V105[0], OpenApiExceptionCode.V105[1]);
      } else if (condition.getEndDate() == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.V104[0], OpenApiExceptionCode.V104[1]);
      } else {
         OpenApiParameterValidator openApiParameterValidator = new OpenApiParameterValidator();
         if (!openApiParameterValidator.checkPeriod(condition.getStartDate(), condition.getEndDate())) {
            return null;
         } else {
            String startTime = "00:00:00";
            String endTime = "23:59:59";
            condition.setStartDate(condition.getStartDate() + " " + startTime);
            condition.setEndDate(condition.getEndDate() + " " + endTime);
            PagedListInfo pagedList = mgr.getLogList(condition, startPos, pageSize, section);
            if (pagedList != null) {
               List tmpList = pagedList.getPagedResultList();
               resultList.setResultList(tmpList);
               resultList.setTotalCount(pagedList.getTotalRowCount());
            }

            return resultList;
         }
      }
   }

   private String issueToken() throws ConfigException {
      return StrUtils.nvl(CommonConfig.get("saas.no_token.enable")).equalsIgnoreCase("TRUE") ? this.tokenRegistry.onloadUserContainer(this.user) : this.tokenRegistry.issueToken(this.user);
   }

   public String getContentDownloadProgress(String deviceId, String contentId) throws SQLException {
      DownloadStatusDAO dao = new DownloadStatusDAO();
      String progress = dao.getDownloadProgress(deviceId, contentId, "content");
      return progress;
   }

   public Long getGroupIdByName(String name) throws OpenApiServiceException {
      Long id = null;
      ResultList contentGroupList = this.getContentGroupList();
      List grouplist = contentGroupList.getResultList();
      Iterator var5 = grouplist.iterator();

      while(var5.hasNext()) {
         Object item = var5.next();
         if (item instanceof Group) {
            Group group = (Group)item;
            if (group.getGroup_name().equals(name)) {
               id = group.getGroup_id();
               break;
            }
         }
      }

      return id;
   }

   public String getContentIdByName(String userId, String contentName, String deviceType) throws OpenApiServiceException {
      ContentSearch condition = new ContentSearch();
      condition.setPageSize(Integer.MAX_VALUE);
      condition.setSearchType("all");
      condition.setStartPos(1);
      ResultList resList = this.getContentList(userId, condition, deviceType);
      List valuesList = resList.getResultList();
      Iterator var7 = valuesList.iterator();

      while(var7.hasNext()) {
         Object resVal = var7.next();
         if (resVal instanceof Content) {
            Content content = (Content)resVal;
            if (content.getContent_name().equals(contentName)) {
               return content.getContent_id();
            }
         }
      }

      return "";
   }

   public int getTotalContentDownloadProgress(String deviceId) throws SQLException, OpenApiServiceException {
      DownloadStatusDAO dao = new DownloadStatusDAO();
      List progressList = dao.getTotalDownloadProgress(deviceId);
      double size = (double)progressList.size();
      double TotalProgress = 0.0D;
      if (size == 0.0D) {
         return 0;
      } else {
         int result;
         for(result = 0; (double)result < size; ++result) {
            Map userMap = (Map)progressList.get(result);
            String progressStr = (String)userMap.get("PROGRESS");
            if (progressStr.contains("%")) {
               progressStr = progressStr.replaceAll(" %", "");
            }

            TotalProgress += (double)Integer.valueOf(progressStr);
         }

         result = (int)(TotalProgress / (size * 100.0D) * 100.0D);
         return result;
      }
   }

   public ResultList getConvertTableList() throws SQLException, OpenApiServiceException {
      ConvertDataDao convertDataDao = new ConvertDataDao();
      ResultList resultList = new ResultList();
      List convertTableList = new ArrayList();
      Map map = new HashMap();
      List tableList = convertDataDao.getConvertDataListPage(map, 0, 1000);
      Iterator var6 = tableList.iterator();

      while(var6.hasNext()) {
         ConvertData convertData = (ConvertData)var6.next();
         ConvertTable convertTable = new ConvertTable();
         convertTable.setConvert_data_name(convertData.getConvert_data_name());
         convertTable.setConvert_type(convertData.getConvert_type());
         convertTable.setCreate_date(convertData.getCreate_date());
         List dataList = convertDataDao.getConvertDataViewPage(convertData.getConvert_data_name(), 0, 1000);
         Iterator var10 = dataList.iterator();

         while(var10.hasNext()) {
            ConvertData cd = (ConvertData)var10.next();
            String toData = cd.getTo_data();
            cd.setTo_data(StringEscapeUtils.escapeXml10(toData));
         }

         convertTable.setConvertDataList(dataList);
         convertTableList.add(convertTable);
      }

      resultList.setResultList(convertTableList);
      resultList.setTotalCount(convertTableList.size());
      return resultList;
   }

   public String addConvertTable(ConvertTable convertTable) throws SQLException, OpenApiServiceException {
      String retval = "false";
      int count = 0;
      ConvertDataDao convertDataDao = new ConvertDataDao();
      int result = convertDataDao.addConvertDataTable(convertTable.getConvert_data_name(), convertTable.getConvert_type());
      if (result == 1) {
         List convertDataList = convertTable.getConvertDataList();

         ConvertData convertData;
         for(Iterator var7 = convertDataList.iterator(); var7.hasNext(); count += convertDataDao.addConvertDataList(convertData.getConvert_data_name(), convertData.getFrom_data(), convertData.getTo_data())) {
            convertData = (ConvertData)var7.next();
         }

         if (count == convertDataList.size()) {
            retval = "true";
         }
      }

      return retval;
   }

   public String modifyConvertTable(ConvertTable oldConvertTable, ConvertTable newConvertTable) throws SQLException, OpenApiServiceException {
      String retval = "false";
      int count = 0;
      ConvertDataDao convertDataDao = new ConvertDataDao();
      List convertTableMapList = convertDataDao.getConvertTableMapByConvertTableName(oldConvertTable.getConvert_data_name());

      try {
         Iterator var7 = convertTableMapList.iterator();

         while(var7.hasNext()) {
            ConvertTableMap convertTableMap = (ConvertTableMap)var7.next();
            ContentInfo contentInfo = ContentInfoImpl.getInstance();
            Content dlkContent = contentInfo.getContentAndFileActiveVerInfo(convertTableMap.getDlk_content_id());
            if (dlkContent != null) {
               String oldFileName = FileUtils.getPathFile(dlkContent.getMain_file_id(), dlkContent.getMain_file_name());
               String newContentFileId = UUID.randomUUID().toString().toUpperCase();
               String newFileFolder = FileUtils.getPathFolder(newContentFileId);
               String newFileName = FileUtils.getPathFile(newContentFileId, dlkContent.getMain_file_name());
               FileUtils.makeFolder(newFileFolder);
               ContentXmlEditorInfo xPathContentEditor = new XPathContentEditorImpl();
               ContentXmlEditor contentXmlEditor = new ContentXmlEditor(xPathContentEditor);
               boolean bRet = contentXmlEditor.modifyConvertData(oldFileName, newFileName, convertTableMap, oldConvertTable, newConvertTable);
               int iRet = true;
               if (bRet) {
                  dlkContent = ContentUtils.addNewContent(newFileName, convertTableMap.getDlk_content_id(), newContentFileId, dlkContent, this.user);
                  contentInfo.modifyDlkContentByConvertData(dlkContent);
               }
            }
         }
      } catch (ConfigException var19) {
         logger.error("", var19);
      }

      if (!oldConvertTable.getConvert_data_name().equals(newConvertTable.getConvert_data_name())) {
         convertDataDao.editConvertDataTable(oldConvertTable.getConvert_data_name(), newConvertTable.getConvert_data_name());
         convertDataDao.editConvertTableFromConvertTableMap(oldConvertTable.getConvert_data_name(), newConvertTable.getConvert_data_name());
      }

      List oldDataList = oldConvertTable.getConvertDataList();

      ConvertData convertData;
      for(Iterator var21 = oldDataList.iterator(); var21.hasNext(); count += convertDataDao.deleteConvertDataList(newConvertTable.getConvert_data_name(), convertData.getFrom_data())) {
         convertData = (ConvertData)var21.next();
      }

      List newDataList = newConvertTable.getConvertDataList();

      ConvertData convertData;
      for(Iterator var24 = newDataList.iterator(); var24.hasNext(); count += convertDataDao.addConvertDataList(newConvertTable.getConvert_data_name(), convertData.getFrom_data(), convertData.getTo_data())) {
         convertData = (ConvertData)var24.next();
      }

      if (count == oldDataList.size() + newDataList.size()) {
         retval = "true";
      }

      return retval;
   }

   public String deleteConvertTable(String tableName) throws OpenApiServiceException, SQLException, ConfigException {
      String retval = "false";
      ConvertDataDao convertDataDao = new ConvertDataDao();
      List convertTableMapList = convertDataDao.getConvertTableMapByConvertTableName(tableName);

      try {
         Iterator var5 = convertTableMapList.iterator();

         while(var5.hasNext()) {
            ConvertTableMap convertTableMap = (ConvertTableMap)var5.next();
            ContentInfo contentInfo = ContentInfoImpl.getInstance();
            Content dlkContent = contentInfo.getContentAndFileActiveVerInfo(convertTableMap.getDlk_content_id());
            String oldFileName = FileUtils.getPathFile(dlkContent.getMain_file_id(), dlkContent.getMain_file_name());
            String newContentFileId = UUID.randomUUID().toString().toUpperCase();
            String newFileFolder = FileUtils.getPathFolder(newContentFileId);
            String newFileName = FileUtils.getPathFile(newContentFileId, dlkContent.getMain_file_name());
            FileUtils.makeFolder(newFileFolder);
            ContentXmlEditorInfo xPathContentEditor = new XPathContentEditorImpl();
            ContentXmlEditor contentXmlEditor = new ContentXmlEditor(xPathContentEditor);
            boolean bRet = contentXmlEditor.deleteConvertTable(oldFileName, newFileName, convertTableMap);
            int iRet = true;
            if (bRet) {
               dlkContent = ContentUtils.addNewContent(newFileName, convertTableMap.getDlk_content_id(), newContentFileId, dlkContent, this.user);
               contentInfo.modifyDlkContentByConvertData(dlkContent);
            }
         }
      } catch (ConfigException var17) {
         logger.error("", var17);
      }

      convertDataDao.deleteConvertTableFromConvertTableMap(tableName);
      int result = convertDataDao.deleteConvertDataTable(tableName);
      if (result == 1) {
         retval = "true";
      }

      return retval;
   }

   public void getDlkInfoByDlkContentId(String dlk_content_id) throws SQLException, ParserConfigurationException, SAXException, IOException, XPathExpressionException, ConfigException {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      TemplateElementData templateElementData = new TemplateElementData();
      String dlkContentId = dlk_content_id;
      long dlkVersionId = Long.valueOf(cInfo.getActiveVersionByContentId(dlk_content_id));
      String lftContentId = null;
      long lftVersionId = 0L;
      String inputType = "Media";
      int dataNo = false;
      templateElementData.setIs_inner_datalink(false);
      templateElementData.setInput_type(inputType);
      String dlkMainFileId = cInfo.getMainFileInfo(dlk_content_id).getFile_id();
      ContentFile dlkFile = cInfo.getFileInfo(dlkMainFileId);
      String dlkFilePath = dlkFile.getFile_path() + File.separator + dlkFile.getFile_name();
      DocumentBuilderFactory documentBuilderFactory = DocumentUtils.getDocumentBuilderFactoryInstance();
      DocumentBuilder documentBuilder = documentBuilderFactory.newDocumentBuilder();
      XPathFactory factory = XPathFactory.newInstance();
      XPath xpath = factory.newXPath();
      Document document = documentBuilder.parse(dlkFilePath);
      XPathExpression lftExpr = xpath.compile("/DataLinkContentMeta/LFDContent/@id");
      Object lftObj = lftExpr.evaluate(document, XPathConstants.NODESET);
      NodeList lftNode = (NodeList)lftObj;
      lftContentId = lftNode.item(0).getTextContent();
      lftVersionId = (long)Integer.parseInt(cInfo.getActiveVersionByContentId(lftContentId));
      XPathExpression expr = xpath.compile("/DataLinkContentMeta/Page");
      Object obj = expr.evaluate(document, XPathConstants.NODESET);
      NodeList pageNodes = (NodeList)obj;

      for(int i = 0; i < pageNodes.getLength(); ++i) {
         Element pageNode = (Element)pageNodes.item(i);
         String pageNo = pageNode.getAttribute("no");
         if (pageNo.equals("1")) {
            templateElementData.setPage_no(Integer.parseInt(pageNo));
            NodeList elementNodes = pageNode.getElementsByTagName("Element");

            for(int j = 0; j < elementNodes.getLength(); ++j) {
               Element elementNode = (Element)elementNodes.item(j);
               String elementType = elementNode.getAttribute("type");
               if (elementType.equals("MediaSlide")) {
                  templateElementData.setElement_type(elementType);
                  templateElementData.setElement_no(Integer.parseInt(elementNode.getAttribute("no")));
                  templateElementData.setItem_no(Integer.parseInt(elementNode.getAttribute("no")));
                  templateElementData.setElement_name(elementNode.getFirstChild().getNodeValue());
                  NodeList dataNodes = elementNode.getElementsByTagName("Data");
                  System.out.println(dataNodes.getLength());

                  for(int k = 0; k < dataNodes.getLength(); ++k) {
                     templateElementData.setData_no(k);
                     Element dataNode = (Element)dataNodes.item(k);
                     NodeList fileInfo = dataNode.getElementsByTagName("FileInfo");
                     String fullContentFileId = fileInfo.item(0).getTextContent();
                     String content = StringUtils.substringAfter(fullContentFileId, ".");
                     String fileId = StringUtils.substringBefore(fullContentFileId, "\\");
                     String contentType = null;
                     System.out.println(fullContentFileId);
                     System.out.println(content);
                     if (content.equals("CIFS") || content.equals("FTP")) {
                        if (content.equals("CIFS")) {
                           contentType = "CIFS";
                        } else {
                           contentType = "FTP";
                        }

                        String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
                        String contentFilePath = CONTENTS_HOME + File.separator + fullContentFileId;
                        Document document2 = documentBuilder.parse(contentFilePath);
                        XPathExpression expr3 = xpath.compile("/" + contentType + "Content/@cid");
                        NodeList nodes2 = (NodeList)expr3.evaluate(document2, XPathConstants.NODESET);
                        String contentId = nodes2.item(0).getTextContent();
                        templateElementData.setContent_id(contentId);
                        cInfo.addTemplateElementData(lftContentId, lftVersionId, dlkContentId, dlkVersionId, templateElementData);
                     }
                  }
               }
            }
         }
      }

   }

   public String addDlkInfo(String dlk_content_id, long dlk_version_id, String dlkMapContentList, List convertTableList) throws OpenApiServiceException, SQLException, ConfigException, XPathExpressionException, ParserConfigurationException, SAXException, IOException {
      String retval = "false";
      String[] arrDlkMapContentList = dlkMapContentList.split(",");
      ConvertDataDao convertDataDao = new ConvertDataDao();
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      Content content = null;
      String var11 = null;

      Long dlkActiveVersion;
      try {
         dlkActiveVersion = Long.valueOf(cInfo.getActiveVersionByContentId(dlk_content_id));
      } catch (Exception var17) {
         dlkActiveVersion = dlk_version_id;
      }

      String[] var13 = arrDlkMapContentList;
      int var14 = arrDlkMapContentList.length;

      for(int var15 = 0; var15 < var14; ++var15) {
         String contentId = var13[var15];
         content = cInfo.getContentAndFileActiveVerInfo(contentId);
         if (content != null) {
            if ("LFT".equalsIgnoreCase(content.getMedia_type())) {
               var11 = "LFT";
            } else {
               var11 = "NOTLFT";
            }

            cInfo.addTemplateContent(dlk_content_id, dlkActiveVersion, content.getMedia_type(), contentId);
         }
      }

      Iterator var18 = convertTableList.iterator();

      while(var18.hasNext()) {
         ConvertTableMap convertTable = (ConvertTableMap)var18.next();
         convertDataDao.addMappingDLKConvertDataTable(dlk_content_id, dlk_version_id, convertTable);
      }

      this.getDlkInfoByDlkContentId(dlk_content_id);
      return retval;
   }

   public ResultList getDLKmappingList(String lft_content_id) {
      ResultList resultList = new ResultList();
      ContentInfoImpl contentInfo = ContentInfoImpl.getInstance();

      try {
         List dlkContentList = contentInfo.getContentByTemplateId(lft_content_id);
         resultList.setResultList(dlkContentList);
         resultList.setTotalCount(dlkContentList.size());
      } catch (SQLException var6) {
         logger.error("", var6);
      }

      return resultList;
   }

   @PreAuthorize("hasRole('Content Read Authority')")
   public ResultList getTagContentList(long tagId) {
      ResultList resultList = new ResultList();
      ContentInfoImpl contentInfo = ContentInfoImpl.getInstance();

      try {
         List contentList = contentInfo.getTagContentList(tagId);
         resultList.setResultList(contentList);
         resultList.setTotalCount(contentList.size());
      } catch (Exception var6) {
         logger.error("", var6);
      }

      return resultList;
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public Boolean setTagContent(String contentID, String tagId, String tagConditionId) throws OpenApiServiceException {
      boolean ret = false;
      TagInfo tagInfo = TagInfoImpl.getInstance();
      String[] tagList = tagId.split(",");
      String[] contentIdListTemp = contentID.split(",");
      ArrayList beforeDelTagIds = new ArrayList();

      try {
         String[] var9 = contentIdListTemp;
         int var10 = contentIdListTemp.length;

         for(int var11 = 0; var11 < var10; ++var11) {
            String contentId = var9[var11];
            List tagIds = tagInfo.getTagIdFromContentId(contentId);
            if (tagIds != null && tagIds.size() > 0) {
               Iterator var14 = tagIds.iterator();

               while(var14.hasNext()) {
                  Map map = (Map)var14.next();
                  long tag = (Long)map.get("tag_id");
                  if (!beforeDelTagIds.contains(tag)) {
                     beforeDelTagIds.add(tag);
                  }
               }
            }

            tagInfo.deleteTagInfoFromContentId(contentId);
            int i = 0;
            String[] var26 = tagList;
            int var27 = tagList.length;

            for(int var17 = 0; var17 < var27; ++var17) {
               String tag = var26[var17];
               if (tag != null && !tag.equals("")) {
                  tagInfo.setContentTagMapping(contentId, tag);
                  String[] tagConditionList = tagConditionId.split(",");
                  if (tagConditionList != null) {
                     String[] var20 = tagConditionList;
                     int var21 = tagConditionList.length;

                     for(int var22 = 0; var22 < var21; ++var22) {
                        String tagCondition = var20[var22];
                        tagInfo.setContentTagMapping(contentId, tag, tagCondition);
                     }
                  }

                  if (!beforeDelTagIds.contains(Long.valueOf(tag))) {
                     beforeDelTagIds.add(Long.valueOf(tag));
                  }
               }

               ++i;
            }
         }

         tagInfo.setPlaylistTrigger(beforeDelTagIds);
         ret = true;
      } catch (Exception var24) {
         ret = false;
         logger.error("", var24);
      }

      return ret;
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public Boolean setCategory(String contentID, String categoryId) throws OpenApiServiceException {
      boolean ret = false;
      Map results = new LinkedHashMap();
      CategoryInfoImpl categoryInfo = CategoryInfoImpl.getInstance();

      try {
         String[] contentIdListTemp = contentID.split(",");
         String[] var7 = contentIdListTemp;
         int var8 = contentIdListTemp.length;

         for(int var9 = 0; var9 < var8; ++var9) {
            String contentId = var7[var9];
            categoryInfo.deleteCategoryFromContentId(contentId);
            categoryInfo.setCategoryFromContentId(categoryId, contentId);
         }

         ret = true;
         results.put("status", "success");
      } catch (Exception var11) {
         logger.error("", var11);
         ret = false;
         results.put("status", "fail");
         results.put("message", var11.getMessage());
      }

      return ret;
   }

   @PreAuthorize("hasRole('Content Read Authority')")
   public ResultList getCategoryList(String groupId) throws OpenApiServiceException {
      ResultList resultList = new ResultList();

      try {
         CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
         new ArrayList();
         List categoryList;
         if (groupId != null && !groupId.equals("")) {
            categoryList = categoryInfo.getCategoryWithPgroupId(Long.valueOf(groupId));
         } else {
            String organization = null;
            if (this.user.getUser().getGroup_id() == 0L) {
               organization = "ALL";
            }

            categoryList = categoryInfo.getCategoryWithPgroupId(0L, organization);
         }

         resultList.setResultList(categoryList);
         resultList.setTotalCount(categoryList.size());
      } catch (Exception var6) {
         logger.error("", var6);
      }

      return resultList;
   }

   @PreAuthorize("hasRole('Content Read Authority')")
   public ResultList getContentFileList(String contentId) throws OpenApiServiceException {
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      ResultList resultList = new ResultList();

      try {
         List filsList = contentInfo.getFileListForApi(contentId);
         resultList.setResultList(filsList);
         resultList.setTotalCount(filsList.size());
      } catch (Exception var5) {
         logger.error("", var5);
      }

      return resultList;
   }

   @PreAuthorize("hasRole('Content Read Authority')")
   public ContentFile getFileInfo(String fileId) throws OpenApiServiceException {
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      ContentFile contentFile = null;

      try {
         contentFile = contentInfo.getFileInfo(fileId);
      } catch (Exception var5) {
         logger.error("", var5);
      }

      return contentFile;
   }

   @PreAuthorize("hasRole('Content Read Authority')")
   public ResultList getContentThumbnailByFileId(String fileId, String size) throws OpenApiServiceException {
      ResultList resultList = new ResultList();
      ContentInfoImpl contentInfo = ContentInfoImpl.getInstance();

      try {
         List urlList = new ArrayList();
         Content content = contentInfo.getThumbnailByFileId(fileId);
         if (content != null) {
            if (StringUtils.isBlank(size)) {
               urlList.add("<![CDATA[MagicInfo/servlet/ContentThumbnail?thumb_id=" + content.getThumb_file_id() + "&thumb_filename=" + content.getThumb_file_name() + "]]>");
            } else {
               String filename = "";
               String var8 = size.toUpperCase();
               byte var9 = -1;
               switch(var8.hashCode()) {
               case -2024701067:
                  if (var8.equals("MEDIUM")) {
                     var9 = 1;
                  }
                  break;
               case 2300:
                  if (var8.equals("HD")) {
                     var9 = 2;
                  }
                  break;
               case 79011047:
                  if (var8.equals("SMALL")) {
                     var9 = 0;
                  }
               }

               switch(var9) {
               case 0:
                  filename = content.getThumb_file_name() + "_SMALL.PNG";
                  break;
               case 1:
                  filename = content.getThumb_file_name() + "_MEDIUM.PNG";
                  break;
               case 2:
                  filename = FilenameUtils.removeExtension(content.getThumb_file_name()) + "_HD.PNG";
                  StringBuffer sb = new StringBuffer();
                  sb.append(ContentUtils.getThumbnailHome());
                  sb.append(File.separator);
                  sb.append(content.getThumb_file_id());
                  sb.append(File.separator);
                  sb.append(filename);
                  File hdThumbnailFile = new File(sb.toString());
                  if (!hdThumbnailFile.exists()) {
                     List contentIdList = contentInfo.getContentIdListByContentFileId(fileId);
                     String contentId = ((Map)contentIdList.get(0)).get("content_id").toString();
                     Map hdThumbnailInfo = contentInfo.getHDThumbnailInfo(contentId);
                     if (((String)hdThumbnailInfo.get("result")).toString().equals("success")) {
                        filename = (String)hdThumbnailInfo.get("filename");
                     } else {
                        filename = "";
                     }
                  }
               }

               if (StringUtils.isNotBlank(filename)) {
                  urlList.add("<![CDATA[MagicInfo/servlet/ContentThumbnail?thumb_id=" + content.getThumb_file_id() + "&thumb_filename=" + filename + "]]>");
               }
            }
         }

         resultList.setResultList(urlList);
      } catch (ConfigException | SQLException var15) {
         logger.error("", var15);
      }

      return resultList;
   }

   @PreAuthorize("hasRole('Content Read Authority')")
   public ResultList getContentThumbnail(String contentId, String mode) throws OpenApiServiceException {
      mode = "HD_THUMBNAIL";
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      ResultList resultList = new ResultList();

      try {
         List urlList = new ArrayList();
         List results = contentInfo.getMovieThumbnails(contentId, mode);
         if (results != null && results.size() > 0) {
            Iterator var23 = results.iterator();

            while(var23.hasNext()) {
               Map thumbFile = (Map)var23.next();
               if (thumbFile != null && thumbFile.get("file_name") != null && thumbFile.get("file_id") != null) {
                  urlList.add("<![CDATA[MagicInfo/servlet/ContentThumbnail?thumb_id=" + thumbFile.get("file_id") + "&thumb_filename=" + thumbFile.get("file_name") + "]]>");
               }
            }
         } else {
            long contnetVersion = contentInfo.getContentActiveVer(contentId);
            Content content = contentInfo.getContentVerInfo(contentId, contnetVersion);
            ContentFile contentFile = contentInfo.getFileInfo(content.getMain_file_id());
            File file = new File(contentFile.getFile_path() + File.separator + contentFile.getFile_name());
            FileManagerImpl fileManager = FileManagerImpl.getInstance();

            try {
               label78: {
                  if (file.exists()) {
                     String HD_THUMBNAIL = "HD_THUMBNAIL";
                     long width = 0L;
                     long height = 0L;
                     String resolution = content.getResolution();
                     if (resolution != null && !resolution.equals("")) {
                        String[] resolutionArray = resolution.split("x");
                        if (resolutionArray != null && resolutionArray.length > 1) {
                           if (resolutionArray[0] != null && !resolutionArray[0].equals("")) {
                              width = Long.valueOf(resolutionArray[0].trim());
                           }

                           if (resolutionArray[1] != null && !resolutionArray[1].equals("")) {
                              height = Long.valueOf(resolutionArray[1].trim());
                           }
                        }
                     }

                     fileManager.createMovieThumbnail(file, content.getContent_id(), "HD_THUMBNAIL_1", "HD_THUMBNAIL", "00:00:01", "1", width, height);
                     fileManager.createMovieThumbnail(file, content.getContent_id(), "HD_THUMBNAIL_2", "HD_THUMBNAIL", "00:00:02", "2", width, height);
                     fileManager.createMovieThumbnail(file, content.getContent_id(), "HD_THUMBNAIL_3", "HD_THUMBNAIL", "00:00:03", "3", width, height);
                     fileManager.createMovieThumbnail(file, content.getContent_id(), "HD_THUMBNAIL_4", "HD_THUMBNAIL", "00:00:04", "4", width, height);
                     fileManager.createMovieThumbnail(file, content.getContent_id(), "HD_THUMBNAIL_5", "HD_THUMBNAIL", "00:00:05", "5", width, height);
                     results = contentInfo.getMovieThumbnails(contentId, mode);
                     if (results == null || results.size() <= 0) {
                        break label78;
                     }

                     Iterator var24 = results.iterator();

                     while(true) {
                        if (!var24.hasNext()) {
                           break label78;
                        }

                        Map thumbFile = (Map)var24.next();
                        if (thumbFile != null && thumbFile.get("file_name") != null && thumbFile.get("file_id") != null) {
                           urlList.add("<![CDATA[MagicInfo/servlet/ContentThumbnail?thumb_id=" + thumbFile.get("file_id") + "&thumb_filename=" + thumbFile.get("file_name") + "]]>");
                        }
                     }
                  }

                  throw new Exception("error create thumbnail. contentfile is not exist!");
               }
            } catch (Exception var21) {
               logger.error("", var21);
               throw new Exception("error create thumbnail. error : " + var21.getMessage());
            }
         }

         resultList.setResultList(urlList);
      } catch (Exception var22) {
         logger.error("", var22);
      }

      return resultList;
   }

   @PreAuthorize("hasRole('Content Write Authority')")
   public static boolean uploadContent(MultipartFile contentFile, String groupId, String categoryIds) throws OpenApiServiceException {
      boolean rtn = false;
      if (contentFile != null) {
      }

      return rtn;
   }
}
