package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.service.MaintenanceService;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.Date;
import javax.servlet.ServletContext;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MaintenanceServiceImpl implements MaintenanceService {
  private ServletContext servletContext;
  
  @Autowired
  public MaintenanceServiceImpl(ServletContext servletContext) {
    this.servletContext = servletContext;
  }
  
  public String saveLogsToFile(String logs) throws IOException {
    Path dir = Paths.get(this.servletContext.getRealPath("insertContents"), new String[0]);
    if (Files.notExists(dir, new java.nio.file.LinkOption[0]))
      Files.createDirectories(dir, (FileAttribute<?>[])new FileAttribute[0]); 
    String timestamp = Long.toString((new Date()).getTime());
    String filename = "results_" + timestamp + ".txt";
    Path textFile = dir.resolve(filename);
    IOUtils.write(logs, Files.newOutputStream(textFile, new java.nio.file.OpenOption[0]), StandardCharsets.UTF_8);
    return filename;
  }
  
  public byte[] readLogsFromFile(String filename) throws IOException {
    String sanitizedFilename = FilenameUtils.getName(filename);
    String fileExt = FilenameUtils.getExtension(sanitizedFilename);
    if (fileExt == null || !fileExt.equals("txt"))
      throw new FileItemValidationException("Filename is not a txt file"); 
    Path textFile = Paths.get(this.servletContext.getRealPath("insertContents"), new String[0]).resolve(sanitizedFilename);
    if (Files.notExists(textFile, new java.nio.file.LinkOption[0]))
      throw new FileNotFoundException("File: \"" + textFile.toAbsolutePath() + "\" does not exist."); 
    return Files.readAllBytes(textFile);
  }
}
