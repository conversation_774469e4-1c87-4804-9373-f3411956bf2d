package com.samsung.magicinfo.webauthor2.service.textImage;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.service.TextImageException;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.model.svg.TextImageDescriptor;
import com.samsung.magicinfo.webauthor2.properties.MagicInfoProperties;
import com.samsung.magicinfo.webauthor2.service.textImage.HTMLgenerationService;
import com.samsung.magicinfo.webauthor2.service.textImage.PNGgenerationService;
import com.samsung.magicinfo.webauthor2.service.textImage.TextImageService;
import com.samsung.magicinfo.webauthor2.util.ServerInfo;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

@Service
public class TextImageServiceImpl implements TextImageService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.textImage.TextImageServiceImpl.class);
  
  private ServletContext servletContext;
  
  private UserData userData;
  
  private PNGgenerationService pngservice;
  
  private HTMLgenerationService htmlservice;
  
  private MagicInfoProperties magicInfoProperties;
  
  private ServerInfo serverInfo;
  
  private static final String TEMP_TEXT_FOLDER = "textImage";
  
  private static final String DEFAULT_FONT_NAME = "SamsungSVDMedium_Latin.ttf";
  
  private static final Logger LOGGER = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.textImage.TextImageServiceImpl.class);
  
  @Inject
  public TextImageServiceImpl(ServletContext servletContext, UserData userData, PNGgenerationService pngservice, HTMLgenerationService htmlservice, MagicInfoProperties magicInfoProperties, ServerInfo serverInfo) {
    this.servletContext = servletContext;
    this.userData = userData;
    this.pngservice = pngservice;
    this.htmlservice = htmlservice;
    this.magicInfoProperties = magicInfoProperties;
    this.serverInfo = serverInfo;
  }
  
  public List<String> transcode(List<TextImageDescriptor> descriptors) {
    List<String> pathStrings = new ArrayList<>();
    for (TextImageDescriptor desc : descriptors)
      pathStrings.add(transcode(desc)); 
    return pathStrings;
  }
  
  public byte[] readPngFile(String name) {
    Path path = getTextImagePath(name, "png");
    try {
      return FileUtils.readFileToByteArray(path.toFile());
    } catch (IOException e) {
      LOGGER.error(e.getMessage());
      return new byte[0];
    } 
  }
  
  public Path getPngFilePath(String name) {
    return getTextImagePath(name, "png");
  }
  
  private URI getFontUriFromLocalServer(String fontUrl) {
    URI targetUri;
    int lastIndexOfMagicInfo = fontUrl.lastIndexOf("/" + this.magicInfoProperties.getWebauthorContext() + "/");
    String contentRelativePath = fontUrl.substring(lastIndexOfMagicInfo, fontUrl.length());
    String relativeContentFolder = "/content";
    try {
      if (fontUrl.indexOf(this.magicInfoProperties.getWebauthorContext() + relativeContentFolder) != -1) {
        int lastIndexOfContent = fontUrl.lastIndexOf(relativeContentFolder);
        String fontFilePathWithParentFolder = fontUrl.substring(lastIndexOfContent + relativeContentFolder.length(), fontUrl.length());
        targetUri = Paths.get(this.magicInfoProperties.getMagicInfoContentsLocationPath().toString(), new String[] { fontFilePathWithParentFolder }).toUri();
      } else {
        URI localURI = new URI(this.serverInfo.getServerUrl());
        targetUri = UriComponentsBuilder.newInstance().scheme(localURI.getScheme()).host(localURI.getHost()).port(localURI.getPort()).path(contentRelativePath).build().encode().toUri();
      } 
    } catch (URISyntaxException e) {
      targetUri = Paths.get(this.servletContext.getRealPath("fonts"), new String[] { fontUrl.split("/")[1] }).toUri();
    } 
    logger.info("URL  [{}] replaced to [{}]", fontUrl, targetUri);
    return targetUri;
  }
  
  public String transcode(TextImageDescriptor descriptor) {
    String name = descriptor.getName();
    String pngPath = createPathToFile(name, "png");
    String htmlPath = createPathToFile(name, "html");
    URI fontURL = Paths.get(this.servletContext.getRealPath("fonts"), new String[] { "SamsungSVDMedium_Latin.ttf" }).toUri();
    String fontData = descriptor.getSource().getData();
    if (!Strings.isNullOrEmpty(fontData))
      if (fontData.startsWith("http")) {
        fontURL = getFontUriFromLocalServer(fontData);
      } else {
        fontURL = Paths.get(this.servletContext.getRealPath("fonts"), new String[] { fontData.split("/")[1] }).toUri();
      }  
    try {
      this.htmlservice.createHTMLfile(htmlPath, descriptor, fontURL.toURL().toString());
    } catch (MalformedURLException ex) {
      LOGGER.error(ex.getMessage());
    } 
    int result = this.pngservice.generatePNG(htmlPath, pngPath, descriptor.getWidth(), descriptor.getHeight());
    if (result == 0)
      return name + ".png"; 
    throw new TextImageException("Error during text image transcode. Process exit code: " + result);
  }
  
  public String transcodeFontHtml(TextImageDescriptor descriptor) {
    String name = descriptor.getName();
    String pngPath = createPathToFile(name, "png");
    String htmlPath = createPathToFile(name, "html");
    String htmlString = "";
    URI fontURL = Paths.get(this.servletContext.getRealPath("fonts"), new String[] { "SamsungSVDMedium_Latin.ttf" }).toUri();
    String fontData = descriptor.getSource().getData();
    if (!Strings.isNullOrEmpty(fontData))
      if (fontData.startsWith("http")) {
        try {
          fontURL = new URI(fontData);
        } catch (URISyntaxException ex) {
          LOGGER.error(ex.getReason());
        } 
      } else {
        fontURL = Paths.get(this.servletContext.getRealPath("insertContents"), new String[] { this.userData.getWorkspaceFolderName(), fontData }).toUri();
      }  
    try {
      htmlString = this.htmlservice.createFontFamilyTextHTMLfile(htmlPath, descriptor, fontURL.toURL().toString());
    } catch (MalformedURLException ex) {
      LOGGER.error(ex.getMessage());
    } 
    return htmlString;
  }
  
  public String transcodeFontImage(TextImageDescriptor descriptor) {
    String name = descriptor.getName();
    MediaSource source = descriptor.getSource();
    String fontFileName = source.getFileName();
    String pngPath = createFontLoadPath(name, "png");
    String htmlPath = createFontLoadPath(name, "html");
    URI fontURL = Paths.get(this.servletContext.getRealPath("fonts"), new String[] { fontFileName }).toUri();
    String fontData = descriptor.getSource().getData();
    if (!Strings.isNullOrEmpty(fontData))
      if (fontData.startsWith("http")) {
        try {
          fontURL = new URI(fontData);
        } catch (URISyntaxException ex) {
          LOGGER.error(ex.getReason());
        } 
      } else {
        fontURL = Paths.get(this.servletContext.getRealPath("fonts"), new String[] { fontData.split("/")[1] }).toUri();
      }  
    try {
      this.htmlservice.createHTMLfile(htmlPath, descriptor, fontURL.toURL().toString());
    } catch (MalformedURLException ex) {
      LOGGER.error(ex.getMessage());
    } 
    int result = this.pngservice.generatePNG(htmlPath, pngPath, descriptor.getWidth(), descriptor.getHeight());
    if (result == 0)
      return name + ".png"; 
    throw new TextImageException("Error during text image transcode. Process exit code: " + result);
  }
  
  private String createPathToFile(String fileName, String extension) {
    try {
      Path path = getTextImagePath(fileName, extension);
      Files.deleteIfExists(path);
      Path parent = path.getParent();
      if (parent != null && Files.notExists(parent, new java.nio.file.LinkOption[0]))
        Files.createDirectories(parent, (FileAttribute<?>[])new FileAttribute[0]); 
      return path.toString();
    } catch (IOException e) {
      throw new TextImageException("Can't delete existing file");
    } 
  }
  
  private Path getTextImagePath(String fileName, String extension) {
    String serverDirectoryPath = this.servletContext.getRealPath("insertContents");
    String userWorkspaceFolder = this.userData.getWorkspaceFolderName();
    String fileWithExtension = fileName + "." + extension;
    return Paths.get(serverDirectoryPath, new String[] { userWorkspaceFolder, "textImage", fileWithExtension });
  }
  
  private String createFontLoadPath(String fileName, String extension) {
    try {
      Path path = getFontImagePath(fileName, extension);
      Files.deleteIfExists(path);
      Path parent = path.getParent();
      if (parent != null && Files.notExists(parent, new java.nio.file.LinkOption[0]))
        Files.createDirectories(parent, (FileAttribute<?>[])new FileAttribute[0]); 
      return path.toString();
    } catch (IOException e) {
      throw new TextImageException("Can't delete existing file");
    } 
  }
  
  private Path getFontImagePath(String fileName, String extension) {
    String serverDirectoryPath = this.servletContext.getRealPath("fonts");
    String fileWithExtension = fileName + "." + extension;
    return Paths.get(serverDirectoryPath, new String[] { fileWithExtension });
  }
}
