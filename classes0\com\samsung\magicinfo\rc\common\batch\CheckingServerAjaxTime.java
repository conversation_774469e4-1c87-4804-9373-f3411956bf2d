package com.samsung.magicinfo.rc.common.batch;

import com.samsung.magicinfo.rc.common.exception.OpenApiServiceException;
import com.samsung.magicinfo.rc.common.memory.ServerAuthorityMemory;
import com.samsung.magicinfo.rc.common.memory.ServerTokenMemory;
import com.samsung.magicinfo.rc.common.queue.ServerQueue;
import com.samsung.magicinfo.rc.common.queue.ServerTimeQueue;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CheckingServerAjaxTime extends HashMap<String, Timestamp> {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.common.batch.CheckingServerAjaxTime.class);
  
  @Autowired
  ServerQueue serverQueue;
  
  @Autowired
  ServerTimeQueue serverTimeQueue;
  
  @Autowired
  ServerTokenMemory serverTokenMemory;
  
  @Autowired
  ServerAuthorityMemory serverAuthorityMemory;
  
  static com.samsung.magicinfo.rc.common.batch.CheckingServerAjaxTime instance;
  
  @PostConstruct
  public void init() {
    instance = new com.samsung.magicinfo.rc.common.batch.CheckingServerAjaxTime();
  }
  
  public synchronized void timeCheck(int second) throws OpenApiServiceException {
    Calendar oCalendar = Calendar.getInstance();
    Timestamp nowTime = new Timestamp(oCalendar.getTime().getTime());
    Iterator<String> it = instance.keySet().iterator();
    try {
      while (it.hasNext()) {
        String deviceId = it.next();
        Timestamp threadTime = (Timestamp)instance.get(deviceId);
        Timestamp time = new Timestamp(threadTime.getTime() + TimeUnit.SECONDS.toMillis(second));
        if (nowTime.after(time)) {
          log.info("[ServerAjaxTime] ServerThread delete! device : " + deviceId);
          if (this.serverQueue.containsKey(deviceId)) {
            try {
              log.info("[ServerTimeQueue] ServerThread delete! device : " + deviceId);
              this.serverQueue.inputQueue(deviceId, "1");
              log.info("inputQueue from device - " + deviceId + " stop");
            } catch (Exception e) {
              log.error("", e);
            } 
          } else {
            log.info("do not exist!");
          } 
          if (this.serverQueue.containsKey(deviceId))
            this.serverQueue.DestroyQueue(deviceId); 
          if (this.serverTimeQueue.containsKey(deviceId))
            this.serverTimeQueue.stop(deviceId); 
          if (this.serverTokenMemory.containsKey(deviceId)) {
            this.serverTokenMemory.deleteToken(deviceId);
            this.serverAuthorityMemory.deleteAuthority(deviceId);
          } 
          if (instance.containsKey(deviceId))
            instance.remove(deviceId); 
        } 
      } 
    } catch (Exception e) {
      log.error("", e);
      log.error("[ServerAjaxTime] timeCheck Error in while");
    } 
  }
  
  public void inputTimeQueue(String deviceId) throws OpenApiServiceException {
    Calendar oCalendar = Calendar.getInstance();
    Timestamp time = new Timestamp(oCalendar.getTime().getTime());
    instance.put(deviceId, time);
  }
  
  public void stop(String DeviceId) throws OpenApiServiceException {
    if (instance.containsKey(DeviceId))
      instance.remove(DeviceId); 
  }
}
