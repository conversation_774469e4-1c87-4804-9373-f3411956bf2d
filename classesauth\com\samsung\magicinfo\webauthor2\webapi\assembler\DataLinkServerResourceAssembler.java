package com.samsung.magicinfo.webauthor2.webapi.assembler;

import com.samsung.magicinfo.webauthor2.model.DataLinkServer;
import com.samsung.magicinfo.webauthor2.webapi.controller.DataLinkQueryController;
import com.samsung.magicinfo.webauthor2.webapi.resource.DataLinkServerResource;
import org.springframework.hateoas.ResourceSupport;
import org.springframework.hateoas.core.DummyInvocationUtils;
import org.springframework.hateoas.mvc.ControllerLinkBuilder;
import org.springframework.hateoas.mvc.ResourceAssemblerSupport;
import org.springframework.stereotype.Component;

@Component
public class DataLinkServerResourceAssembler extends ResourceAssemblerSupport<DataLinkServer, DataLinkServerResource> {
  public DataLinkServerResourceAssembler() {
    super(DataLinkQueryController.class, DataLinkServerResource.class);
  }
  
  public DataLinkServerResource toResource(DataLinkServer dataLinkSource) {
    DataLinkServerResource dataLinkSourceResource = new DataLinkServerResource(dataLinkSource, new org.springframework.hateoas.Link[0]);
    dataLinkSourceResource.add(ControllerLinkBuilder.linkTo(((DataLinkQueryController)DummyInvocationUtils.methodOn(DataLinkQueryController.class, new Object[0])).getDatalinkTables(dataLinkSource.getServerName(), "datalink")).withRel("datalink_tables"));
    dataLinkSourceResource.add(ControllerLinkBuilder.linkTo(((DataLinkQueryController)DummyInvocationUtils.methodOn(DataLinkQueryController.class, new Object[0])).getDatalinkTables(dataLinkSource.getServerName(), "dataview")).withRel("dataview_tables"));
    return dataLinkSourceResource;
  }
}
