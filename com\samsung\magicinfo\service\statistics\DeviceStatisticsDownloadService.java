package com.samsung.magicinfo.service.statistics;

import com.samsung.common.export.ExcelBuilder;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.servlet.ModelAndView;

@Scope("sigleton")
public class DeviceStatisticsDownloadService {
   private String returnUrl;
   private ExcelBuilder excelBuilder;

   public DeviceStatisticsDownloadService() {
      super();
   }

   public void setReturnUrl(String returnUrl) {
      this.returnUrl = returnUrl;
   }

   public ModelAndView downloadPage() {
      ModelAndView mvc = new ModelAndView(this.returnUrl);
      return mvc;
   }

   public void downloadExcelFile(Map dataMap, HttpServletResponse response) {
      String fileName = (String)dataMap.get("fileName");
      String sheetName = (String)dataMap.get("sheetName");
      String[] columnNames = (String[])((String[])dataMap.get("columnNames"));
      String[] fieldNames = (String[])((String[])dataMap.get("fieldNames"));
      Object[] dataList = (Object[])((Object[])dataMap.get("dataList"));
      this.excelBuilder = new ExcelBuilder(sheetName);
      this.excelBuilder.setAlign((short)2);
      this.excelBuilder.setBold(true);
      this.excelBuilder.setFontName("Arial");
      this.excelBuilder.setFontSize((short)11);
      this.excelBuilder.setBgColor(ExcelBuilder.COLOR_GREY_25_PERCENT);
      this.excelBuilder.setBorder(true);
      this.excelBuilder.addTitleRow(0, fieldNames);
      this.excelBuilder.setDefaultStyle();
      this.excelBuilder.setColumnWidth(new double[]{50.0D});
      this.excelBuilder.addRows(0, columnNames, dataList);
      this.excelBuilder.setDefaultStyle();
      this.excelBuilder.download(fileName, response);
   }

   public void downloadExcelSheetsFile(Map dataMap, HttpServletResponse response) {
      String fileName = (String)dataMap.get("fileName");
      String[] sheetName = (String[])((String[])dataMap.get("sheetName"));
      List columnNames = (List)dataMap.get("columnNames");
      List fieldNames = (List)dataMap.get("fieldNames");
      List dataList = (List)dataMap.get("dataList");
      this.excelBuilder = new ExcelBuilder(sheetName);
      this.excelBuilder.setAlign((short)2);
      this.excelBuilder.setBold(true);
      this.excelBuilder.setFontName("Arial");
      this.excelBuilder.setFontSize((short)11);
      this.excelBuilder.setBgColor(ExcelBuilder.COLOR_GREY_25_PERCENT);
      this.excelBuilder.setBorder(true);
      this.excelBuilder.setDefaultStyle();
      this.excelBuilder.setColumnWidth(0, new double[]{30.0D});
      this.excelBuilder.setColumnWidth(1, new double[]{30.0D});
      this.excelBuilder.setColumnWidth(2, new double[]{30.0D});
      this.excelBuilder.addTitleRow(0, (String[])fieldNames.get(0));
      this.excelBuilder.addTitleRow(1, (String[])fieldNames.get(1));
      this.excelBuilder.addTitleRow(2, (String[])fieldNames.get(2));
      this.excelBuilder.addRows(0, (String[])columnNames.get(0), (Object[])dataList.get(0));
      this.excelBuilder.addRows(1, (String[])columnNames.get(1), (Object[])dataList.get(1));
      this.excelBuilder.addRows(2, (String[])columnNames.get(2), (Object[])dataList.get(2));
      this.excelBuilder.download(fileName, response);
   }

   public void downloadExcel2SheetsFile(Map dataMap, HttpServletResponse response) {
      String fileName = (String)dataMap.get("fileName");
      String[] sheetName = (String[])((String[])dataMap.get("sheetName"));
      List columnNames = (List)dataMap.get("columnNames");
      List fieldNames = (List)dataMap.get("fieldNames");
      List dataList = (List)dataMap.get("dataList");
      this.excelBuilder = new ExcelBuilder(sheetName);
      this.excelBuilder.setAlign((short)2);
      this.excelBuilder.setBold(true);
      this.excelBuilder.setFontName("Arial");
      this.excelBuilder.setFontSize((short)11);
      this.excelBuilder.setBgColor(ExcelBuilder.COLOR_GREY_25_PERCENT);
      this.excelBuilder.setBorder(true);
      this.excelBuilder.setDefaultStyle();
      this.excelBuilder.setColumnWidth(0, new double[]{30.0D});
      this.excelBuilder.setColumnWidth(1, new double[]{30.0D});
      this.excelBuilder.addTitleRow(0, (String[])fieldNames.get(0));
      this.excelBuilder.addTitleRow(1, (String[])fieldNames.get(1));
      this.excelBuilder.addRows(0, (String[])columnNames.get(0), (Object[])dataList.get(0));
      this.excelBuilder.addRows(1, (String[])columnNames.get(1), (Object[])dataList.get(1));
      this.excelBuilder.download(fileName, response);
   }

   public void saveExcelFile(Map dataMap) throws IOException {
      String fileName = (String)dataMap.get("fileName");
      String sheetName = (String)dataMap.get("sheetName");
      String[] columnNames = (String[])((String[])dataMap.get("columnNames"));
      String[] fieldNames = (String[])((String[])dataMap.get("fieldNames"));
      Object[] dataList = (Object[])((Object[])dataMap.get("dataList"));
      this.excelBuilder = new ExcelBuilder(sheetName);
      this.excelBuilder.setAlign((short)2);
      this.excelBuilder.setBold(true);
      this.excelBuilder.setFontSize((short)11);
      this.excelBuilder.setBgColor(ExcelBuilder.COLOR_GREY_25_PERCENT);
      this.excelBuilder.setBorder(true);
      this.excelBuilder.addTitleRow(0, fieldNames);
      this.excelBuilder.setDefaultStyle();
      this.excelBuilder.setColumnWidth(new double[]{50.0D});
      this.excelBuilder.addRows(0, columnNames, dataList);
      this.excelBuilder.setDefaultStyle();
      this.excelBuilder.save(fileName);
   }
}
