package com.samsung.common.utils;

import java.lang.reflect.InvocationTargetException;
import org.apache.commons.beanutils.BeanUtilsBean;

public class NullAwareBeanUtilsBean extends BeanUtilsBean {
   public NullAwareBeanUtilsBean() {
      super();
   }

   public void copyProperty(Object bean, String name, Object value) throws IllegalAccessException, InvocationTargetException {
      if (value != null) {
         if (!name.equalsIgnoreCase("serialVersionUID")) {
            super.copyProperty(bean, name, value);
         }
      }
   }
}
