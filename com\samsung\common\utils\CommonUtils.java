package com.samsung.common.utils;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.content.constants.ContentConstants;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceDao;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.dao.SlmLicenseDao;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.scheduler.ScheduleManager;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipException;
import java.util.zip.ZipOutputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.ftpserver.db.DownloadInfo;
import org.apache.ftpserver.db.DownloadInfoImpl;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SimpleScheduleBuilder;
import org.quartz.SimpleTrigger;
import org.quartz.TriggerBuilder;

public class CommonUtils {
   static Logger logger = LoggingManagerV2.getLogger(CommonUtils.class);

   public CommonUtils() {
      super();
   }

   public static boolean isUsable(String productType, String[] array) {
      boolean result = false;

      for(int i = 0; i < array.length; ++i) {
         if (productType.equalsIgnoreCase(array[i])) {
            return true;
         }
      }

      return result;
   }

   public static boolean checkNull(String parameter) {
      boolean result = false;
      if (parameter != null && !parameter.equalsIgnoreCase("") && !parameter.equalsIgnoreCase("null")) {
         result = true;
      }

      return result;
   }

   public static void deleteScheduleJob(String programId) {
      deleteJob(programId, "ScheduleJobGroup");
   }

   public static boolean deleteJob(String jobName, String jobGroupName) {
      Scheduler scheduler = ScheduleManager.getSchedulerInstance();
      boolean result = false;
      JobKey jobKey = JobKey.jobKey(jobName, jobGroupName);

      try {
         result = scheduler.deleteJob(jobKey);
      } catch (SchedulerException var6) {
         logger.error("", var6);
      }

      return result;
   }

   public static boolean isValidMACAddress(String device_id) {
      Pattern p = Pattern.compile("^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$");
      Matcher m = p.matcher(device_id);
      return m.find();
   }

   public static boolean pauseJob(String jobName, String jobGroupName) {
      Scheduler scheduler = ScheduleManager.getSchedulerInstance();
      boolean result = true;
      JobKey jobKey = JobKey.jobKey(jobName, jobGroupName);

      try {
         scheduler.pauseJob(jobKey);
      } catch (SchedulerException var6) {
         result = false;
         logger.error("", var6);
      }

      return result;
   }

   public static boolean resumeJob(String jobName, String jobGroupName) {
      Scheduler scheduler = ScheduleManager.getSchedulerInstance();
      boolean result = true;
      JobKey jobKey = JobKey.jobKey(jobName, jobGroupName);

      try {
         scheduler.resumeJob(jobKey);
      } catch (SchedulerException var6) {
         result = false;
         logger.error("", var6);
      }

      return result;
   }

   public static JobDetail getJobDetail(String jobName, String jobGroupName, Class jobClass) {
      JobKey jobKey = JobKey.jobKey(jobName, jobGroupName);
      JobDetail jobdetail = JobBuilder.newJob(jobClass).withIdentity(jobKey).build();
      return jobdetail;
   }

   public static SimpleTrigger getSimpleTrigger(String jobName, String jobGroupName, Date start_time, long intervalInSeconds) {
      SimpleTrigger trigger = (SimpleTrigger)TriggerBuilder.newTrigger().withIdentity(jobName, jobGroupName).startAt(start_time).withSchedule(SimpleScheduleBuilder.simpleSchedule().withIntervalInMilliseconds((long)((int)intervalInSeconds)).repeatForever()).build();
      return trigger;
   }

   public static SimpleTrigger getSimpleTrigger(String jobName, String jobGroupName, Date startTime, Date endTime, int repeatCount, long repeatInterval) {
      SimpleTrigger trigger = (SimpleTrigger)TriggerBuilder.newTrigger().withIdentity(jobName, jobGroupName).startAt(startTime).endAt(endTime).withSchedule(SimpleScheduleBuilder.simpleSchedule().withRepeatCount(repeatCount).withIntervalInSeconds((int)(repeatInterval / 1000L))).build();
      return trigger;
   }

   public static SimpleTrigger getSimpleTrigger(String jobName, String jobGroupName, Date startTime, Date endTime, long intervalInSeconds) {
      SimpleTrigger trigger = (SimpleTrigger)TriggerBuilder.newTrigger().withIdentity(jobName, jobGroupName).startAt(startTime).endAt(endTime).withSchedule(SimpleScheduleBuilder.simpleSchedule().withIntervalInMilliseconds((long)((int)intervalInSeconds)).repeatForever()).build();
      return trigger;
   }

   public static boolean isAuthenticateDevice(String deviceId, String password) throws SQLException {
      boolean result = false;
      DownloadInfo dao = DownloadInfoImpl.getInstance();
      result = dao.isAuthenticateDevice(deviceId, password);
      return result;
   }

   public static String cutLen(char[] str, int len) {
      int l = 0;

      for(int i = 0; i < str.length; ++i) {
         l += str[i] > 128 ? 2 : 1;
         if (l > len) {
            return String.copyValueOf(str, 0, i) + "..";
         }
      }

      return String.copyValueOf(str);
   }

   public static int safeLongToInt(long l) {
      if (l >= -2147483648L && l <= 2147483647L) {
         return (int)l;
      } else {
         throw new IllegalArgumentException(l + " cannot be cast to int without changing its value.");
      }
   }

   public static void addToZipFile(String fileName, ZipOutputStream zos) throws FileNotFoundException, IOException {
      addToZipFile(fileName, "", zos);
   }

   public static void addToZipFile(String fileName, String path, ZipOutputStream zos) throws FileNotFoundException, IOException {
      fileName = SecurityUtils.directoryTraversalChecker(fileName, (String)null);
      File file = new File(fileName);
      byte[] buffer = new byte[1024];
      FileInputStream fis = new FileInputStream(file);

      try {
         if (StringUtils.isBlank(path)) {
            zos.putNextEntry(new ZipEntry(file.getName()));
         } else {
            zos.putNextEntry(new ZipEntry(path + File.separator + file.getName()));
         }

         int length;
         while((length = fis.read(buffer)) > 0) {
            zos.write(buffer, 0, length);
         }
      } catch (ZipException var22) {
         logger.error("", var22);
      } catch (Exception var23) {
         logger.error("", var23);
      } finally {
         try {
            zos.closeEntry();
         } catch (Exception var21) {
            logger.error("", var21);
         }

         try {
            fis.close();
         } catch (Exception var20) {
            logger.error("", var20);
         }

      }

   }

   public static void fileCopy(String inFileName, String outFileName) {
      FileInputStream fis = null;
      FileOutputStream fos = null;

      try {
         fis = new FileInputStream(inFileName);
         fos = new FileOutputStream(outFileName);
         boolean var4 = false;

         int data;
         while((data = fis.read()) != -1) {
            fos.write(data);
         }
      } catch (IOException var13) {
         logger.error("", var13);
      } finally {
         try {
            if (fis != null) {
               fis.close();
            }

            if (fos != null) {
               fos.close();
            }
         } catch (IOException var12) {
            logger.error("", var12);
         }

      }

   }

   public static boolean deleteDirectoryRecursive(File filePath) {
      if (filePath == null) {
         return false;
      } else {
         if (filePath.exists()) {
            File[] files = filePath.listFiles();
            if (files == null) {
               return false;
            }

            for(int i = 0; i < files.length; ++i) {
               if (files[i] == null) {
                  return false;
               }

               if (files[i].isDirectory()) {
                  deleteDirectoryRecursive(files[i]);
               } else {
                  files[i].delete();
               }
            }
         }

         return filePath.delete();
      }
   }

   public static Long getContentDuration(Content content, int beforeOrder, String selEffectList, List contentList, String productType, String deviceType) {
      Long content_duration = 0L;
      if (content != null && content.getPlay_time() != null && !content.getPlay_time().equals("-") && !content.getPlay_time().equals("")) {
         if (productType.equalsIgnoreCase("PREMIUM")) {
            content_duration = ContentUtils.getPlayTimeStr(content.getPlay_time());
         }

         return content_duration;
      } else {
         Map contentDurationMap = getContentDurationByEffectList(selEffectList);
         if (contentDurationMap.containsKey(beforeOrder + "")) {
            content_duration = Long.valueOf((String)contentDurationMap.get(beforeOrder + ""));
            return content_duration;
         } else {
            if (contentList != null) {
               Iterator var8 = contentList.iterator();

               while(var8.hasNext()) {
                  PlaylistContent playlistContent = (PlaylistContent)var8.next();
                  if (playlistContent.getContent_id().equalsIgnoreCase(content.getContent_id()) && playlistContent.getContent_order() == (long)beforeOrder) {
                     content_duration = playlistContent.getContent_duration();
                     return content_duration;
                  }
               }
            }

            content_duration = getDefaultContentDuration(productType, deviceType, content.getContent_duration());
            return content_duration;
         }
      }
   }

   private static Long getDefaultContentDuration(String productType, String deviceType, Long contentDuration) {
      if (contentDuration == 0L && isUsable(productType, new String[]{"PREMIUM"})) {
         if (deviceType.equalsIgnoreCase("iPLAYER")) {
            contentDuration = ContentConstants.CONTENT_DURATION;
         } else if (deviceType.equalsIgnoreCase("SPLAYER")) {
            contentDuration = ContentConstants.LITE_CONTENT_DURATION;
         } else if (!deviceType.equalsIgnoreCase("S2PLAYER") && !deviceType.equalsIgnoreCase("S3PLAYER")) {
            contentDuration = ContentConstants.LITE_CONTENT_DURATION;
         } else {
            contentDuration = ContentConstants.LITE_CONTENT_DURATION;
         }
      }

      return contentDuration;
   }

   private static Map getContentDurationByEffectList(String effectList) {
      Map map = new HashMap();
      if (effectList != null && !effectList.equalsIgnoreCase("")) {
         String[] arrEffectList = effectList.split(",");
         String[] var3 = arrEffectList;
         int var4 = arrEffectList.length;

         for(int var5 = 0; var5 < var4; ++var5) {
            String effect = var3[var5];
            String[] arrEffectItem = effect.split("↑");
            map.put(arrEffectItem[8], arrEffectItem[7]);
         }
      }

      return map;
   }

   public static Long getMinPriorityByDeviceinfo(String deviceType, String deviceTypeVersion) throws SQLException {
      DeviceDao deviceDao = new DeviceDao();
      Long priority = deviceDao.getMinPrioritybyDeviceType(deviceType, deviceTypeVersion);
      return priority;
   }

   private static int findIndex(String arg, String[] array) {
      int index = 0;
      String[] var3 = array;
      int var4 = array.length;

      for(int var5 = 0; var5 < var4; ++var5) {
         String element = var3[var5];
         if (element.equals(arg)) {
            return index;
         }

         ++index;
      }

      if (index >= array.length) {
         return 0;
      } else {
         return index;
      }
   }

   private static int findIndex(String arg, Float[] array) {
      int index = 0;
      Float[] var3 = array;
      int var4 = array.length;

      for(int var5 = 0; var5 < var4; ++var5) {
         Float element = var3[var5];
         if (Float.parseFloat(arg) == element) {
            return index;
         }

         ++index;
      }

      if (index >= array.length) {
         return 0;
      } else {
         return index;
      }
   }

   public static String[] getAllSupportDeviceListByDeviceFilterList(String[] deviceFilterList) {
      List resultList = new ArrayList();
      int maxLength = CommonDataConstants.ALL_CONTENT_DEVICE_TYPE_PRIORITY_ARRAY.length;
      int minPriority = maxLength;
      int tPriority = false;
      int i;
      if (deviceFilterList != null) {
         for(i = 0; i < deviceFilterList.length; ++i) {
            if (deviceFilterList[i].equals("WPLAYER")) {
               resultList.add("WPLAYER");
            } else if (deviceFilterList[i].equals("APLAYER")) {
               resultList.add("APLAYER");
            } else {
               int tPriority = findIndex(deviceFilterList[i], CommonDataConstants.ALL_CONTENT_DEVICE_TYPE_PRIORITY_ARRAY);
               if (tPriority < minPriority) {
                  minPriority = tPriority;
               }
            }
         }
      }

      if (minPriority < maxLength) {
         for(i = minPriority; i < maxLength; ++i) {
            resultList.add(CommonDataConstants.ALL_CONTENT_DEVICE_TYPE_PRIORITY_ARRAY[i]);
         }
      }

      return (String[])resultList.toArray(new String[resultList.size()]);
   }

   public static String[] getIntersectionDeviceType(String[] deviceFilterList1, String[] deviceFilterList2) {
      List resultList = new ArrayList();
      int wPlayerCount = 0;
      int aPlayerCount = 0;
      int[] minPriority = new int[]{-1, -1};
      boolean init = false;

      int a;
      for(a = 0; a < 2; ++a) {
         int tPriority = false;
         String[] deviceFilterList;
         if (a == 0) {
            deviceFilterList = deviceFilterList1;
         } else {
            deviceFilterList = deviceFilterList2;
         }

         if (deviceFilterList != null && deviceFilterList.length > 0) {
            for(int i = 0; i < deviceFilterList.length; ++i) {
               if (deviceFilterList[i].equals("WPLAYER")) {
                  ++wPlayerCount;
               } else if (deviceFilterList[i].equals("APLAYER")) {
                  ++aPlayerCount;
               } else {
                  int tPriority = findIndex(deviceFilterList[i], CommonDataConstants.ALL_CONTENT_DEVICE_TYPE_PRIORITY_ARRAY);
                  if (tPriority > minPriority[a]) {
                     minPriority[a] = tPriority;
                  }
               }
            }
         } else {
            if (deviceFilterList != null) {
               String[] emptyArr = new String[0];
               return emptyArr;
            }

            init = true;
            minPriority[a] = a == 0 ? minPriority[1] : minPriority[0];
         }
      }

      a = minPriority[0] < minPriority[1] ? minPriority[0] : minPriority[1];

      for(int i = 0; i <= a; ++i) {
         resultList.add(CommonDataConstants.ALL_CONTENT_DEVICE_TYPE_PRIORITY_ARRAY[i]);
      }

      if (!init && wPlayerCount == 2 || init && wPlayerCount == 1) {
         resultList.add("WPLAYER");
      }

      if (!init && aPlayerCount == 2 || init && aPlayerCount == 1) {
         resultList.add("APLAYER");
      }

      return (String[])resultList.toArray(new String[resultList.size()]);
   }

   public static String convertDeviceTypeAndVerisonToDeviceType(String deviceType, String deviceTypeVersion) {
      String result = null;
      if (deviceType.equals("SPLAYER")) {
         int arrLength = CommonDataConstants.ALL_DEVICE_TYPE_VERSION_ARRAY.length;
         if (arrLength == CommonDataConstants.ALL_DEVICE_TYPE_SOC_ARRAY.length) {
            int index = findIndex(deviceTypeVersion, CommonDataConstants.ALL_DEVICE_TYPE_VERSION_ARRAY);
            result = CommonDataConstants.ALL_DEVICE_TYPE_SOC_ARRAY[index];
         }
      }

      return result == null ? deviceType : result;
   }

   public static boolean isInteger(String s) {
      try {
         Integer.parseInt(s);
         return true;
      } catch (NumberFormatException var2) {
         return false;
      } catch (NullPointerException var3) {
         return false;
      }
   }

   public static boolean isLong(String s) {
      try {
         Long.parseLong(s);
         return true;
      } catch (NumberFormatException var2) {
         return false;
      } catch (NullPointerException var3) {
         return false;
      }
   }

   public static int getConfigNumber(String configKey, int defaultValue) {
      String str = null;

      try {
         str = CommonConfig.get(configKey);
      } catch (ConfigException var4) {
         return defaultValue;
      }

      if (str != null && !str.isEmpty()) {
         return isInteger(str) ? Integer.valueOf(str) : defaultValue;
      } else {
         return defaultValue;
      }
   }

   public static long getConfigLongNumber(String configKey, long defaultValue, long maxValue) {
      String str = null;

      try {
         str = CommonConfig.get(configKey);
      } catch (ConfigException var8) {
         return defaultValue;
      }

      if (str != null && !str.isEmpty()) {
         if (isLong(str)) {
            long number = Long.valueOf(str);
            if (number <= 0L) {
               return defaultValue;
            } else {
               return number > maxValue ? maxValue : number;
            }
         } else {
            return defaultValue;
         }
      } else {
         return defaultValue;
      }
   }

   public static boolean checkAvailable(String menu) {
      int startIndex = 1;
      int results = 20;
      new ArrayList();
      SlmLicenseDao licenseDao = new SlmLicenseDao();
      Map condition = new HashMap();
      condition.put("sort", "reg_date");
      condition.put("order", "desc");

      try {
         PagedListInfo listInfo = licenseDao.getLicenseList(startIndex, results, condition);
         List list = listInfo.getPagedResultList();
         if (list != null && list.size() > 0) {
            if (list.size() == 1) {
               SlmLicenseEntity info = (SlmLicenseEntity)list.get(0);
               if (info.getProduct_code().equals("01064A")) {
                  if (!"schedule".equals(menu) && !"content".equals(menu) && !"playlist".equals(menu) && !"ruleset".equals(menu)) {
                     return true;
                  }

                  return false;
               }
            }

            return true;
         } else {
            return false;
         }
      } catch (Exception var8) {
         return false;
      }
   }

   public static int checkInt(String value, int defaultValue) {
      int result = defaultValue;
      if (value != null && Pattern.matches("^[0-9]*$", value)) {
         result = Integer.parseInt(value);
      }

      return result;
   }

   public static boolean isNotEmpty(String param) {
      boolean result = false;
      if (param != null && !param.equalsIgnoreCase("") && !param.isEmpty()) {
         result = true;
      }

      return result;
   }

   public static boolean isIncludeSpecialChar(String str) {
      String pattern = "^[a-zA-Z0-9.]*$";
      boolean result = !Pattern.matches(pattern, str);
      return result;
   }

   public static boolean isContainingInvalidCharacters(String str) {
      String pattern = "^[a-zA-Z0-9._-]*$";
      boolean result = !Pattern.matches(pattern, str);
      return result;
   }

   public static boolean isManageableContentByUser(User user, String contentId) throws Exception {
      boolean result = false;
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      String userId = user.getUser_id();
      String userOrgName = user.getOrganization();
      long userOrgId = userGroupInfo.getOrgGroupIdByName(userOrgName);
      Content content = contentInfo.getContentActiveVerInfo(contentId);
      if (content == null) {
         content = contentInfo.getTLFDInfo(contentId);
      }

      if (content != null) {
         long contentOrgId = content.getOrganization_id();
         if ("admin".equals(userId) || userOrgId == contentOrgId) {
            result = true;
         }
      }

      return result;
   }

   public static boolean isManageablePlaylistByUser(User user, String playlistId) throws Exception {
      boolean result = false;
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
      String userId = user.getUser_id();
      String userOrgName = user.getOrganization();
      long userOrgId = userGroupInfo.getOrgGroupIdByName(userOrgName);
      Playlist playlist = playlistInfo.getPlaylistActiveVerInfo(playlistId);
      long playlistOrgId = playlist.getOrganization_id();
      if ("admin".equals(userId) || userOrgId == playlistOrgId) {
         result = true;
      }

      return result;
   }

   public static boolean isManageableProgramByUser(User user, String programId) throws Exception {
      boolean result = false;
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      String userId = user.getUser_id();
      String userOrgName = user.getOrganization();
      long userOrgId = userGroupInfo.getOrgGroupIdByName(userOrgName);
      ProgramEntity program = scheduleInfo.getProgram(programId);
      long programOrgId = program.getProgram_group_id();
      if ("admin".equals(userId) || userOrgId == programOrgId) {
         result = true;
      }

      return result;
   }

   public static Map ConverObjectToMap(Object obj) {
      try {
         Field[] fields = obj.getClass().getDeclaredFields();
         Map resultMap = new HashMap();

         for(int i = 0; i <= fields.length - 1; ++i) {
            fields[i].setAccessible(true);
            resultMap.put(fields[i].getName(), fields[i].get(obj));
         }

         return resultMap;
      } catch (IllegalArgumentException var4) {
         logger.error("", var4);
      } catch (IllegalAccessException var5) {
         logger.error("", var5);
      }

      return null;
   }

   public static Map convertJsonToMap(String body) {
      HashMap map = new HashMap();
      JSONObject jObject = new JSONObject(body);
      Iterator keys = jObject.keys();

      while(keys.hasNext()) {
         String key = (String)keys.next();
         String value = jObject.getString(key);
         map.put(key, value);
      }

      return map;
   }
}
