package com.samsung.magicinfo.webauthor2.model.factory;

import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.ContentGroup;
import com.samsung.magicinfo.webauthor2.repository.model.ContentData;
import com.samsung.magicinfo.webauthor2.repository.model.ContentGroupData;
import java.util.List;

public interface ContentFactory {
  Content fromData(ContentData paramContentData) throws IllegalArgumentException;
  
  List<Content> fromData(List<ContentData> paramList);
  
  ContentGroup fromGroupData(ContentGroupData paramContentGroupData) throws IllegalArgumentException;
  
  List<ContentGroup> fromGroupData(List<ContentGroupData> paramList);
}
