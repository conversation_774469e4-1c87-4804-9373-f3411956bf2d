package com.samsung.magicinfo.openapi.custom.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.magicinfo.framework.content.manager.DataLinkOpenApiManager;
import com.samsung.magicinfo.framework.setup.dao.RmServerDao;
import com.samsung.magicinfo.framework.setup.entity.DatalinkServerEntity;
import com.samsung.magicinfo.framework.setup.entity.RmServerEntity;
import com.samsung.magicinfo.framework.setup.manager.DatalinkServer;
import com.samsung.magicinfo.framework.setup.manager.DatalinkServerImpl;
import com.samsung.magicinfo.openapi.custom.openEntity.ResultList;
import com.samsung.magicinfo.openapi.impl.OpenApiExceptionCode;
import com.samsung.magicinfo.openapi.impl.OpenApiServiceException;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.compiler.MOTree;
import com.samsung.magicinfo.protocol.repository.MORepository;
import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.URL;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.KeyManager;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

@Service("openApiServerStatusService")
@Scope("prototype")
public class ServerStatusService {
   static Logger logger = LoggingManagerV2.getLogger(PremiumClientService.class);
   String token = null;

   public ServerStatusService() {
      super();
   }

   public String getToken() {
      return this.token;
   }

   public void setToken(String token) {
      this.token = token;
   }

   public String checkInternalConn(int val) throws OpenApiServiceException, Exception {
      String result = "";
      if (val == 0) {
         result = this.checkDBStatus();
      } else if (val == 1) {
         result = this.checkWSRMStatus();
      }

      logger.error("\n [checkInternalConn] VAlUE : " + val + ", result : " + result.toString());
      return result;
   }

   public ResultList checkExternalConn(int val) throws Exception {
      ResultList resultList = new ResultList();
      if (val == 0) {
         resultList = this.checkDatalinkServer();
      } else if (val == 1) {
         resultList = this.checkDownloadServer();
      } else if (val == 2) {
         resultList = this.checkS2RemoteServer();
      }

      logger.error("\n [checkExternalConn] VAlUE : " + val + ", resultList : " + resultList.toString());
      return resultList;
   }

   public String getNetworkTraffic() throws InterruptedException {
      return null;
   }

   private String checkDBStatus() throws OpenApiServiceException, ConfigException {
      Connection conn = null;
      String result = "SUCCESS";

      try {
         Class.forName(CommonConfig.get("wsrm.driver").trim());
         String url = CommonConfig.get("wsrm.url").trim();
         String user_name = CommonConfig.get("wsrm.username").trim();
         String password = CommonConfig.get("wsrm.password").trim();
         conn = DriverManager.getConnection(url, user_name, password);
         if (conn == null) {
            result = "FAIL";
         }

         conn.close();
      } catch (ClassNotFoundException var6) {
         result = "FAIL";
      } catch (Exception var7) {
         result = "FAIL";
      }

      return result;
   }

   private String checkWSRMStatus() throws OpenApiServiceException, Exception {
      String result = "SUCCESS";
      MOTree moTree = MORepository.getInstance().getMOTree("Default");
      if (moTree == null) {
         result = "FAIL";
      }

      return result;
   }

   private ResultList checkDatalinkServer() throws OpenApiServiceException {
      ResultList resultList = new ResultList();
      List result = new ArrayList();
      DataLinkOpenApiManager dataLinkOpenApiManager = new DataLinkOpenApiManager();
      DatalinkServer datalinkServerInfo = DatalinkServerImpl.getInstance();
      InputStreamReader isr = null;

      try {
         List ipList = datalinkServerInfo.getDatalinkServerList();
         Iterator itServerNameList = ipList.iterator();

         label110:
         while(true) {
            while(true) {
               if (!itServerNameList.hasNext()) {
                  break label110;
               }

               DatalinkServerEntity dataLinkServer = (DatalinkServerEntity)itServerNameList.next();
               String serverIp = dataLinkServer.getIp_address();
               String serverPort = dataLinkServer.getPort().toString();
               Boolean serverSsl = dataLinkServer.getUse_ssl();
               String tableListUrl = dataLinkOpenApiManager.makeUrl(serverIp, serverPort, serverSsl);
               URL url = new URL(tableListUrl);
               isr = dataLinkOpenApiManager.getConnectionReader(url, serverSsl);
               StringBuffer sb = new StringBuffer();
               if (isr != null) {
                  int c;
                  while((c = isr.read()) != -1) {
                     sb.append((char)c);
                  }

                  isr.close();
                  if (sb.toString().trim().length() < 1) {
                     result.add(serverIp + ";false");
                  } else {
                     result.add(serverIp + ";true");
                  }
               } else {
                  result.add(serverIp + ";false");
               }
            }
         }
      } catch (Exception var24) {
         logger.error("", var24);
      } finally {
         if (isr != null) {
            try {
               isr.close();
            } catch (Exception var23) {
               logger.error("", var23);
            }
         }

      }

      resultList.setResultList(result);
      resultList.setTotalCount(result.size());
      return resultList;
   }

   private ResultList checkDownloadServer() throws OpenApiServiceException, Exception {
      return null;
   }

   private ResultList checkS2RemoteServer() throws Exception {
      ResultList resultList = new ResultList();
      DocumentBuilderFactory factory = DocumentUtils.getDocumentBuilderFactoryInstance();
      DocumentBuilder builder = factory.newDocumentBuilder();
      RmServerDao rmserverDao = new RmServerDao();
      List lists = rmserverDao.getRmserverInfoList();
      List result = new ArrayList();
      String rmServer = null;
      String return_code = null;
      if (lists == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.RM001[0], OpenApiExceptionCode.RM001[1]);
      } else {
         if (lists != null) {
            HttpClient httpclient = new DefaultHttpClient();

            for(int i = 0; i < lists.size(); ++i) {
               if (((RmServerEntity)lists.get(i)).getUse_ssl()) {
                  rmServer = "https://";
               } else {
                  rmServer = "http://";
               }

               rmServer = rmServer + ((RmServerEntity)lists.get(i)).getIp_address() + ":" + ((RmServerEntity)lists.get(i)).getPort() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
               HttpEntity entity;
               String line;
               if (((RmServerEntity)lists.get(i)).getUse_ssl()) {
                  URL url = new URL(rmServer);
                  HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
                  conn.setHostnameVerifier(new HostnameVerifier() {
                     public boolean verify(String hostname, SSLSession session) {
                        return true;
                     }
                  });
                  SSLContext context = SSLContext.getInstance("TLS");
                  context.init((KeyManager[])null, new TrustManager[]{new X509TrustManager() {
                     public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                     }

                     public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                        FileInputStream fis = null;

                        try {
                           KeyStore trustStore = KeyStore.getInstance("JKS");
                           fis = new FileInputStream("MagicInfoIdentity.jks");
                           trustStore.load(fis, CommonConfig.get("keystore.identity.password").trim().toCharArray());
                           TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
                           tmf.init(trustStore);
                           TrustManager[] tms = tmf.getTrustManagers();
                           ((X509TrustManager)tms[0]).checkServerTrusted(chain, authType);
                           fis.close();
                        } catch (ConfigException var21) {
                           ServerStatusService.logger.error(var21);
                        } catch (KeyStoreException var22) {
                           ServerStatusService.logger.error("", var22);
                        } catch (NoSuchAlgorithmException var23) {
                           ServerStatusService.logger.error("", var23);
                        } catch (IOException var24) {
                        } finally {
                           if (fis != null) {
                              try {
                                 fis.close();
                              } catch (IOException var20) {
                                 ServerStatusService.logger.error("", var20);
                              }
                           }

                        }

                     }

                     public X509Certificate[] getAcceptedIssuers() {
                        return null;
                     }
                  }}, (SecureRandom)null);
                  conn.setSSLSocketFactory(context.getSocketFactory());
                  conn.setConnectTimeout(10000);

                  try {
                     conn.connect();
                     conn.setInstanceFollowRedirects(true);
                     InputStream in = conn.getInputStream();
                     BufferedReader reader = new BufferedReader(new InputStreamReader(in));
                     entity = null;

                     String line;
                     for(line = new String(); (line = reader.readLine()) != null; line = line + line) {
                     }

                     Document doc = builder.parse(new InputSource(new StringReader(line)));
                     doc.getDocumentElement().normalize();
                     NodeList headNodeList = doc.getElementsByTagName("response");
                     Element subItem = (Element)headNodeList.item(0);
                     return_code = subItem.getAttribute("code");
                     if (return_code.equals("0")) {
                        result.add(((RmServerEntity)lists.get(i)).getIp_address() + ";true");
                     } else {
                        result.add(((RmServerEntity)lists.get(i)).getIp_address() + ";false");
                     }
                  } catch (Exception var35) {
                     result.add(((RmServerEntity)lists.get(i)).getIp_address() + ";false");
                     logger.info("[checkS2RemoteServer]Openapi SSL time out! : " + ((RmServerEntity)lists.get(i)).getIp_address());
                  }
               } else {
                  InputStreamReader inputStreamReader = null;
                  BufferedReader rd = null;

                  try {
                     HttpGet httpget = new HttpGet(rmServer);
                     HttpParams httpParams = new BasicHttpParams();
                     HttpConnectionParams.setConnectionTimeout(httpParams, 3000);
                     httpclient = new DefaultHttpClient(httpParams);
                     HttpResponse Rmserver_response = httpclient.execute(httpget);
                     entity = Rmserver_response.getEntity();
                     if (entity != null) {
                        inputStreamReader = new InputStreamReader(Rmserver_response.getEntity().getContent());
                        rd = new BufferedReader(inputStreamReader);
                        line = null;

                        String resultXml;
                        for(resultXml = new String(); (line = rd.readLine()) != null; resultXml = resultXml + line) {
                        }

                        Document doc = builder.parse(new InputSource(new StringReader(resultXml)));
                        doc.getDocumentElement().normalize();
                        NodeList headNodeList = doc.getElementsByTagName("response");
                        Element subItem = (Element)headNodeList.item(0);
                        return_code = subItem.getAttribute("code");
                        if (return_code != null) {
                           if (return_code.equals("0")) {
                              result.add(((RmServerEntity)lists.get(i)).getIp_address() + ";true");
                           } else {
                              result.add(((RmServerEntity)lists.get(i)).getIp_address() + ";false");
                           }
                        }
                     }

                     httpget.abort();
                  } catch (Exception var36) {
                     result.add(((RmServerEntity)lists.get(i)).getIp_address() + ";false");
                  } finally {
                     httpclient.getConnectionManager().shutdown();

                     try {
                        if (rd != null) {
                           rd.close();
                           rd = null;
                        }
                     } catch (Exception var34) {
                        logger.error("", var34);
                     }

                     try {
                        if (inputStreamReader != null) {
                           inputStreamReader.close();
                           inputStreamReader = null;
                        }
                     } catch (Exception var33) {
                        logger.error("", var33);
                     }

                  }
               }
            }
         }

         resultList.setResultList(result);
         resultList.setTotalCount(result.size());
         return resultList;
      }
   }
}
