package com.samsung.magicinfo.webauthor2.repository.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
public class DatalinkTableEntityData {
  @XmlElement(name = "server_name")
  private String serverName = "";
  
  @XmlElement(name = "service_name")
  private String serviceName = "";
  
  @XmlElement(name = "dyna_name")
  private String dynaName = "";
  
  @XmlElement(name = "table_name")
  private String tableName = "";
  
  @XmlElement(name = "is_dataView")
  private Boolean isDataView = Boolean.valueOf(false);
  
  public String getServerName() {
    return this.serverName;
  }
  
  public void getServerName(String name) {
    this.serverName = name;
  }
  
  public String getServiceName() {
    return this.serviceName;
  }
  
  public void setServiceName(String name) {
    this.serviceName = name;
  }
  
  public String getDynaName() {
    return this.dynaName;
  }
  
  public void setDynaName(String name) {
    this.dynaName = name;
  }
  
  public String getTableName() {
    return this.tableName;
  }
  
  public void setTableName(String name) {
    this.tableName = name;
  }
  
  public Boolean getIsDataView() {
    return this.isDataView;
  }
  
  public void setIsDataView(Boolean isDataView) {
    this.isDataView = isDataView;
  }
}
