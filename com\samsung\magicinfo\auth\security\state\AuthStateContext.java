package com.samsung.magicinfo.auth.security.state;

import com.samsung.common.cache.CacheFactory;
import com.samsung.magicinfo.auth.security.AuthResource;
import com.samsung.magicinfo.auth.security.otp.OTPAuthType;
import com.samsung.magicinfo.auth.security.state.impl.ActiveAuthState;
import com.samsung.magicinfo.auth.security.state.impl.InitAuthState;
import com.samsung.magicinfo.auth.security.state.impl.ValidAuthState;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;

public class AuthStateContext {
   private AuthState state = null;

   public AuthStateContext() {
      super();
   }

   public void setState(AuthState state) {
      this.state = state;
   }

   public AuthState getState() {
      return this.state;
   }

   public AuthState getState(AuthResource resource) throws Exception {
      UserInfo userInfo = UserInfoImpl.getInstance();
      if (resource.getTotp() == null && resource.getHotp() == null && userInfo.getUserInfo(resource.getUsername()).getSecret_key() == null) {
         this.state = new InitAuthState();
      } else if (resource.getTotp() != null && resource.getUserAuthDevice() != null) {
         this.state = new InitAuthState();
      } else if (CacheFactory.getCache().get("INIT_TOTP" + resource.getUsername()) != null && resource.getTotp() != null) {
         this.state = new ActiveAuthState();
      } else if (CacheFactory.getCache().get("INIT_HOTP" + resource.getUsername()) != null && resource.getHotp() != null) {
         this.state = new ActiveAuthState();
      } else {
         this.state = new ValidAuthState();
      }

      return this.state;
   }

   public OTPAuthType auth(AuthResource resource) {
      return this.state.auth(resource);
   }
}
