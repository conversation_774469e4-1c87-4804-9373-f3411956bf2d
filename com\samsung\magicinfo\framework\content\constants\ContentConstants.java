package com.samsung.magicinfo.framework.content.constants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ContentConstants {
   public static final Long PARENT_GROUP_OF_UNGROUPED = -1L;
   public static final Long GROUP_DEPTH_OF_UNGROUPED = 0L;
   public static final String GROUP_NAME_OF_DEFAULT = "default";
   public static final Long SHARE_FLAG_DEFAULT = 1L;
   public static final Long SHARE_FLAG_YES = 1L;
   public static final Long SHARE_FLAG_NO = 0L;
   public static final String CONTENTS_DIR = "contents_home";
   public static final String CUSTOM_FILES_DIR = "custom_files";
   public static final String THUMBNAIL_DIR = "content_thumbnail";
   public static final String CONTENTS_META_DIR = "contents_meta";
   public static final String JNLP_DIR = "jnlp";
   public static final String JOBS_HOME_DIR = "jobs_home";
   public static final String RESULT = "result";
   public static final String MDD = "mdd";
   public static final String CONTENTS_META_FILENAME = "ContentsMetadata.CSD";
   public static final String MOVIE_THUMBNAIL_ID = "MOVIE_THUMBNAIL";
   public static final String MOVIE_THUMBNAIL_NAME = "MOVIE_THUMBNAIL.PNG";
   public static final String MOVIE_THUMBNAIL_UID = "A1D709B9-A300-4a55-1670-92ECED897E30";
   public static final String OFFICE_THUMBNAIL_ID = "OFFICE_THUMBNAIL";
   public static final String OFFICE_THUMBNAIL_NAME = "OFFICE_THUMBNAIL.PNG";
   public static final String OFFICE_THUMBNAIL_UID = "3DCD10B2-4ACF-48c2-A0C1-B12C56DFAA03";
   public static final String FLASH_THUMBNAIL_ID = "FLASH_THUMBNAIL";
   public static final String FLASH_THUMBNAIL_NAME = "FLASH_THUMBNAIL.PNG";
   public static final String FLASH_THUMBNAIL_UID = "B30E70AD-0C16-421a-BB97-2E4A5D74449F";
   public static final String PDF_THUMBNAIL_ID = "PDF_THUMBNAIL";
   public static final String PDF_THUMBNAIL_NAME = "PDF_THUMBNAIL.PNG";
   public static final String PDF_THUMBNAIL_UID = "8980552A-EB20-4abb-A505-5B61C94A6F22";
   public static final String SOUND_THUMBNAIL_ID = "SOUND_THUMBNAIL";
   public static final String SOUND_THUMBNAIL_NAME = "SOUND_THUMBNAIL.PNG";
   public static final String SOUND_THUMBNAIL_UID = "75D89A0C-900E-427a-83C0-BDBAAB954E90";
   public static final String ETC_THUMBNAIL_ID = "ETC_THUMBNAIL";
   public static final String ETC_THUMBNAIL_NAME = "ETC_THUMBNAIL.PNG";
   public static final String ETC_THUMBNAIL_UID = "51D709B9-A300-4b55-8670-94ECED897E30";
   public static final String NOIMAGE_THUMBNAIL_ID = "NOIMAGE_THUMBNAIL";
   public static final String NOIMAGE_THUMBNAIL_NAME = "NOIMAGE_THUMBNAIL.PNG";
   public static final String STRM_THUMBNAIL_ID = "STRM_THUMBNAIL";
   public static final String STRM_THUMBNAIL_NAME = "STRM_THUMBNAIL.PNG";
   public static final String STRM_THUMBNAIL_UID = "C00798C0-3100-43AF-AAC7-7E76096E9334";
   public static final String URL_THUMBNAIL_ID = "URL_THUMBNAIL";
   public static final String URL_THUMBNAIL_NAME = "URL_THUMBNAIL.PNG";
   public static final String URL_THUMBNAIL_UID = "A1D709B9-A300-4A55-2670-12ECED897E30";
   public static final String HTML_THUMBNAIL_ID = "HTML_THUMBNAIL";
   public static final String HTML_THUMBNAIL_NAME = "HTML_THUMBNAIL.PNG";
   public static final String HTML_THUMBNAIL_UID = "A1D709B9-A300-4N55-1670-52EC3D897E30";
   public static final String SAPP_THUMBNAIL_ID = "SAPP_THUMBNAIL";
   public static final String SAPP_THUMBNAIL_NAME = "SAPP_THUMBNAIL.PNG";
   public static final String RULESET_THUMBNAIL_ID = "RULESET_THUMBNAIL";
   public static final String RULESET_THUMBNAIL_NAME = "RULESET_THUMBNAIL.PNG";
   public static final String ADS_THUMBNAIL_ID = "ADS_THUMBNAIL";
   public static final String ADS_THUMBNAIL_NAME = "ADS_THUMBNAIL.PNG";
   public static final String FTP_THUMBNAIL_ID = "FTP_THUMBNAIL";
   public static final String FTP_THUMBNAIL_NAME = "FTP_THUMBNAIL.PNG";
   public static final String FTP_THUMBNAIL_UID = "EC468734-E085-ED62-3996-BAC7C52AD7B9-********";
   public static final String CIFS_THUMBNAIL_ID = "CIFS_THUMBNAIL";
   public static final String CIFS_THUMBNAIL_NAME = "CIFS_THUMBNAIL.PNG";
   public static final String CIFS_THUMBNAIL_UID = "F9B46184-C564-6838-E545-83AEF501DF4A-C95159DB";
   public static final String DLK_THUMBNAIL_ID = "DLK_THUMBNAIL";
   public static final String DLK_THUMBNAIL_NAME = "DLK_THUMBNAIL.PNG";
   public static final String DLK_THUMBNAIL_UID = "********-4424-00D1-1DB3-F737E5337A57-06494A68";
   public static final String MEDIASLIDE_THUMBNAIL_ID = "MEDIASLIDE_THUMBNAIL";
   public static final String MEDIASLIDE_THUMBNAIL_NAME = "MEDIASLIDE_THUMBNAIL.PNG";
   public static final String MEDIASLIDE_THUMBNAIL_UID = "99B46184-C564-6838-E545-83AEF501DF4A-********";
   public static final String MEDIA_TYPE_TEXT = "TEXT";
   public static final String MEDIA_TYPE_IMAGE = "IMAGE";
   public static final String MEDIA_TYPE_VIDEO = "VIDEO";
   public static final String MEDIA_TYPE_MOVIE = "MOVIE";
   public static final String MEDIA_TYPE_SOUND = "SOUND";
   public static final String MEDIA_TYPE_PDF = "PDF";
   public static final String MEDIA_TYPE_LFD = "LFD";
   public static final String MEDIA_TYPE_VWL = "VWL";
   public static final String MEDIA_TYPE_OFFICE = "OFFICE";
   public static final String MEDIA_TYPE_FLASH = "FLASH";
   public static final String MEDIA_TYPE_ETC = "ETC";
   public static final String MEDIA_TYPE_PROM = "PROM";
   public static final String MEDIA_TYPE_MEDIASLIDE = "MEDIASLIDE";
   public static final String MEDIA_TYPE_TLFD = "TLFD";
   public static final String MEDIA_TYPE_TEMPLATE_CONTENT = "DLK";
   public static final String MEDIA_TYPE_TEMPLATE_EXTENSION = "LFT";
   public static final String MEDIA_TYPE_NOT_TEMPLATE = "NOTLFT";
   public static final String MEDIA_TYPE_TEMPLATE_CONTENT_EXTENSION = "DLK";
   public static final String MEDIA_TYPE_RULE_EXTENSION = "RULE";
   public static final String MEDIA_TYPE_FTP = "FTP";
   public static final String MEDIA_TYPE_CIFS = "CIFS";
   public static final String MEDIA_TYPE_DLK = "DLK";
   public static final String MEDIA_TYPE_LFT = "LFT";
   public static final String MEDIA_TYPE_WORDART = "WORDART";
   public static final String MEDIA_TYPE_TABLE = "TABLE";
   public static final String MEDIA_TYPE_AIRPORT = "AIRPORT";
   public static final String MEDIA_TYPE_URL = "URL";
   public static final String MEDIA_TYPE_HTML = "HTML";
   public static final String MEDIA_TYPE_SAPP = "SAPP";
   public static final String MEDIA_TYPE_FONT = "FONT";
   public static final String MEDIA_TYPE_PLUGIN_EFFECT = "PLUGIN_EFFECT";
   public static final String MEDIA_TYPE_ADS = "ADS";
   public static final String MEDIA_TYPE_ADS_EXTENSION = "JSON";
   public static final String MEDIA_TYPE_PLAYLIST = "PLAYLIST";
   public static final String MEDIA_TYPE_STRM = "STRM";
   public static final String MEDIA_TYPE_RULESET = "RULESET";
   public static final String CONTENT_TYPE_PLAYLIST = "PLAYLIST";
   public static final String CONTENT_TYPE_LITE_PLAYLIST = "LPLAYLIST";
   public static final String MEDIUM_THUMB_SUFFIX = "_MEDIUM.PNG";
   public static final String SMALL_THUMB_SUFFIX = "_SMALL.PNG";
   public static final String HD_THUMB_SUFFIX = "_HD.PNG";
   public static final int HD_THUMB_WIDTH = 1280;
   public static final int HD_THUMB_HEIGHT = 720;
   public static final String GROUP_TYPE_SHARED = "SHARED";
   public static final String GROUP_TYPE_DELETED = "DELETED";
   public static final String GROUP_TYPE_UNGROUPED = "UNGROUPED";
   public static final String GROUP_TYPE_GROUPED = "GROUPED";
   public static final String GROUP_TYPE_ALL = "ALL";
   public static final String GROUP_TYPE_ORGAN = "ORGAN";
   public static final String GROUP_TYPE_USER = "USER";
   public static final String GROUP_TYPE_SUBMITTED = "SUBMITTED";
   public static final String GROUP_TYPE_SHAREFOLDER = "SHAREFOLDER";
   public static final String NORMAL_IMAGE_BG_COLOR = "#f5f9fd";
   public static final String SELECTED_IMAGE_BG_COLOR = "#FF9C00";
   public static final String DISABLED_IMAGE_BG_COLOR = "#4E4E4E";
   public static final Long CONTENT_DURATION = 30L;
   public static final Long LITE_CONTENT_DURATION = 5L;
   public static final Long MINIMUM_CONTENT_DURATION = 5L;
   public static final String FAIL_EXIST_TABLE = "FAIL_EXIST_TABLE";
   public static final String FAIL_EXIST_DATA = "FAIL_EXIST_DATA";
   public static final String ADD_TABLE_SUCCESS = "ADD_TABLE_SUCCESS";
   public static final String ADD_TABLE_FAIL = "ADD_TABLE_FAIL";
   public static final String EDIT_TABLE_SUCCESS = "EDIT_TABLE_SUCCESS";
   public static final String EDIT_TABLE_FAIL = "EDIT_TABLE_FAIL";
   public static final String DEL_TABLE_SUCCESS = "DEL_TABLE_SUCCESS";
   public static final String DEL_TABLE_FAIL = "DEL_TABLE_FAIL";
   public static final String ADD_LIST_SUCCESS = "ADD_LIST_SUCCESS";
   public static final String ADD_LIST_FAIL = "ADD_LIST_FAIL";
   public static final String EDIT_LIST_SUCCESS = "EDIT_LIST_SUCCESS";
   public static final String EDIT_LIST_FAIL = "EDIT_LIST_FAIL";
   public static final String DEL_LIST_SUCCESS = "DEL_LIST_SUCCESS";
   public static final String DEL_LIST_FAIL = "DEL_LIST_FAIL";
   public static final String CONVERT_DATA_LOCK = "CONVERT_DATA_LOCK";
   public static final String CONTENT_TYPE_CONTENT = "CONTENT";
   public static final String CONTENT_TYPE_TEMPLATE = "TEMPLATE";
   public static final String NO_DATA = "NO_DATA";
   public static final String NO_CONNECT = "NO_CONNECT";
   public static final String NO_TABLE = "NO_TABLE";
   public static final String META_FILE_NAME_CSD = "ContentsMetadata.CSD";
   public static final String META_FILE_NAME_FTP = "FtpMetadata.FTP";
   public static final String META_FILE_NAME_CIFS = "CifsMetadata.CIFS";
   public static final String META_FILE_NAME_STRM = "StrmMetadata.STRM";
   public static final String META_FILE_NAME_RULE = "RuleMetadata.RULE";
   public static final String CONTENT_FILE_JSON_EXTENSION = "json";
   public static final String ADS_CONTENT_CONFIG_FILE_NAME = "adsConfig.json";
   public static final String CONTENT_NAME = "content_name";
   public static final String CONTENT_IS_ACTIVE = "is_active";
   public static final String CONTENT_IS_DELETED = "is_deleted";
   public static final String CONTENT_VERSION_ID = "version_id";
   public static final long SESSION_ACCESS_TIME_PERIOD = 5000L;
   public static final long SESSION_CHECK_TIME_PERIOD = 10000L;
   public static final int SESSION_TIME_GAP = 15;
   public static final int DATALINK_OPENAPI_CONNECT_TIME = 5000;
   public static final int DATALINK_OPENAPI_READ_TIME = 5000;
   public static final String CONTENT_VERSION_LIMIT_COUNT_DEFAULT_VALUE = "3";
   public static final String JNLP_SERVLET = "FileLoader&#063;paramPathConfName=JNLP_HOME&amp;download=D&amp;filepath=";
   public static final String JNLP_SERVLET_IE = "FileLoader&#063;paramPathConfName=JNLP_HOME&amp;download=B&amp;filepath=";
   public static final String PL_TYPE_PREMIUM = "0";
   public static final String PL_TYPE_AMS = "1";
   public static final String PL_TYPE_VWL = "2";
   public static final String PL_TYPE_SYNCPLAY = "3";
   public static final String PL_TYPE_ADVERTISEMENT = "4";
   public static final String PL_TYPE_TAG = "5";
   public static final String PL_TYPE_LINKED = "6";
   public static final String APPROVAL_STATUS_UNAPPROVED = "UNAPPROVED";
   public static final String APPROVAL_STATUS_APPROVED = "APPROVED";
   public static final String APPROVAL_STATUS_REJECTED = "REJECTED";
   public static final String STRM_TYPE_RTP = "rtp";
   public static final String STRM_TYPE_RTSP = "rtsp";
   public static final String STRM_TYPE_MMS = "mms";
   public static final String STRM_TYPE_HLS = "hls";
   public static final String STRM_TYPE_HTTP = "http";
   public static final String TYPE_SYNC = "SYNC";
   public static final String TYPE_UNSYNC = "UNSYNC";
   public static final String[] CONT_VALID = new String[]{"C0000", "Content is valid."};
   public static final String[] CONT_CSD_NOT_EXIST = new String[]{"C0001", "Content CSD File is not exist."};
   public static final String[] CONT_DB_NOT_EXIST = new String[]{"C0002", "No file info in MI_CMS_INFO_FILE."};
   public static final String[] CONT_DB_MAP_NOT_EXIST = new String[]{"C0003", "No file info in MI_CMS_MAP_VERSION_FILE."};
   public static final String[] CONT_FILE_SIZE = new String[]{"C0004", "dbFileSize is not same with realFileSize."};
   public static final String[] CONT_FILE_NOT_EXIST = new String[]{"C0005", "The file is not exist in Path."};
   public static final String[] CONT_CSD_FILE_NULL = new String[]{"C0006", "CSD files list is null."};
   public static final String[] CONT_DB_FILE_NULL = new String[]{"C0007", "DB files list is null."};
   public static final String[] CONT_NUM_FILE_ERROR = new String[]{"C0008", "The number of CSD files' is not same with the number of DB files."};
   public static final String[] CONT_HTML_VAILD = new String[]{"C1000", "HTML CONTENT IS VALID."};
   public static final long TAG_TYPE_TEXT = 0L;
   public static final long TAG_TYPE_NUMBER = 1L;
   public static final long TAG_TYPE_BOOLEAN = 2L;
   public static final int PL_CANNOT_DELETE = -100;
   public static List MEDIA_TYPE_FOR_AUTHOR = new ArrayList();
   public static final String ADS_API_KEY = "api_key";
   public static final String ADS_API_KEY_SECRET = "api_key_secret";
   public static final String ADS_PUBLISHER_NAME = "publisher_name";
   public static final String ADS_PUBLISHER_ID = "publisher_id";
   public static final String ADS_AD_UNIT_ID = "ad_unit_id";
   public static final String ADS_IMAGE_TYPE_SET = "image_type_set";
   public static final String ADS_IMAGE_DURATION = "image_duration";
   public static final String ADS_VIDEO_TYPE_SET = "video_type_set";
   public static final String ADS_VIDEO_DURATION = "video_duration";
   public static final String ADS_DEFAULT_CONTENT = "default_content";
   public static final String ADS_DEFAULT_CONTENT_FILE_ID = "default_content_file_id";
   public static final String ADS_CONFIG_FILE_ID = "adsContentConfigFileId";
   public static final String ADS_SETTING = "adsSetting";
   public static final int ADS_CONTENT_SUGGESTION_LIST_COUNT = 5;
   public static final String ADS_CONTENT_AD_UNIT_ID_SUGGESTION_TYPE = "AD_UNIT_ID_LIST";
   public static final String ADS_CONTENT_PUBLISHER_INFO_SUGGESTION_TYPE = "PUBLISHER_INFO_LIST";

   public ContentConstants() {
      super();
   }

   public static Map getMediaTypeMap() {
      Map result = new HashMap();
      result.put("IMAGE", "IMAGE");
      result.put("VIDEO", "VIDEO");
      result.put("MOVIE", "MOVIE");
      result.put("SOUND", "SOUND");
      result.put("PDF", "PDF");
      result.put("LFD", "LFD");
      result.put("VWL", "VWL");
      result.put("OFFICE", "OFFICE");
      result.put("MOVIE", "MOVIE");
      result.put("FLASH", "FLASH");
      result.put("FTP", "FTP");
      result.put("CIFS", "CIFS");
      result.put("STRM", "STRM");
      result.put("LFT", "LFT");
      result.put("DLK", "DLK");
      return result;
   }

   public static List getMediaTypeForAuthor() {
      synchronized(MEDIA_TYPE_FOR_AUTHOR) {
         if (MEDIA_TYPE_FOR_AUTHOR.size() <= 0) {
            MEDIA_TYPE_FOR_AUTHOR.add("FONT");
            MEDIA_TYPE_FOR_AUTHOR.add("PLUGIN_EFFECT");
         }
      }

      return MEDIA_TYPE_FOR_AUTHOR;
   }
}
