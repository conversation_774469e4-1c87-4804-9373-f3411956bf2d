package com.samsung.magicinfo.webauthor;

import com.samsung.magicinfo.webauthor.util.Common;
import com.samsung.magicinfo.webauthor2.properties.MagicInfoProperties;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import javax.imageio.ImageIO;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class AppStartup {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor.AppStartup.class);
  
  private ServletContext servletContext;
  
  private MagicInfoProperties magicInfoProperties;
  
  public AppStartup(ServletContext servletContext, MagicInfoProperties magicInfoProperties) {
    this.servletContext = servletContext;
    this.magicInfoProperties = magicInfoProperties;
  }
  
  @EventListener
  public final void onApplicationEvent(ContextRefreshedEvent event) {
    ImageIO.scanForPlugins();
    Common.MIP_WEB_URL_LOCAL = this.magicInfoProperties.getWebauthorWebUrl();
    Common.MIS_CONTENTS_LOCATION = this.magicInfoProperties.getMagicInfoContentsLocationPath().toString() + "/";
    deleteInsertContentsDirectory();
    logger.info("WebAuthor context refresh successful: ");
  }
  
  private void deleteInsertContentsDirectory() {
    Path insertContents = Paths.get(this.servletContext.getRealPath("insertContents"), new String[0]);
    if (Files.exists(insertContents, new java.nio.file.LinkOption[0]))
      FileUtils.deleteQuietly(insertContents.toFile()); 
  }
}
