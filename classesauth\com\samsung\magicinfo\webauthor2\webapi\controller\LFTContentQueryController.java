package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.LFDContent;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.service.ContentService;
import com.samsung.magicinfo.webauthor2.service.LFDContentService;
import com.samsung.magicinfo.webauthor2.webapi.assembler.ContentResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.assembler.LFDContentResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.resource.ContentResource;
import com.samsung.magicinfo.webauthor2.webapi.resource.LFDContentResource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.PagedResourcesAssembler;
import org.springframework.hateoas.PagedResources;
import org.springframework.hateoas.ResourceAssembler;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/LFTContents"})
public class LFTContentQueryController {
  private LFDContentService lfdContentService;
  
  private ContentService contentService;
  
  private LFDContentResourceAssembler lfdContentResourceAssembler;
  
  private ContentResourceAssembler contentResourceAssembler;
  
  @Autowired
  public LFTContentQueryController(ContentService contentService, LFDContentService lfdContentService, LFDContentResourceAssembler lfdContentResourceAssembler, ContentResourceAssembler contentResourceAssembler) {
    this.contentService = contentService;
    this.lfdContentService = lfdContentService;
    this.lfdContentResourceAssembler = lfdContentResourceAssembler;
    this.contentResourceAssembler = contentResourceAssembler;
  }
  
  @GetMapping
  public HttpEntity<PagedResources<ContentResource>> getLFTContent(@PageableDefault(size = 20, page = 0) Pageable pageable, @RequestParam String playerType, PagedResourcesAssembler<Content> assembler) {
    Page<Content> contentList = this.contentService.getContentResources(pageable, DeviceType.valueOf(playerType), Collections.singletonList(MediaType.LFT));
    PagedResources<ContentResource> contentResources = assembler.toResource(contentList, (ResourceAssembler)this.contentResourceAssembler);
    return (HttpEntity<PagedResources<ContentResource>>)ResponseEntity.ok(contentResources);
  }
  
  @GetMapping({"/{contentId}"})
  public HttpEntity<LFDContentResource> getLFDContent(@PathVariable String contentId) {
    LFDContent content = this.lfdContentService.getLFDContent(contentId);
    if (content == null)
      return (HttpEntity<LFDContentResource>)ResponseEntity.notFound().build(); 
    LFDContentResource contentResources = this.lfdContentResourceAssembler.toResource(content);
    return (HttpEntity<LFDContentResource>)ResponseEntity.ok(contentResources);
  }
  
  @GetMapping({"/relatedDLK/{lftContentId}"})
  public HttpEntity<List<ContentResource>> getRelatedDLKContent(@PathVariable String lftContentId) {
    List<Content> contentList = this.contentService.getRelatedDLKContent(lftContentId);
    if (contentList.isEmpty()) {
      contentList = new ArrayList<>();
      List<ContentResource> list = this.contentResourceAssembler.toResources(contentList);
      return (HttpEntity<List<ContentResource>>)ResponseEntity.ok(list);
    } 
    List<ContentResource> contentResources = this.contentResourceAssembler.toResources(contentList);
    return (HttpEntity<List<ContentResource>>)ResponseEntity.ok(contentResources);
  }
}
