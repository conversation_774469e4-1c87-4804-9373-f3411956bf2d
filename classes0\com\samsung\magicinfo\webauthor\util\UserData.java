package com.samsung.magicinfo.webauthor.util;

import com.google.common.base.Joiner;
import com.samsung.magicinfo.webauthor2.model.ContentSaveElements;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import java.io.Serializable;
import java.nio.file.Path;
import java.util.Date;
import java.util.Locale;
import java.util.Set;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@Scope(value = "session", proxyMode = ScopedProxyMode.TARGET_CLASS)
public class UserData implements Serializable {
  private static final long serialVersionUID = 7501894884287345553L;
  
  private static Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor.util.UserData.class);
  
  private String userId;
  
  private String token;
  
  private Locale locale;
  
  private Set<DeviceType> supportedDeviceTypes;
  
  private String sessionTimestamp;
  
  private String language;
  
  private String startContentId;
  
  private String layoutDeviceType;
  
  private String layoutDeviceGroupId;
  
  private String layoutDeviceId;
  
  private String layoutEditType;
  
  private Path pathTothumbnail;
  
  private ContentSaveElements contentSaveElements;
  
  public UserData() {
    invalidate();
  }
  
  public synchronized boolean isAnonymous() {
    return (StringUtils.isEmpty(this.userId) || StringUtils.isEmpty(this.token));
  }
  
  public synchronized void setCredentials(String token) {
    this.token = token;
  }
  
  public synchronized void setCredentials(String userId, String token) {
    this.userId = userId;
    this.token = token;
  }
  
  public synchronized void invalidate() {
    logger.debug("clean user data: " + toString());
    this.userId = "";
    this.token = "";
    this.locale = null;
    this.language = "";
    this.sessionTimestamp = Long.toString((new Date()).getTime());
    this.contentSaveElements = new ContentSaveElements();
  }
  
  public synchronized String getUserId() {
    return this.userId;
  }
  
  public synchronized String getToken() {
    return this.token;
  }
  
  public synchronized Locale getLocale() {
    return this.locale;
  }
  
  public synchronized void setLocale(Locale locale) {
    this.locale = locale;
  }
  
  public String getSessionTimestamp() {
    return this.sessionTimestamp;
  }
  
  public String getWorkspaceFolderName() {
    return Joiner.on("_").join(getUserId(), getSessionTimestamp(), new Object[0]);
  }
  
  public Set<DeviceType> getSupportedDeviceTypes() {
    return this.supportedDeviceTypes;
  }
  
  public void setSupportedDeviceTypes(Set<DeviceType> supportedDeviceTypes) {
    this.supportedDeviceTypes = supportedDeviceTypes;
  }
  
  public synchronized String getLanguage() {
    return this.language;
  }
  
  public synchronized void setLanguage(String language) {
    this.language = language;
  }
  
  public String getStartContentId() {
    return this.startContentId;
  }
  
  public void setStartContentId(String startContentId) {
    this.startContentId = startContentId;
  }
  
  public String getLayoutDeviceType() {
    return this.layoutDeviceType;
  }
  
  public void setLayoutDeviceType(String layoutDeviceType) {
    this.layoutDeviceType = layoutDeviceType;
  }
  
  public String getLayoutDeviceGroupId() {
    return this.layoutDeviceGroupId;
  }
  
  public void setLayoutDeviceGroupId(String layoutDeviceGroupId) {
    this.layoutDeviceGroupId = layoutDeviceGroupId;
  }
  
  public String getLayoutDeviceId() {
    return this.layoutDeviceId;
  }
  
  public void setLayoutDeviceId(String layoutDeviceId) {
    this.layoutDeviceId = layoutDeviceId;
  }
  
  public String getLayoutEditType() {
    return this.layoutEditType;
  }
  
  public void setLayoutEditType(String layoutEditType) {
    this.layoutEditType = layoutEditType;
  }
  
  public ContentSaveElements getContentSaveElements() {
    return this.contentSaveElements;
  }
  
  public void setContentSaveElements(ContentSaveElements contentSaveElements) {
    this.contentSaveElements = contentSaveElements;
  }
  
  public String toString() {
    return (new ToStringBuilder(this))
      .append("userId", this.userId)
      .append("token", this.token)
      .append("language", this.language)
      .toString();
  }
  
  public Path getPathTothumbnail() {
    return this.pathTothumbnail;
  }
  
  public void setPathTothumbnail(Path pathTothumbnail) {
    this.pathTothumbnail = pathTothumbnail;
  }
}
