package com.samsung.magicinfo.webauthor2.webapi.resource;

import com.samsung.magicinfo.webauthor2.model.DataLinkTable;
import java.io.Serializable;
import org.springframework.hateoas.Link;
import org.springframework.hateoas.Resource;

public class DataLinkTableResource extends Resource<DataLinkTable> implements Serializable {
  private static final long serialVersionUID = 4014742207725774801L;
  
  public DataLinkTableResource(DataLinkTable content, Link... links) {
    super(content, links);
  }
  
  public DataLinkTableResource(DataLinkTable content, Iterable<Link> links) {
    super(content, links);
  }
}
