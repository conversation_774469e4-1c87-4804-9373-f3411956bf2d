package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.model.ContentThumbnailBasic;
import com.samsung.magicinfo.webauthor2.repository.OpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.model.ContentThumbnailListResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.ContentThumbnailResultListData;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

public class GetContentThumbnailByFileIdOpenApiMethod extends OpenApiMethod<ContentThumbnailBasic, ContentThumbnailListResponseData> {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.repository.GetContentThumbnailByFileIdOpenApiMethod.class);
  
  private final String token;
  
  private final String fileId;
  
  private final String size;
  
  public GetContentThumbnailByFileIdOpenApiMethod(RestTemplate restTemplate, String token, String fileId, String size) {
    super(restTemplate);
    this.token = token;
    this.fileId = fileId;
    this.size = size;
  }
  
  protected String getOpenApiClassName() {
    return "CommonContentService";
  }
  
  protected String getOpenApiMethodName() {
    return "getContentThumbnailByFileId";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("token", this.token);
    vars.put("fileId", this.fileId);
    vars.put("size", this.size);
    return vars;
  }
  
  Class<ContentThumbnailListResponseData> getResponseClass() {
    return ContentThumbnailListResponseData.class;
  }
  
  ContentThumbnailBasic convertResponseData(ContentThumbnailListResponseData responseData) {
    ContentThumbnailBasic thumbnailBasic = new ContentThumbnailBasic();
    ContentThumbnailResultListData resultListData = responseData.getResponseClass();
    if (resultListData == null)
      return thumbnailBasic; 
    List<String> resultList = resultListData.getResultList();
    if (resultList == null)
      return thumbnailBasic; 
    if (resultList.size() == 0)
      return thumbnailBasic; 
    String response = resultList.get(0);
    response = response.replace("MagicInfo/servlet/ContentThumbnail?thumb_id=", "");
    response = response.replace("&thumb_filename=", "/");
    String[] values = response.split("/");
    thumbnailBasic.setFileId(values[0]);
    thumbnailBasic.setFileName(values[1]);
    return thumbnailBasic;
  }
}
