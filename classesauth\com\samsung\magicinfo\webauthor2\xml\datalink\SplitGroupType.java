package com.samsung.magicinfo.webauthor2.xml.datalink;

import com.samsung.magicinfo.webauthor2.xml.datalink.DataType;
import com.samsung.magicinfo.webauthor2.xml.datalink.ElementType;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import org.eclipse.persistence.oxm.annotations.XmlCDATA;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SplitGroupType", propOrder = {"name", "element", "data"})
public class SplitGroupType {
  @XmlElement(name = "Name", required = true)
  @XmlCDATA
  protected String name = null;
  
  @XmlElement(name = "Element")
  protected List<ElementType> element;
  
  @XmlElement(name = "Data")
  protected List<DataType> data;
  
  @XmlAttribute(name = "id")
  protected Integer id;
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String value) {
    this.name = value;
  }
  
  public List<ElementType> getElement() {
    if (this.element == null)
      this.element = new ArrayList<>(); 
    return this.element;
  }
  
  public List<DataType> getData() {
    if (this.data == null)
      this.data = new ArrayList<>(); 
    return this.data;
  }
  
  public Integer getId() {
    return this.id;
  }
  
  public void setId(Integer value) {
    this.id = value;
  }
}
