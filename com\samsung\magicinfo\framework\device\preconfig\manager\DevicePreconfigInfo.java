package com.samsung.magicinfo.framework.device.preconfig.manager;

import com.samsung.common.db.DBListExecuter;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceServiceConf;
import com.samsung.magicinfo.framework.device.preconfig.entity.DevicePreconfig;
import com.samsung.magicinfo.framework.device.preconfig.entity.DevicePreconfigResultXml;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface DevicePreconfigInfo extends DBListExecuter {
   int addDevicePreconfigInfo(DevicePreconfig var1) throws SQLException;

   DevicePreconfig getPreconfigInfo(String var1) throws SQLException;

   DevicePreconfig getPreconfigByDeviceId(String var1) throws SQLException;

   DevicePreconfig getPreconfigInfoByDeviceId(String var1) throws SQLException;

   boolean deletePreconfig(String[] var1) throws SQLException;

   int addPreconfigGroupMapping(Map var1) throws SQLException;

   int addPreconfigGroupMappingList(String var1, String var2) throws SQLException;

   int deployToDevice(Device var1) throws Exception;

   boolean updatePreconfigInfo(DevicePreconfig var1) throws SQLException;

   int addServiceConfig(DeviceServiceConf var1) throws SQLException;

   boolean updateServiceConfig(DeviceServiceConf var1) throws SQLException;

   List getServiceConfigInfo(String var1) throws SQLException;

   boolean deleteSoftwareConfig(String var1) throws SQLException;

   boolean addSoftwareConfigList(String var1, List var2) throws SQLException;

   boolean deleteServiceConfig(String var1) throws SQLException;

   boolean addServiceConfigList(String var1, List var2) throws SQLException;

   boolean setDeployStatusSuccess(String var1, String var2) throws SQLException;

   boolean setDeployStatusReportTime(String var1, String var2, Date var3) throws SQLException;

   int initDeployStatusByGroup(String var1, String var2) throws SQLException;

   List getGroupMappingByPreconfig(String var1) throws SQLException;

   List getDeployStatusByPreconfigId(String var1) throws SQLException;

   Map getDeployStatusByDeviceId(String var1) throws SQLException;

   DevicePreconfigResultXml getPreconfigResultXml(String var1) throws Exception;

   Map getPreconfigResultMap(String var1, String var2) throws Exception;

   boolean deletePreconfigFromDevice(String var1) throws SQLException;
}
