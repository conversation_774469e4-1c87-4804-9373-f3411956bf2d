package com.samsung.magicinfo.webauthor2.exception.service;

public final class FileItemValidationExceptionMessages {
  public static final String FILE_NOT_EXIST = "FileNotExist";
  
  public static final String TOO_MANY_FILES = "TooManyFiles";
  
  public static final String ZERO_FILE_SIZE = "FileSizeZero";
  
  public static final String INVALID_FILE_NAME = "InvalidFileName";
  
  public static final String INVALID_FILE_TYPE = "InvalidFileType";
  
  public static final String INVALID_FILE_NAME_SPECIAL = "SpecialCharFileName";
  
  public static final String INVALID_FILE_NAME_NULL = "NullFileName";
  
  public static final String INVALID_FILE_NAME_SHORT = "FileNameTooShort";
  
  public static final String INVALID_FILE_NAME_LONG = "FileNameTooLong";
  
  public static final String INVALID_FILE_NAME_DUPLICATE = "FileNameNotUnique";
}
