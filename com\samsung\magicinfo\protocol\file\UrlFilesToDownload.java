package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.FileUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.CharBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.Charset;
import java.nio.charset.CharsetEncoder;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.logging.log4j.Logger;

public class UrlFilesToDownload {
   private Logger logger = LoggingManagerV2.getLogger(UrlFilesToDownload.class);
   private static volatile UrlFilesToDownload instance;

   private UrlFilesToDownload() {
      super();
   }

   public static UrlFilesToDownload getInstance() {
      if (instance == null) {
         Class var0 = UrlFilesToDownload.class;
         synchronized(UrlFilesToDownload.class) {
            if (instance == null) {
               instance = new UrlFilesToDownload();
            }
         }
      }

      return instance;
   }

   public boolean getUrlFiles(String miUserId, long groupId, String contentId, String urlContentName, String urlAddress, String urlProtocolType) throws Exception {
      boolean result = false;
      ContentInfo dao = ContentInfoImpl.getInstance();
      String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
      Content content = new Content();
      UserInfo uInfo = UserInfoImpl.getInstance();
      long orgId = uInfo.getRootGroupIdByUserId(miUserId);
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
      boolean contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
      boolean editMode = false;
      boolean settingChanged = false;
      if (miUserId.equalsIgnoreCase((String)null) || miUserId.equalsIgnoreCase("")) {
         miUserId = "admin";
      }

      List preContentFileList = dao.getFileList(contentId);

      String mainFileId;
      File urlFile;
      String urlFolderPath;
      for(int i = 0; i < preContentFileList.size(); ++i) {
         mainFileId = ((ContentFile)preContentFileList.get(i)).getFile_name();
         String preContentFileId = ((ContentFile)preContentFileList.get(i)).getFile_id();
         if (mainFileId.equalsIgnoreCase("StrmMetadata.STRM")) {
            File preContentFile = null;
            urlFile = null;
            System.out.println("[STRM_Thread] Previous File Remover : del " + preContentFileId);
            urlFolderPath = CONTENTS_HOME + File.separator + preContentFileId;
            preContentFile = SecurityUtils.getSafeFile(urlFolderPath + File.separator + mainFileId);
            urlFile = SecurityUtils.getSafeFile(urlFolderPath);
            if (preContentFile.exists()) {
               dao.deleteFileFromContentMap(contentId, preContentFileId);
               preContentFile.delete();
               urlFile.delete();
               dao.deleteFile(preContentFileId);
            } else {
               dao.deleteFileFromContentMapByFileName(contentId, mainFileId);
            }
         }
      }

      List StrmSettingInfo = dao.getUrlContentSettingByContentId(contentId);
      if (StrmSettingInfo != null && StrmSettingInfo.size() > 0) {
         editMode = true;
      }

      mainFileId = UUID.randomUUID().toString();
      long totalSizeOfFiles = 0L;
      urlFile = null;
      urlFolderPath = CONTENTS_HOME + File.separator + mainFileId;
      File urlFolder = SecurityUtils.getSafeFile(urlFolderPath);
      if (!urlFolder.exists()) {
         urlFolder.mkdir();
      }

      String src = CONTENTS_HOME + File.separator + mainFileId + File.separator + "StrmMetadata.STRM";
      urlFile = SecurityUtils.getSafeFile(src);
      if (!urlFile.exists() && !urlFile.createNewFile()) {
         return false;
      } else {
         StringBuffer sb = new StringBuffer("");
         sb.append(urlAddress);
         FileOutputStream outputStream = null;
         FileChannel fileChannel = null;

         try {
            outputStream = new FileOutputStream(urlFile);
            fileChannel = outputStream.getChannel();
            Charset cs = Charset.forName("UTF-8");
            CharsetEncoder encoder = cs.newEncoder();
            fileChannel.write(encoder.encode(CharBuffer.wrap(sb.toString())));
            totalSizeOfFiles = urlFile.length();
            content.setContent_id(contentId);
            content.setCreator_id(miUserId);
            content.setContent_name(urlContentName);
            content.setIs_deleted("N");
            content.setShare_flag(1);
            content.setOrganization_id(uInfo.getRootGroupIdByUserId(miUserId));
            content.setVersion_id(0L);
            content.setMedia_type("STRM");
            content.setThumb_file_id("STRM_THUMBNAIL");
            content.setTotal_size(totalSizeOfFiles);
            content.setIs_active("N");
            content.setMain_file_id(mainFileId);
            content.setIs_linear_vwl("N");
            content.setScreen_count(0);
            content.setContent_meta_data("");
            content.setX_count(0);
            content.setY_count(0);
            content.setX_range(0);
            content.setY_range(0);
            content.setIs_streaming("N");
            content.setDevice_type("SPLAYER");
            content.setDevice_type_version(CommonDataConstants.TYPE_VERSION_3_0);
            content.setMain_file_Extension("STRM");
            if (contentsApprovalEnable) {
               AbilityUtils abilityUtils = new AbilityUtils();
               if (abilityUtils.isContentApprovalAuthority(miUserId)) {
                  content.setApproval_status("APPROVED");
               } else {
                  content.setApproval_status("UNAPPROVED");
               }
            } else {
               content.setApproval_status("APPROVED");
            }

            System.out.println("[URL_Thread] isEdit/fileChanged/settingChanged" + editMode + "/" + settingChanged);
            if (editMode) {
               dao.updateContentVersionInfoByContentId(totalSizeOfFiles, contentId);
            } else {
               dao.addContentInfo(content);
               dao.addContentVersionInfo(content);
               dao.addMapGroupContent(contentId, groupId);
            }

            long version = 0L;
            version = dao.getContentNextVer(contentId);
            content.setVersion_id(version);
            File csdFolder = SecurityUtils.getSafeFile(CONTENTS_HOME + File.separator + "contents_meta" + File.separator + contentId);
            if (csdFolder.exists()) {
               File csdFolderOldFile = SecurityUtils.getSafeFile(CONTENTS_HOME + File.separator + "contents_meta" + File.separator + contentId + File.separator + "ContentsMetadata.CSD");
               csdFolderOldFile.delete();
            }

            ContentFile contentFile = new ContentFile();
            String hashCode = "";
            hashCode = FileUtils.getHash(urlFile);
            contentFile.setFile_id(mainFileId);
            contentFile.setFile_name("StrmMetadata.STRM");
            contentFile.setFile_size(totalSizeOfFiles);
            contentFile.setFile_path(urlFolderPath);
            contentFile.setHash_code(hashCode);
            contentFile.setCreator_id(miUserId);
            contentFile.setFile_type("STRM_MAIN");
            contentFile.setIs_streaming("N");
            dao.addFile(contentFile);
            dao.deleteOldVersionContent(contentId, version - 1L);
            dao.updateContentVersionInfoWithFileId(contentId, mainFileId, version);
            dao.addMapContentFile(contentId, version, mainFileId);
            if (editMode) {
               dao.updateUrlSetting(contentId, urlContentName, urlAddress);
            } else {
               dao.addUrlSetting(contentId, urlContentName, urlAddress);
            }

            List fileList = dao.getFileListByContentId(contentId);
            this.createCSD(content, fileList);
            dao.setIsActive(contentId, "Y");
            System.out.println("[STRM_Thread] Updated CSD & DB");
            if (outputStream != null) {
               outputStream.close();
            }

            if (fileChannel != null) {
               fileChannel.close();
            }

            if (editMode) {
               System.out.println("[CListC] Start to schedule");
               ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
               schInfo.setScheduleTrigger(contentId);
               EventInfo eInfo = EventInfoImpl.getInstance();
               eInfo.setContentTrigger(contentId);
               List pList = dao.getPlaylistListUsingContent(contentId);

               for(int i = 0; i < pList.size(); ++i) {
                  Map map = (Map)pList.get(i);
                  String playlistId = (String)map.get("playlist_id");
                  eInfo.setPlaylistTrigger(playlistId);
               }
            }
         } catch (Exception var49) {
            this.logger.error("", var49);
         } finally {
            outputStream.close();
         }

         return result;
      }
   }

   public File createCSD(Content content, List fileList) throws ConfigException {
      ContentInfo dao = ContentInfoImpl.getInstance();
      FileOutputStream outputStream = null;
      FileChannel fileChannel = null;

      StringBuffer sb;
      try {
         String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
         String contentId = content.getContent_id();
         File metaFolder = SecurityUtils.getSafeFile(CONTENTS_HOME + File.separator + "contents_meta");
         if (!metaFolder.exists()) {
            metaFolder.mkdir();
         }

         File csdFolder = SecurityUtils.getSafeFile(CONTENTS_HOME + File.separator + "contents_meta" + File.separator + contentId);
         if (!csdFolder.exists()) {
            csdFolder.mkdir();
         }

         System.out.println("createCSD " + contentId);
         File csdFile = SecurityUtils.getSafeFile(CONTENTS_HOME + File.separator + "contents_meta" + File.separator + contentId + File.separator + "ContentsMetadata.CSD");
         if (csdFile.exists() || csdFile.createNewFile()) {
            sb = new StringBuffer("");
            sb.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n");
            sb.append("<STRMContent cid=\"").append(content.getContent_id()).append("\">\n");
            sb.append("  <User>").append(content.getCreator_id()).append("</User>\n");
            sb.append("  <Title>").append(content.getContent_name()).append("</Title>\n");
            long category = 0L;
            if (content.getGroup_id() != null) {
               category = content.getGroup_id();
            }

            sb.append("<Category>").append(category).append("</Category>\n");
            sb.append("\t<StrmFileContents>\n");

            try {
               List contentFileList = dao.getFileList(content.getContent_id());

               for(int i = 0; i < contentFileList.size(); ++i) {
                  ContentFile contentFile = (ContentFile)contentFileList.get(i);
                  if (contentFile != null) {
                     sb.append("\t\t<FileItem>\n");
                     sb.append("\t\t\t<FileId>").append(contentFile.getFile_id()).append("</FileId>\n");
                     sb.append("\t\t\t<FileName>").append(contentFile.getFile_name()).append("</FileName>\n");
                     sb.append("\t\t\t<FileSize>").append(contentFile.getFile_size()).append("</FileSize>\n");
                     sb.append("\t\t\t<FileHashValue>").append(contentFile.getHash_code()).append("</FileHashValue>\n");
                     sb.append("\t\t</FileItem>\n");
                  }
               }
            } catch (SQLException var29) {
               this.logger.error("", var29);
            }

            sb.append("\t</StrmFileContents>\n");
            sb.append("</STRMContent>");
            outputStream = new FileOutputStream(csdFile);
            fileChannel = outputStream.getChannel();
            Charset cs = Charset.forName("UTF-8");
            CharsetEncoder encoder = cs.newEncoder();
            fileChannel.write(encoder.encode(CharBuffer.wrap(sb.toString())));
            outputStream.close();
            fileChannel.close();
            File var17 = csdFile;
            return var17;
         }

         sb = null;
      } catch (IOException var30) {
         this.logger.info(var30.getMessage());
         return null;
      } finally {
         try {
            if (outputStream != null) {
               outputStream.close();
            }

            if (fileChannel != null) {
               fileChannel.close();
            }
         } catch (IOException var28) {
            this.logger.error("", var28);
         }

      }

      return sb;
   }

   public synchronized void copyFile(File srcFile, File destFile) {
      FileInputStream fis = null;
      FileOutputStream fos = null;
      boolean var5 = false;

      try {
         fis = new FileInputStream(srcFile);
         fos = new FileOutputStream(destFile);

         int data;
         while((data = fis.read()) != -1) {
            fos.write(data);
         }

         System.out.println("[File_Thread] copy file success !!!");
      } catch (Exception var15) {
         System.out.println("[File_Thread] copy file fail !!!");
      } finally {
         try {
            if (fis != null) {
               fis.close();
            }

            if (fos != null) {
               fos.close();
            }
         } catch (Exception var14) {
            this.logger.error("", var14);
         }

      }

   }

   public synchronized void copyDirectory(File sourcelocation, File targetdirectory) throws IOException, InterruptedException {
      FileOutputStream fos;
      if (!targetdirectory.exists()) {
         targetdirectory.mkdir();
         fos = new FileOutputStream(targetdirectory);
         fos.close();
      }

      fos = null;
      FileInputStream fis = null;

      try {
         fis = new FileInputStream(sourcelocation);
         fos = new FileOutputStream(targetdirectory);
         int byteToDownload = 8192;
         byte[] b = new byte[byteToDownload];
         boolean var7 = false;

         int n;
         while((n = fis.read(b)) > 0) {
            fos.write(b, 0, n);
         }

         System.out.println("[STRM_Thread] Copied Sucessfully!");
      } catch (Exception var16) {
         this.logger.error("", var16);
      } finally {
         try {
            if (fos != null) {
               fos.close();
            }

            if (fis != null) {
               fis.close();
            }
         } catch (IOException var15) {
            this.logger.error("", var15);
         }

      }

   }

   public boolean deleteDirectoryRecursive(File filePath) {
      if (filePath.exists()) {
         File[] files = filePath.listFiles();

         for(int i = 0; i < files.length; ++i) {
            if (files[i].isDirectory()) {
               this.deleteDirectoryRecursive(files[i]);
            } else {
               files[i].delete();
            }
         }
      }

      return filePath.delete();
   }
}
