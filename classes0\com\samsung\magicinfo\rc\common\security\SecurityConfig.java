package com.samsung.magicinfo.rc.common.security;

import com.samsung.magicinfo.rc.common.security.AuthenticationJwtFilter;
import javax.servlet.Filter;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.SecurityBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.configurers.ExpressionUrlAuthorizationConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;

@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {
  public void configure(WebSecurity web) throws Exception {
    web.ignoring()
      .antMatchers(new String[] { 
          "/v2/api-docs", "/swagger-resources/**", "/configuration/ui", "/configuration/security", "/swagger-ui.html", "/openapi/**", "/error", "/index.html", "/static/**", "/config.js", 
          "/remote/**", "/images/**", "/api/information/**", "/webjars/**", "/**.png" });
  }
  
  protected void configure(HttpSecurity http) throws Exception {
    ((HttpSecurity)((HttpSecurity)((HttpSecurity)((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl)((HttpSecurity)http.csrf().disable()).authorizeRequests()
      
      .antMatchers(new String[] { "/api/information/**", "/ws/devices/**" })).permitAll()
      .and())
      
      .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
      .and())
      
      .formLogin()
      .disable())
      .addFilterAfter((Filter)new AuthenticationJwtFilter(), BasicAuthenticationFilter.class);
  }
}
