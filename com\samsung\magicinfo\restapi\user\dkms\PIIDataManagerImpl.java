package com.samsung.magicinfo.restapi.user.dkms;

import org.springframework.stereotype.Service;

@Service("PIIDataManager")
public class PIIDataManagerImpl implements PIIDataManager {
   public PIIDataManagerImpl() {
      super();
   }

   public void connect() {
   }

   public boolean checkIsEncrypted(String Text) {
      return false;
   }

   public String decryptData(String cipherText) {
      return cipherText;
   }

   public String encryptData(String plainText, String PIITag) {
      return plainText;
   }

   public String hash(String plainText, String piiTag) {
      return plainText;
   }
}
