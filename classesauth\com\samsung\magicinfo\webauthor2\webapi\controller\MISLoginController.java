package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.service.IllegalMISLoginArgsException;
import com.samsung.magicinfo.webauthor2.exception.service.UnsupportedMISVersionException;
import com.samsung.magicinfo.webauthor2.properties.MagicInfoProperties;
import com.samsung.magicinfo.webauthor2.service.MISLoginService;
import com.samsung.magicinfo.webauthor2.service.SupportedFormatService;
import com.samsung.magicinfo.webauthor2.util.ServerInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
@RequestMapping({"/main"})
public class MISLoginController {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.webapi.controller.MISLoginController.class);
  
  private MISLoginService misLoginService;
  
  private SupportedFormatService supportedFormatService;
  
  private MagicInfoProperties magicInfoProperties;
  
  private ServerInfo serverInfo;
  
  @Autowired
  public MISLoginController(MISLoginService misLoginService, SupportedFormatService supportedFormatService, MagicInfoProperties magicInfoProperties, ServerInfo serverInfo) {
    this.misLoginService = misLoginService;
    this.supportedFormatService = supportedFormatService;
    this.magicInfoProperties = magicInfoProperties;
    this.serverInfo = serverInfo;
  }
  
  @PostMapping
  public String postLogin(@RequestParam(required = false) String username, @RequestParam(required = false) String password, @RequestParam(required = false) String language, @RequestParam(required = false) String token, @RequestParam(required = false) String contentId, @RequestParam(required = false) String serverUrl, Model model) {
    if (!Strings.isNullOrEmpty(serverUrl)) {
      this.serverInfo.setServerUrl(serverUrl);
    } else {
      this.serverInfo.setServerUrl(this.magicInfoProperties.getWebauthorWebUrl());
    } 
    try {
      String actualToken = this.misLoginService.loginToMIS(username, password, token, language, contentId);
      return goToWebAuthorMainPage(actualToken, model);
    } catch (UnsupportedMISVersionException e) {
      logger.error(e.getMessage());
      model.addAttribute("errorMessage", e.getMessage());
      model.addAttribute("javax.servlet.error.status_code", Integer.valueOf(403));
      return "common/error";
    } 
  }
  
  @GetMapping
  public String getLogin(Model model) {
    logger.info("GET: getLogin()");
    try {
      String actualToken = this.misLoginService.refreshMISSessionByActualUserData();
      return goToWebAuthorMainPage(actualToken, model);
    } catch (IllegalMISLoginArgsException ex) {
      return redirectToLoginPage(model);
    } 
  }
  
  @PostMapping({"/inspireToken"})
  public ResponseEntity<?> inspireToken() {
    try {
      this.misLoginService.inspireToken();
      return ResponseEntity.ok().build();
    } catch (IllegalMISLoginArgsException e) {
      logger.error(e.getMessage());
      return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).build();
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    } 
  }
  
  @ExceptionHandler({Exception.class})
  public String misLoginControllerErrors(Exception ex, Model model) {
    logger.error(ex.getMessage());
    model.addAttribute("errorMessage", ex.getMessage());
    model.addAttribute("javax.servlet.error.status_code", Integer.valueOf(500));
    return "common/error";
  }
  
  private String redirectToLoginPage(Model model) {
    if (this.magicInfoProperties.getWebauthorDevelopment() != null && this.magicInfoProperties.getWebauthorDevelopment().equals("1")) {
      logger.info("Accessed WebAuthor developer page.");
      String defaultMisUrl = this.magicInfoProperties.getWebauthorWebUrl();
      model.addAttribute("misUrl", defaultMisUrl);
      return "devLogin";
    } 
    return "redirect:" + this.serverInfo.getServerUrl();
  }
  
  private String goToWebAuthorMainPage(String token, Model model) {
    logger.info("Accessed WebAuthor main page.");
    model.addAttribute("token", token);
    return "index";
  }
}
