package com.samsung.magicinfo.webauthor2.util;

import com.samsung.magicinfo.webauthor2.repository.inmemory.dao.PreviewUsageDao;
import com.samsung.magicinfo.webauthor2.repository.inmemory.model.PreviewUsage;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;

public class CleanPreviewFolder {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.util.CleanPreviewFolder.class);
  
  private static final String EVERYDAY_AT_MIDNIGHT = "0 0 0 * * *";
  
  public static final String PREVIEW_DIRECTORY_ROOT = "preview";
  
  @Autowired
  private ServletContext servletContext;
  
  private PreviewUsageDao previewUsageDao;
  
  private int daysToRemove = 10;
  
  public CleanPreviewFolder(PreviewUsageDao previewUsageDao, int daysToRemove) {
    this.previewUsageDao = previewUsageDao;
    this.daysToRemove = daysToRemove;
  }
  
  @Scheduled(cron = "0 0 0 * * *")
  public void cleanFolder() {
    int result = 0;
    result = removeUnusedContents();
    logger.info("Removed " + result + " unused preview files.");
  }
  
  public int removeUnusedContents() {
    Path pathToRepo = Paths.get(this.servletContext.getRealPath("preview"), new String[0]);
    List<PreviewUsage> toDelete = this.previewUsageDao.findOlderThanDays(this.daysToRemove);
    for (PreviewUsage usage : toDelete) {
      Path pathToDir = pathToRepo.resolve(usage.getContentId());
      logger.info("Deleting unused content: " + pathToDir.toString());
      this.previewUsageDao.delete(usage.getContentId());
      cleanup(pathToDir);
    } 
    return toDelete.size();
  }
  
  public void cleanup(Path dir) {
    if (dir != null && Files.exists(dir, new java.nio.file.LinkOption[0]))
      FileUtils.deleteQuietly(dir.toFile()); 
  }
  
  public static boolean isPathValid(String path) {
    return (!path.contains("%2E%2E") && !path.contains("%25%25") && !path.contains("..") && !path.contains("\\\\"));
  }
  
  public static boolean isCidValid(String cid) {
    Pattern contentIdRegex = Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");
    Matcher m = contentIdRegex.matcher(cid);
    return m.matches();
  }
}
