package com.samsung.common.exception;

public class BasicException extends Exception {
   private static final long serialVersionUID = 1662766671339948089L;
   private String code;
   private String subCode;
   private String reason;

   public BasicException() {
      super();
   }

   public BasicException(String reason) {
      super(reason);
      this.reason = reason;
   }

   public BasicException(Throwable cause) {
      super();
   }

   public BasicException(String code, String reason) {
      super(reason);
      this.code = code;
      this.reason = reason;
   }

   public BasicException(String reason, Throwable cause) {
      super(reason);
      this.reason = reason;
   }

   public BasicException(String code, String subCode, String reason) {
      super(reason);
      this.code = code;
      this.subCode = subCode;
      this.reason = reason;
   }

   public String getCode() {
      return this.code;
   }

   public String getReason() {
      return this.reason;
   }

   public String getSubCode() {
      return this.subCode;
   }

   public void setCode(String code) {
      this.code = code;
   }

   public void setReason(String reason) {
      this.reason = reason;
   }

   public void setSubCode(String subCode) {
      this.subCode = subCode;
   }
}
