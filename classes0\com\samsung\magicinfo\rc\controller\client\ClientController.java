package com.samsung.magicinfo.rc.controller.client;

import com.ctc.wstx.stax.WstxInputFactory;
import com.ctc.wstx.stax.WstxOutputFactory;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.ser.ToXmlGenerator;
import com.samsung.magicinfo.rc.model.client.Parameters;
import com.samsung.magicinfo.rc.model.client.Request;
import com.samsung.magicinfo.rc.model.client.Response;
import com.samsung.magicinfo.rc.service.ClientServiceImpl;
import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLOutputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
public class ClientController {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.controller.client.ClientController.class);
  
  @Autowired
  ClientServiceImpl clientService;
  
  @RequestMapping(value = {"/openapi/open"}, method = {RequestMethod.POST}, produces = {"text/xml;charset=UTF-8"})
  public String service(@RequestParam(value = "xml", required = false) String xml, @RequestParam(value = "token", required = false) String token, @RequestPart(value = "file", required = false) MultipartFile file, @RequestParam Map<String, String> allRequestParams) {
    Response response = new Response();
    XmlMapper xmlMapper = new XmlMapper();
    String deviceId = null;
    try {
      String serviceId = null;
      Parameters params = null;
      if (xml != null) {
        Request request = (Request)xmlMapper.readValue(xml, Request.class);
        if (request != null) {
          serviceId = getServiceId(request.getService().getId());
          params = request.getService().getParameters();
        } 
      } else {
        String paramsService = allRequestParams.get("service");
        serviceId = getServiceId(paramsService);
      } 
      if (params != null && params.getDeviceId() != null)
        deviceId = params.getDeviceId(); 
      switch (serviceId) {
        case "start":
          response = this.clientService.start(deviceId, params.getInterval(), params.getResolution(), params.getSuppKeyboard(), params.getSuppPosition(), params.getSuppLongPress(), params.getSuppRemote());
          break;
        case "stop":
          response = this.clientService.stop(deviceId, token);
          break;
        case "getTriggeringInfo":
          response = this.clientService.getTriggeringInfo(file, deviceId, String.valueOf(params.getAttachMode()));
          break;
      } 
    } catch (JsonProcessingException e) {
      log.error("JsonProcessingException", (Throwable)e);
    } catch (IOException e) {
      log.error("IOException", e);
    } 
    String returnXml = xmlToString(response);
    return returnXml;
  }
  
  @RequestMapping(value = {"/openapi/open"}, method = {RequestMethod.GET})
  public String clientService(@RequestPart(value = "file", required = false) MultipartFile file, @RequestParam(value = "service", required = false) String service, @RequestParam(value = "deviceId", required = false) String deviceId, @RequestParam(value = "token", required = false) String token, @RequestParam(value = "authority", required = false) String authority, @RequestParam(value = "interval", required = false, defaultValue = "0") int interval, @RequestParam(value = "resolution", required = false) String resolution, @RequestParam(value = "suppKeyboard", required = false) String suppKeyboard, @RequestParam(value = "suppPosition", required = false) String suppPosition, @RequestParam(value = "suppRemote", required = false) String suppRemote, @RequestParam(value = "suppLongPress", required = false) String suppLongPress, @RequestParam(value = "attachMode", required = false) String attachMode, @RequestBody(required = false) Request request) {
    Response response = null;
    if (!StringUtils.isEmpty(service) && service.startsWith("RMService")) {
      String action = getServiceId(service);
      switch (action) {
        case "status":
          response = this.clientService.status(deviceId);
          break;
        case "ready":
          response = this.clientService.ready(deviceId, token, authority);
          break;
      } 
    } 
    String rtn = xmlToString(response);
    return rtn;
  }
  
  private String getServiceId(String service) {
    if (service.indexOf(".") > -1) {
      String[] services = service.split("\\.");
      return services[1];
    } 
    return null;
  }
  
  private String xmlToString(Response response) {
    StringWriter stringWriter = new StringWriter();
    WstxOutputFactory wstxOutputFactory = new WstxOutputFactory();
    wstxOutputFactory.setProperty("com.ctc.wstx.useDoubleQuotesInXmlDecl", Boolean.TRUE);
    wstxOutputFactory.setProperty("com.ctc.wstx.outputEmptyElementHandler", (prefix, localName, nsURI, allowEmpty) -> false);
    XmlMapper mapper = new XmlMapper((XMLInputFactory)new WstxInputFactory(), (XMLOutputFactory)wstxOutputFactory);
    mapper.enable(SerializationFeature.INDENT_OUTPUT);
    mapper.configure(ToXmlGenerator.Feature.WRITE_XML_DECLARATION, true);
    mapper.configure(SerializationFeature.ORDER_MAP_ENTRIES_BY_KEYS, true);
    mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
    try {
      XMLStreamWriter sw = wstxOutputFactory.createXMLStreamWriter(stringWriter);
      mapper.writeValue(sw, response);
    } catch (XMLStreamException|IOException e) {
      log.error("", e);
    } 
    return stringWriter.toString();
  }
}
