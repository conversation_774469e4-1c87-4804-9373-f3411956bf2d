package com.samsung.magicinfo.rc.common.exception;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public enum RestExceptionCode {
  BAD_REQUEST("400000", "Bad Request"),
  BAD_REQUEST_PARAMETER_EMPTY("400001", "%s is required value."),
  BAD_REQUEST_PARAMETER_INVALID("400002", "%s is invalid value."),
  BAD_REQUEST_PARAMETER_MINVALUE("400003", "The %s must be greater than or equal to %s."),
  BAD_REQUEST_PARAMETER_MAXVALUE("400004", "The %s must be less than or equal to %s."),
  BAD_REQUEST_PARAMETER_SIZE("400005", "The %s size must be between %s and %s."),
  BAD_REQUEST_PARAMETER_PATTERN("400006", "The %s must match '%s'."),
  BAD_REQUEST_PARAMETER_NOT_ALLOW_PATTERN("400007", "The %s does not allowed pattern like '%s'."),
  BAD_REQUEST_PARAMETER_TYPE("400008", "The %s must be %s type."),
  BAD_REQUEST_SAME_ITEM_EXIST("400009", "The same %s exists."),
  BAD_REQUEST_NO_CHANGES("400010", "No changes have been made."),
  BAD_REQUEST_CONNECT_FAIL("400011", "The %s connection is failed."),
  BAD_REQUEST_RECYCLE_BIN_EMPTY("400012", "The recycle bin is already empty."),
  BAD_REQUEST_EXPIRED_CONTENT_NOT_ADD("400013", "The expired content cannot be added."),
  BAD_REQUEST_REQUESTBODY_INVALID("400014", "The value corresponding to RequestBody is invalid."),
  BAD_REQUEST_VALUE_NOT_SUPPORT("400015", "The %s is invalid. This value is not supported."),
  BAD_REQUEST_USER_NOT_EXIST("400016", "The login information does not exist for the user."),
  BAD_REQUEST_ITEM_ACCESS_DENIED("400017", "This %s does not belong to %s, so access is denied."),
  BAD_REQUEST_USED_ITEM_NOT_DELETE("400018", "The %s in use elsewhere cannot be deleted."),
  BAD_REQUEST_NOT_INCLUDE_IN_GROUP("400019", "The %s is not included in the group."),
  BAD_REQUEST_PARAMETER_NOT_NULL_OR_EMPTY("400020", "The %s value cannot be null or empty."),
  BAD_REQUEST_ENTER_NAME("400021", "Enter the %s name."),
  BAD_REQUEST_PARAMETER_INCORRECT("400022", "The %s is incorrect."),
  BAD_REQUEST_PARAMETER_INVALID_SPECIAL_CHARACTER1("400023", "You cannot enter an invalid special character (< > & ` ! ~ # @ % [ ] { } ; : + * ^ = ( ) \\\\\\\\ \\\\\" \\\\' ?)."),
  BAD_REQUEST_PARAMETER_INVALID_SPECIAL_CHARACTER2("400024", "You cannot enter an invalid special character (  , . < > & ` ! ~ # @ % [ ] { } ; : + * ^ = ( ) \\\\\\\\ \\\\\" \\\\' ?)."),
  BAD_REQUEST_CANNOT_DELETE_GROUP("400025", "You cannot delete this group."),
  BAD_REQUEST_CANNOT_NAME_WITH_SPACE("400026", "You cannot use a name that consists of spaces only."),
  BAD_REQUEST_INPUT_LENGTH_LIMIT("400027", "The maximum input length has been exceeded."),
  BAD_REQUEST_CANNOT_EDIT_ITEM("400028", "Cannot edit the %s as it is being edited by another user."),
  BAD_REQUEST_CHECK_INPUT_CHARACTER("400029", "Only alphanumeric characters, \"-\" and \"_\" are allowed. Special characters and spaces are not allowed."),
  BAD_REQUEST_INCORRECT_MOVE_GROUP("400030", "Move Group is incorrect."),
  BAD_REQUEST_CANNOT_EDIT_ANOTHER_USERS_GROUP("400031", "You cannot edit group of another user's ."),
  BAD_REQUEST_MULTI_BACKUP_NOT_WORK_SYNC("4000032", "Multi Backup does not work during Sync Play."),
  BAD_REQUEST_PARAMETER_NOT_SAMETIME("400033", "The parameter %s must not be entered at the same time."),
  BAD_REQUEST_CANNOT_DELETE_ROOT_GROUP("400034", "Cannot delete the root group."),
  BAD_REQUEST_CANNOT_DELETE_CONTAINS_REFERENCED_ITEMS_IN_GROUP("400035", "This group contains %s that is referenced elsewhere."),
  BAD_REQUEST_PARAMETER_TOGETHER("400036", "%s and %s must be used together."),
  UNAUTHORIZED("401000", "Unauthorized"),
  UNAUTHORIZED_TOKEN_EXPIRED("401001", "Token is expired."),
  UNAUTHORIZED_NO_SESSION_ID("401002", "There is no a session id in token."),
  FORBIDDEN("403000", "Forbidden"),
  FORBIDDEN_PERMISSION_DENIED("403001", "You are not allowed permission."),
  FORBIDDEN_ACCESS_DENIED("403002", "You are access denied."),
  FORBIDDEN_PERMISSION_NOT_HAVE("403003", "You do not have %s permission."),
  DATA_NOT_FOUND("404000", "Data not found"),
  METHOD_NOT_ALLOWED("405000", "Method not allowed"),
  INTERNAL_SERVER_ERROR("500000", "Internal Server Error"),
  INTERNAL_SERVER_ERROR_UNKNOWN("500001", "Unknown error occurred."),
  INTERNAL_SERVER_ERROR_UNEXPECTED("500002", "Unexpected condition was encountered."),
  SERVICE_UNAVAILABLE("503000", "Service Unavailable");
  
  private String code;
  
  private String message;
  
  private static final Map<String, com.samsung.magicinfo.rc.common.exception.RestExceptionCode> exceptionCodeMap;
  
  RestExceptionCode(String code, String message) {
    this.code = code;
    this.message = message;
  }
  
  public String getCode() {
    return this.code;
  }
  
  public String getMessage() {
    return this.message;
  }
  
  static {
    exceptionCodeMap = new HashMap<>();
    for (com.samsung.magicinfo.rc.common.exception.RestExceptionCode restExceptionCode : values())
      exceptionCodeMap.put(restExceptionCode.code, restExceptionCode); 
  }
  
  public static com.samsung.magicinfo.rc.common.exception.RestExceptionCode findBy(int key) {
    return exceptionCodeMap.get(String.valueOf(key));
  }
  
  public String generateFormattedMessages(String... stringFields) {
    String composedMsg = this.message;
    String[] formats = Optional.<String[]>ofNullable(stringFields).orElse(new String[0]);
    if (formats.length > 0)
      composedMsg = String.format(this.message, (Object[])formats); 
    return composedMsg;
  }
}
