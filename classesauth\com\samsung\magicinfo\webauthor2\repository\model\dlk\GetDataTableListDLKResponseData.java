package com.samsung.magicinfo.webauthor2.repository.model.dlk;

import com.samsung.magicinfo.webauthor2.repository.model.dlk.DLKTableData;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "replytablelist")
@XmlAccessorType(XmlAccessType.FIELD)
public class GetDataTableListDLKResponseData {
  @XmlElement
  private List<DLKTableData> tablelist;
  
  public List<DLKTableData> getTablelist() {
    return this.tablelist;
  }
  
  public void setTablelist(List<DLKTableData> tablelist) {
    this.tablelist = tablelist;
  }
}
