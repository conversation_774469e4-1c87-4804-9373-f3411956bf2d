package com.samsung.magicinfo.webauthor2.repository.inmemory.model;

import com.samsung.magicinfo.webauthor2.repository.inmemory.model.PreviewUsage;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.joda.time.LocalDate;
import org.springframework.jdbc.core.RowMapper;

public class PreviewUsageMapper implements RowMapper<PreviewUsage> {
  public PreviewUsage mapRow(ResultSet resultSet, int i) throws SQLException {
    PreviewUsage previewUsage = new PreviewUsage();
    previewUsage.setContentId(resultSet.getString("contentid"));
    previewUsage.setVersionId(resultSet.getInt("versionid"));
    previewUsage.setLastused(LocalDate.fromDateFields(resultSet.getDate("lastused")));
    previewUsage.setUserId(resultSet.getString("userid"));
    previewUsage.setStartPage(resultSet.getString("startpage"));
    previewUsage.setProgress(resultSet.getInt("progress"));
    return previewUsage;
  }
}
