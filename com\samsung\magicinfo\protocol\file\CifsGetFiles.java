package com.samsung.magicinfo.protocol.file;

import com.hierynomus.msdtyp.AccessMask;
import com.hierynomus.msfscc.fileinformation.FileIdBothDirectoryInformation;
import com.hierynomus.mssmb2.SMB2CreateDisposition;
import com.hierynomus.mssmb2.SMB2ShareAccess;
import com.hierynomus.mssmb2.SMBApiException;
import com.hierynomus.smbj.SMBClient;
import com.hierynomus.smbj.SmbConfig;
import com.hierynomus.smbj.auth.AuthenticationContext;
import com.hierynomus.smbj.connection.Connection;
import com.hierynomus.smbj.session.Session;
import com.hierynomus.smbj.share.DiskShare;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.FileUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentXmlManager;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.msgqueue.producer.MsgProducer;
import com.samsung.magicinfo.msgqueue.producer.MsgProducerImpl;
import com.samsung.magicinfo.msgqueue.util.AMQUtil;
import com.samsung.magicinfo.msgqueue.vo.MsgVO;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.scheduler.ScheduleManager;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.CharBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.Charset;
import java.nio.charset.CharsetEncoder;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.EnumSet;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.apache.xml.security.utils.Base64;
import org.quartz.JobDetail;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SimpleTrigger;
import org.springframework.jms.JmsException;

public class CifsGetFiles {
   private Logger logger = LoggingManagerV2.getLogger(CifsGetFiles.class);
   String miUserId = "";
   long groupId = 0L;
   String contentId = "";
   String contentName = "";
   String serverIp = "";
   String loginId = "";
   String password = "";
   String localPathByIp = "";
   String directory = "";
   long refreshInterval = 0L;
   boolean scheduledJob = false;
   String canRefresh = "";
   long loginRetryMaxCount = 0L;
   long loginRetryCount = 0L;
   String canLoginRetry = "";
   boolean result = false;
   List fileListToSave = new ArrayList();
   SMBClient client = null;
   Connection connection = null;
   Session session = null;
   DiskShare share = null;
   ContentInfo contentInfo = ContentInfoImpl.getInstance();
   String CONTENTS_HOME = "";
   boolean editMode = false;
   boolean settingChanged = false;
   List remoteFiles = new ArrayList();
   long totalSizeOfFiles = 0L;
   boolean fileChanged = false;
   String mainFileId = UUID.randomUUID().toString().toUpperCase();
   String characterEncoding = "";
   boolean isFilesToDownload = false;
   String subFolder = "";
   String connTimeout = "10";
   MsgVO msgVO = null;
   String pollingStatus = "SUCCESS";
   String statusDescription = "";

   CifsGetFiles(String miUserId, long groupId, String contentId, String contentName, String serverIp, String loginId, String password, String localPathByIp, String directory, long refreshInterval, boolean scheduledJob, String canRefresh, long loginRetryMaxCount, String canLoginRetry) {
      super();
      this.miUserId = miUserId;
      this.groupId = groupId;
      this.contentId = contentId;
      this.contentName = contentName;
      this.serverIp = serverIp;
      this.loginId = loginId;
      this.password = password;
      this.localPathByIp = localPathByIp;
      this.directory = directory;
      this.refreshInterval = refreshInterval;
      this.scheduledJob = scheduledJob;
      this.canRefresh = canRefresh;
      this.loginRetryMaxCount = loginRetryMaxCount;
      this.canLoginRetry = canLoginRetry;
   }

   CifsGetFiles(MsgVO msgVO) {
      super();
      this.miUserId = msgVO.getMiUserId();
      this.groupId = msgVO.getGroupId();
      this.contentId = msgVO.getContentId();
      this.contentName = msgVO.getContentName();
      this.serverIp = msgVO.getServerIp();
      this.loginId = msgVO.getLoginId();
      this.password = msgVO.getPassword();
      this.localPathByIp = msgVO.getLocalPathByIp();
      this.directory = msgVO.getDirectory();
      this.refreshInterval = msgVO.getRefreshInterval();
      this.scheduledJob = msgVO.isScheduledJob();
      this.canRefresh = msgVO.getCanRefresh();
      this.loginRetryMaxCount = msgVO.getLoginRetryMaxCount();
      this.loginRetryCount = msgVO.getLoginRetryCount();
      this.canLoginRetry = msgVO.getCanLoginRetry();
      this.result = msgVO.isResult();
      this.fileListToSave.addAll(msgVO.getFileListToSave());
      this.CONTENTS_HOME = msgVO.getCONTENTS_HOME();
      this.editMode = msgVO.isEditMode();
      this.settingChanged = msgVO.isSettingChanged();
      this.remoteFiles.addAll(msgVO.getRemoteFiles());
      this.totalSizeOfFiles = msgVO.getTotalSizeOfFiles();
      this.fileChanged = msgVO.isFileChanged();
      this.mainFileId = msgVO.getMainFileId();
      this.characterEncoding = msgVO.getCharacterEncoding();
      this.isFilesToDownload = msgVO.isFilesToDownload();
      this.subFolder = msgVO.getSubFolder();
      this.connTimeout = msgVO.getConnTimeout();
      this.msgVO = (MsgVO)msgVO.clone();
      this.pollingStatus = msgVO.getPollingStatus();
      this.statusDescription = msgVO.getStatusDescription();
   }

   public boolean getCifsFiles() throws SQLException, IOException, Exception {
      boolean isSuccess = false;
      this.logger.error("#####[INFO][MagicInfo_CIFS] STARTED to get CIFS Files : " + this.contentName + "[" + this.contentId + "]");

      label189: {
         boolean var2;
         try {
            this.CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
            if (!this.checkEditMode()) {
               break label189;
            }

            if (this.accessToRemoteServer()) {
               if (!this.getFileList()) {
                  this.logger.error("@@@@@[MagicInfo_CIFS] FAILED to get File List : " + this.contentName + "[" + this.contentId + "]");
               } else {
                  this.checkChangedFiles();
                  this.removeChangedTempFiles();
                  this.updateContentInfo();
                  if (AMQUtil.isEnabledRCDS() && this.isFilesToDownload) {
                     isSuccess = this.requestDownload();
                  } else {
                     this.downloadChangedFiles();
                     this.updatePlaylistInfo();
                     this.createCSDFile();
                     this.setScheduleJobCifs();
                     this.setScheduleJobDlk();
                  }
               }
               break label189;
            }

            this.logger.error("@@@@@[MagicInfo_CIFS] FAILED to access to RemoteServer : " + this.contentName + "[" + this.contentId + "]");
            var2 = false;
         } catch (Exception var6) {
            this.logger.error("[MagicInfo_CIFS] " + var6.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var6);
            this.logger.error(this.getMembers());
            if ("SUCCESS".equals(this.pollingStatus)) {
               this.pollingStatus = "FAIL";
               this.statusDescription = "UNEXPECTED ERROR";
            }
            break label189;
         } finally {
            if (!AMQUtil.isEnabledRCDS() || !isSuccess) {
               this.contentInfo.setIsReadyForNextCifsThread("Y", this.contentId);
               this.createPollingInfo();
            }

            this.result = true;
            if (this.share != null) {
               this.share.close();
            }

            if (this.session != null) {
               this.session.close();
            }

            if (this.connection != null) {
               this.connection.close();
            }

         }

         return var2;
      }

      this.logger.error("#####[INFO][MagicInfo_CIFS] ENDED to get CIFS Files : " + this.contentName + "[" + this.contentId + "][" + this.result + "]");
      return this.result;
   }

   public boolean getCifsFilesNext() throws SQLException, Exception {
      this.logger.error("#####[INFO][MagicInfo_CIFS] STARTED to get CIFS Files NEXT!! : " + this.contentName + "[" + this.contentId + "]");

      try {
         if (this.editMode && this.settingChanged) {
            this.contentInfo.updateCifsSettingByContentId(this.contentId, this.contentName, this.serverIp, this.loginId, Base64.encode(this.password.getBytes()), this.directory, this.refreshInterval, this.canRefresh, this.loginRetryMaxCount, this.loginRetryCount, this.canLoginRetry);
            if ("N".equals(this.canRefresh)) {
               String schedulerJobName = "CIFS_" + this.contentId;
               String schedulerJobGroup = "UpdateCifsContentService";
               CommonUtils.deleteJob(schedulerJobName, schedulerJobGroup);
            }
         }

         if ("SUCCESS".equals(this.pollingStatus)) {
            this.updateDownloadedFileInfos();
            this.updatePlaylistInfo();
            this.createCSDFile();
            this.setScheduleJobCifs();
            this.setScheduleJobDlk();
         }
      } catch (Exception var6) {
         this.logger.error("[MagicInfo_CIFS_Next] " + var6.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var6);
         this.logger.error(this.msgVO.toString());
         if ("SUCCESS".equals(this.pollingStatus)) {
            this.pollingStatus = "FAIL";
            this.statusDescription = "UNEXPECTED ERROR";
         }
      } finally {
         this.contentInfo.setIsReadyForNextCifsThread("Y", this.contentId);
         this.createPollingInfo();
         this.result = true;
      }

      this.logger.error("#####[INFO][MagicInfo_CIFS] ENDED to get CIFS Files NEXT!! : " + this.contentName + "[" + this.contentId + "][" + this.result + "]");
      return this.result;
   }

   private boolean checkEditMode() {
      boolean methodResult = true;

      try {
         List cifsInfo = this.contentInfo.getCifsContentSettingByContentId(this.contentId);
         if (cifsInfo != null && cifsInfo.size() > 0) {
            this.editMode = true;
            Map cifsInfoValues = (Map)cifsInfo.get(0);
            long oldValue = (Long)cifsInfoValues.get("refresh_interval");
            if (oldValue != this.refreshInterval) {
               this.settingChanged = true;
               this.logger.debug("[MagicInfo_CIFS] editMode " + this.contentId + " " + oldValue + " " + this.refreshInterval);
            }

            this.loginRetryCount = Long.parseLong(cifsInfoValues.get("login_retry_count").toString());
         }

         if (this.editMode) {
            ContentFile contentFile = this.contentInfo.getMainFileInfo(this.contentId);
            if (contentFile != null) {
               this.mainFileId = contentFile.getFile_id();
               this.logger.debug("[MagicInfo_CIFS] editMode : oldMainFile id = " + this.mainFileId);
            } else {
               this.logger.error("[MagicInfo_CIFS] editMode is true but main_file do not exist : " + this.contentName + "[" + this.contentId + "]");
            }
         }
      } catch (Exception var6) {
         this.logger.error("[MagicInfo_CIFS] " + var6.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var6);
         this.pollingStatus = "FAIL";
         this.statusDescription = "UNEXPECTED ERROR";
         methodResult = false;
      }

      return methodResult;
   }

   private boolean accessToRemoteServer() {
      boolean methodResult = true;

      String schedulerJobGroup;
      try {
         long timeout = 10L;
         this.connTimeout = CommonConfig.get("cifs.connection.timeout");
         if (StringUtils.isEmpty(this.connTimeout)) {
            this.connTimeout = "10";
         }

         timeout = Long.valueOf(this.connTimeout);
         if (this.scheduledJob) {
            this.password = new String(Base64.decode(this.password));
         }

         if (StringUtils.isEmpty(this.miUserId)) {
            this.miUserId = "admin";
         }

         this.directory = CifsFilesToDownload.changePath(this.directory);
         String[] splitDomainAndUserId = CifsFilesToDownload.splitDomainAndUserId(this.loginId);
         schedulerJobGroup = "";
         String userId = "";
         if (splitDomainAndUserId != null && splitDomainAndUserId.length > 0) {
            if (splitDomainAndUserId.length > 1) {
               schedulerJobGroup = splitDomainAndUserId[0];
               userId = splitDomainAndUserId[1];
            } else {
               userId = this.loginId;
            }
         } else {
            userId = this.loginId;
         }

         SmbConfig config = SmbConfig.builder().withTimeout(timeout, TimeUnit.SECONDS).withSoTimeout(timeout, TimeUnit.SECONDS).build();
         this.client = new SMBClient(config);
         this.connection = this.client.connect(this.serverIp);
         AuthenticationContext ac = new AuthenticationContext(userId, this.password.toCharArray(), schedulerJobGroup);
         this.session = this.connection.authenticate(ac);
         if (this.editMode && this.loginRetryCount > 0L) {
            this.loginRetryCount = 0L;
            this.contentInfo.updateCifsSettingByContentId(this.contentId, this.contentName, this.serverIp, this.loginId, Base64.encode(this.password.getBytes()), this.directory, this.refreshInterval, this.canRefresh, this.loginRetryMaxCount, this.loginRetryCount, this.canLoginRetry);
         }

         String[] dirInfo = this.directory.split("/");
         this.share = (DiskShare)this.session.connectShare(dirInfo[0]);
         if (dirInfo.length > 1) {
            for(int i = 1; i < dirInfo.length; ++i) {
               this.subFolder = this.subFolder + dirInfo[i];
               if (i < dirInfo.length - 1) {
                  this.subFolder = this.subFolder + "\\";
               }
            }
         }

         if (!this.share.folderExists(this.subFolder)) {
            this.logger.error("[MagicInfo_CIFS] INVALID PATH : " + this.contentName + "[" + this.contentId + "]");
            this.pollingStatus = "FAIL";
            this.statusDescription = "INVALID PATH";
            methodResult = false;
         } else if (!this.share.isConnected()) {
            this.logger.error("[MagicInfo_CIFS] FAILED to login to remote server : " + this.contentName + "[" + this.contentId + "]");
            this.pollingStatus = "FAIL";
            this.statusDescription = "LOGIN FAIL";
            methodResult = false;
         }
      } catch (Exception var12) {
         String exceptionMessage = var12.getMessage();
         this.logger.error("[MagicInfo_CIFS] " + exceptionMessage + "|" + this.contentName + "[" + this.contentId + "]", var12);
         this.pollingStatus = "FAIL";
         if (exceptionMessage.contains("timed out")) {
            this.statusDescription = "CONNECTION TIMEOUT";
         } else if (exceptionMessage.contains("STATUS_LOGON_FAILURE")) {
            this.statusDescription = "LOGIN FAIL";
            if (this.editMode && "Y".equals(this.canLoginRetry)) {
               if (this.loginRetryCount >= this.loginRetryMaxCount) {
                  this.canRefresh = "N";
                  this.canLoginRetry = "N";
                  this.loginRetryCount = 0L;
               } else {
                  ++this.loginRetryCount;
               }

               try {
                  this.contentInfo.updateCifsSettingByContentId(this.contentId, this.contentName, this.serverIp, this.loginId, Base64.encode(this.password.getBytes()), this.directory, this.refreshInterval, this.canRefresh, this.loginRetryMaxCount, this.loginRetryCount, this.canLoginRetry);
                  if ("N".equals(this.canRefresh)) {
                     String schedulerJobName = "CIFS_" + this.contentId;
                     schedulerJobGroup = "UpdateCifsContentService";
                     CommonUtils.deleteJob(schedulerJobName, schedulerJobGroup);
                  }
               } catch (Exception var11) {
                  schedulerJobGroup = var11.getMessage();
                  this.logger.error("[MagicInfo_CIFS] " + schedulerJobGroup + "|" + this.contentName + "[" + this.contentName + "]", var11);
               }
            }
         } else {
            this.statusDescription = "UNEXPECTED ERROR";
         }

         methodResult = false;
      }

      return methodResult;
   }

   private boolean getFileList() throws IOException, SQLException {
      List cifsFiles = this.share.list(this.subFolder);
      if (cifsFiles == null) {
         return false;
      } else {
         Iterator var2 = cifsFiles.iterator();

         while(var2.hasNext()) {
            FileIdBothDirectoryInformation file = (FileIdBothDirectoryInformation)var2.next();
            if (!file.getFileName().equals(".") && !file.getFileName().equals("..")) {
               boolean validType = false;
               String[] tempName = file.getFileName().split("[.]");
               int sizeOfSplitName = false;
               if (tempName.length > 0) {
                  int sizeOfSplitName = tempName.length - 1;
                  validType = this.contentInfo.getCodeFile(tempName[sizeOfSplitName].toUpperCase()).equalsIgnoreCase("");
               }

               if (!validType) {
                  this.remoteFiles.add(this.makeRemoteFileInfo(file.getFileName(), file.getEndOfFile(), "NONE", "N"));
               }
            }
         }

         return true;
      }
   }

   private void checkChangedFiles() throws Exception {
      for(int i = 0; i < this.remoteFiles.size(); ++i) {
         String[] arrRemoteFile = ((String)this.remoteFiles.get(i)).split("[|]");
         String smbFileName = arrRemoteFile[0];
         boolean isFileNameValid = Pattern.compile("([\\\\/:&*?<>|])|(%)([0-9a-fA-F])([0-9a-fA-F])").matcher(smbFileName).find();
         if (!isFileNameValid) {
            long newFileSize = Long.valueOf(arrRemoteFile[1]);
            long oldFileSize = 0L;
            InputStream sfis = null;

            try {
               Set s = new HashSet();
               s.add(SMB2ShareAccess.ALL.iterator().next());
               sfis = this.share.openFile(this.subFolder.equals("") ? smbFileName : this.subFolder + "\\" + smbFileName, EnumSet.of(AccessMask.GENERIC_READ), (Set)null, s, SMB2CreateDisposition.FILE_OPEN, (Set)null).getInputStream();
            } catch (SMBApiException var16) {
               this.logger.error("[MagicInfo_CIFS] FAILED to connect file : " + this.contentName + "[" + smbFileName + "][" + newFileSize + "]");
               this.logger.error("[MagicInfo_CIFS] exception Message : " + var16.getMessage());
               continue;
            } finally {
               if (sfis != null) {
                  sfis.close();
               }

            }

            String src = this.localPathByIp + File.separator + smbFileName;
            File oldFile = SecurityUtils.getSafeFile(src);
            if (oldFile.exists()) {
               oldFileSize = oldFile.length();
            }

            String localFileId = this.contentInfo.getFileIdFromContentByFileNameAndSize(this.contentId, smbFileName, oldFileSize);
            if (!oldFile.exists() || localFileId == null || oldFileSize != newFileSize && smbFileName.equals(oldFile.getName())) {
               this.logger.error("Kim 1.3 cifs compare size OLD vs NEW : " + Long.toString(oldFileSize) + "[ vs ]" + Long.toString(newFileSize));
               this.logger.error("Kim 1.4 cifs compare name OLD vs NEW : " + oldFile.getName() + "[ vs ]" + smbFileName);
               this.fileChanged = true;
               this.isFilesToDownload = true;
               if (oldFile.exists() && localFileId != null) {
                  this.remoteFiles.set(i, this.makeRemoteFileInfo(smbFileName, newFileSize, "MODIFIED", "N"));
               } else {
                  this.remoteFiles.set(i, this.makeRemoteFileInfo(smbFileName, newFileSize, "NEW", "N"));
               }

               this.logger.info("[MagicInfo_CIFS] Need to get new file : " + oldFile.getName());
            }

            this.totalSizeOfFiles += newFileSize;
         }
      }

   }

   private void removeChangedTempFiles() throws Exception {
      List preContentFileList = this.contentInfo.getFileList(this.contentId);
      Iterator var2 = preContentFileList.iterator();

      while(var2.hasNext()) {
         ContentFile contentFile = (ContentFile)var2.next();
         boolean existsServerFile = false;
         String preContentFileName = contentFile.getFile_name();
         long preContentFileSize = contentFile.getFile_size();

         String oldFileHash;
         for(int i = 0; i < this.remoteFiles.size(); ++i) {
            String[] arrRemoteFile = ((String)this.remoteFiles.get(i)).split("[|]");
            oldFileHash = arrRemoteFile[0];
            long smbFileSize = Long.valueOf(arrRemoteFile[1]);
            if (preContentFileName.equalsIgnoreCase(oldFileHash) && preContentFileSize == smbFileSize) {
               existsServerFile = true;
               break;
            }
         }

         if (!existsServerFile && !preContentFileName.equalsIgnoreCase("CifsMetadata.CIFS")) {
            this.logger.info("[MagicInfo_CIFS] will remove Previous file : " + this.contentName + "[" + this.contentId + "] " + preContentFileName);
            String src = this.localPathByIp + File.separator + preContentFileName;
            File preContentFile = SecurityUtils.getSafeFile(src);
            if (preContentFile.exists()) {
               this.logger.info("[MagicInfo_CIFS] local file exists : " + this.contentName + "[" + this.contentId + "] " + preContentFileName);
               oldFileHash = FileUtils.getHash(preContentFile);
               String oldFileId = null;
               if (StringUtils.isEmpty(oldFileHash)) {
                  this.logger.info("[MagicInfo_CIFS] Old File Hash is NULL : " + this.contentName + "[" + this.contentId + "] " + preContentFileName);
                  oldFileId = this.contentInfo.getFileIdFromContentByFileNameAndSize(this.contentId, preContentFileName, preContentFileSize);
               } else {
                  oldFileId = this.contentInfo.getFileIDByHash(preContentFile.getName(), preContentFile.length(), oldFileHash);
               }

               if (oldFileId == null) {
                  this.logger.info("[MagicInfo_CIFS] Old File ID is NULL : " + this.contentName + "[" + this.contentId + "] " + preContentFileName);
               } else {
                  this.contentInfo.deleteFileFromContentMap(this.contentId, oldFileId);
                  if (this.contentInfo.isDeletableFileFromContents(oldFileId)) {
                     this.contentInfo.deleteFile(oldFileId);
                  }
               }

               if (!preContentFile.delete()) {
                  this.logger.error("[MagicInfo_CIFS] Can NOT remove Previous File : " + this.contentName + "[" + this.contentId + "] " + preContentFileName);
               } else {
                  this.logger.info("[MagicInfo_CIFS] REMOVED Previous File : " + this.contentName + "[" + this.contentId + "] " + preContentFileName);
               }
            } else {
               this.logger.info("[MagicInfo_CIFS] local file does NOT exist : " + this.contentName + "[" + this.contentId + "] " + preContentFileName);
               this.contentInfo.deleteFileFromContentMapByFileName(this.contentId, preContentFileName);
            }

            this.fileChanged = true;
         }
      }

   }

   private void updateContentInfo() throws SQLException, ConfigException {
      Content content = new Content();
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      long orgId = userInfo.getRootGroupIdByUserId(this.miUserId);
      Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
      boolean contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
      String fileNameStyle = "CIFS_" + ContentUtils.getFolderIp(this.serverIp) + this.loginId + '_' + this.directory.replace('/', '_');
      if (this.contentName.equalsIgnoreCase("")) {
         this.contentName = fileNameStyle;
      }

      content.setContent_id(this.contentId);
      content.setCreator_id(this.miUserId);
      content.setContent_name(this.contentName);
      content.setIs_deleted("N");
      content.setShare_flag(1);
      content.setOrganization_id(userInfo.getRootGroupIdByUserId(this.miUserId));
      content.setVersion_id(0L);
      content.setMedia_type("CIFS");
      content.setThumb_file_id("CIFS_THUMBNAIL");
      content.setTotal_size(this.totalSizeOfFiles);
      content.setIs_active("N");
      content.setMain_file_id(this.mainFileId);
      content.setIs_linear_vwl("N");
      content.setScreen_count(0);
      content.setContent_meta_data("");
      content.setX_count(0);
      content.setY_count(0);
      content.setX_range(0);
      content.setY_range(0);
      content.setIs_streaming("N");
      content.setMain_file_Extension("CIFS");
      if (contentsApprovalEnable) {
         if (content.getMain_file_Extension().equalsIgnoreCase("LFT")) {
            content.setApproval_status("APPROVED");
         } else {
            AbilityUtils abilityUtils = new AbilityUtils();
            if (abilityUtils.isContentApprovalAuthority(this.miUserId)) {
               content.setApproval_status("APPROVED");
            } else {
               content.setApproval_status("UNAPPROVED");
            }
         }
      } else {
         content.setApproval_status("APPROVED");
      }

      this.logger.info("[MagicInfo_CIFS] isEdit/fileChanged/settingChanged" + this.editMode + "/" + this.fileChanged + "/" + this.settingChanged + "|" + this.contentName + "[" + this.contentId + "]");
      if (this.editMode) {
         if (this.fileChanged) {
            this.contentInfo.updateContentVersionInfoByContentId(this.totalSizeOfFiles, this.contentId);
         }

         if (this.settingChanged) {
            this.contentInfo.updateCifsSettingByContentId(this.contentId, this.contentName, this.serverIp, this.loginId, Base64.encode(this.password.getBytes()), this.directory, this.refreshInterval, this.canRefresh, this.loginRetryMaxCount, this.loginRetryCount, this.canLoginRetry);
         }
      } else {
         this.contentInfo.addContentInfo(content);
         this.contentInfo.addContentVersionInfo(content);
         this.contentInfo.addMapGroupContent(this.contentId, this.groupId);
         this.contentInfo.addCifsSetting(this.contentId, this.contentName, this.serverIp, this.loginId, Base64.encode(this.password.getBytes()), this.directory, this.refreshInterval, this.canRefresh, this.loginRetryMaxCount, this.loginRetryCount, this.canLoginRetry);
         this.contentInfo.setApprovalStatus(content.getContent_id(), content.getApproval_status(), "");
         if (content.getApproval_status() != null && content.getApproval_status().equalsIgnoreCase("UNAPPROVED")) {
            List approverList = userInfo.getContentApproverListByGroupId(userInfo.getRootGroupIdByUserId(content.getCreator_id()));
            if (approverList != null) {
               for(int i = 0; i < approverList.size(); ++i) {
                  String tmpUserId = (String)((Map)approverList.get(i)).get("user_id");
                  this.contentInfo.addContentApproverMap(content.getContent_id(), tmpUserId);
               }
            }
         }
      }

   }

   private void downloadChangedFiles() throws SQLException, Exception {
      if (!this.isFilesToDownload) {
         this.logger.debug("[MagicInfo_CIFS] No Files To Download " + this.contentName + "[" + this.contentId + "]");
      } else {
         File localPathByIpDir = SecurityUtils.getSafeFile(this.localPathByIp);
         if (!localPathByIpDir.exists()) {
            localPathByIpDir.mkdir();
         }

         for(int i = 0; i < this.remoteFiles.size(); ++i) {
            String[] arrRemoteFile = ((String)this.remoteFiles.get(i)).split("[|]");

            try {
               String fileName = arrRemoteFile[0];
               long fileSize = Long.valueOf(arrRemoteFile[1]);
               String fileStatus = arrRemoteFile[2];
               boolean isFileNameValid = Pattern.compile("([\\\\/:&*?<>|])|(%)([0-9a-fA-F])([0-9a-fA-F])").matcher(fileName).find();
               this.logger.error("Kim 2 cifs remoteFiles:" + (String)this.remoteFiles.get(i) + ":" + isFileNameValid);
               if (!isFileNameValid) {
                  String localFileHashCode = this.contentInfo.getHashCodeFromContentByFileNameAndSize(fileName, "CIFS_CONTENT", fileSize);
                  String fileId = "";
                  if (localFileHashCode != null && !localFileHashCode.equals("")) {
                     fileId = this.contentInfo.getFileIDByHash(fileName, fileSize, localFileHashCode);
                  } else {
                     fileId = UUID.randomUUID().toString().toUpperCase();
                  }

                  this.fileListToSave.add(fileId);
                  if (!"NONE".equalsIgnoreCase(fileStatus)) {
                     this.logger.info("[MagicInfo_CIFS] DOWNLOADING : " + this.contentName + "[" + this.contentId + "] " + fileName + "|" + fileSize + "|" + fileStatus);
                     String src = this.localPathByIp + File.separator + fileName;
                     File fileIdDir = SecurityUtils.getSafeFile(this.CONTENTS_HOME + File.separator + fileId);
                     String dest = fileIdDir + File.separator + fileName;
                     File fileFromCIFS = SecurityUtils.getSafeFile(this.localPathByIp, fileName);
                     FileOutputStream fos = null;

                     try {
                        fos = new FileOutputStream(this.localPathByIp + File.separator + fileName);
                        int byteToDownload = 8192;
                        boolean isSuccess = false;
                        Set s = new HashSet();
                        s.add(SMB2ShareAccess.ALL.iterator().next());
                        InputStream sfis = this.share.openFile(this.subFolder.equals("") ? fileName : this.subFolder + "\\" + fileName, EnumSet.of(AccessMask.GENERIC_READ), (Set)null, s, SMB2CreateDisposition.FILE_OPEN, (Set)null).getInputStream();
                        byte[] b = new byte[byteToDownload];
                        boolean var21 = false;

                        int n;
                        while((n = sfis.read(b)) > 0) {
                           fos.write(b, 0, n);
                        }

                        fos.close();
                        sfis.close();
                        if (SecurityUtils.getSafeFile(this.localPathByIp + File.separator + fileName).isFile()) {
                           isSuccess = true;
                        }

                        if (isSuccess) {
                           String cifsFileHashCode = FileUtils.getHash(fileFromCIFS);
                           boolean hashCheck = false;
                           if (!hashCheck) {
                              if (!fileIdDir.exists()) {
                                 fileIdDir.mkdir();
                                 FileOutputStream tempTrargetFos = new FileOutputStream(dest);
                                 tempTrargetFos.close();
                              }

                              this.logger.debug("[MagicInfo_CIFS] copy_src  : " + src);
                              this.logger.debug("[MagicInfo_CIFS] copy_dest : " + dest);
                              this.copyDirectory(SecurityUtils.getSafeFile(src), SecurityUtils.getSafeFile(dest));
                              if (!this.contentInfo.isExistFileByHash(fileName, SecurityUtils.getSafeFile(dest).length(), cifsFileHashCode)) {
                                 ContentFile contentFile;
                                 if (localFileHashCode != null) {
                                    if (localFileHashCode.equalsIgnoreCase(cifsFileHashCode)) {
                                       contentFile = new ContentFile();
                                       fileId = UUID.randomUUID().toString().toUpperCase();
                                       contentFile.setFile_id(fileId);
                                       contentFile.setFile_name(fileName);
                                       contentFile.setFile_size(SecurityUtils.getSafeFile(dest).length());
                                       contentFile.setFile_path(this.CONTENTS_HOME + File.separator + fileId);
                                       contentFile.setHash_code(cifsFileHashCode);
                                       contentFile.setCreator_id(this.miUserId);
                                       contentFile.setFile_type("CONTENT");
                                       contentFile.setIs_streaming("N");
                                       this.contentInfo.addFile(contentFile);
                                    }
                                 } else {
                                    contentFile = new ContentFile();
                                    contentFile.setFile_id(fileId);
                                    contentFile.setFile_name(fileName);
                                    contentFile.setFile_size(SecurityUtils.getSafeFile(dest).length());
                                    contentFile.setFile_path(this.CONTENTS_HOME + File.separator + fileId);
                                    contentFile.setHash_code(cifsFileHashCode);
                                    contentFile.setCreator_id(this.miUserId);
                                    contentFile.setFile_type("CONTENT");
                                    contentFile.setIs_streaming("N");
                                    this.contentInfo.addFile(contentFile);
                                 }
                              } else {
                                 fileId = this.contentInfo.getFileIDByHash(fileName, SecurityUtils.getSafeFile(dest).length(), cifsFileHashCode);
                              }

                              this.remoteFiles.set(i, this.makeRemoteFileInfo(fileName, fileSize, fileStatus, "Y"));
                              long version = this.contentInfo.getVersionInfoByContentId(this.contentId);
                              this.contentInfo.addMapContentFile(this.contentId, version, fileId);
                           } else {
                              this.logger.debug("[MagicInfo_CIFS] The hash value is matched " + fileName + " " + fileSize);
                           }
                        } else {
                           this.logger.info("[MagicInfo_CIFS] FAILED to get file from CIFS " + fileName + " " + fileSize);
                        }
                     } catch (Exception var35) {
                        this.logger.error("[MagicInfo_CIFS] " + var35.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var35);
                     } finally {
                        if (fos != null) {
                           try {
                              fos.close();
                           } catch (Exception var34) {
                              this.logger.error("[MagicInfo_CIFS] " + var34.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var34);
                           }
                        }

                     }
                  } else {
                     this.logger.debug("[MagicInfo_CIFS] The same file exists " + fileName + " " + fileSize);
                  }
               }
            } catch (Exception var37) {
               this.logger.error("[MagicInfo_CIFS] " + var37.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var37);
            }
         }

      }
   }

   private void createCSDFile() throws Exception {
      if (!this.fileChanged) {
         this.logger.debug("[MagicInfo_CIFS][CIFS_Thread] createCSDFile : No Changed Files " + this.contentName + "[" + this.contentId + "]");
      } else {
         List fileList = this.contentInfo.getFileListByContentId(this.contentId);
         long version = this.contentInfo.getVersionInfoByContentId(this.contentId) + 1L;
         File csdFolder = SecurityUtils.getSafeFile(this.CONTENTS_HOME + File.separator + "contents_meta" + File.separator + this.contentId);
         File csdFile;
         if (csdFolder.exists()) {
            csdFile = SecurityUtils.getSafeFile(this.CONTENTS_HOME + File.separator + "contents_meta" + File.separator + this.contentId + File.separator + "ContentsMetadata.CSD");
            csdFile.delete();
         }

         csdFile = this.createCSD(fileList);
         if (csdFile != null && csdFile.exists()) {
            String srcFile = this.CONTENTS_HOME + File.separator + "contents_meta" + File.separator + this.contentId + File.separator + "ContentsMetadata.CSD";
            ContentFile contentFile = new ContentFile();
            String hashCode = "";
            long fileSize = 0L;
            String newMainFileId = UUID.randomUUID().toString().toUpperCase();
            String oldMainFileId = this.mainFileId;
            String newFilePath = this.CONTENTS_HOME + File.separator + newMainFileId;
            String newFilePathFile = this.CONTENTS_HOME + File.separator + newMainFileId + File.separator + "CifsMetadata.CIFS";
            File newMetaFile = SecurityUtils.getSafeFile(newFilePathFile);
            String oldFilePath = this.CONTENTS_HOME + File.separator + oldMainFileId + File.separator;
            File oldMetaFile = SecurityUtils.getSafeFile(oldFilePath + "CifsMetadata.CIFS");
            File oldMetaFileFolder = SecurityUtils.getSafeFile(oldFilePath);
            File fileCmsFile = SecurityUtils.getSafeFile(newFilePath);
            if (!fileCmsFile.exists()) {
               fileCmsFile.mkdir();
            }

            this.logger.debug("[MagicInfo_CIFS] csd_src  : " + srcFile);
            this.logger.debug("[MagicInfo_CIFS] csd_dest : " + newFilePathFile);
            this.copyFile(SecurityUtils.getSafeFile(srcFile), newMetaFile);
            hashCode = FileUtils.getHash(newMetaFile);
            fileSize = newMetaFile.length();
            contentFile.setFile_id(newMainFileId);
            contentFile.setFile_name("CifsMetadata.CIFS");
            contentFile.setFile_size(fileSize);
            contentFile.setFile_path(newFilePath);
            contentFile.setHash_code(hashCode);
            contentFile.setCreator_id(this.miUserId);
            contentFile.setFile_type("CIFS_MAIN");
            contentFile.setIs_streaming("N");
            this.fileListToSave.add(newMainFileId);
            this.contentInfo.deleteFile(oldMainFileId);
            oldMetaFile.delete();
            oldMetaFileFolder.delete();
            this.contentInfo.addFile(contentFile);
            this.logger.info("[MagicInfo_CIFS] updateContentVersionInfoWithFileId : " + newMainFileId + "  " + version);
            this.contentInfo.updateContentVersionInfoWithFileId(this.contentId, newMainFileId, version);
         }

         this.contentInfo.setIsActive(this.contentId, "Y");
         this.logger.info("[MagicInfo_CIFS] Updated CSD & DB |" + this.contentName + "[" + this.contentId + "]");
      }
   }

   private void setScheduleJobCifs() throws Exception {
      if (this.editMode && !this.settingChanged && !this.fileChanged) {
         this.logger.debug("[MagicInfo_CIFS] setScheduleJobCifs : No files in Server " + this.contentName + "[" + this.contentId + "]");
      } else {
         Scheduler scheduler = ScheduleManager.getSchedulerInstance();
         int startingDelay = Integer.valueOf(String.valueOf(this.refreshInterval));
         String schedulerJobName = "CIFS_" + this.contentId;
         String schedulerJobGroup = "UpdateCifsContentService";
         CommonUtils.deleteJob(schedulerJobName, schedulerJobGroup);
         this.logger.info("[MagicInfo_CIFS] UPDATE_CIFS_CONTENT_SERVICE : " + schedulerJobName + " " + this.refreshInterval);
         if (this.refreshInterval > 0L) {
            if ("Y".equals(this.canRefresh)) {
               JobDetail jobdetail = null;
               jobdetail = CommonUtils.getJobDetail(schedulerJobName, schedulerJobGroup, CifsContentScheduleJob.class);
               Calendar currTime = Calendar.getInstance();
               currTime.add(12, startingDelay);
               SimpleTrigger trigger = CommonUtils.getSimpleTrigger(schedulerJobName, schedulerJobGroup, currTime.getTime(), this.refreshInterval * 60L * 1000L);

               try {
                  scheduler.scheduleJob(jobdetail, trigger);
               } catch (SchedulerException var9) {
                  this.logger.error(var9);
               }

               this.logger.info("[MagicInfo_CIFS] Start to schedule : " + this.contentName + "[" + this.contentId + "]");
            }

            ScheduleInfo sInfo = ScheduleInfoImpl.getInstance();
            sInfo.setScheduleTrigger(this.contentId);
            EventInfo eInfo = EventInfoImpl.getInstance();
            eInfo.setContentTrigger(this.contentId);
         } else {
            this.logger.info("[MagicInfo_CIFS] UPDATE_CIFS_CONTENT_SERVICE : refresh_interval is 0 " + this.contentName + "[" + this.contentId + "]");
         }

      }
   }

   private void setScheduleJobDlk() throws ConfigException, Exception {
      if (!this.fileChanged) {
         this.logger.debug("[MagicInfo_CIFS] setScheduleJobDlk : No Changed files " + this.contentName + "[" + this.contentId + "]");
      } else {
         List dlkContentList = null;
         String inputDataContentId = this.contentId;
         dlkContentList = this.contentInfo.getDlkContentIdByIputDataContentId(inputDataContentId);
         if (dlkContentList != null && dlkContentList.size() > 0) {
            for(int i = 0; i < dlkContentList.size(); ++i) {
               ContentXmlManager contentXmlManager = new ContentXmlManager();
               Map dlkContent = (Map)dlkContentList.get(i);
               String dlkContentId = dlkContent.get("DLK_CONTENT_ID").toString();
               long nextVersion = this.contentInfo.getContentNextVer(dlkContentId);
               String strVersionId = String.valueOf(nextVersion);
               String dlkMainFileId = this.contentInfo.getMainFileInfo(dlkContentId).getFile_id();
               String prevMediaSlideMainFileId = this.mainFileId;
               String mediaSlideMainFileId = this.contentInfo.getMainFileInfo(this.contentId).getFile_id();
               ContentFile dlkFile = this.contentInfo.getFileInfo(dlkMainFileId);
               String dlkFilePath = dlkFile.getFile_path() + File.separator + dlkFile.getFile_name();
               ContentFile mediaSlideFile = this.contentInfo.getFileInfo(mediaSlideMainFileId);
               this.logger.debug("[MagicInfo_FTP] New mediaSlideFile ID : " + mediaSlideFile.getFile_id());
               this.logger.debug("[MagicInfo_FTP] dlkContentId/contentID/strVersionID/nextVersion : " + dlkContentId + "/" + this.contentId + "/" + strVersionId + "/" + nextVersion);
               contentXmlManager.modifyMediaSlideInfo(prevMediaSlideMainFileId, mediaSlideFile, dlkFilePath, this.contentId, dlkContentId);
               String hashCode = FileUtils.getHash(SecurityUtils.getSafeFile(dlkFilePath));
               long fileSize = SecurityUtils.getSafeFile(dlkFilePath).length();
               this.contentInfo.updateHashCodeByMainFileId(dlkMainFileId, fileSize, hashCode);
               this.contentInfo.updateVersionAndMainFileIdInContentVersionInfo(nextVersion, dlkMainFileId, dlkContentId);
               this.logger.info("[MagicInfo_FTP] Start to schedule : " + this.contentName + "[" + this.contentId + "]");
               ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
               scheduleInfo.setContentTrigger(dlkContentId);
            }
         }

      }
   }

   private void updateDownloadedFileInfos() throws SQLException, Exception {
      if (!this.isFilesToDownload) {
         this.logger.debug("[MagicInfo_CIFS] No Files To Download " + this.contentName + "[" + this.contentId + "]");
      } else {
         for(int i = 0; i < this.remoteFiles.size(); ++i) {
            try {
               String[] arrRemoteFile = ((String)this.remoteFiles.get(i)).split("[|]");
               String fileName = arrRemoteFile[0];
               long fileSize = Long.valueOf(arrRemoteFile[1]);
               String fileStatus = arrRemoteFile[2];
               String isDownloaded = arrRemoteFile[3];
               this.logger.debug("[MagicInfo_CIFS] " + fileName + ", " + fileSize + ", " + fileStatus + ", isDownloaded(" + isDownloaded + ")");
               String localFileHashCode = this.contentInfo.getHashCodeFromContentByFileNameAndSize(fileName, "CIFS_CONTENT", fileSize);
               String fileId = "";
               if (localFileHashCode != null && !localFileHashCode.equals("")) {
                  fileId = this.contentInfo.getFileIDByHash(fileName, fileSize, localFileHashCode);
               } else {
                  fileId = UUID.randomUUID().toString().toUpperCase();
               }

               this.fileListToSave.add(fileId);
               if (!"NONE".equalsIgnoreCase(fileStatus) && "Y".equalsIgnoreCase(isDownloaded)) {
                  this.logger.debug("[MagicInfo_CIFS] Not exist or mismatched : " + fileName);
                  String src = this.localPathByIp + File.separator + fileName;
                  File fileIdDir = SecurityUtils.getSafeFile(this.CONTENTS_HOME + File.separator + fileId);
                  String dest = fileIdDir + File.separator + fileName;
                  File localPathByIpFile = SecurityUtils.getSafeFile(this.localPathByIp, fileName);

                  try {
                     String cifsFileHashCode = FileUtils.getHash(localPathByIpFile);
                     boolean hashCheck = false;
                     if (!hashCheck) {
                        if (!fileIdDir.exists()) {
                           fileIdDir.mkdir();
                           (new FileOutputStream(dest)).close();
                        }

                        this.logger.debug("[MagicInfo_CIFS] copy_src  : " + src);
                        this.logger.debug("[MagicInfo_CIFS] copy_dest : " + dest);
                        this.copyDirectory(SecurityUtils.getSafeFile(src), SecurityUtils.getSafeFile(dest));
                        if (!this.contentInfo.isExistFileByHash(fileName, SecurityUtils.getSafeFile(dest).length(), cifsFileHashCode)) {
                           ContentFile contentFile;
                           if (localFileHashCode != null) {
                              if (localFileHashCode.equalsIgnoreCase(cifsFileHashCode)) {
                                 contentFile = new ContentFile();
                                 fileId = UUID.randomUUID().toString().toUpperCase();
                                 contentFile.setFile_id(fileId);
                                 contentFile.setFile_name(fileName);
                                 contentFile.setFile_size(SecurityUtils.getSafeFile(dest).length());
                                 contentFile.setFile_path(this.CONTENTS_HOME + File.separator + fileId);
                                 contentFile.setHash_code(cifsFileHashCode);
                                 contentFile.setCreator_id(this.miUserId);
                                 contentFile.setFile_type("CONTENT");
                                 contentFile.setIs_streaming("N");
                                 this.contentInfo.addFile(contentFile);
                              }
                           } else {
                              contentFile = new ContentFile();
                              contentFile.setFile_id(fileId);
                              contentFile.setFile_name(fileName);
                              contentFile.setFile_size(SecurityUtils.getSafeFile(dest).length());
                              contentFile.setFile_path(this.CONTENTS_HOME + File.separator + fileId);
                              contentFile.setHash_code(cifsFileHashCode);
                              contentFile.setCreator_id(this.miUserId);
                              contentFile.setFile_type("CONTENT");
                              contentFile.setIs_streaming("N");
                              this.contentInfo.addFile(contentFile);
                           }
                        } else {
                           fileId = this.contentInfo.getFileIDByHash(fileName, SecurityUtils.getSafeFile(dest).length(), cifsFileHashCode);
                        }

                        long version = this.contentInfo.getVersionInfoByContentId(this.contentId);
                        this.contentInfo.addMapContentFile(this.contentId, version, fileId);
                     } else {
                        this.logger.debug("[MagicInfo_CIFS] The hash value is matched " + fileName + " " + fileSize);
                     }
                  } catch (Exception var18) {
                     this.logger.error("[MagicInfo_CIFS_Next] " + var18.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var18);
                     this.logger.error(this.getMembers());
                  }
               }
            } catch (Exception var19) {
               this.logger.error("[MagicInfo_CIFS_Next] " + var19.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var19);
               this.logger.error(this.getMembers());
            }
         }

      }
   }

   public void updatePlaylistInfo() {
      try {
         PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
         List playlistInfoList = playlistInfo.getPlaylistInfoByContentId(this.contentId);

         for(int idx = 0; idx < playlistInfoList.size(); ++idx) {
            Map mapPlaylistInfo = (Map)playlistInfoList.get(idx);
            String sPlaylistId = mapPlaylistInfo.get("playlist_id").toString();
            long lVersionId = Long.parseLong(mapPlaylistInfo.get("version_id").toString());
            Playlist curPlaylist = playlistInfo.getPlaylistVerInfo(sPlaylistId, lVersionId);
            if (curPlaylist != null) {
               List playlistContentList = playlistInfo.getContentList(curPlaylist.getPlaylist_id(), curPlaylist.getVersion_id());
               long lTotalSize = 0L;

               for(int idx2 = 0; idx2 < playlistContentList.size(); ++idx2) {
                  PlaylistContent playlistContent = (PlaylistContent)playlistContentList.get(idx2);
                  Content curContent = this.contentInfo.getContentActiveVerInfo(playlistContent.getContent_id());
                  if (curContent != null && curContent.getTotal_size() != null) {
                     lTotalSize += curContent.getTotal_size();
                  }
               }

               playlistInfo.setTotalSize(curPlaylist.getPlaylist_id(), curPlaylist.getVersion_id(), lTotalSize);
               this.logger.info("[SF00179387]UPDATED totalSize of Playlist[" + curPlaylist.getPlaylist_id() + "][" + curPlaylist.getPlaylist_name() + "]ver[" + curPlaylist.getVersion_id() + "][" + lTotalSize + "]");
            }
         }
      } catch (Exception var15) {
         this.logger.error("[SF00179387]can NOT update totalSize of Playlist", var15);
      }

   }

   public void createPollingInfo() {
      try {
         if (!this.editMode || this.fileChanged || "FAIL".equals(this.pollingStatus)) {
            Calendar currTime = Calendar.getInstance();
            if (!"FAIL".equals(this.pollingStatus)) {
               List cifsSettingList = this.contentInfo.getCifsContentSettingByContentId(this.contentId);
               if (cifsSettingList != null && cifsSettingList.size() > 0) {
                  for(int i = 0; i < this.remoteFiles.size(); ++i) {
                     String[] arrRemoteFile = ((String)this.remoteFiles.get(i)).split("[|]");
                     String remoteFileName = arrRemoteFile[0];
                     long remoteFileSize = Long.valueOf(arrRemoteFile[1]);
                     String remoteFileStatus = arrRemoteFile[2];
                     String remoteFileDownload = arrRemoteFile[3];
                     if ("NONE".equals(remoteFileStatus)) {
                        remoteFileStatus = "";
                     } else if (!"Y".equals(remoteFileDownload)) {
                        remoteFileStatus = "FAIL";
                     }

                     this.contentInfo.addPollingFileInfo(this.contentId, currTime.getTime(), remoteFileName, remoteFileSize, remoteFileStatus, this.miUserId);
                  }
               }
            }

            this.contentInfo.addPollingInfo(this.contentId, currTime.getTime(), this.pollingStatus, this.remoteFiles.size(), this.statusDescription, this.miUserId);
         }
      } catch (Exception var10) {
         this.logger.error("[MagicInfo_CIFS] " + var10.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var10);
         this.logger.error(this.getMembers());
      }

   }

   public synchronized void copyDirectory(File sourceDir, File targetDir) {
      FileOutputStream fos = null;
      FileInputStream fis = null;

      try {
         if (!targetDir.exists()) {
            targetDir.mkdir();
            (new FileOutputStream(targetDir)).close();
         }

         fis = new FileInputStream(sourceDir);
         fos = new FileOutputStream(targetDir);
         int byteToDownload = 8192;
         byte[] b = new byte[byteToDownload];
         boolean var7 = false;

         int n;
         while((n = fis.read(b)) > 0) {
            fos.write(b, 0, n);
         }

         this.logger.info("[MagicInfo_CIFS] Copied SUCCESSFULLY!! : " + sourceDir.getName() + ", " + targetDir.getName());
      } catch (Exception var16) {
         this.logger.error("[MagicInfo_CIFS] " + var16.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var16);
         this.logger.error(this.getMembers());
      } finally {
         try {
            if (fos != null) {
               fos.close();
            }

            if (fis != null) {
               fis.close();
            }
         } catch (Exception var15) {
            this.logger.error("[MagicInfo_CIFS] " + var15.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var15);
            this.logger.error(this.getMembers());
         }

      }

   }

   public File createCSD(List fileList) {
      FileOutputStream fileOutputStream = null;
      FileChannel fileChannel = null;

      try {
         File metaFolder = SecurityUtils.getSafeFile(this.CONTENTS_HOME + File.separator + "contents_meta");
         if (!metaFolder.exists()) {
            metaFolder.mkdir();
         }

         File csdFolder = SecurityUtils.getSafeFile(this.CONTENTS_HOME + File.separator + "contents_meta" + File.separator + this.contentId);
         if (!csdFolder.exists()) {
            csdFolder.mkdir();
         }

         this.logger.info("[MagicInfo_CIFS] createCSD " + this.contentId);
         File csdFile = SecurityUtils.getSafeFile(this.CONTENTS_HOME + File.separator + "contents_meta" + File.separator + this.contentId + File.separator + "ContentsMetadata.CSD");
         StringBuffer stringBuffer;
         if (csdFile.exists() || csdFile.createNewFile()) {
            stringBuffer = new StringBuffer("");
            stringBuffer.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n");
            stringBuffer.append("<CIFSContent cid=\"").append(this.contentId).append("\">\n");
            stringBuffer.append("  <User>").append(this.miUserId).append("</User>\n");
            stringBuffer.append("  <Title>").append(this.contentName).append("</Title>\n");
            long category = this.groupId;
            stringBuffer.append("<Category>").append(category).append("</Category>\n");
            stringBuffer.append("\t<CifsFileContents>\n");

            try {
               List contentFileList = this.contentInfo.getFileList(this.contentId);
               Iterator var11 = contentFileList.iterator();

               while(var11.hasNext()) {
                  ContentFile contentFile = (ContentFile)var11.next();
                  if (contentFile != null && !contentFile.getFile_name().equalsIgnoreCase("CifsMetadata.CIFS")) {
                     stringBuffer.append("\t\t<FileItem>\n");
                     stringBuffer.append("\t\t\t<FileId>").append(contentFile.getFile_id()).append("</FileId>\n");
                     stringBuffer.append("\t\t\t<FileName>").append(contentFile.getFile_name()).append("</FileName>\n");
                     stringBuffer.append("\t\t\t<FileSize>").append(contentFile.getFile_size()).append("</FileSize>\n");
                     stringBuffer.append("\t\t\t<FileHashValue>").append(contentFile.getHash_code()).append("</FileHashValue>\n");
                     stringBuffer.append("\t\t</FileItem>\n");
                  }
               }
            } catch (Exception var25) {
               this.logger.error("[MagicInfo_CIFS] " + var25.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var25);
            }

            stringBuffer.append("\t</CifsFileContents>\n");
            stringBuffer.append("</CIFSContent>");
            fileOutputStream = new FileOutputStream(csdFile);
            fileChannel = fileOutputStream.getChannel();
            Charset cs = Charset.forName("UTF-8");
            CharsetEncoder encoder = cs.newEncoder();
            fileChannel.write(encoder.encode(CharBuffer.wrap(stringBuffer.toString())));
            fileOutputStream.close();
            fileChannel.close();
            File var13 = csdFile;
            return var13;
         }

         stringBuffer = null;
         return stringBuffer;
      } catch (Exception var26) {
         this.logger.error("[MagicInfo_CIFS] " + var26.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var26);
         this.logger.error(this.getMembers());
      } finally {
         try {
            if (fileOutputStream != null) {
               fileOutputStream.close();
            }

            if (fileChannel != null) {
               fileChannel.close();
            }
         } catch (Exception var24) {
            this.logger.error("[MagicInfo_CIFS] " + var24.getMessage() + "|" + this.contentName + "[" + this.contentId + "]", var24);
            this.logger.error(this.getMembers());
         }

      }

      return null;
   }

   public synchronized void copyFile(File srcFile, File destFile) {
      FileInputStream fis = null;
      FileOutputStream fos = null;
      boolean var5 = false;

      try {
         fis = new FileInputStream(srcFile);
         fos = new FileOutputStream(destFile);

         int data;
         while((data = fis.read()) != -1) {
            fos.write(data);
         }

         this.logger.info("[MagicInfo_CIFS] copy file SUCCESSFULLY! : " + srcFile.getName() + ", " + destFile.getName());
      } catch (Exception var15) {
         this.logger.error("[MagicInfo_CIFS] " + var15.getMessage() + "|" + this.contentName + "|" + srcFile.getName() + "|" + destFile.getName(), var15);
         this.logger.error(this.getMembers());
      } finally {
         try {
            if (fis != null) {
               fis.close();
            }

            if (fos != null) {
               fos.close();
            }
         } catch (Exception var14) {
            this.logger.error("[MagicInfo_CIFS] " + var14.getMessage() + "|" + this.contentName + "|" + srcFile.getName() + "|" + destFile.getName(), var14);
            this.logger.error(this.getMembers());
         }

      }

   }

   public String makeRemoteFileInfo(String fileName, long fileSize, String fileStatus, String isDownload) {
      return fileName + "|" + fileSize + "|" + fileStatus + "|" + isDownload;
   }

   public boolean requestDownload() {
      if (!this.isFilesToDownload) {
         this.logger.debug("[MagicInfo_CIFS] No files To Download : " + this.contentName + "[" + this.contentId + "]");
         return false;
      } else {
         MsgVO msgVO = new MsgVO();
         msgVO.setType("MSG_CIFS_DOWNLOAD_START");
         msgVO.setMiUserId(this.miUserId);
         msgVO.setGroupId(this.groupId);
         msgVO.setContentId(this.contentId);
         msgVO.setContentName(this.contentName);
         msgVO.setServerIp(this.serverIp);
         msgVO.setLoginId(this.loginId);
         msgVO.setPassword(this.password);
         msgVO.setLocalPathByIp(this.localPathByIp);
         msgVO.setDirectory(this.directory);
         msgVO.setRefreshInterval(this.refreshInterval);
         msgVO.setScheduledJob(this.scheduledJob);
         msgVO.setCanRefresh(this.canRefresh);
         msgVO.setLoginRetryMaxCount(this.loginRetryMaxCount);
         msgVO.setLoginRetryCount(this.loginRetryCount);
         msgVO.setCanLoginRetry(this.canLoginRetry);
         msgVO.setResult(this.result);
         List tmpFileListToSave = new ArrayList();
         tmpFileListToSave.addAll(this.fileListToSave);
         msgVO.setFileListToSave(tmpFileListToSave);
         msgVO.setCONTENTS_HOME(this.CONTENTS_HOME);
         msgVO.setEditMode(this.editMode);
         msgVO.setSettingChanged(this.settingChanged);
         List tmpRemoteFiles = new ArrayList();
         tmpRemoteFiles.addAll(this.remoteFiles);
         msgVO.setRemoteFiles(tmpRemoteFiles);
         msgVO.setTotalSizeOfFiles(this.totalSizeOfFiles);
         msgVO.setFileChanged(this.fileChanged);
         msgVO.setMainFileId(this.mainFileId);
         msgVO.setCharacterEncoding(this.characterEncoding);
         msgVO.setFilesToDownload(this.isFilesToDownload);
         msgVO.setSubFolder(this.subFolder);
         msgVO.setConnTimeout(this.connTimeout);
         msgVO.setPollingStatus(this.pollingStatus);
         msgVO.setStatusDescription(this.statusDescription);
         MsgProducer msgProducer = MsgProducerImpl.getInstance();

         try {
            msgProducer.send(msgVO);
         } catch (JmsException var6) {
            this.logger.error("[MagicInfo_CIFS] " + var6.getMessage() + " " + msgVO.getContentName() + "[" + msgVO.getContentId() + "]", var6);
            this.pollingStatus = "FAIL";
            this.statusDescription = "UNEXPECTED ERROR AMQ";
            return false;
         }

         this.logger.info("<<<<<<<<<<[MagicInfo_CIFS] " + msgVO.getType() + " " + msgVO.getContentName() + "[" + msgVO.getContentId() + "]");
         return true;
      }
   }

   public String getMembers() {
      String retVal = "miUserId           : " + this.miUserId + "\ngroupId            : " + this.groupId + "\ncontentId          : " + this.contentId + "\ncontentName        : " + this.contentName + "\nserverIp           : " + this.serverIp + "\nloginId            : " + this.loginId + "\npassword           : " + this.password + "\nlocalPathByIp      : " + this.localPathByIp + "\ndirectory          : " + this.directory + "\nrefreshInterval    : " + this.refreshInterval + "\nscheduledJob       : " + this.scheduledJob + "\ncanRefresh         : " + this.canRefresh + "\nloginRetryMaxCount : " + this.loginRetryMaxCount + "\nloginRetryCount    : " + this.loginRetryCount + "\ncanLoginRetry      : " + this.canLoginRetry + "\nresult             : " + this.result + "\nCONTENTS_HOME      : " + this.CONTENTS_HOME + "\neditMode           : " + this.editMode + "\nsettingChanged     : " + this.settingChanged + "\ntotalSizeOfFiles   : " + this.totalSizeOfFiles + "\nfileChanged        : " + this.fileChanged + "\nmainFileId         : " + this.mainFileId + "\ncharacterEncoding  : " + this.characterEncoding + "\nisFilesToDownload  : " + this.isFilesToDownload + "\nfileListToSave     : \n";

      int i;
      for(i = 0; i < this.fileListToSave.size(); ++i) {
         retVal = retVal + " " + (i + 1) + ") " + (String)this.fileListToSave.get(i) + "\n";
      }

      retVal = retVal + "remoteFiles        : \n";

      for(i = 0; i < this.remoteFiles.size(); ++i) {
         retVal = retVal + " " + (i + 1) + ") " + (String)this.remoteFiles.get(i) + "\n";
      }

      retVal = retVal + "subFolder          : " + this.subFolder + "\n";
      retVal = retVal + "connTimeout        : " + this.connTimeout + "\n";
      retVal = retVal + "pollingStatus      : " + this.pollingStatus + "\n";
      retVal = retVal + "statusDescription  : " + this.statusDescription + "\n";
      return retVal;
   }
}
