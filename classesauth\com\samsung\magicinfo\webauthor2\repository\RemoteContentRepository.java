package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.exception.repository.MagicInfoRemoteContentException;
import java.io.IOException;
import java.nio.file.Path;

public interface RemoteContentRepository {
  String getXmlFileContents(String paramString1, String paramString2) throws MagicInfoRemoteContentException;
  
  Path getContentFileFromMagicInfoServer(Path paramPath, String paramString1, String paramString2) throws IOException, MagicInfoRemoteContentException;
  
  Path getContentFileFromMagicInfoServer(Path paramPath, String paramString1, String paramString2, String paramString3) throws IOException, MagicInfoRemoteContentException;
  
  Path getFontFileFromMagicInfoServer(String paramString1, String paramString2) throws IOException, MagicInfoRemoteContentException;
  
  byte[] getContentFileFromMagicInfoServer(String paramString1, String paramString2) throws MagicInfoRemoteContentException;
  
  String getVwlContent(String paramString1, String paramString2) throws MagicInfoRemoteContentException;
}
