package com.samsung.common.constants;

public class StatisticsConstants {
   public static final String LOG_TYPE_POP = "POP";
   public static final String LOG_TYPE_FACE = "FACE";
   public static final String POP_BASE_DIR = "pop";
   public static final String FACE_BASE_DIR = "face";
   public static final String EVT_POP_BASE_DIR = "evt";
   public static final String FACE_RECOG_CONTENT_BASED_CSV_NAME_PREFIX = "face_cb";
   public static final String FACE_RECOG_HUMAN_BASED_CSV_NAME_PREFIX = "face_hb";
   public static final String FACE_RECOG_AUDIENCE_BASED_CSV_NAME_PREFIX = "face_ab";
   public static final String FACE_RECOG_TRAFFIC_BASED_CSV_NAME_PREFIX = "face_tb";
   public static final String EVENT_BASED_CSV_NAME_PREFIX = "event";
   public static final String EXTRA_TYPE_CSV_NAME_PREFIX = "ext";
   public static final String EXPAND_TYPE_CSV_NAME_PREFIX = "exp";
   public static final String BACKUP_DIR = "backup";
   public static final String REPORT_DIR = "report";
   public static final String RUNNING_DIR = "running";
   public static final int DEFAULT_RUN_COUNT = 10;
   public static final int DEFAULT_DB_RUN_COUNT = 20;
   public static final long DEFAULT_STATS_DB_CLEAN_PERIOD = 30L;
   public static final int STAT_MAX_WAIT_MS = 3600000;
   public static final int DEFAULT_MAX_READ_LINE = 1000;
   public static final long DEFAULT_STAT_JOB_INTERVAL = 3L;
   public static final long DEFAULT_HOURLY_JOB_INTERVAL = 60L;
   public static final long MINUTE_IN_MILLISECONDS = 60000L;
   public static final String POP_FILE_HISTORY_STATUS_NOT_RECEIVED = "NOT_RECEIVED";
   public static final String POP_FILE_HISTORY_STATUS_RECEIVE_SUCCESS = "RECEIVE_SUCCESS";
   public static final String POP_FILE_HISTORY_STATUS_RECEIVE_FAIL = "RECEIVE_FAIL";
   public static final String POP_FILE_HISTORY_STATUS_DB_SUCCESS = "DB_SUCCESS";
   public static final String POP_FILE_HISTORY_STATUS_DB_FAIL = "DB_FAIL";
   public static final String POP_FILE_ERR_TYPE_OVERTIME = "OVERTIME";
   public static final String POP_MODE_NORMAL = "POP_MODE_NORMAL";
   public static final String POP_MODE_WAS = "POP_MODE_WAS";
   public static final String POP_MODE_DB = "POP_MODE_DB";
   public static final String[] arrBackupRegExpList = new String[]{"\\d{8}", "\\d{8}_\\d{3}.zip"};
   public static final String[] arrReportRegExpList = new String[]{"EXT_\\d{4}_M\\d{2}_\\d+_EXT.csv", "EXT_\\d{4}_\\d{2}_W\\d{2}_\\d+_EXT.csv", "EXP_\\d{4}_M\\d{2}_\\d+_EXP.csv", "EXP_\\d{4}_\\d{2}_W\\d{2}_\\d+_EXP.csv", "EVENT_\\d{4}_M\\d{2}_\\d+.csv", "EVENT_\\d{4}_\\d{2}_W\\d{2}_\\d+.csv", "\\d{4}_M\\d{2}_\\d+.csv", "\\d{4}_\\d{2}_W\\d{2}_\\d+.csv"};
   public static final String[] arrPopFilePrefixList = new String[]{"EXT_", "EXP_", "EVENT_"};

   public StatisticsConstants() {
      super();
   }
}
