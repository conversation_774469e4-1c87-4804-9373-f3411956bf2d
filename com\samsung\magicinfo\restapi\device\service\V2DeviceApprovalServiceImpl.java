package com.samsung.magicinfo.restapi.device.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.export.PdfBuilder;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DBCacheUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.ExternalSystemUtils;
import com.samsung.common.utils.RoleUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceDao;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceGroupDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManagerImpl;
import com.samsung.magicinfo.framework.device.job.manager.JobManager;
import com.samsung.magicinfo.framework.device.job.manager.JobManagerImpl;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManager;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManagerImpl;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManager;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManagerImpl;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.device.vwlLayout.dao.VwlLayoutDao;
import com.samsung.magicinfo.framework.device.vwlLayout.entity.VwlLayout;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.DOMVwtParserImpl;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.DOMVwtWriterImpl;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwlLayoutManager;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwlLayoutManagerImpl;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwtXmlParser;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwtXmlParserInfo;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwtXmlWriter;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwtXmlWriterInfo;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfo;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfoImpl;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAO;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.protocol.http.vwlLayoutServlet;
import com.samsung.magicinfo.protocol.interfaces.WSCall;
import com.samsung.magicinfo.restapi.device.model.V2ChildDeviceScanInfo;
import com.samsung.magicinfo.restapi.device.model.V2DeviceApproval;
import com.samsung.magicinfo.restapi.device.model.V2DeviceApprovalResources;
import com.samsung.magicinfo.restapi.device.model.V2DeviceApproveResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceApprovedItem;
import com.samsung.magicinfo.restapi.device.model.V2DeviceCheckPossibility;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReplacementInfo;
import com.samsung.magicinfo.restapi.device.model.V2DeviceResource;
import com.samsung.magicinfo.restapi.device.model.V2LicenseUsageResource;
import com.samsung.magicinfo.restapi.device.model.V2VwlLayoutResource;
import com.samsung.magicinfo.restapi.device.utils.RESTDeviceUtils;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.service.statistics.DeviceStatisticsDownloadService;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.ModelAndView;

@Service("V2DeviceApprovalService")
@Transactional
public class V2DeviceApprovalServiceImpl implements V2DeviceApprovalService {
   protected Logger logger = LoggingManagerV2.getLogger(V2DeviceApprovalServiceImpl.class);
   private DeviceStatisticsDownloadService downloadService = null;
   private DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
   private DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
   private DeviceGroupDao deviceGroupDao = new DeviceGroupDao();
   private DeviceLogManager logManager = DeviceLogManagerImpl.getInstance();
   private MonitoringManager monitoringManager = MonitoringManagerImpl.getInstance();
   private DeviceDao deviceDao = new DeviceDao((SqlSession)null);
   private AlarmManager alarmManager = AlarmManagerImpl.getInstance();
   private DeviceConfManager deviceConfManager = DeviceConfManagerImpl.getInstance();
   private JobManager jobManager = JobManagerImpl.getInstance();
   private MonitoringManagerInfo monitoringManagerInfo = MonitoringManagerInfoImpl.getInstance("PREMIUM");
   private DeviceTimeConfManager deviceTimeConfManager = DeviceTimeConfManagerImpl.getInstance("PREMIUM");
   private DeviceSystemSetupConfManager deviceSystemSetupConfManager = DeviceSystemSetupConfManagerImpl.getInstance("PREMIUM");
   private MessageInfo messageInfo = MessageInfoImpl.getInstance();
   private ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
   private ContentInfo contentInfo = ContentInfoImpl.getInstance();
   private ScheduleInfoDAO scheduleInfoDao = new ScheduleInfoDAO();

   public V2DeviceApprovalServiceImpl() {
      super();
   }

   public void setDownloadService(DeviceStatisticsDownloadService downloadService) {
      this.downloadService = downloadService;
   }

   @PreAuthorize("hasAnyAuthority('Device Only Approval Authority')")
   public V2PageResource getUnapprovedDevices(int startIndex, int pageSize) throws Exception {
      String order_dir = "asc";
      int orderNumber = true;
      String sortName = this.getSortNameByOrderNumber(1);
      SelectCondition selectCondition = new SelectCondition();
      selectCondition.setOrder_dir("asc");
      selectCondition.setSort_name(sortName);
      Map condition = new HashMap();
      condition.put("sort", sortName);
      condition.put("dir", "asc");
      condition.put("condition", condition);
      PagedListInfo info = this.deviceDao.getNonApprovedDeviceList(startIndex, pageSize, condition);
      List deviceList = info.getPagedResultList();
      List list = new ArrayList();
      Map preAssignedGroupMap = DBCacheUtils.getPreAssignedGroupMap();
      Iterator var12 = deviceList.iterator();

      while(var12.hasNext()) {
         Map hash = (Map)var12.next();
         list.add(this.getDeviceResourceFromMap(hash, preAssignedGroupMap));
      }

      V2PageResource newResource = V2PageResource.createPageResource(list, info, pageSize);
      newResource.setStartIndex(startIndex);
      return newResource;
   }

   private String getSortNameByOrderNumber(int orderNumber) {
      if (orderNumber == 1) {
         return "device_name";
      } else if (orderNumber == 2) {
         return "device_id";
      } else if (orderNumber == 3) {
         return "ip_address";
      } else if (orderNumber == 4) {
         return "device_model_name";
      } else {
         return orderNumber == 5 ? "serial_decimal" : "create_date";
      }
   }

   private Timestamp getCreateTimeFromDeviceMap(Map hash) {
      return null == hash.get("create_date") ? null : (Timestamp)hash.get("create_date");
   }

   private boolean getSupportUhdFromFlag(String supportFlag) {
      return supportFlag != null && supportFlag.length() >= 4 && supportFlag.charAt(3) == '1';
   }

   private boolean getSupportCabinetSettingFromFlag(String supportFlag) {
      return supportFlag != null && supportFlag.length() >= 5 && supportFlag.charAt(4) == '1';
   }

   private V2DeviceResource getDeviceResourceFromMap(Map deviceMap, Map preAssignedGroupMap) {
      Timestamp createTime = this.getCreateTimeFromDeviceMap(deviceMap);
      String supportFlag = (String)deviceMap.get("support_flag");
      boolean supportUhd = this.getSupportUhdFromFlag(supportFlag);
      boolean supportCabinetSetting = this.getSupportCabinetSettingFromFlag(supportFlag);
      V2DeviceResource resource = new V2DeviceResource();
      resource.setDeviceId((String)deviceMap.get("device_id"));
      resource.setDeviceType((String)deviceMap.get("device_type"));
      resource.setDeviceTypeVersion(String.valueOf(deviceMap.get("device_type_version")));
      resource.setDeviceModelName((String)deviceMap.get("device_model_name"));
      resource.setSerialDecimal((String)((String)(deviceMap.get("serial_decimal") != null ? deviceMap.get("serial_decimal") : "")));
      resource.setIpAddress(StrUtils.nvl((String)deviceMap.get("ip_address")));
      resource.setCreateDate(createTime);
      resource.setDeviceName(StrUtils.nvl((String)deviceMap.get("device_name")));
      resource.setHasChild((Boolean)deviceMap.get("has_child"));
      resource.setSupportFlag((String)deviceMap.get("support_flag"));
      resource.setSupportUhd(supportUhd);
      resource.setSupportCabinetSetting(supportCabinetSetting);
      DeviceGroup preAssignedGroup = (DeviceGroup)preAssignedGroupMap.get(deviceMap.get("device_id"));
      resource.setGroupId(String.valueOf(preAssignedGroup != null ? preAssignedGroup.getGroup_id() : -1L));
      resource.setDeviceGroupName(String.valueOf(preAssignedGroup != null ? preAssignedGroup.getGroup_name() : ""));
      resource.setDeviceModelName(StrUtils.nvl((String)deviceMap.get("device_model_name")));
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceApprovalResources getPossibilityOfGroupMovement(List deviceIds, String groupId) throws Exception {
      Long groupIdLong = Long.parseLong(groupId);
      List failedList = new ArrayList();
      List successfulList = new ArrayList();
      long index = 1L;
      Iterator var8 = deviceIds.iterator();

      while(var8.hasNext()) {
         String deviceId = (String)var8.next();
         V2DeviceCheckPossibility item = this.getGroupMovementResultItem(deviceId, groupIdLong, index++);
         if ("failure".equalsIgnoreCase(item.getStatus())) {
            failedList.add(item);
         } else {
            successfulList.add(item);
         }
      }

      V2DeviceApprovalResources result = new V2DeviceApprovalResources();
      result.setDeviceIds(deviceIds);
      result.setGroupId(groupId);
      result.setApproveList(successfulList);
      result.setUnapproveList(failedList);
      return result;
   }

   private V2DeviceCheckPossibility getGroupMovementResultItem(String deviceId, Long groupIdLong, long index) throws Exception {
      String checkResult = DeviceUtils.checkGroupStatusToMove(deviceId, "", groupIdLong);
      V2DeviceCheckPossibility item = new V2DeviceCheckPossibility();
      String failureStatus = "failure";
      String successStatus = "success";
      RestExceptionCode restExceptionCode;
      if (null != checkResult && !"FAIL_UNKNOWN_ERROR".equalsIgnoreCase(checkResult) && !"FAIL_UNAVAILABLE_GROUP".equalsIgnoreCase(checkResult)) {
         if ((CommonConfig.get("e2e.enable") == null || CommonConfig.get("e2e.enable").equalsIgnoreCase("false")) && !DeviceUtils.checkLicenseCountToMove(deviceId, groupIdLong, index)) {
            restExceptionCode = RestExceptionCode.SERVICE_CANNOT_MOVE_LIMITED_LICENSE;
            item.setAllFields(deviceId, "failure", restExceptionCode.getMessage(), restExceptionCode.getCode());
            return item;
         } else if ("FAIL_IS_REDUNDANCY_GROUP".equalsIgnoreCase(checkResult)) {
            restExceptionCode = RestExceptionCode.BAD_REQUEST_DEVICE_GROUP_TYPE_DIFFERENT;
            item.setAllFields(deviceId, "failure", restExceptionCode.getMessage(), restExceptionCode.getCode());
            int deviceCount = this.deviceGroupDao.getCntDeviceInDeviceGroup(groupIdLong.intValue());
            if (0 == deviceCount) {
               this.deviceGroupDao.setIsRedundancy(groupIdLong, false);
               this.deviceInfo.deleteBackupPlayer(groupIdLong.intValue());
               this.deviceInfo.deleteBackupTargetPlayer(groupIdLong.intValue());
            }

            return item;
         } else if ("FAIL_VWT_DEVICE".equalsIgnoreCase(checkResult)) {
            restExceptionCode = RestExceptionCode.BAD_REQUEST_CANNOT_MOVE_IN_VWL_GROUP_DEVICE;
            item.setAllFields(deviceId, "failure", restExceptionCode.getMessage(), restExceptionCode.getCode());
            return item;
         } else if ("FAIL_SYNCPLAY_GROUP".equalsIgnoreCase(checkResult)) {
            restExceptionCode = RestExceptionCode.BAD_REQUEST_DEVICE_IN_SYNCPLAY_GROUP;
            item.setAllFields(deviceId, "failure", restExceptionCode.getMessage(), restExceptionCode.getCode());
            return item;
         } else if ("FAIL_LOWER_PRIRORITY_DEVICE".equalsIgnoreCase(checkResult)) {
            restExceptionCode = RestExceptionCode.BAD_REQUEST_LOW_PRIORITY_DEVICE;
            item.setAllFields(deviceId, "failure", restExceptionCode.getMessage(), restExceptionCode.getCode());
            return item;
         } else {
            DeviceGroupDao devGroupDao = new DeviceGroupDao();
            boolean isVwlGroup = devGroupDao.getBooleanVwlGroupId(groupIdLong);
            int deviceCount = this.deviceGroupDao.getCntDeviceInDeviceGroup(groupIdLong.intValue());
            if (isVwlGroup && 0 < deviceCount) {
               RestExceptionCode restExceptionCode = RestExceptionCode.BAD_REQUEST_CANNOT_ADD_DEVICE_TO_VWL_GROUP;
               item.setAllFields(deviceId, "failure", restExceptionCode.getMessage(), restExceptionCode.getCode());
               return item;
            } else {
               if (isVwlGroup) {
                  this.deviceGroupDao.cancelVwlGroup(groupIdLong + "");
               }

               item.setDeviceId(deviceId);
               item.setStatus("success");
               return item;
            }
         }
      } else {
         restExceptionCode = RestExceptionCode.BAD_REQUEST_DEVICE_GROUP_TYPE_DIFFERENT;
         item.setAllFields(deviceId, "failure", restExceptionCode.getMessage(), restExceptionCode.getCode());
         return item;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Only Approval Authority')")
   public V2DeviceApproveResource approveDevices(V2DeviceApproval body) throws Exception {
      Locale locale = SecurityUtils.getLocale();
      String userId = SecurityUtils.getLoginUserId();
      V2DeviceApproveResource result = new V2DeviceApproveResource();
      List approvedList = new ArrayList();
      List unapprovedList = new ArrayList();
      Long groupId = Long.parseLong(body.getGroupId());
      List deviceIds = body.getDeviceIds();
      boolean sequenceFlag = 1 < deviceIds.size();
      if (!SecurityUtils.checkDeviceApprovalPermission()) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      String deviceName = body.getDeviceName();
      String location = body.getLocation();
      String slaveNumber = this.convertString(body.getSlaveNumber());
      String cabinetIP = this.convertString(body.getCabinetIP());
      String autoSetID = this.convertString(body.getAutoSetID());
      String deviceType = StrUtils.nvl(body.getDeviceType());
      DeviceGroupDao groups = new DeviceGroupDao();
      String organization = groups.getOrgNameByGroupId(groupId);
      String sessionId = UUID.randomUUID().toString().toUpperCase();
      String expiredDate = this.parseExpiredDate(body.getExpiredDate());
      DeviceGroupDao devGroupDao = new DeviceGroupDao();
      boolean isVwlGroup = devGroupDao.getBooleanVwlGroupId(groupId);
      User loginUser = SecurityUtils.getUserContainer().getUser();
      long loginUserOrgId = SecurityUtils.getLoginUserOrganizationId();
      String loginUserOrgName = SecurityUtils.getLoginUserOrganization();
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      boolean hasDevicePermission = serverSetupDao.checkPermissionsDeviceByOrgId(loginUserOrgId);
      DeviceGroupInfo deviceGroup = DeviceGroupInfoImpl.getInstance();
      List permissionDeviceGroupList = deviceGroup.getAllAuthorityDeviceGroups(loginUser.getUser_id());
      long approvalDeviceCount = 0L;
      Iterator var32 = deviceIds.iterator();

      while(var32.hasNext()) {
         String deviceId = (String)var32.next();
         V2DeviceApprovedItem item = new V2DeviceApprovedItem();

         try {
            if (!"ROOT".equals(loginUserOrgName)) {
               boolean isApprovalAuthorized = true;
               Long deviceUnapprovedGroupCode = this.deviceDao.getDeviceUnapprovedGroupCode(deviceId);
               if (deviceUnapprovedGroupCode == -1L) {
                  isApprovalAuthorized = false;
               }

               if (hasDevicePermission) {
                  if (!permissionDeviceGroupList.isEmpty() && !permissionDeviceGroupList.contains(groupId)) {
                     isApprovalAuthorized = false;
                  }
               } else {
                  String deviceOrgName = deviceGroup.getParentOrgNameByGroupId(deviceUnapprovedGroupCode);
                  if (!deviceOrgName.equalsIgnoreCase(loginUserOrgName)) {
                     isApprovalAuthorized = false;
                  }
               }

               if (!isApprovalAuthorized) {
                  item.setDeviceId(deviceId);
                  item.setStatus("failed");
                  item.setReason(RestExceptionCode.BAD_REQUEST_DEVICE_NOT_APPROVED_UNAUTHORIZED.getMessage());
                  item.setReasonCode(RestExceptionCode.BAD_REQUEST_DEVICE_NOT_APPROVED_UNAUTHORIZED.getCode());
                  unapprovedList.add(item);
                  continue;
               }
            }

            if (isVwlGroup) {
               item.setDeviceId(deviceId);
               item.setStatus("failed");
               item.setReason(RestExceptionCode.BAD_REQUEST_NOT_APPROVAL_IN_VWL_GROUP.getMessage());
               item.setReasonCode(RestExceptionCode.BAD_REQUEST_NOT_APPROVAL_IN_VWL_GROUP.getCode());
               unapprovedList.add(item);
            } else {
               String resultMsg;
               if (CommonConfig.get("e2e.enable") != null && CommonConfig.get("e2e.enable").equalsIgnoreCase("true")) {
                  HashMap resultMap;
                  if (CommonConfig.get("e2e.license.system") != null && !CommonConfig.get("e2e.license.system").toUpperCase().equals(ExternalSystemUtils.SYSTEM_PBP)) {
                     resultMap = DeviceUtils.approveDeviceForE2E_SLMDirect(deviceIds, groupId, deviceName, location, deviceId, locale, sessionId, userId, expiredDate, organization, "REST API v2.0", body.getSoldToCodeId(), body.getModelCd(), body.getSecorgId());
                     resultMsg = (String)resultMap.get("message");
                  } else {
                     resultMap = DeviceUtils.approveDeviceForE2E(deviceIds, groupId, deviceName, location, deviceId, locale, sessionId, userId, expiredDate, organization, "REST API v2.0", body.getAccountCode(), body.getBrandCode(), body.getModelCd());
                     resultMsg = (String)resultMap.get("message");
                  }
               } else {
                  resultMsg = RESTDeviceUtils.approveDevice(groupId, deviceName, location, deviceId, locale, sessionId, userId, expiredDate, organization, "REST API v2.0", sequenceFlag);
               }

               if ("device_approval_success".equals(resultMsg)) {
                  ++approvalDeviceCount;
                  this.setDeviceApprovalSuccessItem(item, deviceId, deviceType, groupId, slaveNumber, cabinetIP, autoSetID);
                  approvedList.add(item);
               } else if ("device_approval_max_connection_over".equals(resultMsg)) {
                  this.setDeviceApprovalMaxConnectionOver(item, deviceId);
                  unapprovedList.add(item);
               } else {
                  this.setDeviceApprovalInternalServerError(item, deviceId);
                  unapprovedList.add(item);
               }
            }
         } catch (Exception var38) {
            this.logger.error("", var38);
            item.setDeviceId(deviceId);
            item.setStatus("failed");
            item.setReason(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN.getMessage());
            item.setReasonCode(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN.getCode());
            unapprovedList.add(item);
         }
      }

      if (0L < approvalDeviceCount) {
         this.deviceGroupInfo.addGroupTotalCount(groupId, approvalDeviceCount);
         this.deviceGroupInfo.updateCacheDeviceGroup();
      }

      result.setDeviceIds(body.getDeviceIds());
      result.setApprovedList(approvedList);
      result.setUnapprovedList(unapprovedList);
      return result;
   }

   private String parseExpiredDate(String rawExpiredDate) {
      String dateFormat = SecurityUtils.getUserContainer().getUser().getDate_format();
      if (null == dateFormat) {
         dateFormat = "yyyy-MM-dd";
      }

      String expireDate = StrUtils.nvl(rawExpiredDate);
      if ("INFINITE".equalsIgnoreCase(expireDate)) {
         return "∞";
      } else {
         return !"".equals(expireDate) && !"yyyy-MM-dd".equals(dateFormat) ? StrUtils.convertDateFormat(expireDate, dateFormat, "yyyy-MM-dd") : expireDate;
      }
   }

   private void setDeviceApprovalSuccessItem(V2DeviceApprovedItem item, String deviceId, String deviceType, Long groupId, String slaveNumber, String cabinetIP, String autoSetID) throws Exception {
      DeviceUtils.refreshPriorityByDeviceType(groupId, deviceId);
      Device device = this.deviceInfo.getDevice(deviceId);
      if (!"SIGNAGE".equals(deviceType) && !"RSIGNAGE".equals(deviceType)) {
         if (!"LEDBOX".equals(deviceType) && !"RLEDBOX".equals(deviceType)) {
            this.setDeviceApprovalItemFromDevice(item, device, "success", (RestExceptionCode)null);
         } else {
            String[] cabinetIPArr = this.splitString(cabinetIP, ",");
            String[] autoSetIDArr = this.splitString(autoSetID, ",");
            if (null != slaveNumber && !"".equals(slaveNumber)) {
               if (0 <= slaveNumber.indexOf(44)) {
                  String[] slaveNumberArr = slaveNumber.split(",");
                  long[] slaveInfo = new long[slaveNumberArr.length];

                  for(int i = 0; i < slaveNumberArr.length; ++i) {
                     slaveInfo[i] = Long.parseLong(slaveNumberArr[i]);
                  }

                  DeviceUtils.scanChildDevice(deviceId, slaveInfo, cabinetIPArr, autoSetIDArr);
               } else {
                  long[] slaveInfo = new long[]{Long.parseLong(slaveNumber)};
                  DeviceUtils.scanChildDevice(deviceId, slaveInfo, cabinetIPArr, autoSetIDArr);
               }

               this.setDeviceApprovalItemFromDevice(item, device, "success", (RestExceptionCode)null);
            } else {
               this.setDeviceApprovalItemFromDevice(item, device, "success", (RestExceptionCode)null);
            }
         }
      } else {
         DeviceUtils.scanChildDevice(deviceId, Long.parseLong(slaveNumber));
         this.setDeviceApprovalItemFromDevice(item, device, "success", (RestExceptionCode)null);
      }
   }

   private String[] splitString(String value, String delimiter) {
      return null != value && !"".equals(value) ? value.split(delimiter) : null;
   }

   private void setDeviceApprovalMaxConnectionOver(V2DeviceApprovedItem item, String deviceId) throws Exception {
      Device device = this.deviceInfo.getDevice(deviceId);
      if (null != deviceId && !deviceId.isEmpty()) {
         this.setDeviceApprovalItemFromDevice(item, device, "failed", RestExceptionCode.BAD_REQUEST_LICENSE_QUANTITY_EXCEED);
      } else {
         this.setDeviceApprovalItemFromDevice(item, device, "failed", RestExceptionCode.INTERNAL_SERVER_ERROR_GROUP_DELETE_FAIL);
      }
   }

   private void setDeviceApprovalInternalServerError(V2DeviceApprovedItem item, String deviceId) throws Exception {
      Device device = this.deviceInfo.getDevice(deviceId);
      this.setDeviceApprovalItemFromDevice(item, device, "failed", RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN);
   }

   private void setDeviceApprovalItemFromDevice(V2DeviceApprovedItem item, Device device, String status, RestExceptionCode exceptionOnFailure) {
      item.setDeviceId(device.getDevice_id());
      item.setDeviceName(device.getDevice_name());
      item.setGroupId(device.getGroup_id());
      item.setDeviceModelName(device.getDevice_model_name());
      item.setDeviceType(device.getDevice_type());
      item.setStatus(status);
      if (null != exceptionOnFailure) {
         item.setReason(exceptionOnFailure.getMessage());
         item.setReasonCode(exceptionOnFailure.getCode());
      }

   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2ChildDeviceScanInfo scanChildDevices(String deviceId, int countOfChildDevice) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      long childCnt = (long)countOfChildDevice;
      this.deleteChildDevicesFromCache(deviceId);
      List childDeviceIdList = DeviceUtils.getChildDeviceIdList(deviceId, childCnt);

      try {
         this.deviceInfo.deleteChildDevice(deviceId);
      } catch (Exception var10) {
         this.logger.error("error while deleteChildDevice " + deviceId, var10);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      }

      try {
         Device parentDevice = this.deviceInfo.getDevice(deviceId);
         parentDevice.setChild_cnt(childCnt);
         this.deviceInfo.setDevice(parentDevice);
      } catch (Exception var9) {
         this.logger.error("error while setChild_cnt " + deviceId, var9);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      }

      String requestId = UUID.randomUUID().toString();

      try {
         DeviceUtils.addDummyChildDevice(deviceId, childDeviceIdList, "SIG_CHILD");
      } catch (Exception var8) {
         this.logger.error("error while adding dummy child" + deviceId, var8);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      }

      this.deviceConfManager.reqSetSignageCmd(deviceId, "SCAN_CHILD_INFO", String.valueOf(childCnt), requestId);
      V2ChildDeviceScanInfo info = new V2ChildDeviceScanInfo();
      info.setDeviceId(deviceId);
      info.setSignageCommand("SCAN_CHILD_INFO");
      info.setCountOfChildDevice(String.valueOf(childCnt));
      info.setRequestId(requestId);
      return info;
   }

   private void deleteChildDevicesFromCache(String deviceId) throws Exception {
      List childDeviceIds = this.deviceInfo.getChildDeviceIdList(deviceId);
      if (null != childDeviceIds) {
         Iterator var3 = childDeviceIds.iterator();

         while(var3.hasNext()) {
            String childDeviceId = (String)var3.next();
            this.monitoringManager.setDisconnected(childDeviceId);
            DBCacheUtils.deleteDeviceEntities(childDeviceId);
         }

      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public List replaceDevice(String currentDeviceId, String newDeviceId, V2DeviceReplacementInfo body) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, body.getNewDeviceGroupId(), currentDeviceId);
      Locale locale = SecurityUtils.getLocale();
      String userId = SecurityUtils.getLoginUserId();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      VwlLayoutManager vwlLayoutManager = VwlLayoutManagerImpl.getInstance();
      String newDeviceName = StrUtils.nvl(body.getNewDeviceName());
      String newDeviceLocation = StrUtils.nvl(body.getNewDeviceLocation());
      String newDeviceIpAddr = StrUtils.nvl(body.getNewDeviceIpAddr());
      Long newDeviceGroupId = body.getNewDeviceGroupId();
      String currentVwtId = this.deviceGroupInfo.getVwlLayoutIdByGroupId(newDeviceGroupId + "");
      List vwlLayoutList = vwlLayoutManager.getVwlLayoutsByGroupId(newDeviceGroupId.intValue());
      String organization = this.deviceGroupInfo.getOrgNameByGroupId(newDeviceGroupId);
      String sessionId = UUID.randomUUID().toString().toUpperCase();
      String newDeviceExpiredDate = StrUtils.nvl(body.getNewDeviceExpiredDate());
      if ("INFINITE".equalsIgnoreCase(newDeviceExpiredDate)) {
         newDeviceExpiredDate = "∞";
      }

      String approvalResult = DeviceUtils.approveAndreplace("", newDeviceGroupId, newDeviceName, newDeviceLocation, newDeviceId, locale, sessionId, userId, newDeviceExpiredDate, organization, newDeviceIpAddr);
      if ("device_approval_max_connection_over".equals(approvalResult)) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_DEVICE_APPROVAL_EXCEED);
      } else if (!"device_approval_success".equals(approvalResult)) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      } else {
         String vwtNewFileName;
         String vwtNewFilePath;
         String VWTid;
         try {
            VwlLayoutDao vwlLayoutDao = new VwlLayoutDao();
            VwlLayout vwlLayout = vwlLayoutDao.getVwlLayoutInfo(currentVwtId);
            VWTid = String.join(File.separator, Arrays.asList(CommonConfig.get("VWT_HOME"), currentVwtId, vwlLayout.getVwt_file_name()));
            String vwtTempPath = String.join(File.separator, Arrays.asList(CommonConfig.get("VWT_HOME"), "TEMP", ""));
            String vwtTempFileName = UUID.randomUUID().toString() + ".VWL";
            vwtNewFileName = vwtTempFileName;
            File currentFile = SecurityUtils.getSafeFile(VWTid);
            File tmpFile = SecurityUtils.getSafeFile(vwtTempPath + vwtTempFileName);
            if (!tmpFile.exists()) {
               tmpFile.createNewFile();
            }

            vwtNewFilePath = tmpFile.getPath();

            try {
               FileOutputStream fos = new FileOutputStream(tmpFile.getPath());
               Throwable var28 = null;

               try {
                  OutputStreamWriter osw = new OutputStreamWriter(fos, "UTF8");
                  Throwable var30 = null;

                  try {
                     BufferedWriter output = new BufferedWriter(osw);
                     Throwable var32 = null;

                     try {
                        FileInputStream fis = new FileInputStream(currentFile);
                        Throwable var34 = null;

                        try {
                           InputStreamReader isr = new InputStreamReader(fis, "UTF-8");
                           Throwable var36 = null;

                           try {
                              BufferedReader br = new BufferedReader(isr);
                              Throwable var38 = null;

                              try {
                                 List lines = (List)br.lines().collect(Collectors.toList());
                                 Iterator var40 = lines.iterator();

                                 while(var40.hasNext()) {
                                    String str = (String)var40.next();
                                    output.write(str.replace(currentDeviceId, newDeviceId) + "\n");
                                 }
                              } catch (Throwable var222) {
                                 var38 = var222;
                                 throw var222;
                              } finally {
                                 if (br != null) {
                                    if (var38 != null) {
                                       try {
                                          br.close();
                                       } catch (Throwable var219) {
                                          var38.addSuppressed(var219);
                                       }
                                    } else {
                                       br.close();
                                    }
                                 }

                              }
                           } catch (Throwable var224) {
                              var36 = var224;
                              throw var224;
                           } finally {
                              if (isr != null) {
                                 if (var36 != null) {
                                    try {
                                       isr.close();
                                    } catch (Throwable var218) {
                                       var36.addSuppressed(var218);
                                    }
                                 } else {
                                    isr.close();
                                 }
                              }

                           }
                        } catch (Throwable var226) {
                           var34 = var226;
                           throw var226;
                        } finally {
                           if (fis != null) {
                              if (var34 != null) {
                                 try {
                                    fis.close();
                                 } catch (Throwable var217) {
                                    var34.addSuppressed(var217);
                                 }
                              } else {
                                 fis.close();
                              }
                           }

                        }
                     } catch (Throwable var228) {
                        var32 = var228;
                        throw var228;
                     } finally {
                        if (output != null) {
                           if (var32 != null) {
                              try {
                                 output.close();
                              } catch (Throwable var216) {
                                 var32.addSuppressed(var216);
                              }
                           } else {
                              output.close();
                           }
                        }

                     }
                  } catch (Throwable var230) {
                     var30 = var230;
                     throw var230;
                  } finally {
                     if (osw != null) {
                        if (var30 != null) {
                           try {
                              osw.close();
                           } catch (Throwable var215) {
                              var30.addSuppressed(var215);
                           }
                        } else {
                           osw.close();
                        }
                     }

                  }
               } catch (Throwable var232) {
                  var28 = var232;
                  throw var232;
               } finally {
                  if (fos != null) {
                     if (var28 != null) {
                        try {
                           fos.close();
                        } catch (Throwable var214) {
                           var28.addSuppressed(var214);
                        }
                     } else {
                        fos.close();
                     }
                  }

               }
            } catch (Exception var234) {
               this.logger.error("", var234);
            }
         } catch (Exception var235) {
            this.logger.error("", var235);
            if ("device_approval_success".equals(approvalResult)) {
               this.deviceInfo.deleteDevice(newDeviceId);
            }

            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_VWL_FILE_CREATE);
         }

         try {
            if (this.deviceInfo.deleteDevice(currentDeviceId)) {
               this.monitoringManager.connectionReload(currentDeviceId, 0);
               this.monitoringManager.scheduleReload(currentDeviceId, 0);
               this.monitoringManager.deleteConnectionInfo(currentDeviceId);
               WSCall.setPlayerRequest(currentDeviceId, "agent restart");
            }
         } catch (Exception var221) {
            this.logger.error("", var221);
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
         }

         VwtXmlWriterInfo domXmlWriter = new DOMVwtWriterImpl();
         new VwtXmlWriter(domXmlWriter);
         List deviceList = this.deviceInfo.getDeviceListByGroupId(newDeviceGroupId);
         VWTid = domXmlWriter.writeVWTFile(vwtNewFilePath, vwtNewFileName);
         VwtXmlParserInfo domVwtXmlParser = new DOMVwtParserImpl();
         VwtXmlParser vwtXmlParser = new VwtXmlParser(domVwtXmlParser);
         vwtXmlParser.setVwtMonitorInfo(VWTid, vwtNewFileName);
         Iterator var240 = deviceList.iterator();

         while(var240.hasNext()) {
            Device device = (Device)var240.next();

            try {
               vwlLayoutManager.deployVwlLayout(device.getMac_address(), "FINISH_LAYOUT", VWTid, vwtNewFilePath);
            } catch (Exception var213) {
               this.logger.error("", var213);
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_VWL_FILE_CREATE);
            }
         }

         vwlLayoutServlet.cleanTempLayoutFile();

         try {
            String[] vwlIds = new String[vwlLayoutList.size()];
            int i = 0;

            while(true) {
               if (i >= vwlLayoutList.size()) {
                  vwlLayoutManager.deleteVwlLayoutInfoWithFile(vwlIds);
                  break;
               }

               vwlIds[i] = ((VwlLayout)vwlLayoutList.get(i)).getVwt_id();
               ++i;
            }
         } catch (Exception var220) {
            this.logger.error("", var220);
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
         }

         this.deviceGroupInfo.updateCacheDeviceGroup();
         List list = vwlLayoutManager.getVwlLayoutsByGroupId(newDeviceGroupId.intValue());
         List resources = new ArrayList();
         Iterator var246 = list.iterator();

         while(var246.hasNext()) {
            VwlLayout item = (VwlLayout)var246.next();
            V2VwlLayoutResource resource = new V2VwlLayoutResource();
            resource.setVwtId(item.getVwt_id());
            resource.setVwtFileName(item.getFileName());
            resource.setCreatorId(item.getCreator_id());
            if (null != item.getCreate_date()) {
               resource.setCreateDate(item.getCreate_date());
            }

            resource.setLinear(item.getIs_linear());
            resource.setModelCountInfo(item.getModel_count_info());
            resource.setModelCountMap(item.getModelCountMap());
            resource.setxCount(item.getX_count());
            resource.setyCount(item.getY_count());
            resource.setxRange(item.getX_range());
            resource.setyRange(item.getY_range());
            resource.setDeviceModelName(item.getDevice_model_name());
            resource.setDeviceNumber(item.getDevice_number());
            resource.setUseCount(item.getUse_count());
            resource.setGroupId(item.getGroup_id());
            resource.setDeviceId(item.getDevice_id());
            resource.setPosition(item.getPosition());
            resource.setWidth(item.getWidth());
            resource.setHeight(item.getHeight());
            resource.setAngle(item.getAngle());
            resource.setThumbFileName(item.getThumb_file_name());
            resource.setMapId(item.getMap_id());
            resource.setContentId(item.getContent_id());
            resource.setPlayerType(item.getPlayer_type());
            resource.setFileName(item.getFileName());
            resource.setFileId(item.getFileId());
            resources.add(resource);
         }

         return resources;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public List getLicenseUsage() throws Exception {
      SlmLicenseManager licenseDao = SlmLicenseManagerImpl.getInstance();
      Set productCodeList = licenseDao.getSupportProductCode();
      boolean integrateLicense = false;
      int cntSlmLicIntegrate = 0;
      int cntSlmLicLite = 0;
      int cntSlmLicMoblie = false;
      int cntSlmLicAndroid = 0;
      int cntSlmLicRMS = 0;
      boolean hasMigrationLicense = licenseDao.hasMigrationLicense() >= 0L;
      Iterator var10 = productCodeList.iterator();

      while(var10.hasNext()) {
         String productCode = (String)var10.next();
         byte var13 = -1;
         switch(productCode.hashCode()) {
         case **********:
            if (productCode.equals("01011N")) {
               var13 = 6;
            }
            break;
         case **********:
            if (productCode.equals("010120")) {
               var13 = 2;
            }
            break;
         case **********:
            if (productCode.equals("010121")) {
               var13 = 3;
            }
            break;
         case **********:
            if (productCode.equals("01014A")) {
               var13 = 0;
            }
            break;
         case **********:
            if (productCode.equals("01015A")) {
               var13 = 1;
            }
            break;
         case **********:
            if (productCode.equals("010311")) {
               var13 = 5;
            }
            break;
         case **********:
            if (productCode.equals("01064A")) {
               var13 = 7;
            }
            break;
         case **********:
            if (productCode.equals("010V31")) {
               var13 = 4;
            }
         }

         switch(var13) {
         case 0:
         case 1:
         case 2:
         case 3:
         case 4:
            if (!integrateLicense) {
               cntSlmLicIntegrate = licenseDao.getLicenseCountByProductCode(productCode);
               integrateLicense = true;
            }
            break;
         case 5:
            cntSlmLicLite = licenseDao.getLicenseCountByProductCode(productCode);
            break;
         case 6:
            cntSlmLicAndroid = licenseDao.getLicenseCountByProductCode(productCode);
            break;
         case 7:
            cntSlmLicRMS = licenseDao.getLicenseCountByProductCode(productCode);
         }
      }

      List resources = new ArrayList();
      long licenseCount = (long)licenseDao.getUsedLicenseCountByProductCode("01015A");
      V2LicenseUsageResource resource = new V2LicenseUsageResource();
      resource.setLicenseType("Unified Player");
      resource.setUsedLicenseCount(licenseCount);
      resource.setTotalLicenseCount((long)cntSlmLicIntegrate);
      resources.add(resource);
      if (cntSlmLicLite > 0) {
         licenseCount = (long)licenseDao.getUsedLicenseCountByProductCode("010311");
         resource = new V2LicenseUsageResource();
         resource.setLicenseType("Lite Player");
         resource.setUsedLicenseCount(licenseCount);
         resource.setTotalLicenseCount((long)cntSlmLicLite);
         resources.add(resource);
      }

      if (cntSlmLicAndroid > 0) {
         licenseCount = (long)licenseDao.getUsedLicenseCountByProductCode("01011N");
         resource = new V2LicenseUsageResource();
         resource.setLicenseType("Android Player");
         resource.setUsedLicenseCount(licenseCount);
         resource.setTotalLicenseCount((long)cntSlmLicAndroid);
         resources.add(resource);
      }

      if (cntSlmLicRMS > 0) {
         licenseCount = (long)licenseDao.getUsedLicenseCountByProductCode("01064A");
         resource = new V2LicenseUsageResource();
         resource.setLicenseType("RM Player");
         resource.setUsedLicenseCount(licenseCount);
         resource.setTotalLicenseCount((long)cntSlmLicRMS);
         resources.add(resource);
      }

      if (hasMigrationLicense) {
         V2LicenseUsageResource resource = new V2LicenseUsageResource();
         resource.setLicenseType("Migration License");
         resource.setUsedLicenseCount(1L);
         resource.setTotalLicenseCount(1L);
         resources.add(resource);
      }

      return resources;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ModelAndView exportUnapprovedDevice(String exportType, String searchText, String sortColumn, String sortOrder, HttpServletResponse response, String localeData) {
      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = "create_date";
      }

      if (StrUtils.nvl(sortOrder).equals("")) {
         sortOrder = "desc";
      }

      if (StrUtils.nvl(searchText).equals("")) {
         searchText = "";
      }

      if (StrUtils.nvl(exportType).equals("")) {
         exportType = "EXCEL";
      }

      SelectCondition condition = new SelectCondition();
      condition.setSort_name(sortColumn);
      condition.setOrder_dir(sortOrder);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      condition.setSrc_name(searchText.toUpperCase());
      String fileExtension = exportType.toUpperCase().equals("PDF") ? "pdf" : "xls";
      Map dataMap = new HashMap();
      if (StrUtils.nvl(localeData).equals("")) {
         String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
         if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
            localeData = userLocale;
         } else {
            localeData = "en";
         }
      }

      Locale locale = new Locale(localeData);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      String deviceName = rms.getMessage("TABLE_DEVICE_NAME_P", (Object[])null, locale);
      String deviceId = rms.getMessage("TABLE_MAC_ADDR_P", (Object[])null, locale);
      String ipAddress = rms.getMessage("TABLE_IP_ADDR_P", (Object[])null, locale);
      String deviceModelName = rms.getMessage("TABLE_DEVICE_MODEL_NAME_P", (Object[])null, locale);
      String deviceSerial = rms.getMessage("TABLE_DEVICE_SERIAL_P", (Object[])null, locale);
      String registerDate = rms.getMessage("ADMIN_DEVICEEVENT_ALARMRULE_ASSIGNRULE_CONTENTS_REGISTERED", (Object[])null, locale);
      Timestamp nowTime = new Timestamp(System.currentTimeMillis());
      String fileName = "UnapprovedDeviceList_" + nowTime.toString() + "." + fileExtension;
      String sheetName = "UnapprovedDeviceList";
      String[] columnNames = new String[]{"device_name", "device_id", "ip_address", "device_model_name", "serial_decimal", "create_date"};
      String[] fieldNames = new String[]{deviceName, deviceId, ipAddress, deviceModelName, deviceSerial, registerDate};
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();

      try {
         ListManager listMgr = new ListManager(deviceDao, "list");
         listMgr.addSearchInfo("sort", condition.getSort_name());
         listMgr.addSearchInfo("dir", condition.getOrder_dir());
         listMgr.addSearchInfo("src_name", condition.getSrc_name());
         listMgr.setSection("getNonApprovedDevice");
         List deviceList = listMgr.V2dbexecute(1, 1000000);
         int dataListSize = false;
         Object[] dataList = null;
         if (deviceList != null) {
            int dataListSize = deviceList.size();
            dataList = new Object[dataListSize];

            for(int index = 0; index < dataListSize; ++index) {
               Map hash = (Map)deviceList.get(index);
               Device device = new Device();
               device.setDevice_id((String)hash.get("device_id"));
               device.setDevice_name((String)hash.get("device_name"));
               device.setIp_address((String)hash.get("ip_address"));
               device.setDevice_model_name((String)hash.get("device_model_name"));
               device.setSerial_decimal((String)hash.get("serial_decimal"));
               device.setCreate_date((Timestamp)hash.get("create_date"));
               dataList[index] = device;
            }
         }

         dataMap.put("fileName", fileName);
         dataMap.put("sheetName", sheetName);
         dataMap.put("columnNames", columnNames);
         dataMap.put("fieldNames", fieldNames);
         dataMap.put("dataList", dataList);
      } catch (Exception var31) {
         this.logger.error(var31);
      }

      if (exportType.equalsIgnoreCase("PDF")) {
         PdfBuilder pdfView = new PdfBuilder();
         return new ModelAndView(pdfView, dataMap);
      } else {
         this.downloadService = new DeviceStatisticsDownloadService();
         this.downloadService.downloadExcelFile(dataMap, response);
         return null;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ModelAndView exportUnapprovedGroupDevice(HttpServletResponse response, String localeData) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      Long orgGroupId = userContainer.getUser().getGroup_id();
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, orgGroupId);
      SelectCondition condition = new SelectCondition();
      condition.setGroup_id(orgGroupId);
      Map dataMap = new HashMap();
      if (StrUtils.nvl(localeData).equals("")) {
         String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
         if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
            localeData = userLocale;
         } else {
            localeData = "en";
         }
      }

      Locale locale = new Locale(localeData);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      String groupId = "Code";
      String organization = rms.getMessage("TABLE_ORGANIZATION_P", (Object[])null, locale);
      String groupName = rms.getMessage("COM_TABLE_GROUP_NAME_P", (Object[])null, locale);
      Timestamp nowTime = new Timestamp(System.currentTimeMillis());
      String fileName = "DeviceGroupList_" + nowTime.toString() + ".xls";
      String sheetName = "DeviceGroupList";
      String[] columnNames = new String[]{"description", "group_name", "group_id"};
      String[] fieldNames = new String[]{organization, groupName, groupId};
      DeviceGroupInfo groupInfo = DeviceGroupInfoImpl.getInstance();
      List groupList = null;

      try {
         User currentUser = SecurityUtils.getUserContainer().getUser();
         int dataListSize;
         if (RoleUtils.isServerAdminRole(currentUser) || !currentUser.isMu() && currentUser.getGroup_id() == 0L) {
            groupList = groupInfo.getAllDeviceGroups(0L);
         } else {
            String testOrg = currentUser.getOrganization();

            try {
               dataListSize = groupInfo.getDeviceGroupForUser(testOrg);
               groupList = groupInfo.getAllDeviceGroups((long)dataListSize);
            } catch (Exception var26) {
               this.logger.error(var26);
            }
         }

         DeviceGroup ownOrg = null;
         if (condition.getGroup_id() > 0L) {
            ownOrg = this.getOrganization(groupInfo.getGroup(condition.getGroup_id().intValue()), groupList);
         }

         int dataListSize = false;
         Object[] dataList = null;
         int index = 0;
         if (groupList != null) {
            dataListSize = groupList.size();
            dataList = new Object[dataListSize];

            for(int i = 0; i < dataListSize; ++i) {
               DeviceGroup group = (DeviceGroup)groupList.get(i);
               if (group.getP_group_id() > 0L && (ownOrg == null || ownOrg.getGroup_id() == group.getP_group_id())) {
                  group.setDescription(this.getOrganization(group, groupList).getGroup_name());
                  dataList[index++] = group;
               }
            }
         }

         dataMap.put("fileName", fileName);
         dataMap.put("sheetName", sheetName);
         dataMap.put("columnNames", columnNames);
         dataMap.put("fieldNames", fieldNames);
         dataMap.put("dataList", dataList);
      } catch (SQLException var27) {
         this.logger.error(var27);
      }

      this.downloadService = new DeviceStatisticsDownloadService();
      this.downloadService.downloadExcelFile(dataMap, response);
      return null;
   }

   private DeviceGroup getOrganization(DeviceGroup currGroup, List groupList) {
      DeviceGroup pGroup = null;
      if (currGroup.getP_group_id() == 0L) {
         return currGroup;
      } else {
         Iterator var4 = groupList.iterator();

         while(var4.hasNext()) {
            DeviceGroup group = (DeviceGroup)var4.next();
            if (group.getGroup_id().equals(currGroup.getP_group_id())) {
               pGroup = group;
               break;
            }
         }

         if (pGroup == null) {
            return currGroup;
         } else {
            return pGroup.getP_group_id() == 0L ? pGroup : this.getOrganization(pGroup, groupList);
         }
      }
   }

   public String convertString(List list) {
      if (null != list && !list.isEmpty()) {
         StringBuffer strBuf = new StringBuffer();

         for(int i = 0; i < list.size(); ++i) {
            strBuf.append(list.get(i));
            if (i < list.size() - 1) {
               strBuf.append(",");
            }
         }

         return strBuf.toString();
      } else {
         return "";
      }
   }
}
