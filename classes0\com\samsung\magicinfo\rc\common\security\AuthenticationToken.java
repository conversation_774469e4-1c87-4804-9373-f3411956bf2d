package com.samsung.magicinfo.rc.common.security;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;

public class AuthenticationToken extends UsernamePasswordAuthenticationToken {
  String jwt;
  
  public AuthenticationToken(Object principal, Object credentials, String jwt) {
    super(principal, credentials);
    this.jwt = jwt;
  }
  
  public String getJwt() {
    return this.jwt;
  }
  
  public void setJwt(String jwt) {
    this.jwt = jwt;
  }
}
