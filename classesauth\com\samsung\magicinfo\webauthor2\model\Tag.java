package com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.repository.model.tag.TagData;

public class Tag {
  private int id;
  
  private String name;
  
  private String value;
  
  private String description;
  
  private Long organizationId;
  
  private int type;
  
  public Tag(int id, String name, String value, String description, Long organizationId, int type) {
    this.id = id;
    this.name = name;
    this.value = value;
    this.description = description;
    this.organizationId = organizationId;
    this.type = type;
  }
  
  public static com.samsung.magicinfo.webauthor2.model.Tag fromData(TagData tagData) {
    com.samsung.magicinfo.webauthor2.model.Tag tag = new com.samsung.magicinfo.webauthor2.model.Tag(tagData.getId(), tagData.getName(), tagData.getValue(), tagData.getDescription(), tagData.getOrganizationId(), tagData.getType());
    return tag;
  }
  
  public String toString() {
    return "Tag{id=" + this.id + ", name=" + this.name + ", value=" + this.value + ", description=" + this.description + ", organizationId=" + this.organizationId + ", type=" + this.type + '}';
  }
  
  public int getId() {
    return this.id;
  }
  
  public void setId(int id) {
    this.id = id;
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public String getValue() {
    return this.value;
  }
  
  public void setValue(String value) {
    this.value = value;
  }
  
  public String getDescription() {
    return this.description;
  }
  
  public void setDescription(String description) {
    this.description = description;
  }
  
  public Long getOrganizationId() {
    return this.organizationId;
  }
  
  public void setOrganizationId(Long organizationId) {
    this.organizationId = organizationId;
  }
  
  public int getType() {
    return this.type;
  }
  
  public void setType(int type) {
    this.type = type;
  }
}
