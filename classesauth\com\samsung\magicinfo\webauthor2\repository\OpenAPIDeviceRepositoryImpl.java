package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.GetDeviceGroupInfoOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.GetDeviceGroupListOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.GetDeviceListOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.GetDeviceListWithDeviceTypeOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.GetVideowallLayoutUrlOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.GetWPlayerDeviceListOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.OpenAPIDeviceRepository;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceData;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupData;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupLayout;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.util.List;
import javax.inject.Inject;
import org.springframework.stereotype.Repository;
import org.springframework.web.client.RestTemplate;

@Repository
public class OpenAPIDeviceRepositoryImpl implements OpenAPIDeviceRepository {
  private final RestTemplate restTemplate;
  
  private final UserData userData;
  
  @Inject
  public OpenAPIDeviceRepositoryImpl(RestTemplate restTemplate, UserData userData) {
    this.restTemplate = restTemplate;
    this.userData = userData;
  }
  
  public List<DeviceGroupData> getDeviceGroupList() {
    GetDeviceGroupListOpenApiMethod openApiMethod = new GetDeviceGroupListOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken());
    return (List<DeviceGroupData>)openApiMethod.callMethod();
  }
  
  public List<DeviceGroupData> getDeviceGroupInfo(String parentGroupId) {
    GetDeviceGroupInfoOpenApiMethod openApiMethod = new GetDeviceGroupInfoOpenApiMethod(this.restTemplate, this.userData.getToken(), parentGroupId);
    return (List<DeviceGroupData>)openApiMethod.callMethod();
  }
  
  public List<DeviceData> getDeviceList(String groupId) {
    GetDeviceListOpenApiMethod openApiMethod = new GetDeviceListOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken(), groupId);
    return (List<DeviceData>)openApiMethod.callMethod();
  }
  
  public List<DeviceData> getDeviceListWithDeviceType(String groupId) {
    GetDeviceListWithDeviceTypeOpenApiMethod openApiMethod = new GetDeviceListWithDeviceTypeOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken(), groupId);
    return (List<DeviceData>)openApiMethod.callMethod();
  }
  
  public DeviceGroupLayout getVideowallLayoutPath(String groupId) {
    GetVideowallLayoutUrlOpenApiMethod openApiMethod = new GetVideowallLayoutUrlOpenApiMethod(this.restTemplate, this.userData.getToken(), groupId);
    return (DeviceGroupLayout)openApiMethod.callMethod();
  }
  
  public List<DeviceData> getWPlayerDeviceList() {
    GetWPlayerDeviceListOpenApiMethod openApiMethod = new GetWPlayerDeviceListOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken());
    return (List<DeviceData>)openApiMethod.callMethod();
  }
}
