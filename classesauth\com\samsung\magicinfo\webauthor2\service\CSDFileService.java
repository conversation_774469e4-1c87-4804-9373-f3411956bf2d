package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.ContentSaveElements;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.xml.transferfile.response.TransferFilesResponseType;
import java.util.List;

public interface CSDFileService {
  String generateCSD(ContentSaveElements paramContentSaveElements, String paramString);
  
  String generateCSD(List<MediaSource> paramList, String paramString, DeviceType paramDeviceType);
  
  List<MediaSource> updateSaveElements(String paramString1, ContentSaveElements paramContentSaveElements, String paramString2);
  
  List<MediaSource> updateUserDataSaveElements(TransferFilesResponseType paramTransferFilesResponseType, ContentSaveElements paramContentSaveElements);
  
  TransferFilesResponseType postProjectCsdToMips(String paramString1, String paramString2, String paramString3);
  
  TransferFilesResponseType postSingleFileCsdToMips(String paramString1, String paramString2);
  
  List<MediaSource> updateMediaSources(List<MediaSource> paramList, TransferFilesResponseType paramTransferFilesResponseType);
}
