package com.samsung.magicinfo.config;

import com.samsung.magicinfo.config.AppConfig;
import java.net.Socket;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.X509ExtendedTrustManager;

class null extends X509ExtendedTrustManager {
  public void checkClientTrusted(X509Certificate[] xcs, String string, Socket socket) throws CertificateException {}
  
  public void checkServerTrusted(X509Certificate[] xcs, String string, Socket socket) throws CertificateException {}
  
  public void checkClientTrusted(X509Certificate[] xcs, String string, SSLEngine ssle) throws CertificateException {}
  
  public void checkServerTrusted(X509Certificate[] xcs, String string, SSLEngine ssle) throws CertificateException {}
  
  public void checkClientTrusted(X509Certificate[] xcs, String string) throws CertificateException {}
  
  public void checkServerTrusted(X509Certificate[] xcs, String string) throws CertificateException {}
  
  public X509Certificate[] getAcceptedIssuers() {
    return new X509Certificate[0];
  }
}
