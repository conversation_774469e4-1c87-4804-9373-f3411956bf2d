package com.samsung.magicinfo.restapi.device.service;

import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceDao;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceGroupDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManagerImpl;
import com.samsung.magicinfo.framework.device.job.manager.JobManager;
import com.samsung.magicinfo.framework.device.job.manager.JobManagerImpl;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManager;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManagerImpl;
import com.samsung.magicinfo.framework.device.preconfig.entity.DevicePreconfig;
import com.samsung.magicinfo.framework.device.preconfig.manager.DevicePreconfigInfo;
import com.samsung.magicinfo.framework.device.preconfig.manager.DevicePreconfigInfoImpl;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManager;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManagerImpl;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfo;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfoImpl;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAO;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.device.model.V2DeviceFilter;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSettingListResource;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.rms.model.DeviceSystemSetupConfResource;
import com.samsung.magicinfo.rms.model.GeneralInfoResource;
import com.samsung.magicinfo.rms.util.DeviceModelConverter;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2DeviceSettingService")
@Transactional
public class V2DeviceSettingServiceImpl implements V2DeviceSettingService {
   protected Logger logger = LoggingManagerV2.getLogger(V2DeviceSettingServiceImpl.class);
   private Boolean rsm_enabled = false;
   private static final int BUF_SIZE = 1048576;
   private static final int EOF = -1;
   DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
   DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
   DeviceGroupDao devGroupDao = new DeviceGroupDao();
   DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
   DeviceDao dao = new DeviceDao((SqlSession)null);
   AlarmManager alarmManager = AlarmManagerImpl.getInstance();
   DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
   JobManager jobMgr = JobManagerImpl.getInstance();
   MonitoringManagerInfo deviceMonitoringDao = MonitoringManagerInfoImpl.getInstance("PREMIUM");
   DeviceTimeConfManager deviceConf = DeviceTimeConfManagerImpl.getInstance("PREMIUM");
   DeviceSystemSetupConfManager deviceSetupConf = DeviceSystemSetupConfManagerImpl.getInstance("PREMIUM");
   MessageInfo msgInfo = MessageInfoImpl.getInstance();
   ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
   ContentInfo contentDao = ContentInfoImpl.getInstance();
   ScheduleInfoDAO scheduleDao = new ScheduleInfoDAO();
   final String ALL_MDC = "ALL_MDC";
   final String MODEL_KIND_NEW = "NEW";
   final String REQUEST_ID = "requestId";
   String productType = "PREMIUM";

   public V2DeviceSettingServiceImpl() {
      super();
   }

   private static boolean isValidSystemRestartInterval(String str) {
      String systemRestartTime;
      String[] temp = str.split(" ");
      systemRestartTime = "";
      label27:
      switch(temp.length) {
      case 1:
         systemRestartTime = temp[0];
         break;
      case 2:
         systemRestartTime = temp[1];
         String[] systemRestartDays = temp[0].split(";");
         List validDays = Arrays.asList("sun", "mon", "tue", "wed", "thu", "fri", "sat");
         String[] var5 = systemRestartDays;
         int var6 = systemRestartDays.length;
         int var7 = 0;

         while(true) {
            if (var7 >= var6) {
               break label27;
            }

            String systemRestartDay = var5[var7];
            if (!systemRestartDay.isEmpty() && !validDays.contains(systemRestartDay)) {
               return false;
            }

            ++var7;
         }
      default:
         return false;
      }

      if (systemRestartTime.isEmpty()) {
         return false;
      } else {
         Pattern timeInHHMM = Pattern.compile("([01]?\\d|2[0-3]):[0-5]\\d");
         Matcher m = timeInHHMM.matcher(systemRestartTime);
         return m.matches();
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2DeviceReqServiceResource updateSetupInfo(DeviceSystemSetupConfResource param) throws Exception {
      boolean checkFlag = false;
      List deviceIds = param.getDeviceIds();
      Iterator var4 = deviceIds.iterator();

      while(var4.hasNext()) {
         String deviceId = (String)var4.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var38) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      Iterator var40 = deviceIds.iterator();

      String productType;
      while(var40.hasNext()) {
         productType = (String)var40.next();
         if (!DeviceUtils.isConnected(productType)) {
            resource.setFailList(deviceIds);
            this.logger.error("The device is not connected.");
            return resource;
         }
      }

      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      productType = "iPLAYER";
      String requestId = UUID.randomUUID().toString();
      List successList = new ArrayList();
      List failList = new ArrayList();
      DeviceSystemSetupConf obj = DeviceModelConverter.newConvertSystemSetupConfToSnakeStyle(param);
      Iterator var12 = deviceIds.iterator();

      while(var12.hasNext()) {
         String deviceId = (String)var12.next();

         try {
            DeviceSystemSetupConf deviceSystemSetupConf = (DeviceSystemSetupConf)obj.clone();
            deviceSystemSetupConf.setDevice_id(deviceId);
            if (deviceSystemSetupConf.getProxy_setting() != null) {
               DeviceSystemSetupConfManager systemSetupDao = DeviceSystemSetupConfManagerImpl.getInstance();
               DeviceSystemSetupConf original = systemSetupDao.getDeviceSystemSetupConf(deviceId);
               if (original.getProxy_setting_authorization() != null) {
                  String[] temp = DeviceUtils.splitter(deviceSystemSetupConf.getProxy_setting(), 5);

                  try {
                     deviceSystemSetupConf.setProxy_setting_authorization(temp[3] + ":" + temp[4]);
                     deviceSystemSetupConf.setProxy_setting(temp[0] + ";" + temp[1] + ";" + temp[2] + ";;");
                  } catch (Exception var30) {
                     this.logger.error("proxy_setting_authorization Failed", var30);
                  }
               }
            }

            if (deviceSystemSetupConf.getSystem_restart_interval() != null && !isValidSystemRestartInterval(deviceSystemSetupConf.getSystem_restart_interval())) {
               deviceSystemSetupConf.setSystem_restart_interval((String)null);
               this.logger.error("Invalid system_restart_interval");
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_INVALID_SYSTEM_RESTART_INTERVAL);
            }

            this.confManager.reqSetSystemSetupToDevice(deviceSystemSetupConf, requestId, productType);
            if (obj.getStatisticsFileRefresh() != null && !obj.getStatisticsFileRefresh().isEmpty()) {
               label252: {
                  boolean var29 = false;

                  boolean requestTimeExist;
                  label216: {
                     try {
                        var29 = true;
                        confMgr.reqGetRequestStatistics(deviceId, "");
                        var29 = false;
                        break label216;
                     } catch (Exception var34) {
                        this.logger.error("Error while sending request for refresh of statistic file", var34);
                        var29 = false;
                     } finally {
                        if (var29) {
                           try {
                              boolean requestTimeExist = this.deviceDao.isRequestTimeExist(deviceId);
                              if (!requestTimeExist) {
                                 this.deviceDao.addStatRequestTimeInsertCurrent(deviceId);
                              } else {
                                 this.deviceMonitoringDao.setStatRequestTimeByDeviceId(deviceId);
                              }
                           } catch (Exception var31) {
                              this.logger.error("Error while updating requested time of statistic file", var31);
                              failList.add(deviceId);
                              continue;
                           }

                        }
                     }

                     try {
                        requestTimeExist = this.deviceDao.isRequestTimeExist(deviceId);
                        if (!requestTimeExist) {
                           this.deviceDao.addStatRequestTimeInsertCurrent(deviceId);
                        } else {
                           this.deviceMonitoringDao.setStatRequestTimeByDeviceId(deviceId);
                        }
                        break label252;
                     } catch (Exception var33) {
                        this.logger.error("Error while updating requested time of statistic file", var33);
                        failList.add(deviceId);
                        continue;
                     }
                  }

                  try {
                     requestTimeExist = this.deviceDao.isRequestTimeExist(deviceId);
                     if (!requestTimeExist) {
                        this.deviceDao.addStatRequestTimeInsertCurrent(deviceId);
                     } else {
                        this.deviceMonitoringDao.setStatRequestTimeByDeviceId(deviceId);
                     }
                  } catch (Exception var32) {
                     this.logger.error("Error while updating requested time of statistic file", var32);
                     failList.add(deviceId);
                     continue;
                  }
               }
            }
         } catch (CloneNotSupportedException var36) {
            this.logger.error("[REST_v2.0][DEVICE SETTING SERVICE][updateSetupInfo]" + ExceptionCode.RES909[2], var36);
            failList.add(deviceId);
            continue;
         } catch (Exception var37) {
            this.logger.error("[REST_v2.0][DEVICE SETTING SERVICE][updateSetupInfo]", var37);
            failList.add(deviceId);
            continue;
         }

         successList.add(deviceId);
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      if (successList.size() > 0) {
         resource.setRequestId(requestId);
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2CommonBulkResultResource getSetupInfo(V2CommonIds deviceIds) throws Exception {
      boolean checkFlag = false;
      Iterator var3 = deviceIds.getIds().iterator();

      while(var3.hasNext()) {
         String deviceId = (String)var3.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var19) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var6 = deviceIds.getIds().iterator();

      while(var6.hasNext()) {
         String deviceId = (String)var6.next();

         try {
            DeviceSystemSetupConfResource result = this.getDeviceSetupInfoFromDB(deviceId);
            Device device = this.deviceDao.getMonitoringViewDevice(deviceId);
            if (StringUtils.isNotEmpty(device.getPre_config_version())) {
               String[] temp = device.getPre_config_version().split(";");
               String preconfigVersion = temp[0];
               if (preconfigVersion != null && preconfigVersion.length() > 0 && !"-".equals(preconfigVersion)) {
                  DevicePreconfigInfo preconfigInfo = DevicePreconfigInfoImpl.getInstance();
                  DevicePreconfig preconfig = preconfigInfo.getPreconfigByDeviceId(deviceId);
                  Integer preconfigSuccess = 0;
                  Integer preconfigFailed = 0;

                  try {
                     preconfigSuccess = Integer.valueOf(temp[1]);
                     preconfigFailed = Integer.valueOf(temp[2]);
                  } catch (Exception var17) {
                     preconfigSuccess = 0;
                     preconfigFailed = 0;
                  }

                  if (preconfig == null) {
                     result.setPreconfigVersion("-");
                  } else {
                     result.setPreconfigVersion(preconfigVersion);
                  }

                  result.setPreconfig(preconfig);
                  result.setPreconfigSuccess(preconfigSuccess);
                  result.setPreconfigFailed(preconfigFailed);
               } else {
                  result.setPreconfigVersion("-");
               }
            }

            successList.add(result);
         } catch (Exception var18) {
            this.logger.error("[REST_v2.0][DEVICE SETTING SERVICE][getSetupInfo]", var18);
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   public DeviceSystemSetupConfResource getDeviceSetupInfoFromDB(String deviceId) throws SQLException {
      DeviceSystemSetupConf systemSetup = this.deviceSetupConf.getDeviceSystemSetupConf(deviceId);
      DeviceSystemSetupConfResource systemSetupCamel = new DeviceSystemSetupConfResource();
      List timzoneList = new ArrayList();
      String deviceType = null;
      String[] dstArr;
      if (systemSetup != null) {
         if (systemSetup.getProxy_setting() != null && systemSetup.getProxy_setting_authorization() != null) {
            String[] arrProxy = DeviceUtils.splitter(systemSetup.getProxy_setting(), 5);

            try {
               dstArr = systemSetup.getProxy_setting_authorization().split(":");
               if (dstArr.length >= 2) {
                  arrProxy[3] = dstArr[0];
                  arrProxy[4] = dstArr[1];
               }
            } catch (Exception var12) {
               this.logger.error("proxy_setting_authoriztion has failed.", var12);
            }

            systemSetup.setProxy_setting(String.join(";", arrProxy));
         }

         systemSetupCamel = DeviceModelConverter.newConvertSystemSetupToCamelStyle(systemSetup);
         deviceType = systemSetup.getDevice_type();
         List timeZoneListItem = this.deviceSetupConf.getTimeZoneMapList(systemSetup.getTime_zone_version());
         Map tmpTimezone;
         HashMap timezone;
         int i;
         if (deviceType != null && deviceType.equalsIgnoreCase("iPLAYER")) {
            for(i = 0; i < timeZoneListItem.size(); ++i) {
               tmpTimezone = (Map)timeZoneListItem.get(i);
               timezone = new HashMap();
               timezone.put("index", tmpTimezone.get("display").toString());
               timezone.put("dst", tmpTimezone.get("is_day_light_saving"));
               timzoneList.add(timezone);
            }
         } else {
            for(i = 0; i < timeZoneListItem.size(); ++i) {
               tmpTimezone = (Map)timeZoneListItem.get(i);
               timezone = new HashMap();
               timezone.put("index", tmpTimezone.get("display").toString());
               timezone.put("dst", true);
               timzoneList.add(timezone);
            }
         }
      }

      systemSetupCamel.setTimeZoneList(timzoneList);
      if (systemSetupCamel.getDayLightSaving() != null) {
         String dstManual = systemSetupCamel.getDayLightSavingManual();
         if (dstManual != null && dstManual.length() > 0) {
            dstArr = dstManual.split(";");
            if (dstArr.length > 8) {
               systemSetupCamel.setDstStartMonth(dstArr[0]);
               systemSetupCamel.setDstStartWeek(dstArr[1]);
               systemSetupCamel.setDstStartDay(dstArr[2]);
               String[] starttimearr = dstArr[3].split(":");
               String dstStartTime = StrUtils.getLeftFilledString(starttimearr[0], "0", 2) + ":" + StrUtils.getLeftFilledString(starttimearr[1], "0", 2);
               systemSetupCamel.setDstStartTime(dstStartTime);
               systemSetupCamel.setDstEndMonth(dstArr[4]);
               systemSetupCamel.setDstEndWeek(dstArr[5]);
               systemSetupCamel.setDstEndDay(dstArr[6]);
               String[] endtimearr = dstArr[7].split(":");
               String dstEndTime = StrUtils.getLeftFilledString(endtimearr[0], "0", 2) + ":" + StrUtils.getLeftFilledString(endtimearr[1], "0", 2);
               systemSetupCamel.setDstEndTime(dstEndTime);
               systemSetupCamel.setDstTimeDifference(dstArr[8]);
            }
         } else {
            systemSetupCamel.setDayLightSavingManual((String)null);
         }
      }

      return systemSetupCamel;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource filterDeviceSettingList(V2DeviceFilter filter, HttpServletRequest request, HttpServletResponse response) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      String groupId = StrUtils.nvl(filter.getGroupId());
      if (!groupId.equals("")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.valueOf(groupId));
      } else {
         UserInfo userInfo = UserInfoImpl.getInstance();
         String temp = userInfo.getOrganNameByRootGroupId(userContainer.getUser().getRoot_group_id());
         groupId = String.valueOf(deviceGroupDao.getOrganGroupIdByName(temp));
      }

      V2PageResource result = new V2PageResource();
      boolean isRoot = false;
      Map res = deviceGroupDao.getDeviceOrganizationByGroupId(Integer.parseInt(groupId));
      if (res != null && res.get("GROUP_ID") != null && Long.parseLong(groupId) == (Long)res.get("GROUP_ID")) {
         isRoot = true;
      }

      if (StrUtils.nvl(filter.getSortColumn()).equals("")) {
         filter.setSortColumn("device_name");
      }

      String connectionStatus = StrUtils.nvl(filter.getConnectionStatus()).equals("") ? "device_status_view_all" : filter.getConnectionStatus();
      String disconnectPeriod = null;
      int lastindex = connectionStatus.lastIndexOf("_");
      if (connectionStatus.substring(0, lastindex).equals("device_status_view_disconnection")) {
         disconnectPeriod = connectionStatus.substring(lastindex + 1);
         connectionStatus = connectionStatus.substring(0, lastindex);
      }

      String expirationDate = "device_status_view_all";
      String customInputVal = "0";
      String groupIds = "";
      if (filter.getGroupIds() != null) {
         groupIds = this.convertString(filter.getGroupIds());
      }

      String tagIds = null;
      if (filter.getTagIds() != null) {
         tagIds = this.convertString(filter.getTagIds());
      }

      String alarmTypes = "";
      if (filter.getAlarmTypes() != null) {
         alarmTypes = this.convertString(filter.getAlarmTypes());
      }

      String functionTypes = "";
      if (filter.getFunctionTypes() != null) {
         functionTypes = this.convertString(filter.getFunctionTypes());
      }

      String searchText = StrUtils.nvl(filter.getSearchText()).equals("") ? "" : filter.getSearchText();
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      String inputSources = "";
      if (filter.getInputSources() != null) {
         inputSources = this.convertString(filter.getInputSources());
      }

      String deviceType = "";
      if (filter.getDeviceType() != null) {
         deviceType = this.convertString(filter.getDeviceType());
      }

      DeviceSystemSetupConfManager dao = DeviceSystemSetupConfManagerImpl.getInstance(this.productType);
      ListManager listMgr = new ListManager(dao, "commonlist");
      SelectCondition condition = new SelectCondition();
      condition.setSort_name(filter.getSortColumn());
      condition.setOrder_dir(filter.getSortOrder());
      condition.setGroup_id(Long.valueOf(groupId));
      condition.setSrc_name(searchText);
      condition.setIsRoot(isRoot);
      condition.setStatus_view_mode(connectionStatus);
      if (disconnectPeriod != null) {
         condition.setDisconn_period(disconnectPeriod);
      }

      condition.setExpiration_date(expirationDate);
      condition.setCustom_input_val(customInputVal);
      condition.setFilter_group_ids(groupIds);
      condition.setRole_name(SecurityUtils.getUserContainer().getUser().getRole_name());
      condition.setUser_id(SecurityUtils.getUserContainer().getUser().getUser_id());
      condition.setTagFilter(tagIds);
      condition.setSourceFilter(inputSources);
      if (StringUtils.isNotBlank(alarmTypes)) {
         condition.setAlarmFiltersByString(alarmTypes);
      }

      if (StringUtils.isNotBlank(functionTypes)) {
         condition.setFunctionFiltersByString(functionTypes);
      }

      if (StringUtils.isNotBlank(searchText)) {
         condition.setCommonSearchKeyword(searchText);
      }

      if (deviceType != null && !deviceType.equals("")) {
         condition.setDevice_type(deviceType);
      }

      ArrayList sourceList;
      String[] list;
      int totalChildCount;
      int i;
      if (StringUtils.isNotBlank(condition.getSourceFilter())) {
         sourceList = new ArrayList();
         list = condition.getSourceFilter().split(",");
         String[] var27 = list;
         totalChildCount = list.length;

         for(i = 0; i < totalChildCount; ++i) {
            String tag = var27[i];
            sourceList.add(Long.parseLong(tag));
         }

         condition.setSourceFilterList(sourceList);
      }

      sourceList = null;
      list = null;
      listMgr.addSearchInfo("condition", condition);
      listMgr.setLstSize(Integer.valueOf(filter.getPageSize()));
      listMgr.setSection("getDeviceSystemSetupConfList");
      List list = listMgr.V2dbexecute(filter.getStartIndex(), filter.getPageSize());
      PageManager pageMgr = listMgr.getPageManager();
      response.setContentType("application/json;charset=UTF-8");
      List settingList = new ArrayList();
      result.setRecordsReturned(list.size());
      result.setRecordsTotal(pageMgr.getTotalRowCount());
      result.setRecordsFiltered(pageMgr.getTotalRowCount());
      if (filter.getSortColumn() != null && !filter.getSortColumn().equals("")) {
         result.setSortColumn(filter.getSortColumn());
         result.setSortOrder(filter.getSortOrder());
      }

      result.setPageSize(pageMgr.getInfo().getPageSize());
      totalChildCount = 0;
      i = 0;

      for(int cnt = 0; i < list.size(); ++i) {
         new V2DeviceSettingListResource();
         DeviceSystemSetupConf info = (DeviceSystemSetupConf)list.get(i);
         V2DeviceSettingListResource resource = this.writeData(cnt + "", this.productType, info, request);
         ++cnt;
         if (info.getHas_child() != null && info.getHas_child()) {
            totalChildCount = (int)((long)totalChildCount + info.getChild_cnt());
         }

         resource.setChildCount((long)totalChildCount);
         settingList.add(resource);
         if (info.getHas_child() != null && info.getHas_child()) {
            for(int j = 0; (long)j < info.getChild_cnt(); ++j) {
               DeviceSystemSetupConf childDevice = dao.getDeviceSystemSetupConf(info.getDevice_id() + "_" + (j + 1));
               if (childDevice != null) {
                  new V2DeviceSettingListResource();
                  V2DeviceSettingListResource childResource = this.writeData(cnt + "", this.productType, childDevice, request);
                  settingList.add(childResource);
                  ++cnt;
               }
            }
         }
      }

      result = V2PageResource.createPageResource(settingList, pageMgr);
      result.setStartIndex(filter.getStartIndex());
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceReqServiceResource getCurrentStatusSetup(V2CommonIds deviceIds) throws Exception {
      boolean checkFlag = false;
      Iterator var3 = deviceIds.getIds().iterator();

      while(var3.hasNext()) {
         String deviceId = (String)var3.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var12) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      String sessionId = UUID.randomUUID().toString();
      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var7 = deviceIds.getIds().iterator();

      String deviceId;
      while(var7.hasNext()) {
         deviceId = (String)var7.next();
         if (!DeviceUtils.isConnected(deviceId)) {
            resource.setFailList(deviceIds.getIds());
            this.logger.error("The device is not connected.");
            return resource;
         }
      }

      var7 = deviceIds.getIds().iterator();

      while(var7.hasNext()) {
         deviceId = (String)var7.next();
         new GeneralInfoResource();

         try {
            this.confManager.reqGetSystemSetupFromDevice(deviceId, sessionId);
            successList.add(deviceId);
         } catch (Exception var11) {
            this.logger.error(var11);
            failList.add(deviceId);
         }
      }

      if (successList.size() > 0) {
         resource.setRequestId(sessionId);
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2CommonBulkResultResource getCurrentStatusSetupWithRequestId(V2DeviceReqServiceConf body) throws Exception {
      List deviceIds = body.getDeviceIds();
      Boolean isOnlyRequestStatistic = false;
      if (body.getIsRequestStatistics() != null) {
         isOnlyRequestStatistic = body.getIsRequestStatistics();
      }

      boolean checkFlag = false;
      Iterator var5 = deviceIds.iterator();

      while(var5.hasNext()) {
         String deviceId = (String)var5.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var16) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      String requestId = body.getRequestId();
      Iterator var9 = deviceIds.iterator();

      while(var9.hasNext()) {
         String deviceId = (String)var9.next();
         String productType = "PREMIUM";
         DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
         DeviceSystemSetupConf info = confMgr.getSettingResultBySystemSetup(deviceId, requestId, "SET_DEVICE_SYSTEM_SETUP_CONF");
         if (info == null && !isOnlyRequestStatistic) {
            failList.add(deviceId);
            resource.setSuccessList(successList);
            resource.setFailList(failList);
            return resource;
         }

         try {
            DeviceSystemSetupConfResource systemSetupCamel = this.getDeviceSetupInfoFromDB(deviceId);
            systemSetupCamel.setDeviceId(deviceId);
            successList.add(systemSetupCamel);
         } catch (SQLException var15) {
            this.logger.error("", var15);
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   private V2DeviceSettingListResource writeData(String i, String productType, DeviceSystemSetupConf info, HttpServletRequest request) throws Exception {
      V2DeviceSettingListResource resource = new V2DeviceSettingListResource();
      String strReverse = "Asynchronous";
      String strNormal = "Normal";
      String strOn = "ON";
      String strOff = "OFF";
      String strDay = "Day";
      resource.setCheck("");
      resource.setDeviceId(info.getDevice_id());
      resource.setDeviceType(info.getDevice_type());
      resource.setDeviceName(info.getDevice_name());
      resource.setDeviceTypeVersion(info.getDevice_type_version());
      boolean power = DeviceUtils.isConnected(info.getDevice_id());
      if (power && info.getIs_child() && !DeviceUtils.isConnected(info.getDevice_id().split("_")[0])) {
         power = false;
      }

      resource.setPower(power);
      if (info.getError_flag() != null) {
         resource.setErrorFlag(info.getError_flag());
      }

      if (info.getTrigger_interval() != null) {
         resource.setTriggerInterval(info.getTrigger_interval().toString());
      } else {
         resource.setTriggerInterval("");
      }

      if (info.getMonitoring_interval() != null) {
         resource.setMonitoringInterval(info.getMonitoring_interval().toString());
      } else {
         resource.setMonitoringInterval("");
      }

      if (info.getConnection_limit_time() != null) {
         resource.setConnectionLimitTime(String.valueOf(info.getConnection_limit_time()));
      } else {
         resource.setConnectionLimitTime("");
      }

      if (info.getFtp_connect_mode() != null) {
         resource.setFtpConnectMode(info.getFtp_connect_mode());
      } else {
         resource.setFtpConnectMode("");
      }

      String[] arrProxy;
      String tagIdList;
      String strTime;
      SimpleDateFormat formatter;
      String fontcolor;
      String str_today;
      if (info.getExpiration_date() != null) {
         arrProxy = info.getExpiration_date().toString().split(" ");
         DateUtils dateUtils = new DateUtils();
         tagIdList = null;
         strTime = "yyyy-MM-dd";
         formatter = new SimpleDateFormat(strTime);
         Date today = new Date();
         Timestamp exdate = info.getExpiration_date();
         String fontcolor = "#000000";
         if (exdate != null && !exdate.toString().equals("")) {
            fontcolor = formatter.format(today);
            str_today = formatter.format(exdate);
            tagIdList = DateUtils.getDayDiff(fontcolor, str_today, strTime);
            if (Integer.parseInt(tagIdList) <= 7) {
               fontcolor = "#FF0000";
            } else {
               fontcolor = "#000000";
            }
         } else {
            tagIdList = "";
         }

         fontcolor = StrUtils.convertDateFormat(arrProxy[0], strTime, SecurityUtils.getUserDateFormat());
         resource.setExpirationDate(exdate);
      } else {
         resource.setExpirationDate((Timestamp)null);
      }

      resource.setVwtId(info.getVwt_id());
      resource.setIconErrorSw(info.getIcon_error_sw());
      resource.setIconErrorHw(info.getIcon_error_hw());
      resource.setIconAlarm(info.getIcon_alarm());
      resource.setIconProcessContentDownload(info.getIcon_process_content_download());
      resource.setIconProcessLog(info.getIcon_process_log());
      resource.setIconProcessSwDownload(info.getIcon_process_sw_download());
      resource.setIconMemo(info.getIcon_memo());
      resource.setIconBackup(info.getIcon_backup());
      resource.setWebcam(info.getWebcam());
      resource.setProxySetting(info.getProxy_setting());
      resource.setBgColor(info.getBg_color());
      String none;
      if (info.getProxy_setting() != null && !info.getProxy_setting().equals("")) {
         arrProxy = info.getProxy_setting().split(";");
         if (arrProxy.length > 0) {
            if (Integer.parseInt(arrProxy[0]) == 0) {
               resource.setProxySetting("Do not use");
            } else if (Integer.parseInt(arrProxy[0]) == 1) {
               resource.setProxySetting("Use Browser Settings");
            }

            if (Integer.parseInt(arrProxy[0]) == 2) {
               none = "";
               if (arrProxy.length > 1) {
                  none = "Address:" + arrProxy[1];
                  if (arrProxy.length > 2) {
                     none = none + ",Port:" + arrProxy[2];
                     if (arrProxy.length > 3) {
                        none = none + ",User ID:" + arrProxy[3];
                        if (arrProxy.length > 4) {
                           none = none + ",Password:" + arrProxy[4];
                        }
                     }
                  }
               }

               resource.setProxySetting(none);
            }
         }
      } else {
         resource.setProxySetting("");
      }

      resource.setScreenCaptureInterval(info.getScreen_capture_interval() == null ? "" : info.getScreen_capture_interval().toString());
      if (info.getProof_of_play_mnt() != null) {
         arrProxy = info.getProof_of_play_mnt().split(";");
         if (arrProxy.length == 2) {
            resource.setPlayRecordTerm(arrProxy[0]);
            resource.setPlayRecordSize(arrProxy[1]);
         }
      }

      resource.setScreenRotation(String.valueOf(info.getScreen_rotation()));
      if ("0".equalsIgnoreCase(info.getTime_zone_version())) {
         resource.setTimeZoneIndex("N/A");
         resource.setDayLightSaving("N/A");
      } else {
         if (info.getTime_zone_index() != null) {
            resource.setTimeZoneIndex(info.getTime_zone_index());
         } else {
            resource.setTimeZoneIndex("N/A");
         }

         if (info.getDay_light_saving() != null && info.getDay_light_saving()) {
            resource.setDayLightSaving(strOn);
         } else {
            resource.setDayLightSaving(strOff);
         }
      }

      if (info.getSmart_download() != null && info.getSmart_download() != 0L) {
         resource.setSmartDownload(strOn);
      } else if (info.getSmart_download() != null && info.getSmart_download() == 0L) {
         resource.setSmartDownload(strOff);
      }

      if (info.getProtocol_priority() != null) {
         switch(info.getProtocol_priority().intValue()) {
         case 0:
            resource.setContentDownloadProtocol("HTTP");
            break;
         case 1:
            resource.setContentDownloadProtocol("FTP/HTTP");
            break;
         case 2:
            resource.setContentDownloadProtocol("FTP");
            break;
         default:
            resource.setContentDownloadProtocol((String)null);
         }
      } else {
         resource.setContentDownloadProtocol((String)null);
      }

      resource.setMagicinfoServerUrl(StrUtils.nvl(info.getMagicinfo_server_url()));
      resource.setCpuType(info.getDevice_type_version());
      String tempAmsMode = "0";
      String logLevel;
      if (info.getWebcam() != null && info.getWebcam()) {
         if (info.getAms_play_mode() != null) {
            tempAmsMode = info.getAms_play_mode();
         }

         resource.setAudienceStatistics("Audience Statistics");
         resource.setMode("Mode");
         none = "Not Used";
         tagIdList = "Traffic";
         strTime = "Audience Measurement";
         logLevel = "Traffic & Audience Measurement";
         if (tempAmsMode.equals("0")) {
            resource.setMsg(none);
         } else if (tempAmsMode.equals("1")) {
            resource.setMsg(tagIdList);
         } else if (tempAmsMode.equals("2")) {
            resource.setMsg(strTime);
         } else if (tempAmsMode.equals("3")) {
            resource.setMsg(logLevel);
         }
      }

      resource.setIsBackupPlayer(info.getIs_redundancy());
      if (info.getEdgeServer() != null) {
         resource.setDownloadServer(info.getEdgeServer());
      } else {
         resource.setDownloadServer("");
      }

      resource.setSystemRestartInterval(info.getSystem_restart_interval() == null ? "" : info.getSystem_restart_interval());
      if (!info.getDevice_type().equals("LPLAYER")) {
         DeviceSystemSetupConfManager dao = DeviceSystemSetupConfManagerImpl.getInstance(productType);
         tagIdList = dao.getTagValueListString(info.getDevice_id());
         if (info.getDevice_type().equals("SPLAYER") && info.getDevice_type_version() == CommonDataConstants.TYPE_VERSION_1_0) {
            resource.setTagId(i);
         } else {
            List tempList = new ArrayList();
            String[] tempStr = tagIdList.split(",");

            for(int j = 0; j < tempStr.length; ++j) {
               tempList.add(tempStr[j]);
            }

            resource.setTagId(i);
            resource.setTagList(tempList);
         }

         if (info.getIs_reverse() != null && info.getIs_reverse()) {
            resource.setIsReverse(strReverse);
         } else {
            resource.setIsReverse(strNormal);
         }

         if (info.getAuto_time_setting() != null && info.getAuto_time_setting()) {
            resource.setAutoTimeSetting(strOn);
         } else {
            resource.setAutoTimeSetting(strOff);
         }

         if (info.getRepository_path() != null) {
            resource.setRepositoryPath(info.getRepository_path().replace("\\", "\\\\"));
         } else {
            resource.setRepositoryPath("");
         }

         if (info.getMnt_folder_path() != null) {
            resource.setMntFolderPath(info.getMnt_folder_path().replace("\\", "\\\\"));
         } else {
            resource.setMntFolderPath("");
         }

         if (info.getTime_zone_index() != null) {
            if (!info.getTime_zone_index().equals("4") && !info.getTime_zone_index().equals("5")) {
               resource.setTimeZoneIndex(info.getTime_zone_index());
            } else {
               strTime = changeTimezone(info.getTime_zone_index());
               resource.setTimeZoneIndex(strTime);
            }
         } else {
            resource.setTimeZoneIndex("");
         }

         resource.setTunnelingServer(info.getTunneling_server() == null ? "" : info.getTunneling_server());
         if (info.getOn_timer_setting() != null && info.getOn_timer_setting()) {
            resource.setOnTimerSetting(strOn);
         } else {
            resource.setOnTimerSetting(strOff);
         }

         if (info.getOff_timer_setting() != null && info.getOff_timer_setting()) {
            resource.setOffTimerSetting(strOn);
         } else {
            resource.setOffTimerSetting(strOff);
         }

         String[] arrLog;
         if (info.getLog_mnt() != null && !info.getLog_mnt().equals("")) {
            arrLog = info.getLog_mnt().split(";");
            if (arrLog.length == 3) {
               logLevel = "";
               switch(Integer.parseInt(arrLog[0])) {
               case 0:
                  logLevel = "User";
                  break;
               case 1:
                  logLevel = "Developer";
                  break;
               case 2:
                  logLevel = "Debugger";
                  break;
               default:
                  logLevel = "";
               }

               resource.setLogLevel(logLevel);
               resource.setLogTerm(arrLog[1]);
               resource.setLogSize(arrLog[2]);
            }
         }

         if (info.getContent_mnt() != null) {
            arrLog = info.getContent_mnt().split(";");
            if (arrLog.length == 2) {
               resource.setContentTerm(arrLog[0]);
               resource.setContentSize(arrLog[1]);
            }
         }

         resource.setDeviceModelName(info.getDevice_model_name());
         resource.setHasChild(info.getHas_child());
         if (info.getChild_cnt() != null) {
            resource.setChildCount(info.getChild_cnt());
         } else {
            resource.setChildCount(0L);
         }

         resource.setIsChild(info.getIs_child().toString());
         if (info.getLast_connection_time() != null) {
            strTime = "";
            formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            formatter.format(info.getLast_connection_time());
            resource.setLastConnectionTime(info.getLast_connection_time());
         }
      } else {
         resource.setTagId("N/A");
         resource.setIsReverse("N/A");
         resource.setAutoTimeSetting("N/A");
         resource.setRepositoryPath("N/A");
         resource.setMntFolderPath("N/A");
         resource.setTunnelingServer("N/A");
         resource.setOnTimerSetting("N/A");
         resource.setOffTimerSetting("N/A");
         if (info.getExpiration_date() != null) {
            String[] arrDate = info.getExpiration_date().toString().split(" ");
            DateUtils dateUtils = new DateUtils();
            strTime = null;
            logLevel = "yyyy-MM-dd";
            SimpleDateFormat dateFormat = new SimpleDateFormat(logLevel);
            Date today = new Date();
            Timestamp exdate = info.getExpiration_date();
            fontcolor = "#000000";
            if (exdate != null && !exdate.toString().equals("")) {
               str_today = dateFormat.format(today);
               String date = dateFormat.format(exdate);
               strTime = DateUtils.getDayDiff(str_today, date, logLevel);
               if (Integer.parseInt(strTime) <= 7) {
                  fontcolor = "#FF0000";
               } else {
                  fontcolor = "#000000";
               }
            } else {
               strTime = "";
            }

            resource.setExpirationDate(exdate);
         } else {
            resource.setExpirationDate((Timestamp)null);
         }
      }

      return resource;
   }

   public String convertString(List list) {
      String result = "";
      if (!list.isEmpty() && list.size() > 0) {
         StringBuffer strBuf = new StringBuffer();

         for(int i = 0; i < list.size(); ++i) {
            strBuf.append(list.get(i));
            if (i < list.size() - 1) {
               strBuf.append(",");
            }
         }

         result = strBuf.toString();
      }

      return result;
   }

   public static String changeTimezone(String timezoneIdx) {
      String temp = "";
      byte var3 = -1;
      switch(timezoneIdx.hashCode()) {
      case -189045690:
         if (timezoneIdx.equals("c00000046_new")) {
            var3 = 21;
         }
         break;
      case -120705136:
         if (timezoneIdx.equals("c0000005a_new")) {
            var3 = 28;
         }
         break;
      case 276694371:
         if (timezoneIdx.equals("c00000000")) {
            var3 = 0;
         }
         break;
      case 276694372:
         if (timezoneIdx.equals("c00000001")) {
            var3 = 1;
         }
         break;
      case 276694373:
         if (timezoneIdx.equals("c00000002")) {
            var3 = 2;
         }
         break;
      case 276694374:
         if (timezoneIdx.equals("c00000003")) {
            var3 = 3;
         }
         break;
      case 276694375:
         if (timezoneIdx.equals("c00000004")) {
            var3 = 4;
         }
         break;
      case 276694406:
         if (timezoneIdx.equals("c00000014")) {
            var3 = 8;
         }
         break;
      case 276694411:
         if (timezoneIdx.equals("c00000019")) {
            var3 = 9;
         }
         break;
      case 276694420:
         if (timezoneIdx.equals("c0000000a")) {
            var3 = 5;
         }
         break;
      case 276694423:
         if (timezoneIdx.equals("c0000000d")) {
            var3 = 6;
         }
         break;
      case 276694425:
         if (timezoneIdx.equals("c0000000f")) {
            var3 = 7;
         }
         break;
      case 276694434:
         if (timezoneIdx.equals("c00000021")) {
            var3 = 11;
         }
         break;
      case 276694436:
         if (timezoneIdx.equals("c00000023")) {
            var3 = 12;
         }
         break;
      case 276694441:
         if (timezoneIdx.equals("c00000028")) {
            var3 = 13;
         }
         break;
      case 276694455:
         if (timezoneIdx.equals("c0000001e")) {
            var3 = 10;
         }
         break;
      case 276694466:
         if (timezoneIdx.equals("c00000032")) {
            var3 = 15;
         }
         break;
      case 276694471:
         if (timezoneIdx.equals("c00000037")) {
            var3 = 16;
         }
         break;
      case 276694472:
         if (timezoneIdx.equals("c00000038")) {
            var3 = 17;
         }
         break;
      case 276694485:
         if (timezoneIdx.equals("c0000002d")) {
            var3 = 14;
         }
         break;
      case 276694496:
         if (timezoneIdx.equals("c00000041")) {
            var3 = 19;
         }
         break;
      case 276694501:
         if (timezoneIdx.equals("c00000046")) {
            var3 = 20;
         }
         break;
      case 276694504:
         if (timezoneIdx.equals("c00000049")) {
            var3 = 22;
         }
         break;
      case 276694515:
         if (timezoneIdx.equals("c0000003c")) {
            var3 = 18;
         }
         break;
      case 276694526:
         if (timezoneIdx.equals("c00000050")) {
            var3 = 24;
         }
         break;
      case 276694529:
         if (timezoneIdx.equals("c00000053")) {
            var3 = 25;
         }
         break;
      case 276694531:
         if (timezoneIdx.equals("c00000055")) {
            var3 = 26;
         }
         break;
      case 276694545:
         if (timezoneIdx.equals("c0000004b")) {
            var3 = 23;
         }
         break;
      case 276694561:
         if (timezoneIdx.equals("c00000064")) {
            var3 = 30;
         }
         break;
      case 276694566:
         if (timezoneIdx.equals("c00000069")) {
            var3 = 31;
         }
         break;
      case 276694575:
         if (timezoneIdx.equals("c0000005a")) {
            var3 = 27;
         }
         break;
      case 276694580:
         if (timezoneIdx.equals("c0000005f")) {
            var3 = 29;
         }
         break;
      case 276694589:
         if (timezoneIdx.equals("c00000071")) {
            var3 = 33;
         }
         break;
      case 276694591:
         if (timezoneIdx.equals("c00000073")) {
            var3 = 34;
         }
         break;
      case 276694596:
         if (timezoneIdx.equals("c00000078")) {
            var3 = 35;
         }
         break;
      case 276694610:
         if (timezoneIdx.equals("c0000006e")) {
            var3 = 32;
         }
         break;
      case 276694621:
         if (timezoneIdx.equals("c00000082")) {
            var3 = 37;
         }
         break;
      case 276694626:
         if (timezoneIdx.equals("c00000087")) {
            var3 = 38;
         }
         break;
      case 276694640:
         if (timezoneIdx.equals("c0000007d")) {
            var3 = 36;
         }
         break;
      case 276694651:
         if (timezoneIdx.equals("c00000091")) {
            var3 = 40;
         }
         break;
      case 276694656:
         if (timezoneIdx.equals("c00000096")) {
            var3 = 41;
         }
         break;
      case 276694670:
         if (timezoneIdx.equals("c0000008c")) {
            var3 = 39;
         }
         break;
      case 276694700:
         if (timezoneIdx.equals("c0000009b")) {
            var3 = 42;
         }
         break;
      case 276694703:
         if (timezoneIdx.equals("c0000009e")) {
            var3 = 43;
         }
         break;
      case 276695336:
         if (timezoneIdx.equals("c00000104")) {
            var3 = 70;
         }
         break;
      case 276695341:
         if (timezoneIdx.equals("c00000109")) {
            var3 = 71;
         }
         break;
      case 276695366:
         if (timezoneIdx.equals("c00000113")) {
            var3 = 73;
         }
         break;
      case 276695371:
         if (timezoneIdx.equals("c00000118")) {
            var3 = 74;
         }
         break;
      case 276695385:
         if (timezoneIdx.equals("c0000010e")) {
            var3 = 72;
         }
         break;
      case 276695396:
         if (timezoneIdx.equals("c00000122")) {
            var3 = 76;
         }
         break;
      case 276695415:
         if (timezoneIdx.equals("c0000011d")) {
            var3 = 75;
         }
         break;
      case 276695445:
         if (timezoneIdx.equals("c0000012c")) {
            var3 = 77;
         }
         break;
      case 276695890:
         if (timezoneIdx.equals("c000000a0")) {
            var3 = 44;
         }
         break;
      case 276695895:
         if (timezoneIdx.equals("c000000a5")) {
            var3 = 45;
         }
         break;
      case 276695925:
         if (timezoneIdx.equals("c000000b4")) {
            var3 = 48;
         }
         break;
      case 276695930:
         if (timezoneIdx.equals("c000000b9")) {
            var3 = 49;
         }
         break;
      case 276695939:
         if (timezoneIdx.equals("c000000aa")) {
            var3 = 46;
         }
         break;
      case 276695944:
         if (timezoneIdx.equals("c000000af")) {
            var3 = 47;
         }
         break;
      case 276695953:
         if (timezoneIdx.equals("c000000c1")) {
            var3 = 52;
         }
         break;
      case 276695955:
         if (timezoneIdx.equals("c000000c3")) {
            var3 = 53;
         }
         break;
      case 276695960:
         if (timezoneIdx.equals("c000000c8")) {
            var3 = 54;
         }
         break;
      case 276695961:
         if (timezoneIdx.equals("c000000c9")) {
            var3 = 55;
         }
         break;
      case 276695974:
         if (timezoneIdx.equals("c000000be")) {
            var3 = 51;
         }
         break;
      case 276695985:
         if (timezoneIdx.equals("c000000d2")) {
            var3 = 59;
         }
         break;
      case 276695990:
         if (timezoneIdx.equals("c000000d7")) {
            var3 = 60;
         }
         break;
      case 276696002:
         if (timezoneIdx.equals("c000000cb")) {
            var3 = 56;
         }
         break;
      case 276696004:
         if (timezoneIdx.equals("c000000cd")) {
            var3 = 57;
         }
         break;
      case 276696006:
         if (timezoneIdx.equals("c000000cf")) {
            var3 = 58;
         }
         break;
      case 276696015:
         if (timezoneIdx.equals("c000000e1")) {
            var3 = 62;
         }
         break;
      case 276696017:
         if (timezoneIdx.equals("c000000e3")) {
            var3 = 63;
         }
         break;
      case 276696020:
         if (timezoneIdx.equals("c000000e6")) {
            var3 = 64;
         }
         break;
      case 276696034:
         if (timezoneIdx.equals("c000000dc")) {
            var3 = 61;
         }
         break;
      case 276696045:
         if (timezoneIdx.equals("c000000f0")) {
            var3 = 66;
         }
         break;
      case 276696050:
         if (timezoneIdx.equals("c000000f5")) {
            var3 = 67;
         }
         break;
      case 276696064:
         if (timezoneIdx.equals("c000000eb")) {
            var3 = 65;
         }
         break;
      case 276696094:
         if (timezoneIdx.equals("c000000fa")) {
            var3 = 68;
         }
         break;
      case 276696099:
         if (timezoneIdx.equals("c000000ff")) {
            var3 = 69;
         }
         break;
      case 1130665819:
         if (timezoneIdx.equals("c000000b9_new")) {
            var3 = 50;
         }
         break;
      case 1334275287:
         if (timezoneIdx.equals("c80000040")) {
            var3 = 78;
         }
         break;
      case 1334275288:
         if (timezoneIdx.equals("c80000041")) {
            var3 = 79;
         }
         break;
      case 1334275289:
         if (timezoneIdx.equals("c80000042")) {
            var3 = 80;
         }
         break;
      case 1334275290:
         if (timezoneIdx.equals("c80000043")) {
            var3 = 81;
         }
         break;
      case 1334275291:
         if (timezoneIdx.equals("c80000044")) {
            var3 = 82;
         }
         break;
      case 1334275292:
         if (timezoneIdx.equals("c80000045")) {
            var3 = 83;
         }
         break;
      case 1334275293:
         if (timezoneIdx.equals("c80000046")) {
            var3 = 84;
         }
         break;
      case 1334275294:
         if (timezoneIdx.equals("c80000047")) {
            var3 = 85;
         }
         break;
      case 1334275295:
         if (timezoneIdx.equals("c80000048")) {
            var3 = 86;
         }
         break;
      case 1334275296:
         if (timezoneIdx.equals("c80000049")) {
            var3 = 87;
         }
         break;
      case 1334275336:
         if (timezoneIdx.equals("c8000004a")) {
            var3 = 88;
         }
         break;
      case 1334275337:
         if (timezoneIdx.equals("c8000004b")) {
            var3 = 89;
         }
         break;
      case 1334275338:
         if (timezoneIdx.equals("c8000004c")) {
            var3 = 90;
         }
         break;
      case 1334275339:
         if (timezoneIdx.equals("c8000004d")) {
            var3 = 91;
         }
         break;
      case 1334275340:
         if (timezoneIdx.equals("c8000004e")) {
            var3 = 92;
         }
      }

      switch(var3) {
      case 0:
         temp = "(GMT-12:00) International Date Line West";
         break;
      case 1:
         temp = "(GMT-11:00) Midway Island, Samoa";
         break;
      case 2:
         temp = "(GMT-10:00) Hawaii";
         break;
      case 3:
         temp = "(GMT-09:00) Alaska";
         break;
      case 4:
         temp = "(GMT-08:00) Pacific Time (US & Canada)";
         break;
      case 5:
         temp = "(GMT-07:00) Mountain Time (US & Canada)";
         break;
      case 6:
         temp = "(GMT-07:00) Chihuahua, La Paz, Mazatlan - Old";
         break;
      case 7:
         temp = "(GMT-07:00) Arizona";
         break;
      case 8:
         temp = "(GMT-06:00) Central Time (US & Canada)";
         break;
      case 9:
         temp = "(GMT-06:00) Saskatchewan";
         break;
      case 10:
         temp = "(GMT-06:00) Guadalajara, Mexico City, Monterrey - Old";
         break;
      case 11:
         temp = "(GMT-06:00) Central America";
         break;
      case 12:
         temp = "(GMT-05:00) Eastern Time (US & Canada)";
         break;
      case 13:
         temp = "(GMT-05:00) Indiana (East)";
         break;
      case 14:
         temp = "(GMT-05:00) Bogota, Lima, Quito, Rio Branco";
         break;
      case 15:
         temp = "(GMT-04:00) Atlantic Time (Canada)";
         break;
      case 16:
         temp = "(GMT-04:00) La Paz";
         break;
      case 17:
         temp = "(GMT-04:00) Santiago";
         break;
      case 18:
         temp = "(GMT-03:30) Newfoundland";
         break;
      case 19:
         temp = "(GMT-03:00) Brasilia";
         break;
      case 20:
         temp = "(GMT-03:00) Buenos Aires, Georgetown";
         break;
      case 21:
         temp = "(GMT-03:00) Georgetown";
         break;
      case 22:
         temp = "(GMT-03:00) Greenland";
         break;
      case 23:
         temp = "(GMT-02:00) Mid-Atlantic";
         break;
      case 24:
         temp = "(GMT-01:00) Azores";
         break;
      case 25:
         temp = "(GMT-01:00) Cape Verde Is.";
         break;
      case 26:
         temp = "(GMT) Greenwich Mean Time : Dublin, Edinburgh, Lisbon, London";
         break;
      case 27:
         temp = "(GMT) Casablanca, Monrovia, Reykjavik";
         break;
      case 28:
         temp = "(GMT) Monrovia, Reykjavik";
         break;
      case 29:
         temp = "(GMT+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague";
         break;
      case 30:
         temp = "(GMT+01:00) Sarajevo, Skopje, Warsaw, Zagreb";
         break;
      case 31:
         temp = "(GMT+01:00) Brussels, Copenhagen, Madrid, Paris";
         break;
      case 32:
         temp = "(GMT+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna";
         break;
      case 33:
         temp = "(GMT+01:00) West Central Africa";
         break;
      case 34:
         temp = "(GMT+02:00) Minsk";
         break;
      case 35:
         temp = "(GMT+02:00) Cairo";
         break;
      case 36:
         temp = "(GMT+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius";
         break;
      case 37:
         temp = "(GMT+02:00) Athens, Bucharest";
         break;
      case 38:
         temp = "(GMT+02:00) Jerusalem";
         break;
      case 39:
         temp = "(GMT+02:00) Harare, Pretoria";
         break;
      case 40:
         temp = "(GMT+03:00) Moscow, St. Petersburg, Volgograd";
         break;
      case 41:
         temp = "(GMT+03:00) Kuwait, Riyadh, Istanbul";
         break;
      case 42:
         temp = "(GMT+03:00) Nairobi";
         break;
      case 43:
         temp = "(GMT+03:00) Baghdad";
         break;
      case 44:
         temp = "(GMT+03:30) Tehran";
         break;
      case 45:
         temp = "(GMT+04:00) Abu Dhabi, Muscat";
         break;
      case 46:
         temp = "(GMT+04:00) Caucasus Standard Time";
         break;
      case 47:
         temp = "(GMT+04:30) Kabul";
         break;
      case 48:
         temp = "(GMT+05:00) Ekaterinburg";
         break;
      case 49:
         temp = "(GMT+05:00) Islamabad, Karachi, Tashkent";
         break;
      case 50:
         temp = "(GMT+05:00) Tashkent";
         break;
      case 51:
         temp = "(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi";
         break;
      case 52:
         temp = "(GMT+05:45) Kathmandu";
         break;
      case 53:
         temp = "(GMT+06:00) Astana, Dhaka";
         break;
      case 54:
         temp = "(GMT+05:30) Sri Jayawardenepura";
         break;
      case 55:
         temp = "(GMT+06:00) Almaty, Novosibirsk";
         break;
      case 56:
         temp = "(GMT+06:30) Yangon (Rangoon)";
         break;
      case 57:
         temp = "(GMT+07:00) Bangkok, Hanoi, Jakarta";
         break;
      case 58:
         temp = "(GMT+07:00) Krasnoyarsk";
         break;
      case 59:
         temp = "(GMT+08:00) Beijing, Chongqing, Hong Kong, Urumqi";
         break;
      case 60:
         temp = "(GMT+08:00) Kuala Lumpur, Singapore";
         break;
      case 61:
         temp = "(GMT+08:00) Taipei";
         break;
      case 62:
         temp = "(GMT+08:00) Perth";
         break;
      case 63:
         temp = "(GMT+08:00) Irkutsk, Ulaan Bataar";
         break;
      case 64:
         temp = "(GMT+09:00) Seoul";
         break;
      case 65:
         temp = "(GMT+09:00) Osaka, Sapporo, Tokyo";
         break;
      case 66:
         temp = "(GMT+09:00) Yakutsk";
         break;
      case 67:
         temp = "(GMT+09:30) Darwin";
         break;
      case 68:
         temp = "(GMT+09:30) Adelaide";
         break;
      case 69:
         temp = "(GMT+10:00) Canberra, Melbourne, Sydney";
         break;
      case 70:
         temp = "(GMT+10:00) Brisbane";
         break;
      case 71:
         temp = "(GMT+10:00) Hobart";
         break;
      case 72:
         temp = "(GMT+10:00) Vladivostok";
         break;
      case 73:
         temp = "(GMT+10:00) Guam, Port Moresby";
         break;
      case 74:
         temp = "(GMT+11:00) Magadan, Solomon Is., New Caledonia";
         break;
      case 75:
         temp = "(GMT+12:00) Fiji, Kamchatka, Marshall Is.";
         break;
      case 76:
         temp = "(GMT+12:00) Auckland, Wellington";
         break;
      case 77:
         temp = "(GMT+13:00) Nuku'alofa";
         break;
      case 78:
         temp = "(GMT+04:00) Baku";
         break;
      case 79:
         temp = "(GMT+02:00) Beirut";
         break;
      case 80:
         temp = "(GMT+02:00) Amman";
         break;
      case 81:
         temp = "(GMT-06:00) Guadalajara, Mexico City, Monterrey - New";
         break;
      case 82:
         temp = "(GMT-07:00) Chihuahua, La Paz, Mazatlan - New";
         break;
      case 83:
         temp = "(GMT-08:00) Tijuana, Baja California";
         break;
      case 84:
         temp = "(GMT+02:00) Windhoek";
         break;
      case 85:
         temp = "(GMT+03:00) Tbilisi";
         break;
      case 86:
         temp = "(GMT-04:00) Manaus";
         break;
      case 87:
         temp = "(GMT-03:00) Montevideo";
         break;
      case 88:
         temp = "(GMT+04:00) Yerevan";
         break;
      case 89:
         temp = "(GMT-04:30) Caracas";
         break;
      case 90:
         temp = "(GMT-03:00) Buenos Aires";
         break;
      case 91:
         temp = "(GMT) Casablanca";
         break;
      case 92:
         temp = "(GMT+05:00) Islamabad, Karachi";
      }

      return temp;
   }
}
