package com.samsung.magicinfo.webauthor2.repository.model;

import com.samsung.magicinfo.webauthor2.repository.model.ContentData;
import java.io.Serializable;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "response")
public class ResponseContentData implements Serializable {
  private static final long serialVersionUID = 2687025384137552022L;
  
  @XmlAttribute
  private String code;
  
  @XmlElement
  private String errorMessage;
  
  @XmlElement
  private ContentData responseClass;
  
  public String getCode() {
    return this.code;
  }
  
  public String getErrorMessage() {
    return this.errorMessage;
  }
  
  public ContentData getResponseClass() {
    return this.responseClass;
  }
}
