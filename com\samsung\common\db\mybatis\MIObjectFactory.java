package com.samsung.common.db.mybatis;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;
import org.apache.commons.collections.map.CaseInsensitiveMap;
import org.apache.ibatis.reflection.factory.DefaultObjectFactory;

public class MIObjectFactory extends DefaultObjectFactory {
   private static final long serialVersionUID = 874095501476001218L;

   public MIObjectFactory() {
      super();
   }

   protected Class resolveInterface(Class type) {
      Class classToCreate;
      if (type != List.class && type != Collection.class) {
         if (type != HashMap.class && type != Map.class) {
            if (type == SortedSet.class) {
               classToCreate = TreeSet.class;
            } else if (type == Set.class) {
               classToCreate = HashSet.class;
            } else {
               classToCreate = type;
            }
         } else {
            classToCreate = CaseInsensitiveMap.class;
         }
      } else {
         classToCreate = ArrayList.class;
      }

      return classToCreate;
   }
}
