package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.service.UploadFileNameValidationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class UploadFileNameValidationServiceImpl implements UploadFileNameValidationService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.UploadFileNameValidationServiceImpl.class);
  
  public Boolean validateFileNameNotToMoveIntoUpperFolder(MultipartFile multipartFile) {
    Boolean validate = validateFileNameNotToMoveIntoUpperFolder(multipartFile.getName());
    if (false == validate.booleanValue())
      return Boolean.valueOf(false); 
    return validateFileNameNotToMoveIntoUpperFolder(multipartFile.getOriginalFilename());
  }
  
  public Boolean validateFileNameNotToMoveIntoUpperFolder(String fileName) {
    if (fileName.contains("../") || fileName.contains("..\\"))
      return Boolean.valueOf(false); 
    return Boolean.valueOf(true);
  }
}
