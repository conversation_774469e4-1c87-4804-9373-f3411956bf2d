package com.samsung.magicinfo.webauthor2.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

public class BasicFileInfo implements Serializable {
  private static final long serialVersionUID = 4232286467036483294L;
  
  private final String fileId;
  
  private final String fileName;
  
  private final String size;
  
  @JsonCreator
  public BasicFileInfo(@JsonProperty("fileId") String fileId, @JsonProperty("fileName") String fileName, @JsonProperty("size") String size) {
    this.fileId = fileId;
    this.fileName = fileName;
    this.size = size;
  }
  
  public String getFileId() {
    return this.fileId;
  }
  
  public String getFileName() {
    return this.fileName;
  }
  
  public String getSize() {
    return this.size;
  }
}
