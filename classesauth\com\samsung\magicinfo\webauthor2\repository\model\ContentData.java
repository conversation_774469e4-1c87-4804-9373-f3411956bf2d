package com.samsung.magicinfo.webauthor2.repository.model;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlElement;

public class ContentData implements Serializable {
  @XmlElement(name = "content_id")
  private String contentId;
  
  @XmlElement(name = "version_id")
  private Integer versionId;
  
  @XmlElement(name = "content_name")
  private String contentName;
  
  @XmlElement(name = "media_type")
  private String mediaType;
  
  @XmlElement(name = "creator_id")
  private String creatorId;
  
  @XmlElement(name = "create_date")
  private String createDate;
  
  @XmlElement(name = "last_modified_date")
  private String lastModifiedDate;
  
  @XmlElement(name = "total_size")
  private Long totalSize;
  
  @XmlElement(name = "play_time")
  private String playTime;
  
  @XmlElement(name = "resolution")
  private String resolution;
  
  @XmlElement(name = "is_deleted")
  private String isDeleted;
  
  @XmlElement(name = "is_active")
  private String isActive;
  
  @XmlElement(name = "share_flag")
  private String shareFlag;
  
  @XmlElement(name = "content_meta_data")
  private String contentMetaData;
  
  @XmlElement(name = "group_id")
  private Long groupId;
  
  @XmlElement(name = "group_name")
  private String groupName;
  
  @XmlElement(name = "main_file_id")
  private String mainFileId = "";
  
  @XmlElement(name = "thumb_file_id")
  private String thumbFileId;
  
  @XmlElement(name = "sfi_file_id")
  private String sfiFileId;
  
  @XmlElement(name = "main_file_name")
  private String mainFileName = "";
  
  @XmlElement(name = "thumb_file_path")
  private String thumbFilePath;
  
  @XmlElement(name = "thumb_file_name")
  private String thumbFileName;
  
  @XmlElement(name = "is_linear_vwl")
  private String isLinearVwl;
  
  @XmlElement(name = "screen_count")
  private Integer screenCount;
  
  @XmlElement(name = "x_count")
  private Integer xCount;
  
  @XmlElement(name = "y_count")
  private Integer yCount;
  
  @XmlElement(name = "x_range")
  private Integer xRange;
  
  @XmlElement(name = "y_range")
  private Integer yRange;
  
  @XmlElement(name = "is_streaming")
  private String isStreaming;
  
  @XmlElement(name = "organization_id")
  private Long organizationId;
  
  @XmlElement(name = "org_creator_id")
  private Long orgCreatorId;
  
  @XmlElement(name = "exist_flv")
  private String existFlv;
  
  @XmlElement(name = "main_file_extension")
  private String mainFileExtension;
  
  @XmlElement(name = "content_order")
  private Integer contentOrder;
  
  @XmlElement(name = "content_duration")
  private Integer contentDuration;
  
  @XmlElement(name = "vwl_version")
  private String vwlVersion;
  
  @XmlElement(name = "multi_vwl")
  private String multiVwl;
  
  @XmlElement(name = "html_start_page")
  private String htmlStartPage = "";
  
  @XmlElement(name = "device_type_version")
  private String deviceTypeVersion;
  
  @XmlElement(name = "is_used_template")
  private String isUsedTemplate;
  
  @XmlElement(name = "template_page_count")
  private String templatePageCount;
  
  @XmlElement(name = "device_type")
  private String deviceType;
  
  @XmlElement(name = "refresh_interval")
  private String refreshInterval;
  
  public String getMainFileId() {
    return this.mainFileId;
  }
  
  public String getMainFileName() {
    return this.mainFileName;
  }
  
  public String getContentId() {
    return this.contentId;
  }
  
  public Integer getVersionId() {
    return this.versionId;
  }
  
  public String getContentName() {
    return this.contentName;
  }
  
  public String getMediaType() {
    return this.mediaType;
  }
  
  public String getCreatorId() {
    return this.creatorId;
  }
  
  public String getCreateDate() {
    return this.createDate;
  }
  
  public String getLastModifiedDate() {
    return this.lastModifiedDate;
  }
  
  public Long getTotalSize() {
    return this.totalSize;
  }
  
  public String getPlayTime() {
    return this.playTime;
  }
  
  public String getResolution() {
    return this.resolution;
  }
  
  public String getIsDeleted() {
    return this.isDeleted;
  }
  
  public String getContentMetaData() {
    return this.contentMetaData;
  }
  
  public Long getGroupId() {
    return this.groupId;
  }
  
  public String getGroupName() {
    return this.groupName;
  }
  
  public String getThumbFileId() {
    return this.thumbFileId;
  }
  
  public String getThumbFileName() {
    return this.thumbFileName;
  }
  
  public String getIsStreaming() {
    return this.isStreaming;
  }
  
  public long getOrganizationId() {
    return this.organizationId.longValue();
  }
  
  public String getIsActive() {
    return this.isActive;
  }
  
  public String getShareFlag() {
    return this.shareFlag;
  }
  
  public String getSfiFileId() {
    return this.sfiFileId;
  }
  
  public String getThumbFilePath() {
    return this.thumbFilePath;
  }
  
  public String getIsLinearVwl() {
    return this.isLinearVwl;
  }
  
  public Integer getScreenCount() {
    return this.screenCount;
  }
  
  public Integer getxCount() {
    return this.xCount;
  }
  
  public Integer getyCount() {
    return this.yCount;
  }
  
  public Integer getxRange() {
    return this.xRange;
  }
  
  public Integer getyRange() {
    return this.yRange;
  }
  
  public Long getOrgCreatorId() {
    return this.orgCreatorId;
  }
  
  public String getExistFlv() {
    return this.existFlv;
  }
  
  public String getMainFileExtension() {
    return this.mainFileExtension;
  }
  
  public Integer getContentOrder() {
    return this.contentOrder;
  }
  
  public Integer getContentDuration() {
    return this.contentDuration;
  }
  
  public String getVwlVersion() {
    return this.vwlVersion;
  }
  
  public String getMultiVwl() {
    return this.multiVwl;
  }
  
  public String getDeviceTypeVersion() {
    return this.deviceTypeVersion;
  }
  
  public String getHtmlStartPage() {
    return this.htmlStartPage;
  }
  
  public String getIsUsedTemplate() {
    return this.isUsedTemplate;
  }
  
  public String getTemplatePageCount() {
    return this.templatePageCount;
  }
  
  public String getDeviceType() {
    return this.deviceType;
  }
  
  public String getRefreshInterval() {
    return this.refreshInterval;
  }
}
