package com.samsung.common.constants;

public class CommonDataConstants {
   public static final String TYPE_ALL = "ALL";
   public static final String TYPE_PREMIUM = "iPLAYER";
   public static final String TYPE_SOC = "SPLAYER";
   public static final String TYPE_SOC2 = "S2PLAYER";
   public static final String TYPE_SOC3 = "S3PLAYER";
   public static final String TYPE_SOC4 = "S4PLAYER";
   public static final String TYPE_SOC5 = "S5PLAYER";
   public static final String TYPE_SOC6 = "S6PLAYER";
   public static final String TYPE_SOC7 = "S7PLAYER";
   public static final String TYPE_SOC9 = "S9PLAYER";
   public static final String TYPE_SOC10 = "S10PLAYER";
   public static final String TYPE_MOBILE = "MPLAYER";
   public static final String TYPE_LITE = "LPLAYER";
   public static final String TYPE_THIRDPARTY = "3rdPartyPLAYER";
   public static final String TYPE_AUTO = "AUTO";
   public static final String TYPE_SIGNAGE = "SIGNAGE";
   public static final String TYPE_SIGNAGE6 = "SIGNAGE6";
   public static final String TYPE_SIGNAGE4 = "SIGNAGE4";
   public static final String TYPE_SIGNAGE3 = "SIGNAGE3";
   public static final String TYPE_SIGNAGE_CHILD = "SIG_CHILD";
   public static final String TYPE_LEDBOX = "LEDBOX";
   public static final String TYPE_LEDBOX4 = "LEDBOX4";
   public static final String TYPE_LEDBOX6 = "LEDBOX6";
   public static final String TYPE_LEDBOX9 = "LEDBOX9";
   public static final String TYPE_LEDBOX10 = "LEDBOX10";
   public static final String TYPE_LEDBOX_CHILD = "LED_CABINET";
   public static final String TYPE_APLAYER = "APLAYER";
   public static final String TYPE_WPLAYER = "WPLAYER";
   public static final String TYPE_RMS = "RMS";
   public static final String TYPE_RMS_S = "RSPLAYER";
   public static final String TYPE_RMS_S4 = "RSPLAYER4";
   public static final String TYPE_RMS_S5 = "RSPLAYER5";
   public static final String TYPE_RMS_S6 = "RSPLAYER6";
   public static final String TYPE_RMS_S7 = "RSPLAYER7";
   public static final String TYPE_RMS_S9 = "RSPLAYER9";
   public static final String TYPE_RMS_S10 = "RSPLAYER10";
   public static final String TYPE_RMS_I = "RIPLAYER";
   public static final String TYPE_FLIP = "FLIP";
   public static final String TYPE_FLIP2 = "FLIP2";
   public static final String TYPE_FLIP3 = "FLIP3";
   public static final String TYPE_FLIP4 = "FLIP4";
   public static final String TYPE_RMS_LEDBOX = "RLEDBOX";
   public static final String TYPE_RMS_LEDBOX4 = "RLEDBOX4";
   public static final String TYPE_RMS_LEDBOX6 = "RLEDBOX6";
   public static final String TYPE_RMS_LEDBOX9 = "RLEDBOX9";
   public static final String TYPE_RMS_LEDBOX10 = "RLEDBOX10";
   public static final String TYPE_RMS_SIGNAGE = "RSIGNAGE";
   public static final String TYPE_RMS_SIGNAGE4 = "RSIGNAGE4";
   public static final String TYPE_RMS_SIGNAGE6 = "RSIGNAGE6";
   public static final String TYPE_RMS_KIOSK = "RKIOSK";
   public static final String RMS_MODE = "RMS_MODE";
   public static final Float TYPE_VERSION_10_0 = 10.0F;
   public static final Float TYPE_VERSION_9_0 = 9.0F;
   public static final Float TYPE_VERSION_7_0 = 7.0F;
   public static final Float TYPE_VERSION_6_0 = 6.0F;
   public static final Float TYPE_VERSION_5_0 = 5.0F;
   public static final Float TYPE_VERSION_4_0 = 4.0F;
   public static final Float TYPE_VERSION_3_0 = 3.0F;
   public static final Float TYPE_VERSION_2_0 = 2.0F;
   public static final Float TYPE_VERSION_1_0 = 1.0F;
   public static final Float EVENT_TYPE_VERSION = 2.0F;
   public static final String[] ALL_DEVICE_TYPE_ARRAY = new String[]{"iPLAYER", "SPLAYER", "SIGNAGE", "3rdPartyPLAYER", "MPLAYER", "APLAYER", "WPLAYER", "LPLAYER", "LEDBOX", "RMS", "RSPLAYER", "RIPLAYER", "FLIP", "RLEDBOX", "RSIGNAGE", "RKIOSK"};
   public static final Float[] ALL_DEVICE_TYPE_VERSION_ARRAY;
   public static final String[] ALL_DEVICE_TYPE_SOC_ARRAY;
   public static final String[] RM_DEVICE_TYPE_ARRAY;
   public static final String[] ALL_CONTENT_DEVICE_TYPE_PRIORITY_ARRAY;
   public static final String[] ALL_DEVICE_ARRAY;
   public static final String EMPTY_CHILD_DEVICE_ID = "-";
   public static final String STR_NA = "N/A";
   public static final String PRODUCT_TYPE_PREMIUM = "PREMIUM";
   public static final String PRODUCT_TYPE_SERVER = "SERVER";
   public static final String PRODUCT_VERSION = "8000.0";
   public static final String BROWSER_IE = "IE";
   public static final String BROWSER_ETC = "ETC";
   public static final String JNLP_SERVLET_IE = "FileLoader?paramPathConfName=JNLP_HOME&download=B&filepath=";
   public static final String JNLP_SERVLET = "FileLoader?paramPathConfName=JNLP_HOME&download=D&filepath=";
   public static final String MENU_TYPE_PLAYLIST = "PLAYLIST";
   public static final String MENU_TYPE_SCHEDULE = "SCHEDULE";
   public static final String CMD_TYPE_SELECT = "SELECT";
   public static final String CMD_TYPE_EDIT = "EDIT";
   public static final int FLAG_MEM_DEL = 0;
   public static final int FLAG_MEM_ADD = 1;
   public static final Long DOWNLOAD_MODE_ASSIGNED_ONLY;
   public static final Long DOWNLOAD_MODE_ALL;
   public static final String MENU_CONTENT = "content";
   public static final String MENU_PLAYLIST = "playlist";
   public static final String MENU_DEVICE = "device";
   public static final String MENU_SCHEDULE = "schedule";
   public static final String MENU_STATISTICS = "statistics";
   public static final String MENU_DASHBOARD = "dashboard";
   public static final String MENU_RULESET = "ruleset";
   public static final String EX_SERVER_TYPE_RM = "RM";
   public static final String EX_SERVER_TYPE_DOWNLOAD = "DOWNLOAD";
   public static final String EX_SERVER_TYPE_DATALINK = "DATALINK";
   public static final String EX_SERVER_TYPE_INSIGHT = "INSIGHT";
   public static final String YYYY_MM_DD_DATE_FORMAT = "yyyy-MM-dd";
   public static final String ROOT_ORG = "ROOT";
   public static final String ALL = "ALL";
   public static final Long ROOT_ID;

   public CommonDataConstants() {
      super();
   }

   static {
      ALL_DEVICE_TYPE_VERSION_ARRAY = new Float[]{TYPE_VERSION_10_0, TYPE_VERSION_9_0, TYPE_VERSION_7_0, TYPE_VERSION_6_0, TYPE_VERSION_5_0, TYPE_VERSION_4_0, TYPE_VERSION_3_0, TYPE_VERSION_2_0, TYPE_VERSION_1_0};
      ALL_DEVICE_TYPE_SOC_ARRAY = new String[]{"S10PLAYER", "S9PLAYER", "S7PLAYER", "S6PLAYER", "S5PLAYER", "S4PLAYER", "S3PLAYER", "S2PLAYER", "SPLAYER"};
      RM_DEVICE_TYPE_ARRAY = new String[]{"FLIP", "RIPLAYER", "RSPLAYER", "RLEDBOX", "RSIGNAGE", "RKIOSK"};
      ALL_CONTENT_DEVICE_TYPE_PRIORITY_ARRAY = new String[]{"iPLAYER", "S10PLAYER", "S9PLAYER", "S7PLAYER", "S6PLAYER", "S5PLAYER", "S4PLAYER", "S3PLAYER", "S2PLAYER", "SPLAYER", "LPLAYER"};
      ALL_DEVICE_ARRAY = new String[]{"iPLAYER", "SPLAYER", "S2PLAYER", "S3PLAYER", "S4PLAYER", "S5PLAYER", "S6PLAYER", "S7PLAYER", "S9PLAYER", "S10PLAYER", "APLAYER", "WPLAYER", "FLIP", "FLIP2", "FLIP3", "FLIP4", "LPLAYER", "SIGNAGE3", "SIGNAGE4", "SIGNAGE6", "LEDBOX4", "LEDBOX6", "LEDBOX9", "LEDBOX10", "RIPLAYER", "RSPLAYER", "RLEDBOX4", "RLEDBOX6", "RLEDBOX9", "RLEDBOX10", "RSIGNAGE4", "RSIGNAGE6", "RKIOSK"};
      DOWNLOAD_MODE_ASSIGNED_ONLY = 0L;
      DOWNLOAD_MODE_ALL = 1L;
      ROOT_ID = 0L;
   }
}
