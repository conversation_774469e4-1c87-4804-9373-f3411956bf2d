package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.monitoring.util.ParsedCsvFileName;
import com.samsung.magicinfo.framework.statistics.dao.NewContentFrequencyStatisticsForChartDao;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Date;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.io.FilenameUtils;
import org.apache.logging.log4j.Logger;

public class CsvFileUploadServlet extends HttpServlet {
   private static final long serialVersionUID = 8746018344105858231L;
   private static final int BUF_SIZE = 1048576;
   private static final int EOF = -1;
   private Logger logger = LoggingManagerV2.getLogger(CsvFileUploadServlet.class);
   boolean onStats = true;

   public CsvFileUploadServlet() {
      super();

      try {
         if (StrUtils.nvl(CommonConfig.get("pop.enable")).equalsIgnoreCase("false")) {
            this.onStats = false;
         } else {
            this.onStats = true;
         }
      } catch (ConfigException var2) {
         this.onStats = true;
      }

   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      this.doPost(request, response);
   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      String device_id = "";
      if (this.onStats) {
         long l1 = System.currentTimeMillis();
         Timestamp t1 = new Timestamp(l1);
         this.logger.debug("Start " + t1.toLocaleString());
         InputStream is = null;
         FileOutputStream fos = null;
         boolean var9 = false;

         try {
            String upload_path = CommonConfig.get("UPLOAD_HOME").replace('/', File.separatorChar);
            String s3Path = "";
            if (upload_path != null && !upload_path.equals("")) {
               String fileName = FilenameUtils.getName(StrUtils.nvl(request.getHeader("fileName")));
               String fileExt = FilenameUtils.getExtension(StrUtils.nvl(fileName));
               if (fileExt != null && fileExt.equals("csv")) {
                  String logType = StrUtils.nvl(request.getHeader("logType"));
                  String fileSize = StrUtils.nvl(request.getHeader("fileSize"));
                  String deviceId = fileName.substring(fileName.indexOf("_") + 1, fileName.indexOf("."));
                  this.logger.info("fileName : " + fileName);
                  this.logger.info("logType : " + logType);
                  this.logger.info("deviceId : " + deviceId);
                  File uploadPathChecker = SecurityUtils.getSafeFile(upload_path);
                  if (!uploadPathChecker.exists()) {
                     boolean fSuccess = uploadPathChecker.mkdir();
                     if (!fSuccess) {
                        this.logger.error(fSuccess);
                     }
                  }

                  String paramPath = null;
                  if (logType.equalsIgnoreCase("POP")) {
                     paramPath = CommonConfig.get("POP_LOG_DIR");
                     if (paramPath == null || paramPath.equals("")) {
                        paramPath = "pop";
                     }
                  } else {
                     if (!logType.equalsIgnoreCase("FACE")) {
                        StringBuffer sb = new StringBuffer(logType);
                        sb.append(" is not supported for logType");
                        this.logger.error(sb.toString());
                        return;
                     }

                     paramPath = CommonConfig.get("FACE_LOG_DIR");
                     if (paramPath == null || paramPath.equals("")) {
                        paramPath = "face";
                     }
                  }

                  String filePath = upload_path + File.separator + paramPath;
                  File filePathChecker = SecurityUtils.getSafeFile(filePath);
                  if (!filePathChecker.exists()) {
                     boolean ret = filePathChecker.mkdir();
                     if (!ret) {
                        this.logger.error("returned fail");
                     }
                  }

                  File csvFileChecker = SecurityUtils.getSafeFile(filePath + File.separator + fileName);
                  if (csvFileChecker.isFile()) {
                     StringBuffer sb = new StringBuffer("[Blocked] File has been blocked - duplicated - fileName :");
                     sb.append(fileName);
                     sb.append(", paramPathConfName :");
                     sb.append(logType);
                     this.logger.error(sb.toString());
                     return;
                  }

                  is = request.getInputStream();
                  fos = new FileOutputStream(filePath + File.separator + fileName, false);
                  byte[] buf = new byte[1048576];
                  boolean var23 = false;

                  int binaryRead;
                  while((binaryRead = is.read(buf)) != -1) {
                     fos.write(buf, 0, binaryRead);
                  }

                  File popFile = new File(filePath + File.separator + fileName);
                  if (!fileSize.equals("") && Long.valueOf(fileSize) != popFile.length()) {
                     response.sendError(602, "File Size different");
                     this.logger.error("CsvFileUpload Servlet File Size Error" + fileName + "Heaer = " + fileSize + "RealSize = " + popFile.length());
                     return;
                  }

                  StringBuffer sb = new StringBuffer("[Success] File has been sent - fileName :");
                  sb.append(fileName);
                  sb.append(", paramPathConfName :");
                  sb.append(logType);
                  this.logger.error(sb.toString());
                  ParsedCsvFileName csvFile = new ParsedCsvFileName(fileName);
                  if (csvFile.isCSV() && !csvFile.isAms() && !csvFile.isAudience() && !csvFile.isTraffic() && !csvFile.isEvent() && !csvFile.isExpandType()) {
                     NewContentFrequencyStatisticsForChartDao content_freq_dao = new NewContentFrequencyStatisticsForChartDao();
                     Date date = csvFile.getDate();
                     Calendar cal = Calendar.getInstance();
                     cal.setTime(date);
                     int day = cal.get(5);
                     String year = String.valueOf(cal.get(1));
                     String month = "";
                     if (cal.get(2) <= 8) {
                        month = "0" + String.valueOf(cal.get(2) + 1);
                     } else {
                        month = String.valueOf(cal.get(2) + 1);
                     }

                     String tableNo = content_freq_dao.getPOPFileHistoryMappingInfoByDate(year + "-" + month);
                     if (tableNo == null) {
                        this.logger.error("No DB Info to insert" + fileName);
                     } else {
                        content_freq_dao.updatePOPFileHistory(deviceId, "RECEIVE_SUCCESS", tableNo, String.valueOf(day));
                     }
                  }

                  long l2 = System.currentTimeMillis();
                  Timestamp t2 = new Timestamp(l2);
                  this.logger.debug("END" + fileName + " " + t2.toLocaleString());
                  this.logger.debug("servlet running time : " + (l2 - l1) + "(milisecond) ");
                  return;
               }

               this.logger.error("file extension is not valid.");
               return;
            }

            this.logger.error("upload_path cannot found in config.properties");
            return;
         } catch (Exception var37) {
            response.sendError(600, var37.toString());
            this.logger.error("", var37);
         } finally {
            if (is != null) {
               is.close();
            }

            if (fos != null) {
               fos.close();
            }

         }
      }

   }
}
