package com.samsung.magicinfo.restapi.schedule.utils;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistInterface;
import com.samsung.magicinfo.framework.scheduler.entity.EventScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.MessageEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.common.MessageInterface;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.schedule.model.TTV2AdScheduleEventResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2AdSlotResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2ChannelResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2DeviceGroupInfo;
import com.samsung.magicinfo.restapi.schedule.model.TTV2FrameResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2ProgramResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2ScheduleEventResource;
import com.samsung.magicinfo.restapi.schedule.model.TTV2SubframeResource;
import com.samsung.magicinfo.restapi.schedule.model.V2ContentScheduleDeployReservation;
import com.samsung.magicinfo.restapi.schedule.service.V2ScheduleContentServiceImpl;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;

public class V2ContentScheduleValidator {
   protected final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   private static V2ContentScheduleValidator instance;
   private static TTV2ProgramResource resource = null;

   public V2ContentScheduleValidator() {
      super();
   }

   public static V2ContentScheduleValidator getInstance(TTV2ProgramResource resource) {
      if (instance == null) {
         instance = new V2ContentScheduleValidator();
      }

      V2ContentScheduleValidator.resource = resource;
      return instance;
   }

   public void validate() throws SQLException, ParseException {
      boolean isDeployReserved = resource.getIsDeployReserved();
      if (isDeployReserved) {
         this.checkDeployReservationPossibleToBeTriggered(resource.getDeployReservation());
      }

      if (resource.getDeviceGroups() != null && !resource.getDeviceGroups().isEmpty()) {
         DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
         String minDeviceType = resource.getDeviceType();
         float minDeviceTypeVersion = resource.getDeviceTypeVersion();
         long targetPriority = deviceGroupInfo.getPriority(minDeviceType, minDeviceTypeVersion);
         long groupPriorities = deviceGroupInfo.getMinimumPriority(V2ContentScheduleHelper.getDeviceGroupIdListAsString(resource.getDeviceGroups()));
         if (targetPriority > groupPriorities) {
            StringBuilder invalidGroupIds = new StringBuilder();
            Iterator var46 = resource.getDeviceGroups().iterator();

            while(var46.hasNext()) {
               TTV2DeviceGroupInfo info = (TTV2DeviceGroupInfo)var46.next();
               long groupId = info.getGroupId();
               long priority = deviceGroupInfo.getMinimumPriority(String.valueOf(groupId));
               if (targetPriority > priority) {
                  invalidGroupIds.append(groupId).append(", ");
               }
            }

            invalidGroupIds.delete(invalidGroupIds.lastIndexOf(", "), invalidGroupIds.length());
            this.logger.error("deviceGroupIds has mismatched groupId with deviceType & deviceTypeVersion. Invalid groupId : [" + invalidGroupIds.toString() + "]");
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"groupId"});
         }
      }

      if (StringUtils.equals(resource.getProgramType(), "VWL") && StringUtils.isNotBlank(resource.getBgmContentId())) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_VIDEO_WALL_CANNOT_HAS_BACKGROUND_MUSIC);
      } else {
         List channels = resource.getChannels();
         if (channels != null && !channels.isEmpty()) {
            if (channels.size() > 1) {
               if (resource.getProgramType().equals("ADV")) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQEUST_ADVERTISEMENT_PROGRAM_HAVE_ONLY_SINGLE_CHANNEL);
               }

               for(int i = 0; i < channels.size() - 1; ++i) {
                  int channelNo1 = ((TTV2ChannelResource)channels.get(i)).getChannelNo();

                  for(int j = i + 1; j < channels.size(); ++j) {
                     int channelNo2 = ((TTV2ChannelResource)channels.get(j)).getChannelNo();
                     if (channelNo1 == channelNo2) {
                        this.logger.error("Duplicated channelNo. [channel index : " + i + ", " + j + "]");
                        throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SAME_ITEM_EXIST, new String[]{"channelNo"});
                     }
                  }
               }
            }

            Iterator var19 = channels.iterator();

            while(true) {
               while(var19.hasNext()) {
                  TTV2ChannelResource channel = (TTV2ChannelResource)var19.next();
                  TTV2FrameResource frame = channel.getFrame();
                  if (frame == null) {
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_NOT_NULL_OR_EMPTY, new String[]{"frame"});
                  }

                  List subframes = frame.getFrames();
                  String slotId1;
                  String frameId;
                  int i;
                  String slotId;
                  if (subframes != null && !subframes.isEmpty()) {
                     if (resource.getProgramType().equals("ADV")) {
                        throw new RestServiceException(RestExceptionCode.BAD_REQEUST_ADVERTISEMENT_PROGRAM_HAVE_ONLY_SINGLE_CHANNEL);
                     }

                     frameId = frame.getFrameId();

                     int i;
                     for(i = 0; i < subframes.size(); ++i) {
                        TTV2SubframeResource subframe = (TTV2SubframeResource)subframes.get(i);
                        slotId1 = subframe.getFrameId();
                        if (!frameId.isEmpty() && !slotId1.isEmpty() && frameId.equals(slotId1)) {
                           this.logger.error("Duplicated frameId. [frameId : " + frameId + "]");
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SAME_ITEM_EXIST, new String[]{"frameId"});
                        }

                        List events = subframe.getEvents();
                        if (events != null && !events.isEmpty()) {
                           Iterator var12 = events.iterator();

                           while(var12.hasNext()) {
                              TTV2ScheduleEventResource event = (TTV2ScheduleEventResource)var12.next();
                              if ("PLAYLIST".equals(event.getContentType())) {
                                 this.checkLfdContentInPlaylist(event.getContentId(), resource.getDeviceType());
                              }

                              if (StringUtils.equalsIgnoreCase(event.getContentType(), "HW_IS")) {
                                 throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SUB_FRAME_CANNOT_HAS_INPUT_SOURCE);
                              }

                              if (StringUtils.equalsIgnoreCase(event.getContentType(), "RULESET")) {
                                 throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SUB_FRAME_CANNOT_HAS_RULESET);
                              }
                           }
                        }
                     }

                     for(i = 0; i < subframes.size() - 1; ++i) {
                        String frameId1 = ((TTV2SubframeResource)subframes.get(i)).getFrameId();

                        for(i = i + 1; i < subframes.size(); ++i) {
                           slotId = ((TTV2SubframeResource)subframes.get(i)).getFrameId();
                           if (!frameId1.isEmpty() && !slotId.isEmpty() && frameId1.equals(slotId)) {
                              this.logger.error("Duplicated frameId. [frameId : " + frameId1 + "]");
                              throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SAME_ITEM_EXIST, new String[]{"frameId"});
                           }
                        }
                     }
                  }

                  frameId = frame.getFrameId();
                  List events;
                  int i;
                  if (resource.getProgramType().equals("ADV")) {
                     if (resource.getSlotCount() != frame.getAdSlots().size()) {
                        throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLOT_COUNT_ITEM_COUNT_NOT_MATCH);
                     }

                     List slots = frame.getAdSlots();
                     if (slots == null || slots.isEmpty()) {
                        throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_NOT_NULL_OR_EMPTY, new String[]{"slots"});
                     }

                     if (slots.size() > 1) {
                        for(int i = 0; i < slots.size() - 1; ++i) {
                           slotId1 = ((TTV2AdSlotResource)slots.get(i)).getSlotId();

                           for(int j = i + 1; j < slots.size(); ++j) {
                              String slotId2 = ((TTV2AdSlotResource)slots.get(j)).getSlotId();
                              if (!slotId1.isEmpty() && !slotId2.isEmpty() && slotId1.equals(slotId2)) {
                                 this.logger.error("Duplicated slotId. [adSlot index : " + i + ", " + j + "]");
                                 throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SAME_ITEM_EXIST, new String[]{"slotId"});
                              }
                           }
                        }
                     }

                     TTV2AdSlotResource slot;
                     label329:
                     for(Iterator var32 = slots.iterator(); var32.hasNext(); this.checkAdScheduleEventTimeOverlapped(slot)) {
                        slot = (TTV2AdSlotResource)var32.next();
                        slotId = slot.getSlotId();
                        if ((!frameId.isEmpty() || !slot.getFrameId().isEmpty()) && !frameId.equals(slot.getFrameId())) {
                           this.logger.error("Not matched frameId. [frameId : " + frameId + ", frameId of slot : " + slot.getFrameId() + "]");
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_FRAME_ID_NOT_MATCH);
                        }

                        events = slot.getEvents();
                        if (events != null) {
                           if (events.size() > 1) {
                              for(i = 0; i < events.size() - 1; ++i) {
                                 String scheduleId1 = ((TTV2AdScheduleEventResource)events.get(i)).getScheduleId();

                                 for(int j = i + 1; j < events.size(); ++j) {
                                    String scheduleId2 = ((TTV2AdScheduleEventResource)events.get(j)).getScheduleId();
                                    if (!scheduleId1.isEmpty() && !scheduleId2.isEmpty() && scheduleId1.equals(scheduleId2)) {
                                       this.logger.error("Duplicated scheduleId. [event index : " + i + ", " + j + "]");
                                       throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SAME_ITEM_EXIST, new String[]{"scheduleId"});
                                    }
                                 }
                              }
                           }

                           Iterator var49 = events.iterator();

                           TTV2AdScheduleEventResource event;
                           do {
                              do {
                                 if (!var49.hasNext()) {
                                    continue label329;
                                 }

                                 event = (TTV2AdScheduleEventResource)var49.next();
                              } while(slotId.isEmpty() && event.getSlotId().isEmpty());
                           } while(slotId.equals(event.getSlotId()));

                           this.logger.error("Not matched slotId. [slotId : " + slotId + ", slotId of event : " + event.getSlotId() + "]");
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLOT_ID_NOT_MATCH);
                        }
                     }
                  } else {
                     List tempScheduleIds = new ArrayList();
                     List events = frame.getEvents();
                     if (events != null && !events.isEmpty()) {
                        for(i = 0; i < events.size(); ++i) {
                           tempScheduleIds.add(((TTV2ScheduleEventResource)events.get(i)).getScheduleId());
                           if (subframes != null && !subframes.isEmpty() && "PLAYLIST".equals(((TTV2ScheduleEventResource)events.get(i)).getContentType())) {
                              this.checkLfdContentInPlaylist(((TTV2ScheduleEventResource)events.get(i)).getContentId(), resource.getDeviceType());
                           }
                        }

                        if (subframes != null && !subframes.isEmpty()) {
                           for(i = 0; i < subframes.size(); ++i) {
                              TTV2SubframeResource subframe = (TTV2SubframeResource)subframes.get(i);
                              if (subframe != null) {
                                 events = subframe.getEvents();
                                 if (events != null && !events.isEmpty()) {
                                    for(i = 0; i < events.size(); ++i) {
                                       tempScheduleIds.add(((TTV2ScheduleEventResource)events.get(i)).getScheduleId());
                                    }
                                 }
                              }
                           }
                        }

                        for(i = 0; i < tempScheduleIds.size() - 1; ++i) {
                           slotId = (String)tempScheduleIds.get(i);

                           for(int j = i + 1; j < tempScheduleIds.size(); ++j) {
                              String scheduleId2 = (String)tempScheduleIds.get(j);
                              if (!slotId.isEmpty() && !scheduleId2.isEmpty() && slotId.equals(scheduleId2)) {
                                 this.logger.error("Duplicated scheduleId. [scheduleId : " + slotId + "]");
                                 throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SAME_ITEM_EXIST, new String[]{"scheduleId"});
                              }
                           }
                        }
                     }

                     if (events != null && !events.isEmpty()) {
                        for(i = 0; i < events.size(); ++i) {
                           TTV2ScheduleEventResource event = (TTV2ScheduleEventResource)events.get(i);
                           this.checkExpiredContents(event.getContentId());
                           this.checkLFDContentInMultiFrameSchedule(resource.getDeviceType(), frame.getLineData(), event.getContentType());
                           if (!this.isValidEventRepetitionSetting(i, event)) {
                              this.logger.error("Invalid repetition setting. [event index : " + i + "]");
                              throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"repetition setting"});
                           }

                           this.checkPlaylistInVwlMode(event);
                        }
                     }
                  }
               }

               return;
            }
         } else {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_NOT_NULL_OR_EMPTY, new String[]{"channels"});
         }
      }
   }

   private void checkLfdContentInPlaylist(String playlistId, String minSupportDeviceTypeOfProgram) throws SQLException {
      PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
      if (playlistId != null && !playlistId.isEmpty()) {
         Playlist playlist = pInfo.getPlaylistActiveVerInfo(playlistId);
         List contentList = pInfo.getContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
         Iterator var6 = contentList.iterator();

         while(var6.hasNext()) {
            Content contents = (Content)var6.next();
            String mediaType = contents.getMedia_type();
            if ("SPLAYER".equals(minSupportDeviceTypeOfProgram) && ("DLK".equals(mediaType) || "LFD".equals(mediaType))) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_LFD_CONTENT_NOT_PLAY_MULTI_FRAME_SCHEDULE);
            }
         }
      }

   }

   private void checkPlaylistInVwlMode(TTV2ScheduleEventResource event) throws SQLException {
      if (StringUtils.equals(event.getContentType(), "PLAYLIST") && StringUtils.equals(event.getPlayerMode(), "vwl")) {
         PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
         Playlist pl = playlistInfo.getPlaylistActiveVerInfo(event.getContentId());
         if (!StringUtils.equals(pl.getIs_vwl(), "Y")) {
            this.logger.error("Cannot use a General Playlist in VWL Mode. schedule ID = " + event.getScheduleId() + ", playlist ID = " + event.getContentId());
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_USE_GENERAL_PLAYLIST_IN_VIDEOWALL_MODE);
         }
      }

   }

   private void checkDeployReservationPossibleToBeTriggered(V2ContentScheduleDeployReservation reservation) {
      String today = DateUtils.getCurrentTime("yyyy-MM-dd HH:mm:ss");
      String startDate = reservation.getStartDate();
      String endDate = reservation.getEndDate();
      String deployTime = reservation.getDeployTime();
      String repeatType = reservation.getRepeatType();
      Calendar calendarS = Calendar.getInstance();
      Calendar calendarE = Calendar.getInstance();
      Calendar calendarT = Calendar.getInstance();
      calendarS.set(Integer.parseInt(startDate.substring(0, 4)), Integer.parseInt(startDate.substring(5, 7)) - 1, Integer.parseInt(startDate.substring(8, 10)), Integer.parseInt(deployTime.substring(0, 2)), Integer.parseInt(deployTime.substring(3, 5)), Integer.parseInt(deployTime.substring(6, 8)));
      calendarE.set(Integer.parseInt(endDate.substring(0, 4)), Integer.parseInt(endDate.substring(5, 7)) - 1, Integer.parseInt(endDate.substring(8, 10)), Integer.parseInt(deployTime.substring(0, 2)), Integer.parseInt(deployTime.substring(3, 5)), Integer.parseInt(deployTime.substring(6, 8)));
      calendarT.set(Integer.parseInt(today.substring(0, 4)), Integer.parseInt(today.substring(5, 7)) - 1, Integer.parseInt(today.substring(8, 10)), Integer.parseInt(today.substring(11, 13)), Integer.parseInt(today.substring(14, 16)), Integer.parseInt(today.substring(17, 19)));
      if (!calendarE.after(calendarT)) {
         this.logger.error("[deployReservation] startDate and endDate of deploy reservation is not valid.");
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"startDate and endDate of deploy reservation"});
      } else {
         if (!repeatType.equals("daily".toUpperCase())) {
            List dateOfMonthList;
            int dateOfMonth;
            String fromDate;
            Calendar calendar;
            String toDate;
            if (repeatType.equals("weekly".toUpperCase())) {
               dateOfMonthList = reservation.getRepeatedDayOfWeekList();
               String[] strDayOfWeekArr = new String[]{"SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"};
               boolean isValid = false;

               String strDayOfWeek;
               for(dateOfMonth = 0; dateOfMonth < dateOfMonthList.size() && !isValid; ++dateOfMonth) {
                  fromDate = (String)dateOfMonthList.get(dateOfMonth);
                  Calendar calendar = calendarS.after(calendarT) ? (Calendar)calendarS.clone() : (Calendar)calendarT.clone();

                  while(calendar.compareTo(calendarE) <= 0) {
                     strDayOfWeek = strDayOfWeekArr[calendar.get(7) - 1];
                     if (fromDate.equals(strDayOfWeek)) {
                        isValid = true;
                        break;
                     }

                     calendar.add(5, 1);
                  }
               }

               if (!isValid) {
                  SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                  calendar = calendarS.after(calendarT) ? (Calendar)calendarS.clone() : (Calendar)calendarT.clone();
                  toDate = sdf.format(calendar.getTime());
                  strDayOfWeek = sdf.format(calendarE.getTime());
                  this.logger.error("[deployReservation] There is no possible day of week from " + toDate + " to " + strDayOfWeek);
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NO_POSSIBLE_DAY_OF_WEEK, new String[]{toDate, strDayOfWeek});
               }
            } else if (repeatType.equals("monthly".toUpperCase())) {
               dateOfMonthList = reservation.getRepeatedDateOfMonthList();
               boolean isValid = false;

               for(int i = 0; i < dateOfMonthList.size() && !isValid; ++i) {
                  dateOfMonth = (Integer)dateOfMonthList.get(i);
                  calendar = calendarS.after(calendarT) ? (Calendar)calendarS.clone() : (Calendar)calendarT.clone();

                  while(calendar.compareTo(calendarE) <= 0) {
                     int date = calendar.get(5);
                     if (date == dateOfMonth) {
                        isValid = true;
                        break;
                     }

                     calendar.add(5, 1);
                  }
               }

               if (!isValid) {
                  SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                  Calendar calendar = calendarS.after(calendarT) ? (Calendar)calendarS.clone() : (Calendar)calendarT.clone();
                  fromDate = sdf.format(calendar.getTime());
                  toDate = sdf.format(calendarE.getTime());
                  this.logger.error("[deployReservation] There is no possible date from " + fromDate + " to " + toDate);
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NO_POSSIBLE_DATE, new String[]{fromDate, toDate});
               }
            }
         }

      }
   }

   private void checkAdScheduleEventTimeOverlapped(TTV2AdSlotResource slot) throws ParseException {
      SimpleDateFormat transFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      List events = slot.getEvents();
      if (events != null) {
         for(int i = 0; i < events.size(); ++i) {
            TTV2AdScheduleEventResource event1 = (TTV2AdScheduleEventResource)events.get(i);
            Date oldStart = transFormat.parse(event1.getStartDate() + " " + event1.getStartTime());
            Date oldEnd = transFormat.parse(event1.getStopDate() + " " + event1.getStopTime());

            for(int j = 0; j < events.size(); ++j) {
               if (i != j) {
                  TTV2AdScheduleEventResource event2 = (TTV2AdScheduleEventResource)events.get(j);
                  Date newStart = transFormat.parse(event2.getStartDate() + " " + event2.getStartTime());
                  Date newStop = transFormat.parse(event2.getStopDate() + " " + event2.getStopTime());
                  if (V2ScheduleContentServiceImpl.isOverlapping(oldStart, oldEnd, newStart, newStop)) {
                     this.logger.error("[events] Time is overlapped. Check the date & time fields. [event index : " + i + ", " + j + "]");
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_TIME_IS_OVERLAPPED);
                  }
               }
            }
         }
      }

   }

   private void checkExpiredContents(String contentId) throws SQLException {
      ContentInfo contentInfoImpl = ContentInfoImpl.getInstance();
      Content contentEntity = contentInfoImpl.getContentAndFileActiveVerInfo(contentId);
      if (contentEntity != null && contentEntity.getExpiration_date() != null && contentEntity.getExpiration_date().compareTo(DateUtils.getCurrentTime("yyyyMMdd")) < 0) {
         this.logger.error("Found expired content(contentId : " + contentEntity.getContent_id() + ", contentName : " + contentEntity.getContent_name() + ", expirationDate : " + contentEntity.getExpiration_date() + ")");
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_EXPIRED_CONTENT);
      }
   }

   private void checkLFDContentInMultiFrameSchedule(String minSupportDeviceTypeOfProgram, String lineDataOfFrame, String contentType) {
      if (minSupportDeviceTypeOfProgram.equals("SPLAYER") && contentType != null && (contentType.equals("LFD") || contentType.equals("LFT")) && lineDataOfFrame != null && !lineDataOfFrame.equals("ZERO_FRAME_ONLY")) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_LFD_CONTENT_NOT_PLAY_MULTI_FRAME_SCHEDULE);
      }
   }

   private boolean isValidEventRepetitionSetting(int index, TTV2ScheduleEventResource resource) {
      String repeatType = resource.getRepeatType();
      String playingStartDate = resource.getStartDate();
      String playingEndDate = resource.getEndDate();
      Calendar calendarS = Calendar.getInstance();
      Calendar calendarE = Calendar.getInstance();
      calendarS.set(Integer.parseInt(playingStartDate.substring(0, 4)), Integer.parseInt(playingStartDate.substring(5, 7)) - 1, Integer.parseInt(playingStartDate.substring(8, 10)));
      calendarE.set(Integer.parseInt(playingEndDate.substring(0, 4)), Integer.parseInt(playingEndDate.substring(5, 7)) - 1, Integer.parseInt(playingEndDate.substring(8, 10)));
      if (calendarS.compareTo(calendarE) > 0) {
         this.logger.error("startDate of event item must be the same as or set after the endDate of event. [event index : " + index + "]");
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_END_DATE_BEFORE_START_DATE);
      } else {
         String dateFormat;
         boolean ret;
         int i;
         if (repeatType.toLowerCase().equals("weekly")) {
            String[] days = new String[]{"SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"};
            dateFormat = ConvertUtil.convertListToStringWithSeparator(resource.getRepeatedDayOfWeekList(), ",");
            if (calendarS.compareTo(calendarE) == 0) {
               int dow = calendarS.get(7);
               return dateFormat.matches("(.*)" + days[dow - 1] + "(.*)");
            } else {
               ret = false;

               for(i = 0; i < days.length; ++i) {
                  int dow = calendarS.get(7);
                  if (dateFormat.matches("(.*)" + days[dow - 1] + "(.*)")) {
                     ret = true;
                     break;
                  }

                  if (calendarS.compareTo(calendarE) == 0) {
                     break;
                  }

                  calendarS.add(5, 1);
               }

               return ret;
            }
         } else if (!repeatType.toLowerCase().equals("monthly")) {
            return true;
         } else {
            String monthDays = ConvertUtil.convertListToStringWithSeparator(resource.getRepeatedDateOfMonthList(), ",");
            dateFormat = "%01d";
            if (calendarS.compareTo(calendarE) == 0) {
               String dom = String.format(dateFormat, calendarS.get(5));
               return monthDays.matches("(.*)" + dom + "(.*)");
            } else {
               ret = false;

               for(i = 0; calendarE.compareTo(calendarS) >= 0 && i <= 30; ++i) {
                  String dom = String.format(dateFormat, calendarS.get(5));
                  if (monthDays.matches("(.*)" + dom + "(.*)")) {
                     ret = true;
                  }

                  calendarS.add(5, 1);
               }

               return ret;
            }
         }
      }
   }

   public static boolean isValidActionAsScheduleEditor(String scheduleId, RestAPIAuthorityCheckUtil.AuthorityCheckType checkType) throws Exception {
      User user = SecurityUtils.getLoginUser();
      if (user != null) {
         String roleName = user.getRole_name();
         if (!"Schedule Editor".equals(roleName)) {
            return true;
         }

         String loginUserId;
         String creatorId;
         if (checkType == RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT_SCHEDULE) {
            ScheduleInfo contentScheduleInfo = ScheduleInfoImpl.getInstance();
            ProgramEntity programEntity = contentScheduleInfo.getProgram(scheduleId);
            if (programEntity != null) {
               loginUserId = user.getUser_id();
               creatorId = programEntity.getUser_id();
               if (creatorId != null && creatorId.equals(loginUserId)) {
                  return true;
               }
            }
         }

         if (checkType == RestAPIAuthorityCheckUtil.AuthorityCheckType.MESSAGE_SCHEDULE) {
            MessageInterface messageScheduleInfo = DAOFactory.getMessageInfoImpl("PREMIUM");
            MessageEntity messageEntity = (MessageEntity)messageScheduleInfo.getMessage(scheduleId);
            if (messageEntity != null) {
               loginUserId = user.getUser_id();
               creatorId = messageEntity.getUser_id();
               if (creatorId != null && creatorId.equals(loginUserId)) {
                  return true;
               }
            }
         }

         if (checkType == RestAPIAuthorityCheckUtil.AuthorityCheckType.EVENT_SCHEDULE) {
            EventScheduleInfo eventScheduleInfo = EventScheduleInfoImpl.getInstance();
            EventScheduleEntity eventScheduleEntity = eventScheduleInfo.getEventSchedule(scheduleId);
            if (eventScheduleEntity != null) {
               loginUserId = user.getUser_id();
               creatorId = eventScheduleEntity.getUser_id();
               if (creatorId != null && creatorId.equals(loginUserId)) {
                  return true;
               }
            }
         }
      }

      return false;
   }
}
