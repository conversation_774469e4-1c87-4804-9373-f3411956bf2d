package com.samsung.magicinfo.webauthor2.model;

public class ContentThumbnailBasic {
  private String fileId;
  
  private String fileName;
  
  public ContentThumbnailBasic() {
    this.fileId = "";
    this.fileName = "";
  }
  
  public ContentThumbnailBasic(String fileId, String fileName) {
    this.fileId = fileId;
    this.fileName = fileName;
  }
  
  public String getFileId() {
    return this.fileId;
  }
  
  public String getFileName() {
    return this.fileName;
  }
  
  public void setFileId(String fileId) {
    this.fileId = fileId;
  }
  
  public void setFileName(String fileName) {
    this.fileName = fileName;
  }
  
  public Boolean isEmpty() {
    return Boolean.valueOf((this.fileId == ""));
  }
  
  public String toString() {
    return "Content Thumbnail {fileId=" + this.fileId + ", fileName=" + this.fileName + '}';
  }
}
