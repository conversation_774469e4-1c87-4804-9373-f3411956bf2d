package com.samsung.magicinfo.rc.model.client;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

public class Parameters {
  @JacksonXmlProperty(localName = "deviceId")
  String deviceId;
  
  @JacksonXmlProperty(localName = "Interval")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  int interval;
  
  @JacksonXmlProperty(localName = "attachMode")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  int attachMode;
  
  @JacksonXmlProperty(localName = "resolution")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  String resolution;
  
  @JacksonXmlProperty(localName = "suppKeyboard")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  String suppKeyboard;
  
  @JacksonXmlProperty(localName = "suppPosition")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  String suppPosition;
  
  @JacksonXmlProperty(localName = "suppRemote")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  String suppRemote;
  
  @JacksonXmlProperty(localName = "suppLongPress")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  String suppLongPress;
  
  public String getDeviceId() {
    return this.deviceId;
  }
  
  public void setDeviceId(String deviceId) {
    this.deviceId = deviceId;
  }
  
  public int getInterval() {
    return this.interval;
  }
  
  public void setInterval(int interval) {
    this.interval = interval;
  }
  
  public int getAttachMode() {
    return this.attachMode;
  }
  
  public void setAttachMode(int attachMode) {
    this.attachMode = attachMode;
  }
  
  public String getResolution() {
    return this.resolution;
  }
  
  public void setResolution(String resolution) {
    this.resolution = resolution;
  }
  
  public String getSuppKeyboard() {
    return this.suppKeyboard;
  }
  
  public void setSuppKeyboard(String suppKeyboard) {
    this.suppKeyboard = suppKeyboard;
  }
  
  public String getSuppPosition() {
    return this.suppPosition;
  }
  
  public void setSuppPosition(String suppPosition) {
    this.suppPosition = suppPosition;
  }
  
  public String getSuppRemote() {
    return this.suppRemote;
  }
  
  public void setSuppRemote(String suppRemote) {
    this.suppRemote = suppRemote;
  }
  
  public String getSuppLongPress() {
    return this.suppLongPress;
  }
  
  public void setSuppLongPress(String suppLongPress) {
    this.suppLongPress = suppLongPress;
  }
}
