package com.samsung.magicinfo.rms.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.DiagnosisConstants;
import com.samsung.common.exception.BasicException;
import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.DocumentUtils;
import com.samsung.common.utils.MDCTimeStrUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.WakeOnLan;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.FileManagerImpl;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.dashboard.entity.UserDashboardEntity;
import com.samsung.magicinfo.framework.dashboard.manager.DashboardManager;
import com.samsung.magicinfo.framework.dashboard.manager.DashboardManagerImpl;
import com.samsung.magicinfo.framework.device.constants.DeviceConstants;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceDao;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceGroupDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceLogCollectEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceModel;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSecurityConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTag;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeHolidayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.RmMonitoring;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSecurityConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSecurityConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.LedCabinetConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.LedCabinetConfManagerImpl;
import com.samsung.magicinfo.framework.device.job.manager.JobManager;
import com.samsung.magicinfo.framework.device.job.manager.JobManagerImpl;
import com.samsung.magicinfo.framework.device.log.entity.ServerLogEntity;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManager;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManagerImpl;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManager;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManagerImpl;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.device.software.entity.Software;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManager;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManagerImpl;
import com.samsung.magicinfo.framework.monitoring.entity.ClientFaultEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ContentList;
import com.samsung.magicinfo.framework.monitoring.entity.CurrentPlayingEntity;
import com.samsung.magicinfo.framework.monitoring.entity.DashboardEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ScheduleInfoEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ServerTimeDescCompare;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfo;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfoImpl;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAO;
import com.samsung.magicinfo.framework.scheduler.entity.DownloadContentEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.dao.RmServerDao;
import com.samsung.magicinfo.framework.setup.entity.RmServerEntity;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.openapi.auth.TokenRegistry;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.file.ChecksumCRC32;
import com.samsung.magicinfo.protocol.file.FileUploadCommonHelper;
import com.samsung.magicinfo.protocol.interfaces.WSCall;
import com.samsung.magicinfo.protocol.util.TimeUtil;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.rms.model.CamelCabinetGroup;
import com.samsung.magicinfo.rms.model.DeviceApproveResource;
import com.samsung.magicinfo.rms.model.DeviceDashboardResource;
import com.samsung.magicinfo.rms.model.DeviceDisplayConfResource;
import com.samsung.magicinfo.rms.model.DeviceFilter;
import com.samsung.magicinfo.rms.model.DeviceGeneralConfResource;
import com.samsung.magicinfo.rms.model.DeviceLedCabinetResource;
import com.samsung.magicinfo.rms.model.DeviceMonitoringResource;
import com.samsung.magicinfo.rms.model.DeviceResource;
import com.samsung.magicinfo.rms.model.DeviceRmMonitoring;
import com.samsung.magicinfo.rms.model.DeviceSecurityConfResource;
import com.samsung.magicinfo.rms.model.DeviceSystemSetupConfResource;
import com.samsung.magicinfo.rms.model.DeviceTagAssignment;
import com.samsung.magicinfo.rms.model.DeviceTagResource;
import com.samsung.magicinfo.rms.model.DeviceTimeHolidayResource;
import com.samsung.magicinfo.rms.model.DeviceTimeconfResource;
import com.samsung.magicinfo.rms.model.GeneralInfoResource;
import com.samsung.magicinfo.rms.model.SettingResultByTime;
import com.samsung.magicinfo.rms.util.DeviceModelConverter;
import com.samsung.magicinfo.rms.util.DeviceModelUtils;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileFilter;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.ConnectException;
import java.net.SocketException;
import java.net.URL;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;
import javax.net.ssl.HttpsURLConnection;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXParseException;

@Service("DeviceService")
@Transactional
public class DeviceServiceImpl implements DeviceService {
   protected Logger logger = LoggingManagerV2.getLogger(DeviceServiceImpl.class);
   private static final int BUF_SIZE = 1048576;
   private static final int EOF = -1;
   ResourceBundleMessageSource rms;
   DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
   DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
   DeviceGroupDao devGroupDao = new DeviceGroupDao();
   DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
   MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
   DeviceDao dao = new DeviceDao((SqlSession)null);
   AlarmManager alarmManager = AlarmManagerImpl.getInstance();
   DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
   JobManager jobMgr = JobManagerImpl.getInstance();
   MonitoringManagerInfo deviceMonitoringDao = MonitoringManagerInfoImpl.getInstance("PREMIUM");
   DeviceTimeConfManager deviceConf = DeviceTimeConfManagerImpl.getInstance("PREMIUM");
   DeviceSystemSetupConfManager deviceSetupConf = DeviceSystemSetupConfManagerImpl.getInstance("PREMIUM");
   MessageInfo msgInfo = MessageInfoImpl.getInstance();
   ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
   ContentInfo contentDao = ContentInfoImpl.getInstance();
   ScheduleInfoDAO scheduleDao = new ScheduleInfoDAO();
   final String ALL_MDC = "ALL_MDC";
   final String MODEL_KIND_NEW = "NEW";
   final String REQUEST_ID = "requestId";

   public DeviceServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ResponseBody getDashboardDeviceInfo() throws SQLException {
      ResponseBody responseBody = new ResponseBody();
      DeviceDashboardResource result = new DeviceDashboardResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      boolean useDashboard = false;
      DashboardManager dashboardInfo = DashboardManagerImpl.getInstance();
      List list = dashboardInfo.getDashboard(userId);
      if (list != null) {
         Iterator var8 = list.iterator();

         while(var8.hasNext()) {
            UserDashboardEntity entity = (UserDashboardEntity)var8.next();
            if ("deviceInfoDashboard".equalsIgnoreCase(entity.getDashboard_name())) {
               useDashboard = true;
            }
         }

         DashboardEntity entity = this.monMgr.getDashboardStatus(userContainer.getUser().getOrganization(), (String)null, (Float)null);
         if (entity != null) {
            result.setConnectionCount(entity.getConnectionCount());
            result.setDisConnectionCount(entity.getDisconnectionCount());
            if (SecurityUtils.checkDeviceApprovalPermission()) {
               result.setUnapprovedCount((long)entity.getNonApprovalCount());
            }

            result.setFaultCount(entity.getFaultDeviceCount());
            result.setAlarmCount(entity.getAlarmDeviceCount());
            responseBody.setItems(result);
            responseBody.setStatus("Success");
            return responseBody;
         } else {
            responseBody.setStatus("Fail");
            return responseBody;
         }
      } else {
         responseBody.setStatus("Fail");
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ResponseBody getAllDeviceList(DeviceFilter params) throws SQLException {
      ResponseBody responseBody = new ResponseBody();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      User user = userContainer.getUser();
      params.setRole_name(user.getRole_name());
      params.setUser_id(user.getUser_id());
      params.setStart_index(params.getStartIndex());
      params.setPage_size(params.getPageSize());
      String groupId = "";
      this.logger.info("[REST][DEVICE][filterDeviceList] organization = " + userContainer.getUser().getOrganization());
      if (params.getGroupId() == null) {
         groupId = this.deviceGroupDao.getOrganGroupIdByName(userContainer.getUser().getOrganization()) + "c";
         this.logger.info("[REST][DEVICE][filterDeviceList] groupId = " + groupId);
         if (!groupId.equals("0c")) {
            params.setGroupId(groupId);
            this.logger.info("[REST][DEVICE][filterDeviceList] groupId is not 0");
         }
      }

      List deviceListInfo = this.dao.getDeviceMonitoringList(params);
      if (deviceListInfo != null && deviceListInfo.size() > 0) {
         List deviceListInfoCamel = DeviceModelConverter.convertMonitoringInfoToCamelStyle(deviceListInfo);
         responseBody.setItems(deviceListInfoCamel);
      } else {
         responseBody.setItems(deviceListInfo);
      }

      responseBody.setStatus("Success");
      int totalCount = false;
      int totalCount = this.dao.getCntDeviceMonitoringList(params);
      responseBody.setTotalCount(totalCount);
      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Device Delete Authority')")
   public ResponseBody deleteDevice(String deviceIds) throws SQLException, BasicException, ConfigException {
      ResponseBody responseBody = new ResponseBody();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String[] deviceIdArr = deviceIds.split(",");
      boolean flag = true;
      boolean isRedundancy = false;
      boolean isVwtDevice = false;
      String[] var8 = deviceIdArr;
      int var9 = deviceIdArr.length;

      int var10;
      String deviceId;
      boolean result;
      for(var10 = 0; var10 < var9; ++var10) {
         deviceId = var8[var10];
         result = false;

         try {
            result = this.deviceDao.isRedundancyDevice(deviceId);
         } catch (Exception var16) {
            responseBody.setStatus("Fail");
            responseBody.setErrorCode(ExceptionCode.RES905[0]);
            responseBody.setErrorMessage(ExceptionCode.RES905[2]);
            return responseBody;
         }

         isRedundancy = isRedundancy || result;
         boolean isRedundancyGroupTarget = false;

         try {
            isRedundancyGroupTarget = this.deviceGroupDao.isRedundancyGroup(Integer.parseInt(this.deviceDao.getDeviceGroupIdByDeviceId(deviceId)));
         } catch (Exception var15) {
            this.logger.error("", var15);
         }

         isRedundancy = isRedundancy || isRedundancyGroupTarget;
         Device device = this.deviceDao.getDevice(deviceId);
         if (device != null && device.getVwt_id() != null && !device.getVwt_id().equals("")) {
            isVwtDevice = true;
            break;
         }
      }

      if (!isVwtDevice && !isRedundancy) {
         var8 = deviceIdArr;
         var9 = deviceIdArr.length;

         for(var10 = 0; var10 < var9; ++var10) {
            deviceId = var8[var10];
            if (deviceId != null && !deviceId.equals("")) {
               result = this.deviceDao.deleteDevice(deviceId);
               if (!result) {
                  flag = false;
               } else {
                  new ServerLogEntity();
                  this.monMgr.connectionReload(deviceId, 0);
                  this.monMgr.scheduleReload(deviceId, 0);
                  WSCall.setPlayerRequest(deviceId, "agent restart");
                  this.rms = new ResourceBundleMessageSource();
                  this.rms.setBasename("resource/messages");
               }
            } else {
               flag = false;
            }
         }
      } else {
         flag = false;
      }

      if (flag) {
         responseBody.setStatus("Success");
         responseBody.setErrorMessage("device_delete_success");
      } else if (isVwtDevice) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage("vwllayout_group_delete_fail");
      } else if (isRedundancy) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage("redundancy_group_fail_delete");
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Device Move Authority')")
   public ResponseBody moveDevice(String deviceId, DeviceFilter params) throws SQLException, BasicException, ConfigException {
      ResponseBody responseBody = new ResponseBody();
      String moveGroupId = params.getGroup_id().toString();
      String[] deviceIdArr = params.getDeviceId().split(",");
      User user = SecurityUtils.getLoginUser();

      try {
         String orgName = null;
         String userId = null;
         if (user != null) {
            orgName = user.getOrganization();
            userId = user.getUser_id();
            Map var9 = this.deviceGroupDao.getOrgGroupId(orgName);
            String groupId = ((Long)var9.get("group_id")).toString();
            boolean var11 = this.deviceGroupDao.isVwlGroup(moveGroupId);
            if (var11) {
               responseBody.setStatus("Fail");
               responseBody.setErrorMessage("vwllayout_group_delete_fail");
               return responseBody;
            } else {
               this.deviceGroupDao.getGroup(Integer.parseInt(moveGroupId)).getGroup_name();
               if (deviceIdArr != null && deviceIdArr.length > 0) {
                  int i;
                  for(i = 0; i < deviceIdArr.length; ++i) {
                     Map map = new HashMap();
                     map.put("device_id", deviceIdArr[i]);
                     map.put("group_id", Long.parseLong(moveGroupId));
                     String prvGroupName = (String)this.deviceGroupDao.getGroupNameByDeviceId(deviceIdArr[i]).get("group_name");
                     Device device = this.deviceDao.getDevice(deviceIdArr[i]);
                     map.put("device_type", device.getDevice_type());
                     String organization = null;
                     if (map.get("organization") == null) {
                        organization = this.devGroupDao.getOrgNameByGroupId((Long)map.get("group_id"));
                     } else {
                        organization = (String)map.get("organization");
                     }

                     map.put("organization", organization);
                     if (device.getChild_cnt() != null && device.getChild_cnt() != 0L) {
                        map.put("child_cnt", device.getChild_cnt());
                     }

                     this.deviceDao.removeDeviceTotalCount(Long.valueOf(groupId), 1);
                     this.deviceDao.setDeviceGroupId(map);
                     this.deviceDao.deviceTotalCount(Long.valueOf(moveGroupId), 1);
                     this.monMgr.scheduleReload(deviceIdArr[i], 1);
                  }

                  for(i = 0; i < deviceIdArr.length; ++i) {
                     ScheduleInfoEntity schEntity = this.monMgr.getScheduleStatus(deviceIdArr[i]);
                     if (schEntity != null) {
                        this.schInfo.deploySchedule(deviceIdArr[i], schEntity.getScheduleId(), schEntity.getScheduleVersion());
                        this.msgInfo.deployMessage(deviceIdArr[i], schEntity.getMessageId(), schEntity.getMessageVersion());
                     }

                     Device tmpDev = this.deviceDao.getDevice(deviceIdArr[i]);
                     this.jobMgr.deployJobSchedule("", tmpDev);
                  }
               }

               this.deviceGroupDao.updateCacheDeviceGroup();
               responseBody.setStatus("Success");
               responseBody.setErrorMessage("device_group_move_success");
               return responseBody;
            }
         } else {
            throw new AccessDeniedException("no userId");
         }
      } catch (Exception var19) {
         this.logger.error(var19.getMessage().toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var19.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Only Approval Authority')")
   public ResponseBody approveDevice(String deviceId, DeviceApproveResource params) throws SQLException, BasicException, ConfigException {
      ResponseBody responseBody = new ResponseBody();
      String deviceType = params.getDeviceType();
      String childCnt = params.getChildCnt();
      String hasChild = params.getHasChild();
      String deviceIds = params.getDeviceId();
      Long groupId = Long.parseLong(params.getGroupId());
      String deviceName = params.getDeviceName();
      String location = params.getLocation();
      String calDate = params.getCalDate();
      String slaveNumber = params.getSlaveNumber();
      Locale locale = new Locale("en");
      String sessionId = UUID.randomUUID().toString();

      try {
         HashMap resultMap = DeviceUtils.checkGroupStatus("approve", deviceId, Long.valueOf(params.getGroupId()));
         if (((String)resultMap.get("status")).equals("failure")) {
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage((String)resultMap.get("reason"));
            return responseBody;
         } else {
            boolean isVwlGroup = this.devGroupDao.getBooleanVwlGroupId(groupId);
            User user = SecurityUtils.getLoginUser();
            String userId = null;
            if (user != null) {
               userId = user.getUser_id();
               if (isVwlGroup) {
                  responseBody.setStatus("Fail");
                  responseBody.setErrorMessage("vwllayout_group_APPROVAL_FAIL");
                  return responseBody;
               } else {
                  String organization = params.getOrganization();
                  if (organization == null || organization.equals(" ") || organization.equals("") || organization.equals("undefined")) {
                     DeviceGroupDao groups = new DeviceGroupDao();
                     organization = groups.getOrgNameByGroupId(groupId);
                  }

                  try {
                     String selDevice = null;
                     if (deviceIds != null) {
                        String[] deviceArr = deviceIds.split(",");
                        if (deviceArr.length == 1) {
                           selDevice = deviceIds;
                        }
                     }

                     DeviceUtils.approveDevice(deviceIds, groupId, deviceName, location, selDevice, locale, sessionId, userId, calDate, organization, params.getIpAddress());
                  } catch (NullPointerException var22) {
                     responseBody.setStatus("Fail");
                     responseBody.setErrorCode(ExceptionCode.RES905[0]);
                     responseBody.setErrorMessage(ExceptionCode.RES905[2]);
                     return responseBody;
                  }

                  if (deviceType != null) {
                     if (!deviceType.equals("SIGNAGE") && !deviceType.equals("RSIGNAGE")) {
                        if ((deviceType.equals("LEDBOX") || deviceType.equals("RLEDBOX")) && slaveNumber != null && !slaveNumber.equals("")) {
                           if (slaveNumber.indexOf(59) >= 0) {
                              String[] slaveNumberArr = slaveNumber.split(";");
                              long[] slaveInfo = new long[]{Long.parseLong(slaveNumberArr[0]), Long.parseLong(slaveNumberArr[1]), Long.parseLong(slaveNumberArr[2]), Long.parseLong(slaveNumberArr[3])};
                              DeviceUtils.scanChildDevice(deviceIds, slaveInfo, (String[])null, (String[])null);
                           } else {
                              long[] slaveInfo = new long[]{Long.parseLong(slaveNumber)};
                              DeviceUtils.scanChildDevice(deviceIds, slaveInfo, (String[])null, (String[])null);
                           }
                        }
                     } else {
                        DeviceUtils.scanChildDevice(deviceIds, Long.parseLong(slaveNumber));
                     }
                  }

                  DeviceUtils.refreshPriorityByDeviceType(groupId, deviceIds);
                  DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
                  groupDao.updateCacheDeviceGroup();
                  responseBody.setStatus("Success");
                  responseBody.setErrorMessage("device_approval_success");
                  return responseBody;
               }
            } else {
               throw new AccessDeniedException("no userId");
            }
         }
      } catch (Exception var23) {
         this.logger.error("", var23);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var23.toString());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ResponseBody getGeneralInfo(String deviceId) {
      ResponseBody responseBody = new ResponseBody();

      try {
         DeviceGeneralConf generalInfo = this.deviceDao.getDeviceGeneralConf(deviceId);
         if (generalInfo != null) {
            DeviceGeneralConfResource generalInfoCamel = DeviceModelConverter.convertGeneralInfoToCamelStyle(generalInfo);
            List tmpModelNameList = new ArrayList();
            List modelNameList = this.deviceDao.getModelNameListByDeviceId(deviceId);
            if (modelNameList != null) {
               int i;
               if ("LPLAYER".equalsIgnoreCase(generalInfo.getDevice_type())) {
                  for(i = 0; i < modelNameList.size(); ++i) {
                     tmpModelNameList.add(StrUtils.getLiteDeviceModelName(((DeviceModel)modelNameList.get(i)).getDevice_model_name()));
                  }
               } else {
                  for(i = 0; i < modelNameList.size(); ++i) {
                     tmpModelNameList.add(((DeviceModel)modelNameList.get(i)).getDevice_model_name());
                  }
               }
            }

            generalInfoCamel.setModelNameList(tmpModelNameList);
            generalInfoCamel.setPower(this.monMgr.isConnected(deviceId));
            responseBody.setStatus("Success");
            responseBody.setItems(generalInfoCamel);
         } else {
            responseBody.setStatus("Fail");
            responseBody.setErrorCode(ExceptionCode.RES905[0]);
            responseBody.setErrorMessage(ExceptionCode.RES905[2]);
         }

         return responseBody;
      } catch (SQLException var8) {
         this.logger.error("", var8);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var8.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ResponseBody getTimeInfo(String deviceId) {
      ResponseBody responseBody = new ResponseBody();
      DeviceTimeConf timeConf = null;

      try {
         Device device = this.deviceDao.getDeviceMinInfo(deviceId);
         String deviceModelCode = device.getDevice_model_code();
         int timerCnt = MDCTimeStrUtils.getTimerCnt(deviceModelCode);
         String modelKind = MDCTimeStrUtils.getModelKind(deviceModelCode);
         if ("NEW".equalsIgnoreCase(modelKind)) {
            timeConf = this.deviceConf.getDeviceNewTimeConf(deviceId, timerCnt);
            timeConf.fillTimeClock();
            timeConf.fillTimerValues();
         } else {
            timeConf = this.deviceConf.getDeviceOldTimeConf(deviceId);
         }

         DeviceTimeconfResource timeConfCamel = DeviceModelConverter.convertTimeinfoToCamelStyle(timeConf);
         timeConfCamel.setTimerCount(timerCnt);
         List tmpHolidayList = this.deviceConf.getDeviceTimeHolidayConf(deviceId);
         if (tmpHolidayList != null && tmpHolidayList.size() > 0) {
            for(int i = 0; i < tmpHolidayList.size(); ++i) {
               DeviceTimeHolidayConf tmp = (DeviceTimeHolidayConf)tmpHolidayList.get(i);
               DeviceTimeHolidayResource holiday = new DeviceTimeHolidayResource();
               holiday.setDeviceId(tmp.getDevice_id());
               holiday.setMonth1(tmp.getMonth1());
               holiday.setMonth2(tmp.getMonth2());
               holiday.setDay1(tmp.getDay1());
               holiday.setDay2(tmp.getDay2());
               holiday.setHolidaySeq(0);
               timeConfCamel.addDeviceTimeHolidayResource(holiday);
            }
         }

         String inputSourceCode = "20:30:24:12:4:8:32:96:80:31:33:35:49:51:34:36:50:52:37:38:48:64:31:13:14:85:97";
         String inputSourceText = "PC:BNC:DVI:AV:S-Video:Component:MagicInfo:MagicInfo-Lite/S:Plug In Module:DVI_VIDEO:HDMI1:HDMI2:HDMI3:HDMI4:HDMI1_PC:HDMI2_PC:HDMI3_PC:HDMI4_PC:Display_Port:Display_Port2:ATV:DTV:DVI_VIDEO:AV2:Ext:HDBaseT:WiDi";
         String[] codeArr = inputSourceCode.split(":");
         String[] textArr = inputSourceText.split(":");
         List inputSourceList = new ArrayList();

         for(int i = 0; i < codeArr.length; ++i) {
            HashMap inputSource = new HashMap();
            inputSource.put("code", codeArr[i]);
            inputSource.put("text", textArr[i]);
            inputSourceList.add(inputSource);
         }

         timeConfCamel.setInputSourceList(inputSourceList);
         responseBody.setStatus("Success");
         responseBody.setItems(timeConfCamel);
         return responseBody;
      } catch (NullPointerException var17) {
         responseBody.setStatus("Fail");
         responseBody.setErrorCode(ExceptionCode.RES905[0]);
         responseBody.setErrorMessage(ExceptionCode.RES905[2]);
         return responseBody;
      } catch (SQLException var18) {
         this.logger.error("", var18);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var18.getMessage());
         return responseBody;
      } catch (ConfigException var19) {
         this.logger.error("", var19);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var19.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ResponseBody getSetupInfo(String deviceId) {
      ResponseBody responseBody = new ResponseBody();

      try {
         DeviceSystemSetupConfResource systemSetupCamel = this.getDeviceSetupInfoFromDB(deviceId);
         responseBody.setStatus("Success");
         responseBody.setItems(systemSetupCamel);
         return responseBody;
      } catch (NullPointerException var4) {
         responseBody.setStatus("Fail");
         responseBody.setErrorCode(ExceptionCode.RES905[0]);
         responseBody.setErrorMessage(ExceptionCode.RES905[2]);
         return responseBody;
      } catch (SQLException var5) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var5.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ResponseBody getDisplayControlInfo(String deviceId) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      DeviceDisplayConfManager displayDao = DeviceDisplayConfManagerImpl.getInstance("PREMIUM");

      try {
         DeviceDisplayConf deviceDisplayConf = displayDao.getDeviceDisplayConf(deviceId);
         DeviceDisplayConfResource displayInfoCamel = DeviceModelConverter.convertDisplayInfoToCamelStyle(deviceDisplayConf);
         String[] codeArr = DeviceConstants.INPUTSOURCE_CODE;
         String[] textArr = DeviceConstants.INPUTSOURCE_NAME;
         List inputSourceList = new ArrayList();

         for(int i = 0; i < codeArr.length; ++i) {
            HashMap inputSource = new HashMap();
            inputSource.put("code", codeArr[i]);
            inputSource.put("text", textArr[i]);
            inputSourceList.add(inputSource);
         }

         displayInfoCamel.setInputSourceList(inputSourceList);
         boolean isPcMode = true;
         if (displayInfoCamel.getBasicSource() != null) {
            isPcMode = DeviceUtils.checkPcMode(displayInfoCamel.getBasicSource());
         }

         displayInfoCamel.setPcMode(isPcMode);
         responseBody.setStatus("Success");
         responseBody.setItems(displayInfoCamel);
         return responseBody;
      } catch (SQLException var11) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var11.getMessage());
         return responseBody;
      } catch (NullPointerException var12) {
         responseBody.setStatus("Fail");
         responseBody.setErrorCode(ExceptionCode.RES905[0]);
         responseBody.setErrorMessage(ExceptionCode.RES905[2]);
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Security Authority')")
   public ResponseBody getSecurityControlInfo(String deviceId) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      DeviceSecurityConfManager securityDao = DeviceSecurityConfManagerImpl.getInstance("PREMIUM");

      try {
         DeviceSecurityConf deviceSecurityConf = securityDao.getDeviceSecurityConf(deviceId);
         DeviceSecurityConfResource securityInfoCamel = DeviceModelConverter.convertSecurityInfoToCamelStyle(deviceSecurityConf);
         responseBody.setStatus("Success");
         responseBody.setItems(securityInfoCamel);
         return responseBody;
      } catch (SQLException var6) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var6.getMessage());
         return responseBody;
      } catch (NullPointerException var7) {
         responseBody.setStatus("Fail");
         responseBody.setErrorCode(ExceptionCode.RES905[0]);
         responseBody.setErrorMessage(ExceptionCode.RES905[2]);
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public ResponseBody updateGeneralInfo(String deviceId, DeviceGeneralConfResource obj) {
      ResponseBody responseBody = new ResponseBody();
      GeneralInfoResource data = new GeneralInfoResource();
      String requestId = UUID.randomUUID().toString();
      DeviceGeneralConf deviceGeneralConf = new DeviceGeneralConf();
      deviceGeneralConf.setDevice_id(deviceId);
      String location = null;
      String deviceModelName = null;
      boolean result = false;

      try {
         result = this.deviceDao.setDeviceNameAndLocation(deviceId, (String)null, (String)location, (String)null, (String)deviceModelName);
      } catch (NullPointerException var11) {
         responseBody.setStatus("Fail");
         responseBody.setErrorCode(ExceptionCode.RES905[0]);
         responseBody.setErrorMessage(ExceptionCode.RES905[2]);
         return responseBody;
      } catch (SQLException var12) {
         this.logger.error("", var12);
         responseBody.setStatus("Fail");
         return responseBody;
      }

      if (result) {
         data.setLocation((String)location);
         data.setDeviceModelName((String)deviceModelName);
         responseBody.setItems(data);
         responseBody.setStatus("Success");
         return responseBody;
      } else {
         responseBody.setStatus("Fail");
         responseBody.setErrorCode(ExceptionCode.RES909[0]);
         responseBody.setErrorMessage(ExceptionCode.RES909[2]);
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public ResponseBody updateGeneral(String deviceId, DeviceGeneralConfResource obj) {
      ResponseBody responseBody = new ResponseBody();
      GeneralInfoResource data = new GeneralInfoResource();
      String requestId = UUID.randomUUID().toString();
      DeviceGeneralConf deviceGeneralConf = new DeviceGeneralConf();
      deviceGeneralConf.setDevice_id(deviceId);
      String deviceName = null;
      String deviceModelName = null;
      String location = null;
      if (obj.getDeviceName() != null) {
         deviceName = obj.getDeviceName();
         deviceGeneralConf.setDevice_name(deviceName);
      }

      if (obj.getDeviceModelName() != null) {
         if (obj.getDeviceModelName().startsWith("LITE_")) {
            deviceModelName = obj.getDeviceModelName().substring(5);
         } else {
            deviceModelName = obj.getDeviceModelName();
         }

         deviceGeneralConf.setDevice_model_name(deviceModelName);
      }

      if (obj.getLocation() != null) {
         location = obj.getLocation();
         deviceGeneralConf.setLocation(location);
      }

      try {
         this.deviceDao.setDeviceNameAndLocation(obj.getDeviceId(), deviceName, location, (String)null, deviceModelName);
         this.confManager.reqSetGeneralToDevice(deviceGeneralConf, requestId);
      } catch (NullPointerException var12) {
         responseBody.setStatus("Fail");
         responseBody.setErrorCode(ExceptionCode.RES905[0]);
         responseBody.setErrorMessage(ExceptionCode.RES905[2]);
         return responseBody;
      } catch (Exception var13) {
         this.logger.error("", var13);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var13.getMessage());
         return responseBody;
      }

      if (obj.getSendCleanStorageFlag()) {
         try {
            this.confManager.reqGetDevicePredefinedCmd(deviceId, "CLEANUP_STORAGE_DATA", requestId);
         } catch (Exception var11) {
            this.logger.error("", var11);
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage(var11.getMessage());
            return responseBody;
         }
      }

      data.setRequestId(requestId);
      responseBody.setItems(data);
      responseBody.setStatus("Success");
      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public ResponseBody getUpdatedGeneralInfoResult(String deviceId, String requestId) {
      ResponseBody responseBody = new ResponseBody();

      try {
         DeviceGeneralConf deviceGeneralConf = this.deviceDao.getDeviceGeneralConf(deviceId);
         if (deviceGeneralConf != null) {
            DeviceGeneralConfResource generalInfoCamel = DeviceModelConverter.convertGeneralInfoToCamelStyle(deviceGeneralConf);
            List tmpModelNameList = new ArrayList();
            List modelNameList = this.deviceDao.getModelNameListByDeviceId(deviceId);
            if (modelNameList != null) {
               int i;
               if ("LPLAYER".equalsIgnoreCase(deviceGeneralConf.getDevice_type())) {
                  for(i = 0; i < modelNameList.size(); ++i) {
                     tmpModelNameList.add(StrUtils.getLiteDeviceModelName(((DeviceModel)modelNameList.get(i)).getDevice_model_name()));
                  }
               } else {
                  for(i = 0; i < modelNameList.size(); ++i) {
                     tmpModelNameList.add(((DeviceModel)modelNameList.get(i)).getDevice_model_name());
                  }
               }
            }

            generalInfoCamel.setModelNameList(tmpModelNameList);
            generalInfoCamel.setPower(this.monMgr.isConnected(deviceId));
            responseBody.setStatus("Success");
            responseBody.setItems(generalInfoCamel);
         } else {
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage("Get timeout.");
         }
      } catch (SQLException var9) {
         this.logger.error("", var9);
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public ResponseBody updateTimeInfo(String deviceId, DeviceTimeconfResource obj) {
      ResponseBody responseBody = new ResponseBody();
      String productType = "PREMIUM";
      Device device = null;
      String deviceModelCode = null;
      String modelKind = null;
      int timerCnt = 0;
      DeviceTimeConf deviceTimeConf = null;

      try {
         device = this.deviceDao.getDeviceMinInfo(deviceId);
         deviceModelCode = device.getDevice_model_code();
         modelKind = MDCTimeStrUtils.getModelKind(deviceModelCode);
         timerCnt = MDCTimeStrUtils.getTimerCnt(deviceModelCode);
      } catch (NullPointerException var20) {
         responseBody.setStatus("Fail");
         responseBody.setErrorCode(ExceptionCode.RES905[0]);
         responseBody.setErrorMessage(ExceptionCode.RES905[2]);
         return responseBody;
      } catch (SQLException var21) {
         this.logger.error("", var21);
      }

      try {
         if ("NEW".equalsIgnoreCase(modelKind) && !obj.isSeparatableDevice()) {
            obj.setTimerTimer1(MDCTimeStrUtils.convert15to13(obj.getTimerTimer1()));
            obj.setTimerTimer2(MDCTimeStrUtils.convert15to13(obj.getTimerTimer2()));
            obj.setTimerTimer3(MDCTimeStrUtils.convert15to13(obj.getTimerTimer3()));
            if (timerCnt == 7) {
               obj.setTimerTimer4(MDCTimeStrUtils.convert15to13(obj.getTimerTimer4()));
               obj.setTimerTimer5(MDCTimeStrUtils.convert15to13(obj.getTimerTimer5()));
               obj.setTimerTimer6(MDCTimeStrUtils.convert15to13(obj.getTimerTimer6()));
               obj.setTimerTimer7(MDCTimeStrUtils.convert15to13(obj.getTimerTimer7()));
            }
         }
      } catch (Exception var23) {
         this.logger.error("Error while checking separate timer", var23);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var23.getMessage());
         return responseBody;
      }

      String requestId = UUID.randomUUID().toString();
      String performHolidayList = obj.getTimerHoliday();
      obj.setTimer_holiday((String)null);
      if (obj.getTimeClockConf() != null) {
         obj.combineTimeClock();
      }

      if (obj.getTimerConfTimer1() != null) {
         obj.combineTimer1Values();
      }

      if (obj.getTimerConfTimer2() != null) {
         obj.combineTimer2Values();
      }

      if (obj.getTimerConfTimer3() != null) {
         obj.combineTimer3Values();
      }

      if (obj.getTimerConfTimer4() != null) {
         obj.combineTimer4Values();
      }

      if (obj.getTimerConfTimer5() != null) {
         obj.combineTimer5Values();
      }

      if (obj.getTimerConfTimer6() != null) {
         obj.combineTimer6Values();
      }

      if (obj.getTimerConfTimer7() != null) {
         obj.combineTimer7Values();
      }

      deviceTimeConf = obj.converToTimeConf();
      deviceTimeConf.setDevice_id(deviceId);

      try {
         this.confManager.reqSetTimeToDevice(deviceTimeConf, requestId);
         List holidayList = obj.getDeviceTimeHolidayList();
         if (holidayList != null && holidayList.size() > 0) {
            DeviceTimeConf info = new DeviceTimeConf();
            info.setDevice_id(deviceId);

            for(int k = 0; k < holidayList.size(); ++k) {
               DeviceTimeHolidayResource holiday = (DeviceTimeHolidayResource)holidayList.get(k);
               String tmp = holiday.getHolidaySeq() + ";" + Integer.parseInt(holiday.getMonth1()) + ";" + Integer.parseInt(holiday.getDay1()) + ";" + Integer.parseInt(holiday.getMonth2()) + ";" + Integer.parseInt(holiday.getDay2());
               info.setTimer_holiday(tmp);

               try {
                  this.confManager.reqSetTimeToDevice(info, requestId);
               } catch (Exception var19) {
                  this.logger.error("", var19);
                  responseBody.setStatus("Fail");
                  responseBody.setErrorMessage(var19.getMessage());
                  return responseBody;
               }
            }
         }

         GeneralInfoResource data = new GeneralInfoResource();
         data.setRequestId(requestId);
         responseBody.setStatus("Success");
         responseBody.setItems(data);
         return responseBody;
      } catch (Exception var22) {
         this.logger.error("", var22);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var22.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public ResponseBody getUpdatedTimeInfoResult(String deviceId, String requestId) {
      ResponseBody responseBody = new ResponseBody();

      try {
         Map resultMap = this.confManager.getSettingResultByTime(deviceId, requestId, "SET_DEVICE_TIME_CONF");
         SettingResultByTime settingResultByTime = new SettingResultByTime();
         if (!StringUtils.isEmpty((CharSequence)resultMap.get("on_time"))) {
            settingResultByTime.setOn_time((String)resultMap.get("on_time"));
         }

         if (!StringUtils.isEmpty((CharSequence)resultMap.get("off_time"))) {
            settingResultByTime.setOn_time((String)resultMap.get("off_time"));
         }

         if (!StringUtils.isEmpty((CharSequence)resultMap.get("current_time"))) {
            settingResultByTime.setOn_time((String)resultMap.get("current_time"));
         }

         if (!StringUtils.isEmpty((CharSequence)resultMap.get("clock"))) {
            settingResultByTime.setOn_time((String)resultMap.get("clock"));
         }

         if (!StringUtils.isEmpty((CharSequence)resultMap.get("timer1"))) {
            settingResultByTime.setOn_time((String)resultMap.get("timer1"));
         }

         if (!StringUtils.isEmpty((CharSequence)resultMap.get("timer2"))) {
            settingResultByTime.setOn_time((String)resultMap.get("timer2"));
         }

         if (!StringUtils.isEmpty((CharSequence)resultMap.get("timer3"))) {
            settingResultByTime.setOn_time((String)resultMap.get("timer3"));
         }

         if (!StringUtils.isEmpty((CharSequence)resultMap.get("timer4"))) {
            settingResultByTime.setOn_time((String)resultMap.get("timer4"));
         }

         if (!StringUtils.isEmpty((CharSequence)resultMap.get("timer5"))) {
            settingResultByTime.setOn_time((String)resultMap.get("timer5"));
         }

         if (!StringUtils.isEmpty((CharSequence)resultMap.get("timer6"))) {
            settingResultByTime.setOn_time((String)resultMap.get("timer6"));
         }

         if (!StringUtils.isEmpty((CharSequence)resultMap.get("timer7"))) {
            settingResultByTime.setOn_time((String)resultMap.get("timer7"));
         }

         if (!StringUtils.isEmpty((CharSequence)resultMap.get("holiday"))) {
            settingResultByTime.setOn_time((String)resultMap.get("holiday"));
         }

         if (resultMap != null) {
            responseBody.setStatus("Success");
            responseBody.setItems(settingResultByTime);
            return responseBody;
         } else {
            responseBody.setStatus("Fail");
            return responseBody;
         }
      } catch (NullPointerException var6) {
         responseBody.setStatus("Fail");
         responseBody.setErrorCode(ExceptionCode.RES905[0]);
         responseBody.setErrorMessage(ExceptionCode.RES905[2]);
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public ResponseBody updateSetupInfo(String deviceId, DeviceSystemSetupConfResource param) {
      ResponseBody responseBody = new ResponseBody();
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      String productType = "iPLAYER";
      String requestId = UUID.randomUUID().toString();
      DeviceSystemSetupConf obj = DeviceModelConverter.convertSystemSetupConfToSnakeStyle(param);

      try {
         DeviceSystemSetupConf deviceSystemSetupConf = (DeviceSystemSetupConf)obj.clone();
         deviceSystemSetupConf.setDevice_id(deviceId);
         this.confManager.reqSetSystemSetupToDevice(deviceSystemSetupConf, requestId, productType);
         if (obj.getStatisticsFileRefresh() != null && !obj.getStatisticsFileRefresh().isEmpty()) {
            boolean var22 = false;

            label119: {
               ResponseBody var11;
               try {
                  var22 = true;
                  confMgr.reqGetRequestStatistics(deviceId, "");
                  var22 = false;
                  break label119;
               } catch (Exception var26) {
                  this.logger.error("Error while sending request for refresh of statistic file", var26);
                  responseBody.setStatus("Fail");
                  responseBody.setErrorMessage(var26.getMessage());
                  var11 = responseBody;
                  var22 = false;
               } finally {
                  if (var22) {
                     try {
                        boolean requestTimeExist = this.deviceDao.isRequestTimeExist(deviceId);
                        if (!requestTimeExist) {
                           this.deviceDao.addStatRequestTimeInsertCurrent(deviceId);
                        } else {
                           this.deviceMonitoringDao.setStatRequestTimeByDeviceId(deviceId);
                        }
                     } catch (Exception var23) {
                        this.logger.error("Error while updating requested time of statistic file", var23);
                        responseBody.setStatus("Fail");
                        responseBody.setErrorMessage(var23.getMessage());
                        return responseBody;
                     }

                  }
               }

               try {
                  boolean requestTimeExist = this.deviceDao.isRequestTimeExist(deviceId);
                  if (!requestTimeExist) {
                     this.deviceDao.addStatRequestTimeInsertCurrent(deviceId);
                  } else {
                     this.deviceMonitoringDao.setStatRequestTimeByDeviceId(deviceId);
                  }

                  return var11;
               } catch (Exception var24) {
                  this.logger.error("Error while updating requested time of statistic file", var24);
                  responseBody.setStatus("Fail");
                  responseBody.setErrorMessage(var24.getMessage());
                  return responseBody;
               }
            }

            try {
               boolean requestTimeExist = this.deviceDao.isRequestTimeExist(deviceId);
               if (!requestTimeExist) {
                  this.deviceDao.addStatRequestTimeInsertCurrent(deviceId);
               } else {
                  this.deviceMonitoringDao.setStatRequestTimeByDeviceId(deviceId);
               }
            } catch (Exception var25) {
               this.logger.error("Error while updating requested time of statistic file", var25);
               responseBody.setStatus("Fail");
               responseBody.setErrorMessage(var25.getMessage());
               return responseBody;
            }
         }

         responseBody.setStatus("Success");
         GeneralInfoResource data = new GeneralInfoResource();
         data.setRequestId(requestId);
         responseBody.setItems(data);
         return responseBody;
      } catch (CloneNotSupportedException var28) {
         this.logger.error("", var28);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var28.getMessage());
         return responseBody;
      } catch (Exception var29) {
         this.logger.error("", var29);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var29.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public ResponseBody getUpdatedSetupInfoResult(String deviceId, String requestId) {
      ResponseBody responseBody = new ResponseBody();

      try {
         String productType = "PREMIUM";
         DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
         DeviceSystemSetupConf info = confMgr.getSettingResultBySystemSetup(deviceId, requestId, "SET_DEVICE_SYSTEM_SETUP_CONF");
         if (info == null) {
            responseBody.setStatus("Fail");
            responseBody.setErrorCode(ExceptionCode.RES910[0]);
            responseBody.setErrorMessage(ExceptionCode.RES910[2]);
            return responseBody;
         } else {
            DeviceSystemSetupConfResource systemSetupCamel = this.getDeviceSetupInfoFromDB(deviceId);
            responseBody.setStatus("Success");
            responseBody.setItems(systemSetupCamel);
            return responseBody;
         }
      } catch (Exception var8) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var8.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public ResponseBody updateDisplayInfo(String deviceId, DeviceDisplayConfResource param) throws Exception {
      ResponseBody responseBody = new ResponseBody();
      DeviceDisplayConf deviceDisplayConf = null;
      MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
      Device device = null;
      String deviceModelCode = null;
      String modelKind = null;
      boolean var9 = false;

      try {
         device = this.deviceDao.getDeviceMinInfo(deviceId);
         deviceModelCode = device.getDevice_model_code();
         modelKind = MDCTimeStrUtils.getModelKind(deviceModelCode);
         int var19 = MDCTimeStrUtils.getTimerCnt(deviceModelCode);
      } catch (SQLException var15) {
         this.logger.error("", var15);
      } catch (NullPointerException var16) {
         this.logger.error("", var16);
         responseBody.setStatus("Fail");
         responseBody.setErrorCode(ExceptionCode.RES905[0]);
         responseBody.setErrorMessage(ExceptionCode.RES905[2]);
         return responseBody;
      }

      GeneralInfoResource data = new GeneralInfoResource();
      deviceDisplayConf = DeviceModelConverter.convertDisplayConfToSnakeStyle(param);
      String reuqestId = UUID.randomUUID().toString();

      try {
         deviceDisplayConf.setDevice_id(deviceId);
         this.confManager.reqSetDisplayToDevice(deviceDisplayConf, reuqestId, "ALL_MDC");
         data.setRequestId(reuqestId);
         if (deviceDisplayConf.getBasic_power() != null) {
            if (deviceDisplayConf.getBasic_power().equals("0")) {
               DeviceUtils.setDisconnected(deviceId);
            } else if (deviceDisplayConf.getBasic_power().equals("1")) {
               CurrentPlayingEntity cpe = motMgr.getPlayingContent(deviceId);
               WakeOnLan wol = new WakeOnLan();
               if (cpe == null || cpe != null && cpe.getInputSource() != 1000) {
                  wol.wol(device.getIp_address(), device.getSubnet_mask(), device.getDevice_id());
               }
            }
         }
      } catch (CloneNotSupportedException var17) {
         this.logger.error("", var17);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var17.getMessage());
         return responseBody;
      } catch (Exception var18) {
         this.logger.error("", var18);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var18.getMessage());
         return responseBody;
      }

      responseBody.setItems(data);
      responseBody.setStatus("Success");
      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public ResponseBody getUpdatedDisplayInfoResult(String deviceId, String reuqestId) {
      ResponseBody responseBody = new ResponseBody();

      try {
         DeviceDisplayConf deviceDisplayConf = this.confManager.getSettingResultByDisplay(deviceId, reuqestId, "SET_DEVICE_DISPLAY_CONF");
         if (deviceDisplayConf != null) {
            DeviceDisplayConfResource displayInfoCamel = DeviceModelConverter.convertDisplayInfoToCamelStyle(deviceDisplayConf);
            String inputSourceCode = "20:30:24:12:4:8:32:96:80:31:33:35:49:51:34:36:50:52:37:38:48:64:31:13:14:85:97";
            String inputSourceText = "PC:BNC:DVI:AV:S-Video:Component:MagicInfo:MagicInfo-Lite/S:Plug In Module:DVI_VIDEO:HDMI1:HDMI2:HDMI3:HDMI4:HDMI1_PC:HDMI2_PC:HDMI3_PC:HDMI4_PC:Display_Port:Display_Port2:ATV:DTV:DVI_VIDEO:AV2:Ext:HDBaseT:WiDi";
            String[] codeArr = inputSourceCode.split(":");
            String[] textArr = inputSourceText.split(":");
            List inputSourceList = new ArrayList();

            for(int i = 0; i < codeArr.length; ++i) {
               HashMap inputSource = new HashMap();
               inputSource.put("code", codeArr[i]);
               inputSource.put("text", textArr[i]);
               inputSourceList.add(inputSource);
            }

            displayInfoCamel.setInputSourceList(inputSourceList);
            responseBody.setStatus("Success");
            responseBody.setItems(displayInfoCamel);
         } else {
            responseBody.setErrorCode(ExceptionCode.RES905[0]);
            responseBody.setErrorMessage(ExceptionCode.RES905[2]);
            responseBody.setStatus("Fail");
         }

         return responseBody;
      } catch (SQLException var13) {
         this.logger.error("", var13);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var13.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public ResponseBody updateSecurityInfo(String deviceId, DeviceSecurityConfResource param) {
      ResponseBody responseBody = new ResponseBody();
      DeviceSecurityConf deviceSecurityConf = null;
      GeneralInfoResource data = new GeneralInfoResource();
      deviceSecurityConf = DeviceModelConverter.convertSecurityConfToSnakeStyle(param);
      String reuqestId = UUID.randomUUID().toString();

      try {
         deviceSecurityConf.setDevice_id(deviceId);
         this.confManager.reqSetSecurityToDevice(deviceSecurityConf, reuqestId, "ALL_MDC");
         data.setRequestId(reuqestId);
      } catch (CloneNotSupportedException var8) {
         this.logger.error("", var8);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var8.getMessage());
         return responseBody;
      } catch (Exception var9) {
         this.logger.error("", var9);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var9.getMessage());
         return responseBody;
      }

      responseBody.setItems(data);
      responseBody.setStatus("Success");
      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public ResponseBody updateCabinetInfo(String deviceId, DeviceLedCabinetResource param) {
      ResponseBody responseBody = new ResponseBody();

      try {
         if (deviceId != null && !deviceId.equals("")) {
            DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
            DeviceDisplayConf deviceDisplayConf = new DeviceDisplayConf();
            DeviceDisplayConfManager displayConfMgr = DeviceDisplayConfManagerImpl.getInstance();
            LedCabinet ledCabinet = new LedCabinet();
            boolean sendSetMo = false;
            if (param.getAbl() != null) {
               ledCabinet.setAbl(param.getAbl());
               sendSetMo = true;
            }

            if (param.getInputSource() != null) {
               ledCabinet.setInput_source(param.getInputSource());
               sendSetMo = true;
            }

            if (param.getGamut() != null) {
               ledCabinet.setGamut(param.getGamut());
               sendSetMo = true;
            }

            if (param.getBacklight() != null) {
               ledCabinet.setBacklight(param.getBacklight());
               sendSetMo = true;
            }

            if (param.getPixelRgbCc() != null) {
               ledCabinet.setPixel_rgb_cc(param.getPixelRgbCc());
               sendSetMo = true;
            }

            if (param.getModuleRgbCc() != null) {
               ledCabinet.setModule_rgb_cc(param.getModuleRgbCc());
               sendSetMo = true;
            }

            if (param.getEdgeCorrection() != null) {
               ledCabinet.setEdge_correction(param.getEdgeCorrection());
               sendSetMo = true;
            }

            DeviceDisplayConf conf;
            if (param.getChildAlarmTemperature() != null) {
               conf = new DeviceDisplayConf();
               conf.setDevice_id(deviceId);
               conf.setChild_alarm_temperature(param.getChildAlarmTemperature());
               displayConfMgr.setDeviceDisplayConf(conf);
            }

            String reuqestId;
            if (deviceDisplayConf.getAuto_brightness() != null && deviceId != null && !deviceId.equals("")) {
               conf = new DeviceDisplayConf();
               reuqestId = UUID.randomUUID().toString();
               conf.setDevice_id(deviceId);
               conf.setAuto_brightness(deviceDisplayConf.getAuto_brightness());
               confManager.reqSetDisplayToDevice(deviceDisplayConf, reuqestId, "ADVANCED_MDC");
            }

            if (sendSetMo) {
               List childIds = new ArrayList();
               reuqestId = UUID.randomUUID().toString();
               if (param.getChildIdList() != null && param.getChildIdList().size() != 0) {
                  ledCabinet.setParent_device_id(deviceId);

                  for(int i = 1; i <= 4; ++i) {
                     childIds.clear();
                     Iterator var12 = param.getChildIdList().iterator();

                     while(var12.hasNext()) {
                        String temp = (String)var12.next();
                        if (temp.split("-")[0].equals(i + "")) {
                           childIds.add(temp);
                        }
                     }

                     if (childIds.size() > 0) {
                        confManager.reqSetCabinetConfToDevice(ledCabinet, childIds, reuqestId, "CABINET_MDC");
                     }
                  }
               } else {
                  ledCabinet.setParent_device_id(deviceId);
                  confManager.reqSetCabinetConfToDevice(ledCabinet, (List)null, reuqestId, "CABINET_MDC");
               }
            }

            responseBody.setStatus("Success");
         } else {
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage("deviceId is null");
         }
      } catch (Exception var15) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var15.getMessage());
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public ResponseBody getDevice(String deviceId) {
      ResponseBody responseBody = new ResponseBody();
      DeviceResource results = new DeviceResource();
      LinkedHashMap datas = new LinkedHashMap();

      try {
         Device device = this.deviceDao.getMonitoringViewDevice(deviceId);
         if (device == null) {
            responseBody.setStatus("Fail");
            responseBody.setErrorCode(ExceptionCode.RES905[0]);
            responseBody.setErrorMessage(ExceptionCode.RES905[2]);
            return responseBody;
         }

         Map menu = new LinkedHashMap();
         results.setDeviceId(device.getDevice_id());
         results.setIpAddress(device.getIp_address());
         results.setDeviceName(device.getDevice_name());
         results.setDeviceType(device.getDevice_type());
         results.setDeviceModelName(device.getDevice_model_name());
         results.setSoftwareUpdateVersion(device.getSoftwareUpdateVersion());
         results.setFirmwareVersion(device.getFirmware_version());
         results.setApplicationVersion(device.getApplication_version());
         results.setDiskSpaceRepository(device.getDisk_space_repository());
         results.setMapLocation(device.getMap_location());
         results.setRmRuleVersion(device.getRm_rule_version());
         String groupName = "";
         List groups = DeviceUtils.getGroupNamePath(device.getGroup_id().intValue());
         if (groups != null && groups.size() > 0) {
            for(int i = 0; i < groups.size(); ++i) {
               if (!groupName.equals("")) {
                  groupName = groupName + " > ";
               }

               groupName = groupName + ((DeviceGroup)groups.get(i)).getGroup_name();
            }
         }

         results.setDeviceGroupName(groupName);
         results.setDeviceTypeVersion(device.getDevice_type_version());
         String diskAvailable = "-";
         if (device.getDisk_space_repository() != null && device.getDisk_space_repository() > 0L) {
            if (device.getDisk_space_repository() > 1073741824L) {
               diskAvailable = device.getDisk_space_repository() / 1073741824L + "GB";
            } else {
               diskAvailable = device.getDisk_space_repository() / 1048576L + "MB";
            }
         }

         menu.put("diskAvailable", diskAvailable);
         datas.put("menu", menu);
         String[] deviceIdList = new String[]{deviceId};
         List deviceList = this.deviceDao.getMonitoringInfoByDeviceIdList(deviceIdList);
         String captureUrl = "/image/img/thumb_img_power.png";
         String thumbUrl = "/image/img/thumb_img_power.png";

         try {
            DeviceMonitoring monitoring = (DeviceMonitoring)deviceList.get(0);
            HashMap resultMap = DeviceUtils.getDeviceStatusInfos("", monitoring, false);
            captureUrl = (String)resultMap.get("captureUrl");
            thumbUrl = (String)resultMap.get("thumbUrl");
         } catch (Exception var45) {
            this.logger.error("", var45);
         }

         results.setCaptureUrl(captureUrl);
         results.setThumbUrl(thumbUrl);
         if (device.getScreen_rotation() != null && device.getScreen_rotation() != 0L) {
            results.setLandscape("portrait");
         } else {
            results.setLandscape("landscape");
         }

         String isDefaultContentSchedule = "N";
         CurrentPlayingEntity playingEntity = this.monMgr.getPlayingContent(deviceId);

         try {
            if (playingEntity != null) {
               results.setCurrentChannel(playingEntity.getContentChannel());
            }

            isDefaultContentSchedule = this.schInfo.getProgram(playingEntity.getProgramId()).getIs_default();
         } catch (Exception var44) {
         }

         results.setPower(this.monMgr.isConnected(deviceId));
         if (device.getLast_connection_time() != null) {
            results.setLastConnectionTime(TimeUtil.getGMTTime(device.getLast_connection_time()));
         }

         String messageScheduleCreateDate = "-";
         if (playingEntity != null && playingEntity.getMessageScheduleId() != null) {
            try {
               messageScheduleCreateDate = this.msgInfo.getMessage(playingEntity.getMessageScheduleId(), 0).getCreate_date().toString();
            } catch (Exception var43) {
            }
         }

         if (playingEntity != null && playingEntity.getEventScheduleId() != null && playingEntity.getEventScheduleId().equals("00000000-0000-0000-0000-000000000000")) {
            try {
               List eList = this.schInfo.getEventList(playingEntity.getEventScheduleId());
               results.seteList(eList);
            } catch (Exception var42) {
            }
         }

         if (playingEntity != null) {
            results.setContentScheduleName(playingEntity.getProgramName());
            results.setContentScheduleId(playingEntity.getProgramId());
            results.setDefaultContentSchedule(isDefaultContentSchedule);
            results.setMessageScheduleName(playingEntity.getMessageScheduleName());
            results.setMessageScheduleId(playingEntity.getMessageScheduleId());
            results.setMessageScheduleCreateDate(messageScheduleCreateDate);
            results.setEventScheduleName(playingEntity.getEventScheduleName());
            results.setEventScheduleId(playingEntity.getEventScheduleId());
         }

         datas.put("playingContentScheduleList", this.getNowPlayingContentList(deviceId));
         datas.put("contentDownloadStatus", this.getDownloadStatus("content", deviceId));
         datas.put("eventDownloadStatus", this.getDownloadStatus("event", deviceId));
         ClientFaultEntity playerError = this.monMgr.getClientFaultStatus(device.getDevice_id());
         List hwSwError = this.alarmManager.getClientFaultListByDeviceId(device.getDevice_id());
         ClientFaultEntity cfe;
         if (playerError != null && playerError.getFaultMapList() != null && playerError.getFaultMapList().size() > 0) {
            cfe = new ClientFaultEntity();
            ArrayList playerErrorList = playerError.getFaultMapList();
            Iterator var21 = playerErrorList.iterator();

            while(var21.hasNext()) {
               Map errorMap = (Map)var21.next();
               String errorStr = null;
               if (errorMap.containsKey("SCH")) {
                  errorStr = (String)errorMap.get("SCH");
               } else if (errorMap.containsKey("EVT")) {
                  errorStr = (String)errorMap.get("EVT");
               } else if (errorMap.containsKey("NET")) {
                  errorStr = (String)errorMap.get("NET");
               }

               if (errorStr != null) {
                  String[] errorStringArr = errorStr.split(";");
                  if (errorStringArr != null && errorStringArr.length > 0) {
                     cfe.setCode(errorStringArr[1]);
                     cfe.setClient_time(errorStringArr[3]);
                     cfe.setServer_time(Timestamp.valueOf(errorStringArr[5]));
                     hwSwError.add(cfe);
                  }
               }
            }
         }

         if (hwSwError != null && hwSwError.size() > 0) {
            cfe = null;

            try {
               Collections.sort(hwSwError, new ServerTimeDescCompare());
            } catch (Exception var41) {
               this.logger.error("", var41);
            }

            List hwSwErrorLatestList = new ArrayList(hwSwError.subList(0, hwSwError.size() > 5 ? 5 : hwSwError.size()));
            Iterator var53 = hwSwErrorLatestList.iterator();

            while(true) {
               if (!var53.hasNext()) {
                  datas.put("errorList", hwSwErrorLatestList);
                  break;
               }

               ClientFaultEntity cfe = (ClientFaultEntity)var53.next();
               String error_script = this.alarmManager.getErrorScript(cfe.getCode());
               if (error_script == null || error_script.equals("") || error_script.equalsIgnoreCase("null")) {
                  error_script = "Invalid Error Code";
               }

               cfe.setBody_format(error_script + "(" + cfe.getCode() + ")");
               cfe.setClient_time(cfe.getClient_time().replace("T", " "));
            }
         }

         Map logCollect = new HashMap();
         String logCollectConfig = StrUtils.nvl(CommonConfig.get("device.log_collect"));
         Map errorMap;
         String key;
         String childId;
         String panelStatus;
         File logFileFolder;
         ArrayList collectedLogList;
         DeviceLogCollectEntity logentity;
         if (logCollectConfig != null && logCollectConfig.equalsIgnoreCase("true") && device.getDevice_type().equalsIgnoreCase("SPLAYER") && (double)device.getDevice_type_version() >= 3.0D && device.getSupport_flag() != null && device.getSupport_flag().length() > 3 && device.getSupport_flag().charAt(2) == '1') {
            String countConfig = StrUtils.nvl(CommonConfig.get("device.log_collect.count"));
            int supportedCnt = 1;
            int activeCnt = false;
            if (countConfig != null) {
               supportedCnt = Integer.parseInt(countConfig);
            }

            int activeCnt = this.deviceDao.getLogProcessingDeviceCnt();
            List deviceLogCollectEntityList = this.deviceDao.getDeviceLogProcessInfo(deviceId);
            Iterator var25 = deviceLogCollectEntityList.iterator();

            while(var25.hasNext()) {
               DeviceLogCollectEntity logEntity = (DeviceLogCollectEntity)var25.next();
               if (logEntity != null && !logEntity.getStatus().equalsIgnoreCase("END")) {
                  Map entityMap = new HashMap();
                  entityMap.put("script", logEntity.getCategory_script());
                  entityMap.put("startTime", logEntity.getStart_time());
                  entityMap.put("duration", logEntity.getDuration());
                  entityMap.put("size", logEntity.getPacket_size());
                  errorMap = DeviceConstants.getDeviceLogCollectError();
                  if (errorMap != null && errorMap.containsKey(logEntity.getStatus())) {
                     key = logEntity.getStatus();
                     entityMap.put("status", key + " (" + (String)errorMap.get(key) + ")");
                  }

                  logCollect.put("collectInfo", entityMap);
               }
            }

            logCollect.put("isEnable", Boolean.valueOf(logCollectConfig));
            logCollect.put("availableCnt", supportedCnt - activeCnt);
            String homePath = CommonConfig.get("CONTENTS_HOME");
            String homeDir = "C:\\";
            if (homePath != null) {
               homeDir = homePath.split(":")[0] + ":\\";
            }

            File[] roots = File.listRoots();
            long freeSizeLong = 0L;

            for(int i = 0; roots != null && i < roots.length; ++i) {
               if (roots[i].getPath().equalsIgnoreCase(homeDir) && roots[i].getTotalSpace() > 0L) {
                  freeSizeLong = roots[i].getFreeSpace();
               }
            }

            logCollect.put("freeSize", freeSizeLong);
            logentity = null;
            if (deviceLogCollectEntityList != null && !deviceLogCollectEntityList.isEmpty()) {
               logentity = (DeviceLogCollectEntity)deviceLogCollectEntityList.get(0);
            }

            if (logentity != null) {
               childId = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "device_log";
               panelStatus = childId + File.separator + deviceId;
               logFileFolder = SecurityUtils.getSafeFile(panelStatus);
               collectedLogList = new ArrayList();
               SimpleDateFormat df = new SimpleDateFormat("yyyy-mm-dd HH-mm-ss");
               if (logFileFolder.exists()) {
                  File[] files = logFileFolder.listFiles();
                  if (files != null && files.length > 0) {
                     for(int i = 0; i < files.length; ++i) {
                        HashMap fileInfo;
                        Date today;
                        String modifiedDate;
                        if (logentity.getStatus().equalsIgnoreCase("END")) {
                           if (files[i].getName() != null && !DiagnosisConstants.key.equalsIgnoreCase(files[i].getName()) && !DiagnosisConstants.diagnosticKey.equalsIgnoreCase(files[i].getName())) {
                              fileInfo = new HashMap();
                              today = new Date(files[i].lastModified());
                              modifiedDate = df.format(today);
                              fileInfo.put("fileName", files[i].getName());
                              fileInfo.put("modifiedDate", modifiedDate);
                              fileInfo.put("size", files[i].length());
                              collectedLogList.add(fileInfo);
                           }
                        } else if (files[i].length() >= 10485760L && files[i].getName() != null && !files[i].getName().equalsIgnoreCase(DiagnosisConstants.key) && !files[i].getName().equalsIgnoreCase(DiagnosisConstants.diagnosticKey)) {
                           fileInfo = new HashMap();
                           today = new Date(files[i].lastModified());
                           modifiedDate = df.format(today);
                           fileInfo.put("fileName", files[i].getName());
                           fileInfo.put("modifiedDate", modifiedDate);
                           fileInfo.put("size", files[i].length());
                           collectedLogList.add(fileInfo);
                        }
                     }
                  }
               }

               logCollect.put("collectedLogList", collectedLogList);
            }
         } else {
            logCollect.put("isEnable", Boolean.valueOf("false"));
         }

         results.setHasChild(device.getHas_child());
         MonitoringManager mgr = MonitoringManagerImpl.getInstance();
         if ((device.getDevice_type().equalsIgnoreCase("SIGNAGE") || device.getDevice_type().equalsIgnoreCase("RSIGNAGE")) && device.getHas_child() != null && device.getHas_child()) {
            List childDeviceIdList = DeviceUtils.getChildDeviceIdList(deviceId, device.getChild_cnt());
            long child_cnt = (long)childDeviceIdList.size();
            long signage_mon_interval = 60L;
            DeviceDisplayConfManager displayConfDao = DeviceDisplayConfManagerImpl.getInstance();
            errorMap = null;
            key = null;
            logentity = null;
            childId = null;
            panelStatus = null;
            logFileFolder = null;
            collectedLogList = null;
            ArrayList slaveDeviceListMap = new ArrayList();
            if (childDeviceIdList != null && child_cnt > 0L) {
               for(int i = 0; (long)i < child_cnt; ++i) {
                  LinkedHashMap tmpchild = new LinkedHashMap();
                  key = (String)childDeviceIdList.get(i);
                  Device childDevice = this.deviceDao.getDevice(key);
                  DeviceDisplayConf displayConf = displayConfDao.getDeviceDisplayConf(key);
                  String parnetId = childDevice.getDevice_id().split("_")[0];
                  childId = childDevice.getDevice_id().split("_")[1];
                  boolean isConnectedChild = mgr.isConnected(key);
                  if (isConnectedChild) {
                     if (displayConf.getBasic_panel_status() == 1L) {
                        panelStatus = "0";
                     } else {
                        panelStatus = "1";
                     }
                  } else {
                     panelStatus = "-";
                  }

                  String lastScannedTime;
                  if (childDevice.getLast_connection_time() != null && childDevice.getLast_connection_time().getTime() != (new Timestamp(0L)).getTime()) {
                     lastScannedTime = childDevice.getLast_connection_time().toString();
                  } else {
                     lastScannedTime = null;
                  }

                  tmpchild.put("childId", childId);
                  tmpchild.put("isConnected", isConnectedChild);
                  tmpchild.put("panelStatus", panelStatus);
                  if (displayConf.getBasic_source() != null) {
                     tmpchild.put("inputSource", displayConf.getBasic_source().toString());
                  }

                  tmpchild.put("lastScannedTime", lastScannedTime);
                  slaveDeviceListMap.add(tmpchild);
               }
            }

            results.setSlaveDeviceListMap(slaveDeviceListMap);
         }

         results.setLogCollect(logCollect);
         results.setResult(datas);
      } catch (Exception var46) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var46.toString());
         this.logger.error("", var46);
         return responseBody;
      }

      responseBody.setStatus("Success");
      responseBody.setItems(results);
      return responseBody;
   }

   private List getDownloadStatus(String type, String deviceId) throws SQLException {
      Map condition = new HashMap();
      condition.put("device_id", deviceId);
      condition.put("dir", "asc");
      condition.put("sort", "content_name");
      String program_id = this.deviceDao.getProgramIdByDeviceId(deviceId);
      SimpleDateFormat sf;
      if (program_id != null) {
         ProgramEntity programEntity = this.scheduleDao.getProgram(program_id);
         if (programEntity != null) {
            sf = new SimpleDateFormat("yyyy-MM-dd");
            if (programEntity.getLastdeploy_date() != null) {
               String currDateStr = sf.format(programEntity.getLastdeploy_date());
               condition.put("stop_date", currDateStr);
            }
         }
      }

      condition.put("program_id", program_id);
      List downloadStatus = new ArrayList();
      sf = null;
      List list;
      if (type.equalsIgnoreCase("event")) {
         list = this.scheduleDao.getDownloadStatusForEventSchedule(0, 1000, condition);
      } else {
         list = this.scheduleDao.getDownloadStatusForContentSchedule(0, 1000, condition);
      }

      if (list != null && list.size() > 0) {
         for(int i = 0; i < list.size(); ++i) {
            Map tmpMap = new HashMap();
            DownloadContentEntity tmp = (DownloadContentEntity)list.get(i);
            tmpMap.put("contentId", tmp.getContent_id());
            tmpMap.put("contentName", tmp.getContent_name());
            tmpMap.put("totalSize", tmp.getTotal_size());
            if (tmp.getProgress() != null) {
               tmpMap.put("progress", tmp.getProgress());
            } else {
               tmpMap.put("progress", "0%");
            }

            downloadStatus.add(tmpMap);
         }
      }

      return downloadStatus;
   }

   private List getNowPlayingContentList(String deviceId) {
      List nowPlayingContentList = new ArrayList();
      List rtnList = new ArrayList();
      List srcList = null;

      try {
         srcList = this.deviceDao.getMonitoringInfoListByDeviceId(deviceId);
      } catch (SQLException var16) {
         this.logger.error("", var16);
      }

      List playingContentList = null;
      CurrentPlayingEntity playingEntity = this.monMgr.getPlayingContent(deviceId);
      boolean isConnected = this.monMgr.isConnected(deviceId);
      if (isConnected && playingEntity != null) {
         if (playingEntity.getActiveType() != null && playingEntity.getActiveType().equalsIgnoreCase("event")) {
            playingContentList = playingEntity.getEventScheduleContentLists();
         } else {
            playingContentList = playingEntity.getContentLists();
         }
      }

      for(int i = 0; srcList != null && i < srcList.size(); ++i) {
         DeviceMonitoring monitoring = (DeviceMonitoring)srcList.get(i);
         boolean check = false;

         for(int j = 0; playingContentList != null && j < playingContentList.size(); ++j) {
            ContentList playingContent = (ContentList)playingContentList.get(j);
            if (monitoring.getFrame_index().toString().equals(playingContent.getFrameIndex()) && !check) {
               check = true;
               Content content = null;

               try {
                  content = this.contentDao.getThumbInfoOfActiveVersion(playingContent.getContentId());
               } catch (SQLException var15) {
                  this.logger.error("", var15);
               }

               if (content != null) {
                  monitoring.setContent_id(playingContent.getContentId());
                  monitoring.setContent_name(StrUtils.nvl(content.getContent_name()));
                  monitoring.setMedia_type(StrUtils.nvl(content.getMedia_type()));
                  monitoring.setVersion_id(content.getVersion_id());
                  monitoring.setCreate_date(content.getCreate_date());
                  monitoring.setThumb_file_id(content.getThumb_file_id());
                  monitoring.setThumb_file_name(content.getThumb_file_name());
                  monitoring.setFrame_name(playingContent.getFrameName());
               }
               break;
            }
         }

         rtnList.add(monitoring);
      }

      Comparator frameNameComparator = new Comparator() {
         public int compare(DeviceMonitoring d1, DeviceMonitoring d2) {
            return d1.getFrame_name() != null && d2.getFrame_name() != null ? d1.getFrame_name().compareTo(d2.getFrame_name()) : 0;
         }
      };
      Collections.sort(rtnList, frameNameComparator);
      Map frame = new HashMap();
      if (isConnected && playingEntity != null) {
         Iterator var19 = rtnList.iterator();

         while(var19.hasNext()) {
            DeviceMonitoring tempMonitoring = (DeviceMonitoring)var19.next();
            if (tempMonitoring.getContent_name() != null && !frame.containsKey(tempMonitoring.getContent_name())) {
               frame.put(tempMonitoring.getContent_name(), tempMonitoring.getContent_name());
               Map playingContentSchedule = new LinkedHashMap();
               playingContentSchedule.put("playingSchFrameName", tempMonitoring.getFrame_name());
               playingContentSchedule.put("playingSchContentName", tempMonitoring.getContent_name());
               playingContentSchedule.put("playingSchContentId", tempMonitoring.getContent_id());
               playingContentSchedule.put("playingSchMediaType", tempMonitoring.getMedia_type());
               playingContentSchedule.put("playingSchCreateDate", tempMonitoring.getCreate_date());
               nowPlayingContentList.add(playingContentSchedule);
            }
         }
      }

      return nowPlayingContentList;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public ResponseBody getCurrentStatusTime(String deviceId, String requestId) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      GeneralInfoResource data = new GeneralInfoResource();
      DeviceTimeConf info = null;

      try {
         DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
         Map resultMap = null;
         Device device = this.deviceDao.getDeviceMinInfo(deviceId);
         String deviceModelCode = device.getDevice_model_code();
         String modelKind = MDCTimeStrUtils.getModelKind(deviceModelCode);
         int timerCnt = MDCTimeStrUtils.getTimerCnt(deviceModelCode);
         if (requestId == null) {
            String sessionId = UUID.randomUUID().toString();
            confMgr.reqGetTimeFromDevice(deviceId, sessionId);
            data.setRequestId(sessionId);
            responseBody.setItems(data);
            responseBody.setStatus("Success");
            return responseBody;
         } else {
            info = confMgr.getTimeResultSet(deviceId, requestId, "GET_DEVICE_TIME_CONF");
            if (info != null) {
               if ("NEW".equalsIgnoreCase(modelKind)) {
                  info.fillTimeClock();
                  info.fillTimerValues();
               } else {
                  info = this.deviceConf.getDeviceOldTimeConf(deviceId);
               }

               responseBody.setStatus("Success");
               DeviceTimeconfResource timeConfCamel = DeviceModelConverter.convertTimeinfoToCamelStyle(info);
               timeConfCamel.setDeviceId(deviceId);
               timeConfCamel.setTimerCount(timerCnt);
               List tmpHolidayList = this.deviceConf.getDeviceTimeHolidayConf(deviceId);
               if (tmpHolidayList != null && tmpHolidayList.size() > 0) {
                  for(int i = 0; i < tmpHolidayList.size(); ++i) {
                     DeviceTimeHolidayConf tmp = (DeviceTimeHolidayConf)tmpHolidayList.get(i);
                     DeviceTimeHolidayResource holiday = new DeviceTimeHolidayResource();
                     holiday.setDeviceId(tmp.getDevice_id());
                     holiday.setMonth1(StrUtils.getLeftFilledString(tmp.getMonth1(), "0", 2));
                     holiday.setMonth2(StrUtils.getLeftFilledString(tmp.getMonth2(), "0", 2));
                     holiday.setDay1(StrUtils.getLeftFilledString(tmp.getDay1(), "0", 2));
                     holiday.setDay2(StrUtils.getLeftFilledString(tmp.getDay2(), "0", 2));
                     holiday.setHolidaySeq(0);
                     timeConfCamel.addDeviceTimeHolidayResource(holiday);
                  }
               }

               String inputSourceCode = "20:30:24:12:4:8:32:96:80:31:33:35:49:51:34:36:50:52:37:38:48:64:31:13:14:85:97";
               String inputSourceText = "PC:BNC:DVI:AV:S-Video:Component:MagicInfo:MagicInfo-Lite/S:Plug In Module:DVI_VIDEO:HDMI1:HDMI2:HDMI3:HDMI4:HDMI1_PC:HDMI2_PC:HDMI3_PC:HDMI4_PC:Display_Port:Display_Port2:ATV:DTV:DVI_VIDEO:AV2:Ext:HDBaseT:WiDi";
               String[] codeArr = inputSourceCode.split(":");
               String[] textArr = inputSourceText.split(":");
               List inputSourceList = new ArrayList();

               for(int i = 0; i < codeArr.length; ++i) {
                  HashMap inputSource = new HashMap();
                  inputSource.put("code", codeArr[i]);
                  inputSource.put("text", textArr[i]);
                  inputSourceList.add(inputSource);
               }

               timeConfCamel.setInputSourceList(inputSourceList);
               responseBody.setItems(timeConfCamel);
            } else {
               responseBody.setErrorMessage(ExceptionCode.SVC823[2]);
               responseBody.setStatus("Fail");
            }

            return responseBody;
         }
      } catch (NullPointerException var21) {
         this.logger.error("", var21);
         responseBody.setStatus("Fail");
         responseBody.setErrorCode(ExceptionCode.RES905[0]);
         responseBody.setErrorMessage(ExceptionCode.RES905[2]);
         return responseBody;
      } catch (Exception var22) {
         this.logger.error("", var22);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var22.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public ResponseBody getCurrentStatusDisplay(String deviceId, String requestId) {
      ResponseBody responseBody = new ResponseBody();
      GeneralInfoResource data = new GeneralInfoResource();
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      DeviceDisplayConf info = null;
      if (requestId == null) {
         String sessionId = UUID.randomUUID().toString();

         try {
            confMgr.reqGetDisplayFromDevice(deviceId, sessionId, "ALL_MDC");
            data.setRequestId(sessionId);
            responseBody.setStatus("Success");
            responseBody.setItems(data);
         } catch (Exception var17) {
            this.logger.error("", var17);
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage(var17.getMessage());
         }
      } else {
         info = confMgr.getDisplayResultSet(deviceId, requestId, "GET_DEVICE_DISPLAY_CONF", "ALL_MDC");
         if (info == null) {
            responseBody.setStatus("Fail");
            responseBody.setErrorCode(ExceptionCode.RES910[0]);
            responseBody.setErrorMessage(ExceptionCode.RES910[2]);
         } else {
            DeviceDisplayConfManager displayDao = DeviceDisplayConfManagerImpl.getInstance("PREMIUM");

            try {
               DeviceDisplayConf deviceDisplayConf = displayDao.getDeviceDisplayConf(deviceId);
               DeviceDisplayConfResource displayInfoCamel = DeviceModelConverter.convertDisplayInfoToCamelStyle(deviceDisplayConf);
               displayInfoCamel.setDeviceId(deviceId);
               String inputSourceCode = "20:30:24:12:4:8:32:96:80:31:33:35:49:51:34:36:50:52:37:38:48:64:31:13:14:85:97";
               String inputSourceText = "PC:BNC:DVI:AV:S-Video:Component:MagicInfo:MagicInfo-Lite/S:Plug In Module:DVI_VIDEO:HDMI1:HDMI2:HDMI3:HDMI4:HDMI1_PC:HDMI2_PC:HDMI3_PC:HDMI4_PC:Display_Port:Display_Port2:ATV:DTV:DVI_VIDEO:AV2:Ext:HDBaseT:WiDi";
               String[] codeArr = inputSourceCode.split(":");
               String[] textArr = inputSourceText.split(":");
               List inputSourceList = new ArrayList();

               for(int i = 0; i < codeArr.length; ++i) {
                  HashMap inputSource = new HashMap();
                  inputSource.put("code", codeArr[i]);
                  inputSource.put("text", textArr[i]);
                  inputSourceList.add(inputSource);
               }

               displayInfoCamel.setInputSourceList(inputSourceList);
               boolean isPcMode = DeviceUtils.checkPcMode(displayInfoCamel.getBasicSource());
               displayInfoCamel.setPcMode(isPcMode);
               responseBody.setStatus("Success");
               responseBody.setItems(displayInfoCamel);
            } catch (SQLException var18) {
               this.logger.error("", var18);
               responseBody.setStatus("Fail");
               responseBody.setErrorMessage(var18.getMessage());
               return responseBody;
            }
         }
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public ResponseBody getCurrentStatusSecurity(String deviceId, String requestId) {
      ResponseBody responseBody = new ResponseBody();
      GeneralInfoResource data = new GeneralInfoResource();
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      DeviceSecurityConf info = null;
      if (requestId == null) {
         String sessionId = UUID.randomUUID().toString();

         try {
            confMgr.reqGetSecurityFromDevice(deviceId, sessionId, "ALL_MDC");
            data.setRequestId(sessionId);
            responseBody.setStatus("Success");
            responseBody.setItems(data);
         } catch (Exception var11) {
            this.logger.error("", var11);
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage(var11.getMessage());
         }
      } else {
         info = confMgr.getSecurityResultSet(deviceId, requestId, "GET_DEVICE_SECURITY_CONF", "ALL_MDC");
         if (info == null) {
            responseBody.setStatus("Fail");
            responseBody.setErrorCode(ExceptionCode.RES910[0]);
            responseBody.setErrorMessage(ExceptionCode.RES910[2]);
         } else {
            DeviceSecurityConfManager securityDao = DeviceSecurityConfManagerImpl.getInstance("PREMIUM");

            try {
               DeviceSecurityConf deviceSecurityConf = securityDao.getDeviceSecurityConf(deviceId);
               DeviceSecurityConfResource securityInfoCamel = DeviceModelConverter.convertSecurityInfoToCamelStyle(deviceSecurityConf);
               securityInfoCamel.setDeviceId(deviceId);
               responseBody.setStatus("Success");
               responseBody.setItems(securityInfoCamel);
            } catch (SQLException var10) {
               this.logger.error("", var10);
               responseBody.setStatus("Fail");
               responseBody.setErrorMessage(var10.getMessage());
               return responseBody;
            }
         }
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public ResponseBody getUnapprovedDeviceList(DeviceFilter params) {
      ResponseBody responseBody = new ResponseBody();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String result = null;
      String sort = "create_date";
      String dir = "asc";
      int startIndex = 1;
      int results = 100;
      String device_type = null;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      ArrayList unapprovedList = new ArrayList();

      try {
         ListManager listMgr = new ListManager(deviceDao, "list");
         listMgr.addSearchInfo("sort", sort);
         listMgr.addSearchInfo("dir", dir);
         listMgr.addSearchInfo("device_type", params.getDeviceType());
         String searchText = params.getCommonSearchKeyword() != null ? params.getCommonSearchKeyword().toUpperCase() : "";
         searchText = searchText.replaceAll("\\[", "^[");
         searchText = searchText.replaceAll("]", "^]");
         searchText = searchText.replaceAll("%", "^%");
         listMgr.addSearchInfo("src_name", searchText);
         listMgr.setLstSize(1000000);
         listMgr.setSection("getNonApprovedDevice");
         listMgr.addSearchInfo("license_count", DeviceUtils.getRemainedLicenseCount("iPLAYER"));
         listMgr.addSearchInfo("sPlayer_license_count", DeviceUtils.getRemainedLicenseCount("SPLAYER"));
         listMgr.addSearchInfo("signage_license_count", DeviceUtils.getRemainedLicenseCount("SIGNAGE3"));
         listMgr.addSearchInfo("android_license_count", DeviceUtils.getRemainedLicenseCount("APLAYER"));
         listMgr.addSearchInfo("wplayer_license_count", DeviceUtils.getRemainedLicenseCount("WPLAYER"));
         listMgr.addSearchInfo("lite_license_count", DeviceUtils.getRemainedLicenseCount("LPLAYER"));
         listMgr.addSearchInfo("rms_license_count", DeviceUtils.getRemainedLicenseCount("RMS"));
         PageManager pageMgr = null;
         List deviceList = listMgr.dbexecute();
         pageMgr = listMgr.getPageManager();
         String sort_name = "create_date";
         String order_dir = "desc";
         int endIndex = startIndex + results;
         if (endIndex > pageMgr.getTotalRowCount()) {
            endIndex = pageMgr.getTotalRowCount();
         }

         boolean isFirst = true;

         for(int i = startIndex - 1; i < endIndex; ++i) {
            DeviceMonitoringResource tempResource = new DeviceMonitoringResource();
            Map hash = (Map)deviceList.get(i);
            tempResource.setDeviceId(hash.get("device_id").toString());
            tempResource.setDeviceType(hash.get("device_type").toString());
            tempResource.setDeviceTypeVersion(Float.parseFloat(hash.get("device_type_version").toString()));
            tempResource.setDeviceModelName(hash.get("device_model_name").toString());
            tempResource.setIpAddress(StrUtils.nvl((String)hash.get("ip_address")));
            tempResource.setCreateDate((Timestamp)hash.get("create_date"));
            String supportFlag = (String)hash.get("support_flag");
            boolean supportUhd = false;
            if (supportFlag != null && supportFlag.length() >= 4 && supportFlag.charAt(3) == '1') {
               supportUhd = true;
            }

            tempResource.setSupportUhd(supportUhd);
            String deviceName = StrUtils.nvl((String)hash.get("device_name"));
            if (deviceName.length() < 17) {
               tempResource.setDeviceName(deviceName);
            } else {
               tempResource.setDeviceName(deviceName.substring(0, 16));
            }

            unapprovedList.add(i, tempResource);
         }

         responseBody.setStatus("Success");
         responseBody.setStartIndex(params.getStart_index());
         responseBody.setPageSize(params.getPage_size());
         responseBody.setItems(unapprovedList);
      } catch (Exception var26) {
         this.logger.error("", var26);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var26.getMessage());
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public ResponseBody getCabinetList(String deviceId, DeviceFilter params) {
      ResponseBody responseBody = new ResponseBody();

      try {
         LedCabinetConfManager ledInfo = LedCabinetConfManagerImpl.getInstance();
         Map condition = new HashMap();
         condition.put("device_id", deviceId);
         condition.put("sort_name", "");
         condition.put("order_dir", "desc");
         List ledCabinetList = ledInfo.getLedCabinetPagedList(-1, -1, condition);
         int listSize = ledInfo.getLedCabinetCount(deviceId);
         List camelCabinetList = DeviceModelConverter.convertCabinetInfoToCamelStyle(ledCabinetList);
         List convertedCabinetList = new ArrayList();
         Iterator var10 = camelCabinetList.iterator();

         while(var10.hasNext()) {
            Map camelCabinet = (Map)var10.next();
            CamelCabinetGroup camelCabinetGroup = new CamelCabinetGroup();
            camelCabinetGroup.setCabinetList((List)camelCabinet.get("cabinetList"));
            camelCabinetGroup.setCabinetGroupId((Long)camelCabinet.get("cabinetGroupId"));
            convertedCabinetList.add(camelCabinetGroup);
         }

         responseBody.setItems(convertedCabinetList);
         responseBody.setStartIndex(params.getStart_index());
         responseBody.setPageSize(params.getPage_size());
         responseBody.setTotalCount(listSize);
         responseBody.setStatus("Success");
      } catch (Exception var13) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var13.getMessage());
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public ResponseBody getRmServerVnc(String deviceId, String command, HttpServletRequest request) throws Exception {
      ResponseBody responseBody = new ResponseBody();
      GeneralInfoResource result = new GeneralInfoResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String keyStorePath = CommonConfig.get("keystore.identity.path");
      String userId = null;
      String productType = "RmServer";
      String token = null;
      DocumentBuilderFactory factory = DocumentUtils.getDocumentBuilderFactoryInstance();
      DocumentBuilder builder = factory.newDocumentBuilder();
      if (deviceId == null) {
         return null;
      } else {
         DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
         Device device = deviceDao.getDevice(deviceId);
         MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
         CurrentPlayingEntity playingEntity = null;
         playingEntity = motMgr.getPlayingContent(deviceId);
         if (!motMgr.isConnected(deviceId)) {
            responseBody.setErrorCode("9998");
            responseBody.setErrorMessage("Device is disconnected ");
            responseBody.setStatus("Fail");
            return responseBody;
         } else {
            HttpSession session = request.getSession();
            session.setMaxInactiveInterval(1800);
            if (userContainer == null) {
               return null;
            } else {
               userId = userContainer.getUser().getUser_id();
               if (!StrUtils.nvl(CommonConfig.get("saas.no_token.enable")).equalsIgnoreCase("TRUE")) {
                  TokenRegistry tr = TokenRegistry.getTokenRegistry();
                  token = tr.issueToken(userId, userContainer, "RmServer");
               }

               RmServerDao rmserverDao = new RmServerDao();
               List lists = rmserverDao.getRmserverInfoList();
               List rtn_lists = new ArrayList();
               String rmServer = null;
               String return_code = null;
               if (lists == null) {
                  responseBody.setErrorCode("9999");
                  responseBody.setErrorMessage("Not connected to the Rm server.");
                  responseBody.setStatus("Fail");
                  return responseBody;
               } else {
                  HttpClient httpclient = new DefaultHttpClient();

                  HttpsURLConnection conn;
                  InputStream in;
                  NodeList headNodeList;
                  Element subItem;
                  Element subItem;
                  String splayerInfomation;
                  String resultXml;
                  for(int i = 0; i < lists.size(); ++i) {
                     if (((RmServerEntity)lists.get(i)).getPrivate_mode()) {
                        if (((RmServerEntity)lists.get(i)).getPrivate_ssl()) {
                           rmServer = "https://";
                        } else {
                           rmServer = "http://";
                        }

                        rmServer = rmServer + ((RmServerEntity)lists.get(i)).getPrivate_ip_address() + ":" + ((RmServerEntity)lists.get(i)).getPrivate_port() + "/RMServer/openapi/open?service=RMService.status&deviceId=" + deviceId;
                     } else {
                        if (((RmServerEntity)lists.get(i)).getUse_ssl()) {
                           rmServer = "https://";
                        } else {
                           rmServer = "http://";
                        }

                        rmServer = rmServer + ((RmServerEntity)lists.get(i)).getIp_address() + ":" + ((RmServerEntity)lists.get(i)).getPort() + "/RMServer/openapi/open?service=RMService.status&deviceId=" + deviceId;
                     }

                     String connection;
                     if ((((RmServerEntity)lists.get(i)).getPrivate_mode() || !((RmServerEntity)lists.get(i)).getUse_ssl()) && (!((RmServerEntity)lists.get(i)).getPrivate_mode() || !((RmServerEntity)lists.get(i)).getPrivate_ssl())) {
                        try {
                           HttpGet httpget = new HttpGet(rmServer);
                           HttpParams httpParams = new BasicHttpParams();
                           HttpConnectionParams.setConnectionTimeout(httpParams, 3000);
                           httpclient = new DefaultHttpClient(httpParams);
                           HttpResponse Rmserver_response = httpclient.execute(httpget);
                           HttpEntity entity = Rmserver_response.getEntity();
                           if (entity != null) {
                              BufferedReader rd = new BufferedReader(new InputStreamReader(Rmserver_response.getEntity().getContent()));
                              in = null;

                              String resultXml;
                              for(resultXml = new String(); (splayerInfomation = rd.readLine()) != null; resultXml = resultXml + splayerInfomation) {
                              }

                              Document doc = builder.parse(new InputSource(new StringReader(resultXml)));
                              doc.getDocumentElement().normalize();
                              NodeList headNodeList = doc.getElementsByTagName("response");
                              Element subItem = (Element)headNodeList.item(0);
                              return_code = subItem.getAttribute("code");
                              if (return_code != null && return_code.equals("0")) {
                                 headNodeList = doc.getElementsByTagName("resultValue");
                                 connection = headNodeList.item(0).getFirstChild().getNodeValue();
                                 subItem = null;
                                 String[] rmServerResult = connection.split("\\|", 2);
                                 if (rmServerResult.length != 2) {
                                    responseBody.setErrorCode("9997");
                                    responseBody.setErrorMessage("error");
                                    responseBody.setStatus("Fail");
                                    ResponseBody var210 = responseBody;
                                    return var210;
                                 }

                                 int index = rmServerResult[0].indexOf(":");
                                 ResponseBody var209;
                                 if (index == -1) {
                                    responseBody.setErrorCode("9997");
                                    responseBody.setErrorMessage("error");
                                    responseBody.setStatus("Fail");
                                    var209 = responseBody;
                                    return var209;
                                 }

                                 if (index + 1 >= rmServerResult[0].length()) {
                                    responseBody.setErrorCode("9997");
                                    responseBody.setErrorMessage("error");
                                    responseBody.setStatus("Fail");
                                    var209 = responseBody;
                                    return var209;
                                 }

                                 connection = (String)rmServerResult[0].subSequence(index + 1, rmServerResult[0].length());
                                 index = rmServerResult[1].indexOf(":");
                                 if (index == -1) {
                                    responseBody.setErrorCode("9997");
                                    responseBody.setErrorMessage("error");
                                    responseBody.setStatus("Fail");
                                    var209 = responseBody;
                                    return var209;
                                 }

                                 if (index + 1 >= rmServerResult[1].length()) {
                                    responseBody.setErrorCode("9997");
                                    responseBody.setErrorMessage("error");
                                    responseBody.setStatus("Fail");
                                    var209 = responseBody;
                                    return var209;
                                 }

                                 String deviceChk = (String)rmServerResult[1].subSequence(index + 1, rmServerResult[1].length());
                                 new RmServerEntity();
                                 RmServerEntity serverClient = (RmServerEntity)lists.get(i);
                                 serverClient.setConnection(Long.valueOf(connection));
                                 serverClient.setServerDeviceChk(Long.valueOf(deviceChk));
                                 rtn_lists.add(serverClient);
                              }
                           }

                           httpget.abort();
                        } catch (ClientProtocolException var147) {
                        } catch (IllegalStateException var148) {
                        } catch (ConnectException var149) {
                        } catch (ConnectTimeoutException var150) {
                        } catch (SocketException var151) {
                        } catch (SAXParseException var152) {
                        } catch (Exception var153) {
                        } finally {
                           httpclient.getConnectionManager().shutdown();
                        }
                     } else {
                        URL url = new URL(rmServer);
                        SecurityUtils.trustAllCertificates();
                        HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
                        conn.setConnectTimeout(10000);

                        try {
                           conn.connect();
                           conn.setInstanceFollowRedirects(true);
                           InputStream in = conn.getInputStream();
                           BufferedReader reader = new BufferedReader(new InputStreamReader(in));
                           conn = null;

                           String line;
                           for(splayerInfomation = new String(); (line = reader.readLine()) != null; splayerInfomation = splayerInfomation + line) {
                           }

                           Document doc = builder.parse(new InputSource(new StringReader(splayerInfomation)));
                           doc.getDocumentElement().normalize();
                           NodeList headNodeList = doc.getElementsByTagName("response");
                           Element subItem = (Element)headNodeList.item(0);
                           return_code = subItem.getAttribute("code");
                           if (return_code.equals("0")) {
                              NodeList resutNode = doc.getElementsByTagName("resultValue");
                              resultXml = resutNode.item(0).getFirstChild().getNodeValue();
                              subItem = null;
                              String[] rmServerResult = resultXml.split("\\|", 2);
                              if (rmServerResult.length != 2) {
                                 responseBody.setErrorCode("9997");
                                 responseBody.setErrorMessage("error");
                                 responseBody.setStatus("Fail");
                                 return responseBody;
                              }

                              int index = rmServerResult[0].indexOf(":");
                              if (index == -1) {
                                 responseBody.setErrorCode("9997");
                                 responseBody.setErrorMessage("error");
                                 responseBody.setStatus("Fail");
                                 return responseBody;
                              }

                              if (index + 1 >= rmServerResult[0].length()) {
                                 responseBody.setErrorCode("9997");
                                 responseBody.setErrorMessage("error");
                                 responseBody.setStatus("Fail");
                                 return responseBody;
                              }

                              resultXml = (String)rmServerResult[0].subSequence(index + 1, rmServerResult[0].length());
                              index = rmServerResult[1].indexOf(":");
                              if (index == -1) {
                                 responseBody.setErrorCode("9997");
                                 responseBody.setErrorMessage("error");
                                 responseBody.setStatus("Fail");
                                 return responseBody;
                              }

                              if (index + 1 >= rmServerResult[1].length()) {
                                 responseBody.setErrorCode("9997");
                                 responseBody.setErrorMessage("error");
                                 responseBody.setStatus("Fail");
                                 return responseBody;
                              }

                              connection = (String)rmServerResult[1].subSequence(index + 1, rmServerResult[1].length());
                              new RmServerEntity();
                              RmServerEntity serverClient = (RmServerEntity)lists.get(i);
                              serverClient.setConnection(Long.valueOf(resultXml));
                              serverClient.setServerDeviceChk(Long.valueOf(connection));
                              rtn_lists.add(serverClient);
                           }
                        } catch (Exception var146) {
                           this.logger.info("SSL time out!");
                           responseBody.setErrorCode("9997");
                           responseBody.setErrorMessage("error");
                           responseBody.setStatus("Fail");
                           return responseBody;
                        }
                     }
                  }

                  RmServerEntity rmServerClient;
                  String error_string;
                  String rmServer_monitor_url;
                  BufferedReader reader;
                  Document line;
                  String line;
                  if (command.equals("start")) {
                     if (rtn_lists.size() < 1) {
                        responseBody.setErrorCode("9999");
                        responseBody.setErrorMessage("Not connected to the SPLAYER Remote server. Please check RM Server.");
                        responseBody.setStatus("Fail");
                        return responseBody;
                     }

                     Collections.sort(rtn_lists, new Comparator() {
                        public int compare(RmServerEntity contact, RmServerEntity another) {
                           int result = contact.getConnection().compareTo(another.getConnection());
                           if (result == 0) {
                              result = contact.getServerDeviceChk().compareTo(another.getServerDeviceChk());
                           }

                           return result;
                        }
                     });
                     new RmServerEntity();
                     rmServerClient = null;
                     RmServerEntity rmServerClient = (RmServerEntity)rtn_lists.get(0);
                     String use_ssl;
                     if (rmServerClient.getUse_ssl()) {
                        use_ssl = "https://";
                     } else {
                        use_ssl = "http://";
                     }

                     error_string = null;
                     rmServer_monitor_url = null;
                     if (rmServerClient.getServerDeviceChk() > 0L) {
                        responseBody.setErrorCode("1111");
                        responseBody.setErrorMessage("Remote control has been already run");
                        responseBody.setStatus("Fail");
                        return responseBody;
                     }

                     String authority = "";
                     if (userContainer.checkAuthority("Device Write")) {
                        authority = authority + "DeviceWrite";
                     }

                     if (userContainer.checkAuthority("Device Control")) {
                        authority = authority + "DeviceControl";
                     }

                     if (userContainer.checkAuthority("Device Read")) {
                        authority = authority + "DeviceRead";
                     }

                     return_code = null;
                     if (rmServerClient.getPrivate_mode()) {
                        if (rmServerClient.getPrivate_ssl()) {
                           rmServer = "https://" + rmServerClient.getPrivate_ip_address() + ":" + rmServerClient.getPrivate_port() + "/RMServer/openapi/open?service=RMService.ready&deviceId=" + deviceId + "&token=" + token + "&authority=" + authority;
                        } else {
                           rmServer = "http://" + rmServerClient.getPrivate_ip_address() + ":" + rmServerClient.getPrivate_port() + "/RMServer/openapi/open?service=RMService.ready&deviceId=" + deviceId + "&token=" + token + "&authority=" + authority;
                        }

                        if (rmServerClient.getUse_ssl()) {
                           rmServer_monitor_url = "https://" + rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "/RMServer/remocon.do";
                        } else {
                           rmServer_monitor_url = "http://" + rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "/RMServer/remocon.do";
                        }
                     } else {
                        if (rmServerClient.getUse_ssl()) {
                           use_ssl = "https://";
                        } else {
                           use_ssl = "http://";
                        }

                        rmServer_monitor_url = use_ssl + rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "/RMServer/remocon.do";
                        rmServer = use_ssl + rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "/RMServer/openapi/open?service=RMService.ready&deviceId=" + deviceId + "&token=" + token + "&authority=" + authority;
                     }

                     Element subItem;
                     StringBuilder ChangeInfoValue;
                     DeviceConfManager DeviceConfManager;
                     Document doc;
                     NodeList headNodeList;
                     if (rmServerClient.getUse_ssl()) {
                        label2202: {
                           URL url = new URL(rmServer);
                           SecurityUtils.trustAllCertificates();
                           HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
                           conn.setConnectTimeout(10000);
                           reader = null;
                           InputStreamReader isr = null;

                           ResponseBody var194;
                           try {
                              conn.connect();
                              conn.setInstanceFollowRedirects(true);
                              InputStream in = conn.getInputStream();
                              isr = new InputStreamReader(in);
                              reader = new BufferedReader(isr);
                              line = null;

                              for(resultXml = new String(); (line = reader.readLine()) != null; resultXml = resultXml + line) {
                              }

                              doc = builder.parse(new InputSource(new StringReader(resultXml)));
                              doc.getDocumentElement().normalize();
                              headNodeList = doc.getElementsByTagName("response");
                              subItem = (Element)headNodeList.item(0);
                              return_code = subItem.getAttribute("code");
                              if (return_code.equals("0")) {
                                 ChangeInfoValue = new StringBuilder();
                                 if (rmServerClient.getUse_ssl()) {
                                    use_ssl = "true";
                                 } else {
                                    use_ssl = "false";
                                 }

                                 ChangeInfoValue = ChangeInfoValue.append(rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "@" + use_ssl + "@" + token);
                                 DeviceConfManager = DeviceConfManagerImpl.getInstance();

                                 try {
                                    DeviceConfManager.reqSetDeviceConnStatus(deviceId, ".MO.DEVICE_CONF.RM.START", ChangeInfoValue.toString());
                                 } catch (Exception var134) {
                                    this.logger.error(".MO.DEVICE_CONF.RM.START MO fail!", var134);
                                 }
                              } else {
                                 error_string = subItem.getElementsByTagName("errorMessage").item(0).getNodeValue();
                              }
                              break label2202;
                           } catch (Exception var144) {
                              this.logger.info("SSL time out!");
                              responseBody.setErrorCode("9997");
                              responseBody.setErrorMessage("error");
                              responseBody.setStatus("Fail");
                              var194 = responseBody;
                           } finally {
                              isr.close();
                              reader.close();
                           }

                           return var194;
                        }
                     } else {
                        try {
                           HttpClient rmHttpClient = new DefaultHttpClient();
                           HttpGet httpget = new HttpGet(rmServer);
                           HttpResponse Rmserver_response = rmHttpClient.execute(httpget);
                           HttpEntity entity = Rmserver_response.getEntity();
                           if (entity != null) {
                              BufferedReader rd = new BufferedReader(new InputStreamReader(Rmserver_response.getEntity().getContent()));
                              line = null;

                              for(resultXml = new String(); (line = rd.readLine()) != null; resultXml = resultXml + line) {
                              }

                              doc = builder.parse(new InputSource(new StringReader(resultXml)));
                              doc.getDocumentElement().normalize();
                              headNodeList = doc.getElementsByTagName("response");
                              subItem = (Element)headNodeList.item(0);
                              return_code = subItem.getAttribute("code");
                              if (return_code != null) {
                                 if (!return_code.equals("0")) {
                                    error_string = subItem.getElementsByTagName("errorMessage").item(0).getNodeValue();
                                 } else {
                                    ChangeInfoValue = new StringBuilder();
                                    if (rmServerClient.getUse_ssl()) {
                                       use_ssl = "true";
                                    } else {
                                       use_ssl = "false";
                                    }

                                    ChangeInfoValue = ChangeInfoValue.append(rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "@" + use_ssl + "@" + token);
                                    DeviceConfManager = DeviceConfManagerImpl.getInstance();

                                    try {
                                       DeviceConfManager.reqSetDeviceConnStatus(deviceId, ".MO.DEVICE_CONF.RM.START", ChangeInfoValue.toString());
                                    } catch (Exception var133) {
                                    }
                                 }
                              }
                           }

                           httpget.abort();
                        } catch (ClientProtocolException var140) {
                           this.logger.error("", var140);
                        } catch (IOException var141) {
                           this.logger.error("", var141);
                        } catch (Exception var142) {
                           this.logger.error("", var142);
                        } finally {
                           httpclient.getConnectionManager().shutdown();
                        }
                     }

                     if (return_code == null || !return_code.equals("0")) {
                        responseBody.setErrorCode(return_code);
                        responseBody.setErrorMessage(error_string);
                        responseBody.setStatus("Fail");
                        return responseBody;
                     }

                     MonitoringManager deviceInfo = MonitoringManagerImpl.getInstance();
                     deviceInfo.setRMToken(deviceId, token);
                     splayerInfomation = device.getDevice_name() + "|";
                     if (playingEntity != null) {
                        if (playingEntity.getProgramName() == null) {
                           splayerInfomation = splayerInfomation + "|";
                        } else {
                           splayerInfomation = splayerInfomation + playingEntity.getProgramName() + "|";
                        }
                     } else {
                        splayerInfomation = splayerInfomation + "|";
                     }

                     splayerInfomation = splayerInfomation + device.getDevice_model_name() + "|" + device.getDevice_id() + "|" + device.getFirmware_version() + "|" + device.getIp_address() + "|" + device.getGateway() + "|" + device.getTrigger_interval();
                     result.setUrl(rmServer_monitor_url + "?deviceId=" + deviceId + "&token=" + token + "&information=" + URLEncoder.encode(splayerInfomation, "UTF-8"));
                     result.setToken(token);
                  } else if (command.equals("stop")) {
                     this.logger.error("[MagicInfo_RMServer][" + deviceId + "] stop RM Server! token : " + token);
                     MonitoringManager deviceInfo = MonitoringManagerImpl.getInstance();
                     token = deviceInfo.getRMToken(deviceId);
                     if (token == null) {
                        return null;
                     }

                     if (rtn_lists.size() < 1) {
                        responseBody.setErrorCode("9999");
                        responseBody.setErrorMessage("Not connected to the SPLAYER Remote server. Please check RM Server.");
                        responseBody.setStatus("Fail");
                        return responseBody;
                     }

                     Collections.sort(rtn_lists, new Comparator() {
                        public int compare(RmServerEntity contact, RmServerEntity another) {
                           int result = contact.getConnection().compareTo(another.getConnection());
                           if (result == 0) {
                              result = contact.getServerDeviceChk().compareTo(another.getServerDeviceChk());
                           }

                           return result;
                        }
                     });
                     new RmServerEntity();
                     error_string = null;
                     rmServerClient = (RmServerEntity)rtn_lists.get(0);
                     if (rmServerClient.getUse_ssl()) {
                        error_string = "https://";
                     } else {
                        error_string = "http://";
                     }

                     rmServer_monitor_url = null;
                     return_code = null;
                     if (rmServerClient.getPrivate_mode()) {
                        rmServer = error_string + rmServerClient.getPrivate_ip_address() + ":" + rmServerClient.getPrivate_port() + "/RMServer/openapi/open?service=RMService.stop&deviceId=" + deviceId + "&token=" + token;
                     } else {
                        rmServer = error_string + rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "/RMServer/openapi/open?service=RMService.stop&deviceId=" + deviceId + "&token=" + token;
                     }

                     String line;
                     if (rmServerClient.getUse_ssl()) {
                        URL url = new URL(rmServer);
                        SecurityUtils.trustAllCertificates();
                        conn = (HttpsURLConnection)url.openConnection();
                        conn.setConnectTimeout(10000);

                        try {
                           conn.connect();
                           conn.setInstanceFollowRedirects(true);
                           in = conn.getInputStream();
                           reader = new BufferedReader(new InputStreamReader(in));
                           String line = null;

                           for(line = new String(); (line = reader.readLine()) != null; line = line + line) {
                           }

                           line = builder.parse(new InputSource(new StringReader(line)));
                           line.getDocumentElement().normalize();
                           headNodeList = line.getElementsByTagName("response");
                           subItem = (Element)headNodeList.item(0);
                           return_code = subItem.getAttribute("code");
                        } catch (Exception var139) {
                           this.logger.info("SSL time out!");
                           responseBody.setErrorCode("9997");
                           responseBody.setErrorMessage("error");
                           responseBody.setStatus("Fail");
                           return responseBody;
                        }
                     } else {
                        try {
                           HttpClient rmHttpClient = new DefaultHttpClient();
                           HttpGet httpget = new HttpGet(rmServer);
                           HttpResponse Rmserver_response = rmHttpClient.execute(httpget);
                           HttpEntity entity = Rmserver_response.getEntity();
                           if (entity != null) {
                              BufferedReader rd = new BufferedReader(new InputStreamReader(Rmserver_response.getEntity().getContent()));
                              line = null;

                              for(line = new String(); (line = rd.readLine()) != null; line = line + line) {
                              }

                              Document doc = builder.parse(new InputSource(new StringReader(line)));
                              doc.getDocumentElement().normalize();
                              NodeList headNodeList = doc.getElementsByTagName("response");
                              subItem = (Element)headNodeList.item(0);
                              return_code = subItem.getAttribute("code");
                              rd.close();
                           }

                           httpget.abort();
                        } catch (ClientProtocolException var135) {
                           this.logger.error("", var135);
                        } catch (IOException var136) {
                           this.logger.error("", var136);
                        } catch (Exception var137) {
                           this.logger.error("", var137);
                        } finally {
                           httpclient.getConnectionManager().shutdown();
                        }
                     }

                     if (return_code != null && return_code.equals("0")) {
                        responseBody.setStatus("Success");
                        return responseBody;
                     }

                     responseBody.setErrorCode(return_code);
                     responseBody.setErrorMessage(return_code);
                     responseBody.setStatus("Fail");
                     return responseBody;
                  }

                  responseBody.setErrorCode("0");
                  responseBody.setItems(result);
                  responseBody.setStatus("Success");
                  return responseBody;
               }
            }
         }
      }
   }

   public DeviceSystemSetupConfResource getDeviceSetupInfoFromDB(String deviceId) throws SQLException {
      DeviceSystemSetupConf systemSetup = this.deviceSetupConf.getDeviceSystemSetupConf(deviceId);
      DeviceSystemSetupConfResource systemSetupCamel = DeviceModelConverter.convertSystemSetupToCamelStyle(systemSetup);
      String deviceType = systemSetup.getDevice_type();
      List timzoneList = new ArrayList();
      if (systemSetup != null) {
         List timeZoneListItem = this.deviceSetupConf.getTimeZoneMapList(systemSetup.getTime_zone_version());
         int i;
         Map tmpTimezone;
         HashMap timezone;
         if (deviceType != null && deviceType.equalsIgnoreCase("iPLAYER")) {
            for(i = 0; i < timeZoneListItem.size(); ++i) {
               tmpTimezone = (Map)timeZoneListItem.get(i);
               timezone = new HashMap();
               timezone.put("index", tmpTimezone.get("display").toString());
               timezone.put("dst", tmpTimezone.get("is_day_light_saving"));
               timzoneList.add(timezone);
            }
         } else {
            for(i = 0; i < timeZoneListItem.size(); ++i) {
               tmpTimezone = (Map)timeZoneListItem.get(i);
               timezone = new HashMap();
               timezone.put("index", tmpTimezone.get("display").toString());
               timezone.put("dst", true);
               timzoneList.add(timezone);
            }
         }
      }

      systemSetupCamel.setTimeZoneList(timzoneList);
      if (!deviceType.equalsIgnoreCase("LPLAYER") && systemSetupCamel.getDayLightSaving() != null) {
         String dstManual = systemSetupCamel.getDayLightSavingManual();
         if (dstManual != null && dstManual.length() > 0) {
            String[] dstArr = dstManual.split(";");
            if (dstArr.length > 8) {
               systemSetupCamel.setDstStartMonth(dstArr[0]);
               systemSetupCamel.setDstStartWeek(dstArr[1]);
               systemSetupCamel.setDstStartDay(dstArr[2]);
               String[] starttimearr = dstArr[3].split(":");
               String dstStartTime = StrUtils.getLeftFilledString(starttimearr[0], "0", 2) + ":" + StrUtils.getLeftFilledString(starttimearr[1], "0", 2);
               systemSetupCamel.setDstStartTime(dstStartTime);
               systemSetupCamel.setDstEndMonth(dstArr[4]);
               systemSetupCamel.setDstEndWeek(dstArr[5]);
               systemSetupCamel.setDstEndDay(dstArr[6]);
               String[] endtimearr = dstArr[7].split(":");
               String dstEndTime = StrUtils.getLeftFilledString(endtimearr[0], "0", 2) + ":" + StrUtils.getLeftFilledString(endtimearr[1], "0", 2);
               systemSetupCamel.setDstEndTime(dstEndTime);
               systemSetupCamel.setDstTimeDifference(dstArr[8]);
            }
         } else {
            systemSetupCamel.setDayLightSavingManual((String)null);
         }
      }

      return systemSetupCamel;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public Long uploadCustomizeFile(String softwareType, String swName, MultipartFile softwareFile) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
      new ServerLogEntity();
      this.rms = new ResourceBundleMessageSource();
      this.rms.setBasename("resource/messages");
      String device_type = "SPLAYER";
      String device_model = "Default";
      Software software = new Software();
      if (swName.equals("")) {
         swName = softwareFile.getOriginalFilename();
      }

      software.setSoftware_name(swName);
      software.setFile_name(softwareFile.getOriginalFilename());
      software.setFile_size(softwareFile.getSize());
      software.setDevice_type(device_type);
      software.setDevice_model_name(device_model);
      software.setIs_auto_update(false);
      software.setCreator_id(userContainer.getUser().getUser_id());
      software.setUpgrade_version("multi");
      String fileSavePath;
      String savedFileName;
      String[] acceptFilesVideo;
      int var16;
      boolean isFileSuccess;
      String filePath;
      if (softwareType.equalsIgnoreCase("customLogo")) {
         savedFileName = null;
         String[] acceptFiles = new String[]{".jpg", ".jpeg", ".bmp", ".png", ".gif"};
         acceptFilesVideo = new String[]{".avi", ".mpg", ".mpeg", ".mp4", ".vob", ".vro", ".ts", ".trp", ".tp", ".svi", ".mkv", ".wmv", ".asf", ".3gp", ".divx", ".flv", ".m2ts", ".mov", ".rm", ".rmvb", ".mts", ".webm", ".s4ud", ".ps", ".avs", ".um4", ".km4"};
         String[] var15 = acceptFiles;
         var16 = acceptFiles.length;

         int var17;
         String str;
         for(var17 = 0; var17 < var16; ++var17) {
            str = var15[var17];
            if (softwareFile.getOriginalFilename().toLowerCase().indexOf(str) > 0) {
               savedFileName = "picture";
            }
         }

         if (savedFileName == null) {
            var15 = acceptFilesVideo;
            var16 = acceptFilesVideo.length;

            for(var17 = 0; var17 < var16; ++var17) {
               str = var15[var17];
               if (softwareFile.getOriginalFilename().toLowerCase().indexOf(str) > 0) {
                  savedFileName = "video";
               }
            }
         }

         if (savedFileName == null) {
            throw new Exception("FileTypeError");
         }

         if (savedFileName.equals("picture") && softwareFile.getSize() > 52428800L) {
            throw new Exception("FileSizeExceed");
         }

         if (savedFileName.equals("video") && softwareFile.getSize() > 157286400L) {
            throw new Exception("FileSizeExceed");
         }

         fileSavePath = "sw.logo";
         software.setSoftware_type("03");
      } else {
         if (!softwareType.equalsIgnoreCase("defaultContent")) {
            throw new Exception("WrongSWType");
         }

         String[] acceptFiles = new String[]{".wmv", ".vro", ".vob", ".ts", ".trp", ".tp", ".svi", ".mts", ".mpg", ".mpeg", ".mp4", ".mov", ".mkv", ".m2ts", ".flv", ".divx", ".avi", ".asf", ".3gp"};
         isFileSuccess = false;
         acceptFilesVideo = acceptFiles;
         int var22 = acceptFiles.length;

         for(var16 = 0; var16 < var22; ++var16) {
            filePath = acceptFilesVideo[var16];
            if (softwareFile.getOriginalFilename().toLowerCase().indexOf(filePath) > 0) {
               isFileSuccess = true;
            }
         }

         if (!isFileSuccess) {
            throw new Exception("FileTypeError");
         }

         fileSavePath = "sw.default_content";
         software.setSoftware_type("04");
      }

      savedFileName = System.currentTimeMillis() + softwareFile.getOriginalFilename();
      isFileSuccess = FileUploadCommonHelper.saveFileToDisk(savedFileName, softwareFile, fileSavePath);
      if (!isFileSuccess) {
         throw new Exception("software_file_upload_fail");
      } else {
         software.setMime_type(ChecksumCRC32.getCRC32Value(savedFileName, fileSavePath));
         software.setFile_path(FileUploadCommonHelper.getWebPath(fileSavePath) + "/" + savedFileName);
         Long software_id = new Long((long)SequenceDB.getNextValue("MI_DMS_INFO_SOFTWARE"));
         software.setSoftware_id(software_id);
         String UPLOAD_HOME = "";
         UPLOAD_HOME = CommonConfig.get("UPLOAD_HOME");
         UPLOAD_HOME = UPLOAD_HOME.replace('/', File.separatorChar);
         FileManagerImpl fileManager;
         File swFile;
         if ("03".equalsIgnoreCase(software.getSoftware_type())) {
            fileManager = FileManagerImpl.getInstance();
            filePath = FileUploadCommonHelper.getAbsoluteDirectoryPath("sw.logo") + File.separator + savedFileName;
            swFile = new File(filePath);
            fileManager.createThumbnailFile(swFile, FileUploadCommonHelper.getAbsoluteDirectoryPath(fileSavePath), savedFileName);
         } else {
            if (!"04".equalsIgnoreCase(software.getSoftware_type())) {
               throw new Exception("WrongSWType");
            }

            fileManager = FileManagerImpl.getInstance();
            filePath = FileUploadCommonHelper.getAbsoluteDirectoryPath("sw.default_content") + File.separator + savedFileName;
            swFile = new File(filePath);
            fileManager.createThumbnailFile(swFile, FileUploadCommonHelper.getAbsoluteDirectoryPath(fileSavePath), savedFileName);
         }

         SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
         boolean result = softwareDao.addDeviceSoftware(software, "P");
         if (!result) {
            throw new Exception("software_file_upload_fail");
         } else {
            return software_id;
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public void publishCustomizeFile(String softwareId, String groupID) throws Exception {
      Long result = 0L;
      String rsvDate = "NOW";
      String deployAppliedVer = "";
      String appliedType = "GROUP";
      String appliedGroupStr = groupID;
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String var9 = userContainer.getUser().getUser_id();

      try {
         SoftwareManager swMgr = SoftwareManagerImpl.getInstance();
         Software software = swMgr.getSoftware(Long.parseLong(softwareId));
         if (rsvDate.equals("NOW")) {
            String timeStamp = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(Calendar.getInstance().getTime());
            software.setRsv_date(Timestamp.valueOf(timeStamp));
         } else {
            software.setType(1L);
            software.setRsv_date(DateUtils.string2Timestamp(rsvDate, SecurityUtils.getUserDateFormat() + " " + SecurityUtils.getUserTimeFormat()));
         }

         software.setDeploy_applied_ver(deployAppliedVer);
         software.setApplied_type(appliedType);
         if (appliedType.equals("GROUP")) {
            software.setDevice_group(appliedGroupStr);
         }

         software.setSubscriber_id(userContainer.getUser().getUser_id());
         Object[] rtn = swMgr.deploySoftwareToDevices(software, (String)null);
         if (rtn != null && rtn.length > 0) {
            result = (Long)rtn[0];
         }
      } catch (Exception var13) {
         this.logger.error("[SoftwareController] DEPLOY_SAVE fail! softwareId : " + softwareId, var13);
         result = 0L;
      }

      boolean flag = result > 0L;
      if (!flag) {
         throw new Exception("software_file_deploy_fail");
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public ResponseBody assignTag(String deviceId, DeviceTagAssignment tagInfo) throws Exception {
      ResponseBody responseBody = new ResponseBody();

      try {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
         DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
         List tagIdList = tagInfo.getTagIds();
         List tagConditionList = null;
         String requestId = UUID.randomUUID().toString();
         String tagType = tagInfo.getTagType();
         if ("VARIABLE".equalsIgnoreCase(tagType)) {
            tagConditionList = new ArrayList();
            if (tagInfo.getTagConditionIds().isEmpty()) {
               tagConditionList.add("0");
            } else {
               int tagConditionCount = tagInfo.getTagConditionIds().size();

               for(int i = 0; i < tagConditionCount; ++i) {
                  tagConditionList.add(tagInfo.getTagConditionIds().get(i));
               }
            }
         }

         try {
            this.updateDeviceTag(deviceId, tagIdList, tagConditionList);
         } catch (Exception var15) {
            throw new SQLException("Error during saving device tags");
         }

         if (DeviceUtils.isConnected(deviceId)) {
            DeviceSystemSetupConf info = new DeviceSystemSetupConf();
            info.setDevice_id(deviceId);
            info.setTag_id_list(tagIdList);
            if (tagConditionList != null) {
               info.setTag_condition_list(tagConditionList);
            }

            confManager.reqSetSystemSetupToDevice(info, requestId);
         }

         String[] deviceIds = new String[]{deviceId};
         List deviceTags = deviceInfo.getDeviceAndTagListByDeviceIds(deviceIds, "variable".equalsIgnoreCase(tagType));
         List resources = new ArrayList();

         for(int i = 0; i < deviceTags.size(); ++i) {
            if (((DeviceTag)deviceTags.get(i)).getTag_id() != null) {
               DeviceTagResource resource = new DeviceTagResource();
               resource.setDeviceId(((DeviceTag)deviceTags.get(i)).getDevice_id());
               resource.setDeviceName(((DeviceTag)deviceTags.get(i)).getDevice_name());
               resource.setTagId(String.valueOf(((DeviceTag)deviceTags.get(i)).getTag_id()));
               resource.setTagType(String.valueOf(((DeviceTag)deviceTags.get(i)).getTag_type()));
               resource.setTagName(((DeviceTag)deviceTags.get(i)).getTag_name());
               resource.setTagConditionId(String.valueOf(((DeviceTag)deviceTags.get(i)).getTag_condition_id()));
               resource.setTagConditionName(((DeviceTag)deviceTags.get(i)).getTag_condition());
               resource.setTagDescription(((DeviceTag)deviceTags.get(i)).getTag_desc());
               resources.add(resource);
            }
         }

         responseBody.setTotalCount(resources.size());
         responseBody.setItems(resources);
         responseBody.setStatus("Success");
      } catch (Exception var16) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var16.getMessage());
      }

      return responseBody;
   }

   private void updateDeviceTag(String deviceId, List tagIdList, List tagConditionList) throws SQLException {
      DeviceSystemSetupConfManager systemSetupDao = DeviceSystemSetupConfManagerImpl.getInstance();
      int i;
      if (tagConditionList != null) {
         systemSetupDao.deleteDeviceVarTagAll(deviceId);
         if (tagIdList.size() > 0 && !((String)tagIdList.get(0)).equals("0")) {
            for(i = 0; i < tagIdList.size(); ++i) {
               if (tagConditionList != null && tagConditionList.size() > i) {
                  systemSetupDao.setDeviceTag(deviceId, Long.parseLong((String)tagIdList.get(i)), Long.parseLong((String)tagConditionList.get(i)));
               }
            }
         }
      } else {
         systemSetupDao.deleteDeviceTagAll(deviceId);
         if (tagIdList.size() > 0) {
            for(i = 0; i < tagIdList.size(); ++i) {
               if (!((String)tagIdList.get(0)).equals("0")) {
                  systemSetupDao.setDeviceTag(deviceId, Long.parseLong((String)tagIdList.get(i)));
               }
            }
         }
      }

   }

   public String saveRmRuleFile(MultipartFile file) throws Exception {
      if (file == null) {
         throw new Exception("FileIsEmpty");
      } else if (!"text/xml".equalsIgnoreCase(file.getContentType())) {
         throw new Exception("NotValidFile");
      } else {
         String rmRuleFilePath = CommonConfig.get("CONTENTS_HOME") + File.separator + "rm_rule";
         File uploadFileChecker = SecurityUtils.getSafeFile(rmRuleFilePath);
         InputStream is = null;
         FileOutputStream fos = null;
         String originalFilename = file.getOriginalFilename();
         int tmp = originalFilename.lastIndexOf(".");
         String ext = tmp > 0 ? originalFilename.substring(tmp) : ".xml";
         if (!uploadFileChecker.exists()) {
            boolean fSuccess = uploadFileChecker.mkdir();
            if (!fSuccess) {
               this.logger.fatal(fSuccess);
               throw new Exception("FilePathError");
            }
         }

         int index = 0;
         String filename = "rm_rule_" + System.currentTimeMillis();

         StringBuilder var10000;
         for(uploadFileChecker = SecurityUtils.getSafeFile(rmRuleFilePath + File.separator + filename + ext); uploadFileChecker.exists(); uploadFileChecker = SecurityUtils.getSafeFile(var10000.append(index).append(ext).toString())) {
            var10000 = (new StringBuilder()).append(rmRuleFilePath).append(File.separator).append(filename);
            ++index;
         }

         String finalFilename = filename + (index > 0 ? index : "") + ext;

         try {
            is = file.getInputStream();
            fos = new FileOutputStream(rmRuleFilePath + File.separator + finalFilename, false);
            byte[] buf = new byte[1048576];
            boolean var13 = false;

            int binaryRead;
            while((binaryRead = is.read(buf)) != -1) {
               fos.write(buf, 0, binaryRead);
            }
         } catch (Exception var17) {
            this.logger.error("", var17);
            throw var17;
         } finally {
            if (is != null) {
               is.close();
            }

            if (fos != null) {
               fos.close();
            }

         }

         return finalFilename;
      }
   }

   public ResponseBody getDeviceMonitoring(String[] deviceIds, Map periodMap) throws Exception {
      ResponseBody responseBody = new ResponseBody();

      try {
         List monitoringList = null;
         Integer errorPeriod = Integer.valueOf((String)periodMap.get(DeviceModelUtils.PERIOD_DEVICE_ERROR_WARNING));
         if (errorPeriod == null) {
            errorPeriod = 10;
         }

         if (deviceIds != null && deviceIds.length > 0) {
            if (deviceIds.length > 50) {
               responseBody.setStatus("Fail");
               responseBody.setErrorMessage("The maximum number of devices is 50.");
               return responseBody;
            }

            Timestamp errorStandard = new Timestamp(System.currentTimeMillis() - (long)(errorPeriod * 60 * 1000));
            List deviceList = this.deviceDao.getRmMonitoringList(deviceIds, errorStandard);
            monitoringList = new ArrayList();
            Iterator var8 = deviceList.iterator();

            while(var8.hasNext()) {
               RmMonitoring device = (RmMonitoring)var8.next();
               DeviceRmMonitoring monitoring = new DeviceRmMonitoring();
               String deviceId = device.getDevice_id();
               boolean isConnected = this.monMgr.isConnected(deviceId);
               monitoring.setDeviceId(deviceId);
               monitoring.setConnectionStatus(isConnected);
               monitoring.setLastConnectionTime(device.getLast_connection_time());
               monitoring.setErrorCount(device.getError_count());
               monitoring.setWarningCount(device.getWarning_count());
               monitoring.setRmRuleVersion(device.getRm_rule_version());
               if (isConnected) {
                  CurrentPlayingEntity playingEntity = this.monMgr.getPlayingContent(deviceId);
                  if (playingEntity != null) {
                     List playingContentList = null;
                     if (playingEntity.getActiveType() != null && playingEntity.getActiveType().equalsIgnoreCase("event")) {
                        playingContentList = playingEntity.getEventScheduleContentLists();
                     } else {
                        playingContentList = playingEntity.getContentLists();
                     }

                     List playingList = null;
                     if (playingContentList != null && playingContentList.size() > 0) {
                        playingList = new ArrayList();
                        Iterator var16 = playingContentList.iterator();

                        while(var16.hasNext()) {
                           ContentList playingContent = (ContentList)var16.next();
                           Map pc = new HashMap();
                           pc.put("frameIndex", playingContent.getFrameIndex());
                           pc.put("frameName", playingContent.getFrameName());
                           pc.put("contentId", playingContent.getContentId());
                           pc.put("contentName", playingContent.getContentName());
                           playingList.add(pc);
                        }
                     }

                     monitoring.setCurrentPlaying(playingList);
                  }
               }

               Integer rmDataPeriod = Integer.valueOf((String)periodMap.get(DeviceModelUtils.PERIOD_RM_MONITORING));
               if (rmDataPeriod == null) {
                  rmDataPeriod = 10;
               }

               String startTime = (String)periodMap.get(DeviceModelUtils.OFFSET_RM_MONITORING_DATA);
               SimpleDateFormat transFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
               Date offset = transFormat.parse(startTime);
               Map rmData = this.getRmData(deviceId, offset, rmDataPeriod);
               monitoring.setRmData(rmData);
               monitoringList.add(monitoring);
            }
         }

         responseBody.setItems(monitoringList);
         responseBody.setStatus("Success");
         return responseBody;
      } catch (Exception var19) {
         this.logger.error("", var19);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var19.toString());
         return responseBody;
      }
   }

   private Map getRmData(String deviceId, final Date offsetGMT, final Integer period) {
      String data = "";
      HashMap resultMap = new HashMap();

      try {
         String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar);
         String RM_MONITORING_DIR = CommonConfig.get("RM_MONITORING_DIR");
         if (StringUtil.isEmpty(RM_MONITORING_DIR)) {
            RM_MONITORING_DIR = "rm_monitoring";
         }

         String dirPath = CONTENTS_HOME + File.separator + RM_MONITORING_DIR + File.separator + deviceId;
         File directory = SecurityUtils.getSafeFile(dirPath);
         if (!directory.exists()) {
            resultMap.put("result", "fail");
            resultMap.put("message", "Not exist");
            return resultMap;
         }

         Integer cleanPeriod = 1;

         try {
            cleanPeriod = Integer.valueOf(CommonConfig.get("rm.data.clean.period"));
         } catch (Exception var28) {
         }

         if (period > 1440 * cleanPeriod) {
            resultMap.put("message", "Max period is " + 1440 * cleanPeriod + ". So period will be set to " + 1440 * cleanPeriod + ".");
         }

         File[] files = directory.listFiles(new FileFilter() {
            public boolean accept(File subfile) {
               try {
                  SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                  sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
                  Date lastModifiedDate = new Date(subfile.lastModified());
                  String GMTFileDateStr = sdf.format(lastModifiedDate);
                  Date GMTfileDate = sdf.parse(GMTFileDateStr);
                  Calendar cal = Calendar.getInstance();
                  cal.setTime(offsetGMT);
                  cal.add(12, period);
                  Date end = cal.getTime();
                  if (GMTfileDate.after(offsetGMT) && lastModifiedDate.before(end)) {
                     return true;
                  }
               } catch (Exception var8) {
                  DeviceServiceImpl.this.logger.error(var8);
               }

               return false;
            }
         });
         if (files != null && files.length > 0) {
            Integer maxFileCount = 100;

            try {
               maxFileCount = Integer.valueOf(CommonConfig.get("rm.data.max.count"));
            } catch (Exception var27) {
            }

            if (files.length > maxFileCount) {
               resultMap.put("result", "fail");
               resultMap.put("message", "Too many files exist! The available number of files is " + files.length + ". (Max is 100)");
               return resultMap;
            }

            Map dataMap = new HashMap();

            for(int i = 0; i < files.length; ++i) {
               if (files[i].exists()) {
                  FileInputStream fis = null;
                  BufferedReader reader = null;
                  StringBuffer sb = new StringBuffer();

                  try {
                     int length = (int)files[i].length();
                     char[] tmp = new char[length];
                     fis = new FileInputStream(files[i]);
                     reader = new BufferedReader(new InputStreamReader(fis));
                     reader.read(tmp);
                     sb.append(tmp);
                  } catch (Exception var26) {
                     this.logger.error(var26.toString());
                  } finally {
                     if (fis != null) {
                        fis.close();
                     }

                     if (reader != null) {
                        reader.close();
                     }

                  }

                  dataMap.put(files[i].getName(), sb.toString());
               }
            }

            resultMap.put("data", dataMap);
         }

         resultMap.put("result", "success");
      } catch (Exception var30) {
         this.logger.error(var30);
         resultMap.put("result", "fail");
         resultMap.put("message", "unexpected error!");
      }

      return resultMap;
   }

   public ResponseBody getError(String[] deviceIds, String type, Integer period, Integer status) throws SQLException {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");

      try {
         Timestamp errorStandard = new Timestamp(System.currentTimeMillis() - (long)period * 60L * 1000L);
         List clientFaultList = this.deviceDao.getErrorList(deviceIds, type, errorStandard, status);
         List resourceList = null;
         if (clientFaultList != null && clientFaultList.size() > 0) {
            responseBody.setItems(clientFaultList);
         }

         responseBody.setStatus("Success");
         return responseBody;
      } catch (SQLException var9) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var9.getMessage());
         return responseBody;
      } catch (NullPointerException var10) {
         responseBody.setStatus("Fail");
         responseBody.setErrorCode(ExceptionCode.RES905[0]);
         responseBody.setErrorMessage(ExceptionCode.RES905[2]);
         return responseBody;
      }
   }
}
