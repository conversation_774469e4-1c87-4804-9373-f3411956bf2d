<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAOMapper">

    <insert id="insert_addFrame">
        INSERT INTO MI_CDS_INFO_FRAME_TEMP (SESSION_ID, PROGRAM_ID, CHANNEL_NO, SCREEN_INDEX, FRAME_INDEX
            , FRAME_NAME, X, Y, WIDTH, HEIGHT, DEFAULT_CONTENT_ID, IS_MAIN_FRAME, FRAME_ID, VERSION
            , LINE_DATA)
            VALUES (#{frame.session_id}, #{frame.program_id}, #{frame.channel_no}, #{frame.screen_index}, #{frame.frame_index}
                , #{frame.frame_name}, #{frame.x}, #{frame.y}, #{frame.width}, #{frame.height}
                , #{frame.default_content_id}, #{frame.is_main_frame}, #{frame.frame_id}, #{frame.version}
                , #{frame.line_data})
    </insert>

    <insert id="insert2_addFrame">
        INSERT INTO MI_CDS_MAP_FRAME_USER_TEMP (SESSION_ID, PROGRAM_ID, FRAME_ID, USER_GROUP_ID)
            VALUES (#{frame.session_id}, #{frame.program_id}, #{frame.frame_id}, #{userGroupId})
    </insert>

    <insert id="addDeviceGroupMappedInProgramTemp">
        INSERT INTO MI_CDS_MAP_PROGRAM_DEVICE_TEMP (PROGRAM_ID, DEVICE_GROUP_ID, DEVICE_GROUPS, DEFAULT_CONTENT_ID, BGM_CONTENT_ID)
            VALUES (#{program_id}, #{groupId}, #{groupName}, #{default_content}, #{bgm_content_id})
    </insert>

    <insert id="addDeviceGroupMappedInProgram">
        INSERT INTO MI_CDS_MAP_PROGRAM_DEVICE (PROGRAM_ID, DEVICE_GROUP_ID) VALUES (#{program_id}, #{device_group_id})
    </insert>

    <insert id="insert_addProgram">
        INSERT INTO MI_CDS_INFO_PROGRAM (PROGRAM_ID, VERSION, PROGRAM_NAME, PROGRAM_TYPE
        , DEPLOY_TIME, SCHEDULE_LIMIT, IS_DEFAULT, IS_MEDIA_MERCHANT
        , SCREEN_COUNT, USER_ID, CREATE_DATE
        , MODIFY_DATE, BGM_CONTENT_ID, IS_BGM_WITH_CONTENT, DESCRIPTION
        , ASPECT_RATIO, DELETED, FRAME_LAYOUT_TYPE,
        RESOLUTION, SYNCHRONIZATION, RESUME, DEVICE_TYPE, DEVICE_TYPE_VERSION, USE_MULTI_VWL, USE_SYNC_PLAY, USE_AD_SCHEDULE, AD_DURATION,
        RESERVATION_REPEAT_TYPE, RESERVATION_START_DATE, RESERVATION_END_DATE, RESERVATION_WEEKLY, RESERVATION_MONTHLY)
        VALUES (#{program.program_id}, #{program.version}, #{program.program_name}, #{program.program_type}
        , #{program.deploy_time}, #{program.schedult_limit}, #{program.is_default}, #{program.is_media_merchant}
        , #{program.screen_count}, #{program.user_id},
        <include refid="utils.currentTimestamp"/>
        ,<include refid="utils.currentTimestamp"/>, #{program.bgm_content_id}, #{program.is_bgm_with_content}
        , #{program.description}, #{program.aspect_ratio}, #{program.deleted}, #{program.frame_layout_type}
        , #{program.resolution}, #{program.synchronization}, #{program.resume}, #{program.device_type}
        , #{program.device_type_version}, #{program.use_multi_vwl}, #{program.use_sync_play}, #{program.use_ad_schedule}, #{program.ad_duration}
        , #{program.reservation_repeat_type}, #{program.reservation_start_date}, #{program.reservation_end_date}, #{program.reservation_weekly}, #{program.reservation_monthly})
    </insert>

    <insert id="insert2_addProgram">
        INSERT INTO MI_CDS_MAP_PROGRAM_GROUP (PROGRAM_ID, GROUP_ID)
            VALUES (#{program.program_id}, #{program.program_group_id})
    </insert>

    <insert id="insert3_addProgram">
        INSERT INTO MI_CDS_MAP_PROGRAM_DEVICE (PROGRAM_ID, DEVICE_GROUP_ID) VALUES (#{program_id}, #{deviceId})
    </insert>

    <insert id="insert4_addProgram">
        INSERT INTO MI_CDS_INFO_SCHEDULE (PROGRAM_ID, PLAYER_MODE, SAFETYLOCK, SCREEN_INDEX, FRAME_INDEX, SCHEDULE_ID, START_DATE, STOP_DATE
            , START_TIME, DURATION, REPEAT_TYPE, WEEKDAYS, MONTHDAYS, USER_ID, CREATE_DATE, MODIFY_DATE, SCHEDULE_TYPE
            , HW_INPUT_SOURCE, HW_ATVDTV, HW_AIRCABLE, HW_MAJORCH, HW_MINORCH, HW_VOLUME, HW_SCH_CH, CONTENT_ID, CONTENT_TYPE
            , REPEAT_TIME, IN_EFFECT_TYPE, IN_EFFECT_DURATION, IN_EFFECT_DIRECTION, OUT_EFFECT_TYPE
            , OUT_EFFECT_DURATION, OUT_EFFECT_DIRECTION, CHANNEL_NO, PRIORITY, IS_STREAMING, SLIDE_TRANSITION_TIME)
            SELECT
                PROGRAM_ID,
                PLAYER_MODE, 
                SAFETYLOCK,
                SCREEN_INDEX,
                FRAME_INDEX,
                SCHEDULE_ID,
                START_DATE,
                STOP_DATE,
                START_TIME,
                DURATION,
                REPEAT_TYPE,
                WEEKDAYS,
                MONTHDAYS,
                USER_ID,
                CREATE_DATE,
                MODIFY_DATE,
                SCHEDULE_TYPE,
                HW_INPUT_SOURCE,
                HW_ATVDTV,
                HW_AIRCABLE,
                HW_MAJORCH,
                HW_MINORCH,
                HW_VOLUME,
                HW_SCH_CH,
                CONTENT_ID,
                CONTENT_TYPE,
                REPEAT_TIME,
                IN_EFFECT_TYPE,
                IN_EFFECT_DURATION,
                IN_EFFECT_DIRECTION,
                OUT_EFFECT_TYPE,
                OUT_EFFECT_DURATION,
                OUT_EFFECT_DIRECTION,
                CHANNEL_NO,
                PRIORITY,
                IS_STREAMING,
                SLIDE_TRANSITION_TIME
            FROM
                MI_CDS_INFO_SCHEDULE_TEMP
            WHERE
                PROGRAM_ID = #{program_id} AND SESSION_ID = #{sessionId}
    </insert>

    <insert id="insert5_addProgram">
        INSERT INTO MI_CDS_INFO_FRAME (PROGRAM_ID, SCREEN_INDEX, CHANNEL_NO, FRAME_INDEX, FRAME_NAME, X, Y, WIDTH, HEIGHT
            , DEFAULT_CONTENT_ID, IS_MAIN_FRAME, FRAME_ID, VERSION, LINE_DATA)
            SELECT
                PROGRAM_ID,
                SCREEN_INDEX,
               	CHANNEL_NO,
                FRAME_INDEX,
                FRAME_NAME,
                X,
                Y,
                WIDTH,
                HEIGHT,
                DEFAULT_CONTENT_ID,
                IS_MAIN_FRAME,
                FRAME_ID,
                VERSION,
                LINE_DATA
            FROM
                MI_CDS_INFO_FRAME_TEMP
            WHERE
                PROGRAM_ID = #{program_id} AND SESSION_ID = #{sessionId}
            ORDER BY
                FRAME_INDEX ASC
    </insert>

    <insert id="insert6_addProgram">
        INSERT INTO MI_CDS_MAP_FRAME_USER (PROGRAM_ID, FRAME_ID, USER_GROUP_ID)
            SELECT
                PROGRAM_ID,
                FRAME_ID,
                USER_GROUP_ID
            FROM MI_CDS_MAP_FRAME_USER_TEMP
            WHERE PROGRAM_ID = #{program_id} AND SESSION_ID = #{sessionId}
    </insert>

    <insert id="insertIntoMiCdsInfoChannel_addProgram">
    	INSERT INTO MI_CDS_INFO_CHANNEL (PROGRAM_ID, CHANNEL_NO, CHANNEL_NAME, CHANNEL_DESCRIPTION )
    	SELECT PROGRAM_ID, CHANNEL_NO, CHANNEL_NAME, CHANNEL_DESCRIPTION
    	FROM MI_CDS_INFO_CHANNEL_TEMP
    	WHERE PROGRAM_ID = #{program_id} AND SESSION_ID = #{sessionId}
    	ORDER BY CHANNEL_NO ASC
    </insert>

    <insert id="insert7_addProgram">
        INSERT INTO MI_CDS_LOG_PROGRAM (LOG_ID, PROGRAM_ID, EVENT_TIME, EVENT_TYPE, USER_ID, PROGRAM_NAME, IP_ADDRESS)
        VALUES (#{logId}, #{program.program_id},<include refid="utils.currentTimestamp"/>, #{logProgramCreate}
        , #{program.user_id}, #{program.program_name}, #{ipAddress})
    </insert>

    <insert id="insert_addDefaultProgram">
        INSERT INTO MI_CDS_INFO_PROGRAM (PROGRAM_ID, VERSION, PROGRAM_NAME, PROGRAM_TYPE
        , DEPLOY_TIME, SCHEDULE_LIMIT, IS_DEFAULT, IS_MEDIA_MERCHANT
        , SCREEN_COUNT, USER_ID, CREATE_DATE
        , MODIFY_DATE, BGM_CONTENT_ID, IS_BGM_WITH_CONTENT
        , DESCRIPTION, ASPECT_RATIO, DELETED, FRAME_LAYOUT_TYPE
        , RESOLUTION, SYNCHRONIZATION, RESUME, DEVICE_TYPE, USE_MULTI_VWL, USE_SYNC_PLAY)
        VALUES (#{program.program_id}, #{program.version}, #{program.program_name}, #{program.program_type}
        , #{program.deploy_time}, #{program.schedult_limit}, #{program.is_default}, #{program.is_media_merchant}
        , #{program.screen_count}, #{program.user_id},<include refid="utils.currentTimestamp"/>,
        <include refid="utils.currentTimestamp"/>, #{program.bgm_content_id}, #{program.is_bgm_with_content}
        , #{program.description}, #{program.aspect_ratio}, #{program.deleted}, #{program.frame_layout_type}
        , #{program.resolution}, #{program.synchronization}, #{program.resume}, #{program.device_type}, #{program.use_multi_vwl},  #{program.use_sync_play})
    </insert>

    <insert id="insert_miCdsInfoChannelTemp_addDefaultProgram">
    	INSERT INTO MI_CDS_INFO_CHANNEL_TEMP (PROGRAM_ID, CHANNEL_NO, CHANNEL_NAME, CHANNEL_DESCRIPTION) 
    	VALUES (#{channel.program_id}, #{channel.channel_no}, #{channel.channel_name}, #{channel.channel_description})
    </insert>

    <insert id="insert2_addDefaultProgram">
        INSERT INTO MI_CDS_MAP_PROGRAM_DEVICE (PROGRAM_ID, DEVICE_GROUP_ID) VALUES (#{program_id}, #{device_id})
    </insert>

    <insert id="insert3_addDefaultProgram">
        INSERT INTO MI_CDS_INFO_FRAME (PROGRAM_ID, SCREEN_INDEX, FRAME_INDEX, FRAME_NAME
            , X, Y, WIDTH, HEIGHT, DEFAULT_CONTENT_ID
            , IS_MAIN_FRAME, FRAME_ID, VERSION, LINE_DATA)
            VALUES (#{frame.program_id}, #{frame.screen_index}, #{frame.frame_index}, #{frame.frame_name}
                , #{frame.x}, #{frame.y}, #{frame.width}, #{frame.height}, #{frame.default_content_id}
                , #{frame.is_main_frame}, #{frame.frame_id}, #{frame.version}, #{frame.line_data})
    </insert>

    <insert id="insert4_addDefaultProgram">
        INSERT INTO MI_CDS_MAP_PROGRAM_GROUP (PROGRAM_ID, GROUP_ID) VALUES (#{program_id}, #{orgGroupId})
    </insert>

    <insert id="insert5_addDefaultProgram">
        INSERT INTO MI_CDS_LOG_PROGRAM (LOG_ID, PROGRAM_ID, EVENT_TIME, EVENT_TYPE, USER_ID, PROGRAM_NAME, IP_ADDRESS)
        VALUES (#{logId}, #{program.program_id},<include refid="utils.currentTimestamp"/>, #{logProgramCreate}
        , #{program.user_id}, #{program.program_name}, #{ipAddress})
    </insert>

    <insert id="insert_updateProgram">
        INSERT INTO MI_CDS_MAP_PROGRAM_DEVICE (PROGRAM_ID, DEVICE_GROUP_ID) VALUES (#{program_id}, #{device_id})
    </insert>

    <insert id="insert2_updateProgram">
        INSERT INTO MI_CDS_INFO_SCHEDULE (PROGRAM_ID, PLAYER_MODE, SAFETYLOCK, SCREEN_INDEX, CHANNEL_NO, FRAME_INDEX, SCHEDULE_ID, START_DATE, STOP_DATE, START_TIME, DURATION, REPEAT_TYPE, WEEKDAYS, MONTHDAYS, USER_ID, CREATE_DATE, MODIFY_DATE, SCHEDULE_TYPE, HW_INPUT_SOURCE, HW_ATVDTV, HW_AIRCABLE, HW_MAJORCH, HW_MINORCH, HW_VOLUME, HW_SCH_CH, CONTENT_ID, CONTENT_TYPE, REPEAT_TIME, IN_EFFECT_TYPE, IN_EFFECT_DURATION, IN_EFFECT_DIRECTION, OUT_EFFECT_TYPE, OUT_EFFECT_DURATION, OUT_EFFECT_DIRECTION, PRIORITY, IS_STREAMING, SLIDE_TRANSITION_TIME)
            SELECT
                PROGRAM_ID,
                PLAYER_MODE, 
                SAFETYLOCK,
                SCREEN_INDEX,
                CHANNEL_NO,
                FRAME_INDEX,
                SCHEDULE_ID,
                START_DATE,
                STOP_DATE,
                START_TIME,
                DURATION,
                REPEAT_TYPE,
                WEEKDAYS,
                MONTHDAYS,
                USER_ID,
                CREATE_DATE,
                MODIFY_DATE,
                SCHEDULE_TYPE,
                HW_INPUT_SOURCE,
                HW_ATVDTV,
                HW_AIRCABLE,
                HW_MAJORCH,
                HW_MINORCH,
                HW_VOLUME,
                HW_SCH_CH,
                CONTENT_ID,
                CONTENT_TYPE,
                REPEAT_TIME,
                IN_EFFECT_TYPE,
                IN_EFFECT_DURATION,
                IN_EFFECT_DIRECTION,
                OUT_EFFECT_TYPE,
                OUT_EFFECT_DURATION,
                OUT_EFFECT_DIRECTION,
                PRIORITY,
                IS_STREAMING,
                SLIDE_TRANSITION_TIME
            FROM
                MI_CDS_INFO_SCHEDULE_TEMP
            WHERE
                PROGRAM_ID = #{program_id} AND SESSION_ID = #{sessionId}
    </insert>

    <insert id="insert3_updateProgram">
        INSERT INTO MI_CDS_INFO_FRAME (PROGRAM_ID, CHANNEL_NO, SCREEN_INDEX, FRAME_INDEX, FRAME_NAME, X, Y, WIDTH, HEIGHT, DEFAULT_CONTENT_ID, IS_MAIN_FRAME, FRAME_ID, VERSION, LINE_DATA)
            SELECT
                PROGRAM_ID,
                CHANNEL_NO,
                SCREEN_INDEX,
                FRAME_INDEX,
                FRAME_NAME,
                X,
                Y,
                WIDTH,
                HEIGHT,
                DEFAULT_CONTENT_ID,
                IS_MAIN_FRAME,
                FRAME_ID,
                VERSION,
                LINE_DATA
            FROM
                MI_CDS_INFO_FRAME_TEMP
            WHERE
                PROGRAM_ID = #{program_id} AND SESSION_ID = #{sessionId}
            ORDER BY
                FRAME_INDEX ASC
    </insert>

    <insert id="insert4_updateProgram">
        INSERT INTO MI_CDS_MAP_FRAME_USER (PROGRAM_ID, FRAME_ID, USER_GROUP_ID)
            SELECT
                PROGRAM_ID,
                FRAME_ID,
                USER_GROUP_ID
            FROM MI_CDS_MAP_FRAME_USER_TEMP
            WHERE PROGRAM_ID = #{program_id} AND SESSION_ID = #{sessionId}
    </insert>

    <insert id="insert5_updateProgram">
        INSERT INTO MI_CDS_LOG_PROGRAM (LOG_ID, PROGRAM_ID, EVENT_TIME, EVENT_TYPE, USER_ID,PROGRAM_NAME,IP_ADDRESS)
        VALUES (#{logId}, #{program.program_id},<include refid="utils.currentTimestamp"/>, #{logProgramEdit}
        , #{program.user_id}, #{program.program_name}, #{ipAddress})
    </insert>

    <insert id="insert2_addContentSchedule">
        INSERT INTO MI_CDS_INFO_SCHEDULE_TEMP (SESSION_ID, PROGRAM_ID, PLAYER_MODE
        , SAFETYLOCK, CHANNEL_NO, SCREEN_INDEX, FRAME_INDEX
        , SCHEDULE_ID, START_DATE, STOP_DATE, START_TIME
        , DURATION, REPEAT_TYPE, WEEKDAYS, MONTHDAYS
        , USER_ID, CREATE_DATE, MODIFY_DATE
        , SCHEDULE_TYPE, HW_INPUT_SOURCE, HW_ATVDTV, HW_AIRCABLE
        , HW_MAJORCH, HW_MINORCH, HW_VOLUME, HW_SCH_CH, CONTENT_ID, CONTENT_TYPE, REPEAT_TIME, IN_EFFECT_TYPE, IN_EFFECT_DURATION,
        IN_EFFECT_DIRECTION, OUT_EFFECT_TYPE, OUT_EFFECT_DURATION, OUT_EFFECT_DIRECTION, PRIORITY, IS_STREAMING,
        SLIDE_TRANSITION_TIME)
        VALUES (#{schedule.session_id}, #{schedule.program_id}
        , #{schedule.player_mode}, #{schedule.safetyLock}, #{schedule.channel_no}
        , #{schedule.screen_index}, #{schedule.frame_index}
        , #{schedule.schedule_id}, #{schedule.start_date}, #{schedule.stop_date}, #{schedule.start_time}
        , #{schedule.duration}, #{schedule.repeat_type}, #{schedule.weekdays}, #{schedule.monthdays}
        , #{schedule.user_id},<include refid="utils.currentTimestamp"/>,
        <include refid="utils.currentTimestamp"/>
        , #{schedule.schedule_type}, #{schedule.hw_input_source}, #{schedule.hw_AtvDtv}, #{schedule.hw_AirCable}
        , #{schedule.hw_MajorCH}, #{schedule.hw_MinorCH}, #{schedule.hw_Volume}, #{schedule.hw_sch_ch}, #{schedule.content_id}
        , #{schedule.content_type}, #{schedule.repeat_time}, #{schedule.in_effect_type}
        , #{schedule.in_effect_duration}, #{schedule.in_effect_direction}, #{schedule.out_effect_type}
        , #{schedule.out_effect_duration}, #{schedule.out_effect_direction}, #{priority}, #{schedule.is_streaming}
        , #{schedule.slide_transition_time})
    </insert>

    <insert id="insert_transferProgramDataToMain">
        INSERT INTO MI_CDS_INFO_SCHEDULE (PROGRAM_ID, PLAYER_MODE, SAFETYLOCK, CHANNEL_NO, SCREEN_INDEX, FRAME_INDEX, SCHEDULE_ID, START_DATE, STOP_DATE, START_TIME, DURATION, REPEAT_TYPE, WEEKDAYS, MONTHDAYS, USER_ID, CREATE_DATE, MODIFY_DATE, SCHEDULE_TYPE, HW_INPUT_SOURCE, HW_ATVDTV, HW_AIRCABLE, HW_MAJORCH, HW_MINORCH, HW_VOLUME, HW_SCH_CH, CONTENT_ID, CONTENT_TYPE, REPEAT_TIME, IN_EFFECT_TYPE, IN_EFFECT_DURATION, IN_EFFECT_DIRECTION, OUT_EFFECT_TYPE, OUT_EFFECT_DURATION, OUT_EFFECT_DIRECTION, PRIORITY, IS_STREAMING, SLIDE_TRANSITION_TIME)
            SELECT
                PROGRAM_ID,
                PLAYER_MODE, 
                SAFETYLOCK, 
                CHANNEL_NO,
                SCREEN_INDEX,
                FRAME_INDEX,
                SCHEDULE_ID,
                START_DATE,
                STOP_DATE,
                START_TIME,
                DURATION,
                REPEAT_TYPE,
                WEEKDAYS,
                MONTHDAYS,
                USER_ID,
                CREATE_DATE,
                MODIFY_DATE,
                SCHEDULE_TYPE,
                HW_INPUT_SOURCE,
                HW_ATVDTV,
                HW_AIRCABLE,
                HW_MAJORCH,
                HW_MINORCH,
                HW_VOLUME,
                HW_SCH_CH,
                CONTENT_ID,
                CONTENT_TYPE,
                REPEAT_TIME,
                IN_EFFECT_TYPE,
                IN_EFFECT_DURATION,
                IN_EFFECT_DIRECTION,
                OUT_EFFECT_TYPE,
                OUT_EFFECT_DURATION,
                OUT_EFFECT_DIRECTION,
                PRIORITY,
                IS_STREAMING,
                SLIDE_TRANSITION_TIME
            FROM
                MI_CDS_INFO_SCHEDULE_TEMP
            WHERE
                PROGRAM_ID = #{programId} AND SESSION_ID = #{sessionId}
    </insert>

    <insert id="insert2_transferProgramDataToMain">
        INSERT INTO MI_CDS_INFO_FRAME (PROGRAM_ID, CHANNEL_NO, SCREEN_INDEX, FRAME_INDEX, FRAME_NAME, X, Y, WIDTH, HEIGHT, DEFAULT_CONTENT_ID, IS_MAIN_FRAME, FRAME_ID, VERSION, LINE_DATA)
            SELECT
                PROGRAM_ID,
                CHANNEL_NO,
                SCREEN_INDEX,
                FRAME_INDEX,
                FRAME_NAME,
                X,
                Y,
                WIDTH,
                HEIGHT,
                DEFAULT_CONTENT_ID,
                IS_MAIN_FRAME,
                FRAME_ID,
                VERSION,
                LINE_DATA
            FROM
                MI_CDS_INFO_FRAME_TEMP
            WHERE
                PROGRAM_ID = #{programId} AND SESSION_ID = #{sessionId}
            ORDER BY
                FRAME_INDEX ASC
    </insert>

    <insert id="insert3_transferProgramDataToMain">
        INSERT INTO MI_CDS_MAP_FRAME_USER (PROGRAM_ID, FRAME_ID, USER_GROUP_ID)
            SELECT
                PROGRAM_ID,
                FRAME_ID,
                USER_GROUP_ID
            FROM MI_CDS_MAP_FRAME_USER_TEMP
            WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{sessionId}
    </insert>

    <insert id="insert4_transferProgramDataToMain">
        INSERT INTO MI_CDS_LOG_PROGRAM (LOG_ID, PROGRAM_ID, EVENT_TIME, EVENT_TYPE, USER_ID, PROGRAM_NAME)
        VALUES (#{logId}, #{programId},<include refid="utils.currentTimestamp"/>, #{logContentScheduleEdit}, #{userId})
    </insert>

    <insert id="insert_transferProgramDataToTemp">
        INSERT INTO MI_CDS_INFO_SCHEDULE_TEMP (SESSION_ID, PROGRAM_ID, PLAYER_MODE, SAFETYLOCK, CHANNEL_NO, SCREEN_INDEX, FRAME_INDEX, SCHEDULE_ID
            , START_DATE, STOP_DATE, START_TIME, DURATION, REPEAT_TYPE, WEEKDAYS, MONTHDAYS, USER_ID, CREATE_DATE
            , MODIFY_DATE, SCHEDULE_TYPE, HW_INPUT_SOURCE, HW_ATVDTV, HW_AIRCABLE, HW_MAJORCH, HW_MINORCH, HW_VOLUME, HW_SCH_CH
            , CONTENT_ID, CONTENT_TYPE, REPEAT_TIME, IN_EFFECT_TYPE, IN_EFFECT_DURATION, IN_EFFECT_DIRECTION
            , OUT_EFFECT_TYPE, OUT_EFFECT_DURATION, OUT_EFFECT_DIRECTION, PRIORITY, IS_STREAMING, SLIDE_TRANSITION_TIME)
            SELECT
                #{sessionId},
                PROGRAM_ID, 
                PLAYER_MODE, 
                SAFETYLOCK, 
                CHANNEL_NO,
                SCREEN_INDEX,
                FRAME_INDEX,
                SCHEDULE_ID,
                START_DATE,
                STOP_DATE,
                START_TIME,
                DURATION,
                REPEAT_TYPE,
                WEEKDAYS,
                MONTHDAYS,
                USER_ID,
                CREATE_DATE,
                MODIFY_DATE,
                SCHEDULE_TYPE,
                HW_INPUT_SOURCE,
                HW_ATVDTV,
                HW_AIRCABLE,
                HW_MAJORCH,
                HW_MINORCH,
                HW_VOLUME,
                HW_SCH_CH,
                CONTENT_ID,
                CONTENT_TYPE,
                REPEAT_TIME,
                IN_EFFECT_TYPE,
                IN_EFFECT_DURATION,
                IN_EFFECT_DIRECTION,
                OUT_EFFECT_TYPE,
                OUT_EFFECT_DURATION,
                OUT_EFFECT_DIRECTION,
                PRIORITY,
                IS_STREAMING,
                SLIDE_TRANSITION_TIME
            FROM
                MI_CDS_INFO_SCHEDULE
            WHERE
                PROGRAM_ID = #{programId}
    </insert>

    <insert id="insert2_transferProgramDataToTemp">
        INSERT INTO MI_CDS_INFO_FRAME_TEMP (SESSION_ID, PROGRAM_ID, CHANNEL_NO, SCREEN_INDEX, FRAME_INDEX, FRAME_NAME, X, Y, WIDTH
            , HEIGHT, DEFAULT_CONTENT_ID, IS_MAIN_FRAME, FRAME_ID, VERSION, LINE_DATA)
            SELECT
                #{sessionId},
                PROGRAM_ID,
                CHANNEL_NO,
                SCREEN_INDEX,
                FRAME_INDEX,
                FRAME_NAME,
                X,
                Y,
                WIDTH,
                HEIGHT,
                DEFAULT_CONTENT_ID,
                IS_MAIN_FRAME,
                FRAME_ID,
                VERSION,
                LINE_DATA
            FROM MI_CDS_INFO_FRAME
            WHERE PROGRAM_ID = #{programId}
            ORDER BY FRAME_INDEX ASC
    </insert>

    <insert id="insert3_transferProgramDataToTemp">
        INSERT INTO MI_CDS_MAP_FRAME_USER_TEMP (SESSION_ID, PROGRAM_ID, FRAME_ID, USER_GROUP_ID)
            SELECT
                #{sessionId},
                PROGRAM_ID,
                FRAME_ID,
                USER_GROUP_ID
            FROM MI_CDS_MAP_FRAME_USER
            WHERE PROGRAM_ID = #{programId}
    </insert>

    <insert id="insert_transferScheduleDataToTempWithNewId">
        INSERT INTO MI_CDS_INFO_SCHEDULE_TEMP (PROGRAM_ID, SESSION_ID
        	, PLAYER_MODE, SAFETYLOCK, CHANNEL_NO
        	, SCREEN_INDEX, FRAME_INDEX
            , SCHEDULE_ID, START_DATE, STOP_DATE, START_TIME
            , DURATION, REPEAT_TYPE, WEEKDAYS, MONTHDAYS
            , USER_ID, CREATE_DATE, MODIFY_DATE, SCHEDULE_TYPE
            , HW_INPUT_SOURCE, HW_ATVDTV, HW_AIRCABLE, HW_MAJORCH
            , HW_MINORCH, HW_VOLUME, HW_SCH_CH, CONTENT_ID, CONTENT_TYPE
            , REPEAT_TIME, IN_EFFECT_TYPE, IN_EFFECT_DURATION, IN_EFFECT_DIRECTION
            , OUT_EFFECT_TYPE, OUT_EFFECT_DURATION, OUT_EFFECT_DIRECTION, PRIORITY, IS_STREAMING
            , SLIDE_TRANSITION_TIME)
            VALUES (#{newProgramId}, #{sessionId}
            	, #{schedule.player_mode}, #{schedule.safetyLock}, #{schedule.channel_no}
            	, #{schedule.screen_index}, #{schedule.frame_index}
                , #{scheduleId}, #{schedule.start_date}, #{schedule.stop_date}, #{schedule.start_time}
                , #{schedule.duration}, #{schedule.repeat_type}, #{schedule.weekdays}, #{schedule.monthdays}
                , #{schedule.user_id}, #{schedule.create_date}, #{schedule.modify_date}, #{schedule.schedule_type}
                , #{schedule.hw_input_source}, #{schedule.hw_AtvDtv}, #{schedule.hw_AirCable}, #{schedule.hw_MajorCH}
                , #{schedule.hw_MinorCH}, #{schedule.hw_Volume}, #{schedule.hw_sch_ch}, #{schedule.content_id}, #{schedule.content_type}
                , #{schedule.repeat_time}, #{schedule.in_effect_type}, #{schedule.in_effect_duration}
                , #{schedule.in_effect_direction}, #{schedule.out_effect_type}, #{schedule.out_effect_duration}
                , #{schedule.out_effect_direction}, #{schedule.priority}, #{schedule.is_streaming}
                , #{schedule.slide_transition_time})
    </insert>

    <insert id="insert2_transferScheduleDataToTempWithNewId">
        INSERT INTO MI_CDS_INFO_FRAME_TEMP (PROGRAM_ID, SESSION_ID, CHANNEL_NO, SCREEN_INDEX, FRAME_INDEX, FRAME_NAME
            , X, Y, WIDTH, HEIGHT, DEFAULT_CONTENT_ID
            , IS_MAIN_FRAME, FRAME_ID, VERSION, LINE_DATA)
            VALUES (#{newProgramId}, #{sessionId}, #{frame.channel_no}
            	, #{frame.screen_index}, #{frame.frame_index}, #{frame.frame_name}
                , #{frame.x}, #{frame.y}, #{frame.width}, #{frame.height}, #{frame.default_content_id}
                , #{frame.is_main_frame}, #{frameId}, 1, #{frame.line_data})
    </insert>
    
    <insert id="insertSyncScheduleMatchInfoTemp">
        INSERT INTO MI_CDS_MAP_SYNC_DEVICE_TEMP (SESSION_ID, SCHEDULE_ID, PLAYLIST_ID, SYNC_PLAY_ID, DEVICE_ID)
        VALUES (#{syncschedule.session_id}, #{syncschedule.schedule_id}, #{syncschedule.playlist_id}, #{syncschedule.sync_play_id}, #{syncschedule.device_id})
    </insert>
    
     <insert id="insertSyncScheduleMatchInfo">
        INSERT INTO MI_CDS_MAP_SYNC_DEVICE (SCHEDULE_ID, PLAYLIST_ID, SYNC_PLAY_ID, DEVICE_ID)
        SELECT SCHEDULE_ID, PLAYLIST_ID, SYNC_PLAY_ID, DEVICE_ID FROM MI_CDS_MAP_SYNC_DEVICE_TEMP WHERE SCHEDULE_ID = #{scheduleId} ORDER BY SYNC_PLAY_ID ASC
    </insert>
    
    <insert id="transferSyncScheduleMatchInfoToTemp">
        INSERT INTO MI_CDS_MAP_SYNC_DEVICE_TEMP (SESSION_ID, SCHEDULE_ID, PLAYLIST_ID, SYNC_PLAY_ID, DEVICE_ID)
        SELECT #{sessionId}, SCHEDULE_ID, PLAYLIST_ID, SYNC_PLAY_ID, DEVICE_ID FROM MI_CDS_MAP_SYNC_DEVICE WHERE SCHEDULE_ID = #{scheduleId} ORDER BY SYNC_PLAY_ID ASC
    </insert>
    
    <delete id="deleteSyncScheduleMatchInfoTemp">
        DELETE FROM MI_CDS_MAP_SYNC_DEVICE_TEMP
        WHERE SCHEDULE_ID = #{scheduleId}
    </delete>
    
    <delete id="deleteDownloadStatus">
    	DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS WHERE PROGRAM_ID = #{programId}
    </delete>
    
    <delete id="deleteSyncScheduleMatchInfo">
        DELETE FROM MI_CDS_MAP_SYNC_DEVICE
        WHERE SCHEDULE_ID = #{scheduleId}
    </delete>
    
    <select id="countSyncScheduleMatchInfoTemp" resultType="int">
        SELECT
            COUNT(DEVICE_ID)
        FROM MI_CDS_MAP_SYNC_DEVICE_TEMP
        WHERE SCHEDULE_ID = #{scheduleId}
    </select>
    
    <select id="countSyncScheduleMatchInfo" resultType="com.samsung.magicinfo.framework.scheduler.entity.SyncSchedule">
        SELECT
            COUNT(DEVICE_ID)
        FROM MI_CDS_MAP_SYNC_DEVICE
        WHERE SCHEDULE_ID = #{scheduleId}
    </select>
    
    <select id="getSyncGroupListPerSchedule" resultType="String">
        SELECT DISTINCT SYNC_PLAY_ID FROM MI_CDS_MAP_SYNC_DEVICE WHERE SCHEDULE_ID = #{scheduleId} ORDER BY SYNC_PLAY_ID ASC
    </select>
    
    <select id="getSyncDeviceIdListPerSchedule" resultType="String">
        SELECT  DEVICE_ID FROM MI_CDS_MAP_SYNC_DEVICE WHERE SCHEDULE_ID = #{scheduleId} AND PLAYLIST_ID = #{playlistId} AND SYNC_PLAY_ID = #{syncPlayId} ORDER BY SYNC_PLAY_ID ASC
    </select>
    
    <select id="getSyncScheduleMatchInfoTemp" resultType="com.samsung.magicinfo.framework.scheduler.entity.SyncSchedule">
        SELECT
            *
        FROM MI_CDS_MAP_SYNC_DEVICE_TEMP
        WHERE SCHEDULE_ID = #{scheduleId} ORDER BY SYNC_PLAY_ID ASC
    </select>
    
     <select id="getSyncScheduleMatchInfo" resultType="com.samsung.magicinfo.framework.scheduler.entity.SyncSchedule">
        SELECT
            *
        FROM MI_CDS_MAP_SYNC_DEVICE
        WHERE SCHEDULE_ID = #{scheduleId}
    </select>

    <insert id="addPanelOrZeroFrameSchedule">
        INSERT INTO MI_CDS_INFO_SCHEDULE_TEMP (SESSION_ID, PROGRAM_ID, SCHEDULE_ID, START_TIME
        , DURATION, REPEAT_TYPE, WEEKDAYS, USER_ID
        , CREATE_DATE
        , MODIFY_DATE, SCHEDULE_TYPE, HW_INPUT_SOURCE
        , HW_ATVDTV, HW_AIRCABLE, HW_MAJORCH, HW_MINORCH
        , HW_VOLUME, HW_SCH_CH)
        VALUES (#{schedule.session_id}, #{schedule.program_id}, #{schedule.schedule_id}, #{schedule.start_time}
        , #{schedule.duration}, #{schedule.repeat_type}, #{schedule.weekdays}, #{schedule.user_id}
        ,<include refid="utils.currentTimestamp"/>,
        <include refid="utils.currentTimestamp"/>, #{schedule.schedule_type}, #{schedule.hw_input_source}
        , #{schedule.hw_AtvDtv}, #{schedule.hw_AirCable}, #{schedule.hw_MajorCH}, #{schedule.hw_MinorCH}
        , #{schedule.hw_Volume}, #{schedule.hw_sch_ch})
    </insert>

    <insert id="insert_deleteSchedule">
        INSERT INTO MI_CDS_INFO_SCHEDULE_TEMP (SESSION_ID, SCHEDULE_ID, IS_DELETE)
        VALUES (#{sessionId}, #{scheduleId}, <include refid="isTrue"/>)
    </insert>

    <sql id="isTrue">
        #{is_delete}
    </sql>

    <sql id="isTrue" databaseId="mssql">
        <choose>
            <when test="#{is_delete}">1</when>
            <otherwise>0</otherwise>
        </choose>
    </sql>

    <insert id="insert_setProgram">
        INSERT INTO MI_CDS_MAP_PROGRAM_GROUP (PROGRAM_ID, GROUP_ID)
            VALUES (#{program.program_id}, #{program.program_group_id})
    </insert>

    <insert id="insert2_setProgram">
        INSERT INTO MI_CDS_MAP_PROGRAM_DEVICE (PROGRAM_ID, DEVICE_GROUP_ID) VALUES (#{program_id}, #{deviceId})
    </insert>

    <insert id="insert_setActiveProgramVersion">
        INSERT INTO MI_CDS_INFO_ACTIVE_PROGRAM (PROGRAM_ID, VERSION) VALUES (#{programId}, #{version})
    </insert>

    <insert id="saveFrameTemplate">
        INSERT INTO MI_CDS_INFO_FRAME_TEMPLATE (TEMPLATE_ID, TEMPLATE_NAME, TEMPLATE_TYPE, TEMPLATE_DATA, RESOLUTION
            , CREATE_USER_ID, ORGANIZATION)
            VALUES (#{templateId}, #{fte.template_name}, #{fte.template_type}, #{fte.template_data}, #{fte.resolution}
                , #{fte.create_user_id}, #{fte.organization})
    </insert>

    <insert id="insert_addProgramWithBasicInformation">
        INSERT INTO MI_CDS_INFO_PROGRAM (PROGRAM_ID, VERSION, PROGRAM_NAME, PROGRAM_TYPE
        , DEPLOY_TIME, SCHEDULE_LIMIT, IS_DEFAULT, IS_MEDIA_MERCHANT
        , SCREEN_COUNT, USER_ID
        , CREATE_DATE
        , MODIFY_DATE, BGM_CONTENT_ID, IS_BGM_WITH_CONTENT
        , DESCRIPTION, ASPECT_RATIO, DELETED, FRAME_LAYOUT_TYPE
        , RESOLUTION, SYNCHRONIZATION, RESUME, DEVICE_TYPE, DEVICE_TYPE_VERSION)
        VALUES (#{program.program_id}, #{program.version}, #{program.program_name}, #{program.program_type}
        , #{program.deploy_time}, #{program.schedult_limit}, #{program.is_default}, #{program.is_media_merchant}
        , #{program.screen_count}, #{program.user_id},
        <include refid="utils.currentTimestamp"/>
        ,<include refid="utils.currentTimestamp"/>, #{program.bgm_content_id}, #{program.is_bgm_with_content}
        , #{program.description}, #{program.aspect_ratio}, #{program.deleted}, #{program.frame_layout_type}
        , #{program.resolution}, #{program.synchronization}, #{program.resume}, #{program.device_type}, #{program.device_type_version})
    </insert>

    <insert id="insert2_addProgramWithBasicInformation">
        INSERT INTO MI_CDS_MAP_PROGRAM_GROUP (PROGRAM_ID, GROUP_ID)
            VALUES (#{program.program_id}, #{program.program_group_id})
    </insert>

    <insert id="insert3_addProgramWithBasicInformation">
        INSERT INTO MI_CDS_MAP_PROGRAM_DEVICE (PROGRAM_ID, DEVICE_GROUP_ID) VALUES (#{program_id}, #{device_id})
    </insert>

    <insert id="insert4_addProgramWithBasicInformation">
        INSERT INTO MI_CDS_INFO_FRAME (PROGRAM_ID, SCREEN_INDEX, FRAME_INDEX, FRAME_NAME
            , X, Y, WIDTH, HEIGHT, DEFAULT_CONTENT_ID
            , IS_MAIN_FRAME, FRAME_ID, VERSION, LINE_DATA, CHANNEL_NO)
            VALUES (#{frame.program_id}, #{frame.screen_index}, #{frame.frame_index}, #{frame.frame_name}
                , #{frame.x}, #{frame.y}, #{frame.width}, #{frame.height}, #{frame.default_content_id}
                , #{frame.is_main_frame}, #{frame.frame_id}, #{frame.version}, #{frame.line_data}, #{frame.channel_no})
    </insert>

    <insert id="insert5_addProgramWithBasicInformation">
        INSERT INTO MI_CDS_MAP_FRAME_USER (PROGRAM_ID, FRAME_ID, USER_GROUP_ID, CHANNEL_NO)
            VALUES (#{frame.program_id}, #{frame.frame_id}, #{userGroupId}, #{frame.channel_no})
    </insert>

    <insert id="insert6_addProgramWithBasicInformation">
        INSERT INTO MI_CDS_LOG_PROGRAM (LOG_ID, PROGRAM_ID, EVENT_TIME, EVENT_TYPE, USER_ID,PROGRAM_NAME)
        VALUES (#{logId}, #{program.program_id},<include refid="utils.currentTimestamp"/>, #{logProgramCreate}
        , #{program.user_id}, #{program.program_name})
    </insert>

    <insert id="insert_modifyProgramWithFrameAndHWControlAndContent">
        INSERT INTO MI_CDS_MAP_PROGRAM_DEVICE (PROGRAM_ID, DEVICE_GROUP_ID)
            VALUES (#{program_id}, #{groupId})
    </insert>

    <insert id="insert2_modifyProgramWithFrameAndHWControlAndContent">
        INSERT INTO MI_CDS_MAP_FRAME_USER (PROGRAM_ID, FRAME_ID, USER_GROUP_ID)
            VALUES (#{frame.program_id}, #{frame.frame_id}, #{userGroupId})
    </insert>

    <insert id="insert3_modifyProgramWithFrameAndHWControlAndContent">
        INSERT INTO MI_CDS_LOG_PROGRAM (LOG_ID, PROGRAM_ID, EVENT_TIME, EVENT_TYPE, USER_ID,PROGRAM_NAME)
        VALUES (#{logId}, #{program.program_id},<include refid="utils.currentTimestamp"/>, #{logProgramEdit}
        , #{program.user_id}, #{program.program_name})
    </insert>

    <insert id="insert_addProgramWithFrameAndHWControlAndContent">
        INSERT INTO MI_CDS_INFO_PROGRAM (PROGRAM_ID, VERSION, PROGRAM_NAME, PROGRAM_TYPE
        , DEPLOY_TIME, SCHEDULE_LIMIT, IS_DEFAULT, IS_MEDIA_MERCHANT
        , SCREEN_COUNT, USER_ID
        , CREATE_DATE
        , MODIFY_DATE, BGM_CONTENT_ID, IS_BGM_WITH_CONTENT
        , DESCRIPTION, ASPECT_RATIO, DELETED, FRAME_LAYOUT_TYPE
        , RESOLUTION, SYNCHRONIZATION, RESUME)
        VALUES (#{program.program_id}, #{program.version}, #{program.program_name}, #{program.program_type}
        , #{program.deploy_time}, #{program.schedult_limit}, #{program.is_default}, #{program.is_media_merchant}
        , #{program.screen_count}, #{program.user_id},
        <include refid="utils.currentTimestamp"/>
        ,<include refid="utils.currentTimestamp"/>, #{program.bgm_content_id}, #{program.is_bgm_with_content}
        , #{program.description}, #{program.aspect_ratio}, #{program.deleted}, #{program.frame_layout_type},
        #{program.resolution}, #{program.synchronization}, #{program.resume})
    </insert>

    <insert id="insert2_addProgramWithFrameAndHWControlAndContent">
        INSERT INTO MI_CDS_MAP_PROGRAM_GROUP (PROGRAM_ID, GROUP_ID)
            VALUES (#{program.program_id}, #{program.program_group_id})
    </insert>

    <insert id="insert3_addProgramWithFrameAndHWControlAndContent">
        INSERT INTO MI_CDS_MAP_PROGRAM_DEVICE (PROGRAM_ID, DEVICE_GROUP_ID) VALUES (#{program_id}, #{deviceGroupId})
    </insert>

    <insert id="insert4_addProgramWithFrameAndHWControlAndContent">
        INSERT INTO MI_CDS_INFO_FRAME (PROGRAM_ID, SCREEN_INDEX, FRAME_INDEX, FRAME_NAME, X
            , Y, WIDTH, HEIGHT, DEFAULT_CONTENT_ID, IS_MAIN_FRAME
            , FRAME_ID, VERSION, LINE_DATA)
            VALUES (#{frame.program_id}, #{frame.screen_index}, #{frame.frame_index}, #{frame.frame_name}, #{frame.x},
                    #{frame.y}, #{frame.width}, #{frame.height}, #{frame.default_content_id}, #{frame.is_main_frame},
                    #{frame.frame_id}, #{frame.version}, #{frame.line_data})
    </insert>

    <insert id="insert5_addProgramWithFrameAndHWControlAndContent">
        INSERT INTO MI_CDS_MAP_FRAME_USER (PROGRAM_ID, FRAME_ID, USER_GROUP_ID)
            VALUES (#{frame.program_id}, #{frame.frame_id}, #{userGroupId})
    </insert>

    <insert id="insert6_addProgramWithFrameAndHWControlAndContent">
        INSERT INTO MI_CDS_INFO_SCHEDULE (PROGRAM_ID, PLAYER_MODE, SCREEN_INDEX, FRAME_INDEX, SCHEDULE_ID
        , START_DATE, STOP_DATE, START_TIME, DURATION
        , REPEAT_TYPE, WEEKDAYS, MONTHDAYS, USER_ID
        , CREATE_DATE
        , MODIFY_DATE, SCHEDULE_TYPE, HW_INPUT_SOURCE
        , HW_ATVDTV, HW_AIRCABLE, HW_MAJORCH, HW_MINORCH
        , HW_VOLUME, HW_SCH_CH, CONTENT_ID, CONTENT_TYPE, REPEAT_TIME
        , IN_EFFECT_TYPE, IN_EFFECT_DURATION, IN_EFFECT_DIRECTION
        , OUT_EFFECT_TYPE, OUT_EFFECT_DURATION, OUT_EFFECT_DIRECTION
        , PRIORITY, IS_STREAMING, SLIDE_TRANSITION_TIME, CHANNEL_NO, SAFETYLOCK)
        VALUES (#{schedule.program_id}, #{schedule.player_mode}, #{schedule.screen_index}, #{schedule.frame_index}, #{schedule.schedule_id}
        , #{schedule.start_date}, #{schedule.stop_date}, #{schedule.start_time}, #{schedule.duration}
        , #{schedule.repeat_type}, #{schedule.weekdays}, #{schedule.monthdays}, #{schedule.user_id}
        , #{schedule.create_date}, #{schedule.modify_date} 
        , #{schedule.schedule_type}, #{schedule.hw_input_source}
        , #{schedule.hw_AtvDtv}, #{schedule.hw_AirCable}, #{schedule.hw_MajorCH}, #{schedule.hw_MinorCH}
        , #{schedule.hw_Volume}, #{schedule.hw_sch_ch}, #{schedule.content_id}, #{schedule.content_type}, #{schedule.repeat_time}
        , #{schedule.in_effect_type}, #{schedule.in_effect_duration}, #{schedule.in_effect_direction}
        , #{schedule.out_effect_type}, #{schedule.out_effect_duration}, #{schedule.out_effect_direction}
        , #{priority}, #{schedule.is_streaming}, #{schedule.slide_transition_time}, #{schedule.channel_no}, #{schedule.safetyLock})
    </insert>

    <insert id="insert7_addProgramWithFrameAndHWControlAndContent">
        INSERT INTO MI_CDS_LOG_PROGRAM (LOG_ID, PROGRAM_ID, EVENT_TIME, EVENT_TYPE, USER_ID, PROGRAM_NAME)
        VALUES (#{logId}, #{program.program_id},<include refid="utils.currentTimestamp"/>, #{logProgramCreate}
        , #{program.user_id}, #{program.program_name})
    </insert>

    <insert id="insert_modifyProgramWithBasicInformation">
        INSERT INTO MI_CDS_MAP_PROGRAM_DEVICE (PROGRAM_ID, DEVICE_GROUP_ID) VALUES (#{program_id}, #{deviceGroupId})
    </insert>

    <insert id="insert2_modifyProgramWithBasicInformation">
        INSERT INTO MI_CDS_LOG_PROGRAM (LOG_ID, PROGRAM_ID, EVENT_TIME, EVENT_TYPE, USER_ID,PROGRAM_NAME)
        VALUES (#{logId}, #{program.program_id},<include refid="utils.currentTimestamp"/>, #{logProgramEdit}
        , #{program.user_id}, #{program.program_name})
    </insert>

    <insert id="insert_addContentScheduleWithoutTemp">
        INSERT INTO MI_CDS_INFO_SCHEDULE (PROGRAM_ID, PLAYER_MODE, SAFETYLOCK, CHANNEL_NO, SCREEN_INDEX, FRAME_INDEX, SCHEDULE_ID
        , START_DATE, STOP_DATE, START_TIME, DURATION
        , REPEAT_TYPE, WEEKDAYS, MONTHDAYS, USER_ID
        , CREATE_DATE, MODIFY_DATE, SCHEDULE_TYPE
        , HW_INPUT_SOURCE, HW_ATVDTV, HW_AIRCABLE, HW_MAJORCH
        , HW_MINORCH, HW_VOLUME, HW_SCH_CH, CONTENT_ID, CONTENT_TYPE
        , REPEAT_TIME, IN_EFFECT_TYPE, IN_EFFECT_DURATION
        , IN_EFFECT_DIRECTION, OUT_EFFECT_TYPE, OUT_EFFECT_DURATION,
        OUT_EFFECT_DIRECTION, PRIORITY, IS_STREAMING, SLIDE_TRANSITION_TIME)
        VALUES (#{schedule.program_id}, #{playerSingleMode}, 'false', #{schedule.channel_no}, #{schedule.screen_index}, #{schedule.frame_index}, #{schedule.schedule_id}
        , #{schedule.start_date}, #{schedule.stop_date}, #{schedule.start_time}, #{schedule.duration}
        , #{schedule.repeat_type}, #{schedule.weekdays}, #{schedule.monthdays}, #{schedule.user_id}
        ,<include refid="utils.currentTimestamp"/>,<include refid="utils.currentTimestamp"/>, #{schedule.schedule_type}
        , #{schedule.hw_input_source}, #{schedule.hw_AtvDtv}, #{schedule.hw_AirCable}, #{schedule.hw_MajorCH}
        , #{schedule.hw_MinorCH}, #{schedule.hw_Volume}, #{schedule.hw_sch_ch}, #{schedule.content_id}, #{schedule.content_type}
        , #{schedule.repeat_time}, #{schedule.in_effect_type}, #{schedule.in_effect_duration}
        , #{schedule.in_effect_direction}, #{schedule.out_effect_type}, #{schedule.out_effect_duration}
        , #{schedule.out_effect_direction}, #{priority}, #{schedule.is_streaming}, #{schedule.slide_transition_time})
    </insert>

    <insert id="insert_addHWConstraint">
        INSERT INTO MI_CDS_INFO_SCHEDULE (PROGRAM_ID, CHANNEL_NO, FRAME_INDEX, SCHEDULE_ID, START_TIME
        , DURATION, REPEAT_TYPE, WEEKDAYS, USER_ID
        , CREATE_DATE, MODIFY_DATE, SCHEDULE_TYPE
        , HW_INPUT_SOURCE, HW_ATVDTV, HW_AIRCABLE, HW_MAJORCH
        , HW_MINORCH, HW_VOLUME, HW_SCH_CH, PRIORITY, IS_STREAMING)
        VALUES (#{schedule.program_id}, #{defaultChannelNo}, #{schedule.frame_index}, #{schedule.schedule_id}, #{schedule.start_time}
        , #{schedule.duration}, #{schedule.repeat_type}, #{schedule.weekdays}, #{schedule.user_id}
        ,<include refid="utils.currentTimestamp"/>,<include refid="utils.currentTimestamp"/>, #{schedule.schedule_type}
        , #{schedule.hw_input_source}, #{schedule.hw_AtvDtv}, #{schedule.hw_AirCable}, #{schedule.hw_MajorCH}
        , #{schedule.hw_MinorCH}, #{schedule.hw_Volume}, #{schedule.hw_sch_ch}, #{schedule.priority}, #{schedule.is_streaming})
    </insert>

    <insert id="insert_addFrameWithoutTemp">
        INSERT INTO MI_CDS_INFO_FRAME (PROGRAM_ID, CHANNEL_NO, SCREEN_INDEX, FRAME_INDEX, FRAME_NAME, X
            , Y, WIDTH, HEIGHT, DEFAULT_CONTENT_ID, IS_MAIN_FRAME, FRAME_ID, VERSION, LINE_DATA)
            VALUES (#{frame.program_id}, #{frame.channel_no}, #{frame.screen_index}, #{frame.frame_index}, #{frame.frame_name}, #{frame.x}
                , #{frame.y}, #{frame.width}, #{frame.height}, #{frame.default_content_id}, #{frame.is_main_frame}
                , #{frame.frame_id}, #{frame.version}, #{frame.line_data})
    </insert>

    <insert id="insert2_addFrameWithoutTemp">
        INSERT INTO MI_CDS_MAP_FRAME_USER (PROGRAM_ID, FRAME_ID, USER_GROUP_ID)
            VALUES (#{frame.program_id}, #{frame.frame_id}, #{userGroupId})
    </insert>
    
    <insert id="insertSlot">
    	INSERT INTO MI_CDS_INFO_ADSLOT (PROGRAM_ID, FRAME_ID, SLOT_ID, SLOT_NAME, SLOT_INDEX, DURATION) VALUES (#{slot.program_id}, #{slot.frame_id}, #{slot.slot_id}, #{slot.slot_name}, #{slot.slot_index}, #{slot.duration})
    </insert>
    
    <insert id="insertAdSchedule">
    	INSERT INTO MI_CDS_INFO_ADSCHEDULE (PROGRAM_ID, CONTENT_TYPE, SCHEDULE_ID, START_DATE, STOP_DATE, START_TIME, STOP_TIME, DURATION, REPEAT_TYPE, USER_ID, CREATE_DATE, MODIFY_DATE, CONTENT_ID, SLOT_ID) VALUES (#{schedule.program_id}, #{schedule.content_type}, #{schedule.schedule_id}, #{schedule.start_date}, #{schedule.stop_date}, #{schedule.start_time}, #{schedule.stop_time}, #{schedule.duration}, #{schedule.repeat_type}, #{schedule.user_id}, #{schedule.create_date}, #{schedule.modify_date}, #{schedule.content_id}, #{schedule.slot_id})
    </insert>
    
    <update id="updateTemplate">
    	UPDATE MI_CDS_INFO_FRAME_TEMPLATE
    	SET TEMPLATE_DATA = #{template.template_data}, CREATE_USER_ID = #{template.create_user_id}, ORGANIZATION = #{template.organization}, TEMPLATE_TYPE = #{template.template_type}
    	WHERE TEMPLATE_ID = #{template.template_id}
    </update>

    <update id="mapDeviceGroupWithDefault">
        UPDATE MI_CDS_MAP_PROGRAM_DEVICE
        SET PROGRAM_ID = #{defaultProgramId}
        WHERE DEVICE_GROUP_ID = #{deviceGroupId}
    </update>

    <update id="updateDeviceGroupMappedInProgramAsDefault">
        UPDATE MI_CDS_MAP_PROGRAM_DEVICE
        SET PROGRAM_ID = #{program_id} FROM MI_CDS_MAP_PROGRAM_DEVICE AS A
            JOIN MI_CDS_MAP_PROGRAM_DEVICE_TEMP AS B
                ON A.DEVICE_GROUP_ID = B.DEVICE_GROUP_ID
        WHERE B.DEVICE_GROUP_ID = #{group_id}
    </update>

	<update id="updateDeviceGroupMappedInProgramAsDefault" databaseId="mysql">
        UPDATE MI_CDS_MAP_PROGRAM_DEVICE AS A JOIN MI_CDS_MAP_PROGRAM_DEVICE_TEMP AS B
        ON A.DEVICE_GROUP_ID = B.DEVICE_GROUP_ID
        SET A.PROGRAM_ID = #{program_id} 
        WHERE B.DEVICE_GROUP_ID = #{group_id}
    </update>
	
    <update id="updateDeviceGroupMappedInProgramAsDefaultByPid">
        UPDATE MI_CDS_MAP_PROGRAM_DEVICE
        SET PROGRAM_ID = #{program_id}
        WHERE DEVICE_GROUP_ID = #{group_id}
    </update>

    <update id="update_addProgram">
        UPDATE MI_CDS_INFO_PROGRAM
        SET VERSION = VERSION + 1
        WHERE PROGRAM_ID = #{defaultProgramId}
    </update>

    <update id="update_updateProgram">
        UPDATE MI_CDS_INFO_PROGRAM
        SET VERSION = (VERSION + 1)
        , PROGRAM_NAME = #{program.program_name}, PROGRAM_TYPE = #{program.program_type}
        , DEPLOY_TIME = #{program.deploy_time}, SCHEDULE_LIMIT = #{program.schedult_limit}
        , IS_DEFAULT = #{program.is_default}, IS_MEDIA_MERCHANT = #{program.is_media_merchant}
        , SCREEN_COUNT = #{program.screen_count}, USER_ID = #{program.user_id}
        , MODIFY_DATE =
        <include refid="utils.currentTimestamp"/>
        , BGM_CONTENT_ID = #{program.bgm_content_id}, IS_BGM_WITH_CONTENT = #{program.is_bgm_with_content}
        , DESCRIPTION = #{program.description}, FRAME_LAYOUT_TYPE = #{program.frame_layout_type}
        , RESOLUTION = #{program.resolution}, SYNCHRONIZATION = #{program.synchronization}
        , RESUME = #{program.resume}
        , DEVICE_TYPE = #{program.device_type}
        , DEVICE_TYPE_VERSION = #{program.device_type_version}
        , USE_MULTI_VWL = #{program.use_multi_vwl}
        , USE_SYNC_PLAY = #{program.use_sync_play}
        , USE_AD_SCHEDULE = #{program.use_ad_schedule}
        , AD_DURATION = #{program.ad_duration}
        , RESERVATION_REPEAT_TYPE = #{program.reservation_repeat_type}
        , RESERVATION_START_DATE = #{program.reservation_start_date}
        , RESERVATION_END_DATE = #{program.reservation_end_date}
        , RESERVATION_WEEKLY = #{program.reservation_weekly}
        , RESERVATION_MONTHLY = #{program.reservation_monthly}
        WHERE PROGRAM_ID = #{program.program_id}
    </update>

    <update id="update2_updateProgram">
        UPDATE MI_CDS_MAP_PROGRAM_GROUP
        SET PROGRAM_ID = #{program.program_id}, GROUP_ID = #{program.program_group_id}
        WHERE PROGRAM_ID = #{program.program_id}
    </update>

    <update id="update3_updateProgram">
        UPDATE MI_CDS_INFO_PROGRAM
        SET VERSION = VERSION + 1
        WHERE PROGRAM_ID = #{defaultProgramId}
    </update>

    <update id="updateProgramName">
        UPDATE MI_CDS_INFO_PROGRAM
        SET PROGRAM_NAME = #{program_name}
        WHERE PROGRAM_ID = #{programId}
    </update>

    <update id="update_addContentSchedule">
        UPDATE MI_CDS_INFO_FRAME_TEMP
        SET VERSION = VERSION + 1
        WHERE PROGRAM_ID = #{schedule.program_id} AND CHANNEL_NO=#{schedule.channel_no} AND SESSION_ID = #{schedule.session_id}
              AND FRAME_INDEX = #{schedule.frame_index}
    </update>

    <update id="update_transferProgramDataToMain">
        UPDATE MI_CDS_INFO_PROGRAM
        SET VERSION = VERSION + 1
        WHERE PROGRAM_ID = #{programId}
    </update>

    <update id="update_updateContentSchedule">
        UPDATE MI_CDS_INFO_FRAME_TEMP
        SET VERSION = VERSION + 1
        WHERE PROGRAM_ID = #{schedule.program_id} 
        	  AND SESSION_ID = #{schedule.session_id} 
        	  AND CHANNEL_NO= #{schedule.channel_no} 
              AND FRAME_INDEX = #{schedule.frame_index}
    </update>

    <update id="update2_updateContentSchedule">
        UPDATE MI_CDS_INFO_SCHEDULE_TEMP
        SET START_DATE = #{schedule.start_date}, STOP_DATE = #{schedule.stop_date}, START_TIME = #{schedule.start_time}
        , DURATION = #{schedule.duration}, REPEAT_TYPE = #{schedule.repeat_type}, WEEKDAYS = #{schedule.weekdays}
        , MONTHDAYS = #{schedule.monthdays}, MODIFY_DATE =
        <include refid="utils.currentTimestamp"/>
        , SCHEDULE_TYPE = #{schedule.schedule_type}
        , PLAYER_MODE = #{schedule.player_mode}, SAFETYLOCK =#{schedule.safetyLock}
        , HW_INPUT_SOURCE = #{schedule.hw_input_source}
        , HW_ATVDTV = #{schedule.hw_AtvDtv}, HW_AIRCABLE = #{schedule.hw_AirCable}
        , HW_MAJORCH = #{schedule.hw_MajorCH}, HW_MINORCH = #{schedule.hw_MinorCH}, HW_VOLUME = #{schedule.hw_Volume}
        , CONTENT_ID = #{schedule.content_id} ,CONTENT_TYPE = #{schedule.content_type}
        , REPEAT_TIME = #{schedule.repeat_time}, IN_EFFECT_TYPE = #{schedule.in_effect_type}
        , IN_EFFECT_DURATION = #{schedule.in_effect_duration}, IN_EFFECT_DIRECTION = #{schedule.in_effect_direction}
        , OUT_EFFECT_TYPE = #{schedule.out_effect_type}, OUT_EFFECT_DURATION = #{schedule.out_effect_duration}
        , OUT_EFFECT_DIRECTION = #{schedule.out_effect_direction}, PRIORITY = #{priority}
        , IS_STREAMING = #{schedule.is_streaming}, SLIDE_TRANSITION_TIME = #{schedule.slide_transition_time}
        WHERE SESSION_ID = #{schedule.session_id} AND PROGRAM_ID = #{schedule.program_id}
        AND CHANNEL_NO = #{schedule.channel_no}
        AND FRAME_INDEX = #{schedule.frame_index} AND SCHEDULE_ID = #{schedule.schedule_id}
        AND USER_ID = #{schedule.user_id}
    </update>

    <update id="updatePanelOrZeroFrameSchedule">
        UPDATE MI_CDS_INFO_SCHEDULE_TEMP
        SET SESSION_ID = #{schedule.session_id}, START_TIME = #{schedule.start_time}, DURATION = #{schedule.duration}
        , REPEAT_TYPE = #{schedule.repeat_type}, WEEKDAYS = #{schedule.weekdays}, USER_ID = #{schedule.user_id}
        , MODIFY_DATE =<include refid="utils.currentTimestamp"/>, SCHEDULE_TYPE = #{schedule.schedule_type}
        , HW_INPUT_SOURCE = #{schedule.hw_input_source}, HW_ATVDTV = #{schedule.hw_AtvDtv}
        , HW_AIRCABLE = #{schedule.hw_AirCable}, HW_MAJORCH = #{schedule.hw_MajorCH}
        , HW_MINORCH = #{schedule.hw_MinorCH}, HW_VOLUME = #{schedule.hw_Volume}
        , HW_SCH_CH = #{schedule.hw_sch_ch}
        WHERE PROGRAM_ID = #{schedule.program_id} AND SCHEDULE_ID = #{schedule.schedule_id}
    </update>

    <update id="update_setProgram">
        UPDATE MI_CDS_INFO_PROGRAM
        SET VERSION = VERSION + 1, PROGRAM_NAME = #{program.program_name}, PROGRAM_TYPE = #{program.program_type}
        , DEPLOY_TIME = #{program.deploy_time}, SCHEDULE_LIMIT = #{program.schedult_limit}
        , IS_DEFAULT = #{program.is_default}, IS_MEDIA_MERCHANT = #{program.is_media_merchant}
        , SCREEN_COUNT = #{program.screen_count}, USER_ID = #{program.user_id}
        , MODIFY_DATE =<include refid="utils.currentTimestamp"/>, BGM_CONTENT_ID = #{program.bgm_content_id}
        , IS_BGM_WITH_CONTENT = #{program.is_bgm_with_content}, DESCRIPTION = #{program.description}
        , FRAME_LAYOUT_TYPE = #{program.frame_layout_type}, RESOLUTION = #{program.resolution}, USE_MULTI_VWL = #{program.use_multi_vwl}, USE_SYNC_PLAY = #{program.use_sync_play}
        WHERE PROGRAM_ID = #{program.program_id}
    </update>

    <update id="programVersionUp">
        UPDATE MI_CDS_INFO_PROGRAM
        SET VERSION = VERSION + 1, MODIFY_DATE =
        <include refid="utils.currentTimestamp"/>
        WHERE PROGRAM_ID = #{programId}
    </update>

    <update id="update_setActiveProgramVersion">
        UPDATE MI_CDS_INFO_ACTIVE_PROGRAM
        SET VERSION = #{version}
        WHERE PROGRAM_ID = #{programId}
    </update>

    <update id="setProgramDeployTime">
        UPDATE MI_CDS_INFO_PROGRAM SET LASTDEPLOY_DATE =
        <include refid="utils.currentTimestamp"/>
        WHERE PROGRAM_ID = #{programId}
    </update>

    <update id="update_onProgramLayoutChange">
        UPDATE MI_CDS_INFO_FRAME_TEMP
        SET WIDTH = #{resolution_x}, HEIGHT = #{resolution_y}, LINE_DATA = #{line_data}
        WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{session_id} AND CHANNEL_NO = #{channelNo} AND FRAME_INDEX = 0
    </update>

    <update id="update_addProgramWithBasicInformation">
        UPDATE MI_CDS_INFO_PROGRAM
        SET VERSION = VERSION + 1
        WHERE PROGRAM_ID = #{defaultProgramId}
    </update>

    <update id="update_modifyProgramWithFrameAndHWControlAndContent">
        UPDATE MI_CDS_INFO_PROGRAM
        SET VERSION = VERSION + 1, PROGRAM_NAME = #{program.program_name}, PROGRAM_TYPE = #{program.program_type}
        , DEPLOY_TIME = #{program.deploy_time}, SCHEDULE_LIMIT = #{program.schedult_limit}
        , IS_DEFAULT = #{program.is_default}, IS_MEDIA_MERCHANT = #{program.is_media_merchant}
        , SCREEN_COUNT = #{program.screen_count}, USER_ID = #{program.user_id}
        , MODIFY_DATE =<include refid="utils.currentTimestamp"/>, BGM_CONTENT_ID = #{program.bgm_content_id}
        , IS_BGM_WITH_CONTENT = #{program.is_bgm_with_content}, DESCRIPTION = #{program.description}
        , FRAME_LAYOUT_TYPE = #{program.frame_layout_type}, RESOLUTION = #{program.resolution}
        , SYNCHRONIZATION = #{program.synchronization}
        , RESUME = #{program.resume}
        WHERE PROGRAM_ID = #{program.program_id}
    </update>

    <update id="update2_modifyProgramWithFrameAndHWControlAndContent">
        UPDATE MI_CDS_MAP_PROGRAM_GROUP
        SET PROGRAM_ID = #{program.program_id}, GROUP_ID = #{program.program_group_id}
        WHERE PROGRAM_ID = #{program.program_id}
    </update>

    <update id="update3_modifyProgramWithFrameAndHWControlAndContent">
        UPDATE MI_CDS_INFO_PROGRAM
        SET VERSION = VERSION + 1
        WHERE PROGRAM_ID = #{defaultProgramId}
    </update>

    <update id="update4_modifyProgramWithFrameAndHWControlAndContent">
        UPDATE MI_CDS_INFO_FRAME
        SET PROGRAM_ID = #{frame.program_id}, SCREEN_INDEX = #{frame.screen_index}, FRAME_INDEX = #{frame.frame_index}
            , FRAME_NAME = #{frame.frame_name}, X = #{frame.x}, Y = #{frame.y}, WIDTH = #{frame.width}
            , HEIGHT = #{frame.height}, DEFAULT_CONTENT_ID = #{frame.default_content_id}
            , IS_MAIN_FRAME = #{frame.is_main_frame}, FRAME_ID = #{frame.frame_id}, VERSION = #{frame.version}
            , LINE_DATA = #{frame.line_data}
        WHERE PROGRAM_ID = #{frame.program_id}
    </update>

    <update id="update5_modifyProgramWithFrameAndHWControlAndContent">
        UPDATE MI_CDS_INFO_FRAME
        SET VERSION = VERSION + 1
        WHERE PROGRAM_ID = #{schedule.program_id} AND FRAME_INDEX = #{schedule.frame_index}
    </update>

    <update id="update6_modifyProgramWithFrameAndHWControlAndContent">
        UPDATE MI_CDS_INFO_SCHEDULE
        SET START_DATE = #{schedule.start_date}, STOP_DATE = #{schedule.stop_date}, START_TIME = #{schedule.start_time}
        , DURATION = #{schedule.duration}, REPEAT_TYPE = #{schedule.repeat_type}, WEEKDAYS = #{schedule.weekdays}
        , MONTHDAYS = #{schedule.monthdays}, MODIFY_DATE =
        <include refid="utils.currentTimestamp"/>
        , SCHEDULE_TYPE = #{schedule.schedule_type}, HW_INPUT_SOURCE = #{schedule.hw_input_source}
        , HW_ATVDTV = #{schedule.hw_AtvDtv}, HW_AIRCABLE = #{schedule.hw_AirCable}
        , HW_MAJORCH = #{schedule.hw_MajorCH}, HW_MINORCH = #{schedule.hw_MinorCH}, HW_VOLUME = #{schedule.hw_Volume}
        , HW_SCH_CH = #{schedule.hw_sch_ch}, CONTENT_ID = #{schedule.content_id}, CONTENT_TYPE = #{schedule.content_type}
        , REPEAT_TIME = #{schedule.repeat_time}, IN_EFFECT_TYPE = #{schedule.in_effect_type}
        , IN_EFFECT_DURATION = #{schedule.in_effect_duration}, IN_EFFECT_DIRECTION = #{schedule.in_effect_direction}
        , OUT_EFFECT_TYPE = #{schedule.out_effect_type}, OUT_EFFECT_DURATION = #{schedule.out_effect_duration}
        , OUT_EFFECT_DIRECTION = #{schedule.out_effect_direction}, PRIORITY = #{priority}
        , IS_STREAMING = #{schedule.is_streaming}, SLIDE_TRANSITION_TIME = #{schedule.slide_transition_time}
        WHERE PROGRAM_ID = #{schedule.program_id} AND FRAME_INDEX = #{schedule.frame_index}
        AND SCHEDULE_ID = #{schedule.schedule_id} AND USER_ID = #{schedule.user_id}
    </update>

    <update id="update_addProgramWithFrameAndHWControlAndContent">
        UPDATE MI_CDS_INFO_PROGRAM
        SET VERSION = VERSION + 1
        WHERE PROGRAM_ID = #{defaultProgramId}
    </update>

    <update id="update_modifyProgramWithBasicInformation">
        UPDATE MI_CDS_INFO_PROGRAM
        SET VERSION = VERSION + 1, PROGRAM_NAME = #{program.program_name}, PROGRAM_TYPE = #{program.program_type}
        , DEPLOY_TIME = #{program.deploy_time}, SCHEDULE_LIMIT = #{program.schedult_limit}
        , IS_DEFAULT = #{program.is_default}, IS_MEDIA_MERCHANT = #{program.is_media_merchant}
        , SCREEN_COUNT = #{program.screen_count}, USER_ID = #{program.user_id}
        , MODIFY_DATE =<include refid="utils.currentTimestamp"/>, BGM_CONTENT_ID = #{program.bgm_content_id}
        , IS_BGM_WITH_CONTENT = #{program.is_bgm_with_content}, DESCRIPTION = #{program.description}
        , FRAME_LAYOUT_TYPE = #{program.frame_layout_type}, RESOLUTION = #{program.resolution}
        , SYNCHRONIZATION = #{program.synchronization}
        , RESUME = #{program.resume}
        WHERE PROGRAM_ID = #{program.program_id}
    </update>

    <update id="update2_modifyProgramWithBasicInformation">
        UPDATE MI_CDS_MAP_PROGRAM_GROUP
        SET PROGRAM_ID = #{program.program_id}, GROUP_ID = #{program.program_group_id}
        WHERE PROGRAM_ID = #{program.program_id}
    </update>

    <update id="update3_modifyProgramWithBasicInformation">
        UPDATE MI_CDS_INFO_PROGRAM
        SET VERSION = VERSION + 1
        WHERE PROGRAM_ID = #{defaultProgramId}
    </update>

    <update id="update_modifyContentScheduleWithoutTemp">
        UPDATE MI_CDS_INFO_FRAME
        SET VERSION = VERSION + 1
        WHERE PROGRAM_ID = #{schedule.program_id}
        AND FRAME_INDEX = #{schedule.frame_index}
        AND CHANNEL_NO= #{schedule.channel_no}
    </update>

    <update id="update2_modifyContentScheduleWithoutTemp">
        UPDATE MI_CDS_INFO_SCHEDULE
        SET START_DATE = #{schedule.start_date}, STOP_DATE = #{schedule.stop_date}, START_TIME = #{schedule.start_time}
        , DURATION = #{schedule.duration}, REPEAT_TYPE = #{schedule.repeat_type}, WEEKDAYS = #{schedule.weekdays}
        , MONTHDAYS = #{schedule.monthdays}, MODIFY_DATE =
        <include refid="utils.currentTimestamp"/>
        , SCHEDULE_TYPE = #{schedule.schedule_type}, HW_INPUT_SOURCE = #{schedule.hw_input_source}
        , HW_ATVDTV = #{schedule.hw_AtvDtv}, HW_AIRCABLE = #{schedule.hw_AirCable}
        , HW_MAJORCH = #{schedule.hw_MajorCH}, HW_MINORCH = #{schedule.hw_MinorCH}, HW_VOLUME = #{schedule.hw_Volume}, HW_SCH_CH = #{schedule.hw_sch_ch}
        , CONTENT_ID = #{schedule.content_id}, CONTENT_TYPE = #{schedule.content_type}
        , REPEAT_TIME = #{schedule.repeat_time}, IN_EFFECT_TYPE = #{schedule.in_effect_type}
        , IN_EFFECT_DURATION = #{schedule.in_effect_duration}, IN_EFFECT_DIRECTION = #{schedule.in_effect_direction}
        , OUT_EFFECT_TYPE = #{schedule.out_effect_type}, OUT_EFFECT_DURATION = #{schedule.out_effect_duration}
        , OUT_EFFECT_DIRECTION = #{schedule.out_effect_direction}, PRIORITY = #{priority}
        , IS_STREAMING=#{schedule.is_streaming}, SLIDE_TRANSITION_TIME = #{schedule.slide_transition_time}
        WHERE PROGRAM_ID = #{schedule.program_id} AND SCHEDULE_ID = #{schedule.schedule_id}
        AND CHANNEL_NO = #{schedule.channel_no}
    </update>

    <update id="update_modifyHWConstraint">
        UPDATE MI_CDS_INFO_SCHEDULE
        SET START_TIME = #{schedule.start_time}, DURATION = #{schedule.duration}, REPEAT_TYPE = #{schedule.repeat_type}
        , WEEKDAYS = #{schedule.weekdays}, MODIFY_DATE =
        <include refid="utils.currentTimestamp"/>
        , SCHEDULE_TYPE = #{schedule.schedule_type}, HW_INPUT_SOURCE = #{schedule.hw_input_source}
        , HW_ATVDTV = #{schedule.hw_AtvDtv}, HW_AIRCABLE = #{schedule.hw_AirCable}, HW_MAJORCH = #{schedule.hw_MajorCH}
        , HW_MINORCH = #{schedule.hw_MinorCH}, HW_VOLUME = #{schedule.hw_Volume}, HW_SCH_CH = #{schedule.hw_sch_ch}, PRIORITY = #{schedule.priority}
        , IS_STREAMING=#{schedule.is_streaming}
        WHERE PROGRAM_ID = #{schedule.program_id} AND SCHEDULE_ID = #{schedule.schedule_id}
    </update>

    <update id="setLinedataByProgramId">
        UPDATE MI_CDS_INFO_FRAME
        SET LINE_DATA = #{lineData}
        WHERE PROGRAM_ID = #{programId} AND CHANNEL_NO = #{channelNo}
    </update>

    <update id="setLinedataToZeroFrame">
        UPDATE MI_CDS_INFO_FRAME
        SET LINE_DATA = 'ZeroFrameOnly'
        WHERE PROGRAM_ID = #{programId} AND CHANNEL_NO = #{channelNo}
    </update>

    <update id="updateDefaultProgramDeviceType">
        UPDATE MI_CDS_INFO_PROGRAM
        SET DEVICE_TYPE = #{device_type} FROM MI_DMS_INFO_GROUP B
        WHERE PROGRAM_ID = B.DEFAULT_PROGRAM_ID AND B.GROUP_ID = #{device_group_id}
    </update>

	<update id="updateDefaultProgramDeviceType" databaseId="mysql">
		UPDATE MI_CDS_INFO_PROGRAM JOIN MI_DMS_INFO_GROUP B ON PROGRAM_ID = B.DEFAULT_PROGRAM_ID
		SET DEVICE_TYPE = #{device_type}
		WHERE B.GROUP_ID = #{device_group_id}
	</update>

    <update id="update_setDefaultProgramId">
        UPDATE MI_CDS_MAP_PROGRAM_DEVICE SET PROGRAM_ID = #{programId} WHERE DEVICE_GROUP_ID = #{groupId}
    </update>

    <delete id="deleteDeviceGroupMappedInProgramTempByProgramId">
        DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE_TEMP
        WHERE PROGRAM_ID = #{program_id}
    </delete>

    <delete id="deleteDeviceGroupMappedInProgramByProgramId">
        DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE
        WHERE PROGRAM_ID = #{program_id}
    </delete>

    <delete id="delete_addProgram">
        DELETE FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{defaultProgramId}
    </delete>

    <delete id="delete2_addProgram">
        DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE
        WHERE DEVICE_GROUP_ID = #{deviceId}
    </delete>

    <delete id="delete3_addProgram">
        DELETE FROM MI_CDS_INFO_FRAME_TEMP
        WHERE PROGRAM_ID = #{program_id} AND SESSION_ID = #{sessionId}
    </delete>

    <delete id="delete4_addProgram">
        DELETE FROM MI_CDS_INFO_SCHEDULE_TEMP
        WHERE PROGRAM_ID = #{program_id} AND SESSION_ID = #{sessionId}
    </delete>

    <delete id="delete5_addProgram">
        DELETE FROM MI_CDS_MAP_FRAME_USER_TEMP
        WHERE PROGRAM_ID = #{program_id} AND SESSION_ID = #{sessionId}
    </delete>

    <delete id="deleteFromMiCdsInfoChannelTmp">
    	DELETE FROM MI_CDS_INFO_CHANNEL_TEMP 
    	WHERE PROGRAM_ID = #{program_id} AND SESSION_ID = #{sessionId}
    </delete>
    
    <delete id="deleteFromMiCdsInfoChannel">
    	DELETE FROM MI_CDS_INFO_CHANNEL
    	WHERE PROGRAM_ID = #{program_id}
    </delete>

    <delete id="delete_addDefaultProgram">
        DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE
        WHERE DEVICE_GROUP_ID = #{deviceId}
    </delete>

    <delete id="delete_updateProgram">
        DELETE FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{defaultProgramId}
    </delete>

    <delete id="delete2_updateProgram">
        DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE
        WHERE DEVICE_GROUP_ID = #{deviceId}
    </delete>

    <delete id="delete3_updateProgram">
        DELETE FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{program_id}
    </delete>

    <delete id="delete4_updateProgram">
        DELETE FROM MI_CDS_INFO_FRAME
        WHERE PROGRAM_ID = #{program_id}
    </delete>

    <delete id="delete5_updateProgram">
        DELETE FROM MI_CDS_MAP_FRAME_USER
        WHERE PROGRAM_ID = #{program_id}
    </delete>
    
    <delete id="deleteAdSlotList">
    	DELETE FROM MI_CDS_INFO_ADSLOT
    	WHERE PROGRAM_ID = #{programId}
    </delete>
    
    <delete id="deleteAdScheduleList">
    	DELETE FROM MI_CDS_INFO_ADSCHEDULE
    	WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="delete6_updateProgram">
        DELETE FROM MI_CDS_INFO_FRAME_TEMP
        WHERE PROGRAM_ID = #{program_id} AND SESSION_ID = #{sessionId}
    </delete>

    <delete id="delete7_updateProgram">
        DELETE FROM MI_CDS_INFO_SCHEDULE_TEMP
        WHERE PROGRAM_ID = #{program_id} AND SESSION_ID = #{sessionId}
    </delete>

    <delete id="delete8_updateProgram">
        DELETE FROM MI_CDS_MAP_FRAME_USER_TEMP
        WHERE PROGRAM_ID = #{program_id} AND SESSION_ID = #{sessionId}
    </delete>

    <delete id="delete_transferProgramDataToMain">
        DELETE FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="delete2_transferProgramDataToMain">
        DELETE FROM MI_CDS_INFO_FRAME
        WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="delete3_transferProgramDataToMain">
        DELETE FROM MI_CDS_MAP_FRAME_USER
        WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="delete4_transferProgramDataToMain">
        DELETE FROM MI_CDS_INFO_FRAME_TEMP
        WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{sessionId}
    </delete>

    <delete id="delete5_transferProgramDataToMain">
        DELETE FROM MI_CDS_INFO_SCHEDULE_TEMP
        WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{sessionId}
    </delete>

    <delete id="delete6_transferProgramDataToMain">
        DELETE FROM MI_CDS_MAP_FRAME_USER_TEMP
        WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{sessionId}
    </delete>

    <delete id="deleteTempSchedule">
        DELETE FROM MI_CDS_INFO_SCHEDULE_TEMP
        WHERE PROGRAM_ID = #{programId} AND SCHEDULE_ID = #{scheduleId}
    </delete>

    <delete id="delete_deleteTempFrame">
        DELETE FROM MI_CDS_INFO_FRAME_TEMP
        WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{session_id}
    </delete>

    <delete id="delete2_deleteTempFrame">
        DELETE FROM MI_CDS_MAP_FRAME_USER_TEMP
        WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{session_id}
    </delete>

    <delete id="deleteFrame">
        INSERT INTO MI_CDS_INFO_FRAME_TEMP (SESSION_ID, PROGRAM_ID, SCREEN_INDEX, FRAME_INDEX, IS_DELETE)
            VALUES (#{sessionId}, #{programId}, #{screenIndex}, #{frameIndex}, #{is_delete})
    </delete>

    <delete id="delete_deleteProgram">
        DELETE FROM MI_CDS_MAP_PROGRAM_GROUP
        WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="delete2_deleteProgram">
        DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE
        WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="delete3_deleteProgram">
        DELETE FROM MI_CDS_INFO_PROGRAM
        WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="deleteTempScheduleForFrameIndex">
        DELETE FROM MI_CDS_INFO_SCHEDULE_TEMP
        WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{sessionId} 
        AND CHANNEL_NO = #{channelNo} AND FRAME_INDEX >= #{frameIndex}
    </delete>

    <delete id="deletet_deleteAllProgramTempData">
        DELETE FROM MI_CDS_MAP_FRAME_USER_TEMP
    </delete>

    <delete id="deletet2_deleteAllProgramTempData">
        DELETE FROM MI_CDS_INFO_FRAME_TEMP
    </delete>

    <delete id="deletet3_deleteAllProgramTempData">
        DELETE FROM MI_CDS_INFO_SCHEDULE_TEMP
    </delete>

    <delete id="delete_deleteProgramTempDataWithId">
        DELETE FROM MI_CDS_MAP_FRAME_USER_TEMP
        WHERE PROGRAM_ID = #{programId}
    </delete>
    
    <delete id="delete_deleteProgramDataWithId">
        DELETE FROM MI_CDS_MAP_FRAME_USER
        WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="delete2_deleteProgramTempDataWithId">
        DELETE FROM MI_CDS_INFO_FRAME_TEMP
        WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="delete3_deleteProgramTempDataWithId">
        DELETE FROM MI_CDS_INFO_SCHEDULE_TEMP
        WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="delete_deleteProgramTempDataWithSession">
        DELETE FROM MI_CDS_MAP_FRAME_USER_TEMP
        WHERE SESSION_ID = #{sessionId}
    </delete>

    <delete id="delete2_deleteProgramTempDataWithSession">
        DELETE FROM MI_CDS_INFO_FRAME_TEMP
        WHERE SESSION_ID = #{sessionId}
    </delete>

    <delete id="delete3_deleteProgramTempDataWithSession">
        DELETE FROM MI_CDS_INFO_SCHEDULE_TEMP
        WHERE SESSION_ID = #{sessionId}
    </delete>

    <delete id="delete_deleteProgramTempData">
        DELETE FROM MI_CDS_MAP_FRAME_USER_TEMP
        WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{sessionId}
    </delete>

    <delete id="delete2_deleteProgramTempData">
        DELETE FROM MI_CDS_INFO_FRAME_TEMP
        WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{sessionId}
    </delete>

    <delete id="delete3_deleteProgramTempData">
        DELETE FROM MI_CDS_INFO_SCHEDULE_TEMP
        WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{sessionId}
    </delete>

    <delete id="delete4_deleteProgramTempData">
        DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE_TEMP
        WHERE PROGRAM_ID = #{programId}
    </delete>
    
    <delete id="delete_deleteProgramData">
        DELETE FROM MI_CDS_MAP_FRAME_USER
        WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="delete2_deleteProgramData">
        DELETE FROM MI_CDS_INFO_FRAME
        WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="delete3_deleteProgramData">
        DELETE FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="delete4_deleteProgramData">
        DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE
        WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="delete_setProgram">
        DELETE FROM MI_CDS_MAP_PROGRAM_GROUP
        WHERE PROGRAM_ID = #{program_id}
    </delete>

    <delete id="delete2_setProgram">
        DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE
        WHERE PROGRAM_ID = #{program_id}
    </delete>

    <delete id="delete3_setProgram">
        DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE
        WHERE DEVICE_GROUP_ID = #{deviceId}
    </delete>

    <delete id="delete_onProgramLayoutChange">
        DELETE FROM MI_CDS_INFO_FRAME_TEMP
        WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{session_id} AND CHANNEL_NO = #{channelNo} AND FRAME_INDEX >= 1
    </delete>

    <delete id="delete2_onProgramLayoutChange">
        DELETE FROM MI_CDS_INFO_SCHEDULE_TEMP
        WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{session_id} AND CHANNEL_NO = #{channelNo} AND FRAME_INDEX >= 1
    </delete>

    <delete id="deleteFrameTemplate">
        DELETE FROM MI_CDS_INFO_FRAME_TEMPLATE
        WHERE TEMPLATE_ID = #{template_id}
    </delete>

    <delete id="deleteFrameByFrameId">
        DELETE FROM MI_CDS_INFO_FRAME
        WHERE FRAME_ID = #{frameId}
    </delete>

    <delete id="deleteContentScheduleByScheduleId">
        DELETE FROM MI_CDS_INFO_SCHEDULE
        WHERE SCHEDULE_ID = #{scheduleId}
    </delete>

    <delete id="delete_addProgramWithBasicInformation">
        DELETE FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{defaultProgramId}
    </delete>

    <delete id="delete2_addProgramWithBasicInformation">
        DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE
        WHERE DEVICE_GROUP_ID = #{deviceId}
    </delete>

    <delete id="delete_modifyProgramWithFrameAndHWControlAndContent">
        DELETE FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{defaultProgramId}
    </delete>

    <delete id="delete2_modifyProgramWithFrameAndHWControlAndContent">
        DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE
        WHERE DEVICE_GROUP_ID = #{groupId}
    </delete>

    <delete id="delete_addProgramWithFrameAndHWControlAndContent">
        DELETE FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{defaultProgramId}
    </delete>

    <delete id="delete2_addProgramWithFrameAndHWControlAndContent">
        DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE
        WHERE DEVICE_GROUP_ID = #{deviceGroupId}
    </delete>

    <delete id="delete_modifyProgramWithBasicInformation">
        DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE
        WHERE PROGRAM_ID = #{program_id}
    </delete>

    <delete id="delete2_modifyProgramWithBasicInformation">
        DELETE FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{defaultProgramId}
    </delete>

    <delete id="delete3_modifyProgramWithBasicInformation">
        DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE
        WHERE DEVICE_GROUP_ID = #{deviceGroupId}
    </delete>

    <select id="getProgram" resultType="com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity">
        SELECT
            *
        FROM MI_CDS_INFO_PROGRAM
        WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getDeviceProgramMapList" resultType="map">
        SELECT
            A.*,
            B.DEFAULT_PROGRAM_ID
        FROM MI_CDS_MAP_PROGRAM_DEVICE AS A JOIN MI_DMS_INFO_GROUP AS B
                ON B.GROUP_ID = A.DEVICE_GROUP_ID
        WHERE A.PROGRAM_ID = #{programId}
    </select>

    <select id="getDeviceGroupIdsAndName" resultType="map">
        SELECT
            D.DEVICE_GROUP_ID,
            G.GROUP_NAME
        FROM MI_CDS_MAP_PROGRAM_DEVICE AS D
            LEFT JOIN MI_DMS_INFO_GROUP AS G
                ON D.DEVICE_GROUP_ID = G.GROUP_ID
        WHERE PROGRAM_ID = #{programId}
    </select>
    
    <select id="getGroupNameByGroupId" resultType="String">
         SELECT
            GROUP_NAME
        FROM MI_CDS_INFO_PROGRAM_GROUP
        WHERE GROUP_ID = #{groupId}
    </select>

    <select id="getDeviceGroupMappedInProgramTemp" resultType="java.util.Map">
        SELECT
            DEVICE_GROUP_ID
        FROM MI_CDS_MAP_PROGRAM_DEVICE_TEMP
        WHERE PROGRAM_ID = #{program_id}
    </select>

    <select id="getDeviceGroupMappedInProgram" resultType="java.util.Map">
        SELECT
            DEVICE_GROUP_ID
        FROM MI_CDS_MAP_PROGRAM_DEVICE
        WHERE PROGRAM_ID = #{program_id}
    </select>

    <select id="getDevicesMappedInProgram" resultType="java.lang.String">
        SELECT A.DEVICE_ID from MI_DMS_MAP_GROUP_DEVICE as A
        JOIN MI_CDS_MAP_PROGRAM_DEVICE as B
        ON B.DEVICE_GROUP_ID = A.GROUP_ID
        WHERE B.PROGRAM_ID = #{program_id}
    </select>

    <select id="select_addProgram" resultType="java.util.Map">
        SELECT <include refid="select_addProgram_body"/> LIMIT 1
    </select>

    <select id="select_addProgram" resultType="java.util.Map" databaseId="mssql">
        SELECT TOP (1) <include refid="select_addProgram_body"/>
    </select>

    <sql id="select_addProgram_body">
        A.DEFAULT_PROGRAM_ID,
        B.PROGRAM_ID
        FROM MI_DMS_INFO_GROUP AS A JOIN MI_CDS_MAP_PROGRAM_DEVICE AS B
        ON A.GROUP_ID = B.DEVICE_GROUP_ID
        WHERE
        B.DEVICE_GROUP_ID = #{deviceId}
        AND A.DEFAULT_PROGRAM_ID = B.PROGRAM_ID
        ORDER BY A.GROUP_ID
    </sql>

    <select id="select_addDefaultProgram" resultType="java.lang.Long">
        SELECT
            GROUP_ID
        FROM MI_DMS_INFO_GROUP
        WHERE GROUP_NAME = #{orgName} AND GROUP_DEPTH = 1
    </select>

    <select id="select_updateProgram" resultType="java.util.Map">
        SELECT <include refid="select_updateProgram_body"/> LIMIT 1
    </select>

    <select id="select_updateProgram" resultType="java.util.Map" databaseId="mssql">
        SELECT TOP (1) <include refid="select_updateProgram_body"/>
    </select>

    <sql id="select_updateProgram_body">
        A.DEFAULT_PROGRAM_ID,
        B.PROGRAM_ID
        FROM MI_DMS_INFO_GROUP AS A JOIN MI_CDS_MAP_PROGRAM_DEVICE AS B
        ON A.GROUP_ID = B.DEVICE_GROUP_ID
        WHERE B.DEVICE_GROUP_ID = #{deviceId}
        and A.DEFAULT_PROGRAM_ID = B.PROGRAM_ID
    </sql>

    <select id="select_addContentSchedule" resultType="java.util.Map">
        SELECT <include refid="select_addContentSchedule_body"/> LIMIT 1
    </select>

    <select id="select_addContentSchedule" resultType="java.util.Map" databaseId="mssql">
        SELECT TOP (1) <include refid="select_addContentSchedule_body"/>
    </select>

    <sql id="select_addContentSchedule_body">
        PRIORITY
        FROM MI_CDS_INFO_SCHEDULE_TEMP
        WHERE PROGRAM_ID = #{schedule.program_id} AND CHANNEL_NO=#{schedule.channel_no} AND SESSION_ID = #{schedule.session_id}
        AND FRAME_INDEX = #{schedule.frame_index}
        ORDER BY PRIORITY DESC

    </sql>

    <select id="select_transferScheduleDataToTempWithNewId"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity">
        SELECT
            *
        FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{programId}
    </select>
    
	<select id="getScheduleIdListByProgramId" resultType="String">
        SELECT
             DISTINCT SCHEDULE_ID
        FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{programId}
    </select>
    
    <select id="select2_transferScheduleDataToTempWithNewId"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.FrameEntity">
        SELECT
            *
        FROM MI_CDS_INFO_FRAME
        WHERE PROGRAM_ID = #{programId}
        ORDER BY FRAME_INDEX ASC
    </select>

    <select id="select_updateContentSchedule" resultType="java.util.Map">
        SELECT <include refid="select_updateContentSchedule_body"/> LIMIT 1
    </select>

    <select id="select_updateContentSchedule" resultType="java.util.Map" databaseId="mssql">
        SELECT TOP (1) <include refid="select_updateContentSchedule_body"/>
    </select>

    <sql id="select_updateContentSchedule_body">
        PRIORITY
        FROM MI_CDS_INFO_SCHEDULE_TEMP
        WHERE PROGRAM_ID = #{schedule.program_id} 
        AND SESSION_ID = #{schedule.session_id} 
        AND CHANNEL_NO= #{schedule.channel_no}
        AND FRAME_INDEX = #{schedule.frame_index}
        ORDER BY PRIORITY DESC
    </sql>

    <select id="getFrames" resultType="com.samsung.magicinfo.framework.scheduler.entity.FrameEntity">
        SELECT
            *
        FROM MI_CDS_INFO_FRAME
        WHERE PROGRAM_ID = #{programId} AND CHANNEL_NO = #{channelNo} AND SCREEN_INDEX = #{screenIndex}
    </select>
    
    <select id="getFrame" resultType="com.samsung.magicinfo.framework.scheduler.entity.FrameEntity">
        SELECT
            *
        FROM MI_CDS_INFO_FRAME
        WHERE PROGRAM_ID = #{programId} AND CHANNEL_NO = #{channelNo}
        ORDER BY FRAME_INDEX ASC
    </select>

    <select id="getFramesInfo" resultType="com.samsung.magicinfo.framework.scheduler.entity.FrameEntity">
        SELECT
            FRAME_INDEX,
            FRAME_NAME,
            IS_MAIN_FRAME
        FROM MI_CDS_INFO_FRAME
        WHERE PROGRAM_ID = #{programId} AND SCREEN_INDEX = #{screenIndex}
    </select>

    <select id="select_getTempFrames"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.FrameEntity">
        SELECT
            F.*,
            C.CONTENT_NAME
        FROM MI_CDS_INFO_FRAME_TEMP AS F
            LEFT JOIN MI_CMS_INFO_CONTENT AS C
                ON F.DEFAULT_CONTENT_ID = C.CONTENT_ID
        WHERE PROGRAM_ID = #{programId} AND F.CHANNEL_NO = #{channelNo} AND F.SESSION_ID = #{session_id}
        ORDER BY FRAME_INDEX ASC
    </select>

    <select id="select2_getTempFrames" resultType="java.util.Map">
        SELECT
            A.USER_GROUP_ID,
            B.GROUP_NAME
        FROM MI_CDS_MAP_FRAME_USER_TEMP A JOIN MI_USER_INFO_GROUP B
                ON A.USER_GROUP_ID = B.GROUP_ID
        WHERE SESSION_ID = #{session_id} AND PROGRAM_ID = #{programId} AND A.CHANNEL_NO = #{channelNo} AND FRAME_ID = #{frame_id}
    </select>

    <select id="select_getFrameDate" resultType="com.samsung.magicinfo.framework.scheduler.entity.FrameEntity">
        SELECT
            F.*,
            C.CONTENT_NAME
        FROM MI_CDS_INFO_FRAME AS F LEFT JOIN MI_CMS_INFO_CONTENT AS C
                ON F.DEFAULT_CONTENT_ID = C.CONTENT_ID
        WHERE PROGRAM_ID = #{programId} AND FRAME_INDEX = #{frame_index} AND CHANNEL_NO = (SELECT MIN(CHANNEL_NO) FROM MI_CDS_INFO_FRAME WHERE PROGRAM_ID = #{programId})
    </select>
        
    <select id="select_getFrameDate" resultType="com.samsung.magicinfo.framework.scheduler.entity.FrameEntity" databaseId="mssql">
        SELECT TOP (1)
            F.*,
            C.CONTENT_NAME
        FROM MI_CDS_INFO_FRAME AS F LEFT JOIN MI_CMS_INFO_CONTENT AS C
                ON F.DEFAULT_CONTENT_ID = C.CONTENT_ID
        WHERE PROGRAM_ID = #{programId} AND FRAME_INDEX = #{frame_index} AND CHANNEL_NO = (SELECT MIN(CHANNEL_NO) FROM MI_CDS_INFO_FRAME WHERE PROGRAM_ID = #{programId}) ORDER BY VERSION DESC
    </select>
	
	<select id="select_getFrameDataWithChannelNo" resultType="com.samsung.magicinfo.framework.scheduler.entity.FrameEntity">
		 SELECT
            F.*,
            C.CONTENT_NAME
        FROM MI_CDS_INFO_FRAME AS F LEFT JOIN MI_CMS_INFO_CONTENT AS C
                ON F.DEFAULT_CONTENT_ID = C.CONTENT_ID
        WHERE PROGRAM_ID = #{programId} AND FRAME_INDEX = #{frame_index} AND CHANNEL_NO = #{channelNo}
	</select>

    <select id="select2_getFrameData" resultType="java.util.Map">
        SELECT
            A.USER_GROUP_ID,
            B.GROUP_NAME
        FROM MI_CDS_MAP_FRAME_USER A JOIN MI_USER_INFO_GROUP B
                ON A.USER_GROUP_ID = B.GROUP_ID
        WHERE PROGRAM_ID = #{programId} AND FRAME_ID = #{frame_id}
    </select>

    <select id="getTempFrameCount" resultType="java.lang.Long">
        SELECT
            COUNT(FRAME_INDEX)
        FROM MI_CDS_INFO_FRAME_TEMP
        WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{session_id}
    </select>

    <select id="getTempFrameCountForProgram" resultType="java.lang.Long">
        SELECT
            COUNT(FRAME_INDEX)
        FROM MI_CDS_INFO_FRAME_TEMP
        WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getTempFrameCountForSession" resultType="java.lang.Long">
        SELECT
            COUNT(FRAME_INDEX)
        FROM MI_CDS_INFO_FRAME_TEMP
        WHERE SESSION_ID = #{session_id}
    </select>

    <select id="getProgramName" resultType="java.lang.String">
        SELECT
            PROGRAM_NAME
        FROM MI_CDS_INFO_PROGRAM
        WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getProgramList" resultType="com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity">
        <include refid="getProgramListQuery"/>
    </select>

    <select id="getProgramListCount" resultType="java.lang.Integer">
        SELECT count(*) FROM MI_CDS_INFO_PROGRAM
        <where>
            <if test="map.screen_count">
                SCREEN_COUNT = #{map.screen_count}
            </if>
        </where>
    </select>

    <select id="getBGMContentName" resultType="java.lang.String">
        SELECT
            C.CONTENT_NAME
        FROM MI_CDS_INFO_PROGRAM AS P LEFT JOIN MI_CMS_INFO_CONTENT AS C
                ON P.BGM_CONTENT_ID = C.CONTENT_ID
        WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getContentSchedules"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity">
        SELECT
        *
        FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{programId} AND CHANNEL_NO = #{channelNo} AND SCREEN_INDEX = #{screenIndex} AND FRAME_INDEX = #{frameIndex}
        AND SCHEDULE_TYPE = #{contentScheduleType} 
        AND <include refid="getContentSchedules_STOP_DATE_asDate"/> >= <include refid="utils.currentDate"/>
        ORDER BY CHANNEL_NO
    </select>
    
    <select id="getContentListFromProgramidandChannel" resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity">
    	SELECT *
        FROM MI_CDS_INFO_SCHEDULE SCHEDULE
        WHERE PROGRAM_ID = #{programId} AND CHANNEL_NO = #{channelNo} AND SCREEN_INDEX = #{screenIndex} AND FRAME_INDEX = #{frameIndex}
    </select>

    <sql id="getContentSchedules_STOP_DATE_asDate">
        to_date(STOP_DATE, 'yyyy-MM-dd')
    </sql>
    
    <sql id="getContentSchedules_STOP_DATE_asDate"  databaseId="mssql">
    	CAST(STOP_DATE AS DATE)
    </sql>

	<sql id="getContentSchedules_STOP_DATE_asDate" databaseId="mysql">
        DATE_FORMAT(STOP_DATE, '%Y-%m-%d')
    </sql>
	
    <select id="getContentSchedulesForProgramId"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity">
        (SELECT
            A.CONTENT_TYPE,
            A.CONTENT_ID,
            A.CHANNEL_NO,
            B.CONTENT_NAME AS CONTENT_NAME,
            A.START_DATE,
            A.STOP_DATE,
            A.START_TIME,
            A.PLAYER_MODE,
            A.SCHEDULE_ID,
            A.DURATION
        FROM MI_CDS_INFO_SCHEDULE A, MI_CMS_INFO_CONTENT B
        WHERE A.PROGRAM_ID = #{programId} AND A.SCHEDULE_TYPE = #{contentScheduleType} AND A.CONTENT_ID = B.CONTENT_ID
        UNION
        SELECT
            A.CONTENT_TYPE,
            A.CONTENT_ID,
            A.CHANNEL_NO,
            B.PLAYLIST_NAME AS CONTENT_NAME,
            A.START_DATE,
            A.STOP_DATE,
            A.START_TIME,
            A.PLAYER_MODE,
            A.SCHEDULE_ID,
            A.DURATION
        FROM MI_CDS_INFO_SCHEDULE A, MI_CMS_INFO_PLAYLIST B
        WHERE A.PROGRAM_ID = #{programId} AND A.SCHEDULE_TYPE = #{contentScheduleType} AND A.CONTENT_ID = B.PLAYLIST_ID
        )
        UNION
		SELECT
			A.CONTENT_TYPE,
			A.CONTENT_ID,
			NULL AS CHANNEL_NO,
			B.PLAYLIST_NAME AS CONTENT_NAME,
			A.START_DATE,
			A.STOP_DATE,
			A.START_TIME,
			NULL AS PLAYER_MODE,
			A.SCHEDULE_ID,
			A.DURATION
		FROM MI_CDS_INFO_ADSCHEDULE A, MI_CMS_INFO_PLAYLIST B
		WHERE A.PROGRAM_ID = #{programId} AND A.CONTENT_ID = B.PLAYLIST_ID
		UNION
		SELECT
            A.CONTENT_TYPE,
            A.CONTENT_ID,
            A.CHANNEL_NO,
            B.NAME AS CONTENT_NAME,
            A.START_DATE,
            A.STOP_DATE,
            A.START_TIME,
            A.PLAYER_MODE,
            A.SCHEDULE_ID,
            A.DURATION
        FROM MI_CDS_INFO_SCHEDULE A, MI_RULE_INFO_RULESET B
        WHERE A.PROGRAM_ID = '76fd69ec-cf6d-4a63-905c-fe3d3bfbd575' AND A.SCHEDULE_TYPE = '00' AND A.CONTENT_ID = B.RULESET_ID
    </select>

    <select id="getContentSchedulesForProgramIdAndChannelId"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity">
        SELECT
        	A.SCHEDULE_ID,
            A.CONTENT_TYPE,
            A.CONTENT_ID,
            A.CHANNEL_NO,
            B.CONTENT_NAME AS CONTENT_NAME,
            A.START_DATE,
            A.STOP_DATE,
            A.START_TIME,
            A.DURATION,
            A.REPEAT_TYPE,
            A.WEEKDAYS,
            A.MONTHDAYS
        FROM MI_CDS_INFO_SCHEDULE A, MI_CMS_INFO_CONTENT B
        WHERE A.PROGRAM_ID = #{programId} AND A.SCHEDULE_TYPE = #{contentScheduleType} AND A.CONTENT_ID = B.CONTENT_ID AND A.CHANNEL_NO = #{channelNo}
        UNION
        SELECT
        	A.SCHEDULE_ID,
            A.CONTENT_TYPE,
            A.CONTENT_ID,
            A.CHANNEL_NO,
            B.PLAYLIST_NAME AS CONTENT_NAME,
            A.START_DATE,
            A.STOP_DATE,
            A.START_TIME,
            A.DURATION,
            A.REPEAT_TYPE,
            A.WEEKDAYS,
            A.MONTHDAYS
        FROM MI_CDS_INFO_SCHEDULE A, MI_CMS_INFO_PLAYLIST B
        WHERE A.PROGRAM_ID = #{programId} AND A.SCHEDULE_TYPE = #{contentScheduleType} AND A.CONTENT_ID = B.PLAYLIST_ID AND A.CHANNEL_NO = #{channelNo}
        UNION
      	SELECT
      		SCHEDULE_ID,
			CONTENT_TYPE,
			CONTENT_ID,
			CHANNEL_NO,
			CONTENT_ID AS CONTENT_NAME,
			START_DATE,
			STOP_DATE,
			START_TIME,
			DURATION,
			REPEAT_TYPE,
			WEEKDAYS,
            MONTHDAYS
        FROM MI_CDS_INFO_SCHEDULE 
        WHERE PROGRAM_ID = #{programId} AND SCHEDULE_TYPE = #{contentScheduleType} AND CONTENT_TYPE = 'HW_IS' AND CHANNEL_NO = #{channelNo}
        
        ORDER
        BY START_DATE, START_TIME ASC
    </select>

    <select id="getPanelOrZeroFrameSchedules"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.ScheduleEntity">
        SELECT
            *
        FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{programId} AND SCHEDULE_TYPE = #{scheduleType}
    </select>

    <select id="getPanelOrZeroFrameTempSchedules"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.ScheduleEntity">
        SELECT
            *
        FROM MI_CDS_INFO_SCHEDULE_TEMP
        WHERE PROGRAM_ID = #{programId} AND SCHEDULE_TYPE = #{scheduleType}
    </select>

    <select id="selAllSchedule"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity">
        SELECT
            *
        FROM MI_CDS_INFO_SCHEDULE_TEMP
        WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{sessionId}
    </select>

    <select id="selAllScheduleByMonth"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity">
        SELECT
        S.*,
        C.CONTENT_NAME,
        P.PLAYLIST_NAME,
        V.IS_STREAMING AS STREAMING_ALLOWED
        FROM MI_CDS_INFO_SCHEDULE_TEMP
        AS S LEFT JOIN MI_CMS_INFO_CONTENT AS C
        ON S.CONTENT_ID = C.CONTENT_ID
        LEFT JOIN MI_CMS_INFO_PLAYLIST AS P
        ON
        S.CONTENT_ID = P.PLAYLIST_ID
        LEFT JOIN MI_CMS_INFO_CONTENT_VERSION AS V
        ON C.CONTENT_ID = V.CONTENT_ID
        LEFT JOIN
        MI_CMS_INFO_PLAYLIST_VERSION AS PV
        ON P.PLAYLIST_ID = PV.PLAYLIST_ID
        WHERE PROGRAM_ID = #{map.program_id} AND S.SESSION_ID = #{map.session_id} AND S.CHANNEL_NO = #{channelNo}
        AND (
        (S.SCHEDULE_TYPE = '00' AND S.CONTENT_TYPE != 'PLAYLIST' AND V.IS_ACTIVE = 'Y')
        OR (S.SCHEDULE_TYPE = '00' AND S.CONTENT_TYPE = 'PLAYLIST' AND PV.IS_ACTIVE = 'Y')
        OR (S.SCHEDULE_TYPE = '00' AND S.CONTENT_TYPE = 'HW_IS')
        OR S.SCHEDULE_TYPE != '00')
        AND ((((START_DATE &lt;= #{map.spdate} AND STOP_DATE >= #{map.spdate})
        OR (START_DATE >= #{map.spdate} AND START_DATE &lt;= #{map.epdate}
        AND STOP_DATE >= #{map.spdate})) AND FRAME_INDEX = #{frameIndex})
        OR (SCHEDULE_TYPE = '01'
        <if test="isZeroFrameindex">
            OR SCHEDULE_TYPE = '02'
        </if>
        OR SCHEDULE_TYPE = '03'
        OR SCHEDULE_TYPE = '04'
        ))
        ORDER
        BY SCHEDULE_TYPE ASC
    </select>

    <select id="selAllScheduleByWeek"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity">
        SELECT
        S.*,
        C.CONTENT_NAME,
        P.PLAYLIST_NAME,
        V.IS_STREAMING AS STREAMING_ALLOWED
        FROM MI_CDS_INFO_SCHEDULE_TEMP
        AS S LEFT JOIN MI_CMS_INFO_CONTENT AS C
        ON S.CONTENT_ID = C.CONTENT_ID
        LEFT JOIN MI_CMS_INFO_PLAYLIST AS P
        ON
        S.CONTENT_ID = P.PLAYLIST_ID
        LEFT JOIN MI_CMS_INFO_CONTENT_VERSION AS V
        ON C.CONTENT_ID = V.CONTENT_ID
        LEFT JOIN
        MI_CMS_INFO_PLAYLIST_VERSION AS PV
        ON P.PLAYLIST_ID = PV.PLAYLIST_ID
        WHERE PROGRAM_ID = #{map.program_id} AND S.SESSION_ID = #{map.session_id} AND S.CHANNEL_NO = #{channelNo}
        AND (
        (S.SCHEDULE_TYPE = '00' AND S.CONTENT_TYPE != 'PLAYLIST' AND V.IS_ACTIVE = 'Y')
        OR (S.SCHEDULE_TYPE = '00' AND S.CONTENT_TYPE = 'PLAYLIST' AND PV.IS_ACTIVE = 'Y')
        OR (S.SCHEDULE_TYPE = '00' AND S.CONTENT_TYPE = 'HW_IS')
        OR S.SCHEDULE_TYPE != '00')
        AND ((((START_DATE &lt;= #{map.spdate} AND STOP_DATE >= #{map.spdate})
        OR (START_DATE >= #{map.spdate} AND START_DATE &lt;= #{map.epdate}
        AND STOP_DATE >= #{map.spdate})) AND FRAME_INDEX = #{frameIndex}
        AND ((REPEAT_TYPE = 'once') OR (REPEAT_TYPE = 'daily') OR (REPEAT_TYPE = #{constants.REPEAT_TYPE_DAYOFWEEK})
        OR ((REPEAT_TYPE = #{constants.REPEAT_TYPE_DAYOFMONTH}) AND (
        <if test="weekDates != null">
            <foreach collection="weekDates" separator="OR" item="item">
                MONTHDAYS LIKE '%'<include refid="utils.concatenate"/>#{item}<include refid="utils.concatenate"/>'%'
            </foreach>
        </if>
        )))) OR (
        SCHEDULE_TYPE = '01'
        <if test="isZeroframeIndex">
            OR SCHEDULE_TYPE = '02'
        </if>
        OR SCHEDULE_TYPE = '03'
        OR SCHEDULE_TYPE = '04'
        ))
        ORDER BY SCHEDULE_TYPE ASC
    </select>

    <select id="selAllScheduleByDay"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity">
        SELECT
        S.*,
        C.CONTENT_NAME,
        P.PLAYLIST_NAME,
        V.IS_STREAMING AS STREAMING_ALLOWED
        FROM MI_CDS_INFO_SCHEDULE_TEMP
        AS S LEFT JOIN MI_CMS_INFO_CONTENT AS C
        ON S.CONTENT_ID = C.CONTENT_ID
        LEFT JOIN MI_CMS_INFO_PLAYLIST AS P
        ON
        S.CONTENT_ID = P.PLAYLIST_ID
        LEFT JOIN MI_CMS_INFO_CONTENT_VERSION AS V
        ON C.CONTENT_ID = V.CONTENT_ID
        LEFT JOIN
        MI_CMS_INFO_PLAYLIST_VERSION AS PV
        ON P.PLAYLIST_ID = PV.PLAYLIST_ID
        WHERE PROGRAM_ID = #{map.program_id} AND S.SESSION_ID = #{map.session_id} AND S.CHANNEL_NO = #{channelNo}
        AND (
        (S.SCHEDULE_TYPE = '00' AND S.CONTENT_TYPE != 'PLAYLIST' AND V.IS_ACTIVE = 'Y')
        OR (S.SCHEDULE_TYPE = '00' AND S.CONTENT_TYPE = 'PLAYLIST' AND PV.IS_ACTIVE = 'Y')
        OR (S.SCHEDULE_TYPE = '00' AND S.CONTENT_TYPE = 'HW_IS')
        OR S.SCHEDULE_TYPE != '00')
        AND ((((START_DATE &lt;= #{map.spdate} AND STOP_DATE >= #{map.spdate})
        OR (START_DATE >= #{map.spdate} AND START_DATE &lt;= #{map.epdate}
        AND STOP_DATE >= #{map.spdate}))
        AND FRAME_INDEX = #{frameIndex} AND ((REPEAT_TYPE = 'once') OR (REPEAT_TYPE = 'daily')
        OR ((REPEAT_TYPE = #{constants.REPEAT_TYPE_DAYOFWEEK}) AND WEEKDAYS
            LIKE '%' <include refid="utils.concatenate"/> #{map.day} <include refid="utils.concatenate"/> '%'
        )
        OR ((REPEAT_TYPE = #{constants.REPEAT_TYPE_DAYOFMONTH}) AND MONTHDAYS
            LIKE '%' <include refid="utils.concatenate"/> #{date} <include refid="utils.concatenate"/> '%'
        )))
        OR ((SCHEDULE_TYPE = '01'
        <if test="isZeroFrameIndex">
            OR SCHEDULE_TYPE = '02' OR SCHEDULE_TYPE = '03' OR SCHEDULE_TYPE = '04'
        </if>
        ) AND
        ((REPEAT_TYPE = 'daily') OR (WEEKDAYS
            LIKE '%' <include refid="utils.concatenate"/> #{map.day} <include refid="utils.concatenate"/> '%'
        ))))
        ORDER BY
        SCHEDULE_TYPE ASC
    </select>

    <select id="getScheduleData"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity">
        SELECT
            S.*,
            C.CONTENT_NAME,
            P.PLAYLIST_NAME,
            V.IS_STREAMING AS STREAMING_ALLOWED
        FROM MI_CDS_INFO_SCHEDULE_TEMP AS S LEFT JOIN MI_CMS_INFO_CONTENT AS C
                ON S.CONTENT_ID = C.CONTENT_ID
            LEFT JOIN MI_CMS_INFO_PLAYLIST AS P
                ON S.CONTENT_ID = P.PLAYLIST_ID
            LEFT JOIN MI_CMS_INFO_CONTENT_VERSION AS V
                ON C.CONTENT_ID = V.CONTENT_ID
        WHERE S.PROGRAM_ID = #{map.program_id} AND S.SESSION_ID = #{map.session_id}
              AND S.SCHEDULE_ID = #{map.schedule_id} AND (V.IS_ACTIVE = 'Y' OR CONTENT_TYPE = 'PLAYLIST')
    </select>

    <select id="getScheduleListPage"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity">
        <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />    
        <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
        SELECT
        S.*,
        C.CONTENT_NAME,
        P.PLAYLIST_NAME
        <include refid="getScheduleListPageFromWhere"/>
        ORDER BY ${safe_sortColumn} ${safe_sortOrder}
        LIMIT #{map.limit} OFFSET #{map.offset}
    </select>

    <select id="getScheduleListPage"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity" databaseId="mssql">
        <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset + map.limit)" />
        <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
        SELECT * FROM
        (
        SELECT S.*, C.CONTENT_NAME, P.PLAYLIST_NAME, ROW_NUMBER() OVER(ORDER BY ${safe_sortColumn} ${safe_sortOrder}) as rownum
        <include refid="getScheduleListPageFromWhere"/>
        ) as SubQuery
        WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit}
        ORDER BY rownum
    </select>

    <select id="getScheduleListCnt" resultType="java.lang.Integer">
        SELECT
        COUNT(S.SCHEDULE_ID)
        <include refid="getScheduleListPageFromWhere"/>
    </select>

    <select id="getScheduleList" resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity">
    	<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />                    
        <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
        SELECT
        S.*,
        C.CONTENT_NAME,
        P.PLAYLIST_NAME
        <include refid="getScheduleListFromWhere"/>
        ORDER BY ${safe_sortColumn} ${safe_sortOrder}
        LIMIT #{map.limit} OFFSET #{map.offset}
    </select>

    <select id="getScheduleList" resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity" databaseId="mssql">
    	<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset + map.limit)" />
        <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
        SELECT * FROM
        (
        SELECT S.*, C.CONTENT_NAME, P.PLAYLIST_NAME, ROW_NUMBER() OVER(ORDER BY ${safe_sortColumn} ${safe_sortOrder}) as rownum
        <include refid="getScheduleListFromWhere"/>
        ) as SubQuery
        WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit}
        ORDER BY rownum
    </select>

    <select id="getScheduleListCount" resultType="java.lang.Integer">
        SELECT
        COUNT(S.SCHEDULE_ID)
        <include refid="getScheduleListFromWhere"/>
    </select>

    <select id="getHWConstraintList"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity">
        <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
        <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
        SELECT
        S.*
        <include refid="getHWConstraintListFromWhere"/>
        ORDER BY ${safe_sortColumn} ${safe_sortOrder}
        LIMIT #{map.limit} OFFSET #{map.offset}
    </select>

    <select id="getHWConstraintList"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity" databaseId="mssql">
        <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset + map.limit)" />
        <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
        SELECT * FROM
        (
        SELECT S.*, ROW_NUMBER() OVER(ORDER BY ${safe_sortColumn} ${safe_sortOrder}) as rownum
        <include refid="getHWConstraintListFromWhere"/>
        ) as SubQuery
        WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit}
        ORDER BY rownum
    </select>

    <select id="getHWConstraintListCount" resultType="java.lang.Integer">
        SELECT
        COUNT(S.SCHEDULE_ID)
        <include refid="getHWConstraintListFromWhere"/>
    </select>

    <select id="getDeviceGroupIds" resultType="java.lang.Long">
        SELECT
            DEVICE_GROUP_ID
        FROM MI_CDS_MAP_PROGRAM_DEVICE
        WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getProgramGroupIdAndName" resultType="java.util.Map">
        SELECT
            P.GROUP_ID,
            G.GROUP_NAME
        FROM MI_CDS_MAP_PROGRAM_GROUP AS P LEFT JOIN MI_CDS_INFO_PROGRAM_GROUP AS G
                ON P.GROUP_ID = G.GROUP_ID
        WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getActiveProgramVersion" resultType="java.lang.Long">
        SELECT
            VERSION
        FROM MI_CDS_INFO_ACTIVE_PROGRAM
        WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getProgramVersion" resultType="java.lang.Long">
        SELECT
            VERSION
        FROM MI_CDS_INFO_PROGRAM
        WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getProgramByContentId" resultType="java.util.Map">
        SELECT
            DISTINCT A.PROGRAM_ID,
            A.PROGRAM_NAME
        FROM MI_CDS_INFO_PROGRAM A
            JOIN (SELECT
                      PROGRAM_ID
                  FROM MI_CDS_INFO_PROGRAM
                  WHERE bgm_content_id = #{contentId}
                  UNION SELECT
                            PROGRAM_ID
                        FROM MI_CDS_INFO_FRAME
                        WHERE default_content_id = #{contentId}
                  UNION SELECT
                            PROGRAM_ID
                        FROM MI_CDS_INFO_SCHEDULE
                        WHERE CONTENT_ID = #{contentId} AND CONTENT_TYPE != 'PLAYLIST') B
                ON A.PROGRAM_ID = B.PROGRAM_ID
    </select>

    <select id="getProgramByPlaylistId" resultType="java.util.Map">
        SELECT
            A.PROGRAM_ID,
            A.PROGRAM_NAME
        FROM MI_CDS_INFO_PROGRAM A JOIN (SELECT PROGRAM_ID, CONTENT_ID, CONTENT_TYPE FROM MI_CDS_INFO_SCHEDULE UNION SELECT PROGRAM_ID, CONTENT_ID, CONTENT_TYPE FROM MI_CDS_INFO_ADSCHEDULE) B
                ON A.PROGRAM_ID = B.PROGRAM_ID
        WHERE B.CONTENT_ID = #{playlistId} AND B.CONTENT_TYPE = 'PLAYLIST'
    </select>

    <select id="isProgramNameUnique" resultType="java.lang.Boolean">
        SELECT
        COUNT(PROGRAM_NAME)
        FROM MI_CDS_INFO_PROGRAM A, MI_CDS_MAP_PROGRAM_GROUP B
        WHERE A.PROGRAM_ID =
        B.PROGRAM_ID AND A.PROGRAM_NAME = #{map.program_name} AND A.PROGRAM_ID != #{map.program_id}
        <foreach collection="map.childGroupIds" open="AND B.GROUP_ID IN (" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getChildGroupIdList" resultType="java.util.Map">
        SELECT
            GROUP_ID
        FROM MI_CDS_INFO_PROGRAM_GROUP
        WHERE P_GROUP_ID = #{group_id} AND GROUP_ID != #{nonApprovalGroupId}
    </select>

    <select id="getProgramGroupRoot" resultType="java.util.Map">
        SELECT
            GROUP_ID,
            GROUP_NAME,
            P_GROUP_ID
        FROM MI_CDS_INFO_PROGRAM_GROUP
        WHERE GROUP_ID = #{groupId}
    </select>

    <select id="getDownloadContentList"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.DownloadContentEntity">
        SELECT
            A.CONTENT_ID,
            A.CONTENT_NAME,
            A.TOTAL_SIZE,
            A.PROGRESS, 
            (CASE WHEN B.CONTENT_ID IS NOT NULL THEN 'COMPLETE'
             ELSE 'READY' END) AS STATUS
        FROM (SELECT
                  A.CONTENT_ID,
                  A.CONTENT_NAME,
                  B.TOTAL_SIZE
              FROM MI_CMS_INFO_CONTENT A, MI_CMS_INFO_CONTENT_VERSION B
                  , (SELECT
                         CONTENT_ID
                     FROM MI_CDS_INFO_SCHEDULE
                     WHERE CONTENT_TYPE != 'PLAYLIST' AND
                           PROGRAM_ID = #{programId}
                     UNION SELECT
                               B.CONTENT_ID
                           FROM MI_CDS_INFO_SCHEDULE A,
                               MI_CMS_MAP_PLAYLIST_CONTENT B
                           WHERE A.CONTENT_TYPE = 'PLAYLIST' AND
                                 A.CONTENT_ID = B.PLAYLIST_ID AND
                                 A.PROGRAM_ID = #{programId}) C
              WHERE A.CONTENT_ID = B.CONTENT_ID AND A.CONTENT_ID = C.CONTENT_ID) A
            LEFT JOIN (SELECT
                           CONTENT_ID
                       FROM
                           MI_CDS_DOWNLOAD_STATUS
                       WHERE DEVICE_ID = #{device_id}) B
                ON A.CONTENT_ID = B.CONTENT_ID
    </select>
    
    <!-- <select id="getScheduleDetailPublishStatusList"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.DetailDownloadContentEntity">
                   
SELECT
    A.CONTENT_ID,
    A.CONTENT_NAME,
    A.TOTAL_SIZE,
    A.GROUP_NAME, 
    A.DEVICE_NAME,
    A.DEVICE_ID,
    B.PROGRESS
        FROM (SELECT
                  A.CONTENT_ID,
                  A.CONTENT_NAME,
                  B.TOTAL_SIZE,
                  D.GROUP_NAME,
                  E.DEVICE_NAME, 
                  F.DEVICE_ID
              FROM MI_CMS_INFO_CONTENT A, MI_CMS_INFO_CONTENT_VERSION B, 
                   (SELECT CONTENT_ID FROM MI_CDS_INFO_SCHEDULE
                     WHERE CONTENT_TYPE != 'PLAYLIST' AND PROGRAM_ID = #{programId} 
                     UNION SELECT B.CONTENT_ID FROM MI_CDS_INFO_SCHEDULE A, MI_CMS_MAP_PLAYLIST_CONTENT B
                           WHERE A.CONTENT_TYPE = 'PLAYLIST' AND
                                 A.CONTENT_ID = B.PLAYLIST_ID AND
                                 A.PROGRAM_ID = #{programId} ) C, MI_DMS_INFO_GROUP D, MI_DMS_INFO_DEVICE E, MI_DMS_MAP_GROUP_DEVICE F
              WHERE A.CONTENT_ID = B.CONTENT_ID AND A.CONTENT_ID = C.CONTENT_ID AND D.GROUP_ID = #{device_group_id} AND F.GROUP_ID=#{device_group_id} AND F.DEVICE_ID=E.DEVICE_ID) A
            LEFT JOIN (
		SELECT X.CONTENT_ID, X.PROGRESS, X.DEVICE_ID FROM MI_CDS_DOWNLOAD_STATUS X
                       WHERE X.DEVICE_ID IN ( 
			SELECT DEVICE_ID FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = #{device_group_id} ) 
			) B
                ON A.CONTENT_ID = B.CONTENT_ID AND A.DEVICE_ID=B.DEVICE_ID ORDER BY B.DEVICE_ID
                   
                
    </select> -->
    
    <select id="getScheduleDetailPublishStatusList"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.DetailDownloadContentEntity">                   
		SELECT DISTINCT ON(DEVICE_ID) * FROM MI_CDS_DOWNLOAD_STATUS WHERE PROGRAM_ID = #{programId} AND GROUP_ID = #{device_group_id} ORDER BY DEVICE_ID
    </select>
    
    <select id="getScheduleDetailPublishStatusList"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.DetailDownloadContentEntity" databaseId="mssql">                   
		SELECT DISTINCT * FROM MI_CDS_DOWNLOAD_STATUS WHERE PROGRAM_ID = #{programId} AND GROUP_ID = #{device_group_id} ORDER BY DEVICE_ID
    </select>
    
    <select id="getScheduleDetailProgress" resultType="String">
    	SELECT PROGRESS FROM MI_CDS_DOWNLOAD_STATUS_DETAIL WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getDownloadContentPagedList"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.DownloadContentEntity">
        <bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
        <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
        SELECT
        A.CONTENT_ID,
        A.CONTENT_NAME,
        A.TOTAL_SIZE,
        (CASE WHEN B.CONTENT_ID IS NOT NULL THEN 'COMPLETE'
        ELSE 'READY' END) AS STATUS
        <include refid="getDownloadContentPagedListFromWhere"/>        
        ORDER BY ${safe_columnName} ${safe_sortOrder}
        LIMIT #{map.limit} OFFSET #{map.offset}
    </select>
    
     <select id="getDownloadStatusListCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM (
        	<include refid="getDownloadStatusPagedList_select_union_all"/>
        ) AS CountSubQuery
    </select>
    
    <select id="getDownloadStatusPagedList"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.DownloadContentEntity">
        <bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
        <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
        
        <include refid="getDownloadStatusPagedList_select_union_all"/>
		
		ORDER BY ${safe_columnName} ${safe_sortOrder}
		LIMIT #{map.limit} OFFSET #{map.offset}
    </select>
    
    <select id="getDownloadStatusPagedList"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.DownloadContentEntity" databaseId="mssql">
        <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset + map.limit)" />
        <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
        
        SELECT * FROM (
			SELECT ROW_NUMBER() OVER (ORDER BY ${safe_sortColumn} ${safe_sortOrder}) as rownum, * FROM (
				<include refid="getDownloadStatusPagedList_select_union_all"/>
			) AS SubQuery
		) AS SubQuery2
		WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit} ORDER BY rownum
    </select>
    
    <sql id="getDownloadStatusPagedList_select_union_all">
    	SELECT B.RULESET_ID AS CONTENT_ID, B.NAME AS CONTENT_NAME, C.FILE_SIZE AS TOTAL_SIZE, A.PROGRESS, <include refid="utils.true"/> AS IS_RULESET
		FROM MI_CDS_DOWNLOAD_STATUS A, MI_RULE_INFO_RULESET B, MI_CMS_INFO_FILE C
		WHERE
			(A.PROGRAM_ID IS NOT NULL AND A.PROGRAM_ID != '')
			AND A.CONTENT_ID = B.RULESET_ID
			AND B.FILE_ID = C.FILE_ID
			AND A.DEVICE_ID = #{map.device_id} AND A.PROGRAM_ID = #{map.program_id}

		UNION ALL
			
		SELECT STATUS.CONTENT_ID, CONTENTS.CONTENT_NAME, CONTENT_VERSION.TOTAL_SIZE, STATUS.PROGRESS, <include refid="utils.false"/> AS IS_RULESET
		FROM
			MI_CDS_DOWNLOAD_STATUS STATUS, MI_CMS_INFO_CONTENT CONTENTS, MI_CMS_INFO_CONTENT_VERSION CONTENT_VERSION

		WHERE (STATUS.PROGRAM_ID IS NOT NULL AND STATUS.PROGRAM_ID != '')
			AND STATUS.CONTENT_ID = CONTENTS.CONTENT_ID
			AND STATUS.CONTENT_ID = CONTENT_VERSION.CONTENT_ID
			AND CONTENT_VERSION.IS_ACTIVE = 'Y'
			AND STATUS.DEVICE_ID = #{map.device_id}
			AND STATUS.PROGRAM_ID = #{map.program_id}
    </sql>
    
    <select id="getDownloadStatusContentList" resultType="com.samsung.magicinfo.framework.scheduler.entity.DownloadContentEntity">
        <bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
        <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
        SELECT STATUS.CONTENT_ID, CONTENTS.CONTENT_NAME, CONTENT_VERSION.TOTAL_SIZE, PROGRESS AS STATUS
        <include refid="getDownloadContentListFromWhere"/>        
        ORDER BY ${safe_columnName} ${safe_sortOrder}
        LIMIT #{map.limit} OFFSET #{map.offset}
    </select>
    
     <select id="getDownloadStatusContentList" resultType="com.samsung.magicinfo.framework.scheduler.entity.DownloadContentEntity" databaseId="mssql">
        <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset + map.limit)" />
        <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
        SELECT * FROM
        (
        STATUS.CONTENT_ID, CONTENTS.CONTENT_NAME, CONTENT_VERSION.TOTAL_SIZE, PROGRESS AS STATUS, ROW_NUMBER() OVER(ORDER BY ${safe_sortColumn} ${safe_sortOrder}) as rownum
        <include refid="getDownloadContentListFromWhere"/>http://localhost:52264/browser/#
        ) as SubQuery
        WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit}
        ORDER BY rownum
    </select>
    
    <sql id="getDownloadContentListFromWhere">
    	FROM MI_CDS_DOWNLOAD_STATUS STATUS
        LEFT JOIN MI_CMS_INFO_CONTENT CONTENTS ON STATUS.CONTENT_ID = CONTENTS.CONTENT_ID
		LEFT JOIN MI_CMS_INFO_CONTENT_VERSION CONTENT_VERSION ON CONTENTS.CONTENT_ID = CONTENT_VERSION.CONTENT_ID
		WHERE DEVICE_ID = #{map.device_id} AND CONTENT_VERSION.IS_ACTIVE  = 'Y'
    </sql>

    <select id="getDownloadContentPagedList"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.DownloadContentEntity" databaseId="mssql">
         <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset + map.limit)" />
        <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
        SELECT * FROM
        (
        SELECT A.CONTENT_ID, A.CONTENT_NAME, A.TOTAL_SIZE, (CASE WHEN B.CONTENT_ID IS NOT NULL THEN 'COMPLETE'
        ELSE 'READY' END) AS STATUS, ROW_NUMBER() OVER(ORDER BY ${safe_sortColumn} ${safe_sortOrder}) as rownum
        <include refid="getDownloadContentPagedListFromWhere"/>
        ) as SubQuery
        WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit}
        ORDER BY rownum
    </select>

    <select id="getDownloadContentPagedListCount" resultType="java.lang.Integer">
        SELECT
        COUNT(A.CONTENT_ID)
        <include refid="getDownloadContentPagedListFromWhere"/>
    </select>
    
    <select id="getDownloadStatusContenCount" resultType="java.lang.Integer">
        SELECT
        COUNT(STATUS.CONTENT_ID)
        <include refid="getDownloadContentListFromWhere"/>
    </select>

    <select id="getContentScheduleCntToday" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT PROGRAM_ID)
        FROM MI_CDS_INFO_SCHEDULE
        WHERE MODIFY_DATE > #{start_time} AND MODIFY_DATE &lt; #{end_time}
    </select>

    <select id="getContentScheduleTodayGroupId" resultType="java.util.Map">
        SELECT
            B.GROUP_ID
        FROM MI_CDS_INFO_SCHEDULE AS A JOIN MI_CDS_MAP_PROGRAM_GROUP AS B
                ON A.PROGRAM_ID = B
            .PROGRAM_ID
        WHERE A.MODIFY_DATE > #{start_time} AND A.MODIFY_DATE &lt; #{end_time}
    </select>

    <select id="getContentScheduleCntThisWeek" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT PROGRAM_ID)
        FROM MI_CDS_INFO_SCHEDULE
        WHERE MODIFY_DATE > #{start_time} AND MODIFY_DATE &lt; #{end_time}
    </select>

    <select id="getAllScheduleCount" resultType="java.lang.Long">
        SELECT
            COUNT(A.PROGRAM_ID)
        FROM
        	MI_CDS_INFO_PROGRAM A,
        	MI_CDS_MAP_PROGRAM_GROUP B,
			MI_CDS_INFO_PROGRAM_GROUP C
        WHERE
        	A.PROGRAM_ID = B.PROGRAM_ID
        	AND B.GROUP_ID = C.GROUP_ID
        	AND A.DELETED = 'N' AND A.IS_DEFAULT = 'N'
    </select>

    <select id="getAllScheduleGroupId" resultType="java.util.Map">
        SELECT
            GROUP_ID
        FROM MI_CDS_INFO_PROGRAM AS A JOIN MI_CDS_MAP_PROGRAM_GROUP AS B
                ON B.PROGRAM_ID = A.PROGRAM_ID
        WHERE A.DELETED = 'N' AND A.IS_DEFAULT = 'N'
    </select>

    <select id="isDelete" resultType="java.util.Map">
        SELECT
            DELETED
        FROM MI_CDS_INFO_PROGRAM
        WHERE PROGRAM_ID = #{programId}
        LIMIT 1
    </select>

    <select id="isDelete" resultType="java.util.Map" databaseId="mssql">
        SELECT TOP (1)
        DELETED
        FROM MI_CDS_INFO_PROGRAM
        WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getMappedScheduleCount" resultType="java.lang.Long">
        SELECT
            COUNT(PROGRAM_ID)
        FROM MI_CDS_INFO_PROGRAM
        WHERE DELETED = 'N' AND IS_DEFAULT = 'N' AND PROGRAM_ID IN (
            SELECT
                PROGRAM_ID
            FROM MI_CDS_MAP_PROGRAM_DEVICE)
    </select>

    <select id="getMapedScheduleGroupId" resultType="java.util.Map">
        SELECT
            B.GROUP_ID
        FROM MI_CDS_INFO_PROGRAM AS A JOIN MI_CDS_MAP_PROGRAM_GROUP AS B
                ON A.PROGRAM_ID = B.PROGRAM_ID
        WHERE A.DELETED = 'N' AND A.IS_DEFAULT = 'N' AND A.PROGRAM_ID IN (
            SELECT
                PROGRAM_ID
            FROM MI_CDS_MAP_PROGRAM_DEVICE)
    </select>

    <select id="getNotMapedScheduleCount" resultType="java.lang.Long">
        SELECT
            COUNT(PROGRAM_ID)
        FROM MI_CDS_INFO_PROGRAM
        WHERE DELETED = 'N' AND IS_DEFAULT = 'N' AND PROGRAM_ID NOT IN (
            SELECT
                PROGRAM_ID
            FROM MI_CDS_MAP_PROGRAM_DEVICE)
    </select>

    <select id="getNotMapedScheduleGroupId" resultType="java.util.Map">
        SELECT
            B.GROUP_ID
        FROM MI_CDS_INFO_PROGRAM AS A JOIN MI_CDS_MAP_PROGRAM_GROUP AS B
                ON A.PROGRAM_ID = B.PROGRAM_ID
        WHERE A.DELETED = 'N' AND A.IS_DEFAULT = 'N' AND A.PROGRAM_ID NOT IN (
            SELECT
                PROGRAM_ID
            FROM MI_CDS_MAP_PROGRAM_DEVICE)
    </select>

    <select id="select_getFrameTemplates"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.FrameTemplateEntity">
        SELECT
            *
        FROM MI_CDS_INFO_FRAME_TEMPLATE
        WHERE TEMPLATE_TYPE = #{template_type} AND (ORGANIZATION = #{organization} OR ORGANIZATION = 'ROOT')
        ORDER BY TEMPLATE_NAME
    </select>

    <select id="select2_getFrameTemplates"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.FrameTemplateEntity">
        SELECT
            *
        FROM MI_CDS_INFO_FRAME_TEMPLATE
        WHERE TEMPLATE_TYPE = #{template_type}
        ORDER BY TEMPLATE_NAME
    </select>

    <select id="select3_getFrameTemplates"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.FrameTemplateEntity">
        SELECT
            *
        FROM MI_CDS_INFO_FRAME_TEMPLATE
        WHERE (ORGANIZATION = #{organization} OR ORGANIZATION = 'ROOT') AND RESOLUTION = #{resolution}
        <choose>
            <when test="template_type != null and template_type.equals('ALL')"></when>
            <otherwise>AND TEMPLATE_TYPE = #{template_type}</otherwise>
        </choose>
        ORDER BY TEMPLATE_NAME
    </select>

    <select id="select_checkAvailableDiskSpace" resultType="java.lang.Long">
        SELECT
            CASE WHEN Z.TOTAL IS NULL THEN 0
            ELSE Z.TOTAL END
        FROM (
                 SELECT
                     MIN(D.DISK_SPACE_REPOSITORY) AS TOTAL
                 FROM MI_CDS_MAP_PROGRAM_DEVICE_TEMP AS A JOIN MI_DMS_MAP_GROUP_DEVICE AS B
                         ON B.GROUP_ID = A.DEVICE_GROUP_ID
                     JOIN MI_DMS_INFO_DEVICE AS D
                         ON B.DEVICE_ID = D.DEVICE_ID
                 WHERE A.PROGRAM_ID = #{programId}) AS Z
    </select>

    <select id="select2_checkAvailableDiskSpace" resultType="java.lang.Long">
        SELECT
            CASE WHEN Z.TOTAL IS NULL THEN 0
            ELSE Z.TOTAL END
        FROM (
                 SELECT
                     MIN(D.DISK_SPACE_REPOSITORY) AS TOTAL
                 FROM MI_CDS_MAP_PROGRAM_DEVICE AS A JOIN MI_DMS_MAP_GROUP_DEVICE AS B
                         ON B.GROUP_ID = A.DEVICE_GROUP_ID
                     JOIN MI_DMS_INFO_DEVICE AS D
                         ON B.DEVICE_ID = D.DEVICE_ID
                 WHERE A.PROGRAM_ID = #{programId}
             ) AS Z
    </select>

    <select id="select3_checkAvailableDiskSpace" resultType="java.lang.Long">
        SELECT
            CASE WHEN SUM(Z.TOTAL_SIZE) IS NULL THEN 0
            ELSE SUM(Z.TOTAL_SIZE) END AS TOTAL
        FROM (SELECT
                  DISTINCT Y.CONTENT_ID,
                  Y.TOTAL_SIZE
              FROM (
                       (SELECT
                            CV.CONTENT_ID,
                            CV.TOTAL_SIZE
                        FROM MI_CDS_INFO_SCHEDULE_TEMP S, MI_CMS_INFO_PLAYLIST_VERSION PV,
                            MI_CMS_MAP_PLAYLIST_CONTENT PC,
                            MI_CMS_INFO_CONTENT_VERSION CV
                        WHERE
                            S.PROGRAM_ID = #{programId} AND S.SESSION_ID = #{session_id}
                            AND S.STOP_DATE >= #{curr_date} AND S.CONTENT_TYPE = 'PLAYLIST'
                            AND S.CONTENT_ID = PV.PLAYLIST_ID AND PV.PLAYLIST_ID = PC.PLAYLIST_ID
                            AND PV.VERSION_ID = PC.VERSION_ID AND PC.CONTENT_ID = CV.CONTENT_ID AND PV.IS_ACTIVE = 'Y'
                            AND CV.IS_ACTIVE = 'Y')
                       UNION
                       (SELECT
                            S.CONTENT_ID,
                            CV.TOTAL_SIZE
                        FROM MI_CDS_INFO_SCHEDULE_TEMP AS S LEFT JOIN MI_CMS_INFO_CONTENT_VERSION AS CV
                                ON S.CONTENT_ID = CV.CONTENT_ID
                        WHERE S.PROGRAM_ID = #{programId} AND S.SESSION_ID = #{session_id}
                              AND S.STOP_DATE >= #{curr_date} AND S.CONTENT_TYPE != 'PLAYLIST' AND CV.IS_ACTIVE = 'Y')
                   )
                  AS Y)
            AS Z
    </select>

    <select id="select4_checkAvailableDiskSpace" resultType="java.lang.Long">
        SELECT
            TOTAL_SIZE
        FROM MI_CMS_INFO_CONTENT_VERSION
        WHERE CONTENT_ID IN (
            SELECT
                DEFAULT_CONTENT_ID
            FROM MI_CDS_MAP_PROGRAM_DEVICE_TEMP
            WHERE DEFAULT_CONTENT_ID NOT LIKE '' AND DEFAULT_CONTENT_ID IS NOT NULL AND PROGRAM_ID = #{programId})
    </select>

    <select id="select5_checkAvailableDiskSpace" resultType="java.lang.Long">
        SELECT
            TOTAL_SIZE
        FROM MI_CMS_INFO_CONTENT_VERSION
        WHERE CONTENT_ID IN (
            SELECT
                DEFAULT_CONTENT_ID
            FROM MI_CDS_INFO_FRAME
            WHERE DEFAULT_CONTENT_ID IN (
                SELECT
                    DISTINCT (B.DEFAULT_CONTENT_ID)
                FROM MI_CDS_MAP_PROGRAM_DEVICE AS A, MI_CDS_INFO_FRAME AS B
                WHERE A.PROGRAM_ID = B.PROGRAM_ID AND B.DEFAULT_CONTENT_ID NOT LIKE ''
                      AND B.DEFAULT_CONTENT_ID IS NOT NULL AND A.PROGRAM_ID = #{programId}))
    </select>

    <select id="select6_checkAvailableDiskSpace" resultType="java.lang.Long">
        SELECT
            TOTAL_SIZE
        FROM MI_CMS_INFO_CONTENT_VERSION
        WHERE CONTENT_ID IN (
            SELECT
                BGM_CONTENT_ID
            FROM MI_CDS_MAP_PROGRAM_DEVICE_TEMP
            WHERE BGM_CONTENT_ID NOT LIKE '' AND BGM_CONTENT_ID IS NOT NULL AND PROGRAM_ID = #{programId})
    </select>

    <select id="select7_checkAvailableDiskSpace" resultType="java.lang.Long">
        SELECT
            TOTAL_SIZE
        FROM MI_CMS_INFO_CONTENT_VERSION
        WHERE CONTENT_ID IN (
            SELECT
                DISTINCT (B.BGM_CONTENT_ID)
            FROM MI_CDS_MAP_PROGRAM_DEVICE AS A, MI_CDS_INFO_PROGRAM AS B
            WHERE A.PROGRAM_ID = B.PROGRAM_ID AND B.BGM_CONTENT_ID NOT LIKE '' AND B.BGM_CONTENT_ID IS NOT NULL
                  AND A.PROGRAM_ID = #{programId})
    </select>

    <select id="getProgramIdByProgramName" resultType="java.lang.String">
        SELECT
            PROGRAM_ID
        FROM MI_CDS_INFO_PROGRAM
        WHERE PROGRAM_NAME = #{programName}
    </select>

    <select id="select_addProgramWithBasicInformation" resultType="java.util.Map">
        SELECT <include refid="select_addProgramWithBasicInformation_body"/> LIMIT 1
    </select>

    <select id="select_addProgramWithBasicInformation" resultType="java.util.Map" databaseId="mssql">
        SELECT TOP (1) <include refid="select_addProgramWithBasicInformation_body"/>
    </select>

    <sql id="select_addProgramWithBasicInformation_body">
        A.DEFAULT_PROGRAM_ID,
        B.PROGRAM_ID
        FROM MI_DMS_INFO_GROUP AS A JOIN MI_CDS_MAP_PROGRAM_DEVICE AS B
        ON A.GROUP_ID = B.DEVICE_GROUP_ID
        WHERE B.DEVICE_GROUP_ID = #{deviceId}
        AND A.DEFAULT_PROGRAM_ID = B.PROGRAM_ID
        ORDER BY A.GROUP_ID ASC
    </sql>

    <select id="select_modifyProgramWithFrameAndHWControlAndContent" resultType="java.util.Map">
        SELECT <include refid="select_modifyProgramWithFrameAndHWControlAndContent_body"/> LIMIT 1
    </select>

    <select id="select_modifyProgramWithFrameAndHWControlAndContent" resultType="java.util.Map" databaseId="mssql">
        SELECT TOP (1) <include refid="select_modifyProgramWithFrameAndHWControlAndContent_body"/>
    </select>

    <sql id="select_modifyProgramWithFrameAndHWControlAndContent_body">
        A.DEFAULT_PROGRAM_ID,
        B.PROGRAM_ID
        FROM MI_DMS_INFO_GROUP AS A JOIN MI_CDS_MAP_PROGRAM_DEVICE AS B
        ON A.GROUP_ID = B.DEVICE_GROUP_ID
        WHERE B.DEVICE_GROUP_ID = #{groupId}
        ORDER BY A.GROUP_ID ASC
    </sql>

    <select id="select2_modifyProgramWithFrameAndHWControlAndContent" resultType="java.util.Map">
        SELECT <include refid="select2_modifyProgramWithFrameAndHWControlAndContent_body"/> LIMIT 1
    </select>

    <select id="select2_modifyProgramWithFrameAndHWControlAndContent" resultType="java.util.Map" databaseId="mssql">
        SELECT TOP (1) <include refid="select2_modifyProgramWithFrameAndHWControlAndContent_body"/>
    </select>

    <sql id="select2_modifyProgramWithFrameAndHWControlAndContent_body">
        PRIORITY
        FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{schedule.program_id} AND FRAME_INDEX = #{schedule.frame_index}
        ORDER BY PRIORITY DESC
    </sql>

    <select id="select_addProgramWithFrameAndHWControlAndContent" resultType="java.util.Map">
        SELECT <include refid="select_addProgramWithFrameAndHWControlAndContent_body" /> LIMIT 1
    </select>

    <select id="select_addProgramWithFrameAndHWControlAndContent" resultType="java.util.Map" databaseId="mssql">
        SELECT TOP (1) <include refid="select_addProgramWithFrameAndHWControlAndContent_body" />
    </select>

    <sql id="select_addProgramWithFrameAndHWControlAndContent_body">
        A.DEFAULT_PROGRAM_ID,
        B.PROGRAM_ID
        FROM MI_DMS_INFO_GROUP AS A JOIN MI_CDS_MAP_PROGRAM_DEVICE AS B
        ON A.GROUP_ID = B.DEVICE_GROUP_ID
        WHERE B.DEVICE_GROUP_ID = #{deviceGroupId}
        ORDER BY A.GROUP_ID ASC
    </sql>

    <select id="select2_addProgramWithFrameAndHWControlAndContent" resultType="java.util.Map">
        SELECT <include refid="select2_addProgramWithFrameAndHWControlAndContent_body"/> LIMIT 1
    </select>

    <select id="select2_addProgramWithFrameAndHWControlAndContent" resultType="java.util.Map" databaseId="mssql">
        SELECT TOP (1) <include refid="select2_addProgramWithFrameAndHWControlAndContent_body"/>
    </select>

    <sql id="select2_addProgramWithFrameAndHWControlAndContent_body">
        PRIORITY
        FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{schedule.program_id} AND FRAME_INDEX = #{schedule.frame_index}
        ORDER BY PRIORITY DESC
    </sql>

    <select id="select_modifyProgramWithBasicInformation" resultType="java.util.Map">
        SELECT <include refid="select_modifyProgramWithBasicInformation_body"/> LIMIT 1
    </select>

    <select id="select_modifyProgramWithBasicInformation" resultType="java.util.Map" databaseId="mssql">
        SELECT TOP (1) <include refid="select_modifyProgramWithBasicInformation_body"/>
    </select>

    <sql id="select_modifyProgramWithBasicInformation_body">
        A.DEFAULT_PROGRAM_ID,
        B.PROGRAM_ID
        FROM MI_DMS_INFO_GROUP AS A JOIN MI_CDS_MAP_PROGRAM_DEVICE AS B
        ON A.GROUP_ID = B.DEVICE_GROUP_ID
        WHERE B.DEVICE_GROUP_ID = #{deviceGroupId}
        ORDER BY A.GROUP_ID ASC
    </sql>

    <select id="select_addContentScheduleWithoutTemp" resultType="java.util.Map">
        SELECT <include refid="select_addContentScheduleWithoutTemp_body"/> LIMIT 1
    </select>

    <select id="select_addContentScheduleWithoutTemp" resultType="java.util.Map" databaseId="mssql">
        SELECT TOP (1) <include refid="select_addContentScheduleWithoutTemp_body"/>
    </select>

    <sql id="select_addContentScheduleWithoutTemp_body">
        PRIORITY
        FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{schedule.program_id} AND FRAME_INDEX = #{schedule.frame_index}
        ORDER BY PRIORITY DESC
    </sql>

    <select id="select_modifyContentScheduleWithoutTemp" resultType="java.util.Map">
        SELECT <include refid="select_modifyContentScheduleWithoutTemp_body"/> LIMIT 1
    </select>

    <select id="select_modifyContentScheduleWithoutTemp" resultType="java.util.Map" databaseId="mssql">
        SELECT TOP (1) <include refid="select_modifyContentScheduleWithoutTemp_body"/>
    </select>

    <sql id="select_modifyContentScheduleWithoutTemp_body">
        PRIORITY
        FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{schedule.program_id}
        AND FRAME_INDEX = #{schedule.frame_index}
        AND CHANNEL_NO = #{schedule.channel_no}
        ORDER BY PRIORITY DESC
    </sql>

    <select id="getContentScheduleIdByFrameId" resultType="java.util.Map">
        SELECT
            S.SCHEDULE_ID
        FROM MI_CDS_INFO_FRAME AS F JOIN MI_CDS_INFO_SCHEDULE AS S
                ON F.PROGRAM_ID = S.PROGRAM_ID AND F.FRAME_INDEX = S.FRAME_INDEX
        WHERE S.FRAME_INDEX = F.FRAME_INDEX AND F.FRAME_ID = #{frameId}
    </select>

    <select id="getFrameIndexByFrameId" resultType="java.lang.Long">
        SELECT
            FRAME_INDEX
        FROM MI_CDS_INFO_FRAME
        WHERE FRAME_ID = #{frameId}
    </select>

    <select id="getDeviceTypeByProgramId" resultType="java.lang.String">
        SELECT
            DEVICE_TYPE
        FROM MI_CDS_INFO_PROGRAM
        WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getProgramIdByFrameId" resultType="java.lang.String">
        SELECT
            PROGRAM_ID
        FROM MI_CDS_INFO_FRAME
        WHERE FRAME_ID = #{frameId}
        LIMIT 1
    </select>

    <select id="getProgramIdByFrameId" resultType="java.lang.String" databaseId="mssql">
        SELECT TOP (1)
        PROGRAM_ID
        FROM MI_CDS_INFO_FRAME
        WHERE FRAME_ID = #{frameId}
    </select>

    <select id="getProgramIdByByScheduleId" resultType="java.util.Map">
        SELECT
            PROGRAM_ID
        FROM MI_CDS_INFO_SCHEDULE
        WHERE SCHEDULE_ID = #{scheduleId}
    </select>

    <select id="getCreatorIdByProgramId" resultType="java.lang.String">
        SELECT
            USER_ID
        FROM MI_CDS_INFO_PROGRAM
        WHERE PROGRAM_ID = #{programId}
        LIMIT 1
    </select>

    <select id="getCreatorIdByProgramId" resultType="java.lang.String" databaseId="mssql">
        SELECT TOP (1)
        USER_ID
        FROM MI_CDS_INFO_PROGRAM
        WHERE PROGRAM_ID = #{programId}
    </select>

    <sql id="getProgramListQuery">
        SELECT * FROM MI_CDS_INFO_PROGRAM
        <where>
            <if test="map.screen_count">
                SCREEN_COUNT = #{map.screen_count}
            </if>
        </where>
        <if test="map.program_id">
            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.program_id)" />
            ORDER BY PROGRAM_ID ${safe_sortOrder}
        </if>
        LIMIT #{map.limit} OFFSET #{map.offset}
    </sql>

    <sql id="getProgramListQuery" databaseId="mssql">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset + map.limit)" />
        SELECT * FROM
        (
        SELECT *, ROW_NUMBER() OVER(
        <choose>
            <when test="map.program_id">
                <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.program_id)" />
                ORDER BY PROGRAM_ID ${safe_sortOrder}
            </when>
            <otherwise>ORDER BY PROGRAM_ID</otherwise>
        </choose>
        ) as rownum
        FROM MI_CDS_INFO_PROGRAM
        <where>
            <if test="map.screen_count">
                SCREEN_COUNT = #{map.screen_count}
            </if>
        </where>
        ) as SubQuery
        WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit}
        ORDER BY rownum
    </sql>

    <sql id="getScheduleListPageFromWhere">
        FROM MI_CDS_INFO_SCHEDULE_TEMP AS S LEFT JOIN MI_CMS_INFO_CONTENT AS C
        ON S.CONTENT_ID = C.CONTENT_ID
        LEFT JOIN MI_CMS_INFO_PLAYLIST AS P
        ON S.CONTENT_ID = P.PLAYLIST_ID
        LEFT JOIN MI_CMS_INFO_CONTENT_VERSION AS V
        ON C.CONTENT_ID = V.CONTENT_ID
        LEFT JOIN MI_CMS_INFO_PLAYLIST_VERSION AS PV
        ON P.PLAYLIST_ID = PV.PLAYLIST_ID
        WHERE PROGRAM_ID = #{map.program_id} AND S.SESSION_ID = #{map.session_id} AND CHANNEL_NO = #{map.channel_no}
        AND FRAME_INDEX = #{map.frame_index} AND SCHEDULE_TYPE = '00' AND
        ((S.SCHEDULE_TYPE = '00' AND S.CONTENT_TYPE != 'PLAYLIST' AND V.IS_ACTIVE = 'Y') OR
        (S.SCHEDULE_TYPE = '00' AND S.CONTENT_TYPE = 'PLAYLIST' AND PV.IS_ACTIVE = 'Y') OR
        (S.SCHEDULE_TYPE = '00' AND S.CONTENT_TYPE = 'HW_IS'))
        <include refid="dateCondition"/>
        <include refid="timeCondition"/>
    </sql>

    <sql id="getScheduleListFromWhere">
        FROM MI_CDS_INFO_SCHEDULE AS S LEFT JOIN MI_CMS_INFO_CONTENT AS C
        ON S.CONTENT_ID = C.CONTENT_ID
        LEFT JOIN MI_CMS_INFO_PLAYLIST AS P
        ON S.CONTENT_ID = P.PLAYLIST_ID
        LEFT JOIN MI_CMS_INFO_CONTENT_VERSION AS V
        ON C.CONTENT_ID = V.CONTENT_ID
        LEFT JOIN MI_CMS_INFO_PLAYLIST_VERSION AS PV
        ON P.PLAYLIST_ID = PV.PLAYLIST_ID
        WHERE PROGRAM_ID = #{map.program_id} AND CHANNEL_NO = #{map.channel_no} AND FRAME_INDEX = #{map.frame_index} AND SCHEDULE_TYPE = '00' AND
        ((S.SCHEDULE_TYPE = '00' AND S.CONTENT_TYPE != 'PLAYLIST' AND V.IS_ACTIVE = 'Y') OR
        (S.SCHEDULE_TYPE = '00' AND S.CONTENT_TYPE = 'PLAYLIST' AND PV.IS_ACTIVE = 'Y'))
        <include refid="dateCondition"/>
        <include refid="timeCondition"/>
        <if test="map.searchText">
            AND UPPER(C.CONTENT_NAME)
                LIKE '%' <include refid="utils.concatenate"/> #{map.searchText} <include refid="utils.concatenate"/> '%'
                ESCAPE '^'
        </if>
    </sql>

    <sql id="dateCondition">
        <if test="map.spdate and map.epdate">
            AND ((START_DATE &lt;= #{map.spdate} AND STOP_DATE >= #{map.spdate})
            OR (START_DATE >= #{map.spdate} AND START_DATE &lt;= #{map.epdate} AND STOP_DATE >= #{map.spdate}))
        </if>
    </sql>

    <sql id="timeCondition">
        <if test="map.sptime and map.eptime">
            AND ((START_TIME &lt;= #{map.sptime}
            AND TO_CHAR((START_TIME::TIME + DURATION * INTERVAL '1 SECOND'), 'HH24:MI:SS') >= #{map.sptime})
            OR (START_TIME >= #{map.sptime} AND START_TIME &lt;= #{map.eptime}
            AND TO_CHAR((START_TIME::TIME + DURATION * INTERVAL '1 SECOND'), 'HH24:MI:SS') >= #{map.sptime}))
        </if>
    </sql>

    <sql id="timeCondition" databaseId="mssql">
        <if test="map.sptime and map.eptime">
            AND (
            (START_TIME &lt;= #{map.sptime}
            AND convert(varchar(8), DATEADD(S, DURATION, convert(datetime, START_TIME, 114)), 114) >= #{map.sptime}
            )
            OR  (START_TIME >= #{map.sptime} AND START_TIME &lt;= #{map.eptime}
            AND convert(varchar(8), DATEADD(S, DURATION , convert(datetime, START_TIME, 114)), 114) >= #{map.sptime}
            )
            )
        </if>
    </sql>
    
    <sql id="timeCondition" databaseId="mysql">
        <if test="map.sptime and map.eptime">
            AND (
            (START_TIME &lt;= #{map.sptime}
            AND TIME_FORMAT(SEC_TO_TIME(DURATION + TIME_TO_SEC(START_TIME)),'%H:%i:%s') >= #{map.sptime}
            )
            OR  (START_TIME >= #{map.sptime} AND START_TIME &lt;= #{map.eptime}
            AND TIME_FORMAT(SEC_TO_TIME(DURATION + TIME_TO_SEC(START_TIME)),'%H:%i:%s') >= #{map.sptime}
            )
            )
        </if>
    </sql>

    <sql id="getHWConstraintListFromWhere">
        FROM MI_CDS_INFO_SCHEDULE AS S
        WHERE PROGRAM_ID = #{map.program_id} AND SCHEDULE_TYPE != '00'
        <include refid="dateCondition"/>
        <include refid="timeCondition"/>
    </sql>

    <sql id="getDownloadContentPagedListFromWhere">
        FROM (SELECT
        A.CONTENT_ID,
        A.CONTENT_NAME,
        B.TOTAL_SIZE
        FROM MI_CMS_INFO_CONTENT A, MI_CMS_INFO_CONTENT_VERSION B
        , (SELECT
        CONTENT_ID
        FROM MI_CDS_INFO_SCHEDULE
        WHERE CONTENT_TYPE != 'PLAYLIST' AND PROGRAM_ID = #{map.program_id} 
        AND <include refid="getDownloadContentPagedListFromWhere_STOP_DATE_asDate"/> >= <include refid="getDownloadContentPagedListFromWhere_mapStopDate_or_systemDate"/>
        UNION SELECT
        DISTINCT (A.CONTENT_ID)
        FROM MI_CMS_MAP_PLAYLIST_CONTENT A,
        (SELECT
        A.VERSION_ID,
        A.PLAYLIST_ID
        FROM
        MI_CMS_INFO_PLAYLIST_VERSION A,
        MI_CDS_INFO_SCHEDULE B
        WHERE A.IS_ACTIVE = 'Y' AND A.PLAYLIST_ID = B.CONTENT_ID
        AND B.PROGRAM_ID = #{map.program_id} 
        AND <include refid="getDownloadContentPagedListFromWhere_STOP_DATE_asDate"/> >= <include refid="getDownloadContentPagedListFromWhere_mapStopDate_or_systemDate"/>
        ) B
        WHERE A.VERSION_ID = B.VERSION_ID AND A.PLAYLIST_ID = B.PLAYLIST_ID AND (A.EXPIRED_DATE IS NULL OR (A.EXPIRED_DATE >= <include refid="getDownloadContentPagedListFromWhere_ExpiredDate"/>))) C
        WHERE A.CONTENT_ID = B.CONTENT_ID AND A.CONTENT_ID = C.CONTENT_ID AND B.IS_ACTIVE = 'Y') A LEFT JOIN
        (SELECT
        CONTENT_ID
        FROM MI_CDS_DOWNLOAD_STATUS
        WHERE DEVICE_ID = #{map.device_id} AND ACTIVE_TYPE = 'content') B
        ON A.CONTENT_ID = B.CONTENT_ID
    </sql>
    
    <sql id="getDownloadContentPagedListFromWhere_ExpiredDate" databaseId="mssql">
    	CAST(#{map.expired_date} AS DATE)
    </sql>
    
    <sql id="getDownloadContentPagedListFromWhere_ExpiredDate">
    	to_date(#{map.expired_date}, 'yyyy-MM-dd')
    </sql>

    <sql id="getDownloadContentPagedListFromWhere_STOP_DATE_asDate">
        to_date(STOP_DATE, 'yyyy-MM-dd')
    </sql>
    <sql id="getDownloadContentPagedListFromWhere_STOP_DATE_asDate" databaseId="mssql">
    	CAST(STOP_DATE AS DATE)
    </sql>
    <sql id="getDownloadContentPagedListFromWhere_STOP_DATE_asDate" databaseId="mysql">
    	DATE_FORMAT(STOP_DATE, '%Y-%m-%d')
    </sql>
	
    <sql id="getDownloadContentPagedListFromWhere_stopDate_or_systemDate_MAP_STOP_DATE_asDate">
        to_date(#{map.stop_date}, 'yyyy-MM-dd')
    </sql>
    <sql id="getDownloadContentPagedListFromWhere_stopDate_or_systemDate_MAP_STOP_DATE_asDate" databaseId="mssql">
    	CAST(#{map.stop_date} AS DATE)
    </sql>
    <sql id="getDownloadContentPagedListFromWhere_stopDate_or_systemDate_MAP_STOP_DATE_asDate" databaseId="mysql">
    	DATE_FORMAT(#{map.stop_date}, '%Y-%m-%d')
    </sql>
    
    <sql id="getDownloadContentPagedListFromWhere_mapStopDate_or_systemDate">
        <choose>
            <when test="map.stop_date"><include refid="getDownloadContentPagedListFromWhere_stopDate_or_systemDate_MAP_STOP_DATE_asDate"/></when>
            <otherwise><include refid="utils.currentDate"/></otherwise>
        </choose>
    </sql>


    <delete id="deleteChannel">
    	DELETE FROM MI_CDS_INFO_CHANNEL WHERE PROGRAM_ID = #{programId}
    </delete>

    <insert id="copyChannelFromTmpToOriginTable">
    	INSERT INTO MI_CDS_INFO_CHANNEL (PROGRAM_ID, CHANNEL_NO, CHANNEL_NAME, CHANNEL_DESCRIPTION)
    	SELECT PROGRAM_ID, CHANNEL_NO, CHANNEL_NAME, CHANNEL_DESCRIPTION FROM MI_CDS_INFO_CHANNEL_TEMP
    	 WHERE PROGRAM_ID = #{programId}  AND SESSION_ID = #{sessionId} ORDER BY CHANNEL_NO ASC
    </insert>

    <delete id="deleteChannelTmp">
    	DELETE FROM MI_CDS_INFO_CHANNEL_TEMP WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{sessionId}
    </delete>

    <insert id="copyChannelFromOriginToTmpTable">
    	INSERT INTO MI_CDS_INFO_CHANNEL_TEMP 
    	(SESSION_ID, PROGRAM_ID, CHANNEL_NO, CHANNEL_NAME, CHANNEL_DESCRIPTION)
    	SELECT #{sessionId} , PROGRAM_ID, CHANNEL_NO, CHANNEL_NAME, CHANNEL_DESCRIPTION
    	FROM MI_CDS_INFO_CHANNEL WHERE PROGRAM_ID = #{programId} ORDER BY CHANNEL_NO ASC
    </insert>

    <select id="selectChannelsWithProgramId" resultType="com.samsung.magicinfo.framework.scheduler.entity.ChannelEntity">
    	SELECT * FROM MI_CDS_INFO_CHANNEL WHERE PROGRAM_ID = #{programId} ORDER BY CHANNEL_NO ASC
    </select>

    <insert id="insertIntoChannelTmpTable">
    	INSERT INTO MI_CDS_INFO_CHANNEL_TEMP (PROGRAM_ID, SESSION_ID, CHANNEL_NO, CHANNEL_NAME, CHANNEL_DESCRIPTION)
    	VALUES (#{newProgramId}, #{sessionId}, #{channel.channel_no}, 
    	#{channel.channel_name}, #{channel.channel_description})
    </insert>

    <select id="selectSchedulesWithSheduleId" resultType="com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity">
    	SELECT * FROM MI_CDS_INFO_SCHEDULE WHERE SCHEDULE_ID = #{scheduleId}
    </select>

    <insert id="insertScheduleTmp">
    	INSERT INTO MI_CDS_INFO_SCHEDULE_TEMP (PROGRAM_ID, SESSION_ID, PLAYER_MODE
        , SAFETYLOCK, CHANNEL_NO, SCREEN_INDEX, FRAME_INDEX
        , SCHEDULE_ID, START_DATE, STOP_DATE, START_TIME
        , DURATION, REPEAT_TYPE, WEEKDAYS, MONTHDAYS
        , USER_ID, CREATE_DATE, MODIFY_DATE
        , SCHEDULE_TYPE, HW_INPUT_SOURCE, HW_ATVDTV, HW_AIRCABLE
        , HW_MAJORCH, HW_MINORCH, HW_VOLUME, HW_SCH_CH, CONTENT_ID, CONTENT_TYPE, REPEAT_TIME, IN_EFFECT_TYPE, IN_EFFECT_DURATION,
        IN_EFFECT_DIRECTION, OUT_EFFECT_TYPE, OUT_EFFECT_DURATION, OUT_EFFECT_DIRECTION, PRIORITY, IS_STREAMING,
        SLIDE_TRANSITION_TIME)
        VALUES (#{programId}, #{sessionId}
        , #{schedule.player_mode}, #{schedule.safetyLock}, #{channelNo}
        , #{schedule.screen_index}, #{schedule.frame_index}
        , #{scheduleId}, #{schedule.start_date}, #{schedule.stop_date}, #{schedule.start_time}
        , #{schedule.duration}, #{schedule.repeat_type}, #{schedule.weekdays}, #{schedule.monthdays}
        , #{schedule.user_id}, #{schedule.create_date}, #{schedule.modify_date}
        , #{schedule.schedule_type}, #{schedule.hw_input_source}, #{schedule.hw_AtvDtv}, #{schedule.hw_AirCable}
        , #{schedule.hw_MajorCH}, #{schedule.hw_MinorCH}, #{schedule.hw_Volume}, #{schedule.hw_sch_ch}, #{schedule.content_id}
        , #{schedule.content_type}, #{schedule.repeat_time}, #{schedule.in_effect_type}
        , #{schedule.in_effect_duration}, #{schedule.in_effect_direction}, #{schedule.out_effect_type}
        , #{schedule.out_effect_duration}, #{schedule.out_effect_direction}, #{schedule.priority}, #{schedule.is_streaming}
        , #{schedule.slide_transition_time})
    </insert>

    <insert id="insert7_addProgramWithBasicInformation">
        INSERT INTO MI_CDS_INFO_CHANNEL (PROGRAM_ID, CHANNEL_NO, CHANNEL_NAME, CHANNEL_DESCRIPTION)
        VALUES (#{channel.program_id}, #{channel.channel_no}, #{channel.channel_name}, #{channel.channel_description})
    </insert>

    <delete id="deleteTempScheduleByChannelNo">
		DELETE FROM MI_CDS_INFO_SCHEDULE_TEMP WHERE PROGRAM_ID = #{programId} AND SESSION_ID = #{sessionId} AND CHANNEL_NO = #{channelNo}
	</delete>

    <delete id="deleteTempFrameByChannelNo">
		DELETE FROM MI_CDS_INFO_FRAME_TEMP 
		WHERE PROGRAM_ID = #{programId}
		AND SESSION_ID = #{sessionId}
		AND CHANNEL_NO = #{channelNo}
	</delete>

    <select id="getChannels" resultType="com.samsung.magicinfo.framework.scheduler.entity.ChannelEntity">
		SELECT * FROM MI_CDS_INFO_CHANNEL WHERE PROGRAM_ID = #{programId} ORDER BY CHANNEL_NO
	</select>

    <select id="getTempFrameCountWithChannelNoCondition" resultType="long">
		SELECT COUNT(FRAME_INDEX) FROM  MI_CDS_INFO_FRAME_TEMP
		WHERE PROGRAM_ID = #{programId} AND CHANNEL_NO = #{channelNo} AND SESSION_ID = #{sessionId}
	</select>

    <delete id="deleteFromMiCdsInfoChannelTmpWithSessionId">
    	DELETE FROM MI_CDS_INFO_CHANNEL_TEMP WHERE SESSION_ID = #{sessionId}
    </delete>

    <delete id="deleteFromMiCdsInfoChannelTmpWithProgramId">
    	DELETE FROM MI_CDS_INFO_CHANNEL_TEMP WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="deleteAllChannelsTmp">
    	DELETE FROM MI_CDS_INFO_CHANNEL_TEMP
    </delete>

    <select id="getDeviceTypeVersionByProgramId" resultType="Float">
    	SELECT DEVICE_TYPE_VERSION FROM MI_CDS_INFO_PROGRAM WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getProgramListBySchOrgId" resultType="com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity">
        SELECT B.* FROM MI_CDS_MAP_PROGRAM_GROUP AS A, MI_CDS_INFO_PROGRAM AS B
        WHERE A.PROGRAM_ID = B.PROGRAM_ID AND B.IS_DEFAULT='N'
        <if test="tempGroupList.size() > 0">
            AND A.GROUP_ID IN
            <foreach item="programGroup" collection="tempGroupList" open="(" close=")" separator=",">
                #{programGroup.group_id}
            </foreach>
        </if>
    </select>

    <delete id="deleteFrameByChannelNo">
		DELETE FROM MI_CDS_INFO_FRAME 
		WHERE PROGRAM_ID = #{programId} AND CHANNEL_NO = #{channelNo}
	</delete>

    <delete id="deleteScheduleByChannelNo">
		DELETE FROM MI_CDS_INFO_SCHEDULE WHERE PROGRAM_ID = #{programId} AND CHANNEL_NO = #{channelNo}
	</delete>

    <select id="getEventList" resultType="com.samsung.magicinfo.framework.scheduler.entity.EventEntity">
        SELECT DISTINCT A.EVENT_ID as event_id, A.EVENT_NAME, A.DESCRIPTION, A.MODIFY_DATE, A.DATALINK_ENABLE
        FROM MI_EVENT_INFO_EVENT AS A LEFT JOIN MI_EVENT_MAP_SCHEDULE_EVENT AS B
        ON A.EVENT_ID = B.EVENT_ID
        WHERE B.SCHEDULE_ID = #{scheduleId}
    </select>
    
    <select id="getEventListPaged"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.EventEntity">
        SELECT DISTINCT A.EVENT_ID as event_id, A.EVENT_NAME, A.DESCRIPTION, A.CREATE_DATE, A.DATALINK_ENABLE
        FROM
            MI_EVENT_INFO_EVENT AS A LEFT JOIN MI_EVENT_MAP_SCHEDULE_EVENT AS B
        ON
            A.EVENT_ID = B.EVENT_ID
        WHERE
            B.SCHEDULE_ID = #{scheduleId}
        <if test="sort != null and sort != '' and direction != null and direction != ''">
        	<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(direction)" />
            <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(sort)" />
            ORDER BY ${safe_sortColumn} ${safe_sortOrder}
        </if>
        LIMIT #{pageSize} OFFSET #{startPos}
    </select>
    
	<select id="getEventListPaged"
            resultType="com.samsung.magicinfo.framework.scheduler.entity.EventEntity" databaseId="mssql">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        SELECT * FROM
        (
        	SELECT DISTINCT A.EVENT_ID as event_id, A.EVENT_NAME, A.DESCRIPTION, A.CREATE_DATE, A.DATALINK_ENABLE, 
        	ROW_NUMBER() OVER(ORDER BY 
        		<choose>
        			<when test="sort != null and sort != '' and direction != null and direction != ''">
        			<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(direction)" />
                    <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(sort)" />
        			${safe_sortColumn} ${safe_sortOrder}
        			</when>
        			<otherwise>A.EVENT_ID ASC</otherwise>
        		</choose>
        	) as rownum
        	FROM
            	MI_EVENT_INFO_EVENT AS A LEFT JOIN MI_EVENT_MAP_SCHEDULE_EVENT AS B
        	ON
            	A.EVENT_ID = B.EVENT_ID
        	WHERE
            	B.SCHEDULE_ID = #{scheduleId}        
        ) as SubQuery
        WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit}
        ORDER BY rownum
    </select>

    <select id="getEventCountByScheduleId" resultType="java.lang.Integer">
        SELECT COUNT(A.EVENT_ID)
        FROM MI_EVENT_INFO_EVENT AS A LEFT
        JOIN MI_EVENT_MAP_SCHEDULE_EVENT AS B
        ON A.EVENT_ID = B.EVENT_ID
        WHERE B.SCHEDULE_ID = #{scheduleId}
    </select>
    
	<select id="getDownloadContentPagedListForEventSchedule"
    	resultType="com.samsung.magicinfo.framework.scheduler.entity.DownloadContentEntity">
        SELECT * FROM (
        SELECT
        <include refid="rownumSql" />
        A.CONTENT_ID, A.CONTENT_NAME, B.TOTAL_SIZE , C.PROGRESS, (CASE WHEN B.CONTENT_ID IS NOT NULL THEN 'COMPLETE' ELSE 'READY' END) AS STATUS 
		<include refid="getDownloadContentPagedListForEventScheduleFromWhere"/>
        ) as DCPLFES
		<include refid="downloadContentPagedLimitClause" />
    </select>

    <sql id="downloadContentPagedLimitClause">
        <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
        <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
        ORDER BY ${safe_sortColumn} ${safe_sortOrder}
        LIMIT #{map.limit} OFFSET #{map.offset}
    </sql>

    <sql id="downloadContentPagedLimitClause" databaseId="mssql">
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset + map.limit)" />
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.offset)" />
        WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit}
    </sql>

    <sql id="rownumSql" databaseId="mssql">
        <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sortColumn)" />
        <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.sortOrder)" />
        ROW_NUMBER() OVER (ORDER BY ${safe_sortColumn} ${safe_sortOrder}) as rownum, 
    </sql>

    <sql id="rownumSql">
        <!-- intentionally empty -->
    </sql>


    <select id="getDownloadContentPagedListCountForEventSchedule" resultType="java.lang.Integer">
        SELECT
        COUNT(A.CONTENT_ID)
        <include refid="getDownloadContentPagedListForEventScheduleFromWhere"/>
    </select>
    
    <sql id="getDownloadContentPagedListForEventScheduleFromWhere">
		FROM MI_CMS_INFO_CONTENT A, MI_CMS_INFO_CONTENT_VERSION B, MI_CDS_DOWNLOAD_STATUS C 
		WHERE A.CONTENT_ID = B.CONTENT_ID AND B.IS_ACTIVE='Y' AND A.CONTENT_ID = C.CONTENT_ID  AND C.CONTENT_ID = #{map.content_id} AND C.ACTIVE_TYPE = 'event' AND C.DEVICE_ID = #{map.device_id}
    </sql>
	
	<update id="modifyProgramDeviceTypeAndVersion">
		UPDATE MI_CDS_INFO_PROGRAM SET DEVICE_TYPE = #{deviceType}, DEVICE_TYPE_VERSION = #{deviceTypeVersion}  WHERE PROGRAM_ID = #{programId}
    </update>
    
    <select id="getContentListInSchedule" resultType="String">
        SELECT CONTENT_ID FROM MI_CDS_INFO_SCHEDULE
                     WHERE CONTENT_TYPE != 'PLAYLIST' AND PROGRAM_ID = #{programId} 
                     UNION SELECT B.CONTENT_ID FROM MI_CDS_INFO_SCHEDULE A, MI_CMS_MAP_PLAYLIST_CONTENT B, MI_CMS_INFO_PLAYLIST_VERSION C
                           WHERE A.CONTENT_TYPE = 'PLAYLIST' AND
                                 A.CONTENT_ID = B.PLAYLIST_ID AND
                                 A.PROGRAM_ID = #{programId} AND
                                 C.PLAYLIST_ID = B.PLAYLIST_ID AND 
                                 C.VERSION_ID = B.VERSION_ID AND 
                                 C.IS_ACTIVE = 'Y'
    </select>
    
    
    <select id="getContentListInScheduleWithStopDate" resultType="String">
    	SELECT CONTENT_ID FROM MI_CDS_INFO_SCHEDULE
				WHERE CONTENT_TYPE != 'PLAYLIST' AND PROGRAM_ID = #{programId} <if test="stopDate != null"> AND to_date(STOP_DATE, 'yyyy-MM-dd') >= to_date(#{stopDate}, 'yyyy-MM-dd')</if> 
		UNION SELECT B.CONTENT_ID FROM MI_CDS_INFO_SCHEDULE A, MI_CMS_MAP_PLAYLIST_CONTENT B, MI_CMS_INFO_PLAYLIST_VERSION C
				WHERE A.CONTENT_TYPE = 'PLAYLIST' AND A.CONTENT_ID = B.PLAYLIST_ID AND A.PROGRAM_ID = #{programId} 
				AND C. PLAYLIST_ID = B.PLAYLIST_ID AND C.VERSION_ID = B.VERSION_ID AND C.IS_ACTIVE = 'Y' 
				<if test="stopDate != null">
					AND (EXPIRED_DATE IS NULL OR EXPIRED_DATE >= to_date(#{stopDate}, 'yyyy-MM-dd'))
					AND to_date(A.STOP_DATE, 'yyyy-MM-dd') >= to_date(#{stopDate}, 'yyyy-MM-dd')
				</if>
		UNION SELECT DISTINCT CONTENT_ID
		FROM MI_CMS_INFO_PLAYLIST_VERSION VERSIONS, MI_CMS_MAP_PLAYLIST_CONTENT CONTENTS
        WHERE VERSIONS.IS_ACTIVE = 'Y' AND CONTENTS.PLAYLIST_ID = VERSIONS.PLAYLIST_ID AND CONTENTS.VERSION_ID = VERSIONS.VERSION_ID
        <if test="stopDate != null">
            AND (EXPIRED_DATE IS NULL OR EXPIRED_DATE >= to_date(#{stopDate}, 'yyyy-MM-dd'))
        </if>
        AND VERSIONS.PLAYLIST_ID IN (
			SELECT B.CONTENT_ID FROM MI_CDS_INFO_SCHEDULE A, MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST B, MI_CMS_INFO_PLAYLIST_VERSION C, MI_CMS_INFO_PLAYLIST D
			WHERE A.CONTENT_TYPE = 'PLAYLIST' AND A.CONTENT_ID = B.PLAYLIST_ID AND A.PROGRAM_ID = #{programId} 
			AND C.PLAYLIST_ID = B.PLAYLIST_ID AND  C.VERSION_ID = B.VERSION_ID AND C.IS_ACTIVE = 'Y' AND D.PLAYLIST_ID = C.PLAYLIST_ID
			<if test="stopDate != null">
				AND (EXPIRED_DATE IS NULL OR EXPIRED_DATE >= to_date(#{stopDate}, 'yyyy-MM-dd'))
				AND to_date(A.STOP_DATE, 'yyyy-MM-dd') >= to_date(#{stopDate}, 'yyyy-MM-dd')
			</if>)
	</select>
    
    <select id="getContentListInScheduleWithStopDate" resultType="String" databaseId="mssql">
		SELECT CONTENT_ID FROM MI_CDS_INFO_SCHEDULE
			WHERE CONTENT_TYPE != 'PLAYLIST' AND PROGRAM_ID = #{programId} <if test="stopDate != null"> AND CAST(STOP_DATE AS DATE) >= CAST(#{stopDate} AS DATE)</if>
		UNION SELECT B.CONTENT_ID FROM MI_CDS_INFO_SCHEDULE A, MI_CMS_MAP_PLAYLIST_CONTENT B, MI_CMS_INFO_PLAYLIST_VERSION C
			WHERE A.CONTENT_TYPE = 'PLAYLIST' AND A.CONTENT_ID = B.PLAYLIST_ID AND A.PROGRAM_ID = #{programId} 
			AND C. PLAYLIST_ID = B.PLAYLIST_ID AND C.VERSION_ID = B.VERSION_ID AND C.IS_ACTIVE = 'Y' 
			<if test="stopDate != null">
				AND (EXPIRED_DATE IS NULL OR EXPIRED_DATE >= CAST(#{stopDate} AS DATE))
				AND CAST(A.STOP_DATE AS DATE) >= CAST(#{stopDate} AS DATE)
			</if>
		UNION SELECT DISTINCT CONTENT_ID FROM MI_CMS_INFO_PLAYLIST_VERSION VERSIONS, MI_CMS_MAP_PLAYLIST_CONTENT CONTENTS
			WHERE VERSIONS.IS_ACTIVE = 'Y' AND CONTENTS.PLAYLIST_ID = VERSIONS.PLAYLIST_ID AND CONTENTS.VERSION_ID = VERSIONS.VERSION_ID AND
			VERSIONS.PLAYLIST_ID IN (
				SELECT B.CONTENT_ID FROM MI_CDS_INFO_SCHEDULE A, MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST B, MI_CMS_INFO_PLAYLIST_VERSION C, MI_CMS_INFO_PLAYLIST D
				WHERE A.CONTENT_TYPE = 'PLAYLIST' AND A.CONTENT_ID = B.PLAYLIST_ID AND A.PROGRAM_ID = #{programId}
				AND C.PLAYLIST_ID = B.PLAYLIST_ID AND  C.VERSION_ID = B.VERSION_ID AND C.IS_ACTIVE = 'Y' AND D.PLAYLIST_ID = C.PLAYLIST_ID
				<if test="stopDate != null">
					AND (EXPIRED_DATE IS NULL OR EXPIRED_DATE >= CAST(#{stopDate} AS DATE))
					AND CAST(A.STOP_DATE AS DATE) >= CAST(#{stopDate} AS DATE)
				</if>)
    </select>
    
    <select id="getContentListInADScheduleWithStopDate" resultType="String">
    	SELECT DISTINCT CONTENTS.CONTENT_ID
		FROM MI_CDS_INFO_ADSCHEDULE SCHEDULES
		LEFT JOIN MI_CMS_INFO_PLAYLIST_VERSION PLAYLISTS ON SCHEDULES.CONTENT_ID = PLAYLISTS.PLAYLIST_ID
		LEFT JOIN MI_CMS_MAP_PLAYLIST_CONTENT CONTENTS ON PLAYLISTS.VERSION_ID = CONTENTS.VERSION_ID AND CONTENTS.PLAYLIST_ID = PLAYLISTS.PLAYLIST_ID
		WHERE SCHEDULES.PROGRAM_ID = #{programId} AND PLAYLISTS.IS_ACTIVE = 'Y' <if test="stopDate != null">AND (EXPIRED_DATE IS NULL OR EXPIRED_DATE >= to_date(#{stopDate}, 'yyyy-MM-dd'))</if>
	</select>
	
	<select id="getContentListInADScheduleWithStopDate" resultType="String" databaseId="mssql">
    	SELECT DISTINCT CONTENTS.CONTENT_ID
		FROM MI_CDS_INFO_ADSCHEDULE SCHEDULES
		LEFT JOIN MI_CMS_INFO_PLAYLIST_VERSION PLAYLISTS ON SCHEDULES.CONTENT_ID = PLAYLISTS.PLAYLIST_ID
		LEFT JOIN MI_CMS_MAP_PLAYLIST_CONTENT CONTENTS ON PLAYLISTS.VERSION_ID = CONTENTS.VERSION_ID AND CONTENTS.PLAYLIST_ID = PLAYLISTS.PLAYLIST_ID
		WHERE SCHEDULES.PROGRAM_ID = #{programId} AND PLAYLISTS.IS_ACTIVE = 'Y' <if test="stopDate != null">AND (EXPIRED_DATE IS NULL OR EXPIRED_DATE >= CAST(#{stopDate} AS DATE))</if>
	</select>
    
	<insert id="addContentPublishData">
        INSERT INTO MI_CDS_DOWNLOAD_STATUS_DETAIL (DEVICE_NAME, GROUP_NAME, GROUP_ID, CONTENT_ID, DEVICE_ID, PROGRAM_ID)
            VALUES (#{entity.device_name}, #{entity.group_name}, #{entity.group_id}, #{entity.content_id}, #{entity.device_id}, #{entity.program_id})
    </insert>
    
	<delete id="deleteContentPublishData">
       DELETE FROM MI_CDS_DOWNLOAD_STATUS_DETAIL
       WHERE PROGRAM_ID = #{programId}
   </delete>
   
   	<select id="getContentPublishDataCount" resultType="int">
   		SELECT COUNT(PROGRAM_ID) FROM MI_CDS_DOWNLOAD_STATUS_DETAIL WHERE PROGRAM_ID = #{entity.program_id} AND DEVICE_ID = #{entity.device_id} AND CONTENT_ID = #{entity.content_id}
    </select>
    
    <update id="updateContentPublishDataProgress">
		UPDATE MI_CDS_DOWNLOAD_STATUS_DETAIL SET PROGRESS = #{entity.progress} WHERE PROGRAM_ID = #{entity.program_id} AND DEVICE_ID = #{entity.device_id} AND CONTENT_ID = #{entity.content_id}
    </update>
    
    <select id="getSchedulePublishStatusList"
           resultType="com.samsung.magicinfo.framework.scheduler.entity.DetailDownloadContentEntity">
		SELECT DEVICE_NAME, A.DEVICE_ID, STATUS FROM MI_DMS_INFO_DEVICE A, MI_CDS_MAP_PROGRAM_DEVICE_STATUS B WHERE A.DEVICE_ID IN (SELECT DEVICE_ID FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = #{device_group_id}) AND A.DEVICE_ID = B.DEVICE_ID AND B.PROGRAM_ID = #{programId}  ORDER BY DEVICE_ID 
    </select>
    
       
   	<select id="getCheckContentPublishCount" resultType="int">
   		SELECT COUNT(PROGRAM_ID) FROM MI_CDS_DOWNLOAD_STATUS_DETAIL WHERE PROGRESS IS NOT NULL AND PROGRAM_ID = #{programId}
    </select>
    
    <insert id="addDynaminTagInfoTemp">
        INSERT INTO MI_TAG_MAP_DYNAMIC_TAG_TEMP (SESSION_ID, SCHEDULE_ID, PLAYLIST_ID, SYNC_PLAY_ID, TAG_ID, MATCH_TYPE)
            VALUES (#{entity.session_id}, #{entity.schedule_id}, #{entity.playlist_id}, #{entity.sync_play_id}, #{entity.tag_id}, #{entity.match_type})
    </insert>
    
    <insert id="addDynaminTagInfo">
        INSERT INTO MI_TAG_MAP_DYNAMIC_TAG (SCHEDULE_ID, PLAYLIST_ID, SYNC_PLAY_ID, TAG_ID, MATCH_TYPE)
        SELECT SCHEDULE_ID, PLAYLIST_ID, SYNC_PLAY_ID, TAG_ID, MATCH_TYPE FROM MI_TAG_MAP_DYNAMIC_TAG_TEMP WHERE SCHEDULE_ID = #{scheduleId} AND SESSION_ID = #{sessionId} ORDER BY SYNC_PLAY_ID ASC
    </insert>
    
    <insert id="addDynaminTagInfoList">
    	INSERT INTO MI_TAG_MAP_DYNAMIC_TAG (SCHEDULE_ID, PLAYLIST_ID, SYNC_PLAY_ID, TAG_ID, MATCH_TYPE)
            VALUES (#{entity.schedule_id}, #{entity.playlist_id}, #{entity.sync_play_id}, #{entity.tag_id}, #{entity.match_type})
    </insert>
    
    <insert id="transferToDynaminTagInfoTemp">
        INSERT INTO MI_TAG_MAP_DYNAMIC_TAG_TEMP (SESSION_ID, SCHEDULE_ID, PLAYLIST_ID, SYNC_PLAY_ID, TAG_ID, MATCH_TYPE)
        SELECT #{sessionId}, SCHEDULE_ID, PLAYLIST_ID, SYNC_PLAY_ID, TAG_ID, MATCH_TYPE FROM MI_TAG_MAP_DYNAMIC_TAG WHERE SCHEDULE_ID = #{scheduleId} ORDER BY SYNC_PLAY_ID ASC
    </insert>
    
    <insert id="transferToDynaminTagInfoTempWithNewId">
        INSERT INTO MI_TAG_MAP_DYNAMIC_TAG_TEMP (SESSION_ID, SCHEDULE_ID, PLAYLIST_ID, SYNC_PLAY_ID, TAG_ID, MATCH_TYPE)
        SELECT #{sessionId}, #{newScheduleId}, PLAYLIST_ID, SYNC_PLAY_ID, TAG_ID, MATCH_TYPE FROM MI_TAG_MAP_DYNAMIC_TAG WHERE SCHEDULE_ID = #{scheduleId} ORDER BY SYNC_PLAY_ID ASC
    </insert>
    
    <delete id="deleteDynaminTagInfoTemp">
       DELETE FROM MI_TAG_MAP_DYNAMIC_TAG_TEMP WHERE SCHEDULE_ID = #{scheduleId}
    </delete>
    
    <delete id="deleteDynaminTagInfoTempWithSession">
       DELETE FROM MI_TAG_MAP_DYNAMIC_TAG_TEMP WHERE SESSION_ID = #{sessionId}
    </delete>
   
    <delete id="deleteDynaminTagInfo">
       DELETE FROM MI_TAG_MAP_DYNAMIC_TAG WHERE SCHEDULE_ID = #{scheduleId}
    </delete>
    
    <select id="getDynaminTagInfo" resultType="com.samsung.magicinfo.framework.scheduler.entity.DynamicTagEntity">
   		SELECT * FROM MI_TAG_MAP_DYNAMIC_TAG WHERE SCHEDULE_ID = #{scheduleId} ORDER BY TAG_ID ASC
    </select>
    
    <select id="getDynaminTagInfoTemp" resultType="com.samsung.magicinfo.framework.scheduler.entity.DynamicTagEntity">
   		SELECT * FROM MI_TAG_MAP_DYNAMIC_TAG_TEMP WHERE SESSION_ID = #{sessionId} AND SCHEDULE_ID = #{scheduleId} ORDER BY SYNC_PLAY_ID, TAG_ID ASC
    </select>
    
    <select id="getTagListWithIsSync" resultType="int">
   		SELECT A.TAG_ID FROM MI_TAG_MAP_DYNAMIC_TAG A WHERE A.SCHEDULE_ID = #{scheduleId} AND
   		SYNC_PLAY_ID IN (SELECT B.SYNC_PLAY_ID FROM MI_CMS_MAP_SYNC_GROUP B WHERE B.PLAYLIST_ID = #{playlistId} AND B.VERSION_ID=#{versionId} AND B.IS_SYNC = 'Y')
    </select>
    
    <select id="getTagListForContent" resultType="int">
   		SELECT DISTINCT(TAG_ID) FROM MI_TAG_MAP_DYNAMIC_TAG WHERE SCHEDULE_ID = #{scheduleId} AND PLAYLIST_ID = #{playlistId} AND SYNC_PLAY_ID = #{syncPlayId}
    </select>
    
    <delete id="delete1_deleteProgramDataWithId">
        DELETE FROM MI_CDS_MAP_FRAME_USER
        WHERE PROGRAM_ID = #{programId}
    </delete>
    <delete id="delete2_deleteProgramDataWithId">
        DELETE FROM MI_CDS_INFO_FRAME
        WHERE PROGRAM_ID = #{programId}
    </delete>

    <delete id="delete3_deleteProgramDataWithId">
        DELETE FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{programId}
    </delete>
    
    <delete id="deleteAllChannelsWithId">
    	DELETE FROM MI_CDS_INFO_CHANNEL
    	WHERE PROGRAM_ID = #{programId}
    </delete>
    
    <select id="getScheduleContentCount" resultType="int">
		select count(program_id) from mi_cds_info_schedule where program_id = #{programId} and content_id = #{contentId}
	</select>
	
	<select id="getDeletedProgramIdList" resultType="map">
		SELECT A.PROGRAM_ID FROM MI_CDS_INFO_PROGRAM A, MI_CDS_MAP_PROGRAM_GROUP B, MI_CDS_INFO_PROGRAM_GROUP C WHERE A.DELETED = 'Y'
			AND A.PROGRAM_ID = B.PROGRAM_ID AND B.GROUP_ID = C.GROUP_ID
		<if test="organization != null and !organization.equals('ROOT')">
			AND C.GROUP_NAME = #{organization}
		</if>
	</select>
	
	<update id="updateProgramGroupId">
		UPDATE MI_CDS_MAP_PROGRAM_GROUP
		SET GROUP_ID = #{groupId}
		WHERE PROGRAM_ID = #{programId}
	</update>
	
	<update id="updateProgramView">
		UPDATE MI_CDS_INFO_PROGRAM
        SET PROGRAM_NAME = #{program.program_name}, DESCRIPTION = #{program.description}, MODIFY_DATE = <include refid="utils.currentTimestamp"/>
        WHERE PROGRAM_ID = #{program.program_id}
	</update>
		
	<select id="getScheduleMappedContentTotalSize" resultType="java.util.Map">
		SELECT B.TOTAL_SIZE
		FROM MI_CDS_INFO_SCHEDULE A, MI_CMS_INFO_CONTENT_VERSION B
		WHERE A.SCHEDULE_TYPE = #{contentScheduleType} AND A.CONTENT_ID = B.CONTENT_ID AND B.IS_ACTIVE = 'Y' AND A.PROGRAM_ID = #{programId} 
		UNION
		SELECT B.TOTAL_SIZE
		FROM MI_CDS_INFO_SCHEDULE A, MI_CMS_INFO_PLAYLIST_VERSION B
		WHERE A.SCHEDULE_TYPE = #{contentScheduleType} AND A.CONTENT_ID = B.PLAYLIST_ID AND B.IS_ACTIVE = 'Y' AND A.PROGRAM_ID = #{programId} 
	</select>
	
	<select id="getTemplateEntity" resultType="com.samsung.magicinfo.framework.scheduler.entity.FrameTemplateEntity">
		SELECT * FROM MI_CDS_INFO_FRAME_TEMPLATE
		WHERE TEMPLATE_ID = #{templateId}
	</select>
	
	<select id="getAdSlotList" resultType="com.samsung.magicinfo.framework.scheduler.entity.AdSlotEntity">
		SELECT * FROM MI_CDS_INFO_ADSLOT
		WHERE PROGRAM_ID = #{programId} AND FRAME_ID = #{frameId}
		ORDER BY SLOT_INDEX ASC
	</select>
	
	<select id="getAdSlotListFromProgramId" resultType="com.samsung.magicinfo.framework.scheduler.entity.AdSlotEntity">
		SELECT * FROM MI_CDS_INFO_ADSLOT
		WHERE PROGRAM_ID = #{programId}
		ORDER BY SLOT_INDEX ASC
	</select> 
	
	<select id="getAdScheduleList" resultType="com.samsung.magicinfo.framework.scheduler.entity.AdScheduleEntity">
		SELECT * FROM MI_CDS_INFO_ADSCHEDULE
		WHERE PROGRAM_ID = #{programId} AND SLOT_ID = #{slotId}	
	</select>
	
	<select id="getContentName" resultType="java.util.Map">
		SELECT CONTENT_NAME FROM MI_CMS_INFO_CONTENT WHERE CONTENT_ID = #{contentId}
	</select>
	
	<select id="getReserveScheduleList" resultType="com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity">
		SELECT * FROM MI_CDS_INFO_PROGRAM WHERE DEPLOY_TIME IS NOT NULL AND DEPLOY_TIME != 'null' AND DEPLOY_TIME != '' AND DELETED = 'N'
	</select>
	
	<select id="getProgramIdListForVWL" resultType="java.util.Map">
		SELECT DISTINCT ON(PROGRAM_ID) PROGRAM_ID FROM MI_CDS_INFO_SCHEDULE WHERE PLAYER_MODE = 'vwl'
	</select>
	
	<select id="getProgramIdListForVWL" resultType="java.util.Map" databaseId="mssql">
		SELECT DISTINCT PROGRAM_ID FROM MI_CDS_INFO_SCHEDULE WHERE PLAYER_MODE = 'vwl'
	</select>
	
	<update id="updateProgramTypeFromProgramType">
    	UPDATE MI_CDS_INFO_PROGRAM
    	SET PROGRAM_TYPE = #{toProgramType}
    	WHERE PROGRAM_TYPE = #{fromProgramType}
    </update>
    
    <update id="updateProgramTypeFromProgramId">
    	UPDATE MI_CDS_INFO_PROGRAM
    	SET PROGRAM_TYPE = #{programType}
    	WHERE PROGRAM_ID = #{programId}
    </update>
    
    <update id="updateProgramTypeFromSyncPlay">
    	UPDATE MI_CDS_INFO_PROGRAM
    	SET PROGRAM_TYPE = #{programType}
    	WHERE USE_SYNC_PLAY = 'Y'
    </update>
    
    <update id="updateProgramTypeFromAdvertisement">
    	UPDATE MI_CDS_INFO_PROGRAM
    	SET PROGRAM_TYPE = #{programType}
    	WHERE USE_AD_SCHEDULE = 'Y'
    </update>
    
    
    <select id="getCountProgram" resultType="int">
		<if test="programType != null and !programType.equals('')">
			<if test="programType.equals('VWL')">
				SELECT COUNT(PROGRAM_LIST.PROGRAM_ID)
				FROM MI_CDS_INFO_PROGRAM AS PROGRAM_LIST
				LEFT JOIN (SELECT DISTINCT ON(PROGRAM_ID) PROGRAM_ID FROM MI_CDS_INFO_SCHEDULE WHERE PLAYER_MODE = 'vwl') AS OLD_PROGRAM ON PROGRAM_LIST.PROGRAM_ID = OLD_PROGRAM.PROGRAM_ID
				WHERE PROGRAM_LIST.PROGRAM_TYPE = 'LFD' AND OLD_PROGRAM.PROGRAM_ID IS NOT NULL	
			</if>
			<if test="programType.equals('SYNC')">
				SELECT COUNT(PROGRAM_ID)
				FROM (SELECT PROGRAM_ID, PROGRAM_TYPE FROM MI_CDS_INFO_PROGRAM WHERE USE_SYNC_PLAY = 'Y') AS OLD_PROGRAM
				WHERE PROGRAM_TYPE = 'LFD'
			</if>
			<if test="programType.equals('ADV')">
				SELECT COUNT(PROGRAM_ID)
				FROM (SELECT PROGRAM_ID, PROGRAM_TYPE FROM MI_CDS_INFO_PROGRAM WHERE USE_AD_SCHEDULE = 'Y') AS OLD_PROGRAM
				WHERE PROGRAM_TYPE = 'LFD'
			</if>
        </if>
	</select>
	
	<select id="getCountProgram" resultType="int" databaseId="mssql">
		<if test="programType != null and !programType.equals('')">
			<if test="programType.equals('VWL')">
				SELECT COUNT(PROGRAM_LIST.PROGRAM_ID)
				FROM MI_CDS_INFO_PROGRAM AS PROGRAM_LIST
				LEFT JOIN (SELECT DISTINCT PROGRAM_ID FROM MI_CDS_INFO_SCHEDULE WHERE PLAYER_MODE = 'vwl') AS OLD_PROGRAM ON PROGRAM_LIST.PROGRAM_ID = OLD_PROGRAM.PROGRAM_ID
				WHERE PROGRAM_LIST.PROGRAM_TYPE = 'LFD' AND OLD_PROGRAM.PROGRAM_ID IS NOT NULL	
			</if>
			<if test="programType.equals('SYNC')">
				SELECT COUNT(PROGRAM_ID)
				FROM (SELECT PROGRAM_ID, PROGRAM_TYPE FROM MI_CDS_INFO_PROGRAM WHERE USE_SYNC_PLAY = 'Y') AS OLD_PROGRAM
				WHERE PROGRAM_TYPE = 'LFD'
			</if>
			<if test="programType.equals('ADV')">
				SELECT COUNT(PROGRAM_ID)
				FROM (SELECT PROGRAM_ID, PROGRAM_TYPE FROM MI_CDS_INFO_PROGRAM WHERE USE_AD_SCHEDULE = 'Y') AS OLD_PROGRAM
				WHERE PROGRAM_TYPE = 'LFD'	
			</if>
        </if>
	</select>
	
	<select id="getTagPlaylistIdVersion" resultType="map">
		SELECT DISTINCT PLAYLIST_VERSION.PLAYLIST_ID, PLAYLIST_VERSION.VERSION_ID
		FROM MI_CDS_INFO_SCHEDULE INFO_SCHEDULE
		LEFT JOIN MI_CMS_INFO_PLAYLIST_VERSION PLAYLIST_VERSION ON INFO_SCHEDULE.CONTENT_ID = PLAYLIST_VERSION.PLAYLIST_ID
		LEFT JOIN MI_CMS_MAP_PLAYLIST_TAG PLAYLIST_TAG ON PLAYLIST_VERSION.PLAYLIST_ID = PLAYLIST_TAG.PLAYLIST_ID
		WHERE INFO_SCHEDULE.PROGRAM_ID = #{programId} AND INFO_SCHEDULE.CONTENT_TYPE = #{contentType} AND PLAYLIST_VERSION.IS_ACTIVE = 'Y'
	</select>
	
	<select id="getCountProgramIdByGroupId" resultType="int">
		SELECT COUNT(PROGRAM_ID)
		FROM MI_CDS_MAP_PROGRAM_DEVICE
		WHERE DEVICE_GROUP_ID = #{groupId}
	</select>
	
	<select id="getDynamicTagByScheduleIdIdAndPlaylistId" resultType="map">
		SELECT *
		FROM MI_TAG_MAP_DYNAMIC_TAG
		WHERE SCHEDULE_ID = #{scheduleId} AND PLAYLIST_ID = #{playlistId}
	</select>
	
	<select id="getOrganiationByProgramId" resultType="String">
		SELECT ORGANIZATION 
		FROM MI_USER_INFO_USER
		WHERE USER_ID = (
		      SELECT USER_ID FROM MI_CDS_INFO_PROGRAM WHERE PROGRAM_ID = #{programId}
		      )
    </select>
    
    <select id="existsProgramId" resultType="java.lang.Long">
    	SELECT COUNT(PROGRAM_ID)
		FROM MI_CDS_INFO_PROGRAM
		WHERE PROGRAM_ID = #{programId}
    </select>

	<select id="getProgramWithGroupIdAndNameByProgramId" resultType="com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity">
		SELECT PROGRAMS.*, MAP_GROUP.GROUP_ID, GROUPS.GROUP_NAME
        FROM MI_CDS_INFO_PROGRAM PROGRAMS
        LEFT JOIN MI_CDS_MAP_PROGRAM_GROUP MAP_GROUP ON PROGRAMS.PROGRAM_ID = MAP_GROUP.PROGRAM_ID
        LEFT JOIN MI_CDS_INFO_PROGRAM_GROUP GROUPS ON MAP_GROUP.GROUP_ID = GROUPS.GROUP_ID
        WHERE PROGRAMS.PROGRAM_ID = #{programId}
	</select>
	
	<select id="getDeviceListByScheduleToExpire" resultType="Map">
		SELECT 
			DV.device_id,  DV.device_name, DV.ip_address, DV.device_model_name, DV.device_type_version, DV.device_type, 
			DV_GRP.group_name AS device_group_name, PR_DV_MAP.device_group_id, 
			MAX(SCHEDULES.STOP_DATE) AS STOP_DATE, PROGRAMS.program_id, PROGRAMS.program_name, PROGRAMS.program_type 
		<include refid="getDeviceScheduleToExpire_from"/>
		
		AND PR_DV_MAP.device_group_id is not null
    	AND DV.is_child is false 

		GROUP BY PROGRAMS.program_id, PROGRAMS.program_name, PROGRAMS.program_type, PROGRAMS.version, PR_DV_MAP.device_group_id, 
				 DV.device_id, DV.device_model_name,   DV.device_name, DV_GRP.group_name, DV.ip_address		
		<if test="condition != null">
			<if test="condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
				<bind name="safe_sortUpper"	value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
				<bind name="safe_sortOrder"	value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
				ORDER BY ${safe_sortUpper} ${safe_sortOrder}
			</if>
		</if>
		<if test="pageSize != -1">
        	LIMIT #{pageSize}
        </if>
        <if test="startPos != -1">
        	OFFSET #{startPos}
        </if>
	</select>

	<select id="getDeviceListByScheduleToExpire" resultType="Map" databaseId="mssql">
		<include refid="groupRecursiveQuery"/>
		SELECT * FROM (
    		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        	<bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        	<bind name="safe_sortUpper"	value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
			<bind name="safe_sortOrder"	value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
			SELECT *, ROW_NUMBER() OVER(ORDER BY ${safe_sortUpper} ${safe_sortOrder}) as RowNum FROM (
				SELECT 
					MAX(SCHEDULES.STOP_DATE) AS STOP_DATE,
					DV.DEVICE_ID, DV.DEVICE_NAME, DV.IP_ADDRESS, DV.DEVICE_MODEL_NAME, DV.DEVICE_TYPE_VERSION, DV.DEVICE_TYPE, DV_GRP.GROUP_NAME AS DEVICE_GROUP_NAME, 
					PR_DV_MAP.DEVICE_GROUP_ID, PROGRAMS.PROGRAM_ID, PROGRAMS.PROGRAM_NAME, PROGRAMS.PROGRAM_TYPE
					<include refid="getDeviceScheduleToExpire_from"/>
					AND PR_DV_MAP.DEVICE_GROUP_ID IS NOT NULL
					AND DV.IS_CHILD = 0
				GROUP BY DV.DEVICE_ID, DV.DEVICE_NAME, DV.IP_ADDRESS, DV.DEVICE_MODEL_NAME, DV.DEVICE_TYPE_VERSION, DV.DEVICE_TYPE, DV_GRP.GROUP_NAME, 
					PR_DV_MAP.DEVICE_GROUP_ID, PROGRAMS.PROGRAM_ID, PROGRAMS.PROGRAM_NAME, PROGRAMS.PROGRAM_TYPE
			) as SubQuery
		) as SubQuery2
        WHERE
            1 = 1
            <if test="safe_startPos > -1">
            and RowNum > ${safe_startPos}
            </if>
            <if test="safe_rownumLimit > -1">
            and RowNum &lt;= ${safe_rownumLimit}
            </if>        
        ORDER BY RowNum
	</select>

	<!-- SF[00216289]-->
	<select id="getListScheduleToExpire" resultType="com.samsung.magicinfo.framework.scheduler.entity.ScheduleAdminEntity">
		SELECT  
			array_to_string(array_agg(DISTINCT PR_DV_MAP.device_group_id), ',') AS device_group_ids, 
			array_to_string(array_agg(DISTINCT DV_GRP.group_name), ',') AS device_group_name,
			MAX(SCHEDULES.STOP_DATE) AS STOP_DATE, PROGRAMS.program_id, PROGRAMS.program_name, PROGRAMS.program_type, PROGRAMS.version, PROGRAMS.modify_date, PROGRAMS.device_type, PROGRAMS.device_type_version, PROGRAMS.create_date
		<include refid="getCountScheduleToExpire_from"/>
		GROUP BY PROGRAMS.program_id, PROGRAMS.program_name, PROGRAMS.program_type, PROGRAMS.version
		<if test="condition != null">
			<if test="condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
				<bind name="safe_sortUpper"	value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
				<bind name="safe_sortOrder"	value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
				ORDER BY
                    <choose>
                        <when test="safe_sortUpper != null and safe_sortUpper.equals('CREATE_DATE')">PROGRAMS.CREATE_DATE</when>
                        <otherwise>${safe_sortUpper}</otherwise>
                    </choose>
				    ${safe_sortOrder}
			</if>
		</if>
		<if test="pageSize != -1">
        	LIMIT #{pageSize}
        </if>
        <if test="startPos != -1">
        	OFFSET #{startPos}
        </if>
	</select>
	
	<!-- SF[00216289]-->
	<select id="getListScheduleToExpire" resultType="com.samsung.magicinfo.framework.scheduler.entity.ScheduleAdminEntity" databaseId="mssql" >
		<include refid="groupRecursiveQuery"/>
		SELECT * FROM (
    		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        	<bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        	<bind name="safe_sortUpper"	value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
			<bind name="safe_sortOrder"	value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
        	SELECT   
				MAX(SCHEDULES.STOP_DATE) AS STOP_DATE, PROGRAMS.program_id, PROGRAMS.program_name, PROGRAMS.program_type, PROGRAMS.version, PROGRAMS.modify_date, PROGRAMS.device_type, PROGRAMS.create_date, PROGRAMS.device_type_version,
				DENSE_RANK() OVER(ORDER BY  
				<choose>
					<when test="safe_sortUpper != null and safe_sortUpper.equals('STOP_DATE')">SCHEDULES.STOP_DATE</when>
					<otherwise> PROGRAMS.${safe_sortUpper}</otherwise> 
				</choose>
				 ${safe_sortOrder} , PROGRAMS.program_id ) as RowNum
        	<include refid="getCountScheduleToExpire_from"/>
        	GROUP BY PROGRAMS.CREATE_DATE, STOP_DATE, PROGRAMS.program_id, PROGRAMS.program_name, PROGRAMS.program_type, PROGRAMS.version, PROGRAMS.modify_date, PROGRAMS.device_type, PROGRAMS.device_type_version
		)as SubQuery      
        WHERE
            1 = 1
            <if test="safe_startPos > -1">
            and RowNum > ${safe_startPos}
            </if>
            <if test="safe_rownumLimit > -1">
            and RowNum &lt;= ${safe_rownumLimit}
            </if>        
        ORDER BY RowNum
	</select>
	
	<select id="getCountScheduleToExpire" resultType="Integer">
		SELECT COUNT(DISTINCT PROGRAMS.PROGRAM_ID)
		<include refid="getCountScheduleToExpire_from"/>
	</select>
	
	<select id="getDeviceCountByScheduleToExpire" resultType="Integer">
		SELECT COUNT(RESULT) 
		FROM
			(SELECT DV.device_id
				<include refid="getCountScheduleToExpire_from"/>	
				AND PR_DV_MAP.device_group_id is not null
				AND DV.is_child = <include refid="utils.false"/>
		
			GROUP BY PROGRAMS.program_id, PROGRAMS.program_name, PROGRAMS.program_type, PROGRAMS.version, PR_DV_MAP.device_group_id, 
					 DV.device_id, DV.device_model_name, DV.device_name, DV_GRP.group_name, DV.ip_address
			) AS RESULT
	</select>	
	
	<select id="getDeviceCountByScheduleToExpire" resultType="Integer" databaseId="mssql">
		<include refid="groupRecursiveQuery"/>
		SELECT COUNT(*) 
		FROM
			(SELECT DV.device_id
				<include refid="getCountScheduleToExpire_from"/>	
				AND PR_DV_MAP.device_group_id is not null
				AND DV.is_child = <include refid="utils.false"/>
		
			GROUP BY PROGRAMS.program_id, PROGRAMS.program_name, PROGRAMS.program_type, PROGRAMS.version, PR_DV_MAP.device_group_id, 
					 DV.device_id, DV.device_model_name, DV.device_name, DV_GRP.group_name, DV.ip_address
			) AS RESULT
	</select>	
	
	<select id="getCountScheduleToExpire" resultType="Integer" databaseId="mssql">
		<include refid="groupRecursiveQuery"/>
        SELECT COUNT(DISTINCT PROGRAMS.PROGRAM_ID)
        <include refid="getCountScheduleToExpire_from"/>
	</select>

    <select id="getMaxPriorityByProgramId" resultType="Long">
        SELECT MAX(PRIORITY)
		FROM MI_CDS_INFO_SCHEDULE WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getMinPriorityByProgramId" resultType="Long">
        SELECT MIN(PRIORITY)
        FROM MI_CDS_INFO_SCHEDULE WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getPriorityByScheduleId" resultType="Long">
        SELECT PRIORITY
        FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAM_ID = #{programId} AND SCHEDULE_ID = #{scheduleId}
    </select>

    <select id="getScheduleIdAndPriorityByProgramId" resultType="Map">
        SELECT SCHEDULE_ID, PRIORITY
        FROM MI_CDS_INFO_SCHEDULE WHERE PROGRAM_ID = #{programId}
        ORDER BY PRIORITY
    </select>

    <update id="updateSchedulePriorityByProgramId">
        UPDATE MI_CDS_INFO_SCHEDULE
        SET PRIORITY = #{priority}
        WHERE PROGRAM_ID = #{programId} AND SCHEDULE_ID = #{scheduleId}
    </update>

    <sql id="getCountScheduleToExpire_from">
		FROM MI_CDS_MAP_PROGRAM_GROUP MAP_PROGRAM
			INNER JOIN MI_CDS_INFO_PROGRAM PROGRAMS			ON MAP_PROGRAM.PROGRAM_ID = PROGRAMS.PROGRAM_ID
			INNER JOIN MI_CDS_INFO_SCHEDULE SCHEDULES		ON PROGRAMS.PROGRAM_ID = SCHEDULES.PROGRAM_ID
			LEFT JOIN MI_CDS_MAP_PROGRAM_DEVICE PR_DV_MAP	ON PR_DV_MAP.PROGRAM_ID = PROGRAMS.PROGRAM_ID
			LEFT JOIN MI_DMS_INFO_GROUP DV_GRP				ON DV_GRP.GROUP_ID = PR_DV_MAP.DEVICE_GROUP_ID  
			LEFT JOIN MI_DMS_MAP_GROUP_DEVICE DV_GRP_MAP 	ON PR_DV_MAP.DEVICE_GROUP_ID = DV_GRP_MAP.GROUP_ID
			LEFT JOIN MI_DMS_INFO_DEVICE DV					ON DV.DEVICE_ID = DV_GRP_MAP.DEVICE_ID
		WHERE MAP_PROGRAM.GROUP_ID IN (
			<include refid="groupRecursiveQuery"/>
			SELECT GROUP_ID
			FROM B
		)
		AND PROGRAMS.IS_DEFAULT = 'N' AND PROGRAMS.DELETED = 'N'
        AND SCHEDULES.STOP_DATE &lt;= CAST(#{stopDate} AS VARCHAR)
        AND NOT EXISTS ( SELECT 1
        FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAMS.PROGRAM_ID = PROGRAM_ID
        AND STOP_DATE  &lt;= CAST(#{stopDate} AS VARCHAR)
        AND SCHEDULES.schedule_id != schedule_id
        AND STOP_DATE &gt; SCHEDULES.STOP_DATE )
        <if test="condition != null">
			<if test="condition.commonSearchKeyword != null and !condition.commonSearchKeyword.equals('')">
				AND PROGRAMS.PROGRAM_NAME = #{condition.commonSearchKeyword}
			</if>
			<if test="condition.src_name != null and condition.src_name != ''">
	            <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
	            <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            	AND UPPER(PROGRAMS.PROGRAM_NAME) 
            	LIKE #{srcNamePattern} 
        	</if>
        </if>
    </sql>

    <sql id="getCountScheduleToExpire_from" databaseId="mssql">
        FROM MI_CDS_MAP_PROGRAM_GROUP MAP_PROGRAM
        INNER JOIN MI_CDS_INFO_PROGRAM 		PROGRAMS 		ON MAP_PROGRAM.PROGRAM_ID = PROGRAMS.PROGRAM_ID
        INNER JOIN MI_CDS_INFO_SCHEDULE 	SCHEDULES 		ON PROGRAMS.PROGRAM_ID = SCHEDULES.PROGRAM_ID
        LEFT JOIN MI_CDS_MAP_PROGRAM_DEVICE 	PR_DV_MAP 		ON PR_DV_MAP.PROGRAM_ID = PROGRAMS.PROGRAM_ID
        LEFT JOIN MI_DMS_INFO_GROUP 			DV_GRP 			ON DV_GRP.GROUP_ID = PR_DV_MAP.DEVICE_GROUP_ID
        LEFT JOIN MI_DMS_MAP_GROUP_DEVICE 		DV_GRP_MAP 	ON PR_DV_MAP.DEVICE_GROUP_ID = DV_GRP_MAP.GROUP_ID
        LEFT JOIN MI_DMS_INFO_DEVICE 			DV 			ON DV.DEVICE_ID = DV_GRP_MAP.DEVICE_ID
        WHERE MAP_PROGRAM.GROUP_ID IN ( SELECT GROUP_ID FROM B )
        AND PROGRAMS.IS_DEFAULT = 'N' AND PROGRAMS.DELETED = 'N'
        AND SCHEDULES.STOP_DATE  &lt;= CAST(#{stopDate} AS NVARCHAR)
        AND NOT EXISTS ( SELECT 1
        FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAMS.PROGRAM_ID = PROGRAM_ID
        AND STOP_DATE  &lt;= CAST(#{stopDate} AS NVARCHAR)
        AND SCHEDULES.schedule_id != schedule_id
        AND STOP_DATE &gt; SCHEDULES.STOP_DATE )
        <if test="condition != null">
            <if test="condition.commonSearchKeyword != null and !condition.commonSearchKeyword.equals('')">
                AND PROGRAMS.PROGRAM_NAME = #{condition.commonSearchKeyword}
            </if>
            <if test="condition.src_name != null and condition.src_name != ''">
                <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
                <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
                AND UPPER(PROGRAMS.PROGRAM_NAME)
                LIKE #{srcNamePattern}
            </if>
        </if>
    </sql>

    <sql id="groupRecursiveQuery">
        WITH RECURSIVE B AS (
        SELECT GROUP_ID
        FROM MI_CDS_INFO_PROGRAM_GROUP
        WHERE
        <choose>
            <when test="groupList != null">
                <foreach item="group" collection="groupList" open="(" separator=" OR " close=")">
                    GROUP_ID = #{group.group_id}
                </foreach>
            </when>
            <otherwise>
                GROUP_ID = 999999
            </otherwise>
        </choose>
        UNION ALL
        SELECT CHILD_GROUP.GROUP_ID
        FROM MI_CDS_INFO_PROGRAM_GROUP CHILD_GROUP
        JOIN B ON CHILD_GROUP.P_GROUP_ID = B.GROUP_ID
        )
    </sql>

    <sql id="groupRecursiveQuery" databaseId="mssql">
        WITH B AS (
        SELECT GROUP_ID
        FROM MI_CDS_INFO_PROGRAM_GROUP
        WHERE
        <choose>
            <when test="groupList != null">
                <foreach item="group" collection="groupList" open="(" separator=" OR " close=")">
                    GROUP_ID = #{group.group_id}
                </foreach>
            </when>
            <otherwise>
                GROUP_ID = 999999
            </otherwise>
        </choose>
        UNION ALL
        SELECT CHILD_GROUP.GROUP_ID
        FROM MI_CDS_INFO_PROGRAM_GROUP CHILD_GROUP
        JOIN B ON CHILD_GROUP.P_GROUP_ID = B.GROUP_ID
        )
    </sql>

    <!-- KDH [RQ190703-00340][19.10 RC] 컨텐츠 사용기간 추가(만료) S -->
    <select id="getProgramByExpiredContentId" resultType="java.util.Map">
        SELECT T2.TYPE
              ,T1.PROGRAM_ID
              ,T1.PROGRAM_NAME
              ,T2.CHANNEL_NO
              ,T2.SCREEN_INDEX
              ,T2.FRAME_ID
              ,T2.SCHEDULE_ID
          FROM MI_CDS_INFO_PROGRAM T1
               JOIN (SELECT 'program'      AS TYPE
                           ,T11.PROGRAM_ID AS PROGRAM_ID
                           ,-1             AS CHANNEL_NO
                           ,-1             AS SCREEN_INDEX
                           ,null           AS FRAME_ID
                           ,null           AS SCHEDULE_ID
                       FROM MI_CDS_INFO_PROGRAM T11
                      WHERE T11.BGM_CONTENT_ID = #{contentId}
                     UNION
                     SELECT 'frame'          AS TYPE
                           ,T21.PROGRAM_ID   AS PROGRAM_ID
                           ,T21.CHANNEL_NO   AS CHANNEL_NO
                           ,T21.SCREEN_INDEX AS SCREEN_INDEX
                           ,T21.FRAME_ID     AS FRAME_ID
                           ,''               AS SCHEDULE_ID
                       FROM MI_CDS_INFO_FRAME   T21
                      WHERE T21.DEFAULT_CONTENT_ID = #{contentId}
                     UNION
                     SELECT 'schedule'      AS TYPE
                           ,T31.PROGRAM_ID  AS PROGRAM_ID
                           ,-1              AS CHANNEL_NO
                           ,-1              AS SCREEN_INDEX
                           ,''              AS FRAME_ID
                           ,T31.SCHEDULE_ID AS SCHEDULE_ID
                       FROM MI_CDS_INFO_SCHEDULE T31
                      WHERE T31.CONTENT_ID    = #{contentId}
                        AND T31.CONTENT_TYPE != 'PLAYLIST'
                    ) T2
               ON T2.PROGRAM_ID = T1.PROGRAM_ID
         ORDER BY T1.PROGRAM_ID
    </select>

    <update id="deleteExpiredContentInProgram">
        UPDATE MI_CDS_INFO_PROGRAM
           SET BGM_CONTENT_ID = null
         WHERE PROGRAM_ID     = #{programId}
           AND BGM_CONTENT_ID = #{contentId}
    </update>

    <update id="deleteExpiredContentInFrame">
        UPDATE MI_CDS_INFO_FRAME
           SET DEFAULT_CONTENT_ID = null
         WHERE PROGRAM_ID         = #{programId}
           AND CHANNEL_NO         = #{channelNo}
           AND SCREEN_INDEX       = #{screenIndex}
           AND FRAME_ID           = #{frameId}
           AND DEFAULT_CONTENT_ID = #{contentId}
    </update>

    <delete id="deleteExpiredContentInSchedule">
        DELETE
          FROM MI_CDS_INFO_SCHEDULE
         WHERE SCHEDULE_ID = #{scheduleId}
           AND CONTENT_ID  = #{contentId}
    </delete>

    <delete id="deletePlaylistInSchedule">
        <choose>
            <when test="programType == 'ADV'">
                DELETE
                  FROM MI_CDS_INFO_ADSCHEDULE
                 WHERE SCHEDULE_ID = #{scheduleId}
                   AND CONTENT_ID  = #{playlistId}
            </when>
            <otherwise>
                DELETE
                  FROM MI_CDS_INFO_SCHEDULE
                 WHERE SCHEDULE_ID = #{scheduleId}
                   AND CONTENT_ID  = #{playlistId}
            </otherwise>
        </choose>
    </delete>

    <select id="getPlaylistByExpiredContentId" resultType="java.util.Map">
        SELECT DISTINCT(C.PLAYLIST_ID), C.PLAYLIST_NAME, 'playlist' as TYPE
          FROM MI_CMS_MAP_PLAYLIST_CONTENT  A
              ,MI_CMS_INFO_PLAYLIST_VERSION B
              ,MI_CMS_INFO_PLAYLIST         C
         WHERE A.CONTENT_ID  = #{contentId}
           AND A.PLAYLIST_ID = B.PLAYLIST_ID
           AND A.VERSION_ID  = B.VERSION_ID
           AND B.IS_ACTIVE   = 'Y'
           AND B.PLAYLIST_ID = C.PLAYLIST_ID
        UNION
        SELECT DISTINCT(D.PLAYLIST_ID), D.PLAYLIST_NAME, 'tag_playlist' as TYPE
          FROM MI_TAG_MAP_CONTENT           A /* 컨텐츠_태그 매핑 */
              ,MI_CMS_MAP_PLAYLIST_TAG      B /* 플레이리스트_태그 매핑 */
              ,MI_CMS_INFO_PLAYLIST_VERSION C /* 플레이리스트 이력 */
              ,MI_CMS_INFO_PLAYLIST         D /* 플레이리스트 기본 */
         WHERE A.CONTENT_ID  = #{contentId}
           AND B.TAG_ID      = A.TAG_ID
           AND C.PLAYLIST_ID = B.PLAYLIST_ID
           AND C.VERSION_ID  = B.VERSION_ID
           AND C.IS_ACTIVE   = 'Y'
           AND D.PLAYLIST_ID = C.PLAYLIST_ID
    </select>

    <select id="getContentListByProgramId" resultType="java.util.Map">
        SELECT T1.CONTENT_ID
              ,T1.CONTENT_NAME
          FROM MI_CMS_INFO_CONTENT T1
               JOIN (SELECT T11.BGM_CONTENT_ID AS CONTENT_ID
                       FROM MI_CDS_INFO_PROGRAM T11
                      WHERE T11.PROGRAM_ID = #{programId}
                     UNION
                     SELECT T12.DEFAULT_CONTENT_ID AS CONTENT_ID
                       FROM MI_CDS_INFO_FRAME T12
                      WHERE T12.PROGRAM_ID = #{programId}
                     UNION
                     SELECT T13.CONTENT_ID
                       FROM MI_CDS_INFO_SCHEDULE T13
                      WHERE T13.PROGRAM_ID    = #{programId}
                        AND T13.CONTENT_TYPE != 'PLAYLIST'
                    ) T2
               ON T2.CONTENT_ID = T1.CONTENT_ID
    </select>

    <select id="getScheduleByPlaylistId" resultType="java.util.Map">
        SELECT T0.PROGRAM_ID
              ,T0.PROGRAM_NAME
              ,T0.SCHEDULE_ID
              ,T0.PROGRAM_TYPE
          FROM (SELECT T1.PROGRAM_ID
                      ,T1.PROGRAM_NAME
                      ,T2.SCHEDULE_ID
                      ,T1.PROGRAM_TYPE
                  FROM MI_CDS_INFO_PROGRAM  T1
                      ,MI_CDS_INFO_SCHEDULE T2
                 WHERE T2.PROGRAM_ID   = T1.PROGRAM_ID
                   AND T2.CONTENT_ID   = #{playlistId}
                   AND T2.CONTENT_TYPE = 'PLAYLIST'
                UNION
                SELECT T1.PROGRAM_ID
                      ,T1.PROGRAM_NAME
                      ,T2.SCHEDULE_ID
                      ,T1.PROGRAM_TYPE
                  FROM MI_CDS_INFO_PROGRAM    T1
                      ,MI_CDS_INFO_ADSCHEDULE T2
                 WHERE T2.PROGRAM_ID   = T1.PROGRAM_ID
                   AND T2.CONTENT_ID   = #{playlistId}
                   AND T2.CONTENT_TYPE = 'PLAYLIST'
               ) T0
          ORDER BY T0.PROGRAM_ID
    </select>
    <!-- KDH [RQ190703-00340][19.10 RC] 컨텐츠 사용기간 추가(만료) E -->

    <select id="getContentIdByEventScheduleId" resultType="java.util.Map">
        SELECT CONTENT_ID
        FROM MI_EVENT_INFO_CONDITION AS CONTENT,
            (SELECT EVENT_ID FROM MI_EVENT_MAP_SCHEDULE_EVENT WHERE SCHEDULE_ID=#{scheduleId}) AS EVENT
        WHERE
	        EVENT.EVENT_ID = CONTENT.EVENT_ID;
    </select>

    <select id="getFrameContentsByProgramId" resultType="String">
        SELECT DEFAULT_CONTENT_ID
        FROM MI_CDS_INFO_FRAME
        WHERE PROGRAM_ID = #{programId};
    </select>
    
    <!-- KYJ [19-12-26] 그룹 서치 추가 S -->
    <select id="getScheduleGroupBySearchText" resultType="com.samsung.magicinfo.framework.scheduler.entity.ProgramGroup">
        WITH RECURSIVE GROUP_IDS AS (
            SELECT *
            <bind name="safe_tableName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(tableName)" />
            FROM ${safe_tableName}
            WHERE P_GROUP_ID = 0 
             <if test="organizationName != 'ROOT'">
            AND GROUP_ID = (SELECT GROUP_ID FROM MI_CDS_INFO_PROGRAM_GROUP WHERE GROUP_NAME = #{organizationName} AND DESCRIPTION = 'Organization')
            </if> 
            UNION ALL
            SELECT CHILD_GROUP.*
            <bind name="safe_tableName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(tableName)" />
            FROM ${safe_tableName} CHILD_GROUP
            JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
        )
        SELECT * FROM GROUP_IDS 
         <if test="searchText != null and searchText.length() > 0">
              <bind name="searchText" value="'%' + searchText + '%'" />
               WHERE GROUP_NAME LIKE #{searchText} ESCAPE '^'
        </if> 
    </select>

    <select id="getScheduleGroupBySearchText" resultType="com.samsung.magicinfo.framework.scheduler.entity.ProgramGroup" databaseId="mssql">
        WITH GROUP_IDS AS (
        SELECT *
        <bind name="safe_tableName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(tableName)" />
        FROM ${safe_tableName}
        WHERE P_GROUP_ID = 0
        <if test="organizationName != 'ROOT'">
            AND GROUP_ID = (SELECT GROUP_ID FROM MI_CDS_INFO_PROGRAM_GROUP WHERE GROUP_NAME = #{organizationName} AND DESCRIPTION = 'Organization')
        </if>
        UNION ALL
        SELECT CHILD_GROUP.*
        <bind name="safe_tableName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(tableName)" />
        FROM ${safe_tableName} CHILD_GROUP
        JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
        )
        SELECT * FROM GROUP_IDS
        <if test="searchText != null and searchText.length() > 0">
            <bind name="searchText" value="'%' + searchText + '%'" />
            WHERE GROUP_NAME LIKE #{searchText} ESCAPE '^'
        </if>
    </select>

    <select id="getParentsGroupList" resultType="com.samsung.magicinfo.restapi.common.model.V2ParentsScheduleGroup">
         WITH RECURSIVE GROUP_IDS AS (
            SELECT GROUPS.*
            <bind name="safe_tableName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(tableName)" />
            FROM ${safe_tableName} GROUPS
            WHERE GROUP_ID = #{pGroupId}
            UNION ALL
            SELECT PARENT_GROUP.*
            <bind name="safe_tableName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(tableName)" />
            FROM ${safe_tableName} PARENT_GROUP
            JOIN GROUP_IDS ON PARENT_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
        )
        SELECT GROUP_IDS.GROUP_ID as groupId, 
            GROUP_IDS.P_GROUP_ID as parentGroupId, 
            GROUP_IDS.GROUP_DEPTH as groupDepth, 
            GROUP_IDS.GROUP_NAME as groupName, 
            GROUP_IDS.DESCRIPTION as description
        FROM GROUP_IDS
        WHERE P_GROUP_ID > -1
        ORDER BY group_depth asc
    </select>

    <select id="getParentsGroupList" resultType="com.samsung.magicinfo.restapi.common.model.V2ParentsScheduleGroup" databaseId="mssql">
        WITH GROUP_IDS AS (
        SELECT GROUPS.*
        <bind name="safe_tableName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(tableName)" />
        FROM ${safe_tableName} GROUPS
        WHERE GROUP_ID = #{pGroupId}
        UNION ALL
        SELECT PARENT_GROUP.*
        <bind name="safe_tableName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(tableName)" />
        FROM ${safe_tableName} PARENT_GROUP
        JOIN GROUP_IDS ON PARENT_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
        )
        SELECT GROUP_IDS.GROUP_ID as groupId,
        GROUP_IDS.P_GROUP_ID as parentGroupId,
        GROUP_IDS.GROUP_DEPTH as groupDepth,
        GROUP_IDS.GROUP_NAME as groupName,
        GROUP_IDS.DESCRIPTION as description
        FROM GROUP_IDS
        WHERE P_GROUP_ID > -1
        ORDER BY group_depth asc
    </select>
    <!-- KYJ [19-12-26] 그룹 서치 추가 E -->

    <!-- KYJ [20-03-19] DV.DEVICE_NAME 으로 seach 되게 변경 S -->
    <sql id="getDeviceScheduleToExpire_from">
        FROM MI_CDS_MAP_PROGRAM_GROUP MAP_PROGRAM
            INNER JOIN MI_CDS_INFO_PROGRAM PROGRAMS         ON MAP_PROGRAM.PROGRAM_ID = PROGRAMS.PROGRAM_ID
            INNER JOIN MI_CDS_INFO_SCHEDULE SCHEDULES       ON PROGRAMS.PROGRAM_ID = SCHEDULES.PROGRAM_ID
            LEFT JOIN MI_CDS_MAP_PROGRAM_DEVICE PR_DV_MAP   ON PR_DV_MAP.PROGRAM_ID = PROGRAMS.PROGRAM_ID
            LEFT JOIN MI_DMS_INFO_GROUP DV_GRP              ON DV_GRP.GROUP_ID = PR_DV_MAP.DEVICE_GROUP_ID  
            LEFT JOIN MI_DMS_MAP_GROUP_DEVICE DV_GRP_MAP    ON PR_DV_MAP.DEVICE_GROUP_ID = DV_GRP_MAP.GROUP_ID
            LEFT JOIN MI_DMS_INFO_DEVICE DV                 ON DV.DEVICE_ID = DV_GRP_MAP.DEVICE_ID
        WHERE MAP_PROGRAM.GROUP_ID IN (
            <include refid="groupRecursiveQuery"/>
            SELECT GROUP_ID
            FROM B
        )
        AND PROGRAMS.IS_DEFAULT = 'N' AND PROGRAMS.DELETED = 'N'
        AND SCHEDULES.STOP_DATE &lt;= CAST(#{stopDate} AS VARCHAR)
        AND NOT EXISTS ( SELECT 1
        FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAMS.PROGRAM_ID = PROGRAM_ID
        AND STOP_DATE  &lt;= CAST(#{stopDate} AS VARCHAR)
        AND SCHEDULES.schedule_id != schedule_id
        AND STOP_DATE &gt; SCHEDULES.STOP_DATE )
        <if test="condition != null">
            <if test="condition.commonSearchKeyword != null and !condition.commonSearchKeyword.equals('')">
                AND PROGRAMS.PROGRAM_NAME = #{condition.commonSearchKeyword}
            </if>
            <if test="condition.src_name != null and condition.src_name != ''">
                <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
                <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
                AND UPPER(DV.DEVICE_NAME)
                LIKE #{srcNamePattern} 
            </if>
        </if>
    </sql>
    <sql id="getDeviceScheduleToExpire_from" databaseId="mssql">
        FROM MI_CDS_MAP_PROGRAM_GROUP MAP_PROGRAM
        INNER JOIN MI_CDS_INFO_PROGRAM PROGRAMS         ON MAP_PROGRAM.PROGRAM_ID = PROGRAMS.PROGRAM_ID
        INNER JOIN MI_CDS_INFO_SCHEDULE SCHEDULES       ON PROGRAMS.PROGRAM_ID = SCHEDULES.PROGRAM_ID
        LEFT JOIN MI_CDS_MAP_PROGRAM_DEVICE PR_DV_MAP   ON PR_DV_MAP.PROGRAM_ID = PROGRAMS.PROGRAM_ID
        LEFT JOIN MI_DMS_INFO_GROUP DV_GRP              ON DV_GRP.GROUP_ID = PR_DV_MAP.DEVICE_GROUP_ID
        LEFT JOIN MI_DMS_MAP_GROUP_DEVICE DV_GRP_MAP    ON PR_DV_MAP.DEVICE_GROUP_ID = DV_GRP_MAP.GROUP_ID
        LEFT JOIN MI_DMS_INFO_DEVICE DV                 ON DV.DEVICE_ID = DV_GRP_MAP.DEVICE_ID
        WHERE MAP_PROGRAM.GROUP_ID IN ( SELECT GROUP_ID FROM B )
        AND PROGRAMS.IS_DEFAULT = 'N' AND PROGRAMS.DELETED = 'N'
        AND SCHEDULES.STOP_DATE &lt;= CAST(#{stopDate} AS NVARCHAR)
        AND NOT EXISTS ( SELECT 1
        FROM MI_CDS_INFO_SCHEDULE
        WHERE PROGRAMS.PROGRAM_ID = PROGRAM_ID
        AND STOP_DATE  &lt;= CAST(#{stopDate} AS VARCHAR)
        AND SCHEDULES.schedule_id != schedule_id
        AND STOP_DATE &gt; SCHEDULES.STOP_DATE )
        <if test="condition != null">
            <if test="condition.commonSearchKeyword != null and !condition.commonSearchKeyword.equals('')">
                AND PROGRAMS.PROGRAM_NAME = #{condition.commonSearchKeyword}
            </if>
            <if test="condition.src_name != null and condition.src_name != ''">
                <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
                <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
                AND UPPER(DV.DEVICE_NAME)
                LIKE #{srcNamePattern}
            </if>
        </if>
    </sql>
    <!-- KYJ [20-03-19] DV.DEVICE_NAME 으로 seach 되게 변경 E -->

    <select id="getProgramCountByProgramType" resultType="Map">
  		SELECT PROGRAM_TYPE, COUNT(PROGRAM_ID) AS PROGRAM_COUNT
		FROM MI_CDS_INFO_PROGRAM AS P
		GROUP BY PROGRAM_TYPE
	</select>

    <update id="deleteBgmContentInProgram">
        UPDATE MI_CDS_INFO_PROGRAM
           SET BGM_CONTENT_ID = null
         WHERE BGM_CONTENT_ID = #{contentId}
    </update>

</mapper>
