package com.samsung.magicinfo.protocol.util.mail;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.compiler.MOTree;
import com.samsung.magicinfo.protocol.compiler.MOTreeNode;
import com.samsung.magicinfo.protocol.repository.MORepository;
import com.samsung.magicinfo.protocol.util.MailTemplet;
import com.samsung.magicinfo.protocol.util.mail.dao.MailConfigurationDAO;
import java.util.Locale;
import java.util.Map;
import org.apache.logging.log4j.Logger;
import org.springframework.context.support.ResourceBundleMessageSource;

public class MailConfigurationManager {
   Logger logger = LoggingManagerV2.getLogger(MailConfigurationManager.class);
   public static final String MO_PATH_FAULT = ".MO.FAULT_INFO";
   public static final String MO_PATH_RSC = ".MO.RSC_INFO";
   public static final String MO_PATH_CONF = ".MO.CONF_INFO";
   public static final String MO_PATH_VM_STATUS = ".MO.FAULT_INFO.MACHINE_STATUS";
   private User targetUser;
   private User adminUser = new User();
   private User deviceUser = new User();
   private String deviceModelId;
   private String modelName;
   private String deviceSN;
   private String deviceName;
   private String eventDate;
   private String eventDescription;
   private boolean isResource;
   private String mailBody;
   private String content;
   private String sendTo;
   private String as_seq;
   private String faultSeq;
   private String faultDesc;

   public MailConfigurationManager(String deviceSN, String faultSeq, String as_seq) throws Exception {
      super();
      this.deviceSN = deviceSN;
      this.faultSeq = faultSeq;
      this.as_seq = as_seq;

      try {
         this.init();
      } catch (Exception var5) {
         throw var5;
      }
   }

   public MailConfigurationManager(String deviceSN) throws Exception {
      super();
      this.deviceSN = deviceSN;
      this.isResource = true;

      try {
         this.init();
      } catch (Exception var3) {
         this.logger.error("", var3);
         throw var3;
      }
   }

   public MailConfigurationManager() throws Exception {
      super();
   }

   private void init() throws Exception {
      this.setDeviceInfo();
      if (!this.isResource) {
         this.setFaultInfo();
      }

   }

   private void setFaultInfo() throws Exception {
      try {
         MailConfigurationDAO processingRuleMailDAO = new MailConfigurationDAO();
         Map faultInfo = processingRuleMailDAO.getFaultInfo(this.faultSeq);
         this.eventDate = (String)faultInfo.get("EVENT_DATE");
         this.eventDescription = (String)faultInfo.get("EVENT_DESCRIPTION");
      } catch (Exception var3) {
         this.logger.error("", var3);
         throw var3;
      }
   }

   private void setDeviceInfo() throws Exception {
      try {
         MailConfigurationDAO processingRuleMailDAO = new MailConfigurationDAO();
         Map deviceInfo = processingRuleMailDAO.getDeviceInfo(this.deviceSN);
         if (deviceInfo == null) {
            throw new Exception("디바이스가 등록되지 않았습니다.");
         } else {
            this.deviceModelId = (String)deviceInfo.get("DEVICE_MODEL_CODE");
            this.modelName = (String)deviceInfo.get("DEVICE_MODEL_NAME");
            this.deviceSN = (String)deviceInfo.get("DEVICE_ID");
            this.deviceName = (String)deviceInfo.get("DEVICE_NAME");
         }
      } catch (Exception var3) {
         this.logger.error("", var3);
         throw var3;
      }
   }

   private void setDeviceUserInfo() throws Exception {
      try {
         MailConfigurationDAO processingRuleMailDAO = new MailConfigurationDAO();
         Map deviceInfo = processingRuleMailDAO.getDeviceInfo(this.deviceSN);
         if (deviceInfo == null) {
            throw new Exception("디바이스에 사용자가 등록되지 않았습니다.");
         } else {
            this.deviceUser.setUser_id((String)deviceInfo.get("USER_ID"));
            this.deviceUser.setUser_name((String)deviceInfo.get("DEVICE_USER"));
            this.deviceUser.setEmail((String)deviceInfo.get("EMAIL"));
            this.deviceUser.setPhone_num((String)deviceInfo.get("MOBILE_NUM"));
         }
      } catch (Exception var3) {
         this.logger.error("", var3);
         throw var3;
      }
   }

   private void makeMailBody() {
      StringBuffer sb = new StringBuffer();
      sb.append(this.content);
      this.mailBody = sb.toString();
   }

   public com.samsung.magicinfo.protocol.entity.Mail makeMail(String sendTo, String template, String title, String content, String moPath) throws Exception {
      if (sendTo != null && !sendTo.equals("")) {
         String mailContent = content;
         if (content == null) {
            mailContent = this.getMOPathDescriptionAsContents(this.deviceModelId, moPath);
         }

         com.samsung.magicinfo.protocol.entity.Mail mail = new com.samsung.magicinfo.protocol.entity.Mail();
         UserInfo userInfo = UserInfoImpl.getInstance();
         String toList = userInfo.getMailList(sendTo);
         mail.setFrom(userInfo.getMailList("admin"));
         mail.setToList(toList);
         mail.setSubject(title != null ? title : mailContent);
         String templetURL = CommonConfig.get("mail.templet.url");
         String templetPath = CommonConfig.get("mail.templet.path");
         ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
         rms.setBasename("resource/messages");
         String mailMessage = rms.getMessage("MIS_MESSAGE_DEVICE_OCCUR_FAULT_P", (Object[])null, new Locale("en"));
         if (this.deviceName != null && this.deviceSN != null) {
            mailMessage = mailMessage.replaceAll("%1", this.deviceName);
            mailMessage = mailMessage.replaceAll("%2", this.deviceSN);
         } else {
            mailMessage = title;
         }

         String subTitle = rms.getMessage("MSG_FAULT", (Object[])null, new Locale("en"));
         if (moPath == null) {
            subTitle = "";
         } else if (moPath.startsWith(".MO.FAULT_INFO.")) {
            subTitle = rms.getMessage("MSG_FAULT", (Object[])null, new Locale("en"));
         } else if (moPath.startsWith(".MO.ALARM_INFO.")) {
            subTitle = rms.getMessage("TABLE_ALARM_P", (Object[])null, new Locale("en"));
         }

         String faultContent = rms.getMessage("MIS_TEXT_FAULT_CONTENT_P", (Object[])null, new Locale("en"));
         MailTemplet mt = new MailTemplet(templetPath + "/mail_templete.html");
         mt.set("templetURL", templetURL);
         mt.set("mailTitle", mailMessage);
         mt.set("subTitle", subTitle);
         mt.set("contentName", faultContent);
         mt.set("content", mailContent);
         mail.setBody(mt.getContent());
         return mail;
      } else {
         return null;
      }
   }

   public boolean sendDeviceUser() throws Exception {
      if (this.deviceUser.getEmail() != null && !this.deviceUser.getEmail().equals("")) {
         com.samsung.magicinfo.protocol.entity.Mail mail = new com.samsung.magicinfo.protocol.entity.Mail();
         mail.setToList(this.deviceUser.getEmail());
         mail.setFrom(this.adminUser.getEmail());
         mail.setSubject("[WS-RM] Device User Mail");
         mail.setBody(this.mailBody);

         try {
            MailManagerInterface var2 = MailManager.getInstance();
            return false;
         } catch (Exception var3) {
            this.logger.error("==========================================");
            this.logger.error("MessagingException");
            this.logger.error(var3);
            this.logger.error("==========================================");
            throw var3;
         }
      } else {
         this.logger.debug("deviceUserEmail ============= : " + this.deviceUser.getEmail());
         return false;
      }
   }

   private String getMOPathDescriptionAsContents(String deviceModelId, String moPath) throws Exception {
      String content = null;
      if (deviceModelId == null) {
         return null;
      } else {
         MOTree moTree = MORepository.getInstance().getMOTree(deviceModelId);
         MOTreeNode moTreeNode = moTree.getMOTreeNode(moPath);
         content = moTreeNode.getDescription();
         return content;
      }
   }
}
