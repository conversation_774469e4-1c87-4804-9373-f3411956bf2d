package com.samsung.magicinfo.framework.ruleset.dao;

import com.samsung.common.db.PagedListInfo;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.ruleset.entity.Condition;
import com.samsung.magicinfo.framework.ruleset.entity.Result;
import com.samsung.magicinfo.framework.ruleset.entity.ResultKeyword;
import com.samsung.magicinfo.framework.ruleset.entity.RuleSet;
import com.samsung.magicinfo.framework.ruleset.entity.RulesetGroup;
import com.samsung.magicinfo.framework.ruleset.entity.SelectConditionRuleset;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class RuleSetDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(RuleSetDao.class);

   public RuleSetDao() {
      super();
   }

   public RuleSetDao(SqlSession session) {
      super(session);
   }

   public PagedListInfo getRuleSetList(Map condition) throws SQLException {
      SelectConditionRuleset condObj = (SelectConditionRuleset)condition.get("condition");
      List pageList = ((RuleSetDaoMapper)this.getMapper()).getRuleSetList(condObj);
      int totalCount = ((RuleSetDaoMapper)this.getMapper()).getRuleSetListTotalCount(condObj);
      return new PagedListInfo(pageList, totalCount);
   }

   private void conditionGroupCheck(SelectConditionRuleset condition) throws SQLException {
      Long groupId = condition.getGroupId();
      if (groupId != null) {
         RulesetGroup group = this.getGroupById(groupId);
         if (group.getP_group_id().equals(0L)) {
            condition.setGroupId((Long)null);
            List list = this.getChildGroupList(groupId, true);
            Long[] groupIds = new Long[list.size()];

            for(int i = 0; i < list.size(); ++i) {
               groupIds[i] = ((RulesetGroup)list.get(i)).getGroup_id();
            }

            condition.setGroupIds(groupIds);
         }
      }

   }

   public List getRuleSetList(SelectConditionRuleset condition) throws SQLException {
      this.conditionGroupCheck(condition);
      return ((RuleSetDaoMapper)this.getMapper()).getRuleSetList(condition);
   }

   public Integer getRuleSetListTotalCount(SelectConditionRuleset condition) throws SQLException {
      this.conditionGroupCheck(condition);
      return ((RuleSetDaoMapper)this.getMapper()).getRuleSetListTotalCount(condition);
   }

   public PagedListInfo getConditionList(Map condition, Boolean isPublic) throws SQLException {
      SelectConditionRuleset condObj = (SelectConditionRuleset)condition.get("condition");
      List pageList = ((RuleSetDaoMapper)this.getMapper()).getConditionList(condObj, isPublic);
      int totalCount = ((RuleSetDaoMapper)this.getMapper()).getConditionListTotalCount(condObj, isPublic);
      return new PagedListInfo(pageList, totalCount);
   }

   public PagedListInfo getPublicResultList(Map condition) throws SQLException {
      SelectConditionRuleset condObj = (SelectConditionRuleset)condition.get("condition");
      List results = ((RuleSetDaoMapper)this.getMapper()).getPublicResultList(condObj);
      Iterator var4 = results.iterator();

      while(var4.hasNext()) {
         Result result = (Result)var4.next();
         List contentsIdList = this.getContentIdsInResult(result.getResult_id());
         result.setContentsIDList(contentsIdList);
      }

      int totalCount = ((RuleSetDaoMapper)this.getMapper()).getPublicResultListTotalCount(condObj);
      return new PagedListInfo(results, totalCount);
   }

   public List getConditionList(SelectConditionRuleset condition, Boolean isPublic) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getConditionList(condition, isPublic);
   }

   public Integer getConditionListTotalCount(SelectConditionRuleset condition, Boolean isPublic) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getConditionListTotalCount(condition, isPublic);
   }

   public List getResultList(SelectConditionRuleset condition, Boolean isPublic) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getResultList(condition, isPublic);
   }

   public Integer getResultListTotalCount(SelectConditionRuleset condition, Boolean isPublic) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getResultListTotalCount(condition, isPublic);
   }

   public Boolean addRuleset(RuleSet ruleset) {
      SqlSession session = this.openNewSession(false);

      Boolean var4;
      try {
         String rulesetId = ruleset.getRuleset_id();
         if (ruleset.getDescription().length() > 400) {
            ruleset.setDescription(ruleset.getDescription().substring(0, 400));
         }

         if (this.addRuleset(ruleset, session) > 0 && this.addRulesetGroupMapping(rulesetId, ruleset.getGroup_id(), session) > 0) {
            if (!this.saveRulesetDetails(ruleset, session)) {
               session.rollback();
               var4 = false;
               return var4;
            }

            session.commit();
            return true;
         }

         session.rollback();
         var4 = false;
         return var4;
      } catch (SQLException var8) {
         this.logger.error(var8);
         session.rollback();
         var4 = false;
      } finally {
         session.close();
      }

      return var4;
   }

   public Boolean editRuleset(RuleSet ruleset) {
      SqlSession session = this.openNewSession(false);

      Boolean var4;
      try {
         String rulesetId = ruleset.getRuleset_id();
         if (ruleset.getDescription().length() > 400) {
            ruleset.setDescription(ruleset.getDescription().substring(0, 400));
         }

         if (!this.updateRuleset(ruleset, session) || !this.updateRulesetGroupMapping(rulesetId, ruleset.getGroup_id(), session)) {
            session.rollback();
            var4 = false;
            return var4;
         }

         if (!this.deleteRulesetDetails(rulesetId, session)) {
            session.rollback();
            var4 = false;
            return var4;
         }

         if (this.saveRulesetDetails(ruleset, session)) {
            session.commit();
            return true;
         }

         session.rollback();
         var4 = false;
      } catch (SQLException var8) {
         this.logger.error(var8);
         session.rollback();
         var4 = false;
         return var4;
      } finally {
         session.close();
      }

      return var4;
   }

   public Boolean deleteRulesetDetails(String rulesetId, SqlSession session) {
      try {
         List deletedMapIds = this.getConditionIdsInRuleset(rulesetId, (SqlSession)null);
         this.deleteRulesetConditionMap(rulesetId, session);
         List resultMapIds = this.getResultIdsInRuleset(rulesetId, (SqlSession)null);
         this.deleteRulesetResultMap(rulesetId, session);
         if (!deletedMapIds.isEmpty()) {
            this.deleteConditions(deletedMapIds, session);
         }

         if (!resultMapIds.isEmpty()) {
            Iterator var5 = resultMapIds.iterator();

            while(var5.hasNext()) {
               String resultId = (String)var5.next();
               this.deleteResultContentMapping(resultId, session);
            }

            this.deleteResults(resultMapIds, session);
         }

         return true;
      } catch (Exception var7) {
         this.logger.error("", var7);
         return false;
      }
   }

   public Boolean saveRulesetDetails(RuleSet ruleset, SqlSession session) {
      try {
         String rulesetId = ruleset.getRuleset_id();
         String userId = SecurityUtils.getLoginUserId();
         List conditions = ruleset.getConditions();
         if (conditions != null && !conditions.isEmpty()) {
            Iterator var6 = conditions.iterator();

            while(var6.hasNext()) {
               Condition condition = (Condition)var6.next();
               condition.setCreator(userId);
               this.addCondition(condition, session);
               if (!condition.getIs_public()) {
                  this.addRulesetConditionMapping(rulesetId, condition.getCondition_id(), session);
               }
            }
         }

         List results = ruleset.getResults();
         if (results != null && !results.isEmpty()) {
            Iterator var11 = results.iterator();

            while(var11.hasNext()) {
               Result result = (Result)var11.next();
               result.setCreator(userId);
               this.addResult(result, session);
               if (result.getContentsIDList() != null) {
                  this.addResultContentMapping(result.getResult_id(), result.getContentsIDList(), result.getContents_type(), session);
               }

               if (!result.getIs_public()) {
                  this.addRulesetResultMapping(rulesetId, result.getResult_id(), session);
               }
            }
         }

         return true;
      } catch (Exception var9) {
         this.logger.error("", var9);
         return false;
      }
   }

   public Boolean checkExistedCondition(String conditionId) throws SQLException {
      Condition condition = ((RuleSetDaoMapper)this.getMapper()).getCondition(conditionId);
      return condition != null ? true : false;
   }

   public Condition getCondition(String conditionId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getCondition(conditionId);
   }

   public Result getResult(String resultId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getResult(resultId);
   }

   public RuleSet getRulesetBasicInfo(String rulesetId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getRuleset(rulesetId);
   }

   public List getConditionsInRuleset(String rulesetId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getConditionsInRuleset(rulesetId);
   }

   public List getPublicConditions(Long organizationId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getPublicConditions(organizationId);
   }

   public List getPublicResults(Long organizationId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getPublicResults(organizationId);
   }

   public List getContentsInRuleset(String rulesetId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getContentsInRuleset(rulesetId);
   }

   public List getContentIdsInResult(String resultId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getContentIdsInResult(resultId);
   }

   public Integer addRuleset(RuleSet ruleset, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).addRuleset(ruleset);
   }

   public Integer addRulesetGroupMapping(String rulesetId, Long groupId, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).addRulesetGroupMapping(rulesetId, groupId);
   }

   public Integer addRulesetGroup(RulesetGroup group, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).addRulesetGroup(group);
   }

   public Boolean updateRulesetGroup(RulesetGroup group) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).updateRulesetGroup(group);
   }

   public Boolean deleteRulesetGroup(List groups, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).deleteRulesetGroup(groups);
   }

   public Integer addCondition(Condition condition, SqlSession session) throws SQLException {
      if (condition.getDescription().length() > 200) {
         condition.setDescription(condition.getDescription().substring(0, 200));
      }

      return ((RuleSetDaoMapper)this.getMapper(session)).addCondition(condition);
   }

   public Integer addRulesetConditionMapping(String rulesetId, String conditionId, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).addRulesetConditionMapping(rulesetId, conditionId);
   }

   public Boolean addResultContentMapping(String resultId, List contentsIdList, String contentsType, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).addResultContentMapping(resultId, contentsIdList, contentsType);
   }

   public Integer addRulesetResultMapping(String rulesetId, String resultId, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).addRulesetResultMapping(rulesetId, resultId);
   }

   public Boolean updateRuleset(RuleSet ruleset, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).updateRuleset(ruleset);
   }

   public Boolean deleteRulesetConditionMap(String rulesetId, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).deleteRulesetConditionMap(rulesetId);
   }

   public Boolean deleteRulesetResultMap(String rulesetId, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).deleteRulesetResultMap(rulesetId);
   }

   public Boolean deleteConditions(List conditionIds, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).deleteConditions(conditionIds);
   }

   public Boolean deleteRulesetGroupMapping(String rulesetId, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).deleteRulesetGroupMapping(rulesetId);
   }

   public Boolean updateRulesetGroupMapping(String rulesetId, Long groupId, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).updateRulesetGroupMapping(rulesetId, groupId);
   }

   public List getChildGroupList(Long group_id, boolean recursive) throws SQLException {
      List groupList = new ArrayList();
      List resList = ((RuleSetDaoMapper)this.getMapper()).getChildGroupList(group_id);
      groupList.addAll(resList);
      if (recursive) {
         Iterator var5 = resList.iterator();

         while(var5.hasNext()) {
            RulesetGroup userGroup = (RulesetGroup)var5.next();
            groupList.addAll(this.getChildGroupList(userGroup.getGroup_id(), recursive));
         }
      }

      return groupList;
   }

   public Long getOrgGroupIdByName(String groupName) throws SQLException {
      try {
         return ((RuleSetDaoMapper)this.getMapper()).getOrgGroupIdByName(groupName);
      } catch (Exception var3) {
         return 0L;
      }
   }

   public RulesetGroup getGroupById(long groupId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getGroupById(groupId);
   }

   public Integer getCountGroupedRuleset(Map map) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getCountGroupedRuleset(map);
   }

   public Long getCountRulesetByOrganization(Long organizationId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getCountRulesetByOrganization(organizationId);
   }

   public Integer updateCondition(Condition condition, SqlSession session) throws SQLException {
      if (condition.getDescription().length() > 200) {
         condition.setDescription(condition.getDescription().substring(0, 200));
      }

      return ((RuleSetDaoMapper)this.getMapper(session)).updateCondition(condition);
   }

   public Boolean addResult(Result result, SqlSession session) throws SQLException {
      if (result.getDescription().length() > 200) {
         result.setDescription(result.getDescription().substring(0, 200));
      }

      return ((RuleSetDaoMapper)this.getMapper(session)).addResult(result);
   }

   public Boolean deleteResult(String resultId, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).deleteResult(resultId);
   }

   public Boolean deleteResultContentMapping(String resultId, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).deleteResultContentMapping(resultId);
   }

   public Boolean deleteResults(List resultIds, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).deleteResults(resultIds);
   }

   public PagedListInfo getDeviceListByCondition(List conditions, Integer start, Integer length) throws SQLException {
      List pageList = ((RuleSetDaoMapper)this.getMapper()).getDeviceListByCondition(conditions, start, length);
      int totalCount = ((RuleSetDaoMapper)this.getMapper()).getCountOfDevicesByCondition(conditions);
      return new PagedListInfo(pageList, totalCount);
   }

   public List getConditionIdsInRuleset(String rulesetId, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).getConditionIdsInRuleset(rulesetId);
   }

   public List getResultIdsInRuleset(String rulesetId, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).getResultIdsInRuleset(rulesetId);
   }

   public Boolean setDeleteStatus(Boolean isDeleted, String[] rulesetIds, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).setDeleteStatus(isDeleted, rulesetIds);
   }

   public Boolean deleteRuleset(String[] rulesetIds, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).deleteRuleset(rulesetIds);
   }

   public List getRulesetUsingContents(String contentsId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getRulesetUsingContents(contentsId);
   }

   public List getRulesetUsingSubPlaylist(String playlistId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getRulesetUsingSubPlaylist(playlistId);
   }

   public Integer addRulesetOrganization(Integer groupId, String organName, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).addRulesetOrganization(groupId, organName);
   }

   public Integer addRulesetDefaultGroup(Integer groupId, Integer organId, String groupName, SqlSession session) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper(session)).addRulesetDefaultGroup(groupId, organId, groupName);
   }

   public List getAllDeletedRulesetIds(Long organizationId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getAllDeletedRulesetIds(organizationId);
   }

   public List getRulesetListByGroupId(Long groupId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getRulesetListByGroupId(groupId);
   }

   public List getKeywords(Long organizationId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getKeywords(organizationId);
   }

   public Integer addKeyword(ResultKeyword keyword) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).addKeyword(keyword);
   }

   public Integer updateKeyword(ResultKeyword keyword) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).updateKeyword(keyword);
   }

   public Boolean deleteKeywords(List keywordIds) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).deleteKeywords(keywordIds);
   }

   public Boolean hasContentInRuleset(String rulesetId, String contentId) throws SQLException {
      Integer res = ((RuleSetDaoMapper)this.getMapper()).hasContentInRuleset(rulesetId, contentId);
      return res != null ? res > 0 : false;
   }

   public List getRulesetContentDownloadStatus(String rulesetId, String deviceId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getRulesetContentDownloadStatus(rulesetId, deviceId);
   }

   public List getRulesetGroupBySearch(String organizationName, String searchText) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getRulesetGroupBySearch(organizationName, searchText);
   }

   public Integer getRulesetGroupTotalCount() throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getRulesetGroupTotalCount();
   }

   public List getParentsGroupList(long pGroupId) throws SQLException {
      return ((RuleSetDaoMapper)this.getMapper()).getParentsGroupList(pGroupId);
   }
}
