package com.samsung.magicinfo.webauthor2.service;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.ContentSaveElements;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.repository.CsdRepository;
import com.samsung.magicinfo.webauthor2.repository.model.CsdMappingResponse;
import com.samsung.magicinfo.webauthor2.service.CSDFileService;
import com.samsung.magicinfo.webauthor2.service.transferfile.CSDFileXmlFactory;
import com.samsung.magicinfo.webauthor2.service.transferfile.TransferFileXmlResponseFactory;
import com.samsung.magicinfo.webauthor2.xml.transferfile.response.TransferFileResponseType;
import com.samsung.magicinfo.webauthor2.xml.transferfile.response.TransferFilesResponseType;
import java.util.List;
import java.util.ListIterator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class CSDFileServiceImpl implements CSDFileService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.CSDFileServiceImpl.class);
  
  private CSDFileXmlFactory factory;
  
  private TransferFileXmlResponseFactory transferFileXmlResponseFactory;
  
  private CsdRepository csdRepository;
  
  private ContentSaveElements contentSaveElements;
  
  @Autowired
  public CSDFileServiceImpl(CSDFileXmlFactory factory, TransferFileXmlResponseFactory transferFileXmlResponseFactory, CsdRepository csdRepository, ContentSaveElements contentSaveElements) {
    this.factory = factory;
    this.transferFileXmlResponseFactory = transferFileXmlResponseFactory;
    this.csdRepository = csdRepository;
    this.contentSaveElements = contentSaveElements;
  }
  
  public List<MediaSource> updateSaveElements(String userId, ContentSaveElements contentSaveElements, String cid) {
    String csdXml = generateCSD(contentSaveElements, userId);
    TransferFilesResponseType respTransferFiles = postProjectCsdToMips(csdXml, cid, null);
    return updateUserDataSaveElements(respTransferFiles, contentSaveElements);
  }
  
  public String generateCSD(ContentSaveElements contentSaveElements, String userName) {
    return this.factory.marshal(contentSaveElements, userName);
  }
  
  public List<MediaSource> updateUserDataSaveElements(TransferFilesResponseType csdResponse, ContentSaveElements contentSaveElements) {
    List<MediaSource> mediaSources = contentSaveElements.getMediaSources();
    int fileNo = mediaSources.size();
    for (int i = 0; i < fileNo; i++) {
      ((MediaSource)mediaSources.get(i)).setFileId(((TransferFileResponseType)csdResponse.getTransferFiles().get(i)).getFileId());
      ((MediaSource)mediaSources.get(i)).setIsNew(((TransferFileResponseType)csdResponse.getTransferFiles().get(i)).isNew());
    } 
    contentSaveElements.getProjectThumbnailMediaSource().setFileId(((TransferFileResponseType)csdResponse.getTransferFiles().get(fileNo)).getFileId());
    contentSaveElements.getProjectThumbnailMediaSource().setIsNew(((TransferFileResponseType)csdResponse.getTransferFiles().get(fileNo)).isNew());
    return mediaSources;
  }
  
  public TransferFilesResponseType postProjectCsdToMips(String csdxml, String cid, String templateId) {
    CsdMappingResponse csdMappingResponse = this.csdRepository.postCsdToMips(csdxml, cid, templateId);
    if (csdMappingResponse.getStatusCode() == HttpStatus.OK) {
      this.contentSaveElements.setVersion(csdMappingResponse.getVersionId());
      this.contentSaveElements.setDuplicate(false);
      String contentDuplicate = csdMappingResponse.getContentDuplicate();
      if (contentDuplicate.equalsIgnoreCase("DUPLICATE"))
        this.contentSaveElements.setDuplicate(true); 
      String body = csdMappingResponse.getBody();
      if (Strings.isNullOrEmpty(body)) {
        logger.info(":responseStr == null:");
      } else {
        TransferFilesResponseType respTransferFiles = this.transferFileXmlResponseFactory.unmarshal(body);
        if (respTransferFiles == null || respTransferFiles.getTransferFiles() == null)
          throw new UploaderException("ServerInternalUploadError"); 
        return respTransferFiles;
      } 
    } 
    throw new UploaderException(500, "ServerInternalUploadError");
  }
  
  public String generateCSD(List<MediaSource> mediaSources, String contentId, DeviceType deviceType) {
    return this.factory.marshal(mediaSources, contentId, deviceType);
  }
  
  public TransferFilesResponseType postSingleFileCsdToMips(String csdxml, String cid) {
    CsdMappingResponse csdMappingResponse = this.csdRepository.postCsdToMips(csdxml, cid, null);
    if (csdMappingResponse.getStatusCode() == HttpStatus.OK) {
      String body = csdMappingResponse.getBody();
      if (body != null) {
        TransferFilesResponseType respTransferFiles = this.transferFileXmlResponseFactory.unmarshal(body);
        if (respTransferFiles == null || respTransferFiles.getTransferFiles() == null)
          throw new UploaderException("ServerInternalUploadError"); 
        return respTransferFiles;
      } 
    } 
    throw new UploaderException(500, "ServerInternalUploadError");
  }
  
  public List<MediaSource> updateMediaSources(List<MediaSource> mediaSources, TransferFilesResponseType TransferFilesResponse) {
    List<TransferFileResponseType> transferFiles = TransferFilesResponse.getTransferFiles();
    if (transferFiles.size() != mediaSources.size())
      throw new UploaderException(699, "Wrong CSD response. Not equal number of contents"); 
    ListIterator<MediaSource> mediaIterator = mediaSources.listIterator();
    ListIterator<TransferFileResponseType> csdIterator = transferFiles.listIterator();
    while (mediaIterator.hasNext() && csdIterator.hasNext()) {
      MediaSource ms = mediaIterator.next();
      TransferFileResponseType csd = csdIterator.next();
      ms.setFileId(csd.getFileId());
      ms.setIsNew(csd.isNew());
    } 
    return mediaSources;
  }
}
