package com.samsung.magicinfo.framework.scheduler.dao;

import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.scheduler.entity.AdScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.AdSlotEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ChannelEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.DetailDownloadContentEntity;
import com.samsung.magicinfo.framework.scheduler.entity.DynamicTagEntity;
import com.samsung.magicinfo.framework.scheduler.entity.FrameEntity;
import com.samsung.magicinfo.framework.scheduler.entity.FrameTemplateEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.SyncSchedule;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

public interface ScheduleInfoDAOMapper {
   ProgramEntity getProgram(@Param("programId") String var1) throws SQLException;

   List getDeviceProgramMapList(@Param("programId") String var1) throws SQLException;

   boolean mapDeviceGroupWithDefault(@Param("deviceGroupId") Long var1, @Param("defaultProgramId") String var2) throws SQLException;

   List getDeviceGroupIdsAndName(@Param("programId") String var1) throws SQLException;

   String getGroupNameByGroupId(@Param("groupId") long var1) throws SQLException;

   int insert_addFrame(@Param("frame") FrameEntity var1) throws SQLException;

   int insert2_addFrame(@Param("frame") FrameEntity var1, @Param("userGroupId") long var2) throws SQLException;

   void addDeviceGroupMappedInProgramTemp(@Param("program_id") String var1, @Param("groupId") long var2, @Param("groupName") String var4, @Param("default_content") String var5, @Param("bgm_content_id") String var6) throws SQLException;

   void addDeviceGroupMappedInProgram(@Param("program_id") String var1, @Param("device_group_id") long var2) throws SQLException;

   void deleteDeviceGroupMappedInProgramTempByProgramId(@Param("program_id") String var1) throws SQLException;

   void deleteDeviceGroupMappedInProgramByProgramId(@Param("program_id") String var1) throws SQLException;

   List getDeviceGroupMappedInProgramTemp(@Param("program_id") String var1) throws SQLException;

   List getDeviceGroupMappedInProgram(@Param("program_id") String var1) throws SQLException;

   List getDevicesMappedInProgram(@Param("program_id") String var1) throws SQLException;

   void updateDeviceGroupMappedInProgramAsDefault(@Param("program_id") String var1, @Param("group_id") Long var2) throws SQLException;

   void updateDeviceGroupMappedInProgramAsDefaultByPid(@Param("group_id") Long var1, @Param("program_id") String var2) throws SQLException;

   int insert_addProgram(@Param("program") ProgramEntity var1) throws SQLException;

   int insert2_addProgram(@Param("program") ProgramEntity var1) throws SQLException;

   Map select_addProgram(@Param("deviceId") Long var1) throws SQLException;

   void delete_addProgram(@Param("defaultProgramId") String var1) throws SQLException;

   int update_addProgram(@Param("defaultProgramId") String var1) throws SQLException;

   void delete2_addProgram(@Param("deviceId") Long var1) throws SQLException;

   int insert3_addProgram(@Param("program_id") String var1, @Param("deviceId") Long var2) throws SQLException;

   void insert4_addProgram(@Param("program_id") String var1, @Param("sessionId") String var2) throws SQLException;

   void insert5_addProgram(@Param("program_id") String var1, @Param("sessionId") String var2) throws SQLException;

   void insert6_addProgram(@Param("program_id") String var1, @Param("sessionId") String var2) throws SQLException;

   void insertIntoMiCdsInfoChannel_addProgram(@Param("program_id") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete3_addProgram(@Param("program_id") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete4_addProgram(@Param("program_id") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete5_addProgram(@Param("program_id") String var1, @Param("sessionId") String var2) throws SQLException;

   void deleteFromMiCdsInfoChannelTmp(@Param("program_id") String var1, @Param("sessionId") String var2) throws SQLException;

   void deleteFromMiCdsInfoChannel(@Param("program_id") String var1) throws SQLException;

   int insert7_addProgram(@Param("logId") long var1, @Param("program") ProgramEntity var3, @Param("logProgramCreate") String var4, @Param("ipAddress") String var5) throws SQLException;

   int insert_addDefaultProgram(@Param("program") ProgramEntity var1) throws SQLException;

   int insert_miCdsInfoChannelTemp_addDefaultProgram(@Param("channel") ChannelEntity var1) throws SQLException;

   void delete_addDefaultProgram(@Param("deviceId") Long var1) throws SQLException;

   int insert2_addDefaultProgram(@Param("program_id") String var1, @Param("device_id") long var2) throws SQLException;

   int insert3_addDefaultProgram(@Param("frame") FrameEntity var1) throws SQLException;

   Long select_addDefaultProgram(@Param("orgName") String var1) throws SQLException;

   void insert4_addDefaultProgram(@Param("program_id") String var1, @Param("orgGroupId") Long var2) throws SQLException;

   int insert5_addDefaultProgram(@Param("logId") long var1, @Param("program") ProgramEntity var3, @Param("logProgramCreate") String var4, @Param("ipAddress") String var5) throws SQLException;

   int update_updateProgram(@Param("program") ProgramEntity var1) throws SQLException;

   int update2_updateProgram(@Param("program") ProgramEntity var1) throws SQLException;

   Map select_updateProgram(@Param("deviceId") Long var1) throws SQLException;

   void delete_updateProgram(@Param("defaultProgramId") String var1) throws SQLException;

   int update3_updateProgram(@Param("defaultProgramId") String var1) throws SQLException;

   void delete2_updateProgram(@Param("deviceId") Long var1) throws SQLException;

   int insert_updateProgram(@Param("program_id") String var1, @Param("device_id") long var2) throws SQLException;

   void delete3_updateProgram(@Param("program_id") String var1) throws SQLException;

   void insert2_updateProgram(@Param("program_id") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete4_updateProgram(@Param("program_id") String var1) throws SQLException;

   void insert3_updateProgram(@Param("program_id") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete5_updateProgram(@Param("program_id") String var1) throws SQLException;

   void insert4_updateProgram(@Param("program_id") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete6_updateProgram(@Param("program_id") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete7_updateProgram(@Param("program_id") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete8_updateProgram(@Param("program_id") String var1, @Param("sessionId") String var2) throws SQLException;

   int insert5_updateProgram(@Param("logId") long var1, @Param("program") ProgramEntity var3, @Param("logProgramEdit") String var4, @Param("ipAddress") String var5) throws SQLException;

   int updateProgramName(@Param("program_name") String var1, @Param("programId") String var2) throws SQLException;

   int update_addContentSchedule(@Param("schedule") ContentsScheduleEntity var1) throws SQLException;

   Map select_addContentSchedule(@Param("schedule") ContentsScheduleEntity var1) throws SQLException;

   int insert2_addContentSchedule(@Param("schedule") ContentsScheduleEntity var1, @Param("priority") long var2) throws SQLException;

   void delete_transferProgramDataToMain(@Param("programId") String var1) throws SQLException;

   int update_transferProgramDataToMain(@Param("programId") String var1) throws SQLException;

   void insert_transferProgramDataToMain(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete2_transferProgramDataToMain(@Param("programId") String var1) throws SQLException;

   void insert2_transferProgramDataToMain(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete3_transferProgramDataToMain(@Param("programId") String var1) throws SQLException;

   void insert3_transferProgramDataToMain(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete4_transferProgramDataToMain(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete5_transferProgramDataToMain(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete6_transferProgramDataToMain(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   int insert4_transferProgramDataToMain(@Param("logId") long var1, @Param("programId") String var3, @Param("logContentScheduleEdit") String var4, @Param("userId") String var5) throws SQLException;

   void insert_transferProgramDataToTemp(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   void insert2_transferProgramDataToTemp(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   void insert3_transferProgramDataToTemp(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   boolean transferSyncScheduleMatchInfoToTemp(@Param("scheduleId") String var1, @Param("sessionId") String var2) throws SQLException;

   List select_transferScheduleDataToTempWithNewId(@Param("programId") String var1) throws SQLException;

   int insert_transferScheduleDataToTempWithNewId(@Param("newProgramId") String var1, @Param("sessionId") String var2, @Param("schedule") ContentsScheduleEntity var3, @Param("scheduleId") String var4) throws SQLException;

   List select2_transferScheduleDataToTempWithNewId(@Param("programId") String var1) throws SQLException;

   int insert2_transferScheduleDataToTempWithNewId(@Param("newProgramId") String var1, @Param("sessionId") String var2, @Param("frame") FrameEntity var3, @Param("frameId") String var4) throws SQLException;

   int update_updateContentSchedule(@Param("schedule") ContentsScheduleEntity var1) throws SQLException;

   Map select_updateContentSchedule(@Param("schedule") ContentsScheduleEntity var1) throws SQLException;

   int update2_updateContentSchedule(@Param("schedule") ContentsScheduleEntity var1, @Param("priority") long var2) throws SQLException;

   int addPanelOrZeroFrameSchedule(@Param("schedule") ScheduleEntity var1) throws SQLException;

   int updatePanelOrZeroFrameSchedule(@Param("schedule") ScheduleEntity var1) throws SQLException;

   int deleteTempSchedule(@Param("programId") String var1, @Param("scheduleId") String var2) throws SQLException;

   void delete_deleteTempFrame(@Param("programId") String var1, @Param("session_id") String var2) throws SQLException;

   void delete2_deleteTempFrame(@Param("programId") String var1, @Param("session_id") String var2) throws SQLException;

   int deleteFrame(@Param("sessionId") String var1, @Param("programId") String var2, @Param("screenIndex") int var3, @Param("frameIndex") int var4, @Param("is_delete") Boolean var5) throws SQLException;

   void delete_deleteProgram(@Param("programId") String var1) throws SQLException;

   void delete2_deleteProgram(@Param("programId") String var1) throws SQLException;

   int delete3_deleteProgram(@Param("programId") String var1) throws SQLException;

   int insert_deleteSchedule(@Param("sessionId") String var1, @Param("scheduleId") String var2, @Param("is_delete") Boolean var3) throws SQLException;

   int deleteTempScheduleForFrameIndex(@Param("programId") String var1, @Param("sessionId") String var2, @Param("channelNo") int var3, @Param("frameIndex") int var4) throws SQLException;

   List getFrames(@Param("programId") String var1, @Param("channelNo") int var2, @Param("screenIndex") int var3) throws SQLException;

   List getFrame(@Param("programId") String var1, @Param("channelNo") int var2) throws SQLException;

   List getFramesInfo(@Param("programId") String var1, @Param("screenIndex") int var2) throws SQLException;

   List select_getTempFrames(@Param("programId") String var1, @Param("channelNo") int var2, @Param("session_id") String var3) throws SQLException;

   List select2_getTempFrames(@Param("session_id") String var1, @Param("programId") String var2, @Param("channelNo") int var3, @Param("frame_id") String var4) throws SQLException;

   FrameEntity select_getFrameDate(@Param("programId") String var1, @Param("frame_index") int var2) throws SQLException;

   FrameEntity select_getFrameDataWithChannelNo(@Param("programId") String var1, @Param("frame_index") int var2, @Param("channelNo") int var3) throws SQLException;

   List select2_getFrameData(@Param("programId") String var1, @Param("frame_id") String var2) throws SQLException;

   long getTempFrameCount(@Param("programId") String var1, @Param("session_id") String var2) throws SQLException;

   long getTempFrameCountForProgram(@Param("programId") String var1) throws SQLException;

   long getTempFrameCountForSession(@Param("session_id") String var1) throws SQLException;

   String getProgramName(@Param("programId") String var1) throws SQLException;

   void deletet_deleteAllProgramTempData() throws SQLException;

   void deletet2_deleteAllProgramTempData() throws SQLException;

   void deletet3_deleteAllProgramTempData() throws SQLException;

   void delete_deleteProgramTempDataWithId(@Param("programId") String var1) throws SQLException;

   void delete_deleteProgramDataWithId(@Param("programId") String var1) throws SQLException;

   void delete2_deleteProgramTempDataWithId(@Param("programId") String var1) throws SQLException;

   void delete3_deleteProgramTempDataWithId(@Param("programId") String var1) throws SQLException;

   void delete_deleteProgramTempDataWithSession(@Param("sessionId") String var1) throws SQLException;

   void delete2_deleteProgramTempDataWithSession(@Param("sessionId") String var1) throws SQLException;

   void delete3_deleteProgramTempDataWithSession(@Param("sessionId") String var1) throws SQLException;

   void delete_deleteProgramTempData(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete_deleteProgramData(@Param("programId") String var1) throws SQLException;

   void delete2_deleteProgramTempData(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete2_deleteProgramData(@Param("programId") String var1) throws SQLException;

   void delete3_deleteProgramTempData(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   void delete3_deleteProgramData(@Param("programId") String var1) throws SQLException;

   void delete4_deleteProgramTempData(@Param("programId") String var1) throws SQLException;

   void delete4_deleteProgramData(@Param("programId") String var1) throws SQLException;

   List getProgramList(@Param("map") Map var1) throws SQLException;

   int getProgramListCount(@Param("map") Map var1) throws SQLException;

   String getBGMContentName(@Param("programId") String var1) throws SQLException;

   List getContentSchedules(@Param("programId") String var1, @Param("channelNo") int var2, @Param("screenIndex") int var3, @Param("frameIndex") int var4, @Param("contentScheduleType") String var5) throws SQLException;

   List getContentListFromProgramidandChannel(@Param("programId") String var1, @Param("channelNo") int var2, @Param("screenIndex") int var3, @Param("frameIndex") int var4, @Param("contentScheduleType") String var5) throws SQLException;

   List getContentSchedulesForProgramId(@Param("programId") String var1, @Param("contentScheduleType") String var2) throws SQLException;

   List getContentSchedulesForProgramIdAndChannelId(@Param("programId") String var1, @Param("channelNo") int var2, @Param("contentScheduleType") String var3) throws SQLException;

   List getPanelOrZeroFrameSchedules(@Param("programId") String var1, @Param("scheduleType") String var2) throws SQLException;

   List getPanelOrZeroFrameTempSchedules(@Param("programId") String var1, @Param("scheduleType") String var2) throws SQLException;

   List selAllSchedule(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   List selAllScheduleByMonth(@Param("map") Map var1, @Param("frameIndex") int var2, @Param("channelNo") int var3, @Param("isZeroFrameindex") boolean var4) throws SQLException;

   List selAllScheduleByWeek(@Param("map") Map var1, @Param("weekDates") String[] var2, @Param("frameIndex") int var3, @Param("channelNo") int var4, @Param("isZeroframeIndex") boolean var5, @Param("constants") Map var6) throws SQLException;

   List selAllScheduleByDay(@Param("map") Map var1, @Param("frameIndex") int var2, @Param("channelNo") int var3, @Param("isZeroFrameIndex") boolean var4, @Param("date") String var5, @Param("constants") Map var6) throws SQLException;

   ContentsScheduleEntity getScheduleData(@Param("map") Map var1) throws SQLException;

   List getScheduleListPage(@Param("map") Map var1) throws SQLException;

   int getScheduleListCnt(@Param("map") Map var1) throws SQLException;

   List getScheduleList(@Param("map") Map var1) throws SQLException;

   int getScheduleListCount(@Param("map") Map var1) throws SQLException;

   List getHWConstraintList(@Param("map") Map var1) throws SQLException;

   int getHWConstraintListCount(@Param("map") Map var1) throws SQLException;

   void delete_setProgram(@Param("program_id") String var1) throws SQLException;

   void delete2_setProgram(@Param("program_id") String var1) throws SQLException;

   int update_setProgram(@Param("program") ProgramEntity var1) throws SQLException;

   int insert_setProgram(@Param("program") ProgramEntity var1) throws SQLException;

   void delete3_setProgram(@Param("deviceId") long var1) throws SQLException;

   int insert2_setProgram(@Param("program_id") String var1, @Param("deviceId") long var2) throws SQLException;

   int programVersionUp(@Param("programId") String var1) throws SQLException;

   List getDeviceGroupIds(@Param("programId") String var1) throws SQLException;

   List getProgramGroupIdAndName(@Param("programId") String var1) throws SQLException;

   int update_setActiveProgramVersion(@Param("version") long var1, @Param("programId") String var3) throws SQLException;

   int insert_setActiveProgramVersion(@Param("programId") String var1, @Param("version") long var2) throws SQLException;

   Long getActiveProgramVersion(@Param("programId") String var1) throws SQLException;

   Long getProgramVersion(@Param("programId") String var1) throws SQLException;

   List getProgramByContentId(@Param("contentId") String var1) throws SQLException;

   List getProgramByPlaylistId(@Param("playlistId") String var1) throws SQLException;

   boolean isProgramNameUnique(@Param("map") Map var1) throws SQLException;

   List getChildGroupIdList(@Param("group_id") int var1, @Param("nonApprovalGroupId") int var2) throws SQLException;

   Map getProgramGroupRoot(@Param("groupId") int var1) throws SQLException;

   boolean setProgramDeployTime(@Param("programId") String var1) throws SQLException;

   List getDownloadContentList(@Param("programId") String var1, @Param("device_id") String var2) throws SQLException;

   List getDownloadStatusPagedList(@Param("map") Map var1) throws SQLException;

   int getDownloadStatusListCount(@Param("map") Map var1) throws SQLException;

   List getDownloadContentPagedList(@Param("map") Map var1) throws SQLException;

   int getDownloadContentPagedListCount(@Param("map") Map var1) throws SQLException;

   List getDownloadStatusContentList(@Param("map") Map var1) throws SQLException;

   int getDownloadStatusContenCount(@Param("map") Map var1) throws SQLException;

   long getContentScheduleCntToday(@Param("start_time") Timestamp var1, @Param("end_time") Timestamp var2) throws SQLException;

   List getContentScheduleTodayGroupId(@Param("start_time") Timestamp var1, @Param("end_time") Timestamp var2) throws SQLException;

   long getContentScheduleCntThisWeek(@Param("start_time") Timestamp var1, @Param("end_time") Timestamp var2) throws SQLException;

   long getAllScheduleCount() throws SQLException;

   List getAllScheduleGroupId() throws SQLException;

   Map isDelete(@Param("programId") String var1) throws SQLException;

   long getMappedScheduleCount() throws SQLException;

   List getMapedScheduleGroupId() throws SQLException;

   long getNotMapedScheduleCount() throws SQLException;

   List getNotMapedScheduleGroupId() throws SQLException;

   void delete_onProgramLayoutChange(@Param("programId") String var1, @Param("session_id") String var2, @Param("channelNo") int var3) throws SQLException;

   void delete2_onProgramLayoutChange(@Param("programId") String var1, @Param("session_id") String var2, @Param("channelNo") int var3) throws SQLException;

   int update_onProgramLayoutChange(@Param("programId") String var1, @Param("session_id") String var2, @Param("channelNo") int var3, @Param("resolution_x") double var4, @Param("resolution_y") double var6, @Param("line_data") String var8) throws SQLException;

   List select_getFrameTemplates(@Param("template_type") String var1, @Param("organization") String var2) throws SQLException;

   List select2_getFrameTemplates(@Param("template_type") String var1) throws SQLException;

   List select3_getFrameTemplates(@Param("template_type") String var1, @Param("organization") String var2, @Param("resolution") String var3) throws SQLException;

   int saveFrameTemplate(@Param("templateId") long var1, @Param("fte") FrameTemplateEntity var3) throws SQLException;

   int deleteFrameTemplate(@Param("template_id") Long var1) throws SQLException;

   long select_checkAvailableDiskSpace(@Param("programId") String var1) throws SQLException;

   long select2_checkAvailableDiskSpace(@Param("programId") String var1) throws SQLException;

   long select3_checkAvailableDiskSpace(@Param("programId") String var1, @Param("session_id") String var2, @Param("curr_date") String var3) throws SQLException;

   long select4_checkAvailableDiskSpace(@Param("programId") String var1) throws SQLException;

   long select5_checkAvailableDiskSpace(@Param("programId") String var1) throws SQLException;

   long select6_checkAvailableDiskSpace(@Param("programId") String var1) throws SQLException;

   long select7_checkAvailableDiskSpace(@Param("programId") String var1) throws SQLException;

   String getProgramIdByProgramName(@Param("programName") String var1) throws SQLException;

   int deleteFrameByFrameId(@Param("frameId") String var1) throws SQLException;

   int deleteContentScheduleByScheduleId(@Param("scheduleId") String var1) throws SQLException;

   int insert_addProgramWithBasicInformation(@Param("program") ProgramEntity var1) throws SQLException;

   int insert2_addProgramWithBasicInformation(@Param("program") ProgramEntity var1) throws SQLException;

   Map select_addProgramWithBasicInformation(@Param("deviceId") Long var1) throws SQLException;

   void delete_addProgramWithBasicInformation(@Param("defaultProgramId") String var1) throws SQLException;

   int update_addProgramWithBasicInformation(@Param("defaultProgramId") String var1) throws SQLException;

   void delete2_addProgramWithBasicInformation(@Param("deviceId") Long var1) throws SQLException;

   int insert3_addProgramWithBasicInformation(@Param("program_id") String var1, @Param("device_id") long var2) throws SQLException;

   int insert4_addProgramWithBasicInformation(@Param("frame") FrameEntity var1) throws SQLException;

   int insert5_addProgramWithBasicInformation(@Param("frame") FrameEntity var1, @Param("userGroupId") long var2) throws SQLException;

   int insert6_addProgramWithBasicInformation(@Param("logId") long var1, @Param("program") ProgramEntity var3, @Param("logProgramCreate") String var4) throws SQLException;

   int update_modifyProgramWithFrameAndHWControlAndContent(@Param("program") ProgramEntity var1) throws SQLException;

   int update2_modifyProgramWithFrameAndHWControlAndContent(@Param("program") ProgramEntity var1) throws SQLException;

   Map select_modifyProgramWithFrameAndHWControlAndContent(@Param("groupId") Long var1) throws SQLException;

   void delete_modifyProgramWithFrameAndHWControlAndContent(@Param("defaultProgramId") String var1) throws SQLException;

   int update3_modifyProgramWithFrameAndHWControlAndContent(@Param("defaultProgramId") String var1) throws SQLException;

   void delete2_modifyProgramWithFrameAndHWControlAndContent(@Param("groupId") Long var1) throws SQLException;

   int insert_modifyProgramWithFrameAndHWControlAndContent(@Param("program_id") String var1, @Param("groupId") Long var2) throws SQLException;

   int update4_modifyProgramWithFrameAndHWControlAndContent(@Param("frame") FrameEntity var1) throws SQLException;

   int insert2_modifyProgramWithFrameAndHWControlAndContent(@Param("frame") FrameEntity var1, @Param("userGroupId") Long var2) throws SQLException;

   int update5_modifyProgramWithFrameAndHWControlAndContent(@Param("schedule") ContentsScheduleEntity var1) throws SQLException;

   Map select2_modifyProgramWithFrameAndHWControlAndContent(@Param("schedule") ContentsScheduleEntity var1) throws SQLException;

   int update6_modifyProgramWithFrameAndHWControlAndContent(@Param("schedule") ContentsScheduleEntity var1, @Param("priority") long var2) throws SQLException;

   int insert3_modifyProgramWithFrameAndHWControlAndContent(@Param("logId") long var1, @Param("program") ProgramEntity var3, @Param("logProgramEdit") String var4) throws SQLException;

   int insert_addProgramWithFrameAndHWControlAndContent(@Param("program") ProgramEntity var1) throws SQLException;

   int insert2_addProgramWithFrameAndHWControlAndContent(@Param("program") ProgramEntity var1) throws SQLException;

   Map select_addProgramWithFrameAndHWControlAndContent(@Param("deviceGroupId") Long var1) throws SQLException;

   void delete_addProgramWithFrameAndHWControlAndContent(@Param("defaultProgramId") String var1) throws SQLException;

   int update_addProgramWithFrameAndHWControlAndContent(@Param("defaultProgramId") String var1) throws SQLException;

   void delete2_addProgramWithFrameAndHWControlAndContent(@Param("deviceGroupId") Long var1) throws SQLException;

   int insert3_addProgramWithFrameAndHWControlAndContent(@Param("program_id") String var1, @Param("deviceGroupId") Long var2) throws SQLException;

   int insert4_addProgramWithFrameAndHWControlAndContent(@Param("frame") FrameEntity var1) throws SQLException;

   int insert5_addProgramWithFrameAndHWControlAndContent(@Param("frame") FrameEntity var1, @Param("userGroupId") Long var2) throws SQLException;

   Map select2_addProgramWithFrameAndHWControlAndContent(@Param("schedule") ContentsScheduleEntity var1) throws SQLException;

   int insert6_addProgramWithFrameAndHWControlAndContent(@Param("schedule") ContentsScheduleEntity var1, @Param("priority") long var2) throws SQLException;

   int insert7_addProgramWithFrameAndHWControlAndContent(@Param("logId") long var1, @Param("program") ProgramEntity var3, @Param("logProgramCreate") String var4) throws SQLException;

   int update_modifyProgramWithBasicInformation(@Param("program") ProgramEntity var1) throws SQLException;

   int update2_modifyProgramWithBasicInformation(@Param("program") ProgramEntity var1) throws SQLException;

   void delete_modifyProgramWithBasicInformation(@Param("program_id") String var1) throws SQLException;

   Map select_modifyProgramWithBasicInformation(@Param("deviceGroupId") Long var1) throws SQLException;

   void delete2_modifyProgramWithBasicInformation(@Param("defaultProgramId") String var1) throws SQLException;

   int update3_modifyProgramWithBasicInformation(@Param("defaultProgramId") String var1) throws SQLException;

   void delete3_modifyProgramWithBasicInformation(@Param("deviceGroupId") Long var1) throws SQLException;

   int insert_modifyProgramWithBasicInformation(@Param("program_id") String var1, @Param("deviceGroupId") Long var2) throws SQLException;

   int insert2_modifyProgramWithBasicInformation(@Param("logId") long var1, @Param("program") ProgramEntity var3, @Param("logProgramEdit") String var4) throws SQLException;

   Map select_addContentScheduleWithoutTemp(@Param("schedule") ContentsScheduleEntity var1) throws SQLException;

   int insert_addContentScheduleWithoutTemp(@Param("schedule") ContentsScheduleEntity var1, @Param("priority") long var2, @Param("playerSingleMode") String var4) throws SQLException;

   int insert_addHWConstraint(@Param("schedule") ContentsScheduleEntity var1, @Param("defaultChannelNo") int var2) throws SQLException;

   int update_modifyContentScheduleWithoutTemp(@Param("schedule") ContentsScheduleEntity var1) throws SQLException;

   Map select_modifyContentScheduleWithoutTemp(@Param("schedule") ContentsScheduleEntity var1) throws SQLException;

   int update2_modifyContentScheduleWithoutTemp(@Param("schedule") ContentsScheduleEntity var1, @Param("priority") long var2) throws SQLException;

   int update_modifyHWConstraint(@Param("schedule") ContentsScheduleEntity var1) throws SQLException;

   List getContentScheduleIdByFrameId(@Param("frameId") String var1) throws SQLException;

   int insert_addFrameWithoutTemp(@Param("frame") FrameEntity var1) throws SQLException;

   int insert2_addFrameWithoutTemp(@Param("frame") FrameEntity var1, @Param("userGroupId") long var2) throws SQLException;

   Long getFrameIndexByFrameId(@Param("frameId") String var1) throws SQLException;

   String getDeviceTypeByProgramId(@Param("programId") String var1) throws SQLException;

   int setLinedataByProgramId(@Param("lineData") String var1, @Param("programId") String var2, @Param("channelNo") int var3) throws SQLException;

   int setLinedataToZeroFrame(@Param("programId") String var1, @Param("channelNo") int var2) throws SQLException;

   String getProgramIdByFrameId(@Param("frameId") String var1) throws SQLException;

   List getProgramIdByByScheduleId(@Param("scheduleId") String var1) throws SQLException;

   int updateDefaultProgramDeviceType(@Param("device_group_id") Long var1, @Param("device_type") String var2) throws SQLException;

   String getCreatorIdByProgramId(@Param("programId") String var1) throws SQLException;

   void deleteChannel(@Param("programId") String var1) throws SQLException;

   int copyChannelFromTmpToOriginTable(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   void deleteChannelTmp(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   int copyChannelFromOriginToTmpTable(@Param("programId") String var1, @Param("sessionId") String var2) throws SQLException;

   List selectChannelsWithProgramId(@Param("programId") String var1) throws SQLException;

   int insertIntoChannelTmpTable(@Param("newProgramId") String var1, @Param("sessionId") String var2, @Param("channel") ChannelEntity var3) throws SQLException;

   List selectSchedulesWithSheduleId(@Param("scheduleId") String var1) throws SQLException;

   int insertScheduleTmp(@Param("programId") String var1, @Param("sessionId") String var2, @Param("scheduleId") String var3, @Param("channelNo") int var4, @Param("schedule") ContentsScheduleEntity var5) throws SQLException;

   boolean deleteTempScheduleByChannelNo(@Param("programId") String var1, @Param("sessionId") String var2, @Param("channelNo") int var3) throws SQLException;

   boolean deleteTempFrameByChannelNo(@Param("programId") String var1, @Param("sessionId") String var2, @Param("channelNo") int var3) throws SQLException;

   List getChannels(@Param("programId") String var1) throws SQLException;

   long getTempFrameCountWithChannelNoCondition(@Param("programId") String var1, @Param("channelNo") int var2, @Param("sessionId") String var3) throws SQLException;

   boolean deleteFromMiCdsInfoChannelTmpWithSessionId(@Param("sessionId") String var1) throws SQLException;

   boolean deleteFromMiCdsInfoChannelTmpWithProgramId(@Param("programId") String var1) throws SQLException;

   boolean deleteAllChannelsTmp() throws SQLException;

   Float getDeviceTypeVersionByProgramId(@Param("programId") String var1) throws SQLException;

   List getProgramListBySchOrgId(@Param("tempGroupList") List var1) throws SQLException;

   boolean deleteFrameByChannelNo(@Param("programId") String var1, @Param("channelNo") int var2) throws SQLException;

   boolean deleteScheduleByChannelNo(@Param("programId") String var1, @Param("channelNo") int var2) throws SQLException;

   int update_setDefaultProgramId(@Param("programId") String var1, @Param("groupId") long var2) throws SQLException;

   int insert7_addProgramWithBasicInformation(@Param("channel") ChannelEntity var1) throws SQLException;

   List getEventList(@Param("scheduleId") String var1);

   List getEventListPaged(@Param("scheduleId") String var1, @Param("sort") String var2, @Param("direction") String var3, @Param("startPos") int var4, @Param("pageSize") int var5);

   Integer getEventCountByScheduleId(@Param("scheduleId") String var1);

   List getDownloadContentPagedListForEventSchedule(@Param("map") Map var1) throws SQLException;

   int getDownloadContentPagedListCountForEventSchedule(@Param("map") Map var1) throws SQLException;

   int modifyProgramDeviceTypeAndVersion(@Param("programId") String var1, @Param("deviceType") String var2, @Param("deviceTypeVersion") float var3) throws SQLException;

   int insertSyncScheduleMatchInfoTemp(@Param("syncschedule") SyncSchedule var1) throws SQLException;

   int insertSyncScheduleMatchInfo(@Param("scheduleId") String var1) throws SQLException;

   boolean deleteSyncScheduleMatchInfoTemp(@Param("scheduleId") String var1) throws SQLException;

   boolean deleteSyncScheduleMatchInfo(@Param("scheduleId") String var1) throws SQLException;

   List getSyncScheduleMatchInfoTemp(@Param("scheduleId") String var1) throws SQLException;

   List getSyncScheduleMatchInfo(@Param("scheduleId") String var1) throws SQLException;

   int countSyncScheduleMatchInfoTemp(@Param("scheduleId") String var1) throws SQLException;

   int countSyncScheduleMatchInfo(@Param("scheduleId") String var1) throws SQLException;

   List getScheduleIdListByProgramId(@Param("programId") String var1) throws SQLException;

   List getSyncGroupListPerSchedule(@Param("scheduleId") String var1) throws SQLException;

   List getSyncDeviceIdListPerSchedule(@Param("scheduleId") String var1, @Param("playlistId") String var2, @Param("syncPlayId") String var3) throws SQLException;

   List getScheduleDetailPublishStatusList(@Param("programId") String var1, @Param("device_group_id") long var2);

   List getContentListInSchedule(String var1);

   void addContentPublishData(@Param("entity") DetailDownloadContentEntity var1) throws SQLException;

   void deleteContentPublishData(@Param("programId") String var1) throws SQLException;

   int getContentPublishDataCount(@Param("entity") DetailDownloadContentEntity var1) throws SQLException;

   void updateContentPublishDataProgress(@Param("entity") DetailDownloadContentEntity var1) throws SQLException;

   List getSchedulePublishStatusList(@Param("programId") String var1, @Param("device_group_id") long var2);

   int getCheckContentPublishCount(@Param("programId") String var1);

   int addDynaminTagInfoTemp(@Param("entity") DynamicTagEntity var1) throws SQLException;

   int addDynaminTagInfoList(@Param("entity") DynamicTagEntity var1) throws SQLException;

   int addDynaminTagInfo(@Param("scheduleId") String var1, @Param("sessionId") String var2) throws SQLException;

   boolean deleteDynaminTagInfoTemp(@Param("scheduleId") String var1) throws SQLException;

   boolean deleteDynaminTagInfo(@Param("scheduleId") String var1) throws SQLException;

   List getDynaminTagInfo(@Param("scheduleId") String var1) throws SQLException;

   List getDynaminTagInfoTemp(@Param("sessionId") String var1, @Param("scheduleId") String var2) throws SQLException;

   boolean deleteDynaminTagInfoTempWithSession(@Param("sessionId") String var1) throws SQLException;

   int transferToDynaminTagInfoTemp(@Param("scheduleId") String var1, @Param("sessionId") String var2) throws SQLException;

   List getTagListWithIsSync(@Param("scheduleId") String var1, @Param("playlistId") String var2, @Param("versionId") long var3) throws SQLException;

   List getTagListForContent(@Param("scheduleId") String var1, @Param("playlistId") String var2, @Param("syncPlayId") String var3) throws SQLException;

   int transferToDynaminTagInfoTempWithNewId(@Param("scheduleId") String var1, @Param("newScheduleId") String var2, @Param("sessionId") String var3) throws SQLException;

   void delete1_deleteProgramDataWithId(@Param("programId") String var1) throws SQLException;

   void delete2_deleteProgramDataWithId(@Param("programId") String var1) throws SQLException;

   void delete3_deleteProgramDataWithId(@Param("programId") String var1) throws SQLException;

   void deleteAdSlotList(@Param("programId") String var1) throws SQLException;

   void deleteAdScheduleList(@Param("programId") String var1) throws SQLException;

   boolean deleteAllChannelsWithId(@Param("programId") String var1) throws SQLException;

   boolean addNewVersionProgram(ProgramEntity var1, String var2) throws SQLException;

   List getScheduleDetailProgress(@Param("programId") String var1) throws Exception;

   int getScheduleContentCount(@Param("programId") String var1, @Param("contentId") String var2) throws SQLException;

   List getDeletedProgramIdList(@Param("organization") String var1) throws SQLException;

   boolean updateProgramView(@Param("program") ProgramEntity var1) throws SQLException;

   boolean updateProgramGroupId(@Param("programId") String var1, @Param("groupId") long var2) throws SQLException;

   List getScheduleMappedContentTotalSize(@Param("programId") String var1, @Param("contentScheduleType") String var2) throws SQLException;

   FrameTemplateEntity getTemplateEntity(@Param("templateId") long var1) throws Exception;

   boolean insertSlot(@Param("slot") AdSlotEntity var1) throws SQLException;

   boolean insertAdSchedule(@Param("schedule") AdScheduleEntity var1) throws SQLException;

   List getAdSlotList(@Param("programId") String var1, @Param("frameId") String var2) throws SQLException;

   List getAdScheduleList(@Param("programId") String var1, @Param("slotId") String var2) throws SQLException;

   List getAdSlotListFromProgramId(@Param("programId") String var1) throws SQLException;

   Map getContentName(@Param("contentId") String var1) throws SQLException;

   boolean updateTemplate(@Param("template") FrameTemplateEntity var1) throws Exception;

   List getReserveScheduleList() throws Exception;

   List getProgramIdListForVWL() throws Exception;

   boolean updateProgramTypeFromProgramId(@Param("programId") String var1, @Param("programType") String var2) throws Exception;

   boolean updateProgramTypeFromProgramType(@Param("fromProgramType") String var1, @Param("toProgramType") String var2) throws Exception;

   boolean updateProgramTypeFromSyncPlay(@Param("programType") String var1) throws Exception;

   boolean updateProgramTypeFromAdvertisement(@Param("programType") String var1) throws Exception;

   int getCountProgram(@Param("programType") String var1) throws Exception;

   List getTagPlaylistIdVersion(@Param("programId") String var1, @Param("contentType") String var2) throws Exception;

   int getCountProgramIdByGroupId(@Param("groupId") long var1) throws Exception;

   List getContentListInScheduleWithStopDate(@Param("programId") String var1, @Param("stopDate") String var2) throws Exception;

   List getContentListInADScheduleWithStopDate(@Param("programId") String var1, @Param("stopDate") String var2) throws Exception;

   void deleteDownloadStatus(@Param("programId") String var1) throws SQLException;

   List getDynamicTagByScheduleIdIdAndPlaylistId(@Param("scheduleId") String var1, @Param("playlistId") String var2) throws SQLException;

   String getOrganiationByProgramId(@Param("programId") String var1) throws SQLException;

   long existsProgramId(@Param("programId") String var1) throws SQLException;

   ProgramEntity getProgramWithGroupIdAndNameByProgramId(@Param("programId") String var1) throws SQLException;

   int getCountScheduleToExpire(@Param("groupList") List var1, @Param("userId") String var2, @Param("stopDate") String var3, @Param("condition") SelectCondition var4) throws SQLException;

   int getDeviceCountByScheduleToExpire(@Param("groupList") List var1, @Param("userId") String var2, @Param("stopDate") String var3, @Param("condition") SelectCondition var4) throws SQLException;

   List getListScheduleToExpire(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("groupList") List var3, @Param("stopDate") String var4, @Param("condition") SelectCondition var5) throws SQLException;

   List getDeviceListByScheduleToExpire(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("groupList") List var3, @Param("stopDate") String var4, @Param("condition") SelectCondition var5) throws SQLException;

   Long getMaxPriorityByProgramId(@Param("programId") String var1) throws SQLException;

   Long getMinPriorityByProgramId(@Param("programId") String var1) throws SQLException;

   Long getPriorityByScheduleId(@Param("programId") String var1, @Param("scheduleId") String var2) throws SQLException;

   List getScheduleIdAndPriorityByProgramId(@Param("programId") String var1) throws SQLException;

   int updateSchedulePriorityByProgramId(@Param("programId") String var1, @Param("scheduleId") String var2, @Param("priority") Long var3) throws SQLException;

   List getProgramByExpiredContentId(@Param("contentId") String var1) throws SQLException;

   boolean deleteExpiredContentInProgram(@Param("programId") String var1, @Param("contentId") String var2) throws SQLException;

   boolean deleteExpiredContentInFrame(@Param("programId") String var1, @Param("channelNo") int var2, @Param("screenIndex") int var3, @Param("frameId") String var4, @Param("contentId") String var5) throws SQLException;

   boolean deleteExpiredContentInSchedule(@Param("scheduleId") String var1, @Param("contentId") String var2) throws SQLException;

   boolean deletePlaylistInSchedule(@Param("programType") String var1, @Param("scheduleId") String var2, @Param("playlistId") String var3) throws SQLException;

   List getPlaylistByExpiredContentId(@Param("contentId") String var1) throws SQLException;

   List getContentListByProgramId(@Param("programId") String var1) throws SQLException;

   List getScheduleByPlaylistId(@Param("playlistId") String var1) throws SQLException;

   List getContentIdByEventScheduleId(@Param("scheduleId") String var1) throws SQLException;

   List getFrameContentsByProgramId(@Param("programId") String var1) throws Exception;

   List getScheduleGroupBySearchText(@Param("searchText") String var1, @Param("organizationName") String var2, @Param("tableName") String var3) throws SQLException;

   List getParentsGroupList(@Param("pGroupId") int var1, @Param("tableName") String var2) throws SQLException;

   List getProgramCountByProgramType() throws SQLException;

   int deleteBgmContentInProgram(@Param("contentId") String var1) throws SQLException;
}
