package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.google.common.base.Optional;
import com.samsung.magicinfo.webauthor2.model.weather.CityData;
import com.samsung.magicinfo.webauthor2.model.weather.Country;
import com.samsung.magicinfo.webauthor2.model.weather.Language;
import com.samsung.magicinfo.webauthor2.service.WeatherWidgetDataService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/weather"})
public class WeatherWidgetDataQueryController {
  private WeatherWidgetDataService dataService;
  
  @Autowired
  public WeatherWidgetDataQueryController(WeatherWidgetDataService dataService) {
    this.dataService = dataService;
  }
  
  @GetMapping({"/country"})
  public HttpEntity<List<Country>> getCountryList(@RequestParam(required = false) String languageCode) {
    Optional<String> language = Optional.fromNullable(languageCode);
    List<Country> countries = this.dataService.getCountryList(language.isPresent() ? languageCode : "en");
    return (HttpEntity<List<Country>>)ResponseEntity.ok(countries);
  }
  
  @GetMapping({"/city"})
  public HttpEntity<List<CityData>> getCityList(@RequestParam int countryId, @RequestParam(required = false) String languageCode) {
    Optional<String> language = Optional.fromNullable(languageCode);
    List<CityData> cityList = this.dataService.getCityList(countryId, language.isPresent() ? languageCode : "en");
    return (HttpEntity<List<CityData>>)ResponseEntity.ok(cityList);
  }
  
  @GetMapping({"/language"})
  public HttpEntity<List<Language>> getLanguageList() {
    List<Language> languages = this.dataService.getLanguageList();
    return (HttpEntity<List<Language>>)ResponseEntity.ok(languages);
  }
  
  @ExceptionHandler({IllegalArgumentException.class})
  @ResponseBody
  public ResponseEntity<String> illegalArgumentException(IllegalArgumentException ex) {
    return ResponseEntity.badRequest().body(ex.getMessage());
  }
}
