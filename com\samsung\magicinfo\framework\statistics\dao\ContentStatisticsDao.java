package com.samsung.magicinfo.framework.statistics.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.statistics.entity.content.ContentStorageEntity;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.util.BeanUtils;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManager;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManagerImpl;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import org.apache.logging.log4j.Logger;

public class ContentStatisticsDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(ContentStatisticsDao.class);

   public ContentStatisticsDao() {
      super();
   }

   public List getRegisteredContentListByPeriod(String start_date, String end_date, List orgList) {
      List data_list = null;
      Timestamp startDate = Timestamp.valueOf(start_date + " 00:00:00");
      Timestamp endDate = Timestamp.valueOf(end_date + " 23:59:59");

      try {
         data_list = ((ContentStatisticsDaoMapper)this.getMapper()).getRegisteredContentListByPeriod(startDate, endDate, orgList);
      } catch (Exception var8) {
         this.logger.error("", var8);
      }

      return data_list;
   }

   public List getDistinctDateByPeriod(String start_date, String end_date) {
      List number = null;
      Timestamp startDate = Timestamp.valueOf(start_date + " 00:00:00");
      Timestamp endDate = Timestamp.valueOf(end_date + " 23:59:59");

      try {
         number = ((ContentStatisticsDaoMapper)this.getMapper()).getDistinctDateByPeriod(startDate, endDate);
      } catch (Exception var7) {
         this.logger.error("", var7);
      }

      return number;
   }

   public List getTotalRegisteredContent(int org_id) {
      List content_type = null;

      try {
         content_type = ((ContentStatisticsDaoMapper)this.getMapper()).getTotalRegisteredContent(org_id);
      } catch (Exception var4) {
         this.logger.error("", var4);
      }

      return content_type;
   }

   public List getFileTypeNCount() {
      List file_type_list = null;

      try {
         file_type_list = ((ContentStatisticsDaoMapper)this.getMapper()).getFileTypeNCount();
      } catch (Exception var3) {
         this.logger.error("", var3);
      }

      return file_type_list;
   }

   public List getMediaTypeList() {
      List object = null;

      try {
         object = ((ContentStatisticsDaoMapper)this.getMapper()).getMediaTypeList();
      } catch (Exception var3) {
         this.logger.error("", var3);
      }

      return object;
   }

   public List getFileTypeList(String media_type) throws SQLException {
      List object = null;

      try {
         object = ((ContentStatisticsDaoMapper)this.getMapper()).getFileTypeList(media_type);
      } catch (Exception var4) {
         this.logger.error("", var4);
      }

      return object;
   }

   public String getMediaTypeCode(String media_type_name) throws SQLException {
      return ((ContentStatisticsDaoMapper)this.getMapper()).getMediaTypeCode(media_type_name);
   }

   public String getMediaTypeName(String media_type_code) throws SQLException {
      return ((ContentStatisticsDaoMapper)this.getMapper()).getMediaTypeName(media_type_code);
   }

   public String getMediaTypeByFileType(String file_type) throws SQLException {
      return ((ContentStatisticsDaoMapper)this.getMapper()).getMediaTypeByFileType(file_type);
   }

   public Long getAllContentSizeByUserId(String user_id) throws SQLException {
      try {
         return ((ContentStatisticsDaoMapper)this.getMapper()).getAllContentSizeByUserId(user_id) == null ? 0L : ((ContentStatisticsDaoMapper)this.getMapper()).getAllContentSizeByUserId(user_id);
      } catch (Exception var3) {
         this.logger.error("", var3);
         return 0L;
      }
   }

   public List getContentStorageUsageList(String orgId) throws SQLException {
      List content_usage_list = new ArrayList();
      UserInfo userInfo = UserInfoImpl.getInstance();
      Object userList = new ArrayList();

      try {
         PIIDataManager piiDataManager = (PIIDataManagerImpl)BeanUtils.getBean("PIIDataManager");
         if (orgId.equalsIgnoreCase("AllOrg")) {
            userList = userInfo.getAllUserListByRootGroupId(0L);
         } else if (orgId.equals("0")) {
            userList = userInfo.getAllRootUserList();
         } else if (!orgId.equals("") && !orgId.isEmpty()) {
            Long org = Long.parseLong(orgId);
            userList = userInfo.getAllUserListByRootGroupId(org);
         }

         Iterator var11 = ((List)userList).iterator();

         while(var11.hasNext()) {
            User user = (User)var11.next();
            ContentStorageEntity data = new ContentStorageEntity();
            Long systemUsage = this.getAllContentSizeByUserId(user.getUser_id());
            data.setOrganization(user.getOrganization());
            data.setUser_group(user.getGroup_name());
            data.setUser_id(user.getUser_id());
            data.setUser_name(piiDataManager.decryptData(user.getUser_name()));
            data.setSystem_usage(systemUsage);
            if (!systemUsage.equals(0L)) {
               content_usage_list.add(data);
            }
         }
      } catch (Exception var10) {
         this.logger.error("", var10);
      }

      return content_usage_list;
   }
}
