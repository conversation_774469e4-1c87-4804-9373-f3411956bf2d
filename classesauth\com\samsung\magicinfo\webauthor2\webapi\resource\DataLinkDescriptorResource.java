package com.samsung.magicinfo.webauthor2.webapi.resource;

import com.samsung.magicinfo.webauthor2.model.datalink.DataLinkDescriptor;
import java.io.Serializable;
import org.springframework.hateoas.Link;
import org.springframework.hateoas.Resource;

public class DataLinkDescriptorResource extends Resource<DataLinkDescriptor> implements Serializable {
  private static final long serialVersionUID = -2166718728600109361L;
  
  public DataLinkDescriptorResource(DataLinkDescriptor content, Link... links) {
    super(content, links);
  }
  
  public DataLinkDescriptorResource(DataLinkDescriptor content, Iterable<Link> links) {
    super(content, links);
  }
}
