package com.samsung.magicinfo.webauthor2.xml.datalink;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SettingType", propOrder = {"pollingInterval", "backupForADay"})
public class SettingType {
  @XmlElement(name = "PollingInterval")
  protected long pollingInterval;
  
  @XmlElement(name = "KeepPreviousValue")
  protected long backupForADay;
  
  public long getPollingInterval() {
    return this.pollingInterval;
  }
  
  public void setPollingInterval(long value) {
    this.pollingInterval = value;
  }
  
  public long getBackupForADay() {
    return this.backupForADay;
  }
  
  public void setBackupForADay(long value) {
    this.backupForADay = value;
  }
}
