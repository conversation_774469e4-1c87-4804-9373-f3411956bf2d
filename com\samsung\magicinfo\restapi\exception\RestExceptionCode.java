package com.samsung.magicinfo.restapi.exception;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public enum RestExceptionCode {
   BAD_REQUEST("400000", "Bad Request"),
   BAD_REQUEST_PARAMETER_EMPTY("400001", "%s is required value."),
   BAD_REQUEST_PARAMETER_INVALID("400002", "%s is invalid value."),
   BAD_REQUEST_PARAMETER_MINVALUE("400003", "The %s must be greater than or equal to %s."),
   BAD_REQUEST_PARAMETER_MAXVALUE("400004", "The %s must be less than or equal to %s."),
   BAD_REQUEST_PARAMETER_SIZE("400005", "The %s size must be between %s and %s."),
   BAD_REQUEST_PARAMETER_PATTERN("400006", "The %s must match '%s'."),
   BAD_REQUEST_PARAMETER_NOT_ALLOW_PATTERN("400007", "The %s does not allowed pattern like '%s'."),
   BAD_REQUEST_PARAMETER_TYPE("400008", "The %s must be %s type."),
   BAD_REQUEST_SAME_ITEM_EXIST("400009", "The same %s exists."),
   BAD_REQUEST_NO_CHANGES("400010", "No changes have been made."),
   BAD_REQUEST_CONNECT_FAIL("400011", "The %s connection is failed."),
   BAD_REQUEST_RECYCLE_BIN_EMPTY("400012", "The recycle bin is already empty."),
   BAD_REQUEST_EXPIRED_CONTENT_NOT_ADD("400013", "The expired content cannot be added."),
   BAD_REQUEST_REQUESTBODY_INVALID("400014", "The value corresponding to RequestBody is invalid."),
   BAD_REQUEST_VALUE_NOT_SUPPORT("400015", "The %s is invalid. This value is not supported."),
   BAD_REQUEST_USER_NOT_EXIST("400016", "The login information does not exist for the user."),
   BAD_REQUEST_ITEM_ACCESS_DENIED("400017", "This %s does not belong to %s, so access is denied."),
   BAD_REQUEST_USED_ITEM_NOT_DELETE("400018", "The %s in use elsewhere cannot be deleted."),
   BAD_REQUEST_NOT_INCLUDE_IN_GROUP("400019", "The %s is not included in the group."),
   BAD_REQUEST_PARAMETER_NOT_NULL_OR_EMPTY("400020", "The %s value cannot be null or empty."),
   BAD_REQUEST_ENTER_NAME("400021", "Enter the %s name."),
   BAD_REQUEST_PARAMETER_INCORRECT("400022", "The %s is incorrect."),
   BAD_REQUEST_PARAMETER_INVALID_SPECIAL_CHARACTER1("400023", "You cannot enter an invalid special character (< > & ` ! ~ # @ % [ ] { } ; : + * ^ = ( ) \\\\\\\\ \\\\\" \\\\' ?)."),
   BAD_REQUEST_PARAMETER_INVALID_SPECIAL_CHARACTER2("400024", "You cannot enter an invalid special character (  , . < > & ` ! ~ # @ % [ ] { } ; : + * ^ = ( ) \\\\\\\\ \\\\\" \\\\' ?)."),
   BAD_REQUEST_CANNOT_DELETE_GROUP("400025", "You cannot delete this group."),
   BAD_REQUEST_CANNOT_NAME_WITH_SPACE("400026", "You cannot use a name that consists of spaces only."),
   BAD_REQUEST_INPUT_LENGTH_LIMIT("400027", "The maximum input length has been exceeded."),
   BAD_REQUEST_CANNOT_EDIT_ITEM("400028", "Cannot edit the %s as it is being edited by another user."),
   BAD_REQUEST_CHECK_INPUT_CHARACTER("400029", "Only alphanumeric characters, \"-\" and \"_\" are allowed. Special characters and spaces are not allowed."),
   BAD_REQUEST_INCORRECT_MOVE_GROUP("400030", "Move Group is incorrect."),
   BAD_REQUEST_CANNOT_EDIT_ANOTHER_USERS_GROUP("400031", "You cannot edit group of another user's ."),
   BAD_REQUEST_MULTI_BACKUP_NOT_WORK_SYNC("4000032", "Multi Backup does not work during Sync Play."),
   BAD_REQUEST_PARAMETER_NOT_SAMETIME("400033", "The parameter %s must not be entered at the same time."),
   BAD_REQUEST_CANNOT_DELETE_ROOT_GROUP("400034", "Cannot delete the root group."),
   BAD_REQUEST_CANNOT_DELETE_CONTAINS_REFERENCED_ITEMS_IN_GROUP("400035", "This group contains %s that is referenced elsewhere."),
   BAD_REQUEST_PARAMETER_TOGETHER("400036", "%s and %s must be used together."),
   BAD_REQUEST_CANNOT_DELETE_ORGANIZATION_DUE_TO_ONLY_TWO_ORG_IN_ORG_GROUP("400037", "You cannot delete organization due to minimum requirement needed for Multi Org Group"),
   BAD_REQUEST_USED_RULESET_CANNOT_TEMP_SAVE("400200", "You cannot temporarily save the ruleset in use on a schedule."),
   BAD_REQUEST_CANNOT_MOVE_GROUP_ORGANIZATION("400201", "You cannot move the organization."),
   BAD_REQUEST_HAVE_TO_MOVE_SAME_ORGANIZATION("400202", "You have to move the group at same organization."),
   BAD_REQUEST_SERVER_LOGIN_FAIL("400300", "You failed to login to the %s server. Please check your login information."),
   BAD_REQUEST_REMOTE_CONTENT_PARAMETER_INVALID("400301", "One or more parameter(s) are invalid in Remote Content."),
   BAD_REQUEST_CONTENT_EDIT_FAIL_WHILE_DOWNLOADING("400302", "You cannot edit while download is in progress."),
   BAD_REQUEST_REMOTE_DIRECTORY_INVALID("400303", "The remote directory is invalid."),
   BAD_REQUEST_CONTENT_UPLOAD_NOT_MULTIPART_REQUEST("400304", "The multipart type request is required, when uploading contents."),
   BAD_REQUEST_CONTENT_UPLOAD_NOT_SUPPORT_FILE("400305", "The %s file type does not support content upload."),
   BAD_REQUEST_FILE_CHECK_FOR_TEMPLATES("400306", "Only %s files can be saved as templates."),
   BAD_REQUEST_CANNOT_CONVERT_NOT_LFD_TYPE("400307", "Cannot be converted unless it is a LFD type."),
   BAD_REQUEST_CANNOT_CONNECT_REMOTE_SERVER("400308", "Failed to connect to the Remote Server."),
   BAD_REQUEST_NO_AVAILABLE_FILES("400309", "The available files do not exist in the server."),
   BAD_REQUEST_INVALID_PARAMS("400310", "One or more parameter(s) are invalid in Remote Content."),
   BAD_REQUEST_REMOTE_SERVER_LOGIN_FAIL("400311", "Failed to login to the remote server."),
   BAD_REQUEST_SAME_CONTENT_EXIST("400312", "The same content already exists in the Content List."),
   BAD_REQUEST_INVALID_PATH("400313", "Invalid directory path of remote server."),
   BAD_REQUEST_ACCESS_DENIED("400314", "Access denied."),
   BAD_REQUEST_FILE_CHECK_FOR_NOT_APPROVED_CONTENT("400315", "Only %s files can be saved as templates."),
   BAD_REQUEST_INVALID_TLFD_GROUP_ID("400316", "Can not find matched group with groupId: %s."),
   BAD_REQUEST_INVALID_PORT("400317", "Invalid port of remote server."),
   BAD_REQUEST_EXCEEDED_MAX_GROUP_NAME_LENGTH("400318", "Group name length exceeded allowable length(100 characters)."),
   BAD_REQUEST_CONTENT_ITEM_INVALID_VALUE("400319", "Only alphabets, numbers, '-', and '_' can be used."),
   BAD_REQUEST_CONTENT_UPLOAD_NOT_SUPPORTED_BY_AISR("400320", "The %s file type is not supported by AISR."),
   BAD_REQUEST_CONTENT_NOT_EXIST_FOR_UPDATE("400321", "Content does not exist for update"),
   BAD_REQUEST_CONTENT_NOT_SUPPORTED_FOR_DOWNLOAD("400322", "Content does not support for download, It supports only Image/Video file type"),
   BAD_REQUEST_FAIL_TO_DOWNLOAD_FILE("400323", "Fail to download file"),
   BAD_REQUEST_PLAYLIST_NOT_DELETE("400400", "You cannot delete the playlist : %s"),
   BAD_REQUEST_PLAYLIST_NOT_ACTIVE_INFO("400401", "There is no active version infomation for playlistId. : %s"),
   BAD_REQUEST_PLAYLIST_GROUP_MOVE_FAIL("400402", "The playlist group cannot move : %s"),
   BAD_REQUEST_CHECK_TIME_VALUE("400500", "Check the time field values."),
   BAD_REQUEST_LFD_CONTENT_NOT_PLAY_MULTI_FRAME_SCHEDULE("400501", "The LFD content is not played on the multi frame schedule."),
   BAD_REQEUST_ADVERTISEMENT_PROGRAM_HAVE_ONLY_SINGLE_CHANNEL("400502", "The ADVERTISEMENT type program can have only single channel."),
   BAD_REQUEST_ADVERTISEMENT_PROGRAM_HAVE_ONLY_SINGLE_FRAME("400503", "The ADVERTISEMENT type program can have only single frame."),
   BAD_REQUEST_SLOT_COUNT_NOT_MATCH("400504", "The slot count does not match the number of slot data entered in the list."),
   BAD_REQUEST_FRAME_ID_NOT_MATCH("400505", "The frame ID in the schedule data and the frame ID of the slot included in the frame data do not match."),
   BAD_REQUEST_SLOT_ID_NOT_MATCH("400506", "The slot ID in the frame data and the slot ID of the event included in the slot data do not match."),
   BAD_REQUEST_USED_EVENT_SCHEDULE_NOT_DELETE("400507", "You cannot delete the event while they are being used in a event schedule."),
   BAD_REQUEST_SLOT_COUNT_ITEM_COUNT_NOT_MATCH("400508", "The slotCount and count of items in adSlots array are not matched."),
   BAD_REQUEST_NO_POSSIBLE_DAY_OF_WEEK("400509", "There is no possible day of week from %s to %s."),
   BAD_REQUEST_NO_POSSIBLE_DATE("400510", "There is no possible date from %s to %s."),
   BAD_REQUEST_TIME_IS_OVERLAPPED("400511", "Time is overlapped. Check the date & time fields."),
   BAD_REQUEST_EXPIRED_CONTENT("400512", "Found expired content."),
   BAD_REQUEST_END_DATE_BEFORE_START_DATE("400513", "The startDate of event item must be the same as or set after the endDate of event."),
   BAD_REQUEST_FRAME_INFO_LIST_REQUIRED("400514", "frameInfos is required for CUSTOM type frame template."),
   BAD_REQUEST_INVALID_HORIZONTAL_LINE_INFO("400515", "orientationType and startY, endY are not matched.[item index: %s]"),
   BAD_REQUEST_INVALID_VERTICAL_LINE_INFO("400516", "orientationType and startX, endX are not matched.[item index: %s]"),
   BAD_REQUEST_INVALID_ORIENTATION_TYPE("400517", "Not supported orientationType value.[item index: %s]"),
   BAD_REQUEST_FIXED_TYPE_LINE_INFO_REQUIRED("400518", "If the templateType is FIXED, a lineInfos value is required."),
   BAD_REQUEST_FIXED_TYPE_LINE_INFO_ITEM_COUNT_EXCEEDED("400519", "lineInfos arrays can contain up to three line information."),
   BAD_REQUEST_GROUP_ADD("400520", "Cannot add this group to the selected location."),
   BAD_REQUEST_SUB_FRAME_CANNOT_HAS_INPUT_SOURCE("400521", "A sub frame cannot has input source."),
   BAD_REQUEST_VIDEO_WALL_CANNOT_HAS_BACKGROUND_MUSIC("400522", "A Video Wall Schedule cannot has a background music."),
   BAD_REQUEST_SUB_FRAME_CANNOT_HAS_RULESET("400523", "A sub frame cannot has a ruleset."),
   BAD_REQUEST_CANNOT_USE_GENERAL_PLAYLIST_IN_VIDEOWALL_MODE("400524", "Cannot use a General Playlist in VWL Mode"),
   BAD_REQUEST_TARGET_LIMIT_EXCEED("400600", "More than %s device IDs have been entered."),
   BAD_REQUEST_NOT_MOVE_TO_VWL_GROUP("400601", "Cannot move device to VWL group. Cancel VWL group first."),
   BAD_REQUEST_FILE_SIZE_EXCEED("400602", "The size of %s is exceed."),
   BAD_REQUEST_ALL_DEVICE_NOT_CONNECT("400603", "It is possible when all devices are connected."),
   BAD_REQUEST_FILE_COUNT_EXCEED("400604", "There are too many files. The maximum number of files is %s."),
   BAD_REQUEST_VWL_MAPPED_GROUP_NOT_DELETE("400605", "You cannot delete VWL file mapped with group."),
   BAD_REQUEST_CHECK_PRIORITY("400606", "You need to check the priority value of the device and the device group."),
   BAD_REQUEST_LICENSE_QUANTITY_EXCEED("400607", "The license quantity exceeded."),
   BAD_REQUEST_NOT_APPROVAL_IN_VWL_GROUP("400608", "The device belongs to VWL group, so cannot be approved."),
   BAD_REQUEST_NOT_MOVE_TO_SYNC_PLAY_GROUP("400609", "The group ID to be moved is a sync play group, so cannot be moved."),
   BAD_REQUEST_SOFTWARE_FILE_UPLOAD_NOT_SUPPORT_FILE("400610", "This file type does not support software upload."),
   BAD_REQUEST_DEVICE_APPROVAL_EXCEED("400611", "The device approval maximum exceeded."),
   BAD_REQUEST_DEVICE_GROUP_NOT_EXIST("400612", "The device group does not exist."),
   BAD_REQUEST_DEVICE_MODE("400613", "The parameter videowallmode and syncMode cannot be true at the same time."),
   BAD_REQUEST_DEVICE_HOLIDAY_COUNT("400614", "The maximum number of holidays you can set is 20."),
   BAD_REQUEST_DEVICE_MODEL_INVALID("400615", "The model you selected cannot perform this function."),
   BAD_REQUEST_BACKUP_DEVICE_DELETE_FAIL("400616", "Backup Player device cannot be deleted. If you want delete this device, please clear Backup Player on the group."),
   BAD_REQUEST_BACKUP_DEVICE_MOVE_FAIL("400617", "Backup Player device cannot be moved. if you want move this device, please clear Backup Player on the group."),
   BAD_REQUEST_DATALINK_SERVER_LICENSE_EXCEED("400618", "Cannot register any more datalink server. Already exceeded the limit of license."),
   BAD_REQUEST_CANNOT_SET_CURRENT_TIME("400619", "The current time cannot be set for the equipment now. You should set the time zone."),
   BAD_REQUEST_CANNOT_ADD_DEVICE_TO_VWL_GROUP("400620", "Cannot add device to Video wall group."),
   BAD_REQUEST_CANNOT_MOVE_IN_VWL_GROUP_DEVICE("400621", "Device in VWL group cannot move. Cancel VWL group first."),
   BAD_REQUEST_DEVICE_GROUP_TYPE_DIFFERENT("400622", "The device group type is different."),
   BAD_REQUEST_DEVICE_IN_SYNCPLAY_GROUP("400623", "The device belongs to sync play group."),
   BAD_REQUEST_REDUNDANCY_GROUP("400624", "It is a redundancy group."),
   BAD_REQUEST_LOW_PRIORITY_DEVICE("400625", "It is a low priority device."),
   BAD_REQUEST_CANNOT_DELETE_REDUNDANCY_DEVICE("400626", "Cannot delete a redundancy device."),
   BAD_REQUEST_CANNOT_DELETE_VWL_DEVICE("400627", "Cannot delete a vwl device."),
   BAD_REQUEST_SERVER_URL_KNOWN_HOST("400628", "Unknown host in server url."),
   BAD_REQUEST_SERVER_URL_NO_PROTOCOL("400629", "No protocol in server url."),
   BAD_REQUEST_DEVICE_TYPE_GROUP_TYPE_DIFFERENT("400630", " The device type for the requested device ID is different from the device type of the group to which the device belongs."),
   BAD_REQUEST_DEVICE_NO_APPLIED_PRESET_CONFIG("400631", "No preset-configuration applied to the device. Please check if the applied preset-configuration item is a supported feature on the device."),
   BAD_REQUEST_NO_DEVICE_AVAILABLE_FOR_REMOTE_CONTROL("400632", "There is no device available for remote control."),
   BAD_REQUEST_PUBLISH_TIME_ERROR("400633", "Check Publish time."),
   BAD_REQUEST_DEVICE_NOT_APPROVED_UNAUTHORIZED("400634", "Device not approved. Access Denied."),
   BAD_REQUEST_INVALID_SYSTEM_RESTART_INTERVAL("400636", "Invalid value of System Restart Interval."),
   BAD_REQUEST_ENTER_ID("400700", "Enter the user ID."),
   BAD_REQUEST_ENTER_NEW_PASSWORD("400701", "Enter your new password."),
   BAD_REQUEST_ENTER_NEW_PASSWORD_AGAIN("400702", "Enter your new password again for confirmation.."),
   BAD_REQUEST_PASSWORD_NOT_MATCH("400703", "The passwords do not match. Please try again."),
   BAD_REQUEST_PASSWORD_NOT_INCLUDE_USER_ID("400704", "Your password cannot include your user ID."),
   BAD_REQUEST_PASSWORD_NOT_REPEAT("400705", "For your password, you cannot use a three digit serial number or repeat the same character three or more times."),
   BAD_REQUEST_NEW_PASSWORD_NOT_SAME_OLD("400706", "New password cannot be the same as old password."),
   BAD_REQUEST_PASSWORD_NOT_INCLUDE_PHONE_NUMBER("400707", "Your password cannot include your phone number."),
   BAD_REQUEST_PASSWORD_NOT_USE_RECENTLY("400708", "Recently used passwords cannot be used."),
   BAD_REQUEST_NOT_BLANK_SPACE("400709", "Blank space not allowed."),
   BAD_REQUEST_NOT_USE_LDAP("400710", "This organization does not use the LDAP."),
   BAD_REQUEST_CONNECT_ONLY_SERVER_ADMIN("400711", "Only Server Administrator can connect."),
   BAD_REQUEST_NOT_MOVE_TO_CURRENT_ORGANIZATION("400712", "You cannot move to current organization."),
   BAD_REQUEST_USED_ROLE_NOT_DELETE("400713", "Unable to delete the role as it is being used by a user(s)."),
   BAD_REQUEST_DEFAULT_ROLE_NOT_DELETE("400714", "You cannot delete the default roles."),
   BAD_REQUEST_ADMIN_ROLE_NOT_CHANGE("400715", "You cannot change the user role of the administrator."),
   BAD_REQUEST_REQUIRE_AN_ADMIN_IN_ORGANIZATION("400716", "At least one administrator is required for an organization."),
   BAD_REQUEST_NOT_CREATE_NEW_USER("400717", "The login user cannot create new user."),
   BAD_REQUEST_ASSIGN_RULEMANAGER_ONLY_ADMIN_GROUP("400718", "You can assign Rulemanager ability only for the Administrators Group."),
   BAD_REQUEST_YOURSELF_NOT_DELETE("400719", "Unable to delete yourself."),
   BAD_REQUEST_ADMIN_NOT_TRANSFER("400720", "The user ID does not exist or you cannot transfer administrator privileges to the user ID."),
   BAD_REQUEST_SELECT_MORE_THAN_2_ORGANIZATIONS("400721", "Please select at least 2 organizations to create an Organization Group."),
   BAD_REQUEST_ENTER_CONFIRM_PASSWORD("400722", "Enter the confirm password."),
   BAD_REQUEST_PASSWORD_LENGTH_RANGE("400723", "A password must be 10 to 50 characters long."),
   BAD_REQUEST_NOT_MATCH_NEW_N_CONFIRM_PASSWORD("400724", "The confirmation password must be identical with the new password."),
   BAD_REQUEST_USERID_IS_DUPLICATED("400725", "The entered User ID already exists."),
   BAD_REQUEST_USERID_NOT_ALLOW_PATTERN("400726", "User ID only contain alphanumeric characters and periods(.)"),
   BAD_REQUEST_PASSWORD_10_TO_50_ALPHANUMBRRIC("400727", "Choose a password of 10 to 50 alphanumeric characters, or a password of 8 to 50 characters, using a combination of letters, numbers and symbols."),
   BAD_REQUEST_PASSWORD_10_TO_50_COMBINATION("400728", "Use a password of 10 to 50 characters, using a combination of letters and numbers."),
   BAD_REQUEST_USER_PW_AUTH_CODE_NOT_MATCH("400729", "The authentication code do not match."),
   BAD_REQUEST_ORGANIZAION_NAME_NOT_ROOT("400730", "organization name cannot be ROOT."),
   BAD_REQUEST_PASSWORD_DONOT_MATCH("400731", "The passwords do not match."),
   BAD_REQUEST_ORGANIZATION_MOVE_NOT_REJECT("400732", "Organizational move cannot be rejected."),
   BAD_REQUEST_CANNOT_CHANGE_ADMINISTRATIR_ROLE("400733", "You cannot change the user role of the administrator."),
   BAD_REQUEST_EMAIL_INVALID_FORMAT("400734", "Invalid e-mail address format. Enter a correct e-mail address."),
   BAD_REQUEST_CANNOT_DELETE_DEFAULT_ROLE("400735", "You cannot delete the default roles."),
   BAD_REQUEST_CANNOT_EDIT_DEFAULT_ROLE("400736", "You cannot edit the default roles."),
   BAD_REQUEST_USER_ID_NOT_AVAILABLE("400737", "The user ID you entered is already in use.<br>Please enter a different ID."),
   BAD_REQUEST_SELECTED_GROUP_NOT_USE_LDAP("400738", "The selected group does not use LDAP."),
   BAD_REQUEST_NOT_USE_LDAP_SYNC("400739", "The selected group is a part of the organization not using the LDAP sync."),
   BAD_REQUEST_EXPIRED_PASSWORD("400740", "Your password has expired. Please change your password."),
   BAD_REQUEST_INVALID_NEW_ADMIN("400741", "Check whether the administrator ID of the new organization is valid."),
   BAD_REQUEST_SERVER_ADMIN_CANNOT_APPLIED_SUB_ORGANIZATION_USERS("400742", "The server administrator role cannot be applied to sub-organization users."),
   BAD_REQUEST_SERVER_ADMIN_ORGANIZATION_NAME_N_GROUP_NAME_ROOT("400743", "The organization name and group name of users who are Server Administrator must be 'ROOT'."),
   BAD_REQUEST_SERVER_ADMIN_CANNOT_HAVE_ORGANIZATION_GROUP("400744", "Users who are Server Administrator cannot have organization group."),
   BAD_REQUEST_PASSWORD_IS_INCORRECT("400745", "Current password is incorrect."),
   BAD_REQUEST_USERID_LENGTH_INCORRECT("400746", String.format("The user id must be %d-%d characters", 3, 64)),
   BAD_REQUEST_MAIN_SERVER_NOT_DELETE("400800", "The main server cannot be deleted."),
   BAD_REQUEST_SERVER_NAME_ALREADY_IN_USE("400801", "The server name you entered is already in use. Enter a different server name."),
   BAD_REQUEST_RUNNING_SERVER_NOT_DELETE("400802", "You cannot delete a running server. Please remove after stopping."),
   BAD_REQUEST_SLM_SERVER_NOT_AVAILABLE("400803", "Communicating with SLM server is not available now. Please check the network. Please try using Offline Activation."),
   BAD_REQUEST_SAME_TYPE_LICENSE("400804", "The license of the same type can be used only once. You need go to the SLM site to build up using the Extension menu. Please merge the license keys."),
   BAD_REQUEST_NOT_AVAILABLE_SSL("400805", "The error is because the SSL communication is not available. Please check whether the SSL 443 port is blocked. Please try using Offline Activation."),
   BAD_REQUEST_LICENSE_KEY_USED_IN_ANOTHER_PC("400806", "The license key is being used in another PC. Please carry out Return license on the PC that was previously in use, and then check whether the license has been returned to the SLM server."),
   BAD_REQUEST_WRONG_LICENSE_KEY("400807", "The License Key that you input is wrong. Please input a correct value."),
   BAD_REQUEST_LICENSE_IN_USED("400808", "The License is being used."),
   BAD_REQUEST_NOT_MATCH_LICENSE_KEY_N_SELECTED_PRODUCT("400809", "The license key is not for the selected product."),
   BAD_REQUEST_WRONG_TIME_RANGE("400810", "Please enter the time rightly hour between 0 and 12."),
   BAD_REQUEST_NO_SMTP_INFORMATION("400811", "No SMTP information in the server settings."),
   BAD_REQUEST_ADMIN_NOT_WITHDRAW("400812", "Unable to withdraw the administrator."),
   BAD_REQUEST_PRODUCT_NOT_SUPPORT("400813", "Does not support the product."),
   BAD_REQUEST_FREE_LICENSE_NOT_DELETE("400814", "The free license could not be deleted."),
   BAD_REQUEST_NO_ACTIVATION_TO_UPDATE("400815", "There is no Activation data updated. If you changed the amount while having 'Additional Activation', please click the 'Reactivation' button of the License Key on the SLM server and try the Activation process in the MagicInfo server again."),
   BAD_REQUEST_SLM_COMMON_ERROR("400816", "Please contact the SLM license. Error Code : %s"),
   BAD_REQUEST_HW_KEY_INVALID("400817", "H/W Unique Key is invalid."),
   BAD_REQUEST_FREE_LICENSE_NO_ADDITIONAL("400818", "Free license could not be added."),
   BAD_REQUEST_DEACTIVATION_KEY_INVALID("400819", "Failed to delete a license. (Deactivation key is invalid)"),
   BAD_REQUEST_NO_MORE_FREE_LICENSE("400820", "Cannot issue free license anymore."),
   BAD_REQUEST_TAG_NAME_DUPLICATED("400821", "Duplicate name exists."),
   BAD_REQUEST_FAIL_FILE_DOWNLOAD("400822", "Fail to download the file."),
   BAD_REQUEST_CANNOT_DELETE_ORGANIZATION("400823", "Cannot delete organization(s)"),
   BAD_REQUEST_CANNOT_EDIT_ORGANIZATION("400824", "Cannot edit organization(s)"),
   BAD_REQUEST_EXTERNAL_LINK_ID_DUPLICATED("400825", "Duplicate id exists."),
   BAD_REQUEST_NOT_EXIST_ORGANIZATION_ID("400826", "The organization id does not exist."),
   BAD_REQUEST_NOT_EXIST_SLM_LICENSE_PRODUCT_CODE("400827", "The product code does not exist."),
   BAD_REQUEST_CANNOT_ASSIGN_NUMBER_OF_LICENSE_TO_ORGANIZATION("400828", "The number of licenses you want to assign is greater than the number of licenses that can be allocated."),
   BAD_REQUEST_CANNOT_SET_MAX_LICENSE_COUNT("400829", "maxLicenseCount must be 0 if isAssigned is false."),
   BAD_REQUEST_MUST_SET_MAX_LICENSE_COUNT("400830", "If isAssigned is true, maxLicenseCount must be set to at least 0."),
   BAD_REQUEST_CANNOT_USE_PRODUCT_CODE_AS_A_INDEX_NAME("400831", "Cannot use \"Product Code\" as a index name"),
   BAD_REQUEST_CANNOT_DELETE_PRODUCT_CODE("400832", "Cannot delete \"Product Code\""),
   BAD_REQUEST_SLM_CANNOT_ACTIVATE_MORE_LICENSE("400833", "You cannot activate more of the same product type licenses."),
   BAD_REQUEST_NO_NAME_INSIGHT_INDEX("400834", "No name of Insight Index."),
   BAD_REQUEST_CANNOT_SET_ROLE_SERVER_ADMIN("400835", "Cannot set role as Administrator"),
   BAD_REQUEST_INVALID_URL("400836", "Invalid Link URL"),
   BAD_REQUEST_INVALID_NAME("400837", "Invalid Link Name"),
   BAD_REQUEST_DEVICE_AUTHENTICATION_FAILED("400900", "Device FTP authentication failed. Check device ID or Password."),
   BAD_REQUEST_EDGE_SERVER_NOT_FOUND("400901", "The requested EdgeServer is not found."),
   BAD_REQUEST_EDGE_SERVER_LIST_NOT_FOUND("400902", "EdgeServer(s) not found."),
   BAD_REQUEST_EDGE_SERVER_HOST_NAME_LIST_NOT_FOUND("400903", "EdgeServer hostName(s) not found"),
   BAD_REQUEST_EDGE_SERVER_NEED_LICENSE("400904", "Activate Edge Server License."),
   BAD_REQUEST_EDGE_SERVER_WRONG_SERVER_NAME("400905", "Inappropriate server name."),
   BAD_REQUEST_EDGE_SERVER_CANNOT_DELETE_MASTER_WITH_SLAVE("400906", "Cannot delete master servers with slave(s)"),
   BAD_REQUEST_EDGE_SERVER_CANNOT_CHANGE_MASTER_TO_SLAVE_WHEN_SLAVE_PRESENT("400907", "Cannot change master server with slave(s) to slave server"),
   BAD_REQUEST_REMOTE_SERVER_NOT_FOUND("400908", "The requested remote server is not found."),
   UNAUTHORIZED("401000", "Unauthorized"),
   UNAUTHORIZED_NOT_FOUND_USER_DETAIL("401001", "You are unauthorized. Access denied."),
   UNAUTHORIZED_LOGIN_FAIL("401002", "Login failed."),
   UNAUTHORIZED_TOKEN_EXPIRED("401003", "Token is expired."),
   UNAUTHORIZED_REFRESH_TOKEN_EXPIRED("401004", "Refresh token is expired."),
   UNAUTHORIZED_TOKEN_INVALID("401005", "Token is invalid."),
   UNAUTHORIZED_REFRESH_TOKEN_INVALID("401006", "Refresh token is invalid."),
   UNAUTHORIZED_USER_LOCKED("401007", "The account is locked"),
   UNAUTHORIZED_USER_NOT_APPROVED("401008", "The account is not approved"),
   UNAUTHORIZED_TOKEN_EXPIRED_API_TIMEOUT("401009", "Token is expired. API expiration timeout."),
   UNAUTHORIZED_REQUIRED_OTP_VALUE("401010", "You are unauthorized. Access denied."),
   UNAUTHORIZED_REQUIRED_OTP_SETTING("401011", "You are unauthorized. Access denied."),
   UNAUTHORIZED_HOTP_INVALID("401012", "You are unauthorized. Access denied."),
   UNAUTHORIZED_TOTP_INVALID("401013", "You are unauthorized. Access denied."),
   UNAUTHORIZED_TOTP_WARNING("401014", "You are unauthorized. Access denied."),
   UNAUTHORIZED_TOTP_LOCK("401015", "You are unauthorized. Access denied."),
   WRONG_FORMAT_OTP_VALUE("401016", "You are unauthorized. Access denied."),
   FAIL_TO_DEPLOY_SOFTWARE_FOR_WRONG_TIME("400633", "Check publish time"),
   FORBIDDEN("403000", "Forbidden"),
   FORBIDDEN_PERMISSION_DENIED("403001", "You are not allowed permission."),
   FORBIDDEN_ACCESS_DENIED("403002", "You are access denied."),
   FORBIDDEN_PERMISSION_NOT_HAVE("403003", "You do not have %s permission."),
   FORBIDDEN_SHARE_FOLDER_MU("403300", "The user ID does not have permission. (No organization belongs to)"),
   FORBIDDEN_CANNOT_MOVE_GROUP_CONTENTS_OF_ANOTHER_USER("403301", "You cannot edit group of another user's content."),
   FORBIDDEN_USER_ID_MU("403700", "The user ID does not have permission. (It belongs to multi organizations or has an Administrator role or has a Server Administrator or does not have device read permission.)"),
   FORBIDDEN_USER_NOT_ID_MU("403701", "The user ID does not have permission. (Does not belong to multiple organizations.)"),
   FORBIDDEN_ONLY_SERVER_ADMIN_CAN_CONTROL("403702", "Only Server Administrator can control it."),
   FORBIDDEN_ADMINISTRATOR_ORGANIZATION_NOT_MOVE("403703", "You cannot move the organization of administrator."),
   FORBIDDEN_SERVER_ADMINISTRATOR_NOT_DELETE("403704", "You cannot delete a server administrator."),
   FORBIDDEN_NOT_SERVER_ADMINISTRATOR("403705", "You are not a server administrator."),
   FORBIDDEN_ADMINISTRATOR_NOT_DELETE("403706", "You cannot delete an administrator."),
   FORBIDDEN_CANNOT_CHANGE_ADMINISTRATOR_GROUP("403707", "You cannot change the user group of the administrator group."),
   FORBIDDEN_CANNOT_CHANGE_ADMINISTRATOR_ORGANIZATION("403709", "You cannot change the user organization of the administrator."),
   FORBIDDEN_CANNOT_CHANGE_ORGANIZATION_USER_TYPE("403710", "You cannot change the organization of this user type."),
   DATA_NOT_FOUND("404000", "Data not found"),
   DATA_NOT_FOUND_FORMAT("404001", "The %s is not found."),
   INVALID_DETAILS("404002", "Invalid user email combination"),
   DATA_NOT_FOUND_THUMBNAIL_HOME_ON_CONFIG("404300", "The THUMBNAIL_HOME is not found on config data."),
   DATA_NOT_FOUND_FTP_AVAILABLE_FILES("404301", "The available files do not exist in the server."),
   DATA_NOT_FOUND_FTP_INVALID_PATH("404302", "The available files do not exist in the directory of remote server or invalid path."),
   DATA_NOT_FOUND_CONTENT_LIST_MATCH_PLAYLIST_ID("404500", "No content list can be found that matches the playlist ID."),
   DATA_NOT_FOUND_PLAYLIST_MATCH_CONTENT_ID_IN_EVENT_SCHEDULE("404501", "No playlist can be found that matches the content ID of event schedule."),
   DATA_NOT_FOUND_TEMPLATE_ID_MATCH_USER_ID_IN_SCHEDULE("404502", "Cannot find matched user-defined frame template with templateId."),
   DATA_NOT_FOUND_FRAME_LIST_MATCH_PROGRAM_ID("404503", "Cannot find matched frame list with programId."),
   DATA_NOT_FOUND_DEVICE_GROUP_INFO_IN_ORGANIZATION("404600", "The device group information for your organization does not exist."),
   DATA_NOT_FOUND_LDAP_ORGANIZATION("404700", "The LDAP organization is not obtained."),
   DATA_NOT_FOUND_PW_TOKEN("404701", "The authentication token is not found."),
   DATA_NOT_FOUND_MU_ORGANIZATION("404702", "It has multiple organizations but is not included in the user's organization ID."),
   DATA_NOT_FOUND_EXTERNAL_LINK("404800", "External Link Not Found"),
   METHOD_NOT_ALLOWED("405000", "Method not allowed"),
   NOT_ACCEPTABLE("406000", "Not Acceptable"),
   REQUEST_TIMEOUT("408000", "Request timeout"),
   REQUEST_TIMEOUT_SERVICE_RESPONSE("408001", "It is timed out the service response."),
   REQUEST_TIMEOUT_FTP_CONNECTION("408300", "It is timed out the FTP connection."),
   REQUEST_TIMEOUT_CIFS_CONNECTION("408301", "It is timed out the CIFS connection."),
   REQUEST_TIMEOUT_DEVICE_CONNECTION("408600", "It is timed out the device connection."),
   REQUEST_TIMEOUT_USER_PW_AUTH_CODE("408700", "The authentication code has expired."),
   INTERNAL_SERVER_ERROR("500000", "Internal Server Error"),
   INTERNAL_SERVER_ERROR_UNKNOWN("500001", "Unknown error occurred."),
   INTERNAL_SERVER_ERROR_UNEXPECTED("500002", "Unexpected condition was encountered."),
   INTERNAL_SERVER_ERROR_SQLERROR("500003", "SQL Error was encountered."),
   INTERNAL_SERVER_ERROR_CONFIGERROR("500004", "Config error was encountered."),
   INTERNAL_SERVER_ERROR_JOBSCHEDULEERROR("500005", "Job scheduler error was encountered."),
   INTERNAL_SERVER_ERROR_OBJECT_CLONE("500006", "An error occurred when cloning an object."),
   INTERNAL_SERVER_ERROR_BINDING_EXCEPTION("500007", "Bindining error occured"),
   INTERNAL_SERVER_ERROR_SAVE_FAIL("500008", "Failed to save %s"),
   INTERNAL_SERVER_ERROR_FILE_DOWNLOAD_FAIL("500009", "Failed to download %s file(s)."),
   INTERNAL_SERVER_ERROR_FILE_DELETE_FAIL("500010", "Failed to delete %s file(s)."),
   INTERNAL_SERVER_ERROR_DELETE_FAIL("500011", "Failed to delete %s"),
   INTERNAL_SERVER_ERROR_TIME_CONVERSION_FAIL("500012", "Conversion to timestamp is failed."),
   INTERNAL_SERVER_ERROR_FILE_UPLOAD_FAIL("500013", "It is failed to %s file upload."),
   INTERNAL_SERVER_ERROR_DASHBOARD_WRONG_PRIORITY("500014", "The Dashboard priority value is less than 0."),
   INTERNAL_SERVER_ERROR_NOT_ENOUGH_STORAGE_SPACE("500015", "Not enough storage space. Free some space and retry."),
   INTERNAL_SERVER_ERROR_GROUP_DELETE_FAIL("500016", "Failed to delete the group."),
   INTERNAL_SERVER_ERROR_USER_REGISTER_FAIL("500017", "Failed to register the user."),
   INTERNAL_SERVER_ERROR_EMAIL_SEND_FAIL("500018", "Failed to send the mail."),
   INTERNAL_SERVER_ERROR_LOAD_FAIL("500019", "Failed to load the data."),
   INTERNAL_SERVER_ERROR_TOO_MUCH_DATA_FOR_CHART("500020", "There is too much data to generate a chart."),
   INTERNAL_SERVER_ERROR_GROUP_CREATE_FAIL("500021", "Failed to create %s group."),
   INTERNAL_SERVER_ERROR_GROUP_RENAME_FAIL("500022", "Failed to rename %s group."),
   INTERNAL_SERVER_ERROR_GROUP_MOVE_FAIL("500023", "Failed to move group."),
   INTERNAL_SERVER_ERROR_RULE_META_FILE_CREATE("500200", "It is failed to create the rule meta file."),
   INTERNAL_SERVER_ERROR_RULESET_FILE_UPDATE("500201", "An error occurred when updating the ruleset file."),
   INTERNAL_SERVER_ERROR_CONTENT_CREATE("500300", "The %s content creation is failed."),
   INTERNAL_SERVER_ERROR_THUMBNAIL_CREATE("500301", "An error occurred while creating the thumbnail file."),
   INTERNAL_SERVER_ERROR_CIFS_CONTENT_UPDATE("500302", "An unexpected error has occurred while updating the CIFS content."),
   INTERNAL_SERVER_ERROR_SHARE_FOLDER_UPDATE("500303", "It is failed to save the share folder information."),
   INTERNAL_SERVER_ERROR_CONTENT_MANAGEMENT_FAIL("500304", "This content cannot be managed and cannot be deleted."),
   INTERNAL_SERVER_ERROR_THUMBNAIL_LOAD_FAIL("500305", "Thumbnail file exists but cannot be load."),
   INTERNAL_SERVER_ERROR_CONTENT_ADD_FAIL("500306", "Failed to add content."),
   INTERNAL_SERVER_ERROR_SHARED_GROUP_DELETE_FAIL("500307", "Cannot delete as shared folder is part of other organization."),
   INTERNAL_SERVER_ERROR_PLAYLIST_COPY("500400", "It is failed to copy the playlist."),
   INTERNAL_SERVER_ERROR_SCHEDULE_FILE_CREATE("500500", "It is failed to create the schedule file."),
   INTERNAL_SERVER_ERROR_CONTENT_SCHEDULE_DEPLOY("500501", "It is failed to deploy the created content schedule."),
   INTERNAL_SERVER_ERROR_CONTENT_SCHEDULE_UPDATE("500502", "It is failed to update the content schedule file."),
   INTERNAL_SERVER_ERROR_SCHEDULE_FRAME_TEMPLATE_NOT_FOUND("500503", "Succeed to update frame template but cannot found it."),
   INTERNAL_SERVER_ERROR_SCHEDULE_FRAME_TEMPLATE_UPDATE("500504", "Fail to create frame template."),
   INTERNAL_SERVER_ERROR_SCHEDULE_FRAME_TEMPLATE_NOT_FOUND_UPDATE("500505", "Succeed to create frame template but cannot found it."),
   INTERNAL_SERVER_ERROR_MESSAGE_SCHEDULE_DEPLOY("500506", "It is failed to deploy the created message schedule."),
   INTERNAL_SERVER_ERROR_MESSAGE_SCHEDULE_UPDATE("500507", "It is failed to update the message schedule file."),
   INTERNAL_SERVER_ERROR_EVENT_SCHEDULE_DEPLOY("500506", "It is failed to deploy the created event schedule."),
   INTERNAL_SERVER_ERROR_EVENT_SCHEDULE_UPDATE("500507", "It is failed to update the event schedule file."),
   INTERNAL_SERVER_ERROR_CANNOT_DELETE_GROUP_HAS_SCHEDULE("500508", "Cannot delete this group as it has a schedule(s)."),
   INTERNAL_SERVER_ERROR_POSTBOOT_SERVICE("500600", "It is failed to run the postboot service."),
   INTERNAL_SERVER_ERROR_SOFTWARE_FILE_UPLOAD("500601", "It is failed to upload the software file."),
   INTERNAL_SERVER_ERROR_SOFTWARE_FILE_DEPLOY("500602", "It is failed to deploy the software file to devices."),
   INTERNAL_SERVER_ERROR_SOFTWARE_FILE_UPDATE("500603", "It is failed to update the software file."),
   INTERNAL_SERVER_ERROR_LOG_CLEAN_UP("500604", "It is failed to clean up the log folder and log files."),
   INTERNAL_SERVER_ERROR_VWL_FILE_CREATE("500605", "It is failed to create the vwl file."),
   INTERNAL_SERVER_ERROR_VWL_LAYOUT_DEPLOY_SERVICE_RUN("500606", "It is failed to run the vwl layout deployment service."),
   INTERNAL_SERVER_ERROR_STATISTICS_FILE_REFRESH("500607", "An error occurred while sending the statistics file refresh request."),
   INTERNAL_SERVER_ERROR_TIME_UPDATE_IN_STATISTICS_FILE("500608", "An error occurred while updating the requested time of statistics file."),
   INTERNAL_SERVER_ERROR_SETUP_INFORMATION_OBTAIN("500609", "An error occurred when getting device setup information."),
   INTERNAL_SERVER_ERROR_TAG_INFORMATION_UPDATE("500610", "It is failed to update the tag information for device."),
   INTERNAL_SERVER_ERROR_RM_RULE_FILE_DIRECTORY_CREATE("500611", "It is failed to create a home directory of the RM Rule file."),
   INTERNAL_SERVER_ERROR_RM_MONITORING_DIRECTORY_CREATE("500612", "It is failed to create the RM Monitoring directory."),
   INTERNAL_SERVER_ERROR_E2E_DEACTIVATE("500613", "It is failed to the E2E deactivation."),
   INTERNAL_SERVER_ERROR_SCHEDULE_DEPLOY("500614", "It is failed to deploy the schedule."),
   INTERNAL_SERVER_ERROR_ALL_DEVICE_CONNECT_FAIL("500615", "It can be available when all devices are connected."),
   INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION("500616", "There is a problem with the Remote server operation. %s"),
   INTERNAL_SERVER_ERROR_REQUEST_FROM_DEVICE("500617", "Failed to request %s information from device."),
   INTERNAL_SERVER_ERROR_RM_SERVER_CONNECT_FAIL("500618", "Not connected to the %s Remote server. Please check RM Server."),
   INTERNAL_SERVER_ERROR_RM_SERVER_CONTROL_ALREADY_RUN("500619", "Remote control has been already run."),
   INTERNAL_SERVER_ERROR_SOFTWARE_DEPOLY_CHECK("500620", "Failed to cancel the software deployment reservation."),
   INTERNAL_SERVER_ERROR_JOB_CANCEL_FAIL("500621", "Failed to cancel the job."),
   INTERNAL_SERVER_ERROR_SOFTWARE_RESERVATION_FAIL("500622", "Software reservation information cancellation processing is failed."),
   INTERNAL_SERVER_ERROR_CANNOT_CAST_VALUE("500623", "%s cannot be cast to int without changing its value."),
   INTERNAL_SERVER_ERROR_VWL_FILE_EDIT("500624", "Cannot edit VideoWall Layout file. Create VideoWall Layout file using New or Reuse."),
   INTERNAL_SERVER_ERROR_APPROVE_FAIL("500625", "Failed to approve device."),
   INTERNAL_SERVER_ERROR_NO_RESULT_FILE("500626", "No result file exists."),
   INTERNAL_SERVER_ERROR_DEVICE_TYPE_CHANGE_WITHOUT_AUTHORIZATION("500627", "The device type was changed unexpectedly without authorization from the server. The device may not work properly, please delete and reauthorize it."),
   INTERNAL_SERVER_ERROR_CANNOT_CREATE_DEVICE_GROUP("500628", "The selected group cannot be created."),
   INTERNAL_SERVER_ERROR_CANNOT_RENAME_DEVICE_GROUP("500629", "The selected group cannot be renamed."),
   INTERNAL_SERVER_ERROR_SOFTWARE_DELETE_FAIL("500630", "Failed to delete a software"),
   INTERNAL_SERVER_ERROR_WARNING_RULE_DELETE_FAIL("500631", "Failed to delete a warning rule element."),
   INTERNAL_SERVER_ERROR_RM_SERVER_IS_ALREADY_RUNNING("500632", "Remote control has been already run"),
   INTERNAL_SERVER_ERROR_CANNOT_DELETE_DEVICE_GROUP("500633", "The selected group cannot be deleted."),
   INTERNAL_SERVER_ERROR_FIRMWARE_FAIL_ALREADY_FIRMWARE("500633", "%s"),
   INTERNAL_SERVER_ERROR_FIRMWARE_FAIL_OFFLINE_DEVICE("500634", "%s"),
   INTERNAL_SERVER_ERROR_FIRMWARE_FAIL_NO_TARGET("500635", "%s"),
   INTERNAL_SERVER_ERROR_LOGIN_PAGE_IMAGE_FILE_UPLOAD("500636", "It is failed to upload the login page image file."),
   INTERNAL_SERVER_ERROR_ORGANIZATION_GROUP_CREATE("500700", "It is failed to create a new organization group."),
   INTERNAL_SERVER_ERROR_ORGANIZATION_GROUP_DELETE("500701", "It is failed to delete the organization group."),
   INTERNAL_SERVER_ERROR_LDAP_GROUP_SYNC("500702", "This group is already syncing."),
   INTERNAL_SERVER_ERROR_LDAP_ORGANIZATION_SYNC("500703", "This LDAP Organization is already synchronizing."),
   INTERNAL_SERVER_ERROR_ORGANIZATION_WITH_CONTENT_DELETE("500704", "Contents owned by the organization exist. First delete all content."),
   INTERNAL_SERVER_ERROR_RESET_PASSWORD_USER_NOT_APPROVED("500705", "User is not approved"),
   INTERNAL_SERVER_ERROR_RESET_PASSWORD_USER_NOT_EXIST("500706", "User does not exist"),
   INTERNAL_SERVER_ERROR_LDAP_WRONG_ADDRESS("500801", "LDAP server address is wrong."),
   INTERNAL_SERVER_ERROR_LDAP_WRONG_IDPASSWORD("500802", "ID or password you entered is wrong. Please enter again."),
   INTERNAL_SERVER_ERROR_LDAP_EXPIRED_PASSWORD("500803", "The password is expired."),
   INTERNAL_SERVER_ERROR_LDAP_LOGIN_FAIL("500804", "It is failed to login to the server. Please try again."),
   INTERNAL_SERVER_ERROR_LDAP_WRONG_DN("500805", "Please enter correct DN informations."),
   INTERNAL_SERVER_ERROR_LDAP_TIMEOUT("500806", "Timed out. Please try again."),
   INTERNAL_SERVER_ERROR_LDAP_CONNECT_FAIL("500807", "Failed to connect to the LDAP server. Please try again."),
   INTERNAL_SERVER_ERROR_DATA_LINK_SERVER_UPDATE("500808", "Failed to update of Datalink server. Please try again."),
   INTERNAL_SERVER_ERROR_REMOTE_SERVER_UPDATE("500809", "Failed to update of Remote server. Please try again."),
   INTERNAL_SERVER_ERROR_SLM_DB_ERROR("500810", "SLM DB ERROR"),
   INTERNAL_SERVER_ERROR_AUTHENTICATION_CODE_HAS_EXPIRED("500811", "The authentication code has expired. Please try again."),
   INTERNAL_SERVER_ERROR_CANNOT_ISSUE_FREE_LICENSE("500812", "Cannot issue free license anymore."),
   INTERNAL_SERVER_ERROR_INSIGHT_API_ERROR("500900", "Insight API Error. %s"),
   INTERNAL_SERVER_ERROR_INSIGHT_API_ERROR_INVALID_DURATION_TYPE("500901", "%s is invalid Insight Duration Type."),
   INTERNAL_SERVER_ERROR_INSIGHT_API_ERROR_CANNOT_GET_DURATION("500902", "Cannot get insight duration."),
   SERVICE_UNAVAILABLE("503000", "Service Unavailable"),
   SERVICE_UNAVAILABLE_WAIT_A_MOMENT("503001", "Please wait a moment and try again."),
   SERVICE_UNAVAILABLE_CONNECT_TO_SERVER_PLEASE_WAIT("503002", "Connect to the server. Please wait."),
   SERVICE_UNAVAILABLE_NO_DEVICE_AVAILABLE("503600", "There is no device currently available."),
   SERVICE_UNAVAILABLE_DEVICE_DISCONNECT("503601", "The device is disconnected from the server."),
   SERVICE_UNAVAILABLE_DEVICE_NOT_CONNECT("503602", "The device is not connected to the server."),
   SERVICE_UNAVAILABLE_LIMITED_LICENSE("503603", "One or more device might not be in the unapproved device list due to limited license."),
   SERVICE_CANNOT_MOVE_LIMITED_LICENSE("503604", "Device can not move due to limited license."),
   SERVICE_UNAVAILABLE_SIGNUP_RETRY("503700", "You can try again after 60 seconds."),
   SERVICE_UNAVAILABLE_ACCESS_LICENSE_SERVER("503701", "Access license server where Internet is possible."),
   SERVICE_UNAVAILABLE_ADMIN_WITHDRAW_NOT_ALLOWED("503800", "Admin cannot withdraw."),
   INTERNAL_SERVER_ERROR_DATALINK_TABLE_UPDATE("503801", "Cannot get datalink server tables"),
   INTERNAL_SERVER_ERROR_DATALINK_TABLE_INSERT("503802", "Cannot insert datalink server table mapping information"),
   INTERNAL_SERVER_ERROR_EDGE_REGISTRATION_FAIL("503901", "EdgeServer Registration failed."),
   INTERNAL_SERVER_ERROR_EDGE_DELETE_FAIL("503902", "EdgeServer Deletion fail (Not perfect)."),
   INTERNAL_SERVER_ERROR_EDGE_HOSTNAME_UPDATE_FAIL("503903", "EdgeServer Hostname update fail (Not perfect)"),
   SERVICE_UNAVAILABLE_SMTP_SERVER_NOT_EXIST("503900", "SMTP information do not exists on the server settings.");

   private String code;
   private String message;
   private static final Map exceptionCodeMap = new HashMap();

   private RestExceptionCode(String code, String message) {
      this.code = code;
      this.message = message;
   }

   public String getCode() {
      return this.code;
   }

   public String getMessage() {
      return this.message;
   }

   public static RestExceptionCode findBy(int key) {
      return (RestExceptionCode)exceptionCodeMap.get(String.valueOf(key));
   }

   public static RestExceptionCode findBy(String key) {
      return (RestExceptionCode)exceptionCodeMap.get(key);
   }

   public String generateFormattedMessages(String... stringFields) {
      String composedMsg = this.message;
      String[] formats = (String[])Optional.ofNullable(stringFields).orElse(new String[0]);
      if (formats.length > 0) {
         composedMsg = String.format(this.message, formats);
      }

      return composedMsg;
   }

   static {
      RestExceptionCode[] var0 = values();
      int var1 = var0.length;

      for(int var2 = 0; var2 < var1; ++var2) {
         RestExceptionCode restExceptionCode = var0[var2];
         exceptionCodeMap.put(restExceptionCode.code, restExceptionCode);
      }

   }
}
