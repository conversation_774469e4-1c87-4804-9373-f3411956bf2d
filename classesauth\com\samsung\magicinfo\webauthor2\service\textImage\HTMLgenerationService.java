package com.samsung.magicinfo.webauthor2.service.textImage;

import com.samsung.magicinfo.webauthor2.model.svg.TextImageDescriptor;
import java.io.File;

public interface HTMLgenerationService {
  void createHTMLfile(String paramString1, TextImageDescriptor paramTextImageDescriptor, String paramString2);
  
  String createFontFamilyTextHTMLfile(String paramString1, TextImageDescriptor paramTextImageDescriptor, String paramString2);
  
  void createHTMLfileEmbedFont(String paramString1, TextImageDescriptor paramTextImageDescriptor, String paramString2);
  
  String createHTMLstring(TextImageDescriptor paramTextImageDescriptor, String paramString);
  
  String createFontFamilyTextHTMLstring(TextImageDescriptor paramTextImageDescriptor, String paramString);
  
  File createHtmlFileFromString(String paramString1, String paramString2);
}
