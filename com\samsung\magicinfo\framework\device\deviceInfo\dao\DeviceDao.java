package com.samsung.magicinfo.framework.device.deviceInfo.dao;

import com.google.common.collect.Lists;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DBCacheUtils;
import com.samsung.common.utils.DaoTools;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.device.alarmRule.entity.AlarmRule;
import com.samsung.magicinfo.framework.device.alarmRule.manager.AlarmRuleManager;
import com.samsung.magicinfo.framework.device.alarmRule.manager.AlarmRuleManagerImpl;
import com.samsung.magicinfo.framework.device.constants.DeviceConstants;
import com.samsung.magicinfo.framework.device.deviceInfo.custom.entity.DeviceGeneralSearch;
import com.samsung.magicinfo.framework.device.deviceInfo.custom.manager.DeviceGeneralSearchInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.custom.manager.DeviceGeneralSearchInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.BackupPlayEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceLoopOutEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMemo;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceModel;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DisasterAlertStatusEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfo;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfoImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManager;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManagerImpl;
import com.samsung.magicinfo.framework.device.software.entity.Software;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManager;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManagerImpl;
import com.samsung.magicinfo.framework.device.vwlLayout.entity.VwlLayoutMonitor;
import com.samsung.magicinfo.framework.monitoring.dao.DownloadStatusDAO;
import com.samsung.magicinfo.framework.monitoring.manager.DownloadStatusInfo;
import com.samsung.magicinfo.framework.monitoring.manager.DownloadStatusInfoImpl;
import com.samsung.magicinfo.framework.scheduler.dao.MessageInfoDAO;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAO;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleAdminInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleAdminInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.rms.model.DeviceFilter;
import java.io.File;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class DeviceDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(DeviceDao.class);

   public DeviceDao() {
      super();
   }

   public DeviceDao(SqlSession session) {
      super(session);
   }

   public boolean addDevice(Device device) throws SQLException {
      SqlSession session = this.openNewSession(false);
      DeviceDaoMapper mapper = (DeviceDaoMapper)this.getMapper(session);

      boolean var4;
      try {
         if (device != null) {
            if (mapper.addDevice(device) && mapper.addDeviceGroupMapping(999999, device.getDevice_id())) {
               session.commit();
               if (device != null) {
                  DBCacheUtils.setDevice(device, device.getDevice_id());
               }

               var4 = true;
               return var4;
            }

            session.rollback();
            var4 = false;
            return var4;
         }

         this.logger.error("error because of null device");
         session.rollback();
         var4 = false;
      } catch (SQLException var8) {
         session.rollback();
         throw var8;
      } finally {
         session.close();
      }

      return var4;
   }

   public boolean addChildDevice(Device device) throws SQLException {
      SqlSession session = this.openNewSession(false);
      DeviceDaoMapper mapper = (DeviceDaoMapper)this.getMapper(session);

      boolean var4;
      try {
         if (!mapper.addDevice(device)) {
            session.rollback();
            var4 = false;
            return var4;
         }

         session.commit();
         if (device != null) {
            DBCacheUtils.setDevice(device, device.getDevice_id());
         }

         var4 = true;
      } catch (SQLException var8) {
         session.rollback();
         throw var8;
      } finally {
         session.close();
      }

      return var4;
   }

   public boolean addChildDevice(List devices) throws SQLException {
      SqlSession session = this.openNewSession(false);
      DeviceDaoMapper mapper = (DeviceDaoMapper)this.getMapper(session);

      boolean var11;
      try {
         if (!mapper.addDeviceList(devices)) {
            session.rollback();
            var11 = false;
            return var11;
         }

         session.commit();
         if (devices != null) {
            Iterator var4 = devices.iterator();

            while(var4.hasNext()) {
               Device device = (Device)var4.next();
               DBCacheUtils.setDevice(device, device.getDevice_id());
            }
         }

         var11 = true;
      } catch (SQLException var9) {
         session.rollback();
         throw var9;
      } finally {
         session.close();
      }

      return var11;
   }

   private boolean addDeviceGroupMapping(int parentGroupId, String deviceId, SqlSession session) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper(session)).addDeviceGroupMapping(parentGroupId, deviceId);
   }

   public boolean addDeviceGroupMapping(int parentGroupId, String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).addDeviceGroupMapping(parentGroupId, deviceId);
   }

   public boolean addDeviceGroupMapping(int parentGroupId, List deviceIdList) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).addDeviceGroupMappingList(parentGroupId, deviceIdList);
   }

   public boolean addDeviceModel(DeviceModel deviceModel) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).addDeviceModel(deviceModel);
   }

   public boolean addDeviceOperationInfo(Device device) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         if (!((DeviceDaoMapper)this.getMapper(session)).addDeviceOperationInfo(device) || !this.addDeviceGroupMapping(999999, device.getDevice_id(), session) || !this.addDeviceDisplay(device.getDevice_id(), session)) {
            session.rollback();
            var3 = false;
            return var3;
         }

         session.commit();
         DBCacheUtils.setDevice(device, device.getDevice_id());
         var3 = true;
      } catch (SQLException var7) {
         session.rollback();
         throw var7;
      } finally {
         session.close();
      }

      return var3;
   }

   public boolean setUnapprovedGroupCode(String deviceId, Long unapprovedGroupCode) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).setUnapprovedGroupCode(deviceId, unapprovedGroupCode);
   }

   public boolean addVwtInfo(VwlLayoutMonitor vwlLayoutMonitor, String deviceId) throws SQLException {
      String position_x = vwlLayoutMonitor.getPosition_x();
      String position_y = vwlLayoutMonitor.getPosition_y();
      String panel_width = vwlLayoutMonitor.getPanel_width();
      String panel_height = vwlLayoutMonitor.getPanel_height();
      String angle = vwlLayoutMonitor.getAngle();
      String bezel_leftright = vwlLayoutMonitor.getBezel_leftright();
      String bezel_topbottom = vwlLayoutMonitor.getBezel_topbottom();
      String vwt_id = vwlLayoutMonitor.getVwt_id();
      String map_id = vwlLayoutMonitor.getMap_id();
      Map vwl = new HashMap();
      vwl.put("position_x", Integer.parseInt(position_x));
      vwl.put("position_y", Integer.parseInt(position_y));
      vwl.put("panel_width", Integer.parseInt(panel_width));
      vwl.put("panel_height", Integer.parseInt(panel_height));
      vwl.put("angle", Integer.parseInt(angle));
      vwl.put("vwt_id", vwt_id);
      vwl.put("bezel_leftright", Integer.parseInt(bezel_leftright));
      vwl.put("bezel_topbottom", Integer.parseInt(bezel_topbottom));
      vwl.put("map_id", map_id);
      vwl.put("deviceId", deviceId);
      int cnt = ((DeviceDaoMapper)this.getMapper()).addVwtInfo(vwl, deviceId);
      if (cnt > 0) {
         Device device = new Device();
         device.setDevice_id(deviceId);
         device.setPosition_x(Long.parseLong(position_x));
         device.setPosition_y(Long.parseLong(position_y));
         device.setWidth(Long.parseLong(panel_width));
         device.setHeight(Long.parseLong(panel_height));
         device.setAngle(Long.parseLong(angle));
         device.setBezel_leftright(Long.parseLong(bezel_leftright));
         device.setBezel_topbottom(Long.parseLong(bezel_topbottom));
         device.setVwt_id(vwt_id);
         device.setMap_id(map_id);
         DBCacheUtils.setDevice(device, deviceId);
      }

      return cnt > 0;
   }

   public boolean deleteVwtInfo(String deviceId) throws SQLException {
      int cnt = ((DeviceDaoMapper)this.getMapper()).deleteVwtInfo(deviceId);
      if (cnt > 0) {
         Device device = new Device();
         device.setDevice_id(deviceId);
         device.setPosition_x(0L);
         device.setPosition_y(0L);
         device.setWidth(0L);
         device.setHeight(0L);
         device.setAngle(0L);
         device.setBezel_leftright(0L);
         device.setBezel_topbottom(0L);
         device.setMap_id("");
         device.setVwt_id("");
         DBCacheUtils.setDevice(device, deviceId);
      }

      return cnt > 0;
   }

   public void removeDeviceTotalCount(SqlSession sqlSession, long groupId, int count) throws SQLException {
      long curCount = ((DeviceDaoMapper)this.getMapper(sqlSession)).getDeviceTotalCount(groupId);
      ((DeviceDaoMapper)this.getMapper(sqlSession)).setDeviceTotalCount(groupId, curCount - 1L);
      List pgroupIdLists = this.getPgorupIdLIsts(sqlSession, groupId);
      if (pgroupIdLists != null) {
         for(int i = 0; i < pgroupIdLists.size(); ++i) {
            long pGroupId = (Long)((Map)pgroupIdLists.get(i)).get("P_GROUP_ID");
            this.removeDeviceTotalCount(sqlSession, pGroupId, count);
         }
      }

   }

   public void removeDeviceTotalCount(long groupId, int count) throws SQLException {
      long curCount = ((DeviceDaoMapper)this.getMapper()).getDeviceTotalCount(groupId);
      ((DeviceDaoMapper)this.getMapper()).setDeviceTotalCount(groupId, curCount - 1L);
      List pgroupIdLists = this.getPgorupIdLIsts(groupId);
      if (pgroupIdLists != null) {
         for(int i = 0; i < pgroupIdLists.size(); ++i) {
            long pGroupId = (Long)((Map)pgroupIdLists.get(i)).get("P_GROUP_ID");
            this.removeDeviceTotalCount(pGroupId, count);
         }
      }

   }

   public boolean deleteDeviceData(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).deleteDevice(deviceId);
   }

   public boolean deleteDevice(String deviceId) throws SQLException {
      SqlSession session = this.openNewSession(false);
      DownloadStatusDAO downloadStatusDAO = new DownloadStatusDAO(session);
      String groupId = "";

      List deployDefaultPrograms;
      boolean nocGroup;
      label110: {
         try {
            groupId = this.getDeviceGroupIdByDeviceId(session, deviceId);
            this.removeDeviceTotalCount(session, Long.valueOf(groupId), 1);
            String beforeDeleteGroupId = this.getDeviceGroupIdByDeviceId(session, deviceId);
            nocGroup = ((DeviceDaoMapper)this.getMapper(session)).deleteDeviceGroupMappingByDeviceId(deviceId);
            boolean deleteDeviceFlag = ((DeviceDaoMapper)this.getMapper(session)).deleteDevice(deviceId);
            this.deleteChildDevice(deviceId, session);
            if (!nocGroup || !deleteDeviceFlag) {
               throw new RuntimeException("[DeviceDao] cannot delete deviceGroup or a device");
            }

            deployDefaultPrograms = DeviceUtils.refreshPriorityByDeviceType(Long.parseLong(beforeDeleteGroupId), (String)null, session);
            ((DeviceDaoMapper)this.getMapper(session)).deleteDeviceClock(deviceId);
            ((DeviceDaoMapper)this.getMapper(session)).deleteDeviceHoliday(deviceId);
            ((DeviceDaoMapper)this.getMapper(session)).deleteDeviceTimer(deviceId);
            this.logger.error("[Device Update]-Delete Device deviceId: " + deviceId);
            DBCacheUtils.deleteDeviceEntities(deviceId);
            Long deviceGroupId = Long.parseLong(groupId);
            if (((DeviceDaoMapper)this.getMapper(session)).getDeviceCountBygroupId(deviceGroupId) <= 0) {
               ScheduleInfoDAO scheduleInfoDao = new ScheduleInfoDAO(session);
               scheduleInfoDao.deleteDeviceGroupMappingByDeviceGroupId(session, deviceGroupId);
               MessageInfoDAO messageInfoDAO = new MessageInfoDAO(session);
               messageInfoDAO.deleteMessageByDeviceGroupId(session, deviceGroupId);
            }

            downloadStatusDAO.deleteScheduleDeployStatusByDeviceId(session, deviceId);
            downloadStatusDAO.deleteAllDownloadStatusByDeviceId(session, deviceId);
            session.commit();
            break label110;
         } catch (SQLException var22) {
            session.rollback();
            this.logger.error("SQLException : deleteDevice - " + deviceId, var22);
            throw var22;
         } catch (Exception var23) {
            session.rollback();
            this.logger.error("Exception : deleteDevice - " + deviceId, var23);
            nocGroup = false;
         } finally {
            session.close();
         }

         return nocGroup;
      }

      try {
         DownloadStatusInfo downloadStatusInfo = DownloadStatusInfoImpl.getInstacne();
         downloadStatusInfo.deleteDownloadStatusInCache(deviceId);
      } catch (Exception var21) {
         this.logger.error("Download Status Cache Delete Error.");
      }

      try {
         if (CollectionUtils.isNotEmpty(deployDefaultPrograms)) {
            ScheduleAdminInfo scheduleAdminInfo = ScheduleAdminInfoImpl.getInstance();
            scheduleAdminInfo.deployDefaultPrograms(deployDefaultPrograms);
         }
      } catch (Exception var20) {
         this.logger.error("[DeviceDao] fail to deploy default program to " + deployDefaultPrograms.toString());
      }

      try {
         if (DeviceUtils.isSupportNOC()) {
            DeviceNocInfo nocDao = DeviceNocInfoImpl.getInstance();
            nocGroup = nocDao.isNocSupportGroup(Long.valueOf(groupId));
            if (nocGroup) {
               DeviceNocManager nocService = DeviceNocManagerImpl.getInstance();
               nocService.thingworxDeleteDevice(deviceId);
            }
         }
      } catch (Exception var19) {
         this.logger.error("Exception : failed to delete device from thingworx. - " + deviceId, var19);
      }

      return true;
   }

   public boolean deleteDeviceGroupMapping(String deviceId, int groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).deleteDeviceGroupMapping(deviceId, groupId);
   }

   public boolean deleteDeviceModel(String deviceModelName) throws SQLException {
      List deviceList = this.getDeviceListByModelName(deviceModelName);
      if (deviceList != null && deviceList.size() > 0) {
         throw new SQLException("cannot delete model that included device(s).");
      } else {
         boolean ret = false;
         SqlSession session = null;

         try {
            session = this.openNewSession(false);

            try {
               ((DeviceDaoMapper)this.getMapper(session)).deleteModelAndRule(deviceModelName);
               ((DeviceDaoMapper)this.getMapper(session)).deleteDeviceModelSoftware(deviceModelName);
            } catch (SQLException var12) {
               session.rollback();
               throw var12;
            }

            try {
               ((DeviceDaoMapper)this.getMapper(session)).deleteDeviceModel(deviceModelName);
            } catch (SQLException var11) {
               session.rollback();
               throw var11;
            }

            session.commit();
            ret = true;
         } catch (SQLException var13) {
            ret = false;
            session.rollback();
         } finally {
            if (session != null) {
               session.close();
            }

         }

         return ret;
      }
   }

   public boolean deleteDeviceModels(String[] deviceModelNames) throws SQLException, ConfigException {
      SqlSession session = this.openNewSession(false);

      boolean var13;
      try {
         String[] var3 = deviceModelNames;
         int var4 = deviceModelNames.length;

         for(int var5 = 0; var5 < var4; ++var5) {
            String deviceModelName = var3[var5];
            List deviceList = this.getDeviceListByModelName(deviceModelName);
            if (deviceList == null || deviceList.size() <= 0) {
               this.deleteModelAndRule(deviceModelName, session);
               this.deleteDeviceModelSoftware(deviceModelName, session);
            }
         }

         if (((DeviceDaoMapper)this.getMapper(session)).deleteDeviceModels(deviceModelNames)) {
            session.commit();
            var13 = true;
            return var13;
         }

         session.rollback();
         var13 = false;
      } catch (SQLException var11) {
         session.rollback();
         throw var11;
      } finally {
         session.close();
      }

      return var13;
   }

   private void deleteDeviceModelSoftware(String modelName, SqlSession session) throws SQLException, ConfigException {
      List list;
      if (session != null) {
         list = ((DeviceDaoMapper)this.getMapper(session)).deleteDeviceModelSoftware(modelName);
      } else {
         list = ((DeviceDaoMapper)this.getMapper()).deleteDeviceModelSoftware(modelName);
      }

      String topPath = CommonConfig.get("UPLOAD_HOME");
      SoftwareManager mgr = null;
      if (session != null) {
         mgr = SoftwareManagerImpl.getInstance(session);
      } else {
         mgr = SoftwareManagerImpl.getInstance();
      }

      Iterator var6 = list.iterator();

      while(var6.hasNext()) {
         Map map = (Map)var6.next();
         Software software = ((SoftwareManager)mgr).getSoftware((Long)map.get("SOFTWARE_ID"));
         if (((SoftwareManager)mgr).deleteSoftware(software.getSoftware_id())) {
            File file = SecurityUtils.getSafeFile(topPath + File.separator + software.getFile_path());
            if (file.exists()) {
               file.delete();
            }
         }
      }

   }

   public boolean deleteDevices(String[] deviceIdList) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         if (((DeviceDaoMapper)this.getMapper(session)).deleteGroupDevices(deviceIdList) && ((DeviceDaoMapper)this.getMapper(session)).deleteDevices(deviceIdList)) {
            session.commit();
            String[] var12 = deviceIdList;
            int var4 = deviceIdList.length;

            for(int var5 = 0; var5 < var4; ++var5) {
               String deviceId = var12[var5];
               DBCacheUtils.deleteDeviceEntities(deviceId);
            }

            var3 = true;
            return var3;
         }

         session.rollback();
         var3 = false;
      } catch (SQLException var10) {
         session.rollback();
         throw var10;
      } finally {
         session.close();
      }

      return var3;
   }

   private void deleteModelAndRule(String modelName, SqlSession session) throws SQLException, ConfigException {
      List list;
      if (session != null) {
         list = ((DeviceDaoMapper)this.getMapper(session)).deleteModelAndRule(modelName);
      } else {
         list = ((DeviceDaoMapper)this.getMapper()).deleteModelAndRule(modelName);
      }

      String topPath = CommonConfig.get("UPLOAD_HOME");
      AlarmRuleManager mgr;
      if (session != null) {
         mgr = AlarmRuleManagerImpl.getInstance(session.getConnection());
      } else {
         mgr = AlarmRuleManagerImpl.getInstance();
      }

      Iterator var6 = list.iterator();

      while(var6.hasNext()) {
         Map map = (Map)var6.next();
         AlarmRule rule = mgr.getAlarmRule((Long)map.get("RULE_ID"));
         if (mgr.delAlarmRule(rule.getRule_id())) {
            File file = SecurityUtils.getSafeFile(topPath + File.separator + rule.getFile_path());
            if (file.exists()) {
               file.delete();
            }
         }
      }

   }

   public PagedListInfo getApprovedDeviceList(int startPos, int pageSize, Map condition) throws SQLException {
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      DeviceGeneralSearch search = null;
      SelectCondition condObj = (SelectCondition)condition.get("condition");
      List groupList = new ArrayList();
      String roleName = condObj.getRole_name();
      String userId = condObj.getUser_id();
      boolean isDeviceGroupAuth = false;
      if (DeviceUtils.isDeviceGroupAuth(roleName, userId)) {
         isDeviceGroupAuth = true;
      }

      Long search_id = null;
      if (condition.get("search_id") != null && !condition.get("search_id").equals("") && !((String)condition.get("search_id")).equalsIgnoreCase("null")) {
         search_id = Long.parseLong((String)condition.get("search_id"));
      }

      Integer deviceExpirationDate = null;
      if (condObj.getExpiration_date() != null && !condObj.getExpiration_date().equals("device_status_view_all")) {
         if (condObj.getExpiration_date().equals("device_expiration_date_seven")) {
            deviceExpirationDate = 7;
         } else if (condObj.getExpiration_date().equals("device_expiration_date_halfmonth")) {
            deviceExpirationDate = 15;
         } else if (condObj.getExpiration_date().equals("device_expiration_date_month")) {
            deviceExpirationDate = 30;
         } else if (condObj.getExpiration_date().equals("device_expiration_date_custom") && condObj.getCustom_input_val() != null && !condObj.getCustom_input_val().equals("")) {
            deviceExpirationDate = Integer.parseInt(condObj.getCustom_input_val());
         }
      }

      String[] filterGroupId = null;
      if (condObj.getIsRoot() && condObj.getFilter_group_ids() != null && !condObj.getFilter_group_ids().equals("")) {
         filterGroupId = condObj.getFilter_group_ids().split(",");
      }

      if (filterGroupId != null && filterGroupId.length > 0) {
         String[] var22 = filterGroupId;
         int var15 = filterGroupId.length;

         for(int var16 = 0; var16 < var15; ++var16) {
            String groupId = var22[var16];
            ((List)groupList).add(groupDao.getGroup(Integer.valueOf(groupId)));
         }
      } else if (condObj.getGroup_id() != null && condObj.getGroup_id() == 0L) {
         List deviceOrgIds = DeviceUtils.getOrgGroupIdByUserId(condObj.getUser_id());
         if (deviceOrgIds != null && deviceOrgIds.size() > 0) {
            groupList = groupDao.getAllDeviceGroups(deviceOrgIds);
         }
      } else if (condObj.getGroup_id() != null && condObj.getGroup_id().intValue() != 0) {
         DeviceGroup group = groupDao.getGroup(condObj.getGroup_id().intValue());
         if (condObj.getIsRoot() || group.getP_group_id().intValue() == 0) {
            groupList = groupDao.getAllDeviceGroups(condObj.getGroup_id());
         }
      }

      if (search_id != null) {
         DeviceGeneralSearchInfo searchInfo = DeviceGeneralSearchInfoImpl.getInstance();
         search = searchInfo.getDeviceGeneralSearchBySearchId(search_id);
      }

      if (condObj.getDevice_type() != null) {
         condObj.setDevice_type_arr(condObj.getDevice_type().split(","));
      }

      String tagFilter = condObj.getTagFilter();
      List tagFilterIdList = null;
      if (StringUtils.isNotBlank(tagFilter)) {
         tagFilterIdList = new ArrayList();
         String[] tagFilterIds = tagFilter.split(",");
         String[] var28 = tagFilterIds;
         int var18 = tagFilterIds.length;

         for(int var19 = 0; var19 < var18; ++var19) {
            String tagFilterId = var28[var19];
            tagFilterIdList.add(Long.parseLong(tagFilterId));
         }
      }

      List result = new ArrayList();
      int totCnt = 0;
      condObj.setRm_device_types(CommonDataConstants.RM_DEVICE_TYPE_ARRAY);
      int totCnt;
      if (((List)groupList).size() > 0) {
         List groupIds = new ArrayList();
         Iterator var32 = ((List)groupList).iterator();

         while(var32.hasNext()) {
            DeviceGroup group = (DeviceGroup)var32.next();
            groupIds.add(group.getGroup_id());
         }

         String groupListStr = StrUtils.arrayToString(groupIds, ",");
         result.addAll(((DeviceDaoMapper)this.getMapper()).getApprovedDeviceList(startPos - 1, pageSize, condObj, search, groupListStr, 999999, isDeviceGroupAuth, userId, deviceExpirationDate, tagFilterIdList, this.getConstants()));
         totCnt = totCnt + ((DeviceDaoMapper)this.getMapper()).getApprovedDeviceListCnt(startPos - 1, pageSize, condObj, search, groupListStr, 999999, isDeviceGroupAuth, userId, deviceExpirationDate, tagFilterIdList, this.getConstants());
      } else {
         result.addAll(((DeviceDaoMapper)this.getMapper()).getApprovedDeviceList(startPos - 1, pageSize, condObj, search, (String)null, 999999, isDeviceGroupAuth, userId, deviceExpirationDate, tagFilterIdList, this.getConstants()));
         totCnt = ((DeviceDaoMapper)this.getMapper()).getApprovedDeviceListCnt(startPos - 1, pageSize, condObj, search, (String)null, 999999, isDeviceGroupAuth, userId, deviceExpirationDate, tagFilterIdList, this.getConstants());
      }

      return new PagedListInfo(result, totCnt);
   }

   public PagedListInfo getApprovedDeviceListWithFilter(int startPos, int pageSize, Map condition) throws SQLException {
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      DeviceGeneralSearch search = null;
      SelectCondition condObj = (SelectCondition)condition.get("condition");
      List groupList = new ArrayList();
      String roleName = condObj.getRole_name();
      String userId = condObj.getUser_id();
      boolean isDeviceGroupAuth = false;
      if (DeviceUtils.isDeviceGroupAuth(roleName, userId)) {
         isDeviceGroupAuth = true;
      }

      Long search_id = null;
      if (condition.get("search_id") != null && !condition.get("search_id").equals("") && !((String)condition.get("search_id")).equalsIgnoreCase("null")) {
         search_id = Long.parseLong((String)condition.get("search_id"));
      }

      Integer deviceExpirationDate = null;
      if (condObj.getExpiration_date() != null && !condObj.getExpiration_date().equals("device_status_view_all")) {
         if (condObj.getExpiration_date().equals("device_expiration_date_seven")) {
            deviceExpirationDate = 7;
         } else if (condObj.getExpiration_date().equals("device_expiration_date_halfmonth")) {
            deviceExpirationDate = 15;
         } else if (condObj.getExpiration_date().equals("device_expiration_date_month")) {
            deviceExpirationDate = 30;
         } else if (condObj.getExpiration_date().equals("device_expiration_date_custom") && condObj.getCustom_input_val() != null && !condObj.getCustom_input_val().equals("")) {
            deviceExpirationDate = Integer.parseInt(condObj.getCustom_input_val());
         }
      }

      String[] filterGroupId = null;
      if (condObj.getFilter_group_ids() != null && !condObj.getFilter_group_ids().equals("")) {
         if (condObj.getIsRoot()) {
            filterGroupId = condObj.getFilter_group_ids().split(",");
         } else {
            filterGroupId = (String[])Arrays.stream(condObj.getFilter_group_ids().split(",")).filter((groupIdx) -> {
               try {
                  if (StringUtils.equals(groupDao.getDeviceGroupRoot(Integer.parseInt(groupIdx)), SecurityUtils.getLoginUserOrganization())) {
                     return true;
                  }
               } catch (Exception var4) {
                  this.logger.error("get root org id error. groupId = " + groupIdx);
               }

               return false;
            }).toArray((x$0) -> {
               return new String[x$0];
            });
         }
      }

      if (filterGroupId != null && filterGroupId.length > 0) {
         String[] var22 = filterGroupId;
         int var15 = filterGroupId.length;

         for(int var16 = 0; var16 < var15; ++var16) {
            String groupId = var22[var16];
            ((List)groupList).add(groupDao.getGroup(Integer.valueOf(groupId)));
         }
      } else if (condObj.getGroup_id() != null && condObj.getGroup_id() == 0L) {
         List deviceOrgIds = DeviceUtils.getOrgGroupIdByUserId(condObj.getUser_id());
         if (deviceOrgIds != null && deviceOrgIds.size() > 0) {
            groupList = groupDao.getAllDeviceGroups(deviceOrgIds);
         }
      } else if (condObj.getGroup_id() != null && condObj.getGroup_id().intValue() != 0) {
         DeviceGroup group = groupDao.getGroup(condObj.getGroup_id().intValue());
         if (group.getP_group_id().intValue() == 0) {
            groupList = groupDao.getAllDeviceGroups(condObj.getGroup_id());
         }
      }

      if (search_id != null) {
         DeviceGeneralSearchInfo searchInfo = DeviceGeneralSearchInfoImpl.getInstance();
         search = searchInfo.getDeviceGeneralSearchBySearchId(search_id);
      }

      if (condObj.getDevice_type() != null) {
         condObj.setDevice_type_arr(condObj.getDevice_type().split(","));
      }

      String tagFilter = condObj.getTagFilter();
      List tagFilterIdList = null;
      if (StringUtils.isNotBlank(tagFilter)) {
         tagFilterIdList = new ArrayList();
         String[] tagFilterIds = tagFilter.split(",");
         String[] var28 = tagFilterIds;
         int var18 = tagFilterIds.length;

         for(int var19 = 0; var19 < var18; ++var19) {
            String tagFilterId = var28[var19];
            tagFilterIdList.add(Long.parseLong(tagFilterId));
         }
      }

      List result = new ArrayList();
      int totCnt = false;
      condObj.setRm_device_types(CommonDataConstants.RM_DEVICE_TYPE_ARRAY);
      int totCnt;
      if (((List)groupList).size() > 0) {
         List groupIds = new ArrayList();
         Iterator var32 = ((List)groupList).iterator();

         while(var32.hasNext()) {
            DeviceGroup group = (DeviceGroup)var32.next();
            groupIds.add(group.getGroup_id());
         }

         String groupListStr = StrUtils.arrayToString(groupIds, ",");
         result.addAll(((DeviceDaoMapper)this.getMapper()).getApprovedDeviceListWithFilter(startPos - 1, pageSize, condObj, search, groupListStr, 999999, isDeviceGroupAuth, userId, deviceExpirationDate, tagFilterIdList, this.getConstants()));
         totCnt = ((DeviceDaoMapper)this.getMapper()).getApprovedDeviceListCnt(startPos - 1, pageSize, condObj, search, groupListStr, 999999, isDeviceGroupAuth, userId, deviceExpirationDate, tagFilterIdList, this.getConstants());
      } else {
         result.addAll(((DeviceDaoMapper)this.getMapper()).getApprovedDeviceListWithFilter(startPos - 1, pageSize, condObj, search, (String)null, 999999, isDeviceGroupAuth, userId, deviceExpirationDate, tagFilterIdList, this.getConstants()));
         totCnt = ((DeviceDaoMapper)this.getMapper()).getApprovedDeviceListCnt(startPos - 1, pageSize, condObj, search, (String)null, 999999, isDeviceGroupAuth, userId, deviceExpirationDate, tagFilterIdList, this.getConstants());
      }

      return new PagedListInfo(result, totCnt);
   }

   public int getCntApprovedDeviceList(Map condition) throws SQLException {
      DeviceGeneralSearch search = null;
      Long search_id = null;
      if (condition.get("search_id") != null && !condition.get("search_id").equals("") && !((String)condition.get("search_id")).equalsIgnoreCase("null")) {
         search_id = Long.parseLong((String)condition.get("search_id"));
      }

      boolean isRoot = false;
      if (condition.get("isRoot") != null) {
         isRoot = (Boolean)condition.get("isRoot");
      }

      Long groupId = (Long)condition.get("groupId");
      List groupList = null;
      if (search_id != null) {
         DeviceGeneralSearchInfo searchInfo = DeviceGeneralSearchInfoImpl.getInstance();
         search = searchInfo.getDeviceGeneralSearchBySearchId(search_id);
         if (isRoot) {
            DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
            groupList = groupDao.getChildGroupList(groupId.intValue(), true);
         }
      }

      int totCnt = 0;
      if (groupList != null && groupList.size() > 0) {
         List list = Lists.partition(groupList, 1500);

         for(int i = 0; i < list.size(); ++i) {
            totCnt += ((DeviceDaoMapper)this.getMapper()).getCntApprovedDeviceList(search, (List)list.get(i), isRoot, groupId);
         }
      } else {
         totCnt = ((DeviceDaoMapper)this.getMapper()).getCntApprovedDeviceList(search, groupList, isRoot, groupId);
      }

      return totCnt;
   }

   public List getApprovedDeviceFilterList(SelectCondition condition) throws SQLException {
      List groupList = null;
      if (condition.getGroup_id() != null && condition.getGroup_id().intValue() != 0 && condition.getIsRoot()) {
         DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
         groupList = groupDao.getChildGroupList(condition.getGroup_id().intValue(), true);
      }

      return ((DeviceDaoMapper)this.getMapper()).getApprovedDeviceFilterList(condition, groupList, 999999);
   }

   public int getCntEqualDevName(String deviceName, Long orgGroupId) throws SQLException {
      List groupIdList = null;
      if (orgGroupId != null && orgGroupId.intValue() != 0) {
         DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
         groupIdList = groupDao.getChildGroupIdList(orgGroupId.intValue(), true);
      }

      int cnt = 0;
      if (groupIdList != null && groupIdList.size() > 0) {
         List list = Lists.partition(groupIdList, 1500);

         for(int i = 0; i < list.size(); ++i) {
            cnt += ((DeviceDaoMapper)this.getMapper()).getCntEqualDevName(deviceName, (List)list.get(i));
         }
      } else {
         cnt += ((DeviceDaoMapper)this.getMapper()).getCntEqualDevName(deviceName, groupIdList);
      }

      return cnt;
   }

   public int getCntModelByModelCode(String modelCode) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getCntModelByModelCode(modelCode);
   }

   public boolean getExistCenterstage() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getExistCenterstage();
   }

   public Device getDevice(String deviceId) throws SQLException {
      try {
         Device ret = DBCacheUtils.getDevice(deviceId);
         return ret;
      } catch (Exception var4) {
         Device device = ((DeviceDaoMapper)this.getMapper()).getDevice(deviceId);
         return device;
      }
   }

   public Device getDeviceFromDB(String deviceId) throws SQLException {
      try {
         return ((DeviceDaoMapper)this.getMapper()).getDevice(deviceId);
      } catch (Exception var3) {
         return null;
      }
   }

   public Device getDeviceWithGroupId(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceWithGroupId(deviceId);
   }

   public Device getMonitoringViewDevice(String deviceId) throws SQLException {
      Device device = ((DeviceDaoMapper)this.getMapper()).getDevice(deviceId);
      return device;
   }

   public Map getDeviceAndModel(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceAndModel(deviceId.trim());
   }

   public DeviceGeneralConf getDeviceGeneralConf(String deviceId) throws SQLException {
      try {
         DeviceGeneralConf ret = DBCacheUtils.getDeviceGeneralConf(deviceId);
         return ret;
      } catch (Exception var4) {
         DeviceGeneralConf deviceGeneralConf = ((DeviceDaoMapper)this.getMapper()).getDeviceGeneralConf(deviceId);
         DBCacheUtils.setDeviceGeneralConf(deviceGeneralConf, deviceId);
         return deviceGeneralConf;
      }
   }

   public DeviceGeneralConf getDeviceTypeInfo(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceTypeInfo(deviceId);
   }

   public DeviceGeneralConf getDeviceGeneralConf(String deviceId, boolean refresh) throws SQLException {
      try {
         DeviceGeneralConf ret = null;
         if (refresh) {
            ret = ((DeviceDaoMapper)this.getMapper()).getDeviceGeneralConf(deviceId);
         } else {
            ret = DBCacheUtils.getDeviceGeneralConf(deviceId);
         }

         return ret;
      } catch (Exception var5) {
         DeviceGeneralConf deviceGeneralConf = ((DeviceDaoMapper)this.getMapper()).getDeviceGeneralConf(deviceId);
         DBCacheUtils.setDeviceGeneralConf(deviceGeneralConf, deviceId);
         return deviceGeneralConf;
      }
   }

   public List getListDeviceGeneralConf(List deviceIds) throws SQLException {
      List deviceGeneralConf = ((DeviceDaoMapper)this.getMapper()).getListDeviceGeneralConf(deviceIds);
      return deviceGeneralConf;
   }

   public List getDeviceList() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceList((String)null, (String)null, (String)null);
   }

   public List getChildDeviceIdList(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getChildDeviceIdList(deviceId);
   }

   public PagedListInfo getDeviceList(int startPos, int pageSize) throws SQLException {
      return this.getDeviceList(startPos, pageSize, (String)null, (String)null, (String)null);
   }

   public PagedListInfo getDeviceList(int startPos, int pageSize, String modelName, String modelCode, String deviceId) throws SQLException {
      List result = ((DeviceDaoMapper)this.getMapper()).getDeviceListPaged(modelName, modelCode, deviceId, startPos - 1, pageSize);
      int totCnt = ((DeviceDaoMapper)this.getMapper()).getDeviceListCnt(modelName, modelCode, deviceId);
      return new PagedListInfo(result, totCnt);
   }

   public PagedListInfo getDeviceListByGroup(int startPos, int pageSize, String modelName, Long groupId, String deviceId) throws SQLException {
      List result = ((DeviceDaoMapper)this.getMapper()).getDeviceListByGroup(startPos - 1, pageSize, modelName, groupId, deviceId);
      int totCnt = ((DeviceDaoMapper)this.getMapper()).getDeviceListByGroupCnt(modelName, groupId, deviceId);
      return new PagedListInfo(result, totCnt);
   }

   public List getDeviceListByGroupId(Long groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceListByGroupId(groupId);
   }

   public List getDeviceAndTagListByGroupIds(Long[] groupIds) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceAndTagListByGroupIds(groupIds);
   }

   public List getDeviceAndTagListByGroupId(Long groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceAndTagListByGroupId(groupId);
   }

   public Long getDeviceUnapprovedGroupCode(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceUnapprovedGroupCode(deviceId);
   }

   public List getDeviceAndTagListByDeviceIds(String[] deviceIds, Boolean isVarTag) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceAndTagListByDeviceIds(deviceIds, isVarTag);
   }

   public List getDeviceListByModelName(String modelName) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceList(modelName, (String)null, (String)null);
   }

   public List getDeviceListByModelNameAndType(String ModelName, String DeviceType, String organization) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceListByModelNameAndType(ModelName, (String)null, (String)null, DeviceType, organization);
   }

   public List getDeviceListByModelNameAndGroup(String modelName, int groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceListByModelNameAndGroup(modelName, groupId);
   }

   public Device getDeviceMinInfo(String deviceId) throws SQLException {
      try {
         Device ret = DBCacheUtils.getDevice(deviceId);
         return ret;
      } catch (Exception var4) {
         Device device = ((DeviceDaoMapper)this.getMapper()).getDeviceMinInfo(deviceId);
         if (device != null) {
            DBCacheUtils.setDevice(device, device.getDevice_id());
         }

         return device;
      }
   }

   public DeviceModel getDeviceModel(String device_model_name) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceModel(device_model_name);
   }

   public PagedListInfo getDeviceModelList(int startPos, int pageSize, String deviceModelCode) throws SQLException {
      List result = ((DeviceDaoMapper)this.getMapper()).getDeviceModelList(deviceModelCode, startPos - 1, pageSize);
      int totCnt = ((DeviceDaoMapper)this.getMapper()).getDeviceModelListCnt(deviceModelCode);
      return new PagedListInfo(result, totCnt);
   }

   public List getDeviceModelList(String deviceModelCode) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceModelList(deviceModelCode, (Integer)null, (Integer)null);
   }

   public List getExpiredDeviceList(int day) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getExpiredDeviceList(day);
   }

   public List getDeviceMonitoringList(DeviceFilter condition) throws NumberFormatException, SQLException {
      List groupList = new ArrayList();
      String roleName = condition.getRole_name();
      String userId = condition.getUser_id();
      boolean isDeviceGroupAuth = false;

      try {
         if (DeviceUtils.isDeviceGroupAuth(roleName, userId)) {
            isDeviceGroupAuth = true;
         }
      } catch (SQLException var13) {
         this.logger.error("", var13);
      }

      Integer deviceExpirationDate = null;
      if (condition.getExpiration_date() != null && !condition.getExpiration_date().equals("device_status_view_all")) {
         if (condition.getExpiration_date().equals("device_expiration_date_seven")) {
            deviceExpirationDate = 7;
         } else if (condition.getExpiration_date().equals("device_expiration_date_halfmonth")) {
            deviceExpirationDate = 15;
         } else if (condition.getExpiration_date().equals("device_expiration_date_month")) {
            deviceExpirationDate = 30;
         } else if (condition.getExpiration_date().equals("device_expiration_date_custom") && condition.getCustom_input_val() != null && !condition.getCustom_input_val().equals("")) {
            deviceExpirationDate = Integer.parseInt(condition.getCustom_input_val());
         }
      }

      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      String[] tagArray;
      if (condition.getGroup_id() != null && condition.getGroup_id().length() > 0) {
         tagArray = condition.getGroup_id().split(",");
         if (tagArray != null && tagArray.length > 0) {
            for(int i = 0; i < tagArray.length; ++i) {
               boolean recursive = false;
               String tmpGroupId = tagArray[i];
               if (tmpGroupId.indexOf("c") != -1 && tmpGroupId.length() > 1) {
                  tmpGroupId = tmpGroupId.substring(0, tmpGroupId.length() - 1);
                  recursive = true;
               }

               if (Integer.parseInt(tmpGroupId) != 0) {
                  DeviceGroup deviceGroup = groupDao.getGroup(Integer.parseInt(tmpGroupId));
                  if (deviceGroup.getP_group_id() != 0L && deviceGroup.getP_group_id() != -1L) {
                     groupList.add(groupDao.getGroup(Integer.valueOf(tmpGroupId)));
                  }

                  if (recursive) {
                     groupList.addAll(groupDao.getChildGroupList(Integer.parseInt(tmpGroupId), true));
                  }
               }
            }
         }
      }

      if (condition.getDevice_type() != null) {
         condition.setDevice_type_arr(condition.getDevice_type().split(","));
      }

      int i;
      if (condition.getTagFilter() != null) {
         tagArray = condition.getTagFilter().split(",");
         List tagFilterList = new ArrayList();

         for(i = 0; i < tagArray.length; ++i) {
            tagFilterList.add(Long.valueOf(tagArray[i]));
         }

         condition.setTagFilterList(tagFilterList);
      }

      ArrayList result = new ArrayList();

      try {
         if (groupList.size() > 0) {
            List list = Lists.partition(groupList, 1500);

            for(i = 0; i < list.size(); ++i) {
               result.addAll(((DeviceDaoMapper)this.getMapper()).getDeviceMonitoringList(DaoTools.offsetStartPost(condition.getStart_index()), condition.getPage_size(), condition, (List)list.get(i), isDeviceGroupAuth, userId, deviceExpirationDate, this.getConstants()));
            }
         } else {
            result.addAll(((DeviceDaoMapper)this.getMapper()).getDeviceMonitoringList(DaoTools.offsetStartPost(condition.getStart_index()), condition.getPage_size(), (DeviceFilter)condition, groupList, isDeviceGroupAuth, userId, deviceExpirationDate, this.getConstants()));
         }
      } catch (SQLException var14) {
         this.logger.error("", var14);
      }

      return result;
   }

   public int getCntDeviceMonitoringList(DeviceFilter condition) throws NumberFormatException, SQLException {
      List groupList = new ArrayList();
      String roleName = condition.getRole_name();
      String userId = condition.getUser_id();
      boolean isDeviceGroupAuth = false;

      try {
         if (DeviceUtils.isDeviceGroupAuth(roleName, userId)) {
            isDeviceGroupAuth = true;
         }
      } catch (SQLException var13) {
         this.logger.error("", var13);
      }

      Integer deviceExpirationDate = null;
      if (condition.getExpiration_date() != null && !condition.getExpiration_date().equals("device_status_view_all")) {
         if (condition.getExpiration_date().equals("device_expiration_date_seven")) {
            deviceExpirationDate = 7;
         } else if (condition.getExpiration_date().equals("device_expiration_date_halfmonth")) {
            deviceExpirationDate = 15;
         } else if (condition.getExpiration_date().equals("device_expiration_date_month")) {
            deviceExpirationDate = 30;
         } else if (condition.getExpiration_date().equals("device_expiration_date_custom") && condition.getCustom_input_val() != null && !condition.getCustom_input_val().equals("")) {
            deviceExpirationDate = Integer.parseInt(condition.getCustom_input_val());
         }
      }

      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      int result;
      if (condition.getGroup_id() != null && condition.getGroup_id().length() > 0) {
         String[] groupArr = condition.getGroup_id().split(",");
         if (groupArr != null && groupArr.length > 0) {
            for(result = 0; result < groupArr.length; ++result) {
               boolean recursive = false;
               String tmpGroupId = groupArr[result];
               if (tmpGroupId.indexOf("c") != -1 && tmpGroupId.length() > 1) {
                  tmpGroupId = tmpGroupId.substring(0, tmpGroupId.length() - 1);
                  recursive = true;
               }

               if (Integer.parseInt(tmpGroupId) != 0) {
                  DeviceGroup deviceGroup = groupDao.getGroup(Integer.parseInt(tmpGroupId));
                  if (deviceGroup.getP_group_id() != 0L && deviceGroup.getP_group_id() != -1L) {
                     groupList.add(groupDao.getGroup(Integer.valueOf(tmpGroupId)));
                  }

                  if (recursive) {
                     groupList.addAll(groupDao.getChildGroupList(Integer.parseInt(tmpGroupId), true));
                  }
               }
            }
         }
      }

      String commonSearchKeyword = StrUtils.nvl(condition.getCommonSearchKeyword()).equals("") ? "" : condition.getCommonSearchKeyword();
      if (StringUtils.isNotBlank(commonSearchKeyword)) {
         condition.setCommonSearchKeyword(commonSearchKeyword);
      }

      if (condition.getDevice_type() != null) {
         condition.setDevice_type_arr(condition.getDevice_type().split(","));
      }

      int i;
      if (condition.getTagFilter() != null) {
         String[] tagArray = condition.getTagFilter().split(",");
         List tagFilterList = new ArrayList();

         for(i = 0; i < tagArray.length; ++i) {
            tagFilterList.add(Long.valueOf(tagArray[i]));
         }

         condition.setTagFilterList(tagFilterList);
      }

      result = 0;

      try {
         if (groupList.size() > 0) {
            List list = Lists.partition(groupList, 1500);

            for(i = 0; i < list.size(); ++i) {
               result += ((DeviceDaoMapper)this.getMapper()).getDeviceMonitoringListCnt(condition, (List)list.get(i), isDeviceGroupAuth, userId, deviceExpirationDate, this.getConstants());
            }
         } else {
            result = ((DeviceDaoMapper)this.getMapper()).getDeviceMonitoringListCnt((DeviceFilter)condition, groupList, isDeviceGroupAuth, userId, deviceExpirationDate, this.getConstants());
         }
      } catch (SQLException var14) {
         this.logger.error("", var14);
      }

      return result;
   }

   public PagedListInfo getDeviceMonitoringList(int startPos, int pageSize, Map condition) throws SQLException {
      List groupList = new ArrayList();
      SelectCondition condObj = (SelectCondition)condition.get("condition");
      String roleName = condObj.getRole_name();
      String userId = condObj.getUser_id();
      boolean isDeviceGroupAuth = false;
      if (DeviceUtils.isDeviceGroupAuth(roleName, userId)) {
         isDeviceGroupAuth = true;
      }

      Integer deviceExpirationDate = null;
      if (condObj.getExpiration_date() != null && !condObj.getExpiration_date().equals("device_status_view_all")) {
         if (condObj.getExpiration_date().equals("device_expiration_date_seven")) {
            deviceExpirationDate = 7;
         } else if (condObj.getExpiration_date().equals("device_expiration_date_halfmonth")) {
            deviceExpirationDate = 15;
         } else if (condObj.getExpiration_date().equals("device_expiration_date_month")) {
            deviceExpirationDate = 30;
         } else if (condObj.getExpiration_date().equals("device_expiration_date_custom") && condObj.getCustom_input_val() != null && !condObj.getCustom_input_val().equals("")) {
            deviceExpirationDate = Integer.parseInt(condObj.getCustom_input_val());
         }
      }

      String[] filterGroupId = null;
      if (condObj.getIsRoot() && condObj.getFilter_group_ids() != null && !condObj.getFilter_group_ids().equals("")) {
         filterGroupId = condObj.getFilter_group_ids().split(",");
      }

      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      int totCnt;
      if (filterGroupId != null && filterGroupId.length > 0) {
         String[] var17 = filterGroupId;
         totCnt = filterGroupId.length;

         for(int var14 = 0; var14 < totCnt; ++var14) {
            String groupId = var17[var14];
            ((List)groupList).add(groupDao.getGroup(Integer.valueOf(groupId)));
         }
      } else if (condObj.getGroup_id() != null && condObj.getGroup_id() == 0L) {
         List deviceOrgIds = DeviceUtils.getOrgGroupIdByUserId(condObj.getUser_id());
         if (deviceOrgIds != null && deviceOrgIds.size() > 0) {
            groupList = groupDao.getAllDeviceGroups(deviceOrgIds);
         }
      } else if (condObj.getGroup_id() != null && condObj.getGroup_id().intValue() != 0) {
         if (condObj.getIsRoot()) {
            groupList = groupDao.getAllDeviceGroups((long)condObj.getGroup_id().intValue());
         } else {
            DeviceGroup deviceGroup = groupDao.getGroup(condObj.getGroup_id().intValue());
            if (deviceGroup.getP_group_id() == 0L || deviceGroup.getP_group_id() == -1L) {
               groupList = groupDao.getAllDeviceGroups((long)condObj.getGroup_id().intValue());
            }
         }
      }

      if (condObj.getDevice_type() != null) {
         condObj.setDevice_type_arr(condObj.getDevice_type().split(","));
      }

      List result = new ArrayList();
      totCnt = 0;
      condObj.setRm_device_types(CommonDataConstants.RM_DEVICE_TYPE_ARRAY);
      if (((List)groupList).size() > 0) {
         List list = Lists.partition((List)groupList, 1500);

         for(int i = 0; i < list.size(); ++i) {
            result.addAll(((DeviceDaoMapper)this.getMapper()).getDeviceMonitoringList(DaoTools.offsetStartPost(startPos), pageSize, condObj, (List)list.get(i), isDeviceGroupAuth, userId, deviceExpirationDate, this.getConstants()));
            totCnt += ((DeviceDaoMapper)this.getMapper()).getDeviceMonitoringListCnt(condObj, (List)list.get(i), isDeviceGroupAuth, userId, deviceExpirationDate, this.getConstants());
         }
      } else {
         result.addAll(((DeviceDaoMapper)this.getMapper()).getDeviceMonitoringList(DaoTools.offsetStartPost(startPos), pageSize, (SelectCondition)condObj, (List)groupList, isDeviceGroupAuth, userId, deviceExpirationDate, this.getConstants()));
         totCnt = ((DeviceDaoMapper)this.getMapper()).getDeviceMonitoringListCnt((SelectCondition)condObj, (List)groupList, isDeviceGroupAuth, userId, deviceExpirationDate, this.getConstants());
      }

      return new PagedListInfo(result, totCnt);
   }

   public List getDeviceMonitoringFilterList(SelectCondition condition) throws SQLException {
      List groupList = null;
      if (condition.getGroup_id() != null && condition.getGroup_id().intValue() != 0 && condition.getIsRoot()) {
         DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
         groupList = groupDao.getChildGroupList(condition.getGroup_id().intValue(), true);
      }

      List result = new ArrayList();
      if (groupList != null && groupList.size() > 0) {
         List list = Lists.partition(groupList, 1500);

         for(int i = 0; i < list.size(); ++i) {
            result.addAll(((DeviceDaoMapper)this.getMapper()).getDeviceMonitoringFilterList(condition, (List)list.get(i)));
         }
      } else {
         result.addAll(((DeviceDaoMapper)this.getMapper()).getDeviceMonitoringFilterList(condition, groupList));
      }

      return ((DeviceDaoMapper)this.getMapper()).getDeviceMonitoringFilterList(condition, groupList);
   }

   public String getDeviceNameById(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceNameById(deviceId);
   }

   public Device getDeviceOperationInfo(String deviceId) throws SQLException {
      try {
         Device ret = DBCacheUtils.getDevice(deviceId);
         return ret;
      } catch (Exception var4) {
         Device device = ((DeviceDaoMapper)this.getMapper()).getDeviceOperationInfo(deviceId);
         if (device != null) {
            DBCacheUtils.setDevice(device, device.getDevice_id());
         }

         return device;
      }
   }

   public List getDMInfo(String device_id) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDMInfo(device_id);
   }

   public String getModelNameByDeviceId(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getModelNameByDeviceId(deviceId);
   }

   public List getModelNameListByDeviceId(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getModelNameListByDeviceId(deviceId);
   }

   public List getModelNameListByModelCode(String device_model_code) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getModelNameListByModelCode(device_model_code);
   }

   public List getMonitoringInfoListByDeviceId(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getMonitoringInfoByDeviceId(deviceId);
   }

   public boolean getDeviceApprovalStatusByDeviceId(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceApprovalStatusByDeviceId(deviceId);
   }

   public List getMonitoringInfoByDeviceIdList(String[] deviceIdList) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getMonitoringInfoByDeviceIdList(deviceIdList);
   }

   public PagedListInfo getNonApprovedDeviceListLimitByDeviceType(int startPos, int pageSize, Map condition, String deviceType, boolean extraFlag) throws SQLException {
      List result = ((DeviceDaoMapper)this.getMapper()).getNonApprovedDeviceListLimitByDeviceType(startPos - 1, pageSize, condition, deviceType, extraFlag);
      return new PagedListInfo(result, result.size());
   }

   public PagedListInfo getNonApprovedDeviceList(int startPos, int pageSize, Map condition) throws SQLException {
      List result = ((DeviceDaoMapper)this.getMapper()).getNonApprovedDeviceList(startPos - 1, pageSize, condition);
      int totCnt = ((DeviceDaoMapper)this.getMapper()).getNonApprovedDeviceListCnt(condition);
      return new PagedListInfo(result, totCnt);
   }

   public PagedListInfo getNonApprovedDeviceListLimit(int startPos, int pageSize, Map condition) throws SQLException {
      int licenseCnt = (Integer)condition.get("license_count");
      if (licenseCnt < 0) {
         licenseCnt = 0;
      }

      int newPageSize = licenseCnt - startPos + 1;
      List result;
      int totCnt;
      if (newPageSize <= pageSize) {
         result = ((DeviceDaoMapper)this.getMapper()).getNonApprovedDeviceList(startPos - 1, newPageSize, condition);
         totCnt = licenseCnt;
      } else {
         result = ((DeviceDaoMapper)this.getMapper()).getNonApprovedDeviceList(startPos - 1, pageSize, condition);
         totCnt = ((DeviceDaoMapper)this.getMapper()).getNonApprovedDeviceListCnt(condition);
         if (totCnt > licenseCnt) {
            totCnt = licenseCnt;
         }
      }

      return new PagedListInfo(result, totCnt);
   }

   public PagedListInfo getNonApprovedPremiumOnlyDeviceListLimit(int startPos, int pageSize, Map condition) throws SQLException {
      int licenseCnt = (Integer)condition.get("license_count");
      if (licenseCnt < 0) {
         licenseCnt = 0;
      }

      String[] deviceTypeFilter = null;
      if (condition.get("device_type") != null) {
         deviceTypeFilter = condition.get("device_type").toString().split(",");
      }

      int newPageSize = licenseCnt - startPos + 1;
      List result;
      int totCnt;
      if (newPageSize <= pageSize) {
         result = ((DeviceDaoMapper)this.getMapper()).getNonApprovedPremiumOnlyDeviceListLimit(DaoTools.offsetStartPost(startPos), newPageSize, condition, deviceTypeFilter);
         totCnt = licenseCnt;
      } else {
         result = ((DeviceDaoMapper)this.getMapper()).getNonApprovedPremiumOnlyDeviceListLimit(DaoTools.offsetStartPost(startPos), pageSize, condition, deviceTypeFilter);
         totCnt = ((DeviceDaoMapper)this.getMapper()).getNonApprovedPremiumOnlyDeviceListLimitCnt(condition, deviceTypeFilter);
         if (totCnt > licenseCnt) {
            totCnt = licenseCnt;
         }
      }

      return new PagedListInfo(result, totCnt);
   }

   public PagedListInfo getNonApprovedPremiumOnlyDeviceListLimitOpenAPI(int startPos, int pageSize, Map condition) throws SQLException {
      int licenseCnt = (Integer)condition.get("license_count");
      if (licenseCnt < 0) {
         licenseCnt = 0;
      }

      String mode = "";
      if (condition.get("mode") != null) {
         mode = (String)condition.get("mode");
      }

      Integer countOfExtraDisplay = null;
      if (condition.get("cost_license_count") != null) {
         countOfExtraDisplay = ((DeviceDaoMapper)this.getMapper()).getNonApprovedPremiumOnlyDeviceListLimitOpenAPISelect();
      }

      String[] deviceTypeFilter = null;
      if (condition.get("device_type") != null) {
         deviceTypeFilter = condition.get("device_type").toString().split(",");
      }

      Map constants = new HashMap();
      constants.put("TYPE_APLAYER", "APLAYER");
      constants.put("TYPE_WPLAYER", "WPLAYER");
      constants.put("TYPE_SOC3", "S3PLAYER");
      constants.put("TYPE_SOC2", "S2PLAYER");
      constants.put("TYPE_SOC", "SPLAYER");
      constants.put("TYPE_VERSION_2_0", CommonDataConstants.TYPE_VERSION_2_0);
      constants.put("TYPE_VERSION_3_0", CommonDataConstants.TYPE_VERSION_3_0);
      List result;
      if (mode.equals("MEGA")) {
         result = ((DeviceDaoMapper)this.getMapper()).getNonApprovedPremiumOnlyDeviceListLimitOpenAPIForMega(DaoTools.offsetStartPost(startPos), pageSize, condition, countOfExtraDisplay, constants);
      } else {
         int newPageSize = licenseCnt - startPos + 1;
         if (newPageSize <= pageSize) {
            result = ((DeviceDaoMapper)this.getMapper()).getNonApprovedPremiumOnlyDeviceListLimitOpenAPI(DaoTools.offsetStartPost(startPos), newPageSize, condition, deviceTypeFilter, countOfExtraDisplay, constants);
         } else {
            result = ((DeviceDaoMapper)this.getMapper()).getNonApprovedPremiumOnlyDeviceListLimitOpenAPI(DaoTools.offsetStartPost(startPos), pageSize, condition, deviceTypeFilter, countOfExtraDisplay, constants);
         }
      }

      return new PagedListInfo(result, result.size());
   }

   public List getAppVersionList() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getAppVersionList();
   }

   public List getAppVersionListByDeviceType(String deviceType) throws SQLException {
      boolean premiumType = false;
      if (deviceType.equals("iPLAYER")) {
         premiumType = true;
      }

      return ((DeviceDaoMapper)this.getMapper()).getAppVersionListByDeviceType(deviceType, premiumType);
   }

   public List getAppVersionListBy(Map map) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getAppVersionListBy(map);
   }

   public List getDeviceModelNameListBy(Map map) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceModelNameListBy(map);
   }

   public List getRuleVersionList() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getRuleVersionList();
   }

   public boolean moveDevice(String deviceId, int newParentGroupId) throws SQLException {
      int cnt = false;
      SqlSession session = this.openNewSession(false);

      int cnt;
      try {
         List deviceMapList = ((DeviceDaoMapper)this.getMapper(session)).moveDeviceSelect(deviceId);
         if (deviceMapList != null && deviceMapList.size() > 0) {
            Map deviceMap = (Map)deviceMapList.get(0);
            Long current_group_id = (Long)deviceMap.get("GROUP_ID");
            cnt = ((DeviceDaoMapper)this.getMapper(session)).moveDeviceUpdate(deviceId, current_group_id, newParentGroupId);
         } else {
            cnt = ((DeviceDaoMapper)this.getMapper(session)).moveDeviceInsert(deviceId, newParentGroupId);
         }

         if (cnt > 0) {
            session.commit();
         } else {
            session.rollback();
         }
      } catch (SQLException var11) {
         session.rollback();
         throw var11;
      } finally {
         session.close();
      }

      return cnt > 0;
   }

   public List selAllApprovedDevice() throws SQLException {
      List result = null;

      try {
         result = ((DeviceDaoMapper)this.getMapper()).selAllApprovedDevice();
      } catch (Exception var3) {
         this.logger.error("", var3);
      }

      return result;
   }

   public boolean setDevice(Device device, boolean runDBQuery) throws SQLException {
      if (device != null && device.getDevice_id() != null && !device.getDevice_id().equals("")) {
         DBCacheUtils.setDevice(device, device.getDevice_id());
         if (runDBQuery) {
            DBCacheUtils.runDBQueryThread(this.getMapper(), device);
         }

         return true;
      } else {
         return false;
      }
   }

   public boolean setDeviceForApproval(Map map) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         if (this.setNameDeviceAndModel(map, session) <= 0 || this.setDeviceGroupId(map, session) <= 0) {
            session.rollback();
            var3 = false;
            return var3;
         }

         session.commit();
         var3 = true;
      } catch (SQLException var7) {
         session.rollback();
         throw var7;
      } finally {
         session.close();
      }

      return var3;
   }

   public void setDeviceChildCount(String deviceId, Long childCount) {
      ((DeviceDaoMapper)this.getMapper()).setDeviceChildCount(deviceId, childCount);
   }

   public Long getDeviceGroupId(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceGroupId(deviceId);
   }

   public int setApprovalWithSeq(Map map) throws SQLException {
      SqlSession session = this.openNewSession(false);
      int seq = false;
      String prefix = (String)map.get("device_name");
      Long orgGroupId = (Long)map.get("orgGroupId");

      int cnt;
      int seq;
      try {
         do {
            seq = SequenceDB.getNextValue(prefix);
            cnt = this.getCntEqualDevName(prefix + "_" + seq, orgGroupId);
         } while(cnt > 0);
      } catch (Exception var14) {
         session.close();
         return -1;
      }

      map.put("device_name", prefix + "_" + seq);

      byte var7;
      try {
         if (this.setNameDeviceAndModel(map, session) >= 0 && this.setDeviceGroupId(map, session) >= 0) {
            session.commit();
            int var16 = seq;
            return var16;
         }

         session.rollback();
         var7 = -1;
      } catch (SQLException var12) {
         session.rollback();
         throw var12;
      } finally {
         session.close();
      }

      return var7;
   }

   private int setDeviceGroupId(Map map, SqlSession session) throws SQLException {
      String deviceId = (String)map.get("device_id");
      DownloadStatusDAO downloadStatusDAO = new DownloadStatusDAO(session);
      downloadStatusDAO.deleteScheduleDeployStatusByDeviceId(session, deviceId);
      downloadStatusDAO.deleteAllDownloadStatusByDeviceId(session, deviceId);
      DownloadStatusInfo downloadStatusInfo = DownloadStatusInfoImpl.getInstacne();
      downloadStatusInfo.deleteDownloadStatusInCache(deviceId);
      if (session != null) {
         try {
            Device device = DBCacheUtils.getDevice(deviceId);
            device.setDevice_id((String)map.get("device_id"));
            device.setGroup_id((Long)map.get("group_id"));
            device.setIs_overwrite_device_name(true);
            DBCacheUtils.setDevice(device, device.getDevice_id());
         } catch (Exception var7) {
            this.logger.error("", var7);
         }

         map.put("is_overwrite_device_name", true);
         return ((DeviceDaoMapper)this.getMapper(session)).setDeviceGroupId(map);
      } else {
         return ((DeviceDaoMapper)this.getMapper()).setDeviceGroupId(map);
      }
   }

   public int setDeviceGroupId(Map map) throws SQLException {
      String beforeMoveGroupId = this.getDeviceGroupIdByDeviceId(map.get("device_id").toString());
      String deviceId = map.get("device_id").toString();
      int cnt = this.setDeviceGroupId(map, (SqlSession)null);
      if (cnt <= 0) {
         return cnt;
      } else {
         Long child_cnt = (Long)map.get("child_cnt");
         if (child_cnt != null && child_cnt != 0L) {
            int cnt2 = this.setChildDeviceGroupId(map, (SqlSession)null);
            if ((long)cnt2 != child_cnt) {
               this.logger.error("[Device Update] Setting child device group was not work completely.");
            }
         }

         this.logger.error("[Device Update]-Update Device Map Group deviceId: " + deviceId + " beforeMoveGroupId: " + beforeMoveGroupId + " group_id: " + map.get("group_id"));

         try {
            if (!beforeMoveGroupId.equalsIgnoreCase("999999")) {
               DeviceUtils.refreshPriorityByDeviceType(Long.parseLong(beforeMoveGroupId), deviceId);
            }

            DeviceUtils.refreshPriorityByDeviceType((Long)map.get("group_id"), deviceId);
         } catch (NumberFormatException var9) {
            this.logger.error("", var9);
         } catch (Exception var10) {
            this.logger.error("", var10);
         }

         int groupId = Integer.parseInt(map.get("group_id").toString());
         String groupType = map.get("device_type").toString();
         if (groupId >= 0 && groupType != null && !groupType.equals("")) {
            return cnt;
         } else {
            this.logger.error("[setDeviceGroupId] set group_type fail. groupId=" + groupId + ",groupType=" + groupType);
            return cnt;
         }
      }
   }

   private int setChildDeviceGroupId(Map map, SqlSession session) throws SQLException {
      Long child_cnt = (Long)map.get("child_cnt");
      if (session == null) {
         return ((DeviceDaoMapper)this.getMapper()).setChildDeviceGroupId(map);
      } else {
         for(int i = 1; (long)i < child_cnt; ++i) {
            try {
               String deviceId = (String)map.get("device_id");
               Device device = DBCacheUtils.getDevice(deviceId);
               device.setGroup_id((Long)map.get("group_id"));
               device.setIs_overwrite_device_name(true);
               DBCacheUtils.setDevice(device, device.getDevice_id());
            } catch (Exception var7) {
               this.logger.error("", var7);
            }
         }

         return ((DeviceDaoMapper)this.getMapper(session)).setChildDeviceGroupId(map);
      }
   }

   public boolean setDeviceModel(DeviceModel deviceModel) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).setDeviceModel(deviceModel);
   }

   public boolean updateDiskspaceChannel(String deviceId, long diskSpace, String channel) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).updateDiskSpaceChannel(deviceId, diskSpace, channel == null ? "N" : channel);
   }

   public boolean setDeviceOperationInfo(Device device) throws SQLException {
      boolean rtn = false;

      try {
         if (((DeviceDaoMapper)this.getMapper()).setDeviceOperationInfo(device) > 0) {
            this.addDeviceDisplay(device.getDevice_id(), (SqlSession)null);
            this.addStatRequestTime(device.getDevice_id(), (SqlSession)null);
            DBCacheUtils.setDevice(device, device.getDevice_id());
            rtn = true;
         }
      } catch (SQLException var4) {
         this.logger.error("", var4);
      }

      return rtn;
   }

   public int setIsApproved(Map map) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).setIsApproved(map);
   }

   private int setNameDeviceAndModel(Map map, SqlSession session) throws SQLException {
      String calDate = (String)map.get("calDate");
      Timestamp calTimestamp = null;
      if (calDate != null && !calDate.equals("")) {
         calTimestamp = DateUtils.string2Timestamp(calDate, "yyyy-MM-dd");
      }

      if (session != null) {
         try {
            String deviceId = (String)map.get("device_id");
            Device device = DBCacheUtils.getDevice(deviceId);
            device.setDevice_id((String)map.get("device_id"));
            device.setLocation((String)map.get("location"));
            device.setIs_approved(Boolean.TRUE);
            device.setDevice_name((String)map.get("device_name"));
            device.setDevice_model_name((String)map.get("device_model_name"));
            device.setCreate_date(new Timestamp(System.currentTimeMillis()));
            device.setIs_overwrite_device_name(true);
            DBCacheUtils.setDevice(device, device.getDevice_id());
         } catch (Exception var7) {
            this.logger.error("", var7);
         }

         map.put("is_overwrite_device_name", true);
         return ((DeviceDaoMapper)this.getMapper(session)).setNameDeviceAndModel(map, calTimestamp);
      } else {
         return ((DeviceDaoMapper)this.getMapper()).setNameDeviceAndModel(map, calTimestamp);
      }
   }

   public int setNameDeviceAndModel(Map map) throws SQLException {
      return this.setNameDeviceAndModel(map, (SqlSession)null);
   }

   public List getConnectedDeviceModelNameList() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getConnectedDeviceModelNameList();
   }

   public List getDeviceModelNameList() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceModelNameList();
   }

   public List getDeviceLiteModelNameList() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceLiteModelNameList();
   }

   public List getDeviceModelTypeList() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceModelTypeList();
   }

   public boolean setDevicePostBootstrap(Device device, boolean runDBQuery) throws SQLException {
      if (device != null && device.getDevice_id() != null && !device.getDevice_id().equals("")) {
         DBCacheUtils.setDevice(device, device.getDevice_id());
         if (runDBQuery) {
            DBCacheUtils.runDBQueryThread(this.getMapper(), device);
         }

         return true;
      } else {
         return false;
      }
   }

   public boolean setDeviceNameAndLocation(String device_id, String device_name, String location, String map_location, String deviceModelName) throws SQLException {
      try {
         Device device = DBCacheUtils.getDevice(device_id);
         device.setDevice_id(device_id);
         if (device_name != null) {
            device.setDevice_name(device_name);
         }

         if (location != null) {
            device.setLocation(location);
         }

         if (map_location != null) {
            device.setMap_location(map_location);
         }

         if (deviceModelName != null) {
            device.setDevice_model_name(deviceModelName);
         }

         DBCacheUtils.setDevice(device, device_id);
      } catch (Exception var7) {
         this.logger.error("", var7);
      }

      return ((DeviceDaoMapper)this.getMapper()).setDeviceNameAndLocation(device_id, device_name, location, map_location, deviceModelName);
   }

   public boolean setLastConnectionTime(String deviceId) throws SQLException {
      try {
         Device device = DBCacheUtils.getDevice(deviceId);
         device.setDevice_id(deviceId);
         device.setLast_connection_time(new Timestamp(System.currentTimeMillis()));
         DBCacheUtils.setDevice(device, deviceId);
      } catch (Exception var3) {
         this.logger.error("", var3);
      }

      return true;
   }

   public boolean setShutDownConnectionTime(String deviceId, Long monInterval) throws SQLException {
      long currTime = System.currentTimeMillis() - (monInterval + 1L) * 60000L;

      try {
         Device device = DBCacheUtils.getDevice(deviceId);
         device.setDevice_id(deviceId);
         device.setLast_connection_time(new Timestamp(currTime));
         DBCacheUtils.setDevice(device, deviceId);
      } catch (NullPointerException var6) {
         this.logger.error("[Device] is not exist in cache. " + deviceId);
      } catch (Exception var7) {
         this.logger.error("", var7);
      }

      return ((DeviceDaoMapper)this.getMapper()).setShutDownConnectionTime(deviceId, new Timestamp(currTime));
   }

   public List getFirmwareVersionList() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getFirmwareVersionList();
   }

   public List getOSImageVersionList() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getOSImageVersionList();
   }

   public List getDeviceResolutionList(String deviceType) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceResolutionList(deviceType);
   }

   public boolean deleteBindingDevice(Map map) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).deleteBindingDevice((String)map.get("ip_address"));
   }

   public boolean addBindingDevice(Map map) throws SQLException {
      Long count = ((DeviceDaoMapper)this.getMapper()).addBindingDeviceSelect(map);
      return count == 0L && ((DeviceDaoMapper)this.getMapper()).addBindingDeviceInsert(map);
   }

   public List getBindingDeviceList(Map map) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getBindingDeviceList((String)map.get("src_name"));
   }

   public List getBindingDeviceListPage(Map map, int startPos, int pageSize) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getBindingDeviceListPage((String)map.get("src_name"), startPos - 1, pageSize);
   }

   public int getBindingDeviceListCnt(Map map) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getBindingDeviceListCnt((String)map.get("src_name"));
   }

   public int getAllDeviceCount(String deviceType) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getAllDeviceCount(deviceType);
   }

   public int getAllDeviceCountByOrganization(String deviceType, String organization) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getAllDeviceCountByOrganization(deviceType, organization);
   }

   public int getApprovalDeviceCount(String deviceType) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getApprovalDeviceCount(deviceType);
   }

   public int getNonApprovalDeviceCount(String deviceType) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getNonApprovalDeviceCount(deviceType);
   }

   public int getApprovalPremiumDeviceCount() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getApprovalPremiumDeviceCount();
   }

   public int getApprovalExtraDisplayDeviceCount() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getApprovalExtraDisplayDeviceCount();
   }

   public boolean addDeviceBindingInfo(Device device) throws SQLException {
      Long result = ((DeviceDaoMapper)this.getMapper()).addDeviceBindingInfoSelect(device);
      return result >= 0L && ((DeviceDaoMapper)this.getMapper()).addDeviceBindingInfoInsert(device);
   }

   public boolean deleteDeviceBindingInfo(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).deleteDeviceBindingInfoByDeviceId(deviceId);
   }

   public boolean deleteDeviceBindingInfo() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).deleteDeviceBindingInfo();
   }

   public String getDeviceModelCodeByDeviceId(String deviceId) throws SQLException {
      try {
         String cacheDeviceModelCode = DBCacheUtils.getDevice(deviceId).getDevice_model_code();
         if (cacheDeviceModelCode != null && !cacheDeviceModelCode.equals("")) {
            return cacheDeviceModelCode;
         }
      } catch (Exception var3) {
      }

      return ((DeviceDaoMapper)this.getMapper()).getDeviceModelCodeByDeviceId(deviceId);
   }

   public String getDeviceGroupIdByDeviceId(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceGroupIdByDeviceId(deviceId);
   }

   public String getDeviceGroupIdByDeviceId(SqlSession sqlSession, String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper(sqlSession)).getDeviceGroupIdByDeviceId(deviceId);
   }

   public List getDeviceIdGroupIdByDeviceName(String deviceName) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceIdGroupIdByDeviceName(deviceName);
   }

   public String getProgramIdByDeviceId(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getProgramIdByDeviceId(deviceId);
   }

   public String getEventScheduleIdByDeviceId(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getEventScheduleIdByDeviceId(deviceId);
   }

   public String getScheduleIdByProgramId(String programId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getScheduleIdByProgramId(programId);
   }

   public long getVersionByProgramId(String programId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getVersionByProgramId(programId);
   }

   public String getApprovalByDeviceId(String deviceId) throws SQLException {
      Map result = ((DeviceDaoMapper)this.getMapper()).getApprovalByDeviceId(deviceId);
      String retValue = "false";
      if (result != null && result.get("is_approved") != null && (Boolean)result.get("is_approved")) {
         retValue = "true";
      }

      return retValue;
   }

   public List getDeviceIdByDesc(String deviceType) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceIdByDesc(deviceType);
   }

   public List getDeviceIdByAsc(String deviceType) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceIdByAsc(deviceType);
   }

   /** @deprecated */
   @Deprecated
   public boolean isVwlConsole(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).isVwlConsole(deviceId);
   }

   public int getCntDeviceByDeviceType(String deviceType) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getCntDeviceByDeviceType(deviceType);
   }

   public boolean refreshDeviceGroupType(int groupId) throws SQLException {
      if (((DeviceDaoMapper)this.getMapper()).refreshDeviceGroupType(groupId) <= 0) {
         DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
         DeviceGroup deviceGroup = groupDao.getGroup(groupId);
         if (deviceGroup != null && deviceGroup.getGroup_type() != null && !deviceGroup.getGroup_type().equals("")) {
            groupDao.setDeviceGroupType(groupId, "");
         }
      }

      return true;
   }

   public List getDeviceIdListByGroup(int groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceIdListByGroup(groupId);
   }

   public List getNotChildDeviceIdListByGroup(int groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getNotChildDeviceIdListByGroup(groupId);
   }

   private boolean addDeviceDisplay(String deviceId, SqlSession session) throws SQLException {
      Long count;
      if (session != null) {
         count = ((DeviceDaoMapper)this.getMapper(session)).addDeviceDisplaySelect(deviceId);
         return count <= 0L && ((DeviceDaoMapper)this.getMapper(session)).addDeviceDisplayInsert(deviceId);
      } else {
         count = ((DeviceDaoMapper)this.getMapper()).addDeviceDisplaySelect(deviceId);
         return count <= 0L && ((DeviceDaoMapper)this.getMapper()).addDeviceDisplayInsert(deviceId);
      }
   }

   public boolean addDeviceDisplay(String deviceId) throws SQLException {
      return this.addDeviceDisplay(deviceId, (SqlSession)null);
   }

   private boolean addStatRequestTime(String deviceId, SqlSession session) throws SQLException {
      DeviceDaoMapper mapper = null;
      if (session != null) {
         mapper = (DeviceDaoMapper)this.getMapper(session);
      } else {
         mapper = (DeviceDaoMapper)this.getMapper();
      }

      if (mapper.addStatRequestTimeSelect(deviceId) <= 0) {
         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
         Calendar cal = Calendar.getInstance();
         cal.add(5, -1);
         String yesterday = sdf.format(cal.getTime());
         Timestamp requestTime = Timestamp.valueOf(yesterday);
         return mapper.addStatRequestTimeInsert(deviceId, requestTime);
      } else {
         return false;
      }
   }

   public boolean addStatRequestTime(String deviceId) throws SQLException {
      return this.addStatRequestTime(deviceId, (SqlSession)null);
   }

   public boolean addStatRequestTimeInsertCurrent(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).addStatRequestTimeInsertCurrent(deviceId);
   }

   public boolean isRequestTimeExist(String deviceId) throws SQLException {
      int requestCnt = ((DeviceDaoMapper)this.getMapper()).addStatRequestTimeSelect(deviceId);
      return requestCnt > 0;
   }

   public List getApprovalDeviceIdByAsc(String deviceType) throws SQLException {
      String[] socType;
      if (deviceType.equals("SPLAYER")) {
         socType = new String[]{"SPLAYER", "S2PLAYER", "S3PLAYER", "S4PLAYER", "S5PLAYER", "SIGNAGE", "LEDBOX4"};
         return ((DeviceDaoMapper)this.getMapper()).getApprovalDeviceIdByDeviceTypeSocAsc(socType);
      } else if (deviceType.equals("RMS")) {
         socType = new String[]{"RMS"};
         return ((DeviceDaoMapper)this.getMapper()).getApprovalDeviceIdByDeviceTypeSocAsc(socType);
      } else if (deviceType.equals("SIGNAGE")) {
         socType = new String[]{"SIGNAGE"};
         return ((DeviceDaoMapper)this.getMapper()).getApprovalDeviceIdByDeviceTypeSocAsc(socType);
      } else {
         return ((DeviceDaoMapper)this.getMapper()).getApprovalDeviceIdByDeviceTypeAsc(deviceType);
      }
   }

   public List getApprovalDeviceIdByDeviceTypeListAsc(List deviceType) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getApprovalDeviceIdByDeviceTypeListAsc(deviceType);
   }

   public List getApprovalDeviceIdByDeviceTypeListInOrgAssignedLicenseAsc(List deviceType) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getApprovalDeviceIdByDeviceTypeListInOrgAssignedLicenseAsc(deviceType);
   }

   public Long getDeviceMonitoringInterval(String deviceId) throws SQLException {
      try {
         Long cacheMonitoringInterval = DBCacheUtils.getDevice(deviceId).getMonitoring_interval();
         if (cacheMonitoringInterval != null && cacheMonitoringInterval > 0L) {
            return cacheMonitoringInterval;
         }
      } catch (Exception var3) {
      }

      return ((DeviceDaoMapper)this.getMapper()).getDeviceMonitoringInterval(deviceId);
   }

   public List getConnectedDeviceModelNameListTypeS(String deviceType, String organization) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getConnectedDeviceModelNameListTypeS(deviceType, organization);
   }

   public String getDeviceModelNameByDeviceId(String deviceId) throws SQLException {
      try {
         String cacheDeviceModelName = DBCacheUtils.getDevice(deviceId).getDevice_model_name();
         this.logger.fatal("[Serial Bug Check] cacheDeviceModelName : " + cacheDeviceModelName);
         if (cacheDeviceModelName != null && !cacheDeviceModelName.equals("")) {
            this.logger.fatal("[Serial Bug Check] return : " + cacheDeviceModelName);
            return cacheDeviceModelName;
         }
      } catch (Exception var3) {
      }

      Map result = ((DeviceDaoMapper)this.getMapper()).getDeviceModelNameByDeviceId(deviceId);
      return (String)result.get("device_model_name");
   }

   public String getIsRedundancy(int groupId) throws SQLException {
      Map result = ((DeviceDaoMapper)this.getMapper()).getIsRedundancy(groupId);
      return result == null ? "" : (String)result.get("DEVICE_ID");
   }

   public boolean addRedundancyStatus(String deviceId, boolean redundanctStatus) throws SQLException {
      int cnt = ((DeviceDaoMapper)this.getMapper()).addRedundancyStatus(deviceId, redundanctStatus);
      return cnt > 0;
   }

   public boolean isRedundancyDevice(String deviceId) throws SQLException {
      Map result = ((DeviceDaoMapper)this.getMapper()).isRedundancyDevice(deviceId);
      boolean isRedundancy = Boolean.parseBoolean(result.get("is_redundancy").toString());
      return isRedundancy;
   }

   public String getVwtIdByDeviceId(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getVwtIdByDeviceId(deviceId);
   }

   public String getVwtFileName(String vwtId) throws SQLException {
      Map result = ((DeviceDaoMapper)this.getMapper()).getVwtFileName(vwtId);
      return result == null ? "" : (String)result.get("vwt_file_name");
   }

   public boolean addDeviceTypeVersion(String deviceId, Float deviceTypeVersion) throws SQLException {
      BigDecimal deviceTypeVersionBigDecimal = new BigDecimal(String.valueOf(deviceTypeVersion));
      int cnt = ((DeviceDaoMapper)this.getMapper()).addDeviceTypeVersion(deviceId, deviceTypeVersionBigDecimal);
      return cnt > 0;
   }

   public boolean addDeviceInfoAtApprove(Device device) throws SQLException {
      int cnt = ((DeviceDaoMapper)this.getMapper()).addDeviceInfoAtApprove(device);
      return cnt > 0;
   }

   public Long getMinimalPriorityByGroupId(long groupId) throws SQLException {
      Long groupPriority = DeviceConstants.DEV_GROUP_BASIC_PRIORITY;
      String[] var4 = CommonDataConstants.ALL_DEVICE_TYPE_ARRAY;
      int var5 = var4.length;

      for(int var6 = 0; var6 < var5; ++var6) {
         String deviceType = var4[var6];
         Float deviceTypeVersion = ((DeviceDaoMapper)this.getMapper()).getMinimalDeviceTypeVersionByDeviceTypeAndGroupId(deviceType, groupId);
         if (deviceTypeVersion != null) {
            BigDecimal deviceTypeVersionBigDecimal = new BigDecimal(String.valueOf(deviceTypeVersion));
            Long tempPriority = ((DeviceDaoMapper)this.getMapper()).getPriorityByDeviceTypeAndVersion(deviceType, deviceTypeVersionBigDecimal);
            if (tempPriority != null && groupPriority > tempPriority) {
               groupPriority = tempPriority;
            }
         }
      }

      return groupPriority;
   }

   public Long getDeviceGroupPriority(long groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceGroupPriority(groupId);
   }

   public boolean addDeviceWaitingMo(String deviceId) throws SQLException {
      int cnt = ((DeviceDaoMapper)this.getMapper()).addDeviceWaitingMo(deviceId, (String)null, (String)null);
      return cnt > 0;
   }

   public String getDeviceWaitingMo(String deviceId) throws SQLException {
      String moInfo = ((DeviceDaoMapper)this.getMapper()).getDeviceWaitingMo(deviceId);
      return moInfo;
   }

   public boolean deleteWaitingMo(String deviceId) throws SQLException {
      int cnt = ((DeviceDaoMapper)this.getMapper()).deleteWaitingMo(deviceId);
      return cnt > 0;
   }

   public int setDeviceWaitingMo(String deviceId, String serviceName, String infoValue) throws SQLException {
      int cnt = ((DeviceDaoMapper)this.getMapper()).setDeviceWaitingMo(serviceName, infoValue, deviceId);
      return cnt;
   }

   public String getDayLightSavingManual(String deviceId) {
      String daylightSavingManual = ((DeviceDaoMapper)this.getMapper()).getDayLightSavingManual(deviceId);
      return daylightSavingManual;
   }

   private Map getConstants() {
      return DeviceConstants.getConstantsMap();
   }

   public int insertDisasterAlertStatus(DisasterAlertStatusEntity DisasterAlertStatus) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).insertDisasterAlertStatus(DisasterAlertStatus);
   }

   public List selectDisasterAlertStatus(String alert_id) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).selectDisasterAlertStatus(alert_id);
   }

   public List selectDisasterAlertStatusDisconnected(String device_id) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).selectDisasterAlertStatusDisconnected(device_id);
   }

   public List selectDisasterAlertStatusByDeviceId(String device_id) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).selectDisasterAlertStatusByDeviceId(device_id);
   }

   public List selectSimpleDisasterAlertStatus(String alert_id) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).selectSimpleDisasterAlertStatus(alert_id);
   }

   public List getDisconnectedDisasterAlertByDeviceIdAndAlertId(String device_id, String alert_id) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDisconnectedDisasterAlertByDeviceIdAndAlertId(device_id, alert_id);
   }

   public int updateDisasterAlertStatus(DisasterAlertStatusEntity DisasterAlertStatus) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).updateDisasterAlertStatus(DisasterAlertStatus);
   }

   public void deleteDisasterAlertStatus(String alert_id) throws SQLException {
      ((DeviceDaoMapper)this.getMapper()).deleteDisasterAlertStatus(alert_id);
   }

   public void deleteDisconnectedDisasterAlertStatus(String device_id, String alert_id) throws SQLException {
      ((DeviceDaoMapper)this.getMapper()).deleteDisconnectedDisasterAlertStatus(device_id, alert_id);
   }

   public int insertExtDeviceInfo(DeviceLoopOutEntity deviceLoopOutInfo) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).insertExtDeviceInfo(deviceLoopOutInfo);
   }

   public List selectExtDeviceInfo(String device_id, String type) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).selectExtDeviceInfo(device_id, type);
   }

   public int updateExtDeviceInfo(DeviceLoopOutEntity deviceLoopOutInfo) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).updateExtDeviceInfo(deviceLoopOutInfo);
   }

   public void deleteExtDeviceInfo(String device_id) throws SQLException {
      ((DeviceDaoMapper)this.getMapper()).deleteExtDeviceInfo(device_id);
   }

   public Map getSoftwareUpdate(String device_id) throws Exception {
      return ((DeviceDaoMapper)this.getMapper()).getSoftwareUpdate(device_id);
   }

   public boolean setKeepAliveInfo(String deviceId, Long disk_space, String channel, String curr_content_id) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).setKeepAliveInfo(deviceId, disk_space, channel, curr_content_id, (String)null, (String)null);
   }

   public boolean setKeepAliveInfo(String deviceId, Long disk_space, String channel, String curr_content_id, String diskSpaceUsage, String diskSpaceAvailable) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).setKeepAliveInfo(deviceId, disk_space, channel, curr_content_id, diskSpaceUsage, diskSpaceAvailable);
   }

   public PagedListInfo getCheckDeviceList(int startPos, int pageSize, Map condition, String[] deviceTypeFilter) throws SQLException {
      DeviceGeneralSearch search = null;
      SelectCondition condObj = (SelectCondition)condition.get("condition");
      Long search_id = null;
      if (condition.get("search_id") != null && !condition.get("search_id").equals("") && !((String)condition.get("search_id")).equalsIgnoreCase("null")) {
         search_id = Long.parseLong((String)condition.get("search_id"));
      }

      List deviceGroupList = (List)condition.get("deviceGroupList");
      if (search_id != null) {
         DeviceGeneralSearchInfo searchInfo = DeviceGeneralSearchInfoImpl.getInstance();
         search = searchInfo.getDeviceGeneralSearchBySearchId(search_id);
      }

      String roleName = condObj.getRole_name();
      String userId = condObj.getUser_id();
      boolean isDeviceGroupAuth = false;
      if (DeviceUtils.isDeviceGroupAuth(roleName, userId)) {
         isDeviceGroupAuth = true;
      }

      List result = ((DeviceDaoMapper)this.getMapper()).getCheckDeviceList(startPos - 1, pageSize, condObj, deviceGroupList, search, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId);
      int totCnt = ((DeviceDaoMapper)this.getMapper()).getCheckDeviceListCnt(startPos - 1, pageSize, condObj, deviceGroupList, search, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId);
      return new PagedListInfo(result, totCnt);
   }

   public PagedListInfo getCheckDeviceListTimezone(int startPos, int pageSize, Map condition, String[] deviceTypeFilter) throws SQLException {
      DeviceGeneralSearch search = null;
      SelectCondition condObj = (SelectCondition)condition.get("condition");
      Long search_id = null;
      if (condition.get("search_id") != null && !condition.get("search_id").equals("") && !((String)condition.get("search_id")).equalsIgnoreCase("null")) {
         search_id = Long.parseLong((String)condition.get("search_id"));
      }

      if (search_id != null) {
         DeviceGeneralSearchInfo searchInfo = DeviceGeneralSearchInfoImpl.getInstance();
         searchInfo.getDeviceGeneralSearchBySearchId(search_id);
      }

      String roleName = condObj.getRole_name();
      String userId = condObj.getUser_id();
      Long orgGroupId = condObj.getOrg_id();
      boolean isDeviceGroupAuth = false;
      if (DeviceUtils.isDeviceGroupAuth(roleName, userId)) {
         isDeviceGroupAuth = true;
      }

      List groupList = this.getGroupIdsByOrgManagerUserId(userId, orgGroupId);
      int total = ((DeviceDaoMapper)this.getMapper()).getCountTimezoneNotSet(groupList, userId, isDeviceGroupAuth, condObj);
      List result = ((DeviceDaoMapper)this.getMapper()).getListTimezoneNotSet(startPos - 1, pageSize, groupList, userId, isDeviceGroupAuth, condObj);
      return new PagedListInfo(result, total);
   }

   public PagedListInfo getCheckDeviceListSchedule(int startPos, int pageSize, Map condition, String[] deviceTypeFilter) throws SQLException {
      DeviceGeneralSearch search = null;
      SelectCondition condObj = (SelectCondition)condition.get("condition");
      Long search_id = null;
      if (condition.get("search_id") != null && !condition.get("search_id").equals("") && !((String)condition.get("search_id")).equalsIgnoreCase("null")) {
         search_id = Long.parseLong((String)condition.get("search_id"));
      }

      if (search_id != null) {
         DeviceGeneralSearchInfo searchInfo = DeviceGeneralSearchInfoImpl.getInstance();
         searchInfo.getDeviceGeneralSearchBySearchId(search_id);
      }

      String roleName = condObj.getRole_name();
      String userId = condObj.getUser_id();
      Long orgGroupId = condObj.getOrg_id();
      condObj.setRm_device_types(CommonDataConstants.RM_DEVICE_TYPE_ARRAY);
      boolean isDeviceGroupAuth = false;
      if (DeviceUtils.isDeviceGroupAuth(roleName, userId)) {
         isDeviceGroupAuth = true;
      }

      List groupList = this.getGroupIdsByOrgManagerUserId(userId, orgGroupId);
      int total = ((DeviceDaoMapper)this.getMapper()).getCountScheduleNotPublish(groupList, userId, isDeviceGroupAuth, condObj);
      List result = ((DeviceDaoMapper)this.getMapper()).getListScheduleNotPublish(startPos - 1, pageSize, groupList, userId, isDeviceGroupAuth, condObj);
      return new PagedListInfo(result, total);
   }

   public PagedListInfo getCheckDeviceListScheduleFail(int startPos, int pageSize, Map condition, String[] deviceTypeFilter) throws SQLException {
      DeviceGeneralSearch search = null;
      SelectCondition condObj = (SelectCondition)condition.get("condition");
      Long search_id = null;
      if (condition.get("search_id") != null && !condition.get("search_id").equals("") && !((String)condition.get("search_id")).equalsIgnoreCase("null")) {
         search_id = Long.parseLong((String)condition.get("search_id"));
      }

      if (search_id != null) {
         DeviceGeneralSearchInfo searchInfo = DeviceGeneralSearchInfoImpl.getInstance();
         search = searchInfo.getDeviceGeneralSearchBySearchId(search_id);
      }

      String roleName = condObj.getRole_name();
      String userId = condObj.getUser_id();
      boolean isDeviceGroupAuth = false;
      if (DeviceUtils.isDeviceGroupAuth(roleName, userId)) {
         isDeviceGroupAuth = true;
      }

      List deviceGroupList = (List)condition.get("deviceGroupList");
      int total = 0;
      List result = new ArrayList();
      if (deviceGroupList != null) {
         List sublist = Lists.partition(deviceGroupList, 1500);

         for(int i = 0; i < sublist.size(); ++i) {
            List tmp = ((DeviceDaoMapper)this.getMapper()).getCheckDeviceListScheduleFail(startPos - 1, pageSize, condObj, (List)sublist.get(i), search, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId);
            result.addAll(tmp);
            total += ((DeviceDaoMapper)this.getMapper()).getCheckDeviceListCntScheduleFail(startPos - 1, pageSize, condObj, (List)sublist.get(i), search, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId);
         }
      }

      return new PagedListInfo(result, total);
   }

   public PagedListInfo getCheckUpcomingExpiryDatePlaylist(int startPos, int pageSize, Map condition) throws SQLException {
      SelectCondition condObj = (SelectCondition)condition.get("condition");
      String userId = condObj.getUser_id();
      Long orgGroupId = condObj.getOrg_id();
      String stopDate = DateUtils.setDate(2);
      PlaylistInfo playlistDao = PlaylistInfoImpl.getInstance();
      List list = null;
      int total = 0;

      try {
         list = playlistDao.getListPlaylistToExpire(startPos, pageSize, userId, orgGroupId, stopDate, condObj);
         total = playlistDao.getCountPlaylistToExpire(userId, orgGroupId, stopDate, condObj);
      } catch (Exception var12) {
         this.logger.error("", var12);
      }

      return new PagedListInfo(list, total);
   }

   public List getCheckUpcomingExpiryDatePlaylistList(int addDate, String deviceOrgId) throws SQLException {
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      String orgName = groupDao.getGroupNameByGroupId(Long.valueOf(deviceOrgId));
      String stopDate = DateUtils.setDate(addDate);
      SelectCondition condObj = new SelectCondition();
      condObj.setOrder_dir("DESC");
      condObj.setSort_name("create_date");
      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      Integer scheduleOrgId = programGroupInfo.getOrganizationIdByName(orgName);
      if (scheduleOrgId != null) {
         PlaylistInfo playlistDao = PlaylistInfoImpl.getInstance();
         return playlistDao.getListPlaylistToExpire(-1, -1, orgName, (long)scheduleOrgId, stopDate, condObj);
      } else {
         this.logger.error("Cannot found schedule org id. Organization : " + orgName);
         return null;
      }
   }

   public List getCheckUpcomingExpiryDateList(int addDate, Long deviceOrgId) throws SQLException {
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      String orgName = groupDao.getGroupNameByGroupId(deviceOrgId);
      if (orgName == null) {
         this.logger.error("Cannot found Device Organization Name. Organization Id : " + deviceOrgId);
         return null;
      } else {
         String stopDate = DateUtils.setDate(addDate);
         SelectCondition condObj = new SelectCondition();
         condObj.setOrder_dir("DESC");
         condObj.setSort_name("stop_date");
         ScheduleInfo scheduleDao = ScheduleInfoImpl.getInstance();
         ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
         Integer scheduleOrgId = programGroupInfo.getOrganizationIdByName(orgName);
         if (scheduleOrgId != null) {
            return scheduleDao.getListScheduleToExpire(-1, -1, orgName, (long)scheduleOrgId, stopDate, condObj);
         } else {
            this.logger.error("Cannot found schedule org id. Organization : " + orgName);
            return null;
         }
      }
   }

   public PagedListInfo getCheckUpcomingExpiryDate(int startPos, int pageSize, Map condition) throws SQLException {
      SelectCondition condObj = (SelectCondition)condition.get("condition");
      String userId = condObj.getUser_id();
      Long orgGroupId = condObj.getOrg_id();
      String stopDate = DateUtils.setDate(2);
      ScheduleInfo scheduleDao = ScheduleInfoImpl.getInstance();
      List list = scheduleDao.getListScheduleToExpire(startPos - 1, pageSize, userId, orgGroupId, stopDate, condObj);
      int total = scheduleDao.getCountScheduleToExpire(userId, orgGroupId, stopDate, condObj);
      return new PagedListInfo(list, total);
   }

   public List getCheckUpcomingExpiryDateListDevice(int addDate, String orgId) throws SQLException {
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      String orgName = groupDao.getGroupNameByGroupId(Long.valueOf(orgId));
      if (orgName == null) {
         this.logger.error("Cannot found Device Organization Name. Organization Id : " + orgId);
         return null;
      } else {
         String stopDate = DateUtils.setDate(2);
         SelectCondition condObj = new SelectCondition();
         condObj.setOrder_dir("DESC");
         condObj.setSort_name("stop_date");
         ScheduleInfo scheduleDao = ScheduleInfoImpl.getInstance();
         ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
         Integer scheduleOrgId = programGroupInfo.getOrganizationIdByName(orgName);
         if (scheduleOrgId != null) {
            return scheduleDao.getDeviceListByScheduleToExpire(-1, -1, orgName, (long)scheduleOrgId, stopDate, condObj);
         } else {
            this.logger.error("Cannot find schedule org id. Organization : " + orgName);
            return null;
         }
      }
   }

   public PagedListInfo getCheckUpcomingExpiryDateDevice(int startPos, int pageSize, Map condition) throws SQLException {
      SelectCondition condObj = (SelectCondition)condition.get("condition");
      String userId = condObj.getUser_id();
      Long orgGroupId = condObj.getOrg_id();
      String stopDate = DateUtils.setDate(2);
      ScheduleInfo scheduleDao = ScheduleInfoImpl.getInstance();
      List list = scheduleDao.getDeviceListByScheduleToExpire(startPos - 1, pageSize, userId, orgGroupId, stopDate, condObj);
      int total = scheduleDao.getDeviceCountByScheduleToExpire(userId, orgGroupId, stopDate, condObj);
      return new PagedListInfo(list, total);
   }

   public int getCheckUpcomingExpiryDatePlaylistCnt() throws SQLException {
      String stopDate = DateUtils.setDate(2);
      Map obj = new HashMap();
      ProgramGroupInfo programGroup = ProgramGroupInfoImpl.getInstance();
      if (SecurityUtils.getLoginUserOrganization() != null && !SecurityUtils.getLoginUserOrganization().equals("ROOT")) {
         int organizationGroupId = programGroup.getOrganizationIdByName(SecurityUtils.getLoginUserOrganization());
         List programGroups = programGroup.getProgramGroupIdByOrganization(organizationGroupId);
         obj.put("programGroupList", programGroups);
      } else if (SecurityUtils.getLoginUserOrganization() == null || !SecurityUtils.getLoginUserOrganization().equals("ROOT")) {
         return 0;
      }

      return ((DeviceDaoMapper)this.getMapper()).getCheckUpcomingExpiryDatePlaylistCnt(0, 0, obj, stopDate);
   }

   public int getCheckUpcomingExpiryDateCnt() throws SQLException {
      String stopDate = DateUtils.setDate(2);
      Map obj = new HashMap();
      ProgramGroupInfo programGroup = ProgramGroupInfoImpl.getInstance();
      if (SecurityUtils.getLoginUserOrganization() != null && !SecurityUtils.getLoginUserOrganization().equals("ROOT")) {
         int organizationGroupId = programGroup.getOrganizationIdByName(SecurityUtils.getLoginUserOrganization());
         List programGroups = programGroup.getProgramGroupIdByOrganization(organizationGroupId);
         obj.put("programGroupList", programGroups);
      } else if (SecurityUtils.getLoginUserOrganization() == null || !SecurityUtils.getLoginUserOrganization().equals("ROOT")) {
         return 0;
      }

      return ((DeviceDaoMapper)this.getMapper()).getCheckUpcomingExpiryDateCnt(0, 0, obj, stopDate);
   }

   public PagedListInfo getCheckDeviceListReservationScheduleFail(int startPos, int pageSize, Map condition, String[] deviceTypeFilter) throws SQLException {
      DeviceGeneralSearch search = null;
      SelectCondition condObj = (SelectCondition)condition.get("condition");
      Long search_id = null;
      if (condition.get("search_id") != null && !condition.get("search_id").equals("") && !((String)condition.get("search_id")).equalsIgnoreCase("null")) {
         search_id = Long.parseLong((String)condition.get("search_id"));
      }

      List deviceGroupList = (List)condition.get("deviceGroupList");
      if (search_id != null) {
         DeviceGeneralSearchInfo searchInfo = DeviceGeneralSearchInfoImpl.getInstance();
         search = searchInfo.getDeviceGeneralSearchBySearchId(search_id);
      }

      String roleName = condObj.getRole_name();
      String userId = condObj.getUser_id();
      boolean isDeviceGroupAuth = false;
      if (DeviceUtils.isDeviceGroupAuth(roleName, userId)) {
         isDeviceGroupAuth = true;
      }

      int total = 0;
      List result = new ArrayList();
      if (deviceGroupList != null) {
         List sublist = Lists.partition(deviceGroupList, 1500);

         for(int i = 0; i < sublist.size(); ++i) {
            List tmp = ((DeviceDaoMapper)this.getMapper()).getCheckDeviceListReservationScheduleFail(startPos - 1, pageSize, condObj, (List)sublist.get(i), search, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId);
            result.addAll(tmp);
            total += ((DeviceDaoMapper)this.getMapper()).getCheckDeviceListCntReservationScheduleFail(startPos - 1, pageSize, condObj, (List)sublist.get(i), search, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId);
         }
      }

      return new PagedListInfo(result, total);
   }

   public PagedListInfo getCheckDeviceListContent(int startPos, int pageSize, Map condition, String[] deviceTypeFilter) throws SQLException {
      DeviceGeneralSearch search = null;
      SelectCondition condObj = (SelectCondition)condition.get("condition");
      Long search_id = null;
      if (condition.get("search_id") != null && !condition.get("search_id").equals("") && !((String)condition.get("search_id")).equalsIgnoreCase("null")) {
         search_id = Long.parseLong((String)condition.get("search_id"));
      }

      if (search_id != null) {
         DeviceGeneralSearchInfo searchInfo = DeviceGeneralSearchInfoImpl.getInstance();
         searchInfo.getDeviceGeneralSearchBySearchId(search_id);
      }

      String roleName = condObj.getRole_name();
      String userId = condObj.getUser_id();
      Long orgGroupId = condObj.getOrg_id();
      boolean isDeviceGroupAuth = false;
      if (DeviceUtils.isDeviceGroupAuth(roleName, userId)) {
         isDeviceGroupAuth = true;
      }

      List groupList = this.getGroupIdsByOrgManagerUserId(userId, orgGroupId);
      int total = ((DeviceDaoMapper)this.getMapper()).getCountContentError(groupList, userId, isDeviceGroupAuth, condObj);
      List result = ((DeviceDaoMapper)this.getMapper()).getListContentError(startPos - 1, pageSize, groupList, userId, isDeviceGroupAuth, condObj);
      return new PagedListInfo(result, total);
   }

   public PagedListInfo getCheckDeviceListStorage(int startPos, int pageSize, Map condition, String[] deviceTypeFilter) throws SQLException {
      DeviceGeneralSearch search = null;
      SelectCondition condObj = (SelectCondition)condition.get("condition");
      Long search_id = null;
      if (condition.get("search_id") != null && !condition.get("search_id").equals("") && !((String)condition.get("search_id")).equalsIgnoreCase("null")) {
         search_id = Long.parseLong((String)condition.get("search_id"));
      }

      if (search_id != null) {
         DeviceGeneralSearchInfo searchInfo = DeviceGeneralSearchInfoImpl.getInstance();
         searchInfo.getDeviceGeneralSearchBySearchId(search_id);
      }

      String roleName = condObj.getRole_name();
      String userId = condObj.getUser_id();
      Long orgGroupId = condObj.getOrg_id();
      boolean isDeviceGroupAuth = false;
      if (DeviceUtils.isDeviceGroupAuth(roleName, userId)) {
         isDeviceGroupAuth = true;
      }

      List groupList = this.getGroupIdsByOrgManagerUserId(userId, orgGroupId);
      int total = ((DeviceDaoMapper)this.getMapper()).getCountInsufficientCapacity(groupList, userId, isDeviceGroupAuth, condObj);
      List result = ((DeviceDaoMapper)this.getMapper()).getListInsufficientCapacity(startPos - 1, pageSize, groupList, userId, isDeviceGroupAuth, condObj);
      return new PagedListInfo(result, total);
   }

   public List getCheckDeviceListTimezone(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      List result = new ArrayList();
      if (groupList != null) {
         List sublist = Lists.partition(groupList, 1500);

         for(int i = 0; i < sublist.size(); ++i) {
            result.addAll(((DeviceDaoMapper)this.getMapper()).getCheckDeviceListTimezone(-1, -1, (SelectCondition)null, (List)sublist.get(i), (DeviceGeneralSearch)null, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId));
         }
      }

      return result;
   }

   public int getCheckDeviceListCntTimezone(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      int total = 0;
      if (groupList != null) {
         List sublist = Lists.partition(groupList, 1500);

         for(int i = 0; i < sublist.size(); ++i) {
            total += ((DeviceDaoMapper)this.getMapper()).getCheckDeviceListCntTimezone(-1, -1, (SelectCondition)null, (List)sublist.get(i), (DeviceGeneralSearch)null, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId);
         }
      }

      return total;
   }

   public int getCheckDeviceListCntSchedule(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      int total = 0;
      if (groupList != null) {
         List sublist = Lists.partition(groupList, 1500);

         for(int i = 0; i < sublist.size(); ++i) {
            total += ((DeviceDaoMapper)this.getMapper()).getCheckDeviceListCntSchedule(-1, -1, (SelectCondition)null, (List)sublist.get(i), (DeviceGeneralSearch)null, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId);
         }
      }

      return total;
   }

   public List getCheckDeviceListScheduleFail(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      List result = new ArrayList();
      if (groupList != null) {
         List sublist = Lists.partition(groupList, 1500);

         for(int i = 0; i < sublist.size(); ++i) {
            result.addAll(((DeviceDaoMapper)this.getMapper()).getCheckDeviceListScheduleFail(-1, -1, (SelectCondition)null, (List)sublist.get(i), (DeviceGeneralSearch)null, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId));
         }
      }

      return result;
   }

   public int getCheckDeviceListCntScheduleFail(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      int total = 0;
      if (groupList != null) {
         List sublist = Lists.partition(groupList, 1500);

         for(int i = 0; i < sublist.size(); ++i) {
            total += ((DeviceDaoMapper)this.getMapper()).getCheckDeviceListCntScheduleFail(-1, -1, (SelectCondition)null, (List)sublist.get(i), (DeviceGeneralSearch)null, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId);
         }
      }

      return total;
   }

   public List getCheckDeviceListReservationScheduleFail(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      List result = new ArrayList();
      if (groupList != null) {
         List sublist = Lists.partition(groupList, 1500);

         for(int i = 0; i < sublist.size(); ++i) {
            result.addAll(((DeviceDaoMapper)this.getMapper()).getCheckDeviceListReservationScheduleFail(-1, -1, (SelectCondition)null, (List)sublist.get(i), (DeviceGeneralSearch)null, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId));
         }
      }

      return result;
   }

   public int getCheckDeviceListCntReservationScheduleFail(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      int total = 0;
      if (groupList != null) {
         List sublist = Lists.partition(groupList, 1500);

         for(int i = 0; i < sublist.size(); ++i) {
            total += ((DeviceDaoMapper)this.getMapper()).getCheckDeviceListCntReservationScheduleFail(-1, -1, (SelectCondition)null, (List)sublist.get(i), (DeviceGeneralSearch)null, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId);
         }
      }

      return total;
   }

   public List getCheckDeviceListContent(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      List result = new ArrayList();
      if (groupList != null) {
         List sublist = Lists.partition(groupList, 1500);

         for(int i = 0; i < sublist.size(); ++i) {
            result.addAll(((DeviceDaoMapper)this.getMapper()).getCheckDeviceListContent(-1, -1, (SelectCondition)null, (List)sublist.get(i), (DeviceGeneralSearch)null, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId));
         }
      }

      return result;
   }

   public int getCheckDeviceListCntContent(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      int total = 0;
      if (groupList != null) {
         List sublist = Lists.partition(groupList, 1500);

         for(int i = 0; i < sublist.size(); ++i) {
            total += ((DeviceDaoMapper)this.getMapper()).getCheckDeviceListCntContent(-1, -1, (SelectCondition)null, (List)sublist.get(i), (DeviceGeneralSearch)null, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId);
         }
      }

      return total;
   }

   public List getCheckDeviceListStorage(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      List sublist = Lists.partition(groupList, 1500);
      List result = new ArrayList();

      for(int i = 0; i < sublist.size(); ++i) {
         List subresult = ((DeviceDaoMapper)this.getMapper()).getCheckDeviceList(-1, -1, (SelectCondition)null, (List)sublist.get(i), (DeviceGeneralSearch)null, deviceTypeFilter, this.getConstants(), isDeviceGroupAuth, userId);
         result.addAll(subresult);
      }

      List storageResult = new ArrayList();
      Iterator var12 = result.iterator();

      while(var12.hasNext()) {
         DeviceGeneralConf deviceGeneralConf = (DeviceGeneralConf)var12.next();
         if (deviceGeneralConf.getMin_disk_space_available() != null && deviceGeneralConf.getMin_disk_space_available() < 1048576L) {
            storageResult.add(deviceGeneralConf);
         }
      }

      return storageResult;
   }

   public boolean deleteChildDevice(String parentDeviceId) throws SQLException {
      return this.deleteChildDevice(parentDeviceId, (SqlSession)null);
   }

   public boolean deleteChildDevice(String parentDeviceId, SqlSession session) throws SQLException {
      if (session == null) {
         ((DeviceDaoMapper)this.getMapper()).deleteChildDeviceGroupMapping(parentDeviceId);
         ((DeviceDaoMapper)this.getMapper()).deleteChildDeviceDisplayConf(parentDeviceId);
         return ((DeviceDaoMapper)this.getMapper()).deleteChildDevice(parentDeviceId);
      } else {
         ((DeviceDaoMapper)this.getMapper(session)).deleteChildDeviceGroupMapping(parentDeviceId);
         ((DeviceDaoMapper)this.getMapper(session)).deleteChildDeviceDisplayConf(parentDeviceId);
         return ((DeviceDaoMapper)this.getMapper(session)).deleteChildDevice(parentDeviceId);
      }
   }

   public boolean setConnectChildCnt(String deviceId, Long connChildCnt) throws Exception {
      Device device = this.getDevice(deviceId);
      device.setConn_child_cnt(connChildCnt);
      DBCacheUtils.setDevice(device, deviceId);
      return ((DeviceDaoMapper)this.getMapper()).setConnectChildCnt(deviceId, connChildCnt);
   }

   public DeviceMemo getDeviceMemo(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceMemo(deviceId);
   }

   public boolean setDeviceMemo(String cmd, DeviceMemo memo) throws SQLException {
      if (cmd.equals("MODIFY")) {
         return !((DeviceDaoMapper)this.getMapper()).setDeviceMemo(memo) ? ((DeviceDaoMapper)this.getMapper()).addDeviceMemo(memo) : true;
      } else if (cmd.equals("DELETE")) {
         ((DeviceDaoMapper)this.getMapper()).deleteDeviceMemo(memo);
         return false;
      } else {
         return false;
      }
   }

   public boolean addDeviceMemo(DeviceMemo memo) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).addDeviceMemo(memo);
   }

   public List getServerDeviceReport() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceReportList();
   }

   public List getDeviceModelCount() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceModelCount();
   }

   public List getDeviceFirmwareCount() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceFirmwareCount();
   }

   public boolean setDeviceAmsCam(boolean isWebCam, String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).setDeviceAmsCam(isWebCam, deviceId);
   }

   public boolean addBackupPlayer(BackupPlayEntity backup) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).addBackupPlayer(backup);
   }

   public boolean addBackupTargetPlayer(BackupPlayEntity backup) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).addBackupTargetPlayer(backup);
   }

   public boolean deleteBackupPlayer(int groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).deleteBackupPlayer(groupId);
   }

   public boolean deleteBackupTargetPlayer(int groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).deleteBackupTargetPlayer(groupId);
   }

   public boolean setBackupBusyLevel(int busyLevel, String backupDeviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).setBackupBusyLevel(busyLevel, backupDeviceId);
   }

   public boolean setWaitingMoCount(int waitingMoCount, String backupDeviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).setWaitingMoCount(waitingMoCount, backupDeviceId);
   }

   public boolean setBackupDevice(String backupDeviceId, String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).setBackupDevice(backupDeviceId, deviceId);
   }

   public List getBackupPlayers(Long groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getBackupPlayers(groupId);
   }

   public List getBackupPlayerByWaitingMoCount(Long groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getBackupPlayerByWaitingMoCount(groupId);
   }

   public BackupPlayEntity getBackupPlayerByDeviceId(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getBackupPlayerByDeviceId(deviceId);
   }

   public List getBackupTargetPlayers(Long groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getBackupTargetPlayers(groupId);
   }

   public DeviceMonitoring getProgramInfoByDeviceGroupId(Long groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getProgramInfoByDeviceGroupId(groupId);
   }

   public List getDeviceAndGroupInfoByGroupId(Long groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceAndGroupInfoByGroupId(groupId);
   }

   public int cntSyncPlayDevice(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).cntSyncPlayDevice(deviceId);
   }

   public boolean updateLogFileName(String deviceId, String categoryScript, String fileName) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).updateLogFileName(deviceId, categoryScript, fileName);
   }

   public boolean updateLastModifiedTime(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).updateLastModifiedTime(deviceId);
   }

   public List getNewAndModifiedDeviceList(String startDate, String endDate) throws SQLException {
      Timestamp start_Date = Timestamp.valueOf(startDate);
      Timestamp end_Date = Timestamp.valueOf(endDate);
      return ((DeviceDaoMapper)this.getMapper()).getNewAndModifiedDeviceList(start_Date, end_Date);
   }

   public boolean addDeviceTotalCount(long groupId, int count) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).addDeviceTotalCount(groupId, count);
   }

   public List getPgorupIdLIsts(long groupId) throws SQLException {
      return this.getPgorupIdLIsts((SqlSession)null, groupId);
   }

   public List getPgorupIdLIsts(SqlSession session, long groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper(session)).getPgorupIdLIsts(groupId);
   }

   public long getDeviceTotalCount(long groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceTotalCount(groupId);
   }

   public int getDeviceCountBygroupId(long groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceCountBygroupId(groupId);
   }

   public Timestamp getStatisticsFileRequestTime(@Param("deviceId") String deviceId) throws SQLException {
      Timestamp requestTime = null;
      Map tempRequestTime = ((DeviceDaoMapper)this.getMapper()).getStatisticsFileRequestTime(deviceId);
      if (tempRequestTime != null) {
         requestTime = (Timestamp)tempRequestTime.get("request_time");
      }

      return requestTime;
   }

   public List getDeviceLogProcessInfo(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceLogProcessInfo(deviceId);
   }

   public int addDeviceLogProcessInfo(String deviceId, String type, String categoryScript, String status, Timestamp startTime, int duration, int packetSize, String token, int smartDiagnosticVersion) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).addDeviceLogProcessInfo(deviceId, type, categoryScript, status, startTime, duration, packetSize, token, smartDiagnosticVersion);
   }

   public boolean updateDeviceLogProcessStatus(String deviceId, String type, String categoryScript, String status) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).updateDeviceLogProcessStatus(deviceId, type, categoryScript, status);
   }

   public boolean deleteDeviceLogProcessInfoByDeviceId(String deviceId, String categoryScript) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).deleteDeviceLogProcessInfoByDeviceId(deviceId, categoryScript);
   }

   public int getLogProcessingDeviceCnt() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getLogProcessingDeviceCnt();
   }

   public boolean updateDeviceLogInfo(String deviceId, String type, String categoryScript, String status, Timestamp startTime, int duration, int packetSize, String token, String encryptionKey) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).updateDeviceLogInfo(deviceId, type, categoryScript, status, startTime, duration, packetSize, token, encryptionKey);
   }

   public List getAllDeviceLogProcess() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getAllDeviceLogProcess();
   }

   public boolean getIsOverWriteDeviceName(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getIsOverWriteDeviceName(deviceId);
   }

   public List getDeviceListByOrgName(String orgName) {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceListByOrgName(orgName);
   }

   public int getDeviceCountForLicense(List map) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceCountForLicense(map);
   }

   public List getDeviceListFromDeviceId(List deviceIds) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceListFromDeviceId(deviceIds);
   }

   public List getFirstChildrenIDsOfSignageGroup(int groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getFirstChildrenIDsOfSignageGroup(groupId);
   }

   public Long getMinPrioritybyDeviceType(String deviceType, String deviceTypeVersion) throws SQLException {
      BigDecimal deviceTypeVersionBigDecimal = new BigDecimal(String.valueOf(deviceTypeVersion));
      Long priority = ((DeviceDaoMapper)this.getMapper()).getPriorityByDeviceTypeAndVersion(deviceType, deviceTypeVersionBigDecimal);
      return priority;
   }

   public int getProgramDeviceTypeByGroupId(long groupId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getProgramDeviceTypeByGroupId(groupId);
   }

   public int checkFirstReceiveProgress(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).checkFirstReceiveProgress(deviceId);
   }

   public Integer getContentDownloadMode(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getContentDownloadMode(deviceId);
   }

   public List getTagFromDeviceId(String deviceId, Boolean isVarTag) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getTagFromDeviceId(deviceId, isVarTag);
   }

   public int getAllDeviceCountByDeviceTypeList(List deviceTypeList) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getAllDeviceCountByDeviceTypeList(deviceTypeList);
   }

   public String getOrganiationByDeviceId(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getOrganiationByDeviceId(deviceId);
   }

   public boolean updateDeviceMapLocation(String location, String[] deviceIdList) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).updateDeviceMapLocation(location, deviceIdList);
   }

   public String getMapLocationByDeviceId(@Param("deviceId") String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getMapLocationByDeviceId(deviceId);
   }

   public boolean updateDeviceMapLocationByLocation(String location, String[] locationList) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).updateDeviceMapLocationByLocation(location, locationList);
   }

   public List getDeviceMinList(String[] deviceIdList) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceMinList(deviceIdList);
   }

   public boolean addSboxVwtInfo(String deviceId, String sboxVwtId, String sboxVwtFileName) throws SQLException {
      boolean result = ((DeviceDaoMapper)this.getMapper()).addSboxVwtInfo(deviceId, sboxVwtId, sboxVwtFileName);
      if (result) {
         Device device = new Device();
         device.setDevice_id(deviceId);
         device.setLed_vwt_id(sboxVwtId);
         device.setLed_vwt_file_name(sboxVwtFileName);
         DBCacheUtils.setDevice(device, deviceId);
      }

      return result;
   }

   public boolean deleteSboxVwtInfo(String deviceId) throws SQLException {
      boolean result = ((DeviceDaoMapper)this.getMapper()).deleteSboxVwtInfo(deviceId);
      if (result) {
         Device device = new Device();
         device.setDevice_id(deviceId);
         device.setLed_vwt_id("");
         device.setLed_vwt_file_name("");
         DBCacheUtils.setDevice(device, deviceId);
      }

      return result;
   }

   public int setKeepaliveChangedStatus(Boolean keepaliveStatus, String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).setKeepaliveChangedStatus(keepaliveStatus, deviceId);
   }

   public int initKeepaliveChangedStatus() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).initKeepaliveChangedStatus();
   }

   public List getDisconnectedDeviceIdList() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDisconnectedDeviceIdList();
   }

   public boolean setRecommendPlayByDeviceId(String deviceId, boolean value) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).setRecommendPlayByDeviceId(deviceId, value);
   }

   public boolean getRecommendPlayByDeviceId(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getRecommendPlayByDeviceId(deviceId);
   }

   public int getCntRecommendPlayDevice() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getCntRecommendPlayDevice();
   }

   public int getCountDeviceAll(List orgGroupIds, String userId, boolean isDeviceGroupAuth) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getCountDeviceAll(orgGroupIds, userId, isDeviceGroupAuth);
   }

   public int getCountTimezoneNotSet(List orgGroupIds, String userId, boolean isDeviceGroupAuth) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getCountTimezoneNotSet(orgGroupIds, userId, isDeviceGroupAuth, (SelectCondition)null);
   }

   public List getListTimezoneNotSet(List orgGroupIds, String userId, boolean isDeviceGroupAuth) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getListTimezoneNotSet(-1, -1, orgGroupIds, userId, isDeviceGroupAuth, (SelectCondition)null);
   }

   public int getCountInsufficientCapacity(List orgGroupIds, String userId, boolean isDeviceGroupAuth) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getCountInsufficientCapacity(orgGroupIds, userId, isDeviceGroupAuth, (SelectCondition)null);
   }

   public List getListInsufficientCapacity(List orgGroupIds, String userId, boolean isDeviceGroupAuth) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getListInsufficientCapacity(-1, -1, orgGroupIds, userId, isDeviceGroupAuth, (SelectCondition)null);
   }

   public int getCountScheduleNotPublish(List orgGroupIds, String userId, boolean isDeviceGroupAuth) throws SQLException {
      SelectCondition condObj = new SelectCondition();
      condObj.setRm_device_types(CommonDataConstants.RM_DEVICE_TYPE_ARRAY);
      return ((DeviceDaoMapper)this.getMapper()).getCountScheduleNotPublish(orgGroupIds, userId, isDeviceGroupAuth, condObj);
   }

   public List getListScheduleNotPublish(List orgGroupIds, String userId, boolean isDeviceGroupAuth) throws SQLException {
      SelectCondition condObj = new SelectCondition();
      condObj.setRm_device_types(CommonDataConstants.RM_DEVICE_TYPE_ARRAY);
      return ((DeviceDaoMapper)this.getMapper()).getListScheduleNotPublish(-1, -1, orgGroupIds, userId, isDeviceGroupAuth, condObj);
   }

   public int getCountContentError(List orgGroupIds, String userId, boolean isDeviceGroupAuth) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getCountContentError(orgGroupIds, userId, isDeviceGroupAuth, (SelectCondition)null);
   }

   public List getListContentError(List orgGroupIds, String userId, boolean isDeviceGroupAuth) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getListContentError(-1, -1, orgGroupIds, userId, isDeviceGroupAuth, (SelectCondition)null);
   }

   public List getGroupIdsByOrgManagerUserId(String userId, Long groupId) throws SQLException {
      List userGroup = null;
      if (groupId == 0L) {
         userGroup = DeviceUtils.getDeviceGroupByUserId(userId);
      }

      if (!CollectionUtils.isNotEmpty((Collection)userGroup)) {
         userGroup = new ArrayList();
         DeviceGroup deviceGroup = new DeviceGroup();
         deviceGroup.setGroup_id(groupId);
         ((List)userGroup).add(deviceGroup);
      }

      return (List)userGroup;
   }

   public List getRmMonitoringList(String[] deviceIdList, Timestamp errorStandardTime) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getRmMonitoringList(deviceIdList, errorStandardTime);
   }

   public List getErrorList(String[] deviceIdList, String type, Timestamp errorPeriod, Integer status) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getErrorList(deviceIdList, type, errorPeriod, status);
   }

   public List getMaxDeviceTypeVersion() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getMaxDeviceTypeVersion();
   }

   public List getDeviceIdListByCurrentContentIds(String orgName, List contentIdList) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceIdListByCurrentContentIds(orgName, contentIdList);
   }

   public String getCurrentContentIdByDeviceId(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getCurrentContentIdByDeviceId(deviceId);
   }

   public boolean isDoneAtLast(String deviceId) throws SQLException {
      Boolean result = ((DeviceDaoMapper)this.getMapper()).isDoneAtLast(deviceId);
      return result == null || result;
   }

   public List getAllNotDonePlayingDefaultContentHistory() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getAllNotDonePlayingDefaultContentHistory();
   }

   public List getPlayingDefaultContentHistoryList(String orgName) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getPlayingDefaultContentHistoryList(orgName);
   }

   public boolean addPlayingDefaultContentHistory(String deviceId, String orgName) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).addPlayingDefaultContentHistory(deviceId, orgName);
   }

   public int setPlayingDefaultContentDone(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).setPlayingDefaultContentDone(deviceId);
   }

   public boolean deletePlayingDefaultContentHistoryByDeviceId(String deviceId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).deletePlayingDefaultContentHistoryByDeviceId(deviceId);
   }

   public boolean deletePlayingDefaultContentHistoryByOrganizationName(String orgName) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).deletePlayingDefaultContentHistoryByOrganizationName(orgName);
   }

   public int setOrganizationByDeviceId(Map map) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).setOrganizationByDeviceId(map);
   }

   public List getDeviceNameList(String organizationName) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceNameList(organizationName);
   }

   public List getDevicesByGroupIds(List groupIds) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDevicesByGroupIds(groupIds);
   }

   public List getDevicesByProgramId(String programId) throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDevicesByProgramId(programId);
   }

   public List getDeviceCountByDeviceType() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceCountByDeviceType();
   }

   public List getDeviceSbox() throws SQLException {
      return ((DeviceDaoMapper)this.getMapper()).getDeviceSbox();
   }
}
