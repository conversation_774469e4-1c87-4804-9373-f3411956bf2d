package com.samsung.magicinfo.restapi.schedule.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.samsung.magicinfo.framework.kpi.annotation.LogProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Timestamp;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@JsonInclude(Include.NON_NULL)
@ApiModel(
   description = "(modifiedDate) is based on server local time."
)
public class V2MessageScheduleResource {
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000",
      dataType = "string",
      value = "Id of the message schedule"
   )
   @Pattern(
      regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$|",
      message = "[MessageScheduleResource][messageId] Not UUID pattern."
   )
   private String messageId;
   @LogProperty(
      valueType = "NAME"
   )
   @ApiModelProperty(
      example = "test",
      required = true,
      dataType = "string",
      value = "Name of the message schedule"
   )
   @Size(
      max = 50,
      message = "[MessageScheduleResource][messageName] max size is 50."
   )
   private String messageName;
   @ApiModelProperty(
      example = "iPLAYER",
      required = true,
      value = "Minimum supported device type (SPLAYER, iPLAYER, LPLAYER)",
      dataType = "string",
      allowableValues = "SPLAYER,iPLAYER,LPLAYER"
   )
   private String deviceType;
   @ApiModelProperty(
      example = "1.0",
      required = true,
      value = "Version of minimum supported device type",
      dataType = "string"
   )
   private String deviceTypeVersion;
   @Valid
   @ApiModelProperty(
      value = "Device group Id list to deploy.",
      dataType = "list",
      reference = "TTV2DeviceGroupInfo"
   )
   private List deviceGroups;
   @ApiModelProperty(
      example = "5",
      required = true,
      dataType = "string",
      value = "Group Id of the message schedule"
   )
   @Pattern(
      regexp = "^[0-9]*$",
      message = "[MessageScheduleResource][messageGroupId] Only number is available."
   )
   private String messageGroupId;
   @ApiModelProperty(
      example = "default",
      required = true,
      dataType = "string",
      value = "Group name of the message schedule"
   )
   private String messageGroupName;
   @ApiModelProperty(
      example = "Y",
      required = true,
      allowableValues = "Y,N"
   )
   @Pattern(
      regexp = "Y|N"
   )
   private String isInstant;
   @ApiModelProperty(
      example = "admin",
      required = true,
      value = "Creator (author) ID of the message schedule",
      dataType = "string"
   )
   @Size(
      max = 64,
      message = "[MessageScheduleResource][creatorId] max size is 64."
   )
   private String creatorId;
   @ApiModelProperty(
      dataType = "timestamp",
      value = "Modification date of the message schedule"
   )
   private Timestamp modifiedDate;
   @ApiModelProperty(
      dataType = "string",
      value = "Shows the status of the message. There are four states in the message state: \"noUsed,\" \"reservation,\" \"displaying\" and \"completed\"",
      allowableValues = "noUsed,reservation,displaying,completed"
   )
   private String messageStatus;
   @ApiModelProperty(
      dataType = "int",
      value = "Count of device in device group",
      example = "1"
   )
   private int deviceCount;
   private List messageList;

   public V2MessageScheduleResource() {
      super();
   }

   public String getMessageStatus() {
      return this.messageStatus;
   }

   public void setMessageStatus(String messageStatus) {
      this.messageStatus = messageStatus;
   }

   public Timestamp getModifiedDate() {
      return this.modifiedDate;
   }

   public void setModifiedDate(Timestamp modifiedDate) {
      this.modifiedDate = modifiedDate;
   }

   public String getMessageGroupName() {
      return this.messageGroupName;
   }

   public void setMessageGroupName(String messageGroupName) {
      this.messageGroupName = messageGroupName;
   }

   public String getCreatorId() {
      return this.creatorId;
   }

   public void setCreatorId(String creatorId) {
      this.creatorId = creatorId;
   }

   public String getMessageId() {
      return this.messageId;
   }

   public void setMessageId(String messageId) {
      this.messageId = messageId;
   }

   public String getMessageName() {
      return this.messageName;
   }

   public void setMessageName(String messageName) {
      this.messageName = messageName;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public void setDeviceType(String deviceType) {
      this.deviceType = deviceType;
   }

   public String getDeviceTypeVersion() {
      return this.deviceTypeVersion;
   }

   public void setDeviceTypeVersion(String deviceTypeVersion) {
      this.deviceTypeVersion = deviceTypeVersion;
   }

   public String getMessageGroupId() {
      return this.messageGroupId;
   }

   public void setMessageGroupId(String messageGroupId) {
      this.messageGroupId = messageGroupId;
   }

   public String getIsInstant() {
      return this.isInstant;
   }

   public void setIsInstant(String isInstant) {
      this.isInstant = isInstant;
   }

   public List getMessageList() {
      return this.messageList;
   }

   public void setMessageList(List messageList) {
      this.messageList = messageList;
   }

   public List getDeviceGroups() {
      return this.deviceGroups;
   }

   public void setDeviceGroups(List deviceGroups) {
      this.deviceGroups = deviceGroups;
   }

   public int getDeviceCount() {
      return this.deviceCount;
   }

   public void setDeviceCount(int deviceCount) {
      this.deviceCount = deviceCount;
   }
}
