package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.model.Tag;
import com.samsung.magicinfo.webauthor2.service.TagService;
import com.samsung.magicinfo.webauthor2.webapi.assembler.TagResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.resource.TagResource;
import java.util.List;
import javax.inject.Inject;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/tags"})
public class TagQueryController {
  private TagService tagService;
  
  private TagResourceAssembler assembler;
  
  @Inject
  public TagQueryController(TagService tagService, TagResourceAssembler tagResourceAssembler) {
    this.tagService = tagService;
    this.assembler = tagResourceAssembler;
  }
  
  @GetMapping
  public HttpEntity<List<TagResource>> getTagList() {
    List<Tag> list = this.tagService.getTagResource();
    List<TagResource> tagResources = this.assembler.toResources(list);
    return (HttpEntity<List<TagResource>>)ResponseEntity.ok(tagResources);
  }
  
  @ExceptionHandler({IllegalArgumentException.class})
  public HttpEntity<String> illegalArgumentException(IllegalArgumentException ex) {
    return (HttpEntity<String>)ResponseEntity.badRequest().body(ex.getMessage());
  }
}
