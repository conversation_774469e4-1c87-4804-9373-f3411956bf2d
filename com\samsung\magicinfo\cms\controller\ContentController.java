package com.samsung.magicinfo.cms.controller;

import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.cms.model.ContentApproveResource;
import com.samsung.magicinfo.cms.model.ContentFilter;
import com.samsung.magicinfo.cms.model.UrlContentSettingResource;
import com.samsung.magicinfo.cms.service.ContentService;
import com.samsung.magicinfo.framework.kpi.annotation.KPI;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.Authorization;
import java.sql.SQLException;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Api(
   value = "Content Management System Rest api",
   description = "Operations pertaining to content in Content Management System",
   tags = {"Content API Group"}
)
@RestController
@RequestMapping({"/restapi/v1.0/cms/contents"})
public class ContentController {
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   @Autowired
   private ContentService contentService;

   public ContentController() {
      super();
   }

   @ApiOperation(
      value = "get all content list",
      notes = "get all content list without filter.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {""},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity listAllContent(@RequestParam(value = "startIndex",required = true) int startIndex, @RequestParam(value = "pageSize",required = true) int pageSize) throws Exception {
      this.logger.info("[REST][CONTENT][listAllContent] get all content list");
      ResponseBody responsebody = new ResponseBody();

      try {
         if (!CommonUtils.checkAvailable("content")) {
            return new ResponseEntity(responsebody, HttpStatus.METHOD_NOT_ALLOWED);
         } else {
            ContentFilter contentFilter = new ContentFilter();
            contentFilter.setStartIndex(startIndex);
            contentFilter.setPageSize(pageSize);
            responsebody = this.contentService.listContent(contentFilter);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equals("Success")) {
               this.logger.info("[REST][CONTENT][listAllContent] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][CONTENT][listAllContent] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         }
      } catch (AccessDeniedException var5) {
         this.logger.error("[REST][CONTENT][listAllContent] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var6) {
         this.logger.error("[REST][CONTENT][listAllContent] Exception is occured. " + var6.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var6.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @KPI
   @ApiOperation(
      value = "get content list",
      notes = "get content list by filter.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/filter"},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity listContent(@Valid @RequestBody ContentFilter filter, BindingResult result) throws Exception {
      this.logger.info("[REST][CONTENT][listContent] get content list by filter");
      ResponseBody responsebody = new ResponseBody();
      if (!CommonUtils.checkAvailable("content")) {
         return new ResponseEntity(responsebody, HttpStatus.METHOD_NOT_ALLOWED);
      } else if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      } else {
         try {
            responsebody = this.contentService.listContent(filter);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equals("Success")) {
               this.logger.info("[REST][CONTENT][listContent] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][CONTENT][listContent] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var5) {
            this.logger.error("[REST][CONTENT][listContent] access denied.");
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var5.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var6) {
            this.logger.error("[REST][CONTENT][listContent] Exception is occured. " + var6.toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "get details for the content",
      notes = "get details for selected content by content id.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{contentId}"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity getActiveContentInfo(@PathVariable String contentId) throws SQLException {
      this.logger.info("[REST][CONTENT][getActiveContentInfo][" + contentId + "] get content detail information by contentId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.contentService.getActiveContentInfo(contentId);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equals("Success")) {
            this.logger.info("[REST][CONTENT][getActiveContentInfo][" + contentId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT][getActiveContentInfo][" + contentId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][CONTENT][getActiveContentInfo][" + contentId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][CONTENT][getActiveContentInfo][" + contentId + "] Exception is occured. " + var5.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get content detail information",
      notes = "get content detail information by contentId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/details"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity getContentInfoList(@ApiParam("contentIds") @RequestParam String[] contentIds) throws SQLException {
      this.logger.info("[REST][CONTENT][getContentInfoList] get content detail information by contentId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.contentService.getContentInfoList(contentIds);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equals("Success")) {
            this.logger.info("[REST][CONTENT][getContentInfoList]finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT][getContentInfoList]Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][CONTENT][getContentInfoList]access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][CONTENT][getContentInfoList] Exception is occured. " + var5.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get details for the content",
      notes = "get details for selected content by file id.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/files/{fileId}"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity getContentFileInfo(@PathVariable String fileId) throws SQLException {
      this.logger.info("[REST][CONTENT][getContentFileInfo][" + fileId + "] get content detail information by fileId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.contentService.getContentFileInfo(fileId);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equals("Success")) {
            this.logger.info("[REST][CONTENT][getContentFileInfo][" + fileId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT][getContentFileInfo][" + fileId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][CONTENT][getContentFileInfo][" + fileId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][CONTENT][getContentFileInfo][" + fileId + "] Exception is occured. " + var5.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get content list",
      notes = "get content list by filter.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/files/filter"},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity listContentFile(@Valid @RequestBody ContentFilter filter, BindingResult result) throws Exception {
      this.logger.info("[REST][CONTENT][listContent] get content list by filter");
      ResponseBody responsebody = new ResponseBody();
      if (!CommonUtils.checkAvailable("content")) {
         return new ResponseEntity(responsebody, HttpStatus.METHOD_NOT_ALLOWED);
      } else if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      } else {
         try {
            responsebody = this.contentService.listContentFile(filter);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equals("Success")) {
               this.logger.info("[REST][CONTENT][listContent] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][CONTENT][listContent] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var5) {
            this.logger.error("[REST][CONTENT][listContent] access denied.");
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var5.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var6) {
            this.logger.error("[REST][CONTENT][listContent] Exception is occured. " + var6.toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "update content file",
      notes = "update content file by contentId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{contentId}/fileUpdate"},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity updateContentFile(@PathVariable String contentId, HttpServletRequest request) throws SQLException {
      this.logger.info("[REST][CONTENT][updateContentFile][" + contentId + "] update content file by contentId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.contentService.updateContentFile(contentId, request);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equals("Success")) {
            this.logger.info("[REST][CONTENT][updateContentFile][" + contentId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT][updateContentFile][" + contentId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var5) {
         this.logger.error("[REST][CONTENT][updateContentFile][" + contentId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var6) {
         this.logger.error("[REST][CONTENT][updateContentFile][" + contentId + "] Exception is occured. " + var6.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var6.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "delete content",
      notes = "delete content by contentId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{contentId}"},
      method = {RequestMethod.DELETE},
      produces = {"application/json"}
   )
   public ResponseEntity deleteContent(@PathVariable String contentId) throws SQLException {
      this.logger.info("[REST][CONTENT][deleteContent][" + contentId + "] delete content by contentId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.contentService.deleteContent(contentId);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equals("Success")) {
            this.logger.info("[REST][CONTENT][deleteContent][" + contentId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT][deleteContent][" + contentId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][CONTENT][deleteContent][" + contentId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][CONTENT][deleteContent][" + contentId + "] Exception is occured. " + var5.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "force delete specific contents",
      notes = "force delete selected contents by contentId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/force/{contentId}"},
      method = {RequestMethod.DELETE},
      produces = {"application/json"}
   )
   public ResponseEntity forceDeleteContent(@PathVariable String contentId) throws SQLException {
      this.logger.info("[REST][CONTENT][forceDeleteContent][" + contentId + "] force delete content by contentId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.contentService.forceDeleteContent(contentId);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equals("Success")) {
            this.logger.info("[REST][CONTENT][forceDeleteContent][" + contentId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT][forceDeleteContent][" + contentId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][CONTENT][forceDeleteContent][" + contentId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][CONTENT][forceDeleteContent][" + contentId + "] Exception is occured. " + var5.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get content information for dashboard",
      notes = "get content information for dashboard(totalCount, usedCount, unapprovedCount, rejectCount).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/dashboard"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity listDashboardContentInfo() throws Exception {
      this.logger.info("[REST][CONTENT][listDashboardContentInfo] get contents information used in the dashboard");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.contentService.getDashboardContentInfo();
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equals("Success")) {
            this.logger.info("[REST][CONTENT][listDashboardContentInfo] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT][listDashboardContentInfo] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         this.logger.error("[REST][CONTENT][listDashboardContentInfo] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var3.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         this.logger.error("[REST][CONTENT][listDashboardContentInfo] Exception is occured. " + var4.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "change status of unapproved contents",
      notes = "change status of unapproved contents(approved, rejected).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{contentId}/approval"},
      method = {RequestMethod.PUT},
      produces = {"application/json"}
   )
   public ResponseEntity approveContents(@PathVariable String contentId, @Valid @RequestBody ContentApproveResource resource, BindingResult result) throws Exception {
      this.logger.info("[REST][CONTENT][approveContents][" + contentId + "] approve/reject by contentId");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      } else {
         try {
            resource.setContentId(contentId);
            responsebody = this.contentService.approveContents(resource);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equals("Success")) {
               this.logger.info("[REST][CONTENT][approveContents][" + contentId + "] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][CONTENT][approveContents][" + contentId + "] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var6) {
            this.logger.error("[REST][CONTENT][approveContents][" + contentId + "] access denied.");
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var7) {
            this.logger.error("[REST][CONTENT][approveContents][" + contentId + "] Exception is occured. " + var7.toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var7.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @KPI
   @ApiOperation(
      value = "upload content",
      notes = "upload content by groupId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{groupId}"},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity uploadContent(@PathVariable("groupId") String groupId, HttpServletRequest request) throws Exception {
      this.logger.info("[REST][CONTENT][uploadContent] upload content by groupId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.contentService.uploadContent(groupId, request);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equals("Success")) {
            this.logger.info("[REST][CONTENT][uploadContent][" + groupId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT][uploadContent][" + groupId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var5) {
         this.logger.error("[REST][CONTENT][uploadContent][" + groupId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var6) {
         this.logger.error("[REST][CONTENT][uploadContent][" + groupId + "] Exception is occured. " + var6.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var6.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "rename content",
      notes = "change the name of content.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{contentId}/rename"},
      method = {RequestMethod.PUT},
      produces = {"application/json"}
   )
   public ResponseEntity renameContent(@PathVariable String contentId, @RequestParam("newContentName") String newContentName) throws SQLException {
      ResponseBody responsebody = new ResponseBody();
      this.logger.info("[REST][CONTENT][renameContent][" + contentId + "]");

      try {
         responsebody = this.contentService.renameContent(contentId, newContentName);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equals("Success")) {
            this.logger.info("[REST][CONTENT][renameContent][" + contentId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT][renameContent][" + contentId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var5) {
         this.logger.error("[REST][CONTENT][renameContent][" + contentId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var6) {
         this.logger.error("[REST][CONTENT][renameContent][" + contentId + "] Exception is occured. " + var6.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var6.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get tag mapping",
      notes = "get tag mapping information.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/tag"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity getTagMapping(@ApiParam("contentId") @RequestParam String[] contentId) throws Exception {
      this.logger.info("[REST][CONTENT][getTagMapping] get tag mapping");
      ResponseBody responsebody = new ResponseBody();
      if (!CommonUtils.checkAvailable("content")) {
         return new ResponseEntity(responsebody, HttpStatus.METHOD_NOT_ALLOWED);
      } else {
         try {
            responsebody = this.contentService.getTagMapping(contentId);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equals("Success")) {
               this.logger.info("[REST][CONTENT][getTagMapping] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][CONTENT][getTagMapping] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var4) {
            this.logger.error("[REST][CONTENT][getTagMapping] access denied.");
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var4.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var5) {
            this.logger.error("[REST][CONTENT][getTagMapping] Exception is occured. " + var5.toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var5.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "assign tags",
      notes = "assign tags to content.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{contentId}/tags"},
      method = {RequestMethod.PATCH},
      produces = {"application/json"}
   )
   public ResponseEntity assignTags(@PathVariable String contentId, @Valid @RequestBody List tags, BindingResult result) throws Exception {
      this.logger.info("[REST][CONTENT][uploadContent] upload content by groupId");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      } else {
         try {
            responsebody = this.contentService.assignTags(contentId, tags);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equals("Success")) {
               this.logger.info("[REST][CONTENT][assignTags][" + contentId + "] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][CONTENT][assignTags][" + contentId + "] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var6) {
            this.logger.error("[REST][CONTENT][assignTags][" + contentId + "] access denied.");
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var7) {
            this.logger.error("[REST][CONTENT][assignTags][" + contentId + "] Exception is occured. " + var7.toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var7.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "Create URL content",
      notes = "Create URL content.",
      authorizations = {@Authorization("api_key")}
   )
   @PostMapping(
      value = {"/url-settings"},
      produces = {"application/json"}
   )
   public ResponseEntity createUrlContent(@Valid @RequestBody UrlContentSettingResource resource, BindingResult result) throws Exception {
      ResponseBody responseBody = new ResponseBody();
      if (result.hasErrors()) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responseBody, HttpStatus.BAD_REQUEST);
      } else {
         try {
            UrlContentSettingResource newResource = this.contentService.createUrlContent(resource);
            responseBody.setApiVersion("1.0");
            if (responseBody.getStatus().equals("Success")) {
               this.logger.info("[REST_v2.0][CONTENTS][createUrlContent][" + resource.getUrlContentName() + "]");
               responseBody.setItems(newResource);
               return new ResponseEntity(responseBody, HttpStatus.OK);
            } else {
               this.logger.info("[REST_v2.0][CONTENTS][createUrlContent][" + resource.getUrlContentName() + "]");
               return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var5) {
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage(var5.getMessage());
            return new ResponseEntity(responseBody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var6) {
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage(var6.getMessage());
            return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "File upload to create content",
      notes = "Upload file to MagicINFO server which is used as content.(Image, Movie, Web content package file..)",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "file",
   value = "Target file to upload",
   paramType = "form",
   dataType = "__file",
   required = true
), @ApiImplicitParam(
   name = "groupId",
   value = "Group id to be uploaded",
   dataType = "string",
   defaultValue = "0",
   example = "0",
   required = true
), @ApiImplicitParam(
   name = "contentType",
   value = "Type of file to upload",
   dataType = "string",
   allowableValues = "IMAGE,MOVIE,OFFICE,PDF,FLASH,SOUND,HTML",
   example = "HTML",
   required = true
), @ApiImplicitParam(
   name = "updatedContentId",
   value = "Content id to be updated",
   dataType = "string",
   example = "3B55D68D-AB22-4EB8-AE1A-5976057F912A",
   allowEmptyValue = true
), @ApiImplicitParam(
   name = "webContentName",
   value = "Name of web content(web content only)",
   dataType = "string",
   example = "webContentFile.zip",
   allowEmptyValue = true
), @ApiImplicitParam(
   name = "startPage",
   value = "Start page name of web content such as \"index.html\"(web content only)",
   dataType = "string",
   example = "index.html",
   allowEmptyValue = true
), @ApiImplicitParam(
   name = "refreshInterval",
   value = "Refresh interval of web content. Format is \"hh:mm:ss\"(web content only)",
   dataType = "string",
   defaultValue = "00:01:00",
   example = "00:01:00",
   allowEmptyValue = true
), @ApiImplicitParam(
   name = "mode",
   value = "If you want to change web content package, input \"UPDATE\"(web content only)",
   dataType = "string",
   allowableValues = ",UPDATE",
   example = "UPDATE",
   allowEmptyValue = true
)})
   @PostMapping(
      value = {"/files"},
      consumes = {"multipart/form-data"},
      produces = {"application/json"}
   )
   public ResponseEntity uploadContentFile(@NotNull @RequestPart("file") MultipartFile file, @NotNull @NotEmpty @RequestParam("groupId") String groupId, @NotNull @NotEmpty @RequestParam("contentType") String contentType, @RequestParam(value = "updatedContentId",required = false) String updatedContentId, @RequestParam(value = "webContentName",required = false) String webContentName, @RequestParam(value = "startPage",required = false) String startPage, @RequestParam(value = "refreshInterval",required = false) String refreshInterval, @RequestParam(value = "mode",required = false) String mode, HttpServletRequest request) throws Exception {
      List resources = this.contentService.uploadContentFile(file, StrUtils.nvl(groupId), StrUtils.nvl(updatedContentId), StrUtils.nvl(contentType), StrUtils.nvl(webContentName), StrUtils.nvl(startPage), StrUtils.nvl(refreshInterval), StrUtils.nvl(mode), request);
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("2.0");
      responseBody.setStatus("Success");
      responseBody.setErrorCode(ExceptionCode.HTTP200[0]);
      responseBody.setErrorMessage(ExceptionCode.HTTP200[2]);
      responseBody.setTotalCount(resources.size());
      responseBody.setItems(resources);
      this.logger.info("[REST][DASHBOARD][uploadContentFile] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }
}
