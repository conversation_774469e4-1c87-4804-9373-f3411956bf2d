package com.samsung.magicinfo.rc.common.memory;

import java.util.HashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class ServerCaptureImageMemory extends HashMap<String, byte[]> {
  private static final Logger log = LoggerFactory.getLogger(com.samsung.magicinfo.rc.common.memory.ServerCaptureImageMemory.class);
  
  private static final long serialVersionUID = 1L;
  
  public synchronized byte[] put(String deviceId, byte[] byteArr) {
    return super.put(deviceId, byteArr);
  }
  
  public synchronized byte[] get(String deviceId) {
    return (byte[])get(deviceId);
  }
  
  public synchronized void inputImage(String deviceId, byte[] byteArr) {
    try {
      if (containsKey(deviceId)) {
        if (!((byte[])get(deviceId)).equals(byteArr))
          put(deviceId, byteArr); 
      } else {
        super.put(deviceId, byteArr);
        log.info("[RC][" + deviceId + "] write first image file in queue");
      } 
    } catch (Exception e) {
      log.error("", e);
      throw e;
    } 
  }
}
