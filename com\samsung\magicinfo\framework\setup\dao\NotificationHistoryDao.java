package com.samsung.magicinfo.framework.setup.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.setup.entity.NotificationHistoryEntity;
import com.samsung.magicinfo.protocol.util.BeanUtils;
import com.samsung.magicinfo.protocol.util.MailUtil;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManager;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManagerImpl;
import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.Map.Entry;
import jodd.io.FileUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class NotificationHistoryDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(NotificationHistoryDao.class);

   public NotificationHistoryDao() {
      super();
   }

   public boolean addHistory(NotificationHistoryEntity history, String[] attachedFileName, Map toUser) throws SQLException {
      SqlSession session = this.openNewSession(false);
      ArrayList newAttachedFilePathList = new ArrayList();

      boolean var24;
      try {
         Iterator var7;
         try {
            ((NotificationHistoryDaoMapper)this.getMapper(session)).addHistory(history);
            PIIDataManager piiDataManager = (PIIDataManagerImpl)BeanUtils.getBean("PIIDataManager");
            if (attachedFileName != null && attachedFileName.length > 0) {
               String[] var25 = attachedFileName;
               int var26 = attachedFileName.length;

               for(int var9 = 0; var9 < var26; ++var9) {
                  String fileName = var25[var9];
                  String newAttachedFileDirectory = "";
                  String attachedFileId = "";
                  if (StringUtils.isNotBlank(fileName)) {
                     String attachedFilePath = "";
                     attachedFilePath = MailUtil.getAlarmMailDirectory();
                     attachedFileId = UUID.randomUUID().toString().toUpperCase();
                     newAttachedFileDirectory = attachedFilePath + "/" + attachedFileId;
                     File tempFile = new File(fileName);
                     if (!tempFile.exists()) {
                        throw new Exception("Attached File Not Found.");
                     }

                     File newDirectory = new File(newAttachedFileDirectory);
                     if (newDirectory.exists()) {
                        throw new Exception("Directory exist. : " + newAttachedFileDirectory);
                     }

                     String newAttachedFileFullPath = newAttachedFileDirectory + "/" + tempFile.getName();
                     newDirectory.mkdir();
                     newAttachedFilePathList.add(newAttachedFileDirectory);
                     FileUtil.copy(fileName, newAttachedFileFullPath);
                     ((NotificationHistoryDaoMapper)this.getMapper(session)).addAttachedFile(attachedFileId, tempFile.getName(), newAttachedFileDirectory);
                     ((NotificationHistoryDaoMapper)this.getMapper(session)).addHistoryMapAttachedFileId(history.getId(), attachedFileId);
                  }
               }
            }

            var7 = toUser.entrySet().iterator();

            while(var7.hasNext()) {
               Entry element = (Entry)var7.next();
               ((NotificationHistoryDaoMapper)this.getMapper(session)).addHistoryMapUserId(history.getId(), (String)element.getKey(), piiDataManager.encryptData((String)element.getValue(), "email"));
            }

            session.commit();
            var24 = true;
            return var24;
         } catch (Exception var22) {
            this.logger.error(var22);
            session.rollback();
            var7 = newAttachedFilePathList.iterator();
         }

         while(var7.hasNext()) {
            String filePath = (String)var7.next();

            try {
               FileUtils.deleteDirectory(new File(filePath));
            } catch (IOException var21) {
               this.logger.error(var21);
            }
         }

         var24 = false;
      } finally {
         session.close();
      }

      return var24;
   }

   public boolean addAttachedFile(String fileId, String fileName, String filePath) throws SQLException {
      return ((NotificationHistoryDaoMapper)this.getMapper()).addAttachedFile(fileId, fileName, filePath);
   }

   public boolean addHistoryMapIssueId(Long historyId, String issueId, String issueName) throws SQLException {
      return ((NotificationHistoryDaoMapper)this.getMapper()).addHistoryMapIssueId(historyId, issueId, issueName);
   }

   public boolean addHistoryMapUserId(Long historyId, String userId, String email) throws SQLException {
      return ((NotificationHistoryDaoMapper)this.getMapper()).addHistoryMapUserId(historyId, userId, email);
   }

   public List getAllNotificationUserHistory() throws SQLException {
      return ((NotificationHistoryDaoMapper)this.getMapper()).getAllNotificationUserHistory();
   }

   public boolean setUserNotificationHistory(Long historyId, String userId, String email) throws SQLException {
      return ((NotificationHistoryDaoMapper)this.getMapper()).setUserNotificationHistory(historyId, userId, email);
   }

   public int getCount(Map condition) throws SQLException {
      return ((NotificationHistoryDaoMapper)this.getMapper()).getCount(condition);
   }

   public List getAllNotificationHistoryList(String orgName) throws SQLException {
      return ((NotificationHistoryDaoMapper)this.getMapper()).getAllNotificationHistoryList(orgName);
   }

   public List getPagedNotificationHistoryList(Map condition, int startPos, int pageSize) throws SQLException {
      return ((NotificationHistoryDaoMapper)this.getMapper()).getPagedNotificationHistoryList(condition, startPos, pageSize);
   }

   public List getAttachedFileFullPath(Long historyId) throws SQLException {
      List attachedFileList = ((NotificationHistoryDaoMapper)this.getMapper()).getAttachedFileList(historyId);
      List result = new ArrayList();
      Iterator var4 = attachedFileList.iterator();

      while(var4.hasNext()) {
         Map attachedFile = (Map)var4.next();
         String fullPath = attachedFile.get("file_path") + File.separator + attachedFile.get("file_name");
         result.add(fullPath);
      }

      return result;
   }

   public int deleteHistory(Timestamp retentionTime) throws SQLException {
      SqlSession session = this.openNewSession(false);
      int result = 0;
      List deleteFileList = null;
      boolean var17 = false;

      Iterator var5;
      Map fileInfoMap;
      label157: {
         try {
            var17 = true;
            deleteFileList = ((NotificationHistoryDaoMapper)this.getMapper(session)).getAttachedFileListByTime(retentionTime);
            ((NotificationHistoryDaoMapper)this.getMapper(session)).deleteAttachedFile(retentionTime);
            result = ((NotificationHistoryDaoMapper)this.getMapper(session)).deleteHistory(retentionTime);
            session.commit();
            var17 = false;
            break label157;
         } catch (Exception var21) {
            session.rollback();
            this.logger.error(var21);
            var17 = false;
         } finally {
            if (var17) {
               session.close();
               if (deleteFileList != null) {
                  Iterator var9 = deleteFileList.iterator();

                  while(var9.hasNext()) {
                     Map fileInfoMap = (Map)var9.next();

                     try {
                        this.deleteDirectory(fileInfoMap.get("file_path").toString());
                     } catch (Exception var18) {
                        this.logger.error(var18);
                     }
                  }
               }

            }
         }

         session.close();
         if (deleteFileList != null) {
            var5 = deleteFileList.iterator();

            while(var5.hasNext()) {
               fileInfoMap = (Map)var5.next();

               try {
                  this.deleteDirectory(fileInfoMap.get("file_path").toString());
               } catch (Exception var19) {
                  this.logger.error(var19);
               }
            }
         }

         return result;
      }

      session.close();
      if (deleteFileList != null) {
         var5 = deleteFileList.iterator();

         while(var5.hasNext()) {
            fileInfoMap = (Map)var5.next();

            try {
               this.deleteDirectory(fileInfoMap.get("file_path").toString());
            } catch (Exception var20) {
               this.logger.error(var20);
            }
         }
      }

      return result;
   }

   private void deleteDirectory(String path) {
      File deleteFolder = new File(path);
      if (deleteFolder.exists()) {
         File[] deleteFolderList = deleteFolder.listFiles();

         for(int i = 0; i < deleteFolderList.length; ++i) {
            if (deleteFolderList[i].isFile()) {
               deleteFolderList[i].delete();
            } else {
               this.deleteDirectory(deleteFolderList[i].getPath());
            }

            deleteFolderList[i].delete();
         }

         deleteFolder.delete();
      }

   }

   public int deleteGarbageFiles() throws SQLException {
      List deleteFileList = null;
      deleteFileList = ((NotificationHistoryDaoMapper)this.getMapper()).getGarbageFileList();
      if (deleteFileList != null) {
         Iterator var2 = deleteFileList.iterator();

         while(var2.hasNext()) {
            Map fileInfoMap = (Map)var2.next();

            try {
               this.deleteDirectory(fileInfoMap.get("file_path").toString());
            } catch (Exception var5) {
               this.logger.error(var5);
            }
         }
      }

      return ((NotificationHistoryDaoMapper)this.getMapper()).deleteGarbageFiles();
   }

   public List getReceiverList(Long historyId) throws SQLException {
      return ((NotificationHistoryDaoMapper)this.getMapper()).getReceiverList(historyId);
   }

   public List getReceiverIDList(Long historyId) throws SQLException {
      return ((NotificationHistoryDaoMapper)this.getMapper()).getReceiverIDList(historyId);
   }

   public List getAttachedFileNames(Long historyId) throws SQLException {
      List list = ((NotificationHistoryDaoMapper)this.getMapper()).getAttachedFileList(historyId);
      List result = new ArrayList();
      Iterator var4 = list.iterator();

      while(var4.hasNext()) {
         Map item = (Map)var4.next();
         String fileName = item.get("FILE_NAME").toString();
         result.add(fileName);
      }

      return result;
   }

   public NotificationHistoryEntity getNotificationHistoryInfo(Long historyId) throws SQLException {
      return ((NotificationHistoryDaoMapper)this.getMapper()).getNotificationHistoryInfo(historyId);
   }
}
