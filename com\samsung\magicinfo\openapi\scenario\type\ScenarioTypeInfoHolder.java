package com.samsung.magicinfo.openapi.scenario.type;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DocumentUtils;
import java.io.IOException;
import java.io.InputStream;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import javax.annotation.PostConstruct;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

@Service("scenarioTypeInfoHolder")
@Lazy
public class ScenarioTypeInfoHolder implements TypeInfoHolder {
   protected Logger logger = LoggingManagerV2.getLogger(this.getClass());
   private Map actionMap;
   private Map typeMap;
   private Map supportMap;

   public ScenarioTypeInfoHolder() {
      super();
   }

   public List getActionParameterList(ActionId ai) {
      return (List)this.actionMap.get(ai);
   }

   public SupporterTypeInfo getSupportTypeInfo(String supporterId) {
      return (SupporterTypeInfo)this.supportMap.get(supporterId);
   }

   public TypeRefInfo getTypeRefInfo(String typeId) {
      return (TypeRefInfo)this.typeMap.get(typeId);
   }

   public boolean isRegisteredAction(ActionId ai) {
      return this.actionMap.containsKey(ai);
   }

   public SupporterTypeInfo getSupporterByParameter(String service, String method, String aliasID) {
      ActionId aID = new ActionId(service, method);
      List paramInfoList = this.getActionParameterList(aID);
      SupporterTypeInfo supportTypeInfo = this.getAliasSupportTypeInfo(paramInfoList, aliasID);
      return supportTypeInfo;
   }

   public Map getServicesMap() {
      Map servicesMap = new LinkedHashMap();

      ActionId ai;
      String serviceName;
      for(Iterator var2 = this.actionMap.keySet().iterator(); var2.hasNext(); ((List)servicesMap.get(serviceName)).add(ai)) {
         ai = (ActionId)var2.next();
         serviceName = ai.getServiceName();
         if (!servicesMap.containsKey(serviceName)) {
            List aiList = new LinkedList();
            servicesMap.put(serviceName, aiList);
         }
      }

      return servicesMap;
   }

   private SupporterTypeInfo getAliasSupportTypeInfo(List paramInfoList, String aliasID) {
      Iterator var3 = paramInfoList.iterator();

      SupporterTypeInfo res;
      do {
         if (!var3.hasNext()) {
            return null;
         }

         ActionParamTypeInfo paramInfo = (ActionParamTypeInfo)var3.next();
         String type;
         if (paramInfo.isSupported() && paramInfo.getName().equals(aliasID)) {
            type = paramInfo.getSupportedBy();
            return this.getSupportTypeInfo(type);
         }

         type = paramInfo.getType();
         TypeRefInfo typeRef = this.getTypeRefInfo(type);
         List infoList = typeRef.getParamsInfoList();
         res = this.getAliasSupportTypeInfo(infoList, aliasID);
      } while(res == null);

      return res;
   }

   @PostConstruct
   public void init() {
      this.actionMap = new LinkedHashMap();
      this.typeMap = new LinkedHashMap();
      this.supportMap = new LinkedHashMap();
      InputStream is = null;

      try {
         DocumentBuilderFactory factory = DocumentUtils.getDocumentBuilderFactoryInstance();
         DocumentBuilder builder = factory.newDocumentBuilder();
         is = Thread.currentThread().getContextClassLoader().getResourceAsStream("scenario/scenario_descriptor.xml");
         Document document = builder.parse(is);
         NodeList actionsNodeList = document.getElementsByTagName("action");
         NodeList typesNodeList = document.getElementsByTagName("type");
         NodeList supportersNodeList = document.getElementsByTagName("support");

         int iter;
         Node node;
         String supporterName;
         String refName;
         String fieldType;
         String fieldId;
         for(iter = 0; iter < actionsNodeList.getLength(); ++iter) {
            node = actionsNodeList.item(iter);
            if (node instanceof Element) {
               supporterName = node.getAttributes().getNamedItem("method").getNodeValue();
               refName = node.getAttributes().getNamedItem("service").getNodeValue();
               ActionId acId = new ActionId(refName, supporterName);
               List actionParamList = new LinkedList();
               NodeList paramNodes = node.getChildNodes();

               for(int j = 0; j < paramNodes.getLength(); ++j) {
                  Node pNode = paramNodes.item(j);
                  if (pNode instanceof Element && pNode.getNodeName().equals("param") && pNode.getAttributes() != null) {
                     fieldType = pNode.getAttributes().getNamedItem("name").getNodeValue();
                     fieldId = pNode.getAttributes().getNamedItem("type").getNodeValue();
                     ActionParamTypeInfo actionParamInfo = new ActionParamTypeInfo();
                     actionParamInfo.setName(fieldType);
                     actionParamInfo.setType(fieldId);
                     if (pNode.getAttributes().getNamedItem("supportedby") != null) {
                        String supportedby = pNode.getAttributes().getNamedItem("supportedby").getNodeValue();
                        actionParamInfo.setSupportedBy(supportedby);
                     }

                     actionParamList.add(actionParamInfo);
                  }
               }

               if (this.actionMap.containsKey(acId)) {
                  ((List)this.actionMap.get(acId)).addAll(actionParamList);
               } else {
                  this.actionMap.put(acId, actionParamList);
               }
            }
         }

         String isMulti;
         for(iter = 0; iter < typesNodeList.getLength(); ++iter) {
            node = typesNodeList.item(iter);
            if (node instanceof Element) {
               supporterName = node.getAttributes().getNamedItem("name").getNodeValue();
               refName = node.getAttributes().getNamedItem("ref").getNodeValue();
               List fieldInfoList = new LinkedList();
               NodeList paramNodes = node.getChildNodes();

               for(int j = 0; j < paramNodes.getLength(); ++j) {
                  Node pNode = paramNodes.item(j);
                  if (pNode instanceof Element && pNode.getNodeName().equals("field") && pNode.getAttributes() != null) {
                     String fieldId = pNode.getAttributes().getNamedItem("id").getNodeValue();
                     fieldType = pNode.getAttributes().getNamedItem("type").getNodeValue();
                     ActionParamTypeInfo actionParamInfo = new ActionParamTypeInfo();
                     actionParamInfo.setName(fieldId);
                     actionParamInfo.setType(fieldType);
                     if (pNode.getAttributes().getNamedItem("supportedby") != null) {
                        isMulti = pNode.getAttributes().getNamedItem("supportedby").getNodeValue();
                        actionParamInfo.setSupportedBy(isMulti);
                     }

                     if (pNode.getAttributes().getNamedItem("isMultiple") != null) {
                        isMulti = pNode.getAttributes().getNamedItem("isMultiple").getNodeValue();
                        actionParamInfo.setIsMultiple(Boolean.parseBoolean(isMulti));
                     }

                     fieldInfoList.add(actionParamInfo);
                  }
               }

               TypeRefInfo typeRefInfo = new TypeRefInfo();
               typeRefInfo.setRef(refName);
               typeRefInfo.setParamsInfoList(fieldInfoList);
               this.typeMap.put(supporterName, typeRefInfo);
            }
         }

         for(iter = 0; iter < supportersNodeList.getLength(); ++iter) {
            node = supportersNodeList.item(iter);
            if (node instanceof Element) {
               supporterName = node.getAttributes().getNamedItem("name").getNodeValue();
               refName = node.getAttributes().getNamedItem("method").getNodeValue();
               String service = node.getAttributes().getNamedItem("service").getNodeValue();
               ActionId ai = new ActionId(service, refName);
               List fieldInfoList = new LinkedList();
               NodeList paramNodes = node.getChildNodes();

               for(int j = 0; j < paramNodes.getLength(); ++j) {
                  Node pNode = paramNodes.item(j);
                  if (pNode instanceof Element && pNode.getNodeName().equals("field") && pNode.getAttributes() != null) {
                     fieldId = pNode.getAttributes().getNamedItem("id").getNodeValue();
                     isMulti = pNode.getAttributes().getNamedItem("type").getNodeValue();
                     ActionParamTypeInfo actionParamInfo = new ActionParamTypeInfo();
                     actionParamInfo.setName(fieldId);
                     actionParamInfo.setType(isMulti);
                     fieldInfoList.add(actionParamInfo);
                  }
               }

               SupporterTypeInfo supporter = new SupporterTypeInfo();
               supporter.setName(supporterName);
               supporter.setAi(ai);
               supporter.setFieldList(fieldInfoList);
               this.supportMap.put(supporterName, supporter);
            }
         }
      } catch (IOException | ParserConfigurationException | SAXException var29) {
         String errorMessage = "Unable to get type information about OpenAPI items. " + var29.getMessage();
         this.logger.error(errorMessage, var29);
      } finally {
         try {
            if (is != null) {
               is.close();
               is = null;
            }
         } catch (Exception var28) {
            this.logger.error("", var28);
         }

      }

   }
}
