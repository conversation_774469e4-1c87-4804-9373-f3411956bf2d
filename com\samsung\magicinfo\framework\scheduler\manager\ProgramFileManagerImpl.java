package com.samsung.magicinfo.framework.scheduler.manager;

import com.samsung.common.cache.CacheFactory;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.ContentUtils;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramFile;
import com.samsung.magicinfo.framework.scheduler.exception.EnumScheduleException;
import com.samsung.magicinfo.framework.scheduler.exception.ScheduleException;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.Logger;

public class ProgramFileManagerImpl implements ProgramFileManager {
   private static ProgramFileManagerImpl instance;
   private static final String PROGRAM_FILES_CACHE = "PROGRAM_FILES_CACHE";
   private static final Logger logger = LoggingManagerV2.getLogger(ProgramFileManagerImpl.class);

   public ProgramFileManagerImpl() {
      super();
   }

   public static ProgramFileManager getInstance() {
      if (instance == null) {
         Class var0 = ProgramFileManagerImpl.class;
         synchronized(ProgramFileManagerImpl.class) {
            instance = new ProgramFileManagerImpl();
         }
      }

      return instance;
   }

   public ProgramFile getProgramFile(String programId) {
      ProgramFile programFile = null;

      try {
         Object programFileFromCache = CacheFactory.getCache().get("PROGRAM_FILES_CACHE");
         if (programFileFromCache != null) {
            Map maps = (Map)programFileFromCache;
            programFile = (ProgramFile)maps.get(programId);
         }

         if (programFile == null) {
            throw new Exception("cannot find programFile on cache");
         }
      } catch (Exception var14) {
         logger.error("[ProgramFileManagerImpl] error to get program file by " + programId);
         logger.error("Creating New Program File for " + programId);
         ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
         ProgramEntity program = null;

         try {
            program = scheduleInfo.getProgram(programId);
            if (program == null || !"N".equals(program.getDeleted()) || !"N".equals(program.getIs_default())) {
               return null;
            }

            String deviceType = program.getDevice_type();
            float deviceTypeVersion = program.getDevice_type_version();
            long scheduleVersion = !"LPLAYER".equalsIgnoreCase(program.getDevice_type()) && (!"SPLAYER".equalsIgnoreCase(deviceType) || !(deviceTypeVersion < 2.0F)) ? 3L : 2L;
            Object scheduleFileInterface;
            if (scheduleVersion == 2L) {
               scheduleFileInterface = new ScheduleFileManager2_0();
            } else {
               scheduleFileInterface = new ScheduleFileManager3_0();
            }

            programFile = ((ScheduleFileInterface)scheduleFileInterface).createProgram(programId);
            List contentList;
            if (program.getProgram_type() != null && "ADV".equalsIgnoreCase(program.getProgram_type())) {
               contentList = ContentUtils.getAdContentList(programId);
            } else {
               contentList = ContentUtils.getContentList(programId);
            }

            programFile.setContentIds(contentList);
            programFile.setProgram(program);
         } catch (SQLException var12) {
            logger.error("", var12);
         } catch (ScheduleException var13) {
            logger.error("", var13);
         }

         this.setProgramFile(programId, programFile);
      }

      return programFile;
   }

   public synchronized void setProgramFile(String programId, ProgramFile programFile) {
      try {
         Object programFileFromCache = CacheFactory.getCache().get("PROGRAM_FILES_CACHE");
         Object maps;
         if (programFileFromCache == null) {
            maps = new HashMap();
         } else {
            maps = (Map)programFileFromCache;
         }

         ((Map)maps).put(programId, programFile);
         CacheFactory.getCache().set("PROGRAM_FILES_CACHE", maps);
      } catch (Exception var5) {
         logger.error("[ProgramFileManagerImpl] error to set program file by " + programId);
      }

   }

   public void setProgramFiles(Map programFiles) {
      try {
         CacheFactory.getCache().set("PROGRAM_FILES_CACHE", programFiles);
      } catch (Exception var3) {
         logger.error("[ProgramFileManagerImpl] error to update program files");
      }

   }

   public ProgramFile create(ProgramEntity program) throws ScheduleException {
      String deviceType = program.getDevice_type();
      Float deviceTypeVersion = program.getDevice_type_version();
      long scheduleVersion = !"LPLAYER".equalsIgnoreCase(deviceType) && (!"SPLAYER".equalsIgnoreCase(deviceType) || !(deviceTypeVersion < 2.0F)) ? 3L : 2L;
      boolean isDefaultProgram = "Y".equalsIgnoreCase(program.getIs_default());
      Object scheduleFileInterface;
      if (scheduleVersion == 2L) {
         scheduleFileInterface = new ScheduleFileManager2_0();
      } else {
         scheduleFileInterface = new ScheduleFileManager3_0();
      }

      ProgramFile programFile = ((ScheduleFileInterface)scheduleFileInterface).createProgram(program.getProgram_id());
      List contentList = null;
      if (!isDefaultProgram) {
         if (program.getProgram_type() != null && "ADV".equalsIgnoreCase(program.getProgram_type())) {
            contentList = ContentUtils.getAdContentList(program.getProgram_id());
         } else {
            contentList = ContentUtils.getContentList(program.getProgram_id());
         }
      }

      if (programFile != null) {
         if (programFile.getProgramFile() == null) {
            throw new ScheduleException(EnumScheduleException.PROGRAM_FILE_IS_NULL);
         } else {
            programFile.setCreateTime(new Timestamp(System.currentTimeMillis()));
            programFile.setProgramVersion(program.getVersion());
            programFile.setContentIds(contentList);
            programFile.setProgram(program);
            this.setProgramFile(program.getProgram_id(), programFile);
            return programFile;
         }
      } else {
         throw new ScheduleException(EnumScheduleException.PROGRAM_FILE_IS_NULL);
      }
   }

   public Map getProgramFiles() {
      Map programFiles = null;
      Object programFileFromCache = null;

      try {
         programFileFromCache = CacheFactory.getCache().get("PROGRAM_FILES_CACHE");
         if (programFileFromCache != null) {
            programFiles = (Map)programFileFromCache;
         }
      } catch (Exception var4) {
         logger.error("", var4);
      }

      return programFiles;
   }

   public void removeProgramFile(String programId) {
      try {
         Object programFileFromCache = CacheFactory.getCache().get("PROGRAM_FILES_CACHE");
         Object maps;
         if (programFileFromCache == null) {
            maps = new HashMap();
         } else {
            maps = (Map)programFileFromCache;
         }

         ((Map)maps).remove(programId);
         CacheFactory.getCache().set("PROGRAM_FILES_CACHE", maps);
      } catch (Exception var4) {
         logger.error("[ProgramFileManagerImpl] error to delete program file by " + programId);
      }

   }
}
