package com.samsung.magicinfo.webauthor2.endpoint;

public final class OpenApiCommonContentService {
  public static final String COMMON_CONTENT_SERVICE = "CommonContentService";
  
  public static final String GET_CONTENT_INFO = "getContentInfo";
  
  public static final String GET_CONTENT_LIST_BY_TYPE = "getContentListByType";
  
  public static final String GET_CONTENT_LIST = "getContentList";
  
  public static final String GET_CONTENT_FILE_LIST = "getContentFileList";
  
  public static final String GET_CONTENT_GROUP_LIST = "getContentGroupList";
  
  public static final String GET_CONTENT_THUMBNAIL = "getContentThumbnail";
  
  public static final String GET_CONTENT_THUMBNAIL_BY_FILE_ID = "getContentThumbnailByFileId";
  
  public static final String ADD_CONVERT_TABLE = "addConvertTable";
  
  public static final String GET_CONVERT_TABLE_LIST = "getConvertTableList";
  
  public static final String MODIFY_CONVERT_TABLE = "modifyConvertTable";
  
  public static final String DELETE_CONVERT_TABLE = "deleteConvertTable";
  
  public static final String GET_DLK_MAPPING_LIST = "getDLKmappingList ";
  
  public static final String ADD_DLK_INFO = "addDlkInfo";
  
  public static final String GET_FILE_INFO = "getFileInfo";
  
  public static final String GET_FILE_TYPE_LIST = "getFileTypeList";
  
  public static final String GET_MEDIA_TYPE_LIST = "getMediaTypeList";
  
  public static final String DELETE_CONTENT = "deleteContent";
}
