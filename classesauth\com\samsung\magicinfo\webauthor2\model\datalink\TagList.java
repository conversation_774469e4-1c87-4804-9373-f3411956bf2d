package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.samsung.magicinfo.webauthor2.model.datalink.TagMatchType;
import java.util.List;

public class TagList {
  private final List<String> tags;
  
  private final TagMatchType tagMatchType;
  
  @JsonCreator
  public TagList(@JsonProperty("tags") List<String> tags, @JsonProperty("tagMatchType") TagMatchType tagMatchType) {
    this.tags = tags;
    this.tagMatchType = tagMatchType;
  }
  
  public List<String> getTags() {
    return this.tags;
  }
  
  public TagMatchType getTagMatchType() {
    return this.tagMatchType;
  }
}
