package com.samsung.magicinfo.restapi.device.model;

import io.swagger.annotations.ApiModelProperty;

public class V2DeviceLogProcess {
   @ApiModelProperty(
      value = "Category script for log information",
      required = true,
      example = "test"
   )
   private String script;
   @ApiModelProperty(
      value = "Log collection time",
      required = false,
      example = "1"
   )
   private String duration;
   @ApiModelProperty(
      value = "Packet size",
      required = false,
      example = "1"
   )
   private String packetSize;
   @ApiModelProperty(
      value = "Device log status - INIT or READY or START or END",
      required = true,
      example = "INIT"
   )
   private String status;
   @ApiModelProperty(
      value = "Request step",
      required = true,
      example = "0"
   )
   private String step;
   @ApiModelProperty(
      value = "Request count",
      required = true,
      example = "1"
   )
   private String chkTimeout;
   @ApiModelProperty(
      value = "Smart Diagnostic Version",
      required = true,
      example = "1"
   )
   private String smartDiagnosticVersion;
   @ApiModelProperty(
      value = "Request Id issued after registering with the service",
      required = false,
      example = "''"
   )
   private String requestId;

   public V2DeviceLogProcess() {
      super();
   }

   public String getScript() {
      return this.script;
   }

   public void setScript(String script) {
      this.script = script;
   }

   public String getDuration() {
      return this.duration;
   }

   public void setDuration(String duration) {
      this.duration = duration;
   }

   public String getPacketSize() {
      return this.packetSize;
   }

   public void setPacketSize(String packetSize) {
      this.packetSize = packetSize;
   }

   public String getStatus() {
      return this.status;
   }

   public void setStatus(String status) {
      this.status = status;
   }

   public String getStep() {
      return this.step;
   }

   public void setStep(String step) {
      this.step = step;
   }

   public String getChkTimeout() {
      return this.chkTimeout;
   }

   public void setChkTimeout(String chkTimeout) {
      this.chkTimeout = chkTimeout;
   }

   public String getSmartDiagnosticVersion() {
      return this.smartDiagnosticVersion;
   }

   public void setSmartDiagnosticVersion(String smartDiagnosticVersion) {
      this.smartDiagnosticVersion = smartDiagnosticVersion;
   }

   public String getRequestId() {
      return this.requestId;
   }

   public void setRequestId(String requestId) {
      this.requestId = requestId;
   }
}
