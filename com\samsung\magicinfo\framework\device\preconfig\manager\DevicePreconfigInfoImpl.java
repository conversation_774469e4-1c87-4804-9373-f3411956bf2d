package com.samsung.magicinfo.framework.device.preconfig.manager;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.MDCTimeStrUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceControl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSecurityConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceServiceConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSoftwareConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeConf;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSecurityConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSecurityConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManagerImpl;
import com.samsung.magicinfo.framework.device.preconfig.dao.DevicePreconfigDao;
import com.samsung.magicinfo.framework.device.preconfig.entity.DevicePreconfig;
import com.samsung.magicinfo.framework.device.preconfig.entity.DevicePreconfigResultXml;
import com.samsung.magicinfo.protocol.reservation.ReservationManager;
import com.samsung.magicinfo.protocol.reservation.ReservationManagerImpl;
import com.samsung.magicinfo.protocol.reservation.ScheduleReservation;
import java.io.File;
import java.io.FileNotFoundException;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class DevicePreconfigInfoImpl implements DevicePreconfigInfo {
   Logger logger = LoggingManagerV2.getLogger(DevicePreconfigInfoImpl.class);
   private static DevicePreconfigInfoImpl instance;
   private DevicePreconfigDao dao = null;

   public static synchronized DevicePreconfigInfo getInstance() {
      if (instance == null) {
         instance = new DevicePreconfigInfoImpl((SqlSession)null);
      }

      return instance;
   }

   public static DevicePreconfigInfo getInstance(SqlSession session) {
      return new DevicePreconfigInfoImpl(session);
   }

   private DevicePreconfigInfoImpl(SqlSession session) {
      super();
      if (this.dao == null) {
         this.dao = new DevicePreconfigDao(session);
      }

   }

   public PagedListInfo getPagedList(int startPos, int pageSize, Map condition, String section) throws Exception {
      return this.dao.getPreconfigList(condition);
   }

   public int addDevicePreconfigInfo(DevicePreconfig preconfig) throws SQLException {
      return this.dao.addDevicePreconfigInfo(preconfig);
   }

   public DevicePreconfig getPreconfigInfo(String preconfigId) throws SQLException {
      DevicePreconfig preconfig = this.dao.getPreconfigInfo(preconfigId);
      if (preconfig != null) {
         if (preconfig.getOrganization_id() == 0L) {
            preconfig.setOrganization_name("Common");
         }

         DeviceControl control = this.getDeviceControlByPreconfigId(preconfig.getPreconfig_id());
         preconfig.setDevice_control(control);
      }

      return preconfig;
   }

   public DevicePreconfig getPreconfigByDeviceId(String deviceId) throws SQLException {
      DevicePreconfig preconfig = this.dao.getPreconfigInfoByDeviceId(deviceId);
      if (preconfig != null) {
         DeviceControl control = this.getDeviceControlByPreconfigId(preconfig.getPreconfig_id());
         preconfig.setDevice_control(control);
         List serviceConfs = this.getServiceConfigInfo(preconfig.getPreconfig_id());
         preconfig.setDevice_service_confs(serviceConfs);
      }

      return preconfig;
   }

   public DevicePreconfig getPreconfigInfoByDeviceId(String deviceId) throws SQLException {
      return this.dao.getPreconfigInfoByDeviceId(deviceId);
   }

   public boolean deletePreconfig(String[] preconfigIdList) throws SQLException {
      return this.dao.deletePreconfig(preconfigIdList);
   }

   private DeviceControl getDeviceControlByPreconfigId(String preconfigId) {
      String productType = "PREMIUM";
      DeviceControl control = new DeviceControl();

      try {
         DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
         DeviceGeneralConf general = deviceDao.getDeviceGeneralConf(preconfigId, true);
         control.setGeneral(general);
      } catch (Exception var13) {
         control.setGeneral((DeviceGeneralConf)null);
      }

      try {
         DeviceSystemSetupConfManager deviceConf = DeviceSystemSetupConfManagerImpl.getInstance(productType);
         DeviceSystemSetupConf systemSetup = deviceConf.getDeviceSystemSetupConf(preconfigId, false);
         control.setSetup(systemSetup);
      } catch (Exception var12) {
         control.setSetup((DeviceSystemSetupConf)null);
      }

      try {
         DeviceDisplayConfManager displayConfDao = DeviceDisplayConfManagerImpl.getInstance();
         DeviceDisplayConf display = displayConfDao.getDeviceDisplayConf(preconfigId, false);
         control.setDisplay(display);
      } catch (Exception var11) {
         control.setDisplay((DeviceDisplayConf)null);
      }

      try {
         DeviceTimeConfManager deviceConf = DeviceTimeConfManagerImpl.getInstance(productType);
         DeviceTimeConf time = deviceConf.getDeviceNewTimeConf(preconfigId, 7);
         if (time.getHoliday_cnt() != null && time.getHoliday_cnt() > 0L) {
            List holidayList = deviceConf.getDeviceTimeHolidayConf(preconfigId);
            time.setTimer_holiday(MDCTimeStrUtils.holidayObjToStr(holidayList));
         }

         control.setTime(time);
      } catch (Exception var10) {
         control.setTime((DeviceTimeConf)null);
      }

      try {
         DeviceSecurityConfManager securityDao = DeviceSecurityConfManagerImpl.getInstance(productType);
         DeviceSecurityConf security = securityDao.getDeviceSecurityConf(preconfigId, false);
         control.setSecurity(security);
      } catch (Exception var9) {
         control.setSecurity((DeviceSecurityConf)null);
      }

      List software;
      try {
         software = this.dao.getServiceConfigInfo(preconfigId);
         control.setServiceList(software);
      } catch (Exception var8) {
         control.setServiceList((List)null);
      }

      try {
         software = this.dao.getSoftwareConfigInfo(preconfigId);
         control.setSoftwareList(software);
      } catch (Exception var7) {
         control.setSoftwareList((List)null);
      }

      return control;
   }

   public int addPreconfigGroupMapping(Map map) throws SQLException {
      return this.dao.addPreconfigGroupMapping(map);
   }

   public int addPreconfigGroupMappingList(String preconfigId, String groupIds) throws SQLException {
      try {
         if (preconfigId != null) {
            this.dao.deletePreconfigGroupMapping(preconfigId);
            if (groupIds != null && groupIds.length() > 0) {
               String[] groupArr = groupIds.split(",");
               Long[] lGroupArr = new Long[groupArr.length];

               int i;
               for(i = 0; i < groupArr.length; ++i) {
                  lGroupArr[i] = Long.valueOf(groupArr[i]);
               }

               this.dao.deletePreconfigGroupMappingByGroup(lGroupArr);

               for(i = 0; i < lGroupArr.length; ++i) {
                  Map map = new HashMap();
                  Long groupId = lGroupArr[i];
                  map.put("preconfig_id", preconfigId);
                  map.put("device_group_id", groupId);
                  this.dao.addPreconfigGroupMapping(map);
               }
            }

            this.initDeployStatusByGroup(preconfigId, groupIds);
         }

         return 1;
      } catch (Exception var8) {
         this.logger.error("[DeviceUtils:setPreconfigGroupMap] fail, " + var8.getMessage());
         return -1;
      }
   }

   public int deployToDevice(Device device) throws Exception {
      DeviceSystemSetupConfManager deviceSetupMgr = DeviceSystemSetupConfManagerImpl.getInstance();
      DeviceSystemSetupConf setupConf = deviceSetupMgr.getDeviceSystemSetupConf(device.getDevice_id());
      if (setupConf.getPre_config_version() == null) {
         return -1;
      } else {
         DevicePreconfig preconfig = this.getPreconfigByDeviceId(device.getDevice_id());
         if (preconfig != null) {
            Map deployStatus = this.getDeployStatusByDeviceId(device.getDevice_id());
            if (deployStatus != null) {
               this.dao.setDeployStatus(preconfig.getPreconfig_id(), device.getDevice_id(), (String)null);
            } else {
               this.dao.addDeployStatus(preconfig.getPreconfig_id(), device.getDevice_id(), (String)null);
            }

            if (!DeviceUtils.isConnected(device.getDevice_id())) {
               return 1;
            }
         }

         ReservationManager reserveManager = ReservationManagerImpl.getInstance();
         Map params = new HashMap();
         params.put("deploy_type", "PRE_CONFIG");
         params.put("pre_config", preconfig);
         params.put("device", device);
         ScheduleReservation schRsv = new ScheduleReservation();
         schRsv.setService_id("DEPLOY_DEVICE_CONFIG");
         schRsv.setService_params(params);
         reserveManager.insert(schRsv);
         return 0;
      }
   }

   public boolean updatePreconfigInfo(DevicePreconfig preconfig) throws SQLException {
      return this.dao.updatePreconfigInfo(preconfig);
   }

   public int addServiceConfig(DeviceServiceConf service) throws SQLException {
      return this.dao.addServiceConfig(service);
   }

   public boolean updateServiceConfig(DeviceServiceConf service) throws SQLException {
      return this.dao.updateServiceConfig(service);
   }

   public List getServiceConfigInfo(String preconfigId) throws SQLException {
      return this.dao.getServiceConfigInfo(preconfigId);
   }

   public boolean addSoftwareConfigList(String preconfigId, List softwareList) throws SQLException {
      this.deleteSoftwareConfig(preconfigId);
      if (softwareList != null && softwareList.size() > 0) {
         Iterator var3 = softwareList.iterator();

         while(var3.hasNext()) {
            DeviceSoftwareConf software = (DeviceSoftwareConf)var3.next();
            software.setPreconfig_id(preconfigId);
            this.dao.addSoftwareConfig(software);
         }
      }

      return true;
   }

   public boolean deleteSoftwareConfig(String preconfigId) throws SQLException {
      return this.dao.deleteSoftwareConfig(preconfigId);
   }

   public boolean addServiceConfigList(String preconfigId, List serviceList) throws SQLException {
      this.deleteServiceConfig(preconfigId);
      if (serviceList != null && serviceList.size() > 0) {
         Iterator var3 = serviceList.iterator();

         while(var3.hasNext()) {
            DeviceServiceConf service = (DeviceServiceConf)var3.next();
            service.setPreconfig_id(preconfigId);
            this.dao.addServiceConfig(service);
         }
      }

      return true;
   }

   public boolean deleteServiceConfig(String preconfigId) throws SQLException {
      return this.dao.deleteServiceConfig(preconfigId);
   }

   public boolean setDeployStatusSuccess(String preconfigId, String deviceId) throws SQLException {
      return this.dao.setDeployStatus(preconfigId, deviceId, "SUCCESS");
   }

   public boolean setDeployStatusReportTime(String preconfigId, String deviceId, Date reportTime) throws SQLException {
      return this.dao.setDeployStatusReportTime(preconfigId, deviceId, reportTime);
   }

   public int initDeployStatusByGroup(String preconfigId, String groupIds) throws SQLException {
      try {
         this.dao.deleteDeployStatus(preconfigId);
         if (groupIds != null && groupIds.length() > 0) {
            String[] groupArr = groupIds.split(",");
            Long[] lGroupArr = new Long[groupArr.length];

            int i;
            for(i = 0; i < groupArr.length; ++i) {
               lGroupArr[i] = Long.valueOf(groupArr[i]);
            }

            this.dao.deleteDeployStatusByGroup(lGroupArr);

            for(i = 0; i < groupArr.length; ++i) {
               this.dao.setDeployStatusByGroup(preconfigId, (String)null, lGroupArr[i]);
            }
         }

         return 1;
      } catch (Exception var6) {
         return -1;
      }
   }

   public List getGroupMappingByPreconfig(String preconfigId) throws SQLException {
      return this.dao.getGroupMappingByPreconfig(preconfigId);
   }

   public List getDeployStatusByPreconfigId(String preconfigId) throws SQLException {
      return this.dao.getDeployStatusByPreconfigId(preconfigId);
   }

   public Map getDeployStatusByDeviceId(String deviceId) throws SQLException {
      return this.dao.getDeployStatusByDeviceId(deviceId);
   }

   public DevicePreconfigResultXml getPreconfigResultXml(String deviceId) throws Exception {
      String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar);
      File file = new File(CONTENTS_HOME + File.separator + "pre_config_result" + File.separator + deviceId + ".xml");
      if (!file.exists()) {
         throw new FileNotFoundException();
      } else {
         JAXBContext jaxbContext = JAXBContext.newInstance(new Class[]{DevicePreconfigResultXml.class});
         Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
         return (DevicePreconfigResultXml)jaxbUnmarshaller.unmarshal(file);
      }
   }

   public Map getPreconfigResultMap(String deviceId, String preconfigId) throws Exception {
      DevicePreconfigResultXml resultXml = this.getPreconfigResultXml(deviceId);
      if (resultXml.getVersion() != null && resultXml.getVersion().indexOf(preconfigId) >= 0) {
         return PreConfigUtils.convertPreconfigResultMap(resultXml.getDevice_config());
      } else {
         throw new FileNotFoundException("Preconfig ID is not matched.");
      }
   }

   public boolean deletePreconfigFromDevice(String deviceId) throws SQLException {
      return this.dao.deletePreconfigFromDevice(deviceId);
   }
}
